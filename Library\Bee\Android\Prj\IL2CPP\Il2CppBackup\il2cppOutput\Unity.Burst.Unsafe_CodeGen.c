﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void NonVersionableAttribute__ctor_mBC3056ECFEE16B17F3779A50EBDCCD519078E862 (void);
static Il2CppMethodPointer s_methodPointers[2] = 
{
	NULL,
	NonVersionableAttribute__ctor_mBC3056ECFEE16B17F3779A50EBDCCD519078E862,
};
static const int32_t s_InvokerIndices[2] = 
{
	0,
	13298,
};
static const Il2CppTokenRangePair s_rgctxIndices[1] = 
{
	{ 0x06000001, { 0, 1 } },
};
extern const uint32_t g_rgctx_TU26_tD3468B6794620312ED0506E36D2969131D852330;
static const Il2CppRGCTXDefinition s_rgctxValues[1] = 
{
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_tD3468B6794620312ED0506E36D2969131D852330 },
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_Unity_Burst_Unsafe_CodeGenModule;
const Il2CppCodeGenModule g_Unity_Burst_Unsafe_CodeGenModule = 
{
	"Unity.Burst.Unsafe.dll",
	2,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	1,
	s_rgctxIndices,
	1,
	s_rgctxValues,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};

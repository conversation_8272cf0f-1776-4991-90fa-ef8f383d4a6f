<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\Workspace\RGameClient\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\assets"><file name="Audio/GeneratedSoundBanks/Android/Battle_Common.bnk" path="E:\Workspace\RGameClient\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\assets\Audio\GeneratedSoundBanks\Android\Battle_Common.bnk"/><file name="Audio/GeneratedSoundBanks/Android/Battle_Common.txt" path="E:\Workspace\RGameClient\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\assets\Audio\GeneratedSoundBanks\Android\Battle_Common.txt"/><file name="Audio/GeneratedSoundBanks/Android/Init.bnk" path="E:\Workspace\RGameClient\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\assets\Audio\GeneratedSoundBanks\Android\Init.bnk"/><file name="Audio/GeneratedSoundBanks/Android/Init.txt" path="E:\Workspace\RGameClient\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\assets\Audio\GeneratedSoundBanks\Android\Init.txt"/><file name="Audio/GeneratedSoundBanks/Android/PlatformInfo.xml" path="E:\Workspace\RGameClient\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\assets\Audio\GeneratedSoundBanks\Android\PlatformInfo.xml"/><file name="Audio/GeneratedSoundBanks/Android/PluginInfo.xml" path="E:\Workspace\RGameClient\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\assets\Audio\GeneratedSoundBanks\Android\PluginInfo.xml"/><file name="Audio/GeneratedSoundBanks/Android/SoundbanksInfo.xml" path="E:\Workspace\RGameClient\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\assets\Audio\GeneratedSoundBanks\Android\SoundbanksInfo.xml"/><file name="bin/Data/boot.config" path="E:\Workspace\RGameClient\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\assets\bin\Data\boot.config"/><file name="bin/Data/data.unity3d" path="E:\Workspace\RGameClient\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\assets\bin\Data\data.unity3d"/><file name="bin/Data/Managed/Metadata/global-metadata.dat" path="E:\Workspace\RGameClient\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\assets\bin\Data\Managed\Metadata\global-metadata.dat"/><file name="bin/Data/Managed/Resources/mscorlib.dll-resources.dat" path="E:\Workspace\RGameClient\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\assets\bin\Data\Managed\Resources\mscorlib.dll-resources.dat"/><file name="bin/Data/resources.resource" path="E:\Workspace\RGameClient\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\assets\bin\Data\resources.resource"/><file name="bin/Data/RuntimeInitializeOnLoads.json" path="E:\Workspace\RGameClient\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\assets\bin\Data\RuntimeInitializeOnLoads.json"/><file name="bin/Data/ScriptingAssemblies.json" path="E:\Workspace\RGameClient\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\assets\bin\Data\ScriptingAssemblies.json"/><file name="bin/Data/sharedassets0.resource" path="E:\Workspace\RGameClient\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\assets\bin\Data\sharedassets0.resource"/><file name="bin/Data/sharedassets2.resource" path="E:\Workspace\RGameClient\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\assets\bin\Data\sharedassets2.resource"/><file name="bin/Data/unity default resources" path="E:\Workspace\RGameClient\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\assets\bin\Data\unity default resources"/><file name="bin/Data/unity_app_guid" path="E:\Workspace\RGameClient\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\assets\bin\Data\unity_app_guid"/></source><source path="E:\Workspace\RGameClient\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\shader_assets\release\out"/></dataSet><dataSet config="release" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\Workspace\RGameClient\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\release\assets"/></dataSet></merger>
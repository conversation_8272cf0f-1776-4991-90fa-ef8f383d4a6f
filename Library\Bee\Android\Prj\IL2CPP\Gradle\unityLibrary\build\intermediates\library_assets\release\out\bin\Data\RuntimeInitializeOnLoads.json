{"root": [{"assemblyName": "Assembly-CSharp", "nameSpace": "uPools", "className": "SharedGameObjectPool", "methodName": "Init", "loadTypes": 1, "isUnityClass": false}, {"assemblyName": "Assembly-CSharp", "nameSpace": "GM", "className": "GMSystem", "methodName": "RegisterGMs", "loadTypes": 1, "isUnityClass": false}, {"assemblyName": "Assembly-CSharp", "nameSpace": "", "className": "__JobReflectionRegistrationOutput__473105695", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "<PERSON>backManager", "nameSpace": "Callback", "className": "<PERSON>backManager", "methodName": "ClearAll", "loadTypes": 1, "isUnityClass": false}, {"assemblyName": "Events", "nameSpace": "Events", "className": "EventBusRegistry", "methodName": "Reset", "loadTypes": 1, "isUnityClass": false}, {"assemblyName": "LitMotion", "nameSpace": "LitMotion", "className": "Player<PERSON>oop<PERSON>el<PERSON>", "methodName": "Init", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "LitMotion", "nameSpace": "LitMotion", "className": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "methodName": "Init", "loadTypes": 1, "isUnityClass": false}, {"assemblyName": "LitMotion", "nameSpace": "", "className": "__JobReflectionRegistrationOutput__1815361719", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "LitMotion", "nameSpace": "", "className": "$BurstDirectCallInitializer", "methodName": "Initialize", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "LitMotion.Extensions", "nameSpace": "LitMotion.Extensions", "className": "TextMeshProMotionAnimator", "methodName": "Init", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "MeshUI", "nameSpace": "MeshUI", "className": "MeshCanvasSettings", "methodName": "Initialize", "loadTypes": 1, "isUnityClass": false}, {"assemblyName": "Polyperfect.War", "nameSpace": "Polyperfect.War", "className": "SceneTargetsManager", "methodName": "Init", "loadTypes": 1, "isUnityClass": false}, {"assemblyName": "Unity.Collections", "nameSpace": "", "className": "__JobReflectionRegistrationOutput__1910371556", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": true}, {"assemblyName": "Unity.Collections", "nameSpace": "", "className": "$BurstDirectCallInitializer", "methodName": "Initialize", "loadTypes": 2, "isUnityClass": true}, {"assemblyName": "Unity.RenderPipelines.Core.Runtime", "nameSpace": "UnityEngine.Experimental.Rendering", "className": "XRSystem", "methodName": "XRSystemInit", "loadTypes": 3, "isUnityClass": true}, {"assemblyName": "Unity.RenderPipelines.Core.Runtime", "nameSpace": "UnityEngine.Rendering", "className": "DebugUpdater", "methodName": "RuntimeInit", "loadTypes": 0, "isUnityClass": true}, {"assemblyName": "Unity.RenderPipelines.Universal.Runtime", "nameSpace": "", "className": "__JobReflectionRegistrationOutput__2787129498", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": true}, {"assemblyName": "Unity.ResourceManager", "nameSpace": "UnityEngine.ResourceManagement.ResourceProviders", "className": "AssetBundleProvider", "methodName": "Init", "loadTypes": 4, "isUnityClass": true}, {"assemblyName": "Unity.VisualScripting.Core", "nameSpace": "Unity.VisualScripting", "className": "RuntimeVSUsageUtility", "methodName": "RuntimeInitializeOnLoadBeforeSceneLoad", "loadTypes": 1, "isUnityClass": true}]}
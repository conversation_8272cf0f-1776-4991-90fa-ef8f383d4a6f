﻿#include "pch-cpp.hpp"

#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include <limits>


template <typename R>
struct VirtualFuncInvoker0
{
	typedef R (*Func)(void*, const RuntimeMethod*);

	static inline R Invoke (Il2CppMethodSlot slot, RuntimeObject* obj)
	{
		const VirtualInvokeData& invokeData = il2cpp_codegen_get_virtual_invoke_data(slot, obj);
		return ((Func)invokeData.methodPtr)(obj, invokeData.method);
	}
};

struct Action_2_t8E07914D7090FF200FE84404EEEFAF3CE183C9F3;
struct Action_2_t38DEBB6BD6AE1CA882236F63F7E1DB3781D38994;
struct List_1_t77EDD3ECA98BCC1B49E3106C8CB923CA87D088ED;
struct List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D;
struct StringU5BU5DU5BU5D_t8BCC500C5CC1686D9BADCBAA811074FE00F83ACF;
struct ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031;
struct CharU5BU5D_t799905CF001DD5F13F7DBB310181FC4D8B7D0AAB;
struct DelegateU5BU5D_tC5AB7E8F745616680F337909D3A8E6C722CDF771;
struct IntPtrU5BU5D_tFD177F8C806A6921AD7150264CCC62FA00CAD832;
struct MaterialU5BU5D_t2B1D11C42DB07A4400C0535F92DBB87A2E346D3D;
struct ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918;
struct SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C;
struct StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF;
struct StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248;
struct TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB;
struct Behaviour_t01970CFBBA658497AE30F311C447DB0440BAB7FA;
struct Binder_t91BFCE95A7057FADF4D8A1A342AFE52872246235;
struct Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184;
struct CancellationTokenSource_tAAE1E0033BCFC233801F8CB4CED5C852B350CB7B;
struct CommandBuffer_tB56007DC84EF56296C325EC32DD12AC1E3DC91F7;
struct Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3;
struct Delegate_t;
struct DelegateData_t9B286B493293CD2D23A5B2B5EF0E5B1324C2B77E;
struct GameObject_t76FEDD663AB33C991A9C9A23129337651094216F;
struct IDictionary_t6D03155AF1FA9083817AA5B6AD7DEEACC26AB220;
struct IndexOutOfRangeException_t7ECB35264FB6CA8FAA516BD958F4B2ADC78E8A82;
struct Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3;
struct MemberFilter_tF644F1AE82F611B677CE1964D5A3277DDA21D553;
struct MethodInfo_t;
struct MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71;
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C;
struct RenderPipelineAsset_t5F9BF815BF931E1314B184E7F9070FB649C7054E;
struct RenderTexture_tBA90C4C3AD9EECCFDDCC632D97C29FAB80D60D27;
struct Renderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF;
struct SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6;
struct Shader_tADC867D36B7876EE22427FAA2CE485105F4EE692;
struct String_t;
struct TCP2_CameraDepth_t814CEC3D0C44B13A72C4A76F57C47D8885EDF77F;
struct TCP2_GetPosOnWater_tAEFF9D18028A8FDE5B4697289D69F0E4ABE74973;
struct TCP2_GetVertexWavesPosition_t37CBB31361B597192C56DBCE2DF080524AD535CF;
struct TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5;
struct TCP2_ShaderUpdateUnityTime_t8BA15EEC76AA4A8461D2A1C5974BB2E0C6874B90;
struct Texture_t791CBB51219779964E0E8A2ED7C1AA5F92A4A700;
struct Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1;
struct Type_t;
struct UnitySourceGeneratedAssemblyMonoScriptTypes_v1_t41C7444C07A9E4C2804E8968F9F1E0036FBE123F;
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915;
struct CameraCallback_t844E527BFE37BC0495E7F67993E43C07642DA9DD;

IL2CPP_EXTERN_C RuntimeClass* Action_2_t8E07914D7090FF200FE84404EEEFAF3CE183C9F3_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* CameraCallback_t844E527BFE37BC0495E7F67993E43C07642DA9DD_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* CommandBuffer_tB56007DC84EF56296C325EC32DD12AC1E3DC91F7_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Debug_t8394C7EEAECA3689C2C9B9DE9C7166D73596276F_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* GameObject_t76FEDD663AB33C991A9C9A23129337651094216F_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* IndexOutOfRangeException_t7ECB35264FB6CA8FAA516BD958F4B2ADC78E8A82_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* List_1_t77EDD3ECA98BCC1B49E3106C8CB923CA87D088ED_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Math_tEB65DE7CA8B083C412C969C92981C030865486CE_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* RenderPipelineManager_t44E0175AAADDD5487593AEF2B009B1B154957CDB_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* RenderTexture_tBA90C4C3AD9EECCFDDCC632D97C29FAB80D60D27_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* ScriptableRenderContext_t5AB09B3602BEB456E0DC3D53926D3A3BDAF08E36_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* TCP2_GetVertexWavesPosition_t37CBB31361B597192C56DBCE2DF080524AD535CF_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* TCP2_RuntimeUtils_t5E9598A60187D562FEDFC57BE19768E002F7129C_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* TCP2_ShaderUpdateUnityTime_t8BA15EEC76AA4A8461D2A1C5974BB2E0C6874B90_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Type_t_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* UniversalRenderPipeline_t54B4737DC500C08628C5BE283D8C583C14DED98F_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeField* U3CPrivateImplementationDetailsU3E_t7A973F6EE5177382CAF19905E3229862BBF956B3____1A70BAE12848E0CCADB10009D4920BED5C1135E1D288A0F3D390035604846F38_FieldInfo_var;
IL2CPP_EXTERN_C RuntimeField* U3CPrivateImplementationDetailsU3E_t7A973F6EE5177382CAF19905E3229862BBF956B3____2D1418505C7F379AAF6D35BFDE539EC766882C689A946F3A02F6ED78DB0BD2B8_FieldInfo_var;
IL2CPP_EXTERN_C RuntimeField* U3CPrivateImplementationDetailsU3E_t7A973F6EE5177382CAF19905E3229862BBF956B3____5A72A641E92E65759A96A7EDA358E6A14AA5375E292DCE75BEAAF46809339171_FieldInfo_var;
IL2CPP_EXTERN_C RuntimeField* U3CPrivateImplementationDetailsU3E_t7A973F6EE5177382CAF19905E3229862BBF956B3____89C1CF9324638CEDC10B05C2D337701B9E9B858A3906AC94C72AE20569DF6F7C_FieldInfo_var;
IL2CPP_EXTERN_C RuntimeField* U3CPrivateImplementationDetailsU3E_t7A973F6EE5177382CAF19905E3229862BBF956B3____9C6B6E6E7AADE3A98BBB9505E2AA1D9BF3ABEEB62207237B946440D2451BFA11_FieldInfo_var;
IL2CPP_EXTERN_C RuntimeField* U3CPrivateImplementationDetailsU3E_t7A973F6EE5177382CAF19905E3229862BBF956B3____EC951036C7DAFDDEEECD4BFD154D1FD3C37C066CB53EF1D86675B1C8168003BD_FieldInfo_var;
IL2CPP_EXTERN_C String_t* _stringLiteral01F8EE838110D5B806439591F8F77DDA16B245B7;
IL2CPP_EXTERN_C String_t* _stringLiteral055E2BC6FC0C0670695F60037D4D969590DD5A5E;
IL2CPP_EXTERN_C String_t* _stringLiteral0605457F01C020CA4816186DF7D9D058278904DD;
IL2CPP_EXTERN_C String_t* _stringLiteral07D0259BABE43788E27004165B22CA5CB10BF205;
IL2CPP_EXTERN_C String_t* _stringLiteral0C11FFBAB8AA738FEDA8B2E47FCA289F53F977C8;
IL2CPP_EXTERN_C String_t* _stringLiteral0D4F33DD4748386169AA3A8766BC7E48C978031F;
IL2CPP_EXTERN_C String_t* _stringLiteral2386E77CF610F786B06A91AF2C1B3FD2282D2745;
IL2CPP_EXTERN_C String_t* _stringLiteral25A48893C95318D7E0A508DDFBB672FF3E43922D;
IL2CPP_EXTERN_C String_t* _stringLiteral2D31075CE013917736E287455F9EF8ED0FFF65B9;
IL2CPP_EXTERN_C String_t* _stringLiteral32BC544C06225EE440679BE6646FFFE40FC36433;
IL2CPP_EXTERN_C String_t* _stringLiteral380523DDEA84AFF6A3407002134839D87EE413FA;
IL2CPP_EXTERN_C String_t* _stringLiteral45EE4883CDE67FE6B013C71176B61EE1D2154200;
IL2CPP_EXTERN_C String_t* _stringLiteral47D462D5B38F0A5177ED612304E062E150044C2F;
IL2CPP_EXTERN_C String_t* _stringLiteral480159C4AEE93C93F99B50874C51F41411E5009B;
IL2CPP_EXTERN_C String_t* _stringLiteral579B607FD0BB19369CAE06D0FC1F56ED2D7F85D8;
IL2CPP_EXTERN_C String_t* _stringLiteral6E75A7F6B1D53D770E799FA180B1E92C3BEC8B6B;
IL2CPP_EXTERN_C String_t* _stringLiteral716F6BFC32ABF52915936AA6B0445724BBA4451D;
IL2CPP_EXTERN_C String_t* _stringLiteral71E2151BF75F822D32529B598E65CB2CF534C8BD;
IL2CPP_EXTERN_C String_t* _stringLiteral83179BC4C407F849C68702927986CBA637D88748;
IL2CPP_EXTERN_C String_t* _stringLiteral851F9501FFDEDBC611D5201E456668A28E0C7F4B;
IL2CPP_EXTERN_C String_t* _stringLiteral8649F4FD2D10AA91241D05393F34CFB465556BF8;
IL2CPP_EXTERN_C String_t* _stringLiteral88BC903474F3EEC4A94B28FB209B0CB7F7426FCF;
IL2CPP_EXTERN_C String_t* _stringLiteral8E612BB9EE9052F79F367719C40C1CFBB9BC452F;
IL2CPP_EXTERN_C String_t* _stringLiteral90D06E84F06130A1E294B7100441A92113C7EB43;
IL2CPP_EXTERN_C String_t* _stringLiteral998DFB8DAB26DE673AC25A4F7F4555212C8B8C12;
IL2CPP_EXTERN_C String_t* _stringLiteral9C9DA00C6A4C83135AB9BFE5F7C7405FF12BFDCE;
IL2CPP_EXTERN_C String_t* _stringLiteralA9EE614F0746F6CFA739F4BFC0AD67ACBE293F75;
IL2CPP_EXTERN_C String_t* _stringLiteralB23C3717573626FB4C3C7DF5C19EDE7689837214;
IL2CPP_EXTERN_C String_t* _stringLiteralBCA6F238331EF68017D1ADF8BEADDA03900A1940;
IL2CPP_EXTERN_C String_t* _stringLiteralC37459BA213E8B09FB24FA0927B73EA78BDF7C7D;
IL2CPP_EXTERN_C String_t* _stringLiteralCB414C73F9E54AF3AD8F7CFB654C37B3A3B13925;
IL2CPP_EXTERN_C String_t* _stringLiteralCBDC4FFC23E84CE20BE8F9EDFEFD01BCB121707E;
IL2CPP_EXTERN_C String_t* _stringLiteralCF08D47AA950197FBF22492248D649A8126E68AB;
IL2CPP_EXTERN_C String_t* _stringLiteralD34832E26D345212F1E60233E1603D507C2DB8B1;
IL2CPP_EXTERN_C String_t* _stringLiteralD9E119F3EF845484CED205A9FD0CBD04AAE8D9B2;
IL2CPP_EXTERN_C String_t* _stringLiteralDB784C0FDB5E2F468943EF04EB13EE0E6135421C;
IL2CPP_EXTERN_C String_t* _stringLiteralDEAC03BC08603A926530E878D97B96FA6C0CFEED;
IL2CPP_EXTERN_C String_t* _stringLiteralE0A9DCBA20570641426F54838A643EAB380B2A68;
IL2CPP_EXTERN_C String_t* _stringLiteralE79AE536975E96591357EA5533F095F6E75B5545;
IL2CPP_EXTERN_C String_t* _stringLiteralE7B2D52A08CC1556BCF6FBA714FEF97FFEF5178B;
IL2CPP_EXTERN_C String_t* _stringLiteralFA855702F3E0239B3B0D1F427461543C440733A6;
IL2CPP_EXTERN_C const RuntimeMethod* Component_GetComponent_TisCamera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184_m64AC6C06DD93C5FB249091FEC84FA8475457CCC4_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Component_GetComponent_TisRenderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF_mC91ACC92AD57CA6CA00991DAF1DB3830BCE07AF8_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Enumerator_Dispose_mDF603E9BDFD97F66D220C8F24CA68AEFE7A5E9EF_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Enumerator_MoveNext_mFFBA7CC0534330311AFFABB1789CD7C93F6B86DA_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Enumerator_get_Current_m9972E774645548D767A142EE3C74E157C0AF52FF_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* GameObject_GetComponent_TisCamera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184_m3B3C11550E48AA36AFF82788636EB163CC51FEE6_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1_Add_m926708BBEA1A57FF6B812BFB165466C637F5219D_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1_GetEnumerator_m116DF5C3E10600177E00AD6F58CBEAE4085708DE_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1__ctor_m51867D7C9A5A1C3DE4C23B746BAC6CF661BA1929_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* TCP2_PlanarReflection_BeginCameraRendering_Bultin_m40D10D9F9A22782C50A54511880CC575F7C97655_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* TCP2_PlanarReflection_BeginCameraRendering_URP_m2551C1514321AD13604A589B5EA33ED4D8B53D25_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Vector4_get_Item_mB1D001A235857569E479FB799EF77C52391D19EF_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeType* Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184_0_0_0_var;
struct Delegate_t_marshaled_com;
struct Delegate_t_marshaled_pinvoke;
struct Exception_t_marshaled_com;
struct Exception_t_marshaled_pinvoke;

struct ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031;
struct MaterialU5BU5D_t2B1D11C42DB07A4400C0535F92DBB87A2E346D3D;
struct ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918;
struct SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C;
struct StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248;
struct TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB;

IL2CPP_EXTERN_C_BEGIN
IL2CPP_EXTERN_C_END

#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
struct U3CModuleU3E_t4C259E1B0DA0E8D21F6EC1B2ECC59F714BF95FF9 
{
};
struct List_1_t77EDD3ECA98BCC1B49E3106C8CB923CA87D088ED  : public RuntimeObject
{
	StringU5BU5DU5BU5D_t8BCC500C5CC1686D9BADCBAA811074FE00F83ACF* ____items;
	int32_t ____size;
	int32_t ____version;
	RuntimeObject* ____syncRoot;
};
struct List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D  : public RuntimeObject
{
	ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* ____items;
	int32_t ____size;
	int32_t ____version;
	RuntimeObject* ____syncRoot;
};
struct U3CPrivateImplementationDetailsU3E_t7A973F6EE5177382CAF19905E3229862BBF956B3  : public RuntimeObject
{
};
struct MemberInfo_t  : public RuntimeObject
{
};
struct String_t  : public RuntimeObject
{
	int32_t ____stringLength;
	Il2CppChar ____firstChar;
};
struct TCP2_RuntimeUtils_t5E9598A60187D562FEDFC57BE19768E002F7129C  : public RuntimeObject
{
};
struct UnitySourceGeneratedAssemblyMonoScriptTypes_v1_t41C7444C07A9E4C2804E8968F9F1E0036FBE123F  : public RuntimeObject
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F  : public RuntimeObject
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_pinvoke
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_com
{
};
struct Enumerator_t8BACEFB05E69B10743F619D20823D840EB5736CB 
{
	List_1_t77EDD3ECA98BCC1B49E3106C8CB923CA87D088ED* ____list;
	int32_t ____index;
	int32_t ____version;
	StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* ____current;
};
struct Enumerator_t9473BAB568A27E2339D48C1F91319E0F6D244D7A 
{
	List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D* ____list;
	int32_t ____index;
	int32_t ____version;
	RuntimeObject* ____current;
};
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22 
{
	bool ___m_value;
};
struct Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3 
{
	uint8_t ___m_value;
};
struct Color_tD001788D726C3A7F1379BEED0260B9591F440C1F 
{
	float ___r;
	float ___g;
	float ___b;
	float ___a;
};
struct Double_tE150EF3D1D43DEE85D533810AB4C742307EEDE5F 
{
	double ___m_value;
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2  : public ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F
{
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2_marshaled_pinvoke
{
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2_marshaled_com
{
};
struct Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C 
{
	int32_t ___m_value;
};
struct IntPtr_t 
{
	void* ___m_value;
};
struct LayerMask_t97CB6BDADEDC3D6423C7BCFEA7F86DA2EC6241DB 
{
	int32_t ___m_Mask;
};
struct Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6 
{
	float ___m00;
	float ___m10;
	float ___m20;
	float ___m30;
	float ___m01;
	float ___m11;
	float ___m21;
	float ___m31;
	float ___m02;
	float ___m12;
	float ___m22;
	float ___m32;
	float ___m03;
	float ___m13;
	float ___m23;
	float ___m33;
};
struct Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 
{
	float ___x;
	float ___y;
	float ___z;
	float ___w;
};
struct ShaderTagId_t453E2085B5EE9448FF75E550CAB111EFF690ECB0 
{
	int32_t ___m_Id;
};
struct Single_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C 
{
	float ___m_value;
};
struct Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 
{
	float ___x;
	float ___y;
	float ___z;
};
struct Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 
{
	float ___x;
	float ___y;
	float ___z;
	float ___w;
};
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915 
{
	union
	{
		struct
		{
		};
		uint8_t Void_t4861ACF8F4594C3437BB48B6E56783494B843915__padding[1];
	};
};
#pragma pack(push, tp, 1)
struct __StaticArrayInitTypeSizeU3D293_t4B0546892511A7CE9DCD2F24DC968D5180061614 
{
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D293_t4B0546892511A7CE9DCD2F24DC968D5180061614__padding[293];
	};
};
#pragma pack(pop, tp)
#pragma pack(push, tp, 1)
struct __StaticArrayInitTypeSizeU3D32_t0D3909F9E9BC8434B2DFF282DE9135D1EA9661EC 
{
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D32_t0D3909F9E9BC8434B2DFF282DE9135D1EA9661EC__padding[32];
	};
};
#pragma pack(pop, tp)
#pragma pack(push, tp, 1)
struct __StaticArrayInitTypeSizeU3D455_t3C89AFB0B2BDB5505B782E2678138C662D6A5157 
{
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D455_t3C89AFB0B2BDB5505B782E2678138C662D6A5157__padding[455];
	};
};
#pragma pack(pop, tp)
struct MonoScriptData_tD5D9AFE130B0EEBFBBA78808EF79C5BD3AC36D6F 
{
	ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* ___FilePathsData;
	ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* ___TypesData;
	int32_t ___TotalTypes;
	int32_t ___TotalFiles;
	bool ___IsEditorOnly;
};
struct MonoScriptData_tD5D9AFE130B0EEBFBBA78808EF79C5BD3AC36D6F_marshaled_pinvoke
{
	Il2CppSafeArray* ___FilePathsData;
	Il2CppSafeArray* ___TypesData;
	int32_t ___TotalTypes;
	int32_t ___TotalFiles;
	int32_t ___IsEditorOnly;
};
struct MonoScriptData_tD5D9AFE130B0EEBFBBA78808EF79C5BD3AC36D6F_marshaled_com
{
	Il2CppSafeArray* ___FilePathsData;
	Il2CppSafeArray* ___TypesData;
	int32_t ___TotalTypes;
	int32_t ___TotalFiles;
	int32_t ___IsEditorOnly;
};
struct BuiltinRenderTextureType_t3D56813CAC7C6E4AC3B438039BD1CE7E62FE7C4E 
{
	int32_t ___value__;
};
struct CameraClearFlags_t91B921013F611457A09B92EF9C6B218CECF67202 
{
	int32_t ___value__;
};
struct CameraEvent_tAB9B803B9C806EC265ADB328417F41AD0FBD10FE 
{
	int32_t ___value__;
};
struct CameraType_tCA1017DBE96964E1D967942FB98F152F14121FCD 
{
	int32_t ___value__;
};
struct CommandBuffer_tB56007DC84EF56296C325EC32DD12AC1E3DC91F7  : public RuntimeObject
{
	intptr_t ___m_Ptr;
};
struct CubemapFace_t300D6E2CD7DF60D44AA28338748B607677ED1D1B 
{
	int32_t ___value__;
};
struct Delegate_t  : public RuntimeObject
{
	intptr_t ___method_ptr;
	intptr_t ___invoke_impl;
	RuntimeObject* ___m_target;
	intptr_t ___method;
	intptr_t ___delegate_trampoline;
	intptr_t ___extra_arg;
	intptr_t ___method_code;
	intptr_t ___interp_method;
	intptr_t ___interp_invoke_impl;
	MethodInfo_t* ___method_info;
	MethodInfo_t* ___original_method_info;
	DelegateData_t9B286B493293CD2D23A5B2B5EF0E5B1324C2B77E* ___data;
	bool ___method_is_virtual;
};
struct Delegate_t_marshaled_pinvoke
{
	intptr_t ___method_ptr;
	intptr_t ___invoke_impl;
	Il2CppIUnknown* ___m_target;
	intptr_t ___method;
	intptr_t ___delegate_trampoline;
	intptr_t ___extra_arg;
	intptr_t ___method_code;
	intptr_t ___interp_method;
	intptr_t ___interp_invoke_impl;
	MethodInfo_t* ___method_info;
	MethodInfo_t* ___original_method_info;
	DelegateData_t9B286B493293CD2D23A5B2B5EF0E5B1324C2B77E* ___data;
	int32_t ___method_is_virtual;
};
struct Delegate_t_marshaled_com
{
	intptr_t ___method_ptr;
	intptr_t ___invoke_impl;
	Il2CppIUnknown* ___m_target;
	intptr_t ___method;
	intptr_t ___delegate_trampoline;
	intptr_t ___extra_arg;
	intptr_t ___method_code;
	intptr_t ___interp_method;
	intptr_t ___interp_invoke_impl;
	MethodInfo_t* ___method_info;
	MethodInfo_t* ___original_method_info;
	DelegateData_t9B286B493293CD2D23A5B2B5EF0E5B1324C2B77E* ___data;
	int32_t ___method_is_virtual;
};
struct DepthTextureMode_t64422B6053A3474607EEBAB848B2049ECE39472D 
{
	int32_t ___value__;
};
struct Exception_t  : public RuntimeObject
{
	String_t* ____className;
	String_t* ____message;
	RuntimeObject* ____data;
	Exception_t* ____innerException;
	String_t* ____helpURL;
	RuntimeObject* ____stackTrace;
	String_t* ____stackTraceString;
	String_t* ____remoteStackTraceString;
	int32_t ____remoteStackIndex;
	RuntimeObject* ____dynamicMethods;
	int32_t ____HResult;
	String_t* ____source;
	SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6* ____safeSerializationManager;
	StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF* ___captured_traces;
	IntPtrU5BU5D_tFD177F8C806A6921AD7150264CCC62FA00CAD832* ___native_trace_ips;
	int32_t ___caught_in_unmanaged;
};
struct Exception_t_marshaled_pinvoke
{
	char* ____className;
	char* ____message;
	RuntimeObject* ____data;
	Exception_t_marshaled_pinvoke* ____innerException;
	char* ____helpURL;
	Il2CppIUnknown* ____stackTrace;
	char* ____stackTraceString;
	char* ____remoteStackTraceString;
	int32_t ____remoteStackIndex;
	Il2CppIUnknown* ____dynamicMethods;
	int32_t ____HResult;
	char* ____source;
	SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6* ____safeSerializationManager;
	StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF* ___captured_traces;
	Il2CppSafeArray* ___native_trace_ips;
	int32_t ___caught_in_unmanaged;
};
struct Exception_t_marshaled_com
{
	Il2CppChar* ____className;
	Il2CppChar* ____message;
	RuntimeObject* ____data;
	Exception_t_marshaled_com* ____innerException;
	Il2CppChar* ____helpURL;
	Il2CppIUnknown* ____stackTrace;
	Il2CppChar* ____stackTraceString;
	Il2CppChar* ____remoteStackTraceString;
	int32_t ____remoteStackIndex;
	Il2CppIUnknown* ____dynamicMethods;
	int32_t ____HResult;
	Il2CppChar* ____source;
	SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6* ____safeSerializationManager;
	StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF* ___captured_traces;
	Il2CppSafeArray* ___native_trace_ips;
	int32_t ___caught_in_unmanaged;
};
struct FilterMode_t4AD57F1A3FE272D650E0E688BA044AE872BD2A34 
{
	int32_t ___value__;
};
struct HideFlags_tC514182ACEFD3B847988C45D5DB812FF6DB1BF4A 
{
	int32_t ___value__;
};
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C  : public RuntimeObject
{
	intptr_t ___m_CachedPtr;
};
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_marshaled_pinvoke
{
	intptr_t ___m_CachedPtr;
};
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_marshaled_com
{
	intptr_t ___m_CachedPtr;
};
struct RenderTextureFormat_tB6F1ED5040395B46880CE00312D2FDDBF9EEB40F 
{
	int32_t ___value__;
};
struct RenderTextureReadWrite_t74086C1AE386FE2F1E853FD114ABFAFE68D8B49D 
{
	int32_t ___value__;
};
struct RuntimeFieldHandle_t6E4C45B6D2EA12FC99185805A7E77527899B25C5 
{
	intptr_t ___value;
};
struct RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B 
{
	intptr_t ___value;
};
struct ScriptableRenderContext_t5AB09B3602BEB456E0DC3D53926D3A3BDAF08E36 
{
	intptr_t ___m_Ptr;
};
struct Space_tF043E93E06B702DD05199C28C6F779049B38A969 
{
	int32_t ___value__;
};
struct Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3  : public Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C
{
};
struct GameObject_t76FEDD663AB33C991A9C9A23129337651094216F  : public Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C
{
};
struct Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3  : public Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C
{
};
struct MulticastDelegate_t  : public Delegate_t
{
	DelegateU5BU5D_tC5AB7E8F745616680F337909D3A8E6C722CDF771* ___delegates;
};
struct MulticastDelegate_t_marshaled_pinvoke : public Delegate_t_marshaled_pinvoke
{
	Delegate_t_marshaled_pinvoke** ___delegates;
};
struct MulticastDelegate_t_marshaled_com : public Delegate_t_marshaled_com
{
	Delegate_t_marshaled_com** ___delegates;
};
struct RenderTargetIdentifier_tA528663AC6EB3911D8E91AA40F7070FA5455442B 
{
	int32_t ___m_Type;
	int32_t ___m_NameID;
	int32_t ___m_InstanceID;
	intptr_t ___m_BufferPointer;
	int32_t ___m_MipLevel;
	int32_t ___m_CubeFace;
	int32_t ___m_DepthSlice;
};
struct ScriptableObject_tB3BFDB921A1B1795B38A5417D3B97A89A140436A  : public Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C
{
};
struct ScriptableObject_tB3BFDB921A1B1795B38A5417D3B97A89A140436A_marshaled_pinvoke : public Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_marshaled_pinvoke
{
};
struct ScriptableObject_tB3BFDB921A1B1795B38A5417D3B97A89A140436A_marshaled_com : public Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_marshaled_com
{
};
struct Shader_tADC867D36B7876EE22427FAA2CE485105F4EE692  : public Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C
{
};
struct SystemException_tCC48D868298F4C0705279823E34B00F4FBDB7295  : public Exception_t
{
};
struct Texture_t791CBB51219779964E0E8A2ED7C1AA5F92A4A700  : public Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C
{
};
struct Type_t  : public MemberInfo_t
{
	RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B ____impl;
};
struct Action_2_t8E07914D7090FF200FE84404EEEFAF3CE183C9F3  : public MulticastDelegate_t
{
};
struct Behaviour_t01970CFBBA658497AE30F311C447DB0440BAB7FA  : public Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3
{
};
struct IndexOutOfRangeException_t7ECB35264FB6CA8FAA516BD958F4B2ADC78E8A82  : public SystemException_tCC48D868298F4C0705279823E34B00F4FBDB7295
{
};
struct RenderPipelineAsset_t5F9BF815BF931E1314B184E7F9070FB649C7054E  : public ScriptableObject_tB3BFDB921A1B1795B38A5417D3B97A89A140436A
{
};
struct RenderTexture_tBA90C4C3AD9EECCFDDCC632D97C29FAB80D60D27  : public Texture_t791CBB51219779964E0E8A2ED7C1AA5F92A4A700
{
};
struct Renderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF  : public Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3
{
};
struct Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1  : public Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3
{
};
struct CameraCallback_t844E527BFE37BC0495E7F67993E43C07642DA9DD  : public MulticastDelegate_t
{
};
struct Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184  : public Behaviour_t01970CFBBA658497AE30F311C447DB0440BAB7FA
{
};
struct MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71  : public Behaviour_t01970CFBBA658497AE30F311C447DB0440BAB7FA
{
	CancellationTokenSource_tAAE1E0033BCFC233801F8CB4CED5C852B350CB7B* ___m_CancellationTokenSource;
};
struct TCP2_CameraDepth_t814CEC3D0C44B13A72C4A76F57C47D8885EDF77F  : public MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71
{
	bool ___RenderDepth;
};
struct TCP2_GetPosOnWater_tAEFF9D18028A8FDE5B4697289D69F0E4ABE74973  : public MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71
{
	Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* ___WaterMaterial;
	float ___heightScale;
	bool ___followWaterHeight;
	float ___heightOffset;
	bool ___followWaterNormal;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___upAxis;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___postRotation;
	bool ___isValid;
	int32_t ___sineCount;
	SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* ___sinePosOffsetsX;
	SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* ___sinePosOffsetsZ;
	SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* ___sinePhsOffsetsX;
	SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* ___sinePhsOffsetsZ;
};
struct TCP2_GetVertexWavesPosition_t37CBB31361B597192C56DBCE2DF080524AD535CF  : public MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71
{
	Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* ___WaterPlane;
	bool ___followWaterHeight;
	float ___heightOffset;
	bool ___followWaterNormal;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___upAxis;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___postRotation;
	int32_t ___sineCount;
	float ___WavesSpeed;
	float ___WavesHeight;
	float ___WavesFrequency;
	bool ___useCustomTime;
	bool ___customSineValues;
	Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 ___sinOffsets1;
	Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 ___phaseOffsets1;
	Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 ___sinOffsets2;
	Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 ___phaseOffsets2;
	Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 ___sinOffsets3;
	Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 ___phaseOffsets3;
	Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 ___sinOffsets4;
	Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 ___phaseOffsets4;
};
struct TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5  : public MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71
{
	int32_t ___textureSize;
	int32_t ___renderTextureFormat;
	LayerMask_t97CB6BDADEDC3D6423C7BCFEA7F86DA2EC6241DB ___reflectLayers;
	bool ___disablePixelLights;
	float ___clipPlaneOffset;
	bool ___useCustomBackgroundColor;
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___backgroundColor;
	bool ___applyBlur;
	Shader_tADC867D36B7876EE22427FAA2CE485105F4EE692* ___blurShader;
	int32_t ___blurIterations;
	float ___blurDistance;
	bool ___useBlurDepth;
	float ___blurDepthRange;
	Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* ___reflectionCamera;
	RenderTexture_tBA90C4C3AD9EECCFDDCC632D97C29FAB80D60D27* ___reflectionRenderTexture;
	Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* ___blurMaterial;
	CommandBuffer_tB56007DC84EF56296C325EC32DD12AC1E3DC91F7* ___commandBufferBlur;
	Shader_tADC867D36B7876EE22427FAA2CE485105F4EE692* ___reflectionDepthShader;
	RenderTexture_tBA90C4C3AD9EECCFDDCC632D97C29FAB80D60D27* ___reflectionDepthRenderTexture;
	bool ___isURP;
};
struct TCP2_ShaderUpdateUnityTime_t8BA15EEC76AA4A8461D2A1C5974BB2E0C6874B90  : public MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71
{
};
struct List_1_t77EDD3ECA98BCC1B49E3106C8CB923CA87D088ED_StaticFields
{
	StringU5BU5DU5BU5D_t8BCC500C5CC1686D9BADCBAA811074FE00F83ACF* ___s_emptyArray;
};
struct List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D_StaticFields
{
	ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* ___s_emptyArray;
};
struct U3CPrivateImplementationDetailsU3E_t7A973F6EE5177382CAF19905E3229862BBF956B3_StaticFields
{
	__StaticArrayInitTypeSizeU3D32_t0D3909F9E9BC8434B2DFF282DE9135D1EA9661EC ___1A70BAE12848E0CCADB10009D4920BED5C1135E1D288A0F3D390035604846F38;
	__StaticArrayInitTypeSizeU3D293_t4B0546892511A7CE9DCD2F24DC968D5180061614 ___2D1418505C7F379AAF6D35BFDE539EC766882C689A946F3A02F6ED78DB0BD2B8;
	__StaticArrayInitTypeSizeU3D455_t3C89AFB0B2BDB5505B782E2678138C662D6A5157 ___5A72A641E92E65759A96A7EDA358E6A14AA5375E292DCE75BEAAF46809339171;
	__StaticArrayInitTypeSizeU3D32_t0D3909F9E9BC8434B2DFF282DE9135D1EA9661EC ___89C1CF9324638CEDC10B05C2D337701B9E9B858A3906AC94C72AE20569DF6F7C;
	__StaticArrayInitTypeSizeU3D32_t0D3909F9E9BC8434B2DFF282DE9135D1EA9661EC ___9C6B6E6E7AADE3A98BBB9505E2AA1D9BF3ABEEB62207237B946440D2451BFA11;
	__StaticArrayInitTypeSizeU3D32_t0D3909F9E9BC8434B2DFF282DE9135D1EA9661EC ___EC951036C7DAFDDEEECD4BFD154D1FD3C37C066CB53EF1D86675B1C8168003BD;
};
struct String_t_StaticFields
{
	String_t* ___Empty;
};
struct TCP2_RuntimeUtils_t5E9598A60187D562FEDFC57BE19768E002F7129C_StaticFields
{
	List_1_t77EDD3ECA98BCC1B49E3106C8CB923CA87D088ED* ___ShaderVariants;
};
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_StaticFields
{
	String_t* ___TrueString;
	String_t* ___FalseString;
};
struct IntPtr_t_StaticFields
{
	intptr_t ___Zero;
};
struct Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6_StaticFields
{
	Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6 ___zeroMatrix;
	Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6 ___identityMatrix;
};
struct Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974_StaticFields
{
	Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 ___identityQuaternion;
};
struct Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_StaticFields
{
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___zeroVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___oneVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___upVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___downVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___leftVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___rightVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___forwardVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___backVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___positiveInfinityVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___negativeInfinityVector;
};
struct Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3_StaticFields
{
	Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 ___zeroVector;
	Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 ___oneVector;
	Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 ___positiveInfinityVector;
	Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 ___negativeInfinityVector;
};
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_StaticFields
{
	int32_t ___OffsetOfInstanceIDInCPlusPlusObject;
};
struct ScriptableRenderContext_t5AB09B3602BEB456E0DC3D53926D3A3BDAF08E36_StaticFields
{
	ShaderTagId_t453E2085B5EE9448FF75E550CAB111EFF690ECB0 ___kRenderTypeTag;
};
struct Texture_t791CBB51219779964E0E8A2ED7C1AA5F92A4A700_StaticFields
{
	int32_t ___GenerateAllMips;
};
struct Type_t_StaticFields
{
	Binder_t91BFCE95A7057FADF4D8A1A342AFE52872246235* ___s_defaultBinder;
	Il2CppChar ___Delimiter;
	TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* ___EmptyTypes;
	RuntimeObject* ___Missing;
	MemberFilter_tF644F1AE82F611B677CE1964D5A3277DDA21D553* ___FilterAttribute;
	MemberFilter_tF644F1AE82F611B677CE1964D5A3277DDA21D553* ___FilterName;
	MemberFilter_tF644F1AE82F611B677CE1964D5A3277DDA21D553* ___FilterNameIgnoreCase;
};
struct Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184_StaticFields
{
	CameraCallback_t844E527BFE37BC0495E7F67993E43C07642DA9DD* ___onPreCull;
	CameraCallback_t844E527BFE37BC0495E7F67993E43C07642DA9DD* ___onPreRender;
	CameraCallback_t844E527BFE37BC0495E7F67993E43C07642DA9DD* ___onPostRender;
};
struct TCP2_GetVertexWavesPosition_t37CBB31361B597192C56DBCE2DF080524AD535CF_StaticFields
{
	int32_t ____Time;
	int32_t ___LastFrameTimeSampling;
	float ___ShaderTime;
};
struct TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5_StaticFields
{
	bool ___s_InsideRendering;
	int32_t ____ShaderID_ReflectionTex;
	int32_t ____ShaderID_ReflectionDepthTex;
	int32_t ____ShaderID_ReflectivePlaneY;
	int32_t ____ShaderID_ReflectionDepthRange;
	int32_t ____ShaderID_UseReflectionDepth;
};
struct TCP2_ShaderUpdateUnityTime_t8BA15EEC76AA4A8461D2A1C5974BB2E0C6874B90_StaticFields
{
	int32_t ___UnityTime;
	int32_t ___CustomTime;
};
#ifdef __clang__
#pragma clang diagnostic pop
#endif
struct ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031  : public RuntimeArray
{
	ALIGN_FIELD (8) uint8_t m_Items[1];

	inline uint8_t GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline uint8_t* GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, uint8_t value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
	}
	inline uint8_t GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline uint8_t* GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, uint8_t value)
	{
		m_Items[index] = value;
	}
};
struct SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C  : public RuntimeArray
{
	ALIGN_FIELD (8) float m_Items[1];

	inline float GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline float* GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, float value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
	}
	inline float GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline float* GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, float value)
	{
		m_Items[index] = value;
	}
};
struct TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB  : public RuntimeArray
{
	ALIGN_FIELD (8) Type_t* m_Items[1];

	inline Type_t* GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline Type_t** GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, Type_t* value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
	inline Type_t* GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline Type_t** GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, Type_t* value)
	{
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
};
struct MaterialU5BU5D_t2B1D11C42DB07A4400C0535F92DBB87A2E346D3D  : public RuntimeArray
{
	ALIGN_FIELD (8) Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* m_Items[1];

	inline Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3** GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
	inline Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3** GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* value)
	{
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
};
struct StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248  : public RuntimeArray
{
	ALIGN_FIELD (8) String_t* m_Items[1];

	inline String_t* GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline String_t** GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, String_t* value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
	inline String_t* GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline String_t** GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, String_t* value)
	{
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
};
struct ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918  : public RuntimeArray
{
	ALIGN_FIELD (8) RuntimeObject* m_Items[1];

	inline RuntimeObject* GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline RuntimeObject** GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, RuntimeObject* value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
	inline RuntimeObject* GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline RuntimeObject** GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, RuntimeObject* value)
	{
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
};


IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* Component_GetComponent_TisRuntimeObject_m7181F81CAEC2CF53F5D2BC79B7425C16E1F80D33_gshared (Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Action_2__ctor_m80999490097638177C6B639CEA321424D5BB0991_gshared (Action_2_t38DEBB6BD6AE1CA882236F63F7E1DB3781D38994* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* GameObject_GetComponent_TisRuntimeObject_m6EAED4AA356F0F48288F67899E5958792395563B_gshared (GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Enumerator_t9473BAB568A27E2339D48C1F91319E0F6D244D7A List_1_GetEnumerator_mD8294A7FA2BEB1929487127D476F8EC1CDC23BFC_gshared (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Enumerator_Dispose_mD9DC3E3C3697830A4823047AB29A77DBBB5ED419_gshared (Enumerator_t9473BAB568A27E2339D48C1F91319E0F6D244D7A* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR RuntimeObject* Enumerator_get_Current_m6330F15D18EE4F547C05DF9BF83C5EB710376027_gshared_inline (Enumerator_t9473BAB568A27E2339D48C1F91319E0F6D244D7A* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Enumerator_MoveNext_mE921CC8F29FBBDE7CC3209A0ED0D921D58D00BCB_gshared (Enumerator_t9473BAB568A27E2339D48C1F91319E0F6D244D7A* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void List_1__ctor_m7F078BB342729BDF11327FD89D7872265328F690_gshared (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void List_1_Add_mEBCF994CC3814631017F46A387B1A192ED6C85C7_gshared_inline (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D* __this, RuntimeObject* ___0_item, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_NO_INLINE IL2CPP_METHOD_ATTR void List_1_AddWithResize_m79A9BF770BEF9C06BE40D5401E55E375F2726CC4_gshared (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D* __this, RuntimeObject* ___0_item, const RuntimeMethod* method) ;

IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RuntimeHelpers_InitializeArray_m751372AA3F24FBF6DA9B9D687CBFA2DE436CAB9B (RuntimeArray* ___0_array, RuntimeFieldHandle_t6E4C45B6D2EA12FC99185805A7E77527899B25C5 ___1_fldHandle, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2 (RuntimeObject* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TCP2_CameraDepth_SetCameraDepth_mB3A224FDCFFAC5AB96B190D30BB2B41E0CE51441 (TCP2_CameraDepth_t814CEC3D0C44B13A72C4A76F57C47D8885EDF77F* __this, const RuntimeMethod* method) ;
inline Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* Component_GetComponent_TisCamera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184_m64AC6C06DD93C5FB249091FEC84FA8475457CCC4 (Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3* __this, const RuntimeMethod* method)
{
	return ((  Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* (*) (Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3*, const RuntimeMethod*))Component_GetComponent_TisRuntimeObject_m7181F81CAEC2CF53F5D2BC79B7425C16E1F80D33_gshared)(__this, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Camera_get_depthTextureMode_m998CDEBC055FE2A910F3B650585ADE3E2BB141EE (Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Camera_set_depthTextureMode_mE722389E4DF8B3DF7F6100DB142E4DBAF698F6BF (Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* __this, int32_t ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void MonoBehaviour__ctor_m592DB0105CA0BC97AA1C5F4AD27B12D68A3B7C1E (MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371 (Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Transform_get_position_m69CD5FA214FDAE7BB701552943674846C220FDE1 (Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 TCP2_GetPosOnWater_GetPositionOnWater_mD8E5940462F4FF4F18FF94C754B96068EF77F4CB (TCP2_GetPosOnWater_tAEFF9D18028A8FDE5B4697289D69F0E4ABE74973* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_worldPosition, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Transform_set_position_mA1A817124BB41B685043DED2A9BA48CDF37C4156 (Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 TCP2_GetPosOnWater_GetNormalOnWater_mFCFCCF422D5C271B4DB3D451527ADA2026B0F266 (TCP2_GetPosOnWater_tAEFF9D18028A8FDE5B4697289D69F0E4ABE74973* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_worldPosition, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 Quaternion_FromToRotation_mCB3100F93637E72455388B901C36EF8A25DFDB9A (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_fromDirection, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___1_toDirection, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Transform_set_rotation_m61340DE74726CF0F9946743A727C4D444397331D (Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* __this, Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Transform_Rotate_mAE711E1B1F639FDBA7B456E1E1B35DB90EEB737A (Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_eulers, int32_t ___1_relativeTo, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float Material_GetFloat_m2A77F10E6AA13EA3FA56166EFEA897115A14FA5A (Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* __this, String_t* ___0_name, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float Time_get_time_m3A271BB1B20041144AC5B7863B71AB1F0150374B (const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Vector3__ctor_m376936E6B999EF1ECBE57D990A386303E2283DE0_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* __this, float ___0_x, float ___1_y, float ___2_z, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_get_normalized_m736BBF65D5CDA7A18414370D15B4DFCC1E466F07_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Time_get_frameCount_m4A42E558A71301A216BDC49EC402D62F19C79667 (const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 Shader_GetGlobalVector_m01AC86874807EBB7295C45B1B567B9029B4D2C8C (int32_t ___0_nameID, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 TCP2_GetVertexWavesPosition_GetPositionOnWater_SG2_m3AE19B5EE299D432B9CD08ED2B998E3F3D5F0070 (TCP2_GetVertexWavesPosition_t37CBB31361B597192C56DBCE2DF080524AD535CF* __this, float ___0_time, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___1_worldPosition, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 TCP2_GetVertexWavesPosition_GetNormalOnWater_SG2_m68339EE9562658B60D0FEA84A84D227DB0289992 (TCP2_GetVertexWavesPosition_t37CBB31361B597192C56DBCE2DF080524AD535CF* __this, float ___0_time, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___1_worldPosition, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Vector4__ctor_m96B2CD8B862B271F513AF0BDC2EABD58E4DBC813_inline (Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3* __this, float ___0_x, float ___1_y, float ___2_z, float ___3_w, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Transform_get_lossyScale_mFF740DA4BE1489C6882CD2F3A37B7321176E5D07 (Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 TCP2_GetVertexWavesPosition_CalculateSinePosition_m53AE2DD58DB1AC8DA2A353C178D4F043A6A75D81 (TCP2_GetVertexWavesPosition_t37CBB31361B597192C56DBCE2DF080524AD535CF* __this, float ___0_v1, float ___1_v2, Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 ___2_sinOffsets, Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 ___3_phaseOffsets, float* ___4_phase, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 TCP2_GetVertexWavesPosition_CalculateSineNormal_m2CD1B559FB1B6342C0D37CA3CC5DAB8B5678EFEF (TCP2_GetVertexWavesPosition_t37CBB31361B597192C56DBCE2DF080524AD535CF* __this, float ___0_v1, float ___1_v2, Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 ___2_sinOffsets, Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 ___3_phaseOffsets, float* ___4_phase, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Shader_PropertyToID_mE98523D50F5656CAE89B30695C458253EB8956CA (String_t* ___0_name, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TCP2_PlanarReflection_UpdateRenderTexture_m68EC26E08F46066F16884B9CA041AACCBB6F87E5 (TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TCP2_PlanarReflection_UpdateCommandBuffer_m992291EDB61AEA40C332DA5FC2E53C9E72E58D8F (TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RenderPipelineAsset_t5F9BF815BF931E1314B184E7F9070FB649C7054E* GraphicsSettings_get_currentRenderPipeline_mEC94DC23DE4F901D6A629E2DE882982686AF75F1 (const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Object_op_Inequality_mD0BE578448EAA61948F25C32F8DD55AB1F778602 (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C* ___0_x, Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C* ___1_y, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Type_t* Object_GetType_mE10A8FC1E57F3DF29972CCBC026C2DC3942263B3 (RuntimeObject* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool String_Contains_m6D77B121FADA7CA5F397C0FABB65DA62DF03B6C3 (String_t* __this, String_t* ___0_value, const RuntimeMethod* method) ;
inline void Action_2__ctor_mBEB5B9B513FE305CE98CA8065CC6E6CC0E5A4D51 (Action_2_t8E07914D7090FF200FE84404EEEFAF3CE183C9F3* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method)
{
	((  void (*) (Action_2_t8E07914D7090FF200FE84404EEEFAF3CE183C9F3*, RuntimeObject*, intptr_t, const RuntimeMethod*))Action_2__ctor_m80999490097638177C6B639CEA321424D5BB0991_gshared)(__this, ___0_object, ___1_method, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RenderPipelineManager_add_beginCameraRendering_m44DF94A62BE65F929101983FACE63BA4FE4B584A (Action_2_t8E07914D7090FF200FE84404EEEFAF3CE183C9F3* ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void CameraCallback__ctor_mB48D13F30E749B551E4692E4F2D762C375F62B41 (CameraCallback_t844E527BFE37BC0495E7F67993E43C07642DA9DD* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Delegate_t* Delegate_Combine_m1F725AEF318BE6F0426863490691A6F4606E7D00 (Delegate_t* ___0_a, Delegate_t* ___1_b, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RenderPipelineManager_remove_beginCameraRendering_m6A9B576247B531A6C1C715870A37343AC702976E (Action_2_t8E07914D7090FF200FE84404EEEFAF3CE183C9F3* ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Delegate_t* Delegate_Remove_m8B7DD5661308FA972E23CA1CC3FC9CEB355504E3 (Delegate_t* ___0_source, Delegate_t* ___1_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TCP2_PlanarReflection_ClearCommandBuffer_mE31CD5BC181ECC6CE57A89FE91FC54D7599A40AF (TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TCP2_PlanarReflection_ClearRenderTexture_m6EF08D43A78324DA76B09AB04BB2063D5F7B1B0A (TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RenderTexture__ctor_m68A1B9CAA1BE0B597C5F4895C296E21502D0C962 (RenderTexture_tBA90C4C3AD9EECCFDDCC632D97C29FAB80D60D27* __this, int32_t ___0_width, int32_t ___1_height, int32_t ___2_depth, int32_t ___3_format, int32_t ___4_readWrite, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Object_GetInstanceID_m554FF4073C9465F3835574CC084E68AAEEC6CC6A (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* Int32_ToString_m030E01C24E294D6762FB0B6F37CB541581F55CA5 (int32_t* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* String_Concat_m9E3155FB84015C823606188F53B47CB44C444991 (String_t* ___0_str0, String_t* ___1_str1, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Object_set_name_mC79E6DC8FFD72479C90F0C4CC7F42A0FEAF5AE47 (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C* __this, String_t* ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Object_set_hideFlags_mACB8BFC903FB3B01BBD427753E791BF28B5E33D4 (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C* __this, int32_t ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RenderTexture_Release_mE7399D6187A0E38945D2913D0FFB41247143AB1E (RenderTexture_tBA90C4C3AD9EECCFDDCC632D97C29FAB80D60D27* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Object_DestroyImmediate_m6336EBC83591A5DB64EC70C92132824C6E258705 (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C* ___0_obj, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Object_op_Equality_mB6120F782D83091EF56A198FCEBCF066DB4A9605 (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C* ___0_x, Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C* ___1_y, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* Component_get_gameObject_m57AEFBB14DB39EC476F740BA000E170355DE691B (Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Debug_LogError_m94F967AB31244EACE68C3BE1DD85B69ED3334C0E (RuntimeObject* ___0_message, Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C* ___1_context, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Material__ctor_m7FDF47105D66D19591BE505A0C42B0F90D88C9BF (Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* __this, Shader_tADC867D36B7876EE22427FAA2CE485105F4EE692* ___0_shader, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Material_SetFloat_m879CF81D740BAE6F23C9822400679F4D16365836 (Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* __this, String_t* ___0_name, float ___1_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void CommandBuffer__ctor_m9445F1606331B732FCA393591F3E230714FD5FF4 (CommandBuffer_tB56007DC84EF56296C325EC32DD12AC1E3DC91F7* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t RenderTexture_get_format_m58556ABB91A1FADA8044BEEA2E8C55280768CF35 (RenderTexture_tBA90C4C3AD9EECCFDDCC632D97C29FAB80D60D27* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void CommandBuffer_GetTemporaryRT_m657797522A6EE9B77BD87C6C3E6765F5A3EA2B36 (CommandBuffer_tB56007DC84EF56296C325EC32DD12AC1E3DC91F7* __this, int32_t ___0_nameID, int32_t ___1_width, int32_t ___2_height, int32_t ___3_depthBuffer, int32_t ___4_filter, int32_t ___5_format, int32_t ___6_readWrite, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RenderTargetIdentifier_tA528663AC6EB3911D8E91AA40F7070FA5455442B RenderTargetIdentifier_op_Implicit_mBF13C6AE62DCEDDEFDC1C7305BE646FE99D2F24C (Texture_t791CBB51219779964E0E8A2ED7C1AA5F92A4A700* ___0_tex, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RenderTargetIdentifier_tA528663AC6EB3911D8E91AA40F7070FA5455442B RenderTargetIdentifier_op_Implicit_m5D9E7FF7B325608E3C4A37BBB52FE728361E7324 (int32_t ___0_nameID, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void CommandBuffer_CopyTexture_mDA8B82E30D465C5CC29046C558507601E3CC5BAF (CommandBuffer_tB56007DC84EF56296C325EC32DD12AC1E3DC91F7* __this, RenderTargetIdentifier_tA528663AC6EB3911D8E91AA40F7070FA5455442B ___0_src, RenderTargetIdentifier_tA528663AC6EB3911D8E91AA40F7070FA5455442B ___1_dst, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void CommandBuffer_Blit_m20AC38869B1D9D16C37E1A697B4EF3E2B0D12530 (CommandBuffer_tB56007DC84EF56296C325EC32DD12AC1E3DC91F7* __this, RenderTargetIdentifier_tA528663AC6EB3911D8E91AA40F7070FA5455442B ___0_source, RenderTargetIdentifier_tA528663AC6EB3911D8E91AA40F7070FA5455442B ___1_dest, Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* ___2_mat, int32_t ___3_pass, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void CommandBuffer_Blit_m1F4B01C6274C6A01C710B640735BF0B36BCE73DF (CommandBuffer_tB56007DC84EF56296C325EC32DD12AC1E3DC91F7* __this, Texture_t791CBB51219779964E0E8A2ED7C1AA5F92A4A700* ___0_source, RenderTargetIdentifier_tA528663AC6EB3911D8E91AA40F7070FA5455442B ___1_dest, Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* ___2_mat, int32_t ___3_pass, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void CommandBuffer_ReleaseTemporaryRT_m4651A4B373DF432AA44F06A6F20852ED5996CC8E (CommandBuffer_tB56007DC84EF56296C325EC32DD12AC1E3DC91F7* __this, int32_t ___0_nameID, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Camera_AddCommandBuffer_m2C1C3C2D93CB62D569714B3FFA694CAB9BF81A9A (Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* __this, int32_t ___0_evt, CommandBuffer_tB56007DC84EF56296C325EC32DD12AC1E3DC91F7* ___1_buffer, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Camera_get_commandBufferCount_m168243A3A4E279C57AA6A5D0469B12B675854B50 (Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Camera_RemoveCommandBuffer_m7749BA282C14AA7E9E71A68E911F41D1B8429F11 (Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* __this, int32_t ___0_evt, CommandBuffer_tB56007DC84EF56296C325EC32DD12AC1E3DC91F7* ___1_buffer, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void CommandBuffer_Clear_m4E1272BD1A0C162C9C26434E115279F42FA557C7 (CommandBuffer_tB56007DC84EF56296C325EC32DD12AC1E3DC91F7* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void CommandBuffer_Release_m7D3C99D5B1598F727BD2E0E115FAD6A4E1A123A6 (CommandBuffer_tB56007DC84EF56296C325EC32DD12AC1E3DC91F7* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Camera_get_cameraType_m85434C4C986D2EAC04FBFA44B284840AFC497851 (Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Type_t* Type_GetTypeFromHandle_m6062B81682F79A4D6DF2640692EE6D9987858C57 (RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B ___0_handle, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void GameObject__ctor_m721D643351E55308EA4F5F41B67D5446D11C61F0 (GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* __this, String_t* ___0_name, TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* ___1_components, const RuntimeMethod* method) ;
inline Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* GameObject_GetComponent_TisCamera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184_m3B3C11550E48AA36AFF82788636EB163CC51FEE6 (GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* __this, const RuntimeMethod* method)
{
	return ((  Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* (*) (GameObject_t76FEDD663AB33C991A9C9A23129337651094216F*, const RuntimeMethod*))GameObject_GetComponent_TisRuntimeObject_m6EAED4AA356F0F48288F67899E5958792395563B_gshared)(__this, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Behaviour_set_enabled_mF1DCFE60EB09E0529FE9476CA804A3AA2D72B16A (Behaviour_t01970CFBBA658497AE30F311C447DB0440BAB7FA* __this, bool ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Camera_set_targetTexture_mE6C740F21A72DA47FB5B1D31D208710738A836C4 (Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* __this, RenderTexture_tBA90C4C3AD9EECCFDDCC632D97C29FAB80D60D27* ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TCP2_PlanarReflection_RenderPlanarReflection_mE12045E540118DE967537D65D974D3F5650587E7 (TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5* __this, Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* ___0_worldCamera, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TCP2_PlanarReflection_RenderPlanarReflection_m25DFF719711FC13622E2F44BC6DDB0527B98AD30 (TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5* __this, ScriptableRenderContext_t5AB09B3602BEB456E0DC3D53926D3A3BDAF08E36 ___0_context, Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* ___1_worldCamera, const RuntimeMethod* method) ;
inline Renderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF* Component_GetComponent_TisRenderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF_mC91ACC92AD57CA6CA00991DAF1DB3830BCE07AF8 (Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3* __this, const RuntimeMethod* method)
{
	return ((  Renderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF* (*) (Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3*, const RuntimeMethod*))Component_GetComponent_TisRuntimeObject_m7181F81CAEC2CF53F5D2BC79B7425C16E1F80D33_gshared)(__this, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Behaviour_get_enabled_mAAC9F15E9EBF552217A5AE2681589CC0BFA300C1 (Behaviour_t01970CFBBA658497AE30F311C447DB0440BAB7FA* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Object_op_Implicit_m93896EF7D68FA113C42D3FE2BC6F661FC7EF514A (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C* ___0_exists, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* Renderer_get_sharedMaterial_mA2E0CA0A564617FFC3E0E50947C6300082C35F81 (Renderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Renderer_get_enabled_mFDDF363859AEC88105A925FA7EA341C077B09B54 (Renderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Transform_get_up_mE47A9D9D96422224DD0539AA5524DA5440145BB2 (Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t QualitySettings_get_pixelLightCount_mBB36ED5F47B5841CEF44032058DC4A9815D3F339 (const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void QualitySettings_set_pixelLightCount_mD49EDE3F96CB8D12A0CFD00F8A13179B204762E3 (int32_t ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Camera_CopyFrom_mFA5C3AB8E95EC4124249520ACEC6F7F25E5CDC52 (Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* __this, Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* ___0_other, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Camera_set_clearFlags_m66541D9CC43CBAA5FE7364A50D43CA5569FD4D93 (Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* __this, int32_t ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Camera_set_backgroundColor_m036FD8C316A93A0B168ACC89AFF16D396B872138 (Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* __this, Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___0_value, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Vector3_Dot_mBB86BB940AA0A32FA7D3C02AC42E5BC7095A5D52_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_lhs, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___1_rhs, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6 Matrix4x4_get_zero_m5D5F0475AD231C2C6BE5A9C80E11E24013B1B827 (const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TCP2_PlanarReflection_CalculateReflectionMatrix_m6583F25BD212A78F820579D3C46367BEACA0F755 (Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6* ___0_reflectionMat, Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 ___1_plane, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Matrix4x4_MultiplyPoint_m20E910B65693559BFDE99382472D8DD02C862E7E (Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_point, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6 Camera_get_worldToCameraMatrix_m48E324BD76706A316A1701EFC6A3DEC7DFB2FF40 (Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6 Matrix4x4_op_Multiply_m75E91775655DCA8DFC8EDE0AB787285BB3935162 (Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6 ___0_lhs, Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6 ___1_rhs, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Camera_set_worldToCameraMatrix_mC199F02E435CE7261F7EECD1FD78A33EA96ABC0D (Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* __this, Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6 ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 TCP2_PlanarReflection_CameraSpacePlane_mA6702267FBBA6A838A9E4254F1479407E43FA086 (TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5* __this, Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* ___0_cam, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___1_pos, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___2_normal, float ___3_sideSign, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6 Camera_CalculateObliqueMatrix_mBBFA94C033BB0C3C21B182F732B2155913E46609 (Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* __this, Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 ___0_clipPlane, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Camera_set_projectionMatrix_m351820E6903139402FFFF40221B32D0C52B5A094 (Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* __this, Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6 ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t GameObject_get_layer_m108902B9C89E9F837CE06B9942AA42307450FEAF (GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t LayerMask_get_value_m70CBE32210A1F0FD4ECB850285DA90ED57B87974 (LayerMask_t97CB6BDADEDC3D6423C7BCFEA7F86DA2EC6241DB* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Camera_set_cullingMask_m14F426710530BA8FA53AEC02F79C418AA558CB32 (Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* __this, int32_t ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void GL_set_invertCulling_m832ECFBD8944553346CE28B729756E3815FE7B85 (bool ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Transform_get_eulerAngles_mCAAF48EFCF628F1ED91C2FFE75A4FD19C039DD6A (Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Transform_set_eulerAngles_m9F0BC484A7915A51FAB87230644229B75BACA004 (Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void UniversalRenderPipeline_RenderSingleCamera_mA32C19DAB85E97DADFAB144453EC6CB23A91DB8F (ScriptableRenderContext_t5AB09B3602BEB456E0DC3D53926D3A3BDAF08E36 ___0_context, Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* ___1_camera, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ScriptableRenderContext_ExecuteCommandBuffer_mBAE37DFC699B7167A6E2C59012066C44A31E9896 (ScriptableRenderContext_t5AB09B3602BEB456E0DC3D53926D3A3BDAF08E36* __this, CommandBuffer_tB56007DC84EF56296C325EC32DD12AC1E3DC91F7* ___0_commandBuffer, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Shader_tADC867D36B7876EE22427FAA2CE485105F4EE692* Shader_Find_m183AA54F78320212DDEC811592F98456898A41C5 (String_t* ___0_name, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t TCP2_PlanarReflection_get_ShaderID_ReflectionDepthTex_mA4D9AB261F8369C429B72CD6DC811B331EA5D0D2 (const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Material_SetTexture_mA9F8461850AAB88F992E9C6FA6F24C2E050B83FD (Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* __this, int32_t ___0_nameID, Texture_t791CBB51219779964E0E8A2ED7C1AA5F92A4A700* ___1_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RenderTexture_tBA90C4C3AD9EECCFDDCC632D97C29FAB80D60D27* Camera_get_targetTexture_mC856D7FF8351476068D04E245E4F08F5C56A55BD (Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Camera_RenderWithShader_m3A63E9183D339D1273BD4604DB72E09C9FADA7C4 (Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* __this, Shader_tADC867D36B7876EE22427FAA2CE485105F4EE692* ___0_shader, String_t* ___1_replacementTag, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t TCP2_PlanarReflection_get_ShaderID_ReflectivePlaneY_m5BD4FD60BE5D9E4CDC43CDCF7C7FE46BE5B0D205 (const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Material_SetFloat_m3ECFD92072347A8620254F014865984FA68211A8 (Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* __this, int32_t ___0_nameID, float ___1_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t TCP2_PlanarReflection_get_ShaderID_ReflectionDepthRange_mE7B06E2DCEED01ACE49728291886D9B9E9C00800 (const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t TCP2_PlanarReflection_get_ShaderID_UseReflectionDepth_mBE5B33FC55A2969E12CFE9C348012732A67DB845 (const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Camera_Render_m6089001EB6710DA9A21C87185D65922F13A24509 (Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR MaterialU5BU5D_t2B1D11C42DB07A4400C0535F92DBB87A2E346D3D* Renderer_get_sharedMaterials_m0B61AFD8EDA35A70C796FFB2F28BB62380051ABF (Renderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t TCP2_PlanarReflection_get_ShaderID_ReflectionTex_m56E78BBCE25DAFF87FEE7FD8A2831F918F84B124 (const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Material_HasProperty_m52E2D3BC3049B8B228149E023CD73C34B05A5222 (Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* __this, int32_t ___0_nameID, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_op_Multiply_m87BA7C578F96C8E49BB07088DAAC4649F83B0353_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_a, float ___1_d, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_op_Addition_m78C0EC70CB66E8DCAC225743D82B268DAEE92067_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_a, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___1_b, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Matrix4x4_MultiplyVector_mFD12F86A473E90BBB0002149ABA3917B2A518937 (Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_vector, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Vector4_get_Item_mB1D001A235857569E479FB799EF77C52391D19EF_inline (Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3* __this, int32_t ___0_index, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR LayerMask_t97CB6BDADEDC3D6423C7BCFEA7F86DA2EC6241DB LayerMask_op_Implicit_m01C8996A2CB2085328B9C33539C43139660D8222 (int32_t ___0_intVal, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Color_tD001788D726C3A7F1379BEED0260B9591F440C1F Color_get_black_mB50217951591A045844C61E7FF31EEE3FEF16737_inline (const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Shader_tADC867D36B7876EE22427FAA2CE485105F4EE692* Material_get_shader_m8B0C11AE6F2AD7DE30AF52D3195EB716F7A71983 (Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* Object_get_name_mAC2F6B897CF1303BA4249B4CB55271AFACBB6392 (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* String_ToLower_m6191ABA3DC514ED47C10BDA23FD0DDCEAE7ACFBD (String_t* __this, const RuntimeMethod* method) ;
inline Enumerator_t8BACEFB05E69B10743F619D20823D840EB5736CB List_1_GetEnumerator_m116DF5C3E10600177E00AD6F58CBEAE4085708DE (List_1_t77EDD3ECA98BCC1B49E3106C8CB923CA87D088ED* __this, const RuntimeMethod* method)
{
	return ((  Enumerator_t8BACEFB05E69B10743F619D20823D840EB5736CB (*) (List_1_t77EDD3ECA98BCC1B49E3106C8CB923CA87D088ED*, const RuntimeMethod*))List_1_GetEnumerator_mD8294A7FA2BEB1929487127D476F8EC1CDC23BFC_gshared)(__this, method);
}
inline void Enumerator_Dispose_mDF603E9BDFD97F66D220C8F24CA68AEFE7A5E9EF (Enumerator_t8BACEFB05E69B10743F619D20823D840EB5736CB* __this, const RuntimeMethod* method)
{
	((  void (*) (Enumerator_t8BACEFB05E69B10743F619D20823D840EB5736CB*, const RuntimeMethod*))Enumerator_Dispose_mD9DC3E3C3697830A4823047AB29A77DBBB5ED419_gshared)(__this, method);
}
inline StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* Enumerator_get_Current_m9972E774645548D767A142EE3C74E157C0AF52FF_inline (Enumerator_t8BACEFB05E69B10743F619D20823D840EB5736CB* __this, const RuntimeMethod* method)
{
	return ((  StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* (*) (Enumerator_t8BACEFB05E69B10743F619D20823D840EB5736CB*, const RuntimeMethod*))Enumerator_get_Current_m6330F15D18EE4F547C05DF9BF83C5EB710376027_gshared_inline)(__this, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* Material_get_shaderKeywords_m11982F09EED6BB0A892342E1A72AEA470C44B105 (Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool String_op_Equality_m030E1B219352228970A076136E455C4E568C02C1 (String_t* ___0_a, String_t* ___1_b, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* String_Concat_m8855A6DE10F84DA7F4EC113CADDB59873A25573B (String_t* ___0_str0, String_t* ___1_str1, String_t* ___2_str2, const RuntimeMethod* method) ;
inline bool Enumerator_MoveNext_mFFBA7CC0534330311AFFABB1789CD7C93F6B86DA (Enumerator_t8BACEFB05E69B10743F619D20823D840EB5736CB* __this, const RuntimeMethod* method)
{
	return ((  bool (*) (Enumerator_t8BACEFB05E69B10743F619D20823D840EB5736CB*, const RuntimeMethod*))Enumerator_MoveNext_mE921CC8F29FBBDE7CC3209A0ED0D921D58D00BCB_gshared)(__this, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* String_TrimEnd_m25B1EA658EE07ADFED51FED61D630E5625336AB5 (String_t* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool String_op_Inequality_m8C940F3CFC42866709D7CA931B3D77B4BE94BCB6 (String_t* ___0_a, String_t* ___1_b, const RuntimeMethod* method) ;
inline void List_1__ctor_m51867D7C9A5A1C3DE4C23B746BAC6CF661BA1929 (List_1_t77EDD3ECA98BCC1B49E3106C8CB923CA87D088ED* __this, const RuntimeMethod* method)
{
	((  void (*) (List_1_t77EDD3ECA98BCC1B49E3106C8CB923CA87D088ED*, const RuntimeMethod*))List_1__ctor_m7F078BB342729BDF11327FD89D7872265328F690_gshared)(__this, method);
}
inline void List_1_Add_m926708BBEA1A57FF6B812BFB165466C637F5219D_inline (List_1_t77EDD3ECA98BCC1B49E3106C8CB923CA87D088ED* __this, StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* ___0_item, const RuntimeMethod* method)
{
	((  void (*) (List_1_t77EDD3ECA98BCC1B49E3106C8CB923CA87D088ED*, StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248*, const RuntimeMethod*))List_1_Add_mEBCF994CC3814631017F46A387B1A192ED6C85C7_gshared_inline)(__this, ___0_item, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Shader_SetGlobalFloat_mE7D0DA2B0A62925E093B318785AF82A173794AFC (int32_t ___0_nameID, float ___1_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Shader_SetGlobalVector_mDC5F45B008D44A2C8BF6D450CFE8B58B847C8190 (int32_t ___0_nameID, Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 ___1_value, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_Normalize_mEF8349CC39674236CFC694189AFD36E31F89AC8F_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void IndexOutOfRangeException__ctor_mFD06819F05B815BE2D6E826D4E04F4C449D0A425 (IndexOutOfRangeException_t7ECB35264FB6CA8FAA516BD958F4B2ADC78E8A82* __this, String_t* ___0_message, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Color__ctor_m3786F0D6E510D9CFA544523A955870BD2A514C8C_inline (Color_tD001788D726C3A7F1379BEED0260B9591F440C1F* __this, float ___0_r, float ___1_g, float ___2_b, float ___3_a, const RuntimeMethod* method) ;
inline void List_1_AddWithResize_m79A9BF770BEF9C06BE40D5401E55E375F2726CC4 (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D* __this, RuntimeObject* ___0_item, const RuntimeMethod* method)
{
	((  void (*) (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D*, RuntimeObject*, const RuntimeMethod*))List_1_AddWithResize_m79A9BF770BEF9C06BE40D5401E55E375F2726CC4_gshared)(__this, ___0_item, method);
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Vector3_Magnitude_m21652D951393A3D7CE92CE40049A0E7F76544D1B_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_vector, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_op_Division_mCC6BB24E372AB96B8380D1678446EF6A8BAE13BB_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_a, float ___1_d, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_get_zero_m0C1249C3F25B1C70EAD3CC8B31259975A457AE39_inline (const RuntimeMethod* method) ;
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR MonoScriptData_tD5D9AFE130B0EEBFBBA78808EF79C5BD3AC36D6F UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_mD8D6A4F6D912B7AD3ABB42F6285AAA8BFDC13D9C (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CPrivateImplementationDetailsU3E_t7A973F6EE5177382CAF19905E3229862BBF956B3____2D1418505C7F379AAF6D35BFDE539EC766882C689A946F3A02F6ED78DB0BD2B8_FieldInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CPrivateImplementationDetailsU3E_t7A973F6EE5177382CAF19905E3229862BBF956B3____5A72A641E92E65759A96A7EDA358E6A14AA5375E292DCE75BEAAF46809339171_FieldInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	MonoScriptData_tD5D9AFE130B0EEBFBBA78808EF79C5BD3AC36D6F V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		il2cpp_codegen_initobj((&V_0), sizeof(MonoScriptData_tD5D9AFE130B0EEBFBBA78808EF79C5BD3AC36D6F));
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_0 = (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)SZArrayNew(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031_il2cpp_TypeInfo_var, (uint32_t)((int32_t)455));
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_1 = L_0;
		RuntimeFieldHandle_t6E4C45B6D2EA12FC99185805A7E77527899B25C5 L_2 = { reinterpret_cast<intptr_t> (U3CPrivateImplementationDetailsU3E_t7A973F6EE5177382CAF19905E3229862BBF956B3____5A72A641E92E65759A96A7EDA358E6A14AA5375E292DCE75BEAAF46809339171_FieldInfo_var) };
		RuntimeHelpers_InitializeArray_m751372AA3F24FBF6DA9B9D687CBFA2DE436CAB9B((RuntimeArray*)L_1, L_2, NULL);
		(&V_0)->___FilePathsData = L_1;
		Il2CppCodeGenWriteBarrier((void**)(&(&V_0)->___FilePathsData), (void*)L_1);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_3 = (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)SZArrayNew(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031_il2cpp_TypeInfo_var, (uint32_t)((int32_t)293));
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_4 = L_3;
		RuntimeFieldHandle_t6E4C45B6D2EA12FC99185805A7E77527899B25C5 L_5 = { reinterpret_cast<intptr_t> (U3CPrivateImplementationDetailsU3E_t7A973F6EE5177382CAF19905E3229862BBF956B3____2D1418505C7F379AAF6D35BFDE539EC766882C689A946F3A02F6ED78DB0BD2B8_FieldInfo_var) };
		RuntimeHelpers_InitializeArray_m751372AA3F24FBF6DA9B9D687CBFA2DE436CAB9B((RuntimeArray*)L_4, L_5, NULL);
		(&V_0)->___TypesData = L_4;
		Il2CppCodeGenWriteBarrier((void**)(&(&V_0)->___TypesData), (void*)L_4);
		(&V_0)->___TotalFiles = 6;
		(&V_0)->___TotalTypes = 6;
		(&V_0)->___IsEditorOnly = (bool)0;
		MonoScriptData_tD5D9AFE130B0EEBFBBA78808EF79C5BD3AC36D6F L_6 = V_0;
		return L_6;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_mB2FE215C5975DA0307EE5A8744A1DDB895EA2A18 (UnitySourceGeneratedAssemblyMonoScriptTypes_v1_t41C7444C07A9E4C2804E8968F9F1E0036FBE123F* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C void MonoScriptData_tD5D9AFE130B0EEBFBBA78808EF79C5BD3AC36D6F_marshal_pinvoke(const MonoScriptData_tD5D9AFE130B0EEBFBBA78808EF79C5BD3AC36D6F& unmarshaled, MonoScriptData_tD5D9AFE130B0EEBFBBA78808EF79C5BD3AC36D6F_marshaled_pinvoke& marshaled)
{
	marshaled.___FilePathsData = il2cpp_codegen_com_marshal_safe_array(IL2CPP_VT_I1, unmarshaled.___FilePathsData);
	marshaled.___TypesData = il2cpp_codegen_com_marshal_safe_array(IL2CPP_VT_I1, unmarshaled.___TypesData);
	marshaled.___TotalTypes = unmarshaled.___TotalTypes;
	marshaled.___TotalFiles = unmarshaled.___TotalFiles;
	marshaled.___IsEditorOnly = static_cast<int32_t>(unmarshaled.___IsEditorOnly);
}
IL2CPP_EXTERN_C void MonoScriptData_tD5D9AFE130B0EEBFBBA78808EF79C5BD3AC36D6F_marshal_pinvoke_back(const MonoScriptData_tD5D9AFE130B0EEBFBBA78808EF79C5BD3AC36D6F_marshaled_pinvoke& marshaled, MonoScriptData_tD5D9AFE130B0EEBFBBA78808EF79C5BD3AC36D6F& unmarshaled)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	unmarshaled.___FilePathsData = (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I1, Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, marshaled.___FilePathsData);
	Il2CppCodeGenWriteBarrier((void**)(&unmarshaled.___FilePathsData), (void*)(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I1, Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, marshaled.___FilePathsData));
	unmarshaled.___TypesData = (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I1, Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, marshaled.___TypesData);
	Il2CppCodeGenWriteBarrier((void**)(&unmarshaled.___TypesData), (void*)(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I1, Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, marshaled.___TypesData));
	int32_t unmarshaledTotalTypes_temp_2 = 0;
	unmarshaledTotalTypes_temp_2 = marshaled.___TotalTypes;
	unmarshaled.___TotalTypes = unmarshaledTotalTypes_temp_2;
	int32_t unmarshaledTotalFiles_temp_3 = 0;
	unmarshaledTotalFiles_temp_3 = marshaled.___TotalFiles;
	unmarshaled.___TotalFiles = unmarshaledTotalFiles_temp_3;
	bool unmarshaledIsEditorOnly_temp_4 = false;
	unmarshaledIsEditorOnly_temp_4 = static_cast<bool>(marshaled.___IsEditorOnly);
	unmarshaled.___IsEditorOnly = unmarshaledIsEditorOnly_temp_4;
}
IL2CPP_EXTERN_C void MonoScriptData_tD5D9AFE130B0EEBFBBA78808EF79C5BD3AC36D6F_marshal_pinvoke_cleanup(MonoScriptData_tD5D9AFE130B0EEBFBBA78808EF79C5BD3AC36D6F_marshaled_pinvoke& marshaled)
{
	il2cpp_codegen_com_destroy_safe_array(marshaled.___FilePathsData);
	marshaled.___FilePathsData = NULL;
	il2cpp_codegen_com_destroy_safe_array(marshaled.___TypesData);
	marshaled.___TypesData = NULL;
}
IL2CPP_EXTERN_C void MonoScriptData_tD5D9AFE130B0EEBFBBA78808EF79C5BD3AC36D6F_marshal_com(const MonoScriptData_tD5D9AFE130B0EEBFBBA78808EF79C5BD3AC36D6F& unmarshaled, MonoScriptData_tD5D9AFE130B0EEBFBBA78808EF79C5BD3AC36D6F_marshaled_com& marshaled)
{
	marshaled.___FilePathsData = il2cpp_codegen_com_marshal_safe_array(IL2CPP_VT_I1, unmarshaled.___FilePathsData);
	marshaled.___TypesData = il2cpp_codegen_com_marshal_safe_array(IL2CPP_VT_I1, unmarshaled.___TypesData);
	marshaled.___TotalTypes = unmarshaled.___TotalTypes;
	marshaled.___TotalFiles = unmarshaled.___TotalFiles;
	marshaled.___IsEditorOnly = static_cast<int32_t>(unmarshaled.___IsEditorOnly);
}
IL2CPP_EXTERN_C void MonoScriptData_tD5D9AFE130B0EEBFBBA78808EF79C5BD3AC36D6F_marshal_com_back(const MonoScriptData_tD5D9AFE130B0EEBFBBA78808EF79C5BD3AC36D6F_marshaled_com& marshaled, MonoScriptData_tD5D9AFE130B0EEBFBBA78808EF79C5BD3AC36D6F& unmarshaled)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	unmarshaled.___FilePathsData = (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I1, Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, marshaled.___FilePathsData);
	Il2CppCodeGenWriteBarrier((void**)(&unmarshaled.___FilePathsData), (void*)(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I1, Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, marshaled.___FilePathsData));
	unmarshaled.___TypesData = (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I1, Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, marshaled.___TypesData);
	Il2CppCodeGenWriteBarrier((void**)(&unmarshaled.___TypesData), (void*)(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I1, Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, marshaled.___TypesData));
	int32_t unmarshaledTotalTypes_temp_2 = 0;
	unmarshaledTotalTypes_temp_2 = marshaled.___TotalTypes;
	unmarshaled.___TotalTypes = unmarshaledTotalTypes_temp_2;
	int32_t unmarshaledTotalFiles_temp_3 = 0;
	unmarshaledTotalFiles_temp_3 = marshaled.___TotalFiles;
	unmarshaled.___TotalFiles = unmarshaledTotalFiles_temp_3;
	bool unmarshaledIsEditorOnly_temp_4 = false;
	unmarshaledIsEditorOnly_temp_4 = static_cast<bool>(marshaled.___IsEditorOnly);
	unmarshaled.___IsEditorOnly = unmarshaledIsEditorOnly_temp_4;
}
IL2CPP_EXTERN_C void MonoScriptData_tD5D9AFE130B0EEBFBBA78808EF79C5BD3AC36D6F_marshal_com_cleanup(MonoScriptData_tD5D9AFE130B0EEBFBBA78808EF79C5BD3AC36D6F_marshaled_com& marshaled)
{
	il2cpp_codegen_com_destroy_safe_array(marshaled.___FilePathsData);
	marshaled.___FilePathsData = NULL;
	il2cpp_codegen_com_destroy_safe_array(marshaled.___TypesData);
	marshaled.___TypesData = NULL;
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TCP2_CameraDepth_OnEnable_m2DCBD808E3D7E9E183355EECF13CF0FBB5A637D0 (TCP2_CameraDepth_t814CEC3D0C44B13A72C4A76F57C47D8885EDF77F* __this, const RuntimeMethod* method) 
{
	{
		TCP2_CameraDepth_SetCameraDepth_mB3A224FDCFFAC5AB96B190D30BB2B41E0CE51441(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TCP2_CameraDepth_OnValidate_m3B695760C6EB8D61BAE02AEF84A4AE616F88F20B (TCP2_CameraDepth_t814CEC3D0C44B13A72C4A76F57C47D8885EDF77F* __this, const RuntimeMethod* method) 
{
	{
		TCP2_CameraDepth_SetCameraDepth_mB3A224FDCFFAC5AB96B190D30BB2B41E0CE51441(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TCP2_CameraDepth_SetCameraDepth_mB3A224FDCFFAC5AB96B190D30BB2B41E0CE51441 (TCP2_CameraDepth_t814CEC3D0C44B13A72C4A76F57C47D8885EDF77F* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Component_GetComponent_TisCamera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184_m64AC6C06DD93C5FB249091FEC84FA8475457CCC4_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* V_0 = NULL;
	{
		Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* L_0;
		L_0 = Component_GetComponent_TisCamera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184_m64AC6C06DD93C5FB249091FEC84FA8475457CCC4(__this, Component_GetComponent_TisCamera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184_m64AC6C06DD93C5FB249091FEC84FA8475457CCC4_RuntimeMethod_var);
		V_0 = L_0;
		bool L_1 = __this->___RenderDepth;
		if (!L_1)
		{
			goto IL_001e;
		}
	}
	{
		Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* L_2 = V_0;
		Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* L_3 = L_2;
		NullCheck(L_3);
		int32_t L_4;
		L_4 = Camera_get_depthTextureMode_m998CDEBC055FE2A910F3B650585ADE3E2BB141EE(L_3, NULL);
		NullCheck(L_3);
		Camera_set_depthTextureMode_mE722389E4DF8B3DF7F6100DB142E4DBAF698F6BF(L_3, ((int32_t)((int32_t)L_4|1)), NULL);
		return;
	}

IL_001e:
	{
		Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* L_5 = V_0;
		Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* L_6 = L_5;
		NullCheck(L_6);
		int32_t L_7;
		L_7 = Camera_get_depthTextureMode_m998CDEBC055FE2A910F3B650585ADE3E2BB141EE(L_6, NULL);
		NullCheck(L_6);
		Camera_set_depthTextureMode_mE722389E4DF8B3DF7F6100DB142E4DBAF698F6BF(L_6, ((int32_t)((int32_t)L_7&((int32_t)-2))), NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TCP2_CameraDepth__ctor_mF571A846A30030BB518CC5C990B75BF2728572DF (TCP2_CameraDepth_t814CEC3D0C44B13A72C4A76F57C47D8885EDF77F* __this, const RuntimeMethod* method) 
{
	{
		__this->___RenderDepth = (bool)1;
		MonoBehaviour__ctor_m592DB0105CA0BC97AA1C5F4AD27B12D68A3B7C1E(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TCP2_GetPosOnWater_LateUpdate_m20072EE7F7E759C6B2B5AD7DC958D839CD15E74F (TCP2_GetPosOnWater_tAEFF9D18028A8FDE5B4697289D69F0E4ABE74973* __this, const RuntimeMethod* method) 
{
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		bool L_0 = __this->___followWaterHeight;
		if (!L_0)
		{
			goto IL_0037;
		}
	}
	{
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_1;
		L_1 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		NullCheck(L_1);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_2;
		L_2 = Transform_get_position_m69CD5FA214FDAE7BB701552943674846C220FDE1(L_1, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_3;
		L_3 = TCP2_GetPosOnWater_GetPositionOnWater_mD8E5940462F4FF4F18FF94C754B96068EF77F4CB(__this, L_2, NULL);
		V_0 = L_3;
		float* L_4 = (float*)(&(&V_0)->___y);
		float* L_5 = L_4;
		float L_6 = *((float*)L_5);
		float L_7 = __this->___heightOffset;
		*((float*)L_5) = (float)((float)il2cpp_codegen_add(L_6, L_7));
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_8;
		L_8 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_9 = V_0;
		NullCheck(L_8);
		Transform_set_position_mA1A817124BB41B685043DED2A9BA48CDF37C4156(L_8, L_9, NULL);
	}

IL_0037:
	{
		bool L_10 = __this->___followWaterNormal;
		if (!L_10)
		{
			goto IL_0078;
		}
	}
	{
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_11;
		L_11 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_12 = __this->___upAxis;
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_13;
		L_13 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		NullCheck(L_13);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_14;
		L_14 = Transform_get_position_m69CD5FA214FDAE7BB701552943674846C220FDE1(L_13, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_15;
		L_15 = TCP2_GetPosOnWater_GetNormalOnWater_mFCFCCF422D5C271B4DB3D451527ADA2026B0F266(__this, L_14, NULL);
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_16;
		L_16 = Quaternion_FromToRotation_mCB3100F93637E72455388B901C36EF8A25DFDB9A(L_12, L_15, NULL);
		NullCheck(L_11);
		Transform_set_rotation_m61340DE74726CF0F9946743A727C4D444397331D(L_11, L_16, NULL);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_17;
		L_17 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_18 = __this->___postRotation;
		NullCheck(L_17);
		Transform_Rotate_mAE711E1B1F639FDBA7B456E1E1B35DB90EEB737A(L_17, L_18, 1, NULL);
	}

IL_0078:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 TCP2_GetPosOnWater_GetPositionOnWater_mD8E5940462F4FF4F18FF94C754B96068EF77F4CB (TCP2_GetPosOnWater_tAEFF9D18028A8FDE5B4697289D69F0E4ABE74973* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_worldPosition, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral579B607FD0BB19369CAE06D0FC1F56ED2D7F85D8);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralA9EE614F0746F6CFA739F4BFC0AD67ACBE293F75);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralE79AE536975E96591357EA5533F095F6E75B5545);
		s_Il2CppMethodInitialized = true;
	}
	float V_0 = 0.0f;
	float V_1 = 0.0f;
	float V_2 = 0.0f;
	float V_3 = 0.0f;
	float V_4 = 0.0f;
	float V_5 = 0.0f;
	float V_6 = 0.0f;
	float V_7 = 0.0f;
	int32_t V_8 = 0;
	{
		Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* L_0 = __this->___WaterMaterial;
		NullCheck(L_0);
		float L_1;
		L_1 = Material_GetFloat_m2A77F10E6AA13EA3FA56166EFEA897115A14FA5A(L_0, _stringLiteralA9EE614F0746F6CFA739F4BFC0AD67ACBE293F75, NULL);
		V_0 = L_1;
		Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* L_2 = __this->___WaterMaterial;
		NullCheck(L_2);
		float L_3;
		L_3 = Material_GetFloat_m2A77F10E6AA13EA3FA56166EFEA897115A14FA5A(L_2, _stringLiteralE79AE536975E96591357EA5533F095F6E75B5545, NULL);
		float L_4 = __this->___heightScale;
		V_1 = ((float)il2cpp_codegen_multiply(L_3, L_4));
		Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* L_5 = __this->___WaterMaterial;
		NullCheck(L_5);
		float L_6;
		L_6 = Material_GetFloat_m2A77F10E6AA13EA3FA56166EFEA897115A14FA5A(L_5, _stringLiteral579B607FD0BB19369CAE06D0FC1F56ED2D7F85D8, NULL);
		V_2 = L_6;
		float L_7;
		L_7 = Time_get_time_m3A271BB1B20041144AC5B7863B71AB1F0150374B(NULL);
		float L_8 = V_2;
		V_3 = ((float)il2cpp_codegen_multiply(L_7, L_8));
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_9 = ___0_worldPosition;
		float L_10 = L_9.___x;
		float L_11 = V_0;
		V_4 = ((float)il2cpp_codegen_multiply(L_10, L_11));
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_12 = ___0_worldPosition;
		float L_13 = L_12.___z;
		float L_14 = V_0;
		V_5 = ((float)il2cpp_codegen_multiply(L_13, L_14));
		V_6 = (0.0f);
		V_7 = (0.0f);
		int32_t L_15 = __this->___sineCount;
		V_8 = L_15;
		int32_t L_16 = V_8;
		switch (((int32_t)il2cpp_codegen_subtract(L_16, 1)))
		{
			case 0:
			{
				goto IL_0092;
			}
			case 1:
			{
				goto IL_00b1;
			}
			case 2:
			{
				goto IL_0401;
			}
			case 3:
			{
				goto IL_0138;
			}
		}
	}
	{
		int32_t L_17 = V_8;
		if ((((int32_t)L_17) == ((int32_t)8)))
		{
			goto IL_022f;
		}
	}
	{
		goto IL_0401;
	}

IL_0092:
	{
		float L_18 = V_4;
		float L_19 = V_3;
		float L_20;
		L_20 = sinf(((float)il2cpp_codegen_add(L_18, L_19)));
		float L_21 = V_1;
		V_6 = ((float)il2cpp_codegen_multiply(L_20, L_21));
		float L_22 = V_5;
		float L_23 = V_3;
		float L_24;
		L_24 = sinf(((float)il2cpp_codegen_add(L_22, L_23)));
		float L_25 = V_1;
		V_7 = ((float)il2cpp_codegen_multiply(L_24, L_25));
		goto IL_0401;
	}

IL_00b1:
	{
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_26 = __this->___sinePosOffsetsX;
		NullCheck(L_26);
		int32_t L_27 = 0;
		float L_28 = (L_26)->GetAt(static_cast<il2cpp_array_size_t>(L_27));
		float L_29 = V_4;
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_30 = __this->___sinePhsOffsetsX;
		NullCheck(L_30);
		int32_t L_31 = 0;
		float L_32 = (L_30)->GetAt(static_cast<il2cpp_array_size_t>(L_31));
		float L_33 = V_3;
		float L_34;
		L_34 = sinf(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_28, L_29)), ((float)il2cpp_codegen_multiply(L_32, L_33)))));
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_35 = __this->___sinePosOffsetsX;
		NullCheck(L_35);
		int32_t L_36 = 1;
		float L_37 = (L_35)->GetAt(static_cast<il2cpp_array_size_t>(L_36));
		float L_38 = V_4;
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_39 = __this->___sinePhsOffsetsX;
		NullCheck(L_39);
		int32_t L_40 = 1;
		float L_41 = (L_39)->GetAt(static_cast<il2cpp_array_size_t>(L_40));
		float L_42 = V_3;
		float L_43;
		L_43 = sinf(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_37, L_38)), ((float)il2cpp_codegen_multiply(L_41, L_42)))));
		float L_44 = V_1;
		V_6 = ((float)(((float)il2cpp_codegen_multiply(((float)il2cpp_codegen_add(L_34, L_43)), L_44))/(2.0f)));
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_45 = __this->___sinePosOffsetsZ;
		NullCheck(L_45);
		int32_t L_46 = 0;
		float L_47 = (L_45)->GetAt(static_cast<il2cpp_array_size_t>(L_46));
		float L_48 = V_5;
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_49 = __this->___sinePhsOffsetsZ;
		NullCheck(L_49);
		int32_t L_50 = 0;
		float L_51 = (L_49)->GetAt(static_cast<il2cpp_array_size_t>(L_50));
		float L_52 = V_3;
		float L_53;
		L_53 = sinf(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_47, L_48)), ((float)il2cpp_codegen_multiply(L_51, L_52)))));
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_54 = __this->___sinePosOffsetsZ;
		NullCheck(L_54);
		int32_t L_55 = 1;
		float L_56 = (L_54)->GetAt(static_cast<il2cpp_array_size_t>(L_55));
		float L_57 = V_5;
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_58 = __this->___sinePhsOffsetsZ;
		NullCheck(L_58);
		int32_t L_59 = 1;
		float L_60 = (L_58)->GetAt(static_cast<il2cpp_array_size_t>(L_59));
		float L_61 = V_3;
		float L_62;
		L_62 = sinf(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_56, L_57)), ((float)il2cpp_codegen_multiply(L_60, L_61)))));
		float L_63 = V_1;
		V_7 = ((float)(((float)il2cpp_codegen_multiply(((float)il2cpp_codegen_add(L_53, L_62)), L_63))/(2.0f)));
		goto IL_0401;
	}

IL_0138:
	{
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_64 = __this->___sinePosOffsetsX;
		NullCheck(L_64);
		int32_t L_65 = 0;
		float L_66 = (L_64)->GetAt(static_cast<il2cpp_array_size_t>(L_65));
		float L_67 = V_4;
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_68 = __this->___sinePhsOffsetsX;
		NullCheck(L_68);
		int32_t L_69 = 0;
		float L_70 = (L_68)->GetAt(static_cast<il2cpp_array_size_t>(L_69));
		float L_71 = V_3;
		float L_72;
		L_72 = sinf(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_66, L_67)), ((float)il2cpp_codegen_multiply(L_70, L_71)))));
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_73 = __this->___sinePosOffsetsX;
		NullCheck(L_73);
		int32_t L_74 = 1;
		float L_75 = (L_73)->GetAt(static_cast<il2cpp_array_size_t>(L_74));
		float L_76 = V_4;
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_77 = __this->___sinePhsOffsetsX;
		NullCheck(L_77);
		int32_t L_78 = 1;
		float L_79 = (L_77)->GetAt(static_cast<il2cpp_array_size_t>(L_78));
		float L_80 = V_3;
		float L_81;
		L_81 = sinf(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_75, L_76)), ((float)il2cpp_codegen_multiply(L_79, L_80)))));
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_82 = __this->___sinePosOffsetsX;
		NullCheck(L_82);
		int32_t L_83 = 2;
		float L_84 = (L_82)->GetAt(static_cast<il2cpp_array_size_t>(L_83));
		float L_85 = V_4;
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_86 = __this->___sinePhsOffsetsX;
		NullCheck(L_86);
		int32_t L_87 = 2;
		float L_88 = (L_86)->GetAt(static_cast<il2cpp_array_size_t>(L_87));
		float L_89 = V_3;
		float L_90;
		L_90 = sinf(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_84, L_85)), ((float)il2cpp_codegen_multiply(L_88, L_89)))));
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_91 = __this->___sinePosOffsetsX;
		NullCheck(L_91);
		int32_t L_92 = 3;
		float L_93 = (L_91)->GetAt(static_cast<il2cpp_array_size_t>(L_92));
		float L_94 = V_4;
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_95 = __this->___sinePhsOffsetsX;
		NullCheck(L_95);
		int32_t L_96 = 3;
		float L_97 = (L_95)->GetAt(static_cast<il2cpp_array_size_t>(L_96));
		float L_98 = V_3;
		float L_99;
		L_99 = sinf(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_93, L_94)), ((float)il2cpp_codegen_multiply(L_97, L_98)))));
		float L_100 = V_1;
		V_6 = ((float)(((float)il2cpp_codegen_multiply(((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(L_72, L_81)), L_90)), L_99)), L_100))/(4.0f)));
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_101 = __this->___sinePosOffsetsZ;
		NullCheck(L_101);
		int32_t L_102 = 0;
		float L_103 = (L_101)->GetAt(static_cast<il2cpp_array_size_t>(L_102));
		float L_104 = V_5;
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_105 = __this->___sinePhsOffsetsZ;
		NullCheck(L_105);
		int32_t L_106 = 0;
		float L_107 = (L_105)->GetAt(static_cast<il2cpp_array_size_t>(L_106));
		float L_108 = V_3;
		float L_109;
		L_109 = sinf(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_103, L_104)), ((float)il2cpp_codegen_multiply(L_107, L_108)))));
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_110 = __this->___sinePosOffsetsZ;
		NullCheck(L_110);
		int32_t L_111 = 1;
		float L_112 = (L_110)->GetAt(static_cast<il2cpp_array_size_t>(L_111));
		float L_113 = V_5;
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_114 = __this->___sinePhsOffsetsZ;
		NullCheck(L_114);
		int32_t L_115 = 1;
		float L_116 = (L_114)->GetAt(static_cast<il2cpp_array_size_t>(L_115));
		float L_117 = V_3;
		float L_118;
		L_118 = sinf(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_112, L_113)), ((float)il2cpp_codegen_multiply(L_116, L_117)))));
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_119 = __this->___sinePosOffsetsZ;
		NullCheck(L_119);
		int32_t L_120 = 2;
		float L_121 = (L_119)->GetAt(static_cast<il2cpp_array_size_t>(L_120));
		float L_122 = V_5;
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_123 = __this->___sinePhsOffsetsZ;
		NullCheck(L_123);
		int32_t L_124 = 2;
		float L_125 = (L_123)->GetAt(static_cast<il2cpp_array_size_t>(L_124));
		float L_126 = V_3;
		float L_127;
		L_127 = sinf(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_121, L_122)), ((float)il2cpp_codegen_multiply(L_125, L_126)))));
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_128 = __this->___sinePosOffsetsZ;
		NullCheck(L_128);
		int32_t L_129 = 3;
		float L_130 = (L_128)->GetAt(static_cast<il2cpp_array_size_t>(L_129));
		float L_131 = V_5;
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_132 = __this->___sinePhsOffsetsZ;
		NullCheck(L_132);
		int32_t L_133 = 3;
		float L_134 = (L_132)->GetAt(static_cast<il2cpp_array_size_t>(L_133));
		float L_135 = V_3;
		float L_136;
		L_136 = sinf(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_130, L_131)), ((float)il2cpp_codegen_multiply(L_134, L_135)))));
		float L_137 = V_1;
		V_7 = ((float)(((float)il2cpp_codegen_multiply(((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(L_109, L_118)), L_127)), L_136)), L_137))/(4.0f)));
		goto IL_0401;
	}

IL_022f:
	{
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_138 = __this->___sinePosOffsetsX;
		NullCheck(L_138);
		int32_t L_139 = 0;
		float L_140 = (L_138)->GetAt(static_cast<il2cpp_array_size_t>(L_139));
		float L_141 = V_4;
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_142 = __this->___sinePhsOffsetsX;
		NullCheck(L_142);
		int32_t L_143 = 0;
		float L_144 = (L_142)->GetAt(static_cast<il2cpp_array_size_t>(L_143));
		float L_145 = V_3;
		float L_146;
		L_146 = sinf(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_140, L_141)), ((float)il2cpp_codegen_multiply(L_144, L_145)))));
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_147 = __this->___sinePosOffsetsX;
		NullCheck(L_147);
		int32_t L_148 = 1;
		float L_149 = (L_147)->GetAt(static_cast<il2cpp_array_size_t>(L_148));
		float L_150 = V_4;
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_151 = __this->___sinePhsOffsetsX;
		NullCheck(L_151);
		int32_t L_152 = 1;
		float L_153 = (L_151)->GetAt(static_cast<il2cpp_array_size_t>(L_152));
		float L_154 = V_3;
		float L_155;
		L_155 = sinf(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_149, L_150)), ((float)il2cpp_codegen_multiply(L_153, L_154)))));
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_156 = __this->___sinePosOffsetsX;
		NullCheck(L_156);
		int32_t L_157 = 2;
		float L_158 = (L_156)->GetAt(static_cast<il2cpp_array_size_t>(L_157));
		float L_159 = V_4;
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_160 = __this->___sinePhsOffsetsX;
		NullCheck(L_160);
		int32_t L_161 = 2;
		float L_162 = (L_160)->GetAt(static_cast<il2cpp_array_size_t>(L_161));
		float L_163 = V_3;
		float L_164;
		L_164 = sinf(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_158, L_159)), ((float)il2cpp_codegen_multiply(L_162, L_163)))));
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_165 = __this->___sinePosOffsetsX;
		NullCheck(L_165);
		int32_t L_166 = 3;
		float L_167 = (L_165)->GetAt(static_cast<il2cpp_array_size_t>(L_166));
		float L_168 = V_4;
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_169 = __this->___sinePhsOffsetsX;
		NullCheck(L_169);
		int32_t L_170 = 3;
		float L_171 = (L_169)->GetAt(static_cast<il2cpp_array_size_t>(L_170));
		float L_172 = V_3;
		float L_173;
		L_173 = sinf(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_167, L_168)), ((float)il2cpp_codegen_multiply(L_171, L_172)))));
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_174 = __this->___sinePosOffsetsX;
		NullCheck(L_174);
		int32_t L_175 = 4;
		float L_176 = (L_174)->GetAt(static_cast<il2cpp_array_size_t>(L_175));
		float L_177 = V_4;
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_178 = __this->___sinePhsOffsetsX;
		NullCheck(L_178);
		int32_t L_179 = 4;
		float L_180 = (L_178)->GetAt(static_cast<il2cpp_array_size_t>(L_179));
		float L_181 = V_3;
		float L_182;
		L_182 = sinf(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_176, L_177)), ((float)il2cpp_codegen_multiply(L_180, L_181)))));
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_183 = __this->___sinePosOffsetsX;
		NullCheck(L_183);
		int32_t L_184 = 5;
		float L_185 = (L_183)->GetAt(static_cast<il2cpp_array_size_t>(L_184));
		float L_186 = V_4;
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_187 = __this->___sinePhsOffsetsX;
		NullCheck(L_187);
		int32_t L_188 = 5;
		float L_189 = (L_187)->GetAt(static_cast<il2cpp_array_size_t>(L_188));
		float L_190 = V_3;
		float L_191;
		L_191 = sinf(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_185, L_186)), ((float)il2cpp_codegen_multiply(L_189, L_190)))));
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_192 = __this->___sinePosOffsetsX;
		NullCheck(L_192);
		int32_t L_193 = 6;
		float L_194 = (L_192)->GetAt(static_cast<il2cpp_array_size_t>(L_193));
		float L_195 = V_4;
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_196 = __this->___sinePhsOffsetsX;
		NullCheck(L_196);
		int32_t L_197 = 6;
		float L_198 = (L_196)->GetAt(static_cast<il2cpp_array_size_t>(L_197));
		float L_199 = V_3;
		float L_200;
		L_200 = sinf(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_194, L_195)), ((float)il2cpp_codegen_multiply(L_198, L_199)))));
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_201 = __this->___sinePosOffsetsX;
		NullCheck(L_201);
		int32_t L_202 = 7;
		float L_203 = (L_201)->GetAt(static_cast<il2cpp_array_size_t>(L_202));
		float L_204 = V_4;
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_205 = __this->___sinePhsOffsetsX;
		NullCheck(L_205);
		int32_t L_206 = 7;
		float L_207 = (L_205)->GetAt(static_cast<il2cpp_array_size_t>(L_206));
		float L_208 = V_3;
		float L_209;
		L_209 = sinf(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_203, L_204)), ((float)il2cpp_codegen_multiply(L_207, L_208)))));
		float L_210 = V_1;
		V_6 = ((float)(((float)il2cpp_codegen_multiply(((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(L_146, L_155)), L_164)), L_173)), L_182)), L_191)), L_200)), L_209)), L_210))/(8.0f)));
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_211 = __this->___sinePosOffsetsZ;
		NullCheck(L_211);
		int32_t L_212 = 0;
		float L_213 = (L_211)->GetAt(static_cast<il2cpp_array_size_t>(L_212));
		float L_214 = V_5;
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_215 = __this->___sinePhsOffsetsZ;
		NullCheck(L_215);
		int32_t L_216 = 0;
		float L_217 = (L_215)->GetAt(static_cast<il2cpp_array_size_t>(L_216));
		float L_218 = V_3;
		float L_219;
		L_219 = sinf(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_213, L_214)), ((float)il2cpp_codegen_multiply(L_217, L_218)))));
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_220 = __this->___sinePosOffsetsZ;
		NullCheck(L_220);
		int32_t L_221 = 1;
		float L_222 = (L_220)->GetAt(static_cast<il2cpp_array_size_t>(L_221));
		float L_223 = V_5;
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_224 = __this->___sinePhsOffsetsZ;
		NullCheck(L_224);
		int32_t L_225 = 1;
		float L_226 = (L_224)->GetAt(static_cast<il2cpp_array_size_t>(L_225));
		float L_227 = V_3;
		float L_228;
		L_228 = sinf(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_222, L_223)), ((float)il2cpp_codegen_multiply(L_226, L_227)))));
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_229 = __this->___sinePosOffsetsZ;
		NullCheck(L_229);
		int32_t L_230 = 2;
		float L_231 = (L_229)->GetAt(static_cast<il2cpp_array_size_t>(L_230));
		float L_232 = V_5;
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_233 = __this->___sinePhsOffsetsZ;
		NullCheck(L_233);
		int32_t L_234 = 2;
		float L_235 = (L_233)->GetAt(static_cast<il2cpp_array_size_t>(L_234));
		float L_236 = V_3;
		float L_237;
		L_237 = sinf(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_231, L_232)), ((float)il2cpp_codegen_multiply(L_235, L_236)))));
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_238 = __this->___sinePosOffsetsZ;
		NullCheck(L_238);
		int32_t L_239 = 3;
		float L_240 = (L_238)->GetAt(static_cast<il2cpp_array_size_t>(L_239));
		float L_241 = V_5;
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_242 = __this->___sinePhsOffsetsZ;
		NullCheck(L_242);
		int32_t L_243 = 3;
		float L_244 = (L_242)->GetAt(static_cast<il2cpp_array_size_t>(L_243));
		float L_245 = V_3;
		float L_246;
		L_246 = sinf(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_240, L_241)), ((float)il2cpp_codegen_multiply(L_244, L_245)))));
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_247 = __this->___sinePosOffsetsZ;
		NullCheck(L_247);
		int32_t L_248 = 4;
		float L_249 = (L_247)->GetAt(static_cast<il2cpp_array_size_t>(L_248));
		float L_250 = V_5;
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_251 = __this->___sinePhsOffsetsZ;
		NullCheck(L_251);
		int32_t L_252 = 4;
		float L_253 = (L_251)->GetAt(static_cast<il2cpp_array_size_t>(L_252));
		float L_254 = V_3;
		float L_255;
		L_255 = sinf(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_249, L_250)), ((float)il2cpp_codegen_multiply(L_253, L_254)))));
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_256 = __this->___sinePosOffsetsZ;
		NullCheck(L_256);
		int32_t L_257 = 5;
		float L_258 = (L_256)->GetAt(static_cast<il2cpp_array_size_t>(L_257));
		float L_259 = V_5;
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_260 = __this->___sinePhsOffsetsZ;
		NullCheck(L_260);
		int32_t L_261 = 5;
		float L_262 = (L_260)->GetAt(static_cast<il2cpp_array_size_t>(L_261));
		float L_263 = V_3;
		float L_264;
		L_264 = sinf(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_258, L_259)), ((float)il2cpp_codegen_multiply(L_262, L_263)))));
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_265 = __this->___sinePosOffsetsZ;
		NullCheck(L_265);
		int32_t L_266 = 6;
		float L_267 = (L_265)->GetAt(static_cast<il2cpp_array_size_t>(L_266));
		float L_268 = V_5;
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_269 = __this->___sinePhsOffsetsZ;
		NullCheck(L_269);
		int32_t L_270 = 6;
		float L_271 = (L_269)->GetAt(static_cast<il2cpp_array_size_t>(L_270));
		float L_272 = V_3;
		float L_273;
		L_273 = sinf(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_267, L_268)), ((float)il2cpp_codegen_multiply(L_271, L_272)))));
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_274 = __this->___sinePosOffsetsZ;
		NullCheck(L_274);
		int32_t L_275 = 7;
		float L_276 = (L_274)->GetAt(static_cast<il2cpp_array_size_t>(L_275));
		float L_277 = V_5;
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_278 = __this->___sinePhsOffsetsZ;
		NullCheck(L_278);
		int32_t L_279 = 7;
		float L_280 = (L_278)->GetAt(static_cast<il2cpp_array_size_t>(L_279));
		float L_281 = V_3;
		float L_282;
		L_282 = sinf(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_276, L_277)), ((float)il2cpp_codegen_multiply(L_280, L_281)))));
		float L_283 = V_1;
		V_7 = ((float)(((float)il2cpp_codegen_multiply(((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(L_219, L_228)), L_237)), L_246)), L_255)), L_264)), L_273)), L_282)), L_283))/(8.0f)));
	}

IL_0401:
	{
		float L_284 = V_6;
		float L_285 = V_7;
		(&___0_worldPosition)->___y = ((float)il2cpp_codegen_add(L_284, L_285));
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_286 = ___0_worldPosition;
		return L_286;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 TCP2_GetPosOnWater_GetNormalOnWater_mFCFCCF422D5C271B4DB3D451527ADA2026B0F266 (TCP2_GetPosOnWater_tAEFF9D18028A8FDE5B4697289D69F0E4ABE74973* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_worldPosition, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral579B607FD0BB19369CAE06D0FC1F56ED2D7F85D8);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralA9EE614F0746F6CFA739F4BFC0AD67ACBE293F75);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralE79AE536975E96591357EA5533F095F6E75B5545);
		s_Il2CppMethodInitialized = true;
	}
	float V_0 = 0.0f;
	float V_1 = 0.0f;
	float V_2 = 0.0f;
	float V_3 = 0.0f;
	float V_4 = 0.0f;
	float V_5 = 0.0f;
	float V_6 = 0.0f;
	float V_7 = 0.0f;
	int32_t V_8 = 0;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_9;
	memset((&V_9), 0, sizeof(V_9));
	{
		Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* L_0 = __this->___WaterMaterial;
		NullCheck(L_0);
		float L_1;
		L_1 = Material_GetFloat_m2A77F10E6AA13EA3FA56166EFEA897115A14FA5A(L_0, _stringLiteralA9EE614F0746F6CFA739F4BFC0AD67ACBE293F75, NULL);
		V_0 = L_1;
		Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* L_2 = __this->___WaterMaterial;
		NullCheck(L_2);
		float L_3;
		L_3 = Material_GetFloat_m2A77F10E6AA13EA3FA56166EFEA897115A14FA5A(L_2, _stringLiteralE79AE536975E96591357EA5533F095F6E75B5545, NULL);
		float L_4 = __this->___heightScale;
		V_1 = ((float)il2cpp_codegen_multiply(L_3, L_4));
		Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* L_5 = __this->___WaterMaterial;
		NullCheck(L_5);
		float L_6;
		L_6 = Material_GetFloat_m2A77F10E6AA13EA3FA56166EFEA897115A14FA5A(L_5, _stringLiteral579B607FD0BB19369CAE06D0FC1F56ED2D7F85D8, NULL);
		V_2 = L_6;
		float L_7;
		L_7 = Time_get_time_m3A271BB1B20041144AC5B7863B71AB1F0150374B(NULL);
		float L_8 = V_2;
		V_3 = ((float)il2cpp_codegen_multiply(L_7, L_8));
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_9 = ___0_worldPosition;
		float L_10 = L_9.___x;
		float L_11 = V_0;
		V_4 = ((float)il2cpp_codegen_multiply(L_10, L_11));
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_12 = ___0_worldPosition;
		float L_13 = L_12.___z;
		float L_14 = V_0;
		V_5 = ((float)il2cpp_codegen_multiply(L_13, L_14));
		V_6 = (0.0f);
		V_7 = (0.0f);
		int32_t L_15 = __this->___sineCount;
		V_8 = L_15;
		int32_t L_16 = V_8;
		switch (((int32_t)il2cpp_codegen_subtract(L_16, 1)))
		{
			case 0:
			{
				goto IL_0092;
			}
			case 1:
			{
				goto IL_00b3;
			}
			case 2:
			{
				goto IL_0505;
			}
			case 3:
			{
				goto IL_0160;
			}
		}
	}
	{
		int32_t L_17 = V_8;
		if ((((int32_t)L_17) == ((int32_t)8)))
		{
			goto IL_02a1;
		}
	}
	{
		goto IL_0505;
	}

IL_0092:
	{
		float L_18 = V_1;
		float L_19 = V_4;
		float L_20 = V_3;
		float L_21;
		L_21 = cosf(((float)il2cpp_codegen_add(L_19, L_20)));
		V_6 = ((float)il2cpp_codegen_multiply(((-L_18)), L_21));
		float L_22 = V_1;
		float L_23 = V_5;
		float L_24 = V_3;
		float L_25;
		L_25 = cosf(((float)il2cpp_codegen_add(L_23, L_24)));
		V_7 = ((float)il2cpp_codegen_multiply(((-L_22)), L_25));
		goto IL_0505;
	}

IL_00b3:
	{
		float L_26 = V_1;
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_27 = __this->___sinePosOffsetsX;
		NullCheck(L_27);
		int32_t L_28 = 0;
		float L_29 = (L_27)->GetAt(static_cast<il2cpp_array_size_t>(L_28));
		float L_30 = V_4;
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_31 = __this->___sinePhsOffsetsX;
		NullCheck(L_31);
		int32_t L_32 = 0;
		float L_33 = (L_31)->GetAt(static_cast<il2cpp_array_size_t>(L_32));
		float L_34 = V_3;
		float L_35;
		L_35 = cosf(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_29, L_30)), ((float)il2cpp_codegen_multiply(L_33, L_34)))));
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_36 = __this->___sinePosOffsetsX;
		NullCheck(L_36);
		int32_t L_37 = 0;
		float L_38 = (L_36)->GetAt(static_cast<il2cpp_array_size_t>(L_37));
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_39 = __this->___sinePosOffsetsX;
		NullCheck(L_39);
		int32_t L_40 = 1;
		float L_41 = (L_39)->GetAt(static_cast<il2cpp_array_size_t>(L_40));
		float L_42 = V_4;
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_43 = __this->___sinePhsOffsetsX;
		NullCheck(L_43);
		int32_t L_44 = 1;
		float L_45 = (L_43)->GetAt(static_cast<il2cpp_array_size_t>(L_44));
		float L_46 = V_3;
		float L_47;
		L_47 = cosf(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_41, L_42)), ((float)il2cpp_codegen_multiply(L_45, L_46)))));
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_48 = __this->___sinePosOffsetsX;
		NullCheck(L_48);
		int32_t L_49 = 1;
		float L_50 = (L_48)->GetAt(static_cast<il2cpp_array_size_t>(L_49));
		V_6 = ((float)il2cpp_codegen_multiply(((float)(((-L_26))/(2.0f))), ((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_35, L_38)), ((float)il2cpp_codegen_multiply(L_47, L_50))))));
		float L_51 = V_1;
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_52 = __this->___sinePosOffsetsZ;
		NullCheck(L_52);
		int32_t L_53 = 0;
		float L_54 = (L_52)->GetAt(static_cast<il2cpp_array_size_t>(L_53));
		float L_55 = V_5;
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_56 = __this->___sinePhsOffsetsZ;
		NullCheck(L_56);
		int32_t L_57 = 0;
		float L_58 = (L_56)->GetAt(static_cast<il2cpp_array_size_t>(L_57));
		float L_59 = V_3;
		float L_60;
		L_60 = cosf(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_54, L_55)), ((float)il2cpp_codegen_multiply(L_58, L_59)))));
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_61 = __this->___sinePosOffsetsZ;
		NullCheck(L_61);
		int32_t L_62 = 0;
		float L_63 = (L_61)->GetAt(static_cast<il2cpp_array_size_t>(L_62));
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_64 = __this->___sinePosOffsetsZ;
		NullCheck(L_64);
		int32_t L_65 = 1;
		float L_66 = (L_64)->GetAt(static_cast<il2cpp_array_size_t>(L_65));
		float L_67 = V_5;
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_68 = __this->___sinePhsOffsetsZ;
		NullCheck(L_68);
		int32_t L_69 = 1;
		float L_70 = (L_68)->GetAt(static_cast<il2cpp_array_size_t>(L_69));
		float L_71 = V_3;
		float L_72;
		L_72 = cosf(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_66, L_67)), ((float)il2cpp_codegen_multiply(L_70, L_71)))));
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_73 = __this->___sinePosOffsetsZ;
		NullCheck(L_73);
		int32_t L_74 = 1;
		float L_75 = (L_73)->GetAt(static_cast<il2cpp_array_size_t>(L_74));
		V_7 = ((float)il2cpp_codegen_multiply(((float)(((-L_51))/(2.0f))), ((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_60, L_63)), ((float)il2cpp_codegen_multiply(L_72, L_75))))));
		goto IL_0505;
	}

IL_0160:
	{
		float L_76 = V_1;
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_77 = __this->___sinePosOffsetsX;
		NullCheck(L_77);
		int32_t L_78 = 0;
		float L_79 = (L_77)->GetAt(static_cast<il2cpp_array_size_t>(L_78));
		float L_80 = V_4;
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_81 = __this->___sinePhsOffsetsX;
		NullCheck(L_81);
		int32_t L_82 = 0;
		float L_83 = (L_81)->GetAt(static_cast<il2cpp_array_size_t>(L_82));
		float L_84 = V_3;
		float L_85;
		L_85 = cosf(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_79, L_80)), ((float)il2cpp_codegen_multiply(L_83, L_84)))));
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_86 = __this->___sinePosOffsetsX;
		NullCheck(L_86);
		int32_t L_87 = 0;
		float L_88 = (L_86)->GetAt(static_cast<il2cpp_array_size_t>(L_87));
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_89 = __this->___sinePosOffsetsX;
		NullCheck(L_89);
		int32_t L_90 = 1;
		float L_91 = (L_89)->GetAt(static_cast<il2cpp_array_size_t>(L_90));
		float L_92 = V_4;
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_93 = __this->___sinePhsOffsetsX;
		NullCheck(L_93);
		int32_t L_94 = 1;
		float L_95 = (L_93)->GetAt(static_cast<il2cpp_array_size_t>(L_94));
		float L_96 = V_3;
		float L_97;
		L_97 = cosf(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_91, L_92)), ((float)il2cpp_codegen_multiply(L_95, L_96)))));
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_98 = __this->___sinePosOffsetsX;
		NullCheck(L_98);
		int32_t L_99 = 1;
		float L_100 = (L_98)->GetAt(static_cast<il2cpp_array_size_t>(L_99));
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_101 = __this->___sinePosOffsetsX;
		NullCheck(L_101);
		int32_t L_102 = 2;
		float L_103 = (L_101)->GetAt(static_cast<il2cpp_array_size_t>(L_102));
		float L_104 = V_4;
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_105 = __this->___sinePhsOffsetsX;
		NullCheck(L_105);
		int32_t L_106 = 2;
		float L_107 = (L_105)->GetAt(static_cast<il2cpp_array_size_t>(L_106));
		float L_108 = V_3;
		float L_109;
		L_109 = cosf(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_103, L_104)), ((float)il2cpp_codegen_multiply(L_107, L_108)))));
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_110 = __this->___sinePosOffsetsX;
		NullCheck(L_110);
		int32_t L_111 = 2;
		float L_112 = (L_110)->GetAt(static_cast<il2cpp_array_size_t>(L_111));
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_113 = __this->___sinePosOffsetsX;
		NullCheck(L_113);
		int32_t L_114 = 3;
		float L_115 = (L_113)->GetAt(static_cast<il2cpp_array_size_t>(L_114));
		float L_116 = V_4;
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_117 = __this->___sinePhsOffsetsX;
		NullCheck(L_117);
		int32_t L_118 = 3;
		float L_119 = (L_117)->GetAt(static_cast<il2cpp_array_size_t>(L_118));
		float L_120 = V_3;
		float L_121;
		L_121 = cosf(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_115, L_116)), ((float)il2cpp_codegen_multiply(L_119, L_120)))));
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_122 = __this->___sinePosOffsetsX;
		NullCheck(L_122);
		int32_t L_123 = 3;
		float L_124 = (L_122)->GetAt(static_cast<il2cpp_array_size_t>(L_123));
		V_6 = ((float)il2cpp_codegen_multiply(((float)(((-L_76))/(4.0f))), ((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_85, L_88)), ((float)il2cpp_codegen_multiply(L_97, L_100)))), ((float)il2cpp_codegen_multiply(L_109, L_112)))), ((float)il2cpp_codegen_multiply(L_121, L_124))))));
		float L_125 = V_1;
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_126 = __this->___sinePosOffsetsZ;
		NullCheck(L_126);
		int32_t L_127 = 0;
		float L_128 = (L_126)->GetAt(static_cast<il2cpp_array_size_t>(L_127));
		float L_129 = V_5;
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_130 = __this->___sinePhsOffsetsZ;
		NullCheck(L_130);
		int32_t L_131 = 0;
		float L_132 = (L_130)->GetAt(static_cast<il2cpp_array_size_t>(L_131));
		float L_133 = V_3;
		float L_134;
		L_134 = cosf(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_128, L_129)), ((float)il2cpp_codegen_multiply(L_132, L_133)))));
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_135 = __this->___sinePosOffsetsZ;
		NullCheck(L_135);
		int32_t L_136 = 0;
		float L_137 = (L_135)->GetAt(static_cast<il2cpp_array_size_t>(L_136));
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_138 = __this->___sinePosOffsetsZ;
		NullCheck(L_138);
		int32_t L_139 = 1;
		float L_140 = (L_138)->GetAt(static_cast<il2cpp_array_size_t>(L_139));
		float L_141 = V_5;
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_142 = __this->___sinePhsOffsetsZ;
		NullCheck(L_142);
		int32_t L_143 = 1;
		float L_144 = (L_142)->GetAt(static_cast<il2cpp_array_size_t>(L_143));
		float L_145 = V_3;
		float L_146;
		L_146 = cosf(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_140, L_141)), ((float)il2cpp_codegen_multiply(L_144, L_145)))));
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_147 = __this->___sinePosOffsetsZ;
		NullCheck(L_147);
		int32_t L_148 = 1;
		float L_149 = (L_147)->GetAt(static_cast<il2cpp_array_size_t>(L_148));
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_150 = __this->___sinePosOffsetsZ;
		NullCheck(L_150);
		int32_t L_151 = 2;
		float L_152 = (L_150)->GetAt(static_cast<il2cpp_array_size_t>(L_151));
		float L_153 = V_5;
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_154 = __this->___sinePhsOffsetsZ;
		NullCheck(L_154);
		int32_t L_155 = 2;
		float L_156 = (L_154)->GetAt(static_cast<il2cpp_array_size_t>(L_155));
		float L_157 = V_3;
		float L_158;
		L_158 = cosf(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_152, L_153)), ((float)il2cpp_codegen_multiply(L_156, L_157)))));
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_159 = __this->___sinePosOffsetsZ;
		NullCheck(L_159);
		int32_t L_160 = 2;
		float L_161 = (L_159)->GetAt(static_cast<il2cpp_array_size_t>(L_160));
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_162 = __this->___sinePosOffsetsZ;
		NullCheck(L_162);
		int32_t L_163 = 3;
		float L_164 = (L_162)->GetAt(static_cast<il2cpp_array_size_t>(L_163));
		float L_165 = V_5;
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_166 = __this->___sinePhsOffsetsZ;
		NullCheck(L_166);
		int32_t L_167 = 3;
		float L_168 = (L_166)->GetAt(static_cast<il2cpp_array_size_t>(L_167));
		float L_169 = V_3;
		float L_170;
		L_170 = cosf(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_164, L_165)), ((float)il2cpp_codegen_multiply(L_168, L_169)))));
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_171 = __this->___sinePosOffsetsZ;
		NullCheck(L_171);
		int32_t L_172 = 3;
		float L_173 = (L_171)->GetAt(static_cast<il2cpp_array_size_t>(L_172));
		V_7 = ((float)il2cpp_codegen_multiply(((float)(((-L_125))/(4.0f))), ((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_134, L_137)), ((float)il2cpp_codegen_multiply(L_146, L_149)))), ((float)il2cpp_codegen_multiply(L_158, L_161)))), ((float)il2cpp_codegen_multiply(L_170, L_173))))));
		goto IL_0505;
	}

IL_02a1:
	{
		float L_174 = V_1;
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_175 = __this->___sinePosOffsetsX;
		NullCheck(L_175);
		int32_t L_176 = 0;
		float L_177 = (L_175)->GetAt(static_cast<il2cpp_array_size_t>(L_176));
		float L_178 = V_4;
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_179 = __this->___sinePhsOffsetsX;
		NullCheck(L_179);
		int32_t L_180 = 0;
		float L_181 = (L_179)->GetAt(static_cast<il2cpp_array_size_t>(L_180));
		float L_182 = V_3;
		float L_183;
		L_183 = cosf(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_177, L_178)), ((float)il2cpp_codegen_multiply(L_181, L_182)))));
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_184 = __this->___sinePosOffsetsX;
		NullCheck(L_184);
		int32_t L_185 = 0;
		float L_186 = (L_184)->GetAt(static_cast<il2cpp_array_size_t>(L_185));
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_187 = __this->___sinePosOffsetsX;
		NullCheck(L_187);
		int32_t L_188 = 1;
		float L_189 = (L_187)->GetAt(static_cast<il2cpp_array_size_t>(L_188));
		float L_190 = V_4;
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_191 = __this->___sinePhsOffsetsX;
		NullCheck(L_191);
		int32_t L_192 = 1;
		float L_193 = (L_191)->GetAt(static_cast<il2cpp_array_size_t>(L_192));
		float L_194 = V_3;
		float L_195;
		L_195 = cosf(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_189, L_190)), ((float)il2cpp_codegen_multiply(L_193, L_194)))));
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_196 = __this->___sinePosOffsetsX;
		NullCheck(L_196);
		int32_t L_197 = 1;
		float L_198 = (L_196)->GetAt(static_cast<il2cpp_array_size_t>(L_197));
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_199 = __this->___sinePosOffsetsX;
		NullCheck(L_199);
		int32_t L_200 = 2;
		float L_201 = (L_199)->GetAt(static_cast<il2cpp_array_size_t>(L_200));
		float L_202 = V_4;
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_203 = __this->___sinePhsOffsetsX;
		NullCheck(L_203);
		int32_t L_204 = 2;
		float L_205 = (L_203)->GetAt(static_cast<il2cpp_array_size_t>(L_204));
		float L_206 = V_3;
		float L_207;
		L_207 = cosf(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_201, L_202)), ((float)il2cpp_codegen_multiply(L_205, L_206)))));
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_208 = __this->___sinePosOffsetsX;
		NullCheck(L_208);
		int32_t L_209 = 2;
		float L_210 = (L_208)->GetAt(static_cast<il2cpp_array_size_t>(L_209));
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_211 = __this->___sinePosOffsetsX;
		NullCheck(L_211);
		int32_t L_212 = 3;
		float L_213 = (L_211)->GetAt(static_cast<il2cpp_array_size_t>(L_212));
		float L_214 = V_4;
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_215 = __this->___sinePhsOffsetsX;
		NullCheck(L_215);
		int32_t L_216 = 3;
		float L_217 = (L_215)->GetAt(static_cast<il2cpp_array_size_t>(L_216));
		float L_218 = V_3;
		float L_219;
		L_219 = cosf(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_213, L_214)), ((float)il2cpp_codegen_multiply(L_217, L_218)))));
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_220 = __this->___sinePosOffsetsX;
		NullCheck(L_220);
		int32_t L_221 = 3;
		float L_222 = (L_220)->GetAt(static_cast<il2cpp_array_size_t>(L_221));
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_223 = __this->___sinePosOffsetsX;
		NullCheck(L_223);
		int32_t L_224 = 4;
		float L_225 = (L_223)->GetAt(static_cast<il2cpp_array_size_t>(L_224));
		float L_226 = V_4;
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_227 = __this->___sinePhsOffsetsX;
		NullCheck(L_227);
		int32_t L_228 = 4;
		float L_229 = (L_227)->GetAt(static_cast<il2cpp_array_size_t>(L_228));
		float L_230 = V_3;
		float L_231;
		L_231 = cosf(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_225, L_226)), ((float)il2cpp_codegen_multiply(L_229, L_230)))));
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_232 = __this->___sinePosOffsetsX;
		NullCheck(L_232);
		int32_t L_233 = 4;
		float L_234 = (L_232)->GetAt(static_cast<il2cpp_array_size_t>(L_233));
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_235 = __this->___sinePosOffsetsX;
		NullCheck(L_235);
		int32_t L_236 = 5;
		float L_237 = (L_235)->GetAt(static_cast<il2cpp_array_size_t>(L_236));
		float L_238 = V_4;
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_239 = __this->___sinePhsOffsetsX;
		NullCheck(L_239);
		int32_t L_240 = 5;
		float L_241 = (L_239)->GetAt(static_cast<il2cpp_array_size_t>(L_240));
		float L_242 = V_3;
		float L_243;
		L_243 = cosf(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_237, L_238)), ((float)il2cpp_codegen_multiply(L_241, L_242)))));
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_244 = __this->___sinePosOffsetsX;
		NullCheck(L_244);
		int32_t L_245 = 5;
		float L_246 = (L_244)->GetAt(static_cast<il2cpp_array_size_t>(L_245));
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_247 = __this->___sinePosOffsetsX;
		NullCheck(L_247);
		int32_t L_248 = 6;
		float L_249 = (L_247)->GetAt(static_cast<il2cpp_array_size_t>(L_248));
		float L_250 = V_4;
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_251 = __this->___sinePhsOffsetsX;
		NullCheck(L_251);
		int32_t L_252 = 6;
		float L_253 = (L_251)->GetAt(static_cast<il2cpp_array_size_t>(L_252));
		float L_254 = V_3;
		float L_255;
		L_255 = cosf(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_249, L_250)), ((float)il2cpp_codegen_multiply(L_253, L_254)))));
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_256 = __this->___sinePosOffsetsX;
		NullCheck(L_256);
		int32_t L_257 = 6;
		float L_258 = (L_256)->GetAt(static_cast<il2cpp_array_size_t>(L_257));
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_259 = __this->___sinePosOffsetsX;
		NullCheck(L_259);
		int32_t L_260 = 7;
		float L_261 = (L_259)->GetAt(static_cast<il2cpp_array_size_t>(L_260));
		float L_262 = V_4;
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_263 = __this->___sinePhsOffsetsX;
		NullCheck(L_263);
		int32_t L_264 = 7;
		float L_265 = (L_263)->GetAt(static_cast<il2cpp_array_size_t>(L_264));
		float L_266 = V_3;
		float L_267;
		L_267 = cosf(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_261, L_262)), ((float)il2cpp_codegen_multiply(L_265, L_266)))));
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_268 = __this->___sinePosOffsetsX;
		NullCheck(L_268);
		int32_t L_269 = 7;
		float L_270 = (L_268)->GetAt(static_cast<il2cpp_array_size_t>(L_269));
		V_6 = ((float)il2cpp_codegen_multiply(((float)(((-L_174))/(8.0f))), ((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_183, L_186)), ((float)il2cpp_codegen_multiply(L_195, L_198)))), ((float)il2cpp_codegen_multiply(L_207, L_210)))), ((float)il2cpp_codegen_multiply(L_219, L_222)))), ((float)il2cpp_codegen_multiply(L_231, L_234)))), ((float)il2cpp_codegen_multiply(L_243, L_246)))), ((float)il2cpp_codegen_multiply(L_255, L_258)))), ((float)il2cpp_codegen_multiply(L_267, L_270))))));
		float L_271 = V_1;
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_272 = __this->___sinePosOffsetsZ;
		NullCheck(L_272);
		int32_t L_273 = 0;
		float L_274 = (L_272)->GetAt(static_cast<il2cpp_array_size_t>(L_273));
		float L_275 = V_5;
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_276 = __this->___sinePhsOffsetsZ;
		NullCheck(L_276);
		int32_t L_277 = 0;
		float L_278 = (L_276)->GetAt(static_cast<il2cpp_array_size_t>(L_277));
		float L_279 = V_3;
		float L_280;
		L_280 = cosf(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_274, L_275)), ((float)il2cpp_codegen_multiply(L_278, L_279)))));
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_281 = __this->___sinePosOffsetsZ;
		NullCheck(L_281);
		int32_t L_282 = 0;
		float L_283 = (L_281)->GetAt(static_cast<il2cpp_array_size_t>(L_282));
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_284 = __this->___sinePosOffsetsZ;
		NullCheck(L_284);
		int32_t L_285 = 1;
		float L_286 = (L_284)->GetAt(static_cast<il2cpp_array_size_t>(L_285));
		float L_287 = V_5;
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_288 = __this->___sinePhsOffsetsZ;
		NullCheck(L_288);
		int32_t L_289 = 1;
		float L_290 = (L_288)->GetAt(static_cast<il2cpp_array_size_t>(L_289));
		float L_291 = V_3;
		float L_292;
		L_292 = cosf(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_286, L_287)), ((float)il2cpp_codegen_multiply(L_290, L_291)))));
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_293 = __this->___sinePosOffsetsZ;
		NullCheck(L_293);
		int32_t L_294 = 1;
		float L_295 = (L_293)->GetAt(static_cast<il2cpp_array_size_t>(L_294));
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_296 = __this->___sinePosOffsetsZ;
		NullCheck(L_296);
		int32_t L_297 = 2;
		float L_298 = (L_296)->GetAt(static_cast<il2cpp_array_size_t>(L_297));
		float L_299 = V_5;
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_300 = __this->___sinePhsOffsetsZ;
		NullCheck(L_300);
		int32_t L_301 = 2;
		float L_302 = (L_300)->GetAt(static_cast<il2cpp_array_size_t>(L_301));
		float L_303 = V_3;
		float L_304;
		L_304 = cosf(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_298, L_299)), ((float)il2cpp_codegen_multiply(L_302, L_303)))));
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_305 = __this->___sinePosOffsetsZ;
		NullCheck(L_305);
		int32_t L_306 = 2;
		float L_307 = (L_305)->GetAt(static_cast<il2cpp_array_size_t>(L_306));
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_308 = __this->___sinePosOffsetsZ;
		NullCheck(L_308);
		int32_t L_309 = 3;
		float L_310 = (L_308)->GetAt(static_cast<il2cpp_array_size_t>(L_309));
		float L_311 = V_5;
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_312 = __this->___sinePhsOffsetsZ;
		NullCheck(L_312);
		int32_t L_313 = 3;
		float L_314 = (L_312)->GetAt(static_cast<il2cpp_array_size_t>(L_313));
		float L_315 = V_3;
		float L_316;
		L_316 = cosf(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_310, L_311)), ((float)il2cpp_codegen_multiply(L_314, L_315)))));
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_317 = __this->___sinePosOffsetsZ;
		NullCheck(L_317);
		int32_t L_318 = 3;
		float L_319 = (L_317)->GetAt(static_cast<il2cpp_array_size_t>(L_318));
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_320 = __this->___sinePosOffsetsZ;
		NullCheck(L_320);
		int32_t L_321 = 4;
		float L_322 = (L_320)->GetAt(static_cast<il2cpp_array_size_t>(L_321));
		float L_323 = V_5;
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_324 = __this->___sinePhsOffsetsZ;
		NullCheck(L_324);
		int32_t L_325 = 4;
		float L_326 = (L_324)->GetAt(static_cast<il2cpp_array_size_t>(L_325));
		float L_327 = V_3;
		float L_328;
		L_328 = cosf(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_322, L_323)), ((float)il2cpp_codegen_multiply(L_326, L_327)))));
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_329 = __this->___sinePosOffsetsZ;
		NullCheck(L_329);
		int32_t L_330 = 4;
		float L_331 = (L_329)->GetAt(static_cast<il2cpp_array_size_t>(L_330));
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_332 = __this->___sinePosOffsetsZ;
		NullCheck(L_332);
		int32_t L_333 = 5;
		float L_334 = (L_332)->GetAt(static_cast<il2cpp_array_size_t>(L_333));
		float L_335 = V_5;
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_336 = __this->___sinePhsOffsetsZ;
		NullCheck(L_336);
		int32_t L_337 = 5;
		float L_338 = (L_336)->GetAt(static_cast<il2cpp_array_size_t>(L_337));
		float L_339 = V_3;
		float L_340;
		L_340 = cosf(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_334, L_335)), ((float)il2cpp_codegen_multiply(L_338, L_339)))));
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_341 = __this->___sinePosOffsetsZ;
		NullCheck(L_341);
		int32_t L_342 = 5;
		float L_343 = (L_341)->GetAt(static_cast<il2cpp_array_size_t>(L_342));
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_344 = __this->___sinePosOffsetsZ;
		NullCheck(L_344);
		int32_t L_345 = 6;
		float L_346 = (L_344)->GetAt(static_cast<il2cpp_array_size_t>(L_345));
		float L_347 = V_5;
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_348 = __this->___sinePhsOffsetsZ;
		NullCheck(L_348);
		int32_t L_349 = 6;
		float L_350 = (L_348)->GetAt(static_cast<il2cpp_array_size_t>(L_349));
		float L_351 = V_3;
		float L_352;
		L_352 = cosf(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_346, L_347)), ((float)il2cpp_codegen_multiply(L_350, L_351)))));
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_353 = __this->___sinePosOffsetsZ;
		NullCheck(L_353);
		int32_t L_354 = 6;
		float L_355 = (L_353)->GetAt(static_cast<il2cpp_array_size_t>(L_354));
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_356 = __this->___sinePosOffsetsZ;
		NullCheck(L_356);
		int32_t L_357 = 7;
		float L_358 = (L_356)->GetAt(static_cast<il2cpp_array_size_t>(L_357));
		float L_359 = V_5;
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_360 = __this->___sinePhsOffsetsZ;
		NullCheck(L_360);
		int32_t L_361 = 7;
		float L_362 = (L_360)->GetAt(static_cast<il2cpp_array_size_t>(L_361));
		float L_363 = V_3;
		float L_364;
		L_364 = cosf(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_358, L_359)), ((float)il2cpp_codegen_multiply(L_362, L_363)))));
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_365 = __this->___sinePosOffsetsZ;
		NullCheck(L_365);
		int32_t L_366 = 7;
		float L_367 = (L_365)->GetAt(static_cast<il2cpp_array_size_t>(L_366));
		V_7 = ((float)il2cpp_codegen_multiply(((float)(((-L_271))/(8.0f))), ((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_280, L_283)), ((float)il2cpp_codegen_multiply(L_292, L_295)))), ((float)il2cpp_codegen_multiply(L_304, L_307)))), ((float)il2cpp_codegen_multiply(L_316, L_319)))), ((float)il2cpp_codegen_multiply(L_328, L_331)))), ((float)il2cpp_codegen_multiply(L_340, L_343)))), ((float)il2cpp_codegen_multiply(L_352, L_355)))), ((float)il2cpp_codegen_multiply(L_364, L_367))))));
	}

IL_0505:
	{
		float L_368 = V_6;
		float L_369 = V_7;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_370;
		memset((&L_370), 0, sizeof(L_370));
		Vector3__ctor_m376936E6B999EF1ECBE57D990A386303E2283DE0_inline((&L_370), L_368, (1.0f), L_369, NULL);
		V_9 = L_370;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_371;
		L_371 = Vector3_get_normalized_m736BBF65D5CDA7A18414370D15B4DFCC1E466F07_inline((&V_9), NULL);
		return L_371;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TCP2_GetPosOnWater__ctor_m2AB9931D7BE111125731BE959060CF45965F5F4F (TCP2_GetPosOnWater_tAEFF9D18028A8FDE5B4697289D69F0E4ABE74973* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CPrivateImplementationDetailsU3E_t7A973F6EE5177382CAF19905E3229862BBF956B3____1A70BAE12848E0CCADB10009D4920BED5C1135E1D288A0F3D390035604846F38_FieldInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CPrivateImplementationDetailsU3E_t7A973F6EE5177382CAF19905E3229862BBF956B3____89C1CF9324638CEDC10B05C2D337701B9E9B858A3906AC94C72AE20569DF6F7C_FieldInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CPrivateImplementationDetailsU3E_t7A973F6EE5177382CAF19905E3229862BBF956B3____9C6B6E6E7AADE3A98BBB9505E2AA1D9BF3ABEEB62207237B946440D2451BFA11_FieldInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CPrivateImplementationDetailsU3E_t7A973F6EE5177382CAF19905E3229862BBF956B3____EC951036C7DAFDDEEECD4BFD154D1FD3C37C066CB53EF1D86675B1C8168003BD_FieldInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		__this->___heightScale = (1.0f);
		__this->___followWaterHeight = (bool)1;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0;
		memset((&L_0), 0, sizeof(L_0));
		Vector3__ctor_m376936E6B999EF1ECBE57D990A386303E2283DE0_inline((&L_0), (0.0f), (1.0f), (0.0f), NULL);
		__this->___upAxis = L_0;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_1;
		memset((&L_1), 0, sizeof(L_1));
		Vector3__ctor_m376936E6B999EF1ECBE57D990A386303E2283DE0_inline((&L_1), (0.0f), (0.0f), (0.0f), NULL);
		__this->___postRotation = L_1;
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_2 = (SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C*)(SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C*)SZArrayNew(SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C_il2cpp_TypeInfo_var, (uint32_t)8);
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_3 = L_2;
		RuntimeFieldHandle_t6E4C45B6D2EA12FC99185805A7E77527899B25C5 L_4 = { reinterpret_cast<intptr_t> (U3CPrivateImplementationDetailsU3E_t7A973F6EE5177382CAF19905E3229862BBF956B3____89C1CF9324638CEDC10B05C2D337701B9E9B858A3906AC94C72AE20569DF6F7C_FieldInfo_var) };
		RuntimeHelpers_InitializeArray_m751372AA3F24FBF6DA9B9D687CBFA2DE436CAB9B((RuntimeArray*)L_3, L_4, NULL);
		__this->___sinePosOffsetsX = L_3;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___sinePosOffsetsX), (void*)L_3);
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_5 = (SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C*)(SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C*)SZArrayNew(SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C_il2cpp_TypeInfo_var, (uint32_t)8);
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_6 = L_5;
		RuntimeFieldHandle_t6E4C45B6D2EA12FC99185805A7E77527899B25C5 L_7 = { reinterpret_cast<intptr_t> (U3CPrivateImplementationDetailsU3E_t7A973F6EE5177382CAF19905E3229862BBF956B3____EC951036C7DAFDDEEECD4BFD154D1FD3C37C066CB53EF1D86675B1C8168003BD_FieldInfo_var) };
		RuntimeHelpers_InitializeArray_m751372AA3F24FBF6DA9B9D687CBFA2DE436CAB9B((RuntimeArray*)L_6, L_7, NULL);
		__this->___sinePosOffsetsZ = L_6;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___sinePosOffsetsZ), (void*)L_6);
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_8 = (SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C*)(SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C*)SZArrayNew(SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C_il2cpp_TypeInfo_var, (uint32_t)8);
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_9 = L_8;
		RuntimeFieldHandle_t6E4C45B6D2EA12FC99185805A7E77527899B25C5 L_10 = { reinterpret_cast<intptr_t> (U3CPrivateImplementationDetailsU3E_t7A973F6EE5177382CAF19905E3229862BBF956B3____9C6B6E6E7AADE3A98BBB9505E2AA1D9BF3ABEEB62207237B946440D2451BFA11_FieldInfo_var) };
		RuntimeHelpers_InitializeArray_m751372AA3F24FBF6DA9B9D687CBFA2DE436CAB9B((RuntimeArray*)L_9, L_10, NULL);
		__this->___sinePhsOffsetsX = L_9;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___sinePhsOffsetsX), (void*)L_9);
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_11 = (SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C*)(SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C*)SZArrayNew(SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C_il2cpp_TypeInfo_var, (uint32_t)8);
		SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* L_12 = L_11;
		RuntimeFieldHandle_t6E4C45B6D2EA12FC99185805A7E77527899B25C5 L_13 = { reinterpret_cast<intptr_t> (U3CPrivateImplementationDetailsU3E_t7A973F6EE5177382CAF19905E3229862BBF956B3____1A70BAE12848E0CCADB10009D4920BED5C1135E1D288A0F3D390035604846F38_FieldInfo_var) };
		RuntimeHelpers_InitializeArray_m751372AA3F24FBF6DA9B9D687CBFA2DE436CAB9B((RuntimeArray*)L_12, L_13, NULL);
		__this->___sinePhsOffsetsZ = L_12;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___sinePhsOffsetsZ), (void*)L_12);
		MonoBehaviour__ctor_m592DB0105CA0BC97AA1C5F4AD27B12D68A3B7C1E(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TCP2_GetVertexWavesPosition_LateUpdate_mC364F6282C101FFE2968258946439E008C144F8D (TCP2_GetVertexWavesPosition_t37CBB31361B597192C56DBCE2DF080524AD535CF* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TCP2_GetVertexWavesPosition_t37CBB31361B597192C56DBCE2DF080524AD535CF_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	float V_0 = 0.0f;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_1;
	memset((&V_1), 0, sizeof(V_1));
	{
		bool L_0 = __this->___useCustomTime;
		if (!L_0)
		{
			goto IL_0010;
		}
	}
	{
		float L_1;
		L_1 = Time_get_time_m3A271BB1B20041144AC5B7863B71AB1F0150374B(NULL);
		V_0 = L_1;
		goto IL_0040;
	}

IL_0010:
	{
		il2cpp_codegen_runtime_class_init_inline(TCP2_GetVertexWavesPosition_t37CBB31361B597192C56DBCE2DF080524AD535CF_il2cpp_TypeInfo_var);
		int32_t L_2 = ((TCP2_GetVertexWavesPosition_t37CBB31361B597192C56DBCE2DF080524AD535CF_StaticFields*)il2cpp_codegen_static_fields_for(TCP2_GetVertexWavesPosition_t37CBB31361B597192C56DBCE2DF080524AD535CF_il2cpp_TypeInfo_var))->___LastFrameTimeSampling;
		int32_t L_3;
		L_3 = Time_get_frameCount_m4A42E558A71301A216BDC49EC402D62F19C79667(NULL);
		if ((((int32_t)L_2) >= ((int32_t)L_3)))
		{
			goto IL_003a;
		}
	}
	{
		il2cpp_codegen_runtime_class_init_inline(TCP2_GetVertexWavesPosition_t37CBB31361B597192C56DBCE2DF080524AD535CF_il2cpp_TypeInfo_var);
		int32_t L_4 = ((TCP2_GetVertexWavesPosition_t37CBB31361B597192C56DBCE2DF080524AD535CF_StaticFields*)il2cpp_codegen_static_fields_for(TCP2_GetVertexWavesPosition_t37CBB31361B597192C56DBCE2DF080524AD535CF_il2cpp_TypeInfo_var))->____Time;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_5;
		L_5 = Shader_GetGlobalVector_m01AC86874807EBB7295C45B1B567B9029B4D2C8C(L_4, NULL);
		float L_6 = L_5.___y;
		((TCP2_GetVertexWavesPosition_t37CBB31361B597192C56DBCE2DF080524AD535CF_StaticFields*)il2cpp_codegen_static_fields_for(TCP2_GetVertexWavesPosition_t37CBB31361B597192C56DBCE2DF080524AD535CF_il2cpp_TypeInfo_var))->___ShaderTime = L_6;
		int32_t L_7;
		L_7 = Time_get_frameCount_m4A42E558A71301A216BDC49EC402D62F19C79667(NULL);
		((TCP2_GetVertexWavesPosition_t37CBB31361B597192C56DBCE2DF080524AD535CF_StaticFields*)il2cpp_codegen_static_fields_for(TCP2_GetVertexWavesPosition_t37CBB31361B597192C56DBCE2DF080524AD535CF_il2cpp_TypeInfo_var))->___LastFrameTimeSampling = L_7;
	}

IL_003a:
	{
		il2cpp_codegen_runtime_class_init_inline(TCP2_GetVertexWavesPosition_t37CBB31361B597192C56DBCE2DF080524AD535CF_il2cpp_TypeInfo_var);
		float L_8 = ((TCP2_GetVertexWavesPosition_t37CBB31361B597192C56DBCE2DF080524AD535CF_StaticFields*)il2cpp_codegen_static_fields_for(TCP2_GetVertexWavesPosition_t37CBB31361B597192C56DBCE2DF080524AD535CF_il2cpp_TypeInfo_var))->___ShaderTime;
		V_0 = L_8;
	}

IL_0040:
	{
		bool L_9 = __this->___followWaterHeight;
		if (!L_9)
		{
			goto IL_0067;
		}
	}
	{
		float L_10 = V_0;
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_11;
		L_11 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		NullCheck(L_11);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_12;
		L_12 = Transform_get_position_m69CD5FA214FDAE7BB701552943674846C220FDE1(L_11, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_13;
		L_13 = TCP2_GetVertexWavesPosition_GetPositionOnWater_SG2_m3AE19B5EE299D432B9CD08ED2B998E3F3D5F0070(__this, L_10, L_12, NULL);
		V_1 = L_13;
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_14;
		L_14 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_15 = V_1;
		NullCheck(L_14);
		Transform_set_position_mA1A817124BB41B685043DED2A9BA48CDF37C4156(L_14, L_15, NULL);
	}

IL_0067:
	{
		bool L_16 = __this->___followWaterNormal;
		if (!L_16)
		{
			goto IL_00a9;
		}
	}
	{
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_17;
		L_17 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_18 = __this->___upAxis;
		float L_19 = V_0;
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_20;
		L_20 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		NullCheck(L_20);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_21;
		L_21 = Transform_get_position_m69CD5FA214FDAE7BB701552943674846C220FDE1(L_20, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_22;
		L_22 = TCP2_GetVertexWavesPosition_GetNormalOnWater_SG2_m68339EE9562658B60D0FEA84A84D227DB0289992(__this, L_19, L_21, NULL);
		Quaternion_tDA59F214EF07D7700B26E40E562F267AF7306974 L_23;
		L_23 = Quaternion_FromToRotation_mCB3100F93637E72455388B901C36EF8A25DFDB9A(L_18, L_22, NULL);
		NullCheck(L_17);
		Transform_set_rotation_m61340DE74726CF0F9946743A727C4D444397331D(L_17, L_23, NULL);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_24;
		L_24 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_25 = __this->___postRotation;
		NullCheck(L_24);
		Transform_Rotate_mAE711E1B1F639FDBA7B456E1E1B35DB90EEB737A(L_24, L_25, 1, NULL);
	}

IL_00a9:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 TCP2_GetVertexWavesPosition_CalculateSinePosition_m53AE2DD58DB1AC8DA2A353C178D4F043A6A75D81 (TCP2_GetVertexWavesPosition_t37CBB31361B597192C56DBCE2DF080524AD535CF* __this, float ___0_v1, float ___1_v2, Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 ___2_sinOffsets, Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 ___3_phaseOffsets, float* ___4_phase, const RuntimeMethod* method) 
{
	{
		float L_0 = ___0_v1;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_1 = ___2_sinOffsets;
		float L_2 = L_1.___x;
		float* L_3 = ___4_phase;
		float L_4 = *((float*)L_3);
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_5 = ___3_phaseOffsets;
		float L_6 = L_5.___x;
		float L_7;
		L_7 = sinf(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_0, L_2)), ((float)il2cpp_codegen_multiply(L_4, L_6)))));
		float L_8 = ___0_v1;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_9 = ___2_sinOffsets;
		float L_10 = L_9.___y;
		float* L_11 = ___4_phase;
		float L_12 = *((float*)L_11);
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_13 = ___3_phaseOffsets;
		float L_14 = L_13.___y;
		float L_15;
		L_15 = sinf(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_8, L_10)), ((float)il2cpp_codegen_multiply(L_12, L_14)))));
		float L_16 = ___1_v2;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_17 = ___2_sinOffsets;
		float L_18 = L_17.___z;
		float* L_19 = ___4_phase;
		float L_20 = *((float*)L_19);
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_21 = ___3_phaseOffsets;
		float L_22 = L_21.___z;
		float L_23;
		L_23 = sinf(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_16, L_18)), ((float)il2cpp_codegen_multiply(L_20, L_22)))));
		float L_24 = ___1_v2;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_25 = ___2_sinOffsets;
		float L_26 = L_25.___w;
		float* L_27 = ___4_phase;
		float L_28 = *((float*)L_27);
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_29 = ___3_phaseOffsets;
		float L_30 = L_29.___w;
		float L_31;
		L_31 = sinf(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_24, L_26)), ((float)il2cpp_codegen_multiply(L_28, L_30)))));
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_32;
		memset((&L_32), 0, sizeof(L_32));
		Vector4__ctor_m96B2CD8B862B271F513AF0BDC2EABD58E4DBC813_inline((&L_32), L_7, L_15, L_23, L_31, NULL);
		return L_32;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 TCP2_GetVertexWavesPosition_CalculateSineNormal_m2CD1B559FB1B6342C0D37CA3CC5DAB8B5678EFEF (TCP2_GetVertexWavesPosition_t37CBB31361B597192C56DBCE2DF080524AD535CF* __this, float ___0_v1, float ___1_v2, Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 ___2_sinOffsets, Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 ___3_phaseOffsets, float* ___4_phase, const RuntimeMethod* method) 
{
	{
		float L_0 = ___0_v1;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_1 = ___2_sinOffsets;
		float L_2 = L_1.___x;
		float* L_3 = ___4_phase;
		float L_4 = *((float*)L_3);
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_5 = ___3_phaseOffsets;
		float L_6 = L_5.___x;
		float L_7;
		L_7 = cosf(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_0, L_2)), ((float)il2cpp_codegen_multiply(L_4, L_6)))));
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_8 = ___2_sinOffsets;
		float L_9 = L_8.___x;
		float L_10 = ___0_v1;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_11 = ___2_sinOffsets;
		float L_12 = L_11.___y;
		float* L_13 = ___4_phase;
		float L_14 = *((float*)L_13);
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_15 = ___3_phaseOffsets;
		float L_16 = L_15.___y;
		float L_17;
		L_17 = cosf(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_10, L_12)), ((float)il2cpp_codegen_multiply(L_14, L_16)))));
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_18 = ___2_sinOffsets;
		float L_19 = L_18.___y;
		float L_20 = ___1_v2;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_21 = ___2_sinOffsets;
		float L_22 = L_21.___z;
		float* L_23 = ___4_phase;
		float L_24 = *((float*)L_23);
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_25 = ___3_phaseOffsets;
		float L_26 = L_25.___z;
		float L_27;
		L_27 = cosf(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_20, L_22)), ((float)il2cpp_codegen_multiply(L_24, L_26)))));
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_28 = ___2_sinOffsets;
		float L_29 = L_28.___z;
		float L_30 = ___1_v2;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_31 = ___2_sinOffsets;
		float L_32 = L_31.___w;
		float* L_33 = ___4_phase;
		float L_34 = *((float*)L_33);
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_35 = ___3_phaseOffsets;
		float L_36 = L_35.___w;
		float L_37;
		L_37 = cosf(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_30, L_32)), ((float)il2cpp_codegen_multiply(L_34, L_36)))));
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_38 = ___2_sinOffsets;
		float L_39 = L_38.___w;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_40;
		memset((&L_40), 0, sizeof(L_40));
		Vector4__ctor_m96B2CD8B862B271F513AF0BDC2EABD58E4DBC813_inline((&L_40), ((float)il2cpp_codegen_multiply(L_7, L_9)), ((float)il2cpp_codegen_multiply(L_17, L_19)), ((float)il2cpp_codegen_multiply(L_27, L_29)), ((float)il2cpp_codegen_multiply(L_37, L_39)), NULL);
		return L_40;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 TCP2_GetVertexWavesPosition_GetPositionOnWater_SG2_m3AE19B5EE299D432B9CD08ED2B998E3F3D5F0070 (TCP2_GetVertexWavesPosition_t37CBB31361B597192C56DBCE2DF080524AD535CF* __this, float ___0_time, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___1_worldPosition, const RuntimeMethod* method) 
{
	float V_0 = 0.0f;
	float V_1 = 0.0f;
	float V_2 = 0.0f;
	float V_3 = 0.0f;
	float V_4 = 0.0f;
	float V_5 = 0.0f;
	int32_t V_6 = 0;
	Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 V_7;
	memset((&V_7), 0, sizeof(V_7));
	Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 V_8;
	memset((&V_8), 0, sizeof(V_8));
	Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 V_9;
	memset((&V_9), 0, sizeof(V_9));
	Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 V_10;
	memset((&V_10), 0, sizeof(V_10));
	Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 V_11;
	memset((&V_11), 0, sizeof(V_11));
	Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 V_12;
	memset((&V_12), 0, sizeof(V_12));
	Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 V_13;
	memset((&V_13), 0, sizeof(V_13));
	{
		float L_0 = ___0_time;
		float L_1 = __this->___WavesSpeed;
		V_0 = ((float)il2cpp_codegen_multiply(L_0, L_1));
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_2 = ___1_worldPosition;
		float L_3 = L_2.___x;
		float L_4 = __this->___WavesFrequency;
		V_1 = ((float)il2cpp_codegen_multiply(L_3, L_4));
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_5 = ___1_worldPosition;
		float L_6 = L_5.___z;
		float L_7 = __this->___WavesFrequency;
		V_2 = ((float)il2cpp_codegen_multiply(L_6, L_7));
		float L_8 = __this->___WavesHeight;
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_9 = __this->___WaterPlane;
		NullCheck(L_9);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_10;
		L_10 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(L_9, NULL);
		NullCheck(L_10);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_11;
		L_11 = Transform_get_lossyScale_mFF740DA4BE1489C6882CD2F3A37B7321176E5D07(L_10, NULL);
		float L_12 = L_11.___y;
		V_3 = ((float)il2cpp_codegen_multiply(L_8, L_12));
		V_4 = (0.0f);
		V_5 = (0.0f);
		int32_t L_13 = __this->___sineCount;
		V_6 = L_13;
		int32_t L_14 = V_6;
		switch (((int32_t)il2cpp_codegen_subtract(L_14, 1)))
		{
			case 0:
			{
				goto IL_0248;
			}
			case 1:
			{
				goto IL_007e;
			}
			case 2:
			{
				goto IL_0260;
			}
			case 3:
			{
				goto IL_00cd;
			}
		}
	}
	{
		int32_t L_15 = V_6;
		if ((((int32_t)L_15) == ((int32_t)8)))
		{
			goto IL_0154;
		}
	}
	{
		goto IL_0260;
	}

IL_007e:
	{
		float L_16 = V_1;
		float L_17 = V_2;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_18 = __this->___sinOffsets1;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_19 = __this->___phaseOffsets1;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_20;
		L_20 = TCP2_GetVertexWavesPosition_CalculateSinePosition_m53AE2DD58DB1AC8DA2A353C178D4F043A6A75D81(__this, L_16, L_17, L_18, L_19, (&V_0), NULL);
		V_7 = L_20;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_21 = V_7;
		float L_22 = L_21.___x;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_23 = V_7;
		float L_24 = L_23.___y;
		float L_25 = V_3;
		V_4 = ((float)(((float)il2cpp_codegen_multiply(((float)il2cpp_codegen_add(L_22, L_24)), L_25))/(2.0f)));
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_26 = V_7;
		float L_27 = L_26.___z;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_28 = V_7;
		float L_29 = L_28.___w;
		float L_30 = V_3;
		V_5 = ((float)(((float)il2cpp_codegen_multiply(((float)il2cpp_codegen_add(L_27, L_29)), L_30))/(2.0f)));
		goto IL_0260;
	}

IL_00cd:
	{
		float L_31 = V_1;
		float L_32 = V_1;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_33 = __this->___sinOffsets1;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_34 = __this->___phaseOffsets1;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_35;
		L_35 = TCP2_GetVertexWavesPosition_CalculateSinePosition_m53AE2DD58DB1AC8DA2A353C178D4F043A6A75D81(__this, L_31, L_32, L_33, L_34, (&V_0), NULL);
		V_8 = L_35;
		float L_36 = V_2;
		float L_37 = V_2;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_38 = __this->___sinOffsets2;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_39 = __this->___phaseOffsets2;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_40;
		L_40 = TCP2_GetVertexWavesPosition_CalculateSinePosition_m53AE2DD58DB1AC8DA2A353C178D4F043A6A75D81(__this, L_36, L_37, L_38, L_39, (&V_0), NULL);
		V_9 = L_40;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_41 = V_8;
		float L_42 = L_41.___x;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_43 = V_8;
		float L_44 = L_43.___y;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_45 = V_8;
		float L_46 = L_45.___z;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_47 = V_8;
		float L_48 = L_47.___w;
		float L_49 = V_3;
		V_4 = ((float)(((float)il2cpp_codegen_multiply(((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(L_42, L_44)), L_46)), L_48)), L_49))/(4.0f)));
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_50 = V_9;
		float L_51 = L_50.___x;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_52 = V_9;
		float L_53 = L_52.___y;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_54 = V_9;
		float L_55 = L_54.___z;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_56 = V_9;
		float L_57 = L_56.___w;
		float L_58 = V_3;
		V_5 = ((float)(((float)il2cpp_codegen_multiply(((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(L_51, L_53)), L_55)), L_57)), L_58))/(4.0f)));
		goto IL_0260;
	}

IL_0154:
	{
		float L_59 = V_1;
		float L_60 = V_1;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_61 = __this->___sinOffsets1;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_62 = __this->___phaseOffsets1;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_63;
		L_63 = TCP2_GetVertexWavesPosition_CalculateSinePosition_m53AE2DD58DB1AC8DA2A353C178D4F043A6A75D81(__this, L_59, L_60, L_61, L_62, (&V_0), NULL);
		V_10 = L_63;
		float L_64 = V_2;
		float L_65 = V_2;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_66 = __this->___sinOffsets2;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_67 = __this->___phaseOffsets2;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_68;
		L_68 = TCP2_GetVertexWavesPosition_CalculateSinePosition_m53AE2DD58DB1AC8DA2A353C178D4F043A6A75D81(__this, L_64, L_65, L_66, L_67, (&V_0), NULL);
		V_11 = L_68;
		float L_69 = V_1;
		float L_70 = V_1;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_71 = __this->___sinOffsets3;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_72 = __this->___phaseOffsets3;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_73;
		L_73 = TCP2_GetVertexWavesPosition_CalculateSinePosition_m53AE2DD58DB1AC8DA2A353C178D4F043A6A75D81(__this, L_69, L_70, L_71, L_72, (&V_0), NULL);
		V_12 = L_73;
		float L_74 = V_2;
		float L_75 = V_2;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_76 = __this->___sinOffsets4;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_77 = __this->___phaseOffsets4;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_78;
		L_78 = TCP2_GetVertexWavesPosition_CalculateSinePosition_m53AE2DD58DB1AC8DA2A353C178D4F043A6A75D81(__this, L_74, L_75, L_76, L_77, (&V_0), NULL);
		V_13 = L_78;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_79 = V_10;
		float L_80 = L_79.___x;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_81 = V_10;
		float L_82 = L_81.___y;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_83 = V_10;
		float L_84 = L_83.___z;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_85 = V_10;
		float L_86 = L_85.___w;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_87 = V_12;
		float L_88 = L_87.___x;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_89 = V_12;
		float L_90 = L_89.___y;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_91 = V_12;
		float L_92 = L_91.___z;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_93 = V_12;
		float L_94 = L_93.___w;
		float L_95 = V_3;
		V_4 = ((float)(((float)il2cpp_codegen_multiply(((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(L_80, L_82)), L_84)), L_86)), L_88)), L_90)), L_92)), L_94)), L_95))/(8.0f)));
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_96 = V_11;
		float L_97 = L_96.___x;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_98 = V_11;
		float L_99 = L_98.___y;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_100 = V_11;
		float L_101 = L_100.___z;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_102 = V_11;
		float L_103 = L_102.___w;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_104 = V_13;
		float L_105 = L_104.___x;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_106 = V_13;
		float L_107 = L_106.___y;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_108 = V_13;
		float L_109 = L_108.___z;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_110 = V_13;
		float L_111 = L_110.___w;
		float L_112 = V_3;
		V_5 = ((float)(((float)il2cpp_codegen_multiply(((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(L_97, L_99)), L_101)), L_103)), L_105)), L_107)), L_109)), L_111)), L_112))/(8.0f)));
		goto IL_0260;
	}

IL_0248:
	{
		float L_113 = V_1;
		float L_114 = V_0;
		float L_115;
		L_115 = sinf(((float)il2cpp_codegen_add(L_113, L_114)));
		float L_116 = V_3;
		V_4 = ((float)il2cpp_codegen_multiply(L_115, L_116));
		float L_117 = V_2;
		float L_118 = V_0;
		float L_119;
		L_119 = sinf(((float)il2cpp_codegen_add(L_117, L_118)));
		float L_120 = V_3;
		V_5 = ((float)il2cpp_codegen_multiply(L_119, L_120));
	}

IL_0260:
	{
		float L_121 = V_4;
		float L_122 = V_5;
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_123 = __this->___WaterPlane;
		NullCheck(L_123);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_124;
		L_124 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(L_123, NULL);
		NullCheck(L_124);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_125;
		L_125 = Transform_get_position_m69CD5FA214FDAE7BB701552943674846C220FDE1(L_124, NULL);
		float L_126 = L_125.___y;
		float L_127 = __this->___heightOffset;
		(&___1_worldPosition)->___y = ((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(L_121, L_122)), L_126)), L_127));
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_128 = ___1_worldPosition;
		return L_128;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 TCP2_GetVertexWavesPosition_GetNormalOnWater_SG2_m68339EE9562658B60D0FEA84A84D227DB0289992 (TCP2_GetVertexWavesPosition_t37CBB31361B597192C56DBCE2DF080524AD535CF* __this, float ___0_time, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___1_worldPosition, const RuntimeMethod* method) 
{
	float V_0 = 0.0f;
	float V_1 = 0.0f;
	float V_2 = 0.0f;
	float V_3 = 0.0f;
	float V_4 = 0.0f;
	float V_5 = 0.0f;
	int32_t V_6 = 0;
	Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 V_7;
	memset((&V_7), 0, sizeof(V_7));
	Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 V_8;
	memset((&V_8), 0, sizeof(V_8));
	Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 V_9;
	memset((&V_9), 0, sizeof(V_9));
	Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 V_10;
	memset((&V_10), 0, sizeof(V_10));
	Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 V_11;
	memset((&V_11), 0, sizeof(V_11));
	Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 V_12;
	memset((&V_12), 0, sizeof(V_12));
	Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 V_13;
	memset((&V_13), 0, sizeof(V_13));
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_14;
	memset((&V_14), 0, sizeof(V_14));
	{
		float L_0 = ___0_time;
		float L_1 = __this->___WavesSpeed;
		V_0 = ((float)il2cpp_codegen_multiply(L_0, L_1));
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_2 = ___1_worldPosition;
		float L_3 = L_2.___x;
		float L_4 = __this->___WavesFrequency;
		V_1 = ((float)il2cpp_codegen_multiply(L_3, L_4));
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_5 = ___1_worldPosition;
		float L_6 = L_5.___z;
		float L_7 = __this->___WavesFrequency;
		V_2 = ((float)il2cpp_codegen_multiply(L_6, L_7));
		float L_8 = __this->___WavesHeight;
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_9 = __this->___WaterPlane;
		NullCheck(L_9);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_10;
		L_10 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(L_9, NULL);
		NullCheck(L_10);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_11;
		L_11 = Transform_get_lossyScale_mFF740DA4BE1489C6882CD2F3A37B7321176E5D07(L_10, NULL);
		float L_12 = L_11.___y;
		V_3 = ((float)il2cpp_codegen_multiply(L_8, L_12));
		V_4 = (0.0f);
		V_5 = (0.0f);
		int32_t L_13 = __this->___sineCount;
		V_6 = L_13;
		int32_t L_14 = V_6;
		switch (((int32_t)il2cpp_codegen_subtract(L_14, 1)))
		{
			case 0:
			{
				goto IL_024e;
			}
			case 1:
			{
				goto IL_007e;
			}
			case 2:
			{
				goto IL_0268;
			}
			case 3:
			{
				goto IL_00cf;
			}
		}
	}
	{
		int32_t L_15 = V_6;
		if ((((int32_t)L_15) == ((int32_t)8)))
		{
			goto IL_0158;
		}
	}
	{
		goto IL_0268;
	}

IL_007e:
	{
		float L_16 = V_1;
		float L_17 = V_2;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_18 = __this->___sinOffsets1;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_19 = __this->___phaseOffsets1;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_20;
		L_20 = TCP2_GetVertexWavesPosition_CalculateSineNormal_m2CD1B559FB1B6342C0D37CA3CC5DAB8B5678EFEF(__this, L_16, L_17, L_18, L_19, (&V_0), NULL);
		V_7 = L_20;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_21 = V_7;
		float L_22 = L_21.___x;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_23 = V_7;
		float L_24 = L_23.___y;
		float L_25 = V_3;
		V_4 = ((float)(((float)il2cpp_codegen_multiply(((float)il2cpp_codegen_add(L_22, L_24)), ((-L_25))))/(2.0f)));
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_26 = V_7;
		float L_27 = L_26.___z;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_28 = V_7;
		float L_29 = L_28.___w;
		float L_30 = V_3;
		V_5 = ((float)(((float)il2cpp_codegen_multiply(((float)il2cpp_codegen_add(L_27, L_29)), ((-L_30))))/(2.0f)));
		goto IL_0268;
	}

IL_00cf:
	{
		float L_31 = V_1;
		float L_32 = V_1;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_33 = __this->___sinOffsets1;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_34 = __this->___phaseOffsets1;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_35;
		L_35 = TCP2_GetVertexWavesPosition_CalculateSineNormal_m2CD1B559FB1B6342C0D37CA3CC5DAB8B5678EFEF(__this, L_31, L_32, L_33, L_34, (&V_0), NULL);
		V_8 = L_35;
		float L_36 = V_2;
		float L_37 = V_2;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_38 = __this->___sinOffsets2;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_39 = __this->___phaseOffsets2;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_40;
		L_40 = TCP2_GetVertexWavesPosition_CalculateSineNormal_m2CD1B559FB1B6342C0D37CA3CC5DAB8B5678EFEF(__this, L_36, L_37, L_38, L_39, (&V_0), NULL);
		V_9 = L_40;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_41 = V_8;
		float L_42 = L_41.___x;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_43 = V_8;
		float L_44 = L_43.___y;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_45 = V_8;
		float L_46 = L_45.___z;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_47 = V_8;
		float L_48 = L_47.___w;
		float L_49 = V_3;
		V_4 = ((float)(((float)il2cpp_codegen_multiply(((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(L_42, L_44)), L_46)), L_48)), ((-L_49))))/(4.0f)));
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_50 = V_9;
		float L_51 = L_50.___x;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_52 = V_9;
		float L_53 = L_52.___y;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_54 = V_9;
		float L_55 = L_54.___z;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_56 = V_9;
		float L_57 = L_56.___w;
		float L_58 = V_3;
		V_5 = ((float)(((float)il2cpp_codegen_multiply(((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(L_51, L_53)), L_55)), L_57)), ((-L_58))))/(4.0f)));
		goto IL_0268;
	}

IL_0158:
	{
		float L_59 = V_1;
		float L_60 = V_1;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_61 = __this->___sinOffsets1;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_62 = __this->___phaseOffsets1;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_63;
		L_63 = TCP2_GetVertexWavesPosition_CalculateSineNormal_m2CD1B559FB1B6342C0D37CA3CC5DAB8B5678EFEF(__this, L_59, L_60, L_61, L_62, (&V_0), NULL);
		V_10 = L_63;
		float L_64 = V_2;
		float L_65 = V_2;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_66 = __this->___sinOffsets2;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_67 = __this->___phaseOffsets2;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_68;
		L_68 = TCP2_GetVertexWavesPosition_CalculateSineNormal_m2CD1B559FB1B6342C0D37CA3CC5DAB8B5678EFEF(__this, L_64, L_65, L_66, L_67, (&V_0), NULL);
		V_11 = L_68;
		float L_69 = V_1;
		float L_70 = V_1;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_71 = __this->___sinOffsets3;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_72 = __this->___phaseOffsets3;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_73;
		L_73 = TCP2_GetVertexWavesPosition_CalculateSineNormal_m2CD1B559FB1B6342C0D37CA3CC5DAB8B5678EFEF(__this, L_69, L_70, L_71, L_72, (&V_0), NULL);
		V_12 = L_73;
		float L_74 = V_2;
		float L_75 = V_2;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_76 = __this->___sinOffsets4;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_77 = __this->___phaseOffsets4;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_78;
		L_78 = TCP2_GetVertexWavesPosition_CalculateSineNormal_m2CD1B559FB1B6342C0D37CA3CC5DAB8B5678EFEF(__this, L_74, L_75, L_76, L_77, (&V_0), NULL);
		V_13 = L_78;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_79 = V_10;
		float L_80 = L_79.___x;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_81 = V_10;
		float L_82 = L_81.___y;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_83 = V_10;
		float L_84 = L_83.___z;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_85 = V_10;
		float L_86 = L_85.___w;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_87 = V_12;
		float L_88 = L_87.___x;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_89 = V_12;
		float L_90 = L_89.___y;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_91 = V_12;
		float L_92 = L_91.___z;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_93 = V_12;
		float L_94 = L_93.___w;
		float L_95 = V_3;
		V_4 = ((float)(((float)il2cpp_codegen_multiply(((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(L_80, L_82)), L_84)), L_86)), L_88)), L_90)), L_92)), L_94)), ((-L_95))))/(8.0f)));
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_96 = V_11;
		float L_97 = L_96.___x;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_98 = V_11;
		float L_99 = L_98.___y;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_100 = V_11;
		float L_101 = L_100.___z;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_102 = V_11;
		float L_103 = L_102.___w;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_104 = V_13;
		float L_105 = L_104.___x;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_106 = V_13;
		float L_107 = L_106.___y;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_108 = V_13;
		float L_109 = L_108.___z;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_110 = V_13;
		float L_111 = L_110.___w;
		float L_112 = V_3;
		V_5 = ((float)(((float)il2cpp_codegen_multiply(((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(L_97, L_99)), L_101)), L_103)), L_105)), L_107)), L_109)), L_111)), ((-L_112))))/(8.0f)));
		goto IL_0268;
	}

IL_024e:
	{
		float L_113 = V_1;
		float L_114 = V_0;
		float L_115;
		L_115 = cosf(((float)il2cpp_codegen_add(L_113, L_114)));
		float L_116 = V_3;
		V_4 = ((float)il2cpp_codegen_multiply(L_115, ((-L_116))));
		float L_117 = V_2;
		float L_118 = V_0;
		float L_119;
		L_119 = cosf(((float)il2cpp_codegen_add(L_117, L_118)));
		float L_120 = V_3;
		V_5 = ((float)il2cpp_codegen_multiply(L_119, ((-L_120))));
	}

IL_0268:
	{
		float L_121 = V_4;
		float L_122 = V_5;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_123;
		memset((&L_123), 0, sizeof(L_123));
		Vector3__ctor_m376936E6B999EF1ECBE57D990A386303E2283DE0_inline((&L_123), L_121, (1.0f), L_122, NULL);
		V_14 = L_123;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_124;
		L_124 = Vector3_get_normalized_m736BBF65D5CDA7A18414370D15B4DFCC1E466F07_inline((&V_14), NULL);
		return L_124;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TCP2_GetVertexWavesPosition__ctor_m72C28EAB273764836C3546E743BC6325401DCE49 (TCP2_GetVertexWavesPosition_t37CBB31361B597192C56DBCE2DF080524AD535CF* __this, const RuntimeMethod* method) 
{
	{
		__this->___followWaterHeight = (bool)1;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0;
		memset((&L_0), 0, sizeof(L_0));
		Vector3__ctor_m376936E6B999EF1ECBE57D990A386303E2283DE0_inline((&L_0), (0.0f), (1.0f), (0.0f), NULL);
		__this->___upAxis = L_0;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_1;
		memset((&L_1), 0, sizeof(L_1));
		Vector3__ctor_m376936E6B999EF1ECBE57D990A386303E2283DE0_inline((&L_1), (0.0f), (0.0f), (0.0f), NULL);
		__this->___postRotation = L_1;
		__this->___sineCount = 1;
		__this->___WavesSpeed = (2.0f);
		__this->___WavesHeight = (0.100000001f);
		__this->___WavesFrequency = (1.0f);
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_2;
		memset((&L_2), 0, sizeof(L_2));
		Vector4__ctor_m96B2CD8B862B271F513AF0BDC2EABD58E4DBC813_inline((&L_2), (1.0f), (2.20000005f), (0.600000024f), (1.29999995f), NULL);
		__this->___sinOffsets1 = L_2;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_3;
		memset((&L_3), 0, sizeof(L_3));
		Vector4__ctor_m96B2CD8B862B271F513AF0BDC2EABD58E4DBC813_inline((&L_3), (1.0f), (1.29999995f), (2.20000005f), (0.400000006f), NULL);
		__this->___phaseOffsets1 = L_3;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_4;
		memset((&L_4), 0, sizeof(L_4));
		Vector4__ctor_m96B2CD8B862B271F513AF0BDC2EABD58E4DBC813_inline((&L_4), (0.600000024f), (1.29999995f), (3.0999999f), (2.4000001f), NULL);
		__this->___sinOffsets2 = L_4;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_5;
		memset((&L_5), 0, sizeof(L_5));
		Vector4__ctor_m96B2CD8B862B271F513AF0BDC2EABD58E4DBC813_inline((&L_5), (2.20000005f), (0.400000006f), (3.29999995f), (2.9000001f), NULL);
		__this->___phaseOffsets2 = L_5;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_6;
		memset((&L_6), 0, sizeof(L_6));
		Vector4__ctor_m96B2CD8B862B271F513AF0BDC2EABD58E4DBC813_inline((&L_6), (1.39999998f), (1.79999995f), (4.19999981f), (3.5999999f), NULL);
		__this->___sinOffsets3 = L_6;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_7;
		memset((&L_7), 0, sizeof(L_7));
		Vector4__ctor_m96B2CD8B862B271F513AF0BDC2EABD58E4DBC813_inline((&L_7), (0.200000003f), (2.5999999f), (0.699999988f), (3.0999999f), NULL);
		__this->___phaseOffsets3 = L_7;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_8;
		memset((&L_8), 0, sizeof(L_8));
		Vector4__ctor_m96B2CD8B862B271F513AF0BDC2EABD58E4DBC813_inline((&L_8), (1.10000002f), (2.79999995f), (1.70000005f), (4.30000019f), NULL);
		__this->___sinOffsets4 = L_8;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_9;
		memset((&L_9), 0, sizeof(L_9));
		Vector4__ctor_m96B2CD8B862B271F513AF0BDC2EABD58E4DBC813_inline((&L_9), (0.5f), (4.80000019f), (3.0999999f), (2.29999995f), NULL);
		__this->___phaseOffsets4 = L_9;
		MonoBehaviour__ctor_m592DB0105CA0BC97AA1C5F4AD27B12D68A3B7C1E(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TCP2_GetVertexWavesPosition__cctor_m003CEFE27F1C3005AEED418C3821FAE44583BC59 (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TCP2_GetVertexWavesPosition_t37CBB31361B597192C56DBCE2DF080524AD535CF_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral45EE4883CDE67FE6B013C71176B61EE1D2154200);
		s_Il2CppMethodInitialized = true;
	}
	{
		int32_t L_0;
		L_0 = Shader_PropertyToID_mE98523D50F5656CAE89B30695C458253EB8956CA(_stringLiteral45EE4883CDE67FE6B013C71176B61EE1D2154200, NULL);
		((TCP2_GetVertexWavesPosition_t37CBB31361B597192C56DBCE2DF080524AD535CF_StaticFields*)il2cpp_codegen_static_fields_for(TCP2_GetVertexWavesPosition_t37CBB31361B597192C56DBCE2DF080524AD535CF_il2cpp_TypeInfo_var))->____Time = L_0;
		((TCP2_GetVertexWavesPosition_t37CBB31361B597192C56DBCE2DF080524AD535CF_StaticFields*)il2cpp_codegen_static_fields_for(TCP2_GetVertexWavesPosition_t37CBB31361B597192C56DBCE2DF080524AD535CF_il2cpp_TypeInfo_var))->___ShaderTime = (0.0f);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t TCP2_PlanarReflection_get_ShaderID_ReflectionTex_m56E78BBCE25DAFF87FEE7FD8A2831F918F84B124 (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral88BC903474F3EEC4A94B28FB209B0CB7F7426FCF);
		s_Il2CppMethodInitialized = true;
	}
	{
		il2cpp_codegen_runtime_class_init_inline(TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5_il2cpp_TypeInfo_var);
		int32_t L_0 = ((TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5_StaticFields*)il2cpp_codegen_static_fields_for(TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5_il2cpp_TypeInfo_var))->____ShaderID_ReflectionTex;
		if ((((int32_t)L_0) >= ((int32_t)0)))
		{
			goto IL_0017;
		}
	}
	{
		int32_t L_1;
		L_1 = Shader_PropertyToID_mE98523D50F5656CAE89B30695C458253EB8956CA(_stringLiteral88BC903474F3EEC4A94B28FB209B0CB7F7426FCF, NULL);
		il2cpp_codegen_runtime_class_init_inline(TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5_il2cpp_TypeInfo_var);
		((TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5_StaticFields*)il2cpp_codegen_static_fields_for(TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5_il2cpp_TypeInfo_var))->____ShaderID_ReflectionTex = L_1;
	}

IL_0017:
	{
		il2cpp_codegen_runtime_class_init_inline(TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5_il2cpp_TypeInfo_var);
		int32_t L_2 = ((TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5_StaticFields*)il2cpp_codegen_static_fields_for(TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5_il2cpp_TypeInfo_var))->____ShaderID_ReflectionTex;
		return L_2;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t TCP2_PlanarReflection_get_ShaderID_ReflectionDepthTex_mA4D9AB261F8369C429B72CD6DC811B331EA5D0D2 (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral2D31075CE013917736E287455F9EF8ED0FFF65B9);
		s_Il2CppMethodInitialized = true;
	}
	{
		il2cpp_codegen_runtime_class_init_inline(TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5_il2cpp_TypeInfo_var);
		int32_t L_0 = ((TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5_StaticFields*)il2cpp_codegen_static_fields_for(TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5_il2cpp_TypeInfo_var))->____ShaderID_ReflectionDepthTex;
		if ((((int32_t)L_0) >= ((int32_t)0)))
		{
			goto IL_0017;
		}
	}
	{
		int32_t L_1;
		L_1 = Shader_PropertyToID_mE98523D50F5656CAE89B30695C458253EB8956CA(_stringLiteral2D31075CE013917736E287455F9EF8ED0FFF65B9, NULL);
		il2cpp_codegen_runtime_class_init_inline(TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5_il2cpp_TypeInfo_var);
		((TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5_StaticFields*)il2cpp_codegen_static_fields_for(TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5_il2cpp_TypeInfo_var))->____ShaderID_ReflectionDepthTex = L_1;
	}

IL_0017:
	{
		il2cpp_codegen_runtime_class_init_inline(TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5_il2cpp_TypeInfo_var);
		int32_t L_2 = ((TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5_StaticFields*)il2cpp_codegen_static_fields_for(TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5_il2cpp_TypeInfo_var))->____ShaderID_ReflectionDepthTex;
		return L_2;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t TCP2_PlanarReflection_get_ShaderID_ReflectivePlaneY_m5BD4FD60BE5D9E4CDC43CDCF7C7FE46BE5B0D205 (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral998DFB8DAB26DE673AC25A4F7F4555212C8B8C12);
		s_Il2CppMethodInitialized = true;
	}
	{
		il2cpp_codegen_runtime_class_init_inline(TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5_il2cpp_TypeInfo_var);
		int32_t L_0 = ((TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5_StaticFields*)il2cpp_codegen_static_fields_for(TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5_il2cpp_TypeInfo_var))->____ShaderID_ReflectivePlaneY;
		if ((((int32_t)L_0) >= ((int32_t)0)))
		{
			goto IL_0017;
		}
	}
	{
		int32_t L_1;
		L_1 = Shader_PropertyToID_mE98523D50F5656CAE89B30695C458253EB8956CA(_stringLiteral998DFB8DAB26DE673AC25A4F7F4555212C8B8C12, NULL);
		il2cpp_codegen_runtime_class_init_inline(TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5_il2cpp_TypeInfo_var);
		((TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5_StaticFields*)il2cpp_codegen_static_fields_for(TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5_il2cpp_TypeInfo_var))->____ShaderID_ReflectivePlaneY = L_1;
	}

IL_0017:
	{
		il2cpp_codegen_runtime_class_init_inline(TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5_il2cpp_TypeInfo_var);
		int32_t L_2 = ((TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5_StaticFields*)il2cpp_codegen_static_fields_for(TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5_il2cpp_TypeInfo_var))->____ShaderID_ReflectivePlaneY;
		return L_2;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t TCP2_PlanarReflection_get_ShaderID_ReflectionDepthRange_mE7B06E2DCEED01ACE49728291886D9B9E9C00800 (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral480159C4AEE93C93F99B50874C51F41411E5009B);
		s_Il2CppMethodInitialized = true;
	}
	{
		il2cpp_codegen_runtime_class_init_inline(TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5_il2cpp_TypeInfo_var);
		int32_t L_0 = ((TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5_StaticFields*)il2cpp_codegen_static_fields_for(TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5_il2cpp_TypeInfo_var))->____ShaderID_ReflectionDepthRange;
		if ((((int32_t)L_0) >= ((int32_t)0)))
		{
			goto IL_0017;
		}
	}
	{
		int32_t L_1;
		L_1 = Shader_PropertyToID_mE98523D50F5656CAE89B30695C458253EB8956CA(_stringLiteral480159C4AEE93C93F99B50874C51F41411E5009B, NULL);
		il2cpp_codegen_runtime_class_init_inline(TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5_il2cpp_TypeInfo_var);
		((TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5_StaticFields*)il2cpp_codegen_static_fields_for(TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5_il2cpp_TypeInfo_var))->____ShaderID_ReflectionDepthRange = L_1;
	}

IL_0017:
	{
		il2cpp_codegen_runtime_class_init_inline(TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5_il2cpp_TypeInfo_var);
		int32_t L_2 = ((TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5_StaticFields*)il2cpp_codegen_static_fields_for(TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5_il2cpp_TypeInfo_var))->____ShaderID_ReflectionDepthRange;
		return L_2;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t TCP2_PlanarReflection_get_ShaderID_UseReflectionDepth_mBE5B33FC55A2969E12CFE9C348012732A67DB845 (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral83179BC4C407F849C68702927986CBA637D88748);
		s_Il2CppMethodInitialized = true;
	}
	{
		il2cpp_codegen_runtime_class_init_inline(TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5_il2cpp_TypeInfo_var);
		int32_t L_0 = ((TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5_StaticFields*)il2cpp_codegen_static_fields_for(TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5_il2cpp_TypeInfo_var))->____ShaderID_UseReflectionDepth;
		if ((((int32_t)L_0) >= ((int32_t)0)))
		{
			goto IL_0017;
		}
	}
	{
		int32_t L_1;
		L_1 = Shader_PropertyToID_mE98523D50F5656CAE89B30695C458253EB8956CA(_stringLiteral83179BC4C407F849C68702927986CBA637D88748, NULL);
		il2cpp_codegen_runtime_class_init_inline(TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5_il2cpp_TypeInfo_var);
		((TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5_StaticFields*)il2cpp_codegen_static_fields_for(TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5_il2cpp_TypeInfo_var))->____ShaderID_UseReflectionDepth = L_1;
	}

IL_0017:
	{
		il2cpp_codegen_runtime_class_init_inline(TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5_il2cpp_TypeInfo_var);
		int32_t L_2 = ((TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5_StaticFields*)il2cpp_codegen_static_fields_for(TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5_il2cpp_TypeInfo_var))->____ShaderID_UseReflectionDepth;
		return L_2;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TCP2_PlanarReflection_OnValidate_mD359A860E6C3CA734D6F679E317B9A87335308D3 (TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5* __this, const RuntimeMethod* method) 
{
	{
		TCP2_PlanarReflection_UpdateRenderTexture_m68EC26E08F46066F16884B9CA041AACCBB6F87E5(__this, NULL);
		TCP2_PlanarReflection_UpdateCommandBuffer_m992291EDB61AEA40C332DA5FC2E53C9E72E58D8F(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TCP2_PlanarReflection_OnEnable_m410E1A5E0D4211EB67F46AE16D66F129FBCA7DCF (TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_2_t8E07914D7090FF200FE84404EEEFAF3CE183C9F3_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&CameraCallback_t844E527BFE37BC0495E7F67993E43C07642DA9DD_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&RenderPipelineManager_t44E0175AAADDD5487593AEF2B009B1B154957CDB_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TCP2_PlanarReflection_BeginCameraRendering_Bultin_m40D10D9F9A22782C50A54511880CC575F7C97655_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TCP2_PlanarReflection_BeginCameraRendering_URP_m2551C1514321AD13604A589B5EA33ED4D8B53D25_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralDEAC03BC08603A926530E878D97B96FA6C0CFEED);
		s_Il2CppMethodInitialized = true;
	}
	TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5* G_B2_0 = NULL;
	TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5* G_B1_0 = NULL;
	int32_t G_B3_0 = 0;
	TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5* G_B3_1 = NULL;
	{
		RenderPipelineAsset_t5F9BF815BF931E1314B184E7F9070FB649C7054E* L_0;
		L_0 = GraphicsSettings_get_currentRenderPipeline_mEC94DC23DE4F901D6A629E2DE882982686AF75F1(NULL);
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_1;
		L_1 = Object_op_Inequality_mD0BE578448EAA61948F25C32F8DD55AB1F778602(L_0, (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*)NULL, NULL);
		if (!L_1)
		{
			G_B2_0 = __this;
			goto IL_0029;
		}
		G_B1_0 = __this;
	}
	{
		RenderPipelineAsset_t5F9BF815BF931E1314B184E7F9070FB649C7054E* L_2;
		L_2 = GraphicsSettings_get_currentRenderPipeline_mEC94DC23DE4F901D6A629E2DE882982686AF75F1(NULL);
		NullCheck(L_2);
		Type_t* L_3;
		L_3 = Object_GetType_mE10A8FC1E57F3DF29972CCBC026C2DC3942263B3(L_2, NULL);
		NullCheck(L_3);
		String_t* L_4;
		L_4 = VirtualFuncInvoker0< String_t* >::Invoke(7, L_3);
		NullCheck(L_4);
		bool L_5;
		L_5 = String_Contains_m6D77B121FADA7CA5F397C0FABB65DA62DF03B6C3(L_4, _stringLiteralDEAC03BC08603A926530E878D97B96FA6C0CFEED, NULL);
		G_B3_0 = ((int32_t)(L_5));
		G_B3_1 = G_B1_0;
		goto IL_002a;
	}

IL_0029:
	{
		G_B3_0 = 0;
		G_B3_1 = G_B2_0;
	}

IL_002a:
	{
		NullCheck(G_B3_1);
		G_B3_1->___isURP = (bool)G_B3_0;
		bool L_6 = __this->___isURP;
		if (!L_6)
		{
			goto IL_004a;
		}
	}
	{
		Action_2_t8E07914D7090FF200FE84404EEEFAF3CE183C9F3* L_7 = (Action_2_t8E07914D7090FF200FE84404EEEFAF3CE183C9F3*)il2cpp_codegen_object_new(Action_2_t8E07914D7090FF200FE84404EEEFAF3CE183C9F3_il2cpp_TypeInfo_var);
		Action_2__ctor_mBEB5B9B513FE305CE98CA8065CC6E6CC0E5A4D51(L_7, __this, (intptr_t)((void*)TCP2_PlanarReflection_BeginCameraRendering_URP_m2551C1514321AD13604A589B5EA33ED4D8B53D25_RuntimeMethod_var), NULL);
		il2cpp_codegen_runtime_class_init_inline(RenderPipelineManager_t44E0175AAADDD5487593AEF2B009B1B154957CDB_il2cpp_TypeInfo_var);
		RenderPipelineManager_add_beginCameraRendering_m44DF94A62BE65F929101983FACE63BA4FE4B584A(L_7, NULL);
		goto IL_006a;
	}

IL_004a:
	{
		CameraCallback_t844E527BFE37BC0495E7F67993E43C07642DA9DD* L_8 = ((Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184_StaticFields*)il2cpp_codegen_static_fields_for(Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184_il2cpp_TypeInfo_var))->___onPreRender;
		CameraCallback_t844E527BFE37BC0495E7F67993E43C07642DA9DD* L_9 = (CameraCallback_t844E527BFE37BC0495E7F67993E43C07642DA9DD*)il2cpp_codegen_object_new(CameraCallback_t844E527BFE37BC0495E7F67993E43C07642DA9DD_il2cpp_TypeInfo_var);
		CameraCallback__ctor_mB48D13F30E749B551E4692E4F2D762C375F62B41(L_9, __this, (intptr_t)((void*)TCP2_PlanarReflection_BeginCameraRendering_Bultin_m40D10D9F9A22782C50A54511880CC575F7C97655_RuntimeMethod_var), NULL);
		Delegate_t* L_10;
		L_10 = Delegate_Combine_m1F725AEF318BE6F0426863490691A6F4606E7D00(L_8, L_9, NULL);
		((Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184_StaticFields*)il2cpp_codegen_static_fields_for(Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184_il2cpp_TypeInfo_var))->___onPreRender = ((CameraCallback_t844E527BFE37BC0495E7F67993E43C07642DA9DD*)CastclassSealed((RuntimeObject*)L_10, CameraCallback_t844E527BFE37BC0495E7F67993E43C07642DA9DD_il2cpp_TypeInfo_var));
		Il2CppCodeGenWriteBarrier((void**)(&((Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184_StaticFields*)il2cpp_codegen_static_fields_for(Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184_il2cpp_TypeInfo_var))->___onPreRender), (void*)((CameraCallback_t844E527BFE37BC0495E7F67993E43C07642DA9DD*)CastclassSealed((RuntimeObject*)L_10, CameraCallback_t844E527BFE37BC0495E7F67993E43C07642DA9DD_il2cpp_TypeInfo_var)));
	}

IL_006a:
	{
		TCP2_PlanarReflection_UpdateRenderTexture_m68EC26E08F46066F16884B9CA041AACCBB6F87E5(__this, NULL);
		TCP2_PlanarReflection_UpdateCommandBuffer_m992291EDB61AEA40C332DA5FC2E53C9E72E58D8F(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TCP2_PlanarReflection_OnDisable_m10FC5C022E9D909DBCD4EE31457FA4DCEE775BA4 (TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_2_t8E07914D7090FF200FE84404EEEFAF3CE183C9F3_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&CameraCallback_t844E527BFE37BC0495E7F67993E43C07642DA9DD_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&RenderPipelineManager_t44E0175AAADDD5487593AEF2B009B1B154957CDB_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TCP2_PlanarReflection_BeginCameraRendering_Bultin_m40D10D9F9A22782C50A54511880CC575F7C97655_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TCP2_PlanarReflection_BeginCameraRendering_URP_m2551C1514321AD13604A589B5EA33ED4D8B53D25_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		bool L_0 = __this->___isURP;
		if (!L_0)
		{
			goto IL_001b;
		}
	}
	{
		Action_2_t8E07914D7090FF200FE84404EEEFAF3CE183C9F3* L_1 = (Action_2_t8E07914D7090FF200FE84404EEEFAF3CE183C9F3*)il2cpp_codegen_object_new(Action_2_t8E07914D7090FF200FE84404EEEFAF3CE183C9F3_il2cpp_TypeInfo_var);
		Action_2__ctor_mBEB5B9B513FE305CE98CA8065CC6E6CC0E5A4D51(L_1, __this, (intptr_t)((void*)TCP2_PlanarReflection_BeginCameraRendering_URP_m2551C1514321AD13604A589B5EA33ED4D8B53D25_RuntimeMethod_var), NULL);
		il2cpp_codegen_runtime_class_init_inline(RenderPipelineManager_t44E0175AAADDD5487593AEF2B009B1B154957CDB_il2cpp_TypeInfo_var);
		RenderPipelineManager_remove_beginCameraRendering_m6A9B576247B531A6C1C715870A37343AC702976E(L_1, NULL);
		goto IL_003b;
	}

IL_001b:
	{
		CameraCallback_t844E527BFE37BC0495E7F67993E43C07642DA9DD* L_2 = ((Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184_StaticFields*)il2cpp_codegen_static_fields_for(Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184_il2cpp_TypeInfo_var))->___onPreRender;
		CameraCallback_t844E527BFE37BC0495E7F67993E43C07642DA9DD* L_3 = (CameraCallback_t844E527BFE37BC0495E7F67993E43C07642DA9DD*)il2cpp_codegen_object_new(CameraCallback_t844E527BFE37BC0495E7F67993E43C07642DA9DD_il2cpp_TypeInfo_var);
		CameraCallback__ctor_mB48D13F30E749B551E4692E4F2D762C375F62B41(L_3, __this, (intptr_t)((void*)TCP2_PlanarReflection_BeginCameraRendering_Bultin_m40D10D9F9A22782C50A54511880CC575F7C97655_RuntimeMethod_var), NULL);
		Delegate_t* L_4;
		L_4 = Delegate_Remove_m8B7DD5661308FA972E23CA1CC3FC9CEB355504E3(L_2, L_3, NULL);
		((Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184_StaticFields*)il2cpp_codegen_static_fields_for(Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184_il2cpp_TypeInfo_var))->___onPreRender = ((CameraCallback_t844E527BFE37BC0495E7F67993E43C07642DA9DD*)CastclassSealed((RuntimeObject*)L_4, CameraCallback_t844E527BFE37BC0495E7F67993E43C07642DA9DD_il2cpp_TypeInfo_var));
		Il2CppCodeGenWriteBarrier((void**)(&((Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184_StaticFields*)il2cpp_codegen_static_fields_for(Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184_il2cpp_TypeInfo_var))->___onPreRender), (void*)((CameraCallback_t844E527BFE37BC0495E7F67993E43C07642DA9DD*)CastclassSealed((RuntimeObject*)L_4, CameraCallback_t844E527BFE37BC0495E7F67993E43C07642DA9DD_il2cpp_TypeInfo_var)));
	}

IL_003b:
	{
		TCP2_PlanarReflection_ClearCommandBuffer_mE31CD5BC181ECC6CE57A89FE91FC54D7599A40AF(__this, NULL);
		TCP2_PlanarReflection_ClearRenderTexture_m6EF08D43A78324DA76B09AB04BB2063D5F7B1B0A(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TCP2_PlanarReflection_UpdateRenderTexture_m68EC26E08F46066F16884B9CA041AACCBB6F87E5 (TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&RenderTexture_tBA90C4C3AD9EECCFDDCC632D97C29FAB80D60D27_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralCBDC4FFC23E84CE20BE8F9EDFEFD01BCB121707E);
		s_Il2CppMethodInitialized = true;
	}
	int32_t V_0 = 0;
	{
		RenderTexture_tBA90C4C3AD9EECCFDDCC632D97C29FAB80D60D27* L_0 = __this->___reflectionRenderTexture;
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_1;
		L_1 = Object_op_Inequality_mD0BE578448EAA61948F25C32F8DD55AB1F778602(L_0, (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*)NULL, NULL);
		if (!L_1)
		{
			goto IL_0014;
		}
	}
	{
		TCP2_PlanarReflection_ClearRenderTexture_m6EF08D43A78324DA76B09AB04BB2063D5F7B1B0A(__this, NULL);
	}

IL_0014:
	{
		int32_t L_2 = __this->___textureSize;
		int32_t L_3 = __this->___textureSize;
		int32_t L_4 = __this->___renderTextureFormat;
		RenderTexture_tBA90C4C3AD9EECCFDDCC632D97C29FAB80D60D27* L_5 = (RenderTexture_tBA90C4C3AD9EECCFDDCC632D97C29FAB80D60D27*)il2cpp_codegen_object_new(RenderTexture_tBA90C4C3AD9EECCFDDCC632D97C29FAB80D60D27_il2cpp_TypeInfo_var);
		RenderTexture__ctor_m68A1B9CAA1BE0B597C5F4895C296E21502D0C962(L_5, L_2, L_3, ((int32_t)16), L_4, 2, NULL);
		__this->___reflectionRenderTexture = L_5;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___reflectionRenderTexture), (void*)L_5);
		RenderTexture_tBA90C4C3AD9EECCFDDCC632D97C29FAB80D60D27* L_6 = __this->___reflectionRenderTexture;
		int32_t L_7;
		L_7 = Object_GetInstanceID_m554FF4073C9465F3835574CC084E68AAEEC6CC6A(__this, NULL);
		V_0 = L_7;
		String_t* L_8;
		L_8 = Int32_ToString_m030E01C24E294D6762FB0B6F37CB541581F55CA5((&V_0), NULL);
		String_t* L_9;
		L_9 = String_Concat_m9E3155FB84015C823606188F53B47CB44C444991(_stringLiteralCBDC4FFC23E84CE20BE8F9EDFEFD01BCB121707E, L_8, NULL);
		NullCheck(L_6);
		Object_set_name_mC79E6DC8FFD72479C90F0C4CC7F42A0FEAF5AE47(L_6, L_9, NULL);
		RenderTexture_tBA90C4C3AD9EECCFDDCC632D97C29FAB80D60D27* L_10 = __this->___reflectionRenderTexture;
		NullCheck(L_10);
		Object_set_hideFlags_mACB8BFC903FB3B01BBD427753E791BF28B5E33D4(L_10, ((int32_t)20), NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TCP2_PlanarReflection_ClearRenderTexture_m6EF08D43A78324DA76B09AB04BB2063D5F7B1B0A (TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		RenderTexture_tBA90C4C3AD9EECCFDDCC632D97C29FAB80D60D27* L_0 = __this->___reflectionRenderTexture;
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_1;
		L_1 = Object_op_Inequality_mD0BE578448EAA61948F25C32F8DD55AB1F778602(L_0, (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*)NULL, NULL);
		if (!L_1)
		{
			goto IL_0024;
		}
	}
	{
		RenderTexture_tBA90C4C3AD9EECCFDDCC632D97C29FAB80D60D27* L_2 = __this->___reflectionRenderTexture;
		NullCheck(L_2);
		RenderTexture_Release_mE7399D6187A0E38945D2913D0FFB41247143AB1E(L_2, NULL);
		RenderTexture_tBA90C4C3AD9EECCFDDCC632D97C29FAB80D60D27* L_3 = __this->___reflectionRenderTexture;
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		Object_DestroyImmediate_m6336EBC83591A5DB64EC70C92132824C6E258705(L_3, NULL);
	}

IL_0024:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TCP2_PlanarReflection_UpdateCommandBuffer_m992291EDB61AEA40C332DA5FC2E53C9E72E58D8F (TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&CommandBuffer_tB56007DC84EF56296C325EC32DD12AC1E3DC91F7_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Debug_t8394C7EEAECA3689C2C9B9DE9C7166D73596276F_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral01F8EE838110D5B806439591F8F77DDA16B245B7);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral07D0259BABE43788E27004165B22CA5CB10BF205);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral380523DDEA84AFF6A3407002134839D87EE413FA);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralE7B2D52A08CC1556BCF6FBA714FEF97FFEF5178B);
		s_Il2CppMethodInitialized = true;
	}
	int32_t V_0 = 0;
	int32_t V_1 = 0;
	{
		CommandBuffer_tB56007DC84EF56296C325EC32DD12AC1E3DC91F7* L_0 = __this->___commandBufferBlur;
		if (!L_0)
		{
			goto IL_000e;
		}
	}
	{
		TCP2_PlanarReflection_ClearCommandBuffer_mE31CD5BC181ECC6CE57A89FE91FC54D7599A40AF(__this, NULL);
	}

IL_000e:
	{
		bool L_1 = __this->___applyBlur;
		if (L_1)
		{
			goto IL_0017;
		}
	}
	{
		return;
	}

IL_0017:
	{
		Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* L_2 = __this->___blurMaterial;
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_3;
		L_3 = Object_op_Equality_mB6120F782D83091EF56A198FCEBCF066DB4A9605(L_2, (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*)NULL, NULL);
		if (!L_3)
		{
			goto IL_0065;
		}
	}
	{
		Shader_tADC867D36B7876EE22427FAA2CE485105F4EE692* L_4 = __this->___blurShader;
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_5;
		L_5 = Object_op_Equality_mB6120F782D83091EF56A198FCEBCF066DB4A9605(L_4, (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*)NULL, NULL);
		if (!L_5)
		{
			goto IL_0044;
		}
	}
	{
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_6;
		L_6 = Component_get_gameObject_m57AEFBB14DB39EC476F740BA000E170355DE691B(__this, NULL);
		il2cpp_codegen_runtime_class_init_inline(Debug_t8394C7EEAECA3689C2C9B9DE9C7166D73596276F_il2cpp_TypeInfo_var);
		Debug_LogError_m94F967AB31244EACE68C3BE1DD85B69ED3334C0E(_stringLiteral01F8EE838110D5B806439591F8F77DDA16B245B7, L_6, NULL);
		return;
	}

IL_0044:
	{
		Shader_tADC867D36B7876EE22427FAA2CE485105F4EE692* L_7 = __this->___blurShader;
		Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* L_8 = (Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3*)il2cpp_codegen_object_new(Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3_il2cpp_TypeInfo_var);
		Material__ctor_m7FDF47105D66D19591BE505A0C42B0F90D88C9BF(L_8, L_7, NULL);
		__this->___blurMaterial = L_8;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___blurMaterial), (void*)L_8);
		Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* L_9 = __this->___blurMaterial;
		NullCheck(L_9);
		Object_set_name_mC79E6DC8FFD72479C90F0C4CC7F42A0FEAF5AE47(L_9, _stringLiteral380523DDEA84AFF6A3407002134839D87EE413FA, NULL);
	}

IL_0065:
	{
		Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* L_10 = __this->___blurMaterial;
		float L_11 = __this->___blurDistance;
		NullCheck(L_10);
		Material_SetFloat_m879CF81D740BAE6F23C9822400679F4D16365836(L_10, _stringLiteral07D0259BABE43788E27004165B22CA5CB10BF205, L_11, NULL);
		RenderTexture_tBA90C4C3AD9EECCFDDCC632D97C29FAB80D60D27* L_12 = __this->___reflectionRenderTexture;
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_13;
		L_13 = Object_op_Equality_mB6120F782D83091EF56A198FCEBCF066DB4A9605(L_12, (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*)NULL, NULL);
		if (!L_13)
		{
			goto IL_008a;
		}
	}
	{
		return;
	}

IL_008a:
	{
		Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* L_14 = __this->___reflectionCamera;
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_15;
		L_15 = Object_op_Equality_mB6120F782D83091EF56A198FCEBCF066DB4A9605(L_14, (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*)NULL, NULL);
		if (!L_15)
		{
			goto IL_0099;
		}
	}
	{
		return;
	}

IL_0099:
	{
		CommandBuffer_tB56007DC84EF56296C325EC32DD12AC1E3DC91F7* L_16 = (CommandBuffer_tB56007DC84EF56296C325EC32DD12AC1E3DC91F7*)il2cpp_codegen_object_new(CommandBuffer_tB56007DC84EF56296C325EC32DD12AC1E3DC91F7_il2cpp_TypeInfo_var);
		CommandBuffer__ctor_m9445F1606331B732FCA393591F3E230714FD5FF4(L_16, NULL);
		__this->___commandBufferBlur = L_16;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___commandBufferBlur), (void*)L_16);
		int32_t L_17;
		L_17 = Shader_PropertyToID_mE98523D50F5656CAE89B30695C458253EB8956CA(_stringLiteralE7B2D52A08CC1556BCF6FBA714FEF97FFEF5178B, NULL);
		V_0 = L_17;
		CommandBuffer_tB56007DC84EF56296C325EC32DD12AC1E3DC91F7* L_18 = __this->___commandBufferBlur;
		int32_t L_19 = V_0;
		int32_t L_20 = __this->___textureSize;
		int32_t L_21 = __this->___textureSize;
		RenderTexture_tBA90C4C3AD9EECCFDDCC632D97C29FAB80D60D27* L_22 = __this->___reflectionRenderTexture;
		NullCheck(L_22);
		int32_t L_23;
		L_23 = RenderTexture_get_format_m58556ABB91A1FADA8044BEEA2E8C55280768CF35(L_22, NULL);
		NullCheck(L_18);
		CommandBuffer_GetTemporaryRT_m657797522A6EE9B77BD87C6C3E6765F5A3EA2B36(L_18, L_19, L_20, L_21, ((int32_t)16), 1, L_23, 2, NULL);
		CommandBuffer_tB56007DC84EF56296C325EC32DD12AC1E3DC91F7* L_24 = __this->___commandBufferBlur;
		RenderTexture_tBA90C4C3AD9EECCFDDCC632D97C29FAB80D60D27* L_25 = __this->___reflectionRenderTexture;
		RenderTargetIdentifier_tA528663AC6EB3911D8E91AA40F7070FA5455442B L_26;
		L_26 = RenderTargetIdentifier_op_Implicit_mBF13C6AE62DCEDDEFDC1C7305BE646FE99D2F24C(L_25, NULL);
		int32_t L_27 = V_0;
		RenderTargetIdentifier_tA528663AC6EB3911D8E91AA40F7070FA5455442B L_28;
		L_28 = RenderTargetIdentifier_op_Implicit_m5D9E7FF7B325608E3C4A37BBB52FE728361E7324(L_27, NULL);
		NullCheck(L_24);
		CommandBuffer_CopyTexture_mDA8B82E30D465C5CC29046C558507601E3CC5BAF(L_24, L_26, L_28, NULL);
		CommandBuffer_tB56007DC84EF56296C325EC32DD12AC1E3DC91F7* L_29 = __this->___commandBufferBlur;
		int32_t L_30 = V_0;
		RenderTargetIdentifier_tA528663AC6EB3911D8E91AA40F7070FA5455442B L_31;
		L_31 = RenderTargetIdentifier_op_Implicit_m5D9E7FF7B325608E3C4A37BBB52FE728361E7324(L_30, NULL);
		RenderTexture_tBA90C4C3AD9EECCFDDCC632D97C29FAB80D60D27* L_32 = __this->___reflectionRenderTexture;
		RenderTargetIdentifier_tA528663AC6EB3911D8E91AA40F7070FA5455442B L_33;
		L_33 = RenderTargetIdentifier_op_Implicit_mBF13C6AE62DCEDDEFDC1C7305BE646FE99D2F24C(L_32, NULL);
		Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* L_34 = __this->___blurMaterial;
		NullCheck(L_29);
		CommandBuffer_Blit_m20AC38869B1D9D16C37E1A697B4EF3E2B0D12530(L_29, L_31, L_33, L_34, 0, NULL);
		V_1 = 0;
		goto IL_015e;
	}

IL_0119:
	{
		CommandBuffer_tB56007DC84EF56296C325EC32DD12AC1E3DC91F7* L_35 = __this->___commandBufferBlur;
		RenderTexture_tBA90C4C3AD9EECCFDDCC632D97C29FAB80D60D27* L_36 = __this->___reflectionRenderTexture;
		int32_t L_37 = V_0;
		RenderTargetIdentifier_tA528663AC6EB3911D8E91AA40F7070FA5455442B L_38;
		L_38 = RenderTargetIdentifier_op_Implicit_m5D9E7FF7B325608E3C4A37BBB52FE728361E7324(L_37, NULL);
		Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* L_39 = __this->___blurMaterial;
		NullCheck(L_35);
		CommandBuffer_Blit_m1F4B01C6274C6A01C710B640735BF0B36BCE73DF(L_35, L_36, L_38, L_39, 1, NULL);
		CommandBuffer_tB56007DC84EF56296C325EC32DD12AC1E3DC91F7* L_40 = __this->___commandBufferBlur;
		int32_t L_41 = V_0;
		RenderTargetIdentifier_tA528663AC6EB3911D8E91AA40F7070FA5455442B L_42;
		L_42 = RenderTargetIdentifier_op_Implicit_m5D9E7FF7B325608E3C4A37BBB52FE728361E7324(L_41, NULL);
		RenderTexture_tBA90C4C3AD9EECCFDDCC632D97C29FAB80D60D27* L_43 = __this->___reflectionRenderTexture;
		RenderTargetIdentifier_tA528663AC6EB3911D8E91AA40F7070FA5455442B L_44;
		L_44 = RenderTargetIdentifier_op_Implicit_mBF13C6AE62DCEDDEFDC1C7305BE646FE99D2F24C(L_43, NULL);
		Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* L_45 = __this->___blurMaterial;
		NullCheck(L_40);
		CommandBuffer_Blit_m20AC38869B1D9D16C37E1A697B4EF3E2B0D12530(L_40, L_42, L_44, L_45, 2, NULL);
		int32_t L_46 = V_1;
		V_1 = ((int32_t)il2cpp_codegen_add(L_46, 1));
	}

IL_015e:
	{
		int32_t L_47 = V_1;
		int32_t L_48 = __this->___blurIterations;
		if ((((int32_t)L_47) < ((int32_t)L_48)))
		{
			goto IL_0119;
		}
	}
	{
		CommandBuffer_tB56007DC84EF56296C325EC32DD12AC1E3DC91F7* L_49 = __this->___commandBufferBlur;
		int32_t L_50 = V_0;
		NullCheck(L_49);
		CommandBuffer_ReleaseTemporaryRT_m4651A4B373DF432AA44F06A6F20852ED5996CC8E(L_49, L_50, NULL);
		Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* L_51 = __this->___reflectionCamera;
		CommandBuffer_tB56007DC84EF56296C325EC32DD12AC1E3DC91F7* L_52 = __this->___commandBufferBlur;
		NullCheck(L_51);
		Camera_AddCommandBuffer_m2C1C3C2D93CB62D569714B3FFA694CAB9BF81A9A(L_51, ((int32_t)20), L_52, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TCP2_PlanarReflection_ClearCommandBuffer_mE31CD5BC181ECC6CE57A89FE91FC54D7599A40AF (TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* L_0 = __this->___reflectionCamera;
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_1;
		L_1 = Object_op_Inequality_mD0BE578448EAA61948F25C32F8DD55AB1F778602(L_0, (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*)NULL, NULL);
		if (!L_1)
		{
			goto IL_002f;
		}
	}
	{
		Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* L_2 = __this->___reflectionCamera;
		NullCheck(L_2);
		int32_t L_3;
		L_3 = Camera_get_commandBufferCount_m168243A3A4E279C57AA6A5D0469B12B675854B50(L_2, NULL);
		if ((((int32_t)L_3) <= ((int32_t)0)))
		{
			goto IL_002f;
		}
	}
	{
		Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* L_4 = __this->___reflectionCamera;
		CommandBuffer_tB56007DC84EF56296C325EC32DD12AC1E3DC91F7* L_5 = __this->___commandBufferBlur;
		NullCheck(L_4);
		Camera_RemoveCommandBuffer_m7749BA282C14AA7E9E71A68E911F41D1B8429F11(L_4, ((int32_t)20), L_5, NULL);
	}

IL_002f:
	{
		CommandBuffer_tB56007DC84EF56296C325EC32DD12AC1E3DC91F7* L_6 = __this->___commandBufferBlur;
		if (!L_6)
		{
			goto IL_0054;
		}
	}
	{
		CommandBuffer_tB56007DC84EF56296C325EC32DD12AC1E3DC91F7* L_7 = __this->___commandBufferBlur;
		NullCheck(L_7);
		CommandBuffer_Clear_m4E1272BD1A0C162C9C26434E115279F42FA557C7(L_7, NULL);
		CommandBuffer_tB56007DC84EF56296C325EC32DD12AC1E3DC91F7* L_8 = __this->___commandBufferBlur;
		NullCheck(L_8);
		CommandBuffer_Release_m7D3C99D5B1598F727BD2E0E115FAD6A4E1A123A6(L_8, NULL);
		__this->___commandBufferBlur = (CommandBuffer_tB56007DC84EF56296C325EC32DD12AC1E3DC91F7*)NULL;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___commandBufferBlur), (void*)(CommandBuffer_tB56007DC84EF56296C325EC32DD12AC1E3DC91F7*)NULL);
	}

IL_0054:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TCP2_PlanarReflection_BeginCameraRendering_Bultin_m40D10D9F9A22782C50A54511880CC575F7C97655 (TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5* __this, Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* ___0_camera, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&GameObject_GetComponent_TisCamera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184_m3B3C11550E48AA36AFF82788636EB163CC51FEE6_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&GameObject_t76FEDD663AB33C991A9C9A23129337651094216F_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Type_t_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral0605457F01C020CA4816186DF7D9D058278904DD);
		s_Il2CppMethodInitialized = true;
	}
	GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* V_0 = NULL;
	{
		Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* L_0 = ___0_camera;
		NullCheck(L_0);
		int32_t L_1;
		L_1 = Camera_get_cameraType_m85434C4C986D2EAC04FBFA44B284840AFC497851(L_0, NULL);
		if (((int32_t)((int32_t)L_1&3)))
		{
			goto IL_000b;
		}
	}
	{
		return;
	}

IL_000b:
	{
		Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* L_2 = __this->___reflectionCamera;
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_3;
		L_3 = Object_op_Equality_mB6120F782D83091EF56A198FCEBCF066DB4A9605(L_2, (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*)NULL, NULL);
		if (!L_3)
		{
			goto IL_0074;
		}
	}
	{
		TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* L_4 = (TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB*)(TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB*)SZArrayNew(TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB_il2cpp_TypeInfo_var, (uint32_t)1);
		TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* L_5 = L_4;
		RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B L_6 = { reinterpret_cast<intptr_t> (Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184_0_0_0_var) };
		il2cpp_codegen_runtime_class_init_inline(Type_t_il2cpp_TypeInfo_var);
		Type_t* L_7;
		L_7 = Type_GetTypeFromHandle_m6062B81682F79A4D6DF2640692EE6D9987858C57(L_6, NULL);
		NullCheck(L_5);
		ArrayElementTypeCheck (L_5, L_7);
		(L_5)->SetAt(static_cast<il2cpp_array_size_t>(0), (Type_t*)L_7);
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_8 = (GameObject_t76FEDD663AB33C991A9C9A23129337651094216F*)il2cpp_codegen_object_new(GameObject_t76FEDD663AB33C991A9C9A23129337651094216F_il2cpp_TypeInfo_var);
		GameObject__ctor_m721D643351E55308EA4F5F41B67D5446D11C61F0(L_8, _stringLiteral0605457F01C020CA4816186DF7D9D058278904DD, L_5, NULL);
		V_0 = L_8;
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_9 = V_0;
		NullCheck(L_9);
		Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* L_10;
		L_10 = GameObject_GetComponent_TisCamera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184_m3B3C11550E48AA36AFF82788636EB163CC51FEE6(L_9, GameObject_GetComponent_TisCamera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184_m3B3C11550E48AA36AFF82788636EB163CC51FEE6_RuntimeMethod_var);
		__this->___reflectionCamera = L_10;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___reflectionCamera), (void*)L_10);
		Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* L_11 = __this->___reflectionCamera;
		NullCheck(L_11);
		Behaviour_set_enabled_mF1DCFE60EB09E0529FE9476CA804A3AA2D72B16A(L_11, (bool)0, NULL);
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_12 = V_0;
		NullCheck(L_12);
		Object_set_hideFlags_mACB8BFC903FB3B01BBD427753E791BF28B5E33D4(L_12, ((int32_t)21), NULL);
		TCP2_PlanarReflection_UpdateRenderTexture_m68EC26E08F46066F16884B9CA041AACCBB6F87E5(__this, NULL);
		TCP2_PlanarReflection_UpdateCommandBuffer_m992291EDB61AEA40C332DA5FC2E53C9E72E58D8F(__this, NULL);
		Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* L_13 = __this->___reflectionCamera;
		RenderTexture_tBA90C4C3AD9EECCFDDCC632D97C29FAB80D60D27* L_14 = __this->___reflectionRenderTexture;
		NullCheck(L_13);
		Camera_set_targetTexture_mE6C740F21A72DA47FB5B1D31D208710738A836C4(L_13, L_14, NULL);
	}

IL_0074:
	{
		Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* L_15 = ___0_camera;
		TCP2_PlanarReflection_RenderPlanarReflection_mE12045E540118DE967537D65D974D3F5650587E7(__this, L_15, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TCP2_PlanarReflection_BeginCameraRendering_URP_m2551C1514321AD13604A589B5EA33ED4D8B53D25 (TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5* __this, ScriptableRenderContext_t5AB09B3602BEB456E0DC3D53926D3A3BDAF08E36 ___0_context, Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* ___1_camera, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&GameObject_GetComponent_TisCamera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184_m3B3C11550E48AA36AFF82788636EB163CC51FEE6_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&GameObject_t76FEDD663AB33C991A9C9A23129337651094216F_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Type_t_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral0605457F01C020CA4816186DF7D9D058278904DD);
		s_Il2CppMethodInitialized = true;
	}
	GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* V_0 = NULL;
	{
		Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* L_0 = ___1_camera;
		NullCheck(L_0);
		int32_t L_1;
		L_1 = Camera_get_cameraType_m85434C4C986D2EAC04FBFA44B284840AFC497851(L_0, NULL);
		if (((int32_t)((int32_t)L_1&3)))
		{
			goto IL_000b;
		}
	}
	{
		return;
	}

IL_000b:
	{
		Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* L_2 = __this->___reflectionCamera;
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_3;
		L_3 = Object_op_Equality_mB6120F782D83091EF56A198FCEBCF066DB4A9605(L_2, (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*)NULL, NULL);
		if (!L_3)
		{
			goto IL_0074;
		}
	}
	{
		TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* L_4 = (TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB*)(TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB*)SZArrayNew(TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB_il2cpp_TypeInfo_var, (uint32_t)1);
		TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* L_5 = L_4;
		RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B L_6 = { reinterpret_cast<intptr_t> (Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184_0_0_0_var) };
		il2cpp_codegen_runtime_class_init_inline(Type_t_il2cpp_TypeInfo_var);
		Type_t* L_7;
		L_7 = Type_GetTypeFromHandle_m6062B81682F79A4D6DF2640692EE6D9987858C57(L_6, NULL);
		NullCheck(L_5);
		ArrayElementTypeCheck (L_5, L_7);
		(L_5)->SetAt(static_cast<il2cpp_array_size_t>(0), (Type_t*)L_7);
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_8 = (GameObject_t76FEDD663AB33C991A9C9A23129337651094216F*)il2cpp_codegen_object_new(GameObject_t76FEDD663AB33C991A9C9A23129337651094216F_il2cpp_TypeInfo_var);
		GameObject__ctor_m721D643351E55308EA4F5F41B67D5446D11C61F0(L_8, _stringLiteral0605457F01C020CA4816186DF7D9D058278904DD, L_5, NULL);
		V_0 = L_8;
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_9 = V_0;
		NullCheck(L_9);
		Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* L_10;
		L_10 = GameObject_GetComponent_TisCamera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184_m3B3C11550E48AA36AFF82788636EB163CC51FEE6(L_9, GameObject_GetComponent_TisCamera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184_m3B3C11550E48AA36AFF82788636EB163CC51FEE6_RuntimeMethod_var);
		__this->___reflectionCamera = L_10;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___reflectionCamera), (void*)L_10);
		Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* L_11 = __this->___reflectionCamera;
		NullCheck(L_11);
		Behaviour_set_enabled_mF1DCFE60EB09E0529FE9476CA804A3AA2D72B16A(L_11, (bool)0, NULL);
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_12 = V_0;
		NullCheck(L_12);
		Object_set_hideFlags_mACB8BFC903FB3B01BBD427753E791BF28B5E33D4(L_12, ((int32_t)20), NULL);
		TCP2_PlanarReflection_UpdateRenderTexture_m68EC26E08F46066F16884B9CA041AACCBB6F87E5(__this, NULL);
		TCP2_PlanarReflection_UpdateCommandBuffer_m992291EDB61AEA40C332DA5FC2E53C9E72E58D8F(__this, NULL);
		Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* L_13 = __this->___reflectionCamera;
		RenderTexture_tBA90C4C3AD9EECCFDDCC632D97C29FAB80D60D27* L_14 = __this->___reflectionRenderTexture;
		NullCheck(L_13);
		Camera_set_targetTexture_mE6C740F21A72DA47FB5B1D31D208710738A836C4(L_13, L_14, NULL);
	}

IL_0074:
	{
		ScriptableRenderContext_t5AB09B3602BEB456E0DC3D53926D3A3BDAF08E36 L_15 = ___0_context;
		Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* L_16 = ___1_camera;
		TCP2_PlanarReflection_RenderPlanarReflection_m25DFF719711FC13622E2F44BC6DDB0527B98AD30(__this, L_15, L_16, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TCP2_PlanarReflection_RenderPlanarReflection_mE12045E540118DE967537D65D974D3F5650587E7 (TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5* __this, Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* ___0_worldCamera, const RuntimeMethod* method) 
{
	ScriptableRenderContext_t5AB09B3602BEB456E0DC3D53926D3A3BDAF08E36 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		il2cpp_codegen_initobj((&V_0), sizeof(ScriptableRenderContext_t5AB09B3602BEB456E0DC3D53926D3A3BDAF08E36));
		ScriptableRenderContext_t5AB09B3602BEB456E0DC3D53926D3A3BDAF08E36 L_0 = V_0;
		Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* L_1 = ___0_worldCamera;
		TCP2_PlanarReflection_RenderPlanarReflection_m25DFF719711FC13622E2F44BC6DDB0527B98AD30(__this, L_0, L_1, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TCP2_PlanarReflection_RenderPlanarReflection_m25DFF719711FC13622E2F44BC6DDB0527B98AD30 (TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5* __this, ScriptableRenderContext_t5AB09B3602BEB456E0DC3D53926D3A3BDAF08E36 ___0_context, Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* ___1_worldCamera, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Component_GetComponent_TisRenderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF_mC91ACC92AD57CA6CA00991DAF1DB3830BCE07AF8_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&RenderTexture_tBA90C4C3AD9EECCFDDCC632D97C29FAB80D60D27_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ScriptableRenderContext_t5AB09B3602BEB456E0DC3D53926D3A3BDAF08E36_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&UniversalRenderPipeline_t54B4737DC500C08628C5BE283D8C583C14DED98F_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral8E612BB9EE9052F79F367719C40C1CFBB9BC452F);
		s_Il2CppMethodInitialized = true;
	}
	Renderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF* V_0 = NULL;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_1;
	memset((&V_1), 0, sizeof(V_1));
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_2;
	memset((&V_2), 0, sizeof(V_2));
	int32_t V_3 = 0;
	float V_4 = 0.0f;
	Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 V_5;
	memset((&V_5), 0, sizeof(V_5));
	Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6 V_6;
	memset((&V_6), 0, sizeof(V_6));
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_7;
	memset((&V_7), 0, sizeof(V_7));
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_8;
	memset((&V_8), 0, sizeof(V_8));
	Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 V_9;
	memset((&V_9), 0, sizeof(V_9));
	Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6 V_10;
	memset((&V_10), 0, sizeof(V_10));
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_11;
	memset((&V_11), 0, sizeof(V_11));
	RenderTexture_tBA90C4C3AD9EECCFDDCC632D97C29FAB80D60D27* V_12 = NULL;
	MaterialU5BU5D_t2B1D11C42DB07A4400C0535F92DBB87A2E346D3D* V_13 = NULL;
	int32_t V_14 = 0;
	Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* V_15 = NULL;
	{
		Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* L_0 = ___1_worldCamera;
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_1;
		L_1 = Object_op_Equality_mB6120F782D83091EF56A198FCEBCF066DB4A9605(L_0, (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*)NULL, NULL);
		if (!L_1)
		{
			goto IL_000a;
		}
	}
	{
		return;
	}

IL_000a:
	{
		Renderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF* L_2;
		L_2 = Component_GetComponent_TisRenderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF_mC91ACC92AD57CA6CA00991DAF1DB3830BCE07AF8(__this, Component_GetComponent_TisRenderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF_mC91ACC92AD57CA6CA00991DAF1DB3830BCE07AF8_RuntimeMethod_var);
		V_0 = L_2;
		bool L_3;
		L_3 = Behaviour_get_enabled_mAAC9F15E9EBF552217A5AE2681589CC0BFA300C1(__this, NULL);
		if (!L_3)
		{
			goto IL_0036;
		}
	}
	{
		Renderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF* L_4 = V_0;
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_5;
		L_5 = Object_op_Implicit_m93896EF7D68FA113C42D3FE2BC6F661FC7EF514A(L_4, NULL);
		if (!L_5)
		{
			goto IL_0036;
		}
	}
	{
		Renderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF* L_6 = V_0;
		NullCheck(L_6);
		Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* L_7;
		L_7 = Renderer_get_sharedMaterial_mA2E0CA0A564617FFC3E0E50947C6300082C35F81(L_6, NULL);
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_8;
		L_8 = Object_op_Implicit_m93896EF7D68FA113C42D3FE2BC6F661FC7EF514A(L_7, NULL);
		if (!L_8)
		{
			goto IL_0036;
		}
	}
	{
		Renderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF* L_9 = V_0;
		NullCheck(L_9);
		bool L_10;
		L_10 = Renderer_get_enabled_mFDDF363859AEC88105A925FA7EA341C077B09B54(L_9, NULL);
		if (L_10)
		{
			goto IL_0037;
		}
	}

IL_0036:
	{
		return;
	}

IL_0037:
	{
		il2cpp_codegen_runtime_class_init_inline(TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5_il2cpp_TypeInfo_var);
		bool L_11 = ((TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5_StaticFields*)il2cpp_codegen_static_fields_for(TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5_il2cpp_TypeInfo_var))->___s_InsideRendering;
		if (!L_11)
		{
			goto IL_003f;
		}
	}
	{
		return;
	}

IL_003f:
	{
		il2cpp_codegen_runtime_class_init_inline(TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5_il2cpp_TypeInfo_var);
		((TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5_StaticFields*)il2cpp_codegen_static_fields_for(TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5_il2cpp_TypeInfo_var))->___s_InsideRendering = (bool)1;
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_12;
		L_12 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		NullCheck(L_12);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_13;
		L_13 = Transform_get_position_m69CD5FA214FDAE7BB701552943674846C220FDE1(L_12, NULL);
		V_1 = L_13;
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_14;
		L_14 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		NullCheck(L_14);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_15;
		L_15 = Transform_get_up_mE47A9D9D96422224DD0539AA5524DA5440145BB2(L_14, NULL);
		V_2 = L_15;
		int32_t L_16;
		L_16 = QualitySettings_get_pixelLightCount_mBB36ED5F47B5841CEF44032058DC4A9815D3F339(NULL);
		V_3 = L_16;
		bool L_17 = __this->___disablePixelLights;
		if (!L_17)
		{
			goto IL_0071;
		}
	}
	{
		QualitySettings_set_pixelLightCount_mD49EDE3F96CB8D12A0CFD00F8A13179B204762E3(0, NULL);
	}

IL_0071:
	{
		Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* L_18 = __this->___reflectionCamera;
		Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* L_19 = ___1_worldCamera;
		NullCheck(L_18);
		Camera_CopyFrom_mFA5C3AB8E95EC4124249520ACEC6F7F25E5CDC52(L_18, L_19, NULL);
		bool L_20 = __this->___useCustomBackgroundColor;
		if (!L_20)
		{
			goto IL_00a2;
		}
	}
	{
		Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* L_21 = __this->___reflectionCamera;
		NullCheck(L_21);
		Camera_set_clearFlags_m66541D9CC43CBAA5FE7364A50D43CA5569FD4D93(L_21, 2, NULL);
		Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* L_22 = __this->___reflectionCamera;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_23 = __this->___backgroundColor;
		NullCheck(L_22);
		Camera_set_backgroundColor_m036FD8C316A93A0B168ACC89AFF16D396B872138(L_22, L_23, NULL);
	}

IL_00a2:
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_24 = V_2;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_25 = V_1;
		float L_26;
		L_26 = Vector3_Dot_mBB86BB940AA0A32FA7D3C02AC42E5BC7095A5D52_inline(L_24, L_25, NULL);
		float L_27 = __this->___clipPlaneOffset;
		V_4 = ((float)il2cpp_codegen_subtract(((-L_26)), L_27));
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_28 = V_2;
		float L_29 = L_28.___x;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_30 = V_2;
		float L_31 = L_30.___y;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_32 = V_2;
		float L_33 = L_32.___z;
		float L_34 = V_4;
		Vector4__ctor_m96B2CD8B862B271F513AF0BDC2EABD58E4DBC813_inline((&V_5), L_29, L_31, L_33, L_34, NULL);
		Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6 L_35;
		L_35 = Matrix4x4_get_zero_m5D5F0475AD231C2C6BE5A9C80E11E24013B1B827(NULL);
		V_6 = L_35;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_36 = V_5;
		il2cpp_codegen_runtime_class_init_inline(TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5_il2cpp_TypeInfo_var);
		TCP2_PlanarReflection_CalculateReflectionMatrix_m6583F25BD212A78F820579D3C46367BEACA0F755((&V_6), L_36, NULL);
		Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* L_37 = ___1_worldCamera;
		NullCheck(L_37);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_38;
		L_38 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(L_37, NULL);
		NullCheck(L_38);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_39;
		L_39 = Transform_get_position_m69CD5FA214FDAE7BB701552943674846C220FDE1(L_38, NULL);
		V_7 = L_39;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_40 = V_7;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_41;
		L_41 = Matrix4x4_MultiplyPoint_m20E910B65693559BFDE99382472D8DD02C862E7E((&V_6), L_40, NULL);
		V_8 = L_41;
		Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* L_42 = __this->___reflectionCamera;
		Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* L_43 = ___1_worldCamera;
		NullCheck(L_43);
		Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6 L_44;
		L_44 = Camera_get_worldToCameraMatrix_m48E324BD76706A316A1701EFC6A3DEC7DFB2FF40(L_43, NULL);
		Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6 L_45 = V_6;
		Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6 L_46;
		L_46 = Matrix4x4_op_Multiply_m75E91775655DCA8DFC8EDE0AB787285BB3935162(L_44, L_45, NULL);
		NullCheck(L_42);
		Camera_set_worldToCameraMatrix_mC199F02E435CE7261F7EECD1FD78A33EA96ABC0D(L_42, L_46, NULL);
		Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* L_47 = __this->___reflectionCamera;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_48 = V_1;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_49 = V_2;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_50;
		L_50 = TCP2_PlanarReflection_CameraSpacePlane_mA6702267FBBA6A838A9E4254F1479407E43FA086(__this, L_47, L_48, L_49, (1.0f), NULL);
		V_9 = L_50;
		Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* L_51 = ___1_worldCamera;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_52 = V_9;
		NullCheck(L_51);
		Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6 L_53;
		L_53 = Camera_CalculateObliqueMatrix_mBBFA94C033BB0C3C21B182F732B2155913E46609(L_51, L_52, NULL);
		V_10 = L_53;
		Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* L_54 = __this->___reflectionCamera;
		Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6 L_55 = V_10;
		NullCheck(L_54);
		Camera_set_projectionMatrix_m351820E6903139402FFFF40221B32D0C52B5A094(L_54, L_55, NULL);
		Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* L_56 = __this->___reflectionCamera;
		RenderTexture_tBA90C4C3AD9EECCFDDCC632D97C29FAB80D60D27* L_57 = __this->___reflectionRenderTexture;
		NullCheck(L_56);
		Camera_set_targetTexture_mE6C740F21A72DA47FB5B1D31D208710738A836C4(L_56, L_57, NULL);
		Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* L_58 = __this->___reflectionCamera;
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_59;
		L_59 = Component_get_gameObject_m57AEFBB14DB39EC476F740BA000E170355DE691B(__this, NULL);
		NullCheck(L_59);
		int32_t L_60;
		L_60 = GameObject_get_layer_m108902B9C89E9F837CE06B9942AA42307450FEAF(L_59, NULL);
		LayerMask_t97CB6BDADEDC3D6423C7BCFEA7F86DA2EC6241DB* L_61 = (LayerMask_t97CB6BDADEDC3D6423C7BCFEA7F86DA2EC6241DB*)(&__this->___reflectLayers);
		int32_t L_62;
		L_62 = LayerMask_get_value_m70CBE32210A1F0FD4ECB850285DA90ED57B87974(L_61, NULL);
		NullCheck(L_58);
		Camera_set_cullingMask_m14F426710530BA8FA53AEC02F79C418AA558CB32(L_58, ((int32_t)(((~((int32_t)(1<<((int32_t)(L_60&((int32_t)31)))))))&L_62)), NULL);
		GL_set_invertCulling_m832ECFBD8944553346CE28B729756E3815FE7B85((bool)1, NULL);
		Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* L_63 = __this->___reflectionCamera;
		NullCheck(L_63);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_64;
		L_64 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(L_63, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_65 = V_8;
		NullCheck(L_64);
		Transform_set_position_mA1A817124BB41B685043DED2A9BA48CDF37C4156(L_64, L_65, NULL);
		Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* L_66 = ___1_worldCamera;
		NullCheck(L_66);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_67;
		L_67 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(L_66, NULL);
		NullCheck(L_67);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_68;
		L_68 = Transform_get_eulerAngles_mCAAF48EFCF628F1ED91C2FFE75A4FD19C039DD6A(L_67, NULL);
		V_11 = L_68;
		Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* L_69 = __this->___reflectionCamera;
		NullCheck(L_69);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_70;
		L_70 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(L_69, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_71 = V_11;
		float L_72 = L_71.___y;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_73 = V_11;
		float L_74 = L_73.___z;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_75;
		memset((&L_75), 0, sizeof(L_75));
		Vector3__ctor_m376936E6B999EF1ECBE57D990A386303E2283DE0_inline((&L_75), (0.0f), L_72, L_74, NULL);
		NullCheck(L_70);
		Transform_set_eulerAngles_m9F0BC484A7915A51FAB87230644229B75BACA004(L_70, L_75, NULL);
		bool L_76 = __this->___isURP;
		if (!L_76)
		{
			goto IL_01fc;
		}
	}
	{
		ScriptableRenderContext_t5AB09B3602BEB456E0DC3D53926D3A3BDAF08E36 L_77 = ___0_context;
		Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* L_78 = __this->___reflectionCamera;
		il2cpp_codegen_runtime_class_init_inline(UniversalRenderPipeline_t54B4737DC500C08628C5BE283D8C583C14DED98F_il2cpp_TypeInfo_var);
		UniversalRenderPipeline_RenderSingleCamera_mA32C19DAB85E97DADFAB144453EC6CB23A91DB8F(L_77, L_78, NULL);
		bool L_79 = __this->___applyBlur;
		if (!L_79)
		{
			goto IL_0353;
		}
	}
	{
		CommandBuffer_tB56007DC84EF56296C325EC32DD12AC1E3DC91F7* L_80 = __this->___commandBufferBlur;
		if (!L_80)
		{
			goto IL_0353;
		}
	}
	{
		CommandBuffer_tB56007DC84EF56296C325EC32DD12AC1E3DC91F7* L_81 = __this->___commandBufferBlur;
		il2cpp_codegen_runtime_class_init_inline(ScriptableRenderContext_t5AB09B3602BEB456E0DC3D53926D3A3BDAF08E36_il2cpp_TypeInfo_var);
		ScriptableRenderContext_ExecuteCommandBuffer_mBAE37DFC699B7167A6E2C59012066C44A31E9896((&___0_context), L_81, NULL);
		goto IL_0353;
	}

IL_01fc:
	{
		bool L_82 = __this->___applyBlur;
		if (!L_82)
		{
			goto IL_0301;
		}
	}
	{
		bool L_83 = __this->___useBlurDepth;
		if (!L_83)
		{
			goto IL_0301;
		}
	}
	{
		Shader_tADC867D36B7876EE22427FAA2CE485105F4EE692* L_84 = __this->___reflectionDepthShader;
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_85;
		L_85 = Object_op_Equality_mB6120F782D83091EF56A198FCEBCF066DB4A9605(L_84, (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*)NULL, NULL);
		if (!L_85)
		{
			goto IL_0230;
		}
	}
	{
		Shader_tADC867D36B7876EE22427FAA2CE485105F4EE692* L_86;
		L_86 = Shader_Find_m183AA54F78320212DDEC811592F98456898A41C5(_stringLiteral8E612BB9EE9052F79F367719C40C1CFBB9BC452F, NULL);
		__this->___reflectionDepthShader = L_86;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___reflectionDepthShader), (void*)L_86);
	}

IL_0230:
	{
		RenderTexture_tBA90C4C3AD9EECCFDDCC632D97C29FAB80D60D27* L_87 = __this->___reflectionDepthRenderTexture;
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_88;
		L_88 = Object_op_Equality_mB6120F782D83091EF56A198FCEBCF066DB4A9605(L_87, (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*)NULL, NULL);
		if (!L_88)
		{
			goto IL_0270;
		}
	}
	{
		int32_t L_89 = __this->___textureSize;
		int32_t L_90 = __this->___textureSize;
		RenderTexture_tBA90C4C3AD9EECCFDDCC632D97C29FAB80D60D27* L_91 = (RenderTexture_tBA90C4C3AD9EECCFDDCC632D97C29FAB80D60D27*)il2cpp_codegen_object_new(RenderTexture_tBA90C4C3AD9EECCFDDCC632D97C29FAB80D60D27_il2cpp_TypeInfo_var);
		RenderTexture__ctor_m68A1B9CAA1BE0B597C5F4895C296E21502D0C962(L_91, L_89, L_90, ((int32_t)16), ((int32_t)15), 1, NULL);
		__this->___reflectionDepthRenderTexture = L_91;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___reflectionDepthRenderTexture), (void*)L_91);
		Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* L_92 = __this->___blurMaterial;
		il2cpp_codegen_runtime_class_init_inline(TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5_il2cpp_TypeInfo_var);
		int32_t L_93;
		L_93 = TCP2_PlanarReflection_get_ShaderID_ReflectionDepthTex_mA4D9AB261F8369C429B72CD6DC811B331EA5D0D2(NULL);
		RenderTexture_tBA90C4C3AD9EECCFDDCC632D97C29FAB80D60D27* L_94 = __this->___reflectionDepthRenderTexture;
		NullCheck(L_92);
		Material_SetTexture_mA9F8461850AAB88F992E9C6FA6F24C2E050B83FD(L_92, L_93, L_94, NULL);
	}

IL_0270:
	{
		Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* L_95 = __this->___reflectionCamera;
		NullCheck(L_95);
		RenderTexture_tBA90C4C3AD9EECCFDDCC632D97C29FAB80D60D27* L_96;
		L_96 = Camera_get_targetTexture_mC856D7FF8351476068D04E245E4F08F5C56A55BD(L_95, NULL);
		V_12 = L_96;
		Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* L_97 = __this->___reflectionCamera;
		RenderTexture_tBA90C4C3AD9EECCFDDCC632D97C29FAB80D60D27* L_98 = __this->___reflectionDepthRenderTexture;
		NullCheck(L_97);
		Camera_set_targetTexture_mE6C740F21A72DA47FB5B1D31D208710738A836C4(L_97, L_98, NULL);
		Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* L_99 = __this->___reflectionCamera;
		Shader_tADC867D36B7876EE22427FAA2CE485105F4EE692* L_100 = __this->___reflectionDepthShader;
		NullCheck(L_99);
		Camera_RenderWithShader_m3A63E9183D339D1273BD4604DB72E09C9FADA7C4(L_99, L_100, (String_t*)NULL, NULL);
		Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* L_101 = __this->___reflectionCamera;
		RenderTexture_tBA90C4C3AD9EECCFDDCC632D97C29FAB80D60D27* L_102 = V_12;
		NullCheck(L_101);
		Camera_set_targetTexture_mE6C740F21A72DA47FB5B1D31D208710738A836C4(L_101, L_102, NULL);
		Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* L_103 = __this->___blurMaterial;
		il2cpp_codegen_runtime_class_init_inline(TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5_il2cpp_TypeInfo_var);
		int32_t L_104;
		L_104 = TCP2_PlanarReflection_get_ShaderID_ReflectivePlaneY_m5BD4FD60BE5D9E4CDC43CDCF7C7FE46BE5B0D205(NULL);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_105;
		L_105 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		NullCheck(L_105);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_106;
		L_106 = Transform_get_position_m69CD5FA214FDAE7BB701552943674846C220FDE1(L_105, NULL);
		float L_107 = L_106.___y;
		float L_108 = __this->___clipPlaneOffset;
		NullCheck(L_103);
		Material_SetFloat_m3ECFD92072347A8620254F014865984FA68211A8(L_103, L_104, ((float)il2cpp_codegen_add(L_107, L_108)), NULL);
		Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* L_109 = __this->___blurMaterial;
		int32_t L_110;
		L_110 = TCP2_PlanarReflection_get_ShaderID_ReflectionDepthRange_mE7B06E2DCEED01ACE49728291886D9B9E9C00800(NULL);
		float L_111 = __this->___blurDepthRange;
		NullCheck(L_109);
		Material_SetFloat_m3ECFD92072347A8620254F014865984FA68211A8(L_109, L_110, L_111, NULL);
		Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* L_112 = __this->___blurMaterial;
		int32_t L_113;
		L_113 = TCP2_PlanarReflection_get_ShaderID_UseReflectionDepth_mBE5B33FC55A2969E12CFE9C348012732A67DB845(NULL);
		NullCheck(L_112);
		Material_SetFloat_m3ECFD92072347A8620254F014865984FA68211A8(L_112, L_113, (1.0f), NULL);
		goto IL_0348;
	}

IL_0301:
	{
		bool L_114 = __this->___applyBlur;
		if (!L_114)
		{
			goto IL_0328;
		}
	}
	{
		bool L_115 = __this->___useBlurDepth;
		if (L_115)
		{
			goto IL_0328;
		}
	}
	{
		Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* L_116 = __this->___blurMaterial;
		il2cpp_codegen_runtime_class_init_inline(TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5_il2cpp_TypeInfo_var);
		int32_t L_117;
		L_117 = TCP2_PlanarReflection_get_ShaderID_UseReflectionDepth_mBE5B33FC55A2969E12CFE9C348012732A67DB845(NULL);
		NullCheck(L_116);
		Material_SetFloat_m3ECFD92072347A8620254F014865984FA68211A8(L_116, L_117, (0.0f), NULL);
		goto IL_0348;
	}

IL_0328:
	{
		RenderTexture_tBA90C4C3AD9EECCFDDCC632D97C29FAB80D60D27* L_118 = __this->___reflectionDepthRenderTexture;
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_119;
		L_119 = Object_op_Inequality_mD0BE578448EAA61948F25C32F8DD55AB1F778602(L_118, (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*)NULL, NULL);
		if (!L_119)
		{
			goto IL_0348;
		}
	}
	{
		RenderTexture_tBA90C4C3AD9EECCFDDCC632D97C29FAB80D60D27* L_120 = __this->___reflectionDepthRenderTexture;
		NullCheck(L_120);
		RenderTexture_Release_mE7399D6187A0E38945D2913D0FFB41247143AB1E(L_120, NULL);
		__this->___reflectionDepthRenderTexture = (RenderTexture_tBA90C4C3AD9EECCFDDCC632D97C29FAB80D60D27*)NULL;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___reflectionDepthRenderTexture), (void*)(RenderTexture_tBA90C4C3AD9EECCFDDCC632D97C29FAB80D60D27*)NULL);
	}

IL_0348:
	{
		Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* L_121 = __this->___reflectionCamera;
		NullCheck(L_121);
		Camera_Render_m6089001EB6710DA9A21C87185D65922F13A24509(L_121, NULL);
	}

IL_0353:
	{
		Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* L_122 = __this->___reflectionCamera;
		NullCheck(L_122);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_123;
		L_123 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(L_122, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_124 = V_7;
		NullCheck(L_123);
		Transform_set_position_mA1A817124BB41B685043DED2A9BA48CDF37C4156(L_123, L_124, NULL);
		GL_set_invertCulling_m832ECFBD8944553346CE28B729756E3815FE7B85((bool)0, NULL);
		Renderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF* L_125 = V_0;
		NullCheck(L_125);
		MaterialU5BU5D_t2B1D11C42DB07A4400C0535F92DBB87A2E346D3D* L_126;
		L_126 = Renderer_get_sharedMaterials_m0B61AFD8EDA35A70C796FFB2F28BB62380051ABF(L_125, NULL);
		V_13 = L_126;
		V_14 = 0;
		goto IL_03c5;
	}

IL_0378:
	{
		MaterialU5BU5D_t2B1D11C42DB07A4400C0535F92DBB87A2E346D3D* L_127 = V_13;
		int32_t L_128 = V_14;
		NullCheck(L_127);
		int32_t L_129 = L_128;
		Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* L_130 = (L_127)->GetAt(static_cast<il2cpp_array_size_t>(L_129));
		V_15 = L_130;
		Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* L_131 = V_15;
		il2cpp_codegen_runtime_class_init_inline(TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5_il2cpp_TypeInfo_var);
		int32_t L_132;
		L_132 = TCP2_PlanarReflection_get_ShaderID_ReflectionTex_m56E78BBCE25DAFF87FEE7FD8A2831F918F84B124(NULL);
		NullCheck(L_131);
		bool L_133;
		L_133 = Material_HasProperty_m52E2D3BC3049B8B228149E023CD73C34B05A5222(L_131, L_132, NULL);
		if (!L_133)
		{
			goto IL_039f;
		}
	}
	{
		Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* L_134 = V_15;
		il2cpp_codegen_runtime_class_init_inline(TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5_il2cpp_TypeInfo_var);
		int32_t L_135;
		L_135 = TCP2_PlanarReflection_get_ShaderID_ReflectionTex_m56E78BBCE25DAFF87FEE7FD8A2831F918F84B124(NULL);
		RenderTexture_tBA90C4C3AD9EECCFDDCC632D97C29FAB80D60D27* L_136 = __this->___reflectionRenderTexture;
		NullCheck(L_134);
		Material_SetTexture_mA9F8461850AAB88F992E9C6FA6F24C2E050B83FD(L_134, L_135, L_136, NULL);
	}

IL_039f:
	{
		Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* L_137 = V_15;
		il2cpp_codegen_runtime_class_init_inline(TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5_il2cpp_TypeInfo_var);
		int32_t L_138;
		L_138 = TCP2_PlanarReflection_get_ShaderID_ReflectionDepthTex_mA4D9AB261F8369C429B72CD6DC811B331EA5D0D2(NULL);
		NullCheck(L_137);
		bool L_139;
		L_139 = Material_HasProperty_m52E2D3BC3049B8B228149E023CD73C34B05A5222(L_137, L_138, NULL);
		if (!L_139)
		{
			goto IL_03bf;
		}
	}
	{
		Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* L_140 = V_15;
		il2cpp_codegen_runtime_class_init_inline(TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5_il2cpp_TypeInfo_var);
		int32_t L_141;
		L_141 = TCP2_PlanarReflection_get_ShaderID_ReflectionDepthTex_mA4D9AB261F8369C429B72CD6DC811B331EA5D0D2(NULL);
		RenderTexture_tBA90C4C3AD9EECCFDDCC632D97C29FAB80D60D27* L_142 = __this->___reflectionDepthRenderTexture;
		NullCheck(L_140);
		Material_SetTexture_mA9F8461850AAB88F992E9C6FA6F24C2E050B83FD(L_140, L_141, L_142, NULL);
	}

IL_03bf:
	{
		int32_t L_143 = V_14;
		V_14 = ((int32_t)il2cpp_codegen_add(L_143, 1));
	}

IL_03c5:
	{
		int32_t L_144 = V_14;
		MaterialU5BU5D_t2B1D11C42DB07A4400C0535F92DBB87A2E346D3D* L_145 = V_13;
		NullCheck(L_145);
		if ((((int32_t)L_144) < ((int32_t)((int32_t)(((RuntimeArray*)L_145)->max_length)))))
		{
			goto IL_0378;
		}
	}
	{
		bool L_146 = __this->___disablePixelLights;
		if (!L_146)
		{
			goto IL_03db;
		}
	}
	{
		int32_t L_147 = V_3;
		QualitySettings_set_pixelLightCount_mD49EDE3F96CB8D12A0CFD00F8A13179B204762E3(L_147, NULL);
	}

IL_03db:
	{
		il2cpp_codegen_runtime_class_init_inline(TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5_il2cpp_TypeInfo_var);
		((TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5_StaticFields*)il2cpp_codegen_static_fields_for(TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5_il2cpp_TypeInfo_var))->___s_InsideRendering = (bool)0;
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 TCP2_PlanarReflection_CameraSpacePlane_mA6702267FBBA6A838A9E4254F1479407E43FA086 (TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5* __this, Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* ___0_cam, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___1_pos, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___2_normal, float ___3_sideSign, const RuntimeMethod* method) 
{
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_0;
	memset((&V_0), 0, sizeof(V_0));
	Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6 V_1;
	memset((&V_1), 0, sizeof(V_1));
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_2;
	memset((&V_2), 0, sizeof(V_2));
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_3;
	memset((&V_3), 0, sizeof(V_3));
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_4;
	memset((&V_4), 0, sizeof(V_4));
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = ___1_pos;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_1 = ___2_normal;
		float L_2 = __this->___clipPlaneOffset;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_3;
		L_3 = Vector3_op_Multiply_m87BA7C578F96C8E49BB07088DAAC4649F83B0353_inline(L_1, L_2, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_4;
		L_4 = Vector3_op_Addition_m78C0EC70CB66E8DCAC225743D82B268DAEE92067_inline(L_0, L_3, NULL);
		V_0 = L_4;
		Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* L_5 = ___0_cam;
		NullCheck(L_5);
		Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6 L_6;
		L_6 = Camera_get_worldToCameraMatrix_m48E324BD76706A316A1701EFC6A3DEC7DFB2FF40(L_5, NULL);
		V_1 = L_6;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_7 = V_0;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_8;
		L_8 = Matrix4x4_MultiplyPoint_m20E910B65693559BFDE99382472D8DD02C862E7E((&V_1), L_7, NULL);
		V_2 = L_8;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_9 = ___2_normal;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_10;
		L_10 = Matrix4x4_MultiplyVector_mFD12F86A473E90BBB0002149ABA3917B2A518937((&V_1), L_9, NULL);
		V_4 = L_10;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_11;
		L_11 = Vector3_get_normalized_m736BBF65D5CDA7A18414370D15B4DFCC1E466F07_inline((&V_4), NULL);
		float L_12 = ___3_sideSign;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_13;
		L_13 = Vector3_op_Multiply_m87BA7C578F96C8E49BB07088DAAC4649F83B0353_inline(L_11, L_12, NULL);
		V_3 = L_13;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_14 = V_3;
		float L_15 = L_14.___x;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_16 = V_3;
		float L_17 = L_16.___y;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_18 = V_3;
		float L_19 = L_18.___z;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_20 = V_2;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_21 = V_3;
		float L_22;
		L_22 = Vector3_Dot_mBB86BB940AA0A32FA7D3C02AC42E5BC7095A5D52_inline(L_20, L_21, NULL);
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_23;
		memset((&L_23), 0, sizeof(L_23));
		Vector4__ctor_m96B2CD8B862B271F513AF0BDC2EABD58E4DBC813_inline((&L_23), L_15, L_17, L_19, ((-L_22)), NULL);
		return L_23;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TCP2_PlanarReflection_CalculateReflectionMatrix_m6583F25BD212A78F820579D3C46367BEACA0F755 (Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6* ___0_reflectionMat, Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 ___1_plane, const RuntimeMethod* method) 
{
	{
		Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6* L_0 = ___0_reflectionMat;
		float L_1;
		L_1 = Vector4_get_Item_mB1D001A235857569E479FB799EF77C52391D19EF_inline((&___1_plane), 0, NULL);
		float L_2;
		L_2 = Vector4_get_Item_mB1D001A235857569E479FB799EF77C52391D19EF_inline((&___1_plane), 0, NULL);
		L_0->___m00 = ((float)il2cpp_codegen_subtract((1.0f), ((float)il2cpp_codegen_multiply(((float)il2cpp_codegen_multiply((2.0f), L_1)), L_2))));
		Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6* L_3 = ___0_reflectionMat;
		float L_4;
		L_4 = Vector4_get_Item_mB1D001A235857569E479FB799EF77C52391D19EF_inline((&___1_plane), 0, NULL);
		float L_5;
		L_5 = Vector4_get_Item_mB1D001A235857569E479FB799EF77C52391D19EF_inline((&___1_plane), 1, NULL);
		L_3->___m01 = ((float)il2cpp_codegen_multiply(((float)il2cpp_codegen_multiply((-2.0f), L_4)), L_5));
		Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6* L_6 = ___0_reflectionMat;
		float L_7;
		L_7 = Vector4_get_Item_mB1D001A235857569E479FB799EF77C52391D19EF_inline((&___1_plane), 0, NULL);
		float L_8;
		L_8 = Vector4_get_Item_mB1D001A235857569E479FB799EF77C52391D19EF_inline((&___1_plane), 2, NULL);
		L_6->___m02 = ((float)il2cpp_codegen_multiply(((float)il2cpp_codegen_multiply((-2.0f), L_7)), L_8));
		Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6* L_9 = ___0_reflectionMat;
		float L_10;
		L_10 = Vector4_get_Item_mB1D001A235857569E479FB799EF77C52391D19EF_inline((&___1_plane), 3, NULL);
		float L_11;
		L_11 = Vector4_get_Item_mB1D001A235857569E479FB799EF77C52391D19EF_inline((&___1_plane), 0, NULL);
		L_9->___m03 = ((float)il2cpp_codegen_multiply(((float)il2cpp_codegen_multiply((-2.0f), L_10)), L_11));
		Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6* L_12 = ___0_reflectionMat;
		float L_13;
		L_13 = Vector4_get_Item_mB1D001A235857569E479FB799EF77C52391D19EF_inline((&___1_plane), 1, NULL);
		float L_14;
		L_14 = Vector4_get_Item_mB1D001A235857569E479FB799EF77C52391D19EF_inline((&___1_plane), 0, NULL);
		L_12->___m10 = ((float)il2cpp_codegen_multiply(((float)il2cpp_codegen_multiply((-2.0f), L_13)), L_14));
		Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6* L_15 = ___0_reflectionMat;
		float L_16;
		L_16 = Vector4_get_Item_mB1D001A235857569E479FB799EF77C52391D19EF_inline((&___1_plane), 1, NULL);
		float L_17;
		L_17 = Vector4_get_Item_mB1D001A235857569E479FB799EF77C52391D19EF_inline((&___1_plane), 1, NULL);
		L_15->___m11 = ((float)il2cpp_codegen_subtract((1.0f), ((float)il2cpp_codegen_multiply(((float)il2cpp_codegen_multiply((2.0f), L_16)), L_17))));
		Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6* L_18 = ___0_reflectionMat;
		float L_19;
		L_19 = Vector4_get_Item_mB1D001A235857569E479FB799EF77C52391D19EF_inline((&___1_plane), 1, NULL);
		float L_20;
		L_20 = Vector4_get_Item_mB1D001A235857569E479FB799EF77C52391D19EF_inline((&___1_plane), 2, NULL);
		L_18->___m12 = ((float)il2cpp_codegen_multiply(((float)il2cpp_codegen_multiply((-2.0f), L_19)), L_20));
		Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6* L_21 = ___0_reflectionMat;
		float L_22;
		L_22 = Vector4_get_Item_mB1D001A235857569E479FB799EF77C52391D19EF_inline((&___1_plane), 3, NULL);
		float L_23;
		L_23 = Vector4_get_Item_mB1D001A235857569E479FB799EF77C52391D19EF_inline((&___1_plane), 1, NULL);
		L_21->___m13 = ((float)il2cpp_codegen_multiply(((float)il2cpp_codegen_multiply((-2.0f), L_22)), L_23));
		Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6* L_24 = ___0_reflectionMat;
		float L_25;
		L_25 = Vector4_get_Item_mB1D001A235857569E479FB799EF77C52391D19EF_inline((&___1_plane), 2, NULL);
		float L_26;
		L_26 = Vector4_get_Item_mB1D001A235857569E479FB799EF77C52391D19EF_inline((&___1_plane), 0, NULL);
		L_24->___m20 = ((float)il2cpp_codegen_multiply(((float)il2cpp_codegen_multiply((-2.0f), L_25)), L_26));
		Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6* L_27 = ___0_reflectionMat;
		float L_28;
		L_28 = Vector4_get_Item_mB1D001A235857569E479FB799EF77C52391D19EF_inline((&___1_plane), 2, NULL);
		float L_29;
		L_29 = Vector4_get_Item_mB1D001A235857569E479FB799EF77C52391D19EF_inline((&___1_plane), 1, NULL);
		L_27->___m21 = ((float)il2cpp_codegen_multiply(((float)il2cpp_codegen_multiply((-2.0f), L_28)), L_29));
		Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6* L_30 = ___0_reflectionMat;
		float L_31;
		L_31 = Vector4_get_Item_mB1D001A235857569E479FB799EF77C52391D19EF_inline((&___1_plane), 2, NULL);
		float L_32;
		L_32 = Vector4_get_Item_mB1D001A235857569E479FB799EF77C52391D19EF_inline((&___1_plane), 2, NULL);
		L_30->___m22 = ((float)il2cpp_codegen_subtract((1.0f), ((float)il2cpp_codegen_multiply(((float)il2cpp_codegen_multiply((2.0f), L_31)), L_32))));
		Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6* L_33 = ___0_reflectionMat;
		float L_34;
		L_34 = Vector4_get_Item_mB1D001A235857569E479FB799EF77C52391D19EF_inline((&___1_plane), 3, NULL);
		float L_35;
		L_35 = Vector4_get_Item_mB1D001A235857569E479FB799EF77C52391D19EF_inline((&___1_plane), 2, NULL);
		L_33->___m23 = ((float)il2cpp_codegen_multiply(((float)il2cpp_codegen_multiply((-2.0f), L_34)), L_35));
		Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6* L_36 = ___0_reflectionMat;
		L_36->___m30 = (0.0f);
		Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6* L_37 = ___0_reflectionMat;
		L_37->___m31 = (0.0f);
		Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6* L_38 = ___0_reflectionMat;
		L_38->___m32 = (0.0f);
		Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6* L_39 = ___0_reflectionMat;
		L_39->___m33 = (1.0f);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TCP2_PlanarReflection__ctor_m87E2C90CA9C833B5B30AED8F8989F93C81552477 (TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5* __this, const RuntimeMethod* method) 
{
	{
		__this->___textureSize = ((int32_t)1024);
		__this->___renderTextureFormat = 7;
		LayerMask_t97CB6BDADEDC3D6423C7BCFEA7F86DA2EC6241DB L_0;
		L_0 = LayerMask_op_Implicit_m01C8996A2CB2085328B9C33539C43139660D8222((-1), NULL);
		__this->___reflectLayers = L_0;
		__this->___clipPlaneOffset = (0.0700000003f);
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_1;
		L_1 = Color_get_black_mB50217951591A045844C61E7FF31EEE3FEF16737_inline(NULL);
		__this->___backgroundColor = L_1;
		__this->___blurIterations = 1;
		__this->___blurDistance = (1.0f);
		__this->___blurDepthRange = (2.0f);
		MonoBehaviour__ctor_m592DB0105CA0BC97AA1C5F4AD27B12D68A3B7C1E(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TCP2_PlanarReflection__cctor_m78017627D581CE9DC88156EA0A0590A9F5E69932 (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		((TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5_StaticFields*)il2cpp_codegen_static_fields_for(TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5_il2cpp_TypeInfo_var))->____ShaderID_ReflectionTex = (-1);
		((TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5_StaticFields*)il2cpp_codegen_static_fields_for(TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5_il2cpp_TypeInfo_var))->____ShaderID_ReflectionDepthTex = (-1);
		((TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5_StaticFields*)il2cpp_codegen_static_fields_for(TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5_il2cpp_TypeInfo_var))->____ShaderID_ReflectivePlaneY = (-1);
		((TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5_StaticFields*)il2cpp_codegen_static_fields_for(TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5_il2cpp_TypeInfo_var))->____ShaderID_ReflectionDepthRange = (-1);
		((TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5_StaticFields*)il2cpp_codegen_static_fields_for(TCP2_PlanarReflection_tD111F602323876FF3EB8EB46730347E353E3BAC5_il2cpp_TypeInfo_var))->____ShaderID_UseReflectionDepth = (-1);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Shader_tADC867D36B7876EE22427FAA2CE485105F4EE692* TCP2_RuntimeUtils_GetShaderWithKeywords_mE11554E03F5B2A42F0233E7128B06AF30D30D69A (Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* ___0_material, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Enumerator_Dispose_mDF603E9BDFD97F66D220C8F24CA68AEFE7A5E9EF_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Enumerator_MoveNext_mFFBA7CC0534330311AFFABB1789CD7C93F6B86DA_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Enumerator_get_Current_m9972E774645548D767A142EE3C74E157C0AF52FF_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_GetEnumerator_m116DF5C3E10600177E00AD6F58CBEAE4085708DE_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TCP2_RuntimeUtils_t5E9598A60187D562FEDFC57BE19768E002F7129C_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral2386E77CF610F786B06A91AF2C1B3FD2282D2745);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralBCA6F238331EF68017D1ADF8BEADDA03900A1940);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralCB414C73F9E54AF3AD8F7CFB654C37B3A3B13925);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralCF08D47AA950197FBF22492248D649A8126E68AB);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralDB784C0FDB5E2F468943EF04EB13EE0E6135421C);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralFA855702F3E0239B3B0D1F427461543C440733A6);
		s_Il2CppMethodInitialized = true;
	}
	String_t* V_0 = NULL;
	String_t* V_1 = NULL;
	String_t* V_2 = NULL;
	Enumerator_t8BACEFB05E69B10743F619D20823D840EB5736CB V_3;
	memset((&V_3), 0, sizeof(V_3));
	StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* V_4 = NULL;
	StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* V_5 = NULL;
	int32_t V_6 = 0;
	String_t* V_7 = NULL;
	int32_t V_8 = 0;
	int32_t G_B3_0 = 0;
	String_t* G_B6_0 = NULL;
	{
		Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* L_0 = ___0_material;
		NullCheck(L_0);
		Shader_tADC867D36B7876EE22427FAA2CE485105F4EE692* L_1;
		L_1 = Material_get_shader_m8B0C11AE6F2AD7DE30AF52D3195EB716F7A71983(L_0, NULL);
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_2;
		L_2 = Object_op_Inequality_mD0BE578448EAA61948F25C32F8DD55AB1F778602(L_1, (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*)NULL, NULL);
		if (!L_2)
		{
			goto IL_002a;
		}
	}
	{
		Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* L_3 = ___0_material;
		NullCheck(L_3);
		Shader_tADC867D36B7876EE22427FAA2CE485105F4EE692* L_4;
		L_4 = Material_get_shader_m8B0C11AE6F2AD7DE30AF52D3195EB716F7A71983(L_3, NULL);
		NullCheck(L_4);
		String_t* L_5;
		L_5 = Object_get_name_mAC2F6B897CF1303BA4249B4CB55271AFACBB6392(L_4, NULL);
		NullCheck(L_5);
		String_t* L_6;
		L_6 = String_ToLower_m6191ABA3DC514ED47C10BDA23FD0DDCEAE7ACFBD(L_5, NULL);
		NullCheck(L_6);
		bool L_7;
		L_7 = String_Contains_m6D77B121FADA7CA5F397C0FABB65DA62DF03B6C3(L_6, _stringLiteralFA855702F3E0239B3B0D1F427461543C440733A6, NULL);
		G_B3_0 = ((int32_t)(L_7));
		goto IL_002b;
	}

IL_002a:
	{
		G_B3_0 = 0;
	}

IL_002b:
	{
		if (G_B3_0)
		{
			goto IL_0034;
		}
	}
	{
		G_B6_0 = _stringLiteralDB784C0FDB5E2F468943EF04EB13EE0E6135421C;
		goto IL_0039;
	}

IL_0034:
	{
		G_B6_0 = _stringLiteralCB414C73F9E54AF3AD8F7CFB654C37B3A3B13925;
	}

IL_0039:
	{
		V_0 = G_B6_0;
		String_t* L_8 = V_0;
		V_1 = L_8;
		il2cpp_codegen_runtime_class_init_inline(TCP2_RuntimeUtils_t5E9598A60187D562FEDFC57BE19768E002F7129C_il2cpp_TypeInfo_var);
		List_1_t77EDD3ECA98BCC1B49E3106C8CB923CA87D088ED* L_9 = ((TCP2_RuntimeUtils_t5E9598A60187D562FEDFC57BE19768E002F7129C_StaticFields*)il2cpp_codegen_static_fields_for(TCP2_RuntimeUtils_t5E9598A60187D562FEDFC57BE19768E002F7129C_il2cpp_TypeInfo_var))->___ShaderVariants;
		NullCheck(L_9);
		Enumerator_t8BACEFB05E69B10743F619D20823D840EB5736CB L_10;
		L_10 = List_1_GetEnumerator_m116DF5C3E10600177E00AD6F58CBEAE4085708DE(L_9, List_1_GetEnumerator_m116DF5C3E10600177E00AD6F58CBEAE4085708DE_RuntimeMethod_var);
		V_3 = L_10;
	}
	{
		auto __finallyBlock = il2cpp::utils::Finally([&]
		{

FINALLY_00b0:
			{
				Enumerator_Dispose_mDF603E9BDFD97F66D220C8F24CA68AEFE7A5E9EF((&V_3), Enumerator_Dispose_mDF603E9BDFD97F66D220C8F24CA68AEFE7A5E9EF_RuntimeMethod_var);
				return;
			}
		});
		try
		{
			{
				goto IL_00a5_1;
			}

IL_0049_1:
			{
				StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_11;
				L_11 = Enumerator_get_Current_m9972E774645548D767A142EE3C74E157C0AF52FF_inline((&V_3), Enumerator_get_Current_m9972E774645548D767A142EE3C74E157C0AF52FF_RuntimeMethod_var);
				V_4 = L_11;
				Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* L_12 = ___0_material;
				NullCheck(L_12);
				StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_13;
				L_13 = Material_get_shaderKeywords_m11982F09EED6BB0A892342E1A72AEA470C44B105(L_12, NULL);
				V_5 = L_13;
				V_6 = 0;
				goto IL_009d_1;
			}

IL_005f_1:
			{
				StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_14 = V_5;
				int32_t L_15 = V_6;
				NullCheck(L_14);
				int32_t L_16 = L_15;
				String_t* L_17 = (L_14)->GetAt(static_cast<il2cpp_array_size_t>(L_16));
				V_7 = L_17;
				V_8 = 1;
				goto IL_008f_1;
			}

IL_006b_1:
			{
				String_t* L_18 = V_7;
				StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_19 = V_4;
				int32_t L_20 = V_8;
				NullCheck(L_19);
				int32_t L_21 = L_20;
				String_t* L_22 = (L_19)->GetAt(static_cast<il2cpp_array_size_t>(L_21));
				bool L_23;
				L_23 = String_op_Equality_m030E1B219352228970A076136E455C4E568C02C1(L_18, L_22, NULL);
				if (!L_23)
				{
					goto IL_0089_1;
				}
			}
			{
				String_t* L_24 = V_1;
				StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_25 = V_4;
				NullCheck(L_25);
				int32_t L_26 = 0;
				String_t* L_27 = (L_25)->GetAt(static_cast<il2cpp_array_size_t>(L_26));
				String_t* L_28;
				L_28 = String_Concat_m8855A6DE10F84DA7F4EC113CADDB59873A25573B(L_24, _stringLiteral2386E77CF610F786B06A91AF2C1B3FD2282D2745, L_27, NULL);
				V_1 = L_28;
			}

IL_0089_1:
			{
				int32_t L_29 = V_8;
				V_8 = ((int32_t)il2cpp_codegen_add(L_29, 1));
			}

IL_008f_1:
			{
				int32_t L_30 = V_8;
				StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_31 = V_4;
				NullCheck(L_31);
				if ((((int32_t)L_30) < ((int32_t)((int32_t)(((RuntimeArray*)L_31)->max_length)))))
				{
					goto IL_006b_1;
				}
			}
			{
				int32_t L_32 = V_6;
				V_6 = ((int32_t)il2cpp_codegen_add(L_32, 1));
			}

IL_009d_1:
			{
				int32_t L_33 = V_6;
				StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_34 = V_5;
				NullCheck(L_34);
				if ((((int32_t)L_33) < ((int32_t)((int32_t)(((RuntimeArray*)L_34)->max_length)))))
				{
					goto IL_005f_1;
				}
			}

IL_00a5_1:
			{
				bool L_35;
				L_35 = Enumerator_MoveNext_mFFBA7CC0534330311AFFABB1789CD7C93F6B86DA((&V_3), Enumerator_MoveNext_mFFBA7CC0534330311AFFABB1789CD7C93F6B86DA_RuntimeMethod_var);
				if (L_35)
				{
					goto IL_0049_1;
				}
			}
			{
				goto IL_00be;
			}
		}
		catch(Il2CppExceptionWrapper& e)
		{
			__finallyBlock.StoreException(e.ex);
		}
	}

IL_00be:
	{
		String_t* L_36 = V_1;
		NullCheck(L_36);
		String_t* L_37;
		L_37 = String_TrimEnd_m25B1EA658EE07ADFED51FED61D630E5625336AB5(L_36, NULL);
		V_1 = L_37;
		V_2 = _stringLiteralCF08D47AA950197FBF22492248D649A8126E68AB;
		String_t* L_38 = V_1;
		String_t* L_39 = V_0;
		bool L_40;
		L_40 = String_op_Inequality_m8C940F3CFC42866709D7CA931B3D77B4BE94BCB6(L_38, L_39, NULL);
		if (!L_40)
		{
			goto IL_00da;
		}
	}
	{
		V_2 = _stringLiteralBCA6F238331EF68017D1ADF8BEADDA03900A1940;
	}

IL_00da:
	{
		String_t* L_41 = V_2;
		String_t* L_42 = V_1;
		String_t* L_43;
		L_43 = String_Concat_m9E3155FB84015C823606188F53B47CB44C444991(L_41, L_42, NULL);
		Shader_tADC867D36B7876EE22427FAA2CE485105F4EE692* L_44;
		L_44 = Shader_Find_m183AA54F78320212DDEC811592F98456898A41C5(L_43, NULL);
		return L_44;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TCP2_RuntimeUtils__cctor_m2467276D4A212FAFCCEA6835C4CE135F41C67AA4 (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_Add_m926708BBEA1A57FF6B812BFB165466C637F5219D_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1__ctor_m51867D7C9A5A1C3DE4C23B746BAC6CF661BA1929_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_t77EDD3ECA98BCC1B49E3106C8CB923CA87D088ED_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TCP2_RuntimeUtils_t5E9598A60187D562FEDFC57BE19768E002F7129C_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral055E2BC6FC0C0670695F60037D4D969590DD5A5E);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral0C11FFBAB8AA738FEDA8B2E47FCA289F53F977C8);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral25A48893C95318D7E0A508DDFBB672FF3E43922D);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral32BC544C06225EE440679BE6646FFFE40FC36433);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral47D462D5B38F0A5177ED612304E062E150044C2F);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral6E75A7F6B1D53D770E799FA180B1E92C3BEC8B6B);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral71E2151BF75F822D32529B598E65CB2CF534C8BD);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral851F9501FFDEDBC611D5201E456668A28E0C7F4B);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral8649F4FD2D10AA91241D05393F34CFB465556BF8);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral90D06E84F06130A1E294B7100441A92113C7EB43);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral9C9DA00C6A4C83135AB9BFE5F7C7405FF12BFDCE);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralC37459BA213E8B09FB24FA0927B73EA78BDF7C7D);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralD34832E26D345212F1E60233E1603D507C2DB8B1);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralD9E119F3EF845484CED205A9FD0CBD04AAE8D9B2);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralE0A9DCBA20570641426F54838A643EAB380B2A68);
		s_Il2CppMethodInitialized = true;
	}
	List_1_t77EDD3ECA98BCC1B49E3106C8CB923CA87D088ED* V_0 = NULL;
	{
		List_1_t77EDD3ECA98BCC1B49E3106C8CB923CA87D088ED* L_0 = (List_1_t77EDD3ECA98BCC1B49E3106C8CB923CA87D088ED*)il2cpp_codegen_object_new(List_1_t77EDD3ECA98BCC1B49E3106C8CB923CA87D088ED_il2cpp_TypeInfo_var);
		List_1__ctor_m51867D7C9A5A1C3DE4C23B746BAC6CF661BA1929(L_0, List_1__ctor_m51867D7C9A5A1C3DE4C23B746BAC6CF661BA1929_RuntimeMethod_var);
		V_0 = L_0;
		List_1_t77EDD3ECA98BCC1B49E3106C8CB923CA87D088ED* L_1 = V_0;
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_2 = (StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248*)(StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248*)SZArrayNew(StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248_il2cpp_TypeInfo_var, (uint32_t)2);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_3 = L_2;
		NullCheck(L_3);
		(L_3)->SetAt(static_cast<il2cpp_array_size_t>(0), (String_t*)_stringLiteral32BC544C06225EE440679BE6646FFFE40FC36433);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_4 = L_3;
		NullCheck(L_4);
		(L_4)->SetAt(static_cast<il2cpp_array_size_t>(1), (String_t*)_stringLiteral055E2BC6FC0C0670695F60037D4D969590DD5A5E);
		NullCheck(L_1);
		List_1_Add_m926708BBEA1A57FF6B812BFB165466C637F5219D_inline(L_1, L_4, List_1_Add_m926708BBEA1A57FF6B812BFB165466C637F5219D_RuntimeMethod_var);
		List_1_t77EDD3ECA98BCC1B49E3106C8CB923CA87D088ED* L_5 = V_0;
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_6 = (StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248*)(StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248*)SZArrayNew(StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248_il2cpp_TypeInfo_var, (uint32_t)3);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_7 = L_6;
		NullCheck(L_7);
		(L_7)->SetAt(static_cast<il2cpp_array_size_t>(0), (String_t*)_stringLiteral71E2151BF75F822D32529B598E65CB2CF534C8BD);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_8 = L_7;
		NullCheck(L_8);
		(L_8)->SetAt(static_cast<il2cpp_array_size_t>(1), (String_t*)_stringLiteral6E75A7F6B1D53D770E799FA180B1E92C3BEC8B6B);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_9 = L_8;
		NullCheck(L_9);
		(L_9)->SetAt(static_cast<il2cpp_array_size_t>(2), (String_t*)_stringLiteral9C9DA00C6A4C83135AB9BFE5F7C7405FF12BFDCE);
		NullCheck(L_5);
		List_1_Add_m926708BBEA1A57FF6B812BFB165466C637F5219D_inline(L_5, L_9, List_1_Add_m926708BBEA1A57FF6B812BFB165466C637F5219D_RuntimeMethod_var);
		List_1_t77EDD3ECA98BCC1B49E3106C8CB923CA87D088ED* L_10 = V_0;
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_11 = (StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248*)(StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248*)SZArrayNew(StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248_il2cpp_TypeInfo_var, (uint32_t)2);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_12 = L_11;
		NullCheck(L_12);
		(L_12)->SetAt(static_cast<il2cpp_array_size_t>(0), (String_t*)_stringLiteral90D06E84F06130A1E294B7100441A92113C7EB43);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_13 = L_12;
		NullCheck(L_13);
		(L_13)->SetAt(static_cast<il2cpp_array_size_t>(1), (String_t*)_stringLiteralC37459BA213E8B09FB24FA0927B73EA78BDF7C7D);
		NullCheck(L_10);
		List_1_Add_m926708BBEA1A57FF6B812BFB165466C637F5219D_inline(L_10, L_13, List_1_Add_m926708BBEA1A57FF6B812BFB165466C637F5219D_RuntimeMethod_var);
		List_1_t77EDD3ECA98BCC1B49E3106C8CB923CA87D088ED* L_14 = V_0;
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_15 = (StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248*)(StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248*)SZArrayNew(StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248_il2cpp_TypeInfo_var, (uint32_t)2);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_16 = L_15;
		NullCheck(L_16);
		(L_16)->SetAt(static_cast<il2cpp_array_size_t>(0), (String_t*)_stringLiteral25A48893C95318D7E0A508DDFBB672FF3E43922D);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_17 = L_16;
		NullCheck(L_17);
		(L_17)->SetAt(static_cast<il2cpp_array_size_t>(1), (String_t*)_stringLiteralE0A9DCBA20570641426F54838A643EAB380B2A68);
		NullCheck(L_14);
		List_1_Add_m926708BBEA1A57FF6B812BFB165466C637F5219D_inline(L_14, L_17, List_1_Add_m926708BBEA1A57FF6B812BFB165466C637F5219D_RuntimeMethod_var);
		List_1_t77EDD3ECA98BCC1B49E3106C8CB923CA87D088ED* L_18 = V_0;
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_19 = (StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248*)(StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248*)SZArrayNew(StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248_il2cpp_TypeInfo_var, (uint32_t)2);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_20 = L_19;
		NullCheck(L_20);
		(L_20)->SetAt(static_cast<il2cpp_array_size_t>(0), (String_t*)_stringLiteralD9E119F3EF845484CED205A9FD0CBD04AAE8D9B2);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_21 = L_20;
		NullCheck(L_21);
		(L_21)->SetAt(static_cast<il2cpp_array_size_t>(1), (String_t*)_stringLiteral851F9501FFDEDBC611D5201E456668A28E0C7F4B);
		NullCheck(L_18);
		List_1_Add_m926708BBEA1A57FF6B812BFB165466C637F5219D_inline(L_18, L_21, List_1_Add_m926708BBEA1A57FF6B812BFB165466C637F5219D_RuntimeMethod_var);
		List_1_t77EDD3ECA98BCC1B49E3106C8CB923CA87D088ED* L_22 = V_0;
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_23 = (StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248*)(StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248*)SZArrayNew(StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248_il2cpp_TypeInfo_var, (uint32_t)2);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_24 = L_23;
		NullCheck(L_24);
		(L_24)->SetAt(static_cast<il2cpp_array_size_t>(0), (String_t*)_stringLiteral8649F4FD2D10AA91241D05393F34CFB465556BF8);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_25 = L_24;
		NullCheck(L_25);
		(L_25)->SetAt(static_cast<il2cpp_array_size_t>(1), (String_t*)_stringLiteral47D462D5B38F0A5177ED612304E062E150044C2F);
		NullCheck(L_22);
		List_1_Add_m926708BBEA1A57FF6B812BFB165466C637F5219D_inline(L_22, L_25, List_1_Add_m926708BBEA1A57FF6B812BFB165466C637F5219D_RuntimeMethod_var);
		List_1_t77EDD3ECA98BCC1B49E3106C8CB923CA87D088ED* L_26 = V_0;
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_27 = (StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248*)(StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248*)SZArrayNew(StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248_il2cpp_TypeInfo_var, (uint32_t)2);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_28 = L_27;
		NullCheck(L_28);
		(L_28)->SetAt(static_cast<il2cpp_array_size_t>(0), (String_t*)_stringLiteralD34832E26D345212F1E60233E1603D507C2DB8B1);
		StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* L_29 = L_28;
		NullCheck(L_29);
		(L_29)->SetAt(static_cast<il2cpp_array_size_t>(1), (String_t*)_stringLiteral0C11FFBAB8AA738FEDA8B2E47FCA289F53F977C8);
		NullCheck(L_26);
		List_1_Add_m926708BBEA1A57FF6B812BFB165466C637F5219D_inline(L_26, L_29, List_1_Add_m926708BBEA1A57FF6B812BFB165466C637F5219D_RuntimeMethod_var);
		List_1_t77EDD3ECA98BCC1B49E3106C8CB923CA87D088ED* L_30 = V_0;
		((TCP2_RuntimeUtils_t5E9598A60187D562FEDFC57BE19768E002F7129C_StaticFields*)il2cpp_codegen_static_fields_for(TCP2_RuntimeUtils_t5E9598A60187D562FEDFC57BE19768E002F7129C_il2cpp_TypeInfo_var))->___ShaderVariants = L_30;
		Il2CppCodeGenWriteBarrier((void**)(&((TCP2_RuntimeUtils_t5E9598A60187D562FEDFC57BE19768E002F7129C_StaticFields*)il2cpp_codegen_static_fields_for(TCP2_RuntimeUtils_t5E9598A60187D562FEDFC57BE19768E002F7129C_il2cpp_TypeInfo_var))->___ShaderVariants), (void*)L_30);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TCP2_ShaderUpdateUnityTime_LateUpdate_m8E1AC06A13572836D0D66470EF350465BD0AE355 (TCP2_ShaderUpdateUnityTime_t8BA15EEC76AA4A8461D2A1C5974BB2E0C6874B90* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TCP2_ShaderUpdateUnityTime_t8BA15EEC76AA4A8461D2A1C5974BB2E0C6874B90_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		il2cpp_codegen_runtime_class_init_inline(TCP2_ShaderUpdateUnityTime_t8BA15EEC76AA4A8461D2A1C5974BB2E0C6874B90_il2cpp_TypeInfo_var);
		int32_t L_0 = ((TCP2_ShaderUpdateUnityTime_t8BA15EEC76AA4A8461D2A1C5974BB2E0C6874B90_StaticFields*)il2cpp_codegen_static_fields_for(TCP2_ShaderUpdateUnityTime_t8BA15EEC76AA4A8461D2A1C5974BB2E0C6874B90_il2cpp_TypeInfo_var))->___UnityTime;
		float L_1;
		L_1 = Time_get_time_m3A271BB1B20041144AC5B7863B71AB1F0150374B(NULL);
		Shader_SetGlobalFloat_mE7D0DA2B0A62925E093B318785AF82A173794AFC(L_0, L_1, NULL);
		int32_t L_2 = ((TCP2_ShaderUpdateUnityTime_t8BA15EEC76AA4A8461D2A1C5974BB2E0C6874B90_StaticFields*)il2cpp_codegen_static_fields_for(TCP2_ShaderUpdateUnityTime_t8BA15EEC76AA4A8461D2A1C5974BB2E0C6874B90_il2cpp_TypeInfo_var))->___CustomTime;
		float L_3;
		L_3 = Time_get_time_m3A271BB1B20041144AC5B7863B71AB1F0150374B(NULL);
		float L_4;
		L_4 = Time_get_time_m3A271BB1B20041144AC5B7863B71AB1F0150374B(NULL);
		float L_5;
		L_5 = Time_get_time_m3A271BB1B20041144AC5B7863B71AB1F0150374B(NULL);
		float L_6;
		L_6 = Time_get_time_m3A271BB1B20041144AC5B7863B71AB1F0150374B(NULL);
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_7;
		memset((&L_7), 0, sizeof(L_7));
		Vector4__ctor_m96B2CD8B862B271F513AF0BDC2EABD58E4DBC813_inline((&L_7), ((float)(L_3/(20.0f))), L_4, ((float)il2cpp_codegen_multiply(L_5, (2.0f))), ((float)il2cpp_codegen_multiply(L_6, (3.0f))), NULL);
		Shader_SetGlobalVector_mDC5F45B008D44A2C8BF6D450CFE8B58B847C8190(L_2, L_7, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TCP2_ShaderUpdateUnityTime__ctor_mDF471253BFD5B945AAA49F1558489D2A816936F2 (TCP2_ShaderUpdateUnityTime_t8BA15EEC76AA4A8461D2A1C5974BB2E0C6874B90* __this, const RuntimeMethod* method) 
{
	{
		MonoBehaviour__ctor_m592DB0105CA0BC97AA1C5F4AD27B12D68A3B7C1E(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TCP2_ShaderUpdateUnityTime__cctor_m3172D7CFE4DA56B6B1265CE3343B76216F5DD2CB (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TCP2_ShaderUpdateUnityTime_t8BA15EEC76AA4A8461D2A1C5974BB2E0C6874B90_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral0D4F33DD4748386169AA3A8766BC7E48C978031F);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral716F6BFC32ABF52915936AA6B0445724BBA4451D);
		s_Il2CppMethodInitialized = true;
	}
	{
		int32_t L_0;
		L_0 = Shader_PropertyToID_mE98523D50F5656CAE89B30695C458253EB8956CA(_stringLiteral716F6BFC32ABF52915936AA6B0445724BBA4451D, NULL);
		((TCP2_ShaderUpdateUnityTime_t8BA15EEC76AA4A8461D2A1C5974BB2E0C6874B90_StaticFields*)il2cpp_codegen_static_fields_for(TCP2_ShaderUpdateUnityTime_t8BA15EEC76AA4A8461D2A1C5974BB2E0C6874B90_il2cpp_TypeInfo_var))->___UnityTime = L_0;
		int32_t L_1;
		L_1 = Shader_PropertyToID_mE98523D50F5656CAE89B30695C458253EB8956CA(_stringLiteral0D4F33DD4748386169AA3A8766BC7E48C978031F, NULL);
		((TCP2_ShaderUpdateUnityTime_t8BA15EEC76AA4A8461D2A1C5974BB2E0C6874B90_StaticFields*)il2cpp_codegen_static_fields_for(TCP2_ShaderUpdateUnityTime_t8BA15EEC76AA4A8461D2A1C5974BB2E0C6874B90_il2cpp_TypeInfo_var))->___CustomTime = L_1;
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Vector3__ctor_m376936E6B999EF1ECBE57D990A386303E2283DE0_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* __this, float ___0_x, float ___1_y, float ___2_z, const RuntimeMethod* method) 
{
	{
		float L_0 = ___0_x;
		__this->___x = L_0;
		float L_1 = ___1_y;
		__this->___y = L_1;
		float L_2 = ___2_z;
		__this->___z = L_2;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_get_normalized_m736BBF65D5CDA7A18414370D15B4DFCC1E466F07_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* __this, const RuntimeMethod* method) 
{
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = (*(Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)__this);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_1;
		L_1 = Vector3_Normalize_mEF8349CC39674236CFC694189AFD36E31F89AC8F_inline(L_0, NULL);
		V_0 = L_1;
		goto IL_000f;
	}

IL_000f:
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_2 = V_0;
		return L_2;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Vector4__ctor_m96B2CD8B862B271F513AF0BDC2EABD58E4DBC813_inline (Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3* __this, float ___0_x, float ___1_y, float ___2_z, float ___3_w, const RuntimeMethod* method) 
{
	{
		float L_0 = ___0_x;
		__this->___x = L_0;
		float L_1 = ___1_y;
		__this->___y = L_1;
		float L_2 = ___2_z;
		__this->___z = L_2;
		float L_3 = ___3_w;
		__this->___w = L_3;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Vector3_Dot_mBB86BB940AA0A32FA7D3C02AC42E5BC7095A5D52_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_lhs, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___1_rhs, const RuntimeMethod* method) 
{
	float V_0 = 0.0f;
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = ___0_lhs;
		float L_1 = L_0.___x;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_2 = ___1_rhs;
		float L_3 = L_2.___x;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_4 = ___0_lhs;
		float L_5 = L_4.___y;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_6 = ___1_rhs;
		float L_7 = L_6.___y;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_8 = ___0_lhs;
		float L_9 = L_8.___z;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_10 = ___1_rhs;
		float L_11 = L_10.___z;
		V_0 = ((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_1, L_3)), ((float)il2cpp_codegen_multiply(L_5, L_7)))), ((float)il2cpp_codegen_multiply(L_9, L_11))));
		goto IL_002d;
	}

IL_002d:
	{
		float L_12 = V_0;
		return L_12;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_op_Multiply_m87BA7C578F96C8E49BB07088DAAC4649F83B0353_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_a, float ___1_d, const RuntimeMethod* method) 
{
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = ___0_a;
		float L_1 = L_0.___x;
		float L_2 = ___1_d;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_3 = ___0_a;
		float L_4 = L_3.___y;
		float L_5 = ___1_d;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_6 = ___0_a;
		float L_7 = L_6.___z;
		float L_8 = ___1_d;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_9;
		memset((&L_9), 0, sizeof(L_9));
		Vector3__ctor_m376936E6B999EF1ECBE57D990A386303E2283DE0_inline((&L_9), ((float)il2cpp_codegen_multiply(L_1, L_2)), ((float)il2cpp_codegen_multiply(L_4, L_5)), ((float)il2cpp_codegen_multiply(L_7, L_8)), NULL);
		V_0 = L_9;
		goto IL_0021;
	}

IL_0021:
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_10 = V_0;
		return L_10;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_op_Addition_m78C0EC70CB66E8DCAC225743D82B268DAEE92067_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_a, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___1_b, const RuntimeMethod* method) 
{
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = ___0_a;
		float L_1 = L_0.___x;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_2 = ___1_b;
		float L_3 = L_2.___x;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_4 = ___0_a;
		float L_5 = L_4.___y;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_6 = ___1_b;
		float L_7 = L_6.___y;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_8 = ___0_a;
		float L_9 = L_8.___z;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_10 = ___1_b;
		float L_11 = L_10.___z;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_12;
		memset((&L_12), 0, sizeof(L_12));
		Vector3__ctor_m376936E6B999EF1ECBE57D990A386303E2283DE0_inline((&L_12), ((float)il2cpp_codegen_add(L_1, L_3)), ((float)il2cpp_codegen_add(L_5, L_7)), ((float)il2cpp_codegen_add(L_9, L_11)), NULL);
		V_0 = L_12;
		goto IL_0030;
	}

IL_0030:
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_13 = V_0;
		return L_13;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Vector4_get_Item_mB1D001A235857569E479FB799EF77C52391D19EF_inline (Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3* __this, int32_t ___0_index, const RuntimeMethod* method) 
{
	int32_t V_0 = 0;
	int32_t V_1 = 0;
	float V_2 = 0.0f;
	{
		int32_t L_0 = ___0_index;
		V_1 = L_0;
		int32_t L_1 = V_1;
		V_0 = L_1;
		int32_t L_2 = V_0;
		switch (L_2)
		{
			case 0:
			{
				goto IL_001d;
			}
			case 1:
			{
				goto IL_0026;
			}
			case 2:
			{
				goto IL_002f;
			}
			case 3:
			{
				goto IL_0038;
			}
		}
	}
	{
		goto IL_0041;
	}

IL_001d:
	{
		float L_3 = __this->___x;
		V_2 = L_3;
		goto IL_004c;
	}

IL_0026:
	{
		float L_4 = __this->___y;
		V_2 = L_4;
		goto IL_004c;
	}

IL_002f:
	{
		float L_5 = __this->___z;
		V_2 = L_5;
		goto IL_004c;
	}

IL_0038:
	{
		float L_6 = __this->___w;
		V_2 = L_6;
		goto IL_004c;
	}

IL_0041:
	{
		IndexOutOfRangeException_t7ECB35264FB6CA8FAA516BD958F4B2ADC78E8A82* L_7 = (IndexOutOfRangeException_t7ECB35264FB6CA8FAA516BD958F4B2ADC78E8A82*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&IndexOutOfRangeException_t7ECB35264FB6CA8FAA516BD958F4B2ADC78E8A82_il2cpp_TypeInfo_var)));
		IndexOutOfRangeException__ctor_mFD06819F05B815BE2D6E826D4E04F4C449D0A425(L_7, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteralB23C3717573626FB4C3C7DF5C19EDE7689837214)), NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_7, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&Vector4_get_Item_mB1D001A235857569E479FB799EF77C52391D19EF_RuntimeMethod_var)));
	}

IL_004c:
	{
		float L_8 = V_2;
		return L_8;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Color_tD001788D726C3A7F1379BEED0260B9591F440C1F Color_get_black_mB50217951591A045844C61E7FF31EEE3FEF16737_inline (const RuntimeMethod* method) 
{
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_0;
		memset((&L_0), 0, sizeof(L_0));
		Color__ctor_m3786F0D6E510D9CFA544523A955870BD2A514C8C_inline((&L_0), (0.0f), (0.0f), (0.0f), (1.0f), NULL);
		V_0 = L_0;
		goto IL_001d;
	}

IL_001d:
	{
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_1 = V_0;
		return L_1;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR RuntimeObject* Enumerator_get_Current_m6330F15D18EE4F547C05DF9BF83C5EB710376027_gshared_inline (Enumerator_t9473BAB568A27E2339D48C1F91319E0F6D244D7A* __this, const RuntimeMethod* method) 
{
	{
		RuntimeObject* L_0 = __this->____current;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void List_1_Add_mEBCF994CC3814631017F46A387B1A192ED6C85C7_gshared_inline (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D* __this, RuntimeObject* ___0_item, const RuntimeMethod* method) 
{
	ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* V_0 = NULL;
	int32_t V_1 = 0;
	{
		int32_t L_0 = __this->____version;
		__this->____version = ((int32_t)il2cpp_codegen_add(L_0, 1));
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_1 = __this->____items;
		V_0 = L_1;
		int32_t L_2 = __this->____size;
		V_1 = L_2;
		int32_t L_3 = V_1;
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_4 = V_0;
		NullCheck(L_4);
		if ((!(((uint32_t)L_3) < ((uint32_t)((int32_t)(((RuntimeArray*)L_4)->max_length))))))
		{
			goto IL_0034;
		}
	}
	{
		int32_t L_5 = V_1;
		__this->____size = ((int32_t)il2cpp_codegen_add(L_5, 1));
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_6 = V_0;
		int32_t L_7 = V_1;
		RuntimeObject* L_8 = ___0_item;
		NullCheck(L_6);
		(L_6)->SetAt(static_cast<il2cpp_array_size_t>(L_7), (RuntimeObject*)L_8);
		return;
	}

IL_0034:
	{
		RuntimeObject* L_9 = ___0_item;
		List_1_AddWithResize_m79A9BF770BEF9C06BE40D5401E55E375F2726CC4(__this, L_9, il2cpp_rgctx_method(method->klass->rgctx_data, 14));
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_Normalize_mEF8349CC39674236CFC694189AFD36E31F89AC8F_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_value, const RuntimeMethod* method) 
{
	float V_0 = 0.0f;
	bool V_1 = false;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_2;
	memset((&V_2), 0, sizeof(V_2));
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = ___0_value;
		float L_1;
		L_1 = Vector3_Magnitude_m21652D951393A3D7CE92CE40049A0E7F76544D1B_inline(L_0, NULL);
		V_0 = L_1;
		float L_2 = V_0;
		V_1 = (bool)((((float)L_2) > ((float)(9.99999975E-06f)))? 1 : 0);
		bool L_3 = V_1;
		if (!L_3)
		{
			goto IL_001e;
		}
	}
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_4 = ___0_value;
		float L_5 = V_0;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_6;
		L_6 = Vector3_op_Division_mCC6BB24E372AB96B8380D1678446EF6A8BAE13BB_inline(L_4, L_5, NULL);
		V_2 = L_6;
		goto IL_0026;
	}

IL_001e:
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_7;
		L_7 = Vector3_get_zero_m0C1249C3F25B1C70EAD3CC8B31259975A457AE39_inline(NULL);
		V_2 = L_7;
		goto IL_0026;
	}

IL_0026:
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_8 = V_2;
		return L_8;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Color__ctor_m3786F0D6E510D9CFA544523A955870BD2A514C8C_inline (Color_tD001788D726C3A7F1379BEED0260B9591F440C1F* __this, float ___0_r, float ___1_g, float ___2_b, float ___3_a, const RuntimeMethod* method) 
{
	{
		float L_0 = ___0_r;
		__this->___r = L_0;
		float L_1 = ___1_g;
		__this->___g = L_1;
		float L_2 = ___2_b;
		__this->___b = L_2;
		float L_3 = ___3_a;
		__this->___a = L_3;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Vector3_Magnitude_m21652D951393A3D7CE92CE40049A0E7F76544D1B_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_vector, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Math_tEB65DE7CA8B083C412C969C92981C030865486CE_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	float V_0 = 0.0f;
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = ___0_vector;
		float L_1 = L_0.___x;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_2 = ___0_vector;
		float L_3 = L_2.___x;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_4 = ___0_vector;
		float L_5 = L_4.___y;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_6 = ___0_vector;
		float L_7 = L_6.___y;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_8 = ___0_vector;
		float L_9 = L_8.___z;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_10 = ___0_vector;
		float L_11 = L_10.___z;
		il2cpp_codegen_runtime_class_init_inline(Math_tEB65DE7CA8B083C412C969C92981C030865486CE_il2cpp_TypeInfo_var);
		double L_12;
		L_12 = sqrt(((double)((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_1, L_3)), ((float)il2cpp_codegen_multiply(L_5, L_7)))), ((float)il2cpp_codegen_multiply(L_9, L_11))))));
		V_0 = ((float)L_12);
		goto IL_0034;
	}

IL_0034:
	{
		float L_13 = V_0;
		return L_13;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_op_Division_mCC6BB24E372AB96B8380D1678446EF6A8BAE13BB_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_a, float ___1_d, const RuntimeMethod* method) 
{
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = ___0_a;
		float L_1 = L_0.___x;
		float L_2 = ___1_d;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_3 = ___0_a;
		float L_4 = L_3.___y;
		float L_5 = ___1_d;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_6 = ___0_a;
		float L_7 = L_6.___z;
		float L_8 = ___1_d;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_9;
		memset((&L_9), 0, sizeof(L_9));
		Vector3__ctor_m376936E6B999EF1ECBE57D990A386303E2283DE0_inline((&L_9), ((float)(L_1/L_2)), ((float)(L_4/L_5)), ((float)(L_7/L_8)), NULL);
		V_0 = L_9;
		goto IL_0021;
	}

IL_0021:
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_10 = V_0;
		return L_10;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_get_zero_m0C1249C3F25B1C70EAD3CC8B31259975A457AE39_inline (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = ((Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_StaticFields*)il2cpp_codegen_static_fields_for(Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_il2cpp_TypeInfo_var))->___zeroVector;
		V_0 = L_0;
		goto IL_0009;
	}

IL_0009:
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_1 = V_0;
		return L_1;
	}
}

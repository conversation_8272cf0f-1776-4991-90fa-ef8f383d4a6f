﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void WWW__ctor_m5D29D83E9EE0925ED8252347CE24EC236401503D (void);
extern void WWW_get_text_m95F7CAAC33FD0BAB9B535E3AEAFDA36B81B3EC4F (void);
extern void WWW_CreateTextureFromDownloadedData_m670DE15775E657BC8CE6CD19FE2C29EA3429F8F0 (void);
extern void WWW_get_texture_mB38F7FC4220AC09935423B84FD4EB852CF172AAE (void);
extern void WWW_get_url_m368B1D7D23DC22E412A3F802C6E3047760665519 (void);
extern void WWW_get_keepWaiting_m2D6B60FD9CB9C3E86D69E87EA32953AA68AE26B9 (void);
extern void WWW_Dispose_mE5FC4A2013C63A68C287F139B177D86F75C4A74F (void);
extern void WWW_WaitUntilDoneIfPossible_mD975AFF6737F00BB5003C5AEDBD795751F129A84 (void);
static Il2CppMethodPointer s_methodPointers[8] = 
{
	WWW__ctor_m5D29D83E9EE0925ED8252347CE24EC236401503D,
	WWW_get_text_m95F7CAAC33FD0BAB9B535E3AEAFDA36B81B3EC4F,
	WWW_CreateTextureFromDownloadedData_m670DE15775E657BC8CE6CD19FE2C29EA3429F8F0,
	WWW_get_texture_mB38F7FC4220AC09935423B84FD4EB852CF172AAE,
	WWW_get_url_m368B1D7D23DC22E412A3F802C6E3047760665519,
	WWW_get_keepWaiting_m2D6B60FD9CB9C3E86D69E87EA32953AA68AE26B9,
	WWW_Dispose_mE5FC4A2013C63A68C287F139B177D86F75C4A74F,
	WWW_WaitUntilDoneIfPossible_mD975AFF6737F00BB5003C5AEDBD795751F129A84,
};
static const int32_t s_InvokerIndices[8] = 
{
	10682,
	13052,
	9254,
	13052,
	13052,
	12815,
	13298,
	12815,
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_UnityWebRequestWWWModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_UnityWebRequestWWWModule_CodeGenModule = 
{
	"UnityEngine.UnityWebRequestWWWModule.dll",
	8,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};

using UnityEngine;

[System.Serializable]
public class BitmapGlyph
{
    [Header("Identification")]
    [SerializeField] private string m_GlyphName = "";
    [SerializeField] private char m_Character = '\0';
    
    [<PERSON><PERSON>("Sprite")]
    [Serialize<PERSON>ield] private Sprite m_Sprite;
    
    [<PERSON><PERSON>("Metrics")]
    [SerializeField] private float m_Width = 16f;
    [SerializeField] private float m_Height = 16f;
    [SerializeField] private float m_XOffset = 0f;
    [SerializeField] private float m_YOffset = 0f;
    [SerializeField] private float m_XAdvance = 16f;
    
    [<PERSON><PERSON>("UV Coordinates")]
    [SerializeField] private Rect m_UVRect = new Rect(0, 0, 1, 1);
    
    public string glyphName 
    { 
        get => m_GlyphName; 
        set => m_GlyphName = value; 
    }
    
    public char character 
    { 
        get => m_Character; 
        set => m_Character = value; 
    }
    
    public Sprite sprite 
    { 
        get => m_Sprite; 
        set 
        { 
            m_Sprite = value;
            if (value != null)
            {
                UpdateFromSprite();
            }
        } 
    }
    
    public float width 
    { 
        get => m_Width; 
        set => m_Width = value; 
    }
    
    public float height 
    { 
        get => m_Height; 
        set => m_Height = value; 
    }
    
    public float xOffset 
    { 
        get => m_XOffset; 
        set => m_XOffset = value; 
    }
    
    public float yOffset 
    { 
        get => m_YOffset; 
        set => m_YOffset = value; 
    }
    
    public float xAdvance 
    { 
        get => m_XAdvance; 
        set => m_XAdvance = value; 
    }
    
    public Rect uvRect 
    { 
        get => m_UVRect; 
        set => m_UVRect = value; 
    }
    
    /// <summary>
    /// Default constructor
    /// </summary>
    public BitmapGlyph()
    {
    }
    
    /// <summary>
    /// Constructor with character
    /// </summary>
    public BitmapGlyph(char character)
    {
        m_Character = character;
        m_GlyphName = character.ToString();
    }
    
    /// <summary>
    /// Constructor with glyph name
    /// </summary>
    public BitmapGlyph(string glyphName)
    {
        m_GlyphName = glyphName;
    }
    
    /// <summary>
    /// Constructor with character and sprite
    /// </summary>
    public BitmapGlyph(char character, Sprite sprite)
    {
        m_Character = character;
        m_GlyphName = character.ToString();
        this.sprite = sprite; // Use property to trigger UpdateFromSprite
    }
    
    /// <summary>
    /// Constructor with glyph name and sprite
    /// </summary>
    public BitmapGlyph(string glyphName, Sprite sprite)
    {
        m_GlyphName = glyphName;
        this.sprite = sprite; // Use property to trigger UpdateFromSprite
    }
    
    /// <summary>
    /// Update glyph metrics from sprite data
    /// </summary>
    public void UpdateFromSprite()
    {
        if (m_Sprite == null) return;
        
        var rect = m_Sprite.rect;
        var texture = m_Sprite.texture;
        
        // Update size
        m_Width = rect.width;
        m_Height = rect.height;
        
        // Update UV coordinates
        if (texture != null)
        {
            m_UVRect = new Rect(
                rect.x / texture.width,
                rect.y / texture.height,
                rect.width / texture.width,
                rect.height / texture.height
            );
        }
        
        // Set default advance to width if not set
        if (m_XAdvance <= 0)
        {
            m_XAdvance = m_Width;
        }
        
        // Update offsets based on sprite pivot
        var pivot = m_Sprite.pivot;
        m_XOffset = -pivot.x;
        m_YOffset = pivot.y - rect.height;
    }
    
    /// <summary>
    /// Get the bounds of this glyph when rendered
    /// </summary>
    public Rect GetBounds(Vector2 position)
    {
        return new Rect(
            position.x + m_XOffset,
            position.y + m_YOffset,
            m_Width,
            m_Height
        );
    }
    
    /// <summary>
    /// Check if this glyph is valid for rendering
    /// </summary>
    public bool IsValid()
    {
        return m_Sprite != null && m_Width > 0 && m_Height > 0;
    }
    
    /// <summary>
    /// Create a copy of this glyph
    /// </summary>
    public BitmapGlyph Clone()
    {
        var clone = new BitmapGlyph();
        clone.m_GlyphName = m_GlyphName;
        clone.m_Character = m_Character;
        clone.m_Sprite = m_Sprite;
        clone.m_Width = m_Width;
        clone.m_Height = m_Height;
        clone.m_XOffset = m_XOffset;
        clone.m_YOffset = m_YOffset;
        clone.m_XAdvance = m_XAdvance;
        clone.m_UVRect = m_UVRect;
        return clone;
    }
    
    public override string ToString()
    {
        return $"BitmapGlyph('{m_GlyphName}', '{m_Character}', {m_Width}x{m_Height})";
    }
}

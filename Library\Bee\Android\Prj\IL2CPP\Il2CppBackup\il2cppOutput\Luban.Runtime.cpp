﻿#include "pch-cpp.hpp"

#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include <limits>


template <typename T1>
struct VirtualActionInvoker1
{
	typedef void (*Action)(void*, T1, const RuntimeMethod*);

	static inline void Invoke (Il2CppMethodSlot slot, RuntimeObject* obj, T1 p1)
	{
		const VirtualInvokeData& invokeData = il2cpp_codegen_get_virtual_invoke_data(slot, obj);
		((Action)invokeData.methodPtr)(obj, p1, invokeData.method);
	}
};
template <typename T1, typename T2>
struct VirtualActionInvoker2
{
	typedef void (*Action)(void*, T1, T2, const RuntimeMethod*);

	static inline void Invoke (Il2CppMethodSlot slot, RuntimeObject* obj, T1 p1, T2 p2)
	{
		const VirtualInvokeData& invokeData = il2cpp_codegen_get_virtual_invoke_data(slot, obj);
		((Action)invokeData.methodPtr)(obj, p1, p2, invokeData.method);
	}
};
template <typename T1, typename T2, typename T3, typename T4>
struct VirtualActionInvoker4
{
	typedef void (*Action)(void*, T1, T2, T3, T4, const RuntimeMethod*);

	static inline void Invoke (Il2CppMethodSlot slot, RuntimeObject* obj, T1 p1, T2 p2, T3 p3, T4 p4)
	{
		const VirtualInvokeData& invokeData = il2cpp_codegen_get_virtual_invoke_data(slot, obj);
		((Action)invokeData.methodPtr)(obj, p1, p2, p3, p4, invokeData.method);
	}
};
template <typename R>
struct VirtualFuncInvoker0
{
	typedef R (*Func)(void*, const RuntimeMethod*);

	static inline R Invoke (Il2CppMethodSlot slot, RuntimeObject* obj)
	{
		const VirtualInvokeData& invokeData = il2cpp_codegen_get_virtual_invoke_data(slot, obj);
		return ((Func)invokeData.methodPtr)(obj, invokeData.method);
	}
};
template <typename R, typename T1>
struct VirtualFuncInvoker1
{
	typedef R (*Func)(void*, T1, const RuntimeMethod*);

	static inline R Invoke (Il2CppMethodSlot slot, RuntimeObject* obj, T1 p1)
	{
		const VirtualInvokeData& invokeData = il2cpp_codegen_get_virtual_invoke_data(slot, obj);
		return ((Func)invokeData.methodPtr)(obj, p1, invokeData.method);
	}
};

struct Dictionary_2_t9FA6D82CAFC18769F7515BB51D1C56DAE09381C3;
struct Dictionary_2_t14FE4A752A83D53771C584E4C8D14E01F2AFD7BA;
struct Dictionary_2_tE1603CE612C16451D1E56FF4D4859D4FE4087C28;
struct Dictionary_2_t9F8163AF97B5FC284EA73FF608A15D501B0D9BDC;
struct IEnumerable_1_t254121F7A43D09D0D2C9ABDDB9DE8E7E374EE671;
struct IEnumerator_1_t16BDCBA0AAAF22181718C64E801BA6B1D5540C42;
struct IEqualityComparer_1_tAE94C8F24AD5B94D4EE85CA9FC59E3409D41CAF7;
struct KeyCollection_t34A2F3F2228DD4284B597461431814F81466EF49;
struct List_1_t2B71C7C40AB24AD3BA7EFF058B78E7FD76195295;
struct List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D;
struct Stack_1_tBFA4A76FD7F09AC05C41D7A61CC4D933CAF033DC;
struct Stack_1_tAD790A47551563636908E21E4F08C54C0C323EB5;
struct ValueCollection_t1510510487B8FC816CBDE09AF063E28F3119D47E;
struct EntryU5BU5D_t3038E7E397842129132C446208107AE5FAC9EF87;
struct ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031;
struct CharU5BU5D_t799905CF001DD5F13F7DBB310181FC4D8B7D0AAB;
struct Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C;
struct IntPtrU5BU5D_tFD177F8C806A6921AD7150264CCC62FA00CAD832;
struct JSONNodeU5BU5D_tF049C2A413B85559640FD86EA7387C21176E3F76;
struct ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918;
struct StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF;
struct StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248;
struct BeanBase_tE882EE5D0CEB6587A1AB19056B5EA531600AAD67;
struct Calendar_t0A117CC7532A54C17188C2EFEA1F79DB20DF3A3B;
struct CompareInfo_t1B1A6AC3486B570C76ABA52149C9BD4CD82F9E57;
struct CultureData_tEEFDCF4ECA1BBF6C0C8C94EB3541657245598F9D;
struct CultureInfo_t9BA817D41AD55AC8BD07480DD8AC22F8FFA378E0;
struct DateTimeFormatInfo_t0457520F9FA7B5C8EAAEB3AD50413B6AEEB7458A;
struct Exception_t;
struct IDictionary_t6D03155AF1FA9083817AA5B6AD7DEEACC26AB220;
struct IEnumerator_t7B609C2FFA6EB5167D9C62A0C32A21DE2F666DAA;
struct IFormatProvider_tC202922D43BFF3525109ABF3FB79625F5646AB52;
struct JSONArray_tE863C8377E61FEEC9ACA640D090C176C7CDCB589;
struct JSONBool_t2BBE341953E8A8D293388B59CE964319333E6ECD;
struct JSONLazyCreator_t93B398608BA0717C413F6875922A3BF9C7ABD8B9;
struct JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA;
struct JSONNull_tE058D16E7011140DB8F406485C84D3D51DA7A97D;
struct JSONNumber_t90BD0D71B6F0447E77909AD12B258928EF559DE4;
struct JSONObject_t8136ED73F77B01FF951AB5A2C9B81C4CD0B5C7B6;
struct JSONString_tFCFD5D96312BC1A93DA8183AB4B30444DE9B7EB9;
struct NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A;
struct NumberFormatInfo_t8E26808B202927FEBF9064FCFEEA4D6E076E6472;
struct SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6;
struct SerializationException_t9BAC85A4408413811FD4394F0919F478DC33294B;
struct String_t;
struct StringBuilder_t;
struct TextInfo_tD3BAFCFD77418851E7D5CB8D2588F47019E414B4;
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915;
struct U3Cget_ChildrenU3Ed__24_tAAE17304DD542179299B598CFEA2DF919C468400;
struct U3Cget_ChildrenU3Ed__43_tBA5BD787315C9FDAC35DA9789C45EAD75D696F2D;
struct U3Cget_ChildrenU3Ed__27_t601D7688734A9685C53234154970690BCEDBCA60;

IL2CPP_EXTERN_C RuntimeClass* Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Convert_t7097FF336D592F7C06D88A98349A44646F91EFFC_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* CultureInfo_t9BA817D41AD55AC8BD07480DD8AC22F8FFA378E0_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Decimal_tDA6C877282B2D789CF97C0949661CC11D643969F_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Dictionary_2_t9F8163AF97B5FC284EA73FF608A15D501B0D9BDC_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Double_tE150EF3D1D43DEE85D533810AB4C742307EEDE5F_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Exception_t_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Int16_tB8EF286A9C33492FA6E6D6E67320BE93E794A175_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Int64_t092CFB123BE63C28ACDAF65C68F21A526050DBA3_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* JSONArray_tE863C8377E61FEEC9ACA640D090C176C7CDCB589_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* JSONBool_t2BBE341953E8A8D293388B59CE964319333E6ECD_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* JSONLazyCreator_t93B398608BA0717C413F6875922A3BF9C7ABD8B9_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* JSONNull_tE058D16E7011140DB8F406485C84D3D51DA7A97D_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* JSONNumber_t90BD0D71B6F0447E77909AD12B258928EF559DE4_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* JSONObject_t8136ED73F77B01FF951AB5A2C9B81C4CD0B5C7B6_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* JSONString_tFCFD5D96312BC1A93DA8183AB4B30444DE9B7EB9_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* List_1_t2B71C7C40AB24AD3BA7EFF058B78E7FD76195295_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* SByte_tFEFFEF5D2FEBF5207950AE6FAC150FC53B668DB5_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Single_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Stack_1_tBFA4A76FD7F09AC05C41D7A61CC4D933CAF033DC_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* StringBuilder_t_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* String_t_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* U3Cget_ChildrenU3Ed__24_tAAE17304DD542179299B598CFEA2DF919C468400_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* U3Cget_ChildrenU3Ed__27_t601D7688734A9685C53234154970690BCEDBCA60_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* U3Cget_ChildrenU3Ed__43_tBA5BD787315C9FDAC35DA9789C45EAD75D696F2D_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* UInt16_tF4C148C876015C212FD72652D0B6ED8CC247A455_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* UInt32_t1833D51FFA667B18A5AA4B8D34DE284F8495D29B_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* UInt64_t8F12534CC8FC4B5860F2A2CD1EE79D322E7A41AF_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C String_t* _stringLiteral0767326DBE1DD31063256737FD019DC6682353E2;
IL2CPP_EXTERN_C String_t* _stringLiteral5962E944D7340CE47999BF097B4AFD70C1501FB9;
IL2CPP_EXTERN_C String_t* _stringLiteral5BEFD8CC60A79699B5BB00E37BAC5B62D371E174;
IL2CPP_EXTERN_C String_t* _stringLiteral77D38C0623F92B292B925F6E72CF5CF99A20D4EB;
IL2CPP_EXTERN_C String_t* _stringLiteral785F17F45C331C415D0A7458E6AAC36966399C51;
IL2CPP_EXTERN_C String_t* _stringLiteral7F3238CD8C342B06FB9AB185C610175C84625462;
IL2CPP_EXTERN_C String_t* _stringLiteral848E5ED630B3142F565DD995C6E8D30187ED33CD;
IL2CPP_EXTERN_C String_t* _stringLiteral870C43A28360ADF668EFBACF63A4553351C7FA4C;
IL2CPP_EXTERN_C String_t* _stringLiteralA7C3FCA8C63E127B542B38A5CA5E3FEEDDD1B122;
IL2CPP_EXTERN_C String_t* _stringLiteralB78F235D4291950A7D101307609C259F3E1F033F;
IL2CPP_EXTERN_C String_t* _stringLiteralB7C45DD316C68ABF3429C20058C2981C652192F2;
IL2CPP_EXTERN_C String_t* _stringLiteralBF00FC1AEA59DE3445148D940526441AD4E1FFA7;
IL2CPP_EXTERN_C String_t* _stringLiteralD68508B50CDE1B2E777400476044304CB8149311;
IL2CPP_EXTERN_C String_t* _stringLiteralDA39A3EE5E6B4B0D3255BFEF95601890AFD80709;
IL2CPP_EXTERN_C String_t* _stringLiteralDA666908BB15F4E1D2649752EC5DCBD0D5C64699;
IL2CPP_EXTERN_C String_t* _stringLiteralF18840F490E42D3CE48CDCBF47229C1C240F8ABE;
IL2CPP_EXTERN_C const RuntimeMethod* Dictionary_2_Add_m84B40B9FC5E37AFDFA1D8C971B0B449CC1B95C23_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Dictionary_2_ContainsKey_m99618DFA61C82E07AE40B4277E4FD6719478CA8E_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Dictionary_2_GetEnumerator_m3ED1AC853C31B2712204402D7F3138E2F21B38E2_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Dictionary_2__ctor_mF206CED015A4F12961D84BB5FE5F78D15E491CA3_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Dictionary_2_get_Count_m47F3CE035B00098EF81B1B8690C6EBC6CD4AB31C_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Dictionary_2_get_Item_mF5EABC94CF3E2E6916B4FBE50D92487827CF875E_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Dictionary_2_set_Item_m53E9B223E60BB4A4CFBB5807E236C253836BBADD_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Enumerator_Dispose_m0B5EFE87A935F7B15321F6D02492EC3989DD70D9_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Enumerator_Dispose_m5D3018F5956EA066A5237FF7F5E60E1683EA32E7_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Enumerator_MoveNext_m351A4EF4E1F8015902C7DC4DC2BA4A74B6ACB9C4_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Enumerator_MoveNext_mA1F5B4123B297DE3CEBD20E326757A151D8342F0_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Enumerator_get_Current_m0FA410EDA8DC3E5EC38251FBE80DF30999C8A369_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Enumerator_get_Current_m58A7BF3F5786B6959A34BAE6A8DFCF82E3E39AAD_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* JSONLazyCreator_Set_TisJSONArray_tE863C8377E61FEEC9ACA640D090C176C7CDCB589_m4DFE3CBE4BC89F56E51DE5A97EE67B6A1CC4B7FE_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* JSONLazyCreator_Set_TisJSONBool_t2BBE341953E8A8D293388B59CE964319333E6ECD_mCB9B55A39FD54AD56A8C8DB28060A98E4938171D_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* JSONLazyCreator_Set_TisJSONNumber_t90BD0D71B6F0447E77909AD12B258928EF559DE4_m6C57C23A870484E26456D2D79A770C47A63DD218_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* JSONLazyCreator_Set_TisJSONObject_t8136ED73F77B01FF951AB5A2C9B81C4CD0B5C7B6_m086A73FD912BB396452D77E526AC375844AABFD3_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* JSONNode_Parse_mADBE0CF264E68AFDA1B37FA20CB5FDAFD89134DB_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* KeyValuePair_2_get_Key_mE5155D1D01DACA3EBAB489C906488E451D0EC647_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* KeyValuePair_2_get_Value_m82D9F258CADB85DFD8CE44E3F810B55B94E3DA67_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1_Add_m149138AF1B43A8019AD94D106EB9006FE8996B98_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1_GetEnumerator_mCB0A62D5C74B9F474105BB886112E0E86695C4CF_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1__ctor_m01E98620490C05F4956E30BE71BB07091CD21F78_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1_get_Count_mD73536BDBC542E80A39D3303987D954F5B4E1673_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1_get_Item_m77B77650E01A165CE9FD65AB34DDCD18725B258F_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Stack_1_Peek_mAEF9B60F85F7E57C33817ED5A6546491F9A3505F_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Stack_1_Pop_m95C9A9B157580A32C0DF3B738100E1408B8FF7E8_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Stack_1_Push_m6AC701F463F347526ED326A3D0EE06EF172F219E_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Stack_1__ctor_m74F3363A06AB84AF5E67EEF6BFC1AF3AC9EA84C8_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Stack_1_get_Count_mE3AE26433E9D1F6B2307F77F0D0F67ACC885BA24_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3Cget_ChildrenU3Ed__24_System_Collections_IEnumerator_Reset_mA18412275DA36B0363FB34F6B8892515A8714998_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3Cget_ChildrenU3Ed__27_System_Collections_IEnumerator_Reset_m6B6CFD88298C682F8711FDEA83945EF3CCFB89A0_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3Cget_ChildrenU3Ed__43_System_Collections_IEnumerator_Reset_mA525437DD47F7DFF24EC4B929622D3A7AEED6DA0_RuntimeMethod_var;
struct CultureData_tEEFDCF4ECA1BBF6C0C8C94EB3541657245598F9D_marshaled_com;
struct CultureData_tEEFDCF4ECA1BBF6C0C8C94EB3541657245598F9D_marshaled_pinvoke;
struct CultureInfo_t9BA817D41AD55AC8BD07480DD8AC22F8FFA378E0_marshaled_com;
struct CultureInfo_t9BA817D41AD55AC8BD07480DD8AC22F8FFA378E0_marshaled_pinvoke;
struct Exception_t_marshaled_com;
struct Exception_t_marshaled_pinvoke;

struct ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918;

IL2CPP_EXTERN_C_BEGIN
IL2CPP_EXTERN_C_END

#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
struct U3CModuleU3E_tBF86342D46D41ABDAAE090A2EA84ED114A4A2B46 
{
};
struct Dictionary_2_t9F8163AF97B5FC284EA73FF608A15D501B0D9BDC  : public RuntimeObject
{
	Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ____buckets;
	EntryU5BU5D_t3038E7E397842129132C446208107AE5FAC9EF87* ____entries;
	int32_t ____count;
	int32_t ____freeList;
	int32_t ____freeCount;
	int32_t ____version;
	RuntimeObject* ____comparer;
	KeyCollection_t34A2F3F2228DD4284B597461431814F81466EF49* ____keys;
	ValueCollection_t1510510487B8FC816CBDE09AF063E28F3119D47E* ____values;
	RuntimeObject* ____syncRoot;
};
struct List_1_t2B71C7C40AB24AD3BA7EFF058B78E7FD76195295  : public RuntimeObject
{
	JSONNodeU5BU5D_tF049C2A413B85559640FD86EA7387C21176E3F76* ____items;
	int32_t ____size;
	int32_t ____version;
	RuntimeObject* ____syncRoot;
};
struct List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D  : public RuntimeObject
{
	ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* ____items;
	int32_t ____size;
	int32_t ____version;
	RuntimeObject* ____syncRoot;
};
struct Stack_1_tBFA4A76FD7F09AC05C41D7A61CC4D933CAF033DC  : public RuntimeObject
{
	JSONNodeU5BU5D_tF049C2A413B85559640FD86EA7387C21176E3F76* ____array;
	int32_t ____size;
	int32_t ____version;
	RuntimeObject* ____syncRoot;
};
struct Stack_1_tAD790A47551563636908E21E4F08C54C0C323EB5  : public RuntimeObject
{
	ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* ____array;
	int32_t ____size;
	int32_t ____version;
	RuntimeObject* ____syncRoot;
};
struct BeanBase_tE882EE5D0CEB6587A1AB19056B5EA531600AAD67  : public RuntimeObject
{
};
struct CultureInfo_t9BA817D41AD55AC8BD07480DD8AC22F8FFA378E0  : public RuntimeObject
{
	bool ___m_isReadOnly;
	int32_t ___cultureID;
	int32_t ___parent_lcid;
	int32_t ___datetime_index;
	int32_t ___number_index;
	int32_t ___default_calendar_type;
	bool ___m_useUserOverride;
	NumberFormatInfo_t8E26808B202927FEBF9064FCFEEA4D6E076E6472* ___numInfo;
	DateTimeFormatInfo_t0457520F9FA7B5C8EAAEB3AD50413B6AEEB7458A* ___dateTimeInfo;
	TextInfo_tD3BAFCFD77418851E7D5CB8D2588F47019E414B4* ___textInfo;
	String_t* ___m_name;
	String_t* ___englishname;
	String_t* ___nativename;
	String_t* ___iso3lang;
	String_t* ___iso2lang;
	String_t* ___win3lang;
	String_t* ___territory;
	StringU5BU5D_t7674CD946EC0CE7B3AE0BE70E6EE85F2ECD9F248* ___native_calendar_names;
	CompareInfo_t1B1A6AC3486B570C76ABA52149C9BD4CD82F9E57* ___compareInfo;
	void* ___textinfo_data;
	int32_t ___m_dataItem;
	Calendar_t0A117CC7532A54C17188C2EFEA1F79DB20DF3A3B* ___calendar;
	CultureInfo_t9BA817D41AD55AC8BD07480DD8AC22F8FFA378E0* ___parent_culture;
	bool ___constructed;
	ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* ___cached_serialized_form;
	CultureData_tEEFDCF4ECA1BBF6C0C8C94EB3541657245598F9D* ___m_cultureData;
	bool ___m_isInherited;
};
struct CultureInfo_t9BA817D41AD55AC8BD07480DD8AC22F8FFA378E0_marshaled_pinvoke
{
	int32_t ___m_isReadOnly;
	int32_t ___cultureID;
	int32_t ___parent_lcid;
	int32_t ___datetime_index;
	int32_t ___number_index;
	int32_t ___default_calendar_type;
	int32_t ___m_useUserOverride;
	NumberFormatInfo_t8E26808B202927FEBF9064FCFEEA4D6E076E6472* ___numInfo;
	DateTimeFormatInfo_t0457520F9FA7B5C8EAAEB3AD50413B6AEEB7458A* ___dateTimeInfo;
	TextInfo_tD3BAFCFD77418851E7D5CB8D2588F47019E414B4* ___textInfo;
	char* ___m_name;
	char* ___englishname;
	char* ___nativename;
	char* ___iso3lang;
	char* ___iso2lang;
	char* ___win3lang;
	char* ___territory;
	char** ___native_calendar_names;
	CompareInfo_t1B1A6AC3486B570C76ABA52149C9BD4CD82F9E57* ___compareInfo;
	void* ___textinfo_data;
	int32_t ___m_dataItem;
	Calendar_t0A117CC7532A54C17188C2EFEA1F79DB20DF3A3B* ___calendar;
	CultureInfo_t9BA817D41AD55AC8BD07480DD8AC22F8FFA378E0_marshaled_pinvoke* ___parent_culture;
	int32_t ___constructed;
	Il2CppSafeArray* ___cached_serialized_form;
	CultureData_tEEFDCF4ECA1BBF6C0C8C94EB3541657245598F9D_marshaled_pinvoke* ___m_cultureData;
	int32_t ___m_isInherited;
};
struct CultureInfo_t9BA817D41AD55AC8BD07480DD8AC22F8FFA378E0_marshaled_com
{
	int32_t ___m_isReadOnly;
	int32_t ___cultureID;
	int32_t ___parent_lcid;
	int32_t ___datetime_index;
	int32_t ___number_index;
	int32_t ___default_calendar_type;
	int32_t ___m_useUserOverride;
	NumberFormatInfo_t8E26808B202927FEBF9064FCFEEA4D6E076E6472* ___numInfo;
	DateTimeFormatInfo_t0457520F9FA7B5C8EAAEB3AD50413B6AEEB7458A* ___dateTimeInfo;
	TextInfo_tD3BAFCFD77418851E7D5CB8D2588F47019E414B4* ___textInfo;
	Il2CppChar* ___m_name;
	Il2CppChar* ___englishname;
	Il2CppChar* ___nativename;
	Il2CppChar* ___iso3lang;
	Il2CppChar* ___iso2lang;
	Il2CppChar* ___win3lang;
	Il2CppChar* ___territory;
	Il2CppChar** ___native_calendar_names;
	CompareInfo_t1B1A6AC3486B570C76ABA52149C9BD4CD82F9E57* ___compareInfo;
	void* ___textinfo_data;
	int32_t ___m_dataItem;
	Calendar_t0A117CC7532A54C17188C2EFEA1F79DB20DF3A3B* ___calendar;
	CultureInfo_t9BA817D41AD55AC8BD07480DD8AC22F8FFA378E0_marshaled_com* ___parent_culture;
	int32_t ___constructed;
	Il2CppSafeArray* ___cached_serialized_form;
	CultureData_tEEFDCF4ECA1BBF6C0C8C94EB3541657245598F9D_marshaled_com* ___m_cultureData;
	int32_t ___m_isInherited;
};
struct JSON_t6875DE0B3466BBC190DA24BD4C2F1F1065EBFA0A  : public RuntimeObject
{
};
struct JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA  : public RuntimeObject
{
};
struct String_t  : public RuntimeObject
{
	int32_t ____stringLength;
	Il2CppChar ____firstChar;
};
struct StringBuilder_t  : public RuntimeObject
{
	CharU5BU5D_t799905CF001DD5F13F7DBB310181FC4D8B7D0AAB* ___m_ChunkChars;
	StringBuilder_t* ___m_ChunkPrevious;
	int32_t ___m_ChunkLength;
	int32_t ___m_ChunkOffset;
	int32_t ___m_MaxCapacity;
};
struct StringUtil_t83FC7434DA457FEE74B3C9C89C9C4D36A73AA9B0  : public RuntimeObject
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F  : public RuntimeObject
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_pinvoke
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_com
{
};
struct U3Cget_ChildrenU3Ed__43_tBA5BD787315C9FDAC35DA9789C45EAD75D696F2D  : public RuntimeObject
{
	int32_t ___U3CU3E1__state;
	JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* ___U3CU3E2__current;
	int32_t ___U3CU3El__initialThreadId;
};
struct Enumerator_t89C98852F05E305EB8D5EA5673CCC6A3E5D1ED06 
{
	List_1_t2B71C7C40AB24AD3BA7EFF058B78E7FD76195295* ____list;
	int32_t ____index;
	int32_t ____version;
	JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* ____current;
};
struct Enumerator_t9473BAB568A27E2339D48C1F91319E0F6D244D7A 
{
	List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D* ____list;
	int32_t ____index;
	int32_t ____version;
	RuntimeObject* ____current;
};
struct KeyValuePair_2_tFC32D2507216293851350D29B64D79F950B55230 
{
	RuntimeObject* ___key;
	RuntimeObject* ___value;
};
struct KeyValuePair_2_tDBA0DCDFC0258820AE84EA4644610E4FB71EA809 
{
	String_t* ___key;
	JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* ___value;
};
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22 
{
	bool ___m_value;
};
struct Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3 
{
	uint8_t ___m_value;
};
struct Char_t521A6F19B456D956AF452D926C32709DC03D6B17 
{
	Il2CppChar ___m_value;
};
struct Decimal_tDA6C877282B2D789CF97C0949661CC11D643969F 
{
	union
	{
		#pragma pack(push, tp, 1)
		struct
		{
			int32_t ___flags;
		};
		#pragma pack(pop, tp)
		struct
		{
			int32_t ___flags_forAlignmentOnly;
		};
		#pragma pack(push, tp, 1)
		struct
		{
			char ___hi_OffsetPadding[4];
			int32_t ___hi;
		};
		#pragma pack(pop, tp)
		struct
		{
			char ___hi_OffsetPadding_forAlignmentOnly[4];
			int32_t ___hi_forAlignmentOnly;
		};
		#pragma pack(push, tp, 1)
		struct
		{
			char ___lo_OffsetPadding[8];
			int32_t ___lo;
		};
		#pragma pack(pop, tp)
		struct
		{
			char ___lo_OffsetPadding_forAlignmentOnly[8];
			int32_t ___lo_forAlignmentOnly;
		};
		#pragma pack(push, tp, 1)
		struct
		{
			char ___mid_OffsetPadding[12];
			int32_t ___mid;
		};
		#pragma pack(pop, tp)
		struct
		{
			char ___mid_OffsetPadding_forAlignmentOnly[12];
			int32_t ___mid_forAlignmentOnly;
		};
		#pragma pack(push, tp, 1)
		struct
		{
			char ___ulomidLE_OffsetPadding[8];
			uint64_t ___ulomidLE;
		};
		#pragma pack(pop, tp)
		struct
		{
			char ___ulomidLE_OffsetPadding_forAlignmentOnly[8];
			uint64_t ___ulomidLE_forAlignmentOnly;
		};
	};
};
struct Double_tE150EF3D1D43DEE85D533810AB4C742307EEDE5F 
{
	double ___m_value;
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2  : public ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F
{
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2_marshaled_pinvoke
{
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2_marshaled_com
{
};
struct Guid_t 
{
	int32_t ____a;
	int16_t ____b;
	int16_t ____c;
	uint8_t ____d;
	uint8_t ____e;
	uint8_t ____f;
	uint8_t ____g;
	uint8_t ____h;
	uint8_t ____i;
	uint8_t ____j;
	uint8_t ____k;
};
struct Int16_tB8EF286A9C33492FA6E6D6E67320BE93E794A175 
{
	int16_t ___m_value;
};
struct Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C 
{
	int32_t ___m_value;
};
struct Int64_t092CFB123BE63C28ACDAF65C68F21A526050DBA3 
{
	int64_t ___m_value;
};
struct IntPtr_t 
{
	void* ___m_value;
};
struct JSONArray_tE863C8377E61FEEC9ACA640D090C176C7CDCB589  : public JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA
{
	List_1_t2B71C7C40AB24AD3BA7EFF058B78E7FD76195295* ___m_List;
	bool ___inline;
};
struct JSONBool_t2BBE341953E8A8D293388B59CE964319333E6ECD  : public JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA
{
	bool ___m_Data;
};
struct JSONLazyCreator_t93B398608BA0717C413F6875922A3BF9C7ABD8B9  : public JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA
{
	JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* ___m_Node;
	String_t* ___m_Key;
};
struct JSONNull_tE058D16E7011140DB8F406485C84D3D51DA7A97D  : public JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA
{
};
struct JSONNumber_t90BD0D71B6F0447E77909AD12B258928EF559DE4  : public JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA
{
	double ___m_Data;
};
struct JSONObject_t8136ED73F77B01FF951AB5A2C9B81C4CD0B5C7B6  : public JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA
{
	Dictionary_2_t9F8163AF97B5FC284EA73FF608A15D501B0D9BDC* ___m_Dict;
	bool ___inline;
};
struct JSONString_tFCFD5D96312BC1A93DA8183AB4B30444DE9B7EB9  : public JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA
{
	String_t* ___m_Data;
};
struct SByte_tFEFFEF5D2FEBF5207950AE6FAC150FC53B668DB5 
{
	int8_t ___m_value;
};
struct Single_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C 
{
	float ___m_value;
};
struct UInt16_tF4C148C876015C212FD72652D0B6ED8CC247A455 
{
	uint16_t ___m_value;
};
struct UInt32_t1833D51FFA667B18A5AA4B8D34DE284F8495D29B 
{
	uint32_t ___m_value;
};
struct UInt64_t8F12534CC8FC4B5860F2A2CD1EE79D322E7A41AF 
{
	uint64_t ___m_value;
};
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915 
{
	union
	{
		struct
		{
		};
		uint8_t Void_t4861ACF8F4594C3437BB48B6E56783494B843915__padding[1];
	};
};
struct Enumerator_tEA93FE2B778D098F590CA168BEFC4CD85D73A6B9 
{
	Dictionary_2_t14FE4A752A83D53771C584E4C8D14E01F2AFD7BA* ____dictionary;
	int32_t ____version;
	int32_t ____index;
	KeyValuePair_2_tFC32D2507216293851350D29B64D79F950B55230 ____current;
	int32_t ____getEnumeratorRetType;
};
struct Enumerator_t0C0DD82B30597416F0154D4B6B95DB6D43A2AA04 
{
	Dictionary_2_t9F8163AF97B5FC284EA73FF608A15D501B0D9BDC* ____dictionary;
	int32_t ____version;
	int32_t ____index;
	KeyValuePair_2_tDBA0DCDFC0258820AE84EA4644610E4FB71EA809 ____current;
	int32_t ____getEnumeratorRetType;
};
struct Exception_t  : public RuntimeObject
{
	String_t* ____className;
	String_t* ____message;
	RuntimeObject* ____data;
	Exception_t* ____innerException;
	String_t* ____helpURL;
	RuntimeObject* ____stackTrace;
	String_t* ____stackTraceString;
	String_t* ____remoteStackTraceString;
	int32_t ____remoteStackIndex;
	RuntimeObject* ____dynamicMethods;
	int32_t ____HResult;
	String_t* ____source;
	SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6* ____safeSerializationManager;
	StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF* ___captured_traces;
	IntPtrU5BU5D_tFD177F8C806A6921AD7150264CCC62FA00CAD832* ___native_trace_ips;
	int32_t ___caught_in_unmanaged;
};
struct Exception_t_marshaled_pinvoke
{
	char* ____className;
	char* ____message;
	RuntimeObject* ____data;
	Exception_t_marshaled_pinvoke* ____innerException;
	char* ____helpURL;
	Il2CppIUnknown* ____stackTrace;
	char* ____stackTraceString;
	char* ____remoteStackTraceString;
	int32_t ____remoteStackIndex;
	Il2CppIUnknown* ____dynamicMethods;
	int32_t ____HResult;
	char* ____source;
	SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6* ____safeSerializationManager;
	StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF* ___captured_traces;
	Il2CppSafeArray* ___native_trace_ips;
	int32_t ___caught_in_unmanaged;
};
struct Exception_t_marshaled_com
{
	Il2CppChar* ____className;
	Il2CppChar* ____message;
	RuntimeObject* ____data;
	Exception_t_marshaled_com* ____innerException;
	Il2CppChar* ____helpURL;
	Il2CppIUnknown* ____stackTrace;
	Il2CppChar* ____stackTraceString;
	Il2CppChar* ____remoteStackTraceString;
	int32_t ____remoteStackIndex;
	Il2CppIUnknown* ____dynamicMethods;
	int32_t ____HResult;
	Il2CppChar* ____source;
	SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6* ____safeSerializationManager;
	StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF* ___captured_traces;
	Il2CppSafeArray* ___native_trace_ips;
	int32_t ___caught_in_unmanaged;
};
struct JSONContainerType_tD01C360919C4B7F7CBDBBCCE7E0DF9DE27E0668C 
{
	int32_t ___value__;
};
struct JSONTextMode_t523E0204A202C3209D102A27BDB90623CB089FE0 
{
	int32_t ___value__;
};
struct NumberStyles_t567C6CBC2A2B5B5A2C43B2855D158949984A810C 
{
	int32_t ___value__;
};
struct U3Cget_ChildrenU3Ed__24_tAAE17304DD542179299B598CFEA2DF919C468400  : public RuntimeObject
{
	int32_t ___U3CU3E1__state;
	JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* ___U3CU3E2__current;
	int32_t ___U3CU3El__initialThreadId;
	JSONArray_tE863C8377E61FEEC9ACA640D090C176C7CDCB589* ___U3CU3E4__this;
	Enumerator_t89C98852F05E305EB8D5EA5673CCC6A3E5D1ED06 ___U3CU3E7__wrap1;
};
struct SerializationException_t9BAC85A4408413811FD4394F0919F478DC33294B  : public Exception_t
{
};
struct SystemException_tCC48D868298F4C0705279823E34B00F4FBDB7295  : public Exception_t
{
};
struct U3Cget_ChildrenU3Ed__27_t601D7688734A9685C53234154970690BCEDBCA60  : public RuntimeObject
{
	int32_t ___U3CU3E1__state;
	JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* ___U3CU3E2__current;
	int32_t ___U3CU3El__initialThreadId;
	JSONObject_t8136ED73F77B01FF951AB5A2C9B81C4CD0B5C7B6* ___U3CU3E4__this;
	Enumerator_t0C0DD82B30597416F0154D4B6B95DB6D43A2AA04 ___U3CU3E7__wrap1;
};
struct NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A  : public SystemException_tCC48D868298F4C0705279823E34B00F4FBDB7295
{
};
struct List_1_t2B71C7C40AB24AD3BA7EFF058B78E7FD76195295_StaticFields
{
	JSONNodeU5BU5D_tF049C2A413B85559640FD86EA7387C21176E3F76* ___s_emptyArray;
};
struct List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D_StaticFields
{
	ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* ___s_emptyArray;
};
struct CultureInfo_t9BA817D41AD55AC8BD07480DD8AC22F8FFA378E0_StaticFields
{
	CultureInfo_t9BA817D41AD55AC8BD07480DD8AC22F8FFA378E0* ___invariant_culture_info;
	RuntimeObject* ___shared_table_lock;
	CultureInfo_t9BA817D41AD55AC8BD07480DD8AC22F8FFA378E0* ___default_current_culture;
	CultureInfo_t9BA817D41AD55AC8BD07480DD8AC22F8FFA378E0* ___s_DefaultThreadCurrentUICulture;
	CultureInfo_t9BA817D41AD55AC8BD07480DD8AC22F8FFA378E0* ___s_DefaultThreadCurrentCulture;
	Dictionary_2_t9FA6D82CAFC18769F7515BB51D1C56DAE09381C3* ___shared_by_number;
	Dictionary_2_tE1603CE612C16451D1E56FF4D4859D4FE4087C28* ___shared_by_name;
	CultureInfo_t9BA817D41AD55AC8BD07480DD8AC22F8FFA378E0* ___s_UserPreferredCultureInfoInAppX;
	bool ___IsTaiwanSku;
};
struct JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA_StaticFields
{
	bool ___forceASCII;
	bool ___longAsString;
	bool ___allowLineComments;
	uint8_t ___Color32DefaultAlpha;
	float ___ColorDefaultAlpha;
	int32_t ___VectorContainerType;
	int32_t ___QuaternionContainerType;
	int32_t ___RectContainerType;
	int32_t ___ColorContainerType;
};
struct JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA_ThreadStaticFields
{
	StringBuilder_t* ___m_EscapeBuilder;
};
struct String_t_StaticFields
{
	String_t* ___Empty;
};
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_StaticFields
{
	String_t* ___TrueString;
	String_t* ___FalseString;
};
struct Char_t521A6F19B456D956AF452D926C32709DC03D6B17_StaticFields
{
	ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* ___s_categoryForLatin1;
};
struct Decimal_tDA6C877282B2D789CF97C0949661CC11D643969F_StaticFields
{
	Decimal_tDA6C877282B2D789CF97C0949661CC11D643969F ___Zero;
	Decimal_tDA6C877282B2D789CF97C0949661CC11D643969F ___One;
	Decimal_tDA6C877282B2D789CF97C0949661CC11D643969F ___MinusOne;
	Decimal_tDA6C877282B2D789CF97C0949661CC11D643969F ___MaxValue;
	Decimal_tDA6C877282B2D789CF97C0949661CC11D643969F ___MinValue;
};
struct Guid_t_StaticFields
{
	Guid_t ___Empty;
};
struct JSONNull_tE058D16E7011140DB8F406485C84D3D51DA7A97D_StaticFields
{
	JSONNull_tE058D16E7011140DB8F406485C84D3D51DA7A97D* ___m_StaticInstance;
	bool ___reuseSameInstance;
};
struct Exception_t_StaticFields
{
	RuntimeObject* ___s_EDILock;
};
#ifdef __clang__
#pragma clang diagnostic pop
#endif
struct ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918  : public RuntimeArray
{
	ALIGN_FIELD (8) RuntimeObject* m_Items[1];

	inline RuntimeObject* GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline RuntimeObject** GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, RuntimeObject* value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
	inline RuntimeObject* GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline RuntimeObject** GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, RuntimeObject* value)
	{
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
};


IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Stack_1__ctor_m70E8EDA96A608CE9BAB7FC8313B233AADA573BD4_gshared (Stack_1_tAD790A47551563636908E21E4F08C54C0C323EB5* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Stack_1_Push_m709DD11BC1291A905814182CF9A367DE7399A778_gshared (Stack_1_tAD790A47551563636908E21E4F08C54C0C323EB5* __this, RuntimeObject* ___0_item, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* Stack_1_Peek_mF0ECF6A61726B66E6D9B33D8C4DEAA47E586E6E4_gshared (Stack_1_tAD790A47551563636908E21E4F08C54C0C323EB5* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t Stack_1_get_Count_mD08AE71D49787D30DDD9D484BCD323D646744D2E_gshared_inline (Stack_1_tAD790A47551563636908E21E4F08C54C0C323EB5* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* Stack_1_Pop_m2AFF69249659372F07EE25817DBCAFE74E1CF778_gshared (Stack_1_tAD790A47551563636908E21E4F08C54C0C323EB5* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t List_1_get_Count_m4407E4C389F22B8CEC282C15D56516658746C383_gshared_inline (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void List_1_Add_mEBCF994CC3814631017F46A387B1A192ED6C85C7_gshared_inline (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D* __this, RuntimeObject* ___0_item, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* List_1_get_Item_m33561245D64798C2AB07584C0EC4F240E4839A38_gshared (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D* __this, int32_t ___0_index, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void List_1__ctor_m7F078BB342729BDF11327FD89D7872265328F690_gshared (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Enumerator_t9473BAB568A27E2339D48C1F91319E0F6D244D7A List_1_GetEnumerator_mD8294A7FA2BEB1929487127D476F8EC1CDC23BFC_gshared (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR RuntimeObject* Enumerator_get_Current_m6330F15D18EE4F547C05DF9BF83C5EB710376027_gshared_inline (Enumerator_t9473BAB568A27E2339D48C1F91319E0F6D244D7A* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Enumerator_MoveNext_mE921CC8F29FBBDE7CC3209A0ED0D921D58D00BCB_gshared (Enumerator_t9473BAB568A27E2339D48C1F91319E0F6D244D7A* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Enumerator_Dispose_mD9DC3E3C3697830A4823047AB29A77DBBB5ED419_gshared (Enumerator_t9473BAB568A27E2339D48C1F91319E0F6D244D7A* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Dictionary_2_ContainsKey_m703047C213F7AB55C9DC346596287773A1F670CD_gshared (Dictionary_2_t14FE4A752A83D53771C584E4C8D14E01F2AFD7BA* __this, RuntimeObject* ___0_key, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* Dictionary_2_get_Item_m4AAAECBE902A211BF2126E6AFA280AEF73A3E0D6_gshared (Dictionary_2_t14FE4A752A83D53771C584E4C8D14E01F2AFD7BA* __this, RuntimeObject* ___0_key, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Dictionary_2_get_Count_m4DDA9442C238A443489115E22B026AD366851549_gshared (Dictionary_2_t14FE4A752A83D53771C584E4C8D14E01F2AFD7BA* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Dictionary_2_set_Item_m1A840355E8EDAECEA9D0C6F5E51B248FAA449CBD_gshared (Dictionary_2_t14FE4A752A83D53771C584E4C8D14E01F2AFD7BA* __this, RuntimeObject* ___0_key, RuntimeObject* ___1_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Dictionary_2_Add_m93FFFABE8FCE7FA9793F0915E2A8842C7CD0C0C1_gshared (Dictionary_2_t14FE4A752A83D53771C584E4C8D14E01F2AFD7BA* __this, RuntimeObject* ___0_key, RuntimeObject* ___1_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Enumerator_tEA93FE2B778D098F590CA168BEFC4CD85D73A6B9 Dictionary_2_GetEnumerator_m52AB12790B0B9B46B1DFB1F861C9DBEAB07C1FDA_gshared (Dictionary_2_t14FE4A752A83D53771C584E4C8D14E01F2AFD7BA* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Enumerator_Dispose_mEA5E01B81EB943B7003D87CEC1B6040524F0402C_gshared (Enumerator_tEA93FE2B778D098F590CA168BEFC4CD85D73A6B9* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR KeyValuePair_2_tFC32D2507216293851350D29B64D79F950B55230 Enumerator_get_Current_mE3475384B761E1C7971D3639BD09117FE8363422_gshared_inline (Enumerator_tEA93FE2B778D098F590CA168BEFC4CD85D73A6B9* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR RuntimeObject* KeyValuePair_2_get_Key_mBD8EA7557C27E6956F2AF29DA3F7499B2F51A282_gshared_inline (KeyValuePair_2_tFC32D2507216293851350D29B64D79F950B55230* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR RuntimeObject* KeyValuePair_2_get_Value_mC6BD8075F9C9DDEF7B4D731E5C38EC19103988E7_gshared_inline (KeyValuePair_2_tFC32D2507216293851350D29B64D79F950B55230* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Enumerator_MoveNext_mCD4950A75FFADD54AF354D48C6C0DB0B5A22A5F4_gshared (Enumerator_tEA93FE2B778D098F590CA168BEFC4CD85D73A6B9* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Dictionary_2__ctor_m5B32FBC624618211EB461D59CFBB10E987FD1329_gshared (Dictionary_2_t14FE4A752A83D53771C584E4C8D14E01F2AFD7BA* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* JSONLazyCreator_Set_TisRuntimeObject_m8D40709437526FAE2AAA7A340753FB80084F246F_gshared (JSONLazyCreator_t93B398608BA0717C413F6875922A3BF9C7ABD8B9* __this, RuntimeObject* ___0_aVal, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_NO_INLINE IL2CPP_METHOD_ATTR void List_1_AddWithResize_m79A9BF770BEF9C06BE40D5401E55E375F2726CC4_gshared (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D* __this, RuntimeObject* ___0_item, const RuntimeMethod* method) ;

IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3Cget_ChildrenU3Ed__43__ctor_mE7537C1ADA2199B30E3DD1E586C06868E952582D (U3Cget_ChildrenU3Ed__43_tBA5BD787315C9FDAC35DA9789C45EAD75D696F2D* __this, int32_t ___0_U3CU3E1__state, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void StringBuilder__ctor_m1D99713357DE05DAFA296633639DB55F8C30587D (StringBuilder_t* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR CultureInfo_t9BA817D41AD55AC8BD07480DD8AC22F8FFA378E0* CultureInfo_get_InvariantCulture_mD1E96DC845E34B10F78CB744B0CB5D7D63CEB1E6 (const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Double_TryParse_m1D39DC22A45BC9A576B9D9130600BFD3CB6DA382 (String_t* ___0_s, int32_t ___1_style, RuntimeObject* ___2_provider, double* ___3_result, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Boolean_TryParse_m417053B6E8D3724D0EED9E87C90D143622158352 (String_t* ___0_value, bool* ___1_result, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool String_IsNullOrEmpty_mEA9E3FB005AC28FE02E69FCF95A7B8456192B478 (String_t* ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void JSONString__ctor_m67139D815079187D65873B84717A70843DD17264 (JSONString_tFCFD5D96312BC1A93DA8183AB4B30444DE9B7EB9* __this, String_t* ___0_aData, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR JSONNull_tE058D16E7011140DB8F406485C84D3D51DA7A97D* JSONNull_CreateOrGet_m920C3D38D052C4C8EDE996476F91D9913B6017F2 (const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool JSONNode_op_Equality_mDD36B312D77EC1FC9939276C087EBA8E7B8AD92F (JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* ___0_a, RuntimeObject* ___1_b, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void JSONNumber__ctor_m7E2D0AFEE8AA0DD3E0BD39509764944024821801 (JSONNumber_t90BD0D71B6F0447E77909AD12B258928EF559DE4* __this, double ___0_aData, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void JSONBool__ctor_mA722148B8765BE496706554AD85D6933240357E5 (JSONBool_t2BBE341953E8A8D293388B59CE964319333E6ECD* __this, bool ___0_aData, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Object_GetHashCode_m372C5A7AB16CAC13307C11C4256D706CE57E090C (RuntimeObject* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR StringBuilder_t* JSONNode_get_EscapeBuilder_m438107AE7EFFD53ED93EED225A8B44CE3DA88B28 (const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void StringBuilder_set_Length_mE2427BDAEF91C4E4A6C80F3BDF1F6E01DBCC2414 (StringBuilder_t* __this, int32_t ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t StringBuilder_get_Capacity_m9DBF3B3940BC0BB882CA26F0EDB53896A491AD1E (StringBuilder_t* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t String_get_Length_m42625D67623FA5CC7A44D47425CE86FB946542D2_inline (String_t* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void StringBuilder_set_Capacity_m11BD24481D70C842320ADF7C959CC674D18AF574 (StringBuilder_t* __this, int32_t ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Il2CppChar String_get_Chars_mC49DF0CD2D3BE7BE97B3AD9C995BE3094F8E36D3 (String_t* __this, int32_t ___0_index, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR StringBuilder_t* StringBuilder_Append_m08904D74E0C78E5F36DCD9C9303BDD07886D9F7D (StringBuilder_t* __this, String_t* ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* UInt16_ToString_m75C61173B6A4DCF2D678D8A03EA713FEE29CC98C (uint16_t* __this, String_t* ___0_format, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR StringBuilder_t* StringBuilder_Append_m71228B30F05724CD2CD96D9611DCD61BFB96A6E1 (StringBuilder_t* __this, Il2CppChar ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* JSONNode_op_Implicit_mC9FDB9C979D8D3495B64EC7896BBABB028E6DFBA (String_t* ___0_s, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* String_ToLower_m6191ABA3DC514ED47C10BDA23FD0DDCEAE7ACFBD (String_t* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool String_op_Equality_m030E1B219352228970A076136E455C4E568C02C1 (String_t* ___0_a, String_t* ___1_b, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* JSONNode_op_Implicit_mF47BAEE8FBAC7F443963385B753B916F6029CDFF (bool ___0_b, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* JSONNode_op_Implicit_m8B9AC587F68218D2632273D0BCA2581560305A9C (double ___0_n, const RuntimeMethod* method) ;
inline void Stack_1__ctor_m74F3363A06AB84AF5E67EEF6BFC1AF3AC9EA84C8 (Stack_1_tBFA4A76FD7F09AC05C41D7A61CC4D933CAF033DC* __this, const RuntimeMethod* method)
{
	((  void (*) (Stack_1_tBFA4A76FD7F09AC05C41D7A61CC4D933CAF033DC*, const RuntimeMethod*))Stack_1__ctor_m70E8EDA96A608CE9BAB7FC8313B233AADA573BD4_gshared)(__this, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void JSONObject__ctor_mD4F6D4B7B8D8D3F4FCF45B4BAC32AEB66588307A (JSONObject_t8136ED73F77B01FF951AB5A2C9B81C4CD0B5C7B6* __this, const RuntimeMethod* method) ;
inline void Stack_1_Push_m6AC701F463F347526ED326A3D0EE06EF172F219E (Stack_1_tBFA4A76FD7F09AC05C41D7A61CC4D933CAF033DC* __this, JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* ___0_item, const RuntimeMethod* method)
{
	((  void (*) (Stack_1_tBFA4A76FD7F09AC05C41D7A61CC4D933CAF033DC*, JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA*, const RuntimeMethod*))Stack_1_Push_m709DD11BC1291A905814182CF9A367DE7399A778_gshared)(__this, ___0_item, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool JSONNode_op_Inequality_mF703DC644DDA438C8F56773D20194C2899DDE6F4 (JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* ___0_a, RuntimeObject* ___1_b, const RuntimeMethod* method) ;
inline JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* Stack_1_Peek_mAEF9B60F85F7E57C33817ED5A6546491F9A3505F (Stack_1_tBFA4A76FD7F09AC05C41D7A61CC4D933CAF033DC* __this, const RuntimeMethod* method)
{
	return ((  JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* (*) (Stack_1_tBFA4A76FD7F09AC05C41D7A61CC4D933CAF033DC*, const RuntimeMethod*))Stack_1_Peek_mF0ECF6A61726B66E6D9B33D8C4DEAA47E586E6E4_gshared)(__this, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void JSONArray__ctor_mD53A24956E3512E4F0F8989651E9E8BA8B2257B8 (JSONArray_tE863C8377E61FEEC9ACA640D090C176C7CDCB589* __this, const RuntimeMethod* method) ;
inline int32_t Stack_1_get_Count_mE3AE26433E9D1F6B2307F77F0D0F67ACC885BA24_inline (Stack_1_tBFA4A76FD7F09AC05C41D7A61CC4D933CAF033DC* __this, const RuntimeMethod* method)
{
	return ((  int32_t (*) (Stack_1_tBFA4A76FD7F09AC05C41D7A61CC4D933CAF033DC*, const RuntimeMethod*))Stack_1_get_Count_mD08AE71D49787D30DDD9D484BCD323D646744D2E_gshared_inline)(__this, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Exception__ctor_m9B2BD92CD68916245A75109105D9071C9D430E7F (Exception_t* __this, String_t* ___0_message, const RuntimeMethod* method) ;
inline JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* Stack_1_Pop_m95C9A9B157580A32C0DF3B738100E1408B8FF7E8 (Stack_1_tBFA4A76FD7F09AC05C41D7A61CC4D933CAF033DC* __this, const RuntimeMethod* method)
{
	return ((  JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* (*) (Stack_1_tBFA4A76FD7F09AC05C41D7A61CC4D933CAF033DC*, const RuntimeMethod*))Stack_1_Pop_m2AFF69249659372F07EE25817DBCAFE74E1CF778_gshared)(__this, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t StringBuilder_get_Length_mDEA041E7357C68CC3B5885276BB403676DAAE0D8 (StringBuilder_t* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* JSONNode_ParseElement_m5BA504C2E93CF73EA23A0AB555BD759F26481B5A (String_t* ___0_token, bool ___1_quoted, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* String_Substring_mB1D94F47935D22E130FF2C01DBB6A4135FBB76CE (String_t* __this, int32_t ___0_startIndex, int32_t ___1_length, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Int32_Parse_mF336325913DF125A6F8F05F2909E3AFB0D73830E (String_t* ___0_s, int32_t ___1_style, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2 (RuntimeObject* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Environment_get_CurrentManagedThreadId_m66483AADCCC13272EBDCD94D31D2E52603C24BDF (const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void NotSupportedException__ctor_m1398D0CDE19B36AA3DE9392879738C1EA2439CDF (NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* U3Cget_ChildrenU3Ed__43_System_Collections_Generic_IEnumerableU3CSimpleJSON_JSONNodeU3E_GetEnumerator_mE865D7DB0E34EA57362F88DA761C43889CB795ED (U3Cget_ChildrenU3Ed__43_tBA5BD787315C9FDAC35DA9789C45EAD75D696F2D* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void JSONLazyCreator__ctor_m5A3DAB47229CD45650E935BDB7D0C9FACA460720 (JSONLazyCreator_t93B398608BA0717C413F6875922A3BF9C7ABD8B9* __this, JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* ___0_aNode, const RuntimeMethod* method) ;
inline int32_t List_1_get_Count_mD73536BDBC542E80A39D3303987D954F5B4E1673_inline (List_1_t2B71C7C40AB24AD3BA7EFF058B78E7FD76195295* __this, const RuntimeMethod* method)
{
	return ((  int32_t (*) (List_1_t2B71C7C40AB24AD3BA7EFF058B78E7FD76195295*, const RuntimeMethod*))List_1_get_Count_m4407E4C389F22B8CEC282C15D56516658746C383_gshared_inline)(__this, method);
}
inline void List_1_Add_m149138AF1B43A8019AD94D106EB9006FE8996B98_inline (List_1_t2B71C7C40AB24AD3BA7EFF058B78E7FD76195295* __this, JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* ___0_item, const RuntimeMethod* method)
{
	((  void (*) (List_1_t2B71C7C40AB24AD3BA7EFF058B78E7FD76195295*, JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA*, const RuntimeMethod*))List_1_Add_mEBCF994CC3814631017F46A387B1A192ED6C85C7_gshared_inline)(__this, ___0_item, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3Cget_ChildrenU3Ed__24__ctor_m95EEF28AA5E9412DACD322799156F76F44D26F51 (U3Cget_ChildrenU3Ed__24_tAAE17304DD542179299B598CFEA2DF919C468400* __this, int32_t ___0_U3CU3E1__state, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR StringBuilder_t* StringBuilder_AppendLine_m3BC704C4E6A8531027D8C9287D0AB2AA0188AC4E (StringBuilder_t* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR StringBuilder_t* StringBuilder_Append_mE20F6CD28FC8E8C9FD65987DBD32E6087CCE1CF3 (StringBuilder_t* __this, Il2CppChar ___0_value, int32_t ___1_repeatCount, const RuntimeMethod* method) ;
inline JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* List_1_get_Item_m77B77650E01A165CE9FD65AB34DDCD18725B258F (List_1_t2B71C7C40AB24AD3BA7EFF058B78E7FD76195295* __this, int32_t ___0_index, const RuntimeMethod* method)
{
	return ((  JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* (*) (List_1_t2B71C7C40AB24AD3BA7EFF058B78E7FD76195295*, int32_t, const RuntimeMethod*))List_1_get_Item_m33561245D64798C2AB07584C0EC4F240E4839A38_gshared)(__this, ___0_index, method);
}
inline void List_1__ctor_m01E98620490C05F4956E30BE71BB07091CD21F78 (List_1_t2B71C7C40AB24AD3BA7EFF058B78E7FD76195295* __this, const RuntimeMethod* method)
{
	((  void (*) (List_1_t2B71C7C40AB24AD3BA7EFF058B78E7FD76195295*, const RuntimeMethod*))List_1__ctor_m7F078BB342729BDF11327FD89D7872265328F690_gshared)(__this, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void JSONNode__ctor_mB710A69AF2EDA72D624E6291A6B8438316D5E8DA (JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3Cget_ChildrenU3Ed__24_U3CU3Em__Finally1_mCBE434BF8D79BABE04A07BDD8B884F9AC88725DE (U3Cget_ChildrenU3Ed__24_tAAE17304DD542179299B598CFEA2DF919C468400* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3Cget_ChildrenU3Ed__24_System_IDisposable_Dispose_m46DC15736BACD0533B79A2CED81CBC6E8F1602FA (U3Cget_ChildrenU3Ed__24_tAAE17304DD542179299B598CFEA2DF919C468400* __this, const RuntimeMethod* method) ;
inline Enumerator_t89C98852F05E305EB8D5EA5673CCC6A3E5D1ED06 List_1_GetEnumerator_mCB0A62D5C74B9F474105BB886112E0E86695C4CF (List_1_t2B71C7C40AB24AD3BA7EFF058B78E7FD76195295* __this, const RuntimeMethod* method)
{
	return ((  Enumerator_t89C98852F05E305EB8D5EA5673CCC6A3E5D1ED06 (*) (List_1_t2B71C7C40AB24AD3BA7EFF058B78E7FD76195295*, const RuntimeMethod*))List_1_GetEnumerator_mD8294A7FA2BEB1929487127D476F8EC1CDC23BFC_gshared)(__this, method);
}
inline JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* Enumerator_get_Current_m0FA410EDA8DC3E5EC38251FBE80DF30999C8A369_inline (Enumerator_t89C98852F05E305EB8D5EA5673CCC6A3E5D1ED06* __this, const RuntimeMethod* method)
{
	return ((  JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* (*) (Enumerator_t89C98852F05E305EB8D5EA5673CCC6A3E5D1ED06*, const RuntimeMethod*))Enumerator_get_Current_m6330F15D18EE4F547C05DF9BF83C5EB710376027_gshared_inline)(__this, method);
}
inline bool Enumerator_MoveNext_mA1F5B4123B297DE3CEBD20E326757A151D8342F0 (Enumerator_t89C98852F05E305EB8D5EA5673CCC6A3E5D1ED06* __this, const RuntimeMethod* method)
{
	return ((  bool (*) (Enumerator_t89C98852F05E305EB8D5EA5673CCC6A3E5D1ED06*, const RuntimeMethod*))Enumerator_MoveNext_mE921CC8F29FBBDE7CC3209A0ED0D921D58D00BCB_gshared)(__this, method);
}
inline void Enumerator_Dispose_m0B5EFE87A935F7B15321F6D02492EC3989DD70D9 (Enumerator_t89C98852F05E305EB8D5EA5673CCC6A3E5D1ED06* __this, const RuntimeMethod* method)
{
	((  void (*) (Enumerator_t89C98852F05E305EB8D5EA5673CCC6A3E5D1ED06*, const RuntimeMethod*))Enumerator_Dispose_mD9DC3E3C3697830A4823047AB29A77DBBB5ED419_gshared)(__this, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* U3Cget_ChildrenU3Ed__24_System_Collections_Generic_IEnumerableU3CSimpleJSON_JSONNodeU3E_GetEnumerator_mA138DAA861DE2241F9AFAB0F9A425A3BDF7D3A49 (U3Cget_ChildrenU3Ed__24_tAAE17304DD542179299B598CFEA2DF919C468400* __this, const RuntimeMethod* method) ;
inline bool Dictionary_2_ContainsKey_m99618DFA61C82E07AE40B4277E4FD6719478CA8E (Dictionary_2_t9F8163AF97B5FC284EA73FF608A15D501B0D9BDC* __this, String_t* ___0_key, const RuntimeMethod* method)
{
	return ((  bool (*) (Dictionary_2_t9F8163AF97B5FC284EA73FF608A15D501B0D9BDC*, String_t*, const RuntimeMethod*))Dictionary_2_ContainsKey_m703047C213F7AB55C9DC346596287773A1F670CD_gshared)(__this, ___0_key, method);
}
inline JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* Dictionary_2_get_Item_mF5EABC94CF3E2E6916B4FBE50D92487827CF875E (Dictionary_2_t9F8163AF97B5FC284EA73FF608A15D501B0D9BDC* __this, String_t* ___0_key, const RuntimeMethod* method)
{
	return ((  JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* (*) (Dictionary_2_t9F8163AF97B5FC284EA73FF608A15D501B0D9BDC*, String_t*, const RuntimeMethod*))Dictionary_2_get_Item_m4AAAECBE902A211BF2126E6AFA280AEF73A3E0D6_gshared)(__this, ___0_key, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void JSONLazyCreator__ctor_m3965360CD8D0DEA5E2ED0B3E4981936534F30C93 (JSONLazyCreator_t93B398608BA0717C413F6875922A3BF9C7ABD8B9* __this, JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* ___0_aNode, String_t* ___1_aKey, const RuntimeMethod* method) ;
inline int32_t Dictionary_2_get_Count_m47F3CE035B00098EF81B1B8690C6EBC6CD4AB31C (Dictionary_2_t9F8163AF97B5FC284EA73FF608A15D501B0D9BDC* __this, const RuntimeMethod* method)
{
	return ((  int32_t (*) (Dictionary_2_t9F8163AF97B5FC284EA73FF608A15D501B0D9BDC*, const RuntimeMethod*))Dictionary_2_get_Count_m4DDA9442C238A443489115E22B026AD366851549_gshared)(__this, method);
}
inline void Dictionary_2_set_Item_m53E9B223E60BB4A4CFBB5807E236C253836BBADD (Dictionary_2_t9F8163AF97B5FC284EA73FF608A15D501B0D9BDC* __this, String_t* ___0_key, JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* ___1_value, const RuntimeMethod* method)
{
	((  void (*) (Dictionary_2_t9F8163AF97B5FC284EA73FF608A15D501B0D9BDC*, String_t*, JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA*, const RuntimeMethod*))Dictionary_2_set_Item_m1A840355E8EDAECEA9D0C6F5E51B248FAA449CBD_gshared)(__this, ___0_key, ___1_value, method);
}
inline void Dictionary_2_Add_m84B40B9FC5E37AFDFA1D8C971B0B449CC1B95C23 (Dictionary_2_t9F8163AF97B5FC284EA73FF608A15D501B0D9BDC* __this, String_t* ___0_key, JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* ___1_value, const RuntimeMethod* method)
{
	((  void (*) (Dictionary_2_t9F8163AF97B5FC284EA73FF608A15D501B0D9BDC*, String_t*, JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA*, const RuntimeMethod*))Dictionary_2_Add_m93FFFABE8FCE7FA9793F0915E2A8842C7CD0C0C1_gshared)(__this, ___0_key, ___1_value, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Guid_t Guid_NewGuid_m1F4894E8DC089811D6252148AD5858E58D43A7BD (const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* Guid_ToString_m2BFFD5FA726E03FA707AAFCCF065896C46D5290C (Guid_t* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3Cget_ChildrenU3Ed__27__ctor_m18A161AD65F41D242EAC8B1AE005D17C47287B05 (U3Cget_ChildrenU3Ed__27_t601D7688734A9685C53234154970690BCEDBCA60* __this, int32_t ___0_U3CU3E1__state, const RuntimeMethod* method) ;
inline Enumerator_t0C0DD82B30597416F0154D4B6B95DB6D43A2AA04 Dictionary_2_GetEnumerator_m3ED1AC853C31B2712204402D7F3138E2F21B38E2 (Dictionary_2_t9F8163AF97B5FC284EA73FF608A15D501B0D9BDC* __this, const RuntimeMethod* method)
{
	return ((  Enumerator_t0C0DD82B30597416F0154D4B6B95DB6D43A2AA04 (*) (Dictionary_2_t9F8163AF97B5FC284EA73FF608A15D501B0D9BDC*, const RuntimeMethod*))Dictionary_2_GetEnumerator_m52AB12790B0B9B46B1DFB1F861C9DBEAB07C1FDA_gshared)(__this, method);
}
inline void Enumerator_Dispose_m5D3018F5956EA066A5237FF7F5E60E1683EA32E7 (Enumerator_t0C0DD82B30597416F0154D4B6B95DB6D43A2AA04* __this, const RuntimeMethod* method)
{
	((  void (*) (Enumerator_t0C0DD82B30597416F0154D4B6B95DB6D43A2AA04*, const RuntimeMethod*))Enumerator_Dispose_mEA5E01B81EB943B7003D87CEC1B6040524F0402C_gshared)(__this, method);
}
inline KeyValuePair_2_tDBA0DCDFC0258820AE84EA4644610E4FB71EA809 Enumerator_get_Current_m58A7BF3F5786B6959A34BAE6A8DFCF82E3E39AAD_inline (Enumerator_t0C0DD82B30597416F0154D4B6B95DB6D43A2AA04* __this, const RuntimeMethod* method)
{
	return ((  KeyValuePair_2_tDBA0DCDFC0258820AE84EA4644610E4FB71EA809 (*) (Enumerator_t0C0DD82B30597416F0154D4B6B95DB6D43A2AA04*, const RuntimeMethod*))Enumerator_get_Current_mE3475384B761E1C7971D3639BD09117FE8363422_gshared_inline)(__this, method);
}
inline String_t* KeyValuePair_2_get_Key_mE5155D1D01DACA3EBAB489C906488E451D0EC647_inline (KeyValuePair_2_tDBA0DCDFC0258820AE84EA4644610E4FB71EA809* __this, const RuntimeMethod* method)
{
	return ((  String_t* (*) (KeyValuePair_2_tDBA0DCDFC0258820AE84EA4644610E4FB71EA809*, const RuntimeMethod*))KeyValuePair_2_get_Key_mBD8EA7557C27E6956F2AF29DA3F7499B2F51A282_gshared_inline)(__this, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* JSONNode_Escape_m6646863678DB23BDEB28738CC57E452169FFB0FF (String_t* ___0_aText, const RuntimeMethod* method) ;
inline JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* KeyValuePair_2_get_Value_m82D9F258CADB85DFD8CE44E3F810B55B94E3DA67_inline (KeyValuePair_2_tDBA0DCDFC0258820AE84EA4644610E4FB71EA809* __this, const RuntimeMethod* method)
{
	return ((  JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* (*) (KeyValuePair_2_tDBA0DCDFC0258820AE84EA4644610E4FB71EA809*, const RuntimeMethod*))KeyValuePair_2_get_Value_mC6BD8075F9C9DDEF7B4D731E5C38EC19103988E7_gshared_inline)(__this, method);
}
inline bool Enumerator_MoveNext_m351A4EF4E1F8015902C7DC4DC2BA4A74B6ACB9C4 (Enumerator_t0C0DD82B30597416F0154D4B6B95DB6D43A2AA04* __this, const RuntimeMethod* method)
{
	return ((  bool (*) (Enumerator_t0C0DD82B30597416F0154D4B6B95DB6D43A2AA04*, const RuntimeMethod*))Enumerator_MoveNext_mCD4950A75FFADD54AF354D48C6C0DB0B5A22A5F4_gshared)(__this, method);
}
inline void Dictionary_2__ctor_mF206CED015A4F12961D84BB5FE5F78D15E491CA3 (Dictionary_2_t9F8163AF97B5FC284EA73FF608A15D501B0D9BDC* __this, const RuntimeMethod* method)
{
	((  void (*) (Dictionary_2_t9F8163AF97B5FC284EA73FF608A15D501B0D9BDC*, const RuntimeMethod*))Dictionary_2__ctor_m5B32FBC624618211EB461D59CFBB10E987FD1329_gshared)(__this, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3Cget_ChildrenU3Ed__27_U3CU3Em__Finally1_m8BC545E469064B06F65F22A2FDA8F9BCCC693CF0 (U3Cget_ChildrenU3Ed__27_t601D7688734A9685C53234154970690BCEDBCA60* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3Cget_ChildrenU3Ed__27_System_IDisposable_Dispose_m3DCE5C10014923EEDA1EEE48F20B376E8A60C30B (U3Cget_ChildrenU3Ed__27_t601D7688734A9685C53234154970690BCEDBCA60* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* U3Cget_ChildrenU3Ed__27_System_Collections_Generic_IEnumerableU3CSimpleJSON_JSONNodeU3E_GetEnumerator_m7C7CEB31168871A6A9A9A8BB596BE0714969C4A2 (U3Cget_ChildrenU3Ed__27_t601D7688734A9685C53234154970690BCEDBCA60* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool JSONNode_Equals_m2F945F67C1B2DB40C080EB1E4CFF37C4B5EF4C73 (JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* __this, RuntimeObject* ___0_obj, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* Double_ToString_m4318830D9F771852FDCF21C14CF9E8ABC7E77357 (double* __this, RuntimeObject* ___0_provider, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool JSONNumber_IsNumeric_m9C7E4C675A347170CB4627C5DC33B5AE16FC2855 (RuntimeObject* ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR double Convert_ToDouble_m86FF4F837721833186E883102C056A35F0860EB0 (RuntimeObject* ___0_value, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t Double_GetHashCode_m3761FC05AD24D97A68FA1E8412A9454DF3880E32_inline (double* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* Boolean_ToString_m6646C8026B1DF381A1EE8CD13549175E9703CC63 (bool* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Boolean_GetHashCode_mEDB6904770C962BAF4510E5D24F08083C33900E3 (bool* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void JSONNull__ctor_m483EFD80DA95F906B6F966CB6FC63ED8194457C4 (JSONNull_tE058D16E7011140DB8F406485C84D3D51DA7A97D* __this, const RuntimeMethod* method) ;
inline JSONArray_tE863C8377E61FEEC9ACA640D090C176C7CDCB589* JSONLazyCreator_Set_TisJSONArray_tE863C8377E61FEEC9ACA640D090C176C7CDCB589_m4DFE3CBE4BC89F56E51DE5A97EE67B6A1CC4B7FE (JSONLazyCreator_t93B398608BA0717C413F6875922A3BF9C7ABD8B9* __this, JSONArray_tE863C8377E61FEEC9ACA640D090C176C7CDCB589* ___0_aVal, const RuntimeMethod* method)
{
	return ((  JSONArray_tE863C8377E61FEEC9ACA640D090C176C7CDCB589* (*) (JSONLazyCreator_t93B398608BA0717C413F6875922A3BF9C7ABD8B9*, JSONArray_tE863C8377E61FEEC9ACA640D090C176C7CDCB589*, const RuntimeMethod*))JSONLazyCreator_Set_TisRuntimeObject_m8D40709437526FAE2AAA7A340753FB80084F246F_gshared)(__this, ___0_aVal, method);
}
inline JSONObject_t8136ED73F77B01FF951AB5A2C9B81C4CD0B5C7B6* JSONLazyCreator_Set_TisJSONObject_t8136ED73F77B01FF951AB5A2C9B81C4CD0B5C7B6_m086A73FD912BB396452D77E526AC375844AABFD3 (JSONLazyCreator_t93B398608BA0717C413F6875922A3BF9C7ABD8B9* __this, JSONObject_t8136ED73F77B01FF951AB5A2C9B81C4CD0B5C7B6* ___0_aVal, const RuntimeMethod* method)
{
	return ((  JSONObject_t8136ED73F77B01FF951AB5A2C9B81C4CD0B5C7B6* (*) (JSONLazyCreator_t93B398608BA0717C413F6875922A3BF9C7ABD8B9*, JSONObject_t8136ED73F77B01FF951AB5A2C9B81C4CD0B5C7B6*, const RuntimeMethod*))JSONLazyCreator_Set_TisRuntimeObject_m8D40709437526FAE2AAA7A340753FB80084F246F_gshared)(__this, ___0_aVal, method);
}
inline JSONNumber_t90BD0D71B6F0447E77909AD12B258928EF559DE4* JSONLazyCreator_Set_TisJSONNumber_t90BD0D71B6F0447E77909AD12B258928EF559DE4_m6C57C23A870484E26456D2D79A770C47A63DD218 (JSONLazyCreator_t93B398608BA0717C413F6875922A3BF9C7ABD8B9* __this, JSONNumber_t90BD0D71B6F0447E77909AD12B258928EF559DE4* ___0_aVal, const RuntimeMethod* method)
{
	return ((  JSONNumber_t90BD0D71B6F0447E77909AD12B258928EF559DE4* (*) (JSONLazyCreator_t93B398608BA0717C413F6875922A3BF9C7ABD8B9*, JSONNumber_t90BD0D71B6F0447E77909AD12B258928EF559DE4*, const RuntimeMethod*))JSONLazyCreator_Set_TisRuntimeObject_m8D40709437526FAE2AAA7A340753FB80084F246F_gshared)(__this, ___0_aVal, method);
}
inline JSONBool_t2BBE341953E8A8D293388B59CE964319333E6ECD* JSONLazyCreator_Set_TisJSONBool_t2BBE341953E8A8D293388B59CE964319333E6ECD_mCB9B55A39FD54AD56A8C8DB28060A98E4938171D (JSONLazyCreator_t93B398608BA0717C413F6875922A3BF9C7ABD8B9* __this, JSONBool_t2BBE341953E8A8D293388B59CE964319333E6ECD* ___0_aVal, const RuntimeMethod* method)
{
	return ((  JSONBool_t2BBE341953E8A8D293388B59CE964319333E6ECD* (*) (JSONLazyCreator_t93B398608BA0717C413F6875922A3BF9C7ABD8B9*, JSONBool_t2BBE341953E8A8D293388B59CE964319333E6ECD*, const RuntimeMethod*))JSONLazyCreator_Set_TisRuntimeObject_m8D40709437526FAE2AAA7A340753FB80084F246F_gshared)(__this, ___0_aVal, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* JSONNode_Parse_mADBE0CF264E68AFDA1B37FA20CB5FDAFD89134DB (String_t* ___0_aJSON, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Exception__ctor_m203319D1EA1274689B380A947B4ADC8445662B8F (Exception_t* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int64_t BitConverter_DoubleToInt64Bits_m4F42741818550F9956B5FBAF88C051F4DE5B0AE6_inline (double ___0_value, const RuntimeMethod* method) ;
inline void List_1_AddWithResize_m79A9BF770BEF9C06BE40D5401E55E375F2726CC4 (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D* __this, RuntimeObject* ___0_item, const RuntimeMethod* method)
{
	((  void (*) (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D*, RuntimeObject*, const RuntimeMethod*))List_1_AddWithResize_m79A9BF770BEF9C06BE40D5401E55E375F2726CC4_gshared)(__this, ___0_item, method);
}
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* JSONNode_get_Item_m2789110CF7C043941B8ADE346C27D2AD7F5981D6 (JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* __this, String_t* ___0_aKey, const RuntimeMethod* method) 
{
	{
		return (JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA*)NULL;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* JSONNode_get_Value_mFAA1E59DB4A4D92767636C3718E541C4881FAC2C (JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralDA39A3EE5E6B4B0D3255BFEF95601890AFD80709);
		s_Il2CppMethodInitialized = true;
	}
	{
		return _stringLiteralDA39A3EE5E6B4B0D3255BFEF95601890AFD80709;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t JSONNode_get_Count_mA19123F3F13787E6DD0D166AA6D6B712B10A9444 (JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* __this, const RuntimeMethod* method) 
{
	{
		return 0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool JSONNode_get_IsNumber_m160EEE254980E6F06BCFCEB5492E992F05D4726B (JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* __this, const RuntimeMethod* method) 
{
	{
		return (bool)0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool JSONNode_get_IsString_m107542FC9A25BD915A96D7C971CFD6F6241BE66C (JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* __this, const RuntimeMethod* method) 
{
	{
		return (bool)0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool JSONNode_get_IsBoolean_mA0F8374F51535747BFD5F73644DB2F6E518F8795 (JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* __this, const RuntimeMethod* method) 
{
	{
		return (bool)0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool JSONNode_get_IsArray_m2F3E53642DE363005692ACAD90224733568E444D (JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* __this, const RuntimeMethod* method) 
{
	{
		return (bool)0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool JSONNode_get_IsObject_m4C8E25375A5D6E5723A0C3FC7588CFA8DFA361D8 (JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* __this, const RuntimeMethod* method) 
{
	{
		return (bool)0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void JSONNode_set_Inline_mD632F21A061B28017D822B8D01D67AA9EFA97795 (JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* __this, bool ___0_value, const RuntimeMethod* method) 
{
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void JSONNode_Add_m879A11B84014500D73CBAFFC2F05FB8875A37C93 (JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* __this, String_t* ___0_aKey, JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* ___1_aItem, const RuntimeMethod* method) 
{
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void JSONNode_Add_m9346E62B4D6B4E4E11544206C08C6D04A4E390EC (JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* __this, JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* ___0_aItem, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralDA39A3EE5E6B4B0D3255BFEF95601890AFD80709);
		s_Il2CppMethodInitialized = true;
	}
	{
		JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* L_0 = ___0_aItem;
		VirtualActionInvoker2< String_t*, JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* >::Invoke(13, __this, _stringLiteralDA39A3EE5E6B4B0D3255BFEF95601890AFD80709, L_0);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* JSONNode_get_Children_mF12EA954BB989D3A1BD138312EE4442DFA90262A (JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3Cget_ChildrenU3Ed__43_tBA5BD787315C9FDAC35DA9789C45EAD75D696F2D_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		U3Cget_ChildrenU3Ed__43_tBA5BD787315C9FDAC35DA9789C45EAD75D696F2D* L_0 = (U3Cget_ChildrenU3Ed__43_tBA5BD787315C9FDAC35DA9789C45EAD75D696F2D*)il2cpp_codegen_object_new(U3Cget_ChildrenU3Ed__43_tBA5BD787315C9FDAC35DA9789C45EAD75D696F2D_il2cpp_TypeInfo_var);
		U3Cget_ChildrenU3Ed__43__ctor_mE7537C1ADA2199B30E3DD1E586C06868E952582D(L_0, ((int32_t)-2), NULL);
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* JSONNode_ToString_m5CDD027A73248D91FAD6F54411C9EE989579A49C (JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&StringBuilder_t_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	StringBuilder_t* V_0 = NULL;
	{
		StringBuilder_t* L_0 = (StringBuilder_t*)il2cpp_codegen_object_new(StringBuilder_t_il2cpp_TypeInfo_var);
		StringBuilder__ctor_m1D99713357DE05DAFA296633639DB55F8C30587D(L_0, NULL);
		V_0 = L_0;
		StringBuilder_t* L_1 = V_0;
		VirtualActionInvoker4< StringBuilder_t*, int32_t, int32_t, int32_t >::Invoke(16, __this, L_1, 0, 0, 0);
		StringBuilder_t* L_2 = V_0;
		NullCheck(L_2);
		String_t* L_3;
		L_3 = VirtualFuncInvoker0< String_t* >::Invoke(3, L_2);
		return L_3;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR double JSONNode_get_AsDouble_m03198809148461A12A92246E6BEC9971253388CF (JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&CultureInfo_t9BA817D41AD55AC8BD07480DD8AC22F8FFA378E0_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	double V_0 = 0.0;
	{
		V_0 = (0.0);
		String_t* L_0;
		L_0 = VirtualFuncInvoker0< String_t* >::Invoke(5, __this);
		il2cpp_codegen_runtime_class_init_inline(CultureInfo_t9BA817D41AD55AC8BD07480DD8AC22F8FFA378E0_il2cpp_TypeInfo_var);
		CultureInfo_t9BA817D41AD55AC8BD07480DD8AC22F8FFA378E0* L_1;
		L_1 = CultureInfo_get_InvariantCulture_mD1E96DC845E34B10F78CB744B0CB5D7D63CEB1E6(NULL);
		bool L_2;
		L_2 = Double_TryParse_m1D39DC22A45BC9A576B9D9130600BFD3CB6DA382(L_0, ((int32_t)167), L_1, (&V_0), NULL);
		if (!L_2)
		{
			goto IL_0025;
		}
	}
	{
		double L_3 = V_0;
		return L_3;
	}

IL_0025:
	{
		return (0.0);
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t JSONNode_get_AsInt_mEEAD84A7C57B395751839D4743B43FD8C4E1492C (JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* __this, const RuntimeMethod* method) 
{
	{
		double L_0;
		L_0 = VirtualFuncInvoker0< double >::Invoke(17, __this);
		return il2cpp_codegen_cast_double_to_int<int32_t>(L_0);
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float JSONNode_get_AsFloat_mF9F9B03307E2CCEBC04EBEF38E6B0FB887FDFF3B (JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* __this, const RuntimeMethod* method) 
{
	{
		double L_0;
		L_0 = VirtualFuncInvoker0< double >::Invoke(17, __this);
		return ((float)L_0);
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool JSONNode_get_AsBool_m57DF879067093FE2FD9F13EF0A181A4C25E3E87E (JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	bool V_0 = false;
	{
		V_0 = (bool)0;
		String_t* L_0;
		L_0 = VirtualFuncInvoker0< String_t* >::Invoke(5, __this);
		il2cpp_codegen_runtime_class_init_inline(Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_il2cpp_TypeInfo_var);
		bool L_1;
		L_1 = Boolean_TryParse_m417053B6E8D3724D0EED9E87C90D143622158352(L_0, (&V_0), NULL);
		if (!L_1)
		{
			goto IL_0013;
		}
	}
	{
		bool L_2 = V_0;
		return L_2;
	}

IL_0013:
	{
		String_t* L_3;
		L_3 = VirtualFuncInvoker0< String_t* >::Invoke(5, __this);
		bool L_4;
		L_4 = String_IsNullOrEmpty_mEA9E3FB005AC28FE02E69FCF95A7B8456192B478(L_3, NULL);
		return (bool)((((int32_t)L_4) == ((int32_t)0))? 1 : 0);
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* JSONNode_op_Implicit_mC9FDB9C979D8D3495B64EC7896BBABB028E6DFBA (String_t* ___0_s, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&JSONNull_tE058D16E7011140DB8F406485C84D3D51DA7A97D_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&JSONString_tFCFD5D96312BC1A93DA8183AB4B30444DE9B7EB9_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		String_t* L_0 = ___0_s;
		if (!L_0)
		{
			goto IL_000a;
		}
	}
	{
		String_t* L_1 = ___0_s;
		JSONString_tFCFD5D96312BC1A93DA8183AB4B30444DE9B7EB9* L_2 = (JSONString_tFCFD5D96312BC1A93DA8183AB4B30444DE9B7EB9*)il2cpp_codegen_object_new(JSONString_tFCFD5D96312BC1A93DA8183AB4B30444DE9B7EB9_il2cpp_TypeInfo_var);
		JSONString__ctor_m67139D815079187D65873B84717A70843DD17264(L_2, L_1, NULL);
		return L_2;
	}

IL_000a:
	{
		il2cpp_codegen_runtime_class_init_inline(JSONNull_tE058D16E7011140DB8F406485C84D3D51DA7A97D_il2cpp_TypeInfo_var);
		JSONNull_tE058D16E7011140DB8F406485C84D3D51DA7A97D* L_3;
		L_3 = JSONNull_CreateOrGet_m920C3D38D052C4C8EDE996476F91D9913B6017F2(NULL);
		return L_3;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* JSONNode_op_Implicit_m92182AB10DDBDA15C0ACD65DAB7686AC9EBBC1A2 (JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* ___0_d, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* L_0 = ___0_d;
		il2cpp_codegen_runtime_class_init_inline(JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA_il2cpp_TypeInfo_var);
		bool L_1;
		L_1 = JSONNode_op_Equality_mDD36B312D77EC1FC9939276C087EBA8E7B8AD92F(L_0, NULL, NULL);
		if (L_1)
		{
			goto IL_0010;
		}
	}
	{
		JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* L_2 = ___0_d;
		NullCheck(L_2);
		String_t* L_3;
		L_3 = VirtualFuncInvoker0< String_t* >::Invoke(5, L_2);
		return L_3;
	}

IL_0010:
	{
		return (String_t*)NULL;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* JSONNode_op_Implicit_m8B9AC587F68218D2632273D0BCA2581560305A9C (double ___0_n, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&JSONNumber_t90BD0D71B6F0447E77909AD12B258928EF559DE4_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		double L_0 = ___0_n;
		JSONNumber_t90BD0D71B6F0447E77909AD12B258928EF559DE4* L_1 = (JSONNumber_t90BD0D71B6F0447E77909AD12B258928EF559DE4*)il2cpp_codegen_object_new(JSONNumber_t90BD0D71B6F0447E77909AD12B258928EF559DE4_il2cpp_TypeInfo_var);
		JSONNumber__ctor_m7E2D0AFEE8AA0DD3E0BD39509764944024821801(L_1, L_0, NULL);
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float JSONNode_op_Implicit_m0B547315E3441F84DABFA0631791BA6B26026C0E (JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* ___0_d, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* L_0 = ___0_d;
		il2cpp_codegen_runtime_class_init_inline(JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA_il2cpp_TypeInfo_var);
		bool L_1;
		L_1 = JSONNode_op_Equality_mDD36B312D77EC1FC9939276C087EBA8E7B8AD92F(L_0, NULL, NULL);
		if (L_1)
		{
			goto IL_0010;
		}
	}
	{
		JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* L_2 = ___0_d;
		NullCheck(L_2);
		float L_3;
		L_3 = VirtualFuncInvoker0< float >::Invoke(19, L_2);
		return L_3;
	}

IL_0010:
	{
		return (0.0f);
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t JSONNode_op_Implicit_mA64E016EBFB9AC1A1753B7EECD259D6596ECD58F (JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* ___0_d, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* L_0 = ___0_d;
		il2cpp_codegen_runtime_class_init_inline(JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA_il2cpp_TypeInfo_var);
		bool L_1;
		L_1 = JSONNode_op_Equality_mDD36B312D77EC1FC9939276C087EBA8E7B8AD92F(L_0, NULL, NULL);
		if (L_1)
		{
			goto IL_0010;
		}
	}
	{
		JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* L_2 = ___0_d;
		NullCheck(L_2);
		int32_t L_3;
		L_3 = VirtualFuncInvoker0< int32_t >::Invoke(18, L_2);
		return L_3;
	}

IL_0010:
	{
		return 0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* JSONNode_op_Implicit_mF47BAEE8FBAC7F443963385B753B916F6029CDFF (bool ___0_b, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&JSONBool_t2BBE341953E8A8D293388B59CE964319333E6ECD_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		bool L_0 = ___0_b;
		JSONBool_t2BBE341953E8A8D293388B59CE964319333E6ECD* L_1 = (JSONBool_t2BBE341953E8A8D293388B59CE964319333E6ECD*)il2cpp_codegen_object_new(JSONBool_t2BBE341953E8A8D293388B59CE964319333E6ECD_il2cpp_TypeInfo_var);
		JSONBool__ctor_mA722148B8765BE496706554AD85D6933240357E5(L_1, L_0, NULL);
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool JSONNode_op_Implicit_mD4636D7BE4CEB59BBA765E6523EA467190D145C8 (JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* ___0_d, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* L_0 = ___0_d;
		il2cpp_codegen_runtime_class_init_inline(JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA_il2cpp_TypeInfo_var);
		bool L_1;
		L_1 = JSONNode_op_Equality_mDD36B312D77EC1FC9939276C087EBA8E7B8AD92F(L_0, NULL, NULL);
		if (L_1)
		{
			goto IL_0010;
		}
	}
	{
		JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* L_2 = ___0_d;
		NullCheck(L_2);
		bool L_3;
		L_3 = VirtualFuncInvoker0< bool >::Invoke(20, L_2);
		return L_3;
	}

IL_0010:
	{
		return (bool)0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool JSONNode_op_Equality_mDD36B312D77EC1FC9939276C087EBA8E7B8AD92F (JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* ___0_a, RuntimeObject* ___1_b, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&JSONLazyCreator_t93B398608BA0717C413F6875922A3BF9C7ABD8B9_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&JSONNull_tE058D16E7011140DB8F406485C84D3D51DA7A97D_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	bool V_0 = false;
	bool V_1 = false;
	int32_t G_B6_0 = 0;
	int32_t G_B10_0 = 0;
	{
		JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* L_0 = ___0_a;
		RuntimeObject* L_1 = ___1_b;
		if ((!(((RuntimeObject*)(JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA*)L_0) == ((RuntimeObject*)(RuntimeObject*)L_1))))
		{
			goto IL_0006;
		}
	}
	{
		return (bool)1;
	}

IL_0006:
	{
		JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* L_2 = ___0_a;
		if (((JSONNull_tE058D16E7011140DB8F406485C84D3D51DA7A97D*)IsInstClass((RuntimeObject*)L_2, JSONNull_tE058D16E7011140DB8F406485C84D3D51DA7A97D_il2cpp_TypeInfo_var)))
		{
			goto IL_001c;
		}
	}
	{
		JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* L_3 = ___0_a;
		if (!L_3)
		{
			goto IL_001c;
		}
	}
	{
		JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* L_4 = ___0_a;
		G_B6_0 = ((!(((RuntimeObject*)(JSONLazyCreator_t93B398608BA0717C413F6875922A3BF9C7ABD8B9*)((JSONLazyCreator_t93B398608BA0717C413F6875922A3BF9C7ABD8B9*)IsInstClass((RuntimeObject*)L_4, JSONLazyCreator_t93B398608BA0717C413F6875922A3BF9C7ABD8B9_il2cpp_TypeInfo_var))) <= ((RuntimeObject*)(RuntimeObject*)NULL)))? 1 : 0);
		goto IL_001d;
	}

IL_001c:
	{
		G_B6_0 = 1;
	}

IL_001d:
	{
		V_0 = (bool)G_B6_0;
		RuntimeObject* L_5 = ___1_b;
		if (((JSONNull_tE058D16E7011140DB8F406485C84D3D51DA7A97D*)IsInstClass((RuntimeObject*)L_5, JSONNull_tE058D16E7011140DB8F406485C84D3D51DA7A97D_il2cpp_TypeInfo_var)))
		{
			goto IL_0034;
		}
	}
	{
		RuntimeObject* L_6 = ___1_b;
		if (!L_6)
		{
			goto IL_0034;
		}
	}
	{
		RuntimeObject* L_7 = ___1_b;
		G_B10_0 = ((!(((RuntimeObject*)(JSONLazyCreator_t93B398608BA0717C413F6875922A3BF9C7ABD8B9*)((JSONLazyCreator_t93B398608BA0717C413F6875922A3BF9C7ABD8B9*)IsInstClass((RuntimeObject*)L_7, JSONLazyCreator_t93B398608BA0717C413F6875922A3BF9C7ABD8B9_il2cpp_TypeInfo_var))) <= ((RuntimeObject*)(RuntimeObject*)NULL)))? 1 : 0);
		goto IL_0035;
	}

IL_0034:
	{
		G_B10_0 = 1;
	}

IL_0035:
	{
		V_1 = (bool)G_B10_0;
		bool L_8 = V_0;
		bool L_9 = V_1;
		if (!((int32_t)((int32_t)L_8&(int32_t)L_9)))
		{
			goto IL_003d;
		}
	}
	{
		return (bool)1;
	}

IL_003d:
	{
		bool L_10 = V_0;
		if (L_10)
		{
			goto IL_0048;
		}
	}
	{
		JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* L_11 = ___0_a;
		RuntimeObject* L_12 = ___1_b;
		NullCheck(L_11);
		bool L_13;
		L_13 = VirtualFuncInvoker1< bool, RuntimeObject* >::Invoke(0, L_11, L_12);
		return L_13;
	}

IL_0048:
	{
		return (bool)0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool JSONNode_op_Inequality_mF703DC644DDA438C8F56773D20194C2899DDE6F4 (JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* ___0_a, RuntimeObject* ___1_b, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* L_0 = ___0_a;
		RuntimeObject* L_1 = ___1_b;
		il2cpp_codegen_runtime_class_init_inline(JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA_il2cpp_TypeInfo_var);
		bool L_2;
		L_2 = JSONNode_op_Equality_mDD36B312D77EC1FC9939276C087EBA8E7B8AD92F(L_0, L_1, NULL);
		return (bool)((((int32_t)L_2) == ((int32_t)0))? 1 : 0);
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool JSONNode_Equals_m2F945F67C1B2DB40C080EB1E4CFF37C4B5EF4C73 (JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* __this, RuntimeObject* ___0_obj, const RuntimeMethod* method) 
{
	{
		RuntimeObject* L_0 = ___0_obj;
		return (bool)((((RuntimeObject*)(JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA*)__this) == ((RuntimeObject*)(RuntimeObject*)L_0))? 1 : 0);
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t JSONNode_GetHashCode_mC2348FE74B28479DE2E574EB96456B2528AF110F (JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* __this, const RuntimeMethod* method) 
{
	{
		int32_t L_0;
		L_0 = Object_GetHashCode_m372C5A7AB16CAC13307C11C4256D706CE57E090C(__this, NULL);
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR StringBuilder_t* JSONNode_get_EscapeBuilder_m438107AE7EFFD53ED93EED225A8B44CE3DA88B28 (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&StringBuilder_t_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		il2cpp_codegen_runtime_class_init_inline(JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA_il2cpp_TypeInfo_var);
		StringBuilder_t* L_0 = ((JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA_ThreadStaticFields*)il2cpp_codegen_get_thread_static_data(JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA_il2cpp_TypeInfo_var))->___m_EscapeBuilder;
		if (L_0)
		{
			goto IL_0011;
		}
	}
	{
		StringBuilder_t* L_1 = (StringBuilder_t*)il2cpp_codegen_object_new(StringBuilder_t_il2cpp_TypeInfo_var);
		StringBuilder__ctor_m1D99713357DE05DAFA296633639DB55F8C30587D(L_1, NULL);
		il2cpp_codegen_runtime_class_init_inline(JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA_il2cpp_TypeInfo_var);
		((JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA_ThreadStaticFields*)il2cpp_codegen_get_thread_static_data(JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA_il2cpp_TypeInfo_var))->___m_EscapeBuilder = L_1;
		Il2CppCodeGenWriteBarrier((void**)(&((JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA_ThreadStaticFields*)il2cpp_codegen_get_thread_static_data(JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA_il2cpp_TypeInfo_var))->___m_EscapeBuilder), (void*)L_1);
	}

IL_0011:
	{
		il2cpp_codegen_runtime_class_init_inline(JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA_il2cpp_TypeInfo_var);
		StringBuilder_t* L_2 = ((JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA_ThreadStaticFields*)il2cpp_codegen_get_thread_static_data(JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA_il2cpp_TypeInfo_var))->___m_EscapeBuilder;
		return L_2;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* JSONNode_Escape_m6646863678DB23BDEB28738CC57E452169FFB0FF (String_t* ___0_aText, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral5962E944D7340CE47999BF097B4AFD70C1501FB9);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral785F17F45C331C415D0A7458E6AAC36966399C51);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral7F3238CD8C342B06FB9AB185C610175C84625462);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral848E5ED630B3142F565DD995C6E8D30187ED33CD);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralA7C3FCA8C63E127B542B38A5CA5E3FEEDDD1B122);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralB78F235D4291950A7D101307609C259F3E1F033F);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralD68508B50CDE1B2E777400476044304CB8149311);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralDA666908BB15F4E1D2649752EC5DCBD0D5C64699);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralF18840F490E42D3CE48CDCBF47229C1C240F8ABE);
		s_Il2CppMethodInitialized = true;
	}
	StringBuilder_t* V_0 = NULL;
	String_t* V_1 = NULL;
	String_t* V_2 = NULL;
	int32_t V_3 = 0;
	Il2CppChar V_4 = 0x0;
	uint16_t V_5 = 0;
	{
		il2cpp_codegen_runtime_class_init_inline(JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA_il2cpp_TypeInfo_var);
		StringBuilder_t* L_0;
		L_0 = JSONNode_get_EscapeBuilder_m438107AE7EFFD53ED93EED225A8B44CE3DA88B28(NULL);
		V_0 = L_0;
		StringBuilder_t* L_1 = V_0;
		NullCheck(L_1);
		StringBuilder_set_Length_mE2427BDAEF91C4E4A6C80F3BDF1F6E01DBCC2414(L_1, 0, NULL);
		StringBuilder_t* L_2 = V_0;
		NullCheck(L_2);
		int32_t L_3;
		L_3 = StringBuilder_get_Capacity_m9DBF3B3940BC0BB882CA26F0EDB53896A491AD1E(L_2, NULL);
		String_t* L_4 = ___0_aText;
		NullCheck(L_4);
		int32_t L_5;
		L_5 = String_get_Length_m42625D67623FA5CC7A44D47425CE86FB946542D2_inline(L_4, NULL);
		String_t* L_6 = ___0_aText;
		NullCheck(L_6);
		int32_t L_7;
		L_7 = String_get_Length_m42625D67623FA5CC7A44D47425CE86FB946542D2_inline(L_6, NULL);
		if ((((int32_t)L_3) >= ((int32_t)((int32_t)il2cpp_codegen_add(L_5, ((int32_t)(L_7/((int32_t)10))))))))
		{
			goto IL_003b;
		}
	}
	{
		StringBuilder_t* L_8 = V_0;
		String_t* L_9 = ___0_aText;
		NullCheck(L_9);
		int32_t L_10;
		L_10 = String_get_Length_m42625D67623FA5CC7A44D47425CE86FB946542D2_inline(L_9, NULL);
		String_t* L_11 = ___0_aText;
		NullCheck(L_11);
		int32_t L_12;
		L_12 = String_get_Length_m42625D67623FA5CC7A44D47425CE86FB946542D2_inline(L_11, NULL);
		NullCheck(L_8);
		StringBuilder_set_Capacity_m11BD24481D70C842320ADF7C959CC674D18AF574(L_8, ((int32_t)il2cpp_codegen_add(L_10, ((int32_t)(L_12/((int32_t)10))))), NULL);
	}

IL_003b:
	{
		String_t* L_13 = ___0_aText;
		V_2 = L_13;
		V_3 = 0;
		goto IL_0125;
	}

IL_0044:
	{
		String_t* L_14 = V_2;
		int32_t L_15 = V_3;
		NullCheck(L_14);
		Il2CppChar L_16;
		L_16 = String_get_Chars_mC49DF0CD2D3BE7BE97B3AD9C995BE3094F8E36D3(L_14, L_15, NULL);
		V_4 = L_16;
		Il2CppChar L_17 = V_4;
		switch (((int32_t)il2cpp_codegen_subtract((int32_t)L_17, 8)))
		{
			case 0:
			{
				goto IL_00c6;
			}
			case 1:
			{
				goto IL_00b8;
			}
			case 2:
			{
				goto IL_009c;
			}
			case 3:
			{
				goto IL_00e2;
			}
			case 4:
			{
				goto IL_00d4;
			}
			case 5:
			{
				goto IL_00aa;
			}
		}
	}
	{
		Il2CppChar L_18 = V_4;
		if ((((int32_t)L_18) == ((int32_t)((int32_t)34))))
		{
			goto IL_008b;
		}
	}
	{
		Il2CppChar L_19 = V_4;
		if ((!(((uint32_t)L_19) == ((uint32_t)((int32_t)92)))))
		{
			goto IL_00e2;
		}
	}
	{
		StringBuilder_t* L_20 = V_0;
		NullCheck(L_20);
		StringBuilder_t* L_21;
		L_21 = StringBuilder_Append_m08904D74E0C78E5F36DCD9C9303BDD07886D9F7D(L_20, _stringLiteralF18840F490E42D3CE48CDCBF47229C1C240F8ABE, NULL);
		goto IL_0121;
	}

IL_008b:
	{
		StringBuilder_t* L_22 = V_0;
		NullCheck(L_22);
		StringBuilder_t* L_23;
		L_23 = StringBuilder_Append_m08904D74E0C78E5F36DCD9C9303BDD07886D9F7D(L_22, _stringLiteral848E5ED630B3142F565DD995C6E8D30187ED33CD, NULL);
		goto IL_0121;
	}

IL_009c:
	{
		StringBuilder_t* L_24 = V_0;
		NullCheck(L_24);
		StringBuilder_t* L_25;
		L_25 = StringBuilder_Append_m08904D74E0C78E5F36DCD9C9303BDD07886D9F7D(L_24, _stringLiteral785F17F45C331C415D0A7458E6AAC36966399C51, NULL);
		goto IL_0121;
	}

IL_00aa:
	{
		StringBuilder_t* L_26 = V_0;
		NullCheck(L_26);
		StringBuilder_t* L_27;
		L_27 = StringBuilder_Append_m08904D74E0C78E5F36DCD9C9303BDD07886D9F7D(L_26, _stringLiteralB78F235D4291950A7D101307609C259F3E1F033F, NULL);
		goto IL_0121;
	}

IL_00b8:
	{
		StringBuilder_t* L_28 = V_0;
		NullCheck(L_28);
		StringBuilder_t* L_29;
		L_29 = StringBuilder_Append_m08904D74E0C78E5F36DCD9C9303BDD07886D9F7D(L_28, _stringLiteral7F3238CD8C342B06FB9AB185C610175C84625462, NULL);
		goto IL_0121;
	}

IL_00c6:
	{
		StringBuilder_t* L_30 = V_0;
		NullCheck(L_30);
		StringBuilder_t* L_31;
		L_31 = StringBuilder_Append_m08904D74E0C78E5F36DCD9C9303BDD07886D9F7D(L_30, _stringLiteral5962E944D7340CE47999BF097B4AFD70C1501FB9, NULL);
		goto IL_0121;
	}

IL_00d4:
	{
		StringBuilder_t* L_32 = V_0;
		NullCheck(L_32);
		StringBuilder_t* L_33;
		L_33 = StringBuilder_Append_m08904D74E0C78E5F36DCD9C9303BDD07886D9F7D(L_32, _stringLiteralA7C3FCA8C63E127B542B38A5CA5E3FEEDDD1B122, NULL);
		goto IL_0121;
	}

IL_00e2:
	{
		Il2CppChar L_34 = V_4;
		if ((((int32_t)L_34) < ((int32_t)((int32_t)32))))
		{
			goto IL_00f5;
		}
	}
	{
		il2cpp_codegen_runtime_class_init_inline(JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA_il2cpp_TypeInfo_var);
		bool L_35 = ((JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA_StaticFields*)il2cpp_codegen_static_fields_for(JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA_il2cpp_TypeInfo_var))->___forceASCII;
		if (!L_35)
		{
			goto IL_0118;
		}
	}
	{
		Il2CppChar L_36 = V_4;
		if ((((int32_t)L_36) <= ((int32_t)((int32_t)127))))
		{
			goto IL_0118;
		}
	}

IL_00f5:
	{
		Il2CppChar L_37 = V_4;
		V_5 = L_37;
		StringBuilder_t* L_38 = V_0;
		NullCheck(L_38);
		StringBuilder_t* L_39;
		L_39 = StringBuilder_Append_m08904D74E0C78E5F36DCD9C9303BDD07886D9F7D(L_38, _stringLiteralDA666908BB15F4E1D2649752EC5DCBD0D5C64699, NULL);
		String_t* L_40;
		L_40 = UInt16_ToString_m75C61173B6A4DCF2D678D8A03EA713FEE29CC98C((&V_5), _stringLiteralD68508B50CDE1B2E777400476044304CB8149311, NULL);
		NullCheck(L_39);
		StringBuilder_t* L_41;
		L_41 = StringBuilder_Append_m08904D74E0C78E5F36DCD9C9303BDD07886D9F7D(L_39, L_40, NULL);
		goto IL_0121;
	}

IL_0118:
	{
		StringBuilder_t* L_42 = V_0;
		Il2CppChar L_43 = V_4;
		NullCheck(L_42);
		StringBuilder_t* L_44;
		L_44 = StringBuilder_Append_m71228B30F05724CD2CD96D9611DCD61BFB96A6E1(L_42, L_43, NULL);
	}

IL_0121:
	{
		int32_t L_45 = V_3;
		V_3 = ((int32_t)il2cpp_codegen_add(L_45, 1));
	}

IL_0125:
	{
		int32_t L_46 = V_3;
		String_t* L_47 = V_2;
		NullCheck(L_47);
		int32_t L_48;
		L_48 = String_get_Length_m42625D67623FA5CC7A44D47425CE86FB946542D2_inline(L_47, NULL);
		if ((((int32_t)L_46) < ((int32_t)L_48)))
		{
			goto IL_0044;
		}
	}
	{
		StringBuilder_t* L_49 = V_0;
		NullCheck(L_49);
		String_t* L_50;
		L_50 = VirtualFuncInvoker0< String_t* >::Invoke(3, L_49);
		V_1 = L_50;
		StringBuilder_t* L_51 = V_0;
		NullCheck(L_51);
		StringBuilder_set_Length_mE2427BDAEF91C4E4A6C80F3BDF1F6E01DBCC2414(L_51, 0, NULL);
		String_t* L_52 = V_1;
		return L_52;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* JSONNode_ParseElement_m5BA504C2E93CF73EA23A0AB555BD759F26481B5A (String_t* ___0_token, bool ___1_quoted, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&CultureInfo_t9BA817D41AD55AC8BD07480DD8AC22F8FFA378E0_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&JSONNull_tE058D16E7011140DB8F406485C84D3D51DA7A97D_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral5BEFD8CC60A79699B5BB00E37BAC5B62D371E174);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral77D38C0623F92B292B925F6E72CF5CF99A20D4EB);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralB7C45DD316C68ABF3429C20058C2981C652192F2);
		s_Il2CppMethodInitialized = true;
	}
	double V_0 = 0.0;
	String_t* V_1 = NULL;
	{
		bool L_0 = ___1_quoted;
		if (!L_0)
		{
			goto IL_000a;
		}
	}
	{
		String_t* L_1 = ___0_token;
		il2cpp_codegen_runtime_class_init_inline(JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA_il2cpp_TypeInfo_var);
		JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* L_2;
		L_2 = JSONNode_op_Implicit_mC9FDB9C979D8D3495B64EC7896BBABB028E6DFBA(L_1, NULL);
		return L_2;
	}

IL_000a:
	{
		String_t* L_3 = ___0_token;
		NullCheck(L_3);
		int32_t L_4;
		L_4 = String_get_Length_m42625D67623FA5CC7A44D47425CE86FB946542D2_inline(L_3, NULL);
		if ((((int32_t)L_4) > ((int32_t)5)))
		{
			goto IL_0058;
		}
	}
	{
		String_t* L_5 = ___0_token;
		NullCheck(L_5);
		String_t* L_6;
		L_6 = String_ToLower_m6191ABA3DC514ED47C10BDA23FD0DDCEAE7ACFBD(L_5, NULL);
		V_1 = L_6;
		String_t* L_7 = V_1;
		bool L_8;
		L_8 = String_op_Equality_m030E1B219352228970A076136E455C4E568C02C1(L_7, _stringLiteral77D38C0623F92B292B925F6E72CF5CF99A20D4EB, NULL);
		if (L_8)
		{
			goto IL_0034;
		}
	}
	{
		String_t* L_9 = V_1;
		bool L_10;
		L_10 = String_op_Equality_m030E1B219352228970A076136E455C4E568C02C1(L_9, _stringLiteralB7C45DD316C68ABF3429C20058C2981C652192F2, NULL);
		if (!L_10)
		{
			goto IL_0045;
		}
	}

IL_0034:
	{
		String_t* L_11 = V_1;
		bool L_12;
		L_12 = String_op_Equality_m030E1B219352228970A076136E455C4E568C02C1(L_11, _stringLiteralB7C45DD316C68ABF3429C20058C2981C652192F2, NULL);
		il2cpp_codegen_runtime_class_init_inline(JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA_il2cpp_TypeInfo_var);
		JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* L_13;
		L_13 = JSONNode_op_Implicit_mF47BAEE8FBAC7F443963385B753B916F6029CDFF(L_12, NULL);
		return L_13;
	}

IL_0045:
	{
		String_t* L_14 = V_1;
		bool L_15;
		L_15 = String_op_Equality_m030E1B219352228970A076136E455C4E568C02C1(L_14, _stringLiteral5BEFD8CC60A79699B5BB00E37BAC5B62D371E174, NULL);
		if (!L_15)
		{
			goto IL_0058;
		}
	}
	{
		il2cpp_codegen_runtime_class_init_inline(JSONNull_tE058D16E7011140DB8F406485C84D3D51DA7A97D_il2cpp_TypeInfo_var);
		JSONNull_tE058D16E7011140DB8F406485C84D3D51DA7A97D* L_16;
		L_16 = JSONNull_CreateOrGet_m920C3D38D052C4C8EDE996476F91D9913B6017F2(NULL);
		return L_16;
	}

IL_0058:
	{
		String_t* L_17 = ___0_token;
		il2cpp_codegen_runtime_class_init_inline(CultureInfo_t9BA817D41AD55AC8BD07480DD8AC22F8FFA378E0_il2cpp_TypeInfo_var);
		CultureInfo_t9BA817D41AD55AC8BD07480DD8AC22F8FFA378E0* L_18;
		L_18 = CultureInfo_get_InvariantCulture_mD1E96DC845E34B10F78CB744B0CB5D7D63CEB1E6(NULL);
		bool L_19;
		L_19 = Double_TryParse_m1D39DC22A45BC9A576B9D9130600BFD3CB6DA382(L_17, ((int32_t)167), L_18, (&V_0), NULL);
		if (!L_19)
		{
			goto IL_0073;
		}
	}
	{
		double L_20 = V_0;
		il2cpp_codegen_runtime_class_init_inline(JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA_il2cpp_TypeInfo_var);
		JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* L_21;
		L_21 = JSONNode_op_Implicit_m8B9AC587F68218D2632273D0BCA2581560305A9C(L_20, NULL);
		return L_21;
	}

IL_0073:
	{
		String_t* L_22 = ___0_token;
		il2cpp_codegen_runtime_class_init_inline(JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA_il2cpp_TypeInfo_var);
		JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* L_23;
		L_23 = JSONNode_op_Implicit_mC9FDB9C979D8D3495B64EC7896BBABB028E6DFBA(L_22, NULL);
		return L_23;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* JSONNode_Parse_mADBE0CF264E68AFDA1B37FA20CB5FDAFD89134DB (String_t* ___0_aJSON, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&JSONArray_tE863C8377E61FEEC9ACA640D090C176C7CDCB589_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&JSONObject_t8136ED73F77B01FF951AB5A2C9B81C4CD0B5C7B6_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Stack_1_Peek_mAEF9B60F85F7E57C33817ED5A6546491F9A3505F_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Stack_1_Pop_m95C9A9B157580A32C0DF3B738100E1408B8FF7E8_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Stack_1_Push_m6AC701F463F347526ED326A3D0EE06EF172F219E_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Stack_1__ctor_m74F3363A06AB84AF5E67EEF6BFC1AF3AC9EA84C8_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Stack_1_get_Count_mE3AE26433E9D1F6B2307F77F0D0F67ACC885BA24_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Stack_1_tBFA4A76FD7F09AC05C41D7A61CC4D933CAF033DC_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&StringBuilder_t_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralDA39A3EE5E6B4B0D3255BFEF95601890AFD80709);
		s_Il2CppMethodInitialized = true;
	}
	Stack_1_tBFA4A76FD7F09AC05C41D7A61CC4D933CAF033DC* V_0 = NULL;
	JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* V_1 = NULL;
	int32_t V_2 = 0;
	StringBuilder_t* V_3 = NULL;
	String_t* V_4 = NULL;
	bool V_5 = false;
	bool V_6 = false;
	bool V_7 = false;
	Il2CppChar V_8 = 0x0;
	Il2CppChar V_9 = 0x0;
	String_t* V_10 = NULL;
	{
		Stack_1_tBFA4A76FD7F09AC05C41D7A61CC4D933CAF033DC* L_0 = (Stack_1_tBFA4A76FD7F09AC05C41D7A61CC4D933CAF033DC*)il2cpp_codegen_object_new(Stack_1_tBFA4A76FD7F09AC05C41D7A61CC4D933CAF033DC_il2cpp_TypeInfo_var);
		Stack_1__ctor_m74F3363A06AB84AF5E67EEF6BFC1AF3AC9EA84C8(L_0, Stack_1__ctor_m74F3363A06AB84AF5E67EEF6BFC1AF3AC9EA84C8_RuntimeMethod_var);
		V_0 = L_0;
		V_1 = (JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA*)NULL;
		V_2 = 0;
		StringBuilder_t* L_1 = (StringBuilder_t*)il2cpp_codegen_object_new(StringBuilder_t_il2cpp_TypeInfo_var);
		StringBuilder__ctor_m1D99713357DE05DAFA296633639DB55F8C30587D(L_1, NULL);
		V_3 = L_1;
		V_4 = _stringLiteralDA39A3EE5E6B4B0D3255BFEF95601890AFD80709;
		V_5 = (bool)0;
		V_6 = (bool)0;
		V_7 = (bool)0;
		goto IL_03f0;
	}

IL_0025:
	{
		String_t* L_2 = ___0_aJSON;
		int32_t L_3 = V_2;
		NullCheck(L_2);
		Il2CppChar L_4;
		L_4 = String_get_Chars_mC49DF0CD2D3BE7BE97B3AD9C995BE3094F8E36D3(L_2, L_3, NULL);
		V_8 = L_4;
		Il2CppChar L_5 = V_8;
		if ((!(((uint32_t)L_5) <= ((uint32_t)((int32_t)47)))))
		{
			goto IL_0086;
		}
	}
	{
		Il2CppChar L_6 = V_8;
		if ((!(((uint32_t)L_6) <= ((uint32_t)((int32_t)32)))))
		{
			goto IL_0066;
		}
	}
	{
		Il2CppChar L_7 = V_8;
		switch (((int32_t)il2cpp_codegen_subtract((int32_t)L_7, ((int32_t)9))))
		{
			case 0:
			{
				goto IL_02a9;
			}
			case 1:
			{
				goto IL_02a1;
			}
			case 2:
			{
				goto IL_03de;
			}
			case 3:
			{
				goto IL_03de;
			}
			case 4:
			{
				goto IL_02a1;
			}
		}
	}
	{
		Il2CppChar L_8 = V_8;
		if ((((int32_t)L_8) == ((int32_t)((int32_t)32))))
		{
			goto IL_02a9;
		}
	}
	{
		goto IL_03de;
	}

IL_0066:
	{
		Il2CppChar L_9 = V_8;
		if ((((int32_t)L_9) == ((int32_t)((int32_t)34))))
		{
			goto IL_023b;
		}
	}
	{
		Il2CppChar L_10 = V_8;
		if ((((int32_t)L_10) == ((int32_t)((int32_t)44))))
		{
			goto IL_024e;
		}
	}
	{
		Il2CppChar L_11 = V_8;
		if ((((int32_t)L_11) == ((int32_t)((int32_t)47))))
		{
			goto IL_0386;
		}
	}
	{
		goto IL_03de;
	}

IL_0086:
	{
		Il2CppChar L_12 = V_8;
		if ((!(((uint32_t)L_12) <= ((uint32_t)((int32_t)93)))))
		{
			goto IL_00b0;
		}
	}
	{
		Il2CppChar L_13 = V_8;
		if ((((int32_t)L_13) == ((int32_t)((int32_t)58))))
		{
			goto IL_020d;
		}
	}
	{
		Il2CppChar L_14 = V_8;
		switch (((int32_t)il2cpp_codegen_subtract((int32_t)L_14, ((int32_t)91))))
		{
			case 0:
			{
				goto IL_0126;
			}
			case 1:
			{
				goto IL_02c3;
			}
			case 2:
			{
				goto IL_017c;
			}
		}
	}
	{
		goto IL_03de;
	}

IL_00b0:
	{
		Il2CppChar L_15 = V_8;
		if ((((int32_t)L_15) == ((int32_t)((int32_t)123))))
		{
			goto IL_00d0;
		}
	}
	{
		Il2CppChar L_16 = V_8;
		if ((((int32_t)L_16) == ((int32_t)((int32_t)125))))
		{
			goto IL_017c;
		}
	}
	{
		Il2CppChar L_17 = V_8;
		if ((((int32_t)L_17) == ((int32_t)((int32_t)65279))))
		{
			goto IL_03ec;
		}
	}
	{
		goto IL_03de;
	}

IL_00d0:
	{
		bool L_18 = V_5;
		if (!L_18)
		{
			goto IL_00e7;
		}
	}
	{
		StringBuilder_t* L_19 = V_3;
		String_t* L_20 = ___0_aJSON;
		int32_t L_21 = V_2;
		NullCheck(L_20);
		Il2CppChar L_22;
		L_22 = String_get_Chars_mC49DF0CD2D3BE7BE97B3AD9C995BE3094F8E36D3(L_20, L_21, NULL);
		NullCheck(L_19);
		StringBuilder_t* L_23;
		L_23 = StringBuilder_Append_m71228B30F05724CD2CD96D9611DCD61BFB96A6E1(L_19, L_22, NULL);
		goto IL_03ec;
	}

IL_00e7:
	{
		Stack_1_tBFA4A76FD7F09AC05C41D7A61CC4D933CAF033DC* L_24 = V_0;
		JSONObject_t8136ED73F77B01FF951AB5A2C9B81C4CD0B5C7B6* L_25 = (JSONObject_t8136ED73F77B01FF951AB5A2C9B81C4CD0B5C7B6*)il2cpp_codegen_object_new(JSONObject_t8136ED73F77B01FF951AB5A2C9B81C4CD0B5C7B6_il2cpp_TypeInfo_var);
		JSONObject__ctor_mD4F6D4B7B8D8D3F4FCF45B4BAC32AEB66588307A(L_25, NULL);
		NullCheck(L_24);
		Stack_1_Push_m6AC701F463F347526ED326A3D0EE06EF172F219E(L_24, L_25, Stack_1_Push_m6AC701F463F347526ED326A3D0EE06EF172F219E_RuntimeMethod_var);
		JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* L_26 = V_1;
		il2cpp_codegen_runtime_class_init_inline(JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA_il2cpp_TypeInfo_var);
		bool L_27;
		L_27 = JSONNode_op_Inequality_mF703DC644DDA438C8F56773D20194C2899DDE6F4(L_26, NULL, NULL);
		if (!L_27)
		{
			goto IL_0109;
		}
	}
	{
		JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* L_28 = V_1;
		String_t* L_29 = V_4;
		Stack_1_tBFA4A76FD7F09AC05C41D7A61CC4D933CAF033DC* L_30 = V_0;
		NullCheck(L_30);
		JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* L_31;
		L_31 = Stack_1_Peek_mAEF9B60F85F7E57C33817ED5A6546491F9A3505F(L_30, Stack_1_Peek_mAEF9B60F85F7E57C33817ED5A6546491F9A3505F_RuntimeMethod_var);
		NullCheck(L_28);
		VirtualActionInvoker2< String_t*, JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* >::Invoke(13, L_28, L_29, L_31);
	}

IL_0109:
	{
		V_4 = _stringLiteralDA39A3EE5E6B4B0D3255BFEF95601890AFD80709;
		StringBuilder_t* L_32 = V_3;
		NullCheck(L_32);
		StringBuilder_set_Length_mE2427BDAEF91C4E4A6C80F3BDF1F6E01DBCC2414(L_32, 0, NULL);
		Stack_1_tBFA4A76FD7F09AC05C41D7A61CC4D933CAF033DC* L_33 = V_0;
		NullCheck(L_33);
		JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* L_34;
		L_34 = Stack_1_Peek_mAEF9B60F85F7E57C33817ED5A6546491F9A3505F(L_33, Stack_1_Peek_mAEF9B60F85F7E57C33817ED5A6546491F9A3505F_RuntimeMethod_var);
		V_1 = L_34;
		V_7 = (bool)0;
		goto IL_03ec;
	}

IL_0126:
	{
		bool L_35 = V_5;
		if (!L_35)
		{
			goto IL_013d;
		}
	}
	{
		StringBuilder_t* L_36 = V_3;
		String_t* L_37 = ___0_aJSON;
		int32_t L_38 = V_2;
		NullCheck(L_37);
		Il2CppChar L_39;
		L_39 = String_get_Chars_mC49DF0CD2D3BE7BE97B3AD9C995BE3094F8E36D3(L_37, L_38, NULL);
		NullCheck(L_36);
		StringBuilder_t* L_40;
		L_40 = StringBuilder_Append_m71228B30F05724CD2CD96D9611DCD61BFB96A6E1(L_36, L_39, NULL);
		goto IL_03ec;
	}

IL_013d:
	{
		Stack_1_tBFA4A76FD7F09AC05C41D7A61CC4D933CAF033DC* L_41 = V_0;
		JSONArray_tE863C8377E61FEEC9ACA640D090C176C7CDCB589* L_42 = (JSONArray_tE863C8377E61FEEC9ACA640D090C176C7CDCB589*)il2cpp_codegen_object_new(JSONArray_tE863C8377E61FEEC9ACA640D090C176C7CDCB589_il2cpp_TypeInfo_var);
		JSONArray__ctor_mD53A24956E3512E4F0F8989651E9E8BA8B2257B8(L_42, NULL);
		NullCheck(L_41);
		Stack_1_Push_m6AC701F463F347526ED326A3D0EE06EF172F219E(L_41, L_42, Stack_1_Push_m6AC701F463F347526ED326A3D0EE06EF172F219E_RuntimeMethod_var);
		JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* L_43 = V_1;
		il2cpp_codegen_runtime_class_init_inline(JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA_il2cpp_TypeInfo_var);
		bool L_44;
		L_44 = JSONNode_op_Inequality_mF703DC644DDA438C8F56773D20194C2899DDE6F4(L_43, NULL, NULL);
		if (!L_44)
		{
			goto IL_015f;
		}
	}
	{
		JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* L_45 = V_1;
		String_t* L_46 = V_4;
		Stack_1_tBFA4A76FD7F09AC05C41D7A61CC4D933CAF033DC* L_47 = V_0;
		NullCheck(L_47);
		JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* L_48;
		L_48 = Stack_1_Peek_mAEF9B60F85F7E57C33817ED5A6546491F9A3505F(L_47, Stack_1_Peek_mAEF9B60F85F7E57C33817ED5A6546491F9A3505F_RuntimeMethod_var);
		NullCheck(L_45);
		VirtualActionInvoker2< String_t*, JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* >::Invoke(13, L_45, L_46, L_48);
	}

IL_015f:
	{
		V_4 = _stringLiteralDA39A3EE5E6B4B0D3255BFEF95601890AFD80709;
		StringBuilder_t* L_49 = V_3;
		NullCheck(L_49);
		StringBuilder_set_Length_mE2427BDAEF91C4E4A6C80F3BDF1F6E01DBCC2414(L_49, 0, NULL);
		Stack_1_tBFA4A76FD7F09AC05C41D7A61CC4D933CAF033DC* L_50 = V_0;
		NullCheck(L_50);
		JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* L_51;
		L_51 = Stack_1_Peek_mAEF9B60F85F7E57C33817ED5A6546491F9A3505F(L_50, Stack_1_Peek_mAEF9B60F85F7E57C33817ED5A6546491F9A3505F_RuntimeMethod_var);
		V_1 = L_51;
		V_7 = (bool)0;
		goto IL_03ec;
	}

IL_017c:
	{
		bool L_52 = V_5;
		if (!L_52)
		{
			goto IL_0193;
		}
	}
	{
		StringBuilder_t* L_53 = V_3;
		String_t* L_54 = ___0_aJSON;
		int32_t L_55 = V_2;
		NullCheck(L_54);
		Il2CppChar L_56;
		L_56 = String_get_Chars_mC49DF0CD2D3BE7BE97B3AD9C995BE3094F8E36D3(L_54, L_55, NULL);
		NullCheck(L_53);
		StringBuilder_t* L_57;
		L_57 = StringBuilder_Append_m71228B30F05724CD2CD96D9611DCD61BFB96A6E1(L_53, L_56, NULL);
		goto IL_03ec;
	}

IL_0193:
	{
		Stack_1_tBFA4A76FD7F09AC05C41D7A61CC4D933CAF033DC* L_58 = V_0;
		NullCheck(L_58);
		int32_t L_59;
		L_59 = Stack_1_get_Count_mE3AE26433E9D1F6B2307F77F0D0F67ACC885BA24_inline(L_58, Stack_1_get_Count_mE3AE26433E9D1F6B2307F77F0D0F67ACC885BA24_RuntimeMethod_var);
		if (L_59)
		{
			goto IL_01a6;
		}
	}
	{
		Exception_t* L_60 = (Exception_t*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&Exception_t_il2cpp_TypeInfo_var)));
		Exception__ctor_m9B2BD92CD68916245A75109105D9071C9D430E7F(L_60, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteral870C43A28360ADF668EFBACF63A4553351C7FA4C)), NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_60, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&JSONNode_Parse_mADBE0CF264E68AFDA1B37FA20CB5FDAFD89134DB_RuntimeMethod_var)));
	}

IL_01a6:
	{
		Stack_1_tBFA4A76FD7F09AC05C41D7A61CC4D933CAF033DC* L_61 = V_0;
		NullCheck(L_61);
		JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* L_62;
		L_62 = Stack_1_Pop_m95C9A9B157580A32C0DF3B738100E1408B8FF7E8(L_61, Stack_1_Pop_m95C9A9B157580A32C0DF3B738100E1408B8FF7E8_RuntimeMethod_var);
		StringBuilder_t* L_63 = V_3;
		NullCheck(L_63);
		int32_t L_64;
		L_64 = StringBuilder_get_Length_mDEA041E7357C68CC3B5885276BB403676DAAE0D8(L_63, NULL);
		bool L_65 = V_6;
		if (!((int32_t)(((((int32_t)L_64) > ((int32_t)0))? 1 : 0)|(int32_t)L_65)))
		{
			goto IL_01d0;
		}
	}
	{
		JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* L_66 = V_1;
		String_t* L_67 = V_4;
		StringBuilder_t* L_68 = V_3;
		NullCheck(L_68);
		String_t* L_69;
		L_69 = VirtualFuncInvoker0< String_t* >::Invoke(3, L_68);
		bool L_70 = V_6;
		il2cpp_codegen_runtime_class_init_inline(JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA_il2cpp_TypeInfo_var);
		JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* L_71;
		L_71 = JSONNode_ParseElement_m5BA504C2E93CF73EA23A0AB555BD759F26481B5A(L_69, L_70, NULL);
		NullCheck(L_66);
		VirtualActionInvoker2< String_t*, JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* >::Invoke(13, L_66, L_67, L_71);
	}

IL_01d0:
	{
		JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* L_72 = V_1;
		il2cpp_codegen_runtime_class_init_inline(JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA_il2cpp_TypeInfo_var);
		bool L_73;
		L_73 = JSONNode_op_Inequality_mF703DC644DDA438C8F56773D20194C2899DDE6F4(L_72, NULL, NULL);
		if (!L_73)
		{
			goto IL_01e4;
		}
	}
	{
		JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* L_74 = V_1;
		bool L_75 = V_7;
		NullCheck(L_74);
		VirtualActionInvoker1< bool >::Invoke(12, L_74, (bool)((((int32_t)L_75) == ((int32_t)0))? 1 : 0));
	}

IL_01e4:
	{
		V_6 = (bool)0;
		V_4 = _stringLiteralDA39A3EE5E6B4B0D3255BFEF95601890AFD80709;
		StringBuilder_t* L_76 = V_3;
		NullCheck(L_76);
		StringBuilder_set_Length_mE2427BDAEF91C4E4A6C80F3BDF1F6E01DBCC2414(L_76, 0, NULL);
		Stack_1_tBFA4A76FD7F09AC05C41D7A61CC4D933CAF033DC* L_77 = V_0;
		NullCheck(L_77);
		int32_t L_78;
		L_78 = Stack_1_get_Count_mE3AE26433E9D1F6B2307F77F0D0F67ACC885BA24_inline(L_77, Stack_1_get_Count_mE3AE26433E9D1F6B2307F77F0D0F67ACC885BA24_RuntimeMethod_var);
		if ((((int32_t)L_78) <= ((int32_t)0)))
		{
			goto IL_03ec;
		}
	}
	{
		Stack_1_tBFA4A76FD7F09AC05C41D7A61CC4D933CAF033DC* L_79 = V_0;
		NullCheck(L_79);
		JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* L_80;
		L_80 = Stack_1_Peek_mAEF9B60F85F7E57C33817ED5A6546491F9A3505F(L_79, Stack_1_Peek_mAEF9B60F85F7E57C33817ED5A6546491F9A3505F_RuntimeMethod_var);
		V_1 = L_80;
		goto IL_03ec;
	}

IL_020d:
	{
		bool L_81 = V_5;
		if (!L_81)
		{
			goto IL_0224;
		}
	}
	{
		StringBuilder_t* L_82 = V_3;
		String_t* L_83 = ___0_aJSON;
		int32_t L_84 = V_2;
		NullCheck(L_83);
		Il2CppChar L_85;
		L_85 = String_get_Chars_mC49DF0CD2D3BE7BE97B3AD9C995BE3094F8E36D3(L_83, L_84, NULL);
		NullCheck(L_82);
		StringBuilder_t* L_86;
		L_86 = StringBuilder_Append_m71228B30F05724CD2CD96D9611DCD61BFB96A6E1(L_82, L_85, NULL);
		goto IL_03ec;
	}

IL_0224:
	{
		StringBuilder_t* L_87 = V_3;
		NullCheck(L_87);
		String_t* L_88;
		L_88 = VirtualFuncInvoker0< String_t* >::Invoke(3, L_87);
		V_4 = L_88;
		StringBuilder_t* L_89 = V_3;
		NullCheck(L_89);
		StringBuilder_set_Length_mE2427BDAEF91C4E4A6C80F3BDF1F6E01DBCC2414(L_89, 0, NULL);
		V_6 = (bool)0;
		goto IL_03ec;
	}

IL_023b:
	{
		bool L_90 = V_5;
		V_5 = (bool)((((int32_t)L_90) == ((int32_t)0))? 1 : 0);
		bool L_91 = V_6;
		bool L_92 = V_5;
		V_6 = (bool)((int32_t)((int32_t)L_91|(int32_t)L_92));
		goto IL_03ec;
	}

IL_024e:
	{
		bool L_93 = V_5;
		if (!L_93)
		{
			goto IL_0265;
		}
	}
	{
		StringBuilder_t* L_94 = V_3;
		String_t* L_95 = ___0_aJSON;
		int32_t L_96 = V_2;
		NullCheck(L_95);
		Il2CppChar L_97;
		L_97 = String_get_Chars_mC49DF0CD2D3BE7BE97B3AD9C995BE3094F8E36D3(L_95, L_96, NULL);
		NullCheck(L_94);
		StringBuilder_t* L_98;
		L_98 = StringBuilder_Append_m71228B30F05724CD2CD96D9611DCD61BFB96A6E1(L_94, L_97, NULL);
		goto IL_03ec;
	}

IL_0265:
	{
		StringBuilder_t* L_99 = V_3;
		NullCheck(L_99);
		int32_t L_100;
		L_100 = StringBuilder_get_Length_mDEA041E7357C68CC3B5885276BB403676DAAE0D8(L_99, NULL);
		bool L_101 = V_6;
		if (!((int32_t)(((((int32_t)L_100) > ((int32_t)0))? 1 : 0)|(int32_t)L_101)))
		{
			goto IL_0288;
		}
	}
	{
		JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* L_102 = V_1;
		String_t* L_103 = V_4;
		StringBuilder_t* L_104 = V_3;
		NullCheck(L_104);
		String_t* L_105;
		L_105 = VirtualFuncInvoker0< String_t* >::Invoke(3, L_104);
		bool L_106 = V_6;
		il2cpp_codegen_runtime_class_init_inline(JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA_il2cpp_TypeInfo_var);
		JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* L_107;
		L_107 = JSONNode_ParseElement_m5BA504C2E93CF73EA23A0AB555BD759F26481B5A(L_105, L_106, NULL);
		NullCheck(L_102);
		VirtualActionInvoker2< String_t*, JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* >::Invoke(13, L_102, L_103, L_107);
	}

IL_0288:
	{
		V_6 = (bool)0;
		V_4 = _stringLiteralDA39A3EE5E6B4B0D3255BFEF95601890AFD80709;
		StringBuilder_t* L_108 = V_3;
		NullCheck(L_108);
		StringBuilder_set_Length_mE2427BDAEF91C4E4A6C80F3BDF1F6E01DBCC2414(L_108, 0, NULL);
		V_6 = (bool)0;
		goto IL_03ec;
	}

IL_02a1:
	{
		V_7 = (bool)1;
		goto IL_03ec;
	}

IL_02a9:
	{
		bool L_109 = V_5;
		if (!L_109)
		{
			goto IL_03ec;
		}
	}
	{
		StringBuilder_t* L_110 = V_3;
		String_t* L_111 = ___0_aJSON;
		int32_t L_112 = V_2;
		NullCheck(L_111);
		Il2CppChar L_113;
		L_113 = String_get_Chars_mC49DF0CD2D3BE7BE97B3AD9C995BE3094F8E36D3(L_111, L_112, NULL);
		NullCheck(L_110);
		StringBuilder_t* L_114;
		L_114 = StringBuilder_Append_m71228B30F05724CD2CD96D9611DCD61BFB96A6E1(L_110, L_113, NULL);
		goto IL_03ec;
	}

IL_02c3:
	{
		int32_t L_115 = V_2;
		V_2 = ((int32_t)il2cpp_codegen_add(L_115, 1));
		bool L_116 = V_5;
		if (!L_116)
		{
			goto IL_03ec;
		}
	}
	{
		String_t* L_117 = ___0_aJSON;
		int32_t L_118 = V_2;
		NullCheck(L_117);
		Il2CppChar L_119;
		L_119 = String_get_Chars_mC49DF0CD2D3BE7BE97B3AD9C995BE3094F8E36D3(L_117, L_118, NULL);
		V_9 = L_119;
		Il2CppChar L_120 = V_9;
		if ((!(((uint32_t)L_120) <= ((uint32_t)((int32_t)102)))))
		{
			goto IL_02ee;
		}
	}
	{
		Il2CppChar L_121 = V_9;
		if ((((int32_t)L_121) == ((int32_t)((int32_t)98))))
		{
			goto IL_033a;
		}
	}
	{
		Il2CppChar L_122 = V_9;
		if ((((int32_t)L_122) == ((int32_t)((int32_t)102))))
		{
			goto IL_0347;
		}
	}
	{
		goto IL_037b;
	}

IL_02ee:
	{
		Il2CppChar L_123 = V_9;
		if ((((int32_t)L_123) == ((int32_t)((int32_t)110))))
		{
			goto IL_032c;
		}
	}
	{
		Il2CppChar L_124 = V_9;
		switch (((int32_t)il2cpp_codegen_subtract((int32_t)L_124, ((int32_t)114))))
		{
			case 0:
			{
				goto IL_031e;
			}
			case 1:
			{
				goto IL_037b;
			}
			case 2:
			{
				goto IL_0310;
			}
			case 3:
			{
				goto IL_0355;
			}
		}
	}
	{
		goto IL_037b;
	}

IL_0310:
	{
		StringBuilder_t* L_125 = V_3;
		NullCheck(L_125);
		StringBuilder_t* L_126;
		L_126 = StringBuilder_Append_m71228B30F05724CD2CD96D9611DCD61BFB96A6E1(L_125, ((int32_t)9), NULL);
		goto IL_03ec;
	}

IL_031e:
	{
		StringBuilder_t* L_127 = V_3;
		NullCheck(L_127);
		StringBuilder_t* L_128;
		L_128 = StringBuilder_Append_m71228B30F05724CD2CD96D9611DCD61BFB96A6E1(L_127, ((int32_t)13), NULL);
		goto IL_03ec;
	}

IL_032c:
	{
		StringBuilder_t* L_129 = V_3;
		NullCheck(L_129);
		StringBuilder_t* L_130;
		L_130 = StringBuilder_Append_m71228B30F05724CD2CD96D9611DCD61BFB96A6E1(L_129, ((int32_t)10), NULL);
		goto IL_03ec;
	}

IL_033a:
	{
		StringBuilder_t* L_131 = V_3;
		NullCheck(L_131);
		StringBuilder_t* L_132;
		L_132 = StringBuilder_Append_m71228B30F05724CD2CD96D9611DCD61BFB96A6E1(L_131, 8, NULL);
		goto IL_03ec;
	}

IL_0347:
	{
		StringBuilder_t* L_133 = V_3;
		NullCheck(L_133);
		StringBuilder_t* L_134;
		L_134 = StringBuilder_Append_m71228B30F05724CD2CD96D9611DCD61BFB96A6E1(L_133, ((int32_t)12), NULL);
		goto IL_03ec;
	}

IL_0355:
	{
		String_t* L_135 = ___0_aJSON;
		int32_t L_136 = V_2;
		NullCheck(L_135);
		String_t* L_137;
		L_137 = String_Substring_mB1D94F47935D22E130FF2C01DBB6A4135FBB76CE(L_135, ((int32_t)il2cpp_codegen_add(L_136, 1)), 4, NULL);
		V_10 = L_137;
		StringBuilder_t* L_138 = V_3;
		String_t* L_139 = V_10;
		int32_t L_140;
		L_140 = Int32_Parse_mF336325913DF125A6F8F05F2909E3AFB0D73830E(L_139, ((int32_t)512), NULL);
		NullCheck(L_138);
		StringBuilder_t* L_141;
		L_141 = StringBuilder_Append_m71228B30F05724CD2CD96D9611DCD61BFB96A6E1(L_138, ((int32_t)(uint16_t)L_140), NULL);
		int32_t L_142 = V_2;
		V_2 = ((int32_t)il2cpp_codegen_add(L_142, 4));
		goto IL_03ec;
	}

IL_037b:
	{
		StringBuilder_t* L_143 = V_3;
		Il2CppChar L_144 = V_9;
		NullCheck(L_143);
		StringBuilder_t* L_145;
		L_145 = StringBuilder_Append_m71228B30F05724CD2CD96D9611DCD61BFB96A6E1(L_143, L_144, NULL);
		goto IL_03ec;
	}

IL_0386:
	{
		il2cpp_codegen_runtime_class_init_inline(JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA_il2cpp_TypeInfo_var);
		bool L_146 = ((JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA_StaticFields*)il2cpp_codegen_static_fields_for(JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA_il2cpp_TypeInfo_var))->___allowLineComments;
		if (!L_146)
		{
			goto IL_03ce;
		}
	}
	{
		bool L_147 = V_5;
		if (L_147)
		{
			goto IL_03ce;
		}
	}
	{
		int32_t L_148 = V_2;
		String_t* L_149 = ___0_aJSON;
		NullCheck(L_149);
		int32_t L_150;
		L_150 = String_get_Length_m42625D67623FA5CC7A44D47425CE86FB946542D2_inline(L_149, NULL);
		if ((((int32_t)((int32_t)il2cpp_codegen_add(L_148, 1))) >= ((int32_t)L_150)))
		{
			goto IL_03ce;
		}
	}
	{
		String_t* L_151 = ___0_aJSON;
		int32_t L_152 = V_2;
		NullCheck(L_151);
		Il2CppChar L_153;
		L_153 = String_get_Chars_mC49DF0CD2D3BE7BE97B3AD9C995BE3094F8E36D3(L_151, ((int32_t)il2cpp_codegen_add(L_152, 1)), NULL);
		if ((!(((uint32_t)L_153) == ((uint32_t)((int32_t)47)))))
		{
			goto IL_03ce;
		}
	}

IL_03a9:
	{
		int32_t L_154 = V_2;
		int32_t L_155 = ((int32_t)il2cpp_codegen_add(L_154, 1));
		V_2 = L_155;
		String_t* L_156 = ___0_aJSON;
		NullCheck(L_156);
		int32_t L_157;
		L_157 = String_get_Length_m42625D67623FA5CC7A44D47425CE86FB946542D2_inline(L_156, NULL);
		if ((((int32_t)L_155) >= ((int32_t)L_157)))
		{
			goto IL_03ec;
		}
	}
	{
		String_t* L_158 = ___0_aJSON;
		int32_t L_159 = V_2;
		NullCheck(L_158);
		Il2CppChar L_160;
		L_160 = String_get_Chars_mC49DF0CD2D3BE7BE97B3AD9C995BE3094F8E36D3(L_158, L_159, NULL);
		if ((((int32_t)L_160) == ((int32_t)((int32_t)10))))
		{
			goto IL_03ec;
		}
	}
	{
		String_t* L_161 = ___0_aJSON;
		int32_t L_162 = V_2;
		NullCheck(L_161);
		Il2CppChar L_163;
		L_163 = String_get_Chars_mC49DF0CD2D3BE7BE97B3AD9C995BE3094F8E36D3(L_161, L_162, NULL);
		if ((!(((uint32_t)L_163) == ((uint32_t)((int32_t)13)))))
		{
			goto IL_03a9;
		}
	}
	{
		goto IL_03ec;
	}

IL_03ce:
	{
		StringBuilder_t* L_164 = V_3;
		String_t* L_165 = ___0_aJSON;
		int32_t L_166 = V_2;
		NullCheck(L_165);
		Il2CppChar L_167;
		L_167 = String_get_Chars_mC49DF0CD2D3BE7BE97B3AD9C995BE3094F8E36D3(L_165, L_166, NULL);
		NullCheck(L_164);
		StringBuilder_t* L_168;
		L_168 = StringBuilder_Append_m71228B30F05724CD2CD96D9611DCD61BFB96A6E1(L_164, L_167, NULL);
		goto IL_03ec;
	}

IL_03de:
	{
		StringBuilder_t* L_169 = V_3;
		String_t* L_170 = ___0_aJSON;
		int32_t L_171 = V_2;
		NullCheck(L_170);
		Il2CppChar L_172;
		L_172 = String_get_Chars_mC49DF0CD2D3BE7BE97B3AD9C995BE3094F8E36D3(L_170, L_171, NULL);
		NullCheck(L_169);
		StringBuilder_t* L_173;
		L_173 = StringBuilder_Append_m71228B30F05724CD2CD96D9611DCD61BFB96A6E1(L_169, L_172, NULL);
	}

IL_03ec:
	{
		int32_t L_174 = V_2;
		V_2 = ((int32_t)il2cpp_codegen_add(L_174, 1));
	}

IL_03f0:
	{
		int32_t L_175 = V_2;
		String_t* L_176 = ___0_aJSON;
		NullCheck(L_176);
		int32_t L_177;
		L_177 = String_get_Length_m42625D67623FA5CC7A44D47425CE86FB946542D2_inline(L_176, NULL);
		if ((((int32_t)L_175) < ((int32_t)L_177)))
		{
			goto IL_0025;
		}
	}
	{
		bool L_178 = V_5;
		if (!L_178)
		{
			goto IL_040b;
		}
	}
	{
		Exception_t* L_179 = (Exception_t*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&Exception_t_il2cpp_TypeInfo_var)));
		Exception__ctor_m9B2BD92CD68916245A75109105D9071C9D430E7F(L_179, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteralBF00FC1AEA59DE3445148D940526441AD4E1FFA7)), NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_179, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&JSONNode_Parse_mADBE0CF264E68AFDA1B37FA20CB5FDAFD89134DB_RuntimeMethod_var)));
	}

IL_040b:
	{
		JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* L_180 = V_1;
		il2cpp_codegen_runtime_class_init_inline(JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA_il2cpp_TypeInfo_var);
		bool L_181;
		L_181 = JSONNode_op_Equality_mDD36B312D77EC1FC9939276C087EBA8E7B8AD92F(L_180, NULL, NULL);
		if (!L_181)
		{
			goto IL_0422;
		}
	}
	{
		StringBuilder_t* L_182 = V_3;
		NullCheck(L_182);
		String_t* L_183;
		L_183 = VirtualFuncInvoker0< String_t* >::Invoke(3, L_182);
		bool L_184 = V_6;
		il2cpp_codegen_runtime_class_init_inline(JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA_il2cpp_TypeInfo_var);
		JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* L_185;
		L_185 = JSONNode_ParseElement_m5BA504C2E93CF73EA23A0AB555BD759F26481B5A(L_183, L_184, NULL);
		return L_185;
	}

IL_0422:
	{
		JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* L_186 = V_1;
		return L_186;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void JSONNode__ctor_mB710A69AF2EDA72D624E6291A6B8438316D5E8DA (JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void JSONNode__cctor_m5590ECAA39E02983EFECFC2457C77E933A9454CA (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		((JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA_StaticFields*)il2cpp_codegen_static_fields_for(JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA_il2cpp_TypeInfo_var))->___forceASCII = (bool)0;
		((JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA_StaticFields*)il2cpp_codegen_static_fields_for(JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA_il2cpp_TypeInfo_var))->___longAsString = (bool)0;
		((JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA_StaticFields*)il2cpp_codegen_static_fields_for(JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA_il2cpp_TypeInfo_var))->___allowLineComments = (bool)1;
		((JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA_StaticFields*)il2cpp_codegen_static_fields_for(JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA_il2cpp_TypeInfo_var))->___Color32DefaultAlpha = (uint8_t)((int32_t)255);
		((JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA_StaticFields*)il2cpp_codegen_static_fields_for(JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA_il2cpp_TypeInfo_var))->___ColorDefaultAlpha = (1.0f);
		((JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA_StaticFields*)il2cpp_codegen_static_fields_for(JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA_il2cpp_TypeInfo_var))->___VectorContainerType = 0;
		((JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA_StaticFields*)il2cpp_codegen_static_fields_for(JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA_il2cpp_TypeInfo_var))->___QuaternionContainerType = 0;
		((JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA_StaticFields*)il2cpp_codegen_static_fields_for(JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA_il2cpp_TypeInfo_var))->___RectContainerType = 0;
		((JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA_StaticFields*)il2cpp_codegen_static_fields_for(JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA_il2cpp_TypeInfo_var))->___ColorContainerType = 0;
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3Cget_ChildrenU3Ed__43__ctor_mE7537C1ADA2199B30E3DD1E586C06868E952582D (U3Cget_ChildrenU3Ed__43_tBA5BD787315C9FDAC35DA9789C45EAD75D696F2D* __this, int32_t ___0_U3CU3E1__state, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		int32_t L_0 = ___0_U3CU3E1__state;
		__this->___U3CU3E1__state = L_0;
		int32_t L_1;
		L_1 = Environment_get_CurrentManagedThreadId_m66483AADCCC13272EBDCD94D31D2E52603C24BDF(NULL);
		__this->___U3CU3El__initialThreadId = L_1;
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3Cget_ChildrenU3Ed__43_System_IDisposable_Dispose_mE0750C6999A3B99BA5EA25CBA8EE78FC5D2E32A7 (U3Cget_ChildrenU3Ed__43_tBA5BD787315C9FDAC35DA9789C45EAD75D696F2D* __this, const RuntimeMethod* method) 
{
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool U3Cget_ChildrenU3Ed__43_MoveNext_m0FA30CAB66F936066E46B3E37E076F324CCAE18D (U3Cget_ChildrenU3Ed__43_tBA5BD787315C9FDAC35DA9789C45EAD75D696F2D* __this, const RuntimeMethod* method) 
{
	int32_t V_0 = 0;
	{
		int32_t L_0 = __this->___U3CU3E1__state;
		V_0 = L_0;
		int32_t L_1 = V_0;
		if (!L_1)
		{
			goto IL_000c;
		}
	}
	{
		return (bool)0;
	}

IL_000c:
	{
		__this->___U3CU3E1__state = (-1);
		return (bool)0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* U3Cget_ChildrenU3Ed__43_System_Collections_Generic_IEnumeratorU3CSimpleJSON_JSONNodeU3E_get_Current_m3348853117A1CB8C4299F7D874A05D5389D2E8A4 (U3Cget_ChildrenU3Ed__43_tBA5BD787315C9FDAC35DA9789C45EAD75D696F2D* __this, const RuntimeMethod* method) 
{
	{
		JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* L_0 = __this->___U3CU3E2__current;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3Cget_ChildrenU3Ed__43_System_Collections_IEnumerator_Reset_mA525437DD47F7DFF24EC4B929622D3A7AEED6DA0 (U3Cget_ChildrenU3Ed__43_tBA5BD787315C9FDAC35DA9789C45EAD75D696F2D* __this, const RuntimeMethod* method) 
{
	{
		NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A* L_0 = (NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A_il2cpp_TypeInfo_var)));
		NotSupportedException__ctor_m1398D0CDE19B36AA3DE9392879738C1EA2439CDF(L_0, NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_0, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&U3Cget_ChildrenU3Ed__43_System_Collections_IEnumerator_Reset_mA525437DD47F7DFF24EC4B929622D3A7AEED6DA0_RuntimeMethod_var)));
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* U3Cget_ChildrenU3Ed__43_System_Collections_IEnumerator_get_Current_m438EAF263933605323729A301FE050F69ECC75F0 (U3Cget_ChildrenU3Ed__43_tBA5BD787315C9FDAC35DA9789C45EAD75D696F2D* __this, const RuntimeMethod* method) 
{
	{
		JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* L_0 = __this->___U3CU3E2__current;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* U3Cget_ChildrenU3Ed__43_System_Collections_Generic_IEnumerableU3CSimpleJSON_JSONNodeU3E_GetEnumerator_mE865D7DB0E34EA57362F88DA761C43889CB795ED (U3Cget_ChildrenU3Ed__43_tBA5BD787315C9FDAC35DA9789C45EAD75D696F2D* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3Cget_ChildrenU3Ed__43_tBA5BD787315C9FDAC35DA9789C45EAD75D696F2D_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	U3Cget_ChildrenU3Ed__43_tBA5BD787315C9FDAC35DA9789C45EAD75D696F2D* V_0 = NULL;
	{
		int32_t L_0 = __this->___U3CU3E1__state;
		if ((!(((uint32_t)L_0) == ((uint32_t)((int32_t)-2)))))
		{
			goto IL_0022;
		}
	}
	{
		int32_t L_1 = __this->___U3CU3El__initialThreadId;
		int32_t L_2;
		L_2 = Environment_get_CurrentManagedThreadId_m66483AADCCC13272EBDCD94D31D2E52603C24BDF(NULL);
		if ((!(((uint32_t)L_1) == ((uint32_t)L_2))))
		{
			goto IL_0022;
		}
	}
	{
		__this->___U3CU3E1__state = 0;
		V_0 = __this;
		goto IL_0029;
	}

IL_0022:
	{
		U3Cget_ChildrenU3Ed__43_tBA5BD787315C9FDAC35DA9789C45EAD75D696F2D* L_3 = (U3Cget_ChildrenU3Ed__43_tBA5BD787315C9FDAC35DA9789C45EAD75D696F2D*)il2cpp_codegen_object_new(U3Cget_ChildrenU3Ed__43_tBA5BD787315C9FDAC35DA9789C45EAD75D696F2D_il2cpp_TypeInfo_var);
		U3Cget_ChildrenU3Ed__43__ctor_mE7537C1ADA2199B30E3DD1E586C06868E952582D(L_3, 0, NULL);
		V_0 = L_3;
	}

IL_0029:
	{
		U3Cget_ChildrenU3Ed__43_tBA5BD787315C9FDAC35DA9789C45EAD75D696F2D* L_4 = V_0;
		return L_4;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* U3Cget_ChildrenU3Ed__43_System_Collections_IEnumerable_GetEnumerator_mC817252D040A030FAE1ECA3813AF6EE051DEB131 (U3Cget_ChildrenU3Ed__43_tBA5BD787315C9FDAC35DA9789C45EAD75D696F2D* __this, const RuntimeMethod* method) 
{
	{
		RuntimeObject* L_0;
		L_0 = U3Cget_ChildrenU3Ed__43_System_Collections_Generic_IEnumerableU3CSimpleJSON_JSONNodeU3E_GetEnumerator_mE865D7DB0E34EA57362F88DA761C43889CB795ED(__this, NULL);
		return L_0;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void JSONArray_set_Inline_m90446F9F14C011A0544797B84BAB132E52C9DA0D (JSONArray_tE863C8377E61FEEC9ACA640D090C176C7CDCB589* __this, bool ___0_value, const RuntimeMethod* method) 
{
	{
		bool L_0 = ___0_value;
		__this->___inline = L_0;
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool JSONArray_get_IsArray_mF146237B10879A0A57F792D1465137A82578B9AC (JSONArray_tE863C8377E61FEEC9ACA640D090C176C7CDCB589* __this, const RuntimeMethod* method) 
{
	{
		return (bool)1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* JSONArray_get_Item_mD02219BCD5FDEE90D2CC56ECF84622FA314D189B (JSONArray_tE863C8377E61FEEC9ACA640D090C176C7CDCB589* __this, String_t* ___0_aKey, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&JSONLazyCreator_t93B398608BA0717C413F6875922A3BF9C7ABD8B9_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		JSONLazyCreator_t93B398608BA0717C413F6875922A3BF9C7ABD8B9* L_0 = (JSONLazyCreator_t93B398608BA0717C413F6875922A3BF9C7ABD8B9*)il2cpp_codegen_object_new(JSONLazyCreator_t93B398608BA0717C413F6875922A3BF9C7ABD8B9_il2cpp_TypeInfo_var);
		JSONLazyCreator__ctor_m5A3DAB47229CD45650E935BDB7D0C9FACA460720(L_0, __this, NULL);
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t JSONArray_get_Count_m3A177847B02DD3F048AA9E997B179F9836042C1C (JSONArray_tE863C8377E61FEEC9ACA640D090C176C7CDCB589* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_get_Count_mD73536BDBC542E80A39D3303987D954F5B4E1673_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		List_1_t2B71C7C40AB24AD3BA7EFF058B78E7FD76195295* L_0 = __this->___m_List;
		NullCheck(L_0);
		int32_t L_1;
		L_1 = List_1_get_Count_mD73536BDBC542E80A39D3303987D954F5B4E1673_inline(L_0, List_1_get_Count_mD73536BDBC542E80A39D3303987D954F5B4E1673_RuntimeMethod_var);
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void JSONArray_Add_m2B76C3E7B373A9F338C2E0884089E0AC9A536ABA (JSONArray_tE863C8377E61FEEC9ACA640D090C176C7CDCB589* __this, String_t* ___0_aKey, JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* ___1_aItem, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&JSONNull_tE058D16E7011140DB8F406485C84D3D51DA7A97D_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_Add_m149138AF1B43A8019AD94D106EB9006FE8996B98_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* L_0 = ___1_aItem;
		il2cpp_codegen_runtime_class_init_inline(JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA_il2cpp_TypeInfo_var);
		bool L_1;
		L_1 = JSONNode_op_Equality_mDD36B312D77EC1FC9939276C087EBA8E7B8AD92F(L_0, NULL, NULL);
		if (!L_1)
		{
			goto IL_0010;
		}
	}
	{
		il2cpp_codegen_runtime_class_init_inline(JSONNull_tE058D16E7011140DB8F406485C84D3D51DA7A97D_il2cpp_TypeInfo_var);
		JSONNull_tE058D16E7011140DB8F406485C84D3D51DA7A97D* L_2;
		L_2 = JSONNull_CreateOrGet_m920C3D38D052C4C8EDE996476F91D9913B6017F2(NULL);
		___1_aItem = L_2;
	}

IL_0010:
	{
		List_1_t2B71C7C40AB24AD3BA7EFF058B78E7FD76195295* L_3 = __this->___m_List;
		JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* L_4 = ___1_aItem;
		NullCheck(L_3);
		List_1_Add_m149138AF1B43A8019AD94D106EB9006FE8996B98_inline(L_3, L_4, List_1_Add_m149138AF1B43A8019AD94D106EB9006FE8996B98_RuntimeMethod_var);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* JSONArray_get_Children_m147FCAA9833545DE298C6C6412D5AFF7005364E5 (JSONArray_tE863C8377E61FEEC9ACA640D090C176C7CDCB589* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3Cget_ChildrenU3Ed__24_tAAE17304DD542179299B598CFEA2DF919C468400_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		U3Cget_ChildrenU3Ed__24_tAAE17304DD542179299B598CFEA2DF919C468400* L_0 = (U3Cget_ChildrenU3Ed__24_tAAE17304DD542179299B598CFEA2DF919C468400*)il2cpp_codegen_object_new(U3Cget_ChildrenU3Ed__24_tAAE17304DD542179299B598CFEA2DF919C468400_il2cpp_TypeInfo_var);
		U3Cget_ChildrenU3Ed__24__ctor_m95EEF28AA5E9412DACD322799156F76F44D26F51(L_0, ((int32_t)-2), NULL);
		U3Cget_ChildrenU3Ed__24_tAAE17304DD542179299B598CFEA2DF919C468400* L_1 = L_0;
		NullCheck(L_1);
		L_1->___U3CU3E4__this = __this;
		Il2CppCodeGenWriteBarrier((void**)(&L_1->___U3CU3E4__this), (void*)__this);
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void JSONArray_WriteToStringBuilder_mABF3E6448FD36889F23F085C9FE44216B0F1D9F4 (JSONArray_tE863C8377E61FEEC9ACA640D090C176C7CDCB589* __this, StringBuilder_t* ___0_aSB, int32_t ___1_aIndent, int32_t ___2_aIndentInc, int32_t ___3_aMode, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_get_Count_mD73536BDBC542E80A39D3303987D954F5B4E1673_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_get_Item_m77B77650E01A165CE9FD65AB34DDCD18725B258F_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	int32_t V_0 = 0;
	int32_t V_1 = 0;
	{
		StringBuilder_t* L_0 = ___0_aSB;
		NullCheck(L_0);
		StringBuilder_t* L_1;
		L_1 = StringBuilder_Append_m71228B30F05724CD2CD96D9611DCD61BFB96A6E1(L_0, ((int32_t)91), NULL);
		List_1_t2B71C7C40AB24AD3BA7EFF058B78E7FD76195295* L_2 = __this->___m_List;
		NullCheck(L_2);
		int32_t L_3;
		L_3 = List_1_get_Count_mD73536BDBC542E80A39D3303987D954F5B4E1673_inline(L_2, List_1_get_Count_mD73536BDBC542E80A39D3303987D954F5B4E1673_RuntimeMethod_var);
		V_0 = L_3;
		bool L_4 = __this->___inline;
		if (!L_4)
		{
			goto IL_0020;
		}
	}
	{
		___3_aMode = 0;
	}

IL_0020:
	{
		V_1 = 0;
		goto IL_006a;
	}

IL_0024:
	{
		int32_t L_5 = V_1;
		if ((((int32_t)L_5) <= ((int32_t)0)))
		{
			goto IL_0031;
		}
	}
	{
		StringBuilder_t* L_6 = ___0_aSB;
		NullCheck(L_6);
		StringBuilder_t* L_7;
		L_7 = StringBuilder_Append_m71228B30F05724CD2CD96D9611DCD61BFB96A6E1(L_6, ((int32_t)44), NULL);
	}

IL_0031:
	{
		int32_t L_8 = ___3_aMode;
		if ((!(((uint32_t)L_8) == ((uint32_t)1))))
		{
			goto IL_003d;
		}
	}
	{
		StringBuilder_t* L_9 = ___0_aSB;
		NullCheck(L_9);
		StringBuilder_t* L_10;
		L_10 = StringBuilder_AppendLine_m3BC704C4E6A8531027D8C9287D0AB2AA0188AC4E(L_9, NULL);
	}

IL_003d:
	{
		int32_t L_11 = ___3_aMode;
		if ((!(((uint32_t)L_11) == ((uint32_t)1))))
		{
			goto IL_004e;
		}
	}
	{
		StringBuilder_t* L_12 = ___0_aSB;
		int32_t L_13 = ___1_aIndent;
		int32_t L_14 = ___2_aIndentInc;
		NullCheck(L_12);
		StringBuilder_t* L_15;
		L_15 = StringBuilder_Append_mE20F6CD28FC8E8C9FD65987DBD32E6087CCE1CF3(L_12, ((int32_t)32), ((int32_t)il2cpp_codegen_add(L_13, L_14)), NULL);
	}

IL_004e:
	{
		List_1_t2B71C7C40AB24AD3BA7EFF058B78E7FD76195295* L_16 = __this->___m_List;
		int32_t L_17 = V_1;
		NullCheck(L_16);
		JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* L_18;
		L_18 = List_1_get_Item_m77B77650E01A165CE9FD65AB34DDCD18725B258F(L_16, L_17, List_1_get_Item_m77B77650E01A165CE9FD65AB34DDCD18725B258F_RuntimeMethod_var);
		StringBuilder_t* L_19 = ___0_aSB;
		int32_t L_20 = ___1_aIndent;
		int32_t L_21 = ___2_aIndentInc;
		int32_t L_22 = ___2_aIndentInc;
		int32_t L_23 = ___3_aMode;
		NullCheck(L_18);
		VirtualActionInvoker4< StringBuilder_t*, int32_t, int32_t, int32_t >::Invoke(16, L_18, L_19, ((int32_t)il2cpp_codegen_add(L_20, L_21)), L_22, L_23);
		int32_t L_24 = V_1;
		V_1 = ((int32_t)il2cpp_codegen_add(L_24, 1));
	}

IL_006a:
	{
		int32_t L_25 = V_1;
		int32_t L_26 = V_0;
		if ((((int32_t)L_25) < ((int32_t)L_26)))
		{
			goto IL_0024;
		}
	}
	{
		int32_t L_27 = ___3_aMode;
		if ((!(((uint32_t)L_27) == ((uint32_t)1))))
		{
			goto IL_0082;
		}
	}
	{
		StringBuilder_t* L_28 = ___0_aSB;
		NullCheck(L_28);
		StringBuilder_t* L_29;
		L_29 = StringBuilder_AppendLine_m3BC704C4E6A8531027D8C9287D0AB2AA0188AC4E(L_28, NULL);
		int32_t L_30 = ___1_aIndent;
		NullCheck(L_29);
		StringBuilder_t* L_31;
		L_31 = StringBuilder_Append_mE20F6CD28FC8E8C9FD65987DBD32E6087CCE1CF3(L_29, ((int32_t)32), L_30, NULL);
	}

IL_0082:
	{
		StringBuilder_t* L_32 = ___0_aSB;
		NullCheck(L_32);
		StringBuilder_t* L_33;
		L_33 = StringBuilder_Append_m71228B30F05724CD2CD96D9611DCD61BFB96A6E1(L_32, ((int32_t)93), NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void JSONArray__ctor_mD53A24956E3512E4F0F8989651E9E8BA8B2257B8 (JSONArray_tE863C8377E61FEEC9ACA640D090C176C7CDCB589* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1__ctor_m01E98620490C05F4956E30BE71BB07091CD21F78_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_t2B71C7C40AB24AD3BA7EFF058B78E7FD76195295_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		List_1_t2B71C7C40AB24AD3BA7EFF058B78E7FD76195295* L_0 = (List_1_t2B71C7C40AB24AD3BA7EFF058B78E7FD76195295*)il2cpp_codegen_object_new(List_1_t2B71C7C40AB24AD3BA7EFF058B78E7FD76195295_il2cpp_TypeInfo_var);
		List_1__ctor_m01E98620490C05F4956E30BE71BB07091CD21F78(L_0, List_1__ctor_m01E98620490C05F4956E30BE71BB07091CD21F78_RuntimeMethod_var);
		__this->___m_List = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___m_List), (void*)L_0);
		il2cpp_codegen_runtime_class_init_inline(JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA_il2cpp_TypeInfo_var);
		JSONNode__ctor_mB710A69AF2EDA72D624E6291A6B8438316D5E8DA(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3Cget_ChildrenU3Ed__24__ctor_m95EEF28AA5E9412DACD322799156F76F44D26F51 (U3Cget_ChildrenU3Ed__24_tAAE17304DD542179299B598CFEA2DF919C468400* __this, int32_t ___0_U3CU3E1__state, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		int32_t L_0 = ___0_U3CU3E1__state;
		__this->___U3CU3E1__state = L_0;
		int32_t L_1;
		L_1 = Environment_get_CurrentManagedThreadId_m66483AADCCC13272EBDCD94D31D2E52603C24BDF(NULL);
		__this->___U3CU3El__initialThreadId = L_1;
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3Cget_ChildrenU3Ed__24_System_IDisposable_Dispose_m46DC15736BACD0533B79A2CED81CBC6E8F1602FA (U3Cget_ChildrenU3Ed__24_tAAE17304DD542179299B598CFEA2DF919C468400* __this, const RuntimeMethod* method) 
{
	int32_t V_0 = 0;
	{
		int32_t L_0 = __this->___U3CU3E1__state;
		V_0 = L_0;
		int32_t L_1 = V_0;
		if ((((int32_t)L_1) == ((int32_t)((int32_t)-3))))
		{
			goto IL_0010;
		}
	}
	{
		int32_t L_2 = V_0;
		if ((!(((uint32_t)L_2) == ((uint32_t)1))))
		{
			goto IL_001a;
		}
	}

IL_0010:
	{
	}
	{
		auto __finallyBlock = il2cpp::utils::Finally([&]
		{

FINALLY_0013:
			{
				U3Cget_ChildrenU3Ed__24_U3CU3Em__Finally1_mCBE434BF8D79BABE04A07BDD8B884F9AC88725DE(__this, NULL);
				return;
			}
		});
		try
		{
			goto IL_001a;
		}
		catch(Il2CppExceptionWrapper& e)
		{
			__finallyBlock.StoreException(e.ex);
		}
	}

IL_001a:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool U3Cget_ChildrenU3Ed__24_MoveNext_m88F1E73329046E9EA0D8238779D268D7D3CF148D (U3Cget_ChildrenU3Ed__24_tAAE17304DD542179299B598CFEA2DF919C468400* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Enumerator_MoveNext_mA1F5B4123B297DE3CEBD20E326757A151D8342F0_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Enumerator_get_Current_m0FA410EDA8DC3E5EC38251FBE80DF30999C8A369_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_GetEnumerator_mCB0A62D5C74B9F474105BB886112E0E86695C4CF_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	bool V_0 = false;
	int32_t V_1 = 0;
	JSONArray_tE863C8377E61FEEC9ACA640D090C176C7CDCB589* V_2 = NULL;
	JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* V_3 = NULL;
	{
		auto __finallyBlock = il2cpp::utils::Fault([&]
		{

FAULT_0084:
			{
				U3Cget_ChildrenU3Ed__24_System_IDisposable_Dispose_m46DC15736BACD0533B79A2CED81CBC6E8F1602FA(__this, NULL);
				return;
			}
		});
		try
		{
			{
				int32_t L_0 = __this->___U3CU3E1__state;
				V_1 = L_0;
				JSONArray_tE863C8377E61FEEC9ACA640D090C176C7CDCB589* L_1 = __this->___U3CU3E4__this;
				V_2 = L_1;
				int32_t L_2 = V_1;
				if (!L_2)
				{
					goto IL_0019_1;
				}
			}
			{
				int32_t L_3 = V_1;
				if ((((int32_t)L_3) == ((int32_t)1)))
				{
					goto IL_0059_1;
				}
			}
			{
				V_0 = (bool)0;
				goto IL_008b;
			}

IL_0019_1:
			{
				__this->___U3CU3E1__state = (-1);
				JSONArray_tE863C8377E61FEEC9ACA640D090C176C7CDCB589* L_4 = V_2;
				NullCheck(L_4);
				List_1_t2B71C7C40AB24AD3BA7EFF058B78E7FD76195295* L_5 = L_4->___m_List;
				NullCheck(L_5);
				Enumerator_t89C98852F05E305EB8D5EA5673CCC6A3E5D1ED06 L_6;
				L_6 = List_1_GetEnumerator_mCB0A62D5C74B9F474105BB886112E0E86695C4CF(L_5, List_1_GetEnumerator_mCB0A62D5C74B9F474105BB886112E0E86695C4CF_RuntimeMethod_var);
				__this->___U3CU3E7__wrap1 = L_6;
				Il2CppCodeGenWriteBarrier((void**)&(((&__this->___U3CU3E7__wrap1))->____list), (void*)NULL);
				#if IL2CPP_ENABLE_STRICT_WRITE_BARRIERS
				Il2CppCodeGenWriteBarrier((void**)&(((&__this->___U3CU3E7__wrap1))->____current), (void*)NULL);
				#endif
				__this->___U3CU3E1__state = ((int32_t)-3);
				goto IL_0061_1;
			}

IL_003b_1:
			{
				Enumerator_t89C98852F05E305EB8D5EA5673CCC6A3E5D1ED06* L_7 = (Enumerator_t89C98852F05E305EB8D5EA5673CCC6A3E5D1ED06*)(&__this->___U3CU3E7__wrap1);
				JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* L_8;
				L_8 = Enumerator_get_Current_m0FA410EDA8DC3E5EC38251FBE80DF30999C8A369_inline(L_7, Enumerator_get_Current_m0FA410EDA8DC3E5EC38251FBE80DF30999C8A369_RuntimeMethod_var);
				V_3 = L_8;
				JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* L_9 = V_3;
				__this->___U3CU3E2__current = L_9;
				Il2CppCodeGenWriteBarrier((void**)(&__this->___U3CU3E2__current), (void*)L_9);
				__this->___U3CU3E1__state = 1;
				V_0 = (bool)1;
				goto IL_008b;
			}

IL_0059_1:
			{
				__this->___U3CU3E1__state = ((int32_t)-3);
			}

IL_0061_1:
			{
				Enumerator_t89C98852F05E305EB8D5EA5673CCC6A3E5D1ED06* L_10 = (Enumerator_t89C98852F05E305EB8D5EA5673CCC6A3E5D1ED06*)(&__this->___U3CU3E7__wrap1);
				bool L_11;
				L_11 = Enumerator_MoveNext_mA1F5B4123B297DE3CEBD20E326757A151D8342F0(L_10, Enumerator_MoveNext_mA1F5B4123B297DE3CEBD20E326757A151D8342F0_RuntimeMethod_var);
				if (L_11)
				{
					goto IL_003b_1;
				}
			}
			{
				U3Cget_ChildrenU3Ed__24_U3CU3Em__Finally1_mCBE434BF8D79BABE04A07BDD8B884F9AC88725DE(__this, NULL);
				Enumerator_t89C98852F05E305EB8D5EA5673CCC6A3E5D1ED06* L_12 = (Enumerator_t89C98852F05E305EB8D5EA5673CCC6A3E5D1ED06*)(&__this->___U3CU3E7__wrap1);
				il2cpp_codegen_initobj(L_12, sizeof(Enumerator_t89C98852F05E305EB8D5EA5673CCC6A3E5D1ED06));
				V_0 = (bool)0;
				goto IL_008b;
			}
		}
		catch(Il2CppExceptionWrapper& e)
		{
			__finallyBlock.StoreException(e.ex);
		}
	}

IL_008b:
	{
		bool L_13 = V_0;
		return L_13;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3Cget_ChildrenU3Ed__24_U3CU3Em__Finally1_mCBE434BF8D79BABE04A07BDD8B884F9AC88725DE (U3Cget_ChildrenU3Ed__24_tAAE17304DD542179299B598CFEA2DF919C468400* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Enumerator_Dispose_m0B5EFE87A935F7B15321F6D02492EC3989DD70D9_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		__this->___U3CU3E1__state = (-1);
		Enumerator_t89C98852F05E305EB8D5EA5673CCC6A3E5D1ED06* L_0 = (Enumerator_t89C98852F05E305EB8D5EA5673CCC6A3E5D1ED06*)(&__this->___U3CU3E7__wrap1);
		Enumerator_Dispose_m0B5EFE87A935F7B15321F6D02492EC3989DD70D9(L_0, Enumerator_Dispose_m0B5EFE87A935F7B15321F6D02492EC3989DD70D9_RuntimeMethod_var);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* U3Cget_ChildrenU3Ed__24_System_Collections_Generic_IEnumeratorU3CSimpleJSON_JSONNodeU3E_get_Current_m407DB9E152EACE8041BD776E9C43E95CCB611AA3 (U3Cget_ChildrenU3Ed__24_tAAE17304DD542179299B598CFEA2DF919C468400* __this, const RuntimeMethod* method) 
{
	{
		JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* L_0 = __this->___U3CU3E2__current;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3Cget_ChildrenU3Ed__24_System_Collections_IEnumerator_Reset_mA18412275DA36B0363FB34F6B8892515A8714998 (U3Cget_ChildrenU3Ed__24_tAAE17304DD542179299B598CFEA2DF919C468400* __this, const RuntimeMethod* method) 
{
	{
		NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A* L_0 = (NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A_il2cpp_TypeInfo_var)));
		NotSupportedException__ctor_m1398D0CDE19B36AA3DE9392879738C1EA2439CDF(L_0, NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_0, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&U3Cget_ChildrenU3Ed__24_System_Collections_IEnumerator_Reset_mA18412275DA36B0363FB34F6B8892515A8714998_RuntimeMethod_var)));
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* U3Cget_ChildrenU3Ed__24_System_Collections_IEnumerator_get_Current_m678D2F3353FF941B656CD0DA2B60099241B1ED1E (U3Cget_ChildrenU3Ed__24_tAAE17304DD542179299B598CFEA2DF919C468400* __this, const RuntimeMethod* method) 
{
	{
		JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* L_0 = __this->___U3CU3E2__current;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* U3Cget_ChildrenU3Ed__24_System_Collections_Generic_IEnumerableU3CSimpleJSON_JSONNodeU3E_GetEnumerator_mA138DAA861DE2241F9AFAB0F9A425A3BDF7D3A49 (U3Cget_ChildrenU3Ed__24_tAAE17304DD542179299B598CFEA2DF919C468400* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3Cget_ChildrenU3Ed__24_tAAE17304DD542179299B598CFEA2DF919C468400_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	U3Cget_ChildrenU3Ed__24_tAAE17304DD542179299B598CFEA2DF919C468400* V_0 = NULL;
	{
		int32_t L_0 = __this->___U3CU3E1__state;
		if ((!(((uint32_t)L_0) == ((uint32_t)((int32_t)-2)))))
		{
			goto IL_0022;
		}
	}
	{
		int32_t L_1 = __this->___U3CU3El__initialThreadId;
		int32_t L_2;
		L_2 = Environment_get_CurrentManagedThreadId_m66483AADCCC13272EBDCD94D31D2E52603C24BDF(NULL);
		if ((!(((uint32_t)L_1) == ((uint32_t)L_2))))
		{
			goto IL_0022;
		}
	}
	{
		__this->___U3CU3E1__state = 0;
		V_0 = __this;
		goto IL_0035;
	}

IL_0022:
	{
		U3Cget_ChildrenU3Ed__24_tAAE17304DD542179299B598CFEA2DF919C468400* L_3 = (U3Cget_ChildrenU3Ed__24_tAAE17304DD542179299B598CFEA2DF919C468400*)il2cpp_codegen_object_new(U3Cget_ChildrenU3Ed__24_tAAE17304DD542179299B598CFEA2DF919C468400_il2cpp_TypeInfo_var);
		U3Cget_ChildrenU3Ed__24__ctor_m95EEF28AA5E9412DACD322799156F76F44D26F51(L_3, 0, NULL);
		V_0 = L_3;
		U3Cget_ChildrenU3Ed__24_tAAE17304DD542179299B598CFEA2DF919C468400* L_4 = V_0;
		JSONArray_tE863C8377E61FEEC9ACA640D090C176C7CDCB589* L_5 = __this->___U3CU3E4__this;
		NullCheck(L_4);
		L_4->___U3CU3E4__this = L_5;
		Il2CppCodeGenWriteBarrier((void**)(&L_4->___U3CU3E4__this), (void*)L_5);
	}

IL_0035:
	{
		U3Cget_ChildrenU3Ed__24_tAAE17304DD542179299B598CFEA2DF919C468400* L_6 = V_0;
		return L_6;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* U3Cget_ChildrenU3Ed__24_System_Collections_IEnumerable_GetEnumerator_m3235CB3BB5E5D4FD660A2DB7359F45521A6EE285 (U3Cget_ChildrenU3Ed__24_tAAE17304DD542179299B598CFEA2DF919C468400* __this, const RuntimeMethod* method) 
{
	{
		RuntimeObject* L_0;
		L_0 = U3Cget_ChildrenU3Ed__24_System_Collections_Generic_IEnumerableU3CSimpleJSON_JSONNodeU3E_GetEnumerator_mA138DAA861DE2241F9AFAB0F9A425A3BDF7D3A49(__this, NULL);
		return L_0;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void JSONObject_set_Inline_mF8D7E439225CCD750F9D257A66EA937094FDEC25 (JSONObject_t8136ED73F77B01FF951AB5A2C9B81C4CD0B5C7B6* __this, bool ___0_value, const RuntimeMethod* method) 
{
	{
		bool L_0 = ___0_value;
		__this->___inline = L_0;
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool JSONObject_get_IsObject_mCA2563BDEBF5B61BAD1CCCFB63A33C8DB09D5B65 (JSONObject_t8136ED73F77B01FF951AB5A2C9B81C4CD0B5C7B6* __this, const RuntimeMethod* method) 
{
	{
		return (bool)1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* JSONObject_get_Item_m8324E7D7055596CFF2C7A8F762575E4D2D8F32E3 (JSONObject_t8136ED73F77B01FF951AB5A2C9B81C4CD0B5C7B6* __this, String_t* ___0_aKey, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Dictionary_2_ContainsKey_m99618DFA61C82E07AE40B4277E4FD6719478CA8E_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Dictionary_2_get_Item_mF5EABC94CF3E2E6916B4FBE50D92487827CF875E_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&JSONLazyCreator_t93B398608BA0717C413F6875922A3BF9C7ABD8B9_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Dictionary_2_t9F8163AF97B5FC284EA73FF608A15D501B0D9BDC* L_0 = __this->___m_Dict;
		String_t* L_1 = ___0_aKey;
		NullCheck(L_0);
		bool L_2;
		L_2 = Dictionary_2_ContainsKey_m99618DFA61C82E07AE40B4277E4FD6719478CA8E(L_0, L_1, Dictionary_2_ContainsKey_m99618DFA61C82E07AE40B4277E4FD6719478CA8E_RuntimeMethod_var);
		if (!L_2)
		{
			goto IL_001b;
		}
	}
	{
		Dictionary_2_t9F8163AF97B5FC284EA73FF608A15D501B0D9BDC* L_3 = __this->___m_Dict;
		String_t* L_4 = ___0_aKey;
		NullCheck(L_3);
		JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* L_5;
		L_5 = Dictionary_2_get_Item_mF5EABC94CF3E2E6916B4FBE50D92487827CF875E(L_3, L_4, Dictionary_2_get_Item_mF5EABC94CF3E2E6916B4FBE50D92487827CF875E_RuntimeMethod_var);
		return L_5;
	}

IL_001b:
	{
		String_t* L_6 = ___0_aKey;
		JSONLazyCreator_t93B398608BA0717C413F6875922A3BF9C7ABD8B9* L_7 = (JSONLazyCreator_t93B398608BA0717C413F6875922A3BF9C7ABD8B9*)il2cpp_codegen_object_new(JSONLazyCreator_t93B398608BA0717C413F6875922A3BF9C7ABD8B9_il2cpp_TypeInfo_var);
		JSONLazyCreator__ctor_m3965360CD8D0DEA5E2ED0B3E4981936534F30C93(L_7, __this, L_6, NULL);
		return L_7;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t JSONObject_get_Count_m5AA6D6E9604751BEB349B290F1EA21BDFEA8F34D (JSONObject_t8136ED73F77B01FF951AB5A2C9B81C4CD0B5C7B6* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Dictionary_2_get_Count_m47F3CE035B00098EF81B1B8690C6EBC6CD4AB31C_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Dictionary_2_t9F8163AF97B5FC284EA73FF608A15D501B0D9BDC* L_0 = __this->___m_Dict;
		NullCheck(L_0);
		int32_t L_1;
		L_1 = Dictionary_2_get_Count_m47F3CE035B00098EF81B1B8690C6EBC6CD4AB31C(L_0, Dictionary_2_get_Count_m47F3CE035B00098EF81B1B8690C6EBC6CD4AB31C_RuntimeMethod_var);
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void JSONObject_Add_m3BE5C09B6AD2F9C2B3B00BC722E18A6308B7FDB8 (JSONObject_t8136ED73F77B01FF951AB5A2C9B81C4CD0B5C7B6* __this, String_t* ___0_aKey, JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* ___1_aItem, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Dictionary_2_Add_m84B40B9FC5E37AFDFA1D8C971B0B449CC1B95C23_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Dictionary_2_ContainsKey_m99618DFA61C82E07AE40B4277E4FD6719478CA8E_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Dictionary_2_set_Item_m53E9B223E60BB4A4CFBB5807E236C253836BBADD_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&JSONNull_tE058D16E7011140DB8F406485C84D3D51DA7A97D_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Guid_t V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* L_0 = ___1_aItem;
		il2cpp_codegen_runtime_class_init_inline(JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA_il2cpp_TypeInfo_var);
		bool L_1;
		L_1 = JSONNode_op_Equality_mDD36B312D77EC1FC9939276C087EBA8E7B8AD92F(L_0, NULL, NULL);
		if (!L_1)
		{
			goto IL_0010;
		}
	}
	{
		il2cpp_codegen_runtime_class_init_inline(JSONNull_tE058D16E7011140DB8F406485C84D3D51DA7A97D_il2cpp_TypeInfo_var);
		JSONNull_tE058D16E7011140DB8F406485C84D3D51DA7A97D* L_2;
		L_2 = JSONNull_CreateOrGet_m920C3D38D052C4C8EDE996476F91D9913B6017F2(NULL);
		___1_aItem = L_2;
	}

IL_0010:
	{
		String_t* L_3 = ___0_aKey;
		if (!L_3)
		{
			goto IL_003d;
		}
	}
	{
		Dictionary_2_t9F8163AF97B5FC284EA73FF608A15D501B0D9BDC* L_4 = __this->___m_Dict;
		String_t* L_5 = ___0_aKey;
		NullCheck(L_4);
		bool L_6;
		L_6 = Dictionary_2_ContainsKey_m99618DFA61C82E07AE40B4277E4FD6719478CA8E(L_4, L_5, Dictionary_2_ContainsKey_m99618DFA61C82E07AE40B4277E4FD6719478CA8E_RuntimeMethod_var);
		if (!L_6)
		{
			goto IL_002f;
		}
	}
	{
		Dictionary_2_t9F8163AF97B5FC284EA73FF608A15D501B0D9BDC* L_7 = __this->___m_Dict;
		String_t* L_8 = ___0_aKey;
		JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* L_9 = ___1_aItem;
		NullCheck(L_7);
		Dictionary_2_set_Item_m53E9B223E60BB4A4CFBB5807E236C253836BBADD(L_7, L_8, L_9, Dictionary_2_set_Item_m53E9B223E60BB4A4CFBB5807E236C253836BBADD_RuntimeMethod_var);
		return;
	}

IL_002f:
	{
		Dictionary_2_t9F8163AF97B5FC284EA73FF608A15D501B0D9BDC* L_10 = __this->___m_Dict;
		String_t* L_11 = ___0_aKey;
		JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* L_12 = ___1_aItem;
		NullCheck(L_10);
		Dictionary_2_Add_m84B40B9FC5E37AFDFA1D8C971B0B449CC1B95C23(L_10, L_11, L_12, Dictionary_2_Add_m84B40B9FC5E37AFDFA1D8C971B0B449CC1B95C23_RuntimeMethod_var);
		return;
	}

IL_003d:
	{
		Dictionary_2_t9F8163AF97B5FC284EA73FF608A15D501B0D9BDC* L_13 = __this->___m_Dict;
		Guid_t L_14;
		L_14 = Guid_NewGuid_m1F4894E8DC089811D6252148AD5858E58D43A7BD(NULL);
		V_0 = L_14;
		String_t* L_15;
		L_15 = Guid_ToString_m2BFFD5FA726E03FA707AAFCCF065896C46D5290C((&V_0), NULL);
		JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* L_16 = ___1_aItem;
		NullCheck(L_13);
		Dictionary_2_Add_m84B40B9FC5E37AFDFA1D8C971B0B449CC1B95C23(L_13, L_15, L_16, Dictionary_2_Add_m84B40B9FC5E37AFDFA1D8C971B0B449CC1B95C23_RuntimeMethod_var);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* JSONObject_get_Children_m3DCF67F343A0363AE4FD29E6C4EB8DE71011FE14 (JSONObject_t8136ED73F77B01FF951AB5A2C9B81C4CD0B5C7B6* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3Cget_ChildrenU3Ed__27_t601D7688734A9685C53234154970690BCEDBCA60_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		U3Cget_ChildrenU3Ed__27_t601D7688734A9685C53234154970690BCEDBCA60* L_0 = (U3Cget_ChildrenU3Ed__27_t601D7688734A9685C53234154970690BCEDBCA60*)il2cpp_codegen_object_new(U3Cget_ChildrenU3Ed__27_t601D7688734A9685C53234154970690BCEDBCA60_il2cpp_TypeInfo_var);
		U3Cget_ChildrenU3Ed__27__ctor_m18A161AD65F41D242EAC8B1AE005D17C47287B05(L_0, ((int32_t)-2), NULL);
		U3Cget_ChildrenU3Ed__27_t601D7688734A9685C53234154970690BCEDBCA60* L_1 = L_0;
		NullCheck(L_1);
		L_1->___U3CU3E4__this = __this;
		Il2CppCodeGenWriteBarrier((void**)(&L_1->___U3CU3E4__this), (void*)__this);
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void JSONObject_WriteToStringBuilder_m4DCAA68A651E392130E88C7D163EC1CC581B6122 (JSONObject_t8136ED73F77B01FF951AB5A2C9B81C4CD0B5C7B6* __this, StringBuilder_t* ___0_aSB, int32_t ___1_aIndent, int32_t ___2_aIndentInc, int32_t ___3_aMode, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Dictionary_2_GetEnumerator_m3ED1AC853C31B2712204402D7F3138E2F21B38E2_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Enumerator_Dispose_m5D3018F5956EA066A5237FF7F5E60E1683EA32E7_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Enumerator_MoveNext_m351A4EF4E1F8015902C7DC4DC2BA4A74B6ACB9C4_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Enumerator_get_Current_m58A7BF3F5786B6959A34BAE6A8DFCF82E3E39AAD_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&KeyValuePair_2_get_Key_mE5155D1D01DACA3EBAB489C906488E451D0EC647_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&KeyValuePair_2_get_Value_m82D9F258CADB85DFD8CE44E3F810B55B94E3DA67_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral0767326DBE1DD31063256737FD019DC6682353E2);
		s_Il2CppMethodInitialized = true;
	}
	bool V_0 = false;
	Enumerator_t0C0DD82B30597416F0154D4B6B95DB6D43A2AA04 V_1;
	memset((&V_1), 0, sizeof(V_1));
	KeyValuePair_2_tDBA0DCDFC0258820AE84EA4644610E4FB71EA809 V_2;
	memset((&V_2), 0, sizeof(V_2));
	{
		StringBuilder_t* L_0 = ___0_aSB;
		NullCheck(L_0);
		StringBuilder_t* L_1;
		L_1 = StringBuilder_Append_m71228B30F05724CD2CD96D9611DCD61BFB96A6E1(L_0, ((int32_t)123), NULL);
		V_0 = (bool)1;
		bool L_2 = __this->___inline;
		if (!L_2)
		{
			goto IL_0016;
		}
	}
	{
		___3_aMode = 0;
	}

IL_0016:
	{
		Dictionary_2_t9F8163AF97B5FC284EA73FF608A15D501B0D9BDC* L_3 = __this->___m_Dict;
		NullCheck(L_3);
		Enumerator_t0C0DD82B30597416F0154D4B6B95DB6D43A2AA04 L_4;
		L_4 = Dictionary_2_GetEnumerator_m3ED1AC853C31B2712204402D7F3138E2F21B38E2(L_3, Dictionary_2_GetEnumerator_m3ED1AC853C31B2712204402D7F3138E2F21B38E2_RuntimeMethod_var);
		V_1 = L_4;
	}
	{
		auto __finallyBlock = il2cpp::utils::Finally([&]
		{

FINALLY_00b7:
			{
				Enumerator_Dispose_m5D3018F5956EA066A5237FF7F5E60E1683EA32E7((&V_1), Enumerator_Dispose_m5D3018F5956EA066A5237FF7F5E60E1683EA32E7_RuntimeMethod_var);
				return;
			}
		});
		try
		{
			{
				goto IL_00a9_1;
			}

IL_0027_1:
			{
				KeyValuePair_2_tDBA0DCDFC0258820AE84EA4644610E4FB71EA809 L_5;
				L_5 = Enumerator_get_Current_m58A7BF3F5786B6959A34BAE6A8DFCF82E3E39AAD_inline((&V_1), Enumerator_get_Current_m58A7BF3F5786B6959A34BAE6A8DFCF82E3E39AAD_RuntimeMethod_var);
				V_2 = L_5;
				bool L_6 = V_0;
				if (L_6)
				{
					goto IL_003b_1;
				}
			}
			{
				StringBuilder_t* L_7 = ___0_aSB;
				NullCheck(L_7);
				StringBuilder_t* L_8;
				L_8 = StringBuilder_Append_m71228B30F05724CD2CD96D9611DCD61BFB96A6E1(L_7, ((int32_t)44), NULL);
			}

IL_003b_1:
			{
				V_0 = (bool)0;
				int32_t L_9 = ___3_aMode;
				if ((!(((uint32_t)L_9) == ((uint32_t)1))))
				{
					goto IL_0049_1;
				}
			}
			{
				StringBuilder_t* L_10 = ___0_aSB;
				NullCheck(L_10);
				StringBuilder_t* L_11;
				L_11 = StringBuilder_AppendLine_m3BC704C4E6A8531027D8C9287D0AB2AA0188AC4E(L_10, NULL);
			}

IL_0049_1:
			{
				int32_t L_12 = ___3_aMode;
				if ((!(((uint32_t)L_12) == ((uint32_t)1))))
				{
					goto IL_005a_1;
				}
			}
			{
				StringBuilder_t* L_13 = ___0_aSB;
				int32_t L_14 = ___1_aIndent;
				int32_t L_15 = ___2_aIndentInc;
				NullCheck(L_13);
				StringBuilder_t* L_16;
				L_16 = StringBuilder_Append_mE20F6CD28FC8E8C9FD65987DBD32E6087CCE1CF3(L_13, ((int32_t)32), ((int32_t)il2cpp_codegen_add(L_14, L_15)), NULL);
			}

IL_005a_1:
			{
				StringBuilder_t* L_17 = ___0_aSB;
				NullCheck(L_17);
				StringBuilder_t* L_18;
				L_18 = StringBuilder_Append_m71228B30F05724CD2CD96D9611DCD61BFB96A6E1(L_17, ((int32_t)34), NULL);
				String_t* L_19;
				L_19 = KeyValuePair_2_get_Key_mE5155D1D01DACA3EBAB489C906488E451D0EC647_inline((&V_2), KeyValuePair_2_get_Key_mE5155D1D01DACA3EBAB489C906488E451D0EC647_RuntimeMethod_var);
				il2cpp_codegen_runtime_class_init_inline(JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA_il2cpp_TypeInfo_var);
				String_t* L_20;
				L_20 = JSONNode_Escape_m6646863678DB23BDEB28738CC57E452169FFB0FF(L_19, NULL);
				NullCheck(L_18);
				StringBuilder_t* L_21;
				L_21 = StringBuilder_Append_m08904D74E0C78E5F36DCD9C9303BDD07886D9F7D(L_18, L_20, NULL);
				NullCheck(L_21);
				StringBuilder_t* L_22;
				L_22 = StringBuilder_Append_m71228B30F05724CD2CD96D9611DCD61BFB96A6E1(L_21, ((int32_t)34), NULL);
				int32_t L_23 = ___3_aMode;
				if (L_23)
				{
					goto IL_008a_1;
				}
			}
			{
				StringBuilder_t* L_24 = ___0_aSB;
				NullCheck(L_24);
				StringBuilder_t* L_25;
				L_25 = StringBuilder_Append_m71228B30F05724CD2CD96D9611DCD61BFB96A6E1(L_24, ((int32_t)58), NULL);
				goto IL_0096_1;
			}

IL_008a_1:
			{
				StringBuilder_t* L_26 = ___0_aSB;
				NullCheck(L_26);
				StringBuilder_t* L_27;
				L_27 = StringBuilder_Append_m08904D74E0C78E5F36DCD9C9303BDD07886D9F7D(L_26, _stringLiteral0767326DBE1DD31063256737FD019DC6682353E2, NULL);
			}

IL_0096_1:
			{
				JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* L_28;
				L_28 = KeyValuePair_2_get_Value_m82D9F258CADB85DFD8CE44E3F810B55B94E3DA67_inline((&V_2), KeyValuePair_2_get_Value_m82D9F258CADB85DFD8CE44E3F810B55B94E3DA67_RuntimeMethod_var);
				StringBuilder_t* L_29 = ___0_aSB;
				int32_t L_30 = ___1_aIndent;
				int32_t L_31 = ___2_aIndentInc;
				int32_t L_32 = ___2_aIndentInc;
				int32_t L_33 = ___3_aMode;
				NullCheck(L_28);
				VirtualActionInvoker4< StringBuilder_t*, int32_t, int32_t, int32_t >::Invoke(16, L_28, L_29, ((int32_t)il2cpp_codegen_add(L_30, L_31)), L_32, L_33);
			}

IL_00a9_1:
			{
				bool L_34;
				L_34 = Enumerator_MoveNext_m351A4EF4E1F8015902C7DC4DC2BA4A74B6ACB9C4((&V_1), Enumerator_MoveNext_m351A4EF4E1F8015902C7DC4DC2BA4A74B6ACB9C4_RuntimeMethod_var);
				if (L_34)
				{
					goto IL_0027_1;
				}
			}
			{
				goto IL_00c5;
			}
		}
		catch(Il2CppExceptionWrapper& e)
		{
			__finallyBlock.StoreException(e.ex);
		}
	}

IL_00c5:
	{
		int32_t L_35 = ___3_aMode;
		if ((!(((uint32_t)L_35) == ((uint32_t)1))))
		{
			goto IL_00d9;
		}
	}
	{
		StringBuilder_t* L_36 = ___0_aSB;
		NullCheck(L_36);
		StringBuilder_t* L_37;
		L_37 = StringBuilder_AppendLine_m3BC704C4E6A8531027D8C9287D0AB2AA0188AC4E(L_36, NULL);
		int32_t L_38 = ___1_aIndent;
		NullCheck(L_37);
		StringBuilder_t* L_39;
		L_39 = StringBuilder_Append_mE20F6CD28FC8E8C9FD65987DBD32E6087CCE1CF3(L_37, ((int32_t)32), L_38, NULL);
	}

IL_00d9:
	{
		StringBuilder_t* L_40 = ___0_aSB;
		NullCheck(L_40);
		StringBuilder_t* L_41;
		L_41 = StringBuilder_Append_m71228B30F05724CD2CD96D9611DCD61BFB96A6E1(L_40, ((int32_t)125), NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void JSONObject__ctor_mD4F6D4B7B8D8D3F4FCF45B4BAC32AEB66588307A (JSONObject_t8136ED73F77B01FF951AB5A2C9B81C4CD0B5C7B6* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Dictionary_2__ctor_mF206CED015A4F12961D84BB5FE5F78D15E491CA3_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Dictionary_2_t9F8163AF97B5FC284EA73FF608A15D501B0D9BDC_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Dictionary_2_t9F8163AF97B5FC284EA73FF608A15D501B0D9BDC* L_0 = (Dictionary_2_t9F8163AF97B5FC284EA73FF608A15D501B0D9BDC*)il2cpp_codegen_object_new(Dictionary_2_t9F8163AF97B5FC284EA73FF608A15D501B0D9BDC_il2cpp_TypeInfo_var);
		Dictionary_2__ctor_mF206CED015A4F12961D84BB5FE5F78D15E491CA3(L_0, Dictionary_2__ctor_mF206CED015A4F12961D84BB5FE5F78D15E491CA3_RuntimeMethod_var);
		__this->___m_Dict = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___m_Dict), (void*)L_0);
		il2cpp_codegen_runtime_class_init_inline(JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA_il2cpp_TypeInfo_var);
		JSONNode__ctor_mB710A69AF2EDA72D624E6291A6B8438316D5E8DA(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3Cget_ChildrenU3Ed__27__ctor_m18A161AD65F41D242EAC8B1AE005D17C47287B05 (U3Cget_ChildrenU3Ed__27_t601D7688734A9685C53234154970690BCEDBCA60* __this, int32_t ___0_U3CU3E1__state, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		int32_t L_0 = ___0_U3CU3E1__state;
		__this->___U3CU3E1__state = L_0;
		int32_t L_1;
		L_1 = Environment_get_CurrentManagedThreadId_m66483AADCCC13272EBDCD94D31D2E52603C24BDF(NULL);
		__this->___U3CU3El__initialThreadId = L_1;
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3Cget_ChildrenU3Ed__27_System_IDisposable_Dispose_m3DCE5C10014923EEDA1EEE48F20B376E8A60C30B (U3Cget_ChildrenU3Ed__27_t601D7688734A9685C53234154970690BCEDBCA60* __this, const RuntimeMethod* method) 
{
	int32_t V_0 = 0;
	{
		int32_t L_0 = __this->___U3CU3E1__state;
		V_0 = L_0;
		int32_t L_1 = V_0;
		if ((((int32_t)L_1) == ((int32_t)((int32_t)-3))))
		{
			goto IL_0010;
		}
	}
	{
		int32_t L_2 = V_0;
		if ((!(((uint32_t)L_2) == ((uint32_t)1))))
		{
			goto IL_001a;
		}
	}

IL_0010:
	{
	}
	{
		auto __finallyBlock = il2cpp::utils::Finally([&]
		{

FINALLY_0013:
			{
				U3Cget_ChildrenU3Ed__27_U3CU3Em__Finally1_m8BC545E469064B06F65F22A2FDA8F9BCCC693CF0(__this, NULL);
				return;
			}
		});
		try
		{
			goto IL_001a;
		}
		catch(Il2CppExceptionWrapper& e)
		{
			__finallyBlock.StoreException(e.ex);
		}
	}

IL_001a:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool U3Cget_ChildrenU3Ed__27_MoveNext_mEADC2C5094DBD8D981A703FE24D0BFB022373464 (U3Cget_ChildrenU3Ed__27_t601D7688734A9685C53234154970690BCEDBCA60* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Dictionary_2_GetEnumerator_m3ED1AC853C31B2712204402D7F3138E2F21B38E2_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Enumerator_MoveNext_m351A4EF4E1F8015902C7DC4DC2BA4A74B6ACB9C4_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Enumerator_get_Current_m58A7BF3F5786B6959A34BAE6A8DFCF82E3E39AAD_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&KeyValuePair_2_get_Value_m82D9F258CADB85DFD8CE44E3F810B55B94E3DA67_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	bool V_0 = false;
	int32_t V_1 = 0;
	JSONObject_t8136ED73F77B01FF951AB5A2C9B81C4CD0B5C7B6* V_2 = NULL;
	KeyValuePair_2_tDBA0DCDFC0258820AE84EA4644610E4FB71EA809 V_3;
	memset((&V_3), 0, sizeof(V_3));
	{
		auto __finallyBlock = il2cpp::utils::Fault([&]
		{

FAULT_008a:
			{
				U3Cget_ChildrenU3Ed__27_System_IDisposable_Dispose_m3DCE5C10014923EEDA1EEE48F20B376E8A60C30B(__this, NULL);
				return;
			}
		});
		try
		{
			{
				int32_t L_0 = __this->___U3CU3E1__state;
				V_1 = L_0;
				JSONObject_t8136ED73F77B01FF951AB5A2C9B81C4CD0B5C7B6* L_1 = __this->___U3CU3E4__this;
				V_2 = L_1;
				int32_t L_2 = V_1;
				if (!L_2)
				{
					goto IL_0019_1;
				}
			}
			{
				int32_t L_3 = V_1;
				if ((((int32_t)L_3) == ((int32_t)1)))
				{
					goto IL_005f_1;
				}
			}
			{
				V_0 = (bool)0;
				goto IL_0091;
			}

IL_0019_1:
			{
				__this->___U3CU3E1__state = (-1);
				JSONObject_t8136ED73F77B01FF951AB5A2C9B81C4CD0B5C7B6* L_4 = V_2;
				NullCheck(L_4);
				Dictionary_2_t9F8163AF97B5FC284EA73FF608A15D501B0D9BDC* L_5 = L_4->___m_Dict;
				NullCheck(L_5);
				Enumerator_t0C0DD82B30597416F0154D4B6B95DB6D43A2AA04 L_6;
				L_6 = Dictionary_2_GetEnumerator_m3ED1AC853C31B2712204402D7F3138E2F21B38E2(L_5, Dictionary_2_GetEnumerator_m3ED1AC853C31B2712204402D7F3138E2F21B38E2_RuntimeMethod_var);
				__this->___U3CU3E7__wrap1 = L_6;
				Il2CppCodeGenWriteBarrier((void**)&(((&__this->___U3CU3E7__wrap1))->____dictionary), (void*)NULL);
				#if IL2CPP_ENABLE_STRICT_WRITE_BARRIERS
				Il2CppCodeGenWriteBarrier((void**)&((&(((&__this->___U3CU3E7__wrap1))->____current))->___key), (void*)NULL);
				#endif
				#if IL2CPP_ENABLE_STRICT_WRITE_BARRIERS
				Il2CppCodeGenWriteBarrier((void**)&((&(((&__this->___U3CU3E7__wrap1))->____current))->___value), (void*)NULL);
				#endif
				__this->___U3CU3E1__state = ((int32_t)-3);
				goto IL_0067_1;
			}

IL_003b_1:
			{
				Enumerator_t0C0DD82B30597416F0154D4B6B95DB6D43A2AA04* L_7 = (Enumerator_t0C0DD82B30597416F0154D4B6B95DB6D43A2AA04*)(&__this->___U3CU3E7__wrap1);
				KeyValuePair_2_tDBA0DCDFC0258820AE84EA4644610E4FB71EA809 L_8;
				L_8 = Enumerator_get_Current_m58A7BF3F5786B6959A34BAE6A8DFCF82E3E39AAD_inline(L_7, Enumerator_get_Current_m58A7BF3F5786B6959A34BAE6A8DFCF82E3E39AAD_RuntimeMethod_var);
				V_3 = L_8;
				JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* L_9;
				L_9 = KeyValuePair_2_get_Value_m82D9F258CADB85DFD8CE44E3F810B55B94E3DA67_inline((&V_3), KeyValuePair_2_get_Value_m82D9F258CADB85DFD8CE44E3F810B55B94E3DA67_RuntimeMethod_var);
				__this->___U3CU3E2__current = L_9;
				Il2CppCodeGenWriteBarrier((void**)(&__this->___U3CU3E2__current), (void*)L_9);
				__this->___U3CU3E1__state = 1;
				V_0 = (bool)1;
				goto IL_0091;
			}

IL_005f_1:
			{
				__this->___U3CU3E1__state = ((int32_t)-3);
			}

IL_0067_1:
			{
				Enumerator_t0C0DD82B30597416F0154D4B6B95DB6D43A2AA04* L_10 = (Enumerator_t0C0DD82B30597416F0154D4B6B95DB6D43A2AA04*)(&__this->___U3CU3E7__wrap1);
				bool L_11;
				L_11 = Enumerator_MoveNext_m351A4EF4E1F8015902C7DC4DC2BA4A74B6ACB9C4(L_10, Enumerator_MoveNext_m351A4EF4E1F8015902C7DC4DC2BA4A74B6ACB9C4_RuntimeMethod_var);
				if (L_11)
				{
					goto IL_003b_1;
				}
			}
			{
				U3Cget_ChildrenU3Ed__27_U3CU3Em__Finally1_m8BC545E469064B06F65F22A2FDA8F9BCCC693CF0(__this, NULL);
				Enumerator_t0C0DD82B30597416F0154D4B6B95DB6D43A2AA04* L_12 = (Enumerator_t0C0DD82B30597416F0154D4B6B95DB6D43A2AA04*)(&__this->___U3CU3E7__wrap1);
				il2cpp_codegen_initobj(L_12, sizeof(Enumerator_t0C0DD82B30597416F0154D4B6B95DB6D43A2AA04));
				V_0 = (bool)0;
				goto IL_0091;
			}
		}
		catch(Il2CppExceptionWrapper& e)
		{
			__finallyBlock.StoreException(e.ex);
		}
	}

IL_0091:
	{
		bool L_13 = V_0;
		return L_13;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3Cget_ChildrenU3Ed__27_U3CU3Em__Finally1_m8BC545E469064B06F65F22A2FDA8F9BCCC693CF0 (U3Cget_ChildrenU3Ed__27_t601D7688734A9685C53234154970690BCEDBCA60* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Enumerator_Dispose_m5D3018F5956EA066A5237FF7F5E60E1683EA32E7_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		__this->___U3CU3E1__state = (-1);
		Enumerator_t0C0DD82B30597416F0154D4B6B95DB6D43A2AA04* L_0 = (Enumerator_t0C0DD82B30597416F0154D4B6B95DB6D43A2AA04*)(&__this->___U3CU3E7__wrap1);
		Enumerator_Dispose_m5D3018F5956EA066A5237FF7F5E60E1683EA32E7(L_0, Enumerator_Dispose_m5D3018F5956EA066A5237FF7F5E60E1683EA32E7_RuntimeMethod_var);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* U3Cget_ChildrenU3Ed__27_System_Collections_Generic_IEnumeratorU3CSimpleJSON_JSONNodeU3E_get_Current_m7C0A551A06619EE7BB1F94EBACD7F976BB9559E3 (U3Cget_ChildrenU3Ed__27_t601D7688734A9685C53234154970690BCEDBCA60* __this, const RuntimeMethod* method) 
{
	{
		JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* L_0 = __this->___U3CU3E2__current;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3Cget_ChildrenU3Ed__27_System_Collections_IEnumerator_Reset_m6B6CFD88298C682F8711FDEA83945EF3CCFB89A0 (U3Cget_ChildrenU3Ed__27_t601D7688734A9685C53234154970690BCEDBCA60* __this, const RuntimeMethod* method) 
{
	{
		NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A* L_0 = (NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&NotSupportedException_t1429765983D409BD2986508963C98D214E4EBF4A_il2cpp_TypeInfo_var)));
		NotSupportedException__ctor_m1398D0CDE19B36AA3DE9392879738C1EA2439CDF(L_0, NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_0, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&U3Cget_ChildrenU3Ed__27_System_Collections_IEnumerator_Reset_m6B6CFD88298C682F8711FDEA83945EF3CCFB89A0_RuntimeMethod_var)));
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* U3Cget_ChildrenU3Ed__27_System_Collections_IEnumerator_get_Current_m8B46E8ACFD0C4FF03BC1F82BF1236AFFA9BA0D75 (U3Cget_ChildrenU3Ed__27_t601D7688734A9685C53234154970690BCEDBCA60* __this, const RuntimeMethod* method) 
{
	{
		JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* L_0 = __this->___U3CU3E2__current;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* U3Cget_ChildrenU3Ed__27_System_Collections_Generic_IEnumerableU3CSimpleJSON_JSONNodeU3E_GetEnumerator_m7C7CEB31168871A6A9A9A8BB596BE0714969C4A2 (U3Cget_ChildrenU3Ed__27_t601D7688734A9685C53234154970690BCEDBCA60* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3Cget_ChildrenU3Ed__27_t601D7688734A9685C53234154970690BCEDBCA60_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	U3Cget_ChildrenU3Ed__27_t601D7688734A9685C53234154970690BCEDBCA60* V_0 = NULL;
	{
		int32_t L_0 = __this->___U3CU3E1__state;
		if ((!(((uint32_t)L_0) == ((uint32_t)((int32_t)-2)))))
		{
			goto IL_0022;
		}
	}
	{
		int32_t L_1 = __this->___U3CU3El__initialThreadId;
		int32_t L_2;
		L_2 = Environment_get_CurrentManagedThreadId_m66483AADCCC13272EBDCD94D31D2E52603C24BDF(NULL);
		if ((!(((uint32_t)L_1) == ((uint32_t)L_2))))
		{
			goto IL_0022;
		}
	}
	{
		__this->___U3CU3E1__state = 0;
		V_0 = __this;
		goto IL_0035;
	}

IL_0022:
	{
		U3Cget_ChildrenU3Ed__27_t601D7688734A9685C53234154970690BCEDBCA60* L_3 = (U3Cget_ChildrenU3Ed__27_t601D7688734A9685C53234154970690BCEDBCA60*)il2cpp_codegen_object_new(U3Cget_ChildrenU3Ed__27_t601D7688734A9685C53234154970690BCEDBCA60_il2cpp_TypeInfo_var);
		U3Cget_ChildrenU3Ed__27__ctor_m18A161AD65F41D242EAC8B1AE005D17C47287B05(L_3, 0, NULL);
		V_0 = L_3;
		U3Cget_ChildrenU3Ed__27_t601D7688734A9685C53234154970690BCEDBCA60* L_4 = V_0;
		JSONObject_t8136ED73F77B01FF951AB5A2C9B81C4CD0B5C7B6* L_5 = __this->___U3CU3E4__this;
		NullCheck(L_4);
		L_4->___U3CU3E4__this = L_5;
		Il2CppCodeGenWriteBarrier((void**)(&L_4->___U3CU3E4__this), (void*)L_5);
	}

IL_0035:
	{
		U3Cget_ChildrenU3Ed__27_t601D7688734A9685C53234154970690BCEDBCA60* L_6 = V_0;
		return L_6;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* U3Cget_ChildrenU3Ed__27_System_Collections_IEnumerable_GetEnumerator_mC0508F72CC9FFC3ABB41B4092E7CA66BC28FCF14 (U3Cget_ChildrenU3Ed__27_t601D7688734A9685C53234154970690BCEDBCA60* __this, const RuntimeMethod* method) 
{
	{
		RuntimeObject* L_0;
		L_0 = U3Cget_ChildrenU3Ed__27_System_Collections_Generic_IEnumerableU3CSimpleJSON_JSONNodeU3E_GetEnumerator_m7C7CEB31168871A6A9A9A8BB596BE0714969C4A2(__this, NULL);
		return L_0;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool JSONString_get_IsString_m38AE2283D152FF9AFB1A8DAEA7A63F2D6DC2CB42 (JSONString_tFCFD5D96312BC1A93DA8183AB4B30444DE9B7EB9* __this, const RuntimeMethod* method) 
{
	{
		return (bool)1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* JSONString_get_Value_m4F6921486DA8D4250F58BFAE1D7FF15F01BE2539 (JSONString_tFCFD5D96312BC1A93DA8183AB4B30444DE9B7EB9* __this, const RuntimeMethod* method) 
{
	{
		String_t* L_0 = __this->___m_Data;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void JSONString__ctor_m67139D815079187D65873B84717A70843DD17264 (JSONString_tFCFD5D96312BC1A93DA8183AB4B30444DE9B7EB9* __this, String_t* ___0_aData, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		il2cpp_codegen_runtime_class_init_inline(JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA_il2cpp_TypeInfo_var);
		JSONNode__ctor_mB710A69AF2EDA72D624E6291A6B8438316D5E8DA(__this, NULL);
		String_t* L_0 = ___0_aData;
		__this->___m_Data = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___m_Data), (void*)L_0);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void JSONString_WriteToStringBuilder_m7282552C4625435BA8F36E31F0CE1AF05F29E254 (JSONString_tFCFD5D96312BC1A93DA8183AB4B30444DE9B7EB9* __this, StringBuilder_t* ___0_aSB, int32_t ___1_aIndent, int32_t ___2_aIndentInc, int32_t ___3_aMode, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		StringBuilder_t* L_0 = ___0_aSB;
		NullCheck(L_0);
		StringBuilder_t* L_1;
		L_1 = StringBuilder_Append_m71228B30F05724CD2CD96D9611DCD61BFB96A6E1(L_0, ((int32_t)34), NULL);
		String_t* L_2 = __this->___m_Data;
		il2cpp_codegen_runtime_class_init_inline(JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA_il2cpp_TypeInfo_var);
		String_t* L_3;
		L_3 = JSONNode_Escape_m6646863678DB23BDEB28738CC57E452169FFB0FF(L_2, NULL);
		NullCheck(L_1);
		StringBuilder_t* L_4;
		L_4 = StringBuilder_Append_m08904D74E0C78E5F36DCD9C9303BDD07886D9F7D(L_1, L_3, NULL);
		NullCheck(L_4);
		StringBuilder_t* L_5;
		L_5 = StringBuilder_Append_m71228B30F05724CD2CD96D9611DCD61BFB96A6E1(L_4, ((int32_t)34), NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool JSONString_Equals_mA621194206EDA384DBB2C1AE955C8E9B6E2137A9 (JSONString_tFCFD5D96312BC1A93DA8183AB4B30444DE9B7EB9* __this, RuntimeObject* ___0_obj, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&JSONString_tFCFD5D96312BC1A93DA8183AB4B30444DE9B7EB9_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&String_t_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	String_t* V_0 = NULL;
	JSONString_tFCFD5D96312BC1A93DA8183AB4B30444DE9B7EB9* V_1 = NULL;
	{
		RuntimeObject* L_0 = ___0_obj;
		bool L_1;
		L_1 = JSONNode_Equals_m2F945F67C1B2DB40C080EB1E4CFF37C4B5EF4C73(__this, L_0, NULL);
		if (!L_1)
		{
			goto IL_000b;
		}
	}
	{
		return (bool)1;
	}

IL_000b:
	{
		RuntimeObject* L_2 = ___0_obj;
		V_0 = ((String_t*)IsInstSealed((RuntimeObject*)L_2, String_t_il2cpp_TypeInfo_var));
		String_t* L_3 = V_0;
		if (!L_3)
		{
			goto IL_0022;
		}
	}
	{
		String_t* L_4 = __this->___m_Data;
		String_t* L_5 = V_0;
		bool L_6;
		L_6 = String_op_Equality_m030E1B219352228970A076136E455C4E568C02C1(L_4, L_5, NULL);
		return L_6;
	}

IL_0022:
	{
		RuntimeObject* L_7 = ___0_obj;
		V_1 = ((JSONString_tFCFD5D96312BC1A93DA8183AB4B30444DE9B7EB9*)IsInstClass((RuntimeObject*)L_7, JSONString_tFCFD5D96312BC1A93DA8183AB4B30444DE9B7EB9_il2cpp_TypeInfo_var));
		JSONString_tFCFD5D96312BC1A93DA8183AB4B30444DE9B7EB9* L_8 = V_1;
		il2cpp_codegen_runtime_class_init_inline(JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA_il2cpp_TypeInfo_var);
		bool L_9;
		L_9 = JSONNode_op_Inequality_mF703DC644DDA438C8F56773D20194C2899DDE6F4(L_8, NULL, NULL);
		if (!L_9)
		{
			goto IL_0044;
		}
	}
	{
		String_t* L_10 = __this->___m_Data;
		JSONString_tFCFD5D96312BC1A93DA8183AB4B30444DE9B7EB9* L_11 = V_1;
		NullCheck(L_11);
		String_t* L_12 = L_11->___m_Data;
		bool L_13;
		L_13 = String_op_Equality_m030E1B219352228970A076136E455C4E568C02C1(L_10, L_12, NULL);
		return L_13;
	}

IL_0044:
	{
		return (bool)0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t JSONString_GetHashCode_m87FD358662735E617F9D2326D46E29E2B0E4935D (JSONString_tFCFD5D96312BC1A93DA8183AB4B30444DE9B7EB9* __this, const RuntimeMethod* method) 
{
	{
		String_t* L_0 = __this->___m_Data;
		NullCheck(L_0);
		int32_t L_1;
		L_1 = VirtualFuncInvoker0< int32_t >::Invoke(2, L_0);
		return L_1;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool JSONNumber_get_IsNumber_mCA929E7FD0C4A4B94963549233707158E003CE7D (JSONNumber_t90BD0D71B6F0447E77909AD12B258928EF559DE4* __this, const RuntimeMethod* method) 
{
	{
		return (bool)1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* JSONNumber_get_Value_mB06E9D6AEF55D74A5779A46743C07CD8FCBE702C (JSONNumber_t90BD0D71B6F0447E77909AD12B258928EF559DE4* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&CultureInfo_t9BA817D41AD55AC8BD07480DD8AC22F8FFA378E0_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		double* L_0 = (double*)(&__this->___m_Data);
		il2cpp_codegen_runtime_class_init_inline(CultureInfo_t9BA817D41AD55AC8BD07480DD8AC22F8FFA378E0_il2cpp_TypeInfo_var);
		CultureInfo_t9BA817D41AD55AC8BD07480DD8AC22F8FFA378E0* L_1;
		L_1 = CultureInfo_get_InvariantCulture_mD1E96DC845E34B10F78CB744B0CB5D7D63CEB1E6(NULL);
		String_t* L_2;
		L_2 = Double_ToString_m4318830D9F771852FDCF21C14CF9E8ABC7E77357(L_0, L_1, NULL);
		return L_2;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR double JSONNumber_get_AsDouble_m8D3036913529A244E3945BB07FAC201EE012714D (JSONNumber_t90BD0D71B6F0447E77909AD12B258928EF559DE4* __this, const RuntimeMethod* method) 
{
	{
		double L_0 = __this->___m_Data;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void JSONNumber__ctor_m7E2D0AFEE8AA0DD3E0BD39509764944024821801 (JSONNumber_t90BD0D71B6F0447E77909AD12B258928EF559DE4* __this, double ___0_aData, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		il2cpp_codegen_runtime_class_init_inline(JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA_il2cpp_TypeInfo_var);
		JSONNode__ctor_mB710A69AF2EDA72D624E6291A6B8438316D5E8DA(__this, NULL);
		double L_0 = ___0_aData;
		__this->___m_Data = L_0;
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void JSONNumber_WriteToStringBuilder_m8802E960F7B3BE574FB87E14A16062ECF45AD826 (JSONNumber_t90BD0D71B6F0447E77909AD12B258928EF559DE4* __this, StringBuilder_t* ___0_aSB, int32_t ___1_aIndent, int32_t ___2_aIndentInc, int32_t ___3_aMode, const RuntimeMethod* method) 
{
	{
		StringBuilder_t* L_0 = ___0_aSB;
		String_t* L_1;
		L_1 = VirtualFuncInvoker0< String_t* >::Invoke(5, __this);
		NullCheck(L_0);
		StringBuilder_t* L_2;
		L_2 = StringBuilder_Append_m08904D74E0C78E5F36DCD9C9303BDD07886D9F7D(L_0, L_1, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool JSONNumber_IsNumeric_m9C7E4C675A347170CB4627C5DC33B5AE16FC2855 (RuntimeObject* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Decimal_tDA6C877282B2D789CF97C0949661CC11D643969F_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Double_tE150EF3D1D43DEE85D533810AB4C742307EEDE5F_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Int16_tB8EF286A9C33492FA6E6D6E67320BE93E794A175_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Int64_t092CFB123BE63C28ACDAF65C68F21A526050DBA3_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SByte_tFEFFEF5D2FEBF5207950AE6FAC150FC53B668DB5_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Single_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&UInt16_tF4C148C876015C212FD72652D0B6ED8CC247A455_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&UInt32_t1833D51FFA667B18A5AA4B8D34DE284F8495D29B_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&UInt64_t8F12534CC8FC4B5860F2A2CD1EE79D322E7A41AF_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		RuntimeObject* L_0 = ___0_value;
		if (((RuntimeObject*)IsInstSealed((RuntimeObject*)L_0, Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_il2cpp_TypeInfo_var)))
		{
			goto IL_005a;
		}
	}
	{
		RuntimeObject* L_1 = ___0_value;
		if (((RuntimeObject*)IsInstSealed((RuntimeObject*)L_1, UInt32_t1833D51FFA667B18A5AA4B8D34DE284F8495D29B_il2cpp_TypeInfo_var)))
		{
			goto IL_005a;
		}
	}
	{
		RuntimeObject* L_2 = ___0_value;
		if (((RuntimeObject*)IsInstSealed((RuntimeObject*)L_2, Single_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_il2cpp_TypeInfo_var)))
		{
			goto IL_005a;
		}
	}
	{
		RuntimeObject* L_3 = ___0_value;
		if (((RuntimeObject*)IsInstSealed((RuntimeObject*)L_3, Double_tE150EF3D1D43DEE85D533810AB4C742307EEDE5F_il2cpp_TypeInfo_var)))
		{
			goto IL_005a;
		}
	}
	{
		RuntimeObject* L_4 = ___0_value;
		if (((RuntimeObject*)IsInstSealed((RuntimeObject*)L_4, Decimal_tDA6C877282B2D789CF97C0949661CC11D643969F_il2cpp_TypeInfo_var)))
		{
			goto IL_005a;
		}
	}
	{
		RuntimeObject* L_5 = ___0_value;
		if (((RuntimeObject*)IsInstSealed((RuntimeObject*)L_5, Int64_t092CFB123BE63C28ACDAF65C68F21A526050DBA3_il2cpp_TypeInfo_var)))
		{
			goto IL_005a;
		}
	}
	{
		RuntimeObject* L_6 = ___0_value;
		if (((RuntimeObject*)IsInstSealed((RuntimeObject*)L_6, UInt64_t8F12534CC8FC4B5860F2A2CD1EE79D322E7A41AF_il2cpp_TypeInfo_var)))
		{
			goto IL_005a;
		}
	}
	{
		RuntimeObject* L_7 = ___0_value;
		if (((RuntimeObject*)IsInstSealed((RuntimeObject*)L_7, Int16_tB8EF286A9C33492FA6E6D6E67320BE93E794A175_il2cpp_TypeInfo_var)))
		{
			goto IL_005a;
		}
	}
	{
		RuntimeObject* L_8 = ___0_value;
		if (((RuntimeObject*)IsInstSealed((RuntimeObject*)L_8, UInt16_tF4C148C876015C212FD72652D0B6ED8CC247A455_il2cpp_TypeInfo_var)))
		{
			goto IL_005a;
		}
	}
	{
		RuntimeObject* L_9 = ___0_value;
		if (((RuntimeObject*)IsInstSealed((RuntimeObject*)L_9, SByte_tFEFFEF5D2FEBF5207950AE6FAC150FC53B668DB5_il2cpp_TypeInfo_var)))
		{
			goto IL_005a;
		}
	}
	{
		RuntimeObject* L_10 = ___0_value;
		return (bool)((!(((RuntimeObject*)(RuntimeObject*)((RuntimeObject*)IsInstSealed((RuntimeObject*)L_10, Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var))) <= ((RuntimeObject*)(RuntimeObject*)NULL)))? 1 : 0);
	}

IL_005a:
	{
		return (bool)1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool JSONNumber_Equals_m0D985065CA03D1987E4888485B153869199CE857 (JSONNumber_t90BD0D71B6F0447E77909AD12B258928EF559DE4* __this, RuntimeObject* ___0_obj, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Convert_t7097FF336D592F7C06D88A98349A44646F91EFFC_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&JSONNumber_t90BD0D71B6F0447E77909AD12B258928EF559DE4_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	JSONNumber_t90BD0D71B6F0447E77909AD12B258928EF559DE4* V_0 = NULL;
	{
		RuntimeObject* L_0 = ___0_obj;
		if (L_0)
		{
			goto IL_0005;
		}
	}
	{
		return (bool)0;
	}

IL_0005:
	{
		RuntimeObject* L_1 = ___0_obj;
		bool L_2;
		L_2 = JSONNode_Equals_m2F945F67C1B2DB40C080EB1E4CFF37C4B5EF4C73(__this, L_1, NULL);
		if (!L_2)
		{
			goto IL_0010;
		}
	}
	{
		return (bool)1;
	}

IL_0010:
	{
		RuntimeObject* L_3 = ___0_obj;
		V_0 = ((JSONNumber_t90BD0D71B6F0447E77909AD12B258928EF559DE4*)IsInstClass((RuntimeObject*)L_3, JSONNumber_t90BD0D71B6F0447E77909AD12B258928EF559DE4_il2cpp_TypeInfo_var));
		JSONNumber_t90BD0D71B6F0447E77909AD12B258928EF559DE4* L_4 = V_0;
		il2cpp_codegen_runtime_class_init_inline(JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA_il2cpp_TypeInfo_var);
		bool L_5;
		L_5 = JSONNode_op_Inequality_mF703DC644DDA438C8F56773D20194C2899DDE6F4(L_4, NULL, NULL);
		if (!L_5)
		{
			goto IL_002f;
		}
	}
	{
		double L_6 = __this->___m_Data;
		JSONNumber_t90BD0D71B6F0447E77909AD12B258928EF559DE4* L_7 = V_0;
		NullCheck(L_7);
		double L_8 = L_7->___m_Data;
		return (bool)((((double)L_6) == ((double)L_8))? 1 : 0);
	}

IL_002f:
	{
		RuntimeObject* L_9 = ___0_obj;
		bool L_10;
		L_10 = JSONNumber_IsNumeric_m9C7E4C675A347170CB4627C5DC33B5AE16FC2855(L_9, NULL);
		if (!L_10)
		{
			goto IL_0046;
		}
	}
	{
		RuntimeObject* L_11 = ___0_obj;
		il2cpp_codegen_runtime_class_init_inline(Convert_t7097FF336D592F7C06D88A98349A44646F91EFFC_il2cpp_TypeInfo_var);
		double L_12;
		L_12 = Convert_ToDouble_m86FF4F837721833186E883102C056A35F0860EB0(L_11, NULL);
		double L_13 = __this->___m_Data;
		return (bool)((((double)L_12) == ((double)L_13))? 1 : 0);
	}

IL_0046:
	{
		return (bool)0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t JSONNumber_GetHashCode_m0FCD993684BC73D858AD59CE37551F5A81D94ECC (JSONNumber_t90BD0D71B6F0447E77909AD12B258928EF559DE4* __this, const RuntimeMethod* method) 
{
	{
		double* L_0 = (double*)(&__this->___m_Data);
		int32_t L_1;
		L_1 = Double_GetHashCode_m3761FC05AD24D97A68FA1E8412A9454DF3880E32_inline(L_0, NULL);
		return L_1;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool JSONBool_get_IsBoolean_m34AF989A389A5DD5642767A023EC04F5D36D13EC (JSONBool_t2BBE341953E8A8D293388B59CE964319333E6ECD* __this, const RuntimeMethod* method) 
{
	{
		return (bool)1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* JSONBool_get_Value_m9B2AC32C27A7BFEA9588F6862B2D4A6E9081AF87 (JSONBool_t2BBE341953E8A8D293388B59CE964319333E6ECD* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		bool* L_0 = (bool*)(&__this->___m_Data);
		il2cpp_codegen_runtime_class_init_inline(Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_il2cpp_TypeInfo_var);
		String_t* L_1;
		L_1 = Boolean_ToString_m6646C8026B1DF381A1EE8CD13549175E9703CC63(L_0, NULL);
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool JSONBool_get_AsBool_m86C531CA66E0FBF42AC40EEA37A78D148839FFF9 (JSONBool_t2BBE341953E8A8D293388B59CE964319333E6ECD* __this, const RuntimeMethod* method) 
{
	{
		bool L_0 = __this->___m_Data;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void JSONBool__ctor_mA722148B8765BE496706554AD85D6933240357E5 (JSONBool_t2BBE341953E8A8D293388B59CE964319333E6ECD* __this, bool ___0_aData, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		il2cpp_codegen_runtime_class_init_inline(JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA_il2cpp_TypeInfo_var);
		JSONNode__ctor_mB710A69AF2EDA72D624E6291A6B8438316D5E8DA(__this, NULL);
		bool L_0 = ___0_aData;
		__this->___m_Data = L_0;
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void JSONBool_WriteToStringBuilder_mDEB7BBD882C0FADA8C00EDC2760F087453430C6D (JSONBool_t2BBE341953E8A8D293388B59CE964319333E6ECD* __this, StringBuilder_t* ___0_aSB, int32_t ___1_aIndent, int32_t ___2_aIndentInc, int32_t ___3_aMode, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral77D38C0623F92B292B925F6E72CF5CF99A20D4EB);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralB7C45DD316C68ABF3429C20058C2981C652192F2);
		s_Il2CppMethodInitialized = true;
	}
	StringBuilder_t* G_B2_0 = NULL;
	StringBuilder_t* G_B1_0 = NULL;
	String_t* G_B3_0 = NULL;
	StringBuilder_t* G_B3_1 = NULL;
	{
		StringBuilder_t* L_0 = ___0_aSB;
		bool L_1 = __this->___m_Data;
		if (L_1)
		{
			G_B2_0 = L_0;
			goto IL_0010;
		}
		G_B1_0 = L_0;
	}
	{
		G_B3_0 = _stringLiteral77D38C0623F92B292B925F6E72CF5CF99A20D4EB;
		G_B3_1 = G_B1_0;
		goto IL_0015;
	}

IL_0010:
	{
		G_B3_0 = _stringLiteralB7C45DD316C68ABF3429C20058C2981C652192F2;
		G_B3_1 = G_B2_0;
	}

IL_0015:
	{
		NullCheck(G_B3_1);
		StringBuilder_t* L_2;
		L_2 = StringBuilder_Append_m08904D74E0C78E5F36DCD9C9303BDD07886D9F7D(G_B3_1, G_B3_0, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool JSONBool_Equals_mF18DAF386FA5E7681AB1835AAE979CB444A69BF5 (JSONBool_t2BBE341953E8A8D293388B59CE964319333E6ECD* __this, RuntimeObject* ___0_obj, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		RuntimeObject* L_0 = ___0_obj;
		if (L_0)
		{
			goto IL_0005;
		}
	}
	{
		return (bool)0;
	}

IL_0005:
	{
		RuntimeObject* L_1 = ___0_obj;
		if (!((RuntimeObject*)IsInstSealed((RuntimeObject*)L_1, Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_il2cpp_TypeInfo_var)))
		{
			goto IL_001c;
		}
	}
	{
		bool L_2 = __this->___m_Data;
		RuntimeObject* L_3 = ___0_obj;
		return (bool)((((int32_t)L_2) == ((int32_t)((*(bool*)((bool*)(bool*)UnBox(L_3, Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_il2cpp_TypeInfo_var))))))? 1 : 0);
	}

IL_001c:
	{
		return (bool)0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t JSONBool_GetHashCode_mDF7AE67A5499A3C6FFFCE284AFC9CAE237C83DE4 (JSONBool_t2BBE341953E8A8D293388B59CE964319333E6ECD* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		bool* L_0 = (bool*)(&__this->___m_Data);
		il2cpp_codegen_runtime_class_init_inline(Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_il2cpp_TypeInfo_var);
		int32_t L_1;
		L_1 = Boolean_GetHashCode_mEDB6904770C962BAF4510E5D24F08083C33900E3(L_0, NULL);
		return L_1;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR JSONNull_tE058D16E7011140DB8F406485C84D3D51DA7A97D* JSONNull_CreateOrGet_m920C3D38D052C4C8EDE996476F91D9913B6017F2 (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&JSONNull_tE058D16E7011140DB8F406485C84D3D51DA7A97D_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		il2cpp_codegen_runtime_class_init_inline(JSONNull_tE058D16E7011140DB8F406485C84D3D51DA7A97D_il2cpp_TypeInfo_var);
		bool L_0 = ((JSONNull_tE058D16E7011140DB8F406485C84D3D51DA7A97D_StaticFields*)il2cpp_codegen_static_fields_for(JSONNull_tE058D16E7011140DB8F406485C84D3D51DA7A97D_il2cpp_TypeInfo_var))->___reuseSameInstance;
		if (!L_0)
		{
			goto IL_000d;
		}
	}
	{
		il2cpp_codegen_runtime_class_init_inline(JSONNull_tE058D16E7011140DB8F406485C84D3D51DA7A97D_il2cpp_TypeInfo_var);
		JSONNull_tE058D16E7011140DB8F406485C84D3D51DA7A97D* L_1 = ((JSONNull_tE058D16E7011140DB8F406485C84D3D51DA7A97D_StaticFields*)il2cpp_codegen_static_fields_for(JSONNull_tE058D16E7011140DB8F406485C84D3D51DA7A97D_il2cpp_TypeInfo_var))->___m_StaticInstance;
		return L_1;
	}

IL_000d:
	{
		JSONNull_tE058D16E7011140DB8F406485C84D3D51DA7A97D* L_2 = (JSONNull_tE058D16E7011140DB8F406485C84D3D51DA7A97D*)il2cpp_codegen_object_new(JSONNull_tE058D16E7011140DB8F406485C84D3D51DA7A97D_il2cpp_TypeInfo_var);
		JSONNull__ctor_m483EFD80DA95F906B6F966CB6FC63ED8194457C4(L_2, NULL);
		return L_2;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void JSONNull__ctor_m483EFD80DA95F906B6F966CB6FC63ED8194457C4 (JSONNull_tE058D16E7011140DB8F406485C84D3D51DA7A97D* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		il2cpp_codegen_runtime_class_init_inline(JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA_il2cpp_TypeInfo_var);
		JSONNode__ctor_mB710A69AF2EDA72D624E6291A6B8438316D5E8DA(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* JSONNull_get_Value_m7D9880D1190697362F228A228D9A4A0CA435A47F (JSONNull_tE058D16E7011140DB8F406485C84D3D51DA7A97D* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral5BEFD8CC60A79699B5BB00E37BAC5B62D371E174);
		s_Il2CppMethodInitialized = true;
	}
	{
		return _stringLiteral5BEFD8CC60A79699B5BB00E37BAC5B62D371E174;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool JSONNull_get_AsBool_m13FF2D4FD1899F9DB55108C65BFB2E4E8CFB529B (JSONNull_tE058D16E7011140DB8F406485C84D3D51DA7A97D* __this, const RuntimeMethod* method) 
{
	{
		return (bool)0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool JSONNull_Equals_m1E77C8048155561039974995F7B113CEBD157326 (JSONNull_tE058D16E7011140DB8F406485C84D3D51DA7A97D* __this, RuntimeObject* ___0_obj, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&JSONNull_tE058D16E7011140DB8F406485C84D3D51DA7A97D_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		RuntimeObject* L_0 = ___0_obj;
		if ((!(((RuntimeObject*)(JSONNull_tE058D16E7011140DB8F406485C84D3D51DA7A97D*)__this) == ((RuntimeObject*)(RuntimeObject*)L_0))))
		{
			goto IL_0006;
		}
	}
	{
		return (bool)1;
	}

IL_0006:
	{
		RuntimeObject* L_1 = ___0_obj;
		return (bool)((!(((RuntimeObject*)(JSONNull_tE058D16E7011140DB8F406485C84D3D51DA7A97D*)((JSONNull_tE058D16E7011140DB8F406485C84D3D51DA7A97D*)IsInstClass((RuntimeObject*)L_1, JSONNull_tE058D16E7011140DB8F406485C84D3D51DA7A97D_il2cpp_TypeInfo_var))) <= ((RuntimeObject*)(RuntimeObject*)NULL)))? 1 : 0);
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t JSONNull_GetHashCode_m04E8A7D1385318506DD21C50D7A38E7889357961 (JSONNull_tE058D16E7011140DB8F406485C84D3D51DA7A97D* __this, const RuntimeMethod* method) 
{
	{
		return 0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void JSONNull_WriteToStringBuilder_m4701985E7E74C6658669A3B688CBBA20BFD7DC79 (JSONNull_tE058D16E7011140DB8F406485C84D3D51DA7A97D* __this, StringBuilder_t* ___0_aSB, int32_t ___1_aIndent, int32_t ___2_aIndentInc, int32_t ___3_aMode, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral5BEFD8CC60A79699B5BB00E37BAC5B62D371E174);
		s_Il2CppMethodInitialized = true;
	}
	{
		StringBuilder_t* L_0 = ___0_aSB;
		NullCheck(L_0);
		StringBuilder_t* L_1;
		L_1 = StringBuilder_Append_m08904D74E0C78E5F36DCD9C9303BDD07886D9F7D(L_0, _stringLiteral5BEFD8CC60A79699B5BB00E37BAC5B62D371E174, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void JSONNull__cctor_mEDAD9F5CCEF36466C93CC4AD6B3F3F93A6E73815 (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&JSONNull_tE058D16E7011140DB8F406485C84D3D51DA7A97D_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		JSONNull_tE058D16E7011140DB8F406485C84D3D51DA7A97D* L_0 = (JSONNull_tE058D16E7011140DB8F406485C84D3D51DA7A97D*)il2cpp_codegen_object_new(JSONNull_tE058D16E7011140DB8F406485C84D3D51DA7A97D_il2cpp_TypeInfo_var);
		JSONNull__ctor_m483EFD80DA95F906B6F966CB6FC63ED8194457C4(L_0, NULL);
		((JSONNull_tE058D16E7011140DB8F406485C84D3D51DA7A97D_StaticFields*)il2cpp_codegen_static_fields_for(JSONNull_tE058D16E7011140DB8F406485C84D3D51DA7A97D_il2cpp_TypeInfo_var))->___m_StaticInstance = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&((JSONNull_tE058D16E7011140DB8F406485C84D3D51DA7A97D_StaticFields*)il2cpp_codegen_static_fields_for(JSONNull_tE058D16E7011140DB8F406485C84D3D51DA7A97D_il2cpp_TypeInfo_var))->___m_StaticInstance), (void*)L_0);
		((JSONNull_tE058D16E7011140DB8F406485C84D3D51DA7A97D_StaticFields*)il2cpp_codegen_static_fields_for(JSONNull_tE058D16E7011140DB8F406485C84D3D51DA7A97D_il2cpp_TypeInfo_var))->___reuseSameInstance = (bool)1;
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void JSONLazyCreator__ctor_m5A3DAB47229CD45650E935BDB7D0C9FACA460720 (JSONLazyCreator_t93B398608BA0717C413F6875922A3BF9C7ABD8B9* __this, JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* ___0_aNode, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		il2cpp_codegen_runtime_class_init_inline(JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA_il2cpp_TypeInfo_var);
		JSONNode__ctor_mB710A69AF2EDA72D624E6291A6B8438316D5E8DA(__this, NULL);
		JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* L_0 = ___0_aNode;
		__this->___m_Node = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___m_Node), (void*)L_0);
		__this->___m_Key = (String_t*)NULL;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___m_Key), (void*)(String_t*)NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void JSONLazyCreator__ctor_m3965360CD8D0DEA5E2ED0B3E4981936534F30C93 (JSONLazyCreator_t93B398608BA0717C413F6875922A3BF9C7ABD8B9* __this, JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* ___0_aNode, String_t* ___1_aKey, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		il2cpp_codegen_runtime_class_init_inline(JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA_il2cpp_TypeInfo_var);
		JSONNode__ctor_mB710A69AF2EDA72D624E6291A6B8438316D5E8DA(__this, NULL);
		JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* L_0 = ___0_aNode;
		__this->___m_Node = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___m_Node), (void*)L_0);
		String_t* L_1 = ___1_aKey;
		__this->___m_Key = L_1;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___m_Key), (void*)L_1);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* JSONLazyCreator_get_Item_m999F0D4444F6FFDA7CB8EAEE6BCE2C3CE0776EAA (JSONLazyCreator_t93B398608BA0717C413F6875922A3BF9C7ABD8B9* __this, String_t* ___0_aKey, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&JSONLazyCreator_t93B398608BA0717C413F6875922A3BF9C7ABD8B9_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		String_t* L_0 = ___0_aKey;
		JSONLazyCreator_t93B398608BA0717C413F6875922A3BF9C7ABD8B9* L_1 = (JSONLazyCreator_t93B398608BA0717C413F6875922A3BF9C7ABD8B9*)il2cpp_codegen_object_new(JSONLazyCreator_t93B398608BA0717C413F6875922A3BF9C7ABD8B9_il2cpp_TypeInfo_var);
		JSONLazyCreator__ctor_m3965360CD8D0DEA5E2ED0B3E4981936534F30C93(L_1, __this, L_0, NULL);
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void JSONLazyCreator_Add_m354FAEC031F11A8DB900BE757DA40FE84F0AB380 (JSONLazyCreator_t93B398608BA0717C413F6875922A3BF9C7ABD8B9* __this, JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* ___0_aItem, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&JSONArray_tE863C8377E61FEEC9ACA640D090C176C7CDCB589_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&JSONLazyCreator_Set_TisJSONArray_tE863C8377E61FEEC9ACA640D090C176C7CDCB589_m4DFE3CBE4BC89F56E51DE5A97EE67B6A1CC4B7FE_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		JSONArray_tE863C8377E61FEEC9ACA640D090C176C7CDCB589* L_0 = (JSONArray_tE863C8377E61FEEC9ACA640D090C176C7CDCB589*)il2cpp_codegen_object_new(JSONArray_tE863C8377E61FEEC9ACA640D090C176C7CDCB589_il2cpp_TypeInfo_var);
		JSONArray__ctor_mD53A24956E3512E4F0F8989651E9E8BA8B2257B8(L_0, NULL);
		JSONArray_tE863C8377E61FEEC9ACA640D090C176C7CDCB589* L_1;
		L_1 = JSONLazyCreator_Set_TisJSONArray_tE863C8377E61FEEC9ACA640D090C176C7CDCB589_m4DFE3CBE4BC89F56E51DE5A97EE67B6A1CC4B7FE(__this, L_0, JSONLazyCreator_Set_TisJSONArray_tE863C8377E61FEEC9ACA640D090C176C7CDCB589_m4DFE3CBE4BC89F56E51DE5A97EE67B6A1CC4B7FE_RuntimeMethod_var);
		JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* L_2 = ___0_aItem;
		NullCheck(L_1);
		VirtualActionInvoker1< JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* >::Invoke(14, L_1, L_2);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void JSONLazyCreator_Add_mECF09D05C3B117E3DC340522EBC1BE0D9D990BB5 (JSONLazyCreator_t93B398608BA0717C413F6875922A3BF9C7ABD8B9* __this, String_t* ___0_aKey, JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* ___1_aItem, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&JSONLazyCreator_Set_TisJSONObject_t8136ED73F77B01FF951AB5A2C9B81C4CD0B5C7B6_m086A73FD912BB396452D77E526AC375844AABFD3_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&JSONObject_t8136ED73F77B01FF951AB5A2C9B81C4CD0B5C7B6_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		JSONObject_t8136ED73F77B01FF951AB5A2C9B81C4CD0B5C7B6* L_0 = (JSONObject_t8136ED73F77B01FF951AB5A2C9B81C4CD0B5C7B6*)il2cpp_codegen_object_new(JSONObject_t8136ED73F77B01FF951AB5A2C9B81C4CD0B5C7B6_il2cpp_TypeInfo_var);
		JSONObject__ctor_mD4F6D4B7B8D8D3F4FCF45B4BAC32AEB66588307A(L_0, NULL);
		JSONObject_t8136ED73F77B01FF951AB5A2C9B81C4CD0B5C7B6* L_1;
		L_1 = JSONLazyCreator_Set_TisJSONObject_t8136ED73F77B01FF951AB5A2C9B81C4CD0B5C7B6_m086A73FD912BB396452D77E526AC375844AABFD3(__this, L_0, JSONLazyCreator_Set_TisJSONObject_t8136ED73F77B01FF951AB5A2C9B81C4CD0B5C7B6_m086A73FD912BB396452D77E526AC375844AABFD3_RuntimeMethod_var);
		String_t* L_2 = ___0_aKey;
		JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* L_3 = ___1_aItem;
		NullCheck(L_1);
		VirtualActionInvoker2< String_t*, JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* >::Invoke(13, L_1, L_2, L_3);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool JSONLazyCreator_Equals_mEF2829A9E6DB5B8EF84C8172CF806670DEE23E94 (JSONLazyCreator_t93B398608BA0717C413F6875922A3BF9C7ABD8B9* __this, RuntimeObject* ___0_obj, const RuntimeMethod* method) 
{
	{
		RuntimeObject* L_0 = ___0_obj;
		if (L_0)
		{
			goto IL_0005;
		}
	}
	{
		return (bool)1;
	}

IL_0005:
	{
		RuntimeObject* L_1 = ___0_obj;
		return (bool)((((RuntimeObject*)(JSONLazyCreator_t93B398608BA0717C413F6875922A3BF9C7ABD8B9*)__this) == ((RuntimeObject*)(RuntimeObject*)L_1))? 1 : 0);
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t JSONLazyCreator_GetHashCode_mF2FA6B801AB1CF016D02F81864A3C6192D81BE47 (JSONLazyCreator_t93B398608BA0717C413F6875922A3BF9C7ABD8B9* __this, const RuntimeMethod* method) 
{
	{
		return 0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t JSONLazyCreator_get_AsInt_mA829B114ECDF70080E7493F397C64EE33527ADB5 (JSONLazyCreator_t93B398608BA0717C413F6875922A3BF9C7ABD8B9* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&JSONLazyCreator_Set_TisJSONNumber_t90BD0D71B6F0447E77909AD12B258928EF559DE4_m6C57C23A870484E26456D2D79A770C47A63DD218_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&JSONNumber_t90BD0D71B6F0447E77909AD12B258928EF559DE4_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		JSONNumber_t90BD0D71B6F0447E77909AD12B258928EF559DE4* L_0 = (JSONNumber_t90BD0D71B6F0447E77909AD12B258928EF559DE4*)il2cpp_codegen_object_new(JSONNumber_t90BD0D71B6F0447E77909AD12B258928EF559DE4_il2cpp_TypeInfo_var);
		JSONNumber__ctor_m7E2D0AFEE8AA0DD3E0BD39509764944024821801(L_0, (0.0), NULL);
		JSONNumber_t90BD0D71B6F0447E77909AD12B258928EF559DE4* L_1;
		L_1 = JSONLazyCreator_Set_TisJSONNumber_t90BD0D71B6F0447E77909AD12B258928EF559DE4_m6C57C23A870484E26456D2D79A770C47A63DD218(__this, L_0, JSONLazyCreator_Set_TisJSONNumber_t90BD0D71B6F0447E77909AD12B258928EF559DE4_m6C57C23A870484E26456D2D79A770C47A63DD218_RuntimeMethod_var);
		return 0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float JSONLazyCreator_get_AsFloat_m82BA70F1AA960ABC9EB0D7DFC1373DCF01A12115 (JSONLazyCreator_t93B398608BA0717C413F6875922A3BF9C7ABD8B9* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&JSONLazyCreator_Set_TisJSONNumber_t90BD0D71B6F0447E77909AD12B258928EF559DE4_m6C57C23A870484E26456D2D79A770C47A63DD218_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&JSONNumber_t90BD0D71B6F0447E77909AD12B258928EF559DE4_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		JSONNumber_t90BD0D71B6F0447E77909AD12B258928EF559DE4* L_0 = (JSONNumber_t90BD0D71B6F0447E77909AD12B258928EF559DE4*)il2cpp_codegen_object_new(JSONNumber_t90BD0D71B6F0447E77909AD12B258928EF559DE4_il2cpp_TypeInfo_var);
		JSONNumber__ctor_m7E2D0AFEE8AA0DD3E0BD39509764944024821801(L_0, (0.0), NULL);
		JSONNumber_t90BD0D71B6F0447E77909AD12B258928EF559DE4* L_1;
		L_1 = JSONLazyCreator_Set_TisJSONNumber_t90BD0D71B6F0447E77909AD12B258928EF559DE4_m6C57C23A870484E26456D2D79A770C47A63DD218(__this, L_0, JSONLazyCreator_Set_TisJSONNumber_t90BD0D71B6F0447E77909AD12B258928EF559DE4_m6C57C23A870484E26456D2D79A770C47A63DD218_RuntimeMethod_var);
		return (0.0f);
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR double JSONLazyCreator_get_AsDouble_mD19160DECA505875AC5A6A9BF3586839103DFBD8 (JSONLazyCreator_t93B398608BA0717C413F6875922A3BF9C7ABD8B9* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&JSONLazyCreator_Set_TisJSONNumber_t90BD0D71B6F0447E77909AD12B258928EF559DE4_m6C57C23A870484E26456D2D79A770C47A63DD218_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&JSONNumber_t90BD0D71B6F0447E77909AD12B258928EF559DE4_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		JSONNumber_t90BD0D71B6F0447E77909AD12B258928EF559DE4* L_0 = (JSONNumber_t90BD0D71B6F0447E77909AD12B258928EF559DE4*)il2cpp_codegen_object_new(JSONNumber_t90BD0D71B6F0447E77909AD12B258928EF559DE4_il2cpp_TypeInfo_var);
		JSONNumber__ctor_m7E2D0AFEE8AA0DD3E0BD39509764944024821801(L_0, (0.0), NULL);
		JSONNumber_t90BD0D71B6F0447E77909AD12B258928EF559DE4* L_1;
		L_1 = JSONLazyCreator_Set_TisJSONNumber_t90BD0D71B6F0447E77909AD12B258928EF559DE4_m6C57C23A870484E26456D2D79A770C47A63DD218(__this, L_0, JSONLazyCreator_Set_TisJSONNumber_t90BD0D71B6F0447E77909AD12B258928EF559DE4_m6C57C23A870484E26456D2D79A770C47A63DD218_RuntimeMethod_var);
		return (0.0);
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool JSONLazyCreator_get_AsBool_mE990672E6EDD401179F238904B1F87454BD89E13 (JSONLazyCreator_t93B398608BA0717C413F6875922A3BF9C7ABD8B9* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&JSONBool_t2BBE341953E8A8D293388B59CE964319333E6ECD_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&JSONLazyCreator_Set_TisJSONBool_t2BBE341953E8A8D293388B59CE964319333E6ECD_mCB9B55A39FD54AD56A8C8DB28060A98E4938171D_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		JSONBool_t2BBE341953E8A8D293388B59CE964319333E6ECD* L_0 = (JSONBool_t2BBE341953E8A8D293388B59CE964319333E6ECD*)il2cpp_codegen_object_new(JSONBool_t2BBE341953E8A8D293388B59CE964319333E6ECD_il2cpp_TypeInfo_var);
		JSONBool__ctor_mA722148B8765BE496706554AD85D6933240357E5(L_0, (bool)0, NULL);
		JSONBool_t2BBE341953E8A8D293388B59CE964319333E6ECD* L_1;
		L_1 = JSONLazyCreator_Set_TisJSONBool_t2BBE341953E8A8D293388B59CE964319333E6ECD_mCB9B55A39FD54AD56A8C8DB28060A98E4938171D(__this, L_0, JSONLazyCreator_Set_TisJSONBool_t2BBE341953E8A8D293388B59CE964319333E6ECD_mCB9B55A39FD54AD56A8C8DB28060A98E4938171D_RuntimeMethod_var);
		return (bool)0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void JSONLazyCreator_WriteToStringBuilder_m39D5E30A28729B6533251B914C20B4A30413270E (JSONLazyCreator_t93B398608BA0717C413F6875922A3BF9C7ABD8B9* __this, StringBuilder_t* ___0_aSB, int32_t ___1_aIndent, int32_t ___2_aIndentInc, int32_t ___3_aMode, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral5BEFD8CC60A79699B5BB00E37BAC5B62D371E174);
		s_Il2CppMethodInitialized = true;
	}
	{
		StringBuilder_t* L_0 = ___0_aSB;
		NullCheck(L_0);
		StringBuilder_t* L_1;
		L_1 = StringBuilder_Append_m08904D74E0C78E5F36DCD9C9303BDD07886D9F7D(L_0, _stringLiteral5BEFD8CC60A79699B5BB00E37BAC5B62D371E174, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* JSON_Parse_mA779EFEA71F2DEEED3D0DECEFFCFA70197A72521 (String_t* ___0_aJSON, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		String_t* L_0 = ___0_aJSON;
		il2cpp_codegen_runtime_class_init_inline(JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA_il2cpp_TypeInfo_var);
		JSONNode_t971AD14718D1E9D3B7242FC4E314DA952A7B39AA* L_1;
		L_1 = JSONNode_Parse_mADBE0CF264E68AFDA1B37FA20CB5FDAFD89134DB(L_0, NULL);
		return L_1;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BeanBase__ctor_mAA920112A0185B23CCF0A8A06CCDF346C019ECB4 (BeanBase_tE882EE5D0CEB6587A1AB19056B5EA531600AAD67* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SerializationException__ctor_mE7CB71876F77ED9C1885672F7A42FC0777E595AA (SerializationException_t9BAC85A4408413811FD4394F0919F478DC33294B* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Exception_t_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		il2cpp_codegen_runtime_class_init_inline(Exception_t_il2cpp_TypeInfo_var);
		Exception__ctor_m203319D1EA1274689B380A947B4ADC8445662B8F(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t String_get_Length_m42625D67623FA5CC7A44D47425CE86FB946542D2_inline (String_t* __this, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = __this->____stringLength;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t Double_GetHashCode_m3761FC05AD24D97A68FA1E8412A9454DF3880E32_inline (double* __this, const RuntimeMethod* method) 
{
	int64_t V_0 = 0;
	{
		double L_0 = *((double*)__this);
		int64_t L_1;
		L_1 = BitConverter_DoubleToInt64Bits_m4F42741818550F9956B5FBAF88C051F4DE5B0AE6_inline(L_0, NULL);
		V_0 = L_1;
		int64_t L_2 = V_0;
		if ((((int64_t)((int64_t)(((int64_t)il2cpp_codegen_subtract(L_2, ((int64_t)1)))&((int64_t)(std::numeric_limits<int64_t>::max)())))) < ((int64_t)((int64_t)9218868437227405312LL))))
		{
			goto IL_002d;
		}
	}
	{
		int64_t L_3 = V_0;
		V_0 = ((int64_t)(L_3&((int64_t)9218868437227405312LL)));
	}

IL_002d:
	{
		int64_t L_4 = V_0;
		int64_t L_5 = V_0;
		return ((int32_t)(((int32_t)L_4)^((int32_t)((int64_t)(L_5>>((int32_t)32))))));
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t Stack_1_get_Count_mD08AE71D49787D30DDD9D484BCD323D646744D2E_gshared_inline (Stack_1_tAD790A47551563636908E21E4F08C54C0C323EB5* __this, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = __this->____size;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t List_1_get_Count_m4407E4C389F22B8CEC282C15D56516658746C383_gshared_inline (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D* __this, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = __this->____size;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void List_1_Add_mEBCF994CC3814631017F46A387B1A192ED6C85C7_gshared_inline (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D* __this, RuntimeObject* ___0_item, const RuntimeMethod* method) 
{
	ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* V_0 = NULL;
	int32_t V_1 = 0;
	{
		int32_t L_0 = __this->____version;
		__this->____version = ((int32_t)il2cpp_codegen_add(L_0, 1));
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_1 = __this->____items;
		V_0 = L_1;
		int32_t L_2 = __this->____size;
		V_1 = L_2;
		int32_t L_3 = V_1;
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_4 = V_0;
		NullCheck(L_4);
		if ((!(((uint32_t)L_3) < ((uint32_t)((int32_t)(((RuntimeArray*)L_4)->max_length))))))
		{
			goto IL_0034;
		}
	}
	{
		int32_t L_5 = V_1;
		__this->____size = ((int32_t)il2cpp_codegen_add(L_5, 1));
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_6 = V_0;
		int32_t L_7 = V_1;
		RuntimeObject* L_8 = ___0_item;
		NullCheck(L_6);
		(L_6)->SetAt(static_cast<il2cpp_array_size_t>(L_7), (RuntimeObject*)L_8);
		return;
	}

IL_0034:
	{
		RuntimeObject* L_9 = ___0_item;
		List_1_AddWithResize_m79A9BF770BEF9C06BE40D5401E55E375F2726CC4(__this, L_9, il2cpp_rgctx_method(method->klass->rgctx_data, 14));
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR RuntimeObject* Enumerator_get_Current_m6330F15D18EE4F547C05DF9BF83C5EB710376027_gshared_inline (Enumerator_t9473BAB568A27E2339D48C1F91319E0F6D244D7A* __this, const RuntimeMethod* method) 
{
	{
		RuntimeObject* L_0 = __this->____current;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR KeyValuePair_2_tFC32D2507216293851350D29B64D79F950B55230 Enumerator_get_Current_mE3475384B761E1C7971D3639BD09117FE8363422_gshared_inline (Enumerator_tEA93FE2B778D098F590CA168BEFC4CD85D73A6B9* __this, const RuntimeMethod* method) 
{
	{
		KeyValuePair_2_tFC32D2507216293851350D29B64D79F950B55230 L_0 = __this->____current;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR RuntimeObject* KeyValuePair_2_get_Key_mBD8EA7557C27E6956F2AF29DA3F7499B2F51A282_gshared_inline (KeyValuePair_2_tFC32D2507216293851350D29B64D79F950B55230* __this, const RuntimeMethod* method) 
{
	{
		RuntimeObject* L_0 = __this->___key;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR RuntimeObject* KeyValuePair_2_get_Value_mC6BD8075F9C9DDEF7B4D731E5C38EC19103988E7_gshared_inline (KeyValuePair_2_tFC32D2507216293851350D29B64D79F950B55230* __this, const RuntimeMethod* method) 
{
	{
		RuntimeObject* L_0 = __this->___value;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int64_t BitConverter_DoubleToInt64Bits_m4F42741818550F9956B5FBAF88C051F4DE5B0AE6_inline (double ___0_value, const RuntimeMethod* method) 
{
	{
		int64_t L_0 = *((int64_t*)((uintptr_t)(&___0_value)));
		return L_0;
	}
}

﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void SR_Format_m271F5A0AF597B1B86AAAFCEF15FC12BDA0C04468 (void);
extern void CopyEncoder_GetBlock_m19B917E27CAE49A3A2C280DB03C7672C326DD659 (void);
extern void CopyEncoder_WriteLenNLen_mCCB24BFD617911FA48996D87C54A76A22CFB921D (void);
extern void DeflateInput_get_Buffer_m9F3AD0BCF828824646D283A1DE5BDEB1F34B3F2F (void);
extern void DeflateInput_set_Buffer_m2366CB2FB1F51BFDC35D40E0C7E0E3EEA165FE0B (void);
extern void DeflateInput_get_Count_m07CF313093713EA551824415A986FE04136BA3D2 (void);
extern void DeflateInput_set_Count_m25E5676842F9BE403E4E2DBE259BFE428DB78B5A (void);
extern void DeflateInput_get_StartIndex_mF0BAC57BB16D5B0493507E27A650DBDD65102779 (void);
extern void DeflateInput_set_StartIndex_mF699B5A9E52487FB89E6BB77DE6E8182AD36670F (void);
extern void DeflateInput_ConsumeBytes_mDA15E6EB947020F2C7D4E3EE2DE6DD971A8ECDC4 (void);
extern void DeflateInput_DumpState_mC617FD41FD589B6C8F6DDC4048AF70A2A5971A8B (void);
extern void DeflateInput_RestoreState_m8F8FD83E53E2538D242CA631E3EB90B90B04A1D2 (void);
extern void DeflateInput__ctor_m3A5CDB0D7B76657D446A20E6175C5A507D102F03 (void);
extern void InputState__ctor_m9F37B404CBF38DB44663775A9B42846F68FDF0ED (void);
extern void DeflateManagedStream__ctor_mB0B8610BA00510F162BEBDE6D466623DF8B93BF7 (void);
extern void DeflateManagedStream_InitializeInflater_m92F2F626C323D45F5D9A6DBBB4C120A13DEF961F (void);
extern void DeflateManagedStream_get_CanRead_mCF994410F491C7F488E0CDBB35865349642CB5AB (void);
extern void DeflateManagedStream_get_CanWrite_mDE69F8F6FCA416C1AE7B2B073F61B7BD7DDCFB9F (void);
extern void DeflateManagedStream_get_CanSeek_m17F64890C4B012C90F8649265040B4724FFB1495 (void);
extern void DeflateManagedStream_get_Length_m2D9B918EDCA9F9DA597047E19C5074B2C9032ED4 (void);
extern void DeflateManagedStream_get_Position_m2B690044BA869ADBCFA0611261D45528B24C0B78 (void);
extern void DeflateManagedStream_set_Position_m1D967D9DCCFF88B96B221EBAF5E0C40FE85C0A80 (void);
extern void DeflateManagedStream_Flush_m678D8DCD6126270BD29616BD473DFC0B3EEF1222 (void);
extern void DeflateManagedStream_Seek_mD9D64344A632B854C36AD411A894B6355F598556 (void);
extern void DeflateManagedStream_SetLength_m4B89B2D809203CBD19ED10DF8C1CB1041BCB241F (void);
extern void DeflateManagedStream_Read_m61432BDD2A0C63A26A0D999795841210E7B1C988 (void);
extern void DeflateManagedStream_ValidateParameters_m5059BD392464410E5B046B7CDBE8DBE1BBEE7A9D (void);
extern void DeflateManagedStream_EnsureNotDisposed_m9F9FEC94DB9DBA9B0A5473F253A25BB76224A819 (void);
extern void DeflateManagedStream_ThrowStreamClosedException_mBA396FFC3EDE93DB8F7AF1C6FAA999E2C211AE10 (void);
extern void DeflateManagedStream_EnsureDecompressionMode_m83D428E3974F52B13F55FCF19EF68C64E831BE7D (void);
extern void DeflateManagedStream_ThrowCannotReadFromDeflateManagedStreamException_m8B3A68B1C5EFD7D02E09FFE6098EA579CE2BCDAD (void);
extern void DeflateManagedStream_EnsureCompressionMode_mAB2118A2D4256FC974EBA6A3C62665667700E37B (void);
extern void DeflateManagedStream_ThrowCannotWriteToDeflateManagedStreamException_m992ADAD121F58DE82AD36D6E49916C61B75EA277 (void);
extern void DeflateManagedStream_BeginRead_m7AACB982B4250162FECF46F9C4DCC4C1E05C572A (void);
extern void DeflateManagedStream_EndRead_m0147192926C251260A7F026E3A0AE14F8C8E59F4 (void);
extern void DeflateManagedStream_ReadAsync_m8A5540EB38FBA7DF6CA9F5A5005DDECD18665594 (void);
extern void DeflateManagedStream_ReadAsyncCore_m0EB7A8BEA37E47D35CE950BCA9FF9B236ED2B43A (void);
extern void DeflateManagedStream_Write_m154CD9DC5E7AFE4ADFFF5D23D15CAE462A57BAB0 (void);
extern void DeflateManagedStream_WriteDeflaterOutput_m15EB635AE22CC4DFE241249D90EABD6FF5FE49AA (void);
extern void DeflateManagedStream_DoMaintenance_m843B9BE156173B2CB7B11D25DA1ED48F3BD56238 (void);
extern void DeflateManagedStream_PurgeBuffers_mE668E8906372479A972F3FE24DAD12E6BFFE0B49 (void);
extern void DeflateManagedStream_Dispose_m8AFF110FB255CD2FE5302ABAC3D6D5BC2B1CB24B (void);
extern void DeflateManagedStream_WriteAsync_m21BA2B55BF37ABE24FB6FE13760FD3429B702E67 (void);
extern void DeflateManagedStream_WriteAsyncCore_mA0DF62D8656774394A7C291AF9307FC317336C7A (void);
extern void DeflateManagedStream_BeginWrite_m10D5C0E489EF74578C8982F264BE345A2C63602B (void);
extern void DeflateManagedStream_EndWrite_m02E69ABDB817734A1F59D6A013121B1B364B1BAC (void);
extern void DeflateManagedStream_U3CU3En__0_mD49534E1CB38B7CC9F740A310565430CDF685F52 (void);
extern void U3CReadAsyncCoreU3Ed__40_MoveNext_m7DE3C87E7BCC9DAC07D7DE47C989D764C8D5269D (void);
extern void U3CReadAsyncCoreU3Ed__40_SetStateMachine_m809D8FF0E75647CDA4B31E3C430FDAEB936B6B71 (void);
extern void U3CWriteAsyncCoreU3Ed__47_MoveNext_m37704F9AAAB247E7FBB28D5FC57E9C729C5D132A (void);
extern void U3CWriteAsyncCoreU3Ed__47_SetStateMachine_m7A3A2B8F2706E9E301A473AACFCAE8B675FBF2F2 (void);
extern void DeflaterManaged_NeedsInput_m80E06DDF7D1FF28B38B2D7428AF0F30B9087C714 (void);
extern void DeflaterManaged_SetInput_mDC0D32D6E849B5AF86C9E2B78D27873357ADCCC1 (void);
extern void DeflaterManaged_GetDeflateOutput_mAA90AD2E8080DDAB639B01E32B1B7F9BA7BCD73F (void);
extern void DeflaterManaged_Finish_mC1E0F1E1EA9546AB32816ECAFCF73CE4CBEA5CDE (void);
extern void DeflaterManaged_UseCompressed_m0A440E14E58AD1952B7B7ECCB8D196F7EED857E5 (void);
extern void DeflaterManaged_FlushInputWindows_mF82B88248066D040B4565C838B0DB66296908BA4 (void);
extern void DeflaterManaged_WriteFinal_m6D42270C9B4F94A20F44561E55B6EED0E575B492 (void);
extern void DeflaterManaged_Dispose_mA33D6301ED85EFB43656A778D2495CB82D170900 (void);
extern void FastEncoder_get_BytesInHistory_mFC7283021C2CF56CD76F65EC31D451A82EA24AA7 (void);
extern void FastEncoder_get_UnprocessedInput_mB235482CF82A7B80D48F02669B0DDD6F91385C1C (void);
extern void FastEncoder_FlushInput_m54EAC91A657CD4521EA17D2D54CA8782B21AB520 (void);
extern void FastEncoder_get_LastCompressionRatio_mC220B5DFFF5D61E79EDD5E6D4E25B2F6090301B5 (void);
extern void FastEncoder_GetBlock_mB5798B6567FF92DE531EF982B72C6BD6E7AC8ABE (void);
extern void FastEncoder_GetCompressedData_mD43E12AC3D6651340FB98F1EFC7193C5EB151EFA (void);
extern void FastEncoder_GetBlockHeader_m364914F16B71A20BEE90A3F6519115138C56EF00 (void);
extern void FastEncoder_GetBlockFooter_m39A255EC4602C5BBFD78C5CC1D7D0C4318327207 (void);
extern void FastEncoder_GetCompressedOutput_m909E0D761BE2AB59255962D61D78934BF20A684D (void);
extern void FastEncoder_GetCompressedOutput_m8E46AA0D57BF6EE85123A4EC3C9B8FB257DBB007 (void);
extern void FastEncoder_InputAvailable_m9B47A868A55971F4C57583ED88D9BA16DB47F6C9 (void);
extern void FastEncoder_SafeToWriteTo_m16E334C4E3615F49119ADE80CDACA4DD7C58AE54 (void);
extern void FastEncoder_WriteEndOfBlock_m920E774F552971C2783C14106E2DA603B40C4A2C (void);
extern void FastEncoder_WriteMatch_m6C19330D739E36E592F725D5D7478B90AE17B55B (void);
extern void FastEncoder_WriteChar_mCD01C995A77AACC39AB098E7CFD134863A0496F8 (void);
extern void FastEncoder_WriteDeflatePreamble_mE6BDD3EAD21E9AB00B1BC2FA9D63FEF8FF92CED4 (void);
extern void FastEncoderStatics_CreateDistanceLookup_m4396554EB7D05F4241EFD8C4C11EED31B161B5F6 (void);
extern void FastEncoderStatics_GetSlot_mC4962BE407950E486BBCED5C84B1B45DA38CC4F4 (void);
extern void FastEncoderStatics_BitReverse_m2B662DAACF47EE24578493C0106A991DD67FAE8D (void);
extern void FastEncoderStatics__cctor_m6CE3E11E00390C3C21386533AE1E93DE6B870DF4 (void);
extern void FastEncoderWindow_get_BytesAvailable_mA40FE361DF72715889A6583478892D87C33B7F56 (void);
extern void FastEncoderWindow_get_UnprocessedInput_mC58081A53ECB232800AD110657E82E37AE56C099 (void);
extern void FastEncoderWindow_FlushWindow_m09821F18B88BC878A5D3543C5AD02AE90B20C83B (void);
extern void FastEncoderWindow_ResetWindow_mBEF006B89DDE20A732312E2A0CAFCE63D0773F32 (void);
extern void FastEncoderWindow_get_FreeWindowSpace_m2182C7AD575FDD9813E2972C502FBCA0808DA716 (void);
extern void FastEncoderWindow_CopyBytes_m2C1711102821420301014022E67B44FF77794B02 (void);
extern void FastEncoderWindow_MoveWindows_mB3C57F07203643CCFFEEDCFCB054D4576FEA0290 (void);
extern void FastEncoderWindow_HashValue_m9C0FEEA4D27B2CFCCDED02C06A62C18C6E4A897F (void);
extern void FastEncoderWindow_InsertString_mE0DF23EED20246540B890FA6D4EF358DCD2CB3AD (void);
extern void FastEncoderWindow_InsertStrings_m83C856465BE7389DA4D7015B463884644BDBFA5C (void);
extern void FastEncoderWindow_GetNextSymbolOrMatch_m2364046212DFA38050F21D081D0AAAFB1A877878 (void);
extern void FastEncoderWindow_FindMatch_m3F0178A2C6BEA6B8AA95C27A729D7973A2C18165 (void);
extern void HuffmanTree_get_StaticLiteralLengthTree_m5497AFFD041352473F2DA39BE57261B519E3025E (void);
extern void HuffmanTree_get_StaticDistanceTree_mA69F234FF97D800FA56E04CA09573BDBFD62194E (void);
extern void HuffmanTree__ctor_m6F9AEAB206B1685B20F05C75FFD7A3712ED56FB5 (void);
extern void HuffmanTree_GetStaticLiteralTreeLength_m09EF1A204A8FEFC4B8DDA28B9E0008B742C1E5C6 (void);
extern void HuffmanTree_GetStaticDistanceTreeLength_m3B10029A8D1AD95365B4C8C6E278B58B43B46783 (void);
extern void HuffmanTree_CalculateHuffmanCode_mC1335916F3F2A885CB28CE3C02A00C562C4DDB82 (void);
extern void HuffmanTree_CreateTable_mB6F94AAA9F2DD676AAD3F170248C7A7CF0C9AAAA (void);
extern void HuffmanTree_GetNextSymbol_mDCFB630B8ECAC8B715A723830B1C8199E933189A (void);
extern void HuffmanTree__cctor_mCD008E30FFA40975081CD8FB02A7EC63A9FA102A (void);
extern void InflaterManaged__ctor_m4E3502300142CD189539B88C0EC75995CC4EC446 (void);
extern void InflaterManaged_Reset_mCD49A25A230A268614195FBC20E813400F07F4AB (void);
extern void InflaterManaged_SetInput_mA2C8B1884D72C34B9A2CEACF7A789C192DE39713 (void);
extern void InflaterManaged_Finished_m9E78850019C3264C72E50720AD07BEE42E7C6382 (void);
extern void InflaterManaged_Inflate_mD93C06153E8A2BA25BAB16028F302EFF0E8C39B3 (void);
extern void InflaterManaged_Decode_m7457D0BB775A8DFF55D38D3556081F727B49E90D (void);
extern void InflaterManaged_DecodeUncompressedBlock_m8C03BD68025687D76557AAE8475DE08D1E76EF55 (void);
extern void InflaterManaged_DecodeBlock_m79C381D2E5BEE792D8E8BBA59E138D808E4E9BCD (void);
extern void InflaterManaged_DecodeDynamicBlockHeader_mF489997B1938D349EAA5DF9FBB44AEC4E46C6132 (void);
extern void InflaterManaged_Dispose_m33ED3547C9EE2E102F0F1CE8CF50EF421FEAB1FE (void);
extern void InflaterManaged__cctor_mF4C537F8692D80453CB2CF3F50B587FCF1C0363E (void);
extern void InputBuffer_get_AvailableBits_m9FF76B78FFAF3D5D866738682833C6564C934059 (void);
extern void InputBuffer_get_AvailableBytes_m7216E116CC66379FB6D11AE037BD4BAD693A92AD (void);
extern void InputBuffer_EnsureBitsAvailable_m487E13293FC2F4B3811A07946D92443BFA850A24 (void);
extern void InputBuffer_TryLoad16Bits_mF1D2CD50DB9384F6409B2874F24614878EBD6543 (void);
extern void InputBuffer_GetBitMask_m058A268DF8E6E1B39DD1C3A2658D5FD4CC512110 (void);
extern void InputBuffer_GetBits_m24AC725CDBCE1EA7CD65B6902EBBC57B8DA75E78 (void);
extern void InputBuffer_CopyTo_mE0952BF004B2F49B425339FBFA7C0183CCC626F4 (void);
extern void InputBuffer_NeedsInput_m9E7898C76A8E37CDD4A4D109CA403693FC004BEB (void);
extern void InputBuffer_SetInput_mB970FC9A216F12C8C9C66454EAA5A2DBDE0E1007 (void);
extern void InputBuffer_SkipBits_m75DC2DC8E1C9264BD29DC8EDBCEF14C318A2B96F (void);
extern void InputBuffer_SkipToByteBoundary_m4FD99AC1DE5FBE46A8602CD4FEC66DB530737414 (void);
extern void InputBuffer__ctor_m469EF0673C2027A23509C02FCBDD6F0820243341 (void);
extern void Match_get_State_m44EB97DC82E110A6E36C893BA42641BB67DFAB95 (void);
extern void Match_set_State_mD82A943EF3AAAD5607358632FF8368E16E2512C8 (void);
extern void Match_get_Position_mD82D52F9C717C59769BAEFBF321C691EFA16F005 (void);
extern void Match_set_Position_mF9ABE68E1CD17AEDCD8399F15456A437A171D6EB (void);
extern void Match_get_Length_m9A479B4AC324230750C13DEB2862BFD318C96C77 (void);
extern void Match_set_Length_m9F1DFC329350691BF0F488B32E65398A3F215089 (void);
extern void Match_get_Symbol_m1D1280640F4B3363440001006F9833E95F18256E (void);
extern void Match_set_Symbol_mA37611C31D036D740C2EB1E76BFE45F3D74DD74F (void);
extern void OutputBuffer_UpdateBuffer_mF5D538E429792790769A87A8214441359E2706BE (void);
extern void OutputBuffer_get_BytesWritten_m083497C056759A0D727F8547ED08E6BA2CF82EC3 (void);
extern void OutputBuffer_get_FreeBytes_m8E200313D40A89D8D5BAD2F823A0C199DCBE6B03 (void);
extern void OutputBuffer_WriteUInt16_m8935B46B52F3F92D73F7104964799A1A33C09C0B (void);
extern void OutputBuffer_WriteBits_mD3A5FFAE670D9B391F58EB9C4BCC9C4157321F67 (void);
extern void OutputBuffer_FlushBits_m7557A09A96F74D29BC7B7266FD1EE28BF94BFE85 (void);
extern void OutputBuffer_WriteBytes_m098BDF921CA5726277F0B5DFC36BA80A1CB09B93 (void);
extern void OutputBuffer_WriteBytesUnaligned_mBDFE44465A25B83527B472807801F9FCAF41BFDA (void);
extern void OutputBuffer_WriteByteUnaligned_m29C0290C5A2F237FB7D4410C23E8295A94AAEF5A (void);
extern void OutputBuffer_get_BitsInBuffer_m1BC64A199DB5FE314B7BC8EC5E2C33FBAFF3F2E1 (void);
extern void OutputBuffer_DumpState_m57A1F6434B5A148CF547962859F16582425566A7 (void);
extern void OutputBuffer_RestoreState_mAEC49DDCC5C217DC8DEA903E54FA6EEFA65CBEC3 (void);
extern void BufferState__ctor_mD4EF7939B6A524E2FA7A43D9CDC13DBA6CFF02FA (void);
extern void OutputWindow_Write_mF1729E92A8DC8C11F1C9E5FF2D0AA5F8347E5A46 (void);
extern void OutputWindow_WriteLengthDistance_m4E301063890AB6191C76C52D9C62100A4CAE1510 (void);
extern void OutputWindow_CopyFrom_m43757C0C7965E0FA9742E049D748AF654507B2D9 (void);
extern void OutputWindow_get_FreeBytes_mFE312FDC636EAB44D2680BE544DFBF0EF73BABD6 (void);
extern void OutputWindow_get_AvailableBytes_m6484DDBE3925F9A4E0BA9276B72AFF6F982544FF (void);
extern void OutputWindow_CopyTo_mBDC2019A354A2BF9A47B0C93A41A97122745D710 (void);
extern void OutputWindow__ctor_mDFA19678376164B99DD2A90D209A26F7AFBC66E0 (void);
extern void PositionPreservingWriteOnlyStreamWrapper__ctor_m00049B661A2C196DFAE1E3DC0675882180B5F879 (void);
extern void PositionPreservingWriteOnlyStreamWrapper_get_CanRead_m2C7020024EF4EEC41934336D15A8C6D4A64E8F47 (void);
extern void PositionPreservingWriteOnlyStreamWrapper_get_CanSeek_mD784CE0F401F4D7CC7E1823601C9EDEBFBA7C4BC (void);
extern void PositionPreservingWriteOnlyStreamWrapper_get_CanWrite_m3A0372FF77DC05CBDAD6A95D4CF1F491F617B3C1 (void);
extern void PositionPreservingWriteOnlyStreamWrapper_get_Position_m55A2397F0B95DC89FD9897FB8FD4084EA8F2A66F (void);
extern void PositionPreservingWriteOnlyStreamWrapper_set_Position_m4C96655E87094384BE89FA2405862733F1C73912 (void);
extern void PositionPreservingWriteOnlyStreamWrapper_Write_mD56A4F88EAF3A8B29668745D50A8C6A55FBF2CB2 (void);
extern void PositionPreservingWriteOnlyStreamWrapper_BeginWrite_m4B7B086406AE3CF97F01F25100117B4DA81215D0 (void);
extern void PositionPreservingWriteOnlyStreamWrapper_EndWrite_mAD27F0850F7D51F40AE2D399F2E502B2FFC2D692 (void);
extern void PositionPreservingWriteOnlyStreamWrapper_WriteByte_m5102BAC7F22BE37F29B9BC2A35831E41EF60F36B (void);
extern void PositionPreservingWriteOnlyStreamWrapper_WriteAsync_mB2159B57C4D9E1A30E5A195D18263E3CA7A4BD42 (void);
extern void PositionPreservingWriteOnlyStreamWrapper_get_CanTimeout_m034EF279B4BD99920211A5CA00D8FBF2FB78EF2D (void);
extern void PositionPreservingWriteOnlyStreamWrapper_get_ReadTimeout_mEDD5DC36B8264ED77A3E7AACFC743F73DEC840EF (void);
extern void PositionPreservingWriteOnlyStreamWrapper_set_ReadTimeout_m80B9CE71876163819D58911E32FF43029D0FB7CE (void);
extern void PositionPreservingWriteOnlyStreamWrapper_get_WriteTimeout_m6DC489300EE23D5D370CACFE7F1A34E1CBC57FF2 (void);
extern void PositionPreservingWriteOnlyStreamWrapper_set_WriteTimeout_mC970964135EABE1545137E47C01164EBA80EEEC0 (void);
extern void PositionPreservingWriteOnlyStreamWrapper_Flush_m0A388D0E61CED5B16C39CB461E7036269F71D213 (void);
extern void PositionPreservingWriteOnlyStreamWrapper_Close_m97D99D4FC4B4B7DE68602FD48537DB8AD452D5B7 (void);
extern void PositionPreservingWriteOnlyStreamWrapper_Dispose_m55E51A1CAF4F3235654AAC05FA33F2B90A7EA582 (void);
extern void PositionPreservingWriteOnlyStreamWrapper_get_Length_m608B54560186BD70F8302A2827DDDCA901AF5967 (void);
extern void PositionPreservingWriteOnlyStreamWrapper_Seek_m36B73C77EB0C77F06FC6AAE3090098A6B45FA75A (void);
extern void PositionPreservingWriteOnlyStreamWrapper_SetLength_mAD378565C6A7B472F961B01BCC77E994ABD73A63 (void);
extern void PositionPreservingWriteOnlyStreamWrapper_Read_m3056C14432DC1160F0B62ABDD7389B4DC5ECFFE7 (void);
extern void ZipArchive__ctor_m23DE01C72EAA87FCED326E56A8BA38A251E9C2E3 (void);
extern void ZipArchive__ctor_m2706DA413E897A83057237178CCC8E51C07230B5 (void);
extern void ZipArchive_get_Mode_mC02087A709688333AF6721BBBBEAE9F379D06127 (void);
extern void ZipArchive_CreateEntry_m52C487BC25504F8951CE8927F64778DC417CE88E (void);
extern void ZipArchive_Dispose_m2FF60D8833B08960AE817AC92DD6D677E4DF3FD0 (void);
extern void ZipArchive_Dispose_mB65DC0892D480934FBB2A8E95A2AAED07179E8B1 (void);
extern void ZipArchive_get_ArchiveReader_m545504179D41E6478352D45CC9BB74589CD2770E (void);
extern void ZipArchive_get_ArchiveStream_m5A8467BA46A12C06DF191597A4810562F4B2E79F (void);
extern void ZipArchive_get_NumberOfThisDisk_m34BE6245FFCA87E7F5068C7A988922877B620DCB (void);
extern void ZipArchive_get_EntryNameEncoding_m91420D79C1E76C90B846EA536B678AD7084DF059 (void);
extern void ZipArchive_set_EntryNameEncoding_mCF0292991EC16697CF419BFA0A1D42612E189625 (void);
extern void ZipArchive_DoCreateEntry_m02CFFDBDCEC117EAAEDA87DB17870735E83C74DE (void);
extern void ZipArchive_AcquireArchiveStream_mD39904134200E16277221E0ABC9020F1578306E7 (void);
extern void ZipArchive_AddEntry_mF0C759DC22B3087EF6D9958FF651726CE17BF1C9 (void);
extern void ZipArchive_ReleaseArchiveStream_m3490E6E1E69188C52F6C95598DEACC188BAE02AE (void);
extern void ZipArchive_RemoveEntry_m56F4AFBF28A9AD87377A0C15620506207060F1D2 (void);
extern void ZipArchive_ThrowIfDisposed_mCF0A1324ED33B058A30E33B00197D4EC37A74CF6 (void);
extern void ZipArchive_CloseStreams_m87A073F74B899F1430CE3BB2A8C891A39ED6FB58 (void);
extern void ZipArchive_EnsureCentralDirectoryRead_m31430840CA181B1DA19233BF56D2E697759CF195 (void);
extern void ZipArchive_Init_m41DBA9F7EC4C8E980497A7FDA896A4470A18C04F (void);
extern void ZipArchive_ReadCentralDirectory_m1EA97D1BCAB7BC75525FE951D757C5AA9071A9AA (void);
extern void ZipArchive_ReadEndOfCentralDirectory_m8682F73E19E15118476CE915A693AAA81CDF28FB (void);
extern void ZipArchive_WriteFile_m4D3BA6D0C52BFA7159EB9B3C7FC69CE14DD61460 (void);
extern void ZipArchive_WriteArchiveEpilogue_mF81EB28294C8DE727602EB7DDFF73D10EBC98E23 (void);
extern void ZipArchiveEntry__ctor_mA2B6549B24B1128D6FC6BA33ED7E3FD04BD4070B (void);
extern void ZipArchiveEntry__ctor_m13DC9D74F91A3B3E61C411874E5269EA0FE02813 (void);
extern void ZipArchiveEntry__ctor_mC25359CA7EF7F28F5176B84636C1CB58D3DCB179 (void);
extern void ZipArchiveEntry_get_FullName_mB226F80A14EA72D5C3D63C912AD483020CE81F2F (void);
extern void ZipArchiveEntry_set_FullName_m193B830DA0C41B1D1E7B69A049AD8BD44CE63D89 (void);
extern void ZipArchiveEntry_Delete_m8801F6B62B477E2C1A6FEE2210AB8497EFFA43F2 (void);
extern void ZipArchiveEntry_Open_m1D0EFB9AD33BA96AAF0C624EADA3E58CD6CC67FF (void);
extern void ZipArchiveEntry_ToString_m8406E95654F42A870D498A246C8026234472CA25 (void);
extern void ZipArchiveEntry_get_EverOpenedForWrite_mED5D3E58C7482A11C3E0C91F4D0F3F779C42B07D (void);
extern void ZipArchiveEntry_get_OffsetOfCompressedData_m4E6B407825F4902E2747798026CCF20063A1982E (void);
extern void ZipArchiveEntry_get_UncompressedData_m44F87FE5D136CBD5006C01163F6EBF2FDF1FAB75 (void);
extern void ZipArchiveEntry_get_CompressionMethod_mA636837EB83C990C39E207F8767626EE630A282B (void);
extern void ZipArchiveEntry_set_CompressionMethod_m4BB5F8B6E417C267393D5DB6887595A5A0161771 (void);
extern void ZipArchiveEntry_DecodeEntryName_mCB5F4B921507F93E2A0BC939C6B71C51C040A3C3 (void);
extern void ZipArchiveEntry_EncodeEntryName_mC69108C3D6659E706949B69111C798CF292423A4 (void);
extern void ZipArchiveEntry_WriteAndFinishLocalEntry_m27D6658B541689E0F5310B370B417B519A1AF05D (void);
extern void ZipArchiveEntry_WriteCentralDirectoryFileHeader_m0D461A71902AEDCA8CDA8719ED36EFA345F92053 (void);
extern void ZipArchiveEntry_LoadLocalHeaderExtraFieldAndCompressedBytesIfNeeded_mF1803EF6C9574AF97616D35E8E8083F8D4590280 (void);
extern void ZipArchiveEntry_ThrowIfNotOpenable_m141851EC16D13A81B699F50EB2B83506E923A995 (void);
extern void ZipArchiveEntry_GetDataCompressor_m0E1AC3095B7F5E79C931ADA856E3CE56209EFD0C (void);
extern void ZipArchiveEntry_GetDataDecompressor_m7FA439A9B0E491A2C2878BDC16DEEE0C17062C63 (void);
extern void ZipArchiveEntry_OpenInReadMode_mA0257EEA737BA85855C8E1D480B894BEA66B330D (void);
extern void ZipArchiveEntry_OpenInWriteMode_m7217BE297FFB16D541242B43605C83F7D4A71698 (void);
extern void ZipArchiveEntry_OpenInUpdateMode_mE527744C62297D9A901C6C004DC0F74E3E21592D (void);
extern void ZipArchiveEntry_IsOpenable_mC92895385CEAC7F1EF832D962C99F3D2A3F4B4CE (void);
extern void ZipArchiveEntry_SizesTooLarge_mBF42A3A3E27152942B65447A7AB5CA89FAB46198 (void);
extern void ZipArchiveEntry_WriteLocalFileHeader_m24ABCA6E8CC57BCE477DDD6E564D04DF5D9FFE91 (void);
extern void ZipArchiveEntry_WriteLocalFileHeaderAndDataIfNeeded_mA95D0CE40CF84C1E7A36783DAD3280C1134EC65C (void);
extern void ZipArchiveEntry_WriteCrcAndSizesInLocalHeader_m67FE451EDE9F8A67F71AC400DF2A76F68D2206E4 (void);
extern void ZipArchiveEntry_WriteDataDescriptor_m9C22F8F8EF9F7A2BC10F7D7DDBBB799D7C262246 (void);
extern void ZipArchiveEntry_UnloadStreams_mAA66F564B73322E65FDA8C40C248C98DC367945C (void);
extern void ZipArchiveEntry_CloseStreams_m6A5C7BDEA3457B2EB1B30DC9E27BDD4639BDC506 (void);
extern void ZipArchiveEntry_VersionToExtractAtLeast_mAE09A7CC80BDF41268A9392CB18883C2714EE8BA (void);
extern void ZipArchiveEntry_ThrowIfInvalidArchive_mF5EF732D2525CC4B388B91369554B32F5170B1C9 (void);
extern void ZipArchiveEntry_GetFileName_Windows_m1C58B47F8475A5A145F94CBA61C7E983655039BC (void);
extern void ZipArchiveEntry_GetFileName_Unix_mB832EE5367BBB1B3887B5613A01E1AE31703FC58 (void);
extern void ZipArchiveEntry_ParseFileName_mB97DF5D033B65F81671D63DAC56A25F2AA32DDF8 (void);
extern void ZipArchiveEntry__cctor_m00A0460A3FCDC9C666E7BB099FE8AA8B283CA860 (void);
extern void DirectToArchiveWriterStream__ctor_m9BB7F2B381424652C58836B4D94CB51D7EEF6A78 (void);
extern void DirectToArchiveWriterStream_get_Length_mF5960C42CA8DD3ACDA89A57D744D120A22182326 (void);
extern void DirectToArchiveWriterStream_get_Position_m1445767E25E4A881159BBDD9D3C7BFCB33811766 (void);
extern void DirectToArchiveWriterStream_set_Position_m75999435FE3C978528F640246A704A87BC4F8BEF (void);
extern void DirectToArchiveWriterStream_get_CanRead_m5693AF05AC7763E3EF75C40D283E39B6CC3D3B6B (void);
extern void DirectToArchiveWriterStream_get_CanSeek_m0982800C7D85BAC0196762C6B4D995561C65F846 (void);
extern void DirectToArchiveWriterStream_get_CanWrite_mA38DC50347D9C86FEEE193ABCA732C1B204110D0 (void);
extern void DirectToArchiveWriterStream_ThrowIfDisposed_mF4D2AD303F7DAD53E17E3A4D57BFB1E94A78D861 (void);
extern void DirectToArchiveWriterStream_Read_mB8DA1FEFCE7016B7E1CD529E80AD08F44ACE9EC5 (void);
extern void DirectToArchiveWriterStream_Seek_m6445B6C433A3EFB99B687B9DF1DD11200787DB51 (void);
extern void DirectToArchiveWriterStream_SetLength_m3EDFAB11631621DB2B28E54F7ABA1D9910CD50CF (void);
extern void DirectToArchiveWriterStream_Write_m3290C62DEC4ECF784DF923A88E04BD5DB8366FAD (void);
extern void DirectToArchiveWriterStream_Flush_mE250B983605764DE0B2857EEFD8CF9E937982807 (void);
extern void DirectToArchiveWriterStream_Dispose_m3AF7D2F140CA3C62F9A44D07D1F5A6DFFFC4D2A5 (void);
extern void U3CU3Ec__cctor_mFB0EA39E385CFFACC7A1E47673D4E1F267C19A74 (void);
extern void U3CU3Ec__ctor_mAEA937E579A34BDF23D9107A2BE5527A46BABC69 (void);
extern void U3CU3Ec_U3CGetDataCompressorU3Eb__69_0_m5DB8D5229C65AC9D1CA956550B2E9A371487ABF9 (void);
extern void U3CU3Ec_U3COpenInWriteModeU3Eb__72_0_m7B46F385AC1653A77BA1FECA69143DF57FE83B10 (void);
extern void U3CU3Ec_U3COpenInUpdateModeU3Eb__73_0_mF7D05AA3555DD6A7046539DD462D161EDDDFE523 (void);
extern void ZipGenericExtraField_get_Tag_m99149829A339C6B07BA86F7BA89277FD61257FE7 (void);
extern void ZipGenericExtraField_get_Size_m56A96EAB8BB98584F13938BEF05A3DC6CFE199C6 (void);
extern void ZipGenericExtraField_get_Data_m0C9F6CC3B46705955EE5151F641D8CCE8B232C43 (void);
extern void ZipGenericExtraField_WriteBlock_mE0E1FDE23207EB8B55074171E6C481A1A471F2C6 (void);
extern void ZipGenericExtraField_TryReadBlock_mA9E4A7FBBB4DE351D49C168F43B0860C44F7DAFB (void);
extern void ZipGenericExtraField_ParseExtraField_m0181585E2CC292C8AB33E7A4DDF89A4C0AA6952C (void);
extern void ZipGenericExtraField_TotalSize_m5CD4706EBD9E58DC7B745B2E037113987FECEDBE (void);
extern void ZipGenericExtraField_WriteAllBlocks_mF43381AFAE22FB4676F87F423EF23479E75B9E11 (void);
extern void Zip64ExtraField_get_TotalSize_m5F50EC851F8B687BFF82B1661A4E569C476A6E85 (void);
extern void Zip64ExtraField_get_UncompressedSize_mC53A72CA1685391354843A9E7A47B6FE60E707B2 (void);
extern void Zip64ExtraField_set_UncompressedSize_m1A0368B13AF3852DCA015652DFE6F8D9E1B2FAF1 (void);
extern void Zip64ExtraField_get_CompressedSize_mD68A6FC67B58A127B2CA1353B882ABC5C4BB9B4E (void);
extern void Zip64ExtraField_set_CompressedSize_mCAA4A05A68B7D6C15724D8323B36D3482CAF29AE (void);
extern void Zip64ExtraField_get_LocalHeaderOffset_m1F7A1833A2E2782CF18E55B965FED3AD3275091D (void);
extern void Zip64ExtraField_set_LocalHeaderOffset_m523C6446CBE2710C17270A84C2D1ED68628663C8 (void);
extern void Zip64ExtraField_get_StartDiskNumber_mB01F9839965C60DFFEB90C588B4FA0CA22787D5E (void);
extern void Zip64ExtraField_UpdateSize_m8FB215062A34D76FBC958859B0DB73222A3EC5BC (void);
extern void Zip64ExtraField_GetJustZip64Block_m6048EFE244AF5DED7677BEE5AAA0884A32B34D41 (void);
extern void Zip64ExtraField_TryGetZip64BlockFromGenericExtraField_m5DB9958C01EDB412C5D3F8B4D9A90769ADD7D0F1 (void);
extern void Zip64ExtraField_GetAndRemoveZip64Block_mB7341E90373208AB63585BC7BBE673D642C8CA0A (void);
extern void Zip64ExtraField_RemoveZip64Blocks_m3BCB96FE6A482AE8BE0D11D9D77DEACC3EAFED5A (void);
extern void Zip64ExtraField_WriteBlock_mAC1D9A401E0299A69AB49E743FF92C02E94F2BAE (void);
extern void Zip64EndOfCentralDirectoryLocator_TryReadBlock_m95F1CB20F372E7626FD6145E6B479FFE5504C4EE (void);
extern void Zip64EndOfCentralDirectoryLocator_WriteBlock_mCF0B88637AE66FD48BB08A6456A267F94D8DAE88 (void);
extern void Zip64EndOfCentralDirectoryRecord_TryReadBlock_m8447AFFBFA76A08B60388C3BE333613C213EC0D1 (void);
extern void Zip64EndOfCentralDirectoryRecord_WriteBlock_m2778F387199A087AEDF6D65FA80B7FCBB9DFB164 (void);
extern void ZipLocalFileHeader_GetExtraFields_m0C152D89B178FD3D8DC55E216AC787EA274D84AB (void);
extern void ZipLocalFileHeader_TrySkipBlock_m8D73DBDBC22EF1E7D1499BCE781C7748EF907646 (void);
extern void ZipCentralDirectoryFileHeader_TryReadBlock_mC50A29A666BDF5303461EB3AB38E2ACE0EB0BB88 (void);
extern void ZipEndOfCentralDirectoryBlock_WriteBlock_m0CA333378E1B10C90EA9B21770208D30CFF33758 (void);
extern void ZipEndOfCentralDirectoryBlock_TryReadBlock_m5222F66524A785C9E17515C634530CCCF932995C (void);
extern void WrappedStream__ctor_mCEFD3D801E6F14698F4BF4C2AA72A92359D62EF3 (void);
extern void WrappedStream__ctor_mB68DE6EB41FFBE42DC901F28CA8754255D8DDBBE (void);
extern void WrappedStream__ctor_m52262F5CE3D365E105A01027C1E6F257D2B1B35A (void);
extern void WrappedStream_get_Length_mEB63DA68A20122010C4AB094081BD19C3AF60F64 (void);
extern void WrappedStream_get_Position_m8CE0B7A544B011AE331B3923C4FA5EAB4B2012FB (void);
extern void WrappedStream_set_Position_mF978AD11D27DCFD6000C08AE3D2A1425211C2008 (void);
extern void WrappedStream_get_CanRead_mF77813A5982F385FEF5486BC57423FD52C008108 (void);
extern void WrappedStream_get_CanSeek_m8D5376D162563B32A7F276DA42F88B466387F8C1 (void);
extern void WrappedStream_get_CanWrite_m64BCC6D462069D9484E841F21DBC90872ECAFE68 (void);
extern void WrappedStream_ThrowIfDisposed_m08BAA20A4A96F141D035374821281CB59F4495F2 (void);
extern void WrappedStream_ThrowIfCantRead_mF788A331845024DE9533292FA01A7A914AFD6FA9 (void);
extern void WrappedStream_ThrowIfCantWrite_mFD83C4E1E9E762A2ACF0B03FDDFBE750A370D6E1 (void);
extern void WrappedStream_ThrowIfCantSeek_m3B766B3922EE178758C3176CF332C329BABC6F4E (void);
extern void WrappedStream_Read_mC5207A75B422C86AA7EA03318E5F4C4D7B60CE5E (void);
extern void WrappedStream_Seek_mDD591904952C39FE0FADC8739E5EAC5EE463EB64 (void);
extern void WrappedStream_SetLength_m7CF59F91FCE6AAFF912B1D360D3EE8B3608670C1 (void);
extern void WrappedStream_Write_mD9C8A5A97FCBB05F635747BC891B5E196E2A262F (void);
extern void WrappedStream_Flush_m9F2CF33C2394D1C7F47B5F3CAEB43B68D56D142C (void);
extern void WrappedStream_Dispose_m1C4CA8E2CAF45DEFCF7D35949E59A31A43616804 (void);
extern void SubReadStream__ctor_m3104F68BD5EF131BBC3AB455283B7CFECE493CC5 (void);
extern void SubReadStream_get_Length_m927C2160A7D463BAFA4214C3983D97B5105B1EBB (void);
extern void SubReadStream_get_Position_m45D06632F50A8AA3280E413F071F9B4BF22BE09E (void);
extern void SubReadStream_set_Position_m63C61821FB7B6CE57065E92A24778FF76C57DB1F (void);
extern void SubReadStream_get_CanRead_mE027F1C797C523EA1EE533F952A7961A932AE4B6 (void);
extern void SubReadStream_get_CanSeek_mF373DC87E94BACF0E2080AF657F8F789949853F0 (void);
extern void SubReadStream_get_CanWrite_mCA6DF3176D34405AB4F4F11E580E18D45DB33B0D (void);
extern void SubReadStream_ThrowIfDisposed_mB2F7DB5B995EA9847EA6346E2D02FFF37ADF4AA8 (void);
extern void SubReadStream_ThrowIfCantRead_m87A273A233AE230EE106DD250F36DFBA8C0A1636 (void);
extern void SubReadStream_Read_mEDA8FACF0C1C1A7464D8B9E140AAD782EC130D0B (void);
extern void SubReadStream_Seek_m2448FE753333F79695AAAC4FC2E3331D4DACC527 (void);
extern void SubReadStream_SetLength_m29FE28460B74DED5EB917335A0F8FED03E9E88F4 (void);
extern void SubReadStream_Write_mC5B6C8136AD27CA18C7DF00DCC1477B49B471CC6 (void);
extern void SubReadStream_Flush_mB4E80E151651465709E444823B0D53DC147EEEE4 (void);
extern void SubReadStream_Dispose_m1190CA46857DCE02BADC6D43EE7455779078FABE (void);
extern void CheckSumAndSizeWriteStream__ctor_m2F8A286280CEE477FE04E9CB046BCD9350F3DF53 (void);
extern void CheckSumAndSizeWriteStream_get_Length_m3D3EDBE5E95899B7585C8104186AD0D4D144587E (void);
extern void CheckSumAndSizeWriteStream_get_Position_mFD88EC55723C1A9FFEE2198EC4C11241EED5F5B9 (void);
extern void CheckSumAndSizeWriteStream_set_Position_m43180641FAA87F88403DB9062160767B2045E1EB (void);
extern void CheckSumAndSizeWriteStream_get_CanRead_mDB591A8EAACA4BB9160CC2D88C88693A7EBFBA98 (void);
extern void CheckSumAndSizeWriteStream_get_CanSeek_mD8FC46B60B7C2C0FB7784C92F0BEF4C42BBA7FE9 (void);
extern void CheckSumAndSizeWriteStream_get_CanWrite_mEEA7725B766EBF5C7FB7F175C38063BB40EE0E7F (void);
extern void CheckSumAndSizeWriteStream_ThrowIfDisposed_m7FFB6BDF119D117AD15F5EBA679832B05AA401D4 (void);
extern void CheckSumAndSizeWriteStream_Read_m76379DE8EAF2C40B135E81A3662A32AFF6A7D04A (void);
extern void CheckSumAndSizeWriteStream_Seek_mB4F12034E45070AF25B560C6ECE732013E098DB6 (void);
extern void CheckSumAndSizeWriteStream_SetLength_m1ACC2D77585B8941447E3BCA0B2B012C2EF167FC (void);
extern void CheckSumAndSizeWriteStream_Write_mCBB708B9DA718DA879C8EDA20CA1A9B198C59F48 (void);
extern void CheckSumAndSizeWriteStream_Flush_mB70E50A32551A5289D4626F94C0682983F62ACB6 (void);
extern void CheckSumAndSizeWriteStream_Dispose_mCA606B257A9D2884C261314F3332BFC97AD0E19D (void);
extern void ZipHelper_RequiresUnicode_m20EAD7C8BAEB2EF5A554C761E8441AC594B6117F (void);
extern void ZipHelper_ReadBytes_m4BCFE2308C656AB48E0BC0D94FDED35AC3FCA8EC (void);
extern void ZipHelper_DosTimeToDateTime_mAC6DC9DEFADB46F3A5A0FE68F30DFAC696E2136E (void);
extern void ZipHelper_DateTimeToDosTime_m81F3D31F7D97F5209332D8BEB65C288F8D2E1AC1 (void);
extern void ZipHelper_SeekBackwardsToSignature_m07FB8913C58E31276264722997B9CBE9F576EC45 (void);
extern void ZipHelper_AdvanceToPosition_m37FE7419FDE8CBD9C17F1969AB585B1B85CA4DFF (void);
extern void ZipHelper_SeekBackwardsAndRead_m433E06E552871B8D2E5FF4D7EB3188282A721DC5 (void);
extern void ZipHelper__cctor_mD61640F38D2D755F1FFBDCA5C2DFF863281B3AED (void);
extern void Crc32Helper_UpdateCrc32_mEF874A4EC7E5936D98AC94494D27CDBFD2E60778 (void);
extern void Crc32Helper_ManagedCrc32_m53D17613D585422A4479D609FCCA9A0AD4BD3A6C (void);
extern void Crc32Helper__cctor_m56DA5438E4C3C739D0AEC99F7300DC8BA83430C7 (void);
extern void TaskToApm_Begin_m1A080755817528FB342A9A1352631ACCBF9CEBAE (void);
extern void TaskToApm_End_mCD6418D34E9684DB6691549A225F5F9CC5C32753 (void);
extern void TaskToApm_InvokeCallbackWhenTaskCompletes_m8553A6EFB5AEF7E94F4377E87ACE37D1920BC932 (void);
extern void TaskWrapperAsyncResult__ctor_mF1F0F980A52876DF07F5CF9B80CDD5CAE167F26D (void);
extern void TaskWrapperAsyncResult_System_IAsyncResult_get_AsyncState_m29AE39D37A24BC4FCB79ED5F37AF16814C7DB323 (void);
extern void TaskWrapperAsyncResult_System_IAsyncResult_get_CompletedSynchronously_m4DA0F3A48335A386436B255ED4D7B6EF941DC39C (void);
extern void TaskWrapperAsyncResult_System_IAsyncResult_get_IsCompleted_m02CCA4658D6C1A2F667137EB32FA082E066D3B9C (void);
extern void TaskWrapperAsyncResult_System_IAsyncResult_get_AsyncWaitHandle_mBD9F33A9F96E111E376192FC1F867CF219C8AB83 (void);
extern void U3CU3Ec__DisplayClass3_0__ctor_m4232277D2AB8EE7F6F66C4565C3CD16E58132C07 (void);
extern void U3CU3Ec__DisplayClass3_0_U3CInvokeCallbackWhenTaskCompletesU3Eb__0_m2C4821F622E71609CAD575ECAC6E757A4BE5CD4F (void);
static Il2CppMethodPointer s_methodPointers[363] = 
{
	SR_Format_m271F5A0AF597B1B86AAAFCEF15FC12BDA0C04468,
	CopyEncoder_GetBlock_m19B917E27CAE49A3A2C280DB03C7672C326DD659,
	CopyEncoder_WriteLenNLen_mCCB24BFD617911FA48996D87C54A76A22CFB921D,
	DeflateInput_get_Buffer_m9F3AD0BCF828824646D283A1DE5BDEB1F34B3F2F,
	DeflateInput_set_Buffer_m2366CB2FB1F51BFDC35D40E0C7E0E3EEA165FE0B,
	DeflateInput_get_Count_m07CF313093713EA551824415A986FE04136BA3D2,
	DeflateInput_set_Count_m25E5676842F9BE403E4E2DBE259BFE428DB78B5A,
	DeflateInput_get_StartIndex_mF0BAC57BB16D5B0493507E27A650DBDD65102779,
	DeflateInput_set_StartIndex_mF699B5A9E52487FB89E6BB77DE6E8182AD36670F,
	DeflateInput_ConsumeBytes_mDA15E6EB947020F2C7D4E3EE2DE6DD971A8ECDC4,
	DeflateInput_DumpState_mC617FD41FD589B6C8F6DDC4048AF70A2A5971A8B,
	DeflateInput_RestoreState_m8F8FD83E53E2538D242CA631E3EB90B90B04A1D2,
	DeflateInput__ctor_m3A5CDB0D7B76657D446A20E6175C5A507D102F03,
	InputState__ctor_m9F37B404CBF38DB44663775A9B42846F68FDF0ED,
	DeflateManagedStream__ctor_mB0B8610BA00510F162BEBDE6D466623DF8B93BF7,
	DeflateManagedStream_InitializeInflater_m92F2F626C323D45F5D9A6DBBB4C120A13DEF961F,
	DeflateManagedStream_get_CanRead_mCF994410F491C7F488E0CDBB35865349642CB5AB,
	DeflateManagedStream_get_CanWrite_mDE69F8F6FCA416C1AE7B2B073F61B7BD7DDCFB9F,
	DeflateManagedStream_get_CanSeek_m17F64890C4B012C90F8649265040B4724FFB1495,
	DeflateManagedStream_get_Length_m2D9B918EDCA9F9DA597047E19C5074B2C9032ED4,
	DeflateManagedStream_get_Position_m2B690044BA869ADBCFA0611261D45528B24C0B78,
	DeflateManagedStream_set_Position_m1D967D9DCCFF88B96B221EBAF5E0C40FE85C0A80,
	DeflateManagedStream_Flush_m678D8DCD6126270BD29616BD473DFC0B3EEF1222,
	DeflateManagedStream_Seek_mD9D64344A632B854C36AD411A894B6355F598556,
	DeflateManagedStream_SetLength_m4B89B2D809203CBD19ED10DF8C1CB1041BCB241F,
	DeflateManagedStream_Read_m61432BDD2A0C63A26A0D999795841210E7B1C988,
	DeflateManagedStream_ValidateParameters_m5059BD392464410E5B046B7CDBE8DBE1BBEE7A9D,
	DeflateManagedStream_EnsureNotDisposed_m9F9FEC94DB9DBA9B0A5473F253A25BB76224A819,
	DeflateManagedStream_ThrowStreamClosedException_mBA396FFC3EDE93DB8F7AF1C6FAA999E2C211AE10,
	DeflateManagedStream_EnsureDecompressionMode_m83D428E3974F52B13F55FCF19EF68C64E831BE7D,
	DeflateManagedStream_ThrowCannotReadFromDeflateManagedStreamException_m8B3A68B1C5EFD7D02E09FFE6098EA579CE2BCDAD,
	DeflateManagedStream_EnsureCompressionMode_mAB2118A2D4256FC974EBA6A3C62665667700E37B,
	DeflateManagedStream_ThrowCannotWriteToDeflateManagedStreamException_m992ADAD121F58DE82AD36D6E49916C61B75EA277,
	DeflateManagedStream_BeginRead_m7AACB982B4250162FECF46F9C4DCC4C1E05C572A,
	DeflateManagedStream_EndRead_m0147192926C251260A7F026E3A0AE14F8C8E59F4,
	DeflateManagedStream_ReadAsync_m8A5540EB38FBA7DF6CA9F5A5005DDECD18665594,
	DeflateManagedStream_ReadAsyncCore_m0EB7A8BEA37E47D35CE950BCA9FF9B236ED2B43A,
	DeflateManagedStream_Write_m154CD9DC5E7AFE4ADFFF5D23D15CAE462A57BAB0,
	DeflateManagedStream_WriteDeflaterOutput_m15EB635AE22CC4DFE241249D90EABD6FF5FE49AA,
	DeflateManagedStream_DoMaintenance_m843B9BE156173B2CB7B11D25DA1ED48F3BD56238,
	DeflateManagedStream_PurgeBuffers_mE668E8906372479A972F3FE24DAD12E6BFFE0B49,
	DeflateManagedStream_Dispose_m8AFF110FB255CD2FE5302ABAC3D6D5BC2B1CB24B,
	DeflateManagedStream_WriteAsync_m21BA2B55BF37ABE24FB6FE13760FD3429B702E67,
	DeflateManagedStream_WriteAsyncCore_mA0DF62D8656774394A7C291AF9307FC317336C7A,
	DeflateManagedStream_BeginWrite_m10D5C0E489EF74578C8982F264BE345A2C63602B,
	DeflateManagedStream_EndWrite_m02E69ABDB817734A1F59D6A013121B1B364B1BAC,
	DeflateManagedStream_U3CU3En__0_mD49534E1CB38B7CC9F740A310565430CDF685F52,
	U3CReadAsyncCoreU3Ed__40_MoveNext_m7DE3C87E7BCC9DAC07D7DE47C989D764C8D5269D,
	U3CReadAsyncCoreU3Ed__40_SetStateMachine_m809D8FF0E75647CDA4B31E3C430FDAEB936B6B71,
	U3CWriteAsyncCoreU3Ed__47_MoveNext_m37704F9AAAB247E7FBB28D5FC57E9C729C5D132A,
	U3CWriteAsyncCoreU3Ed__47_SetStateMachine_m7A3A2B8F2706E9E301A473AACFCAE8B675FBF2F2,
	DeflaterManaged_NeedsInput_m80E06DDF7D1FF28B38B2D7428AF0F30B9087C714,
	DeflaterManaged_SetInput_mDC0D32D6E849B5AF86C9E2B78D27873357ADCCC1,
	DeflaterManaged_GetDeflateOutput_mAA90AD2E8080DDAB639B01E32B1B7F9BA7BCD73F,
	DeflaterManaged_Finish_mC1E0F1E1EA9546AB32816ECAFCF73CE4CBEA5CDE,
	DeflaterManaged_UseCompressed_m0A440E14E58AD1952B7B7ECCB8D196F7EED857E5,
	DeflaterManaged_FlushInputWindows_mF82B88248066D040B4565C838B0DB66296908BA4,
	DeflaterManaged_WriteFinal_m6D42270C9B4F94A20F44561E55B6EED0E575B492,
	DeflaterManaged_Dispose_mA33D6301ED85EFB43656A778D2495CB82D170900,
	FastEncoder_get_BytesInHistory_mFC7283021C2CF56CD76F65EC31D451A82EA24AA7,
	FastEncoder_get_UnprocessedInput_mB235482CF82A7B80D48F02669B0DDD6F91385C1C,
	FastEncoder_FlushInput_m54EAC91A657CD4521EA17D2D54CA8782B21AB520,
	FastEncoder_get_LastCompressionRatio_mC220B5DFFF5D61E79EDD5E6D4E25B2F6090301B5,
	FastEncoder_GetBlock_mB5798B6567FF92DE531EF982B72C6BD6E7AC8ABE,
	FastEncoder_GetCompressedData_mD43E12AC3D6651340FB98F1EFC7193C5EB151EFA,
	FastEncoder_GetBlockHeader_m364914F16B71A20BEE90A3F6519115138C56EF00,
	FastEncoder_GetBlockFooter_m39A255EC4602C5BBFD78C5CC1D7D0C4318327207,
	FastEncoder_GetCompressedOutput_m909E0D761BE2AB59255962D61D78934BF20A684D,
	FastEncoder_GetCompressedOutput_m8E46AA0D57BF6EE85123A4EC3C9B8FB257DBB007,
	FastEncoder_InputAvailable_m9B47A868A55971F4C57583ED88D9BA16DB47F6C9,
	FastEncoder_SafeToWriteTo_m16E334C4E3615F49119ADE80CDACA4DD7C58AE54,
	FastEncoder_WriteEndOfBlock_m920E774F552971C2783C14106E2DA603B40C4A2C,
	FastEncoder_WriteMatch_m6C19330D739E36E592F725D5D7478B90AE17B55B,
	FastEncoder_WriteChar_mCD01C995A77AACC39AB098E7CFD134863A0496F8,
	FastEncoder_WriteDeflatePreamble_mE6BDD3EAD21E9AB00B1BC2FA9D63FEF8FF92CED4,
	FastEncoderStatics_CreateDistanceLookup_m4396554EB7D05F4241EFD8C4C11EED31B161B5F6,
	FastEncoderStatics_GetSlot_mC4962BE407950E486BBCED5C84B1B45DA38CC4F4,
	FastEncoderStatics_BitReverse_m2B662DAACF47EE24578493C0106A991DD67FAE8D,
	FastEncoderStatics__cctor_m6CE3E11E00390C3C21386533AE1E93DE6B870DF4,
	FastEncoderWindow_get_BytesAvailable_mA40FE361DF72715889A6583478892D87C33B7F56,
	FastEncoderWindow_get_UnprocessedInput_mC58081A53ECB232800AD110657E82E37AE56C099,
	FastEncoderWindow_FlushWindow_m09821F18B88BC878A5D3543C5AD02AE90B20C83B,
	FastEncoderWindow_ResetWindow_mBEF006B89DDE20A732312E2A0CAFCE63D0773F32,
	FastEncoderWindow_get_FreeWindowSpace_m2182C7AD575FDD9813E2972C502FBCA0808DA716,
	FastEncoderWindow_CopyBytes_m2C1711102821420301014022E67B44FF77794B02,
	FastEncoderWindow_MoveWindows_mB3C57F07203643CCFFEEDCFCB054D4576FEA0290,
	FastEncoderWindow_HashValue_m9C0FEEA4D27B2CFCCDED02C06A62C18C6E4A897F,
	FastEncoderWindow_InsertString_mE0DF23EED20246540B890FA6D4EF358DCD2CB3AD,
	FastEncoderWindow_InsertStrings_m83C856465BE7389DA4D7015B463884644BDBFA5C,
	FastEncoderWindow_GetNextSymbolOrMatch_m2364046212DFA38050F21D081D0AAAFB1A877878,
	FastEncoderWindow_FindMatch_m3F0178A2C6BEA6B8AA95C27A729D7973A2C18165,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	HuffmanTree_get_StaticLiteralLengthTree_m5497AFFD041352473F2DA39BE57261B519E3025E,
	HuffmanTree_get_StaticDistanceTree_mA69F234FF97D800FA56E04CA09573BDBFD62194E,
	HuffmanTree__ctor_m6F9AEAB206B1685B20F05C75FFD7A3712ED56FB5,
	HuffmanTree_GetStaticLiteralTreeLength_m09EF1A204A8FEFC4B8DDA28B9E0008B742C1E5C6,
	HuffmanTree_GetStaticDistanceTreeLength_m3B10029A8D1AD95365B4C8C6E278B58B43B46783,
	HuffmanTree_CalculateHuffmanCode_mC1335916F3F2A885CB28CE3C02A00C562C4DDB82,
	HuffmanTree_CreateTable_mB6F94AAA9F2DD676AAD3F170248C7A7CF0C9AAAA,
	HuffmanTree_GetNextSymbol_mDCFB630B8ECAC8B715A723830B1C8199E933189A,
	HuffmanTree__cctor_mCD008E30FFA40975081CD8FB02A7EC63A9FA102A,
	InflaterManaged__ctor_m4E3502300142CD189539B88C0EC75995CC4EC446,
	InflaterManaged_Reset_mCD49A25A230A268614195FBC20E813400F07F4AB,
	InflaterManaged_SetInput_mA2C8B1884D72C34B9A2CEACF7A789C192DE39713,
	InflaterManaged_Finished_m9E78850019C3264C72E50720AD07BEE42E7C6382,
	InflaterManaged_Inflate_mD93C06153E8A2BA25BAB16028F302EFF0E8C39B3,
	InflaterManaged_Decode_m7457D0BB775A8DFF55D38D3556081F727B49E90D,
	InflaterManaged_DecodeUncompressedBlock_m8C03BD68025687D76557AAE8475DE08D1E76EF55,
	InflaterManaged_DecodeBlock_m79C381D2E5BEE792D8E8BBA59E138D808E4E9BCD,
	InflaterManaged_DecodeDynamicBlockHeader_mF489997B1938D349EAA5DF9FBB44AEC4E46C6132,
	InflaterManaged_Dispose_m33ED3547C9EE2E102F0F1CE8CF50EF421FEAB1FE,
	InflaterManaged__cctor_mF4C537F8692D80453CB2CF3F50B587FCF1C0363E,
	InputBuffer_get_AvailableBits_m9FF76B78FFAF3D5D866738682833C6564C934059,
	InputBuffer_get_AvailableBytes_m7216E116CC66379FB6D11AE037BD4BAD693A92AD,
	InputBuffer_EnsureBitsAvailable_m487E13293FC2F4B3811A07946D92443BFA850A24,
	InputBuffer_TryLoad16Bits_mF1D2CD50DB9384F6409B2874F24614878EBD6543,
	InputBuffer_GetBitMask_m058A268DF8E6E1B39DD1C3A2658D5FD4CC512110,
	InputBuffer_GetBits_m24AC725CDBCE1EA7CD65B6902EBBC57B8DA75E78,
	InputBuffer_CopyTo_mE0952BF004B2F49B425339FBFA7C0183CCC626F4,
	InputBuffer_NeedsInput_m9E7898C76A8E37CDD4A4D109CA403693FC004BEB,
	InputBuffer_SetInput_mB970FC9A216F12C8C9C66454EAA5A2DBDE0E1007,
	InputBuffer_SkipBits_m75DC2DC8E1C9264BD29DC8EDBCEF14C318A2B96F,
	InputBuffer_SkipToByteBoundary_m4FD99AC1DE5FBE46A8602CD4FEC66DB530737414,
	InputBuffer__ctor_m469EF0673C2027A23509C02FCBDD6F0820243341,
	Match_get_State_m44EB97DC82E110A6E36C893BA42641BB67DFAB95,
	Match_set_State_mD82A943EF3AAAD5607358632FF8368E16E2512C8,
	Match_get_Position_mD82D52F9C717C59769BAEFBF321C691EFA16F005,
	Match_set_Position_mF9ABE68E1CD17AEDCD8399F15456A437A171D6EB,
	Match_get_Length_m9A479B4AC324230750C13DEB2862BFD318C96C77,
	Match_set_Length_m9F1DFC329350691BF0F488B32E65398A3F215089,
	Match_get_Symbol_m1D1280640F4B3363440001006F9833E95F18256E,
	Match_set_Symbol_mA37611C31D036D740C2EB1E76BFE45F3D74DD74F,
	OutputBuffer_UpdateBuffer_mF5D538E429792790769A87A8214441359E2706BE,
	OutputBuffer_get_BytesWritten_m083497C056759A0D727F8547ED08E6BA2CF82EC3,
	OutputBuffer_get_FreeBytes_m8E200313D40A89D8D5BAD2F823A0C199DCBE6B03,
	OutputBuffer_WriteUInt16_m8935B46B52F3F92D73F7104964799A1A33C09C0B,
	OutputBuffer_WriteBits_mD3A5FFAE670D9B391F58EB9C4BCC9C4157321F67,
	OutputBuffer_FlushBits_m7557A09A96F74D29BC7B7266FD1EE28BF94BFE85,
	OutputBuffer_WriteBytes_m098BDF921CA5726277F0B5DFC36BA80A1CB09B93,
	OutputBuffer_WriteBytesUnaligned_mBDFE44465A25B83527B472807801F9FCAF41BFDA,
	OutputBuffer_WriteByteUnaligned_m29C0290C5A2F237FB7D4410C23E8295A94AAEF5A,
	OutputBuffer_get_BitsInBuffer_m1BC64A199DB5FE314B7BC8EC5E2C33FBAFF3F2E1,
	OutputBuffer_DumpState_m57A1F6434B5A148CF547962859F16582425566A7,
	OutputBuffer_RestoreState_mAEC49DDCC5C217DC8DEA903E54FA6EEFA65CBEC3,
	BufferState__ctor_mD4EF7939B6A524E2FA7A43D9CDC13DBA6CFF02FA,
	OutputWindow_Write_mF1729E92A8DC8C11F1C9E5FF2D0AA5F8347E5A46,
	OutputWindow_WriteLengthDistance_m4E301063890AB6191C76C52D9C62100A4CAE1510,
	OutputWindow_CopyFrom_m43757C0C7965E0FA9742E049D748AF654507B2D9,
	OutputWindow_get_FreeBytes_mFE312FDC636EAB44D2680BE544DFBF0EF73BABD6,
	OutputWindow_get_AvailableBytes_m6484DDBE3925F9A4E0BA9276B72AFF6F982544FF,
	OutputWindow_CopyTo_mBDC2019A354A2BF9A47B0C93A41A97122745D710,
	OutputWindow__ctor_mDFA19678376164B99DD2A90D209A26F7AFBC66E0,
	PositionPreservingWriteOnlyStreamWrapper__ctor_m00049B661A2C196DFAE1E3DC0675882180B5F879,
	PositionPreservingWriteOnlyStreamWrapper_get_CanRead_m2C7020024EF4EEC41934336D15A8C6D4A64E8F47,
	PositionPreservingWriteOnlyStreamWrapper_get_CanSeek_mD784CE0F401F4D7CC7E1823601C9EDEBFBA7C4BC,
	PositionPreservingWriteOnlyStreamWrapper_get_CanWrite_m3A0372FF77DC05CBDAD6A95D4CF1F491F617B3C1,
	PositionPreservingWriteOnlyStreamWrapper_get_Position_m55A2397F0B95DC89FD9897FB8FD4084EA8F2A66F,
	PositionPreservingWriteOnlyStreamWrapper_set_Position_m4C96655E87094384BE89FA2405862733F1C73912,
	PositionPreservingWriteOnlyStreamWrapper_Write_mD56A4F88EAF3A8B29668745D50A8C6A55FBF2CB2,
	PositionPreservingWriteOnlyStreamWrapper_BeginWrite_m4B7B086406AE3CF97F01F25100117B4DA81215D0,
	PositionPreservingWriteOnlyStreamWrapper_EndWrite_mAD27F0850F7D51F40AE2D399F2E502B2FFC2D692,
	PositionPreservingWriteOnlyStreamWrapper_WriteByte_m5102BAC7F22BE37F29B9BC2A35831E41EF60F36B,
	PositionPreservingWriteOnlyStreamWrapper_WriteAsync_mB2159B57C4D9E1A30E5A195D18263E3CA7A4BD42,
	PositionPreservingWriteOnlyStreamWrapper_get_CanTimeout_m034EF279B4BD99920211A5CA00D8FBF2FB78EF2D,
	PositionPreservingWriteOnlyStreamWrapper_get_ReadTimeout_mEDD5DC36B8264ED77A3E7AACFC743F73DEC840EF,
	PositionPreservingWriteOnlyStreamWrapper_set_ReadTimeout_m80B9CE71876163819D58911E32FF43029D0FB7CE,
	PositionPreservingWriteOnlyStreamWrapper_get_WriteTimeout_m6DC489300EE23D5D370CACFE7F1A34E1CBC57FF2,
	PositionPreservingWriteOnlyStreamWrapper_set_WriteTimeout_mC970964135EABE1545137E47C01164EBA80EEEC0,
	PositionPreservingWriteOnlyStreamWrapper_Flush_m0A388D0E61CED5B16C39CB461E7036269F71D213,
	PositionPreservingWriteOnlyStreamWrapper_Close_m97D99D4FC4B4B7DE68602FD48537DB8AD452D5B7,
	PositionPreservingWriteOnlyStreamWrapper_Dispose_m55E51A1CAF4F3235654AAC05FA33F2B90A7EA582,
	PositionPreservingWriteOnlyStreamWrapper_get_Length_m608B54560186BD70F8302A2827DDDCA901AF5967,
	PositionPreservingWriteOnlyStreamWrapper_Seek_m36B73C77EB0C77F06FC6AAE3090098A6B45FA75A,
	PositionPreservingWriteOnlyStreamWrapper_SetLength_mAD378565C6A7B472F961B01BCC77E994ABD73A63,
	PositionPreservingWriteOnlyStreamWrapper_Read_m3056C14432DC1160F0B62ABDD7389B4DC5ECFFE7,
	ZipArchive__ctor_m23DE01C72EAA87FCED326E56A8BA38A251E9C2E3,
	ZipArchive__ctor_m2706DA413E897A83057237178CCC8E51C07230B5,
	ZipArchive_get_Mode_mC02087A709688333AF6721BBBBEAE9F379D06127,
	ZipArchive_CreateEntry_m52C487BC25504F8951CE8927F64778DC417CE88E,
	ZipArchive_Dispose_m2FF60D8833B08960AE817AC92DD6D677E4DF3FD0,
	ZipArchive_Dispose_mB65DC0892D480934FBB2A8E95A2AAED07179E8B1,
	ZipArchive_get_ArchiveReader_m545504179D41E6478352D45CC9BB74589CD2770E,
	ZipArchive_get_ArchiveStream_m5A8467BA46A12C06DF191597A4810562F4B2E79F,
	ZipArchive_get_NumberOfThisDisk_m34BE6245FFCA87E7F5068C7A988922877B620DCB,
	ZipArchive_get_EntryNameEncoding_m91420D79C1E76C90B846EA536B678AD7084DF059,
	ZipArchive_set_EntryNameEncoding_mCF0292991EC16697CF419BFA0A1D42612E189625,
	ZipArchive_DoCreateEntry_m02CFFDBDCEC117EAAEDA87DB17870735E83C74DE,
	ZipArchive_AcquireArchiveStream_mD39904134200E16277221E0ABC9020F1578306E7,
	ZipArchive_AddEntry_mF0C759DC22B3087EF6D9958FF651726CE17BF1C9,
	ZipArchive_ReleaseArchiveStream_m3490E6E1E69188C52F6C95598DEACC188BAE02AE,
	ZipArchive_RemoveEntry_m56F4AFBF28A9AD87377A0C15620506207060F1D2,
	ZipArchive_ThrowIfDisposed_mCF0A1324ED33B058A30E33B00197D4EC37A74CF6,
	ZipArchive_CloseStreams_m87A073F74B899F1430CE3BB2A8C891A39ED6FB58,
	ZipArchive_EnsureCentralDirectoryRead_m31430840CA181B1DA19233BF56D2E697759CF195,
	ZipArchive_Init_m41DBA9F7EC4C8E980497A7FDA896A4470A18C04F,
	ZipArchive_ReadCentralDirectory_m1EA97D1BCAB7BC75525FE951D757C5AA9071A9AA,
	ZipArchive_ReadEndOfCentralDirectory_m8682F73E19E15118476CE915A693AAA81CDF28FB,
	ZipArchive_WriteFile_m4D3BA6D0C52BFA7159EB9B3C7FC69CE14DD61460,
	ZipArchive_WriteArchiveEpilogue_mF81EB28294C8DE727602EB7DDFF73D10EBC98E23,
	ZipArchiveEntry__ctor_mA2B6549B24B1128D6FC6BA33ED7E3FD04BD4070B,
	ZipArchiveEntry__ctor_m13DC9D74F91A3B3E61C411874E5269EA0FE02813,
	ZipArchiveEntry__ctor_mC25359CA7EF7F28F5176B84636C1CB58D3DCB179,
	ZipArchiveEntry_get_FullName_mB226F80A14EA72D5C3D63C912AD483020CE81F2F,
	ZipArchiveEntry_set_FullName_m193B830DA0C41B1D1E7B69A049AD8BD44CE63D89,
	ZipArchiveEntry_Delete_m8801F6B62B477E2C1A6FEE2210AB8497EFFA43F2,
	ZipArchiveEntry_Open_m1D0EFB9AD33BA96AAF0C624EADA3E58CD6CC67FF,
	ZipArchiveEntry_ToString_m8406E95654F42A870D498A246C8026234472CA25,
	ZipArchiveEntry_get_EverOpenedForWrite_mED5D3E58C7482A11C3E0C91F4D0F3F779C42B07D,
	ZipArchiveEntry_get_OffsetOfCompressedData_m4E6B407825F4902E2747798026CCF20063A1982E,
	ZipArchiveEntry_get_UncompressedData_m44F87FE5D136CBD5006C01163F6EBF2FDF1FAB75,
	ZipArchiveEntry_get_CompressionMethod_mA636837EB83C990C39E207F8767626EE630A282B,
	ZipArchiveEntry_set_CompressionMethod_m4BB5F8B6E417C267393D5DB6887595A5A0161771,
	ZipArchiveEntry_DecodeEntryName_mCB5F4B921507F93E2A0BC939C6B71C51C040A3C3,
	ZipArchiveEntry_EncodeEntryName_mC69108C3D6659E706949B69111C798CF292423A4,
	ZipArchiveEntry_WriteAndFinishLocalEntry_m27D6658B541689E0F5310B370B417B519A1AF05D,
	ZipArchiveEntry_WriteCentralDirectoryFileHeader_m0D461A71902AEDCA8CDA8719ED36EFA345F92053,
	ZipArchiveEntry_LoadLocalHeaderExtraFieldAndCompressedBytesIfNeeded_mF1803EF6C9574AF97616D35E8E8083F8D4590280,
	ZipArchiveEntry_ThrowIfNotOpenable_m141851EC16D13A81B699F50EB2B83506E923A995,
	ZipArchiveEntry_GetDataCompressor_m0E1AC3095B7F5E79C931ADA856E3CE56209EFD0C,
	ZipArchiveEntry_GetDataDecompressor_m7FA439A9B0E491A2C2878BDC16DEEE0C17062C63,
	ZipArchiveEntry_OpenInReadMode_mA0257EEA737BA85855C8E1D480B894BEA66B330D,
	ZipArchiveEntry_OpenInWriteMode_m7217BE297FFB16D541242B43605C83F7D4A71698,
	ZipArchiveEntry_OpenInUpdateMode_mE527744C62297D9A901C6C004DC0F74E3E21592D,
	ZipArchiveEntry_IsOpenable_mC92895385CEAC7F1EF832D962C99F3D2A3F4B4CE,
	ZipArchiveEntry_SizesTooLarge_mBF42A3A3E27152942B65447A7AB5CA89FAB46198,
	ZipArchiveEntry_WriteLocalFileHeader_m24ABCA6E8CC57BCE477DDD6E564D04DF5D9FFE91,
	ZipArchiveEntry_WriteLocalFileHeaderAndDataIfNeeded_mA95D0CE40CF84C1E7A36783DAD3280C1134EC65C,
	ZipArchiveEntry_WriteCrcAndSizesInLocalHeader_m67FE451EDE9F8A67F71AC400DF2A76F68D2206E4,
	ZipArchiveEntry_WriteDataDescriptor_m9C22F8F8EF9F7A2BC10F7D7DDBBB799D7C262246,
	ZipArchiveEntry_UnloadStreams_mAA66F564B73322E65FDA8C40C248C98DC367945C,
	ZipArchiveEntry_CloseStreams_m6A5C7BDEA3457B2EB1B30DC9E27BDD4639BDC506,
	ZipArchiveEntry_VersionToExtractAtLeast_mAE09A7CC80BDF41268A9392CB18883C2714EE8BA,
	ZipArchiveEntry_ThrowIfInvalidArchive_mF5EF732D2525CC4B388B91369554B32F5170B1C9,
	ZipArchiveEntry_GetFileName_Windows_m1C58B47F8475A5A145F94CBA61C7E983655039BC,
	ZipArchiveEntry_GetFileName_Unix_mB832EE5367BBB1B3887B5613A01E1AE31703FC58,
	ZipArchiveEntry_ParseFileName_mB97DF5D033B65F81671D63DAC56A25F2AA32DDF8,
	ZipArchiveEntry__cctor_m00A0460A3FCDC9C666E7BB099FE8AA8B283CA860,
	DirectToArchiveWriterStream__ctor_m9BB7F2B381424652C58836B4D94CB51D7EEF6A78,
	DirectToArchiveWriterStream_get_Length_mF5960C42CA8DD3ACDA89A57D744D120A22182326,
	DirectToArchiveWriterStream_get_Position_m1445767E25E4A881159BBDD9D3C7BFCB33811766,
	DirectToArchiveWriterStream_set_Position_m75999435FE3C978528F640246A704A87BC4F8BEF,
	DirectToArchiveWriterStream_get_CanRead_m5693AF05AC7763E3EF75C40D283E39B6CC3D3B6B,
	DirectToArchiveWriterStream_get_CanSeek_m0982800C7D85BAC0196762C6B4D995561C65F846,
	DirectToArchiveWriterStream_get_CanWrite_mA38DC50347D9C86FEEE193ABCA732C1B204110D0,
	DirectToArchiveWriterStream_ThrowIfDisposed_mF4D2AD303F7DAD53E17E3A4D57BFB1E94A78D861,
	DirectToArchiveWriterStream_Read_mB8DA1FEFCE7016B7E1CD529E80AD08F44ACE9EC5,
	DirectToArchiveWriterStream_Seek_m6445B6C433A3EFB99B687B9DF1DD11200787DB51,
	DirectToArchiveWriterStream_SetLength_m3EDFAB11631621DB2B28E54F7ABA1D9910CD50CF,
	DirectToArchiveWriterStream_Write_m3290C62DEC4ECF784DF923A88E04BD5DB8366FAD,
	DirectToArchiveWriterStream_Flush_mE250B983605764DE0B2857EEFD8CF9E937982807,
	DirectToArchiveWriterStream_Dispose_m3AF7D2F140CA3C62F9A44D07D1F5A6DFFFC4D2A5,
	U3CU3Ec__cctor_mFB0EA39E385CFFACC7A1E47673D4E1F267C19A74,
	U3CU3Ec__ctor_mAEA937E579A34BDF23D9107A2BE5527A46BABC69,
	U3CU3Ec_U3CGetDataCompressorU3Eb__69_0_m5DB8D5229C65AC9D1CA956550B2E9A371487ABF9,
	U3CU3Ec_U3COpenInWriteModeU3Eb__72_0_m7B46F385AC1653A77BA1FECA69143DF57FE83B10,
	U3CU3Ec_U3COpenInUpdateModeU3Eb__73_0_mF7D05AA3555DD6A7046539DD462D161EDDDFE523,
	ZipGenericExtraField_get_Tag_m99149829A339C6B07BA86F7BA89277FD61257FE7,
	ZipGenericExtraField_get_Size_m56A96EAB8BB98584F13938BEF05A3DC6CFE199C6,
	ZipGenericExtraField_get_Data_m0C9F6CC3B46705955EE5151F641D8CCE8B232C43,
	ZipGenericExtraField_WriteBlock_mE0E1FDE23207EB8B55074171E6C481A1A471F2C6,
	ZipGenericExtraField_TryReadBlock_mA9E4A7FBBB4DE351D49C168F43B0860C44F7DAFB,
	ZipGenericExtraField_ParseExtraField_m0181585E2CC292C8AB33E7A4DDF89A4C0AA6952C,
	ZipGenericExtraField_TotalSize_m5CD4706EBD9E58DC7B745B2E037113987FECEDBE,
	ZipGenericExtraField_WriteAllBlocks_mF43381AFAE22FB4676F87F423EF23479E75B9E11,
	Zip64ExtraField_get_TotalSize_m5F50EC851F8B687BFF82B1661A4E569C476A6E85,
	Zip64ExtraField_get_UncompressedSize_mC53A72CA1685391354843A9E7A47B6FE60E707B2,
	Zip64ExtraField_set_UncompressedSize_m1A0368B13AF3852DCA015652DFE6F8D9E1B2FAF1,
	Zip64ExtraField_get_CompressedSize_mD68A6FC67B58A127B2CA1353B882ABC5C4BB9B4E,
	Zip64ExtraField_set_CompressedSize_mCAA4A05A68B7D6C15724D8323B36D3482CAF29AE,
	Zip64ExtraField_get_LocalHeaderOffset_m1F7A1833A2E2782CF18E55B965FED3AD3275091D,
	Zip64ExtraField_set_LocalHeaderOffset_m523C6446CBE2710C17270A84C2D1ED68628663C8,
	Zip64ExtraField_get_StartDiskNumber_mB01F9839965C60DFFEB90C588B4FA0CA22787D5E,
	Zip64ExtraField_UpdateSize_m8FB215062A34D76FBC958859B0DB73222A3EC5BC,
	Zip64ExtraField_GetJustZip64Block_m6048EFE244AF5DED7677BEE5AAA0884A32B34D41,
	Zip64ExtraField_TryGetZip64BlockFromGenericExtraField_m5DB9958C01EDB412C5D3F8B4D9A90769ADD7D0F1,
	Zip64ExtraField_GetAndRemoveZip64Block_mB7341E90373208AB63585BC7BBE673D642C8CA0A,
	Zip64ExtraField_RemoveZip64Blocks_m3BCB96FE6A482AE8BE0D11D9D77DEACC3EAFED5A,
	Zip64ExtraField_WriteBlock_mAC1D9A401E0299A69AB49E743FF92C02E94F2BAE,
	Zip64EndOfCentralDirectoryLocator_TryReadBlock_m95F1CB20F372E7626FD6145E6B479FFE5504C4EE,
	Zip64EndOfCentralDirectoryLocator_WriteBlock_mCF0B88637AE66FD48BB08A6456A267F94D8DAE88,
	Zip64EndOfCentralDirectoryRecord_TryReadBlock_m8447AFFBFA76A08B60388C3BE333613C213EC0D1,
	Zip64EndOfCentralDirectoryRecord_WriteBlock_m2778F387199A087AEDF6D65FA80B7FCBB9DFB164,
	ZipLocalFileHeader_GetExtraFields_m0C152D89B178FD3D8DC55E216AC787EA274D84AB,
	ZipLocalFileHeader_TrySkipBlock_m8D73DBDBC22EF1E7D1499BCE781C7748EF907646,
	ZipCentralDirectoryFileHeader_TryReadBlock_mC50A29A666BDF5303461EB3AB38E2ACE0EB0BB88,
	ZipEndOfCentralDirectoryBlock_WriteBlock_m0CA333378E1B10C90EA9B21770208D30CFF33758,
	ZipEndOfCentralDirectoryBlock_TryReadBlock_m5222F66524A785C9E17515C634530CCCF932995C,
	WrappedStream__ctor_mCEFD3D801E6F14698F4BF4C2AA72A92359D62EF3,
	WrappedStream__ctor_mB68DE6EB41FFBE42DC901F28CA8754255D8DDBBE,
	WrappedStream__ctor_m52262F5CE3D365E105A01027C1E6F257D2B1B35A,
	WrappedStream_get_Length_mEB63DA68A20122010C4AB094081BD19C3AF60F64,
	WrappedStream_get_Position_m8CE0B7A544B011AE331B3923C4FA5EAB4B2012FB,
	WrappedStream_set_Position_mF978AD11D27DCFD6000C08AE3D2A1425211C2008,
	WrappedStream_get_CanRead_mF77813A5982F385FEF5486BC57423FD52C008108,
	WrappedStream_get_CanSeek_m8D5376D162563B32A7F276DA42F88B466387F8C1,
	WrappedStream_get_CanWrite_m64BCC6D462069D9484E841F21DBC90872ECAFE68,
	WrappedStream_ThrowIfDisposed_m08BAA20A4A96F141D035374821281CB59F4495F2,
	WrappedStream_ThrowIfCantRead_mF788A331845024DE9533292FA01A7A914AFD6FA9,
	WrappedStream_ThrowIfCantWrite_mFD83C4E1E9E762A2ACF0B03FDDFBE750A370D6E1,
	WrappedStream_ThrowIfCantSeek_m3B766B3922EE178758C3176CF332C329BABC6F4E,
	WrappedStream_Read_mC5207A75B422C86AA7EA03318E5F4C4D7B60CE5E,
	WrappedStream_Seek_mDD591904952C39FE0FADC8739E5EAC5EE463EB64,
	WrappedStream_SetLength_m7CF59F91FCE6AAFF912B1D360D3EE8B3608670C1,
	WrappedStream_Write_mD9C8A5A97FCBB05F635747BC891B5E196E2A262F,
	WrappedStream_Flush_m9F2CF33C2394D1C7F47B5F3CAEB43B68D56D142C,
	WrappedStream_Dispose_m1C4CA8E2CAF45DEFCF7D35949E59A31A43616804,
	SubReadStream__ctor_m3104F68BD5EF131BBC3AB455283B7CFECE493CC5,
	SubReadStream_get_Length_m927C2160A7D463BAFA4214C3983D97B5105B1EBB,
	SubReadStream_get_Position_m45D06632F50A8AA3280E413F071F9B4BF22BE09E,
	SubReadStream_set_Position_m63C61821FB7B6CE57065E92A24778FF76C57DB1F,
	SubReadStream_get_CanRead_mE027F1C797C523EA1EE533F952A7961A932AE4B6,
	SubReadStream_get_CanSeek_mF373DC87E94BACF0E2080AF657F8F789949853F0,
	SubReadStream_get_CanWrite_mCA6DF3176D34405AB4F4F11E580E18D45DB33B0D,
	SubReadStream_ThrowIfDisposed_mB2F7DB5B995EA9847EA6346E2D02FFF37ADF4AA8,
	SubReadStream_ThrowIfCantRead_m87A273A233AE230EE106DD250F36DFBA8C0A1636,
	SubReadStream_Read_mEDA8FACF0C1C1A7464D8B9E140AAD782EC130D0B,
	SubReadStream_Seek_m2448FE753333F79695AAAC4FC2E3331D4DACC527,
	SubReadStream_SetLength_m29FE28460B74DED5EB917335A0F8FED03E9E88F4,
	SubReadStream_Write_mC5B6C8136AD27CA18C7DF00DCC1477B49B471CC6,
	SubReadStream_Flush_mB4E80E151651465709E444823B0D53DC147EEEE4,
	SubReadStream_Dispose_m1190CA46857DCE02BADC6D43EE7455779078FABE,
	CheckSumAndSizeWriteStream__ctor_m2F8A286280CEE477FE04E9CB046BCD9350F3DF53,
	CheckSumAndSizeWriteStream_get_Length_m3D3EDBE5E95899B7585C8104186AD0D4D144587E,
	CheckSumAndSizeWriteStream_get_Position_mFD88EC55723C1A9FFEE2198EC4C11241EED5F5B9,
	CheckSumAndSizeWriteStream_set_Position_m43180641FAA87F88403DB9062160767B2045E1EB,
	CheckSumAndSizeWriteStream_get_CanRead_mDB591A8EAACA4BB9160CC2D88C88693A7EBFBA98,
	CheckSumAndSizeWriteStream_get_CanSeek_mD8FC46B60B7C2C0FB7784C92F0BEF4C42BBA7FE9,
	CheckSumAndSizeWriteStream_get_CanWrite_mEEA7725B766EBF5C7FB7F175C38063BB40EE0E7F,
	CheckSumAndSizeWriteStream_ThrowIfDisposed_m7FFB6BDF119D117AD15F5EBA679832B05AA401D4,
	CheckSumAndSizeWriteStream_Read_m76379DE8EAF2C40B135E81A3662A32AFF6A7D04A,
	CheckSumAndSizeWriteStream_Seek_mB4F12034E45070AF25B560C6ECE732013E098DB6,
	CheckSumAndSizeWriteStream_SetLength_m1ACC2D77585B8941447E3BCA0B2B012C2EF167FC,
	CheckSumAndSizeWriteStream_Write_mCBB708B9DA718DA879C8EDA20CA1A9B198C59F48,
	CheckSumAndSizeWriteStream_Flush_mB70E50A32551A5289D4626F94C0682983F62ACB6,
	CheckSumAndSizeWriteStream_Dispose_mCA606B257A9D2884C261314F3332BFC97AD0E19D,
	ZipHelper_RequiresUnicode_m20EAD7C8BAEB2EF5A554C761E8441AC594B6117F,
	ZipHelper_ReadBytes_m4BCFE2308C656AB48E0BC0D94FDED35AC3FCA8EC,
	ZipHelper_DosTimeToDateTime_mAC6DC9DEFADB46F3A5A0FE68F30DFAC696E2136E,
	ZipHelper_DateTimeToDosTime_m81F3D31F7D97F5209332D8BEB65C288F8D2E1AC1,
	ZipHelper_SeekBackwardsToSignature_m07FB8913C58E31276264722997B9CBE9F576EC45,
	ZipHelper_AdvanceToPosition_m37FE7419FDE8CBD9C17F1969AB585B1B85CA4DFF,
	ZipHelper_SeekBackwardsAndRead_m433E06E552871B8D2E5FF4D7EB3188282A721DC5,
	ZipHelper__cctor_mD61640F38D2D755F1FFBDCA5C2DFF863281B3AED,
	Crc32Helper_UpdateCrc32_mEF874A4EC7E5936D98AC94494D27CDBFD2E60778,
	Crc32Helper_ManagedCrc32_m53D17613D585422A4479D609FCCA9A0AD4BD3A6C,
	Crc32Helper__cctor_m56DA5438E4C3C739D0AEC99F7300DC8BA83430C7,
	TaskToApm_Begin_m1A080755817528FB342A9A1352631ACCBF9CEBAE,
	TaskToApm_End_mCD6418D34E9684DB6691549A225F5F9CC5C32753,
	NULL,
	TaskToApm_InvokeCallbackWhenTaskCompletes_m8553A6EFB5AEF7E94F4377E87ACE37D1920BC932,
	TaskWrapperAsyncResult__ctor_mF1F0F980A52876DF07F5CF9B80CDD5CAE167F26D,
	TaskWrapperAsyncResult_System_IAsyncResult_get_AsyncState_m29AE39D37A24BC4FCB79ED5F37AF16814C7DB323,
	TaskWrapperAsyncResult_System_IAsyncResult_get_CompletedSynchronously_m4DA0F3A48335A386436B255ED4D7B6EF941DC39C,
	TaskWrapperAsyncResult_System_IAsyncResult_get_IsCompleted_m02CCA4658D6C1A2F667137EB32FA082E066D3B9C,
	TaskWrapperAsyncResult_System_IAsyncResult_get_AsyncWaitHandle_mBD9F33A9F96E111E376192FC1F867CF219C8AB83,
	U3CU3Ec__DisplayClass3_0__ctor_m4232277D2AB8EE7F6F66C4565C3CD16E58132C07,
	U3CU3Ec__DisplayClass3_0_U3CInvokeCallbackWhenTaskCompletesU3Eb__0_m2C4821F622E71609CAD575ECAC6E757A4BE5CD4F,
};
extern void InputState__ctor_m9F37B404CBF38DB44663775A9B42846F68FDF0ED_AdjustorThunk (void);
extern void U3CReadAsyncCoreU3Ed__40_MoveNext_m7DE3C87E7BCC9DAC07D7DE47C989D764C8D5269D_AdjustorThunk (void);
extern void U3CReadAsyncCoreU3Ed__40_SetStateMachine_m809D8FF0E75647CDA4B31E3C430FDAEB936B6B71_AdjustorThunk (void);
extern void U3CWriteAsyncCoreU3Ed__47_MoveNext_m37704F9AAAB247E7FBB28D5FC57E9C729C5D132A_AdjustorThunk (void);
extern void U3CWriteAsyncCoreU3Ed__47_SetStateMachine_m7A3A2B8F2706E9E301A473AACFCAE8B675FBF2F2_AdjustorThunk (void);
extern void BufferState__ctor_mD4EF7939B6A524E2FA7A43D9CDC13DBA6CFF02FA_AdjustorThunk (void);
extern void ZipGenericExtraField_get_Tag_m99149829A339C6B07BA86F7BA89277FD61257FE7_AdjustorThunk (void);
extern void ZipGenericExtraField_get_Size_m56A96EAB8BB98584F13938BEF05A3DC6CFE199C6_AdjustorThunk (void);
extern void ZipGenericExtraField_get_Data_m0C9F6CC3B46705955EE5151F641D8CCE8B232C43_AdjustorThunk (void);
extern void ZipGenericExtraField_WriteBlock_mE0E1FDE23207EB8B55074171E6C481A1A471F2C6_AdjustorThunk (void);
extern void Zip64ExtraField_get_TotalSize_m5F50EC851F8B687BFF82B1661A4E569C476A6E85_AdjustorThunk (void);
extern void Zip64ExtraField_get_UncompressedSize_mC53A72CA1685391354843A9E7A47B6FE60E707B2_AdjustorThunk (void);
extern void Zip64ExtraField_set_UncompressedSize_m1A0368B13AF3852DCA015652DFE6F8D9E1B2FAF1_AdjustorThunk (void);
extern void Zip64ExtraField_get_CompressedSize_mD68A6FC67B58A127B2CA1353B882ABC5C4BB9B4E_AdjustorThunk (void);
extern void Zip64ExtraField_set_CompressedSize_mCAA4A05A68B7D6C15724D8323B36D3482CAF29AE_AdjustorThunk (void);
extern void Zip64ExtraField_get_LocalHeaderOffset_m1F7A1833A2E2782CF18E55B965FED3AD3275091D_AdjustorThunk (void);
extern void Zip64ExtraField_set_LocalHeaderOffset_m523C6446CBE2710C17270A84C2D1ED68628663C8_AdjustorThunk (void);
extern void Zip64ExtraField_get_StartDiskNumber_mB01F9839965C60DFFEB90C588B4FA0CA22787D5E_AdjustorThunk (void);
extern void Zip64ExtraField_UpdateSize_m8FB215062A34D76FBC958859B0DB73222A3EC5BC_AdjustorThunk (void);
extern void Zip64ExtraField_WriteBlock_mAC1D9A401E0299A69AB49E743FF92C02E94F2BAE_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[20] = 
{
	{ 0x0600000E, InputState__ctor_m9F37B404CBF38DB44663775A9B42846F68FDF0ED_AdjustorThunk },
	{ 0x06000030, U3CReadAsyncCoreU3Ed__40_MoveNext_m7DE3C87E7BCC9DAC07D7DE47C989D764C8D5269D_AdjustorThunk },
	{ 0x06000031, U3CReadAsyncCoreU3Ed__40_SetStateMachine_m809D8FF0E75647CDA4B31E3C430FDAEB936B6B71_AdjustorThunk },
	{ 0x06000032, U3CWriteAsyncCoreU3Ed__47_MoveNext_m37704F9AAAB247E7FBB28D5FC57E9C729C5D132A_AdjustorThunk },
	{ 0x06000033, U3CWriteAsyncCoreU3Ed__47_SetStateMachine_m7A3A2B8F2706E9E301A473AACFCAE8B675FBF2F2_AdjustorThunk },
	{ 0x06000097, BufferState__ctor_mD4EF7939B6A524E2FA7A43D9CDC13DBA6CFF02FA_AdjustorThunk },
	{ 0x06000107, ZipGenericExtraField_get_Tag_m99149829A339C6B07BA86F7BA89277FD61257FE7_AdjustorThunk },
	{ 0x06000108, ZipGenericExtraField_get_Size_m56A96EAB8BB98584F13938BEF05A3DC6CFE199C6_AdjustorThunk },
	{ 0x06000109, ZipGenericExtraField_get_Data_m0C9F6CC3B46705955EE5151F641D8CCE8B232C43_AdjustorThunk },
	{ 0x0600010A, ZipGenericExtraField_WriteBlock_mE0E1FDE23207EB8B55074171E6C481A1A471F2C6_AdjustorThunk },
	{ 0x0600010F, Zip64ExtraField_get_TotalSize_m5F50EC851F8B687BFF82B1661A4E569C476A6E85_AdjustorThunk },
	{ 0x06000110, Zip64ExtraField_get_UncompressedSize_mC53A72CA1685391354843A9E7A47B6FE60E707B2_AdjustorThunk },
	{ 0x06000111, Zip64ExtraField_set_UncompressedSize_m1A0368B13AF3852DCA015652DFE6F8D9E1B2FAF1_AdjustorThunk },
	{ 0x06000112, Zip64ExtraField_get_CompressedSize_mD68A6FC67B58A127B2CA1353B882ABC5C4BB9B4E_AdjustorThunk },
	{ 0x06000113, Zip64ExtraField_set_CompressedSize_mCAA4A05A68B7D6C15724D8323B36D3482CAF29AE_AdjustorThunk },
	{ 0x06000114, Zip64ExtraField_get_LocalHeaderOffset_m1F7A1833A2E2782CF18E55B965FED3AD3275091D_AdjustorThunk },
	{ 0x06000115, Zip64ExtraField_set_LocalHeaderOffset_m523C6446CBE2710C17270A84C2D1ED68628663C8_AdjustorThunk },
	{ 0x06000116, Zip64ExtraField_get_StartDiskNumber_mB01F9839965C60DFFEB90C588B4FA0CA22787D5E_AdjustorThunk },
	{ 0x06000117, Zip64ExtraField_UpdateSize_m8FB215062A34D76FBC958859B0DB73222A3EC5BC_AdjustorThunk },
	{ 0x0600011C, Zip64ExtraField_WriteBlock_mAC1D9A401E0299A69AB49E743FF92C02E94F2BAE_AdjustorThunk },
};
static const int32_t s_InvokerIndices[363] = 
{
	18006,
	2766,
	5809,
	13052,
	10682,
	12996,
	10629,
	12996,
	10629,
	10629,
	13346,
	10975,
	13298,
	5266,
	5711,
	1851,
	12815,
	12815,
	12815,
	12997,
	12997,
	10630,
	13298,
	4419,
	10630,
	2323,
	2735,
	13298,
	21355,
	13298,
	21355,
	13298,
	21355,
	763,
	8845,
	1649,
	771,
	2735,
	13298,
	2735,
	10442,
	10442,
	1649,
	1649,
	763,
	10682,
	1649,
	13298,
	10682,
	13298,
	10682,
	12815,
	2735,
	8845,
	3489,
	7611,
	13298,
	13298,
	13298,
	12996,
	13052,
	13298,
	12875,
	2774,
	5688,
	10682,
	10682,
	2774,
	10682,
	7736,
	7736,
	10682,
	16835,
	18522,
	20847,
	21274,
	20207,
	18235,
	21355,
	12996,
	13052,
	13298,
	13298,
	12996,
	2735,
	13298,
	4641,
	9559,
	4705,
	7736,
	1065,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	21274,
	21274,
	10682,
	21274,
	21274,
	13052,
	13298,
	8845,
	21355,
	5666,
	13298,
	2735,
	12815,
	2323,
	12815,
	7532,
	7532,
	12815,
	13298,
	21355,
	12996,
	12996,
	7685,
	13261,
	9562,
	8801,
	2323,
	12815,
	2735,
	10629,
	13298,
	13298,
	12996,
	10629,
	12996,
	10629,
	12996,
	10629,
	12815,
	10442,
	10682,
	12996,
	12996,
	10890,
	5423,
	13298,
	2735,
	2735,
	10442,
	12996,
	13395,
	11014,
	2672,
	10442,
	5266,
	4159,
	12996,
	12996,
	2323,
	13298,
	10682,
	12815,
	12815,
	12815,
	12997,
	10630,
	2735,
	763,
	10682,
	10442,
	1649,
	12815,
	12996,
	10629,
	12996,
	10629,
	13298,
	13298,
	10442,
	12997,
	4419,
	10630,
	2323,
	5681,
	1890,
	12996,
	9272,
	10442,
	13298,
	13052,
	13052,
	13261,
	13052,
	10682,
	4477,
	10682,
	10682,
	10682,
	10682,
	13298,
	13298,
	13298,
	2732,
	13298,
	13298,
	13298,
	5624,
	5718,
	2774,
	5688,
	13052,
	10682,
	13298,
	13052,
	13052,
	12815,
	12997,
	13052,
	13260,
	10890,
	9272,
	4479,
	13298,
	13298,
	12815,
	4722,
	2390,
	9272,
	9254,
	13052,
	13052,
	2087,
	12815,
	7572,
	13298,
	10442,
	13298,
	13298,
	13298,
	10890,
	13298,
	20515,
	20515,
	17991,
	21355,
	5688,
	12997,
	12997,
	10630,
	12815,
	12815,
	12815,
	13298,
	2323,
	4419,
	10630,
	2735,
	13298,
	10442,
	21355,
	13298,
	404,
	5688,
	10682,
	13260,
	13260,
	13052,
	10682,
	16028,
	20515,
	20211,
	18814,
	13260,
	12486,
	10140,
	12486,
	10140,
	12486,
	10140,
	12484,
	13298,
	14836,
	13873,
	14836,
	20847,
	10682,
	17470,
	18808,
	17470,
	15646,
	20515,
	19891,
	16018,
	14790,
	17470,
	5666,
	1849,
	2778,
	12997,
	12997,
	10630,
	12815,
	12815,
	12815,
	13298,
	13298,
	13298,
	13298,
	2323,
	4419,
	10630,
	2735,
	13298,
	10442,
	2755,
	12997,
	12997,
	10630,
	12815,
	12815,
	12815,
	13298,
	13298,
	2323,
	4419,
	10630,
	2735,
	13298,
	10442,
	431,
	12997,
	12997,
	10630,
	12815,
	12815,
	12815,
	13298,
	2323,
	4419,
	10630,
	2735,
	13298,
	10442,
	19891,
	16902,
	19962,
	20737,
	17484,
	18808,
	16030,
	21355,
	15517,
	15517,
	21355,
	16440,
	20847,
	0,
	16904,
	2766,
	13052,
	12815,
	12815,
	13052,
	13298,
	13298,
};
static const Il2CppTokenRangePair s_rgctxIndices[1] = 
{
	{ 0x06000163, { 0, 6 } },
};
extern const uint32_t g_rgctx_Task_1_t0F04FA2EF346B8D6FC604E9CC320ECCB1447FBC5;
extern const uint32_t g_rgctx_Task_1_GetAwaiter_m7142C93279D1310D3AA70A1DD5B2BDB3F3973F51;
extern const uint32_t g_rgctx_TaskAwaiter_1_tFF0E42528AF4DA67FC4DACCAEAE96652B3CD0094;
extern const uint32_t g_rgctx_TaskAwaiter_1_GetResult_mC3DBC86553501E92B986149379956875D0BE59A9;
extern const uint32_t g_rgctx_TaskAwaiter_1_tFF0E42528AF4DA67FC4DACCAEAE96652B3CD0094;
extern const uint32_t g_rgctx_TResult_tC359688CE8922A2A7D062B1043714219C979482F;
static const Il2CppRGCTXDefinition s_rgctxValues[6] = 
{
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Task_1_t0F04FA2EF346B8D6FC604E9CC320ECCB1447FBC5 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Task_1_GetAwaiter_m7142C93279D1310D3AA70A1DD5B2BDB3F3973F51 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TaskAwaiter_1_tFF0E42528AF4DA67FC4DACCAEAE96652B3CD0094 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TaskAwaiter_1_GetResult_mC3DBC86553501E92B986149379956875D0BE59A9 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TaskAwaiter_1_tFF0E42528AF4DA67FC4DACCAEAE96652B3CD0094 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TResult_tC359688CE8922A2A7D062B1043714219C979482F },
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_System_IO_Compression_CodeGenModule;
const Il2CppCodeGenModule g_System_IO_Compression_CodeGenModule = 
{
	"System.IO.Compression.dll",
	363,
	s_methodPointers,
	20,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	1,
	s_rgctxIndices,
	6,
	s_rgctxValues,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};

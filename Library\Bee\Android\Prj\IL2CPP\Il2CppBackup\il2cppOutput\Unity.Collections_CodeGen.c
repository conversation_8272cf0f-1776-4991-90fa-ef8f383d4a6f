﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"


extern const RuntimeMethod* RewindableAllocator_TryU24BurstManaged_mBB6DAE6A8CDB2E3626C38F3B65186AAF6ACBF6E8_RuntimeMethod_var;
extern const RuntimeMethod* RewindableAllocator_Try_mA4AF5A5088097CB6343C3CC97058959976372C35_RuntimeMethod_var;
extern const RuntimeMethod* SlabAllocator_TryU24BurstManaged_mC48F05E806431B6537727E4D6A10550207FBB1EA_RuntimeMethod_var;
extern const RuntimeMethod* SlabAllocator_Try_mCD7DED588811A6E3F78E4A14CBFE2852D8E39DEB_RuntimeMethod_var;
extern const RuntimeMethod* StackAllocator_TryU24BurstManaged_mB88D607AA12E4D9181BF1FFE81A1AC3117FDB5E2_RuntimeMethod_var;
extern const RuntimeMethod* StackAllocator_Try_m093FA501B1B427E32DD9F654380B3EA56A5A4234_RuntimeMethod_var;



extern void EmbeddedAttribute__ctor_mB9EA4CCF3A3DC39A3BC92CFE9557FFAA77D15404 (void);
extern void IsUnmanagedAttribute__ctor_m15974D59768AFF916E346F7107F7FF7F6AD9099C (void);
extern void EarlyInitHelpers_JobReflectionDataCreationFailed_m385E2249E15484F8BCB1A1EA9BA9AFB7EB425109 (void);
extern void RegisterGenericJobTypeAttribute__ctor_m477E5F1C2EA7264CD6533A7B415C6D76D9538D94 (void);
extern void DOTSCompilerGeneratedAttribute__ctor_m8689CDD675567BC580F1FADCCF386B0FEE07B0E5 (void);
extern void AllocatorManager_Free_mB8AE9C4CB989A9121F4E3F2E6C7781076DFB3025 (void);
extern void AllocatorManager_CheckDelegate_m52D3F12472A2BBC5A28D2F4B5011B19D2E36AC61 (void);
extern void AllocatorManager_UseDelegate_mEB18420309DAA2CC710BA123C6996C9FB6FC3798 (void);
extern void AllocatorManager_allocate_block_mBEB6E6FDC334118DB679CF2619EBB3FF4FDD7FB5 (void);
extern void AllocatorManager_forward_mono_allocate_block_mD2A9A49DFC8CBDC39F27E2749048ABC91E124519 (void);
extern void AllocatorManager_LegacyOf_mAD212C1A7F5295C8987A6C9D7F81E8FF42E0A3BF (void);
extern void AllocatorManager_TryLegacy_mF4F0B8CE7B0293504FA12A6F9C4ACFF28B59FF79 (void);
extern void AllocatorManager_Try_m24A6A4EF594F8909B5677C94C4788F365E02E7F9 (void);
extern void AllocatorManager_Register_mEA1DA775A01BA193E46C21C82BA7DD7215086E23 (void);
extern void AllocatorManager__cctor_m3E94344CB4CD852C9427FE9394EBE4EC36BFEEA1 (void);
extern void AllocatorManager_InitializeU24StackAllocator_Try_000000A2U24BurstDirectCall_mC9E8A64AA142C1819D5DA507431A5C98D70C7F65 (void);
extern void AllocatorManager_InitializeU24SlabAllocator_Try_000000B0U24BurstDirectCall_m9FEBDDDBCD595B109E3EEAC62843A3D9809F8BAD (void);
extern void TryFunction__ctor_m10C4A7B32E87301727B84D8CBA081FABAE3CCE53 (void);
extern void TryFunction_Invoke_mED723D46A7B0C4B590ABECE0868EA02AD94D07A2 (void);
extern void AllocatorHandle_get_TableEntry_m09C5D57FF627FEAD3AAE73FB28CB4B9EEB811A63 (void);
extern void AllocatorHandle_get_IsInstalled_mB38CD887177A87128DC9A2DE6F866F9EC18FA907 (void);
extern void AllocatorHandle_Rewind_mC0426BBD1E638878C33164F91AC3ADC66C850AC1 (void);
extern void AllocatorHandle_Install_m0526A06766A02754698DE0115B926C15566CDD3B (void);
extern void AllocatorHandle_op_Implicit_mDCF4431F31BB4A09438AE644785C4273F86B2B8D (void);
extern void AllocatorHandle_get_Value_m24A0A3E433794106E43E9140CC2BB55493C8F30F (void);
extern void AllocatorHandle_get_Function_m031BF3715926C775DC8C946F6B039F99D738C930 (void);
extern void AllocatorHandle_Try_m4E8677E100693A0F015076408244AF2BA827CBC9 (void);
extern void AllocatorHandle_get_Handle_m440EA9B9A4306115087775DA2AA0AC034107D0E2 (void);
extern void AllocatorHandle_set_Handle_mA11E567A5CF5D42E8A5DC080DF007789B47B1648 (void);
extern void AllocatorHandle_get_ToAllocator_m34C297958A940D9D35C11B3D28B8E5A08E170FDF (void);
extern void AllocatorHandle_Dispose_mB74CBC8980962C016A6C85F09D3F94775A2C58E3 (void);
extern void Range_Dispose_mEABC7B5F5F72CBAC69BAB12C80B8EDEE86B3737A (void);
extern void Block_get_Bytes_m4BB90CD1C72FC56C573BE09951BF1CA68E8BD7D7 (void);
extern void Block_get_AllocatedBytes_mFE126B221F670BDA89C5D59C9E63C9724F79BE08 (void);
extern void Block_get_Alignment_mC2388F87008B204A333664B9323BD38AA20FD633 (void);
extern void Block_set_Alignment_m4E74D90E827C5F58B3EBE91B2F5B097388E0500F (void);
extern void Block_Dispose_m38B474EF3D6A142344D0A58651319FD039FB54ED (void);
extern void Block_TryFree_m0C5FA80AF51DA6F40F40DAE7E97975C3BB402225 (void);
extern void StackAllocator_get_Handle_m31D630726EF42BC6C432F8751E6B2BE346BB2267 (void);
extern void StackAllocator_set_Handle_m0C72B8ACB04FF327C6CB7787CEF767413DCCF003 (void);
extern void StackAllocator_Try_m46B9AAB118C2ACCDB8B0A54DA8E27209CA152558 (void);
extern void StackAllocator_Try_m093FA501B1B427E32DD9F654380B3EA56A5A4234 (void);
extern void StackAllocator_get_Function_m86410BC7650042F3F7FF4016A8E9E6164C226EB9 (void);
extern void StackAllocator_Dispose_m0872D5C01F22CD4BCCF71EA0185DB18EA14D62CB (void);
extern void StackAllocator_TryU24BurstManaged_mB88D607AA12E4D9181BF1FFE81A1AC3117FDB5E2 (void);
extern void Try_000000A2U24PostfixBurstDelegate__ctor_mD47C6C96D42CA4BEFBF90C613E9EA003EDE6F401 (void);
extern void Try_000000A2U24PostfixBurstDelegate_Invoke_m36548B4D971D594B65F761440A46C50609DC8246 (void);
extern void Try_000000A2U24BurstDirectCall_GetFunctionPointerDiscard_mE7ED43A08EDAF97A8E163D7569089142E2E89E6A (void);
extern void Try_000000A2U24BurstDirectCall_GetFunctionPointer_m77A92FE9E3067F3ECBBA8911E283A16AE2107EC9 (void);
extern void Try_000000A2U24BurstDirectCall_Constructor_mAA9397F0B87F27DE392B95B8181C127A3D1338D5 (void);
extern void Try_000000A2U24BurstDirectCall_Initialize_m39B3D67471DACBFFB1F8A03CA8B20D71331D487F (void);
extern void Try_000000A2U24BurstDirectCall__cctor_m923D784BB2F21546516D8375B907F292A3688E1B (void);
extern void Try_000000A2U24BurstDirectCall_Invoke_mFDFEEA7225A6C3FC09BE5C42BFDC6E20298E62A2 (void);
extern void SlabAllocator_get_Handle_m09C2CCD8BCEA26214DC514707F222B228A1E04BE (void);
extern void SlabAllocator_set_Handle_m31EE421CD1B214F4BA885C5BB166C9834A7DC22F (void);
extern void SlabAllocator_get_SlabSizeInBytes_mFFD79D4D6B544F1C21CB0EB40BBDFF5D5477A612 (void);
extern void SlabAllocator_Try_mE7D3D58D59FB68AE5FA9FF99C0C69FD854F482CD (void);
extern void SlabAllocator_Try_mCD7DED588811A6E3F78E4A14CBFE2852D8E39DEB (void);
extern void SlabAllocator_get_Function_m907185458A4C86A6B134EF91E6F323520CEEFB2D (void);
extern void SlabAllocator_Dispose_m9ED75718657190884C0327489A9AE9DB525D7912 (void);
extern void SlabAllocator_TryU24BurstManaged_mC48F05E806431B6537727E4D6A10550207FBB1EA (void);
extern void Try_000000B0U24PostfixBurstDelegate__ctor_m3F1280871ABAD11D51D0CD4BE042A70E4E9275BF (void);
extern void Try_000000B0U24PostfixBurstDelegate_Invoke_m7634CB51D6C7BE5653AD14E399D662D549218637 (void);
extern void Try_000000B0U24BurstDirectCall_GetFunctionPointerDiscard_m5B0BAE81E17C180B578BAEF6468BE062FBCF592E (void);
extern void Try_000000B0U24BurstDirectCall_GetFunctionPointer_m971F771C3CAFF2B2B39C2308B6DB0D2FEB5F7D63 (void);
extern void Try_000000B0U24BurstDirectCall_Constructor_mF8481155C303826FEE9A27A2C2330FE50CB4D121 (void);
extern void Try_000000B0U24BurstDirectCall_Initialize_m3EBB7AA40269601C75C5448A2320AEC603DCE001 (void);
extern void Try_000000B0U24BurstDirectCall__cctor_m1FA86193EBD7241849C669FF5B78041D6247BD77 (void);
extern void Try_000000B0U24BurstDirectCall_Invoke_mD87D11B9A3418C18E24861476A0E2A4915D3A91A (void);
extern void IsInstalled__cctor_m044184B83FFFB2D0845917AAA1E28EB06CA4E277 (void);
extern void TableEntry__cctor_mCA16889126B2ED5EF69666F8B0376FCC8834FCE1 (void);
extern void Managed_RegisterDelegate_m494ED81F0C1945174DD1E82D6711EB329255E36F (void);
extern void Managed_UnregisterDelegate_mE60C615D7705C878A66FB0AA06C7EE86A2359D46 (void);
extern void Managed__cctor_mE3BC99DF4AF7BC63DE01424848BDC790B53500BA (void);
extern void CreatePropertyAttribute__ctor_mFD5BF090F42312F62534D903F2D804C19CE16264 (void);
extern void BurstCompatibleAttribute_set_GenericTypeArguments_mBE8AE08E0824187C5B48F5132C73624CAB2C6906 (void);
extern void BurstCompatibleAttribute__ctor_m6D43ED0B880EE6FCCCBC38B1A63DB493898DD3BA (void);
extern void NotBurstCompatibleAttribute__ctor_mF89AC194CF3CC2F03A673D1FBDE969E1751CB827 (void);
extern void CollectionHelper_Log2Floor_m67F9EE2135763C03633748FD8E819C2D3F46C1ED (void);
extern void CollectionHelper_Hash_mFB14DD4BA7288CEDF90E514A9397FB9C27E55293 (void);
extern void CollectionHelper_ShouldDeallocate_m505E7EDBA71F02BAF52CC9DCD7C593CDA85D5465 (void);
extern void CollectionHelper_AssumePositive_mD1EC1F05F50F605141D9BA5D70C4332AC902B4B1 (void);
extern void Long1024_get_Length_m9FBD7E595A95159BC3DD170F7B715C8DF7BFF520 (void);
extern void Long1024_set_Length_mF197E52E1E211AC830C80AE4901AF50C5B9C2C71 (void);
extern void Long1024_ElementAt_m14B27AD662901124F5CD90CA28E5B0F15953C185 (void);
extern void ConcurrentMask_longestConsecutiveOnes_mD4C89063785B1BDCE865CAAD89B7B99D364EF5E5 (void);
extern void ConcurrentMask_foundAtLeastThisManyConsecutiveOnes_m637F08D215E90D433E8E1997B9F312E783909E3B (void);
extern void ConcurrentMask_foundAtLeastThisManyConsecutiveZeroes_mD644F2E85E07262C1CEFDA2535DEB710F67E5B0F (void);
extern void ConcurrentMask_Succeeded_m0EADE2AF3FA5CF6D4C93A37649EED1471C025F3F (void);
extern void ConcurrentMask_MakeMask_m58F7E98620D891775026140905A5BD1E65C6A7BB (void);
extern void ConcurrentMask_TryFree_m8EA4E66619A5DE669C97CD22210F9060C31759AB (void);
extern void ConcurrentMask_TryAllocate_m17B10152E36D67B7D2DE828BF62B0F4C08ECC4E6 (void);
extern void FixedString32Bytes_GetUnsafePtr_mA653D9296813A2EA93C0D6FC501500008121C3A8 (void);
extern void FixedString32Bytes_get_Length_mA349139D3C731DF762B4124B833B60BF0B283797 (void);
extern void FixedString32Bytes_set_Length_m9BD2B7F7AA48AF38F72C4C3A3C00C9324F39FA8F (void);
extern void FixedString32Bytes_get_Capacity_m4CEE4CE0CC97C33ED774FACD8211D766FEB549E2 (void);
extern void FixedString32Bytes_TryResize_m2E7F8E8810492683C24102604F65EA3294BF6150 (void);
extern void FixedString32Bytes_ElementAt_mCB76C2A56E3E397B8038E51DDEF9FD23571F8806 (void);
extern void FixedString32Bytes_GetEnumerator_m42731C60A6429D594719D1951D8AC6A9D6D4BF75 (void);
extern void FixedString32Bytes_CompareTo_m79B33E69FCFCD08E454F2DEE35FC5F1C9C64E425 (void);
extern void FixedString32Bytes_Equals_m1F50C94DF6B470C0BD1C56421A566EE556341766 (void);
extern void FixedString32Bytes__ctor_m1C1C73C55B3D020EA211DE2E9E6C7CF0400C6408 (void);
extern void FixedString32Bytes_Initialize_m42C7A7BFFFE132CCD2DB5984E779B4467F88D4AC (void);
extern void FixedString32Bytes_CompareTo_mD37FD30C7E3F389CC41E9E1AC9D3D655CA875D19 (void);
extern void FixedString32Bytes_op_Equality_m1426A50ADD3772C2008B14D276BD5C10A5D92573 (void);
extern void FixedString32Bytes_Equals_m7CA0083FD7E3A4F0ECEC273977A88425F13806E6 (void);
extern void FixedString32Bytes_CompareTo_mFC8EDD03F444D1B497D163FBF38A413A5DF47E70 (void);
extern void FixedString32Bytes_op_Equality_m0F27AD70B9D59163D745A9CCC47A0F20739C25F6 (void);
extern void FixedString32Bytes_Equals_m5E5B5F1ACC5BF8C93EF3A97BFB3CC1338F7D5A1D (void);
extern void FixedString32Bytes_CompareTo_m77F538DDC2D8A7B5EFF36482F83E5B9DDCFE08D8 (void);
extern void FixedString32Bytes_op_Equality_m8798D9727925F686AC8BB99439AB90C546A4D6C8 (void);
extern void FixedString32Bytes_Equals_m84D35401C5A983408E4B4D26903EB05BBE05A53A (void);
extern void FixedString32Bytes_CompareTo_m50123082F19E44CB94E7F56E28AD0119C3DE53F8 (void);
extern void FixedString32Bytes_op_Equality_m856509D8F9C3F5E0895D7CBEBD74884F656A224B (void);
extern void FixedString32Bytes_Equals_m23ABF390E00DDB6B6D391EFC121CF7474FD751C5 (void);
extern void FixedString32Bytes_CompareTo_mB5BB0CB6D7DE907B07C07BB20D5E1F8A74EA8EF8 (void);
extern void FixedString32Bytes_op_Equality_mF58CBA454E17E8C74B81BF854A277C9576E3AB67 (void);
extern void FixedString32Bytes_Equals_mAAD5520503E00E6340BAA463BC027C035F7C8317 (void);
extern void FixedString32Bytes_ToString_mCDBDE58EDFFA82B48A8613E724F92305B4C84914 (void);
extern void FixedString32Bytes_GetHashCode_m699B6C9D56B99126CB2F988A01DC87DF8A9CCFFD (void);
extern void FixedString32Bytes_Equals_m95DCBCE85E03295E539FF672D39977CDF1233647 (void);
extern void Enumerator__ctor_m23B356A7343A215746DB725E4E273A675CC41A5E (void);
extern void Enumerator_MoveNext_m73472085BD2AC39AA22ACCAFC95F18489EAEEEE4 (void);
extern void Enumerator_Reset_mDE0608807936013524A168796EDF6D505449D80D (void);
extern void Enumerator_get_Current_mC9F761A2230A3C6148795946B81AAFD09EADF0A8 (void);
extern void Enumerator_System_Collections_IEnumerator_get_Current_m4CDA6A026F68F400E0DFDAEB26184631B55A30B1 (void);
extern void FixedString64Bytes_GetUnsafePtr_mB61C0A15578E5DCFB19AC3A6504D66B4DAA88C6E (void);
extern void FixedString64Bytes_get_Length_m7FC1591B9CBA2113727D06FC5EA43AE4A5F16BF5 (void);
extern void FixedString64Bytes_set_Length_mF1C5F76690D2DB3BE7F6440213FA758E803462C6 (void);
extern void FixedString64Bytes_get_Capacity_mDA79A4601FF4672BDDCCB90EA4EF40613F9C35B0 (void);
extern void FixedString64Bytes_TryResize_m9B49BA15D7B2DFE50F4482E4113EE8E44EE9F138 (void);
extern void FixedString64Bytes_ElementAt_mCD9252823934D28D6921E529D9777BC8DD420EFD (void);
extern void FixedString64Bytes_GetEnumerator_m646BE217BF88AEC39B4D2F377059EAB5E4B4F571 (void);
extern void FixedString64Bytes_CompareTo_m982D1C7FF590144B881A32008C73F1E3A1A8A63E (void);
extern void FixedString64Bytes_Equals_m9E74082A6DAD750845D5D82DCFFCDE5141056D1E (void);
extern void FixedString64Bytes__ctor_mAC760222B77A7BB28B5A7E9A00B9168F3B99F211 (void);
extern void FixedString64Bytes_Initialize_m82ABCB4DCF0902729F32BED310A7EC5F6C8021C4 (void);
extern void FixedString64Bytes_CompareTo_mC70C4EB2FFEEB4DD7372F57E2D3E5DE3B1773E2F (void);
extern void FixedString64Bytes_op_Equality_m3B842D63D93646FC370B5E22A8CFFE176AEE8692 (void);
extern void FixedString64Bytes_Equals_m48F66EA24CE289A0EF42E422ACE3DD222D5011F1 (void);
extern void FixedString64Bytes_CompareTo_mF3A1A476403FA9932C229A1A60D43575087D3D4F (void);
extern void FixedString64Bytes_op_Equality_m6482029A475ADE0C314A5EC31BF89F4773854DA1 (void);
extern void FixedString64Bytes_Equals_m5AA29267D5B6D641B68732BFD430646971ECD62D (void);
extern void FixedString64Bytes_CompareTo_m3295FA78979CDA555DFA04CF29CFF649439B8711 (void);
extern void FixedString64Bytes_op_Equality_m35753FD7FE758FBDF1D107EBAF7405E7D2D778D6 (void);
extern void FixedString64Bytes_Equals_m5A4B5CD2579DAB7CA024749046D711F4621D29E2 (void);
extern void FixedString64Bytes_CompareTo_m19F3795ED3A69BD3F5BC9C3B4C7E35897344AB50 (void);
extern void FixedString64Bytes_op_Equality_m34038710CBDB51FC825814313571D76EF29BE475 (void);
extern void FixedString64Bytes_Equals_mA2CAA548B481B1BBD0A0DC745DEAC5D7824F6970 (void);
extern void FixedString64Bytes_CompareTo_m9407A8045F52D4C006BB232ED67AE6A6C4F56962 (void);
extern void FixedString64Bytes_op_Equality_mE056F9BEACA73D92F6DDF5EC51FB12BDC7A1CB57 (void);
extern void FixedString64Bytes_Equals_mA9BDDEACE5792EA7513B082B280999828FB6C1EB (void);
extern void FixedString64Bytes_ToString_m67C6568EB9ED23E3B624E581A57E5BE8749A254E (void);
extern void FixedString64Bytes_GetHashCode_mECC24662CA8517B714FEE61D8CD82E7A30BF76D4 (void);
extern void FixedString64Bytes_Equals_m0E3F24AA5E7B50BA24D6CEE92A0617E115A86ED9 (void);
extern void Enumerator__ctor_mBB5217F352FC6C14E9D4A64DA1865E56E6778AD1 (void);
extern void Enumerator_MoveNext_m9C6D69E2B3854694ACCE564B8E9850FEA10AD271 (void);
extern void Enumerator_Reset_mC5DE7DA1196A752281928B8B91A6AA62319D9443 (void);
extern void Enumerator_get_Current_mBCC0C54F9A50E0BD2661EEF1F43C7ACC7A4904F9 (void);
extern void Enumerator_System_Collections_IEnumerator_get_Current_m438D18EF1170C1DEFA7979F9B79CEE90FB7B4B1C (void);
extern void FixedString128Bytes_GetUnsafePtr_m5F280F7783EB21A69893D7FEF551F0E68D3E51BA (void);
extern void FixedString128Bytes_get_Length_mB01AB46B1F5415C0379CCAF98ED2AAE6F08C242E (void);
extern void FixedString128Bytes_set_Length_mBBB43CE32D9C6285009629AE64BC6C34759C2ED2 (void);
extern void FixedString128Bytes_get_Capacity_mD161D26116BAA25254B2CCFAB79975FFDA9CA711 (void);
extern void FixedString128Bytes_TryResize_mF1DCAFDBD573E0CCC60E0A534A5E854434B6BC39 (void);
extern void FixedString128Bytes_ElementAt_mED6187F2E6FEADD9A48BF05E54FE7375989B686D (void);
extern void FixedString128Bytes_GetEnumerator_mB563C36FDCB2BD71EE2C8C959983D5B81EA84ECE (void);
extern void FixedString128Bytes_CompareTo_m8FA619D7CD1ADD9AFC7D92148025CB181A117470 (void);
extern void FixedString128Bytes_Equals_mAD97C501810E69B444947F8169017A4A132DCE8B (void);
extern void FixedString128Bytes__ctor_mB0E30CB7FA5CAFF30C1D4FDE75043FD7D7F2AA40 (void);
extern void FixedString128Bytes_Initialize_m4A2BA0C3501339D697E1BCAA11763D6BAA45E1C4 (void);
extern void FixedString128Bytes_CompareTo_mF9C0984E47EEB20E9E28A4AAE1275E59E42D81B5 (void);
extern void FixedString128Bytes_op_Equality_m5F5BA10BF7DCBB3E8DFE55BA6AA3D0B515162637 (void);
extern void FixedString128Bytes_Equals_m5C92E72BEBE4D7E67AF7646C4293F3F9EA7E33EC (void);
extern void FixedString128Bytes_CompareTo_mB8E2D570CDCEE17387FD2BDA4BD35F9AD5C311A8 (void);
extern void FixedString128Bytes_op_Equality_mE6BF574CF44C2046A0D99E8630938F92FA6944A8 (void);
extern void FixedString128Bytes_Equals_mC7C2AEBA071128B24EB0BD130ED6423EA46275C1 (void);
extern void FixedString128Bytes_CompareTo_m3A02146ED7EA6B833583D2C1B38F6FF5B0870E6F (void);
extern void FixedString128Bytes_op_Equality_mE119008C751060334BD3CECF7B3D0B74B288F684 (void);
extern void FixedString128Bytes_Equals_m3B91B177B21344B9CB879DFDAA00AB7E812AD5AB (void);
extern void FixedString128Bytes_CompareTo_m7DAC821750F1242E519D29D8A1A01ABD6B5630C6 (void);
extern void FixedString128Bytes_op_Equality_m585E99914214C76581B50D2279924B42A6E5ADD1 (void);
extern void FixedString128Bytes_Equals_m02FB7C6C4862D4F44EDC97E72A4CADD0BD379D5F (void);
extern void FixedString128Bytes_CompareTo_m340091A89F740B13DE44A1F3AE938A14AE658A4B (void);
extern void FixedString128Bytes_op_Equality_mF721475F602666C9A09E70EB8C08B0D2DF7D83EA (void);
extern void FixedString128Bytes_Equals_m0A10CFEB353647A220B407CCEBE12035F0C1D4A2 (void);
extern void FixedString128Bytes_ToString_m1CD5B095D5A80759EF2E7F60AA95921369958A29 (void);
extern void FixedString128Bytes_GetHashCode_mB211F7E224953364EE91770921BA59760A0E4428 (void);
extern void FixedString128Bytes_Equals_mF8C053D97C4EA171FF1A82613C836A231DDF0DD9 (void);
extern void Enumerator__ctor_mDFC9DFC8E311728973558AFBE6406A5ACCEBD703 (void);
extern void Enumerator_MoveNext_mC0931D9551212966F0C15910269D8E72B140D860 (void);
extern void Enumerator_Reset_m5A3D75D51C04D88B8396AC90C18082C3654F9D61 (void);
extern void Enumerator_get_Current_mECFDD800F6B12953D71596BC5256B904E30CBD57 (void);
extern void Enumerator_System_Collections_IEnumerator_get_Current_m6A2804C78C30C5F9F1D0CDF4B5517C04CB17EA68 (void);
extern void FixedString512Bytes_GetUnsafePtr_m6BE151F6C1AC2994321FFC794DD61AEE5E915E05 (void);
extern void FixedString512Bytes_get_Length_m07F07806B00F694DA08ED7C3BC04C9FCE7CE4E07 (void);
extern void FixedString512Bytes_set_Length_m5BECBE3190D559780E9F84276928B0EC612A1BDE (void);
extern void FixedString512Bytes_get_Capacity_m80069D7197EA5C9AC0149AEE21591CCCFBC0AB55 (void);
extern void FixedString512Bytes_TryResize_m924FCE76C50EFD1773E2A417BCA84B2C6B158ABD (void);
extern void FixedString512Bytes_ElementAt_m7B6C3BFA049CBE0952188BD01720D28505483485 (void);
extern void FixedString512Bytes_GetEnumerator_mF1B489F6A1B771B4A9D21F8C58BDD76969106703 (void);
extern void FixedString512Bytes_CompareTo_mF2E8A9C2171833229322FEBEF4DF783251513B59 (void);
extern void FixedString512Bytes_Equals_mE4279608ABCBD5236ADF59E96788F0837787F11A (void);
extern void FixedString512Bytes__ctor_m15D957FCF419703D82533C04DAC6565D2AE139A4 (void);
extern void FixedString512Bytes_Initialize_m960C30A7580B4016E4EB827FF9DC2D2B009E85F2 (void);
extern void FixedString512Bytes_CompareTo_mFEA161B206C05F7894DDF0CAB24CDBF525AD9F97 (void);
extern void FixedString512Bytes_op_Equality_m9396081EA8C88FEA2006D359260245627184D456 (void);
extern void FixedString512Bytes_Equals_m2AF82E5FAAF96DBD0A105DF94BD1287804D6D568 (void);
extern void FixedString512Bytes_CompareTo_mCF6F5BD2E7F95763F8903514954023697C1A3838 (void);
extern void FixedString512Bytes_op_Equality_m72AD5D8E203AEBD992FFDB15E2AA658549127C77 (void);
extern void FixedString512Bytes_Equals_m14403CF490B635D08B4FD2DCAB8473AD2A510CE6 (void);
extern void FixedString512Bytes_CompareTo_m80D4CAD931945D04AE44A9610D72D8E0839FB2B5 (void);
extern void FixedString512Bytes_op_Equality_m402E2B00BB62CC4AA6E20DBE2A763A64D957A951 (void);
extern void FixedString512Bytes_Equals_m865E75EE8511CFC6CB527D86E54AEE7AE010449A (void);
extern void FixedString512Bytes_CompareTo_m42E210C85C1D09E6698DCDDB40545351895E2574 (void);
extern void FixedString512Bytes_op_Equality_m2D57D6666092B59CE4541DB706A388DA6F7332C0 (void);
extern void FixedString512Bytes_Equals_m34F02FA9086BE42B8856DF5A85DA039FD3036E99 (void);
extern void FixedString512Bytes_CompareTo_mEC561BBFD17069268207BF2D6BD6E2C93E90A1B7 (void);
extern void FixedString512Bytes_op_Equality_m6C60AF29D4AFBB2FCD3F347EB45D4F4F2787AAFF (void);
extern void FixedString512Bytes_Equals_mE00AFBE32D04B3671E8D476855409B35B5C8E674 (void);
extern void FixedString512Bytes_op_Implicit_mCF055F8B2FB98005951417E9FE994E3D230F58E4 (void);
extern void FixedString512Bytes_ToString_m1A47583FB34608DBEEDC65F9CA6E7B8E7930233F (void);
extern void FixedString512Bytes_GetHashCode_m3F22B7550BFBA5E580D804CCAC7528BA8F64862B (void);
extern void FixedString512Bytes_Equals_m344621B84C32BCC845396B588F2AE18154B9C6C3 (void);
extern void Enumerator__ctor_m2273AB72F8E086A370110A63E5B514834696D916 (void);
extern void Enumerator_MoveNext_m30A057A8CC9ADF95DF2D81CA7A10D1E5CA1DBEE8 (void);
extern void Enumerator_Reset_m76202CD1C0493A8DFF8D04AEF5654A4CBA3AC0ED (void);
extern void Enumerator_get_Current_m8120FC6DFAFEA5A1431B2F74BA6C1A59E546C210 (void);
extern void Enumerator_System_Collections_IEnumerator_get_Current_mFB83F84FB64A9ADDBD4C3AED4BA70B2DB349BCBA (void);
extern void FixedString4096Bytes_GetUnsafePtr_mE1DB42C2C0EEA7D6E1B5D0366A4727125D916EEB (void);
extern void FixedString4096Bytes_get_Length_mC2A80F2813739852F0675DA8D2AB68AA92BFC36F (void);
extern void FixedString4096Bytes_set_Length_mB4335BCB8388D446FF0C612F81355BD4861F4F8B (void);
extern void FixedString4096Bytes_get_Capacity_m55EE381DB20EC9C738999C9F562700569906434D (void);
extern void FixedString4096Bytes_TryResize_mC7B1C3EF58A73536BD8A17AEA540B05D1FC4A8F7 (void);
extern void FixedString4096Bytes_ElementAt_m2263812C105D24F8DBF9FBD3CB9DB7510DC203A6 (void);
extern void FixedString4096Bytes_GetEnumerator_mF5A59B3B0B5AE0EE0603D53EAD8C168E4CE37F93 (void);
extern void FixedString4096Bytes_CompareTo_mFE2F6019EBA8EAB9FCCDE7589C8A5F38DA8D4A58 (void);
extern void FixedString4096Bytes_Equals_m3C8E7CA4F35F56F0759EEE2D8E5F1D4591D1E598 (void);
extern void FixedString4096Bytes__ctor_m223FDD4BEB0B99A01E01845C21B5967766AD8577 (void);
extern void FixedString4096Bytes_Initialize_mFD54AAB0FA7203B6FFFB18600E9992DF1F4C7BFB (void);
extern void FixedString4096Bytes_CompareTo_m8D38C45B1D05029012A417EB72A6108BB820C472 (void);
extern void FixedString4096Bytes_op_Equality_mA7CC6C4984FCC9EED79B16AF16237504BD00254D (void);
extern void FixedString4096Bytes_Equals_mF43146C7063DCE299D0708E173E48D146806796A (void);
extern void FixedString4096Bytes_CompareTo_mA10C3E4B7291BD7C68B64922D7EF51FF9019E7B1 (void);
extern void FixedString4096Bytes_op_Equality_m974761DC27A56EB51074713A5B755FEA5C84419B (void);
extern void FixedString4096Bytes_Equals_mF4868F94338530FEFCDF35BB76E1C3D64F54CAEF (void);
extern void FixedString4096Bytes_CompareTo_m40115F59AF4ADBC9F7C70B9A30B90DD660526D19 (void);
extern void FixedString4096Bytes_op_Equality_mCD4EAE52D8F0BA4161054E936710D0C0F3D43260 (void);
extern void FixedString4096Bytes_Equals_mA00838D2DA8F71412C78B2C37D699B39D7897FB5 (void);
extern void FixedString4096Bytes_CompareTo_m16A7E31FA53206F33F3418353A43A2E02F662895 (void);
extern void FixedString4096Bytes_op_Equality_mD2FD6209FE469F2385026575EBC55A6079D3B0C5 (void);
extern void FixedString4096Bytes_Equals_mB76C4C23572B2D379F22EB1EE52B84EE820F075F (void);
extern void FixedString4096Bytes_CompareTo_m1EA8E99320A4B5078747BCE1617B3B1574A53F01 (void);
extern void FixedString4096Bytes_op_Equality_m30D4A0EF7ED2223E8A41181F92E87DA31C73CCAF (void);
extern void FixedString4096Bytes_Equals_m3991F570BEC852688D532724FD842E0BD3AC2BB6 (void);
extern void FixedString4096Bytes_ToString_m1EB551FB3DB9E208AAF34DFFA3864DAB34FD1A26 (void);
extern void FixedString4096Bytes_GetHashCode_m4F13B6A3839EDFD34CE37729A815905DD1AAB720 (void);
extern void FixedString4096Bytes_Equals_mD182C954C7A720E5C452CE5C9B4BAA20D22A4952 (void);
extern void Enumerator__ctor_m4F0B93BF6C32D3867EAB36286E3119996D7C3C28 (void);
extern void Enumerator_MoveNext_m7F50D837FC66C2AD460AD21CB7BF3511273088ED (void);
extern void Enumerator_Reset_m8F760A8C9DB3CF6BBC281DFE1B8DD95615F3D3E9 (void);
extern void Enumerator_get_Current_mA6FED322887F5D0184B7B7AB986B1AA4082183D1 (void);
extern void Enumerator_System_Collections_IEnumerator_get_Current_m18AAF8CEC1B2C674E95734FFA19F0B3041EE5614 (void);
extern void Unmanaged_Allocate_m7310B1FE896DEFFA18303D961C9859C8FF3D21E5 (void);
extern void Unmanaged_Free_m09F6EA89F368ED2C9E5EC5EA60C894C4434F4FD1 (void);
extern void Array_IsCustom_m7651BFF84F5AEFA592FEE86C834A85C373DDC126 (void);
extern void Array_CustomResize_mB51497D583399092F23AA773ABB64F0780610D82 (void);
extern void Array_Resize_mC7BE2965DE3FCF4014D43B606D94951480A65380 (void);
extern void NativeListDispose_Dispose_m3B82496C41B796C86D1461D40A96B0FB87D83223 (void);
extern void NativeListDisposeJob_Execute_mCADA5ED914628592A5834C51F76A20B7CEC1BF2E (void);
extern void NativeQueueBlockPoolData_FreeBlock_m1F946136116E617CAA61EE33BCF4B55E71A2E6DC (void);
extern void NativeQueueData_DeallocateQueue_m00F1377036BFEDFFD7627C8E188150B2BB05F328 (void);
extern void NativeQueueDispose_Dispose_m078AB5F16073B57EF2AA25087E8E5C03C7D0654D (void);
extern void NativeQueueDisposeJob_Execute_m3DBE1DF633A0DE99FD815F98A975B1A5DA0292B9 (void);
extern void NativeReferenceDispose_Dispose_mA377C7A0AEC92DC8071534DD15F6A463021F4E81 (void);
extern void NativeReferenceDisposeJob_Execute_mE349F3D29F93F04237909925E49CE635ED5E7DC5 (void);
extern void NativeStream_Dispose_mD811E61D5C945B23F47323E9C553AD7510889B92 (void);
extern void NativeStream_AllocateForEach_mF01F5D8EDB5CF944B8618FB4DB412A4F68A0AAF9 (void);
extern void ConstructJobList_Execute_m7A858340428CA2870C89EF776C7E205F7C0961A1 (void);
extern void ConstructJob_Execute_m167E59B46B52E41BD4BFA6053CAA2E94BE9DB922 (void);
extern void NativeText__ctor_mFA8CF47C8A938652CF0ED6E30599983F88F56969 (void);
extern void NativeText__ctor_m0CDCA184957424791B499756D915EC43D9E3EAAD (void);
extern void NativeText__ctor_mD6B4A868B0A67C1A007D2F591ABB0DFF68C53BC9 (void);
extern void NativeText_get_Length_mC7FDA822088AB8401ADC3D16438C85B593630F6B (void);
extern void NativeText_set_Length_mF7ACD25110E2CD2E0B98ADF8E5269FAA6B7B3136 (void);
extern void NativeText_get_Capacity_mE5F3A32D0B61826DF90EE2B2E64EF5EA526B2D5E (void);
extern void NativeText_TryResize_mB75889A7FBF6CE1B776D2DA623425B226BDA62A3 (void);
extern void NativeText_GetUnsafePtr_m94C548B1404B4F4EE679749C5C20C5C7F1A84F49 (void);
extern void NativeText_ElementAt_mA84CB997C07D52765ACB0815C9B83576AB5817F1 (void);
extern void NativeText_Clear_mD49A69B25BC09362FD2F16C9DAC37569CFE72E9F (void);
extern void NativeText_CompareTo_mB4F973252827A0222F7928E1B3C89FEEF0FB18FC (void);
extern void NativeText_Equals_m28844EF971044D0B2C670CBF99E3F941EEBC60C6 (void);
extern void NativeText_Equals_m06336C6B8E06D0AAADDA47B85DD6D866B7AE8CE2 (void);
extern void NativeText_Dispose_m63038BF3BBD97854D5CC9C1C6CA5B8B6F401F13B (void);
extern void NativeText_CompareTo_m0A844996020B3F42947EE125954C97A863A2CFFE (void);
extern void NativeText_Equals_m772A182676730E5AC4C51AD1F3009E4212C50538 (void);
extern void NativeText_CompareTo_m56EC356FA7227D7D65D1C0E266BEE008D821648D (void);
extern void NativeText_op_Equality_m259B9C721354CA4DF0BF84C7017F0121B7FB31B9 (void);
extern void NativeText_Equals_mAAC438EEC174C08A34CFDF388A412D51DBE9B652 (void);
extern void NativeText_CompareTo_mDC83F96708C007F463683CBA7105F5F30A1E2053 (void);
extern void NativeText_op_Equality_mCFE56D1DCECD5AD8C7AE2F0E514FD6118DC3D58A (void);
extern void NativeText_Equals_mC7E1FAEB26EB4E65D6725E36526948D27DEDCDAB (void);
extern void NativeText_CompareTo_m98C7DA3D7186387868689E2092B70C101471FF85 (void);
extern void NativeText_op_Equality_m36CF4A20C0B0E638B4B5CE44803BAA1A706DB6F8 (void);
extern void NativeText_Equals_m76D0CAE524665E08BBA9CE8F86466AE96FD5C8C7 (void);
extern void NativeText_CompareTo_m71D261DCED41F458B1B9D8996B314D84C9CCE404 (void);
extern void NativeText_op_Equality_mF250FB75F91ED85D482BAA03D37E79C5BB417DD0 (void);
extern void NativeText_Equals_mB0ABCB86801685ECD7D98F7E1E77F5E10F4B7402 (void);
extern void NativeText_CompareTo_m8DC763DD2142D5CC430A74B9AC87358BE6F53E12 (void);
extern void NativeText_op_Equality_m362C0D603EE9BCDAA391C94719862E94BD25B39D (void);
extern void NativeText_Equals_m6A06741AE2C20A0912956700C6FFB487A28804C6 (void);
extern void NativeText_ToString_mB94FA89B0022211D9B7612D275913E179F7DA691 (void);
extern void NativeText_GetHashCode_mDC42CD251822F0A3DC067D75C2BD179045C4E406 (void);
extern void NativeText_Equals_m485EF7755FDE0CEEFD4A4C4C7A23F8358EB3E93B (void);
extern void ReadOnly_get_Capacity_m18C003500A9CF63E584FCF4D2A38E439A8FE4377 (void);
extern void ReadOnly_get_Length_m820BA559FC99A7ED314D334AF78BE97ADCF61122 (void);
extern void ReadOnly_set_Length_m7852F4413AF08787E52590A2C4BA72B35D01CD26 (void);
extern void ReadOnly_ElementAt_m6562FA1A434B6025EE69D304BC122128F03707D4 (void);
extern void ReadOnly_GetUnsafePtr_m71A211ECD401F9D60A6638740319231990A03A42 (void);
extern void ReadOnly_TryResize_m7263EAE1B98C096590E0ABA69195D588CE2C181A (void);
extern void ReadOnly_CompareTo_mA1C25AB3988C9E015332DF13A830234B3CC15EFF (void);
extern void ReadOnly_Equals_mB77DA315143191BBF711931FB48EF4C3B1D54C3D (void);
extern void ReadOnly_Equals_mBA4A75B3D0D0DB11FE3505C296FEED7D1D8632AF (void);
extern void ReadOnly_CompareTo_m6E99BE0FD05C043FC3FE7A6E7608D89DE6A81F85 (void);
extern void ReadOnly_Equals_mCE9BC83B8AD17DA09D14C6447ACCEF531BA3BA05 (void);
extern void ReadOnly_CompareTo_mB5B0FCE69EC4E0CBD6EBA1ED00B012240D17693D (void);
extern void ReadOnly_op_Equality_m6CFA34E331BEE7AF59C6BD3E81AAA8F36000CD0E (void);
extern void ReadOnly_Equals_m28E07244572B5F9EC30186C0955FD8CF9A67A204 (void);
extern void ReadOnly_CompareTo_m50A0B42856B587167D497681AB6BDC2487A807DA (void);
extern void ReadOnly_op_Equality_m69A5ABAE6510F5830F6873CC2018A8F0CCD03EB7 (void);
extern void ReadOnly_Equals_m471D08AF3CD31C47278F73D1488AFC527D75D0B9 (void);
extern void ReadOnly_CompareTo_m5A3ACD58B20B20C6256E78C3F4C14FADC53B222A (void);
extern void ReadOnly_op_Equality_m569BF71788CAE1CBDD8AD1D38B915103620FB774 (void);
extern void ReadOnly_Equals_m698ABF40421F36BD7C45D9CD101564ED5DDD9570 (void);
extern void ReadOnly_CompareTo_mB68D0C12C06B2215E9D69C86D38FC428A4467EED (void);
extern void ReadOnly_op_Equality_mB5FCD67E2A878B1CE0510B0A9693556115463361 (void);
extern void ReadOnly_Equals_mDA5D9768212584A698F76714D27A2498443BE4F2 (void);
extern void ReadOnly_CompareTo_mCCF733865B5E96CB4C83585D48FCF9FDCD892402 (void);
extern void ReadOnly_op_Equality_m3F4385071EF09E112F07424C8DDFCE61376E6361 (void);
extern void ReadOnly_Equals_mC68B6EB3A7289429EED41E2DE41AFFF7E5E6CFA4 (void);
extern void ReadOnly_ToString_mCB6276C65E02F958F111D95D7EEB6A529EDF52F1 (void);
extern void ReadOnly_GetHashCode_m0AA9F9ECCDEB716AE2E5A2949098D9529638490F (void);
extern void ReadOnly_Equals_mA35F95DAD673F5F2E89A08A52C719CA80E6FB877 (void);
extern void Spinner_Lock_mF38DA7980552D7B8E60444A1B64ADF440D3DFAA7 (void);
extern void Spinner_Unlock_m1664CFC96DCA3A7B36092F61C9E34624C497EB1A (void);
extern void RewindableAllocator_Initialize_m79AF71658BA5502EEE9E8A8CAB98EDDECBC2964D (void);
extern void RewindableAllocator_Rewind_m8EB623F05C707C3AF52FF082A7C095FE3A1CE595 (void);
extern void RewindableAllocator_Dispose_mD873C81842DAEBF01369B8023805FF930C4C8A2F (void);
extern void RewindableAllocator_get_Function_m5461BF00748D262D57516C368291514DC2A84E00 (void);
extern void RewindableAllocator_Try_m6C020E9D4D72801E38775286491991C3FCE064ED (void);
extern void RewindableAllocator_Try_mA4AF5A5088097CB6343C3CC97058959976372C35 (void);
extern void RewindableAllocator_get_Handle_mF81EDA2102485C46965AAB56347E8F64FE551D9E (void);
extern void RewindableAllocator_set_Handle_mE59F440E3F692073FEF47367713603419814928D (void);
extern void RewindableAllocator_TryU24BurstManaged_mBB6DAE6A8CDB2E3626C38F3B65186AAF6ACBF6E8 (void);
extern void MemoryBlock__ctor_m0DEC878B6827C0B400BE1C00CA37C2F8F1C31D7F (void);
extern void MemoryBlock_Rewind_m64C7EC463789D78802B8D27695AFFD37133E7069 (void);
extern void MemoryBlock_Dispose_mE0EAA3828B238B9A26EF6BB5CA7CB5EC592FBD6F (void);
extern void MemoryBlock_TryAllocate_m34CC70E419486D5B1C90E7BF4A6762BE3D130F10 (void);
extern void MemoryBlock_Contains_m2F035A4F1F9063B42ACB1B590C4EFD1741E39CEC (void);
extern void Try_000008B3U24PostfixBurstDelegate__ctor_m57E3EE890202AE74FE7E12FBC0AC2972365BCDAC (void);
extern void Try_000008B3U24PostfixBurstDelegate_Invoke_m785645BDD70C786352410296AA316FBE67DF8DBB (void);
extern void Try_000008B3U24BurstDirectCall_GetFunctionPointerDiscard_mAB3D1EED8ED2B4063EC3967B39DBB3DB757D169A (void);
extern void Try_000008B3U24BurstDirectCall_GetFunctionPointer_m9D88099F720A5E5D7E6313E9DF7FD0E9D8B120F2 (void);
extern void Try_000008B3U24BurstDirectCall_Constructor_mCBF0953D72E1101834468CB323794FB873CD3AF7 (void);
extern void Try_000008B3U24BurstDirectCall_Initialize_m7B4523DB38BE3980D82832E67561525969F33A26 (void);
extern void Try_000008B3U24BurstDirectCall__cctor_mAFF466152A95BC683D4086DE52CB1AC56D50480F (void);
extern void Try_000008B3U24BurstDirectCall_Invoke_m2BAC3EA10EB68BAC379A736EE8EF4EEDA12137DA (void);
extern void Unicode_IsValidCodePoint_m6F8516FA18D35B6D052FD6BDE01D9D497EA06B9D (void);
extern void Unicode_NotTrailer_m9FD6FC331044FE0EFC90E29A2808C4A6535E4CAD (void);
extern void Unicode_get_ReplacementCharacter_m525CDE0E6CAB489454025711F93FF832A600556A (void);
extern void Unicode_get_BadRune_m70003825794A2ABBBF589890D9601B885041E3E8 (void);
extern void Unicode_Utf8ToUcs_m013E3A507C4B6F5459B09C6EA8EA229BDC979827 (void);
extern void Unicode_IsLeadingSurrogate_mCFA66EB348CD8F48DB573D45B2658A95BA248F06 (void);
extern void Unicode_IsTrailingSurrogate_m2ECEB76882DCD6006C95D3861BFA7654D3A22B25 (void);
extern void Unicode_Utf16ToUcs_m55352C5470C6C4C506B02E9827F05C7285F113D3 (void);
extern void Unicode_UcsToUtf8_mBE0B9FE432D859A3409B7BD03C26C958CF9301BE (void);
extern void Unicode_UcsToUtf16_m14C1098270C0DFFAF6B48D47C3214344FD4FAE0E (void);
extern void Unicode_Utf16ToUtf8_mF16BCB8771E0A53EE7464D418792F4CEB99A641E (void);
extern void Unicode_Utf8ToUtf16_mF3051E9181A57301EEF945C10B97D3C9356706DD (void);
extern void Rune_op_Explicit_mE522A7646091C5FC49F41954FCDB8880AB9D6595 (void);
extern void Rune_LengthInUtf8Bytes_m2E470564E773DB43A761FC2A5DA17F0885E81489 (void);
extern void UTF8ArrayUnsafeUtility_Copy_m599E2064905A6DF1D3F386114B76668CD0E5B7B9 (void);
extern void UTF8ArrayUnsafeUtility_StrCmp_mEA1B1EC00085F9EDEC1CDEFCF53C2E83830374D4 (void);
extern void UTF8ArrayUnsafeUtility_EqualsUTF8Bytes_m96A84A5B2B6E4ABA06A65995D7EA16477797E754 (void);
extern void UTF8ArrayUnsafeUtility_StrCmp_m2C38EE679FD06EDDEB1409956BA7398431FF2FEC (void);
extern void Comparison__ctor_m40D70075E3FE042352BE5C6589C23C5D7D41668C (void);
extern void xxHash3_Avx2HashLongInternalLoop_mCAEEE715FCB699CA2F1B947BCD252AA0F87D2B15 (void);
extern void xxHash3_Avx2ScrambleAcc_m64D8B68219EA3E164A61D2001E0969263CF098CE (void);
extern void xxHash3_Avx2Accumulate_mD57A48AB8FB3471A923F64F7C8B52FF8538E791D (void);
extern void xxHash3_Avx2Accumulate512_mBB4B8AAAA2DC7E6B1350597687C11B82E81CEF06 (void);
extern void xxHash3_Hash64Long_m9950702E864DCCD9B8DEAAE23E7CBB5E79D4AC62 (void);
extern void xxHash3_Hash128Long_mED9958D31B54E0E0666AAD34A52DE7CDEB802E6F (void);
extern void xxHash3_ToUint4_m811AB95294FBBC0F17A5358D0A22669691CE3633 (void);
extern void xxHash3_Read64LE_mD275A5EFD8727CDE8B8E280D4A5D5B82D5E3B195 (void);
extern void xxHash3_Write64LE_m79CC2011BF16363F2338D61BE43E99E6467A9437 (void);
extern void xxHash3_Mul32To64_m9210E9379305FC38A6D69C698F6E1A30013BC4F5 (void);
extern void xxHash3_XorShift64_mF4245CDE1C4AF6B1CC8F57AAE0DA8C7E04673CFC (void);
extern void xxHash3_Mul128Fold64_mF59DCB5142027D151F52C7748BFA28C32B3B8F38 (void);
extern void xxHash3_Avalanche_m059990B780566C6F04C66700B2BE7817B4FA2F18 (void);
extern void xxHash3_Mix2Acc_mDEB8D0C149D943295B8A3049A437578BE879BED8 (void);
extern void xxHash3_MergeAcc_mB01ADB1934EDFE8FE3B2AAB13DA6884EB1133A14 (void);
extern void xxHash3_DefaultHashLongInternalLoop_m9181A3A8DBE8DBEFF1B730ECC9A9AA5E93110F1B (void);
extern void xxHash3_DefaultAccumulate_m3D28C5486CC42D31D2D832F40DEFE1A7CF508CA5 (void);
extern void xxHash3_DefaultAccumulate512_mFADF15092DA5379116D3FCCFC4238ADBF48D85D7 (void);
extern void xxHash3_DefaultScrambleAcc_mA46D6E8E1BA4613A50B56C8536B0DA3F50437137 (void);
extern void xxHash3_Hash64LongU24BurstManaged_m71E36BBD116CCA46ED23162F80B08D3B2F782B4D (void);
extern void xxHash3_Hash128LongU24BurstManaged_m961A07284DAB6ADFF52EB4287E9D105AB971FDF6 (void);
extern void Hash64Long_000008F4U24PostfixBurstDelegate__ctor_mBD21053C281809ADF6ECBA131BEC82EBB3EC1185 (void);
extern void Hash64Long_000008F4U24PostfixBurstDelegate_Invoke_mD637DD771E9D46F0D2B6D0E383686E7A0C645DEC (void);
extern void Hash64Long_000008F4U24BurstDirectCall_GetFunctionPointerDiscard_mF4FADACDE81C41E1CACC4958936D6CA8DA0CA737 (void);
extern void Hash64Long_000008F4U24BurstDirectCall_GetFunctionPointer_m0B5E2A4795494A2C5E95435865B3139A6B37449E (void);
extern void Hash64Long_000008F4U24BurstDirectCall_Constructor_m4FE50D7EF6F2FA3EBF79562DCA19F13A8FB57FBF (void);
extern void Hash64Long_000008F4U24BurstDirectCall_Initialize_m9C0FA302804D6C59139845EB6F93F2B455240FBB (void);
extern void Hash64Long_000008F4U24BurstDirectCall__cctor_m0E175D3CA9EA57BCE626EB0E1FF2895FE08850F6 (void);
extern void Hash64Long_000008F4U24BurstDirectCall_Invoke_m7F6D574177DFCDAF3C6CC44E0FDFEBF5129E3DC1 (void);
extern void Hash128Long_000008FBU24PostfixBurstDelegate__ctor_m3F6314D81B156F12D1A950580AF3869339036A22 (void);
extern void Hash128Long_000008FBU24PostfixBurstDelegate_Invoke_m2EE80A525330D5CA95BA62585357273CF5CD3E08 (void);
extern void Hash128Long_000008FBU24BurstDirectCall_GetFunctionPointerDiscard_mAA88F5910E87D551ECD388C3C275C63B20BE2106 (void);
extern void Hash128Long_000008FBU24BurstDirectCall_GetFunctionPointer_m8BC65E819E1CE22B18DAAEA1EF2E553AF364A18F (void);
extern void Hash128Long_000008FBU24BurstDirectCall_Constructor_m05F2CCE734F7CAFCFA0120ECCE368F5C7EDA6C0C (void);
extern void Hash128Long_000008FBU24BurstDirectCall_Initialize_m10E736DA8A39D57A5CE218041BA1779936AD9600 (void);
extern void Hash128Long_000008FBU24BurstDirectCall__cctor_m2CD618EFADD691FB66F7D6AEA85A4A3D550F5639 (void);
extern void Hash128Long_000008FBU24BurstDirectCall_Invoke_m91D203668FDD811BCFB61014E7F6E92AA04AD059 (void);
extern void UnsafeDisposeJob_Execute_m6D0C969CE5C1124239FB9B69EC2A1E486E4F38BC (void);
extern void UnsafeParallelHashMapData_DeallocateHashMap_m8D0FEE08B8522A1D05FBFFBBB43CB203304F114F (void);
extern void UnsafeParallelHashMapDataDispose_Dispose_mCFF4782249AE264F799027E84E4CC704737066A3 (void);
extern void UnsafeParallelHashMapDataDisposeJob_Execute_m129F30954F3CF5922C620E4DDB1E50E540A06DCC (void);
extern void UnsafeParallelHashMapDisposeJob_Execute_mCEF89FC4B3D6D8DC7E213FF274053A41805215E1 (void);
extern void UnsafeStream_AllocateForEach_m1EE9C9B5CF33ED03FA2C01A5DF2C5D99BAABC065 (void);
extern void UnsafeStream_Deallocate_mF13D45026C45A52DD441B1F021CC4BE9454D466C (void);
extern void UnsafeStream_Dispose_m4D493F40C6ED4C7346017C05FBC006B5632AE49C (void);
extern void DisposeJob_Execute_m089DE6120E788ECA6418DFAB36FFC3F4E90FC10E (void);
extern void ConstructJobList_Execute_mDECEE80A756D3E19EF0FC58ADB0C4665626F7DAB (void);
extern void ConstructJob_Execute_mAA49F134BF92ED7BB0356BC460A30C9B39A87C50 (void);
extern void UnsafeTextExtensions_AsUnsafeListOfBytes_mA80ABFE08762E38D788ACF506BEB4A0E3621D439 (void);
extern void UnsafeText__ctor_m4F0450604AA478F4C19A2CD4746878981C7DA31F (void);
extern void UnsafeText_get_IsCreated_mEE504C37FEADA4DFD3A6AFB47751B20A1423BBF1 (void);
extern void UnsafeText_Dispose_m715EBE3FAD0BA5111276CFCB7F65419701F3BABC (void);
extern void UnsafeText_ElementAt_mE1021122866981C2F08E17BA1320C2A294886844 (void);
extern void UnsafeText_GetUnsafePtr_m1C1462A867C4714017B3A5974F1FAF3716FB8F8C (void);
extern void UnsafeText_TryResize_m4D31F691405B691F1CB3DABF9ED09D360FB13BDC (void);
extern void UnsafeText_get_Capacity_m70C15A6A1753D3D824DEE599902E3E7FFC75F815 (void);
extern void UnsafeText_get_Length_m7D4616C7F2AAA07EC0E98FB430C6D7DA2D4D5E40 (void);
extern void UnsafeText_set_Length_m98076AB8E3E55B44FD260347BD22D944DCAE4DD9 (void);
extern void UnsafeText_ToString_m02C6D467CDF758100B8CCC3199168304B1FCE2B2 (void);
extern void __JobReflectionRegistrationOutput__1910371556_CreateJobReflectionData_mEF64DEB65AA7AC2A64F15736132D339337A68D67 (void);
extern void __JobReflectionRegistrationOutput__1910371556_EarlyInit_m9B00513909A2241E426EA3D838FAED0C29E82224 (void);
extern void U24BurstDirectCallInitializer_Initialize_mBB7299DE1F1DF732C60394307234ED45AE14AD82 (void);
static Il2CppMethodPointer s_methodPointers[692] = 
{
	EmbeddedAttribute__ctor_mB9EA4CCF3A3DC39A3BC92CFE9557FFAA77D15404,
	IsUnmanagedAttribute__ctor_m15974D59768AFF916E346F7107F7FF7F6AD9099C,
	EarlyInitHelpers_JobReflectionDataCreationFailed_m385E2249E15484F8BCB1A1EA9BA9AFB7EB425109,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	RegisterGenericJobTypeAttribute__ctor_m477E5F1C2EA7264CD6533A7B415C6D76D9538D94,
	DOTSCompilerGeneratedAttribute__ctor_m8689CDD675567BC580F1FADCCF386B0FEE07B0E5,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	AllocatorManager_Free_mB8AE9C4CB989A9121F4E3F2E6C7781076DFB3025,
	NULL,
	AllocatorManager_CheckDelegate_m52D3F12472A2BBC5A28D2F4B5011B19D2E36AC61,
	AllocatorManager_UseDelegate_mEB18420309DAA2CC710BA123C6996C9FB6FC3798,
	AllocatorManager_allocate_block_mBEB6E6FDC334118DB679CF2619EBB3FF4FDD7FB5,
	AllocatorManager_forward_mono_allocate_block_mD2A9A49DFC8CBDC39F27E2749048ABC91E124519,
	AllocatorManager_LegacyOf_mAD212C1A7F5295C8987A6C9D7F81E8FF42E0A3BF,
	AllocatorManager_TryLegacy_mF4F0B8CE7B0293504FA12A6F9C4ACFF28B59FF79,
	AllocatorManager_Try_m24A6A4EF594F8909B5677C94C4788F365E02E7F9,
	AllocatorManager_Register_mEA1DA775A01BA193E46C21C82BA7DD7215086E23,
	NULL,
	NULL,
	NULL,
	NULL,
	AllocatorManager__cctor_m3E94344CB4CD852C9427FE9394EBE4EC36BFEEA1,
	AllocatorManager_InitializeU24StackAllocator_Try_000000A2U24BurstDirectCall_mC9E8A64AA142C1819D5DA507431A5C98D70C7F65,
	AllocatorManager_InitializeU24SlabAllocator_Try_000000B0U24BurstDirectCall_m9FEBDDDBCD595B109E3EEAC62843A3D9809F8BAD,
	TryFunction__ctor_m10C4A7B32E87301727B84D8CBA081FABAE3CCE53,
	TryFunction_Invoke_mED723D46A7B0C4B590ABECE0868EA02AD94D07A2,
	AllocatorHandle_get_TableEntry_m09C5D57FF627FEAD3AAE73FB28CB4B9EEB811A63,
	AllocatorHandle_get_IsInstalled_mB38CD887177A87128DC9A2DE6F866F9EC18FA907,
	AllocatorHandle_Rewind_mC0426BBD1E638878C33164F91AC3ADC66C850AC1,
	AllocatorHandle_Install_m0526A06766A02754698DE0115B926C15566CDD3B,
	AllocatorHandle_op_Implicit_mDCF4431F31BB4A09438AE644785C4273F86B2B8D,
	AllocatorHandle_get_Value_m24A0A3E433794106E43E9140CC2BB55493C8F30F,
	AllocatorHandle_get_Function_m031BF3715926C775DC8C946F6B039F99D738C930,
	AllocatorHandle_Try_m4E8677E100693A0F015076408244AF2BA827CBC9,
	AllocatorHandle_get_Handle_m440EA9B9A4306115087775DA2AA0AC034107D0E2,
	AllocatorHandle_set_Handle_mA11E567A5CF5D42E8A5DC080DF007789B47B1648,
	AllocatorHandle_get_ToAllocator_m34C297958A940D9D35C11B3D28B8E5A08E170FDF,
	AllocatorHandle_Dispose_mB74CBC8980962C016A6C85F09D3F94775A2C58E3,
	Range_Dispose_mEABC7B5F5F72CBAC69BAB12C80B8EDEE86B3737A,
	Block_get_Bytes_m4BB90CD1C72FC56C573BE09951BF1CA68E8BD7D7,
	Block_get_AllocatedBytes_mFE126B221F670BDA89C5D59C9E63C9724F79BE08,
	Block_get_Alignment_mC2388F87008B204A333664B9323BD38AA20FD633,
	Block_set_Alignment_m4E74D90E827C5F58B3EBE91B2F5B097388E0500F,
	Block_Dispose_m38B474EF3D6A142344D0A58651319FD039FB54ED,
	Block_TryFree_m0C5FA80AF51DA6F40F40DAE7E97975C3BB402225,
	NULL,
	NULL,
	NULL,
	NULL,
	StackAllocator_get_Handle_m31D630726EF42BC6C432F8751E6B2BE346BB2267,
	StackAllocator_set_Handle_m0C72B8ACB04FF327C6CB7787CEF767413DCCF003,
	StackAllocator_Try_m46B9AAB118C2ACCDB8B0A54DA8E27209CA152558,
	StackAllocator_Try_m093FA501B1B427E32DD9F654380B3EA56A5A4234,
	StackAllocator_get_Function_m86410BC7650042F3F7FF4016A8E9E6164C226EB9,
	StackAllocator_Dispose_m0872D5C01F22CD4BCCF71EA0185DB18EA14D62CB,
	StackAllocator_TryU24BurstManaged_mB88D607AA12E4D9181BF1FFE81A1AC3117FDB5E2,
	Try_000000A2U24PostfixBurstDelegate__ctor_mD47C6C96D42CA4BEFBF90C613E9EA003EDE6F401,
	Try_000000A2U24PostfixBurstDelegate_Invoke_m36548B4D971D594B65F761440A46C50609DC8246,
	Try_000000A2U24BurstDirectCall_GetFunctionPointerDiscard_mE7ED43A08EDAF97A8E163D7569089142E2E89E6A,
	Try_000000A2U24BurstDirectCall_GetFunctionPointer_m77A92FE9E3067F3ECBBA8911E283A16AE2107EC9,
	Try_000000A2U24BurstDirectCall_Constructor_mAA9397F0B87F27DE392B95B8181C127A3D1338D5,
	Try_000000A2U24BurstDirectCall_Initialize_m39B3D67471DACBFFB1F8A03CA8B20D71331D487F,
	Try_000000A2U24BurstDirectCall__cctor_m923D784BB2F21546516D8375B907F292A3688E1B,
	Try_000000A2U24BurstDirectCall_Invoke_mFDFEEA7225A6C3FC09BE5C42BFDC6E20298E62A2,
	SlabAllocator_get_Handle_m09C2CCD8BCEA26214DC514707F222B228A1E04BE,
	SlabAllocator_set_Handle_m31EE421CD1B214F4BA885C5BB166C9834A7DC22F,
	SlabAllocator_get_SlabSizeInBytes_mFFD79D4D6B544F1C21CB0EB40BBDFF5D5477A612,
	SlabAllocator_Try_mE7D3D58D59FB68AE5FA9FF99C0C69FD854F482CD,
	SlabAllocator_Try_mCD7DED588811A6E3F78E4A14CBFE2852D8E39DEB,
	SlabAllocator_get_Function_m907185458A4C86A6B134EF91E6F323520CEEFB2D,
	SlabAllocator_Dispose_m9ED75718657190884C0327489A9AE9DB525D7912,
	SlabAllocator_TryU24BurstManaged_mC48F05E806431B6537727E4D6A10550207FBB1EA,
	Try_000000B0U24PostfixBurstDelegate__ctor_m3F1280871ABAD11D51D0CD4BE042A70E4E9275BF,
	Try_000000B0U24PostfixBurstDelegate_Invoke_m7634CB51D6C7BE5653AD14E399D662D549218637,
	Try_000000B0U24BurstDirectCall_GetFunctionPointerDiscard_m5B0BAE81E17C180B578BAEF6468BE062FBCF592E,
	Try_000000B0U24BurstDirectCall_GetFunctionPointer_m971F771C3CAFF2B2B39C2308B6DB0D2FEB5F7D63,
	Try_000000B0U24BurstDirectCall_Constructor_mF8481155C303826FEE9A27A2C2330FE50CB4D121,
	Try_000000B0U24BurstDirectCall_Initialize_m3EBB7AA40269601C75C5448A2320AEC603DCE001,
	Try_000000B0U24BurstDirectCall__cctor_m1FA86193EBD7241849C669FF5B78041D6247BD77,
	Try_000000B0U24BurstDirectCall_Invoke_mD87D11B9A3418C18E24861476A0E2A4915D3A91A,
	NULL,
	NULL,
	NULL,
	IsInstalled__cctor_m044184B83FFFB2D0845917AAA1E28EB06CA4E277,
	TableEntry__cctor_mCA16889126B2ED5EF69666F8B0376FCC8834FCE1,
	Managed_RegisterDelegate_m494ED81F0C1945174DD1E82D6711EB329255E36F,
	Managed_UnregisterDelegate_mE60C615D7705C878A66FB0AA06C7EE86A2359D46,
	Managed__cctor_mE3BC99DF4AF7BC63DE01424848BDC790B53500BA,
	NULL,
	NULL,
	NULL,
	CreatePropertyAttribute__ctor_mFD5BF090F42312F62534D903F2D804C19CE16264,
	BurstCompatibleAttribute_set_GenericTypeArguments_mBE8AE08E0824187C5B48F5132C73624CAB2C6906,
	BurstCompatibleAttribute__ctor_m6D43ED0B880EE6FCCCBC38B1A63DB493898DD3BA,
	NotBurstCompatibleAttribute__ctor_mF89AC194CF3CC2F03A673D1FBDE969E1751CB827,
	CollectionHelper_Log2Floor_m67F9EE2135763C03633748FD8E819C2D3F46C1ED,
	CollectionHelper_Hash_mFB14DD4BA7288CEDF90E514A9397FB9C27E55293,
	CollectionHelper_ShouldDeallocate_m505E7EDBA71F02BAF52CC9DCD7C593CDA85D5465,
	CollectionHelper_AssumePositive_mD1EC1F05F50F605141D9BA5D70C4332AC902B4B1,
	Long1024_get_Length_m9FBD7E595A95159BC3DD170F7B715C8DF7BFF520,
	Long1024_set_Length_mF197E52E1E211AC830C80AE4901AF50C5B9C2C71,
	Long1024_ElementAt_m14B27AD662901124F5CD90CA28E5B0F15953C185,
	ConcurrentMask_longestConsecutiveOnes_mD4C89063785B1BDCE865CAAD89B7B99D364EF5E5,
	ConcurrentMask_foundAtLeastThisManyConsecutiveOnes_m637F08D215E90D433E8E1997B9F312E783909E3B,
	ConcurrentMask_foundAtLeastThisManyConsecutiveZeroes_mD644F2E85E07262C1CEFDA2535DEB710F67E5B0F,
	ConcurrentMask_Succeeded_m0EADE2AF3FA5CF6D4C93A37649EED1471C025F3F,
	ConcurrentMask_MakeMask_m58F7E98620D891775026140905A5BD1E65C6A7BB,
	ConcurrentMask_TryFree_m8EA4E66619A5DE669C97CD22210F9060C31759AB,
	ConcurrentMask_TryAllocate_m17B10152E36D67B7D2DE828BF62B0F4C08ECC4E6,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	FixedString32Bytes_GetUnsafePtr_mA653D9296813A2EA93C0D6FC501500008121C3A8,
	FixedString32Bytes_get_Length_mA349139D3C731DF762B4124B833B60BF0B283797,
	FixedString32Bytes_set_Length_m9BD2B7F7AA48AF38F72C4C3A3C00C9324F39FA8F,
	FixedString32Bytes_get_Capacity_m4CEE4CE0CC97C33ED774FACD8211D766FEB549E2,
	FixedString32Bytes_TryResize_m2E7F8E8810492683C24102604F65EA3294BF6150,
	FixedString32Bytes_ElementAt_mCB76C2A56E3E397B8038E51DDEF9FD23571F8806,
	FixedString32Bytes_GetEnumerator_m42731C60A6429D594719D1951D8AC6A9D6D4BF75,
	FixedString32Bytes_CompareTo_m79B33E69FCFCD08E454F2DEE35FC5F1C9C64E425,
	FixedString32Bytes_Equals_m1F50C94DF6B470C0BD1C56421A566EE556341766,
	FixedString32Bytes__ctor_m1C1C73C55B3D020EA211DE2E9E6C7CF0400C6408,
	FixedString32Bytes_Initialize_m42C7A7BFFFE132CCD2DB5984E779B4467F88D4AC,
	FixedString32Bytes_CompareTo_mD37FD30C7E3F389CC41E9E1AC9D3D655CA875D19,
	FixedString32Bytes_op_Equality_m1426A50ADD3772C2008B14D276BD5C10A5D92573,
	FixedString32Bytes_Equals_m7CA0083FD7E3A4F0ECEC273977A88425F13806E6,
	FixedString32Bytes_CompareTo_mFC8EDD03F444D1B497D163FBF38A413A5DF47E70,
	FixedString32Bytes_op_Equality_m0F27AD70B9D59163D745A9CCC47A0F20739C25F6,
	FixedString32Bytes_Equals_m5E5B5F1ACC5BF8C93EF3A97BFB3CC1338F7D5A1D,
	FixedString32Bytes_CompareTo_m77F538DDC2D8A7B5EFF36482F83E5B9DDCFE08D8,
	FixedString32Bytes_op_Equality_m8798D9727925F686AC8BB99439AB90C546A4D6C8,
	FixedString32Bytes_Equals_m84D35401C5A983408E4B4D26903EB05BBE05A53A,
	FixedString32Bytes_CompareTo_m50123082F19E44CB94E7F56E28AD0119C3DE53F8,
	FixedString32Bytes_op_Equality_m856509D8F9C3F5E0895D7CBEBD74884F656A224B,
	FixedString32Bytes_Equals_m23ABF390E00DDB6B6D391EFC121CF7474FD751C5,
	FixedString32Bytes_CompareTo_mB5BB0CB6D7DE907B07C07BB20D5E1F8A74EA8EF8,
	FixedString32Bytes_op_Equality_mF58CBA454E17E8C74B81BF854A277C9576E3AB67,
	FixedString32Bytes_Equals_mAAD5520503E00E6340BAA463BC027C035F7C8317,
	FixedString32Bytes_ToString_mCDBDE58EDFFA82B48A8613E724F92305B4C84914,
	FixedString32Bytes_GetHashCode_m699B6C9D56B99126CB2F988A01DC87DF8A9CCFFD,
	FixedString32Bytes_Equals_m95DCBCE85E03295E539FF672D39977CDF1233647,
	Enumerator__ctor_m23B356A7343A215746DB725E4E273A675CC41A5E,
	Enumerator_MoveNext_m73472085BD2AC39AA22ACCAFC95F18489EAEEEE4,
	Enumerator_Reset_mDE0608807936013524A168796EDF6D505449D80D,
	Enumerator_get_Current_mC9F761A2230A3C6148795946B81AAFD09EADF0A8,
	Enumerator_System_Collections_IEnumerator_get_Current_m4CDA6A026F68F400E0DFDAEB26184631B55A30B1,
	FixedString64Bytes_GetUnsafePtr_mB61C0A15578E5DCFB19AC3A6504D66B4DAA88C6E,
	FixedString64Bytes_get_Length_m7FC1591B9CBA2113727D06FC5EA43AE4A5F16BF5,
	FixedString64Bytes_set_Length_mF1C5F76690D2DB3BE7F6440213FA758E803462C6,
	FixedString64Bytes_get_Capacity_mDA79A4601FF4672BDDCCB90EA4EF40613F9C35B0,
	FixedString64Bytes_TryResize_m9B49BA15D7B2DFE50F4482E4113EE8E44EE9F138,
	FixedString64Bytes_ElementAt_mCD9252823934D28D6921E529D9777BC8DD420EFD,
	FixedString64Bytes_GetEnumerator_m646BE217BF88AEC39B4D2F377059EAB5E4B4F571,
	FixedString64Bytes_CompareTo_m982D1C7FF590144B881A32008C73F1E3A1A8A63E,
	FixedString64Bytes_Equals_m9E74082A6DAD750845D5D82DCFFCDE5141056D1E,
	FixedString64Bytes__ctor_mAC760222B77A7BB28B5A7E9A00B9168F3B99F211,
	FixedString64Bytes_Initialize_m82ABCB4DCF0902729F32BED310A7EC5F6C8021C4,
	FixedString64Bytes_CompareTo_mC70C4EB2FFEEB4DD7372F57E2D3E5DE3B1773E2F,
	FixedString64Bytes_op_Equality_m3B842D63D93646FC370B5E22A8CFFE176AEE8692,
	FixedString64Bytes_Equals_m48F66EA24CE289A0EF42E422ACE3DD222D5011F1,
	FixedString64Bytes_CompareTo_mF3A1A476403FA9932C229A1A60D43575087D3D4F,
	FixedString64Bytes_op_Equality_m6482029A475ADE0C314A5EC31BF89F4773854DA1,
	FixedString64Bytes_Equals_m5AA29267D5B6D641B68732BFD430646971ECD62D,
	FixedString64Bytes_CompareTo_m3295FA78979CDA555DFA04CF29CFF649439B8711,
	FixedString64Bytes_op_Equality_m35753FD7FE758FBDF1D107EBAF7405E7D2D778D6,
	FixedString64Bytes_Equals_m5A4B5CD2579DAB7CA024749046D711F4621D29E2,
	FixedString64Bytes_CompareTo_m19F3795ED3A69BD3F5BC9C3B4C7E35897344AB50,
	FixedString64Bytes_op_Equality_m34038710CBDB51FC825814313571D76EF29BE475,
	FixedString64Bytes_Equals_mA2CAA548B481B1BBD0A0DC745DEAC5D7824F6970,
	FixedString64Bytes_CompareTo_m9407A8045F52D4C006BB232ED67AE6A6C4F56962,
	FixedString64Bytes_op_Equality_mE056F9BEACA73D92F6DDF5EC51FB12BDC7A1CB57,
	FixedString64Bytes_Equals_mA9BDDEACE5792EA7513B082B280999828FB6C1EB,
	FixedString64Bytes_ToString_m67C6568EB9ED23E3B624E581A57E5BE8749A254E,
	FixedString64Bytes_GetHashCode_mECC24662CA8517B714FEE61D8CD82E7A30BF76D4,
	FixedString64Bytes_Equals_m0E3F24AA5E7B50BA24D6CEE92A0617E115A86ED9,
	Enumerator__ctor_mBB5217F352FC6C14E9D4A64DA1865E56E6778AD1,
	Enumerator_MoveNext_m9C6D69E2B3854694ACCE564B8E9850FEA10AD271,
	Enumerator_Reset_mC5DE7DA1196A752281928B8B91A6AA62319D9443,
	Enumerator_get_Current_mBCC0C54F9A50E0BD2661EEF1F43C7ACC7A4904F9,
	Enumerator_System_Collections_IEnumerator_get_Current_m438D18EF1170C1DEFA7979F9B79CEE90FB7B4B1C,
	FixedString128Bytes_GetUnsafePtr_m5F280F7783EB21A69893D7FEF551F0E68D3E51BA,
	FixedString128Bytes_get_Length_mB01AB46B1F5415C0379CCAF98ED2AAE6F08C242E,
	FixedString128Bytes_set_Length_mBBB43CE32D9C6285009629AE64BC6C34759C2ED2,
	FixedString128Bytes_get_Capacity_mD161D26116BAA25254B2CCFAB79975FFDA9CA711,
	FixedString128Bytes_TryResize_mF1DCAFDBD573E0CCC60E0A534A5E854434B6BC39,
	FixedString128Bytes_ElementAt_mED6187F2E6FEADD9A48BF05E54FE7375989B686D,
	FixedString128Bytes_GetEnumerator_mB563C36FDCB2BD71EE2C8C959983D5B81EA84ECE,
	FixedString128Bytes_CompareTo_m8FA619D7CD1ADD9AFC7D92148025CB181A117470,
	FixedString128Bytes_Equals_mAD97C501810E69B444947F8169017A4A132DCE8B,
	FixedString128Bytes__ctor_mB0E30CB7FA5CAFF30C1D4FDE75043FD7D7F2AA40,
	FixedString128Bytes_Initialize_m4A2BA0C3501339D697E1BCAA11763D6BAA45E1C4,
	FixedString128Bytes_CompareTo_mF9C0984E47EEB20E9E28A4AAE1275E59E42D81B5,
	FixedString128Bytes_op_Equality_m5F5BA10BF7DCBB3E8DFE55BA6AA3D0B515162637,
	FixedString128Bytes_Equals_m5C92E72BEBE4D7E67AF7646C4293F3F9EA7E33EC,
	FixedString128Bytes_CompareTo_mB8E2D570CDCEE17387FD2BDA4BD35F9AD5C311A8,
	FixedString128Bytes_op_Equality_mE6BF574CF44C2046A0D99E8630938F92FA6944A8,
	FixedString128Bytes_Equals_mC7C2AEBA071128B24EB0BD130ED6423EA46275C1,
	FixedString128Bytes_CompareTo_m3A02146ED7EA6B833583D2C1B38F6FF5B0870E6F,
	FixedString128Bytes_op_Equality_mE119008C751060334BD3CECF7B3D0B74B288F684,
	FixedString128Bytes_Equals_m3B91B177B21344B9CB879DFDAA00AB7E812AD5AB,
	FixedString128Bytes_CompareTo_m7DAC821750F1242E519D29D8A1A01ABD6B5630C6,
	FixedString128Bytes_op_Equality_m585E99914214C76581B50D2279924B42A6E5ADD1,
	FixedString128Bytes_Equals_m02FB7C6C4862D4F44EDC97E72A4CADD0BD379D5F,
	FixedString128Bytes_CompareTo_m340091A89F740B13DE44A1F3AE938A14AE658A4B,
	FixedString128Bytes_op_Equality_mF721475F602666C9A09E70EB8C08B0D2DF7D83EA,
	FixedString128Bytes_Equals_m0A10CFEB353647A220B407CCEBE12035F0C1D4A2,
	FixedString128Bytes_ToString_m1CD5B095D5A80759EF2E7F60AA95921369958A29,
	FixedString128Bytes_GetHashCode_mB211F7E224953364EE91770921BA59760A0E4428,
	FixedString128Bytes_Equals_mF8C053D97C4EA171FF1A82613C836A231DDF0DD9,
	Enumerator__ctor_mDFC9DFC8E311728973558AFBE6406A5ACCEBD703,
	Enumerator_MoveNext_mC0931D9551212966F0C15910269D8E72B140D860,
	Enumerator_Reset_m5A3D75D51C04D88B8396AC90C18082C3654F9D61,
	Enumerator_get_Current_mECFDD800F6B12953D71596BC5256B904E30CBD57,
	Enumerator_System_Collections_IEnumerator_get_Current_m6A2804C78C30C5F9F1D0CDF4B5517C04CB17EA68,
	FixedString512Bytes_GetUnsafePtr_m6BE151F6C1AC2994321FFC794DD61AEE5E915E05,
	FixedString512Bytes_get_Length_m07F07806B00F694DA08ED7C3BC04C9FCE7CE4E07,
	FixedString512Bytes_set_Length_m5BECBE3190D559780E9F84276928B0EC612A1BDE,
	FixedString512Bytes_get_Capacity_m80069D7197EA5C9AC0149AEE21591CCCFBC0AB55,
	FixedString512Bytes_TryResize_m924FCE76C50EFD1773E2A417BCA84B2C6B158ABD,
	FixedString512Bytes_ElementAt_m7B6C3BFA049CBE0952188BD01720D28505483485,
	FixedString512Bytes_GetEnumerator_mF1B489F6A1B771B4A9D21F8C58BDD76969106703,
	FixedString512Bytes_CompareTo_mF2E8A9C2171833229322FEBEF4DF783251513B59,
	FixedString512Bytes_Equals_mE4279608ABCBD5236ADF59E96788F0837787F11A,
	FixedString512Bytes__ctor_m15D957FCF419703D82533C04DAC6565D2AE139A4,
	FixedString512Bytes_Initialize_m960C30A7580B4016E4EB827FF9DC2D2B009E85F2,
	FixedString512Bytes_CompareTo_mFEA161B206C05F7894DDF0CAB24CDBF525AD9F97,
	FixedString512Bytes_op_Equality_m9396081EA8C88FEA2006D359260245627184D456,
	FixedString512Bytes_Equals_m2AF82E5FAAF96DBD0A105DF94BD1287804D6D568,
	FixedString512Bytes_CompareTo_mCF6F5BD2E7F95763F8903514954023697C1A3838,
	FixedString512Bytes_op_Equality_m72AD5D8E203AEBD992FFDB15E2AA658549127C77,
	FixedString512Bytes_Equals_m14403CF490B635D08B4FD2DCAB8473AD2A510CE6,
	FixedString512Bytes_CompareTo_m80D4CAD931945D04AE44A9610D72D8E0839FB2B5,
	FixedString512Bytes_op_Equality_m402E2B00BB62CC4AA6E20DBE2A763A64D957A951,
	FixedString512Bytes_Equals_m865E75EE8511CFC6CB527D86E54AEE7AE010449A,
	FixedString512Bytes_CompareTo_m42E210C85C1D09E6698DCDDB40545351895E2574,
	FixedString512Bytes_op_Equality_m2D57D6666092B59CE4541DB706A388DA6F7332C0,
	FixedString512Bytes_Equals_m34F02FA9086BE42B8856DF5A85DA039FD3036E99,
	FixedString512Bytes_CompareTo_mEC561BBFD17069268207BF2D6BD6E2C93E90A1B7,
	FixedString512Bytes_op_Equality_m6C60AF29D4AFBB2FCD3F347EB45D4F4F2787AAFF,
	FixedString512Bytes_Equals_mE00AFBE32D04B3671E8D476855409B35B5C8E674,
	FixedString512Bytes_op_Implicit_mCF055F8B2FB98005951417E9FE994E3D230F58E4,
	FixedString512Bytes_ToString_m1A47583FB34608DBEEDC65F9CA6E7B8E7930233F,
	FixedString512Bytes_GetHashCode_m3F22B7550BFBA5E580D804CCAC7528BA8F64862B,
	FixedString512Bytes_Equals_m344621B84C32BCC845396B588F2AE18154B9C6C3,
	Enumerator__ctor_m2273AB72F8E086A370110A63E5B514834696D916,
	Enumerator_MoveNext_m30A057A8CC9ADF95DF2D81CA7A10D1E5CA1DBEE8,
	Enumerator_Reset_m76202CD1C0493A8DFF8D04AEF5654A4CBA3AC0ED,
	Enumerator_get_Current_m8120FC6DFAFEA5A1431B2F74BA6C1A59E546C210,
	Enumerator_System_Collections_IEnumerator_get_Current_mFB83F84FB64A9ADDBD4C3AED4BA70B2DB349BCBA,
	FixedString4096Bytes_GetUnsafePtr_mE1DB42C2C0EEA7D6E1B5D0366A4727125D916EEB,
	FixedString4096Bytes_get_Length_mC2A80F2813739852F0675DA8D2AB68AA92BFC36F,
	FixedString4096Bytes_set_Length_mB4335BCB8388D446FF0C612F81355BD4861F4F8B,
	FixedString4096Bytes_get_Capacity_m55EE381DB20EC9C738999C9F562700569906434D,
	FixedString4096Bytes_TryResize_mC7B1C3EF58A73536BD8A17AEA540B05D1FC4A8F7,
	FixedString4096Bytes_ElementAt_m2263812C105D24F8DBF9FBD3CB9DB7510DC203A6,
	FixedString4096Bytes_GetEnumerator_mF5A59B3B0B5AE0EE0603D53EAD8C168E4CE37F93,
	FixedString4096Bytes_CompareTo_mFE2F6019EBA8EAB9FCCDE7589C8A5F38DA8D4A58,
	FixedString4096Bytes_Equals_m3C8E7CA4F35F56F0759EEE2D8E5F1D4591D1E598,
	FixedString4096Bytes__ctor_m223FDD4BEB0B99A01E01845C21B5967766AD8577,
	FixedString4096Bytes_Initialize_mFD54AAB0FA7203B6FFFB18600E9992DF1F4C7BFB,
	FixedString4096Bytes_CompareTo_m8D38C45B1D05029012A417EB72A6108BB820C472,
	FixedString4096Bytes_op_Equality_mA7CC6C4984FCC9EED79B16AF16237504BD00254D,
	FixedString4096Bytes_Equals_mF43146C7063DCE299D0708E173E48D146806796A,
	FixedString4096Bytes_CompareTo_mA10C3E4B7291BD7C68B64922D7EF51FF9019E7B1,
	FixedString4096Bytes_op_Equality_m974761DC27A56EB51074713A5B755FEA5C84419B,
	FixedString4096Bytes_Equals_mF4868F94338530FEFCDF35BB76E1C3D64F54CAEF,
	FixedString4096Bytes_CompareTo_m40115F59AF4ADBC9F7C70B9A30B90DD660526D19,
	FixedString4096Bytes_op_Equality_mCD4EAE52D8F0BA4161054E936710D0C0F3D43260,
	FixedString4096Bytes_Equals_mA00838D2DA8F71412C78B2C37D699B39D7897FB5,
	FixedString4096Bytes_CompareTo_m16A7E31FA53206F33F3418353A43A2E02F662895,
	FixedString4096Bytes_op_Equality_mD2FD6209FE469F2385026575EBC55A6079D3B0C5,
	FixedString4096Bytes_Equals_mB76C4C23572B2D379F22EB1EE52B84EE820F075F,
	FixedString4096Bytes_CompareTo_m1EA8E99320A4B5078747BCE1617B3B1574A53F01,
	FixedString4096Bytes_op_Equality_m30D4A0EF7ED2223E8A41181F92E87DA31C73CCAF,
	FixedString4096Bytes_Equals_m3991F570BEC852688D532724FD842E0BD3AC2BB6,
	FixedString4096Bytes_ToString_m1EB551FB3DB9E208AAF34DFFA3864DAB34FD1A26,
	FixedString4096Bytes_GetHashCode_m4F13B6A3839EDFD34CE37729A815905DD1AAB720,
	FixedString4096Bytes_Equals_mD182C954C7A720E5C452CE5C9B4BAA20D22A4952,
	Enumerator__ctor_m4F0B93BF6C32D3867EAB36286E3119996D7C3C28,
	Enumerator_MoveNext_m7F50D837FC66C2AD460AD21CB7BF3511273088ED,
	Enumerator_Reset_m8F760A8C9DB3CF6BBC281DFE1B8DD95615F3D3E9,
	Enumerator_get_Current_mA6FED322887F5D0184B7B7AB986B1AA4082183D1,
	Enumerator_System_Collections_IEnumerator_get_Current_m18AAF8CEC1B2C674E95734FFA19F0B3041EE5614,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	Unmanaged_Allocate_m7310B1FE896DEFFA18303D961C9859C8FF3D21E5,
	Unmanaged_Free_m09F6EA89F368ED2C9E5EC5EA60C894C4434F4FD1,
	NULL,
	Array_IsCustom_m7651BFF84F5AEFA592FEE86C834A85C373DDC126,
	Array_CustomResize_mB51497D583399092F23AA773ABB64F0780610D82,
	Array_Resize_mC7BE2965DE3FCF4014D43B606D94951480A65380,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NativeListDispose_Dispose_m3B82496C41B796C86D1461D40A96B0FB87D83223,
	NativeListDisposeJob_Execute_mCADA5ED914628592A5834C51F76A20B7CEC1BF2E,
	NativeQueueBlockPoolData_FreeBlock_m1F946136116E617CAA61EE33BCF4B55E71A2E6DC,
	NativeQueueData_DeallocateQueue_m00F1377036BFEDFFD7627C8E188150B2BB05F328,
	NativeQueueDispose_Dispose_m078AB5F16073B57EF2AA25087E8E5C03C7D0654D,
	NativeQueueDisposeJob_Execute_m3DBE1DF633A0DE99FD815F98A975B1A5DA0292B9,
	NativeReferenceDispose_Dispose_mA377C7A0AEC92DC8071534DD15F6A463021F4E81,
	NativeReferenceDisposeJob_Execute_mE349F3D29F93F04237909925E49CE635ED5E7DC5,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NativeStream_Dispose_mD811E61D5C945B23F47323E9C553AD7510889B92,
	NativeStream_AllocateForEach_mF01F5D8EDB5CF944B8618FB4DB412A4F68A0AAF9,
	ConstructJobList_Execute_m7A858340428CA2870C89EF776C7E205F7C0961A1,
	ConstructJob_Execute_m167E59B46B52E41BD4BFA6053CAA2E94BE9DB922,
	NativeText__ctor_mFA8CF47C8A938652CF0ED6E30599983F88F56969,
	NativeText__ctor_m0CDCA184957424791B499756D915EC43D9E3EAAD,
	NativeText__ctor_mD6B4A868B0A67C1A007D2F591ABB0DFF68C53BC9,
	NativeText_get_Length_mC7FDA822088AB8401ADC3D16438C85B593630F6B,
	NativeText_set_Length_mF7ACD25110E2CD2E0B98ADF8E5269FAA6B7B3136,
	NativeText_get_Capacity_mE5F3A32D0B61826DF90EE2B2E64EF5EA526B2D5E,
	NativeText_TryResize_mB75889A7FBF6CE1B776D2DA623425B226BDA62A3,
	NativeText_GetUnsafePtr_m94C548B1404B4F4EE679749C5C20C5C7F1A84F49,
	NativeText_ElementAt_mA84CB997C07D52765ACB0815C9B83576AB5817F1,
	NativeText_Clear_mD49A69B25BC09362FD2F16C9DAC37569CFE72E9F,
	NativeText_CompareTo_mB4F973252827A0222F7928E1B3C89FEEF0FB18FC,
	NativeText_Equals_m28844EF971044D0B2C670CBF99E3F941EEBC60C6,
	NativeText_Equals_m06336C6B8E06D0AAADDA47B85DD6D866B7AE8CE2,
	NativeText_Dispose_m63038BF3BBD97854D5CC9C1C6CA5B8B6F401F13B,
	NativeText_CompareTo_m0A844996020B3F42947EE125954C97A863A2CFFE,
	NativeText_Equals_m772A182676730E5AC4C51AD1F3009E4212C50538,
	NativeText_CompareTo_m56EC356FA7227D7D65D1C0E266BEE008D821648D,
	NativeText_op_Equality_m259B9C721354CA4DF0BF84C7017F0121B7FB31B9,
	NativeText_Equals_mAAC438EEC174C08A34CFDF388A412D51DBE9B652,
	NativeText_CompareTo_mDC83F96708C007F463683CBA7105F5F30A1E2053,
	NativeText_op_Equality_mCFE56D1DCECD5AD8C7AE2F0E514FD6118DC3D58A,
	NativeText_Equals_mC7E1FAEB26EB4E65D6725E36526948D27DEDCDAB,
	NativeText_CompareTo_m98C7DA3D7186387868689E2092B70C101471FF85,
	NativeText_op_Equality_m36CF4A20C0B0E638B4B5CE44803BAA1A706DB6F8,
	NativeText_Equals_m76D0CAE524665E08BBA9CE8F86466AE96FD5C8C7,
	NativeText_CompareTo_m71D261DCED41F458B1B9D8996B314D84C9CCE404,
	NativeText_op_Equality_mF250FB75F91ED85D482BAA03D37E79C5BB417DD0,
	NativeText_Equals_mB0ABCB86801685ECD7D98F7E1E77F5E10F4B7402,
	NativeText_CompareTo_m8DC763DD2142D5CC430A74B9AC87358BE6F53E12,
	NativeText_op_Equality_m362C0D603EE9BCDAA391C94719862E94BD25B39D,
	NativeText_Equals_m6A06741AE2C20A0912956700C6FFB487A28804C6,
	NativeText_ToString_mB94FA89B0022211D9B7612D275913E179F7DA691,
	NativeText_GetHashCode_mDC42CD251822F0A3DC067D75C2BD179045C4E406,
	NativeText_Equals_m485EF7755FDE0CEEFD4A4C4C7A23F8358EB3E93B,
	ReadOnly_get_Capacity_m18C003500A9CF63E584FCF4D2A38E439A8FE4377,
	ReadOnly_get_Length_m820BA559FC99A7ED314D334AF78BE97ADCF61122,
	ReadOnly_set_Length_m7852F4413AF08787E52590A2C4BA72B35D01CD26,
	ReadOnly_ElementAt_m6562FA1A434B6025EE69D304BC122128F03707D4,
	ReadOnly_GetUnsafePtr_m71A211ECD401F9D60A6638740319231990A03A42,
	ReadOnly_TryResize_m7263EAE1B98C096590E0ABA69195D588CE2C181A,
	ReadOnly_CompareTo_mA1C25AB3988C9E015332DF13A830234B3CC15EFF,
	ReadOnly_Equals_mB77DA315143191BBF711931FB48EF4C3B1D54C3D,
	ReadOnly_Equals_mBA4A75B3D0D0DB11FE3505C296FEED7D1D8632AF,
	ReadOnly_CompareTo_m6E99BE0FD05C043FC3FE7A6E7608D89DE6A81F85,
	ReadOnly_Equals_mCE9BC83B8AD17DA09D14C6447ACCEF531BA3BA05,
	ReadOnly_CompareTo_mB5B0FCE69EC4E0CBD6EBA1ED00B012240D17693D,
	ReadOnly_op_Equality_m6CFA34E331BEE7AF59C6BD3E81AAA8F36000CD0E,
	ReadOnly_Equals_m28E07244572B5F9EC30186C0955FD8CF9A67A204,
	ReadOnly_CompareTo_m50A0B42856B587167D497681AB6BDC2487A807DA,
	ReadOnly_op_Equality_m69A5ABAE6510F5830F6873CC2018A8F0CCD03EB7,
	ReadOnly_Equals_m471D08AF3CD31C47278F73D1488AFC527D75D0B9,
	ReadOnly_CompareTo_m5A3ACD58B20B20C6256E78C3F4C14FADC53B222A,
	ReadOnly_op_Equality_m569BF71788CAE1CBDD8AD1D38B915103620FB774,
	ReadOnly_Equals_m698ABF40421F36BD7C45D9CD101564ED5DDD9570,
	ReadOnly_CompareTo_mB68D0C12C06B2215E9D69C86D38FC428A4467EED,
	ReadOnly_op_Equality_mB5FCD67E2A878B1CE0510B0A9693556115463361,
	ReadOnly_Equals_mDA5D9768212584A698F76714D27A2498443BE4F2,
	ReadOnly_CompareTo_mCCF733865B5E96CB4C83585D48FCF9FDCD892402,
	ReadOnly_op_Equality_m3F4385071EF09E112F07424C8DDFCE61376E6361,
	ReadOnly_Equals_mC68B6EB3A7289429EED41E2DE41AFFF7E5E6CFA4,
	ReadOnly_ToString_mCB6276C65E02F958F111D95D7EEB6A529EDF52F1,
	ReadOnly_GetHashCode_m0AA9F9ECCDEB716AE2E5A2949098D9529638490F,
	ReadOnly_Equals_mA35F95DAD673F5F2E89A08A52C719CA80E6FB877,
	Spinner_Lock_mF38DA7980552D7B8E60444A1B64ADF440D3DFAA7,
	Spinner_Unlock_m1664CFC96DCA3A7B36092F61C9E34624C497EB1A,
	NULL,
	NULL,
	NULL,
	RewindableAllocator_Initialize_m79AF71658BA5502EEE9E8A8CAB98EDDECBC2964D,
	RewindableAllocator_Rewind_m8EB623F05C707C3AF52FF082A7C095FE3A1CE595,
	RewindableAllocator_Dispose_mD873C81842DAEBF01369B8023805FF930C4C8A2F,
	RewindableAllocator_get_Function_m5461BF00748D262D57516C368291514DC2A84E00,
	RewindableAllocator_Try_m6C020E9D4D72801E38775286491991C3FCE064ED,
	RewindableAllocator_Try_mA4AF5A5088097CB6343C3CC97058959976372C35,
	RewindableAllocator_get_Handle_mF81EDA2102485C46965AAB56347E8F64FE551D9E,
	RewindableAllocator_set_Handle_mE59F440E3F692073FEF47367713603419814928D,
	RewindableAllocator_TryU24BurstManaged_mBB6DAE6A8CDB2E3626C38F3B65186AAF6ACBF6E8,
	MemoryBlock__ctor_m0DEC878B6827C0B400BE1C00CA37C2F8F1C31D7F,
	MemoryBlock_Rewind_m64C7EC463789D78802B8D27695AFFD37133E7069,
	MemoryBlock_Dispose_mE0EAA3828B238B9A26EF6BB5CA7CB5EC592FBD6F,
	MemoryBlock_TryAllocate_m34CC70E419486D5B1C90E7BF4A6762BE3D130F10,
	MemoryBlock_Contains_m2F035A4F1F9063B42ACB1B590C4EFD1741E39CEC,
	Try_000008B3U24PostfixBurstDelegate__ctor_m57E3EE890202AE74FE7E12FBC0AC2972365BCDAC,
	Try_000008B3U24PostfixBurstDelegate_Invoke_m785645BDD70C786352410296AA316FBE67DF8DBB,
	Try_000008B3U24BurstDirectCall_GetFunctionPointerDiscard_mAB3D1EED8ED2B4063EC3967B39DBB3DB757D169A,
	Try_000008B3U24BurstDirectCall_GetFunctionPointer_m9D88099F720A5E5D7E6313E9DF7FD0E9D8B120F2,
	Try_000008B3U24BurstDirectCall_Constructor_mCBF0953D72E1101834468CB323794FB873CD3AF7,
	Try_000008B3U24BurstDirectCall_Initialize_m7B4523DB38BE3980D82832E67561525969F33A26,
	Try_000008B3U24BurstDirectCall__cctor_mAFF466152A95BC683D4086DE52CB1AC56D50480F,
	Try_000008B3U24BurstDirectCall_Invoke_m2BAC3EA10EB68BAC379A736EE8EF4EEDA12137DA,
	Unicode_IsValidCodePoint_m6F8516FA18D35B6D052FD6BDE01D9D497EA06B9D,
	Unicode_NotTrailer_m9FD6FC331044FE0EFC90E29A2808C4A6535E4CAD,
	Unicode_get_ReplacementCharacter_m525CDE0E6CAB489454025711F93FF832A600556A,
	Unicode_get_BadRune_m70003825794A2ABBBF589890D9601B885041E3E8,
	Unicode_Utf8ToUcs_m013E3A507C4B6F5459B09C6EA8EA229BDC979827,
	Unicode_IsLeadingSurrogate_mCFA66EB348CD8F48DB573D45B2658A95BA248F06,
	Unicode_IsTrailingSurrogate_m2ECEB76882DCD6006C95D3861BFA7654D3A22B25,
	Unicode_Utf16ToUcs_m55352C5470C6C4C506B02E9827F05C7285F113D3,
	Unicode_UcsToUtf8_mBE0B9FE432D859A3409B7BD03C26C958CF9301BE,
	Unicode_UcsToUtf16_m14C1098270C0DFFAF6B48D47C3214344FD4FAE0E,
	Unicode_Utf16ToUtf8_mF16BCB8771E0A53EE7464D418792F4CEB99A641E,
	Unicode_Utf8ToUtf16_mF3051E9181A57301EEF945C10B97D3C9356706DD,
	Rune_op_Explicit_mE522A7646091C5FC49F41954FCDB8880AB9D6595,
	Rune_LengthInUtf8Bytes_m2E470564E773DB43A761FC2A5DA17F0885E81489,
	UTF8ArrayUnsafeUtility_Copy_m599E2064905A6DF1D3F386114B76668CD0E5B7B9,
	UTF8ArrayUnsafeUtility_StrCmp_mEA1B1EC00085F9EDEC1CDEFCF53C2E83830374D4,
	UTF8ArrayUnsafeUtility_EqualsUTF8Bytes_m96A84A5B2B6E4ABA06A65995D7EA16477797E754,
	UTF8ArrayUnsafeUtility_StrCmp_m2C38EE679FD06EDDEB1409956BA7398431FF2FEC,
	Comparison__ctor_m40D70075E3FE042352BE5C6589C23C5D7D41668C,
	xxHash3_Avx2HashLongInternalLoop_mCAEEE715FCB699CA2F1B947BCD252AA0F87D2B15,
	xxHash3_Avx2ScrambleAcc_m64D8B68219EA3E164A61D2001E0969263CF098CE,
	xxHash3_Avx2Accumulate_mD57A48AB8FB3471A923F64F7C8B52FF8538E791D,
	xxHash3_Avx2Accumulate512_mBB4B8AAAA2DC7E6B1350597687C11B82E81CEF06,
	xxHash3_Hash64Long_m9950702E864DCCD9B8DEAAE23E7CBB5E79D4AC62,
	xxHash3_Hash128Long_mED9958D31B54E0E0666AAD34A52DE7CDEB802E6F,
	xxHash3_ToUint4_m811AB95294FBBC0F17A5358D0A22669691CE3633,
	xxHash3_Read64LE_mD275A5EFD8727CDE8B8E280D4A5D5B82D5E3B195,
	xxHash3_Write64LE_m79CC2011BF16363F2338D61BE43E99E6467A9437,
	xxHash3_Mul32To64_m9210E9379305FC38A6D69C698F6E1A30013BC4F5,
	xxHash3_XorShift64_mF4245CDE1C4AF6B1CC8F57AAE0DA8C7E04673CFC,
	xxHash3_Mul128Fold64_mF59DCB5142027D151F52C7748BFA28C32B3B8F38,
	xxHash3_Avalanche_m059990B780566C6F04C66700B2BE7817B4FA2F18,
	xxHash3_Mix2Acc_mDEB8D0C149D943295B8A3049A437578BE879BED8,
	xxHash3_MergeAcc_mB01ADB1934EDFE8FE3B2AAB13DA6884EB1133A14,
	xxHash3_DefaultHashLongInternalLoop_m9181A3A8DBE8DBEFF1B730ECC9A9AA5E93110F1B,
	xxHash3_DefaultAccumulate_m3D28C5486CC42D31D2D832F40DEFE1A7CF508CA5,
	xxHash3_DefaultAccumulate512_mFADF15092DA5379116D3FCCFC4238ADBF48D85D7,
	xxHash3_DefaultScrambleAcc_mA46D6E8E1BA4613A50B56C8536B0DA3F50437137,
	xxHash3_Hash64LongU24BurstManaged_m71E36BBD116CCA46ED23162F80B08D3B2F782B4D,
	xxHash3_Hash128LongU24BurstManaged_m961A07284DAB6ADFF52EB4287E9D105AB971FDF6,
	Hash64Long_000008F4U24PostfixBurstDelegate__ctor_mBD21053C281809ADF6ECBA131BEC82EBB3EC1185,
	Hash64Long_000008F4U24PostfixBurstDelegate_Invoke_mD637DD771E9D46F0D2B6D0E383686E7A0C645DEC,
	Hash64Long_000008F4U24BurstDirectCall_GetFunctionPointerDiscard_mF4FADACDE81C41E1CACC4958936D6CA8DA0CA737,
	Hash64Long_000008F4U24BurstDirectCall_GetFunctionPointer_m0B5E2A4795494A2C5E95435865B3139A6B37449E,
	Hash64Long_000008F4U24BurstDirectCall_Constructor_m4FE50D7EF6F2FA3EBF79562DCA19F13A8FB57FBF,
	Hash64Long_000008F4U24BurstDirectCall_Initialize_m9C0FA302804D6C59139845EB6F93F2B455240FBB,
	Hash64Long_000008F4U24BurstDirectCall__cctor_m0E175D3CA9EA57BCE626EB0E1FF2895FE08850F6,
	Hash64Long_000008F4U24BurstDirectCall_Invoke_m7F6D574177DFCDAF3C6CC44E0FDFEBF5129E3DC1,
	Hash128Long_000008FBU24PostfixBurstDelegate__ctor_m3F6314D81B156F12D1A950580AF3869339036A22,
	Hash128Long_000008FBU24PostfixBurstDelegate_Invoke_m2EE80A525330D5CA95BA62585357273CF5CD3E08,
	Hash128Long_000008FBU24BurstDirectCall_GetFunctionPointerDiscard_mAA88F5910E87D551ECD388C3C275C63B20BE2106,
	Hash128Long_000008FBU24BurstDirectCall_GetFunctionPointer_m8BC65E819E1CE22B18DAAEA1EF2E553AF364A18F,
	Hash128Long_000008FBU24BurstDirectCall_Constructor_m05F2CCE734F7CAFCFA0120ECCE368F5C7EDA6C0C,
	Hash128Long_000008FBU24BurstDirectCall_Initialize_m10E736DA8A39D57A5CE218041BA1779936AD9600,
	Hash128Long_000008FBU24BurstDirectCall__cctor_m2CD618EFADD691FB66F7D6AEA85A4A3D550F5639,
	Hash128Long_000008FBU24BurstDirectCall_Invoke_m91D203668FDD811BCFB61014E7F6E92AA04AD059,
	NULL,
	UnsafeDisposeJob_Execute_m6D0C969CE5C1124239FB9B69EC2A1E486E4F38BC,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	UnsafeParallelHashMapData_DeallocateHashMap_m8D0FEE08B8522A1D05FBFFBBB43CB203304F114F,
	UnsafeParallelHashMapDataDispose_Dispose_mCFF4782249AE264F799027E84E4CC704737066A3,
	UnsafeParallelHashMapDataDisposeJob_Execute_m129F30954F3CF5922C620E4DDB1E50E540A06DCC,
	UnsafeParallelHashMapDisposeJob_Execute_mCEF89FC4B3D6D8DC7E213FF274053A41805215E1,
	UnsafeStream_AllocateForEach_m1EE9C9B5CF33ED03FA2C01A5DF2C5D99BAABC065,
	UnsafeStream_Deallocate_mF13D45026C45A52DD441B1F021CC4BE9454D466C,
	UnsafeStream_Dispose_m4D493F40C6ED4C7346017C05FBC006B5632AE49C,
	DisposeJob_Execute_m089DE6120E788ECA6418DFAB36FFC3F4E90FC10E,
	ConstructJobList_Execute_mDECEE80A756D3E19EF0FC58ADB0C4665626F7DAB,
	ConstructJob_Execute_mAA49F134BF92ED7BB0356BC460A30C9B39A87C50,
	UnsafeTextExtensions_AsUnsafeListOfBytes_mA80ABFE08762E38D788ACF506BEB4A0E3621D439,
	UnsafeText__ctor_m4F0450604AA478F4C19A2CD4746878981C7DA31F,
	UnsafeText_get_IsCreated_mEE504C37FEADA4DFD3A6AFB47751B20A1423BBF1,
	UnsafeText_Dispose_m715EBE3FAD0BA5111276CFCB7F65419701F3BABC,
	UnsafeText_ElementAt_mE1021122866981C2F08E17BA1320C2A294886844,
	UnsafeText_GetUnsafePtr_m1C1462A867C4714017B3A5974F1FAF3716FB8F8C,
	UnsafeText_TryResize_m4D31F691405B691F1CB3DABF9ED09D360FB13BDC,
	UnsafeText_get_Capacity_m70C15A6A1753D3D824DEE599902E3E7FFC75F815,
	UnsafeText_get_Length_m7D4616C7F2AAA07EC0E98FB430C6D7DA2D4D5E40,
	UnsafeText_set_Length_m98076AB8E3E55B44FD260347BD22D944DCAE4DD9,
	UnsafeText_ToString_m02C6D467CDF758100B8CCC3199168304B1FCE2B2,
	NULL,
	NULL,
	__JobReflectionRegistrationOutput__1910371556_CreateJobReflectionData_mEF64DEB65AA7AC2A64F15736132D339337A68D67,
	__JobReflectionRegistrationOutput__1910371556_EarlyInit_m9B00513909A2241E426EA3D838FAED0C29E82224,
	U24BurstDirectCallInitializer_Initialize_mBB7299DE1F1DF732C60394307234ED45AE14AD82,
};
extern void AllocatorHandle_get_TableEntry_m09C5D57FF627FEAD3AAE73FB28CB4B9EEB811A63_AdjustorThunk (void);
extern void AllocatorHandle_get_IsInstalled_mB38CD887177A87128DC9A2DE6F866F9EC18FA907_AdjustorThunk (void);
extern void AllocatorHandle_Rewind_mC0426BBD1E638878C33164F91AC3ADC66C850AC1_AdjustorThunk (void);
extern void AllocatorHandle_Install_m0526A06766A02754698DE0115B926C15566CDD3B_AdjustorThunk (void);
extern void AllocatorHandle_get_Value_m24A0A3E433794106E43E9140CC2BB55493C8F30F_AdjustorThunk (void);
extern void AllocatorHandle_get_Function_m031BF3715926C775DC8C946F6B039F99D738C930_AdjustorThunk (void);
extern void AllocatorHandle_Try_m4E8677E100693A0F015076408244AF2BA827CBC9_AdjustorThunk (void);
extern void AllocatorHandle_get_Handle_m440EA9B9A4306115087775DA2AA0AC034107D0E2_AdjustorThunk (void);
extern void AllocatorHandle_set_Handle_mA11E567A5CF5D42E8A5DC080DF007789B47B1648_AdjustorThunk (void);
extern void AllocatorHandle_get_ToAllocator_m34C297958A940D9D35C11B3D28B8E5A08E170FDF_AdjustorThunk (void);
extern void AllocatorHandle_Dispose_mB74CBC8980962C016A6C85F09D3F94775A2C58E3_AdjustorThunk (void);
extern void Range_Dispose_mEABC7B5F5F72CBAC69BAB12C80B8EDEE86B3737A_AdjustorThunk (void);
extern void Block_get_Bytes_m4BB90CD1C72FC56C573BE09951BF1CA68E8BD7D7_AdjustorThunk (void);
extern void Block_get_AllocatedBytes_mFE126B221F670BDA89C5D59C9E63C9724F79BE08_AdjustorThunk (void);
extern void Block_get_Alignment_mC2388F87008B204A333664B9323BD38AA20FD633_AdjustorThunk (void);
extern void Block_set_Alignment_m4E74D90E827C5F58B3EBE91B2F5B097388E0500F_AdjustorThunk (void);
extern void Block_Dispose_m38B474EF3D6A142344D0A58651319FD039FB54ED_AdjustorThunk (void);
extern void Block_TryFree_m0C5FA80AF51DA6F40F40DAE7E97975C3BB402225_AdjustorThunk (void);
extern void StackAllocator_get_Handle_m31D630726EF42BC6C432F8751E6B2BE346BB2267_AdjustorThunk (void);
extern void StackAllocator_set_Handle_m0C72B8ACB04FF327C6CB7787CEF767413DCCF003_AdjustorThunk (void);
extern void StackAllocator_Try_m46B9AAB118C2ACCDB8B0A54DA8E27209CA152558_AdjustorThunk (void);
extern void StackAllocator_get_Function_m86410BC7650042F3F7FF4016A8E9E6164C226EB9_AdjustorThunk (void);
extern void StackAllocator_Dispose_m0872D5C01F22CD4BCCF71EA0185DB18EA14D62CB_AdjustorThunk (void);
extern void SlabAllocator_get_Handle_m09C2CCD8BCEA26214DC514707F222B228A1E04BE_AdjustorThunk (void);
extern void SlabAllocator_set_Handle_m31EE421CD1B214F4BA885C5BB166C9834A7DC22F_AdjustorThunk (void);
extern void SlabAllocator_get_SlabSizeInBytes_mFFD79D4D6B544F1C21CB0EB40BBDFF5D5477A612_AdjustorThunk (void);
extern void SlabAllocator_Try_mE7D3D58D59FB68AE5FA9FF99C0C69FD854F482CD_AdjustorThunk (void);
extern void SlabAllocator_get_Function_m907185458A4C86A6B134EF91E6F323520CEEFB2D_AdjustorThunk (void);
extern void SlabAllocator_Dispose_m9ED75718657190884C0327489A9AE9DB525D7912_AdjustorThunk (void);
extern void Long1024_get_Length_m9FBD7E595A95159BC3DD170F7B715C8DF7BFF520_AdjustorThunk (void);
extern void Long1024_set_Length_mF197E52E1E211AC830C80AE4901AF50C5B9C2C71_AdjustorThunk (void);
extern void Long1024_ElementAt_m14B27AD662901124F5CD90CA28E5B0F15953C185_AdjustorThunk (void);
extern void FixedString32Bytes_GetUnsafePtr_mA653D9296813A2EA93C0D6FC501500008121C3A8_AdjustorThunk (void);
extern void FixedString32Bytes_get_Length_mA349139D3C731DF762B4124B833B60BF0B283797_AdjustorThunk (void);
extern void FixedString32Bytes_set_Length_m9BD2B7F7AA48AF38F72C4C3A3C00C9324F39FA8F_AdjustorThunk (void);
extern void FixedString32Bytes_get_Capacity_m4CEE4CE0CC97C33ED774FACD8211D766FEB549E2_AdjustorThunk (void);
extern void FixedString32Bytes_TryResize_m2E7F8E8810492683C24102604F65EA3294BF6150_AdjustorThunk (void);
extern void FixedString32Bytes_ElementAt_mCB76C2A56E3E397B8038E51DDEF9FD23571F8806_AdjustorThunk (void);
extern void FixedString32Bytes_GetEnumerator_m42731C60A6429D594719D1951D8AC6A9D6D4BF75_AdjustorThunk (void);
extern void FixedString32Bytes_CompareTo_m79B33E69FCFCD08E454F2DEE35FC5F1C9C64E425_AdjustorThunk (void);
extern void FixedString32Bytes_Equals_m1F50C94DF6B470C0BD1C56421A566EE556341766_AdjustorThunk (void);
extern void FixedString32Bytes__ctor_m1C1C73C55B3D020EA211DE2E9E6C7CF0400C6408_AdjustorThunk (void);
extern void FixedString32Bytes_Initialize_m42C7A7BFFFE132CCD2DB5984E779B4467F88D4AC_AdjustorThunk (void);
extern void FixedString32Bytes_CompareTo_mD37FD30C7E3F389CC41E9E1AC9D3D655CA875D19_AdjustorThunk (void);
extern void FixedString32Bytes_Equals_m7CA0083FD7E3A4F0ECEC273977A88425F13806E6_AdjustorThunk (void);
extern void FixedString32Bytes_CompareTo_mFC8EDD03F444D1B497D163FBF38A413A5DF47E70_AdjustorThunk (void);
extern void FixedString32Bytes_Equals_m5E5B5F1ACC5BF8C93EF3A97BFB3CC1338F7D5A1D_AdjustorThunk (void);
extern void FixedString32Bytes_CompareTo_m77F538DDC2D8A7B5EFF36482F83E5B9DDCFE08D8_AdjustorThunk (void);
extern void FixedString32Bytes_Equals_m84D35401C5A983408E4B4D26903EB05BBE05A53A_AdjustorThunk (void);
extern void FixedString32Bytes_CompareTo_m50123082F19E44CB94E7F56E28AD0119C3DE53F8_AdjustorThunk (void);
extern void FixedString32Bytes_Equals_m23ABF390E00DDB6B6D391EFC121CF7474FD751C5_AdjustorThunk (void);
extern void FixedString32Bytes_CompareTo_mB5BB0CB6D7DE907B07C07BB20D5E1F8A74EA8EF8_AdjustorThunk (void);
extern void FixedString32Bytes_Equals_mAAD5520503E00E6340BAA463BC027C035F7C8317_AdjustorThunk (void);
extern void FixedString32Bytes_ToString_mCDBDE58EDFFA82B48A8613E724F92305B4C84914_AdjustorThunk (void);
extern void FixedString32Bytes_GetHashCode_m699B6C9D56B99126CB2F988A01DC87DF8A9CCFFD_AdjustorThunk (void);
extern void FixedString32Bytes_Equals_m95DCBCE85E03295E539FF672D39977CDF1233647_AdjustorThunk (void);
extern void Enumerator__ctor_m23B356A7343A215746DB725E4E273A675CC41A5E_AdjustorThunk (void);
extern void Enumerator_MoveNext_m73472085BD2AC39AA22ACCAFC95F18489EAEEEE4_AdjustorThunk (void);
extern void Enumerator_Reset_mDE0608807936013524A168796EDF6D505449D80D_AdjustorThunk (void);
extern void Enumerator_get_Current_mC9F761A2230A3C6148795946B81AAFD09EADF0A8_AdjustorThunk (void);
extern void Enumerator_System_Collections_IEnumerator_get_Current_m4CDA6A026F68F400E0DFDAEB26184631B55A30B1_AdjustorThunk (void);
extern void FixedString64Bytes_GetUnsafePtr_mB61C0A15578E5DCFB19AC3A6504D66B4DAA88C6E_AdjustorThunk (void);
extern void FixedString64Bytes_get_Length_m7FC1591B9CBA2113727D06FC5EA43AE4A5F16BF5_AdjustorThunk (void);
extern void FixedString64Bytes_set_Length_mF1C5F76690D2DB3BE7F6440213FA758E803462C6_AdjustorThunk (void);
extern void FixedString64Bytes_get_Capacity_mDA79A4601FF4672BDDCCB90EA4EF40613F9C35B0_AdjustorThunk (void);
extern void FixedString64Bytes_TryResize_m9B49BA15D7B2DFE50F4482E4113EE8E44EE9F138_AdjustorThunk (void);
extern void FixedString64Bytes_ElementAt_mCD9252823934D28D6921E529D9777BC8DD420EFD_AdjustorThunk (void);
extern void FixedString64Bytes_GetEnumerator_m646BE217BF88AEC39B4D2F377059EAB5E4B4F571_AdjustorThunk (void);
extern void FixedString64Bytes_CompareTo_m982D1C7FF590144B881A32008C73F1E3A1A8A63E_AdjustorThunk (void);
extern void FixedString64Bytes_Equals_m9E74082A6DAD750845D5D82DCFFCDE5141056D1E_AdjustorThunk (void);
extern void FixedString64Bytes__ctor_mAC760222B77A7BB28B5A7E9A00B9168F3B99F211_AdjustorThunk (void);
extern void FixedString64Bytes_Initialize_m82ABCB4DCF0902729F32BED310A7EC5F6C8021C4_AdjustorThunk (void);
extern void FixedString64Bytes_CompareTo_mC70C4EB2FFEEB4DD7372F57E2D3E5DE3B1773E2F_AdjustorThunk (void);
extern void FixedString64Bytes_Equals_m48F66EA24CE289A0EF42E422ACE3DD222D5011F1_AdjustorThunk (void);
extern void FixedString64Bytes_CompareTo_mF3A1A476403FA9932C229A1A60D43575087D3D4F_AdjustorThunk (void);
extern void FixedString64Bytes_Equals_m5AA29267D5B6D641B68732BFD430646971ECD62D_AdjustorThunk (void);
extern void FixedString64Bytes_CompareTo_m3295FA78979CDA555DFA04CF29CFF649439B8711_AdjustorThunk (void);
extern void FixedString64Bytes_Equals_m5A4B5CD2579DAB7CA024749046D711F4621D29E2_AdjustorThunk (void);
extern void FixedString64Bytes_CompareTo_m19F3795ED3A69BD3F5BC9C3B4C7E35897344AB50_AdjustorThunk (void);
extern void FixedString64Bytes_Equals_mA2CAA548B481B1BBD0A0DC745DEAC5D7824F6970_AdjustorThunk (void);
extern void FixedString64Bytes_CompareTo_m9407A8045F52D4C006BB232ED67AE6A6C4F56962_AdjustorThunk (void);
extern void FixedString64Bytes_Equals_mA9BDDEACE5792EA7513B082B280999828FB6C1EB_AdjustorThunk (void);
extern void FixedString64Bytes_ToString_m67C6568EB9ED23E3B624E581A57E5BE8749A254E_AdjustorThunk (void);
extern void FixedString64Bytes_GetHashCode_mECC24662CA8517B714FEE61D8CD82E7A30BF76D4_AdjustorThunk (void);
extern void FixedString64Bytes_Equals_m0E3F24AA5E7B50BA24D6CEE92A0617E115A86ED9_AdjustorThunk (void);
extern void Enumerator__ctor_mBB5217F352FC6C14E9D4A64DA1865E56E6778AD1_AdjustorThunk (void);
extern void Enumerator_MoveNext_m9C6D69E2B3854694ACCE564B8E9850FEA10AD271_AdjustorThunk (void);
extern void Enumerator_Reset_mC5DE7DA1196A752281928B8B91A6AA62319D9443_AdjustorThunk (void);
extern void Enumerator_get_Current_mBCC0C54F9A50E0BD2661EEF1F43C7ACC7A4904F9_AdjustorThunk (void);
extern void Enumerator_System_Collections_IEnumerator_get_Current_m438D18EF1170C1DEFA7979F9B79CEE90FB7B4B1C_AdjustorThunk (void);
extern void FixedString128Bytes_GetUnsafePtr_m5F280F7783EB21A69893D7FEF551F0E68D3E51BA_AdjustorThunk (void);
extern void FixedString128Bytes_get_Length_mB01AB46B1F5415C0379CCAF98ED2AAE6F08C242E_AdjustorThunk (void);
extern void FixedString128Bytes_set_Length_mBBB43CE32D9C6285009629AE64BC6C34759C2ED2_AdjustorThunk (void);
extern void FixedString128Bytes_get_Capacity_mD161D26116BAA25254B2CCFAB79975FFDA9CA711_AdjustorThunk (void);
extern void FixedString128Bytes_TryResize_mF1DCAFDBD573E0CCC60E0A534A5E854434B6BC39_AdjustorThunk (void);
extern void FixedString128Bytes_ElementAt_mED6187F2E6FEADD9A48BF05E54FE7375989B686D_AdjustorThunk (void);
extern void FixedString128Bytes_GetEnumerator_mB563C36FDCB2BD71EE2C8C959983D5B81EA84ECE_AdjustorThunk (void);
extern void FixedString128Bytes_CompareTo_m8FA619D7CD1ADD9AFC7D92148025CB181A117470_AdjustorThunk (void);
extern void FixedString128Bytes_Equals_mAD97C501810E69B444947F8169017A4A132DCE8B_AdjustorThunk (void);
extern void FixedString128Bytes__ctor_mB0E30CB7FA5CAFF30C1D4FDE75043FD7D7F2AA40_AdjustorThunk (void);
extern void FixedString128Bytes_Initialize_m4A2BA0C3501339D697E1BCAA11763D6BAA45E1C4_AdjustorThunk (void);
extern void FixedString128Bytes_CompareTo_mF9C0984E47EEB20E9E28A4AAE1275E59E42D81B5_AdjustorThunk (void);
extern void FixedString128Bytes_Equals_m5C92E72BEBE4D7E67AF7646C4293F3F9EA7E33EC_AdjustorThunk (void);
extern void FixedString128Bytes_CompareTo_mB8E2D570CDCEE17387FD2BDA4BD35F9AD5C311A8_AdjustorThunk (void);
extern void FixedString128Bytes_Equals_mC7C2AEBA071128B24EB0BD130ED6423EA46275C1_AdjustorThunk (void);
extern void FixedString128Bytes_CompareTo_m3A02146ED7EA6B833583D2C1B38F6FF5B0870E6F_AdjustorThunk (void);
extern void FixedString128Bytes_Equals_m3B91B177B21344B9CB879DFDAA00AB7E812AD5AB_AdjustorThunk (void);
extern void FixedString128Bytes_CompareTo_m7DAC821750F1242E519D29D8A1A01ABD6B5630C6_AdjustorThunk (void);
extern void FixedString128Bytes_Equals_m02FB7C6C4862D4F44EDC97E72A4CADD0BD379D5F_AdjustorThunk (void);
extern void FixedString128Bytes_CompareTo_m340091A89F740B13DE44A1F3AE938A14AE658A4B_AdjustorThunk (void);
extern void FixedString128Bytes_Equals_m0A10CFEB353647A220B407CCEBE12035F0C1D4A2_AdjustorThunk (void);
extern void FixedString128Bytes_ToString_m1CD5B095D5A80759EF2E7F60AA95921369958A29_AdjustorThunk (void);
extern void FixedString128Bytes_GetHashCode_mB211F7E224953364EE91770921BA59760A0E4428_AdjustorThunk (void);
extern void FixedString128Bytes_Equals_mF8C053D97C4EA171FF1A82613C836A231DDF0DD9_AdjustorThunk (void);
extern void Enumerator__ctor_mDFC9DFC8E311728973558AFBE6406A5ACCEBD703_AdjustorThunk (void);
extern void Enumerator_MoveNext_mC0931D9551212966F0C15910269D8E72B140D860_AdjustorThunk (void);
extern void Enumerator_Reset_m5A3D75D51C04D88B8396AC90C18082C3654F9D61_AdjustorThunk (void);
extern void Enumerator_get_Current_mECFDD800F6B12953D71596BC5256B904E30CBD57_AdjustorThunk (void);
extern void Enumerator_System_Collections_IEnumerator_get_Current_m6A2804C78C30C5F9F1D0CDF4B5517C04CB17EA68_AdjustorThunk (void);
extern void FixedString512Bytes_GetUnsafePtr_m6BE151F6C1AC2994321FFC794DD61AEE5E915E05_AdjustorThunk (void);
extern void FixedString512Bytes_get_Length_m07F07806B00F694DA08ED7C3BC04C9FCE7CE4E07_AdjustorThunk (void);
extern void FixedString512Bytes_set_Length_m5BECBE3190D559780E9F84276928B0EC612A1BDE_AdjustorThunk (void);
extern void FixedString512Bytes_get_Capacity_m80069D7197EA5C9AC0149AEE21591CCCFBC0AB55_AdjustorThunk (void);
extern void FixedString512Bytes_TryResize_m924FCE76C50EFD1773E2A417BCA84B2C6B158ABD_AdjustorThunk (void);
extern void FixedString512Bytes_ElementAt_m7B6C3BFA049CBE0952188BD01720D28505483485_AdjustorThunk (void);
extern void FixedString512Bytes_GetEnumerator_mF1B489F6A1B771B4A9D21F8C58BDD76969106703_AdjustorThunk (void);
extern void FixedString512Bytes_CompareTo_mF2E8A9C2171833229322FEBEF4DF783251513B59_AdjustorThunk (void);
extern void FixedString512Bytes_Equals_mE4279608ABCBD5236ADF59E96788F0837787F11A_AdjustorThunk (void);
extern void FixedString512Bytes__ctor_m15D957FCF419703D82533C04DAC6565D2AE139A4_AdjustorThunk (void);
extern void FixedString512Bytes_Initialize_m960C30A7580B4016E4EB827FF9DC2D2B009E85F2_AdjustorThunk (void);
extern void FixedString512Bytes_CompareTo_mFEA161B206C05F7894DDF0CAB24CDBF525AD9F97_AdjustorThunk (void);
extern void FixedString512Bytes_Equals_m2AF82E5FAAF96DBD0A105DF94BD1287804D6D568_AdjustorThunk (void);
extern void FixedString512Bytes_CompareTo_mCF6F5BD2E7F95763F8903514954023697C1A3838_AdjustorThunk (void);
extern void FixedString512Bytes_Equals_m14403CF490B635D08B4FD2DCAB8473AD2A510CE6_AdjustorThunk (void);
extern void FixedString512Bytes_CompareTo_m80D4CAD931945D04AE44A9610D72D8E0839FB2B5_AdjustorThunk (void);
extern void FixedString512Bytes_Equals_m865E75EE8511CFC6CB527D86E54AEE7AE010449A_AdjustorThunk (void);
extern void FixedString512Bytes_CompareTo_m42E210C85C1D09E6698DCDDB40545351895E2574_AdjustorThunk (void);
extern void FixedString512Bytes_Equals_m34F02FA9086BE42B8856DF5A85DA039FD3036E99_AdjustorThunk (void);
extern void FixedString512Bytes_CompareTo_mEC561BBFD17069268207BF2D6BD6E2C93E90A1B7_AdjustorThunk (void);
extern void FixedString512Bytes_Equals_mE00AFBE32D04B3671E8D476855409B35B5C8E674_AdjustorThunk (void);
extern void FixedString512Bytes_ToString_m1A47583FB34608DBEEDC65F9CA6E7B8E7930233F_AdjustorThunk (void);
extern void FixedString512Bytes_GetHashCode_m3F22B7550BFBA5E580D804CCAC7528BA8F64862B_AdjustorThunk (void);
extern void FixedString512Bytes_Equals_m344621B84C32BCC845396B588F2AE18154B9C6C3_AdjustorThunk (void);
extern void Enumerator__ctor_m2273AB72F8E086A370110A63E5B514834696D916_AdjustorThunk (void);
extern void Enumerator_MoveNext_m30A057A8CC9ADF95DF2D81CA7A10D1E5CA1DBEE8_AdjustorThunk (void);
extern void Enumerator_Reset_m76202CD1C0493A8DFF8D04AEF5654A4CBA3AC0ED_AdjustorThunk (void);
extern void Enumerator_get_Current_m8120FC6DFAFEA5A1431B2F74BA6C1A59E546C210_AdjustorThunk (void);
extern void Enumerator_System_Collections_IEnumerator_get_Current_mFB83F84FB64A9ADDBD4C3AED4BA70B2DB349BCBA_AdjustorThunk (void);
extern void FixedString4096Bytes_GetUnsafePtr_mE1DB42C2C0EEA7D6E1B5D0366A4727125D916EEB_AdjustorThunk (void);
extern void FixedString4096Bytes_get_Length_mC2A80F2813739852F0675DA8D2AB68AA92BFC36F_AdjustorThunk (void);
extern void FixedString4096Bytes_set_Length_mB4335BCB8388D446FF0C612F81355BD4861F4F8B_AdjustorThunk (void);
extern void FixedString4096Bytes_get_Capacity_m55EE381DB20EC9C738999C9F562700569906434D_AdjustorThunk (void);
extern void FixedString4096Bytes_TryResize_mC7B1C3EF58A73536BD8A17AEA540B05D1FC4A8F7_AdjustorThunk (void);
extern void FixedString4096Bytes_ElementAt_m2263812C105D24F8DBF9FBD3CB9DB7510DC203A6_AdjustorThunk (void);
extern void FixedString4096Bytes_GetEnumerator_mF5A59B3B0B5AE0EE0603D53EAD8C168E4CE37F93_AdjustorThunk (void);
extern void FixedString4096Bytes_CompareTo_mFE2F6019EBA8EAB9FCCDE7589C8A5F38DA8D4A58_AdjustorThunk (void);
extern void FixedString4096Bytes_Equals_m3C8E7CA4F35F56F0759EEE2D8E5F1D4591D1E598_AdjustorThunk (void);
extern void FixedString4096Bytes__ctor_m223FDD4BEB0B99A01E01845C21B5967766AD8577_AdjustorThunk (void);
extern void FixedString4096Bytes_Initialize_mFD54AAB0FA7203B6FFFB18600E9992DF1F4C7BFB_AdjustorThunk (void);
extern void FixedString4096Bytes_CompareTo_m8D38C45B1D05029012A417EB72A6108BB820C472_AdjustorThunk (void);
extern void FixedString4096Bytes_Equals_mF43146C7063DCE299D0708E173E48D146806796A_AdjustorThunk (void);
extern void FixedString4096Bytes_CompareTo_mA10C3E4B7291BD7C68B64922D7EF51FF9019E7B1_AdjustorThunk (void);
extern void FixedString4096Bytes_Equals_mF4868F94338530FEFCDF35BB76E1C3D64F54CAEF_AdjustorThunk (void);
extern void FixedString4096Bytes_CompareTo_m40115F59AF4ADBC9F7C70B9A30B90DD660526D19_AdjustorThunk (void);
extern void FixedString4096Bytes_Equals_mA00838D2DA8F71412C78B2C37D699B39D7897FB5_AdjustorThunk (void);
extern void FixedString4096Bytes_CompareTo_m16A7E31FA53206F33F3418353A43A2E02F662895_AdjustorThunk (void);
extern void FixedString4096Bytes_Equals_mB76C4C23572B2D379F22EB1EE52B84EE820F075F_AdjustorThunk (void);
extern void FixedString4096Bytes_CompareTo_m1EA8E99320A4B5078747BCE1617B3B1574A53F01_AdjustorThunk (void);
extern void FixedString4096Bytes_Equals_m3991F570BEC852688D532724FD842E0BD3AC2BB6_AdjustorThunk (void);
extern void FixedString4096Bytes_ToString_m1EB551FB3DB9E208AAF34DFFA3864DAB34FD1A26_AdjustorThunk (void);
extern void FixedString4096Bytes_GetHashCode_m4F13B6A3839EDFD34CE37729A815905DD1AAB720_AdjustorThunk (void);
extern void FixedString4096Bytes_Equals_mD182C954C7A720E5C452CE5C9B4BAA20D22A4952_AdjustorThunk (void);
extern void Enumerator__ctor_m4F0B93BF6C32D3867EAB36286E3119996D7C3C28_AdjustorThunk (void);
extern void Enumerator_MoveNext_m7F50D837FC66C2AD460AD21CB7BF3511273088ED_AdjustorThunk (void);
extern void Enumerator_Reset_m8F760A8C9DB3CF6BBC281DFE1B8DD95615F3D3E9_AdjustorThunk (void);
extern void Enumerator_get_Current_mA6FED322887F5D0184B7B7AB986B1AA4082183D1_AdjustorThunk (void);
extern void Enumerator_System_Collections_IEnumerator_get_Current_m18AAF8CEC1B2C674E95734FFA19F0B3041EE5614_AdjustorThunk (void);
extern void NativeListDispose_Dispose_m3B82496C41B796C86D1461D40A96B0FB87D83223_AdjustorThunk (void);
extern void NativeListDisposeJob_Execute_mCADA5ED914628592A5834C51F76A20B7CEC1BF2E_AdjustorThunk (void);
extern void NativeQueueBlockPoolData_FreeBlock_m1F946136116E617CAA61EE33BCF4B55E71A2E6DC_AdjustorThunk (void);
extern void NativeQueueDispose_Dispose_m078AB5F16073B57EF2AA25087E8E5C03C7D0654D_AdjustorThunk (void);
extern void NativeQueueDisposeJob_Execute_m3DBE1DF633A0DE99FD815F98A975B1A5DA0292B9_AdjustorThunk (void);
extern void NativeReferenceDispose_Dispose_mA377C7A0AEC92DC8071534DD15F6A463021F4E81_AdjustorThunk (void);
extern void NativeReferenceDisposeJob_Execute_mE349F3D29F93F04237909925E49CE635ED5E7DC5_AdjustorThunk (void);
extern void NativeStream_Dispose_mD811E61D5C945B23F47323E9C553AD7510889B92_AdjustorThunk (void);
extern void NativeStream_AllocateForEach_mF01F5D8EDB5CF944B8618FB4DB412A4F68A0AAF9_AdjustorThunk (void);
extern void ConstructJobList_Execute_m7A858340428CA2870C89EF776C7E205F7C0961A1_AdjustorThunk (void);
extern void ConstructJob_Execute_m167E59B46B52E41BD4BFA6053CAA2E94BE9DB922_AdjustorThunk (void);
extern void NativeText__ctor_mFA8CF47C8A938652CF0ED6E30599983F88F56969_AdjustorThunk (void);
extern void NativeText__ctor_m0CDCA184957424791B499756D915EC43D9E3EAAD_AdjustorThunk (void);
extern void NativeText__ctor_mD6B4A868B0A67C1A007D2F591ABB0DFF68C53BC9_AdjustorThunk (void);
extern void NativeText_get_Length_mC7FDA822088AB8401ADC3D16438C85B593630F6B_AdjustorThunk (void);
extern void NativeText_set_Length_mF7ACD25110E2CD2E0B98ADF8E5269FAA6B7B3136_AdjustorThunk (void);
extern void NativeText_get_Capacity_mE5F3A32D0B61826DF90EE2B2E64EF5EA526B2D5E_AdjustorThunk (void);
extern void NativeText_TryResize_mB75889A7FBF6CE1B776D2DA623425B226BDA62A3_AdjustorThunk (void);
extern void NativeText_GetUnsafePtr_m94C548B1404B4F4EE679749C5C20C5C7F1A84F49_AdjustorThunk (void);
extern void NativeText_ElementAt_mA84CB997C07D52765ACB0815C9B83576AB5817F1_AdjustorThunk (void);
extern void NativeText_Clear_mD49A69B25BC09362FD2F16C9DAC37569CFE72E9F_AdjustorThunk (void);
extern void NativeText_CompareTo_mB4F973252827A0222F7928E1B3C89FEEF0FB18FC_AdjustorThunk (void);
extern void NativeText_Equals_m28844EF971044D0B2C670CBF99E3F941EEBC60C6_AdjustorThunk (void);
extern void NativeText_Equals_m06336C6B8E06D0AAADDA47B85DD6D866B7AE8CE2_AdjustorThunk (void);
extern void NativeText_Dispose_m63038BF3BBD97854D5CC9C1C6CA5B8B6F401F13B_AdjustorThunk (void);
extern void NativeText_CompareTo_m0A844996020B3F42947EE125954C97A863A2CFFE_AdjustorThunk (void);
extern void NativeText_Equals_m772A182676730E5AC4C51AD1F3009E4212C50538_AdjustorThunk (void);
extern void NativeText_CompareTo_m56EC356FA7227D7D65D1C0E266BEE008D821648D_AdjustorThunk (void);
extern void NativeText_Equals_mAAC438EEC174C08A34CFDF388A412D51DBE9B652_AdjustorThunk (void);
extern void NativeText_CompareTo_mDC83F96708C007F463683CBA7105F5F30A1E2053_AdjustorThunk (void);
extern void NativeText_Equals_mC7E1FAEB26EB4E65D6725E36526948D27DEDCDAB_AdjustorThunk (void);
extern void NativeText_CompareTo_m98C7DA3D7186387868689E2092B70C101471FF85_AdjustorThunk (void);
extern void NativeText_Equals_m76D0CAE524665E08BBA9CE8F86466AE96FD5C8C7_AdjustorThunk (void);
extern void NativeText_CompareTo_m71D261DCED41F458B1B9D8996B314D84C9CCE404_AdjustorThunk (void);
extern void NativeText_Equals_mB0ABCB86801685ECD7D98F7E1E77F5E10F4B7402_AdjustorThunk (void);
extern void NativeText_CompareTo_m8DC763DD2142D5CC430A74B9AC87358BE6F53E12_AdjustorThunk (void);
extern void NativeText_Equals_m6A06741AE2C20A0912956700C6FFB487A28804C6_AdjustorThunk (void);
extern void NativeText_ToString_mB94FA89B0022211D9B7612D275913E179F7DA691_AdjustorThunk (void);
extern void NativeText_GetHashCode_mDC42CD251822F0A3DC067D75C2BD179045C4E406_AdjustorThunk (void);
extern void NativeText_Equals_m485EF7755FDE0CEEFD4A4C4C7A23F8358EB3E93B_AdjustorThunk (void);
extern void ReadOnly_get_Capacity_m18C003500A9CF63E584FCF4D2A38E439A8FE4377_AdjustorThunk (void);
extern void ReadOnly_get_Length_m820BA559FC99A7ED314D334AF78BE97ADCF61122_AdjustorThunk (void);
extern void ReadOnly_set_Length_m7852F4413AF08787E52590A2C4BA72B35D01CD26_AdjustorThunk (void);
extern void ReadOnly_ElementAt_m6562FA1A434B6025EE69D304BC122128F03707D4_AdjustorThunk (void);
extern void ReadOnly_GetUnsafePtr_m71A211ECD401F9D60A6638740319231990A03A42_AdjustorThunk (void);
extern void ReadOnly_TryResize_m7263EAE1B98C096590E0ABA69195D588CE2C181A_AdjustorThunk (void);
extern void ReadOnly_CompareTo_mA1C25AB3988C9E015332DF13A830234B3CC15EFF_AdjustorThunk (void);
extern void ReadOnly_Equals_mB77DA315143191BBF711931FB48EF4C3B1D54C3D_AdjustorThunk (void);
extern void ReadOnly_Equals_mBA4A75B3D0D0DB11FE3505C296FEED7D1D8632AF_AdjustorThunk (void);
extern void ReadOnly_CompareTo_m6E99BE0FD05C043FC3FE7A6E7608D89DE6A81F85_AdjustorThunk (void);
extern void ReadOnly_Equals_mCE9BC83B8AD17DA09D14C6447ACCEF531BA3BA05_AdjustorThunk (void);
extern void ReadOnly_CompareTo_mB5B0FCE69EC4E0CBD6EBA1ED00B012240D17693D_AdjustorThunk (void);
extern void ReadOnly_Equals_m28E07244572B5F9EC30186C0955FD8CF9A67A204_AdjustorThunk (void);
extern void ReadOnly_CompareTo_m50A0B42856B587167D497681AB6BDC2487A807DA_AdjustorThunk (void);
extern void ReadOnly_Equals_m471D08AF3CD31C47278F73D1488AFC527D75D0B9_AdjustorThunk (void);
extern void ReadOnly_CompareTo_m5A3ACD58B20B20C6256E78C3F4C14FADC53B222A_AdjustorThunk (void);
extern void ReadOnly_Equals_m698ABF40421F36BD7C45D9CD101564ED5DDD9570_AdjustorThunk (void);
extern void ReadOnly_CompareTo_mB68D0C12C06B2215E9D69C86D38FC428A4467EED_AdjustorThunk (void);
extern void ReadOnly_Equals_mDA5D9768212584A698F76714D27A2498443BE4F2_AdjustorThunk (void);
extern void ReadOnly_CompareTo_mCCF733865B5E96CB4C83585D48FCF9FDCD892402_AdjustorThunk (void);
extern void ReadOnly_Equals_mC68B6EB3A7289429EED41E2DE41AFFF7E5E6CFA4_AdjustorThunk (void);
extern void ReadOnly_ToString_mCB6276C65E02F958F111D95D7EEB6A529EDF52F1_AdjustorThunk (void);
extern void ReadOnly_GetHashCode_m0AA9F9ECCDEB716AE2E5A2949098D9529638490F_AdjustorThunk (void);
extern void ReadOnly_Equals_mA35F95DAD673F5F2E89A08A52C719CA80E6FB877_AdjustorThunk (void);
extern void Spinner_Lock_mF38DA7980552D7B8E60444A1B64ADF440D3DFAA7_AdjustorThunk (void);
extern void Spinner_Unlock_m1664CFC96DCA3A7B36092F61C9E34624C497EB1A_AdjustorThunk (void);
extern void RewindableAllocator_Initialize_m79AF71658BA5502EEE9E8A8CAB98EDDECBC2964D_AdjustorThunk (void);
extern void RewindableAllocator_Rewind_m8EB623F05C707C3AF52FF082A7C095FE3A1CE595_AdjustorThunk (void);
extern void RewindableAllocator_Dispose_mD873C81842DAEBF01369B8023805FF930C4C8A2F_AdjustorThunk (void);
extern void RewindableAllocator_get_Function_m5461BF00748D262D57516C368291514DC2A84E00_AdjustorThunk (void);
extern void RewindableAllocator_Try_m6C020E9D4D72801E38775286491991C3FCE064ED_AdjustorThunk (void);
extern void RewindableAllocator_get_Handle_mF81EDA2102485C46965AAB56347E8F64FE551D9E_AdjustorThunk (void);
extern void RewindableAllocator_set_Handle_mE59F440E3F692073FEF47367713603419814928D_AdjustorThunk (void);
extern void MemoryBlock__ctor_m0DEC878B6827C0B400BE1C00CA37C2F8F1C31D7F_AdjustorThunk (void);
extern void MemoryBlock_Rewind_m64C7EC463789D78802B8D27695AFFD37133E7069_AdjustorThunk (void);
extern void MemoryBlock_Dispose_mE0EAA3828B238B9A26EF6BB5CA7CB5EC592FBD6F_AdjustorThunk (void);
extern void MemoryBlock_TryAllocate_m34CC70E419486D5B1C90E7BF4A6762BE3D130F10_AdjustorThunk (void);
extern void MemoryBlock_Contains_m2F035A4F1F9063B42ACB1B590C4EFD1741E39CEC_AdjustorThunk (void);
extern void Rune_LengthInUtf8Bytes_m2E470564E773DB43A761FC2A5DA17F0885E81489_AdjustorThunk (void);
extern void Comparison__ctor_m40D70075E3FE042352BE5C6589C23C5D7D41668C_AdjustorThunk (void);
extern void UnsafeDisposeJob_Execute_m6D0C969CE5C1124239FB9B69EC2A1E486E4F38BC_AdjustorThunk (void);
extern void UnsafeParallelHashMapDataDispose_Dispose_mCFF4782249AE264F799027E84E4CC704737066A3_AdjustorThunk (void);
extern void UnsafeParallelHashMapDataDisposeJob_Execute_m129F30954F3CF5922C620E4DDB1E50E540A06DCC_AdjustorThunk (void);
extern void UnsafeParallelHashMapDisposeJob_Execute_mCEF89FC4B3D6D8DC7E213FF274053A41805215E1_AdjustorThunk (void);
extern void UnsafeStream_AllocateForEach_m1EE9C9B5CF33ED03FA2C01A5DF2C5D99BAABC065_AdjustorThunk (void);
extern void UnsafeStream_Deallocate_mF13D45026C45A52DD441B1F021CC4BE9454D466C_AdjustorThunk (void);
extern void UnsafeStream_Dispose_m4D493F40C6ED4C7346017C05FBC006B5632AE49C_AdjustorThunk (void);
extern void DisposeJob_Execute_m089DE6120E788ECA6418DFAB36FFC3F4E90FC10E_AdjustorThunk (void);
extern void ConstructJobList_Execute_mDECEE80A756D3E19EF0FC58ADB0C4665626F7DAB_AdjustorThunk (void);
extern void ConstructJob_Execute_mAA49F134BF92ED7BB0356BC460A30C9B39A87C50_AdjustorThunk (void);
extern void UnsafeText__ctor_m4F0450604AA478F4C19A2CD4746878981C7DA31F_AdjustorThunk (void);
extern void UnsafeText_get_IsCreated_mEE504C37FEADA4DFD3A6AFB47751B20A1423BBF1_AdjustorThunk (void);
extern void UnsafeText_Dispose_m715EBE3FAD0BA5111276CFCB7F65419701F3BABC_AdjustorThunk (void);
extern void UnsafeText_ElementAt_mE1021122866981C2F08E17BA1320C2A294886844_AdjustorThunk (void);
extern void UnsafeText_GetUnsafePtr_m1C1462A867C4714017B3A5974F1FAF3716FB8F8C_AdjustorThunk (void);
extern void UnsafeText_TryResize_m4D31F691405B691F1CB3DABF9ED09D360FB13BDC_AdjustorThunk (void);
extern void UnsafeText_get_Capacity_m70C15A6A1753D3D824DEE599902E3E7FFC75F815_AdjustorThunk (void);
extern void UnsafeText_get_Length_m7D4616C7F2AAA07EC0E98FB430C6D7DA2D4D5E40_AdjustorThunk (void);
extern void UnsafeText_set_Length_m98076AB8E3E55B44FD260347BD22D944DCAE4DD9_AdjustorThunk (void);
extern void UnsafeText_ToString_m02C6D467CDF758100B8CCC3199168304B1FCE2B2_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[277] = 
{
	{ 0x0600003F, AllocatorHandle_get_TableEntry_m09C5D57FF627FEAD3AAE73FB28CB4B9EEB811A63_AdjustorThunk },
	{ 0x06000040, AllocatorHandle_get_IsInstalled_mB38CD887177A87128DC9A2DE6F866F9EC18FA907_AdjustorThunk },
	{ 0x06000041, AllocatorHandle_Rewind_mC0426BBD1E638878C33164F91AC3ADC66C850AC1_AdjustorThunk },
	{ 0x06000042, AllocatorHandle_Install_m0526A06766A02754698DE0115B926C15566CDD3B_AdjustorThunk },
	{ 0x06000044, AllocatorHandle_get_Value_m24A0A3E433794106E43E9140CC2BB55493C8F30F_AdjustorThunk },
	{ 0x06000045, AllocatorHandle_get_Function_m031BF3715926C775DC8C946F6B039F99D738C930_AdjustorThunk },
	{ 0x06000046, AllocatorHandle_Try_m4E8677E100693A0F015076408244AF2BA827CBC9_AdjustorThunk },
	{ 0x06000047, AllocatorHandle_get_Handle_m440EA9B9A4306115087775DA2AA0AC034107D0E2_AdjustorThunk },
	{ 0x06000048, AllocatorHandle_set_Handle_mA11E567A5CF5D42E8A5DC080DF007789B47B1648_AdjustorThunk },
	{ 0x06000049, AllocatorHandle_get_ToAllocator_m34C297958A940D9D35C11B3D28B8E5A08E170FDF_AdjustorThunk },
	{ 0x0600004A, AllocatorHandle_Dispose_mB74CBC8980962C016A6C85F09D3F94775A2C58E3_AdjustorThunk },
	{ 0x0600004B, Range_Dispose_mEABC7B5F5F72CBAC69BAB12C80B8EDEE86B3737A_AdjustorThunk },
	{ 0x0600004C, Block_get_Bytes_m4BB90CD1C72FC56C573BE09951BF1CA68E8BD7D7_AdjustorThunk },
	{ 0x0600004D, Block_get_AllocatedBytes_mFE126B221F670BDA89C5D59C9E63C9724F79BE08_AdjustorThunk },
	{ 0x0600004E, Block_get_Alignment_mC2388F87008B204A333664B9323BD38AA20FD633_AdjustorThunk },
	{ 0x0600004F, Block_set_Alignment_m4E74D90E827C5F58B3EBE91B2F5B097388E0500F_AdjustorThunk },
	{ 0x06000050, Block_Dispose_m38B474EF3D6A142344D0A58651319FD039FB54ED_AdjustorThunk },
	{ 0x06000051, Block_TryFree_m0C5FA80AF51DA6F40F40DAE7E97975C3BB402225_AdjustorThunk },
	{ 0x06000056, StackAllocator_get_Handle_m31D630726EF42BC6C432F8751E6B2BE346BB2267_AdjustorThunk },
	{ 0x06000057, StackAllocator_set_Handle_m0C72B8ACB04FF327C6CB7787CEF767413DCCF003_AdjustorThunk },
	{ 0x06000058, StackAllocator_Try_m46B9AAB118C2ACCDB8B0A54DA8E27209CA152558_AdjustorThunk },
	{ 0x0600005A, StackAllocator_get_Function_m86410BC7650042F3F7FF4016A8E9E6164C226EB9_AdjustorThunk },
	{ 0x0600005B, StackAllocator_Dispose_m0872D5C01F22CD4BCCF71EA0185DB18EA14D62CB_AdjustorThunk },
	{ 0x06000065, SlabAllocator_get_Handle_m09C2CCD8BCEA26214DC514707F222B228A1E04BE_AdjustorThunk },
	{ 0x06000066, SlabAllocator_set_Handle_m31EE421CD1B214F4BA885C5BB166C9834A7DC22F_AdjustorThunk },
	{ 0x06000067, SlabAllocator_get_SlabSizeInBytes_mFFD79D4D6B544F1C21CB0EB40BBDFF5D5477A612_AdjustorThunk },
	{ 0x06000068, SlabAllocator_Try_mE7D3D58D59FB68AE5FA9FF99C0C69FD854F482CD_AdjustorThunk },
	{ 0x0600006A, SlabAllocator_get_Function_m907185458A4C86A6B134EF91E6F323520CEEFB2D_AdjustorThunk },
	{ 0x0600006B, SlabAllocator_Dispose_m9ED75718657190884C0327489A9AE9DB525D7912_AdjustorThunk },
	{ 0x06000088, Long1024_get_Length_m9FBD7E595A95159BC3DD170F7B715C8DF7BFF520_AdjustorThunk },
	{ 0x06000089, Long1024_set_Length_mF197E52E1E211AC830C80AE4901AF50C5B9C2C71_AdjustorThunk },
	{ 0x0600008A, Long1024_ElementAt_m14B27AD662901124F5CD90CA28E5B0F15953C185_AdjustorThunk },
	{ 0x060000FD, FixedString32Bytes_GetUnsafePtr_mA653D9296813A2EA93C0D6FC501500008121C3A8_AdjustorThunk },
	{ 0x060000FE, FixedString32Bytes_get_Length_mA349139D3C731DF762B4124B833B60BF0B283797_AdjustorThunk },
	{ 0x060000FF, FixedString32Bytes_set_Length_m9BD2B7F7AA48AF38F72C4C3A3C00C9324F39FA8F_AdjustorThunk },
	{ 0x06000100, FixedString32Bytes_get_Capacity_m4CEE4CE0CC97C33ED774FACD8211D766FEB549E2_AdjustorThunk },
	{ 0x06000101, FixedString32Bytes_TryResize_m2E7F8E8810492683C24102604F65EA3294BF6150_AdjustorThunk },
	{ 0x06000102, FixedString32Bytes_ElementAt_mCB76C2A56E3E397B8038E51DDEF9FD23571F8806_AdjustorThunk },
	{ 0x06000103, FixedString32Bytes_GetEnumerator_m42731C60A6429D594719D1951D8AC6A9D6D4BF75_AdjustorThunk },
	{ 0x06000104, FixedString32Bytes_CompareTo_m79B33E69FCFCD08E454F2DEE35FC5F1C9C64E425_AdjustorThunk },
	{ 0x06000105, FixedString32Bytes_Equals_m1F50C94DF6B470C0BD1C56421A566EE556341766_AdjustorThunk },
	{ 0x06000106, FixedString32Bytes__ctor_m1C1C73C55B3D020EA211DE2E9E6C7CF0400C6408_AdjustorThunk },
	{ 0x06000107, FixedString32Bytes_Initialize_m42C7A7BFFFE132CCD2DB5984E779B4467F88D4AC_AdjustorThunk },
	{ 0x06000108, FixedString32Bytes_CompareTo_mD37FD30C7E3F389CC41E9E1AC9D3D655CA875D19_AdjustorThunk },
	{ 0x0600010A, FixedString32Bytes_Equals_m7CA0083FD7E3A4F0ECEC273977A88425F13806E6_AdjustorThunk },
	{ 0x0600010B, FixedString32Bytes_CompareTo_mFC8EDD03F444D1B497D163FBF38A413A5DF47E70_AdjustorThunk },
	{ 0x0600010D, FixedString32Bytes_Equals_m5E5B5F1ACC5BF8C93EF3A97BFB3CC1338F7D5A1D_AdjustorThunk },
	{ 0x0600010E, FixedString32Bytes_CompareTo_m77F538DDC2D8A7B5EFF36482F83E5B9DDCFE08D8_AdjustorThunk },
	{ 0x06000110, FixedString32Bytes_Equals_m84D35401C5A983408E4B4D26903EB05BBE05A53A_AdjustorThunk },
	{ 0x06000111, FixedString32Bytes_CompareTo_m50123082F19E44CB94E7F56E28AD0119C3DE53F8_AdjustorThunk },
	{ 0x06000113, FixedString32Bytes_Equals_m23ABF390E00DDB6B6D391EFC121CF7474FD751C5_AdjustorThunk },
	{ 0x06000114, FixedString32Bytes_CompareTo_mB5BB0CB6D7DE907B07C07BB20D5E1F8A74EA8EF8_AdjustorThunk },
	{ 0x06000116, FixedString32Bytes_Equals_mAAD5520503E00E6340BAA463BC027C035F7C8317_AdjustorThunk },
	{ 0x06000117, FixedString32Bytes_ToString_mCDBDE58EDFFA82B48A8613E724F92305B4C84914_AdjustorThunk },
	{ 0x06000118, FixedString32Bytes_GetHashCode_m699B6C9D56B99126CB2F988A01DC87DF8A9CCFFD_AdjustorThunk },
	{ 0x06000119, FixedString32Bytes_Equals_m95DCBCE85E03295E539FF672D39977CDF1233647_AdjustorThunk },
	{ 0x0600011A, Enumerator__ctor_m23B356A7343A215746DB725E4E273A675CC41A5E_AdjustorThunk },
	{ 0x0600011B, Enumerator_MoveNext_m73472085BD2AC39AA22ACCAFC95F18489EAEEEE4_AdjustorThunk },
	{ 0x0600011C, Enumerator_Reset_mDE0608807936013524A168796EDF6D505449D80D_AdjustorThunk },
	{ 0x0600011D, Enumerator_get_Current_mC9F761A2230A3C6148795946B81AAFD09EADF0A8_AdjustorThunk },
	{ 0x0600011E, Enumerator_System_Collections_IEnumerator_get_Current_m4CDA6A026F68F400E0DFDAEB26184631B55A30B1_AdjustorThunk },
	{ 0x0600011F, FixedString64Bytes_GetUnsafePtr_mB61C0A15578E5DCFB19AC3A6504D66B4DAA88C6E_AdjustorThunk },
	{ 0x06000120, FixedString64Bytes_get_Length_m7FC1591B9CBA2113727D06FC5EA43AE4A5F16BF5_AdjustorThunk },
	{ 0x06000121, FixedString64Bytes_set_Length_mF1C5F76690D2DB3BE7F6440213FA758E803462C6_AdjustorThunk },
	{ 0x06000122, FixedString64Bytes_get_Capacity_mDA79A4601FF4672BDDCCB90EA4EF40613F9C35B0_AdjustorThunk },
	{ 0x06000123, FixedString64Bytes_TryResize_m9B49BA15D7B2DFE50F4482E4113EE8E44EE9F138_AdjustorThunk },
	{ 0x06000124, FixedString64Bytes_ElementAt_mCD9252823934D28D6921E529D9777BC8DD420EFD_AdjustorThunk },
	{ 0x06000125, FixedString64Bytes_GetEnumerator_m646BE217BF88AEC39B4D2F377059EAB5E4B4F571_AdjustorThunk },
	{ 0x06000126, FixedString64Bytes_CompareTo_m982D1C7FF590144B881A32008C73F1E3A1A8A63E_AdjustorThunk },
	{ 0x06000127, FixedString64Bytes_Equals_m9E74082A6DAD750845D5D82DCFFCDE5141056D1E_AdjustorThunk },
	{ 0x06000128, FixedString64Bytes__ctor_mAC760222B77A7BB28B5A7E9A00B9168F3B99F211_AdjustorThunk },
	{ 0x06000129, FixedString64Bytes_Initialize_m82ABCB4DCF0902729F32BED310A7EC5F6C8021C4_AdjustorThunk },
	{ 0x0600012A, FixedString64Bytes_CompareTo_mC70C4EB2FFEEB4DD7372F57E2D3E5DE3B1773E2F_AdjustorThunk },
	{ 0x0600012C, FixedString64Bytes_Equals_m48F66EA24CE289A0EF42E422ACE3DD222D5011F1_AdjustorThunk },
	{ 0x0600012D, FixedString64Bytes_CompareTo_mF3A1A476403FA9932C229A1A60D43575087D3D4F_AdjustorThunk },
	{ 0x0600012F, FixedString64Bytes_Equals_m5AA29267D5B6D641B68732BFD430646971ECD62D_AdjustorThunk },
	{ 0x06000130, FixedString64Bytes_CompareTo_m3295FA78979CDA555DFA04CF29CFF649439B8711_AdjustorThunk },
	{ 0x06000132, FixedString64Bytes_Equals_m5A4B5CD2579DAB7CA024749046D711F4621D29E2_AdjustorThunk },
	{ 0x06000133, FixedString64Bytes_CompareTo_m19F3795ED3A69BD3F5BC9C3B4C7E35897344AB50_AdjustorThunk },
	{ 0x06000135, FixedString64Bytes_Equals_mA2CAA548B481B1BBD0A0DC745DEAC5D7824F6970_AdjustorThunk },
	{ 0x06000136, FixedString64Bytes_CompareTo_m9407A8045F52D4C006BB232ED67AE6A6C4F56962_AdjustorThunk },
	{ 0x06000138, FixedString64Bytes_Equals_mA9BDDEACE5792EA7513B082B280999828FB6C1EB_AdjustorThunk },
	{ 0x06000139, FixedString64Bytes_ToString_m67C6568EB9ED23E3B624E581A57E5BE8749A254E_AdjustorThunk },
	{ 0x0600013A, FixedString64Bytes_GetHashCode_mECC24662CA8517B714FEE61D8CD82E7A30BF76D4_AdjustorThunk },
	{ 0x0600013B, FixedString64Bytes_Equals_m0E3F24AA5E7B50BA24D6CEE92A0617E115A86ED9_AdjustorThunk },
	{ 0x0600013C, Enumerator__ctor_mBB5217F352FC6C14E9D4A64DA1865E56E6778AD1_AdjustorThunk },
	{ 0x0600013D, Enumerator_MoveNext_m9C6D69E2B3854694ACCE564B8E9850FEA10AD271_AdjustorThunk },
	{ 0x0600013E, Enumerator_Reset_mC5DE7DA1196A752281928B8B91A6AA62319D9443_AdjustorThunk },
	{ 0x0600013F, Enumerator_get_Current_mBCC0C54F9A50E0BD2661EEF1F43C7ACC7A4904F9_AdjustorThunk },
	{ 0x06000140, Enumerator_System_Collections_IEnumerator_get_Current_m438D18EF1170C1DEFA7979F9B79CEE90FB7B4B1C_AdjustorThunk },
	{ 0x06000141, FixedString128Bytes_GetUnsafePtr_m5F280F7783EB21A69893D7FEF551F0E68D3E51BA_AdjustorThunk },
	{ 0x06000142, FixedString128Bytes_get_Length_mB01AB46B1F5415C0379CCAF98ED2AAE6F08C242E_AdjustorThunk },
	{ 0x06000143, FixedString128Bytes_set_Length_mBBB43CE32D9C6285009629AE64BC6C34759C2ED2_AdjustorThunk },
	{ 0x06000144, FixedString128Bytes_get_Capacity_mD161D26116BAA25254B2CCFAB79975FFDA9CA711_AdjustorThunk },
	{ 0x06000145, FixedString128Bytes_TryResize_mF1DCAFDBD573E0CCC60E0A534A5E854434B6BC39_AdjustorThunk },
	{ 0x06000146, FixedString128Bytes_ElementAt_mED6187F2E6FEADD9A48BF05E54FE7375989B686D_AdjustorThunk },
	{ 0x06000147, FixedString128Bytes_GetEnumerator_mB563C36FDCB2BD71EE2C8C959983D5B81EA84ECE_AdjustorThunk },
	{ 0x06000148, FixedString128Bytes_CompareTo_m8FA619D7CD1ADD9AFC7D92148025CB181A117470_AdjustorThunk },
	{ 0x06000149, FixedString128Bytes_Equals_mAD97C501810E69B444947F8169017A4A132DCE8B_AdjustorThunk },
	{ 0x0600014A, FixedString128Bytes__ctor_mB0E30CB7FA5CAFF30C1D4FDE75043FD7D7F2AA40_AdjustorThunk },
	{ 0x0600014B, FixedString128Bytes_Initialize_m4A2BA0C3501339D697E1BCAA11763D6BAA45E1C4_AdjustorThunk },
	{ 0x0600014C, FixedString128Bytes_CompareTo_mF9C0984E47EEB20E9E28A4AAE1275E59E42D81B5_AdjustorThunk },
	{ 0x0600014E, FixedString128Bytes_Equals_m5C92E72BEBE4D7E67AF7646C4293F3F9EA7E33EC_AdjustorThunk },
	{ 0x0600014F, FixedString128Bytes_CompareTo_mB8E2D570CDCEE17387FD2BDA4BD35F9AD5C311A8_AdjustorThunk },
	{ 0x06000151, FixedString128Bytes_Equals_mC7C2AEBA071128B24EB0BD130ED6423EA46275C1_AdjustorThunk },
	{ 0x06000152, FixedString128Bytes_CompareTo_m3A02146ED7EA6B833583D2C1B38F6FF5B0870E6F_AdjustorThunk },
	{ 0x06000154, FixedString128Bytes_Equals_m3B91B177B21344B9CB879DFDAA00AB7E812AD5AB_AdjustorThunk },
	{ 0x06000155, FixedString128Bytes_CompareTo_m7DAC821750F1242E519D29D8A1A01ABD6B5630C6_AdjustorThunk },
	{ 0x06000157, FixedString128Bytes_Equals_m02FB7C6C4862D4F44EDC97E72A4CADD0BD379D5F_AdjustorThunk },
	{ 0x06000158, FixedString128Bytes_CompareTo_m340091A89F740B13DE44A1F3AE938A14AE658A4B_AdjustorThunk },
	{ 0x0600015A, FixedString128Bytes_Equals_m0A10CFEB353647A220B407CCEBE12035F0C1D4A2_AdjustorThunk },
	{ 0x0600015B, FixedString128Bytes_ToString_m1CD5B095D5A80759EF2E7F60AA95921369958A29_AdjustorThunk },
	{ 0x0600015C, FixedString128Bytes_GetHashCode_mB211F7E224953364EE91770921BA59760A0E4428_AdjustorThunk },
	{ 0x0600015D, FixedString128Bytes_Equals_mF8C053D97C4EA171FF1A82613C836A231DDF0DD9_AdjustorThunk },
	{ 0x0600015E, Enumerator__ctor_mDFC9DFC8E311728973558AFBE6406A5ACCEBD703_AdjustorThunk },
	{ 0x0600015F, Enumerator_MoveNext_mC0931D9551212966F0C15910269D8E72B140D860_AdjustorThunk },
	{ 0x06000160, Enumerator_Reset_m5A3D75D51C04D88B8396AC90C18082C3654F9D61_AdjustorThunk },
	{ 0x06000161, Enumerator_get_Current_mECFDD800F6B12953D71596BC5256B904E30CBD57_AdjustorThunk },
	{ 0x06000162, Enumerator_System_Collections_IEnumerator_get_Current_m6A2804C78C30C5F9F1D0CDF4B5517C04CB17EA68_AdjustorThunk },
	{ 0x06000163, FixedString512Bytes_GetUnsafePtr_m6BE151F6C1AC2994321FFC794DD61AEE5E915E05_AdjustorThunk },
	{ 0x06000164, FixedString512Bytes_get_Length_m07F07806B00F694DA08ED7C3BC04C9FCE7CE4E07_AdjustorThunk },
	{ 0x06000165, FixedString512Bytes_set_Length_m5BECBE3190D559780E9F84276928B0EC612A1BDE_AdjustorThunk },
	{ 0x06000166, FixedString512Bytes_get_Capacity_m80069D7197EA5C9AC0149AEE21591CCCFBC0AB55_AdjustorThunk },
	{ 0x06000167, FixedString512Bytes_TryResize_m924FCE76C50EFD1773E2A417BCA84B2C6B158ABD_AdjustorThunk },
	{ 0x06000168, FixedString512Bytes_ElementAt_m7B6C3BFA049CBE0952188BD01720D28505483485_AdjustorThunk },
	{ 0x06000169, FixedString512Bytes_GetEnumerator_mF1B489F6A1B771B4A9D21F8C58BDD76969106703_AdjustorThunk },
	{ 0x0600016A, FixedString512Bytes_CompareTo_mF2E8A9C2171833229322FEBEF4DF783251513B59_AdjustorThunk },
	{ 0x0600016B, FixedString512Bytes_Equals_mE4279608ABCBD5236ADF59E96788F0837787F11A_AdjustorThunk },
	{ 0x0600016C, FixedString512Bytes__ctor_m15D957FCF419703D82533C04DAC6565D2AE139A4_AdjustorThunk },
	{ 0x0600016D, FixedString512Bytes_Initialize_m960C30A7580B4016E4EB827FF9DC2D2B009E85F2_AdjustorThunk },
	{ 0x0600016E, FixedString512Bytes_CompareTo_mFEA161B206C05F7894DDF0CAB24CDBF525AD9F97_AdjustorThunk },
	{ 0x06000170, FixedString512Bytes_Equals_m2AF82E5FAAF96DBD0A105DF94BD1287804D6D568_AdjustorThunk },
	{ 0x06000171, FixedString512Bytes_CompareTo_mCF6F5BD2E7F95763F8903514954023697C1A3838_AdjustorThunk },
	{ 0x06000173, FixedString512Bytes_Equals_m14403CF490B635D08B4FD2DCAB8473AD2A510CE6_AdjustorThunk },
	{ 0x06000174, FixedString512Bytes_CompareTo_m80D4CAD931945D04AE44A9610D72D8E0839FB2B5_AdjustorThunk },
	{ 0x06000176, FixedString512Bytes_Equals_m865E75EE8511CFC6CB527D86E54AEE7AE010449A_AdjustorThunk },
	{ 0x06000177, FixedString512Bytes_CompareTo_m42E210C85C1D09E6698DCDDB40545351895E2574_AdjustorThunk },
	{ 0x06000179, FixedString512Bytes_Equals_m34F02FA9086BE42B8856DF5A85DA039FD3036E99_AdjustorThunk },
	{ 0x0600017A, FixedString512Bytes_CompareTo_mEC561BBFD17069268207BF2D6BD6E2C93E90A1B7_AdjustorThunk },
	{ 0x0600017C, FixedString512Bytes_Equals_mE00AFBE32D04B3671E8D476855409B35B5C8E674_AdjustorThunk },
	{ 0x0600017E, FixedString512Bytes_ToString_m1A47583FB34608DBEEDC65F9CA6E7B8E7930233F_AdjustorThunk },
	{ 0x0600017F, FixedString512Bytes_GetHashCode_m3F22B7550BFBA5E580D804CCAC7528BA8F64862B_AdjustorThunk },
	{ 0x06000180, FixedString512Bytes_Equals_m344621B84C32BCC845396B588F2AE18154B9C6C3_AdjustorThunk },
	{ 0x06000181, Enumerator__ctor_m2273AB72F8E086A370110A63E5B514834696D916_AdjustorThunk },
	{ 0x06000182, Enumerator_MoveNext_m30A057A8CC9ADF95DF2D81CA7A10D1E5CA1DBEE8_AdjustorThunk },
	{ 0x06000183, Enumerator_Reset_m76202CD1C0493A8DFF8D04AEF5654A4CBA3AC0ED_AdjustorThunk },
	{ 0x06000184, Enumerator_get_Current_m8120FC6DFAFEA5A1431B2F74BA6C1A59E546C210_AdjustorThunk },
	{ 0x06000185, Enumerator_System_Collections_IEnumerator_get_Current_mFB83F84FB64A9ADDBD4C3AED4BA70B2DB349BCBA_AdjustorThunk },
	{ 0x06000186, FixedString4096Bytes_GetUnsafePtr_mE1DB42C2C0EEA7D6E1B5D0366A4727125D916EEB_AdjustorThunk },
	{ 0x06000187, FixedString4096Bytes_get_Length_mC2A80F2813739852F0675DA8D2AB68AA92BFC36F_AdjustorThunk },
	{ 0x06000188, FixedString4096Bytes_set_Length_mB4335BCB8388D446FF0C612F81355BD4861F4F8B_AdjustorThunk },
	{ 0x06000189, FixedString4096Bytes_get_Capacity_m55EE381DB20EC9C738999C9F562700569906434D_AdjustorThunk },
	{ 0x0600018A, FixedString4096Bytes_TryResize_mC7B1C3EF58A73536BD8A17AEA540B05D1FC4A8F7_AdjustorThunk },
	{ 0x0600018B, FixedString4096Bytes_ElementAt_m2263812C105D24F8DBF9FBD3CB9DB7510DC203A6_AdjustorThunk },
	{ 0x0600018C, FixedString4096Bytes_GetEnumerator_mF5A59B3B0B5AE0EE0603D53EAD8C168E4CE37F93_AdjustorThunk },
	{ 0x0600018D, FixedString4096Bytes_CompareTo_mFE2F6019EBA8EAB9FCCDE7589C8A5F38DA8D4A58_AdjustorThunk },
	{ 0x0600018E, FixedString4096Bytes_Equals_m3C8E7CA4F35F56F0759EEE2D8E5F1D4591D1E598_AdjustorThunk },
	{ 0x0600018F, FixedString4096Bytes__ctor_m223FDD4BEB0B99A01E01845C21B5967766AD8577_AdjustorThunk },
	{ 0x06000190, FixedString4096Bytes_Initialize_mFD54AAB0FA7203B6FFFB18600E9992DF1F4C7BFB_AdjustorThunk },
	{ 0x06000191, FixedString4096Bytes_CompareTo_m8D38C45B1D05029012A417EB72A6108BB820C472_AdjustorThunk },
	{ 0x06000193, FixedString4096Bytes_Equals_mF43146C7063DCE299D0708E173E48D146806796A_AdjustorThunk },
	{ 0x06000194, FixedString4096Bytes_CompareTo_mA10C3E4B7291BD7C68B64922D7EF51FF9019E7B1_AdjustorThunk },
	{ 0x06000196, FixedString4096Bytes_Equals_mF4868F94338530FEFCDF35BB76E1C3D64F54CAEF_AdjustorThunk },
	{ 0x06000197, FixedString4096Bytes_CompareTo_m40115F59AF4ADBC9F7C70B9A30B90DD660526D19_AdjustorThunk },
	{ 0x06000199, FixedString4096Bytes_Equals_mA00838D2DA8F71412C78B2C37D699B39D7897FB5_AdjustorThunk },
	{ 0x0600019A, FixedString4096Bytes_CompareTo_m16A7E31FA53206F33F3418353A43A2E02F662895_AdjustorThunk },
	{ 0x0600019C, FixedString4096Bytes_Equals_mB76C4C23572B2D379F22EB1EE52B84EE820F075F_AdjustorThunk },
	{ 0x0600019D, FixedString4096Bytes_CompareTo_m1EA8E99320A4B5078747BCE1617B3B1574A53F01_AdjustorThunk },
	{ 0x0600019F, FixedString4096Bytes_Equals_m3991F570BEC852688D532724FD842E0BD3AC2BB6_AdjustorThunk },
	{ 0x060001A0, FixedString4096Bytes_ToString_m1EB551FB3DB9E208AAF34DFFA3864DAB34FD1A26_AdjustorThunk },
	{ 0x060001A1, FixedString4096Bytes_GetHashCode_m4F13B6A3839EDFD34CE37729A815905DD1AAB720_AdjustorThunk },
	{ 0x060001A2, FixedString4096Bytes_Equals_mD182C954C7A720E5C452CE5C9B4BAA20D22A4952_AdjustorThunk },
	{ 0x060001A3, Enumerator__ctor_m4F0B93BF6C32D3867EAB36286E3119996D7C3C28_AdjustorThunk },
	{ 0x060001A4, Enumerator_MoveNext_m7F50D837FC66C2AD460AD21CB7BF3511273088ED_AdjustorThunk },
	{ 0x060001A5, Enumerator_Reset_m8F760A8C9DB3CF6BBC281DFE1B8DD95615F3D3E9_AdjustorThunk },
	{ 0x060001A6, Enumerator_get_Current_mA6FED322887F5D0184B7B7AB986B1AA4082183D1_AdjustorThunk },
	{ 0x060001A7, Enumerator_System_Collections_IEnumerator_get_Current_m18AAF8CEC1B2C674E95734FFA19F0B3041EE5614_AdjustorThunk },
	{ 0x060001D8, NativeListDispose_Dispose_m3B82496C41B796C86D1461D40A96B0FB87D83223_AdjustorThunk },
	{ 0x060001D9, NativeListDisposeJob_Execute_mCADA5ED914628592A5834C51F76A20B7CEC1BF2E_AdjustorThunk },
	{ 0x060001DA, NativeQueueBlockPoolData_FreeBlock_m1F946136116E617CAA61EE33BCF4B55E71A2E6DC_AdjustorThunk },
	{ 0x060001DC, NativeQueueDispose_Dispose_m078AB5F16073B57EF2AA25087E8E5C03C7D0654D_AdjustorThunk },
	{ 0x060001DD, NativeQueueDisposeJob_Execute_m3DBE1DF633A0DE99FD815F98A975B1A5DA0292B9_AdjustorThunk },
	{ 0x060001DE, NativeReferenceDispose_Dispose_mA377C7A0AEC92DC8071534DD15F6A463021F4E81_AdjustorThunk },
	{ 0x060001DF, NativeReferenceDisposeJob_Execute_mE349F3D29F93F04237909925E49CE635ED5E7DC5_AdjustorThunk },
	{ 0x060001ED, NativeStream_Dispose_mD811E61D5C945B23F47323E9C553AD7510889B92_AdjustorThunk },
	{ 0x060001EE, NativeStream_AllocateForEach_mF01F5D8EDB5CF944B8618FB4DB412A4F68A0AAF9_AdjustorThunk },
	{ 0x060001EF, ConstructJobList_Execute_m7A858340428CA2870C89EF776C7E205F7C0961A1_AdjustorThunk },
	{ 0x060001F0, ConstructJob_Execute_m167E59B46B52E41BD4BFA6053CAA2E94BE9DB922_AdjustorThunk },
	{ 0x060001F1, NativeText__ctor_mFA8CF47C8A938652CF0ED6E30599983F88F56969_AdjustorThunk },
	{ 0x060001F2, NativeText__ctor_m0CDCA184957424791B499756D915EC43D9E3EAAD_AdjustorThunk },
	{ 0x060001F3, NativeText__ctor_mD6B4A868B0A67C1A007D2F591ABB0DFF68C53BC9_AdjustorThunk },
	{ 0x060001F4, NativeText_get_Length_mC7FDA822088AB8401ADC3D16438C85B593630F6B_AdjustorThunk },
	{ 0x060001F5, NativeText_set_Length_mF7ACD25110E2CD2E0B98ADF8E5269FAA6B7B3136_AdjustorThunk },
	{ 0x060001F6, NativeText_get_Capacity_mE5F3A32D0B61826DF90EE2B2E64EF5EA526B2D5E_AdjustorThunk },
	{ 0x060001F7, NativeText_TryResize_mB75889A7FBF6CE1B776D2DA623425B226BDA62A3_AdjustorThunk },
	{ 0x060001F8, NativeText_GetUnsafePtr_m94C548B1404B4F4EE679749C5C20C5C7F1A84F49_AdjustorThunk },
	{ 0x060001F9, NativeText_ElementAt_mA84CB997C07D52765ACB0815C9B83576AB5817F1_AdjustorThunk },
	{ 0x060001FA, NativeText_Clear_mD49A69B25BC09362FD2F16C9DAC37569CFE72E9F_AdjustorThunk },
	{ 0x060001FB, NativeText_CompareTo_mB4F973252827A0222F7928E1B3C89FEEF0FB18FC_AdjustorThunk },
	{ 0x060001FC, NativeText_Equals_m28844EF971044D0B2C670CBF99E3F941EEBC60C6_AdjustorThunk },
	{ 0x060001FD, NativeText_Equals_m06336C6B8E06D0AAADDA47B85DD6D866B7AE8CE2_AdjustorThunk },
	{ 0x060001FE, NativeText_Dispose_m63038BF3BBD97854D5CC9C1C6CA5B8B6F401F13B_AdjustorThunk },
	{ 0x060001FF, NativeText_CompareTo_m0A844996020B3F42947EE125954C97A863A2CFFE_AdjustorThunk },
	{ 0x06000200, NativeText_Equals_m772A182676730E5AC4C51AD1F3009E4212C50538_AdjustorThunk },
	{ 0x06000201, NativeText_CompareTo_m56EC356FA7227D7D65D1C0E266BEE008D821648D_AdjustorThunk },
	{ 0x06000203, NativeText_Equals_mAAC438EEC174C08A34CFDF388A412D51DBE9B652_AdjustorThunk },
	{ 0x06000204, NativeText_CompareTo_mDC83F96708C007F463683CBA7105F5F30A1E2053_AdjustorThunk },
	{ 0x06000206, NativeText_Equals_mC7E1FAEB26EB4E65D6725E36526948D27DEDCDAB_AdjustorThunk },
	{ 0x06000207, NativeText_CompareTo_m98C7DA3D7186387868689E2092B70C101471FF85_AdjustorThunk },
	{ 0x06000209, NativeText_Equals_m76D0CAE524665E08BBA9CE8F86466AE96FD5C8C7_AdjustorThunk },
	{ 0x0600020A, NativeText_CompareTo_m71D261DCED41F458B1B9D8996B314D84C9CCE404_AdjustorThunk },
	{ 0x0600020C, NativeText_Equals_mB0ABCB86801685ECD7D98F7E1E77F5E10F4B7402_AdjustorThunk },
	{ 0x0600020D, NativeText_CompareTo_m8DC763DD2142D5CC430A74B9AC87358BE6F53E12_AdjustorThunk },
	{ 0x0600020F, NativeText_Equals_m6A06741AE2C20A0912956700C6FFB487A28804C6_AdjustorThunk },
	{ 0x06000210, NativeText_ToString_mB94FA89B0022211D9B7612D275913E179F7DA691_AdjustorThunk },
	{ 0x06000211, NativeText_GetHashCode_mDC42CD251822F0A3DC067D75C2BD179045C4E406_AdjustorThunk },
	{ 0x06000212, NativeText_Equals_m485EF7755FDE0CEEFD4A4C4C7A23F8358EB3E93B_AdjustorThunk },
	{ 0x06000213, ReadOnly_get_Capacity_m18C003500A9CF63E584FCF4D2A38E439A8FE4377_AdjustorThunk },
	{ 0x06000214, ReadOnly_get_Length_m820BA559FC99A7ED314D334AF78BE97ADCF61122_AdjustorThunk },
	{ 0x06000215, ReadOnly_set_Length_m7852F4413AF08787E52590A2C4BA72B35D01CD26_AdjustorThunk },
	{ 0x06000216, ReadOnly_ElementAt_m6562FA1A434B6025EE69D304BC122128F03707D4_AdjustorThunk },
	{ 0x06000217, ReadOnly_GetUnsafePtr_m71A211ECD401F9D60A6638740319231990A03A42_AdjustorThunk },
	{ 0x06000218, ReadOnly_TryResize_m7263EAE1B98C096590E0ABA69195D588CE2C181A_AdjustorThunk },
	{ 0x06000219, ReadOnly_CompareTo_mA1C25AB3988C9E015332DF13A830234B3CC15EFF_AdjustorThunk },
	{ 0x0600021A, ReadOnly_Equals_mB77DA315143191BBF711931FB48EF4C3B1D54C3D_AdjustorThunk },
	{ 0x0600021B, ReadOnly_Equals_mBA4A75B3D0D0DB11FE3505C296FEED7D1D8632AF_AdjustorThunk },
	{ 0x0600021C, ReadOnly_CompareTo_m6E99BE0FD05C043FC3FE7A6E7608D89DE6A81F85_AdjustorThunk },
	{ 0x0600021D, ReadOnly_Equals_mCE9BC83B8AD17DA09D14C6447ACCEF531BA3BA05_AdjustorThunk },
	{ 0x0600021E, ReadOnly_CompareTo_mB5B0FCE69EC4E0CBD6EBA1ED00B012240D17693D_AdjustorThunk },
	{ 0x06000220, ReadOnly_Equals_m28E07244572B5F9EC30186C0955FD8CF9A67A204_AdjustorThunk },
	{ 0x06000221, ReadOnly_CompareTo_m50A0B42856B587167D497681AB6BDC2487A807DA_AdjustorThunk },
	{ 0x06000223, ReadOnly_Equals_m471D08AF3CD31C47278F73D1488AFC527D75D0B9_AdjustorThunk },
	{ 0x06000224, ReadOnly_CompareTo_m5A3ACD58B20B20C6256E78C3F4C14FADC53B222A_AdjustorThunk },
	{ 0x06000226, ReadOnly_Equals_m698ABF40421F36BD7C45D9CD101564ED5DDD9570_AdjustorThunk },
	{ 0x06000227, ReadOnly_CompareTo_mB68D0C12C06B2215E9D69C86D38FC428A4467EED_AdjustorThunk },
	{ 0x06000229, ReadOnly_Equals_mDA5D9768212584A698F76714D27A2498443BE4F2_AdjustorThunk },
	{ 0x0600022A, ReadOnly_CompareTo_mCCF733865B5E96CB4C83585D48FCF9FDCD892402_AdjustorThunk },
	{ 0x0600022C, ReadOnly_Equals_mC68B6EB3A7289429EED41E2DE41AFFF7E5E6CFA4_AdjustorThunk },
	{ 0x0600022D, ReadOnly_ToString_mCB6276C65E02F958F111D95D7EEB6A529EDF52F1_AdjustorThunk },
	{ 0x0600022E, ReadOnly_GetHashCode_m0AA9F9ECCDEB716AE2E5A2949098D9529638490F_AdjustorThunk },
	{ 0x0600022F, ReadOnly_Equals_mA35F95DAD673F5F2E89A08A52C719CA80E6FB877_AdjustorThunk },
	{ 0x06000230, Spinner_Lock_mF38DA7980552D7B8E60444A1B64ADF440D3DFAA7_AdjustorThunk },
	{ 0x06000231, Spinner_Unlock_m1664CFC96DCA3A7B36092F61C9E34624C497EB1A_AdjustorThunk },
	{ 0x06000235, RewindableAllocator_Initialize_m79AF71658BA5502EEE9E8A8CAB98EDDECBC2964D_AdjustorThunk },
	{ 0x06000236, RewindableAllocator_Rewind_m8EB623F05C707C3AF52FF082A7C095FE3A1CE595_AdjustorThunk },
	{ 0x06000237, RewindableAllocator_Dispose_mD873C81842DAEBF01369B8023805FF930C4C8A2F_AdjustorThunk },
	{ 0x06000238, RewindableAllocator_get_Function_m5461BF00748D262D57516C368291514DC2A84E00_AdjustorThunk },
	{ 0x06000239, RewindableAllocator_Try_m6C020E9D4D72801E38775286491991C3FCE064ED_AdjustorThunk },
	{ 0x0600023B, RewindableAllocator_get_Handle_mF81EDA2102485C46965AAB56347E8F64FE551D9E_AdjustorThunk },
	{ 0x0600023C, RewindableAllocator_set_Handle_mE59F440E3F692073FEF47367713603419814928D_AdjustorThunk },
	{ 0x0600023E, MemoryBlock__ctor_m0DEC878B6827C0B400BE1C00CA37C2F8F1C31D7F_AdjustorThunk },
	{ 0x0600023F, MemoryBlock_Rewind_m64C7EC463789D78802B8D27695AFFD37133E7069_AdjustorThunk },
	{ 0x06000240, MemoryBlock_Dispose_mE0EAA3828B238B9A26EF6BB5CA7CB5EC592FBD6F_AdjustorThunk },
	{ 0x06000241, MemoryBlock_TryAllocate_m34CC70E419486D5B1C90E7BF4A6762BE3D130F10_AdjustorThunk },
	{ 0x06000242, MemoryBlock_Contains_m2F035A4F1F9063B42ACB1B590C4EFD1741E39CEC_AdjustorThunk },
	{ 0x06000258, Rune_LengthInUtf8Bytes_m2E470564E773DB43A761FC2A5DA17F0885E81489_AdjustorThunk },
	{ 0x0600025D, Comparison__ctor_m40D70075E3FE042352BE5C6589C23C5D7D41668C_AdjustorThunk },
	{ 0x06000284, UnsafeDisposeJob_Execute_m6D0C969CE5C1124239FB9B69EC2A1E486E4F38BC_AdjustorThunk },
	{ 0x0600029C, UnsafeParallelHashMapDataDispose_Dispose_mCFF4782249AE264F799027E84E4CC704737066A3_AdjustorThunk },
	{ 0x0600029D, UnsafeParallelHashMapDataDisposeJob_Execute_m129F30954F3CF5922C620E4DDB1E50E540A06DCC_AdjustorThunk },
	{ 0x0600029E, UnsafeParallelHashMapDisposeJob_Execute_mCEF89FC4B3D6D8DC7E213FF274053A41805215E1_AdjustorThunk },
	{ 0x0600029F, UnsafeStream_AllocateForEach_m1EE9C9B5CF33ED03FA2C01A5DF2C5D99BAABC065_AdjustorThunk },
	{ 0x060002A0, UnsafeStream_Deallocate_mF13D45026C45A52DD441B1F021CC4BE9454D466C_AdjustorThunk },
	{ 0x060002A1, UnsafeStream_Dispose_m4D493F40C6ED4C7346017C05FBC006B5632AE49C_AdjustorThunk },
	{ 0x060002A2, DisposeJob_Execute_m089DE6120E788ECA6418DFAB36FFC3F4E90FC10E_AdjustorThunk },
	{ 0x060002A3, ConstructJobList_Execute_mDECEE80A756D3E19EF0FC58ADB0C4665626F7DAB_AdjustorThunk },
	{ 0x060002A4, ConstructJob_Execute_mAA49F134BF92ED7BB0356BC460A30C9B39A87C50_AdjustorThunk },
	{ 0x060002A6, UnsafeText__ctor_m4F0450604AA478F4C19A2CD4746878981C7DA31F_AdjustorThunk },
	{ 0x060002A7, UnsafeText_get_IsCreated_mEE504C37FEADA4DFD3A6AFB47751B20A1423BBF1_AdjustorThunk },
	{ 0x060002A8, UnsafeText_Dispose_m715EBE3FAD0BA5111276CFCB7F65419701F3BABC_AdjustorThunk },
	{ 0x060002A9, UnsafeText_ElementAt_mE1021122866981C2F08E17BA1320C2A294886844_AdjustorThunk },
	{ 0x060002AA, UnsafeText_GetUnsafePtr_m1C1462A867C4714017B3A5974F1FAF3716FB8F8C_AdjustorThunk },
	{ 0x060002AB, UnsafeText_TryResize_m4D31F691405B691F1CB3DABF9ED09D360FB13BDC_AdjustorThunk },
	{ 0x060002AC, UnsafeText_get_Capacity_m70C15A6A1753D3D824DEE599902E3E7FFC75F815_AdjustorThunk },
	{ 0x060002AD, UnsafeText_get_Length_m7D4616C7F2AAA07EC0E98FB430C6D7DA2D4D5E40_AdjustorThunk },
	{ 0x060002AE, UnsafeText_set_Length_m98076AB8E3E55B44FD260347BD22D944DCAE4DD9_AdjustorThunk },
	{ 0x060002AF, UnsafeText_ToString_m02C6D467CDF758100B8CCC3199168304B1FCE2B2_AdjustorThunk },
};
static const int32_t s_InvokerIndices[692] = 
{
	13298,
	13298,
	18814,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	10682,
	13298,
	0,
	0,
	0,
	0,
	0,
	0,
	18877,
	0,
	20831,
	21225,
	20193,
	18502,
	20224,
	20193,
	20193,
	18938,
	0,
	0,
	0,
	0,
	21355,
	21355,
	21355,
	5684,
	4122,
	12792,
	12815,
	13298,
	10959,
	20921,
	12996,
	13052,
	8701,
	13326,
	10958,
	12996,
	13298,
	13298,
	12997,
	12997,
	12996,
	10629,
	13298,
	12996,
	0,
	0,
	0,
	0,
	13326,
	10958,
	8701,
	17831,
	13052,
	13298,
	17831,
	5684,
	4122,
	20831,
	21265,
	21355,
	21355,
	21355,
	17831,
	13326,
	10958,
	12996,
	8701,
	17831,
	13052,
	13298,
	17831,
	5684,
	4122,
	20831,
	21265,
	21355,
	21355,
	21355,
	17831,
	0,
	0,
	0,
	21355,
	21355,
	18531,
	20840,
	21355,
	0,
	0,
	0,
	13298,
	10682,
	13298,
	13298,
	20207,
	18222,
	19911,
	20207,
	12996,
	10629,
	6810,
	16842,
	14902,
	14902,
	19885,
	17881,
	16167,
	16161,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	12792,
	12996,
	10629,
	12996,
	3433,
	6810,
	13357,
	8845,
	7736,
	10682,
	8845,
	8767,
	17365,
	7642,
	8773,
	17365,
	7648,
	8765,
	17365,
	7640,
	8771,
	17365,
	7646,
	8769,
	17365,
	7644,
	13052,
	12996,
	7736,
	10537,
	12815,
	13298,
	13488,
	13052,
	12792,
	12996,
	10629,
	12996,
	3433,
	6810,
	13360,
	8845,
	7736,
	10682,
	8845,
	8767,
	17365,
	7642,
	8773,
	17365,
	7648,
	8765,
	17365,
	7640,
	8771,
	17365,
	7646,
	8769,
	17365,
	7644,
	13052,
	12996,
	7736,
	10543,
	12815,
	13298,
	13488,
	13052,
	12792,
	12996,
	10629,
	12996,
	3433,
	6810,
	13356,
	8845,
	7736,
	10682,
	8845,
	8767,
	17365,
	7642,
	8773,
	17365,
	7648,
	8765,
	17365,
	7640,
	8771,
	17365,
	7646,
	8769,
	17365,
	7644,
	13052,
	12996,
	7736,
	10535,
	12815,
	13298,
	13488,
	13052,
	12792,
	12996,
	10629,
	12996,
	3433,
	6810,
	13359,
	8845,
	7736,
	10682,
	8845,
	8767,
	17365,
	7642,
	8773,
	17365,
	7648,
	8765,
	17365,
	7640,
	8771,
	17365,
	7646,
	8769,
	17365,
	7644,
	20079,
	13052,
	12996,
	7736,
	10541,
	12815,
	13298,
	13488,
	13052,
	12792,
	12996,
	10629,
	12996,
	3433,
	6810,
	13358,
	8845,
	7736,
	10682,
	8845,
	8767,
	17365,
	7642,
	8773,
	17365,
	7648,
	8765,
	17365,
	7640,
	8771,
	17365,
	7646,
	8769,
	17365,
	7644,
	13052,
	12996,
	7736,
	10539,
	12815,
	13298,
	13488,
	13052,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	15958,
	18519,
	0,
	19911,
	13845,
	13845,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	13298,
	13298,
	10415,
	16719,
	13298,
	13298,
	13298,
	13298,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	13298,
	10629,
	13298,
	13298,
	2677,
	5266,
	5470,
	12996,
	10629,
	12996,
	3433,
	12792,
	6810,
	13298,
	8840,
	7730,
	8001,
	13298,
	8845,
	7736,
	8767,
	17365,
	7642,
	8773,
	17365,
	7648,
	8765,
	17365,
	7640,
	8771,
	17365,
	7646,
	8769,
	17365,
	7644,
	13052,
	12996,
	7736,
	12996,
	12996,
	10629,
	6810,
	12792,
	3433,
	8845,
	7736,
	8001,
	8840,
	7730,
	8767,
	17365,
	7642,
	8773,
	17365,
	7648,
	8765,
	17365,
	7640,
	8771,
	17365,
	7646,
	8769,
	17365,
	7644,
	13052,
	12996,
	7736,
	13298,
	13298,
	0,
	0,
	0,
	5184,
	13298,
	13298,
	13052,
	8701,
	17831,
	13326,
	10958,
	17831,
	10630,
	13298,
	13298,
	8701,
	7689,
	5684,
	4122,
	20831,
	21265,
	21355,
	21355,
	21355,
	17831,
	19885,
	19880,
	21372,
	21372,
	14994,
	19904,
	19904,
	14994,
	14995,
	14995,
	14146,
	14146,
	20932,
	12996,
	14145,
	14997,
	14886,
	14997,
	2027,
	13974,
	18502,
	13973,
	15540,
	15525,
	14676,
	18932,
	20766,
	18514,
	18245,
	18246,
	18247,
	20780,
	16540,
	16538,
	13974,
	13973,
	14658,
	18502,
	15525,
	14676,
	5684,
	1708,
	20831,
	21265,
	21355,
	21355,
	21355,
	15525,
	5684,
	804,
	20831,
	21265,
	21355,
	21355,
	21355,
	14676,
	0,
	13298,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	18519,
	13298,
	13298,
	13298,
	10629,
	13298,
	13298,
	13298,
	13298,
	13298,
	19843,
	5470,
	12815,
	13298,
	6810,
	12792,
	3433,
	12996,
	12996,
	10629,
	13052,
	0,
	0,
	21355,
	21355,
	21355,
};
static const Il2CppTokenIndexMethodTuple s_reversePInvokeIndices[6] = 
{
	{ 0x06000059, 18,  (void**)&StackAllocator_Try_m093FA501B1B427E32DD9F654380B3EA56A5A4234_RuntimeMethod_var, 0 },
	{ 0x0600005C, 19,  (void**)&StackAllocator_TryU24BurstManaged_mB88D607AA12E4D9181BF1FFE81A1AC3117FDB5E2_RuntimeMethod_var, 0 },
	{ 0x06000069, 16,  (void**)&SlabAllocator_Try_mCD7DED588811A6E3F78E4A14CBFE2852D8E39DEB_RuntimeMethod_var, 0 },
	{ 0x0600006C, 17,  (void**)&SlabAllocator_TryU24BurstManaged_mC48F05E806431B6537727E4D6A10550207FBB1EA_RuntimeMethod_var, 0 },
	{ 0x0600023A, 9,  (void**)&RewindableAllocator_Try_mA4AF5A5088097CB6343C3CC97058959976372C35_RuntimeMethod_var, 0 },
	{ 0x0600023D, 10,  (void**)&RewindableAllocator_TryU24BurstManaged_mBB6DAE6A8CDB2E3626C38F3B65186AAF6ACBF6E8_RuntimeMethod_var, 0 },
};
static const Il2CppTokenRangePair s_rgctxIndices[67] = 
{
	{ 0x02000007, { 0, 10 } },
	{ 0x0200000B, { 10, 10 } },
	{ 0x0200000F, { 20, 10 } },
	{ 0x02000013, { 30, 10 } },
	{ 0x02000017, { 40, 14 } },
	{ 0x0200002C, { 95, 6 } },
	{ 0x02000031, { 101, 7 } },
	{ 0x0200003C, { 119, 34 } },
	{ 0x0200003E, { 153, 34 } },
	{ 0x02000040, { 187, 34 } },
	{ 0x02000042, { 221, 34 } },
	{ 0x02000044, { 255, 36 } },
	{ 0x0200005D, { 357, 29 } },
	{ 0x0200005E, { 390, 6 } },
	{ 0x0200006A, { 459, 3 } },
	{ 0x02000071, { 462, 6 } },
	{ 0x02000085, { 472, 18 } },
	{ 0x06000026, { 54, 4 } },
	{ 0x06000027, { 58, 2 } },
	{ 0x06000028, { 60, 5 } },
	{ 0x06000029, { 65, 3 } },
	{ 0x0600002A, { 68, 2 } },
	{ 0x0600002B, { 70, 5 } },
	{ 0x0600002D, { 75, 2 } },
	{ 0x06000036, { 77, 6 } },
	{ 0x06000037, { 83, 3 } },
	{ 0x06000038, { 86, 6 } },
	{ 0x06000039, { 92, 3 } },
	{ 0x06000092, { 108, 3 } },
	{ 0x06000093, { 111, 3 } },
	{ 0x06000094, { 114, 1 } },
	{ 0x06000095, { 115, 2 } },
	{ 0x06000096, { 117, 2 } },
	{ 0x060001A8, { 291, 5 } },
	{ 0x060001A9, { 296, 2 } },
	{ 0x060001AA, { 298, 7 } },
	{ 0x060001AB, { 305, 5 } },
	{ 0x060001AC, { 310, 5 } },
	{ 0x060001AD, { 315, 4 } },
	{ 0x060001AE, { 319, 7 } },
	{ 0x060001AF, { 326, 5 } },
	{ 0x060001B0, { 331, 7 } },
	{ 0x060001B1, { 338, 4 } },
	{ 0x060001B2, { 342, 4 } },
	{ 0x060001B3, { 346, 4 } },
	{ 0x060001B8, { 350, 2 } },
	{ 0x060001BC, { 352, 3 } },
	{ 0x060001BD, { 355, 2 } },
	{ 0x060001C3, { 386, 4 } },
	{ 0x060001E0, { 396, 5 } },
	{ 0x060001E1, { 401, 6 } },
	{ 0x060001E2, { 407, 6 } },
	{ 0x060001E3, { 413, 7 } },
	{ 0x060001E4, { 420, 2 } },
	{ 0x060001E5, { 422, 6 } },
	{ 0x060001E6, { 428, 6 } },
	{ 0x060001E7, { 434, 7 } },
	{ 0x060001E8, { 441, 3 } },
	{ 0x060001E9, { 444, 6 } },
	{ 0x060001EA, { 450, 3 } },
	{ 0x060001EB, { 453, 6 } },
	{ 0x06000283, { 468, 4 } },
	{ 0x0600028E, { 490, 5 } },
	{ 0x06000294, { 495, 3 } },
	{ 0x06000295, { 498, 2 } },
	{ 0x060002B0, { 500, 2 } },
	{ 0x060002B1, { 502, 2 } },
};
extern const uint32_t g_rgctx_JobBurstSchedulableProducer_1_t807BFFB05BF467E224E1D425614BBCA588C818B3;
extern const uint32_t g_rgctx_JobBurstSchedulableProducer_1_t807BFFB05BF467E224E1D425614BBCA588C818B3;
extern const uint32_t g_rgctx_T_tB77BC8EFEA441BFECEA21B20FB0C7ABD6C25411E;
extern const uint32_t g_rgctx_JobBurstSchedulableProducer_1_Execute_m879EA138218416A56B7DB8C9C265F54900D1F55B;
extern const uint32_t g_rgctx_ExecuteJobFunction_tDA46B463559182B8F35EAC17AE60229B15053091;
extern const uint32_t g_rgctx_ExecuteJobFunction__ctor_m4058A5D6E5DEAADD18E9C7905AF10EFA2DE99D2D;
extern const uint32_t g_rgctx_TU26_t3B82C136947F56CFEAB4E457649FA82404C21226;
extern const uint32_t g_rgctx_T_tB77BC8EFEA441BFECEA21B20FB0C7ABD6C25411E;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_tB77BC8EFEA441BFECEA21B20FB0C7ABD6C25411E_IJobBurstSchedulable_Execute_m046908BA556F2FE6BB6DC56E1DCDF638230CC017;
extern const uint32_t g_rgctx_SharedStatic_1_GetOrCreate_TisJobBurstSchedulableProducer_1_t807BFFB05BF467E224E1D425614BBCA588C818B3_mE6DAE2C1EA987EB6CAB761950CDEAC9BD79171AA;
extern const uint32_t g_rgctx_JobParallelForBatchProducer_1_t227C3ED15302FCF56720933CC9ED01F1807727CB;
extern const uint32_t g_rgctx_JobParallelForBatchProducer_1_t227C3ED15302FCF56720933CC9ED01F1807727CB;
extern const uint32_t g_rgctx_T_tB969F05B0CD912E344A856A15AC4FF5A2A2EB0F9;
extern const uint32_t g_rgctx_JobParallelForBatchProducer_1_Execute_m817E0EF3E204CC26A45EC7516A93EB330D5D148B;
extern const uint32_t g_rgctx_ExecuteJobFunction_tD9EA28CE05E92454FAC0C7100EC4C5420CD26EFE;
extern const uint32_t g_rgctx_ExecuteJobFunction__ctor_mF717EEE303E3EFD813FD7CB5A5FBF370C11FEA92;
extern const uint32_t g_rgctx_TU26_tE2A631B8DDEA8ED98F28DF3B218A02DAD9DF5721;
extern const uint32_t g_rgctx_T_tB969F05B0CD912E344A856A15AC4FF5A2A2EB0F9;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_tB969F05B0CD912E344A856A15AC4FF5A2A2EB0F9_IJobParallelForBatch_Execute_m3D4D3AA382FCE785E70AE39C51977CDEE30A4AAD;
extern const uint32_t g_rgctx_SharedStatic_1_GetOrCreate_TisJobParallelForBatchProducer_1_t227C3ED15302FCF56720933CC9ED01F1807727CB_m77724EB2258BEEC7745B4C05BFAD7B8586DFE951;
extern const uint32_t g_rgctx_JobParallelForBurstSchedulableProducer_1_t5D4B903F8F053EAECBCEA0C62A94902B23317F2A;
extern const uint32_t g_rgctx_JobParallelForBurstSchedulableProducer_1_t5D4B903F8F053EAECBCEA0C62A94902B23317F2A;
extern const uint32_t g_rgctx_T_tF17783746DBA93934E99516BD05AC18D2442D7D8;
extern const uint32_t g_rgctx_JobParallelForBurstSchedulableProducer_1_Execute_m623F0AE11AFC90F9B4DA37310451A82D77E2C222;
extern const uint32_t g_rgctx_ExecuteJobFunction_t941ECECD0F514B0CE26D421342B2C4D6B66AE596;
extern const uint32_t g_rgctx_ExecuteJobFunction__ctor_m368E653FE4364EBC693F02BD4F3AC6F38C0FA853;
extern const uint32_t g_rgctx_TU26_t504BFF71026C247F4AC9CF960F16C0D2D14ED9E0;
extern const uint32_t g_rgctx_T_tF17783746DBA93934E99516BD05AC18D2442D7D8;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_tF17783746DBA93934E99516BD05AC18D2442D7D8_IJobParallelForBurstSchedulable_Execute_mAF8CD68C26B4342DB0BED65AD2243EF9DC49B5BA;
extern const uint32_t g_rgctx_SharedStatic_1_GetOrCreate_TisJobParallelForBurstSchedulableProducer_1_t5D4B903F8F053EAECBCEA0C62A94902B23317F2A_m6E5873E257EE8175DA199F939CB8E7E83AD71630;
extern const uint32_t g_rgctx_JobParallelForDeferProducer_1_t8FC16D1D487523A5ED99C85A5C284F108F90CA11;
extern const uint32_t g_rgctx_JobParallelForDeferProducer_1_t8FC16D1D487523A5ED99C85A5C284F108F90CA11;
extern const uint32_t g_rgctx_T_t0E9646E87C2BEA97DF729E8ED1A8EC11C1D86830;
extern const uint32_t g_rgctx_JobParallelForDeferProducer_1_Execute_m40F2898B9BBC65421DAA4C8EFF5B1167B73E3E88;
extern const uint32_t g_rgctx_ExecuteJobFunction_tA4B289FCD385EE466E2B78AF7914A1AD8C091CD1;
extern const uint32_t g_rgctx_ExecuteJobFunction__ctor_m30C02A17ED242F0A734C250D99A27A06969B92A7;
extern const uint32_t g_rgctx_TU26_t9388D6AF5E64869FEBD3E5F64B638D362A6AA938;
extern const uint32_t g_rgctx_T_t0E9646E87C2BEA97DF729E8ED1A8EC11C1D86830;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_t0E9646E87C2BEA97DF729E8ED1A8EC11C1D86830_IJobParallelForDefer_Execute_mBDEC705B03843374F7054ADE9F5C17E6D9E1F548;
extern const uint32_t g_rgctx_SharedStatic_1_GetOrCreate_TisJobParallelForDeferProducer_1_t8FC16D1D487523A5ED99C85A5C284F108F90CA11_m76C60C3BC9C7050B21BB07B57814F04CCCF68C3C;
extern const uint32_t g_rgctx_JobParallelForFilterProducer_1_tC0AA381987A1C3A449C603D0458DFB4F6CD82348;
extern const uint32_t g_rgctx_JobParallelForFilterProducer_1_tC0AA381987A1C3A449C603D0458DFB4F6CD82348;
extern const uint32_t g_rgctx_JobWrapper_tC8BF1AA504B165DC28D4AB1AE44B7DF4897727C2;
extern const uint32_t g_rgctx_T_t9AEF47AD0E7A2ED86185872C27E145723CB2BAAB;
extern const uint32_t g_rgctx_JobParallelForFilterProducer_1_Execute_m6F6CF78A4A9CE56E6EE18C2DD3BD667C03B655D9;
extern const uint32_t g_rgctx_ExecuteJobFunction_tF85C2AA1A933A69C8BAE8EAF0A32A4B8EDCE481D;
extern const uint32_t g_rgctx_ExecuteJobFunction__ctor_mE284472F7437B7178B3F2D367384D3486478C8E2;
extern const uint32_t g_rgctx_JobWrapperU26_tB111F1AA1C3DA771E0A0F1E147C4F1FF0B247551;
extern const uint32_t g_rgctx_JobWrapper_tC8BF1AA504B165DC28D4AB1AE44B7DF4897727C2;
extern const uint32_t g_rgctx_JobParallelForFilterProducer_1_ExecuteFilter_mA059543644262A3A1A79CE40107632F928ED163F;
extern const uint32_t g_rgctx_JobParallelForFilterProducer_1_ExecuteAppend_m95CDA3628147D8ECBF9E92CC30DFCEF27E47917F;
extern const uint32_t g_rgctx_T_t9AEF47AD0E7A2ED86185872C27E145723CB2BAAB;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_t9AEF47AD0E7A2ED86185872C27E145723CB2BAAB_IJobParallelForFilter_Execute_m6C099604B0837398A4EE9270286183229B2DC4C8;
extern const uint32_t g_rgctx_SharedStatic_1_GetOrCreate_TisJobParallelForFilterProducer_1_tC0AA381987A1C3A449C603D0458DFB4F6CD82348_m8681B03CCB56B3965FA409845AF69B4372CBB27A;
extern const uint32_t g_rgctx_TU26_t5335F97D02534CC2D1EFAD2B1B8CE794A459864F;
extern const uint32_t g_rgctx_T_t2BD6686FA9DF7A772BB80D160D1EFDB90CDC262F;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_t2BD6686FA9DF7A772BB80D160D1EFDB90CDC262F_IAllocator_get_Handle_m54F384A4E6B7365849D3462AA309C77F8D9E9E6C;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_t2BD6686FA9DF7A772BB80D160D1EFDB90CDC262F_IAllocator_Try_mDD961D5AECA15763EDDE7F745C825C7CD5D7FAB2;
extern const uint32_t g_rgctx_TU26_t5CEA5E60B9EC484E5F0A5233B0FE6573F8FD4204;
extern const uint32_t g_rgctx_AllocatorManager_AllocateBlock_TisT_t4B6526BAD6B8C750196E96687551E6FB248D8B93_m20F711BEC9AE42C4FA04E61E37EDAE5F8E13C2A2;
extern const uint32_t g_rgctx_TU26_tF4435F8B669166A749CCFA3D5E9E46AAD064E2CE;
extern const uint32_t g_rgctx_UnsafeUtility_SizeOf_TisU_tA9035EFB2BDFB918891BB54C12A40A61E813D1AB_m147F0BD45B75434C545EA473E1C858F26B10C11C;
extern const uint32_t g_rgctx_UnsafeUtility_AlignOf_TisU_tA9035EFB2BDFB918891BB54C12A40A61E813D1AB_m21F060306B883FAA6844D2D92424AD0C418E7FB8;
extern const uint32_t g_rgctx_AllocatorManager_Allocate_TisT_t53C98BE980141A98061F7E0C00F1E53863D49172_mCE845A9B9E6485B0D134D2578EAAB89D9FB73D29;
extern const uint32_t g_rgctx_UU2A_t486C2DB331AC02939CDB4420494EF6926230852B;
extern const uint32_t g_rgctx_TU26_t3F3F4F77ACB3DE60F9CEA6D14439ACADFACF96A9;
extern const uint32_t g_rgctx_T_t675DECA271C35699FBAD03C9D9F04E758C9964B7;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_t675DECA271C35699FBAD03C9D9F04E758C9964B7_IAllocator_Try_mDD961D5AECA15763EDDE7F745C825C7CD5D7FAB2;
extern const uint32_t g_rgctx_TU26_t7F0CFCEFF9DD94FC01C07F1F29EF14CBE94EFA6C;
extern const uint32_t g_rgctx_AllocatorManager_FreeBlock_TisT_t35BD07ABBCB8D61BAD11D72A4D6D6D997BD815DA_mCFAB24856A7C4B600583475999609C1BDE147617;
extern const uint32_t g_rgctx_TU26_t512535147A2E70989C9FED965A59897CB227A3CB;
extern const uint32_t g_rgctx_UU2A_tFEE847BDBB617FAF279654649190AF314B52F7B1;
extern const uint32_t g_rgctx_UnsafeUtility_SizeOf_TisU_tE686974E7BAF50725A50289F9FE1E4DAEB20FEB9_m08C7637594479E2DE074EBCB3AB56DE38E47F0EA;
extern const uint32_t g_rgctx_UnsafeUtility_AlignOf_TisU_tE686974E7BAF50725A50289F9FE1E4DAEB20FEB9_m54C6FF17733951B3182314D7A7392CAF02AE8CBE;
extern const uint32_t g_rgctx_AllocatorManager_Free_TisT_tB5F0204FCE510FB4611F370EFC46DA8C45DC09AF_m866B5AC4270563CCF787270E884E9ADB696947CF;
extern const uint32_t g_rgctx_TU2A_t329EAE82F86B22F9B6C69972AF45D5E40392CCEA;
extern const uint32_t g_rgctx_AllocatorManager_Free_TisAllocatorHandle_t3CA09720B1F89F91A8DDBA95E74C28A1EC3E3148_TisT_t570532536E3FD3B2205FD25800E5A7DAFDA40675_mC0583857F21D37F314ADCD109E5E9DD8244E4792;
extern const uint32_t g_rgctx_TU26_tA137A5623086388A54F6037F4EDB41EE5DDD2361;
extern const uint32_t g_rgctx_T_t13CE2F434A3C4FCBF90D09B6D327955FFBC3EB93;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_t13CE2F434A3C4FCBF90D09B6D327955FFBC3EB93_IAllocator_get_Function_m08C0302F73788529C636BB23E001A5C4AB12D855;
extern const uint32_t g_rgctx_UnsafeUtility_AddressOf_TisT_t13CE2F434A3C4FCBF90D09B6D327955FFBC3EB93_m50ECE878124DA46DE8E44114F3BC466694A10956;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_t13CE2F434A3C4FCBF90D09B6D327955FFBC3EB93_IAllocator_set_Handle_m368F9B6297C44D2DE14CA5C0E91DEB802EA17FA4;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_t13CE2F434A3C4FCBF90D09B6D327955FFBC3EB93_IAllocator_get_Handle_m54F384A4E6B7365849D3462AA309C77F8D9E9E6C;
extern const uint32_t g_rgctx_TU26_t1B60EBC063E5737EFCF0E54CFB7DE9BD575C2310;
extern const uint32_t g_rgctx_T_tC5904493B68D4604F7E9591EADDC6FC6BAA9B920;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_tC5904493B68D4604F7E9591EADDC6FC6BAA9B920_IAllocator_get_Handle_m54F384A4E6B7365849D3462AA309C77F8D9E9E6C;
extern const uint32_t g_rgctx_UnsafeUtility_SizeOf_TisT_t65A6261CB8536FA6F17465CEC570EE891D3C5EAE_mE7D528BE5D69AF877DA5B94D0F1CCA8B479DDF1C;
extern const uint32_t g_rgctx_T_t65A6261CB8536FA6F17465CEC570EE891D3C5EAE;
extern const uint32_t g_rgctx_UnsafeUtility_AsRef_TisT_t65A6261CB8536FA6F17465CEC570EE891D3C5EAE_m4F18CE29A94D3F0464849874E1B54EEEF2DD5FFF;
extern const uint32_t g_rgctx_TU26_t2BC6DED49F402D06CBCB0B68842F491E5001EFA4;
extern const uint32_t g_rgctx_AllocatorManager_Register_TisT_t65A6261CB8536FA6F17465CEC570EE891D3C5EAE_m0D8A28412B9296CC4C08ECFFB82EC1AEB9033196;
extern const uint32_t g_rgctx_TU2A_tC7D7997E9D0BA6074EFA93C6146B1D22BA65061F;
extern const uint32_t g_rgctx_TU26_t6A57FD6864FADEB6D69862F7988E43A7537EBB21;
extern const uint32_t g_rgctx_AllocatorManager_Unregister_TisT_t85CFC1E553C1D15A35CD39A7416656AC773670D4_mE11A8460609FC9E5965B5A515E87E9E5742F272A;
extern const uint32_t g_rgctx_UnsafeUtility_AddressOf_TisT_t85CFC1E553C1D15A35CD39A7416656AC773670D4_m986B3DC8C3E085ED3B9650886F25C4A8F2F4DD5E;
extern const uint32_t g_rgctx_Array32768_1_t27311415036D8E10790953E71D1B37720B017554;
extern const uint32_t g_rgctx_Array4096_1_t44B5406E19508C8BD82A7FB15B4FAB261D6376F7;
extern const uint32_t g_rgctx_T_t2B0E445D8A10145BC0D27189CC7F23CC9EF95B34;
extern const uint32_t g_rgctx_UnsafeUtility_AsRef_TisT_t2B0E445D8A10145BC0D27189CC7F23CC9EF95B34_m224DE97901461C7EFDC5FE9F0AC057A7815ACC92;
extern const uint32_t g_rgctx_TU26_t17FDE0F88AA456BDCCA436101E8DF16EEE82EC17;
extern const uint32_t g_rgctx_Array4096_1U26_t538F10C46BA0E53BCE3887D97DD2B3549A3250DC;
extern const uint32_t g_rgctx_AllocatorHelper_1_tE5DDB49FAA0264D8AB1F8057B3CEDDB64106AEC3;
extern const uint32_t g_rgctx_TU2A_t85D946E3A77B2225D4E097025A50E004270E532B;
extern const uint32_t g_rgctx_UnsafeUtility_AsRef_TisT_t64A38344CCB5CDCBADEFC830C652C146C6A3E9ED_m0C04840C181A4834CB822F7CDD4DF405273C8954;
extern const uint32_t g_rgctx_TU26_tE3B78B500D5929085F9003DC18DB9FF8E0EF9A4A;
extern const uint32_t g_rgctx_AllocatorManager_CreateAllocator_TisT_t64A38344CCB5CDCBADEFC830C652C146C6A3E9ED_m5D663E10F521B580FCC083A9A401C62A04C1EFBE;
extern const uint32_t g_rgctx_UnsafeUtility_AddressOf_TisT_t64A38344CCB5CDCBADEFC830C652C146C6A3E9ED_m6FD6C369475C6B7FA07BA86E21D7D9403CA8ED85;
extern const uint32_t g_rgctx_AllocatorManager_DestroyAllocator_TisT_t64A38344CCB5CDCBADEFC830C652C146C6A3E9ED_mC315ED9EE74F31D3822486E65968D1309AFAC6B6;
extern const uint32_t g_rgctx_TU26_tD30ADC83B93D29FA4F7D798411082A266C2DA4B7;
extern const uint32_t g_rgctx_T_t01EB2D6CAA4115BC3BE45DD7F4B9EB8161A098EC;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_t01EB2D6CAA4115BC3BE45DD7F4B9EB8161A098EC_IIndexable_1_ElementAt_m1CBBA2EFCBBFAC5FB938033CB43FA44E7560A939;
extern const uint32_t g_rgctx_TU26_tFC80F75B523AF31493BF95411C8EE31987E3A9C2;
extern const uint32_t g_rgctx_T_tCB4A169961AE49E9EC3BD91E800D851654510C55;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_tCB4A169961AE49E9EC3BD91E800D851654510C55_IIndexable_1_ElementAt_m1CBBA2EFCBBFAC5FB938033CB43FA44E7560A939;
extern const uint32_t g_rgctx_UnsafeUtility_SizeOf_TisT_tA5912E8646DA88E85E66D08CB0327BAA5E6B0184_mA7BC1C40589A53585C6F00F43E6B2FF9C40DBE3B;
extern const uint32_t g_rgctx_UnsafeUtility_SizeOf_TisBUFFER_tFD40F1306B645EA97CA83B52D2CB6CD3FEEAC54D_mF155F456885915E1F3C70F273A8C897F07AA7A9F;
extern const uint32_t g_rgctx_FixedList_PaddingBytes_TisT_t2D6B0CCB470769484641DD2BFB5115894D099F35_m7EF6A496BA6072238092F45DB16A779E764F5489;
extern const uint32_t g_rgctx_FixedList_StorageBytes_TisBUFFER_t73A4AA1DE43400A5108AF898EA2ACE7B858658C5_TisT_t383CE4675CD7273C5AE8AE0CE56DD3BD03C553EF_m916CAB4FC681EE3D68EF2DB793A2E9491D2231D4;
extern const uint32_t g_rgctx_UnsafeUtility_SizeOf_TisT_t383CE4675CD7273C5AE8AE0CE56DD3BD03C553EF_mB419A365B9AA116F362278FF24992D7C8D54CEC7;
extern const uint32_t g_rgctx_FixedList32Bytes_1_tD2F19E017C9CF5DC6CB5780CDA882089FFE4B99C;
extern const uint32_t g_rgctx_FixedList32Bytes_1_get_Length_m09C6C267D9BAF792F6EE22EE3EBFFA6DF40A1AF0;
extern const uint32_t g_rgctx_FixedList32Bytes_1_tD2F19E017C9CF5DC6CB5780CDA882089FFE4B99C;
extern const uint32_t g_rgctx_UnsafeUtility_SizeOf_TisT_t604DA2B2C61AC824178F38507FE80321D55C1110_m1CA32CEFE1BB9A05D042A6414E2C873CFDDB24FA;
extern const uint32_t g_rgctx_FixedList_PaddingBytes_TisT_t604DA2B2C61AC824178F38507FE80321D55C1110_mF9274D4AE2A751E849E75F7FF15624553335C9E3;
extern const uint32_t g_rgctx_FixedList_Capacity_TisFixedBytes30_t7721F11929A3AC08287DF5E6D7AEF85CCEE04AD2_TisT_t604DA2B2C61AC824178F38507FE80321D55C1110_m86A556E7807973A99C024DC03F74323C88AAE239;
extern const uint32_t g_rgctx_FixedList32Bytes_1_get_Buffer_m8C59DC6D188FDA6852C8E1DCD65467F24AB6C6FF;
extern const uint32_t g_rgctx_UnsafeUtility_ArrayElementAsRef_TisT_t604DA2B2C61AC824178F38507FE80321D55C1110_m29A459479200E2961AA8D4F48BD7787ED3F84E15;
extern const uint32_t g_rgctx_TU26_t42543BA455A05A5D15090F8CFE8568C7F4A572B1;
extern const uint32_t g_rgctx_FixedList32Bytes_1_get_LengthInBytes_mF4D1CE0CFE876F4AEA6727E2F9DC3E9ED4C6F49F;
extern const uint32_t g_rgctx_T_t604DA2B2C61AC824178F38507FE80321D55C1110;
extern const uint32_t g_rgctx_FixedList32Bytes_1_CompareTo_mBBBC65A417BA1B4D27BD77E11B44DE17E4AE14EF;
extern const uint32_t g_rgctx_FixedList64Bytes_1_tC4ED4AB3E4EEE98679B1412FB4C80C3E4FA16834;
extern const uint32_t g_rgctx_FixedList64Bytes_1_get_Length_m927C78B4D944D4E876AB25A3E7AB4F6DFAD4F108;
extern const uint32_t g_rgctx_FixedList64Bytes_1_tC4ED4AB3E4EEE98679B1412FB4C80C3E4FA16834;
extern const uint32_t g_rgctx_FixedList32Bytes_1_CompareTo_m6290A920F0B1C958BBD7B60BA6F94FD3164B806A;
extern const uint32_t g_rgctx_FixedList128Bytes_1_t97E27D9873786614696ED6CB6B7DABF2AFF7DCFF;
extern const uint32_t g_rgctx_FixedList128Bytes_1_get_Length_m07F322CB13F58D937FC51D44F14EE31B19067359;
extern const uint32_t g_rgctx_FixedList128Bytes_1_t97E27D9873786614696ED6CB6B7DABF2AFF7DCFF;
extern const uint32_t g_rgctx_FixedList32Bytes_1_CompareTo_m6642F674EE22AC655E358EF533B0F700C16A0A94;
extern const uint32_t g_rgctx_FixedList512Bytes_1_tE0BA0381F29B58D8A0C87FD4F2A4E8DB45DFB48F;
extern const uint32_t g_rgctx_FixedList512Bytes_1_get_Length_m1CC3012C703D85C7C951AE77DD2B3F5B8E42E918;
extern const uint32_t g_rgctx_FixedList512Bytes_1_tE0BA0381F29B58D8A0C87FD4F2A4E8DB45DFB48F;
extern const uint32_t g_rgctx_FixedList32Bytes_1_CompareTo_mFAF2429BB4895D0C3AF7C5B981A3C53F440BE6A5;
extern const uint32_t g_rgctx_FixedList4096Bytes_1_t0629EA7E8B063F8817ECAE15F9A75E6F61CA42F0;
extern const uint32_t g_rgctx_FixedList4096Bytes_1_get_Length_m29F0A70AA1DAC8624B99E5E213DC4E34036BBF0C;
extern const uint32_t g_rgctx_FixedList4096Bytes_1_t0629EA7E8B063F8817ECAE15F9A75E6F61CA42F0;
extern const uint32_t g_rgctx_FixedList32Bytes_1_CompareTo_m21876ACD769677EB12796837397549EACDC6DA1F;
extern const uint32_t g_rgctx_FixedList32Bytes_1_Equals_mA9B2F154A856E8EB9B4983E2745FD077BA0D98C3;
extern const uint32_t g_rgctx_FixedList32Bytes_1_Equals_m496CF2C8A1FAFE63A285C2350D9530A60CC9B463;
extern const uint32_t g_rgctx_FixedList32Bytes_1_Equals_m3B2C3C3508065763A6DEE8185CD3507C80EE2159;
extern const uint32_t g_rgctx_FixedList32Bytes_1_Equals_m8812C7F42A79683AC17FBC09A7F04E5E909E3A67;
extern const uint32_t g_rgctx_FixedList32Bytes_1_Equals_m7701FD9664F730DE055F5A80657EBC6BF96BB399;
extern const uint32_t g_rgctx_IEnumerator_1_t864132BBC4D6B1C046B0F3D7F2450911AEEE1A9D;
extern const uint32_t g_rgctx_FixedList64Bytes_1_t01290055D2315B7FB797137C2585E1D2A8FEFC93;
extern const uint32_t g_rgctx_FixedList64Bytes_1_get_Length_m904CEC4D13DAB3EC2E63867290A4919B3EE07B94;
extern const uint32_t g_rgctx_FixedList64Bytes_1_t01290055D2315B7FB797137C2585E1D2A8FEFC93;
extern const uint32_t g_rgctx_UnsafeUtility_SizeOf_TisT_tD32C0F6209442C9BB65A030D29698147784945B7_m9AEE49A539D4229D6C082363CC4750BC3C7BD959;
extern const uint32_t g_rgctx_FixedList_PaddingBytes_TisT_tD32C0F6209442C9BB65A030D29698147784945B7_mBEB73D718598A132E3FB38993715F6580F14AAF4;
extern const uint32_t g_rgctx_FixedList_Capacity_TisFixedBytes62_t25CC23B7A3CF922DF0D1F0BFD5F801864D4FFD2A_TisT_tD32C0F6209442C9BB65A030D29698147784945B7_mBFE2EB1E8054BA05932A224E8E5B0B172C76A4E2;
extern const uint32_t g_rgctx_FixedList64Bytes_1_get_Buffer_m4ACFD76E5BAB7BBA3B105EF045FB34DF16925121;
extern const uint32_t g_rgctx_UnsafeUtility_ArrayElementAsRef_TisT_tD32C0F6209442C9BB65A030D29698147784945B7_m578879984B9A4BA26E7A659A8E3BC67397790111;
extern const uint32_t g_rgctx_TU26_t376B70AA61950DE5C43FFDF0193DB6362FC21607;
extern const uint32_t g_rgctx_FixedList64Bytes_1_get_LengthInBytes_m830026A47AC35E78ACFB4ED8613C4241631C7FB3;
extern const uint32_t g_rgctx_FixedList32Bytes_1_tF36F3420B2D85BB09FDA7CBE0B0A59127E7E47B5;
extern const uint32_t g_rgctx_FixedList32Bytes_1_get_Length_mDC309D74DBDE3857D8CF451A6C61E4DF244DB906;
extern const uint32_t g_rgctx_FixedList32Bytes_1_tF36F3420B2D85BB09FDA7CBE0B0A59127E7E47B5;
extern const uint32_t g_rgctx_T_tD32C0F6209442C9BB65A030D29698147784945B7;
extern const uint32_t g_rgctx_FixedList64Bytes_1_CompareTo_mFFA2BDEF1D5931103F2349F121D89B9EFDA18B2A;
extern const uint32_t g_rgctx_FixedList64Bytes_1_CompareTo_m1B404F21933A0C0E3F4EDB9BDC56FF1374BA36BB;
extern const uint32_t g_rgctx_FixedList128Bytes_1_t90FF12D54923E198013051CB940E9B9C7747AA0E;
extern const uint32_t g_rgctx_FixedList128Bytes_1_get_Length_m913F6E9C36DA6E5150201BB04705176C61779A4A;
extern const uint32_t g_rgctx_FixedList128Bytes_1_t90FF12D54923E198013051CB940E9B9C7747AA0E;
extern const uint32_t g_rgctx_FixedList64Bytes_1_CompareTo_mAE605CEAEF66638FB3772258684647B86795A026;
extern const uint32_t g_rgctx_FixedList512Bytes_1_t5A455036DE64718823C34266F43B4B930819650E;
extern const uint32_t g_rgctx_FixedList512Bytes_1_get_Length_mB4AC876C1C0A6D784950D1DF9D938E8187CC254A;
extern const uint32_t g_rgctx_FixedList512Bytes_1_t5A455036DE64718823C34266F43B4B930819650E;
extern const uint32_t g_rgctx_FixedList64Bytes_1_CompareTo_m709B483E5DEB7FB21FD73055E1D18EFC77CB8D94;
extern const uint32_t g_rgctx_FixedList4096Bytes_1_t140F024B8BA0DDF7C9A41DFDFE78C7BEECDE6355;
extern const uint32_t g_rgctx_FixedList4096Bytes_1_get_Length_mA84088FDB735E31D8EC68ED7C7241B82F7E382E9;
extern const uint32_t g_rgctx_FixedList4096Bytes_1_t140F024B8BA0DDF7C9A41DFDFE78C7BEECDE6355;
extern const uint32_t g_rgctx_FixedList64Bytes_1_CompareTo_m72AEE1D5D4DD2A52568A3DB0395D4C6A4450D662;
extern const uint32_t g_rgctx_FixedList64Bytes_1_Equals_mFA63385DF5A53E2D34A838F6B4D3E5EB93454707;
extern const uint32_t g_rgctx_FixedList64Bytes_1_Equals_m4A58B7E48EC3D1CE4B417CC606F7AF283179CE19;
extern const uint32_t g_rgctx_FixedList64Bytes_1_Equals_mD426D7F36DA3313161AF4ACB1D163554927B2B5C;
extern const uint32_t g_rgctx_FixedList64Bytes_1_Equals_mA3CB277239697E135B80B1652428B650E393E3EF;
extern const uint32_t g_rgctx_FixedList64Bytes_1_Equals_m676325C656968A3297E0312C5E86C06A021B6A7A;
extern const uint32_t g_rgctx_IEnumerator_1_tB69EBF70B6FD3D2A8F0F87587EDB9568DE58455A;
extern const uint32_t g_rgctx_FixedList128Bytes_1_tD5CBB33E81228C7A982A8BB36F4C59C6886B72D2;
extern const uint32_t g_rgctx_FixedList128Bytes_1_get_Length_m0455E51110778F6A133DB6106D4F22A64B989348;
extern const uint32_t g_rgctx_FixedList128Bytes_1_tD5CBB33E81228C7A982A8BB36F4C59C6886B72D2;
extern const uint32_t g_rgctx_UnsafeUtility_SizeOf_TisT_tF637D573FAC60604ECC747D4E3EAC341C5AAEF30_mDFFBC1AB4195A6724110DE5980F5E23E6FFBD712;
extern const uint32_t g_rgctx_FixedList_PaddingBytes_TisT_tF637D573FAC60604ECC747D4E3EAC341C5AAEF30_m69D18CFEBEB2907DF3FAC8CA19E77BE6A657316C;
extern const uint32_t g_rgctx_FixedList_Capacity_TisFixedBytes126_tC223222E11A3E93A15FE1C62C3429FC169DBC989_TisT_tF637D573FAC60604ECC747D4E3EAC341C5AAEF30_mCCBFF245343A7B36B9527CBA6D9741B77826F0AE;
extern const uint32_t g_rgctx_FixedList128Bytes_1_get_Buffer_m97D30707BB2AEA2F5DBADE3B0FAC8F672E8B1A3D;
extern const uint32_t g_rgctx_UnsafeUtility_ArrayElementAsRef_TisT_tF637D573FAC60604ECC747D4E3EAC341C5AAEF30_m3AC258853E94E1A3A82824C76E3015F3DFDA8ECC;
extern const uint32_t g_rgctx_TU26_t1F0419C99A901A588CC96A5B77396D184C4FD8AD;
extern const uint32_t g_rgctx_FixedList128Bytes_1_get_LengthInBytes_m47F607A647F86AE5CEE40BB1760159288C68D0BB;
extern const uint32_t g_rgctx_FixedList32Bytes_1_t3C413D65609876A5C74284892DEE5A0D625FB108;
extern const uint32_t g_rgctx_FixedList32Bytes_1_get_Length_mB2C086817BC1745080C475DEF2DC556F31A59D67;
extern const uint32_t g_rgctx_FixedList32Bytes_1_t3C413D65609876A5C74284892DEE5A0D625FB108;
extern const uint32_t g_rgctx_T_tF637D573FAC60604ECC747D4E3EAC341C5AAEF30;
extern const uint32_t g_rgctx_FixedList128Bytes_1_CompareTo_m2666E22B9261026A0D7D0DDBE9CAF263093BCA3B;
extern const uint32_t g_rgctx_FixedList64Bytes_1_t870D9CA977C5EB1DE5E7F03D3EBCDF741429CFD5;
extern const uint32_t g_rgctx_FixedList64Bytes_1_get_Length_m4D4D3FDEAC491E57BF639002CE416C8CAEEAF68B;
extern const uint32_t g_rgctx_FixedList64Bytes_1_t870D9CA977C5EB1DE5E7F03D3EBCDF741429CFD5;
extern const uint32_t g_rgctx_FixedList128Bytes_1_CompareTo_mE9957B37908F02DBF2C0FBC8A01F575132266E51;
extern const uint32_t g_rgctx_FixedList128Bytes_1_CompareTo_m4C2FEAF12A383067066580E5AE042BA1E5E15353;
extern const uint32_t g_rgctx_FixedList512Bytes_1_tC3D869C52DA776B0E1C9C381FB198A6197EACD87;
extern const uint32_t g_rgctx_FixedList512Bytes_1_get_Length_m660F2F29958AF880448E6418281CCCEB44F5B7D4;
extern const uint32_t g_rgctx_FixedList512Bytes_1_tC3D869C52DA776B0E1C9C381FB198A6197EACD87;
extern const uint32_t g_rgctx_FixedList128Bytes_1_CompareTo_m437197F791AB58DC99F09DD25DFF430B2F4CFA16;
extern const uint32_t g_rgctx_FixedList4096Bytes_1_t416541239A6A63A742AD7E9584232B8BAEC559DD;
extern const uint32_t g_rgctx_FixedList4096Bytes_1_get_Length_m9A5EEBCAC9EB81EA90B9BD7FFB53C8C16795D572;
extern const uint32_t g_rgctx_FixedList4096Bytes_1_t416541239A6A63A742AD7E9584232B8BAEC559DD;
extern const uint32_t g_rgctx_FixedList128Bytes_1_CompareTo_m255D7C47599B63B530DEF04B43CD7223D203C6DC;
extern const uint32_t g_rgctx_FixedList128Bytes_1_Equals_m086E1CCF2A8A8917B11F2B64CF7D29C6EE148120;
extern const uint32_t g_rgctx_FixedList128Bytes_1_Equals_mBF301D370C792F53697A10E96FFDC089205671AE;
extern const uint32_t g_rgctx_FixedList128Bytes_1_Equals_m2CDB0F15DB14C27621333422EF9796AD3D031766;
extern const uint32_t g_rgctx_FixedList128Bytes_1_Equals_mB57417246E4D25627832638BE91CB141FDF04EA9;
extern const uint32_t g_rgctx_FixedList128Bytes_1_Equals_m23DA07023310D345AD86B3B55A0D2AA231BD7788;
extern const uint32_t g_rgctx_IEnumerator_1_t37AA14CD4097DAA4BDEBD3F3465239E439BF2709;
extern const uint32_t g_rgctx_FixedList512Bytes_1_t669FFE3EF2D7972808F85F458A97B36D3B08A302;
extern const uint32_t g_rgctx_FixedList512Bytes_1_get_Length_m0B248C6A39E59A7CC633B8EBB0E2214A7374A24A;
extern const uint32_t g_rgctx_FixedList512Bytes_1_t669FFE3EF2D7972808F85F458A97B36D3B08A302;
extern const uint32_t g_rgctx_UnsafeUtility_SizeOf_TisT_t2DEEC1EDE9244B8523CA96477075BECFEDDEB70B_mFAB9277A26B57EEBBD96FBFF9A8E348CDF0F3695;
extern const uint32_t g_rgctx_FixedList_PaddingBytes_TisT_t2DEEC1EDE9244B8523CA96477075BECFEDDEB70B_m84508C7415A499FE729E49407F30491D8BA1347B;
extern const uint32_t g_rgctx_FixedList_Capacity_TisFixedBytes510_t95B284C3FF966246998B23701C3F0F55C6BD7973_TisT_t2DEEC1EDE9244B8523CA96477075BECFEDDEB70B_mFF48156C860043812A4CE8D19432EC0D7A50E568;
extern const uint32_t g_rgctx_FixedList512Bytes_1_get_Buffer_mD366D995A0996A4941BFD8FF7751F8888529019A;
extern const uint32_t g_rgctx_UnsafeUtility_ArrayElementAsRef_TisT_t2DEEC1EDE9244B8523CA96477075BECFEDDEB70B_m636A8EC5CD9E4CC03E218C231B2F573944500D88;
extern const uint32_t g_rgctx_TU26_t82245BF48873F403E3E9F1E66A5BD1770097F0A9;
extern const uint32_t g_rgctx_FixedList512Bytes_1_get_LengthInBytes_mE533117FD90EE225AB1657584FE15D9FCD3B31F6;
extern const uint32_t g_rgctx_FixedList32Bytes_1_t16FDC26837A35E8C9EE737B47C5566926DD97D40;
extern const uint32_t g_rgctx_FixedList32Bytes_1_get_Length_mFEB57847565241DE5AC20F3C47DFB1C1FBD77D42;
extern const uint32_t g_rgctx_FixedList32Bytes_1_t16FDC26837A35E8C9EE737B47C5566926DD97D40;
extern const uint32_t g_rgctx_T_t2DEEC1EDE9244B8523CA96477075BECFEDDEB70B;
extern const uint32_t g_rgctx_FixedList512Bytes_1_CompareTo_m2ED44CE82345E495B02845190DC950E9369F9B66;
extern const uint32_t g_rgctx_FixedList64Bytes_1_t692D679ABC650A6E1D639F9B704EBF1C015B7E54;
extern const uint32_t g_rgctx_FixedList64Bytes_1_get_Length_m0E56845145169510605A77387713117B1DF42A2D;
extern const uint32_t g_rgctx_FixedList64Bytes_1_t692D679ABC650A6E1D639F9B704EBF1C015B7E54;
extern const uint32_t g_rgctx_FixedList512Bytes_1_CompareTo_m7D404D1A8DCDBFBA6E77CA9A1BAE087DA8BEDB45;
extern const uint32_t g_rgctx_FixedList128Bytes_1_t855A409C9DAE10C3B3F003764F360E082BE304C1;
extern const uint32_t g_rgctx_FixedList128Bytes_1_get_Length_mC45C102EEA509426C4E25123ACB537AA2E1C8B53;
extern const uint32_t g_rgctx_FixedList128Bytes_1_t855A409C9DAE10C3B3F003764F360E082BE304C1;
extern const uint32_t g_rgctx_FixedList512Bytes_1_CompareTo_m0BE3A1FE674C670F1F163603702EA2EC121C96C0;
extern const uint32_t g_rgctx_FixedList512Bytes_1_CompareTo_mCB87559DEEEFBF43BBE5EDA844844CCB64B12C06;
extern const uint32_t g_rgctx_FixedList4096Bytes_1_t30D9953988646BADE87AE3D019C1B0BB523F33B2;
extern const uint32_t g_rgctx_FixedList4096Bytes_1_get_Length_mAFB31975A1194980FE703C4FF6157CA124568175;
extern const uint32_t g_rgctx_FixedList4096Bytes_1_t30D9953988646BADE87AE3D019C1B0BB523F33B2;
extern const uint32_t g_rgctx_FixedList512Bytes_1_CompareTo_m71F5DF9EF41C7A4B6C05EDC77E4AEBD567829F44;
extern const uint32_t g_rgctx_FixedList512Bytes_1_Equals_mFEFB3BF366F96DCEEF8CF0F400F6F6BC865C4EFA;
extern const uint32_t g_rgctx_FixedList512Bytes_1_Equals_mDC81D66D0B81AB0FEBD1F8666E2BB7783FA5ACB8;
extern const uint32_t g_rgctx_FixedList512Bytes_1_Equals_m2A8267A3F7D08EC772B96BE833442B1E5887C72E;
extern const uint32_t g_rgctx_FixedList512Bytes_1_Equals_mBDE871330097648822A6964D577DA54C9C62CDE1;
extern const uint32_t g_rgctx_FixedList512Bytes_1_Equals_mDAE776DA9235ED2BD2A75CD0AC265DB954F739E3;
extern const uint32_t g_rgctx_IEnumerator_1_tFF57FF7FE99225B911B779420D0023CC4CC8BEBD;
extern const uint32_t g_rgctx_FixedList4096Bytes_1_tDD8407366F29BD7A6221ED46D0353F2A5DEB8D87;
extern const uint32_t g_rgctx_FixedList4096Bytes_1_get_Length_mF981B72DCECDB30EBED5ACCC7749B57FE4D25643;
extern const uint32_t g_rgctx_FixedList4096Bytes_1_tDD8407366F29BD7A6221ED46D0353F2A5DEB8D87;
extern const uint32_t g_rgctx_UnsafeUtility_SizeOf_TisT_tBD073BADADC7386EA705AE4342916B0F07BDACDE_mDF9CEBCF3A941F23B144FCD20FDA1D094151485F;
extern const uint32_t g_rgctx_FixedList_PaddingBytes_TisT_tBD073BADADC7386EA705AE4342916B0F07BDACDE_m866B91A370BB67C092A4B0E5D72A7734F2132F73;
extern const uint32_t g_rgctx_FixedList_Capacity_TisFixedBytes4094_t8611441D8BDC6A677C2D9E551086F59EFBFCBBE5_TisT_tBD073BADADC7386EA705AE4342916B0F07BDACDE_m365CB7E2D2385EF3C160C60E268D84639982C57A;
extern const uint32_t g_rgctx_FixedList4096Bytes_1_get_Buffer_m8C59032AB0882881E308B5310103C4C0C67FE527;
extern const uint32_t g_rgctx_UnsafeUtility_ReadArrayElement_TisT_tBD073BADADC7386EA705AE4342916B0F07BDACDE_m4521B626F59BBB2321D000869DFCBDC991657E02;
extern const uint32_t g_rgctx_T_tBD073BADADC7386EA705AE4342916B0F07BDACDE;
extern const uint32_t g_rgctx_UnsafeUtility_WriteArrayElement_TisT_tBD073BADADC7386EA705AE4342916B0F07BDACDE_m7E4C5808A41558EDB297D03CF1DE6FE52199AA57;
extern const uint32_t g_rgctx_UnsafeUtility_ArrayElementAsRef_TisT_tBD073BADADC7386EA705AE4342916B0F07BDACDE_mB26498A653C7446BD4BC84B57EFA5CA41BF14D3B;
extern const uint32_t g_rgctx_TU26_t0E80504B7A1160E9950B585D6C82FDEC052FC737;
extern const uint32_t g_rgctx_FixedList4096Bytes_1_get_LengthInBytes_mDD272B4890655CD06ACF62B312C72673B7066DAB;
extern const uint32_t g_rgctx_FixedList32Bytes_1_t7AAD6CEC30726A322BBECE5EF36547B20FE7474E;
extern const uint32_t g_rgctx_FixedList32Bytes_1_get_Length_mE8F8A185DE5BB8CB0BEE9383F387044EC232D30C;
extern const uint32_t g_rgctx_FixedList32Bytes_1_t7AAD6CEC30726A322BBECE5EF36547B20FE7474E;
extern const uint32_t g_rgctx_FixedList4096Bytes_1_CompareTo_mDCA60AD837CDD603A14317382F8FE7B7A5332496;
extern const uint32_t g_rgctx_FixedList64Bytes_1_tA47527956352165EF643C47FD1BC7C6169E29EBC;
extern const uint32_t g_rgctx_FixedList64Bytes_1_get_Length_m6B94FC005EB3D868CEC8B0A1A2953929BDB403B9;
extern const uint32_t g_rgctx_FixedList64Bytes_1_tA47527956352165EF643C47FD1BC7C6169E29EBC;
extern const uint32_t g_rgctx_FixedList4096Bytes_1_CompareTo_m1EE2D74734AD460BFAC1124BEC50F6A7242C157A;
extern const uint32_t g_rgctx_FixedList128Bytes_1_t79918BA354DF7C9EED2BB2E611792E496C73C747;
extern const uint32_t g_rgctx_FixedList128Bytes_1_get_Length_mBEFA2EA7F7D0BDBF7900A55AD4870026F5CDD9E0;
extern const uint32_t g_rgctx_FixedList128Bytes_1_t79918BA354DF7C9EED2BB2E611792E496C73C747;
extern const uint32_t g_rgctx_FixedList4096Bytes_1_CompareTo_m76A8C7244A78E281DE7D75356C8C98452CC9B973;
extern const uint32_t g_rgctx_FixedList512Bytes_1_t3AB2AD5617F05D853E1819914CDD7FE040D0A863;
extern const uint32_t g_rgctx_FixedList512Bytes_1_get_Length_m0FB45017FC642488814B76ED2D5856074D0A2C13;
extern const uint32_t g_rgctx_FixedList512Bytes_1_t3AB2AD5617F05D853E1819914CDD7FE040D0A863;
extern const uint32_t g_rgctx_FixedList4096Bytes_1_CompareTo_m7B2C705430CA47FDBA89E556A4BA14F10B5E62D6;
extern const uint32_t g_rgctx_FixedList4096Bytes_1_CompareTo_m48E8E70F5EC08F0B2764E1FA714A0E6B42AD79B3;
extern const uint32_t g_rgctx_FixedList4096Bytes_1_Equals_mA097DF642E512753B3F3819A63A991273FB15433;
extern const uint32_t g_rgctx_FixedList4096Bytes_1_Equals_m3F25D402683E10A10CC6E899C3C5F3242C07DE65;
extern const uint32_t g_rgctx_FixedList4096Bytes_1_Equals_mA57897971800CECFA745EAA611995A129370DE0E;
extern const uint32_t g_rgctx_FixedList4096Bytes_1_Equals_m234039E2C015B564CDC7962B8ECA3F6161456BC2;
extern const uint32_t g_rgctx_FixedList4096Bytes_1_Equals_m79776FC49E1BE2ECE4CD5A12AB1AD9E67199A5B6;
extern const uint32_t g_rgctx_IEnumerator_1_t3529DEFB532FA3B3FC1BECA7AE3AC6C2626EDA06;
extern const uint32_t g_rgctx_TU26_t84A5DDF8149612DCE2DA99F2960BEFFAD48DC63D;
extern const uint32_t g_rgctx_T_t128D4A74C38857567A2488C6CD7C1BA749677358;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_t128D4A74C38857567A2488C6CD7C1BA749677358_IIndexable_1_get_Length_mAED3EB7DD75EF7C1E601F6DEA790BEF4B7C8E2EA;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_t128D4A74C38857567A2488C6CD7C1BA749677358_IUTF8Bytes_TryResize_m42B6D259A45BC13F377FFA6A77393E1B89319CE0;
extern const uint32_t g_rgctx_FixedStringMethods_Write_TisT_t128D4A74C38857567A2488C6CD7C1BA749677358_m5B6E47CFAA22B1EB4E5C1C39A067F65B5B48DFA1;
extern const uint32_t g_rgctx_TU26_t97334FE432BBD59A7F23BEC80DE5A19A87EF2930;
extern const uint32_t g_rgctx_FixedStringMethods_Append_TisT_tCACCA4E422C4855D9ED402558E999CB0C2BC720F_mEFC58699E2B44FF5B958EF465B6A020B2273864C;
extern const uint32_t g_rgctx_T2U26_t25A760909D994F3717596C4EE57CF6DA5115482E;
extern const uint32_t g_rgctx_UnsafeUtilityExtensions_AsRef_TisT2_t32EA74C6914E6CF63B14ADAE219B21DE90DE3FDC_mEACB25F0892A8D2C1017914E9BC224F8AF8A594F;
extern const uint32_t g_rgctx_TU26_t30B77F17623009AE6EF8E22BD2ACB710E4E5EF99;
extern const uint32_t g_rgctx_T2_t32EA74C6914E6CF63B14ADAE219B21DE90DE3FDC;
extern const Il2CppRGCTXConstrainedData g_rgctx_T2_t32EA74C6914E6CF63B14ADAE219B21DE90DE3FDC_IUTF8Bytes_GetUnsafePtr_mB67E683E33AA89AB66BB1187E74C99A53EFEBED1;
extern const Il2CppRGCTXConstrainedData g_rgctx_T2_t32EA74C6914E6CF63B14ADAE219B21DE90DE3FDC_IIndexable_1_get_Length_mAED3EB7DD75EF7C1E601F6DEA790BEF4B7C8E2EA;
extern const uint32_t g_rgctx_FixedStringMethods_Append_TisT_tAF666238E844771565195E661F0E174E5E0F1867_m66FD19C64582D5837D91409D20FCDECDC11D875A;
extern const uint32_t g_rgctx_TU26_tAB160D02D431E15E17E0AC54B4736953CEC6732E;
extern const uint32_t g_rgctx_T_tACE9618446D04FF75CD0C81073BF6B10B6D3E63D;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_tACE9618446D04FF75CD0C81073BF6B10B6D3E63D_IIndexable_1_set_Length_m0F060BE1FF0E5613E5F666EE4DD8E50303DED483;
extern const uint32_t g_rgctx_T2U26_tF16FED89363541D0C2B680036E195EAA75DF3F54;
extern const uint32_t g_rgctx_FixedStringMethods_Append_TisT_tACE9618446D04FF75CD0C81073BF6B10B6D3E63D_TisT2_t8C1FFECC052A229017FB97CDACAFAB459238BFFC_m219C6D865291C529BF96B330F00E8AF6A38C6039;
extern const uint32_t g_rgctx_TU26_tE84BE9D9AEC2AE47CF47BCCCC7A0EF8A1402FA80;
extern const uint32_t g_rgctx_T_t394CB326631A38B224E2EF133552EA72F59A5785;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_t394CB326631A38B224E2EF133552EA72F59A5785_IIndexable_1_get_Length_mAED3EB7DD75EF7C1E601F6DEA790BEF4B7C8E2EA;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_t394CB326631A38B224E2EF133552EA72F59A5785_IUTF8Bytes_TryResize_m42B6D259A45BC13F377FFA6A77393E1B89319CE0;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_t394CB326631A38B224E2EF133552EA72F59A5785_IUTF8Bytes_GetUnsafePtr_mB67E683E33AA89AB66BB1187E74C99A53EFEBED1;
extern const uint32_t g_rgctx_TU26_t74AAB1AA331BAE90999602C2F397DC6327DBFF86;
extern const uint32_t g_rgctx_T_t40CFE80575C0B3A9E97C1A7DB9A1C99154484193;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_t40CFE80575C0B3A9E97C1A7DB9A1C99154484193_IUTF8Bytes_GetUnsafePtr_mB67E683E33AA89AB66BB1187E74C99A53EFEBED1;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_t40CFE80575C0B3A9E97C1A7DB9A1C99154484193_IIndexable_1_get_Length_mAED3EB7DD75EF7C1E601F6DEA790BEF4B7C8E2EA;
extern const uint32_t g_rgctx_T2U26_tA3F74A62486E5E3EAD970C766C0485F663622263;
extern const uint32_t g_rgctx_UnsafeUtilityExtensions_AsRef_TisT2_t2BAE730EBE1EFEDF9B51E00473183A8664C69200_m61DFED76CEE5300C00F730592D7972436A60570F;
extern const uint32_t g_rgctx_TU26_t3F2BB55E2EEE0143FA2C0FC4240F0323EE14D581;
extern const uint32_t g_rgctx_T2_t2BAE730EBE1EFEDF9B51E00473183A8664C69200;
extern const Il2CppRGCTXConstrainedData g_rgctx_T2_t2BAE730EBE1EFEDF9B51E00473183A8664C69200_IUTF8Bytes_GetUnsafePtr_mB67E683E33AA89AB66BB1187E74C99A53EFEBED1;
extern const Il2CppRGCTXConstrainedData g_rgctx_T2_t2BAE730EBE1EFEDF9B51E00473183A8664C69200_IIndexable_1_get_Length_mAED3EB7DD75EF7C1E601F6DEA790BEF4B7C8E2EA;
extern const uint32_t g_rgctx_FixedStringMethods_CompareTo_TisT_t44578AF0C73EE30B7AE87E0F1E82A995DF6FEBCA_m1F12FF739B81A47680F8F08CA3AB5E03308601C8;
extern const uint32_t g_rgctx_TU26_t602269BCA26C709CF8FE4736B0AC87642EF6243F;
extern const uint32_t g_rgctx_T_tEAAE25CD983326DADA347F015AD559DF085F8D3D;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_tEAAE25CD983326DADA347F015AD559DF085F8D3D_IUTF8Bytes_GetUnsafePtr_mB67E683E33AA89AB66BB1187E74C99A53EFEBED1;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_tEAAE25CD983326DADA347F015AD559DF085F8D3D_IIndexable_1_get_Length_mAED3EB7DD75EF7C1E601F6DEA790BEF4B7C8E2EA;
extern const uint32_t g_rgctx_FixedStringMethods_CompareTo_TisT_tEAAE25CD983326DADA347F015AD559DF085F8D3D_mE9180BC22FB55220D9A4AFC14ADDE7EB1F87D710;
extern const uint32_t g_rgctx_T2U26_tFB44CF4B01E1A7E5CEE6AB691F5FBAC317469ADD;
extern const uint32_t g_rgctx_UnsafeUtilityExtensions_AsRef_TisT2_tD89AD2D09633D2D7B863E4F80D70D53F51878460_mB0639EB2C8B2C905592E4AFD67D9D20DE1D7D2F8;
extern const uint32_t g_rgctx_TU26_tE5CE21852D897F87CC71A72500485AD5AFB65163;
extern const uint32_t g_rgctx_T2_tD89AD2D09633D2D7B863E4F80D70D53F51878460;
extern const Il2CppRGCTXConstrainedData g_rgctx_T2_tD89AD2D09633D2D7B863E4F80D70D53F51878460_IUTF8Bytes_GetUnsafePtr_mB67E683E33AA89AB66BB1187E74C99A53EFEBED1;
extern const Il2CppRGCTXConstrainedData g_rgctx_T2_tD89AD2D09633D2D7B863E4F80D70D53F51878460_IIndexable_1_get_Length_mAED3EB7DD75EF7C1E601F6DEA790BEF4B7C8E2EA;
extern const uint32_t g_rgctx_FixedStringMethods_Equals_TisT_tF099DB17311F45946A995974B24F1AEC8CECC5A5_mB70E43D780615DD0351DBE1D6566297BEDDA792F;
extern const uint32_t g_rgctx_TU26_t079B53B1F7BFACF677276B7FB8174797E1FF4E8B;
extern const uint32_t g_rgctx_T_tEA019B08712E2CAE787B21AFA982788BF07899CA;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_tEA019B08712E2CAE787B21AFA982788BF07899CA_IUTF8Bytes_GetUnsafePtr_mB67E683E33AA89AB66BB1187E74C99A53EFEBED1;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_tEA019B08712E2CAE787B21AFA982788BF07899CA_INativeList_1_get_Capacity_mF5B445EAD5F226F5E8C3B2DDF4B8C35EFBB40C4C;
extern const uint32_t g_rgctx_TU26_t9AD8DE72370FBE3DDFE640DBB2D54F0447C30046;
extern const uint32_t g_rgctx_T_tD92B3110D590A5F2CBCE2D8750D7F0E6FB6C1D34;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_tD92B3110D590A5F2CBCE2D8750D7F0E6FB6C1D34_IIndexable_1_get_Length_mAED3EB7DD75EF7C1E601F6DEA790BEF4B7C8E2EA;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_tD92B3110D590A5F2CBCE2D8750D7F0E6FB6C1D34_IUTF8Bytes_GetUnsafePtr_mB67E683E33AA89AB66BB1187E74C99A53EFEBED1;
extern const uint32_t g_rgctx_TU26_tD26F5F71F6F190660014703FD83494ECE47EB008;
extern const uint32_t g_rgctx_T_t0720BD109D0FD7DCF11AF76ED4D7B452221C1557;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_t0720BD109D0FD7DCF11AF76ED4D7B452221C1557_IUTF8Bytes_GetUnsafePtr_mB67E683E33AA89AB66BB1187E74C99A53EFEBED1;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_t0720BD109D0FD7DCF11AF76ED4D7B452221C1557_IIndexable_1_get_Length_mAED3EB7DD75EF7C1E601F6DEA790BEF4B7C8E2EA;
extern const uint32_t g_rgctx_TU2A_t9675EE6497AD8465FD78590B10D7DF78A42B1513;
extern const uint32_t g_rgctx_Array_Resize_TisT_tA7BC8A9B01B94F56CE3273E1C3F4463BAFDB2774_m40E5359FE293594F47DD50DDB1F2AD213B4A709A;
extern const uint32_t g_rgctx_TU2A_tA596F7F23DF141DFAF4BC0E1445C816B4C13BFCF;
extern const uint32_t g_rgctx_UnsafeUtility_SizeOf_TisT_t4439E68F64AF98A1F1E70993ACB3E0626E7A7954_mD44E7FDD63803D509A5BB08B506B82CA121DF38A;
extern const uint32_t g_rgctx_UnsafeUtility_AlignOf_TisT_t4439E68F64AF98A1F1E70993ACB3E0626E7A7954_mD999DEAF969B234226FD5F050A1A8DF99545F7FC;
extern const uint32_t g_rgctx_Array_Resize_TisT_t634E6D84D11B120C1E4D1D5CD66CFD79E344599B_m567688287F6FEC7553806E3C8E440ADD7EECE9EA;
extern const uint32_t g_rgctx_TU2A_tDF9B3864D56377108BA18F71A274E382B1384EDD;
extern const uint32_t g_rgctx_NativeList_1__ctor_mFC02027D535FF8FA6DA0D2F3FF28BE9C3C22FA0D;
extern const uint32_t g_rgctx_NativeList_1_tC78A0006334D5DCFB09CF5B4571E681CD20EEDF1;
extern const uint32_t g_rgctx_UnsafeList_1_t62C841C6D06562004B617CB8FEB154768A22A448;
extern const uint32_t g_rgctx_UnsafeList_1U2A_tC9B789EAE714A4CB9218D40209373216B4911076;
extern const uint32_t g_rgctx_NativeList_1_tC78A0006334D5DCFB09CF5B4571E681CD20EEDF1;
extern const uint32_t g_rgctx_NativeList_1_Initialize_TisAllocatorHandle_t3CA09720B1F89F91A8DDBA95E74C28A1EC3E3148_mDA27076ECD04FDDCE4F08EC240944ED85C58EA1F;
extern const uint32_t g_rgctx_UnsafeList_1_get_Item_m3A75728B303CDC6919167D5BFCD56D9BAE755F4A;
extern const uint32_t g_rgctx_T_t86F43EBF284E6FC6FD87B54AE593FF12A96EACEA;
extern const uint32_t g_rgctx_UnsafeList_1_ElementAt_m94F2861AED0CAABDD863BF768BD5B41FE0A10976;
extern const uint32_t g_rgctx_TU26_t9182D13124B0C24A7BE52A270A8164C77416315C;
extern const uint32_t g_rgctx_UnsafeList_1_get_Length_mBB48D1B7E16C1A3EFC1FECFAE979F4AC003C5BA7;
extern const uint32_t g_rgctx_UnsafeList_1_Resize_m877B9B1A6AA00562D5D52E78696C5B2364FCB296;
extern const uint32_t g_rgctx_UnsafeList_1_get_Capacity_m7CEDBD1C464E131C4BA666F0BE92ED8747465B1D;
extern const uint32_t g_rgctx_UnsafeList_1_set_Capacity_m3A8F01AD3A516A23455A17E28F75BF7E15624DEB;
extern const uint32_t g_rgctx_UnsafeList_1_Destroy_m4365069A2F94BC7B50074F5C3C23A35769947475;
extern const uint32_t g_rgctx_UnsafeList_1_Clear_m763F409F9070AAB6B2E20A3952CED497999D10B5;
extern const uint32_t g_rgctx_NativeList_1_AsArray_mF7F649295B1EB59DCF87D80023CDC9825B535243;
extern const uint32_t g_rgctx_NativeArray_1_t7C08CCE83593752FA8281642C4BF74F6C0DB2D2E;
extern const uint32_t g_rgctx_UnsafeList_1_t62C841C6D06562004B617CB8FEB154768A22A448;
extern const uint32_t g_rgctx_TU2A_tB9A028E4087C5A8ADBA9F589ADEB6159DFEF1E1D;
extern const uint32_t g_rgctx_NativeArrayUnsafeUtility_ConvertExistingDataToNativeArray_TisT_t86F43EBF284E6FC6FD87B54AE593FF12A96EACEA_m30203C993DF4EFC52F1B6D61B11B1BE1438DC7A0;
extern const uint32_t g_rgctx_IEnumerator_1_tE14F09601BCF464F55C6C384864842F6F442D077;
extern const uint32_t g_rgctx_NativeList_1_Clear_mBBBC463C45F8766F8D109EFFC6109E5F90C81D1C;
extern const uint32_t g_rgctx_NativeArray_1_get_Length_mF3D9F277FD8C8586515F953BA049D04561B988F9;
extern const uint32_t g_rgctx_NativeArray_1_t7C08CCE83593752FA8281642C4BF74F6C0DB2D2E;
extern const uint32_t g_rgctx_NativeList_1_Resize_m2DA751BFAA461CD57325BF1C6766FBF50AE0E384;
extern const uint32_t g_rgctx_NativeArray_1_CopyFrom_m447A2DB30EBA84BF47AA1C10F25B05366B110CC6;
extern const uint32_t g_rgctx_ParallelWriter_tA38ACEA2D6CE15CA82A92A21D13E256D2D89ED24;
extern const uint32_t g_rgctx_ParallelWriter__ctor_m91412B47305FD5876EBF9350E5C27BB78704A424;
extern const uint32_t g_rgctx_UU26_t4C94D487822BCBA34B563B36DF016842BCAB0D03;
extern const uint32_t g_rgctx_UnsafeList_1_Create_TisU_t8D9513FF7045C2A2417339533F96C0B604BC6DE3_m87141D5D82ED170DBB65F3C443132ABE0C2B143E;
extern const uint32_t g_rgctx_U_t8D9513FF7045C2A2417339533F96C0B604BC6DE3;
extern const Il2CppRGCTXConstrainedData g_rgctx_U_t8D9513FF7045C2A2417339533F96C0B604BC6DE3_IAllocator_get_Handle_m54F384A4E6B7365849D3462AA309C77F8D9E9E6C;
extern const uint32_t g_rgctx_UnsafeList_1U2A_tFD6373CAA08D3C77B8FCA3752E8A2536287E424C;
extern const uint32_t g_rgctx_ParallelWriter_tA6D09E1AA1BA56D8C5D80A07AD8B488DDC5179A6;
extern const uint32_t g_rgctx_UnsafeList_1_tFD500AA1E7CF0F5EB075E4496F20180D9A1B3F24;
extern const uint32_t g_rgctx_TU2A_t20ACD4B7D10D2569904A979745941D3527E3C7E1;
extern const uint32_t g_rgctx_T_t10D58294F47473AB094BD4C41F79FBF65D1CB6DD;
extern const uint32_t g_rgctx_UnsafeUtility_WriteArrayElement_TisT_t10D58294F47473AB094BD4C41F79FBF65D1CB6DD_mE757F6839718ACD62BC40920203F151319B2703B;
extern const uint32_t g_rgctx_TU2A_t7B76FD9857804BBE0AA1EE6D0BBB72F763A9CE43;
extern const uint32_t g_rgctx_T_t70C3EC27D19FAFB4F9747837E54C3FACF3C6965D;
extern const uint32_t g_rgctx_U_t5FCBD40D8C549213ECA9F91D7D620CA008DC2F79;
extern const uint32_t g_rgctx_IComparer_1_t08CB26918E5DBADDA160026211F462AC1CA4972B;
extern const Il2CppRGCTXConstrainedData g_rgctx_U_t5FCBD40D8C549213ECA9F91D7D620CA008DC2F79_IComparer_1_Compare_m5BA85B80E0E8EC3268576EA6FA38E120947588E0;
extern const uint32_t g_rgctx_NativeList_1_tC1F6E8D2AB78C5B9F4A995EDACBF910986850EAE;
extern const uint32_t g_rgctx_NativeListUnsafeUtility_GetUnsafePtr_TisT_t87B231C51C2608C7F45DF89DED795C27E1DDBD88_m299A3F9D50195108DA00CC6284750118B13D4280;
extern const uint32_t g_rgctx_NativeList_1_get_Length_mA1DEC7E32046882769D495AC93C473C6864FB034;
extern const uint32_t g_rgctx_NativeList_1_tC1F6E8D2AB78C5B9F4A995EDACBF910986850EAE;
extern const uint32_t g_rgctx_U_t716BCA6917D7AE5B382B05528161A14F7E0D2216;
extern const uint32_t g_rgctx_NativeSortExtension_IntroSort_TisT_t87B231C51C2608C7F45DF89DED795C27E1DDBD88_TisU_t716BCA6917D7AE5B382B05528161A14F7E0D2216_mFCE4F64E4C2A5FD9C512A22F7740762A46CB89BE;
extern const uint32_t g_rgctx_UnsafeList_1_t1E6331DE1C6630D24CCF360565225D04639A0070;
extern const uint32_t g_rgctx_TU2A_t0F8FFA0126DBBD3440430D09A66742E016373E81;
extern const uint32_t g_rgctx_UnsafeList_1_get_Length_m1AAB4D93F6F40AE5B8305FCC69FF83CA79D16B12;
extern const uint32_t g_rgctx_UnsafeList_1_t1E6331DE1C6630D24CCF360565225D04639A0070;
extern const uint32_t g_rgctx_U_t0522B71478B41B1C4B354E36ABEFBD88E1F2B88D;
extern const uint32_t g_rgctx_NativeSortExtension_IntroSort_TisT_t1D1CAA3797B318EB8CB47A878FC6F982380CD81D_TisU_t0522B71478B41B1C4B354E36ABEFBD88E1F2B88D_m64B56586DB3045B8E0892319D845AAE9FD03499D;
extern const uint32_t g_rgctx_UnsafeList_1_t6C67008E2069DB675A532CD0F0C50CA9E2920070;
extern const uint32_t g_rgctx_TU2A_t84C85028B3642586C79881AE9DEBF2179510ECE8;
extern const uint32_t g_rgctx_UnsafeList_1_get_Length_mF1523912D6DBD8655982A742745D819F240CB8F2;
extern const uint32_t g_rgctx_UnsafeList_1_t6C67008E2069DB675A532CD0F0C50CA9E2920070;
extern const uint32_t g_rgctx_T_tBE9BDD9637297B0CE397C47A92971E02E573170A;
extern const uint32_t g_rgctx_U_t1201D6465E6320EFCBD3E5906FA139D9C48D10A0;
extern const uint32_t g_rgctx_NativeSortExtension_BinarySearch_TisT_tBE9BDD9637297B0CE397C47A92971E02E573170A_TisU_t1201D6465E6320EFCBD3E5906FA139D9C48D10A0_m84AFBD22CA6B406AE677E7FAFB331DEE57C5B922;
extern const uint32_t g_rgctx_U_t984657F3CA449F6965F5B6A61D21C535A3B6AD2F;
extern const uint32_t g_rgctx_NativeSortExtension_IntroSort_TisT_t92A8A4543C7D53AEF9175B204F0123DCDAF87E3E_TisU_t984657F3CA449F6965F5B6A61D21C535A3B6AD2F_m18B923D57F6D9DC55CE998C45339DF4C2EE55F88;
extern const uint32_t g_rgctx_U_tC691A8B7B753EE7D97DAE216705A19687CD512DB;
extern const uint32_t g_rgctx_NativeSortExtension_SwapIfGreaterWithItems_TisT_t57C04D0ADE650FA6DFBECBDB0949AA01E83F997C_TisU_tC691A8B7B753EE7D97DAE216705A19687CD512DB_m7F3C5537DB0B6A77BF41CEE7917631D9D0DAE694;
extern const uint32_t g_rgctx_NativeSortExtension_InsertionSort_TisT_t57C04D0ADE650FA6DFBECBDB0949AA01E83F997C_TisU_tC691A8B7B753EE7D97DAE216705A19687CD512DB_mF336EBAEC2A1CF05C793A6113D42C2A5BE0E4995;
extern const uint32_t g_rgctx_NativeSortExtension_HeapSort_TisT_t57C04D0ADE650FA6DFBECBDB0949AA01E83F997C_TisU_tC691A8B7B753EE7D97DAE216705A19687CD512DB_mAEDEDE4B2C955D228C634B9350EA10CDC3D89A8D;
extern const uint32_t g_rgctx_NativeSortExtension_Partition_TisT_t57C04D0ADE650FA6DFBECBDB0949AA01E83F997C_TisU_tC691A8B7B753EE7D97DAE216705A19687CD512DB_m4719247E264EC996439C515A1D1AE95021476BDE;
extern const uint32_t g_rgctx_NativeSortExtension_IntroSort_TisT_t57C04D0ADE650FA6DFBECBDB0949AA01E83F997C_TisU_tC691A8B7B753EE7D97DAE216705A19687CD512DB_m8D5EC5A46E31FAD09E85C91CC847E75954473A56;
extern const uint32_t g_rgctx_UnsafeUtility_ReadArrayElement_TisT_t989AD081463D7816B3C7024B0855781C5E3439A1_mB0E2D4F2916CC3CD670930CEBB2F8D8B7AA8FE51;
extern const uint32_t g_rgctx_T_t989AD081463D7816B3C7024B0855781C5E3439A1;
extern const uint32_t g_rgctx_UnsafeUtility_WriteArrayElement_TisT_t989AD081463D7816B3C7024B0855781C5E3439A1_mE2E43B49C1356D94BC6F1E2E9AC0800210F808F7;
extern const uint32_t g_rgctx_U_t71749F86706A6DD523CBA96FDE0CAFF19DC52F9D;
extern const uint32_t g_rgctx_IComparer_1_tF49CABC09B222B7CFBE8189880EEC18D3870444A;
extern const Il2CppRGCTXConstrainedData g_rgctx_U_t71749F86706A6DD523CBA96FDE0CAFF19DC52F9D_IComparer_1_Compare_mF4CDE8817BD70F201A31A25EAB6340238960D036;
extern const uint32_t g_rgctx_U_tC345929E416F15F6572F8BE2BAE8C0E611FA26D4;
extern const uint32_t g_rgctx_NativeSortExtension_SwapIfGreaterWithItems_TisT_t9EF72661B3BD897FB6628CE2E1B95D068B06580A_TisU_tC345929E416F15F6572F8BE2BAE8C0E611FA26D4_mA95426095429FAAC30CA9C60DA7AF506C6D8A6FB;
extern const uint32_t g_rgctx_UnsafeUtility_ReadArrayElement_TisT_t9EF72661B3BD897FB6628CE2E1B95D068B06580A_mA65DFAF646799C5AC863F87AE40B1E6E4C8773F2;
extern const uint32_t g_rgctx_T_t9EF72661B3BD897FB6628CE2E1B95D068B06580A;
extern const uint32_t g_rgctx_NativeSortExtension_Swap_TisT_t9EF72661B3BD897FB6628CE2E1B95D068B06580A_m7D5699FA454AB533F2B925275A27B72C4715AF50;
extern const uint32_t g_rgctx_IComparer_1_t7C67406FAD142DA0F7EF144179C9A8F94ED1FCC6;
extern const Il2CppRGCTXConstrainedData g_rgctx_U_tC345929E416F15F6572F8BE2BAE8C0E611FA26D4_IComparer_1_Compare_m5B89307628B10886D74690DD3FD4D328B3DCBA52;
extern const uint32_t g_rgctx_U_t96D9FF22A5AC3987AA45E988B0F157C7355E83BE;
extern const uint32_t g_rgctx_NativeSortExtension_Heapify_TisT_t8A1DFC90E7840B7CC11C96F845A4586F9F9CA870_TisU_t96D9FF22A5AC3987AA45E988B0F157C7355E83BE_mB5B997E8D5594C8AE402402B43D586C640B4F722;
extern const uint32_t g_rgctx_NativeSortExtension_Swap_TisT_t8A1DFC90E7840B7CC11C96F845A4586F9F9CA870_m3D01BA9FE0B8C5662900E64858DA6876E1914CBB;
extern const uint32_t g_rgctx_UnsafeUtility_ReadArrayElement_TisT_tA43B34C466236E4CF4ACB1D7267181A8DCE8129E_m855A3860C60D5DDB6716348B718BE652EE5CAEE0;
extern const uint32_t g_rgctx_T_tA43B34C466236E4CF4ACB1D7267181A8DCE8129E;
extern const uint32_t g_rgctx_U_t3859302F7BB1D87B147FFF956346B678DE25245F;
extern const uint32_t g_rgctx_IComparer_1_tC03C2835996A7D4E10B7F63FAD31AECB032A47AD;
extern const Il2CppRGCTXConstrainedData g_rgctx_U_t3859302F7BB1D87B147FFF956346B678DE25245F_IComparer_1_Compare_mA5D17366CCAD2454484FC31CF779542D8D0160DE;
extern const uint32_t g_rgctx_UnsafeUtility_WriteArrayElement_TisT_tA43B34C466236E4CF4ACB1D7267181A8DCE8129E_mE56A0F3C00B718186AE0AA64FD6F17FB8DF0C9DF;
extern const uint32_t g_rgctx_UnsafeUtility_ReadArrayElement_TisT_t3B3F1042277DCEFE0E4D5BA9D64D69F692EC97F4_m9D8E17BC9B25E3B14AD9FF848D2488BF71DACCE1;
extern const uint32_t g_rgctx_T_t3B3F1042277DCEFE0E4D5BA9D64D69F692EC97F4;
extern const uint32_t g_rgctx_UnsafeUtility_WriteArrayElement_TisT_t3B3F1042277DCEFE0E4D5BA9D64D69F692EC97F4_m5A2FC63D257B4D1819049282247C956BADE74CF1;
extern const uint32_t g_rgctx_U_t2718C0A06481493BD467989009D3ABB63FA70A44;
extern const uint32_t g_rgctx_UnsafeUtility_ReadArrayElement_TisT_tFC2A00320878DE26C1676CA44DEC8F6FC9EA8F14_mF614B0F5C1A0588A141DC8182A19E02CB3C89415;
extern const uint32_t g_rgctx_T_tFC2A00320878DE26C1676CA44DEC8F6FC9EA8F14;
extern const uint32_t g_rgctx_IComparer_1_t3D6E80FBEB14173816BD7525E8DDEB69D306F20D;
extern const Il2CppRGCTXConstrainedData g_rgctx_U_t2718C0A06481493BD467989009D3ABB63FA70A44_IComparer_1_Compare_mC4058AE4B39C0C3E52F4DC249E5A899CFC12EACB;
extern const uint32_t g_rgctx_NativeSortExtension_Swap_TisT_tFC2A00320878DE26C1676CA44DEC8F6FC9EA8F14_m3A2E22E55D9FBA0A0284CFA34E2B76E3FB8E185C;
extern const uint32_t g_rgctx_T_t575A15A58669A37242DC151F62F2FB44F3F049C8;
extern const uint32_t g_rgctx_IComparable_1_t12EAF1F40970F4947BF6F6AB46FEB1A2D0A24837;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_t575A15A58669A37242DC151F62F2FB44F3F049C8_IComparable_1_CompareTo_m4CF765737A22A55E4D962D9DEF6FA54B2BB6FBA0;
extern const uint32_t g_rgctx_Array_Allocate_TisT_t5BC8A3A1143355B7F5A74A86A9F4AD760F016F3F_mA431EE913AA03D9F69CCBF66FAB64CC283855B20;
extern const uint32_t g_rgctx_TU2A_tA8F0D072948467527BD52919C365F649AD380C4F;
extern const uint32_t g_rgctx_UnmanagedArray_1_tAF6F3819AE72060460FDDAAECEA69E07573B0961;
extern const uint32_t g_rgctx_Unmanaged_Free_TisT_t5BC8A3A1143355B7F5A74A86A9F4AD760F016F3F_m80B9E4D5EF4A9B36865D150DF2BAEA934AF4B721;
extern const uint32_t g_rgctx_T_t5BC8A3A1143355B7F5A74A86A9F4AD760F016F3F;
extern const uint32_t g_rgctx_TU26_tD02DEACCD2B1FD9DEC0AB7F0DFCBBE7EA290A972;
extern const uint32_t g_rgctx_NativeList_1_tAEC8B8878A54935A039ED3A1A299EEDF4C70C35F;
extern const uint32_t g_rgctx_UnsafeList_1U2A_tD2F82C6F716ECB80D9B21720F6C26086520D3F24;
extern const uint32_t g_rgctx_UnsafeList_1_t8C08F76776E0B8E79317E30818A6A0E8C5DE164A;
extern const uint32_t g_rgctx_TU2A_t33FC32499173DC5E7DA6071CDDFC7756091F1147;
extern const uint32_t g_rgctx_UnsafeList_1_t620A94D0181E3DFEF514032D62F7285F783817BF;
extern const uint32_t g_rgctx_UnsafeList_1_get_Capacity_mE30AE2BB42DF086225C6BEEACED7A1E66EAC91ED;
extern const uint32_t g_rgctx_UnsafeList_1_t620A94D0181E3DFEF514032D62F7285F783817BF;
extern const uint32_t g_rgctx_UnsafeList_1_Resize_mD2E7D06E288A389059211E1D82BA7DB35F3AF301;
extern const uint32_t g_rgctx_UnsafeList_1_SetCapacity_mC9FF72A63AF11927A72AFC4491824A38C3741104;
extern const uint32_t g_rgctx_TU2A_t252CC32CDA4B2899B3C069D2669CF4691A3FA43E;
extern const uint32_t g_rgctx_T_t6368C7377A351E8DAE030B3776E2EAB48430F6A1;
extern const uint32_t g_rgctx_TU26_t953A346901CE9031A1E7986603500DA1E9467CB3;
extern const uint32_t g_rgctx_UnsafeList_1U2A_tB931B1757B901B48D48901E8D9AED5AFE81F9DB0;
extern const uint32_t g_rgctx_UnsafeList_1_Dispose_mA9C46557DD7D143A317FDF3F55E3DB921A44125C;
extern const uint32_t g_rgctx_AllocatorManager_Free_TisUnsafeList_1_t620A94D0181E3DFEF514032D62F7285F783817BF_m10CA4CD105BFD216034DA9E1E453483651FF7BA7;
extern const uint32_t g_rgctx_AllocatorManager_Free_TisT_t6368C7377A351E8DAE030B3776E2EAB48430F6A1_m3380E20A35AE021576F755FA3B86CE60AC955DAB;
extern const uint32_t g_rgctx_UnsafeUtility_AlignOf_TisT_t6368C7377A351E8DAE030B3776E2EAB48430F6A1_m541A7459A8FD164187328166064C4404C9CB1247;
extern const uint32_t g_rgctx_UnsafeList_1_SetCapacity_TisAllocatorHandle_t3CA09720B1F89F91A8DDBA95E74C28A1EC3E3148_mB514D6019190B92124D1A238FB222C812F7CC09F;
extern const uint32_t g_rgctx_UnsafeUtility_WriteArrayElement_TisT_t6368C7377A351E8DAE030B3776E2EAB48430F6A1_mA94F2BC0DE06EB1D0025E0C41B8513CADC008966;
extern const uint32_t g_rgctx_UnsafeList_1_get_Length_m84D5FF3632FD449358D5BFBAD4238CD69C98A61D;
extern const uint32_t g_rgctx_UnsafeUtility_SizeOf_TisT_t6368C7377A351E8DAE030B3776E2EAB48430F6A1_m26A1720E75C9C5B642A2C8736B8323913BE7FA21;
extern const uint32_t g_rgctx_IEnumerator_1_tC56E7A39F471FA7A79480CF2118DAD86C52EB939;
extern const uint32_t g_rgctx_UU26_tD9381527A9C070524FBDBDD330FB7658EDFB83FE;
extern const uint32_t g_rgctx_AllocatorManager_Allocate_TisU_t00C790F8C500DC1C444D4422A52F657E0858DB7D_TisUnsafeList_1_t620A94D0181E3DFEF514032D62F7285F783817BF_m33A1C62888A2F0358171DBBB13F92E1995C3982D;
extern const uint32_t g_rgctx_U_t00C790F8C500DC1C444D4422A52F657E0858DB7D;
extern const Il2CppRGCTXConstrainedData g_rgctx_U_t00C790F8C500DC1C444D4422A52F657E0858DB7D_IAllocator_get_Handle_m54F384A4E6B7365849D3462AA309C77F8D9E9E6C;
extern const uint32_t g_rgctx_UnsafeList_1_SetCapacity_TisU_t00C790F8C500DC1C444D4422A52F657E0858DB7D_mED87DE5A2866AD2610DF962DDD2B32565BE50175;
extern const uint32_t g_rgctx_UU26_tE109A31D37B5F00272456593CF5D9883BD89F86A;
extern const uint32_t g_rgctx_AllocatorManager_Allocate_TisU_tD4E7F950E65E51D0FFB646539AFA97213F18FDCB_m3424B5B28B4777480531ABD6CCCBF82C251F0D46;
extern const uint32_t g_rgctx_AllocatorManager_Free_TisU_tD4E7F950E65E51D0FFB646539AFA97213F18FDCB_TisT_t6368C7377A351E8DAE030B3776E2EAB48430F6A1_m54F51F8A7FBB83F8306F70DA0A131C06A38E6706;
extern const uint32_t g_rgctx_UU26_t8968815DF07BA4CC2C505CF3D681EC7542D8FB73;
extern const uint32_t g_rgctx_UnsafeList_1_Realloc_TisU_t74C7EABD355F620088583D354F66E077F74C2116_m601210D03B7815AC3291C90C7517E1FCEB210612;
extern const uint32_t g_rgctx_TU26_t779007D9180A2A29356A0CA9A3C8EC3D3A235EEE;
extern const uint32_t g_rgctx_ILSupport_AddressOf_TisT_tCF8995A642FF6C6B39489CC54F7B7FB179DD8EB7_m61251B4786FAAD41BD9A3A4555501786A9759290;
extern const uint32_t g_rgctx_TU26_t84606A3239A824806232207FA6AE9AC85624866C;
extern const uint32_t g_rgctx_ILSupport_AsRef_TisT_t1AD28EB9FB4F1EE8B862A47B00FCA3990142BEFB_m71FC67ABC68632F08CDF5CCB9EA51989EA7F388A;
static const Il2CppRGCTXDefinition s_rgctxValues[504] = 
{
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_JobBurstSchedulableProducer_1_t807BFFB05BF467E224E1D425614BBCA588C818B3 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_JobBurstSchedulableProducer_1_t807BFFB05BF467E224E1D425614BBCA588C818B3 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_tB77BC8EFEA441BFECEA21B20FB0C7ABD6C25411E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_JobBurstSchedulableProducer_1_Execute_m879EA138218416A56B7DB8C9C265F54900D1F55B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ExecuteJobFunction_tDA46B463559182B8F35EAC17AE60229B15053091 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ExecuteJobFunction__ctor_m4058A5D6E5DEAADD18E9C7905AF10EFA2DE99D2D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_t3B82C136947F56CFEAB4E457649FA82404C21226 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tB77BC8EFEA441BFECEA21B20FB0C7ABD6C25411E },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_tB77BC8EFEA441BFECEA21B20FB0C7ABD6C25411E_IJobBurstSchedulable_Execute_m046908BA556F2FE6BB6DC56E1DCDF638230CC017 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SharedStatic_1_GetOrCreate_TisJobBurstSchedulableProducer_1_t807BFFB05BF467E224E1D425614BBCA588C818B3_mE6DAE2C1EA987EB6CAB761950CDEAC9BD79171AA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_JobParallelForBatchProducer_1_t227C3ED15302FCF56720933CC9ED01F1807727CB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_JobParallelForBatchProducer_1_t227C3ED15302FCF56720933CC9ED01F1807727CB },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_tB969F05B0CD912E344A856A15AC4FF5A2A2EB0F9 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_JobParallelForBatchProducer_1_Execute_m817E0EF3E204CC26A45EC7516A93EB330D5D148B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ExecuteJobFunction_tD9EA28CE05E92454FAC0C7100EC4C5420CD26EFE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ExecuteJobFunction__ctor_mF717EEE303E3EFD813FD7CB5A5FBF370C11FEA92 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_tE2A631B8DDEA8ED98F28DF3B218A02DAD9DF5721 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tB969F05B0CD912E344A856A15AC4FF5A2A2EB0F9 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_tB969F05B0CD912E344A856A15AC4FF5A2A2EB0F9_IJobParallelForBatch_Execute_m3D4D3AA382FCE785E70AE39C51977CDEE30A4AAD },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SharedStatic_1_GetOrCreate_TisJobParallelForBatchProducer_1_t227C3ED15302FCF56720933CC9ED01F1807727CB_m77724EB2258BEEC7745B4C05BFAD7B8586DFE951 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_JobParallelForBurstSchedulableProducer_1_t5D4B903F8F053EAECBCEA0C62A94902B23317F2A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_JobParallelForBurstSchedulableProducer_1_t5D4B903F8F053EAECBCEA0C62A94902B23317F2A },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_tF17783746DBA93934E99516BD05AC18D2442D7D8 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_JobParallelForBurstSchedulableProducer_1_Execute_m623F0AE11AFC90F9B4DA37310451A82D77E2C222 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ExecuteJobFunction_t941ECECD0F514B0CE26D421342B2C4D6B66AE596 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ExecuteJobFunction__ctor_m368E653FE4364EBC693F02BD4F3AC6F38C0FA853 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_t504BFF71026C247F4AC9CF960F16C0D2D14ED9E0 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tF17783746DBA93934E99516BD05AC18D2442D7D8 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_tF17783746DBA93934E99516BD05AC18D2442D7D8_IJobParallelForBurstSchedulable_Execute_mAF8CD68C26B4342DB0BED65AD2243EF9DC49B5BA },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SharedStatic_1_GetOrCreate_TisJobParallelForBurstSchedulableProducer_1_t5D4B903F8F053EAECBCEA0C62A94902B23317F2A_m6E5873E257EE8175DA199F939CB8E7E83AD71630 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_JobParallelForDeferProducer_1_t8FC16D1D487523A5ED99C85A5C284F108F90CA11 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_JobParallelForDeferProducer_1_t8FC16D1D487523A5ED99C85A5C284F108F90CA11 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_t0E9646E87C2BEA97DF729E8ED1A8EC11C1D86830 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_JobParallelForDeferProducer_1_Execute_m40F2898B9BBC65421DAA4C8EFF5B1167B73E3E88 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ExecuteJobFunction_tA4B289FCD385EE466E2B78AF7914A1AD8C091CD1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ExecuteJobFunction__ctor_m30C02A17ED242F0A734C250D99A27A06969B92A7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_t9388D6AF5E64869FEBD3E5F64B638D362A6AA938 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t0E9646E87C2BEA97DF729E8ED1A8EC11C1D86830 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_t0E9646E87C2BEA97DF729E8ED1A8EC11C1D86830_IJobParallelForDefer_Execute_mBDEC705B03843374F7054ADE9F5C17E6D9E1F548 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SharedStatic_1_GetOrCreate_TisJobParallelForDeferProducer_1_t8FC16D1D487523A5ED99C85A5C284F108F90CA11_m76C60C3BC9C7050B21BB07B57814F04CCCF68C3C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_JobParallelForFilterProducer_1_tC0AA381987A1C3A449C603D0458DFB4F6CD82348 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_JobParallelForFilterProducer_1_tC0AA381987A1C3A449C603D0458DFB4F6CD82348 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_JobWrapper_tC8BF1AA504B165DC28D4AB1AE44B7DF4897727C2 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_t9AEF47AD0E7A2ED86185872C27E145723CB2BAAB },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_JobParallelForFilterProducer_1_Execute_m6F6CF78A4A9CE56E6EE18C2DD3BD667C03B655D9 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ExecuteJobFunction_tF85C2AA1A933A69C8BAE8EAF0A32A4B8EDCE481D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ExecuteJobFunction__ctor_mE284472F7437B7178B3F2D367384D3486478C8E2 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_JobWrapperU26_tB111F1AA1C3DA771E0A0F1E147C4F1FF0B247551 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_JobWrapper_tC8BF1AA504B165DC28D4AB1AE44B7DF4897727C2 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_JobParallelForFilterProducer_1_ExecuteFilter_mA059543644262A3A1A79CE40107632F928ED163F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_JobParallelForFilterProducer_1_ExecuteAppend_m95CDA3628147D8ECBF9E92CC30DFCEF27E47917F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t9AEF47AD0E7A2ED86185872C27E145723CB2BAAB },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_t9AEF47AD0E7A2ED86185872C27E145723CB2BAAB_IJobParallelForFilter_Execute_m6C099604B0837398A4EE9270286183229B2DC4C8 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SharedStatic_1_GetOrCreate_TisJobParallelForFilterProducer_1_tC0AA381987A1C3A449C603D0458DFB4F6CD82348_m8681B03CCB56B3965FA409845AF69B4372CBB27A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_t5335F97D02534CC2D1EFAD2B1B8CE794A459864F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t2BD6686FA9DF7A772BB80D160D1EFDB90CDC262F },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_t2BD6686FA9DF7A772BB80D160D1EFDB90CDC262F_IAllocator_get_Handle_m54F384A4E6B7365849D3462AA309C77F8D9E9E6C },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_t2BD6686FA9DF7A772BB80D160D1EFDB90CDC262F_IAllocator_Try_mDD961D5AECA15763EDDE7F745C825C7CD5D7FAB2 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_t5CEA5E60B9EC484E5F0A5233B0FE6573F8FD4204 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AllocatorManager_AllocateBlock_TisT_t4B6526BAD6B8C750196E96687551E6FB248D8B93_m20F711BEC9AE42C4FA04E61E37EDAE5F8E13C2A2 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_tF4435F8B669166A749CCFA3D5E9E46AAD064E2CE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_SizeOf_TisU_tA9035EFB2BDFB918891BB54C12A40A61E813D1AB_m147F0BD45B75434C545EA473E1C858F26B10C11C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_AlignOf_TisU_tA9035EFB2BDFB918891BB54C12A40A61E813D1AB_m21F060306B883FAA6844D2D92424AD0C418E7FB8 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AllocatorManager_Allocate_TisT_t53C98BE980141A98061F7E0C00F1E53863D49172_mCE845A9B9E6485B0D134D2578EAAB89D9FB73D29 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UU2A_t486C2DB331AC02939CDB4420494EF6926230852B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_t3F3F4F77ACB3DE60F9CEA6D14439ACADFACF96A9 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t675DECA271C35699FBAD03C9D9F04E758C9964B7 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_t675DECA271C35699FBAD03C9D9F04E758C9964B7_IAllocator_Try_mDD961D5AECA15763EDDE7F745C825C7CD5D7FAB2 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_t7F0CFCEFF9DD94FC01C07F1F29EF14CBE94EFA6C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AllocatorManager_FreeBlock_TisT_t35BD07ABBCB8D61BAD11D72A4D6D6D997BD815DA_mCFAB24856A7C4B600583475999609C1BDE147617 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_t512535147A2E70989C9FED965A59897CB227A3CB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UU2A_tFEE847BDBB617FAF279654649190AF314B52F7B1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_SizeOf_TisU_tE686974E7BAF50725A50289F9FE1E4DAEB20FEB9_m08C7637594479E2DE074EBCB3AB56DE38E47F0EA },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_AlignOf_TisU_tE686974E7BAF50725A50289F9FE1E4DAEB20FEB9_m54C6FF17733951B3182314D7A7392CAF02AE8CBE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AllocatorManager_Free_TisT_tB5F0204FCE510FB4611F370EFC46DA8C45DC09AF_m866B5AC4270563CCF787270E884E9ADB696947CF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU2A_t329EAE82F86B22F9B6C69972AF45D5E40392CCEA },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AllocatorManager_Free_TisAllocatorHandle_t3CA09720B1F89F91A8DDBA95E74C28A1EC3E3148_TisT_t570532536E3FD3B2205FD25800E5A7DAFDA40675_mC0583857F21D37F314ADCD109E5E9DD8244E4792 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_tA137A5623086388A54F6037F4EDB41EE5DDD2361 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t13CE2F434A3C4FCBF90D09B6D327955FFBC3EB93 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_t13CE2F434A3C4FCBF90D09B6D327955FFBC3EB93_IAllocator_get_Function_m08C0302F73788529C636BB23E001A5C4AB12D855 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_AddressOf_TisT_t13CE2F434A3C4FCBF90D09B6D327955FFBC3EB93_m50ECE878124DA46DE8E44114F3BC466694A10956 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_t13CE2F434A3C4FCBF90D09B6D327955FFBC3EB93_IAllocator_set_Handle_m368F9B6297C44D2DE14CA5C0E91DEB802EA17FA4 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_t13CE2F434A3C4FCBF90D09B6D327955FFBC3EB93_IAllocator_get_Handle_m54F384A4E6B7365849D3462AA309C77F8D9E9E6C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_t1B60EBC063E5737EFCF0E54CFB7DE9BD575C2310 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tC5904493B68D4604F7E9591EADDC6FC6BAA9B920 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_tC5904493B68D4604F7E9591EADDC6FC6BAA9B920_IAllocator_get_Handle_m54F384A4E6B7365849D3462AA309C77F8D9E9E6C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_SizeOf_TisT_t65A6261CB8536FA6F17465CEC570EE891D3C5EAE_mE7D528BE5D69AF877DA5B94D0F1CCA8B479DDF1C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t65A6261CB8536FA6F17465CEC570EE891D3C5EAE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_AsRef_TisT_t65A6261CB8536FA6F17465CEC570EE891D3C5EAE_m4F18CE29A94D3F0464849874E1B54EEEF2DD5FFF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_t2BC6DED49F402D06CBCB0B68842F491E5001EFA4 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AllocatorManager_Register_TisT_t65A6261CB8536FA6F17465CEC570EE891D3C5EAE_m0D8A28412B9296CC4C08ECFFB82EC1AEB9033196 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU2A_tC7D7997E9D0BA6074EFA93C6146B1D22BA65061F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_t6A57FD6864FADEB6D69862F7988E43A7537EBB21 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AllocatorManager_Unregister_TisT_t85CFC1E553C1D15A35CD39A7416656AC773670D4_mE11A8460609FC9E5965B5A515E87E9E5742F272A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_AddressOf_TisT_t85CFC1E553C1D15A35CD39A7416656AC773670D4_m986B3DC8C3E085ED3B9650886F25C4A8F2F4DD5E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Array32768_1_t27311415036D8E10790953E71D1B37720B017554 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Array4096_1_t44B5406E19508C8BD82A7FB15B4FAB261D6376F7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t2B0E445D8A10145BC0D27189CC7F23CC9EF95B34 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_AsRef_TisT_t2B0E445D8A10145BC0D27189CC7F23CC9EF95B34_m224DE97901461C7EFDC5FE9F0AC057A7815ACC92 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_t17FDE0F88AA456BDCCA436101E8DF16EEE82EC17 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Array4096_1U26_t538F10C46BA0E53BCE3887D97DD2B3549A3250DC },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_AllocatorHelper_1_tE5DDB49FAA0264D8AB1F8057B3CEDDB64106AEC3 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU2A_t85D946E3A77B2225D4E097025A50E004270E532B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_AsRef_TisT_t64A38344CCB5CDCBADEFC830C652C146C6A3E9ED_m0C04840C181A4834CB822F7CDD4DF405273C8954 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_tE3B78B500D5929085F9003DC18DB9FF8E0EF9A4A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AllocatorManager_CreateAllocator_TisT_t64A38344CCB5CDCBADEFC830C652C146C6A3E9ED_m5D663E10F521B580FCC083A9A401C62A04C1EFBE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_AddressOf_TisT_t64A38344CCB5CDCBADEFC830C652C146C6A3E9ED_m6FD6C369475C6B7FA07BA86E21D7D9403CA8ED85 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AllocatorManager_DestroyAllocator_TisT_t64A38344CCB5CDCBADEFC830C652C146C6A3E9ED_mC315ED9EE74F31D3822486E65968D1309AFAC6B6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_tD30ADC83B93D29FA4F7D798411082A266C2DA4B7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t01EB2D6CAA4115BC3BE45DD7F4B9EB8161A098EC },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_t01EB2D6CAA4115BC3BE45DD7F4B9EB8161A098EC_IIndexable_1_ElementAt_m1CBBA2EFCBBFAC5FB938033CB43FA44E7560A939 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_tFC80F75B523AF31493BF95411C8EE31987E3A9C2 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tCB4A169961AE49E9EC3BD91E800D851654510C55 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_tCB4A169961AE49E9EC3BD91E800D851654510C55_IIndexable_1_ElementAt_m1CBBA2EFCBBFAC5FB938033CB43FA44E7560A939 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_SizeOf_TisT_tA5912E8646DA88E85E66D08CB0327BAA5E6B0184_mA7BC1C40589A53585C6F00F43E6B2FF9C40DBE3B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_SizeOf_TisBUFFER_tFD40F1306B645EA97CA83B52D2CB6CD3FEEAC54D_mF155F456885915E1F3C70F273A8C897F07AA7A9F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList_PaddingBytes_TisT_t2D6B0CCB470769484641DD2BFB5115894D099F35_m7EF6A496BA6072238092F45DB16A779E764F5489 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList_StorageBytes_TisBUFFER_t73A4AA1DE43400A5108AF898EA2ACE7B858658C5_TisT_t383CE4675CD7273C5AE8AE0CE56DD3BD03C553EF_m916CAB4FC681EE3D68EF2DB793A2E9491D2231D4 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_SizeOf_TisT_t383CE4675CD7273C5AE8AE0CE56DD3BD03C553EF_mB419A365B9AA116F362278FF24992D7C8D54CEC7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList32Bytes_1_tD2F19E017C9CF5DC6CB5780CDA882089FFE4B99C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList32Bytes_1_get_Length_m09C6C267D9BAF792F6EE22EE3EBFFA6DF40A1AF0 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList32Bytes_1_tD2F19E017C9CF5DC6CB5780CDA882089FFE4B99C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_SizeOf_TisT_t604DA2B2C61AC824178F38507FE80321D55C1110_m1CA32CEFE1BB9A05D042A6414E2C873CFDDB24FA },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList_PaddingBytes_TisT_t604DA2B2C61AC824178F38507FE80321D55C1110_mF9274D4AE2A751E849E75F7FF15624553335C9E3 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList_Capacity_TisFixedBytes30_t7721F11929A3AC08287DF5E6D7AEF85CCEE04AD2_TisT_t604DA2B2C61AC824178F38507FE80321D55C1110_m86A556E7807973A99C024DC03F74323C88AAE239 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList32Bytes_1_get_Buffer_m8C59DC6D188FDA6852C8E1DCD65467F24AB6C6FF },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_ArrayElementAsRef_TisT_t604DA2B2C61AC824178F38507FE80321D55C1110_m29A459479200E2961AA8D4F48BD7787ED3F84E15 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_t42543BA455A05A5D15090F8CFE8568C7F4A572B1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList32Bytes_1_get_LengthInBytes_mF4D1CE0CFE876F4AEA6727E2F9DC3E9ED4C6F49F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t604DA2B2C61AC824178F38507FE80321D55C1110 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList32Bytes_1_CompareTo_mBBBC65A417BA1B4D27BD77E11B44DE17E4AE14EF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList64Bytes_1_tC4ED4AB3E4EEE98679B1412FB4C80C3E4FA16834 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList64Bytes_1_get_Length_m927C78B4D944D4E876AB25A3E7AB4F6DFAD4F108 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList64Bytes_1_tC4ED4AB3E4EEE98679B1412FB4C80C3E4FA16834 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList32Bytes_1_CompareTo_m6290A920F0B1C958BBD7B60BA6F94FD3164B806A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList128Bytes_1_t97E27D9873786614696ED6CB6B7DABF2AFF7DCFF },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList128Bytes_1_get_Length_m07F322CB13F58D937FC51D44F14EE31B19067359 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList128Bytes_1_t97E27D9873786614696ED6CB6B7DABF2AFF7DCFF },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList32Bytes_1_CompareTo_m6642F674EE22AC655E358EF533B0F700C16A0A94 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList512Bytes_1_tE0BA0381F29B58D8A0C87FD4F2A4E8DB45DFB48F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList512Bytes_1_get_Length_m1CC3012C703D85C7C951AE77DD2B3F5B8E42E918 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList512Bytes_1_tE0BA0381F29B58D8A0C87FD4F2A4E8DB45DFB48F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList32Bytes_1_CompareTo_mFAF2429BB4895D0C3AF7C5B981A3C53F440BE6A5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList4096Bytes_1_t0629EA7E8B063F8817ECAE15F9A75E6F61CA42F0 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList4096Bytes_1_get_Length_m29F0A70AA1DAC8624B99E5E213DC4E34036BBF0C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList4096Bytes_1_t0629EA7E8B063F8817ECAE15F9A75E6F61CA42F0 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList32Bytes_1_CompareTo_m21876ACD769677EB12796837397549EACDC6DA1F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList32Bytes_1_Equals_mA9B2F154A856E8EB9B4983E2745FD077BA0D98C3 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList32Bytes_1_Equals_m496CF2C8A1FAFE63A285C2350D9530A60CC9B463 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList32Bytes_1_Equals_m3B2C3C3508065763A6DEE8185CD3507C80EE2159 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList32Bytes_1_Equals_m8812C7F42A79683AC17FBC09A7F04E5E909E3A67 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList32Bytes_1_Equals_m7701FD9664F730DE055F5A80657EBC6BF96BB399 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerator_1_t864132BBC4D6B1C046B0F3D7F2450911AEEE1A9D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList64Bytes_1_t01290055D2315B7FB797137C2585E1D2A8FEFC93 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList64Bytes_1_get_Length_m904CEC4D13DAB3EC2E63867290A4919B3EE07B94 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList64Bytes_1_t01290055D2315B7FB797137C2585E1D2A8FEFC93 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_SizeOf_TisT_tD32C0F6209442C9BB65A030D29698147784945B7_m9AEE49A539D4229D6C082363CC4750BC3C7BD959 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList_PaddingBytes_TisT_tD32C0F6209442C9BB65A030D29698147784945B7_mBEB73D718598A132E3FB38993715F6580F14AAF4 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList_Capacity_TisFixedBytes62_t25CC23B7A3CF922DF0D1F0BFD5F801864D4FFD2A_TisT_tD32C0F6209442C9BB65A030D29698147784945B7_mBFE2EB1E8054BA05932A224E8E5B0B172C76A4E2 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList64Bytes_1_get_Buffer_m4ACFD76E5BAB7BBA3B105EF045FB34DF16925121 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_ArrayElementAsRef_TisT_tD32C0F6209442C9BB65A030D29698147784945B7_m578879984B9A4BA26E7A659A8E3BC67397790111 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_t376B70AA61950DE5C43FFDF0193DB6362FC21607 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList64Bytes_1_get_LengthInBytes_m830026A47AC35E78ACFB4ED8613C4241631C7FB3 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList32Bytes_1_tF36F3420B2D85BB09FDA7CBE0B0A59127E7E47B5 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList32Bytes_1_get_Length_mDC309D74DBDE3857D8CF451A6C61E4DF244DB906 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList32Bytes_1_tF36F3420B2D85BB09FDA7CBE0B0A59127E7E47B5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tD32C0F6209442C9BB65A030D29698147784945B7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList64Bytes_1_CompareTo_mFFA2BDEF1D5931103F2349F121D89B9EFDA18B2A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList64Bytes_1_CompareTo_m1B404F21933A0C0E3F4EDB9BDC56FF1374BA36BB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList128Bytes_1_t90FF12D54923E198013051CB940E9B9C7747AA0E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList128Bytes_1_get_Length_m913F6E9C36DA6E5150201BB04705176C61779A4A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList128Bytes_1_t90FF12D54923E198013051CB940E9B9C7747AA0E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList64Bytes_1_CompareTo_mAE605CEAEF66638FB3772258684647B86795A026 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList512Bytes_1_t5A455036DE64718823C34266F43B4B930819650E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList512Bytes_1_get_Length_mB4AC876C1C0A6D784950D1DF9D938E8187CC254A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList512Bytes_1_t5A455036DE64718823C34266F43B4B930819650E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList64Bytes_1_CompareTo_m709B483E5DEB7FB21FD73055E1D18EFC77CB8D94 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList4096Bytes_1_t140F024B8BA0DDF7C9A41DFDFE78C7BEECDE6355 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList4096Bytes_1_get_Length_mA84088FDB735E31D8EC68ED7C7241B82F7E382E9 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList4096Bytes_1_t140F024B8BA0DDF7C9A41DFDFE78C7BEECDE6355 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList64Bytes_1_CompareTo_m72AEE1D5D4DD2A52568A3DB0395D4C6A4450D662 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList64Bytes_1_Equals_mFA63385DF5A53E2D34A838F6B4D3E5EB93454707 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList64Bytes_1_Equals_m4A58B7E48EC3D1CE4B417CC606F7AF283179CE19 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList64Bytes_1_Equals_mD426D7F36DA3313161AF4ACB1D163554927B2B5C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList64Bytes_1_Equals_mA3CB277239697E135B80B1652428B650E393E3EF },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList64Bytes_1_Equals_m676325C656968A3297E0312C5E86C06A021B6A7A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerator_1_tB69EBF70B6FD3D2A8F0F87587EDB9568DE58455A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList128Bytes_1_tD5CBB33E81228C7A982A8BB36F4C59C6886B72D2 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList128Bytes_1_get_Length_m0455E51110778F6A133DB6106D4F22A64B989348 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList128Bytes_1_tD5CBB33E81228C7A982A8BB36F4C59C6886B72D2 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_SizeOf_TisT_tF637D573FAC60604ECC747D4E3EAC341C5AAEF30_mDFFBC1AB4195A6724110DE5980F5E23E6FFBD712 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList_PaddingBytes_TisT_tF637D573FAC60604ECC747D4E3EAC341C5AAEF30_m69D18CFEBEB2907DF3FAC8CA19E77BE6A657316C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList_Capacity_TisFixedBytes126_tC223222E11A3E93A15FE1C62C3429FC169DBC989_TisT_tF637D573FAC60604ECC747D4E3EAC341C5AAEF30_mCCBFF245343A7B36B9527CBA6D9741B77826F0AE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList128Bytes_1_get_Buffer_m97D30707BB2AEA2F5DBADE3B0FAC8F672E8B1A3D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_ArrayElementAsRef_TisT_tF637D573FAC60604ECC747D4E3EAC341C5AAEF30_m3AC258853E94E1A3A82824C76E3015F3DFDA8ECC },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_t1F0419C99A901A588CC96A5B77396D184C4FD8AD },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList128Bytes_1_get_LengthInBytes_m47F607A647F86AE5CEE40BB1760159288C68D0BB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList32Bytes_1_t3C413D65609876A5C74284892DEE5A0D625FB108 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList32Bytes_1_get_Length_mB2C086817BC1745080C475DEF2DC556F31A59D67 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList32Bytes_1_t3C413D65609876A5C74284892DEE5A0D625FB108 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tF637D573FAC60604ECC747D4E3EAC341C5AAEF30 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList128Bytes_1_CompareTo_m2666E22B9261026A0D7D0DDBE9CAF263093BCA3B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList64Bytes_1_t870D9CA977C5EB1DE5E7F03D3EBCDF741429CFD5 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList64Bytes_1_get_Length_m4D4D3FDEAC491E57BF639002CE416C8CAEEAF68B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList64Bytes_1_t870D9CA977C5EB1DE5E7F03D3EBCDF741429CFD5 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList128Bytes_1_CompareTo_mE9957B37908F02DBF2C0FBC8A01F575132266E51 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList128Bytes_1_CompareTo_m4C2FEAF12A383067066580E5AE042BA1E5E15353 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList512Bytes_1_tC3D869C52DA776B0E1C9C381FB198A6197EACD87 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList512Bytes_1_get_Length_m660F2F29958AF880448E6418281CCCEB44F5B7D4 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList512Bytes_1_tC3D869C52DA776B0E1C9C381FB198A6197EACD87 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList128Bytes_1_CompareTo_m437197F791AB58DC99F09DD25DFF430B2F4CFA16 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList4096Bytes_1_t416541239A6A63A742AD7E9584232B8BAEC559DD },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList4096Bytes_1_get_Length_m9A5EEBCAC9EB81EA90B9BD7FFB53C8C16795D572 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList4096Bytes_1_t416541239A6A63A742AD7E9584232B8BAEC559DD },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList128Bytes_1_CompareTo_m255D7C47599B63B530DEF04B43CD7223D203C6DC },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList128Bytes_1_Equals_m086E1CCF2A8A8917B11F2B64CF7D29C6EE148120 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList128Bytes_1_Equals_mBF301D370C792F53697A10E96FFDC089205671AE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList128Bytes_1_Equals_m2CDB0F15DB14C27621333422EF9796AD3D031766 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList128Bytes_1_Equals_mB57417246E4D25627832638BE91CB141FDF04EA9 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList128Bytes_1_Equals_m23DA07023310D345AD86B3B55A0D2AA231BD7788 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerator_1_t37AA14CD4097DAA4BDEBD3F3465239E439BF2709 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList512Bytes_1_t669FFE3EF2D7972808F85F458A97B36D3B08A302 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList512Bytes_1_get_Length_m0B248C6A39E59A7CC633B8EBB0E2214A7374A24A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList512Bytes_1_t669FFE3EF2D7972808F85F458A97B36D3B08A302 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_SizeOf_TisT_t2DEEC1EDE9244B8523CA96477075BECFEDDEB70B_mFAB9277A26B57EEBBD96FBFF9A8E348CDF0F3695 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList_PaddingBytes_TisT_t2DEEC1EDE9244B8523CA96477075BECFEDDEB70B_m84508C7415A499FE729E49407F30491D8BA1347B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList_Capacity_TisFixedBytes510_t95B284C3FF966246998B23701C3F0F55C6BD7973_TisT_t2DEEC1EDE9244B8523CA96477075BECFEDDEB70B_mFF48156C860043812A4CE8D19432EC0D7A50E568 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList512Bytes_1_get_Buffer_mD366D995A0996A4941BFD8FF7751F8888529019A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_ArrayElementAsRef_TisT_t2DEEC1EDE9244B8523CA96477075BECFEDDEB70B_m636A8EC5CD9E4CC03E218C231B2F573944500D88 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_t82245BF48873F403E3E9F1E66A5BD1770097F0A9 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList512Bytes_1_get_LengthInBytes_mE533117FD90EE225AB1657584FE15D9FCD3B31F6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList32Bytes_1_t16FDC26837A35E8C9EE737B47C5566926DD97D40 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList32Bytes_1_get_Length_mFEB57847565241DE5AC20F3C47DFB1C1FBD77D42 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList32Bytes_1_t16FDC26837A35E8C9EE737B47C5566926DD97D40 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t2DEEC1EDE9244B8523CA96477075BECFEDDEB70B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList512Bytes_1_CompareTo_m2ED44CE82345E495B02845190DC950E9369F9B66 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList64Bytes_1_t692D679ABC650A6E1D639F9B704EBF1C015B7E54 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList64Bytes_1_get_Length_m0E56845145169510605A77387713117B1DF42A2D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList64Bytes_1_t692D679ABC650A6E1D639F9B704EBF1C015B7E54 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList512Bytes_1_CompareTo_m7D404D1A8DCDBFBA6E77CA9A1BAE087DA8BEDB45 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList128Bytes_1_t855A409C9DAE10C3B3F003764F360E082BE304C1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList128Bytes_1_get_Length_mC45C102EEA509426C4E25123ACB537AA2E1C8B53 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList128Bytes_1_t855A409C9DAE10C3B3F003764F360E082BE304C1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList512Bytes_1_CompareTo_m0BE3A1FE674C670F1F163603702EA2EC121C96C0 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList512Bytes_1_CompareTo_mCB87559DEEEFBF43BBE5EDA844844CCB64B12C06 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList4096Bytes_1_t30D9953988646BADE87AE3D019C1B0BB523F33B2 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList4096Bytes_1_get_Length_mAFB31975A1194980FE703C4FF6157CA124568175 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList4096Bytes_1_t30D9953988646BADE87AE3D019C1B0BB523F33B2 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList512Bytes_1_CompareTo_m71F5DF9EF41C7A4B6C05EDC77E4AEBD567829F44 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList512Bytes_1_Equals_mFEFB3BF366F96DCEEF8CF0F400F6F6BC865C4EFA },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList512Bytes_1_Equals_mDC81D66D0B81AB0FEBD1F8666E2BB7783FA5ACB8 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList512Bytes_1_Equals_m2A8267A3F7D08EC772B96BE833442B1E5887C72E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList512Bytes_1_Equals_mBDE871330097648822A6964D577DA54C9C62CDE1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList512Bytes_1_Equals_mDAE776DA9235ED2BD2A75CD0AC265DB954F739E3 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerator_1_tFF57FF7FE99225B911B779420D0023CC4CC8BEBD },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList4096Bytes_1_tDD8407366F29BD7A6221ED46D0353F2A5DEB8D87 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList4096Bytes_1_get_Length_mF981B72DCECDB30EBED5ACCC7749B57FE4D25643 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList4096Bytes_1_tDD8407366F29BD7A6221ED46D0353F2A5DEB8D87 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_SizeOf_TisT_tBD073BADADC7386EA705AE4342916B0F07BDACDE_mDF9CEBCF3A941F23B144FCD20FDA1D094151485F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList_PaddingBytes_TisT_tBD073BADADC7386EA705AE4342916B0F07BDACDE_m866B91A370BB67C092A4B0E5D72A7734F2132F73 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList_Capacity_TisFixedBytes4094_t8611441D8BDC6A677C2D9E551086F59EFBFCBBE5_TisT_tBD073BADADC7386EA705AE4342916B0F07BDACDE_m365CB7E2D2385EF3C160C60E268D84639982C57A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList4096Bytes_1_get_Buffer_m8C59032AB0882881E308B5310103C4C0C67FE527 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_ReadArrayElement_TisT_tBD073BADADC7386EA705AE4342916B0F07BDACDE_m4521B626F59BBB2321D000869DFCBDC991657E02 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tBD073BADADC7386EA705AE4342916B0F07BDACDE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_WriteArrayElement_TisT_tBD073BADADC7386EA705AE4342916B0F07BDACDE_m7E4C5808A41558EDB297D03CF1DE6FE52199AA57 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_ArrayElementAsRef_TisT_tBD073BADADC7386EA705AE4342916B0F07BDACDE_mB26498A653C7446BD4BC84B57EFA5CA41BF14D3B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_t0E80504B7A1160E9950B585D6C82FDEC052FC737 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList4096Bytes_1_get_LengthInBytes_mDD272B4890655CD06ACF62B312C72673B7066DAB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList32Bytes_1_t7AAD6CEC30726A322BBECE5EF36547B20FE7474E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList32Bytes_1_get_Length_mE8F8A185DE5BB8CB0BEE9383F387044EC232D30C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList32Bytes_1_t7AAD6CEC30726A322BBECE5EF36547B20FE7474E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList4096Bytes_1_CompareTo_mDCA60AD837CDD603A14317382F8FE7B7A5332496 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList64Bytes_1_tA47527956352165EF643C47FD1BC7C6169E29EBC },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList64Bytes_1_get_Length_m6B94FC005EB3D868CEC8B0A1A2953929BDB403B9 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList64Bytes_1_tA47527956352165EF643C47FD1BC7C6169E29EBC },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList4096Bytes_1_CompareTo_m1EE2D74734AD460BFAC1124BEC50F6A7242C157A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList128Bytes_1_t79918BA354DF7C9EED2BB2E611792E496C73C747 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList128Bytes_1_get_Length_mBEFA2EA7F7D0BDBF7900A55AD4870026F5CDD9E0 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList128Bytes_1_t79918BA354DF7C9EED2BB2E611792E496C73C747 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList4096Bytes_1_CompareTo_m76A8C7244A78E281DE7D75356C8C98452CC9B973 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList512Bytes_1_t3AB2AD5617F05D853E1819914CDD7FE040D0A863 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList512Bytes_1_get_Length_m0FB45017FC642488814B76ED2D5856074D0A2C13 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FixedList512Bytes_1_t3AB2AD5617F05D853E1819914CDD7FE040D0A863 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList4096Bytes_1_CompareTo_m7B2C705430CA47FDBA89E556A4BA14F10B5E62D6 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList4096Bytes_1_CompareTo_m48E8E70F5EC08F0B2764E1FA714A0E6B42AD79B3 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList4096Bytes_1_Equals_mA097DF642E512753B3F3819A63A991273FB15433 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList4096Bytes_1_Equals_m3F25D402683E10A10CC6E899C3C5F3242C07DE65 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList4096Bytes_1_Equals_mA57897971800CECFA745EAA611995A129370DE0E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList4096Bytes_1_Equals_m234039E2C015B564CDC7962B8ECA3F6161456BC2 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedList4096Bytes_1_Equals_m79776FC49E1BE2ECE4CD5A12AB1AD9E67199A5B6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerator_1_t3529DEFB532FA3B3FC1BECA7AE3AC6C2626EDA06 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_t84A5DDF8149612DCE2DA99F2960BEFFAD48DC63D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t128D4A74C38857567A2488C6CD7C1BA749677358 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_t128D4A74C38857567A2488C6CD7C1BA749677358_IIndexable_1_get_Length_mAED3EB7DD75EF7C1E601F6DEA790BEF4B7C8E2EA },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_t128D4A74C38857567A2488C6CD7C1BA749677358_IUTF8Bytes_TryResize_m42B6D259A45BC13F377FFA6A77393E1B89319CE0 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedStringMethods_Write_TisT_t128D4A74C38857567A2488C6CD7C1BA749677358_m5B6E47CFAA22B1EB4E5C1C39A067F65B5B48DFA1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_t97334FE432BBD59A7F23BEC80DE5A19A87EF2930 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedStringMethods_Append_TisT_tCACCA4E422C4855D9ED402558E999CB0C2BC720F_mEFC58699E2B44FF5B958EF465B6A020B2273864C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T2U26_t25A760909D994F3717596C4EE57CF6DA5115482E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtilityExtensions_AsRef_TisT2_t32EA74C6914E6CF63B14ADAE219B21DE90DE3FDC_mEACB25F0892A8D2C1017914E9BC224F8AF8A594F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_t30B77F17623009AE6EF8E22BD2ACB710E4E5EF99 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T2_t32EA74C6914E6CF63B14ADAE219B21DE90DE3FDC },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T2_t32EA74C6914E6CF63B14ADAE219B21DE90DE3FDC_IUTF8Bytes_GetUnsafePtr_mB67E683E33AA89AB66BB1187E74C99A53EFEBED1 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T2_t32EA74C6914E6CF63B14ADAE219B21DE90DE3FDC_IIndexable_1_get_Length_mAED3EB7DD75EF7C1E601F6DEA790BEF4B7C8E2EA },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedStringMethods_Append_TisT_tAF666238E844771565195E661F0E174E5E0F1867_m66FD19C64582D5837D91409D20FCDECDC11D875A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_tAB160D02D431E15E17E0AC54B4736953CEC6732E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tACE9618446D04FF75CD0C81073BF6B10B6D3E63D },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_tACE9618446D04FF75CD0C81073BF6B10B6D3E63D_IIndexable_1_set_Length_m0F060BE1FF0E5613E5F666EE4DD8E50303DED483 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T2U26_tF16FED89363541D0C2B680036E195EAA75DF3F54 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedStringMethods_Append_TisT_tACE9618446D04FF75CD0C81073BF6B10B6D3E63D_TisT2_t8C1FFECC052A229017FB97CDACAFAB459238BFFC_m219C6D865291C529BF96B330F00E8AF6A38C6039 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_tE84BE9D9AEC2AE47CF47BCCCC7A0EF8A1402FA80 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t394CB326631A38B224E2EF133552EA72F59A5785 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_t394CB326631A38B224E2EF133552EA72F59A5785_IIndexable_1_get_Length_mAED3EB7DD75EF7C1E601F6DEA790BEF4B7C8E2EA },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_t394CB326631A38B224E2EF133552EA72F59A5785_IUTF8Bytes_TryResize_m42B6D259A45BC13F377FFA6A77393E1B89319CE0 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_t394CB326631A38B224E2EF133552EA72F59A5785_IUTF8Bytes_GetUnsafePtr_mB67E683E33AA89AB66BB1187E74C99A53EFEBED1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_t74AAB1AA331BAE90999602C2F397DC6327DBFF86 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t40CFE80575C0B3A9E97C1A7DB9A1C99154484193 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_t40CFE80575C0B3A9E97C1A7DB9A1C99154484193_IUTF8Bytes_GetUnsafePtr_mB67E683E33AA89AB66BB1187E74C99A53EFEBED1 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_t40CFE80575C0B3A9E97C1A7DB9A1C99154484193_IIndexable_1_get_Length_mAED3EB7DD75EF7C1E601F6DEA790BEF4B7C8E2EA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T2U26_tA3F74A62486E5E3EAD970C766C0485F663622263 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtilityExtensions_AsRef_TisT2_t2BAE730EBE1EFEDF9B51E00473183A8664C69200_m61DFED76CEE5300C00F730592D7972436A60570F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_t3F2BB55E2EEE0143FA2C0FC4240F0323EE14D581 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T2_t2BAE730EBE1EFEDF9B51E00473183A8664C69200 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T2_t2BAE730EBE1EFEDF9B51E00473183A8664C69200_IUTF8Bytes_GetUnsafePtr_mB67E683E33AA89AB66BB1187E74C99A53EFEBED1 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T2_t2BAE730EBE1EFEDF9B51E00473183A8664C69200_IIndexable_1_get_Length_mAED3EB7DD75EF7C1E601F6DEA790BEF4B7C8E2EA },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedStringMethods_CompareTo_TisT_t44578AF0C73EE30B7AE87E0F1E82A995DF6FEBCA_m1F12FF739B81A47680F8F08CA3AB5E03308601C8 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_t602269BCA26C709CF8FE4736B0AC87642EF6243F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tEAAE25CD983326DADA347F015AD559DF085F8D3D },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_tEAAE25CD983326DADA347F015AD559DF085F8D3D_IUTF8Bytes_GetUnsafePtr_mB67E683E33AA89AB66BB1187E74C99A53EFEBED1 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_tEAAE25CD983326DADA347F015AD559DF085F8D3D_IIndexable_1_get_Length_mAED3EB7DD75EF7C1E601F6DEA790BEF4B7C8E2EA },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedStringMethods_CompareTo_TisT_tEAAE25CD983326DADA347F015AD559DF085F8D3D_mE9180BC22FB55220D9A4AFC14ADDE7EB1F87D710 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T2U26_tFB44CF4B01E1A7E5CEE6AB691F5FBAC317469ADD },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtilityExtensions_AsRef_TisT2_tD89AD2D09633D2D7B863E4F80D70D53F51878460_mB0639EB2C8B2C905592E4AFD67D9D20DE1D7D2F8 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_tE5CE21852D897F87CC71A72500485AD5AFB65163 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T2_tD89AD2D09633D2D7B863E4F80D70D53F51878460 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T2_tD89AD2D09633D2D7B863E4F80D70D53F51878460_IUTF8Bytes_GetUnsafePtr_mB67E683E33AA89AB66BB1187E74C99A53EFEBED1 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T2_tD89AD2D09633D2D7B863E4F80D70D53F51878460_IIndexable_1_get_Length_mAED3EB7DD75EF7C1E601F6DEA790BEF4B7C8E2EA },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FixedStringMethods_Equals_TisT_tF099DB17311F45946A995974B24F1AEC8CECC5A5_mB70E43D780615DD0351DBE1D6566297BEDDA792F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_t079B53B1F7BFACF677276B7FB8174797E1FF4E8B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tEA019B08712E2CAE787B21AFA982788BF07899CA },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_tEA019B08712E2CAE787B21AFA982788BF07899CA_IUTF8Bytes_GetUnsafePtr_mB67E683E33AA89AB66BB1187E74C99A53EFEBED1 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_tEA019B08712E2CAE787B21AFA982788BF07899CA_INativeList_1_get_Capacity_mF5B445EAD5F226F5E8C3B2DDF4B8C35EFBB40C4C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_t9AD8DE72370FBE3DDFE640DBB2D54F0447C30046 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tD92B3110D590A5F2CBCE2D8750D7F0E6FB6C1D34 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_tD92B3110D590A5F2CBCE2D8750D7F0E6FB6C1D34_IIndexable_1_get_Length_mAED3EB7DD75EF7C1E601F6DEA790BEF4B7C8E2EA },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_tD92B3110D590A5F2CBCE2D8750D7F0E6FB6C1D34_IUTF8Bytes_GetUnsafePtr_mB67E683E33AA89AB66BB1187E74C99A53EFEBED1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_tD26F5F71F6F190660014703FD83494ECE47EB008 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t0720BD109D0FD7DCF11AF76ED4D7B452221C1557 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_t0720BD109D0FD7DCF11AF76ED4D7B452221C1557_IUTF8Bytes_GetUnsafePtr_mB67E683E33AA89AB66BB1187E74C99A53EFEBED1 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_t0720BD109D0FD7DCF11AF76ED4D7B452221C1557_IIndexable_1_get_Length_mAED3EB7DD75EF7C1E601F6DEA790BEF4B7C8E2EA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU2A_t9675EE6497AD8465FD78590B10D7DF78A42B1513 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Array_Resize_TisT_tA7BC8A9B01B94F56CE3273E1C3F4463BAFDB2774_m40E5359FE293594F47DD50DDB1F2AD213B4A709A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU2A_tA596F7F23DF141DFAF4BC0E1445C816B4C13BFCF },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_SizeOf_TisT_t4439E68F64AF98A1F1E70993ACB3E0626E7A7954_mD44E7FDD63803D509A5BB08B506B82CA121DF38A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_AlignOf_TisT_t4439E68F64AF98A1F1E70993ACB3E0626E7A7954_mD999DEAF969B234226FD5F050A1A8DF99545F7FC },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Array_Resize_TisT_t634E6D84D11B120C1E4D1D5CD66CFD79E344599B_m567688287F6FEC7553806E3C8E440ADD7EECE9EA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU2A_tDF9B3864D56377108BA18F71A274E382B1384EDD },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeList_1__ctor_mFC02027D535FF8FA6DA0D2F3FF28BE9C3C22FA0D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeList_1_tC78A0006334D5DCFB09CF5B4571E681CD20EEDF1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UnsafeList_1_t62C841C6D06562004B617CB8FEB154768A22A448 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UnsafeList_1U2A_tC9B789EAE714A4CB9218D40209373216B4911076 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeList_1_tC78A0006334D5DCFB09CF5B4571E681CD20EEDF1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeList_1_Initialize_TisAllocatorHandle_t3CA09720B1F89F91A8DDBA95E74C28A1EC3E3148_mDA27076ECD04FDDCE4F08EC240944ED85C58EA1F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeList_1_get_Item_m3A75728B303CDC6919167D5BFCD56D9BAE755F4A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t86F43EBF284E6FC6FD87B54AE593FF12A96EACEA },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeList_1_ElementAt_m94F2861AED0CAABDD863BF768BD5B41FE0A10976 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_t9182D13124B0C24A7BE52A270A8164C77416315C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeList_1_get_Length_mBB48D1B7E16C1A3EFC1FECFAE979F4AC003C5BA7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeList_1_Resize_m877B9B1A6AA00562D5D52E78696C5B2364FCB296 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeList_1_get_Capacity_m7CEDBD1C464E131C4BA666F0BE92ED8747465B1D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeList_1_set_Capacity_m3A8F01AD3A516A23455A17E28F75BF7E15624DEB },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeList_1_Destroy_m4365069A2F94BC7B50074F5C3C23A35769947475 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeList_1_Clear_m763F409F9070AAB6B2E20A3952CED497999D10B5 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeList_1_AsArray_mF7F649295B1EB59DCF87D80023CDC9825B535243 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_t7C08CCE83593752FA8281642C4BF74F6C0DB2D2E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UnsafeList_1_t62C841C6D06562004B617CB8FEB154768A22A448 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU2A_tB9A028E4087C5A8ADBA9F589ADEB6159DFEF1E1D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArrayUnsafeUtility_ConvertExistingDataToNativeArray_TisT_t86F43EBF284E6FC6FD87B54AE593FF12A96EACEA_m30203C993DF4EFC52F1B6D61B11B1BE1438DC7A0 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerator_1_tE14F09601BCF464F55C6C384864842F6F442D077 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeList_1_Clear_mBBBC463C45F8766F8D109EFFC6109E5F90C81D1C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_get_Length_mF3D9F277FD8C8586515F953BA049D04561B988F9 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_t7C08CCE83593752FA8281642C4BF74F6C0DB2D2E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeList_1_Resize_m2DA751BFAA461CD57325BF1C6766FBF50AE0E384 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_CopyFrom_m447A2DB30EBA84BF47AA1C10F25B05366B110CC6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ParallelWriter_tA38ACEA2D6CE15CA82A92A21D13E256D2D89ED24 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ParallelWriter__ctor_m91412B47305FD5876EBF9350E5C27BB78704A424 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UU26_t4C94D487822BCBA34B563B36DF016842BCAB0D03 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeList_1_Create_TisU_t8D9513FF7045C2A2417339533F96C0B604BC6DE3_m87141D5D82ED170DBB65F3C443132ABE0C2B143E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U_t8D9513FF7045C2A2417339533F96C0B604BC6DE3 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_U_t8D9513FF7045C2A2417339533F96C0B604BC6DE3_IAllocator_get_Handle_m54F384A4E6B7365849D3462AA309C77F8D9E9E6C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UnsafeList_1U2A_tFD6373CAA08D3C77B8FCA3752E8A2536287E424C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ParallelWriter_tA6D09E1AA1BA56D8C5D80A07AD8B488DDC5179A6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UnsafeList_1_tFD500AA1E7CF0F5EB075E4496F20180D9A1B3F24 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU2A_t20ACD4B7D10D2569904A979745941D3527E3C7E1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t10D58294F47473AB094BD4C41F79FBF65D1CB6DD },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_WriteArrayElement_TisT_t10D58294F47473AB094BD4C41F79FBF65D1CB6DD_mE757F6839718ACD62BC40920203F151319B2703B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU2A_t7B76FD9857804BBE0AA1EE6D0BBB72F763A9CE43 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t70C3EC27D19FAFB4F9747837E54C3FACF3C6965D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U_t5FCBD40D8C549213ECA9F91D7D620CA008DC2F79 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IComparer_1_t08CB26918E5DBADDA160026211F462AC1CA4972B },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_U_t5FCBD40D8C549213ECA9F91D7D620CA008DC2F79_IComparer_1_Compare_m5BA85B80E0E8EC3268576EA6FA38E120947588E0 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeList_1_tC1F6E8D2AB78C5B9F4A995EDACBF910986850EAE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeListUnsafeUtility_GetUnsafePtr_TisT_t87B231C51C2608C7F45DF89DED795C27E1DDBD88_m299A3F9D50195108DA00CC6284750118B13D4280 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeList_1_get_Length_mA1DEC7E32046882769D495AC93C473C6864FB034 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeList_1_tC1F6E8D2AB78C5B9F4A995EDACBF910986850EAE },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U_t716BCA6917D7AE5B382B05528161A14F7E0D2216 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeSortExtension_IntroSort_TisT_t87B231C51C2608C7F45DF89DED795C27E1DDBD88_TisU_t716BCA6917D7AE5B382B05528161A14F7E0D2216_mFCE4F64E4C2A5FD9C512A22F7740762A46CB89BE },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UnsafeList_1_t1E6331DE1C6630D24CCF360565225D04639A0070 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU2A_t0F8FFA0126DBBD3440430D09A66742E016373E81 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeList_1_get_Length_m1AAB4D93F6F40AE5B8305FCC69FF83CA79D16B12 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UnsafeList_1_t1E6331DE1C6630D24CCF360565225D04639A0070 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U_t0522B71478B41B1C4B354E36ABEFBD88E1F2B88D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeSortExtension_IntroSort_TisT_t1D1CAA3797B318EB8CB47A878FC6F982380CD81D_TisU_t0522B71478B41B1C4B354E36ABEFBD88E1F2B88D_m64B56586DB3045B8E0892319D845AAE9FD03499D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UnsafeList_1_t6C67008E2069DB675A532CD0F0C50CA9E2920070 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU2A_t84C85028B3642586C79881AE9DEBF2179510ECE8 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeList_1_get_Length_mF1523912D6DBD8655982A742745D819F240CB8F2 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UnsafeList_1_t6C67008E2069DB675A532CD0F0C50CA9E2920070 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tBE9BDD9637297B0CE397C47A92971E02E573170A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U_t1201D6465E6320EFCBD3E5906FA139D9C48D10A0 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeSortExtension_BinarySearch_TisT_tBE9BDD9637297B0CE397C47A92971E02E573170A_TisU_t1201D6465E6320EFCBD3E5906FA139D9C48D10A0_m84AFBD22CA6B406AE677E7FAFB331DEE57C5B922 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U_t984657F3CA449F6965F5B6A61D21C535A3B6AD2F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeSortExtension_IntroSort_TisT_t92A8A4543C7D53AEF9175B204F0123DCDAF87E3E_TisU_t984657F3CA449F6965F5B6A61D21C535A3B6AD2F_m18B923D57F6D9DC55CE998C45339DF4C2EE55F88 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U_tC691A8B7B753EE7D97DAE216705A19687CD512DB },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeSortExtension_SwapIfGreaterWithItems_TisT_t57C04D0ADE650FA6DFBECBDB0949AA01E83F997C_TisU_tC691A8B7B753EE7D97DAE216705A19687CD512DB_m7F3C5537DB0B6A77BF41CEE7917631D9D0DAE694 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeSortExtension_InsertionSort_TisT_t57C04D0ADE650FA6DFBECBDB0949AA01E83F997C_TisU_tC691A8B7B753EE7D97DAE216705A19687CD512DB_mF336EBAEC2A1CF05C793A6113D42C2A5BE0E4995 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeSortExtension_HeapSort_TisT_t57C04D0ADE650FA6DFBECBDB0949AA01E83F997C_TisU_tC691A8B7B753EE7D97DAE216705A19687CD512DB_mAEDEDE4B2C955D228C634B9350EA10CDC3D89A8D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeSortExtension_Partition_TisT_t57C04D0ADE650FA6DFBECBDB0949AA01E83F997C_TisU_tC691A8B7B753EE7D97DAE216705A19687CD512DB_m4719247E264EC996439C515A1D1AE95021476BDE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeSortExtension_IntroSort_TisT_t57C04D0ADE650FA6DFBECBDB0949AA01E83F997C_TisU_tC691A8B7B753EE7D97DAE216705A19687CD512DB_m8D5EC5A46E31FAD09E85C91CC847E75954473A56 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_ReadArrayElement_TisT_t989AD081463D7816B3C7024B0855781C5E3439A1_mB0E2D4F2916CC3CD670930CEBB2F8D8B7AA8FE51 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t989AD081463D7816B3C7024B0855781C5E3439A1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_WriteArrayElement_TisT_t989AD081463D7816B3C7024B0855781C5E3439A1_mE2E43B49C1356D94BC6F1E2E9AC0800210F808F7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U_t71749F86706A6DD523CBA96FDE0CAFF19DC52F9D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IComparer_1_tF49CABC09B222B7CFBE8189880EEC18D3870444A },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_U_t71749F86706A6DD523CBA96FDE0CAFF19DC52F9D_IComparer_1_Compare_mF4CDE8817BD70F201A31A25EAB6340238960D036 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U_tC345929E416F15F6572F8BE2BAE8C0E611FA26D4 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeSortExtension_SwapIfGreaterWithItems_TisT_t9EF72661B3BD897FB6628CE2E1B95D068B06580A_TisU_tC345929E416F15F6572F8BE2BAE8C0E611FA26D4_mA95426095429FAAC30CA9C60DA7AF506C6D8A6FB },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_ReadArrayElement_TisT_t9EF72661B3BD897FB6628CE2E1B95D068B06580A_mA65DFAF646799C5AC863F87AE40B1E6E4C8773F2 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t9EF72661B3BD897FB6628CE2E1B95D068B06580A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeSortExtension_Swap_TisT_t9EF72661B3BD897FB6628CE2E1B95D068B06580A_m7D5699FA454AB533F2B925275A27B72C4715AF50 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IComparer_1_t7C67406FAD142DA0F7EF144179C9A8F94ED1FCC6 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_U_tC345929E416F15F6572F8BE2BAE8C0E611FA26D4_IComparer_1_Compare_m5B89307628B10886D74690DD3FD4D328B3DCBA52 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U_t96D9FF22A5AC3987AA45E988B0F157C7355E83BE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeSortExtension_Heapify_TisT_t8A1DFC90E7840B7CC11C96F845A4586F9F9CA870_TisU_t96D9FF22A5AC3987AA45E988B0F157C7355E83BE_mB5B997E8D5594C8AE402402B43D586C640B4F722 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeSortExtension_Swap_TisT_t8A1DFC90E7840B7CC11C96F845A4586F9F9CA870_m3D01BA9FE0B8C5662900E64858DA6876E1914CBB },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_ReadArrayElement_TisT_tA43B34C466236E4CF4ACB1D7267181A8DCE8129E_m855A3860C60D5DDB6716348B718BE652EE5CAEE0 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tA43B34C466236E4CF4ACB1D7267181A8DCE8129E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U_t3859302F7BB1D87B147FFF956346B678DE25245F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IComparer_1_tC03C2835996A7D4E10B7F63FAD31AECB032A47AD },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_U_t3859302F7BB1D87B147FFF956346B678DE25245F_IComparer_1_Compare_mA5D17366CCAD2454484FC31CF779542D8D0160DE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_WriteArrayElement_TisT_tA43B34C466236E4CF4ACB1D7267181A8DCE8129E_mE56A0F3C00B718186AE0AA64FD6F17FB8DF0C9DF },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_ReadArrayElement_TisT_t3B3F1042277DCEFE0E4D5BA9D64D69F692EC97F4_m9D8E17BC9B25E3B14AD9FF848D2488BF71DACCE1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t3B3F1042277DCEFE0E4D5BA9D64D69F692EC97F4 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_WriteArrayElement_TisT_t3B3F1042277DCEFE0E4D5BA9D64D69F692EC97F4_m5A2FC63D257B4D1819049282247C956BADE74CF1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U_t2718C0A06481493BD467989009D3ABB63FA70A44 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_ReadArrayElement_TisT_tFC2A00320878DE26C1676CA44DEC8F6FC9EA8F14_mF614B0F5C1A0588A141DC8182A19E02CB3C89415 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tFC2A00320878DE26C1676CA44DEC8F6FC9EA8F14 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IComparer_1_t3D6E80FBEB14173816BD7525E8DDEB69D306F20D },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_U_t2718C0A06481493BD467989009D3ABB63FA70A44_IComparer_1_Compare_mC4058AE4B39C0C3E52F4DC249E5A899CFC12EACB },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeSortExtension_Swap_TisT_tFC2A00320878DE26C1676CA44DEC8F6FC9EA8F14_m3A2E22E55D9FBA0A0284CFA34E2B76E3FB8E185C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t575A15A58669A37242DC151F62F2FB44F3F049C8 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IComparable_1_t12EAF1F40970F4947BF6F6AB46FEB1A2D0A24837 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_t575A15A58669A37242DC151F62F2FB44F3F049C8_IComparable_1_CompareTo_m4CF765737A22A55E4D962D9DEF6FA54B2BB6FBA0 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Array_Allocate_TisT_t5BC8A3A1143355B7F5A74A86A9F4AD760F016F3F_mA431EE913AA03D9F69CCBF66FAB64CC283855B20 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU2A_tA8F0D072948467527BD52919C365F649AD380C4F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UnmanagedArray_1_tAF6F3819AE72060460FDDAAECEA69E07573B0961 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Unmanaged_Free_TisT_t5BC8A3A1143355B7F5A74A86A9F4AD760F016F3F_m80B9E4D5EF4A9B36865D150DF2BAEA934AF4B721 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t5BC8A3A1143355B7F5A74A86A9F4AD760F016F3F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_tD02DEACCD2B1FD9DEC0AB7F0DFCBBE7EA290A972 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeList_1_tAEC8B8878A54935A039ED3A1A299EEDF4C70C35F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UnsafeList_1U2A_tD2F82C6F716ECB80D9B21720F6C26086520D3F24 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UnsafeList_1_t8C08F76776E0B8E79317E30818A6A0E8C5DE164A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU2A_t33FC32499173DC5E7DA6071CDDFC7756091F1147 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UnsafeList_1_t620A94D0181E3DFEF514032D62F7285F783817BF },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeList_1_get_Capacity_mE30AE2BB42DF086225C6BEEACED7A1E66EAC91ED },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UnsafeList_1_t620A94D0181E3DFEF514032D62F7285F783817BF },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeList_1_Resize_mD2E7D06E288A389059211E1D82BA7DB35F3AF301 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeList_1_SetCapacity_mC9FF72A63AF11927A72AFC4491824A38C3741104 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU2A_t252CC32CDA4B2899B3C069D2669CF4691A3FA43E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t6368C7377A351E8DAE030B3776E2EAB48430F6A1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_t953A346901CE9031A1E7986603500DA1E9467CB3 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UnsafeList_1U2A_tB931B1757B901B48D48901E8D9AED5AFE81F9DB0 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeList_1_Dispose_mA9C46557DD7D143A317FDF3F55E3DB921A44125C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AllocatorManager_Free_TisUnsafeList_1_t620A94D0181E3DFEF514032D62F7285F783817BF_m10CA4CD105BFD216034DA9E1E453483651FF7BA7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AllocatorManager_Free_TisT_t6368C7377A351E8DAE030B3776E2EAB48430F6A1_m3380E20A35AE021576F755FA3B86CE60AC955DAB },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_AlignOf_TisT_t6368C7377A351E8DAE030B3776E2EAB48430F6A1_m541A7459A8FD164187328166064C4404C9CB1247 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeList_1_SetCapacity_TisAllocatorHandle_t3CA09720B1F89F91A8DDBA95E74C28A1EC3E3148_mB514D6019190B92124D1A238FB222C812F7CC09F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_WriteArrayElement_TisT_t6368C7377A351E8DAE030B3776E2EAB48430F6A1_mA94F2BC0DE06EB1D0025E0C41B8513CADC008966 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeList_1_get_Length_m84D5FF3632FD449358D5BFBAD4238CD69C98A61D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_SizeOf_TisT_t6368C7377A351E8DAE030B3776E2EAB48430F6A1_m26A1720E75C9C5B642A2C8736B8323913BE7FA21 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerator_1_tC56E7A39F471FA7A79480CF2118DAD86C52EB939 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UU26_tD9381527A9C070524FBDBDD330FB7658EDFB83FE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AllocatorManager_Allocate_TisU_t00C790F8C500DC1C444D4422A52F657E0858DB7D_TisUnsafeList_1_t620A94D0181E3DFEF514032D62F7285F783817BF_m33A1C62888A2F0358171DBBB13F92E1995C3982D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U_t00C790F8C500DC1C444D4422A52F657E0858DB7D },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_U_t00C790F8C500DC1C444D4422A52F657E0858DB7D_IAllocator_get_Handle_m54F384A4E6B7365849D3462AA309C77F8D9E9E6C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeList_1_SetCapacity_TisU_t00C790F8C500DC1C444D4422A52F657E0858DB7D_mED87DE5A2866AD2610DF962DDD2B32565BE50175 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UU26_tE109A31D37B5F00272456593CF5D9883BD89F86A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AllocatorManager_Allocate_TisU_tD4E7F950E65E51D0FFB646539AFA97213F18FDCB_m3424B5B28B4777480531ABD6CCCBF82C251F0D46 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_AllocatorManager_Free_TisU_tD4E7F950E65E51D0FFB646539AFA97213F18FDCB_TisT_t6368C7377A351E8DAE030B3776E2EAB48430F6A1_m54F51F8A7FBB83F8306F70DA0A131C06A38E6706 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UU26_t8968815DF07BA4CC2C505CF3D681EC7542D8FB73 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeList_1_Realloc_TisU_t74C7EABD355F620088583D354F66E077F74C2116_m601210D03B7815AC3291C90C7517E1FCEB210612 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_t779007D9180A2A29356A0CA9A3C8EC3D3A235EEE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ILSupport_AddressOf_TisT_tCF8995A642FF6C6B39489CC54F7B7FB179DD8EB7_m61251B4786FAAD41BD9A3A4555501786A9759290 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_t84606A3239A824806232207FA6AE9AC85624866C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ILSupport_AsRef_TisT_t1AD28EB9FB4F1EE8B862A47B00FCA3990142BEFB_m71FC67ABC68632F08CDF5CCB9EA51989EA7F388A },
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_Unity_Collections_CodeGenModule;
const Il2CppCodeGenModule g_Unity_Collections_CodeGenModule = 
{
	"Unity.Collections.dll",
	692,
	s_methodPointers,
	277,
	s_adjustorThunks,
	s_InvokerIndices,
	6,
	s_reversePInvokeIndices,
	67,
	s_rgctxIndices,
	504,
	s_rgctxValues,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};

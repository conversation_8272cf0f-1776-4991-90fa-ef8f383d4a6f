﻿#include "pch-cpp.hpp"

#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include <limits>


struct InterfaceActionInvoker0
{
	typedef void (*Action)(void*, const RuntimeMethod*);

	static inline void Invoke (Il2CppMethodSlot slot, RuntimeClass* declaringInterface, RuntimeObject* obj)
	{
		const VirtualInvokeData& invokeData = il2cpp_codegen_get_interface_invoke_data(slot, obj, declaringInterface);
		((Action)invokeData.methodPtr)(obj, invokeData.method);
	}
};

struct List_1_tC84AE8448E9173A7A8DCC57847B9FC3B14E8F9FD;
struct List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D;
struct ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031;
struct ISingletonU5BU5D_t0FFB357EEA3AD575F3181E6066341D67B131952B;
struct ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918;
struct ISingleton_tAE29E1C16AF3BC9818B54859F854B83E316625A0;
struct String_t;
struct UnitySourceGeneratedAssemblyMonoScriptTypes_v1_t6F6868005A3FB0A3AF68DDF1391DEEF92852B889;
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915;

IL2CPP_EXTERN_C RuntimeClass* ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* ISingleton_tAE29E1C16AF3BC9818B54859F854B83E316625A0_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* List_1_tC84AE8448E9173A7A8DCC57847B9FC3B14E8F9FD_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* SingletonMgr_tB7B0DC0DE314BB9203109C85A86CDD4AA0329DCA_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeField* U3CPrivateImplementationDetailsU3E_t8A2C5ACBF266EDAD9302F20DAED08E8F655FBC88____1AC8424CB13984C6AAD6FCDCD26D67A0EFEC1D9F5D3D2FEB337C4CD96A65015A_FieldInfo_var;
IL2CPP_EXTERN_C RuntimeField* U3CPrivateImplementationDetailsU3E_t8A2C5ACBF266EDAD9302F20DAED08E8F655FBC88____89B8B31C0073DA15D56A9366E2A38333D3C56EFE24D969F70784AC00D32C2F07_FieldInfo_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1_Add_m69ACDFBFD301624C80F56826EE76CF024DDD63E7_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1_Clear_m1CD6454920D90D91F47415322CE342C434E51358_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1_Contains_m7D2634490837D17F5ED9E682A0A2652813E47761_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1_Remove_mD45235B1A65A42076EFDBD2F74BC7E6979840416_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1__ctor_mBFE3EF7BCA0839CFB0BC4E35AC697794C7E0B249_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1_get_Count_m134C2EF4FA833C87D1B57B7A0E5548CD137513E4_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1_get_Item_mFA61000D10D666F9CA7133D4817AC2297F9839A1_RuntimeMethod_var;

struct ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031;
struct ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918;

IL2CPP_EXTERN_C_BEGIN
IL2CPP_EXTERN_C_END

#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
struct U3CModuleU3E_t92F62DA0D1F289F95B833E965AD59C51580F5F27 
{
};
struct List_1_tC84AE8448E9173A7A8DCC57847B9FC3B14E8F9FD  : public RuntimeObject
{
	ISingletonU5BU5D_t0FFB357EEA3AD575F3181E6066341D67B131952B* ____items;
	int32_t ____size;
	int32_t ____version;
	RuntimeObject* ____syncRoot;
};
struct List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D  : public RuntimeObject
{
	ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* ____items;
	int32_t ____size;
	int32_t ____version;
	RuntimeObject* ____syncRoot;
};
struct U3CPrivateImplementationDetailsU3E_t8A2C5ACBF266EDAD9302F20DAED08E8F655FBC88  : public RuntimeObject
{
};
struct SingletonMgr_tB7B0DC0DE314BB9203109C85A86CDD4AA0329DCA  : public RuntimeObject
{
};
struct UnitySourceGeneratedAssemblyMonoScriptTypes_v1_t6F6868005A3FB0A3AF68DDF1391DEEF92852B889  : public RuntimeObject
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F  : public RuntimeObject
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_pinvoke
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_com
{
};
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22 
{
	bool ___m_value;
};
struct Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3 
{
	uint8_t ___m_value;
};
struct Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C 
{
	int32_t ___m_value;
};
struct IntPtr_t 
{
	void* ___m_value;
};
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915 
{
	union
	{
		struct
		{
		};
		uint8_t Void_t4861ACF8F4594C3437BB48B6E56783494B843915__padding[1];
	};
};
#pragma pack(push, tp, 1)
struct __StaticArrayInitTypeSizeU3D104_t71805FA0D5A16067C5DF476715D98643D1F122C6 
{
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D104_t71805FA0D5A16067C5DF476715D98643D1F122C6__padding[104];
	};
};
#pragma pack(pop, tp)
#pragma pack(push, tp, 1)
struct __StaticArrayInitTypeSizeU3D232_tDF927AE8FEF974AB2B030549B1B165E4BCEBF89D 
{
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D232_tDF927AE8FEF974AB2B030549B1B165E4BCEBF89D__padding[232];
	};
};
#pragma pack(pop, tp)
struct MonoScriptData_t0D84359088F463B8290308819936D62B17DE2DD9 
{
	ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* ___FilePathsData;
	ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* ___TypesData;
	int32_t ___TotalTypes;
	int32_t ___TotalFiles;
	bool ___IsEditorOnly;
};
struct MonoScriptData_t0D84359088F463B8290308819936D62B17DE2DD9_marshaled_pinvoke
{
	Il2CppSafeArray* ___FilePathsData;
	Il2CppSafeArray* ___TypesData;
	int32_t ___TotalTypes;
	int32_t ___TotalFiles;
	int32_t ___IsEditorOnly;
};
struct MonoScriptData_t0D84359088F463B8290308819936D62B17DE2DD9_marshaled_com
{
	Il2CppSafeArray* ___FilePathsData;
	Il2CppSafeArray* ___TypesData;
	int32_t ___TotalTypes;
	int32_t ___TotalFiles;
	int32_t ___IsEditorOnly;
};
struct RuntimeFieldHandle_t6E4C45B6D2EA12FC99185805A7E77527899B25C5 
{
	intptr_t ___value;
};
struct List_1_tC84AE8448E9173A7A8DCC57847B9FC3B14E8F9FD_StaticFields
{
	ISingletonU5BU5D_t0FFB357EEA3AD575F3181E6066341D67B131952B* ___s_emptyArray;
};
struct List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D_StaticFields
{
	ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* ___s_emptyArray;
};
struct U3CPrivateImplementationDetailsU3E_t8A2C5ACBF266EDAD9302F20DAED08E8F655FBC88_StaticFields
{
	__StaticArrayInitTypeSizeU3D232_tDF927AE8FEF974AB2B030549B1B165E4BCEBF89D ___1AC8424CB13984C6AAD6FCDCD26D67A0EFEC1D9F5D3D2FEB337C4CD96A65015A;
	__StaticArrayInitTypeSizeU3D104_t71805FA0D5A16067C5DF476715D98643D1F122C6 ___89B8B31C0073DA15D56A9366E2A38333D3C56EFE24D969F70784AC00D32C2F07;
};
struct SingletonMgr_tB7B0DC0DE314BB9203109C85A86CDD4AA0329DCA_StaticFields
{
	List_1_tC84AE8448E9173A7A8DCC57847B9FC3B14E8F9FD* ____singletons;
};
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_StaticFields
{
	String_t* ___TrueString;
	String_t* ___FalseString;
};
#ifdef __clang__
#pragma clang diagnostic pop
#endif
struct ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031  : public RuntimeArray
{
	ALIGN_FIELD (8) uint8_t m_Items[1];

	inline uint8_t GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline uint8_t* GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, uint8_t value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
	}
	inline uint8_t GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline uint8_t* GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, uint8_t value)
	{
		m_Items[index] = value;
	}
};
struct ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918  : public RuntimeArray
{
	ALIGN_FIELD (8) RuntimeObject* m_Items[1];

	inline RuntimeObject* GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline RuntimeObject** GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, RuntimeObject* value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
	inline RuntimeObject* GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline RuntimeObject** GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, RuntimeObject* value)
	{
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
};


IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool List_1_Contains_m4C9139C2A6B23E9343D3F87807B32C6E2CFE660D_gshared (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D* __this, RuntimeObject* ___0_item, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void List_1_Add_mEBCF994CC3814631017F46A387B1A192ED6C85C7_gshared_inline (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D* __this, RuntimeObject* ___0_item, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool List_1_Remove_m4DFA48F4CEB9169601E75FC28517C5C06EFA5AD7_gshared (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D* __this, RuntimeObject* ___0_item, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t List_1_get_Count_m4407E4C389F22B8CEC282C15D56516658746C383_gshared_inline (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* List_1_get_Item_m33561245D64798C2AB07584C0EC4F240E4839A38_gshared (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D* __this, int32_t ___0_index, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void List_1_Clear_m16C1F2C61FED5955F10EB36BC1CB2DF34B128994_gshared_inline (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void List_1__ctor_m7F078BB342729BDF11327FD89D7872265328F690_gshared (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_NO_INLINE IL2CPP_METHOD_ATTR void List_1_AddWithResize_m79A9BF770BEF9C06BE40D5401E55E375F2726CC4_gshared (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D* __this, RuntimeObject* ___0_item, const RuntimeMethod* method) ;

IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RuntimeHelpers_InitializeArray_m751372AA3F24FBF6DA9B9D687CBFA2DE436CAB9B (RuntimeArray* ___0_array, RuntimeFieldHandle_t6E4C45B6D2EA12FC99185805A7E77527899B25C5 ___1_fldHandle, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2 (RuntimeObject* __this, const RuntimeMethod* method) ;
inline bool List_1_Contains_m7D2634490837D17F5ED9E682A0A2652813E47761 (List_1_tC84AE8448E9173A7A8DCC57847B9FC3B14E8F9FD* __this, RuntimeObject* ___0_item, const RuntimeMethod* method)
{
	return ((  bool (*) (List_1_tC84AE8448E9173A7A8DCC57847B9FC3B14E8F9FD*, RuntimeObject*, const RuntimeMethod*))List_1_Contains_m4C9139C2A6B23E9343D3F87807B32C6E2CFE660D_gshared)(__this, ___0_item, method);
}
inline void List_1_Add_m69ACDFBFD301624C80F56826EE76CF024DDD63E7_inline (List_1_tC84AE8448E9173A7A8DCC57847B9FC3B14E8F9FD* __this, RuntimeObject* ___0_item, const RuntimeMethod* method)
{
	((  void (*) (List_1_tC84AE8448E9173A7A8DCC57847B9FC3B14E8F9FD*, RuntimeObject*, const RuntimeMethod*))List_1_Add_mEBCF994CC3814631017F46A387B1A192ED6C85C7_gshared_inline)(__this, ___0_item, method);
}
inline bool List_1_Remove_mD45235B1A65A42076EFDBD2F74BC7E6979840416 (List_1_tC84AE8448E9173A7A8DCC57847B9FC3B14E8F9FD* __this, RuntimeObject* ___0_item, const RuntimeMethod* method)
{
	return ((  bool (*) (List_1_tC84AE8448E9173A7A8DCC57847B9FC3B14E8F9FD*, RuntimeObject*, const RuntimeMethod*))List_1_Remove_m4DFA48F4CEB9169601E75FC28517C5C06EFA5AD7_gshared)(__this, ___0_item, method);
}
inline int32_t List_1_get_Count_m134C2EF4FA833C87D1B57B7A0E5548CD137513E4_inline (List_1_tC84AE8448E9173A7A8DCC57847B9FC3B14E8F9FD* __this, const RuntimeMethod* method)
{
	return ((  int32_t (*) (List_1_tC84AE8448E9173A7A8DCC57847B9FC3B14E8F9FD*, const RuntimeMethod*))List_1_get_Count_m4407E4C389F22B8CEC282C15D56516658746C383_gshared_inline)(__this, method);
}
inline RuntimeObject* List_1_get_Item_mFA61000D10D666F9CA7133D4817AC2297F9839A1 (List_1_tC84AE8448E9173A7A8DCC57847B9FC3B14E8F9FD* __this, int32_t ___0_index, const RuntimeMethod* method)
{
	return ((  RuntimeObject* (*) (List_1_tC84AE8448E9173A7A8DCC57847B9FC3B14E8F9FD*, int32_t, const RuntimeMethod*))List_1_get_Item_m33561245D64798C2AB07584C0EC4F240E4839A38_gshared)(__this, ___0_index, method);
}
inline void List_1_Clear_m1CD6454920D90D91F47415322CE342C434E51358_inline (List_1_tC84AE8448E9173A7A8DCC57847B9FC3B14E8F9FD* __this, const RuntimeMethod* method)
{
	((  void (*) (List_1_tC84AE8448E9173A7A8DCC57847B9FC3B14E8F9FD*, const RuntimeMethod*))List_1_Clear_m16C1F2C61FED5955F10EB36BC1CB2DF34B128994_gshared_inline)(__this, method);
}
inline void List_1__ctor_mBFE3EF7BCA0839CFB0BC4E35AC697794C7E0B249 (List_1_tC84AE8448E9173A7A8DCC57847B9FC3B14E8F9FD* __this, const RuntimeMethod* method)
{
	((  void (*) (List_1_tC84AE8448E9173A7A8DCC57847B9FC3B14E8F9FD*, const RuntimeMethod*))List_1__ctor_m7F078BB342729BDF11327FD89D7872265328F690_gshared)(__this, method);
}
inline void List_1_AddWithResize_m79A9BF770BEF9C06BE40D5401E55E375F2726CC4 (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D* __this, RuntimeObject* ___0_item, const RuntimeMethod* method)
{
	((  void (*) (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D*, RuntimeObject*, const RuntimeMethod*))List_1_AddWithResize_m79A9BF770BEF9C06BE40D5401E55E375F2726CC4_gshared)(__this, ___0_item, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Array_Clear_m50BAA3751899858B097D3FF2ED31F284703FE5CB (RuntimeArray* ___0_array, int32_t ___1_index, int32_t ___2_length, const RuntimeMethod* method) ;
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR MonoScriptData_t0D84359088F463B8290308819936D62B17DE2DD9 UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_m4031AFE657BCCFF48129E3DF955DA329882861D9 (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CPrivateImplementationDetailsU3E_t8A2C5ACBF266EDAD9302F20DAED08E8F655FBC88____1AC8424CB13984C6AAD6FCDCD26D67A0EFEC1D9F5D3D2FEB337C4CD96A65015A_FieldInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CPrivateImplementationDetailsU3E_t8A2C5ACBF266EDAD9302F20DAED08E8F655FBC88____89B8B31C0073DA15D56A9366E2A38333D3C56EFE24D969F70784AC00D32C2F07_FieldInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	MonoScriptData_t0D84359088F463B8290308819936D62B17DE2DD9 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		il2cpp_codegen_initobj((&V_0), sizeof(MonoScriptData_t0D84359088F463B8290308819936D62B17DE2DD9));
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_0 = (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)SZArrayNew(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031_il2cpp_TypeInfo_var, (uint32_t)((int32_t)232));
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_1 = L_0;
		RuntimeFieldHandle_t6E4C45B6D2EA12FC99185805A7E77527899B25C5 L_2 = { reinterpret_cast<intptr_t> (U3CPrivateImplementationDetailsU3E_t8A2C5ACBF266EDAD9302F20DAED08E8F655FBC88____1AC8424CB13984C6AAD6FCDCD26D67A0EFEC1D9F5D3D2FEB337C4CD96A65015A_FieldInfo_var) };
		RuntimeHelpers_InitializeArray_m751372AA3F24FBF6DA9B9D687CBFA2DE436CAB9B((RuntimeArray*)L_1, L_2, NULL);
		(&V_0)->___FilePathsData = L_1;
		Il2CppCodeGenWriteBarrier((void**)(&(&V_0)->___FilePathsData), (void*)L_1);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_3 = (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)SZArrayNew(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031_il2cpp_TypeInfo_var, (uint32_t)((int32_t)104));
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_4 = L_3;
		RuntimeFieldHandle_t6E4C45B6D2EA12FC99185805A7E77527899B25C5 L_5 = { reinterpret_cast<intptr_t> (U3CPrivateImplementationDetailsU3E_t8A2C5ACBF266EDAD9302F20DAED08E8F655FBC88____89B8B31C0073DA15D56A9366E2A38333D3C56EFE24D969F70784AC00D32C2F07_FieldInfo_var) };
		RuntimeHelpers_InitializeArray_m751372AA3F24FBF6DA9B9D687CBFA2DE436CAB9B((RuntimeArray*)L_4, L_5, NULL);
		(&V_0)->___TypesData = L_4;
		Il2CppCodeGenWriteBarrier((void**)(&(&V_0)->___TypesData), (void*)L_4);
		(&V_0)->___TotalFiles = 4;
		(&V_0)->___TotalTypes = 4;
		(&V_0)->___IsEditorOnly = (bool)0;
		MonoScriptData_t0D84359088F463B8290308819936D62B17DE2DD9 L_6 = V_0;
		return L_6;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_mD001C0A5A5D4602942338E61395727E5E0A7C15D (UnitySourceGeneratedAssemblyMonoScriptTypes_v1_t6F6868005A3FB0A3AF68DDF1391DEEF92852B889* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C void MonoScriptData_t0D84359088F463B8290308819936D62B17DE2DD9_marshal_pinvoke(const MonoScriptData_t0D84359088F463B8290308819936D62B17DE2DD9& unmarshaled, MonoScriptData_t0D84359088F463B8290308819936D62B17DE2DD9_marshaled_pinvoke& marshaled)
{
	marshaled.___FilePathsData = il2cpp_codegen_com_marshal_safe_array(IL2CPP_VT_I1, unmarshaled.___FilePathsData);
	marshaled.___TypesData = il2cpp_codegen_com_marshal_safe_array(IL2CPP_VT_I1, unmarshaled.___TypesData);
	marshaled.___TotalTypes = unmarshaled.___TotalTypes;
	marshaled.___TotalFiles = unmarshaled.___TotalFiles;
	marshaled.___IsEditorOnly = static_cast<int32_t>(unmarshaled.___IsEditorOnly);
}
IL2CPP_EXTERN_C void MonoScriptData_t0D84359088F463B8290308819936D62B17DE2DD9_marshal_pinvoke_back(const MonoScriptData_t0D84359088F463B8290308819936D62B17DE2DD9_marshaled_pinvoke& marshaled, MonoScriptData_t0D84359088F463B8290308819936D62B17DE2DD9& unmarshaled)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	unmarshaled.___FilePathsData = (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I1, Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, marshaled.___FilePathsData);
	Il2CppCodeGenWriteBarrier((void**)(&unmarshaled.___FilePathsData), (void*)(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I1, Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, marshaled.___FilePathsData));
	unmarshaled.___TypesData = (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I1, Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, marshaled.___TypesData);
	Il2CppCodeGenWriteBarrier((void**)(&unmarshaled.___TypesData), (void*)(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I1, Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, marshaled.___TypesData));
	int32_t unmarshaledTotalTypes_temp_2 = 0;
	unmarshaledTotalTypes_temp_2 = marshaled.___TotalTypes;
	unmarshaled.___TotalTypes = unmarshaledTotalTypes_temp_2;
	int32_t unmarshaledTotalFiles_temp_3 = 0;
	unmarshaledTotalFiles_temp_3 = marshaled.___TotalFiles;
	unmarshaled.___TotalFiles = unmarshaledTotalFiles_temp_3;
	bool unmarshaledIsEditorOnly_temp_4 = false;
	unmarshaledIsEditorOnly_temp_4 = static_cast<bool>(marshaled.___IsEditorOnly);
	unmarshaled.___IsEditorOnly = unmarshaledIsEditorOnly_temp_4;
}
IL2CPP_EXTERN_C void MonoScriptData_t0D84359088F463B8290308819936D62B17DE2DD9_marshal_pinvoke_cleanup(MonoScriptData_t0D84359088F463B8290308819936D62B17DE2DD9_marshaled_pinvoke& marshaled)
{
	il2cpp_codegen_com_destroy_safe_array(marshaled.___FilePathsData);
	marshaled.___FilePathsData = NULL;
	il2cpp_codegen_com_destroy_safe_array(marshaled.___TypesData);
	marshaled.___TypesData = NULL;
}
IL2CPP_EXTERN_C void MonoScriptData_t0D84359088F463B8290308819936D62B17DE2DD9_marshal_com(const MonoScriptData_t0D84359088F463B8290308819936D62B17DE2DD9& unmarshaled, MonoScriptData_t0D84359088F463B8290308819936D62B17DE2DD9_marshaled_com& marshaled)
{
	marshaled.___FilePathsData = il2cpp_codegen_com_marshal_safe_array(IL2CPP_VT_I1, unmarshaled.___FilePathsData);
	marshaled.___TypesData = il2cpp_codegen_com_marshal_safe_array(IL2CPP_VT_I1, unmarshaled.___TypesData);
	marshaled.___TotalTypes = unmarshaled.___TotalTypes;
	marshaled.___TotalFiles = unmarshaled.___TotalFiles;
	marshaled.___IsEditorOnly = static_cast<int32_t>(unmarshaled.___IsEditorOnly);
}
IL2CPP_EXTERN_C void MonoScriptData_t0D84359088F463B8290308819936D62B17DE2DD9_marshal_com_back(const MonoScriptData_t0D84359088F463B8290308819936D62B17DE2DD9_marshaled_com& marshaled, MonoScriptData_t0D84359088F463B8290308819936D62B17DE2DD9& unmarshaled)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	unmarshaled.___FilePathsData = (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I1, Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, marshaled.___FilePathsData);
	Il2CppCodeGenWriteBarrier((void**)(&unmarshaled.___FilePathsData), (void*)(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I1, Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, marshaled.___FilePathsData));
	unmarshaled.___TypesData = (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I1, Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, marshaled.___TypesData);
	Il2CppCodeGenWriteBarrier((void**)(&unmarshaled.___TypesData), (void*)(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I1, Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, marshaled.___TypesData));
	int32_t unmarshaledTotalTypes_temp_2 = 0;
	unmarshaledTotalTypes_temp_2 = marshaled.___TotalTypes;
	unmarshaled.___TotalTypes = unmarshaledTotalTypes_temp_2;
	int32_t unmarshaledTotalFiles_temp_3 = 0;
	unmarshaledTotalFiles_temp_3 = marshaled.___TotalFiles;
	unmarshaled.___TotalFiles = unmarshaledTotalFiles_temp_3;
	bool unmarshaledIsEditorOnly_temp_4 = false;
	unmarshaledIsEditorOnly_temp_4 = static_cast<bool>(marshaled.___IsEditorOnly);
	unmarshaled.___IsEditorOnly = unmarshaledIsEditorOnly_temp_4;
}
IL2CPP_EXTERN_C void MonoScriptData_t0D84359088F463B8290308819936D62B17DE2DD9_marshal_com_cleanup(MonoScriptData_t0D84359088F463B8290308819936D62B17DE2DD9_marshaled_com& marshaled)
{
	il2cpp_codegen_com_destroy_safe_array(marshaled.___FilePathsData);
	marshaled.___FilePathsData = NULL;
	il2cpp_codegen_com_destroy_safe_array(marshaled.___TypesData);
	marshaled.___TypesData = NULL;
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SingletonMgr_Register_m8B9DD4685E470FBD632ADC5C06381AD0A032604A (RuntimeObject* ___0_singleton, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_Add_m69ACDFBFD301624C80F56826EE76CF024DDD63E7_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_Contains_m7D2634490837D17F5ED9E682A0A2652813E47761_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SingletonMgr_tB7B0DC0DE314BB9203109C85A86CDD4AA0329DCA_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		il2cpp_codegen_runtime_class_init_inline(SingletonMgr_tB7B0DC0DE314BB9203109C85A86CDD4AA0329DCA_il2cpp_TypeInfo_var);
		List_1_tC84AE8448E9173A7A8DCC57847B9FC3B14E8F9FD* L_0 = ((SingletonMgr_tB7B0DC0DE314BB9203109C85A86CDD4AA0329DCA_StaticFields*)il2cpp_codegen_static_fields_for(SingletonMgr_tB7B0DC0DE314BB9203109C85A86CDD4AA0329DCA_il2cpp_TypeInfo_var))->____singletons;
		RuntimeObject* L_1 = ___0_singleton;
		NullCheck(L_0);
		bool L_2;
		L_2 = List_1_Contains_m7D2634490837D17F5ED9E682A0A2652813E47761(L_0, L_1, List_1_Contains_m7D2634490837D17F5ED9E682A0A2652813E47761_RuntimeMethod_var);
		if (L_2)
		{
			goto IL_0018;
		}
	}
	{
		il2cpp_codegen_runtime_class_init_inline(SingletonMgr_tB7B0DC0DE314BB9203109C85A86CDD4AA0329DCA_il2cpp_TypeInfo_var);
		List_1_tC84AE8448E9173A7A8DCC57847B9FC3B14E8F9FD* L_3 = ((SingletonMgr_tB7B0DC0DE314BB9203109C85A86CDD4AA0329DCA_StaticFields*)il2cpp_codegen_static_fields_for(SingletonMgr_tB7B0DC0DE314BB9203109C85A86CDD4AA0329DCA_il2cpp_TypeInfo_var))->____singletons;
		RuntimeObject* L_4 = ___0_singleton;
		NullCheck(L_3);
		List_1_Add_m69ACDFBFD301624C80F56826EE76CF024DDD63E7_inline(L_3, L_4, List_1_Add_m69ACDFBFD301624C80F56826EE76CF024DDD63E7_RuntimeMethod_var);
	}

IL_0018:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SingletonMgr_Unregister_mB0FCC77E9790E0538D113D78CF8A0AC1205E3B42 (RuntimeObject* ___0_singleton, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_Contains_m7D2634490837D17F5ED9E682A0A2652813E47761_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_Remove_mD45235B1A65A42076EFDBD2F74BC7E6979840416_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SingletonMgr_tB7B0DC0DE314BB9203109C85A86CDD4AA0329DCA_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		il2cpp_codegen_runtime_class_init_inline(SingletonMgr_tB7B0DC0DE314BB9203109C85A86CDD4AA0329DCA_il2cpp_TypeInfo_var);
		List_1_tC84AE8448E9173A7A8DCC57847B9FC3B14E8F9FD* L_0 = ((SingletonMgr_tB7B0DC0DE314BB9203109C85A86CDD4AA0329DCA_StaticFields*)il2cpp_codegen_static_fields_for(SingletonMgr_tB7B0DC0DE314BB9203109C85A86CDD4AA0329DCA_il2cpp_TypeInfo_var))->____singletons;
		RuntimeObject* L_1 = ___0_singleton;
		NullCheck(L_0);
		bool L_2;
		L_2 = List_1_Contains_m7D2634490837D17F5ED9E682A0A2652813E47761(L_0, L_1, List_1_Contains_m7D2634490837D17F5ED9E682A0A2652813E47761_RuntimeMethod_var);
		if (!L_2)
		{
			goto IL_0019;
		}
	}
	{
		il2cpp_codegen_runtime_class_init_inline(SingletonMgr_tB7B0DC0DE314BB9203109C85A86CDD4AA0329DCA_il2cpp_TypeInfo_var);
		List_1_tC84AE8448E9173A7A8DCC57847B9FC3B14E8F9FD* L_3 = ((SingletonMgr_tB7B0DC0DE314BB9203109C85A86CDD4AA0329DCA_StaticFields*)il2cpp_codegen_static_fields_for(SingletonMgr_tB7B0DC0DE314BB9203109C85A86CDD4AA0329DCA_il2cpp_TypeInfo_var))->____singletons;
		RuntimeObject* L_4 = ___0_singleton;
		NullCheck(L_3);
		bool L_5;
		L_5 = List_1_Remove_mD45235B1A65A42076EFDBD2F74BC7E6979840416(L_3, L_4, List_1_Remove_mD45235B1A65A42076EFDBD2F74BC7E6979840416_RuntimeMethod_var);
	}

IL_0019:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SingletonMgr_Clear_m88ACCFAFB8AF79024ECDB3AF31465C93EA9A9EA4 (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ISingleton_tAE29E1C16AF3BC9818B54859F854B83E316625A0_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_Clear_m1CD6454920D90D91F47415322CE342C434E51358_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_get_Count_m134C2EF4FA833C87D1B57B7A0E5548CD137513E4_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_get_Item_mFA61000D10D666F9CA7133D4817AC2297F9839A1_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SingletonMgr_tB7B0DC0DE314BB9203109C85A86CDD4AA0329DCA_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	int32_t V_0 = 0;
	{
		il2cpp_codegen_runtime_class_init_inline(SingletonMgr_tB7B0DC0DE314BB9203109C85A86CDD4AA0329DCA_il2cpp_TypeInfo_var);
		List_1_tC84AE8448E9173A7A8DCC57847B9FC3B14E8F9FD* L_0 = ((SingletonMgr_tB7B0DC0DE314BB9203109C85A86CDD4AA0329DCA_StaticFields*)il2cpp_codegen_static_fields_for(SingletonMgr_tB7B0DC0DE314BB9203109C85A86CDD4AA0329DCA_il2cpp_TypeInfo_var))->____singletons;
		NullCheck(L_0);
		int32_t L_1;
		L_1 = List_1_get_Count_m134C2EF4FA833C87D1B57B7A0E5548CD137513E4_inline(L_0, List_1_get_Count_m134C2EF4FA833C87D1B57B7A0E5548CD137513E4_RuntimeMethod_var);
		V_0 = ((int32_t)il2cpp_codegen_subtract(L_1, 1));
		goto IL_0023;
	}

IL_000f:
	{
		il2cpp_codegen_runtime_class_init_inline(SingletonMgr_tB7B0DC0DE314BB9203109C85A86CDD4AA0329DCA_il2cpp_TypeInfo_var);
		List_1_tC84AE8448E9173A7A8DCC57847B9FC3B14E8F9FD* L_2 = ((SingletonMgr_tB7B0DC0DE314BB9203109C85A86CDD4AA0329DCA_StaticFields*)il2cpp_codegen_static_fields_for(SingletonMgr_tB7B0DC0DE314BB9203109C85A86CDD4AA0329DCA_il2cpp_TypeInfo_var))->____singletons;
		int32_t L_3 = V_0;
		NullCheck(L_2);
		RuntimeObject* L_4;
		L_4 = List_1_get_Item_mFA61000D10D666F9CA7133D4817AC2297F9839A1(L_2, L_3, List_1_get_Item_mFA61000D10D666F9CA7133D4817AC2297F9839A1_RuntimeMethod_var);
		NullCheck(L_4);
		InterfaceActionInvoker0::Invoke(1, ISingleton_tAE29E1C16AF3BC9818B54859F854B83E316625A0_il2cpp_TypeInfo_var, L_4);
		int32_t L_5 = V_0;
		V_0 = ((int32_t)il2cpp_codegen_subtract(L_5, 1));
	}

IL_0023:
	{
		int32_t L_6 = V_0;
		if ((((int32_t)L_6) >= ((int32_t)0)))
		{
			goto IL_000f;
		}
	}
	{
		il2cpp_codegen_runtime_class_init_inline(SingletonMgr_tB7B0DC0DE314BB9203109C85A86CDD4AA0329DCA_il2cpp_TypeInfo_var);
		List_1_tC84AE8448E9173A7A8DCC57847B9FC3B14E8F9FD* L_7 = ((SingletonMgr_tB7B0DC0DE314BB9203109C85A86CDD4AA0329DCA_StaticFields*)il2cpp_codegen_static_fields_for(SingletonMgr_tB7B0DC0DE314BB9203109C85A86CDD4AA0329DCA_il2cpp_TypeInfo_var))->____singletons;
		NullCheck(L_7);
		List_1_Clear_m1CD6454920D90D91F47415322CE342C434E51358_inline(L_7, List_1_Clear_m1CD6454920D90D91F47415322CE342C434E51358_RuntimeMethod_var);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SingletonMgr__cctor_mC0103DB1EEF48A15274EF43A90BBB49FA2CA2B36 (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1__ctor_mBFE3EF7BCA0839CFB0BC4E35AC697794C7E0B249_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_tC84AE8448E9173A7A8DCC57847B9FC3B14E8F9FD_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SingletonMgr_tB7B0DC0DE314BB9203109C85A86CDD4AA0329DCA_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		List_1_tC84AE8448E9173A7A8DCC57847B9FC3B14E8F9FD* L_0 = (List_1_tC84AE8448E9173A7A8DCC57847B9FC3B14E8F9FD*)il2cpp_codegen_object_new(List_1_tC84AE8448E9173A7A8DCC57847B9FC3B14E8F9FD_il2cpp_TypeInfo_var);
		List_1__ctor_mBFE3EF7BCA0839CFB0BC4E35AC697794C7E0B249(L_0, List_1__ctor_mBFE3EF7BCA0839CFB0BC4E35AC697794C7E0B249_RuntimeMethod_var);
		((SingletonMgr_tB7B0DC0DE314BB9203109C85A86CDD4AA0329DCA_StaticFields*)il2cpp_codegen_static_fields_for(SingletonMgr_tB7B0DC0DE314BB9203109C85A86CDD4AA0329DCA_il2cpp_TypeInfo_var))->____singletons = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&((SingletonMgr_tB7B0DC0DE314BB9203109C85A86CDD4AA0329DCA_StaticFields*)il2cpp_codegen_static_fields_for(SingletonMgr_tB7B0DC0DE314BB9203109C85A86CDD4AA0329DCA_il2cpp_TypeInfo_var))->____singletons), (void*)L_0);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void List_1_Add_mEBCF994CC3814631017F46A387B1A192ED6C85C7_gshared_inline (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D* __this, RuntimeObject* ___0_item, const RuntimeMethod* method) 
{
	ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* V_0 = NULL;
	int32_t V_1 = 0;
	{
		int32_t L_0 = __this->____version;
		__this->____version = ((int32_t)il2cpp_codegen_add(L_0, 1));
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_1 = __this->____items;
		V_0 = L_1;
		int32_t L_2 = __this->____size;
		V_1 = L_2;
		int32_t L_3 = V_1;
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_4 = V_0;
		NullCheck(L_4);
		if ((!(((uint32_t)L_3) < ((uint32_t)((int32_t)(((RuntimeArray*)L_4)->max_length))))))
		{
			goto IL_0034;
		}
	}
	{
		int32_t L_5 = V_1;
		__this->____size = ((int32_t)il2cpp_codegen_add(L_5, 1));
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_6 = V_0;
		int32_t L_7 = V_1;
		RuntimeObject* L_8 = ___0_item;
		NullCheck(L_6);
		(L_6)->SetAt(static_cast<il2cpp_array_size_t>(L_7), (RuntimeObject*)L_8);
		return;
	}

IL_0034:
	{
		RuntimeObject* L_9 = ___0_item;
		List_1_AddWithResize_m79A9BF770BEF9C06BE40D5401E55E375F2726CC4(__this, L_9, il2cpp_rgctx_method(method->klass->rgctx_data, 14));
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t List_1_get_Count_m4407E4C389F22B8CEC282C15D56516658746C383_gshared_inline (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D* __this, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = __this->____size;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void List_1_Clear_m16C1F2C61FED5955F10EB36BC1CB2DF34B128994_gshared_inline (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D* __this, const RuntimeMethod* method) 
{
	int32_t V_0 = 0;
	{
		int32_t L_0 = __this->____version;
		__this->____version = ((int32_t)il2cpp_codegen_add(L_0, 1));
	}
	{
		int32_t L_1 = __this->____size;
		V_0 = L_1;
		__this->____size = 0;
		int32_t L_2 = V_0;
		if ((((int32_t)L_2) <= ((int32_t)0)))
		{
			goto IL_003c;
		}
	}
	{
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_3 = __this->____items;
		int32_t L_4 = V_0;
		Array_Clear_m50BAA3751899858B097D3FF2ED31F284703FE5CB((RuntimeArray*)L_3, 0, L_4, NULL);
		return;
	}

IL_003c:
	{
		return;
	}
}

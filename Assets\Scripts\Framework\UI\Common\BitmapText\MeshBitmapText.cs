using UnityEngine;
using UnityEngine.UI;

[RequireComponent(typeof(Mesh<PERSON><PERSON>er), typeof(MeshFilter))]
[AddComponentMenu("Mesh UI/Bitmap Text")]
public class MeshBitmapText : MonoBehaviour
{
    [Header("Text")]
    [SerializeField, TextArea(3, 10)] private string m_Text = "Sample Text";
    [SerializeField] private BitmapFont m_Font;
    [SerializeField] private BitmapTextSettings m_Settings = new BitmapTextSettings();
    [SerializeField] private Color m_Color = Color.white;

    [Header("Layout")]
    [SerializeField] private Vector2 m_Pivot = new Vector2(0.5f, 0.5f);
    [SerializeField] private bool m_AutoUpdateMesh = true;

    // Components
    private MeshRenderer m_MeshRenderer;
    private MeshFilter m_MeshFilter;
    private Mesh m_Mesh;

    // Cached text info
    private BitmapTextInfo m_TextInfo;
    private bool m_TextDirty = true;
    private bool m_MaterialDirty = true;

    public string text
    {
        get => m_Text;
        set
        {
            if (m_Text != value)
            {
                m_Text = value;
                SetTextDirty();
            }
        }
    }

    public BitmapFont font
    {
        get => m_Font;
        set
        {
            if (m_Font != value)
            {
                m_Font = value;
                SetTextDirty();
                SetMaterialDirty();
            }
        }
    }

    public BitmapTextSettings settings
    {
        get => m_Settings;
        set
        {
            if (m_Settings != value)
            {
                m_Settings = value ?? new BitmapTextSettings();
                SetTextDirty();
            }
        }
    }

    public Color color
    {
        get => m_Color;
        set
        {
            if (m_Color != value)
            {
                m_Color = value;
                SetTextDirty();
            }
        }
    }

    public Vector2 pivot
    {
        get => m_Pivot;
        set
        {
            if (m_Pivot != value)
            {
                m_Pivot = value;
                SetTextDirty();
            }
        }
    }

    public bool autoUpdateMesh
    {
        get => m_AutoUpdateMesh;
        set => m_AutoUpdateMesh = value;
    }

    public MeshRenderer meshRenderer
    {
        get
        {
            if (m_MeshRenderer == null)
                m_MeshRenderer = GetComponent<MeshRenderer>();
            return m_MeshRenderer;
        }
    }

    public MeshFilter meshFilter
    {
        get
        {
            if (m_MeshFilter == null)
                m_MeshFilter = GetComponent<MeshFilter>();
            return m_MeshFilter;
        }
    }

    public Mesh mesh
    {
        get
        {
            if (m_Mesh == null)
            {
                m_Mesh = new Mesh();
                m_Mesh.name = "BitmapText Mesh";
                meshFilter.mesh = m_Mesh;
            }
            return m_Mesh;
        }
    }

    private void Awake()
    {
        // Ensure components exist
        m_MeshRenderer = GetComponent<MeshRenderer>();
        m_MeshFilter = GetComponent<MeshFilter>();

        // Create mesh if needed
        if (m_MeshFilter.sharedMesh == null)
        {
            m_MeshFilter.mesh = mesh;
        }
        else
        {
            m_Mesh = m_MeshFilter.sharedMesh;
        }
    }

    private void Start()
    {
        SetTextDirty();
        SetMaterialDirty();
    }

    private void Update()
    {
        if (m_AutoUpdateMesh)
        {
            UpdateMesh();
            UpdateMaterial();
        }
    }

#if UNITY_EDITOR
    private void OnValidate()
    {
        SetTextDirty();
        SetMaterialDirty();
    }
#endif

    private void SetTextDirty()
    {
        m_TextDirty = true;
    }

    private void SetMaterialDirty()
    {
        m_MaterialDirty = true;
    }

    private void UpdateTextInfo()
    {
        if (!m_TextDirty || m_Font == null)
            return;

        m_TextInfo = BitmapTextGenerator.GenerateText(m_Text, m_Font, m_Settings, m_Color);
        m_TextDirty = false;
    }

    public void UpdateMesh()
    {
        UpdateTextInfo();

        if (m_TextInfo.characters == null || m_TextInfo.characters.Count == 0)
        {
            mesh.Clear();
            return;
        }

        // Use VertexHelper for mesh generation
        using (var vh = new VertexHelper())
        {
            BitmapTextGenerator.PopulateMesh(vh, m_TextInfo, m_Pivot);
            vh.FillMesh(mesh);
        }
    }

    public void UpdateMaterial()
    {
        if (!m_MaterialDirty || m_Font == null)
            return;

        var renderer = meshRenderer;
        if (renderer != null)
        {
            if (m_Font.material != null)
            {
                renderer.material = m_Font.material;
            }

            // Set texture if material supports it
            if (m_Font.atlas != null && renderer.material != null)
            {
                if (renderer.material.HasProperty("_MainTex"))
                {
                    renderer.material.mainTexture = m_Font.atlas;
                }
            }
        }

        m_MaterialDirty = false;
    }

    /// <summary>
    /// Force update both mesh and material immediately
    /// </summary>
    public void ForceUpdate()
    {
        SetTextDirty();
        SetMaterialDirty();
        UpdateMesh();
        UpdateMaterial();
    }

    /// <summary>
    /// Get the bounds of the rendered text
    /// </summary>
    public Bounds GetTextBounds()
    {
        UpdateTextInfo();

        if (m_TextInfo.characters == null || m_TextInfo.characters.Count == 0)
            return new Bounds();

        Vector2 pivotOffset = new Vector2(
            -m_TextInfo.textSize.x * m_Pivot.x,
            -m_TextInfo.textSize.y * m_Pivot.y
        );

        Vector3 center = new Vector3(
            pivotOffset.x + m_TextInfo.textSize.x * 0.5f,
            pivotOffset.y + m_TextInfo.textSize.y * 0.5f,
            0f
        );

        Vector3 size = new Vector3(m_TextInfo.textSize.x, m_TextInfo.textSize.y, 0f);

        return new Bounds(center, size);
    }

    /// <summary>
    /// Get the preferred size for the current text
    /// </summary>
    public Vector2 GetPreferredSize()
    {
        if (m_Font == null)
            return Vector2.zero;

        return BitmapTextGenerator.GetPreferredSize(m_Text, m_Font, m_Settings);
    }

    /// <summary>
    /// Set text and update immediately
    /// </summary>
    public void SetText(string newText)
    {
        text = newText;
        if (!m_AutoUpdateMesh)
        {
            UpdateMesh();
        }
    }

    /// <summary>
    /// Set font and update immediately
    /// </summary>
    public void SetFont(BitmapFont newFont)
    {
        font = newFont;
        if (!m_AutoUpdateMesh)
        {
            UpdateMesh();
            UpdateMaterial();
        }
    }

    private void OnDestroy()
    {
        if (m_Mesh != null)
        {
            if (Application.isPlaying)
            {
                Destroy(m_Mesh);
            }
            else
            {
                DestroyImmediate(m_Mesh);
            }
        }
    }
}

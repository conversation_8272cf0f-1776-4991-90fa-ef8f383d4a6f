﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_m3FD80EDA842E2412B4692EAFCE58AB4212CCE716 (void);
extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_m675666E40643F5105DBA62F0DF47E72BDFC21661 (void);
extern void Localization_get_CurrentLanguage_mDE9179BCA77E2547013721F10AB5484A03B3ECFC (void);
extern void Localization_set_CurrentLanguage_m841FABFFFA84EB4B4B1CDC66409AF6A6E086379C (void);
extern void Localization_Initialize_m26388D559D20CDFA24FFC1C0F2C95C4606D53EB1 (void);
extern void Localization_Get_m2F5E554580E25E1AF96814D0D45BFA5BD69B76B9 (void);
extern void Localization_GetLocalizationFile_mFA807E3A9F56234F464044182627AC21A756D89D (void);
extern void Localization__cctor_m1CB087AC2DF2B4E015CB0067DE05EAA0D335CB47 (void);
extern void Translated__ctor_mA07623BBB587277C2DAA9608C0CD291796E747DD (void);
extern void SerializeHelper__ctor_mF63CEA171EE9F93470186A9CBE2988EDB09563ED (void);
extern void Item__ctor_m7C789D170B24244FA5D664B2047C499CFC22DD67 (void);
extern void LocalizationSettings__ctor_m9B324FDA14A8D5D2F35258B97A03C5CF882720CF (void);
extern void LanguageSetting__ctor_m740895421A53000F08F4DCC408A188FD648F6B0E (void);
extern void TextStyle__ctor_m50D4367015A8C8B98F74822E65B6273177145B0E (void);
extern void StyleSetting__ctor_m03088E68B6057880949CDF848CB37C01D76C8C6F (void);
static Il2CppMethodPointer s_methodPointers[15] = 
{
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_m3FD80EDA842E2412B4692EAFCE58AB4212CCE716,
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_m675666E40643F5105DBA62F0DF47E72BDFC21661,
	Localization_get_CurrentLanguage_mDE9179BCA77E2547013721F10AB5484A03B3ECFC,
	Localization_set_CurrentLanguage_m841FABFFFA84EB4B4B1CDC66409AF6A6E086379C,
	Localization_Initialize_m26388D559D20CDFA24FFC1C0F2C95C4606D53EB1,
	Localization_Get_m2F5E554580E25E1AF96814D0D45BFA5BD69B76B9,
	Localization_GetLocalizationFile_mFA807E3A9F56234F464044182627AC21A756D89D,
	Localization__cctor_m1CB087AC2DF2B4E015CB0067DE05EAA0D335CB47,
	Translated__ctor_mA07623BBB587277C2DAA9608C0CD291796E747DD,
	SerializeHelper__ctor_mF63CEA171EE9F93470186A9CBE2988EDB09563ED,
	Item__ctor_m7C789D170B24244FA5D664B2047C499CFC22DD67,
	LocalizationSettings__ctor_m9B324FDA14A8D5D2F35258B97A03C5CF882720CF,
	LanguageSetting__ctor_m740895421A53000F08F4DCC408A188FD648F6B0E,
	TextStyle__ctor_m50D4367015A8C8B98F74822E65B6273177145B0E,
	StyleSetting__ctor_m03088E68B6057880949CDF848CB37C01D76C8C6F,
};
static const int32_t s_InvokerIndices[15] = 
{
	21389,
	13298,
	21225,
	20832,
	19880,
	20515,
	20498,
	21355,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_Localization_Runtime_CodeGenModule;
const Il2CppCodeGenModule g_Localization_Runtime_CodeGenModule = 
{
	"Localization.Runtime.dll",
	15,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};

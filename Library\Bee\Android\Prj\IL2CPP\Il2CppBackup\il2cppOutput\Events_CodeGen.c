﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_mB44254F75E2A952C73A8E207FF900809E76D09AC (void);
extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_m6C71DB95393219594C0809438F3543C15F8E93E3 (void);
extern void Unsubscriber_Dispose_mCCD048F3A4DFFB9391E74BBBF5CC03F61E93EDC0 (void);
extern void Unsubscriber__ctor_m11B27CCC64445E9582A3F7F98B147AED3A31F344 (void);
extern void EventBusRegistry_RegisterResetCallback_m6C7853D22467C435203CECBE703D561E8B56ACAA (void);
extern void EventBusRegistry_UnregisterResetCallback_m342BA1B17E1933E9FEC244F0F8DB3D93548DD789 (void);
extern void EventBusRegistry_Reset_m6A7C6FD1E9397CD00AB7EBF83F98A7A8F5A26A67 (void);
extern void EventBusRegistry__ctor_mBBD18537BAD3FCD98B80E87FA93BC13DD7A73463 (void);
extern void EventBusRegistry__cctor_m9E5D450ABEC4D23E265CDD5A6374A5D374F46BD3 (void);
extern void OnResetCallback__ctor_m2EDB8669DF8246B4ECBD3CA8BD2151DC70B2FF8C (void);
extern void OnResetCallback_Invoke_mEB3B40B366C17A01388AF539F1E22251A7C1B3E9 (void);
extern void OnResetCallback_BeginInvoke_m8E254F51841595AC81F9775141EDE5B1AA992FA7 (void);
extern void OnResetCallback_EndInvoke_m24025A79BE8BE6470DB0699F689F350F31540A05 (void);
extern void UnsubscriberPool__cctor_m54D0EE577CC777903047CA1E7E3B07AE2415ACF6 (void);
extern void UnsubscriberPool_Get_m4F0BAFC94EDFDE8BC85879C12E7D0BE708BADD25 (void);
extern void UnsubscriberPool_Release_m9C0E8092F933B52EFA6099F495DEEB34AB039F74 (void);
extern void UnsubscriberPool_Clear_m67EFA629F192EE2A1DDB9F9F1DEA6EC64F141481 (void);
extern void UnsubscriberPool__ctor_m4EC4BCA518A09E75742FB76C65664191BF319EC8 (void);
static Il2CppMethodPointer s_methodPointers[32] = 
{
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_mB44254F75E2A952C73A8E207FF900809E76D09AC,
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_m6C71DB95393219594C0809438F3543C15F8E93E3,
	NULL,
	Unsubscriber_Dispose_mCCD048F3A4DFFB9391E74BBBF5CC03F61E93EDC0,
	Unsubscriber__ctor_m11B27CCC64445E9582A3F7F98B147AED3A31F344,
	NULL,
	NULL,
	EventBusRegistry_RegisterResetCallback_m6C7853D22467C435203CECBE703D561E8B56ACAA,
	EventBusRegistry_UnregisterResetCallback_m342BA1B17E1933E9FEC244F0F8DB3D93548DD789,
	EventBusRegistry_Reset_m6A7C6FD1E9397CD00AB7EBF83F98A7A8F5A26A67,
	EventBusRegistry__ctor_mBBD18537BAD3FCD98B80E87FA93BC13DD7A73463,
	EventBusRegistry__cctor_m9E5D450ABEC4D23E265CDD5A6374A5D374F46BD3,
	OnResetCallback__ctor_m2EDB8669DF8246B4ECBD3CA8BD2151DC70B2FF8C,
	OnResetCallback_Invoke_mEB3B40B366C17A01388AF539F1E22251A7C1B3E9,
	OnResetCallback_BeginInvoke_m8E254F51841595AC81F9775141EDE5B1AA992FA7,
	OnResetCallback_EndInvoke_m24025A79BE8BE6470DB0699F689F350F31540A05,
	UnsubscriberPool__cctor_m54D0EE577CC777903047CA1E7E3B07AE2415ACF6,
	UnsubscriberPool_Get_m4F0BAFC94EDFDE8BC85879C12E7D0BE708BADD25,
	UnsubscriberPool_Release_m9C0E8092F933B52EFA6099F495DEEB34AB039F74,
	UnsubscriberPool_Clear_m67EFA629F192EE2A1DDB9F9F1DEA6EC64F141481,
	UnsubscriberPool__ctor_m4EC4BCA518A09E75742FB76C65664191BF319EC8,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};
static const int32_t s_InvokerIndices[32] = 
{
	21384,
	13298,
	0,
	13298,
	13298,
	0,
	0,
	20847,
	20847,
	21355,
	13298,
	21355,
	5684,
	13298,
	4489,
	10682,
	21355,
	21274,
	20847,
	21355,
	13298,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
};
static const Il2CppTokenRangePair s_rgctxIndices[3] = 
{
	{ 0x02000006, { 6, 4 } },
	{ 0x0200000A, { 10, 16 } },
	{ 0x06000003, { 0, 6 } },
};
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass1_0_1_t52388F0BE03A6710E40947FC568C2920A5A03C58;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass1_0_1__ctor_mD9A4AEBBD2C6B68DD65784AFC96EC99B156792F7;
extern const uint32_t g_rgctx_EventHandler_t9E89EDE5F7129BBF19C28D714D1CB6FEF6392F1D;
extern const uint32_t g_rgctx_EventBus_1_Subscribe_m375BCE6E57EDC7AE8E28E4EE0F9CF549B3E3B72C;
extern const uint32_t g_rgctx_EventBus_1_t644C2E4170BAD2570F3B3083A474F8DC62D7C0BF;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass1_0_1_U3CAttachU3Eb__0_m4B8315BD501F2748D3481D7C58DFC6D068D3A5FE;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass1_0_1_tE4C2EC5615E1472ACDDE95B57B52D7D54BA446CF;
extern const uint32_t g_rgctx_EventHandler_tC7D9561C51C2A0FD32AA205BF8F40F6FA9D8A897;
extern const uint32_t g_rgctx_EventBus_1_Unsubscribe_m787E593EB77A59910507A5995A93F8EA2548B710;
extern const uint32_t g_rgctx_EventBus_1_tF5014BA3B2312D995C56BC5E7F5D373EF80B9FFB;
extern const uint32_t g_rgctx_List_1_t07CD475358B49F06EFCE172D7EEE6844E4C2B79D;
extern const uint32_t g_rgctx_List_1__ctor_m86FA2FCFDD306D445249046340F809BC4C8E36B6;
extern const uint32_t g_rgctx_EventBus_1_t27E6DF7E426FAFDA6447E143FDC45D802A51A576;
extern const uint32_t g_rgctx_EventBus_1_t27E6DF7E426FAFDA6447E143FDC45D802A51A576;
extern const uint32_t g_rgctx_EventBus_1_Clear_m3761396F53FB9F8E13C212725E5723458A30A3E0;
extern const uint32_t g_rgctx_List_1_Clear_mF991BAFC96569184B1FA534F2430AD33E10598FB;
extern const uint32_t g_rgctx_EventHandler_tB376439854D1B62FD806DD4AA24E23AC7549CF78;
extern const uint32_t g_rgctx_List_1_Contains_m43066E601AE59782041A03BEB0F308624A7BF4F4;
extern const uint32_t g_rgctx_List_1_Add_m428FD57A2AFEFF818709203949F3C1FF0F312F09;
extern const uint32_t g_rgctx_List_1_Remove_mAE672EE4AC7D3F533A6FE72250737EB87EAA008C;
extern const uint32_t g_rgctx_List_1_get_Count_m814FCD87A423F46E54ABD697E55E3576B93BB482;
extern const uint32_t g_rgctx_List_1_get_Item_mD50EE0B6AE9A7CCC03FD6377670913A6B395B2CD;
extern const uint32_t g_rgctx_List_1_RemoveAt_m63E6734FDCAD97F87EC1F942CD85DB67BB8F4D3E;
extern const uint32_t g_rgctx_T_tACDA08FE13AFA701B8DBEAE00A8E71350C1A14F9;
extern const uint32_t g_rgctx_EventHandler_Invoke_m71F66785210BEAB61977F11F0A7B8D01B336D780;
extern const uint32_t g_rgctx_Unsubscriber_Attach_TisT_tACDA08FE13AFA701B8DBEAE00A8E71350C1A14F9_m3EFB187F4E324CA0B3D38397664A0A750748219B;
static const Il2CppRGCTXDefinition s_rgctxValues[26] = 
{
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec__DisplayClass1_0_1_t52388F0BE03A6710E40947FC568C2920A5A03C58 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec__DisplayClass1_0_1__ctor_mD9A4AEBBD2C6B68DD65784AFC96EC99B156792F7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_EventHandler_t9E89EDE5F7129BBF19C28D714D1CB6FEF6392F1D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_EventBus_1_Subscribe_m375BCE6E57EDC7AE8E28E4EE0F9CF549B3E3B72C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_EventBus_1_t644C2E4170BAD2570F3B3083A474F8DC62D7C0BF },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec__DisplayClass1_0_1_U3CAttachU3Eb__0_m4B8315BD501F2748D3481D7C58DFC6D068D3A5FE },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec__DisplayClass1_0_1_tE4C2EC5615E1472ACDDE95B57B52D7D54BA446CF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_EventHandler_tC7D9561C51C2A0FD32AA205BF8F40F6FA9D8A897 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_EventBus_1_Unsubscribe_m787E593EB77A59910507A5995A93F8EA2548B710 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_EventBus_1_tF5014BA3B2312D995C56BC5E7F5D373EF80B9FFB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_List_1_t07CD475358B49F06EFCE172D7EEE6844E4C2B79D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1__ctor_m86FA2FCFDD306D445249046340F809BC4C8E36B6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_EventBus_1_t27E6DF7E426FAFDA6447E143FDC45D802A51A576 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_EventBus_1_t27E6DF7E426FAFDA6447E143FDC45D802A51A576 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_EventBus_1_Clear_m3761396F53FB9F8E13C212725E5723458A30A3E0 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_Clear_mF991BAFC96569184B1FA534F2430AD33E10598FB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_EventHandler_tB376439854D1B62FD806DD4AA24E23AC7549CF78 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_Contains_m43066E601AE59782041A03BEB0F308624A7BF4F4 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_Add_m428FD57A2AFEFF818709203949F3C1FF0F312F09 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_Remove_mAE672EE4AC7D3F533A6FE72250737EB87EAA008C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_get_Count_m814FCD87A423F46E54ABD697E55E3576B93BB482 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_get_Item_mD50EE0B6AE9A7CCC03FD6377670913A6B395B2CD },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_RemoveAt_m63E6734FDCAD97F87EC1F942CD85DB67BB8F4D3E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tACDA08FE13AFA701B8DBEAE00A8E71350C1A14F9 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_EventHandler_Invoke_m71F66785210BEAB61977F11F0A7B8D01B336D780 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Unsubscriber_Attach_TisT_tACDA08FE13AFA701B8DBEAE00A8E71350C1A14F9_m3EFB187F4E324CA0B3D38397664A0A750748219B },
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_Events_CodeGenModule;
const Il2CppCodeGenModule g_Events_CodeGenModule = 
{
	"Events.dll",
	32,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	3,
	s_rgctxIndices,
	26,
	s_rgctxValues,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};

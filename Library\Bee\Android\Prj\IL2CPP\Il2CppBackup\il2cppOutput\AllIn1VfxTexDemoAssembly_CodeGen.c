﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_mAE4E2A66CF010E8C2BED7DB9C2F20A6F14EFC515 (void);
extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_mE44828C7C605830E75A884206604FB7980FBE0C9 (void);
extern void All1VfxDemoTextureCollection__ctor_m8701222B42461E227951BBD192B9872A7F3899DD (void);
extern void AllIn1DemoScaleTween_Start_m0B21571E9F74A64C41AB87F5BF24F8813A256A8C (void);
extern void AllIn1DemoScaleTween_Update_m3938FE1C4E5421116868A2852379E0925CBA7D27 (void);
extern void AllIn1DemoScaleTween_UpdateScaleToApply_m4666E0FEAF897EF577E86E65146419F2E1624E28 (void);
extern void AllIn1DemoScaleTween_ApplyScale_mD52056E0897F1E588776D833842CAEB34C2C8280 (void);
extern void AllIn1DemoScaleTween_ScaleUpTween_mC906F2123285187BF15A61CAA9E17E17D1C2ED2C (void);
extern void AllIn1DemoScaleTween_ScaleDownTween_m6D50DF50CE6CD6C94F386058A877DED2C1C1C089 (void);
extern void AllIn1DemoScaleTween__ctor_m4A6D3B174060E167607564338E4FD923198C4DB2 (void);
extern void AllIn1VfxTextureDemoManager_Start_m36EFD84845B42769AE25B29548B0EA6D099E5C4C (void);
extern void AllIn1VfxTextureDemoManager_Update_m3F9F917A13101C949BB72AEE73750A44417B8CD5 (void);
extern void AllIn1VfxTextureDemoManager_ChangeTextureIndex_m15CD112C0A31ED729063C634DFBD795618EDCC60 (void);
extern void AllIn1VfxTextureDemoManager_ChangeCollectionIndex_m0D3E2B08840AC6D6D90D2979C0B6279E8B098251 (void);
extern void AllIn1VfxTextureDemoManager_RefreshCollectionAndPageText_m8890CBBA19E8AB449B2B80C394410E7197CE3954 (void);
extern void AllIn1VfxTextureDemoManager_AssignCurrentImages_mFD05381DC5C95EA73ABD1CF331012BE5DD80B800 (void);
extern void AllIn1VfxTextureDemoManager__ctor_mC15E39C98457B1B06DF25F6AB77FF3D50FF649AA (void);
static Il2CppMethodPointer s_methodPointers[17] = 
{
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_mAE4E2A66CF010E8C2BED7DB9C2F20A6F14EFC515,
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_mE44828C7C605830E75A884206604FB7980FBE0C9,
	All1VfxDemoTextureCollection__ctor_m8701222B42461E227951BBD192B9872A7F3899DD,
	AllIn1DemoScaleTween_Start_m0B21571E9F74A64C41AB87F5BF24F8813A256A8C,
	AllIn1DemoScaleTween_Update_m3938FE1C4E5421116868A2852379E0925CBA7D27,
	AllIn1DemoScaleTween_UpdateScaleToApply_m4666E0FEAF897EF577E86E65146419F2E1624E28,
	AllIn1DemoScaleTween_ApplyScale_mD52056E0897F1E588776D833842CAEB34C2C8280,
	AllIn1DemoScaleTween_ScaleUpTween_mC906F2123285187BF15A61CAA9E17E17D1C2ED2C,
	AllIn1DemoScaleTween_ScaleDownTween_m6D50DF50CE6CD6C94F386058A877DED2C1C1C089,
	AllIn1DemoScaleTween__ctor_m4A6D3B174060E167607564338E4FD923198C4DB2,
	AllIn1VfxTextureDemoManager_Start_m36EFD84845B42769AE25B29548B0EA6D099E5C4C,
	AllIn1VfxTextureDemoManager_Update_m3F9F917A13101C949BB72AEE73750A44417B8CD5,
	AllIn1VfxTextureDemoManager_ChangeTextureIndex_m15CD112C0A31ED729063C634DFBD795618EDCC60,
	AllIn1VfxTextureDemoManager_ChangeCollectionIndex_m0D3E2B08840AC6D6D90D2979C0B6279E8B098251,
	AllIn1VfxTextureDemoManager_RefreshCollectionAndPageText_m8890CBBA19E8AB449B2B80C394410E7197CE3954,
	AllIn1VfxTextureDemoManager_AssignCurrentImages_mFD05381DC5C95EA73ABD1CF331012BE5DD80B800,
	AllIn1VfxTextureDemoManager__ctor_mC15E39C98457B1B06DF25F6AB77FF3D50FF649AA,
};
static const int32_t s_InvokerIndices[17] = 
{
	21380,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	10629,
	10629,
	13298,
	13298,
	13298,
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_AllIn1VfxTexDemoAssembly_CodeGenModule;
const Il2CppCodeGenModule g_AllIn1VfxTexDemoAssembly_CodeGenModule = 
{
	"AllIn1VfxTexDemoAssembly.dll",
	17,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};

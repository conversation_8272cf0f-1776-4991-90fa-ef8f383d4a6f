﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void PerlinNoise__ctor_mB0CCE9CA931D37CE3B7B52E8F7ED7F5766DBEB70 (void);
extern void PerlinNoise_Seed_m311A3ECA976C2CA7C665F7C203D8322D09114DD9 (void);
extern void PerlinNoise_Noise_mD231D26E9C05136AB6172829BF6BE54112E5B9F6 (void);
extern void PerlinNoise_FractalNoise_m2A931315B4909A7422FD87281AF2403DD43D4E08 (void);
extern void PerlinNoise_InitGradients_mCB0C6153608CBC1D5E9AD5FED233643B478FE2A0 (void);
extern void PerlinNoise_Permutate_m07B4F7083FC37342CA364733B2A958EDA004BEB4 (void);
extern void PerlinNoise_Index_m1FFB41A4572436A5B270DF35DD5761F43BCD7FCE (void);
extern void PerlinNoise_Lattice_m39FE544BEAF55B53E4B5E0B64493B5DEF740893C (void);
extern void PerlinNoise_Smooth_mB94BBD863BC0F314DEDCDCF6E802D069E30DD175 (void);
extern void Puffy_Cloud_Awake_m36C1833D62D44C2AB8F9C8A36ADE1B981CFD6EF2 (void);
extern void Puffy_Cloud_Build_m92370075A92549D334C794793E6E0D0CD4E35513 (void);
extern void Puffy_Cloud_Update_mE91B5CE660D407795B481A8E8875F4E94597D40B (void);
extern void Puffy_Cloud_OnDestroy_m9F632EE24213A3067651CA0E2D0726334B734908 (void);
extern void Puffy_Cloud__ctor_mEFD09F81046404E9FF3D8AFBDD8A44CD2A40C216 (void);
extern void Puffy_Emitter_Awake_m0F62C51AC4FDE4533229C5B51D0A01AB71918CE2 (void);
extern void Puffy_Emitter_Start_mD535996ACE0F9D15B3001F74D23A462E015504C3 (void);
extern void Puffy_Emitter_WarmUp_mA57AE21707D5CABD3BFC404A68E8EE609FEF8E03 (void);
extern void Puffy_Emitter_Clear_m97B91D82E3DDFE158ADA7DE4E18D12E20ED0E559 (void);
extern void Puffy_Emitter_Kill_m84ABF2E018E1A5DD97D5B1B2C62A877F3D6D4D9B (void);
extern void Puffy_Emitter_Resurrect_m86466B6D6B9169D00B5CB65BF3916AD4A8A2A598 (void);
extern void Puffy_Emitter_Link_m0145A151E48238F73EACF360F80159B268D0CCFE (void);
extern void Puffy_Emitter_Link_m196425023CF56C95086042A75971735C9326037A (void);
extern void Puffy_Emitter_Link_mC82BDF8E3AC910C05F5C528F6070BC9FFC893D87 (void);
extern void Puffy_Emitter_UnLink_mCF79DEF105BA54B0531CD533D1B71779814906D0 (void);
extern void Puffy_Emitter_Link_mE2E3E99AD67D6603FDF219CF9AEDD1C9805ED014 (void);
extern void Puffy_Emitter_UnLink_mDA4E867EE0D21FD046520F12380B64AF19BDF3B6 (void);
extern void Puffy_Emitter_GetActiveGradient_m259DBB6A51972CB8F8F4C29A931A8D5B7DDE4941 (void);
extern void Puffy_Emitter_LateUpdate_m281845EB3E484A92CEFE9F758D143EAA96B5F5E4 (void);
extern void Puffy_Emitter_Threaded_UpdateParticlesTask_m0FA4C39BC684C2375A11C8157401E8AA60BEA54D (void);
extern void Puffy_Emitter_UpdateParticles_m3D087C81A01F59B94AA92A4B5F121BA979B81BA1 (void);
extern void Puffy_Emitter_OnDrawGizmos_mE83EBBA0E58141E8E1FAEB06057AD248105CFCA0 (void);
extern void Puffy_Emitter_OnGUI_m2A06789B10BCEB8ADC87AEC2F055604092A9D974 (void);
extern void Puffy_Emitter_UpdateParticlesTask_m67513F068B4DE701B644C27C773D15AAAE3E915A (void);
extern void Puffy_Emitter_KillParticle_mDA3E4D54EBAC2663F711E40185C3200844B362FD (void);
extern void Puffy_Emitter_SpawnParticle_m6B1569E27C9A848DE0AB532C2B8385E6F716AD6D (void);
extern void Puffy_Emitter_SpawnParticle_m190C8F10895AF603A09B59B65E5D811602C634C2 (void);
extern void Puffy_Emitter_SpawnParticle_mAD293B673EFFCBD5BB5671003D8680B0953FEEA1 (void);
extern void Puffy_Emitter_FastSpawn_m2D6FF84BE229E5EC2947F622F8A4D7E7929B3A27 (void);
extern void Puffy_Emitter_SpawnRow_m44AE824B9E145A3EF2E59BEDE046B0BD014EC8DF (void);
extern void Puffy_Emitter_SpawnRow_m32A289F44D701453CB08E883586B697CB7453FBB (void);
extern void Puffy_Emitter__ctor_m0CF869C2DC3AD8FC0CED7FAC25948F4026BA098B (void);
extern void PointerComparer_Compare_m23E9A70D91A92901F8928A0ED37254B873325D00 (void);
extern void PointerComparer__ctor_mD57980B91454D43019C2D442826E024212C23EA2 (void);
extern void PointerGroup__ctor_m2E6CEEB4FE9E0CA04A9E25DB4DF8CBDBB0AD36B8 (void);
extern void Puffy_ParticleData__ctor_m1C7097C9459D5106A24615666BAE76557B531BF7 (void);
extern void Puffy_ParticleData_Spawn_m88D1AB3D6557619CC6BF06C8DA6C1FDD16543995 (void);
extern void Puffy_ParticleData_Spawn_m8D4C7C2EC22F88D6E009F515610CFCFDA5A5BFA3 (void);
extern void Puffy_ParticleData_Spawn_m75938242C5488E94647FD68F25D39371AC2EF501 (void);
extern void Puffy_ParticleData_Update_m14B384DF2ED4BBA24F432E69359C5AA1A2440667 (void);
extern void Puffy_ParticleData_Kill_m622E5F4230F1A4AD3E915EBAF72DDB68A24DEAEE (void);
extern void Puffy_Gradient_Start_mA9B8C3A2703B7EC87E60E32EB1E171D577C421E7 (void);
extern void Puffy_Gradient_OnValidate_m99FCFD330754256780D56D94579F157BAA53A581 (void);
extern void Puffy_Gradient_UpdateLookup_m582D961F7CCD4CACBB98CBBD184CC76BF5361735 (void);
extern void Puffy_Gradient_Evaluate_m9DFAE796047DAB6D856665760F94DE507E9ADC22 (void);
extern void Puffy_Gradient__ctor_mEC347C18E882168FE5F5A0C57542C52EDBEF3686 (void);
extern void Puffy_MeshSpawner_Start_mFF148FDD62B81B34EBF243FD127E6BB325BA25E3 (void);
extern void Puffy_MeshSpawner_Init_m00716C20ABA4549CF9CF5CEC8500AB39E57F5095 (void);
extern void Puffy_MeshSpawner_Smooth_m4D99E1A0E0775958CC35D2EEA97FC5574E9EC9E1 (void);
extern void Puffy_MeshSpawner__ctor_m870A26C85584BE951C80BE18AAE26675BE8C0D89 (void);
extern void VertexData__ctor_m4DDB93C05B40A4DBA03557489BBC3EE7BEBA9F9F (void);
extern void Puffy_MultiSpawner_Awake_m0A699C1FA4BDBBC2B31BC0AD8C417D2A28E1BA9A (void);
extern void Puffy_MultiSpawner_AddSpawner_m9A71E8B182CE8DFEC141F14D398B921EF570C239 (void);
extern void Puffy_MultiSpawner_RemoveSpawner_m6D6752F507BC69623DACDC2EC6F5235D17E840BB (void);
extern void Puffy_MultiSpawner_CreateSpawner_mFE728EC240C62B178B16D8F6AC77E6F335E36D90 (void);
extern void Puffy_MultiSpawner_GetNextFreeSpawner_m5321072EDD775F554C2F2842D4FA7A750AEAADF5 (void);
extern void Puffy_MultiSpawner_MakeSpawner_m9E670C9F0EF37C1D8CA7CE435D80071062038EDD (void);
extern void Puffy_MultiSpawner_DoUpdate_m0FEE608DB3278FA0C2D2CE298883119E8593B54C (void);
extern void Puffy_MultiSpawner_OnValidate_m44DAB07ABB1AC94834A51A1F86E657D7455F575E (void);
extern void Puffy_MultiSpawner_OnDestroy_mA78A6DD0911B82EAFEF4BBA4AF4CC5B88CA9898A (void);
extern void Puffy_MultiSpawner_OnDrawGizmos_mC13DA920C78BAEF6906ED58CBF013D1A0FF86AF8 (void);
extern void Puffy_MultiSpawner__ctor_mFACEB1DBBFE1FF59E8E4647E8064C3AF52C5937E (void);
extern void Puffy_ParticleSpawner_Awake_mEBCF81317CC1B6561EB5D312518D3DDDAF6BDCA5 (void);
extern void Puffy_ParticleSpawner_OnValidate_m8E0141BE815610BF5E199F85A7A9444666B063D0 (void);
extern void Puffy_ParticleSpawner_UnsetMultiSpawner_mE8E8F176F4AF22364B88FEAF9B37B0E9CD68F7DC (void);
extern void Puffy_ParticleSpawner_SetMultiSpawner_m25FB1BFE41E6D4387D76AD69C4BCA1A8359E08FE (void);
extern void Puffy_ParticleSpawner_Start_m571EED6AB107B9E1E85D311A104A1E92375C75B6 (void);
extern void Puffy_ParticleSpawner_InitSpawnPoint_m8096B1899B6C01AA6CDA1473B6B9C1E9525B4288 (void);
extern void Puffy_ParticleSpawner_UpdateSpawnPoint_mFADB0FEE42AEA0F0F80A8F660BC4EECAC47676BC (void);
extern void Puffy_ParticleSpawner_UpdateSpawnPoint_mC9D9DD134CBABC426AE9F7678DD180828E6C713E (void);
extern void Puffy_ParticleSpawner_DoUpdate_mB02B02E0CA7380A18102517445C7A4F6B504F77C (void);
extern void Puffy_ParticleSpawner_Sleep_mEF2282542FA51A5CA88B666D8A71D433E7ACFD5F (void);
extern void Puffy_ParticleSpawner_Wake_m4E832DF0B4E59576379D7C95E39EA5EEF07B509B (void);
extern void Puffy_ParticleSpawner_OnDestroy_mDD23C6F1856760C8881A4051996345F7D0BA425F (void);
extern void Puffy_ParticleSpawner_OnDrawGizmos_m382788BEC36FDD0CB1B02459B866F544053FD430 (void);
extern void Puffy_ParticleSpawner__ctor_m7E252CC17C1F70D82864DB32CC6C5CCD4C1A5BA4 (void);
extern void Puffy_PerlinCloud_Awake_m6AA80562D76CA4595A61ED4DFBEE5F12E749C862 (void);
extern void Puffy_PerlinCloud_Start_m76CFBE311074E00D88B8B24B4348443752DBDA4B (void);
extern void Puffy_PerlinCloud_Init_mB693D238ED5299A4265B6B6D53BEB14A8B84F6EF (void);
extern void Puffy_PerlinCloud_Build_m867B886977DF5B2439447DFE5D92EE653618EE88 (void);
extern void Puffy_PerlinCloud_Update_mA8F21DF38E2BFBD20B926B62012BB5BA0ECB5754 (void);
extern void Puffy_PerlinCloud_OnDestroy_m9A0C0F0ADF5FAA6E9E48E9545C40D9E90ED3F43A (void);
extern void Puffy_PerlinCloud__ctor_mC96008C173680096B40F310CCE4C23B6D3FAB555 (void);
extern void Puffy_Renderer_ToggleDebug_mF8F2F77E5BA23D97965BB98D07F41DB3158265A7 (void);
extern void Puffy_Renderer_GetCloudRenderer_m98CC325709910DEC89FB8AAFD043BDB54FDA1334 (void);
extern void Puffy_Renderer_GetRenderer_mD16A08482E446C461A0860AFC3FA61687025BA9C (void);
extern void Puffy_Renderer_GetRenderer_mEA4D8FFF6CF3B4A01AC7D47192E9AC097F3DC941 (void);
extern void Puffy_Renderer_GetRenderer_m4CF26DA6FB03DEC5E96C1F544678BFD4D1CC7A26 (void);
extern void Puffy_Renderer_OnDestroy_m7724C1F5C1B846ADAF959D2F172AE63B8216FDE1 (void);
extern void Puffy_Renderer_Awake_m8D379ADC0B950D6BCF3E39CDC53DCBB2570060E8 (void);
extern void Puffy_Renderer_OnGUI_m7D4692818851A05D995278774D7B7261280F247B (void);
extern void Puffy_Renderer_OnDrawGizmos_m90FDC9E081D540AF06667836F2361E98DCB19379 (void);
extern void Puffy_Renderer_AddEmitter_m0A72AEDD32C65061D2DECBCDE638D74FC43ABCAC (void);
extern void Puffy_Renderer_RemoveEmitter_mA61C1EBA497972B367D4B1B441D2336E26385EF7 (void);
extern void Puffy_Renderer_RemoveEmitter_mC63A8531170ADAC21C7EB73EA0CA2635EA124E5A (void);
extern void Puffy_Renderer_RemoveEmitter_m39A913EA09A8636311EE7390E386A3BE981D89DF (void);
extern void Puffy_Renderer_LateUpdate_m39A20D33ACF08552BFE49A199E593D677ABA891E (void);
extern void Puffy_Renderer_Render_mD572DF6F38B7FDB3E0908A0B7E62234DCF8F0CEB (void);
extern void Puffy_Renderer_updateBillboardsData_mBBF6BB727DB920F58584E835CB96DDA7EB0A2490 (void);
extern void Puffy_Renderer_SolveBillboardTangents_m520B44BDFD71D733331D0E0755DF99DE1006C5EE (void);
extern void Puffy_Renderer_FrustumCheck_mDE8929D2A1FC0DCFF3497FADFC12A4A7F955E627 (void);
extern void Puffy_Renderer_Threaded_FrustumTask_m05AD5B107F21EBFCD4598C754048DFECED11FDB3 (void);
extern void Puffy_Renderer_FrustumTask_m5620C85E151F4911A59DCE41969DEA916A7DEC14 (void);
extern void Puffy_Renderer_Threaded_SortParticlesTask_m83E8FD1779C8788D52BAB1DFCFA78F9CBD70A0FD (void);
extern void Puffy_Renderer_SortParticles_m6482F6BC79692561BED7B5CA93B7007AF26BB46D (void);
extern void Puffy_Renderer_AddMesh_m193B48210213D36FE1968A6B6022B1711BC35B08 (void);
extern void Puffy_Renderer_MergeGroups_m164895A6B06A0E8B584DED40D404FF79B9A4E39D (void);
extern void Puffy_Renderer_UpdateMaterial_m72D9008F0552E10CA09F3FF1B800D82DE0268EB0 (void);
extern void Puffy_Renderer_Threaded_BuildMeshesTask_mB9AA8C016C0AFD3CE1D80375B1295CD99E838047 (void);
extern void Puffy_Renderer_BuildMeshesThreads_mFF1F201B0E14A26AED45C04D504B9442A6A72CC8 (void);
extern void Puffy_Renderer_BuildMeshes_task_m41D204CEE920BE2855D1D8071DE0FD3CC1966B65 (void);
extern void Puffy_Renderer_Threaded_UpdateMeshesTask_mA86858E9D230A1E7C828062CC4341DA64826DCFB (void);
extern void Puffy_Renderer_UpdateMeshes_m03BF5EE9857AF42C548C010DBC5ADE561637868D (void);
extern void Puffy_Renderer__ctor_mFAB43F41DCD6DE0CF36DF38FC89E0C5CF454B5D3 (void);
extern void Puffy_Renderer__cctor_m85C991AEFF9A665C1DFB8E9746C6B00034549D2E (void);
extern void SortGroup__ctor_mCE37896571DB2B870A46178C8272E09E3FBF6CDE (void);
extern void SortGroup_Sort_m16A38A32A5C70CA4966BF09AC958EA3ED713EAB5 (void);
extern void SortGroup_Init_m617C13B029B212AE272A7FEE46D9D298EAD280E1 (void);
extern void SortGroup_RadixSortAux_m206E32EBC7D3DB430E3FFC70284825B4E82D48B7 (void);
extern void SortGroup_CountingSort_m9957E3FB0DCE05E46E78995C4828D06219C3CCE6 (void);
extern void SortIndex__ctor_mDC569AFA01D17CBC5871282FB271D9C743086C49 (void);
extern void OrderComparer_Compare_mBD6F249583559133C7B56019BD98AC5A8325C2DE (void);
extern void OrderComparer__ctor_m000E9B1F6D08497BA3E7A7CFBA3FCECB566B27DF (void);
extern void VariableMesh__ctor_mE16AAEAC10B9DBDC9529937ED69688A36027A4AD (void);
extern void VariableMesh_getMeshDataIndex_mFC14EBE06353B1BEC6A5791B9B2A2774D88FD4F4 (void);
extern void VariableMesh_getMeshData_m69A786A38D7CC0E2E1F2244305EECEA86BCE4DAB (void);
extern void VariableMesh_Init_mBDBF82FEDBF35F299290FF04BBAA26B4B5180A7C (void);
extern void VariableMesh_ClearMesh_mF1B06714DEE516F8153BEC13CDEB384A5AF52760 (void);
extern void VariableMesh_UpdateMesh_Step1_m70B9BAAD7237728C5D876BD77B585835E2E910D8 (void);
extern void VariableMesh_UpdateMesh_Step2_m2AB5AD3A2C336C77A35901E323BA70B87E00A7DE (void);
extern void VariableMesh__cctor_mFE1BDEABC9BA8EA8C49E2560DD22FDE7D5FB27C5 (void);
extern void VariableMeshData__ctor_m4D89710102A2EFD55D4721478AE5FD3A0A8CEBBC (void);
extern void DebugTimer_Start_m5F7F0B39663438373A5B44833959532A117D267C (void);
extern void DebugTimer_Stop_m537C226726BC90724D65CF5723EAA29FB7C7D825 (void);
extern void DebugTimer_Average_mA15771475471BE12892572EC434FC8785C082FCC (void);
extern void DebugTimer_ShowGraph_mB1509C8DFDDDBA087805889D276E777C7CFC4C25 (void);
extern void DebugTimer__ctor_m8087E232341BD4B2B0E40C7E63EF9A1D169BDB2F (void);
extern void Puffy_ShapeSpawner_Awake_mDAF10D878F76D0BA2E0E1D4A256D95EB3AE911A8 (void);
extern void Puffy_ShapeSpawner_OnDestroy_m56ED9F77F9E560E9FC6497EDFC8674F5860B94D1 (void);
extern void Puffy_ShapeSpawner_Init_mDD220D4CAE8A95561956E2CA4C35F8A9C707B33B (void);
extern void Puffy_ShapeSpawner_DoUpdate_mF4A1921F8D5793721FFAE374E7A3D71DE9C65ED7 (void);
extern void Puffy_ShapeSpawner__ctor_mC7B6EECAA47AD9D4BF7FC1D8C7A1C1CA365BB186 (void);
extern void MECExtensionMethods2_CancelWith_m87B7F9CA443158DBCDEE4BB6BA9900FCFBE6CEC3 (void);
extern void MECExtensionMethods2_CancelWith_m5FC8E9E0BF0AF9310D0FA7154C4B9093BBBFDD35 (void);
extern void MECExtensionMethods2_CancelWith_m8F7FE1DF1A42265216271B287729242E2AABBDD6 (void);
extern void U3CCancelWithU3Ed__0__ctor_mD1FD76800298A0D9A5CE80E86F62EE2818E6E7DE (void);
extern void U3CCancelWithU3Ed__0_System_IDisposable_Dispose_m13F848E7AFD31EA7991D5E85F6F4DFA41309F198 (void);
extern void U3CCancelWithU3Ed__0_MoveNext_m2FF006D6533F26F1F329A224004D171E0D1EB974 (void);
extern void U3CCancelWithU3Ed__0_System_Collections_Generic_IEnumeratorU3CSystem_SingleU3E_get_Current_mBA68D0B2229FE194CF0A41563FDD64978376505F (void);
extern void U3CCancelWithU3Ed__0_System_Collections_IEnumerator_Reset_mC11674E8852C9735E5EEFF2696D61290A0689AA2 (void);
extern void U3CCancelWithU3Ed__0_System_Collections_IEnumerator_get_Current_mB1E5A3EEA8FA441698CFA684A2044FAE69FEB162 (void);
extern void U3CCancelWithU3Ed__1__ctor_mA1E5A592F03F092BDE452DCCF6BF57ACBF6B3DD9 (void);
extern void U3CCancelWithU3Ed__1_System_IDisposable_Dispose_m859E1797DB9EAEE4D0AE87F3797EF06F985F00C0 (void);
extern void U3CCancelWithU3Ed__1_MoveNext_mC98A04B97394553695C6802894E79F749D5F141C (void);
extern void U3CCancelWithU3Ed__1_System_Collections_Generic_IEnumeratorU3CSystem_SingleU3E_get_Current_mD70FB2F66638127BABE47CBD2EE55233A115D3DC (void);
extern void U3CCancelWithU3Ed__1_System_Collections_IEnumerator_Reset_mE4948E9B9E98B7E553AE4372EDDE0B90E7EFA4EE (void);
extern void U3CCancelWithU3Ed__1_System_Collections_IEnumerator_get_Current_mFD1752A1FE5FBB25E6B10B1B6732B2171F4656F1 (void);
extern void U3CCancelWithU3Ed__2__ctor_mFEA0FD042A7C4AF11BFE1484CFEDD37853A84D78 (void);
extern void U3CCancelWithU3Ed__2_System_IDisposable_Dispose_m405675EB80AB2D8E463C714B3330C19E182B1C18 (void);
extern void U3CCancelWithU3Ed__2_MoveNext_m18A06CC42145B367082A83B0ADD2CA90F2643ACF (void);
extern void U3CCancelWithU3Ed__2_System_Collections_Generic_IEnumeratorU3CSystem_SingleU3E_get_Current_m280EF60C62993FD57D81EB153FC1D93B293D8986 (void);
extern void U3CCancelWithU3Ed__2_System_Collections_IEnumerator_Reset_mD41F142EB9BABD3D7655BA6B81D3CA0DDAF526AE (void);
extern void U3CCancelWithU3Ed__2_System_Collections_IEnumerator_get_Current_mA9DA12EC5F0AB8BB789D8F23EB2F419A287E4642 (void);
extern void MouseLook_Update_m3D3D51464C33641FE42365A4BEAB7223CBE5A933 (void);
extern void MouseLook_Start_m10D8390C49B3ADBB62155CF87FDB1D21974FB4C5 (void);
extern void MouseLook__ctor_m437956CB4A17EB38EB1AD1B1C7FEBEB63D62A555 (void);
extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_mC7CA174A23290C34424DF6D2733D5E64B92E5977 (void);
extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_m3C9D09F94200334DD5FA29A465481C7848AF4549 (void);
extern void Timing_get_LocalTime_m9C49480299611C87C26CF1B8ABFF39EBE066B05F (void);
extern void Timing_get_DeltaTime_m840C0FD73C53D54D09C3BB6F39D048720B3B666E (void);
extern void Timing_add_OnPreExecute_mFB308CDAF20B4ADAFC9EC8D90290FF4AB2C0289F (void);
extern void Timing_remove_OnPreExecute_m86137AB1D52C22AFA6BF84EA74ABE2D0E179E99E (void);
extern void Timing_get_MainThread_m80634C20653DBF90625830B7D92DC0287969783B (void);
extern void Timing_set_MainThread_m092B792966E3FFF6ADB824CE916D72EF6496CFC2 (void);
extern void Timing_get_CurrentCoroutine_m2B19EBA8F7AB1AB577CB1AE9BF866A052C93BEA7 (void);
extern void Timing_get_currentCoroutine_m1779C17402C0C227C7E80A36BB865818DBE9BEEF (void);
extern void Timing_set_currentCoroutine_m3DBBE23CAA1674B5622501475023ECCF1BFAD433 (void);
extern void Timing_get_Instance_m2EEDAE6600D6BF2E9AA0FC443E8401831BD6AEBF (void);
extern void Timing_set_Instance_m6BC74F4B64497965433B61E174600FB67848F69E (void);
extern void Timing_OnDestroy_m92DEE85A3B3BC195C9D62B422B55E101F4ED3DD0 (void);
extern void Timing_OnEnable_mEDCDD8D274AF1DC8E444AA102FA0BD351F83F8DD (void);
extern void Timing_OnDisable_m706C02C5EF51428DE051F14E94AB8C2B3FA795C7 (void);
extern void Timing_InitializeInstanceID_mE7B74EE61B61BF72574985CF7998A6138B7F0FFB (void);
extern void Timing_Update_mE7D1CF9936AAE1B66BCA12AB8C877445C9BCBF4A (void);
extern void Timing_FixedUpdate_mD22C57426883A9C98D287E1F29607D94B4D369E9 (void);
extern void Timing_LateUpdate_mBFC7B68DEA5A814E39B6579BE4152EC1E01ED58A (void);
extern void Timing_RemoveUnused_m0BA36387FAA8CDAA62CF7528028FBEBA9BFA2492 (void);
extern void Timing_RunCoroutine_m1BAAAA0B1ECA15199AA277A76EFDED9F760A86EC (void);
extern void Timing_RunCoroutine_mFD2C706624ACC224D704F547E35EE4BFA91D601B (void);
extern void Timing_RunCoroutine_m4BFEDB3D901FB97F00121727C4D5F825B88DAA39 (void);
extern void Timing_RunCoroutine_mB876598DE22C1C5F76B443F9B934344EB94F2139 (void);
extern void Timing_RunCoroutineOnInstance_mD720F0E125B8E0B1B332C03CC0B79B4797AA8D1F (void);
extern void Timing_RunCoroutineOnInstance_m7F0B219ABDD1304682EE6A525E66A624E718F294 (void);
extern void Timing_RunCoroutineOnInstance_m2DF151338C742F53D0D8DD15C7FB624CBBD54FFE (void);
extern void Timing_RunCoroutineOnInstance_m2FAC3A4F1DA222777634572328C484CDE84CE424 (void);
extern void Timing_RunCoroutineInternal_m2E3890CC6A46C1D584341409AE925FD266162C9A (void);
extern void Timing_KillCoroutines_mAF41B8396F0AC0041747239882993C24DFB31707 (void);
extern void Timing_KillCoroutinesOnInstance_mE11A9993D825C3936E4AAF9E24527DE44AA182A4 (void);
extern void Timing_KillCoroutines_mE3D69F732ABD655ED55CDDD3B841F13B425C46A5 (void);
extern void Timing_KillCoroutinesOnInstance_m2FBCFCA895C9A7EF1AB2FD59D13503CDA9D69844 (void);
extern void Timing_KillCoroutines_mD5F64F5D40C6BBEA38F834680516F2D4ADD51640 (void);
extern void Timing_KillCoroutinesOnInstance_mA63811211C30FE8B7E386311B11E6221BF51EF76 (void);
extern void Timing_PauseCoroutines_m55CFEC6E6C556BDE33ADD43FE24A0A04C186057C (void);
extern void Timing_PauseCoroutinesOnInstance_m406092546839B370231CFE599E70361206338083 (void);
extern void Timing_PauseCoroutines_m5AA3111734731B316A516271D725F6ACE632AE8B (void);
extern void Timing_PauseCoroutinesOnInstance_mB57247C3E73805B70562C7BA78490D8B0A3B83AA (void);
extern void Timing_PauseCoroutines_m34D5555488A0BD53E8136E8F8A7726217743E500 (void);
extern void Timing_PauseCoroutinesOnInstance_m5B0C80D51BA2368FB873042D29F215530BCCD015 (void);
extern void Timing_ResumeCoroutines_m90365DD8F2F45AAD458E290AED34B5C39AC8C26C (void);
extern void Timing_ResumeCoroutinesOnInstance_m8AD21FD8F5D9CB63DCA50A03E14C8FE346809E11 (void);
extern void Timing_ResumeCoroutines_m4F8BE763CF48A5856143A86B8F0037F4F3215B1D (void);
extern void Timing_ResumeCoroutinesOnInstance_m7A77DF92C88FBD5A44E7255966AF15A55578E838 (void);
extern void Timing_ResumeCoroutines_m82CDBF6C566EB38666A7CEE07004D9F70881EDB9 (void);
extern void Timing_ResumeCoroutinesOnInstance_m55F40934D505D01E67E736CFC92443E3DEB9FC21 (void);
extern void Timing_UpdateTimeValues_m99DABFAC068CCA50B69C9C9F60405097A453EF72 (void);
extern void Timing_GetSegmentTime_m213929DBDFB5588571671D9AF28FB3EC95ABEBF6 (void);
extern void Timing_GetInstance_mB6312A6787BBB0E076E944F9970E6790EED86F94 (void);
extern void Timing_AddTag_m8D8BA724E402C4D46A72D82B60293D33A8833F16 (void);
extern void Timing_RemoveTag_mB94759CAF68B83C3FF72A7808FE4D2153307E0FA (void);
extern void Timing_Nullify_m9B619EFEFC996833ABC4D3012562F2121D3A15E4 (void);
extern void Timing_CoindexExtract_m33D6B51F50471A6A01F166F2942C4FB6AB414C24 (void);
extern void Timing_CoindexPeek_mB355D50F83BCB35162D6054F3BD66FF200B332D8 (void);
extern void Timing_CoindexIsNull_mC87FC47C5BE71744FA020E034D8935918D301213 (void);
extern void Timing_SetPause_m50FA8DA2CA59EB92A82D7D0311A5B2AF62C4F2F1 (void);
extern void Timing_SetHeld_mDC852360E7827E85C11E608F947E8F85061A04D9 (void);
extern void Timing__InjectDelay_mF913E3E0B0590A498C1D77EBA8600905E22A0E18 (void);
extern void Timing_CoindexIsPaused_m7FF8A8457A11AAAD90EED4323474B54BFBF2B7EF (void);
extern void Timing_CoindexIsHeld_mB09B193E7AE76180063CA7DC82CA68BA59782583 (void);
extern void Timing_CoindexReplace_m0D3A4644CA93A2E5F8089105EAFD61EE2CC2A8A3 (void);
extern void Timing_WaitForSeconds_m3A9FF018704E61AF36AC41D58A03BF1311F0B00D (void);
extern void Timing_WaitForSecondsOnInstance_m739CE37954128787A9E525694F27B727342F52ED (void);
extern void Timing_WaitUntilDone_mFC98191DF844AACCE31CCD2DD6C4B8A798DC9812 (void);
extern void Timing_WaitUntilDone_mFF8B687BAAF812B45C2463D4D050F1C4AA5EADDD (void);
extern void Timing__StartWhenDone_mCE55F22181934EC891A00D2827A8596FC6A9ED03 (void);
extern void Timing_SwapToLast_m2A265D1BC2C12FAD2DF650F4BED40D3741EEB504 (void);
extern void Timing_CloseWaitingProcess_m15BC8AD80A4856793BCC789091F397889DA35056 (void);
extern void Timing_HandleIsInWaitingList_m47853B06B17A4DE712DC2B553D1E160AC8816784 (void);
extern void Timing_ReturnTmpRefForRepFunc_m313EFB259740206AE125A11199D989EB3D0E65F5 (void);
extern void Timing_WaitUntilDone_mFD48E95A8DB4BC8969DF11BA8414FD63BEE0E846 (void);
extern void Timing__StartWhenDone_m9035AAA240BFE2A47070D8D8BDA9810A29AED365 (void);
extern void Timing_WaitUntilDone_mF657079BAB6D6B62740EBD44025281A5633944D1 (void);
extern void Timing__StartWhenDone_m96923E6A627D0749B53FE9CE0EE4B33556206DE7 (void);
extern void Timing_LockCoroutine_m935CF57B26BA90080F12AEEB795E8DCD861B0E50 (void);
extern void Timing_UnlockCoroutine_m1BF7EC9EA1128241C447FDCE0A692035EF8E607C (void);
extern void Timing_CallDelayed_m6EDDCE02B37773B05443175153D65BA5A23D63A7 (void);
extern void Timing_CallDelayedOnInstance_m89B7F6C1EE065451A51B50476B95DC7485B007CF (void);
extern void Timing_CallDelayed_m8FA00A2B4F1BEC59D2CC2B257985B274D01A8718 (void);
extern void Timing_CallDelayedOnInstance_m02B5386A7499ACBD13481ED98FEBB07A07F6E4EF (void);
extern void Timing_CallDelayed_m5457B7766D02CD0B2F126CA8BD390CB2AA297792 (void);
extern void Timing_CallDelayedOnInstance_m0566B9634265D00866C047DAA470FB0FFFFCA641 (void);
extern void Timing_CallDelayed_m15AEF2B7A29E72057E04F97AFE49CC4B4A1FD88D (void);
extern void Timing_CallDelayedOnInstance_m51588C952EFA9EE38671425F39B9C8B6DDC826A6 (void);
extern void Timing__DelayedCall_m432BA7B006ACC96BA39DEFDA8D43A7252FCE3FD0 (void);
extern void Timing_CallPeriodically_m1D24275D31549F5F9377066C83F94213553F7152 (void);
extern void Timing_CallPeriodicallyOnInstance_mA668AE877CAC737E34DC380069168BBC80270318 (void);
extern void Timing_CallPeriodically_m2CD57972660D7A950B66AD067D174B9895CCD8A7 (void);
extern void Timing_CallPeriodicallyOnInstance_m68E620498EE17E3A22C89B5540B6FB93DCC4E92D (void);
extern void Timing_CallContinuously_m6E12EBC1780E7DC4E03D0E2A2EE64541B7436C40 (void);
extern void Timing_CallContinuouslyOnInstance_mF7C4A529E42C65416FE1400FED32A3FCCB25A5AD (void);
extern void Timing_CallContinuously_mE09387325521236842E1C8EE3F7DF2F610942773 (void);
extern void Timing_CallContinuouslyOnInstance_m30C045040B9D435CCB82434184AF44805AA2161A (void);
extern void Timing__CallContinuously_m0D2C1738F9B290685DE4C73BFA40BDB058CDD2F5 (void);
extern void Timing_StartCoroutine_m34EA09FC796A33CF903739CA3DBF5648879F2186 (void);
extern void Timing_StartCoroutine_mEC2C812E330C4140694739ED3D824E5B012DC5F3 (void);
extern void Timing_StartCoroutine_m9FE9A8CE8F05D05CCD661F99E0A2A3DDD590A2F6 (void);
extern void Timing_StartCoroutine_Auto_m5B48C1505227C1BF8A8A1038B2DCB33299621726 (void);
extern void Timing_StopCoroutine_mF64A78FE242EDBDDEF9CAB862CB470E3E85179F3 (void);
extern void Timing_StopCoroutine_m66984C18B8B1328ABD1A3B3748CACF304F14096C (void);
extern void Timing_StopCoroutine_m119010370418B49B6A125B413C10D1261A59ED7E (void);
extern void Timing_StopAllCoroutines_m623B709C0A9F47ABC47E37A95612D07F80A0F7FC (void);
extern void Timing_Destroy_mC0EF3FE81859163807DC430AC5EC2873E41992FA (void);
extern void Timing_Destroy_mFE6064DA4D3F071742E97CDAA982504EEE87F2A5 (void);
extern void Timing_DestroyObject_m8CA5CC31D289577F3017F7DC32E483C47CB4139C (void);
extern void Timing_DestroyObject_m2255C0D93D513B8E6B7FA0735C2B647197020C81 (void);
extern void Timing_DestroyImmediate_mDB4C516268AAC18D1A3B92F5189B70DBA001EB18 (void);
extern void Timing_DestroyImmediate_mC3D6A466DA2897A8C71DF58B0F428A526A225D81 (void);
extern void Timing_Instantiate_m34B73E6F02337E7DE6FE8F60A3D778BE429C5050 (void);
extern void Timing_Instantiate_mA69DC7167ED58C4898079B37C7BBC0A604626F8B (void);
extern void Timing_FindObjectOfType_m90F5072BECEDA683CC775EF8DF666168812A8135 (void);
extern void Timing_FindObjectsOfType_m80827A902F888C06AE279EC988B2A04F8C4BB175 (void);
extern void Timing_print_mB5DFEE79C97B21A4F15CB92F27D27FFCEE81D1B5 (void);
extern void Timing__ctor_m843921526A267AAA7D4D6C47ACA176581F46F8FE (void);
extern void Timing__cctor_mBE5ECE84AE85CBF56238860254526AFFAB169970 (void);
extern void ProcessIndex_Equals_m0606720D3D1A042B4FEE328E47520C1F54FAAAFC (void);
extern void ProcessIndex_Equals_mE98E3DCDA417344D6838C798774A6B41F375D201 (void);
extern void ProcessIndex_op_Equality_m352DEAB6FCAFE4F7DC428327844042F0C7E8862A (void);
extern void ProcessIndex_op_Inequality_m0AF96BF2B53345ED60385E4B5334B25F8D58F3C8 (void);
extern void ProcessIndex_GetHashCode_m4AAF9C4E8445B9A0FBF925F8E51032FBA39250D7 (void);
extern void U3C_CallContinuouslyU3Ed__159__ctor_m407E2DDCA020D3CE37DC2A3389097E4C408727D2 (void);
extern void U3C_CallContinuouslyU3Ed__159_System_IDisposable_Dispose_mA11DE1F444CD11D68A79D2B393811CFD95C905EA (void);
extern void U3C_CallContinuouslyU3Ed__159_MoveNext_m3254D317AFA7B1B0E4F042F15F292CF837E359AB (void);
extern void U3C_CallContinuouslyU3Ed__159_System_Collections_Generic_IEnumeratorU3CSystem_SingleU3E_get_Current_mF9DB8C5827F773DB9C5001DFA7CA1232647C0940 (void);
extern void U3C_CallContinuouslyU3Ed__159_System_Collections_IEnumerator_Reset_m9A050B4E7C9D745A1925DE5A758AD05F6764D915 (void);
extern void U3C_CallContinuouslyU3Ed__159_System_Collections_IEnumerator_get_Current_m055539DFC41FDE3961D145352C4732E67EA85434 (void);
extern void U3C_DelayedCallU3Ed__150__ctor_m66CD04978D937B16F0BBD768F1D9D9476D2F7AF4 (void);
extern void U3C_DelayedCallU3Ed__150_System_IDisposable_Dispose_m3D62AA8D6C3B9DE7F999BBA71E7416847926C95F (void);
extern void U3C_DelayedCallU3Ed__150_MoveNext_mDFF3EE6ED626ED3EA42A7F207159999AA144DB70 (void);
extern void U3C_DelayedCallU3Ed__150_System_Collections_Generic_IEnumeratorU3CSystem_SingleU3E_get_Current_m16FA13EA12E857331D24995E3DDB38127FD8B178 (void);
extern void U3C_DelayedCallU3Ed__150_System_Collections_IEnumerator_Reset_m0BFF6A5787DC19C1BD48437EFE6CF81F9BC56E4D (void);
extern void U3C_DelayedCallU3Ed__150_System_Collections_IEnumerator_get_Current_m254A3ABA832A0BC944C1F166E822D64BE13080F0 (void);
extern void U3C_InjectDelayU3Ed__123__ctor_m911E53FE6A7C8B3A4F7CA11CACA410BC835E7A46 (void);
extern void U3C_InjectDelayU3Ed__123_System_IDisposable_Dispose_mEEEFA4BFD0C99E365C195B3ADF4367660280D4C7 (void);
extern void U3C_InjectDelayU3Ed__123_MoveNext_m608DAAE0B499E9A66339C9863EAA883CB3D305F1 (void);
extern void U3C_InjectDelayU3Ed__123_System_Collections_Generic_IEnumeratorU3CSystem_SingleU3E_get_Current_m5F8747BF616E23B938A592588EBCF89EFCBEE72D (void);
extern void U3C_InjectDelayU3Ed__123_System_Collections_IEnumerator_Reset_mE5FE65D262656E91D5CFE7E6C39AAB1E093AC161 (void);
extern void U3C_InjectDelayU3Ed__123_System_Collections_IEnumerator_get_Current_m1DB3A4328D230547F4852AF990E17CB55BF030FB (void);
extern void U3C_StartWhenDoneU3Ed__131__ctor_m39D0253CE1E38AE821C7964327E058748B7210BA (void);
extern void U3C_StartWhenDoneU3Ed__131_System_IDisposable_Dispose_m5C5B58F67FB6E2AE352E8915C6CAED9FBE937809 (void);
extern void U3C_StartWhenDoneU3Ed__131_MoveNext_m6A769ADD781A22BC21B10B96962050B08EF5A7C6 (void);
extern void U3C_StartWhenDoneU3Ed__131_U3CU3Em__Finally1_m1E97EF2A45F8FDEB7C893BC8A6A15E002576010C (void);
extern void U3C_StartWhenDoneU3Ed__131_System_Collections_Generic_IEnumeratorU3CSystem_SingleU3E_get_Current_m9483F759791A07FAC776F6633640AFD0D9251308 (void);
extern void U3C_StartWhenDoneU3Ed__131_System_Collections_IEnumerator_Reset_mEE54626DB84DEFD57004CCF8FD4FFFE70776B9CA (void);
extern void U3C_StartWhenDoneU3Ed__131_System_Collections_IEnumerator_get_Current_m491210BBA1E04FBD7EE1F1AF697D0FE6D50738F7 (void);
extern void U3C_StartWhenDoneU3Ed__137__ctor_m2B518F87724686098D3CE5696389C00248EC4723 (void);
extern void U3C_StartWhenDoneU3Ed__137_System_IDisposable_Dispose_mACBB33968FCBF365F7A868A323205C00C4CE2D0E (void);
extern void U3C_StartWhenDoneU3Ed__137_MoveNext_m59AACFD971D94C6773E0EF329B63C860911BE520 (void);
extern void U3C_StartWhenDoneU3Ed__137_System_Collections_Generic_IEnumeratorU3CSystem_SingleU3E_get_Current_mF1C3EC22B6BA7F1AA1C69958A28EF3B60FC0754E (void);
extern void U3C_StartWhenDoneU3Ed__137_System_Collections_IEnumerator_Reset_m2B15CDE0BD27F40E6E9400A162A95DA39052390F (void);
extern void U3C_StartWhenDoneU3Ed__137_System_Collections_IEnumerator_get_Current_m3DA53F2FF39D0DA6F00EAECC83999AB00500F269 (void);
extern void U3C_StartWhenDoneU3Ed__139__ctor_mB0F491FE68F6541263CCFA5074887BD0FAFC187E (void);
extern void U3C_StartWhenDoneU3Ed__139_System_IDisposable_Dispose_mC20C3357FA8080E9855C8C200C775F4C472A2CCC (void);
extern void U3C_StartWhenDoneU3Ed__139_MoveNext_m7D8BC152565993783B926619777BA61470980E3D (void);
extern void U3C_StartWhenDoneU3Ed__139_System_Collections_Generic_IEnumeratorU3CSystem_SingleU3E_get_Current_m2710B26C5A214133F86D74F08F226E1F9F4851D1 (void);
extern void U3C_StartWhenDoneU3Ed__139_System_Collections_IEnumerator_Reset_m13CE88E834090847AE3539C17140A9978FA47FBF (void);
extern void U3C_StartWhenDoneU3Ed__139_System_Collections_IEnumerator_get_Current_m15726F5D7DC87D2568195EA9A0ADF0AFEA0ABF80 (void);
extern void CoroutineHandle_get_Key_mCB7FA3D0481F9A13B0EC1FA22CB6C9AFA95F830B (void);
extern void CoroutineHandle__ctor_mCF4D58FEC43F4D8E816A39148B5C949B78CEB02D (void);
extern void CoroutineHandle_Equals_m376A1B4289D9266EA669B42997BD425DC59A81E3 (void);
extern void CoroutineHandle_Equals_mE9A2F6DD86A6E40A3AC3B2C0B5717FB3154C3E95 (void);
extern void CoroutineHandle_op_Equality_m778E4A099E5894B65791A47723E6DC7063FD8A39 (void);
extern void CoroutineHandle_op_Inequality_m84A9D76D1CCF55D2CD202E199A50FBDC25A2974E (void);
extern void CoroutineHandle_GetHashCode_m146A36001E4646CB9F3F4548A7DD789FF89928EA (void);
extern void CoroutineHandle_get_IsValid_mA4DA81202B56F396DABF930A009CCF65F34FAD53 (void);
extern void CoroutineHandle__cctor_m4097C3A6183F3C35B449D79E41C51223222BFB91 (void);
extern void MECExtensionMethods1_RunCoroutine_m765CC3F57C4649F25A642A01905A46F58D89FAF1 (void);
extern void MECExtensionMethods1_RunCoroutine_m7192C1F6D09713D6685B3EC39A1D3B3D20BBAA7F (void);
extern void MECExtensionMethods1_RunCoroutine_m1B090E1DF8B0EF074A3E121C321B1FA477F0A9B9 (void);
extern void MECExtensionMethods1_RunCoroutine_m2DB311924180339BFE4696FE6A8FF1514B8CD492 (void);
static Il2CppMethodPointer s_methodPointers[365] = 
{
	PerlinNoise__ctor_mB0CCE9CA931D37CE3B7B52E8F7ED7F5766DBEB70,
	PerlinNoise_Seed_m311A3ECA976C2CA7C665F7C203D8322D09114DD9,
	PerlinNoise_Noise_mD231D26E9C05136AB6172829BF6BE54112E5B9F6,
	PerlinNoise_FractalNoise_m2A931315B4909A7422FD87281AF2403DD43D4E08,
	PerlinNoise_InitGradients_mCB0C6153608CBC1D5E9AD5FED233643B478FE2A0,
	PerlinNoise_Permutate_m07B4F7083FC37342CA364733B2A958EDA004BEB4,
	PerlinNoise_Index_m1FFB41A4572436A5B270DF35DD5761F43BCD7FCE,
	PerlinNoise_Lattice_m39FE544BEAF55B53E4B5E0B64493B5DEF740893C,
	PerlinNoise_Smooth_mB94BBD863BC0F314DEDCDCF6E802D069E30DD175,
	Puffy_Cloud_Awake_m36C1833D62D44C2AB8F9C8A36ADE1B981CFD6EF2,
	Puffy_Cloud_Build_m92370075A92549D334C794793E6E0D0CD4E35513,
	Puffy_Cloud_Update_mE91B5CE660D407795B481A8E8875F4E94597D40B,
	Puffy_Cloud_OnDestroy_m9F632EE24213A3067651CA0E2D0726334B734908,
	Puffy_Cloud__ctor_mEFD09F81046404E9FF3D8AFBDD8A44CD2A40C216,
	Puffy_Emitter_Awake_m0F62C51AC4FDE4533229C5B51D0A01AB71918CE2,
	Puffy_Emitter_Start_mD535996ACE0F9D15B3001F74D23A462E015504C3,
	Puffy_Emitter_WarmUp_mA57AE21707D5CABD3BFC404A68E8EE609FEF8E03,
	Puffy_Emitter_Clear_m97B91D82E3DDFE158ADA7DE4E18D12E20ED0E559,
	Puffy_Emitter_Kill_m84ABF2E018E1A5DD97D5B1B2C62A877F3D6D4D9B,
	Puffy_Emitter_Resurrect_m86466B6D6B9169D00B5CB65BF3916AD4A8A2A598,
	Puffy_Emitter_Link_m0145A151E48238F73EACF360F80159B268D0CCFE,
	Puffy_Emitter_Link_m196425023CF56C95086042A75971735C9326037A,
	Puffy_Emitter_Link_mC82BDF8E3AC910C05F5C528F6070BC9FFC893D87,
	Puffy_Emitter_UnLink_mCF79DEF105BA54B0531CD533D1B71779814906D0,
	Puffy_Emitter_Link_mE2E3E99AD67D6603FDF219CF9AEDD1C9805ED014,
	Puffy_Emitter_UnLink_mDA4E867EE0D21FD046520F12380B64AF19BDF3B6,
	Puffy_Emitter_GetActiveGradient_m259DBB6A51972CB8F8F4C29A931A8D5B7DDE4941,
	Puffy_Emitter_LateUpdate_m281845EB3E484A92CEFE9F758D143EAA96B5F5E4,
	Puffy_Emitter_Threaded_UpdateParticlesTask_m0FA4C39BC684C2375A11C8157401E8AA60BEA54D,
	Puffy_Emitter_UpdateParticles_m3D087C81A01F59B94AA92A4B5F121BA979B81BA1,
	Puffy_Emitter_OnDrawGizmos_mE83EBBA0E58141E8E1FAEB06057AD248105CFCA0,
	Puffy_Emitter_OnGUI_m2A06789B10BCEB8ADC87AEC2F055604092A9D974,
	Puffy_Emitter_UpdateParticlesTask_m67513F068B4DE701B644C27C773D15AAAE3E915A,
	Puffy_Emitter_KillParticle_mDA3E4D54EBAC2663F711E40185C3200844B362FD,
	Puffy_Emitter_SpawnParticle_m6B1569E27C9A848DE0AB532C2B8385E6F716AD6D,
	Puffy_Emitter_SpawnParticle_m190C8F10895AF603A09B59B65E5D811602C634C2,
	Puffy_Emitter_SpawnParticle_mAD293B673EFFCBD5BB5671003D8680B0953FEEA1,
	Puffy_Emitter_FastSpawn_m2D6FF84BE229E5EC2947F622F8A4D7E7929B3A27,
	Puffy_Emitter_SpawnRow_m44AE824B9E145A3EF2E59BEDE046B0BD014EC8DF,
	Puffy_Emitter_SpawnRow_m32A289F44D701453CB08E883586B697CB7453FBB,
	Puffy_Emitter__ctor_m0CF869C2DC3AD8FC0CED7FAC25948F4026BA098B,
	PointerComparer_Compare_m23E9A70D91A92901F8928A0ED37254B873325D00,
	PointerComparer__ctor_mD57980B91454D43019C2D442826E024212C23EA2,
	PointerGroup__ctor_m2E6CEEB4FE9E0CA04A9E25DB4DF8CBDBB0AD36B8,
	Puffy_ParticleData__ctor_m1C7097C9459D5106A24615666BAE76557B531BF7,
	Puffy_ParticleData_Spawn_m88D1AB3D6557619CC6BF06C8DA6C1FDD16543995,
	Puffy_ParticleData_Spawn_m8D4C7C2EC22F88D6E009F515610CFCFDA5A5BFA3,
	Puffy_ParticleData_Spawn_m75938242C5488E94647FD68F25D39371AC2EF501,
	Puffy_ParticleData_Update_m14B384DF2ED4BBA24F432E69359C5AA1A2440667,
	Puffy_ParticleData_Kill_m622E5F4230F1A4AD3E915EBAF72DDB68A24DEAEE,
	Puffy_Gradient_Start_mA9B8C3A2703B7EC87E60E32EB1E171D577C421E7,
	Puffy_Gradient_OnValidate_m99FCFD330754256780D56D94579F157BAA53A581,
	Puffy_Gradient_UpdateLookup_m582D961F7CCD4CACBB98CBBD184CC76BF5361735,
	Puffy_Gradient_Evaluate_m9DFAE796047DAB6D856665760F94DE507E9ADC22,
	Puffy_Gradient__ctor_mEC347C18E882168FE5F5A0C57542C52EDBEF3686,
	Puffy_MeshSpawner_Start_mFF148FDD62B81B34EBF243FD127E6BB325BA25E3,
	Puffy_MeshSpawner_Init_m00716C20ABA4549CF9CF5CEC8500AB39E57F5095,
	Puffy_MeshSpawner_Smooth_m4D99E1A0E0775958CC35D2EEA97FC5574E9EC9E1,
	Puffy_MeshSpawner__ctor_m870A26C85584BE951C80BE18AAE26675BE8C0D89,
	VertexData__ctor_m4DDB93C05B40A4DBA03557489BBC3EE7BEBA9F9F,
	Puffy_MultiSpawner_Awake_m0A699C1FA4BDBBC2B31BC0AD8C417D2A28E1BA9A,
	Puffy_MultiSpawner_AddSpawner_m9A71E8B182CE8DFEC141F14D398B921EF570C239,
	Puffy_MultiSpawner_RemoveSpawner_m6D6752F507BC69623DACDC2EC6F5235D17E840BB,
	Puffy_MultiSpawner_CreateSpawner_mFE728EC240C62B178B16D8F6AC77E6F335E36D90,
	Puffy_MultiSpawner_GetNextFreeSpawner_m5321072EDD775F554C2F2842D4FA7A750AEAADF5,
	Puffy_MultiSpawner_MakeSpawner_m9E670C9F0EF37C1D8CA7CE435D80071062038EDD,
	Puffy_MultiSpawner_DoUpdate_m0FEE608DB3278FA0C2D2CE298883119E8593B54C,
	Puffy_MultiSpawner_OnValidate_m44DAB07ABB1AC94834A51A1F86E657D7455F575E,
	Puffy_MultiSpawner_OnDestroy_mA78A6DD0911B82EAFEF4BBA4AF4CC5B88CA9898A,
	Puffy_MultiSpawner_OnDrawGizmos_mC13DA920C78BAEF6906ED58CBF013D1A0FF86AF8,
	Puffy_MultiSpawner__ctor_mFACEB1DBBFE1FF59E8E4647E8064C3AF52C5937E,
	Puffy_ParticleSpawner_Awake_mEBCF81317CC1B6561EB5D312518D3DDDAF6BDCA5,
	Puffy_ParticleSpawner_OnValidate_m8E0141BE815610BF5E199F85A7A9444666B063D0,
	Puffy_ParticleSpawner_UnsetMultiSpawner_mE8E8F176F4AF22364B88FEAF9B37B0E9CD68F7DC,
	Puffy_ParticleSpawner_SetMultiSpawner_m25FB1BFE41E6D4387D76AD69C4BCA1A8359E08FE,
	Puffy_ParticleSpawner_Start_m571EED6AB107B9E1E85D311A104A1E92375C75B6,
	Puffy_ParticleSpawner_InitSpawnPoint_m8096B1899B6C01AA6CDA1473B6B9C1E9525B4288,
	Puffy_ParticleSpawner_UpdateSpawnPoint_mFADB0FEE42AEA0F0F80A8F660BC4EECAC47676BC,
	Puffy_ParticleSpawner_UpdateSpawnPoint_mC9D9DD134CBABC426AE9F7678DD180828E6C713E,
	Puffy_ParticleSpawner_DoUpdate_mB02B02E0CA7380A18102517445C7A4F6B504F77C,
	Puffy_ParticleSpawner_Sleep_mEF2282542FA51A5CA88B666D8A71D433E7ACFD5F,
	Puffy_ParticleSpawner_Wake_m4E832DF0B4E59576379D7C95E39EA5EEF07B509B,
	Puffy_ParticleSpawner_OnDestroy_mDD23C6F1856760C8881A4051996345F7D0BA425F,
	Puffy_ParticleSpawner_OnDrawGizmos_m382788BEC36FDD0CB1B02459B866F544053FD430,
	Puffy_ParticleSpawner__ctor_m7E252CC17C1F70D82864DB32CC6C5CCD4C1A5BA4,
	Puffy_PerlinCloud_Awake_m6AA80562D76CA4595A61ED4DFBEE5F12E749C862,
	Puffy_PerlinCloud_Start_m76CFBE311074E00D88B8B24B4348443752DBDA4B,
	Puffy_PerlinCloud_Init_mB693D238ED5299A4265B6B6D53BEB14A8B84F6EF,
	Puffy_PerlinCloud_Build_m867B886977DF5B2439447DFE5D92EE653618EE88,
	Puffy_PerlinCloud_Update_mA8F21DF38E2BFBD20B926B62012BB5BA0ECB5754,
	Puffy_PerlinCloud_OnDestroy_m9A0C0F0ADF5FAA6E9E48E9545C40D9E90ED3F43A,
	Puffy_PerlinCloud__ctor_mC96008C173680096B40F310CCE4C23B6D3FAB555,
	Puffy_Renderer_ToggleDebug_mF8F2F77E5BA23D97965BB98D07F41DB3158265A7,
	Puffy_Renderer_GetCloudRenderer_m98CC325709910DEC89FB8AAFD043BDB54FDA1334,
	Puffy_Renderer_GetRenderer_mD16A08482E446C461A0860AFC3FA61687025BA9C,
	Puffy_Renderer_GetRenderer_mEA4D8FFF6CF3B4A01AC7D47192E9AC097F3DC941,
	Puffy_Renderer_GetRenderer_m4CF26DA6FB03DEC5E96C1F544678BFD4D1CC7A26,
	Puffy_Renderer_OnDestroy_m7724C1F5C1B846ADAF959D2F172AE63B8216FDE1,
	Puffy_Renderer_Awake_m8D379ADC0B950D6BCF3E39CDC53DCBB2570060E8,
	Puffy_Renderer_OnGUI_m7D4692818851A05D995278774D7B7261280F247B,
	Puffy_Renderer_OnDrawGizmos_m90FDC9E081D540AF06667836F2361E98DCB19379,
	Puffy_Renderer_AddEmitter_m0A72AEDD32C65061D2DECBCDE638D74FC43ABCAC,
	Puffy_Renderer_RemoveEmitter_mA61C1EBA497972B367D4B1B441D2336E26385EF7,
	Puffy_Renderer_RemoveEmitter_mC63A8531170ADAC21C7EB73EA0CA2635EA124E5A,
	Puffy_Renderer_RemoveEmitter_m39A913EA09A8636311EE7390E386A3BE981D89DF,
	Puffy_Renderer_LateUpdate_m39A20D33ACF08552BFE49A199E593D677ABA891E,
	Puffy_Renderer_Render_mD572DF6F38B7FDB3E0908A0B7E62234DCF8F0CEB,
	Puffy_Renderer_updateBillboardsData_mBBF6BB727DB920F58584E835CB96DDA7EB0A2490,
	Puffy_Renderer_SolveBillboardTangents_m520B44BDFD71D733331D0E0755DF99DE1006C5EE,
	Puffy_Renderer_FrustumCheck_mDE8929D2A1FC0DCFF3497FADFC12A4A7F955E627,
	Puffy_Renderer_Threaded_FrustumTask_m05AD5B107F21EBFCD4598C754048DFECED11FDB3,
	Puffy_Renderer_FrustumTask_m5620C85E151F4911A59DCE41969DEA916A7DEC14,
	Puffy_Renderer_Threaded_SortParticlesTask_m83E8FD1779C8788D52BAB1DFCFA78F9CBD70A0FD,
	Puffy_Renderer_SortParticles_m6482F6BC79692561BED7B5CA93B7007AF26BB46D,
	Puffy_Renderer_AddMesh_m193B48210213D36FE1968A6B6022B1711BC35B08,
	Puffy_Renderer_MergeGroups_m164895A6B06A0E8B584DED40D404FF79B9A4E39D,
	Puffy_Renderer_UpdateMaterial_m72D9008F0552E10CA09F3FF1B800D82DE0268EB0,
	Puffy_Renderer_Threaded_BuildMeshesTask_mB9AA8C016C0AFD3CE1D80375B1295CD99E838047,
	Puffy_Renderer_BuildMeshesThreads_mFF1F201B0E14A26AED45C04D504B9442A6A72CC8,
	Puffy_Renderer_BuildMeshes_task_m41D204CEE920BE2855D1D8071DE0FD3CC1966B65,
	Puffy_Renderer_Threaded_UpdateMeshesTask_mA86858E9D230A1E7C828062CC4341DA64826DCFB,
	Puffy_Renderer_UpdateMeshes_m03BF5EE9857AF42C548C010DBC5ADE561637868D,
	Puffy_Renderer__ctor_mFAB43F41DCD6DE0CF36DF38FC89E0C5CF454B5D3,
	Puffy_Renderer__cctor_m85C991AEFF9A665C1DFB8E9746C6B00034549D2E,
	SortGroup__ctor_mCE37896571DB2B870A46178C8272E09E3FBF6CDE,
	SortGroup_Sort_m16A38A32A5C70CA4966BF09AC958EA3ED713EAB5,
	SortGroup_Init_m617C13B029B212AE272A7FEE46D9D298EAD280E1,
	SortGroup_RadixSortAux_m206E32EBC7D3DB430E3FFC70284825B4E82D48B7,
	SortGroup_CountingSort_m9957E3FB0DCE05E46E78995C4828D06219C3CCE6,
	SortIndex__ctor_mDC569AFA01D17CBC5871282FB271D9C743086C49,
	OrderComparer_Compare_mBD6F249583559133C7B56019BD98AC5A8325C2DE,
	OrderComparer__ctor_m000E9B1F6D08497BA3E7A7CFBA3FCECB566B27DF,
	VariableMesh__ctor_mE16AAEAC10B9DBDC9529937ED69688A36027A4AD,
	VariableMesh_getMeshDataIndex_mFC14EBE06353B1BEC6A5791B9B2A2774D88FD4F4,
	VariableMesh_getMeshData_m69A786A38D7CC0E2E1F2244305EECEA86BCE4DAB,
	VariableMesh_Init_mBDBF82FEDBF35F299290FF04BBAA26B4B5180A7C,
	VariableMesh_ClearMesh_mF1B06714DEE516F8153BEC13CDEB384A5AF52760,
	VariableMesh_UpdateMesh_Step1_m70B9BAAD7237728C5D876BD77B585835E2E910D8,
	VariableMesh_UpdateMesh_Step2_m2AB5AD3A2C336C77A35901E323BA70B87E00A7DE,
	VariableMesh__cctor_mFE1BDEABC9BA8EA8C49E2560DD22FDE7D5FB27C5,
	VariableMeshData__ctor_m4D89710102A2EFD55D4721478AE5FD3A0A8CEBBC,
	DebugTimer_Start_m5F7F0B39663438373A5B44833959532A117D267C,
	DebugTimer_Stop_m537C226726BC90724D65CF5723EAA29FB7C7D825,
	DebugTimer_Average_mA15771475471BE12892572EC434FC8785C082FCC,
	DebugTimer_ShowGraph_mB1509C8DFDDDBA087805889D276E777C7CFC4C25,
	DebugTimer__ctor_m8087E232341BD4B2B0E40C7E63EF9A1D169BDB2F,
	Puffy_ShapeSpawner_Awake_mDAF10D878F76D0BA2E0E1D4A256D95EB3AE911A8,
	Puffy_ShapeSpawner_OnDestroy_m56ED9F77F9E560E9FC6497EDFC8674F5860B94D1,
	Puffy_ShapeSpawner_Init_mDD220D4CAE8A95561956E2CA4C35F8A9C707B33B,
	Puffy_ShapeSpawner_DoUpdate_mF4A1921F8D5793721FFAE374E7A3D71DE9C65ED7,
	Puffy_ShapeSpawner__ctor_mC7B6EECAA47AD9D4BF7FC1D8C7A1C1CA365BB186,
	MECExtensionMethods2_CancelWith_m87B7F9CA443158DBCDEE4BB6BA9900FCFBE6CEC3,
	MECExtensionMethods2_CancelWith_m5FC8E9E0BF0AF9310D0FA7154C4B9093BBBFDD35,
	MECExtensionMethods2_CancelWith_m8F7FE1DF1A42265216271B287729242E2AABBDD6,
	U3CCancelWithU3Ed__0__ctor_mD1FD76800298A0D9A5CE80E86F62EE2818E6E7DE,
	U3CCancelWithU3Ed__0_System_IDisposable_Dispose_m13F848E7AFD31EA7991D5E85F6F4DFA41309F198,
	U3CCancelWithU3Ed__0_MoveNext_m2FF006D6533F26F1F329A224004D171E0D1EB974,
	U3CCancelWithU3Ed__0_System_Collections_Generic_IEnumeratorU3CSystem_SingleU3E_get_Current_mBA68D0B2229FE194CF0A41563FDD64978376505F,
	U3CCancelWithU3Ed__0_System_Collections_IEnumerator_Reset_mC11674E8852C9735E5EEFF2696D61290A0689AA2,
	U3CCancelWithU3Ed__0_System_Collections_IEnumerator_get_Current_mB1E5A3EEA8FA441698CFA684A2044FAE69FEB162,
	U3CCancelWithU3Ed__1__ctor_mA1E5A592F03F092BDE452DCCF6BF57ACBF6B3DD9,
	U3CCancelWithU3Ed__1_System_IDisposable_Dispose_m859E1797DB9EAEE4D0AE87F3797EF06F985F00C0,
	U3CCancelWithU3Ed__1_MoveNext_mC98A04B97394553695C6802894E79F749D5F141C,
	U3CCancelWithU3Ed__1_System_Collections_Generic_IEnumeratorU3CSystem_SingleU3E_get_Current_mD70FB2F66638127BABE47CBD2EE55233A115D3DC,
	U3CCancelWithU3Ed__1_System_Collections_IEnumerator_Reset_mE4948E9B9E98B7E553AE4372EDDE0B90E7EFA4EE,
	U3CCancelWithU3Ed__1_System_Collections_IEnumerator_get_Current_mFD1752A1FE5FBB25E6B10B1B6732B2171F4656F1,
	U3CCancelWithU3Ed__2__ctor_mFEA0FD042A7C4AF11BFE1484CFEDD37853A84D78,
	U3CCancelWithU3Ed__2_System_IDisposable_Dispose_m405675EB80AB2D8E463C714B3330C19E182B1C18,
	U3CCancelWithU3Ed__2_MoveNext_m18A06CC42145B367082A83B0ADD2CA90F2643ACF,
	U3CCancelWithU3Ed__2_System_Collections_Generic_IEnumeratorU3CSystem_SingleU3E_get_Current_m280EF60C62993FD57D81EB153FC1D93B293D8986,
	U3CCancelWithU3Ed__2_System_Collections_IEnumerator_Reset_mD41F142EB9BABD3D7655BA6B81D3CA0DDAF526AE,
	U3CCancelWithU3Ed__2_System_Collections_IEnumerator_get_Current_mA9DA12EC5F0AB8BB789D8F23EB2F419A287E4642,
	MouseLook_Update_m3D3D51464C33641FE42365A4BEAB7223CBE5A933,
	MouseLook_Start_m10D8390C49B3ADBB62155CF87FDB1D21974FB4C5,
	MouseLook__ctor_m437956CB4A17EB38EB1AD1B1C7FEBEB63D62A555,
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_mC7CA174A23290C34424DF6D2733D5E64B92E5977,
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_m3C9D09F94200334DD5FA29A465481C7848AF4549,
	Timing_get_LocalTime_m9C49480299611C87C26CF1B8ABFF39EBE066B05F,
	Timing_get_DeltaTime_m840C0FD73C53D54D09C3BB6F39D048720B3B666E,
	Timing_add_OnPreExecute_mFB308CDAF20B4ADAFC9EC8D90290FF4AB2C0289F,
	Timing_remove_OnPreExecute_m86137AB1D52C22AFA6BF84EA74ABE2D0E179E99E,
	Timing_get_MainThread_m80634C20653DBF90625830B7D92DC0287969783B,
	Timing_set_MainThread_m092B792966E3FFF6ADB824CE916D72EF6496CFC2,
	Timing_get_CurrentCoroutine_m2B19EBA8F7AB1AB577CB1AE9BF866A052C93BEA7,
	Timing_get_currentCoroutine_m1779C17402C0C227C7E80A36BB865818DBE9BEEF,
	Timing_set_currentCoroutine_m3DBBE23CAA1674B5622501475023ECCF1BFAD433,
	Timing_get_Instance_m2EEDAE6600D6BF2E9AA0FC443E8401831BD6AEBF,
	Timing_set_Instance_m6BC74F4B64497965433B61E174600FB67848F69E,
	Timing_OnDestroy_m92DEE85A3B3BC195C9D62B422B55E101F4ED3DD0,
	Timing_OnEnable_mEDCDD8D274AF1DC8E444AA102FA0BD351F83F8DD,
	Timing_OnDisable_m706C02C5EF51428DE051F14E94AB8C2B3FA795C7,
	Timing_InitializeInstanceID_mE7B74EE61B61BF72574985CF7998A6138B7F0FFB,
	Timing_Update_mE7D1CF9936AAE1B66BCA12AB8C877445C9BCBF4A,
	Timing_FixedUpdate_mD22C57426883A9C98D287E1F29607D94B4D369E9,
	Timing_LateUpdate_mBFC7B68DEA5A814E39B6579BE4152EC1E01ED58A,
	Timing_RemoveUnused_m0BA36387FAA8CDAA62CF7528028FBEBA9BFA2492,
	Timing_RunCoroutine_m1BAAAA0B1ECA15199AA277A76EFDED9F760A86EC,
	Timing_RunCoroutine_mFD2C706624ACC224D704F547E35EE4BFA91D601B,
	Timing_RunCoroutine_m4BFEDB3D901FB97F00121727C4D5F825B88DAA39,
	Timing_RunCoroutine_mB876598DE22C1C5F76B443F9B934344EB94F2139,
	Timing_RunCoroutineOnInstance_mD720F0E125B8E0B1B332C03CC0B79B4797AA8D1F,
	Timing_RunCoroutineOnInstance_m7F0B219ABDD1304682EE6A525E66A624E718F294,
	Timing_RunCoroutineOnInstance_m2DF151338C742F53D0D8DD15C7FB624CBBD54FFE,
	Timing_RunCoroutineOnInstance_m2FAC3A4F1DA222777634572328C484CDE84CE424,
	Timing_RunCoroutineInternal_m2E3890CC6A46C1D584341409AE925FD266162C9A,
	Timing_KillCoroutines_mAF41B8396F0AC0041747239882993C24DFB31707,
	Timing_KillCoroutinesOnInstance_mE11A9993D825C3936E4AAF9E24527DE44AA182A4,
	Timing_KillCoroutines_mE3D69F732ABD655ED55CDDD3B841F13B425C46A5,
	Timing_KillCoroutinesOnInstance_m2FBCFCA895C9A7EF1AB2FD59D13503CDA9D69844,
	Timing_KillCoroutines_mD5F64F5D40C6BBEA38F834680516F2D4ADD51640,
	Timing_KillCoroutinesOnInstance_mA63811211C30FE8B7E386311B11E6221BF51EF76,
	Timing_PauseCoroutines_m55CFEC6E6C556BDE33ADD43FE24A0A04C186057C,
	Timing_PauseCoroutinesOnInstance_m406092546839B370231CFE599E70361206338083,
	Timing_PauseCoroutines_m5AA3111734731B316A516271D725F6ACE632AE8B,
	Timing_PauseCoroutinesOnInstance_mB57247C3E73805B70562C7BA78490D8B0A3B83AA,
	Timing_PauseCoroutines_m34D5555488A0BD53E8136E8F8A7726217743E500,
	Timing_PauseCoroutinesOnInstance_m5B0C80D51BA2368FB873042D29F215530BCCD015,
	Timing_ResumeCoroutines_m90365DD8F2F45AAD458E290AED34B5C39AC8C26C,
	Timing_ResumeCoroutinesOnInstance_m8AD21FD8F5D9CB63DCA50A03E14C8FE346809E11,
	Timing_ResumeCoroutines_m4F8BE763CF48A5856143A86B8F0037F4F3215B1D,
	Timing_ResumeCoroutinesOnInstance_m7A77DF92C88FBD5A44E7255966AF15A55578E838,
	Timing_ResumeCoroutines_m82CDBF6C566EB38666A7CEE07004D9F70881EDB9,
	Timing_ResumeCoroutinesOnInstance_m55F40934D505D01E67E736CFC92443E3DEB9FC21,
	Timing_UpdateTimeValues_m99DABFAC068CCA50B69C9C9F60405097A453EF72,
	Timing_GetSegmentTime_m213929DBDFB5588571671D9AF28FB3EC95ABEBF6,
	Timing_GetInstance_mB6312A6787BBB0E076E944F9970E6790EED86F94,
	Timing_AddTag_m8D8BA724E402C4D46A72D82B60293D33A8833F16,
	Timing_RemoveTag_mB94759CAF68B83C3FF72A7808FE4D2153307E0FA,
	Timing_Nullify_m9B619EFEFC996833ABC4D3012562F2121D3A15E4,
	Timing_CoindexExtract_m33D6B51F50471A6A01F166F2942C4FB6AB414C24,
	Timing_CoindexPeek_mB355D50F83BCB35162D6054F3BD66FF200B332D8,
	Timing_CoindexIsNull_mC87FC47C5BE71744FA020E034D8935918D301213,
	Timing_SetPause_m50FA8DA2CA59EB92A82D7D0311A5B2AF62C4F2F1,
	Timing_SetHeld_mDC852360E7827E85C11E608F947E8F85061A04D9,
	Timing__InjectDelay_mF913E3E0B0590A498C1D77EBA8600905E22A0E18,
	Timing_CoindexIsPaused_m7FF8A8457A11AAAD90EED4323474B54BFBF2B7EF,
	Timing_CoindexIsHeld_mB09B193E7AE76180063CA7DC82CA68BA59782583,
	Timing_CoindexReplace_m0D3A4644CA93A2E5F8089105EAFD61EE2CC2A8A3,
	Timing_WaitForSeconds_m3A9FF018704E61AF36AC41D58A03BF1311F0B00D,
	Timing_WaitForSecondsOnInstance_m739CE37954128787A9E525694F27B727342F52ED,
	Timing_WaitUntilDone_mFC98191DF844AACCE31CCD2DD6C4B8A798DC9812,
	Timing_WaitUntilDone_mFF8B687BAAF812B45C2463D4D050F1C4AA5EADDD,
	Timing__StartWhenDone_mCE55F22181934EC891A00D2827A8596FC6A9ED03,
	Timing_SwapToLast_m2A265D1BC2C12FAD2DF650F4BED40D3741EEB504,
	Timing_CloseWaitingProcess_m15BC8AD80A4856793BCC789091F397889DA35056,
	Timing_HandleIsInWaitingList_m47853B06B17A4DE712DC2B553D1E160AC8816784,
	Timing_ReturnTmpRefForRepFunc_m313EFB259740206AE125A11199D989EB3D0E65F5,
	Timing_WaitUntilDone_mFD48E95A8DB4BC8969DF11BA8414FD63BEE0E846,
	Timing__StartWhenDone_m9035AAA240BFE2A47070D8D8BDA9810A29AED365,
	Timing_WaitUntilDone_mF657079BAB6D6B62740EBD44025281A5633944D1,
	Timing__StartWhenDone_m96923E6A627D0749B53FE9CE0EE4B33556206DE7,
	Timing_LockCoroutine_m935CF57B26BA90080F12AEEB795E8DCD861B0E50,
	Timing_UnlockCoroutine_m1BF7EC9EA1128241C447FDCE0A692035EF8E607C,
	Timing_CallDelayed_m6EDDCE02B37773B05443175153D65BA5A23D63A7,
	Timing_CallDelayedOnInstance_m89B7F6C1EE065451A51B50476B95DC7485B007CF,
	Timing_CallDelayed_m8FA00A2B4F1BEC59D2CC2B257985B274D01A8718,
	Timing_CallDelayedOnInstance_m02B5386A7499ACBD13481ED98FEBB07A07F6E4EF,
	Timing_CallDelayed_m5457B7766D02CD0B2F126CA8BD390CB2AA297792,
	Timing_CallDelayedOnInstance_m0566B9634265D00866C047DAA470FB0FFFFCA641,
	Timing_CallDelayed_m15AEF2B7A29E72057E04F97AFE49CC4B4A1FD88D,
	Timing_CallDelayedOnInstance_m51588C952EFA9EE38671425F39B9C8B6DDC826A6,
	Timing__DelayedCall_m432BA7B006ACC96BA39DEFDA8D43A7252FCE3FD0,
	Timing_CallPeriodically_m1D24275D31549F5F9377066C83F94213553F7152,
	Timing_CallPeriodicallyOnInstance_mA668AE877CAC737E34DC380069168BBC80270318,
	Timing_CallPeriodically_m2CD57972660D7A950B66AD067D174B9895CCD8A7,
	Timing_CallPeriodicallyOnInstance_m68E620498EE17E3A22C89B5540B6FB93DCC4E92D,
	Timing_CallContinuously_m6E12EBC1780E7DC4E03D0E2A2EE64541B7436C40,
	Timing_CallContinuouslyOnInstance_mF7C4A529E42C65416FE1400FED32A3FCCB25A5AD,
	Timing_CallContinuously_mE09387325521236842E1C8EE3F7DF2F610942773,
	Timing_CallContinuouslyOnInstance_m30C045040B9D435CCB82434184AF44805AA2161A,
	Timing__CallContinuously_m0D2C1738F9B290685DE4C73BFA40BDB058CDD2F5,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	Timing_StartCoroutine_m34EA09FC796A33CF903739CA3DBF5648879F2186,
	Timing_StartCoroutine_mEC2C812E330C4140694739ED3D824E5B012DC5F3,
	Timing_StartCoroutine_m9FE9A8CE8F05D05CCD661F99E0A2A3DDD590A2F6,
	Timing_StartCoroutine_Auto_m5B48C1505227C1BF8A8A1038B2DCB33299621726,
	Timing_StopCoroutine_mF64A78FE242EDBDDEF9CAB862CB470E3E85179F3,
	Timing_StopCoroutine_m66984C18B8B1328ABD1A3B3748CACF304F14096C,
	Timing_StopCoroutine_m119010370418B49B6A125B413C10D1261A59ED7E,
	Timing_StopAllCoroutines_m623B709C0A9F47ABC47E37A95612D07F80A0F7FC,
	Timing_Destroy_mC0EF3FE81859163807DC430AC5EC2873E41992FA,
	Timing_Destroy_mFE6064DA4D3F071742E97CDAA982504EEE87F2A5,
	Timing_DestroyObject_m8CA5CC31D289577F3017F7DC32E483C47CB4139C,
	Timing_DestroyObject_m2255C0D93D513B8E6B7FA0735C2B647197020C81,
	Timing_DestroyImmediate_mDB4C516268AAC18D1A3B92F5189B70DBA001EB18,
	Timing_DestroyImmediate_mC3D6A466DA2897A8C71DF58B0F428A526A225D81,
	Timing_Instantiate_m34B73E6F02337E7DE6FE8F60A3D778BE429C5050,
	Timing_Instantiate_mA69DC7167ED58C4898079B37C7BBC0A604626F8B,
	NULL,
	NULL,
	Timing_FindObjectOfType_m90F5072BECEDA683CC775EF8DF666168812A8135,
	NULL,
	Timing_FindObjectsOfType_m80827A902F888C06AE279EC988B2A04F8C4BB175,
	Timing_print_mB5DFEE79C97B21A4F15CB92F27D27FFCEE81D1B5,
	Timing__ctor_m843921526A267AAA7D4D6C47ACA176581F46F8FE,
	Timing__cctor_mBE5ECE84AE85CBF56238860254526AFFAB169970,
	ProcessIndex_Equals_m0606720D3D1A042B4FEE328E47520C1F54FAAAFC,
	ProcessIndex_Equals_mE98E3DCDA417344D6838C798774A6B41F375D201,
	ProcessIndex_op_Equality_m352DEAB6FCAFE4F7DC428327844042F0C7E8862A,
	ProcessIndex_op_Inequality_m0AF96BF2B53345ED60385E4B5334B25F8D58F3C8,
	ProcessIndex_GetHashCode_m4AAF9C4E8445B9A0FBF925F8E51032FBA39250D7,
	U3C_CallContinuouslyU3Ed__159__ctor_m407E2DDCA020D3CE37DC2A3389097E4C408727D2,
	U3C_CallContinuouslyU3Ed__159_System_IDisposable_Dispose_mA11DE1F444CD11D68A79D2B393811CFD95C905EA,
	U3C_CallContinuouslyU3Ed__159_MoveNext_m3254D317AFA7B1B0E4F042F15F292CF837E359AB,
	U3C_CallContinuouslyU3Ed__159_System_Collections_Generic_IEnumeratorU3CSystem_SingleU3E_get_Current_mF9DB8C5827F773DB9C5001DFA7CA1232647C0940,
	U3C_CallContinuouslyU3Ed__159_System_Collections_IEnumerator_Reset_m9A050B4E7C9D745A1925DE5A758AD05F6764D915,
	U3C_CallContinuouslyU3Ed__159_System_Collections_IEnumerator_get_Current_m055539DFC41FDE3961D145352C4732E67EA85434,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	U3C_DelayedCallU3Ed__150__ctor_m66CD04978D937B16F0BBD768F1D9D9476D2F7AF4,
	U3C_DelayedCallU3Ed__150_System_IDisposable_Dispose_m3D62AA8D6C3B9DE7F999BBA71E7416847926C95F,
	U3C_DelayedCallU3Ed__150_MoveNext_mDFF3EE6ED626ED3EA42A7F207159999AA144DB70,
	U3C_DelayedCallU3Ed__150_System_Collections_Generic_IEnumeratorU3CSystem_SingleU3E_get_Current_m16FA13EA12E857331D24995E3DDB38127FD8B178,
	U3C_DelayedCallU3Ed__150_System_Collections_IEnumerator_Reset_m0BFF6A5787DC19C1BD48437EFE6CF81F9BC56E4D,
	U3C_DelayedCallU3Ed__150_System_Collections_IEnumerator_get_Current_m254A3ABA832A0BC944C1F166E822D64BE13080F0,
	U3C_InjectDelayU3Ed__123__ctor_m911E53FE6A7C8B3A4F7CA11CACA410BC835E7A46,
	U3C_InjectDelayU3Ed__123_System_IDisposable_Dispose_mEEEFA4BFD0C99E365C195B3ADF4367660280D4C7,
	U3C_InjectDelayU3Ed__123_MoveNext_m608DAAE0B499E9A66339C9863EAA883CB3D305F1,
	U3C_InjectDelayU3Ed__123_System_Collections_Generic_IEnumeratorU3CSystem_SingleU3E_get_Current_m5F8747BF616E23B938A592588EBCF89EFCBEE72D,
	U3C_InjectDelayU3Ed__123_System_Collections_IEnumerator_Reset_mE5FE65D262656E91D5CFE7E6C39AAB1E093AC161,
	U3C_InjectDelayU3Ed__123_System_Collections_IEnumerator_get_Current_m1DB3A4328D230547F4852AF990E17CB55BF030FB,
	U3C_StartWhenDoneU3Ed__131__ctor_m39D0253CE1E38AE821C7964327E058748B7210BA,
	U3C_StartWhenDoneU3Ed__131_System_IDisposable_Dispose_m5C5B58F67FB6E2AE352E8915C6CAED9FBE937809,
	U3C_StartWhenDoneU3Ed__131_MoveNext_m6A769ADD781A22BC21B10B96962050B08EF5A7C6,
	U3C_StartWhenDoneU3Ed__131_U3CU3Em__Finally1_m1E97EF2A45F8FDEB7C893BC8A6A15E002576010C,
	U3C_StartWhenDoneU3Ed__131_System_Collections_Generic_IEnumeratorU3CSystem_SingleU3E_get_Current_m9483F759791A07FAC776F6633640AFD0D9251308,
	U3C_StartWhenDoneU3Ed__131_System_Collections_IEnumerator_Reset_mEE54626DB84DEFD57004CCF8FD4FFFE70776B9CA,
	U3C_StartWhenDoneU3Ed__131_System_Collections_IEnumerator_get_Current_m491210BBA1E04FBD7EE1F1AF697D0FE6D50738F7,
	U3C_StartWhenDoneU3Ed__137__ctor_m2B518F87724686098D3CE5696389C00248EC4723,
	U3C_StartWhenDoneU3Ed__137_System_IDisposable_Dispose_mACBB33968FCBF365F7A868A323205C00C4CE2D0E,
	U3C_StartWhenDoneU3Ed__137_MoveNext_m59AACFD971D94C6773E0EF329B63C860911BE520,
	U3C_StartWhenDoneU3Ed__137_System_Collections_Generic_IEnumeratorU3CSystem_SingleU3E_get_Current_mF1C3EC22B6BA7F1AA1C69958A28EF3B60FC0754E,
	U3C_StartWhenDoneU3Ed__137_System_Collections_IEnumerator_Reset_m2B15CDE0BD27F40E6E9400A162A95DA39052390F,
	U3C_StartWhenDoneU3Ed__137_System_Collections_IEnumerator_get_Current_m3DA53F2FF39D0DA6F00EAECC83999AB00500F269,
	U3C_StartWhenDoneU3Ed__139__ctor_mB0F491FE68F6541263CCFA5074887BD0FAFC187E,
	U3C_StartWhenDoneU3Ed__139_System_IDisposable_Dispose_mC20C3357FA8080E9855C8C200C775F4C472A2CCC,
	U3C_StartWhenDoneU3Ed__139_MoveNext_m7D8BC152565993783B926619777BA61470980E3D,
	U3C_StartWhenDoneU3Ed__139_System_Collections_Generic_IEnumeratorU3CSystem_SingleU3E_get_Current_m2710B26C5A214133F86D74F08F226E1F9F4851D1,
	U3C_StartWhenDoneU3Ed__139_System_Collections_IEnumerator_Reset_m13CE88E834090847AE3539C17140A9978FA47FBF,
	U3C_StartWhenDoneU3Ed__139_System_Collections_IEnumerator_get_Current_m15726F5D7DC87D2568195EA9A0ADF0AFEA0ABF80,
	CoroutineHandle_get_Key_mCB7FA3D0481F9A13B0EC1FA22CB6C9AFA95F830B,
	CoroutineHandle__ctor_mCF4D58FEC43F4D8E816A39148B5C949B78CEB02D,
	CoroutineHandle_Equals_m376A1B4289D9266EA669B42997BD425DC59A81E3,
	CoroutineHandle_Equals_mE9A2F6DD86A6E40A3AC3B2C0B5717FB3154C3E95,
	CoroutineHandle_op_Equality_m778E4A099E5894B65791A47723E6DC7063FD8A39,
	CoroutineHandle_op_Inequality_m84A9D76D1CCF55D2CD202E199A50FBDC25A2974E,
	CoroutineHandle_GetHashCode_m146A36001E4646CB9F3F4548A7DD789FF89928EA,
	CoroutineHandle_get_IsValid_mA4DA81202B56F396DABF930A009CCF65F34FAD53,
	CoroutineHandle__cctor_m4097C3A6183F3C35B449D79E41C51223222BFB91,
	MECExtensionMethods1_RunCoroutine_m765CC3F57C4649F25A642A01905A46F58D89FAF1,
	MECExtensionMethods1_RunCoroutine_m7192C1F6D09713D6685B3EC39A1D3B3D20BBAA7F,
	MECExtensionMethods1_RunCoroutine_m1B090E1DF8B0EF074A3E121C321B1FA477F0A9B9,
	MECExtensionMethods1_RunCoroutine_m2DB311924180339BFE4696FE6A8FF1514B8CD492,
};
extern void ProcessIndex_Equals_m0606720D3D1A042B4FEE328E47520C1F54FAAAFC_AdjustorThunk (void);
extern void ProcessIndex_Equals_mE98E3DCDA417344D6838C798774A6B41F375D201_AdjustorThunk (void);
extern void ProcessIndex_GetHashCode_m4AAF9C4E8445B9A0FBF925F8E51032FBA39250D7_AdjustorThunk (void);
extern void CoroutineHandle_get_Key_mCB7FA3D0481F9A13B0EC1FA22CB6C9AFA95F830B_AdjustorThunk (void);
extern void CoroutineHandle__ctor_mCF4D58FEC43F4D8E816A39148B5C949B78CEB02D_AdjustorThunk (void);
extern void CoroutineHandle_Equals_m376A1B4289D9266EA669B42997BD425DC59A81E3_AdjustorThunk (void);
extern void CoroutineHandle_Equals_mE9A2F6DD86A6E40A3AC3B2C0B5717FB3154C3E95_AdjustorThunk (void);
extern void CoroutineHandle_GetHashCode_m146A36001E4646CB9F3F4548A7DD789FF89928EA_AdjustorThunk (void);
extern void CoroutineHandle_get_IsValid_mA4DA81202B56F396DABF930A009CCF65F34FAD53_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[9] = 
{
	{ 0x06000131, ProcessIndex_Equals_m0606720D3D1A042B4FEE328E47520C1F54FAAAFC_AdjustorThunk },
	{ 0x06000132, ProcessIndex_Equals_mE98E3DCDA417344D6838C798774A6B41F375D201_AdjustorThunk },
	{ 0x06000135, ProcessIndex_GetHashCode_m4AAF9C4E8445B9A0FBF925F8E51032FBA39250D7_AdjustorThunk },
	{ 0x06000161, CoroutineHandle_get_Key_mCB7FA3D0481F9A13B0EC1FA22CB6C9AFA95F830B_AdjustorThunk },
	{ 0x06000162, CoroutineHandle__ctor_mCF4D58FEC43F4D8E816A39148B5C949B78CEB02D_AdjustorThunk },
	{ 0x06000163, CoroutineHandle_Equals_m376A1B4289D9266EA669B42997BD425DC59A81E3_AdjustorThunk },
	{ 0x06000164, CoroutineHandle_Equals_mE9A2F6DD86A6E40A3AC3B2C0B5717FB3154C3E95_AdjustorThunk },
	{ 0x06000167, CoroutineHandle_GetHashCode_m146A36001E4646CB9F3F4548A7DD789FF89928EA_AdjustorThunk },
	{ 0x06000168, CoroutineHandle_get_IsValid_mA4DA81202B56F396DABF930A009CCF65F34FAD53_AdjustorThunk },
};
static const int32_t s_InvokerIndices[365] = 
{
	10629,
	10629,
	9465,
	1698,
	13298,
	8801,
	2307,
	367,
	9460,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	10629,
	10442,
	13298,
	13298,
	4493,
	7736,
	7736,
	10682,
	7736,
	10682,
	13052,
	13298,
	10682,
	13298,
	13298,
	13298,
	2642,
	10629,
	2338,
	1616,
	56,
	12996,
	2010,
	30,
	13298,
	4112,
	13298,
	13298,
	10629,
	13298,
	80,
	289,
	5781,
	13298,
	13298,
	13298,
	13298,
	8120,
	13298,
	13298,
	13298,
	13298,
	13298,
	2910,
	13298,
	7736,
	7736,
	4515,
	13052,
	2415,
	10823,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	10682,
	13298,
	5845,
	10912,
	5845,
	13298,
	13298,
	5845,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	21355,
	21274,
	21274,
	20515,
	20511,
	13298,
	13298,
	13298,
	13298,
	7736,
	7736,
	7685,
	7736,
	13298,
	13298,
	13298,
	13298,
	13298,
	10682,
	10629,
	10682,
	13298,
	13052,
	13298,
	10682,
	10682,
	13298,
	836,
	10682,
	13298,
	13298,
	21355,
	13298,
	10442,
	10629,
	2397,
	5681,
	13298,
	4160,
	13298,
	398,
	8801,
	9267,
	4466,
	12875,
	13298,
	4722,
	21355,
	2626,
	13298,
	13298,
	13195,
	10823,
	13298,
	13298,
	13298,
	13298,
	10823,
	13298,
	18006,
	16440,
	15467,
	10629,
	13298,
	12815,
	13195,
	13298,
	13052,
	10629,
	13298,
	12815,
	13195,
	13298,
	13052,
	10629,
	13298,
	12815,
	13195,
	13298,
	13052,
	13298,
	13298,
	13298,
	21382,
	13298,
	21334,
	21334,
	20847,
	20847,
	21274,
	20847,
	21234,
	12843,
	10473,
	21274,
	20847,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	19940,
	17604,
	17603,
	16073,
	8150,
	3805,
	3804,
	2268,
	520,
	21263,
	12996,
	20198,
	8735,
	20211,
	8845,
	21263,
	12996,
	20198,
	8735,
	20211,
	8845,
	21263,
	12996,
	20198,
	8735,
	20211,
	8845,
	7685,
	9456,
	20498,
	5671,
	10473,
	8073,
	9299,
	9299,
	8073,
	3754,
	3754,
	4491,
	8073,
	8073,
	5887,
	20667,
	9460,
	20655,
	18158,
	4455,
	4737,
	10473,
	7595,
	17993,
	20664,
	18006,
	20664,
	18006,
	3368,
	3368,
	17605,
	3806,
	16075,
	2270,
	16074,
	2269,
	14955,
	1041,
	2420,
	14957,
	1043,
	14121,
	521,
	16075,
	2270,
	14956,
	1042,
	1675,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	9272,
	4489,
	9272,
	9272,
	10682,
	10682,
	10682,
	13298,
	20847,
	18820,
	20847,
	18820,
	20847,
	18795,
	20847,
	16924,
	0,
	0,
	20515,
	0,
	20515,
	20847,
	13298,
	21355,
	8073,
	7736,
	17573,
	17573,
	12996,
	10629,
	13298,
	12815,
	13195,
	13298,
	13052,
	0,
	0,
	0,
	0,
	0,
	0,
	10629,
	13298,
	12815,
	13195,
	13298,
	13052,
	10629,
	13298,
	12815,
	13195,
	13298,
	13052,
	10629,
	13298,
	12815,
	13298,
	13195,
	13298,
	13052,
	10629,
	13298,
	12815,
	13195,
	13298,
	13052,
	10629,
	13298,
	12815,
	13195,
	13298,
	13052,
	12815,
	10442,
	7595,
	7736,
	17401,
	17401,
	12996,
	12815,
	21355,
	19940,
	17604,
	17603,
	16073,
};
static const Il2CppTokenRangePair s_rgctxIndices[12] = 
{
	{ 0x0200002A, { 30, 4 } },
	{ 0x06000110, { 0, 3 } },
	{ 0x06000111, { 3, 3 } },
	{ 0x06000112, { 6, 3 } },
	{ 0x06000113, { 9, 3 } },
	{ 0x06000114, { 12, 3 } },
	{ 0x06000115, { 15, 3 } },
	{ 0x06000116, { 18, 3 } },
	{ 0x06000117, { 21, 3 } },
	{ 0x06000118, { 24, 4 } },
	{ 0x0600012A, { 28, 1 } },
	{ 0x0600012C, { 29, 1 } },
};
extern const uint32_t g_rgctx_Action_1_t6BC5F1A10EC7A2B017FEECA2BADCDB9673159F32;
extern const uint32_t g_rgctx_T_t00088A619EF1FC4A079C25D1BACF1E998BA781CC;
extern const uint32_t g_rgctx_Timing__CallContinuously_TisT_t00088A619EF1FC4A079C25D1BACF1E998BA781CC_mA7B08DD83E6C11E82E7814C253ED6623B87AD6B2;
extern const uint32_t g_rgctx_Action_1_tD84F1AA0EABBB4B8842F912468C586599D13B903;
extern const uint32_t g_rgctx_T_tDDAC087B6E5383EABDCEF842A42836B3BCA99F30;
extern const uint32_t g_rgctx_Timing__CallContinuously_TisT_tDDAC087B6E5383EABDCEF842A42836B3BCA99F30_mD358ACB1AC9D79A531FDE3243D3979AE375A587E;
extern const uint32_t g_rgctx_Action_1_t2F1F123EFC2CAC35C81EC6130FF34258F3F09FB1;
extern const uint32_t g_rgctx_T_t671CE967946FAA619437EED05C62ABA61E2553B1;
extern const uint32_t g_rgctx_Timing__CallContinuously_TisT_t671CE967946FAA619437EED05C62ABA61E2553B1_mE75F35EC92C8AF4132474C358BDA58EDE5CA58E8;
extern const uint32_t g_rgctx_Action_1_t3DB89CA058EFF6AEA81A10A0990402368EADB295;
extern const uint32_t g_rgctx_T_tB65DB290DA010B2D1E64214B274249C202D59F27;
extern const uint32_t g_rgctx_Timing__CallContinuously_TisT_tB65DB290DA010B2D1E64214B274249C202D59F27_m5CF34D23472C4C0ACCA64C1518F7ED4073B279D3;
extern const uint32_t g_rgctx_Action_1_t9381C5E54BEBF9302BF550EC5ED6F2A97B3D0784;
extern const uint32_t g_rgctx_T_t7C6BBD98E3B4D192841025F1C7146708D2BE3A25;
extern const uint32_t g_rgctx_Timing__CallContinuously_TisT_t7C6BBD98E3B4D192841025F1C7146708D2BE3A25_mAC17A6E313BADF48CB64126F04C3AD48460B2D55;
extern const uint32_t g_rgctx_Action_1_tCDF0D923B9508E3DA30B0C5D0AF9F008E251D2DA;
extern const uint32_t g_rgctx_T_t8AFA37398D252AE79E8964C4523941D749EE558B;
extern const uint32_t g_rgctx_Timing__CallContinuously_TisT_t8AFA37398D252AE79E8964C4523941D749EE558B_m6712A999F0B150B0B644A71ACCE6B34A4B385827;
extern const uint32_t g_rgctx_Action_1_t07514C42A0FDD3C4ADF16C4A895EBE92042E4D10;
extern const uint32_t g_rgctx_T_t43AAD9952AFBB9FF4EB15501A1FA0BE4297BD300;
extern const uint32_t g_rgctx_Timing__CallContinuously_TisT_t43AAD9952AFBB9FF4EB15501A1FA0BE4297BD300_m51A229C6885432A4EBAA7CDE903B44891765D977;
extern const uint32_t g_rgctx_Action_1_t7D282428717BF058E4E717CB0EFD1A18EECB43B7;
extern const uint32_t g_rgctx_T_t8110535C0D93A9CFF0DE257416996CAFBE0C1D92;
extern const uint32_t g_rgctx_Timing__CallContinuously_TisT_t8110535C0D93A9CFF0DE257416996CAFBE0C1D92_m641B4BBE8990F7207460BE6B3C3427761C51B888;
extern const uint32_t g_rgctx_U3C_CallContinuouslyU3Ed__168_1_tA1E92C1155EC13AD8F74E60FABBB1AA5918055F5;
extern const uint32_t g_rgctx_U3C_CallContinuouslyU3Ed__168_1__ctor_m03CD44EC5939861CCA2BD6181C1528A5FB4C3234;
extern const uint32_t g_rgctx_T_t41D74856A18E396F5C45F60C8718349FA2EBCE53;
extern const uint32_t g_rgctx_Action_1_t13389F57F286F4A988DED68F67A94E2BC4D3526B;
extern const uint32_t g_rgctx_T_t92E524899328790CF5B8133326E3C4BBA2864DEC;
extern const uint32_t g_rgctx_TU5BU5D_tA70093A7C20947E4BAD21E2C84D520E1ABA1F9E0;
extern const uint32_t g_rgctx_U3C_CallContinuouslyU3Ed__168_1_tD27820F995F1CDE2BFE51717D4416E9F771DC472;
extern const uint32_t g_rgctx_Action_1_t65E1A9623A31B99C46B50FD518422F67ECC09DB2;
extern const uint32_t g_rgctx_T_t15C598C27F441E96E5DC2685E26E6D86A3B59012;
extern const uint32_t g_rgctx_Action_1_Invoke_m6624913B601865406477439EFB750DEE13843B72;
static const Il2CppRGCTXDefinition s_rgctxValues[34] = 
{
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_1_t6BC5F1A10EC7A2B017FEECA2BADCDB9673159F32 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t00088A619EF1FC4A079C25D1BACF1E998BA781CC },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Timing__CallContinuously_TisT_t00088A619EF1FC4A079C25D1BACF1E998BA781CC_mA7B08DD83E6C11E82E7814C253ED6623B87AD6B2 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_1_tD84F1AA0EABBB4B8842F912468C586599D13B903 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tDDAC087B6E5383EABDCEF842A42836B3BCA99F30 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Timing__CallContinuously_TisT_tDDAC087B6E5383EABDCEF842A42836B3BCA99F30_mD358ACB1AC9D79A531FDE3243D3979AE375A587E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_1_t2F1F123EFC2CAC35C81EC6130FF34258F3F09FB1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t671CE967946FAA619437EED05C62ABA61E2553B1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Timing__CallContinuously_TisT_t671CE967946FAA619437EED05C62ABA61E2553B1_mE75F35EC92C8AF4132474C358BDA58EDE5CA58E8 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_1_t3DB89CA058EFF6AEA81A10A0990402368EADB295 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tB65DB290DA010B2D1E64214B274249C202D59F27 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Timing__CallContinuously_TisT_tB65DB290DA010B2D1E64214B274249C202D59F27_m5CF34D23472C4C0ACCA64C1518F7ED4073B279D3 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_1_t9381C5E54BEBF9302BF550EC5ED6F2A97B3D0784 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t7C6BBD98E3B4D192841025F1C7146708D2BE3A25 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Timing__CallContinuously_TisT_t7C6BBD98E3B4D192841025F1C7146708D2BE3A25_mAC17A6E313BADF48CB64126F04C3AD48460B2D55 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_1_tCDF0D923B9508E3DA30B0C5D0AF9F008E251D2DA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t8AFA37398D252AE79E8964C4523941D749EE558B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Timing__CallContinuously_TisT_t8AFA37398D252AE79E8964C4523941D749EE558B_m6712A999F0B150B0B644A71ACCE6B34A4B385827 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_1_t07514C42A0FDD3C4ADF16C4A895EBE92042E4D10 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t43AAD9952AFBB9FF4EB15501A1FA0BE4297BD300 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Timing__CallContinuously_TisT_t43AAD9952AFBB9FF4EB15501A1FA0BE4297BD300_m51A229C6885432A4EBAA7CDE903B44891765D977 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_1_t7D282428717BF058E4E717CB0EFD1A18EECB43B7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t8110535C0D93A9CFF0DE257416996CAFBE0C1D92 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Timing__CallContinuously_TisT_t8110535C0D93A9CFF0DE257416996CAFBE0C1D92_m641B4BBE8990F7207460BE6B3C3427761C51B888 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3C_CallContinuouslyU3Ed__168_1_tA1E92C1155EC13AD8F74E60FABBB1AA5918055F5 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3C_CallContinuouslyU3Ed__168_1__ctor_m03CD44EC5939861CCA2BD6181C1528A5FB4C3234 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t41D74856A18E396F5C45F60C8718349FA2EBCE53 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_1_t13389F57F286F4A988DED68F67A94E2BC4D3526B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t92E524899328790CF5B8133326E3C4BBA2864DEC },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_tA70093A7C20947E4BAD21E2C84D520E1ABA1F9E0 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3C_CallContinuouslyU3Ed__168_1_tD27820F995F1CDE2BFE51717D4416E9F771DC472 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_1_t65E1A9623A31B99C46B50FD518422F67ECC09DB2 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t15C598C27F441E96E5DC2685E26E6D86A3B59012 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Action_1_Invoke_m6624913B601865406477439EFB750DEE13843B72 },
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_AssemblyU2DCSharpU2Dfirstpass_CodeGenModule;
const Il2CppCodeGenModule g_AssemblyU2DCSharpU2Dfirstpass_CodeGenModule = 
{
	"Assembly-CSharp-firstpass.dll",
	365,
	s_methodPointers,
	9,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	12,
	s_rgctxIndices,
	34,
	s_rgctxValues,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};

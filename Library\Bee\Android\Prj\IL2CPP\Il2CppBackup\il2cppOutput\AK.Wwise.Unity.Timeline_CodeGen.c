﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void AkEventPlayable_UnityEngine_Timeline_ITimelineClipAsset_get_clipCaps_m7B4D2A34DF3E15533D7E7F0FAA4709DCCD84245C (void);
extern void AkEventPlayable_CreatePlayable_m506E7CF9541880AD9E76A3CEFEB45C71C12E8ADF (void);
extern void AkEventPlayable__ctor_mA62D4E890E1D437EEAE5A36C103A18441A15B887 (void);
extern void AkEventPlayableBehavior_CallbackHandler_m13357825BA759F877AD71AEB773D23B1AF140642 (void);
extern void AkEventPlayableBehavior_IsScrubbing_m0FA4BC4A874AD64EC61ED5579A2DF6A4CE590A95 (void);
extern void AkEventPlayableBehavior_PrepareFrame_mBC6C810E400309DAAD0913C651E10F8969FD3EE6 (void);
extern void AkEventPlayableBehavior_OnBehaviourPlay_mC133D52570753336F0C2F3E1A424DC436CAADB10 (void);
extern void AkEventPlayableBehavior_OnBehaviourPause_m58C91AE1A48D642B81974A54C5ABB493E4794C9E (void);
extern void AkEventPlayableBehavior_ProcessFrame_mCB02DCBD55CCCD9C84C63083C68715341EF9F9EF (void);
extern void AkEventPlayableBehavior_ShouldPlay_mFA953F7B91688A9D65EE7F4801F2A3B00CCF2969 (void);
extern void AkEventPlayableBehavior_CheckForFadeInFadeOut_m1F6BE0593ABD5CF3DCB46974CB0B2648EDC296DE (void);
extern void AkEventPlayableBehavior_CheckForFadeOut_mD1F47ABFC917D804A556F7FBB17DB6DF125628E6 (void);
extern void AkEventPlayableBehavior_TriggerFadeIn_mD344A9234668794A190DBA1B4803FA30200E8DA2 (void);
extern void AkEventPlayableBehavior_TriggerFadeOut_mB83E888D846FC803D522EF99F89DBDB098F4E23A (void);
extern void AkEventPlayableBehavior_StopEvent_m300D04ACCC6FE99C08A7A4724E0579EC96CA90D9 (void);
extern void AkEventPlayableBehavior_PostEvent_m951673CF050842474010CCF0151A866EFBE4CE2C (void);
extern void AkEventPlayableBehavior_PlayEvent_mE1CB01B699C9A4BA97BCEF843B2FD137FEF25EA6 (void);
extern void AkEventPlayableBehavior_RetriggerEvent_mDD448BC817B9537D15FB3A0E9714CFD16ED78AAE (void);
extern void AkEventPlayableBehavior_GetProportionalTime_mC1A23679BE86F00F68E91C85FEE6B6BFCE76BBFB (void);
extern void AkEventPlayableBehavior_SeekToTime_m3AF2CCA85D795BBA6450661C7266AEEB027FF06E (void);
extern void AkEventPlayableBehavior__ctor_m50093E06D02D4CDAD7488D6C1614C6E2AE933176 (void);
extern void AkEventTrack_CreateTrackMixer_mCC5DD95BDCDBEE1EA47A29ACE681355762900EB9 (void);
extern void AkEventTrack__ctor_m5FB29D6FE3F24B8158D3CA546B5723006210987A (void);
extern void AkRTPCPlayable_get_Parameter_m1227B0CB28D68156B94E1981D692A9A81E5F9BCE (void);
extern void AkRTPCPlayable_set_Parameter_m023C242D9E4133746C45CD80C2B7DAD3DFD3835B (void);
extern void AkRTPCPlayable_get_OwningClip_m2967A0971C1065E3810F6C5FD772D3A904C4A30A (void);
extern void AkRTPCPlayable_set_OwningClip_m4BE02E52381D65FB720B8717316A40BBE99DC10F (void);
extern void AkRTPCPlayable_UnityEngine_Timeline_ITimelineClipAsset_get_clipCaps_mF18FBC99DCABA2594CF639E13D45C41D6BF7922E (void);
extern void AkRTPCPlayable_CreatePlayable_m99FC9532FE231A0076775E8D82F98BF34BC3A21F (void);
extern void AkRTPCPlayable__ctor_mB3D67F352631CA3DD05E47A16074FD1584D09688 (void);
extern void AkRTPCPlayableBehaviour_set_setRTPCGlobally_m11AA8E8DD40643A5C711BF7F9A935532024461F2 (void);
extern void AkRTPCPlayableBehaviour_get_setRTPCGlobally_m180C13EF70E81A79A4AE7619BED691E382045225 (void);
extern void AkRTPCPlayableBehaviour_set_overrideTrackObject_m63901DA8AED2B9C6AC21F5ECA29881CE1DFE45D7 (void);
extern void AkRTPCPlayableBehaviour_get_overrideTrackObject_m353710DF51B7FB2C6323C519A232B91253C37D22 (void);
extern void AkRTPCPlayableBehaviour_set_rtpcObject_m562440E072A5AEF30418A5D8900A532D254D8B1F (void);
extern void AkRTPCPlayableBehaviour_get_rtpcObject_m381B4BC3D817190BC100797A5A81D3292435D6D5 (void);
extern void AkRTPCPlayableBehaviour_set_parameter_m4012D94C6DF095347DCAD51CB259BE3F8C3E0ED1 (void);
extern void AkRTPCPlayableBehaviour_get_parameter_mE3F553CF9F070CC73B9AFCF389C018BCBB144673 (void);
extern void AkRTPCPlayableBehaviour_ProcessFrame_mE820F43E9119BD6663A93B287399CD40CFD19BF6 (void);
extern void AkRTPCPlayableBehaviour__ctor_m04E5CD5CE3F0119758A47A41C9137CD060CBBE17 (void);
extern void AkRTPCTrack_CreateTrackMixer_m355B2A8295870EA9434E26B0A1C54FC93EF2C5B2 (void);
extern void AkRTPCTrack_setPlayableProperties_mF5A061895DFB4EB68D31FEAAB22820F6C82BF80F (void);
extern void AkRTPCTrack_OnValidate_m1A5921A246CE5BF5F61DF5895505A94BB0FB4C23 (void);
extern void AkRTPCTrack__ctor_mEAD02E6A7696D4017359207D7CFB8EAB9D2118F5 (void);
extern void AkTimelineEventPlayableBehavior_CallbackHandler_mAA902A794801C40E80E9D09E695B18F0D467CBA1 (void);
extern void AkTimelineEventPlayableBehavior_IsScrubbing_mF624A75A753F486954ADC68E62CCE3BFC88B622A (void);
extern void AkTimelineEventPlayableBehavior_PrintInfo_mFA9C11A3A2CDE9565F0CC966A3EBFBAEB3BA8944 (void);
extern void AkTimelineEventPlayableBehavior_PrepareFrame_m904709D7AF182412E75AD75032BAD698962CA1CF (void);
extern void AkTimelineEventPlayableBehavior_OnBehaviourPlay_m62EE668C2075628AA942BD1F124CB119BC5DC4EE (void);
extern void AkTimelineEventPlayableBehavior_OnBehaviourPause_m2F77425A343E73BCB0188897DD329E01DE824832 (void);
extern void AkTimelineEventPlayableBehavior_ProcessFrame_m971A7977DD8B6AFD114C49933AFD62E2EC020D9F (void);
extern void AkTimelineEventPlayableBehavior_ShouldPlay_m79053DC1E5E77A0DA587A26A8CB5D53208E63369 (void);
extern void AkTimelineEventPlayableBehavior_CheckForFadeInFadeOut_mDD3B5B7CEA6EB1D0894F310A72B9D009D2462890 (void);
extern void AkTimelineEventPlayableBehavior_CheckForFadeOut_mF001B870B4EC2830B2FC689CDF0FA8C347D17701 (void);
extern void AkTimelineEventPlayableBehavior_TriggerFadeIn_mBE1F44048FCCF42B64F67EFD42379E5D5F7FBDEA (void);
extern void AkTimelineEventPlayableBehavior_TriggerFadeOut_m91499ABBCCD6DAEE9E837725ACA2A37B9DAF0573 (void);
extern void AkTimelineEventPlayableBehavior_StopEvent_m324780F319715BFE4F312A727556BB512B786E4D (void);
extern void AkTimelineEventPlayableBehavior_PostEvent_mD6A7A2732C9A1AD238AB42EA2CD31ADD98510EF8 (void);
extern void AkTimelineEventPlayableBehavior_PlayEvent_m25D7FD11A08F2C30F464F04FAC44CF6387B38879 (void);
extern void AkTimelineEventPlayableBehavior_RetriggerEvent_m178C80896DC28E291FAFD3B882903B6516EDAF83 (void);
extern void AkTimelineEventPlayableBehavior_GetProportionalTime_m5D4640D0758D84A8AEE4E16FE1B2FED383967414 (void);
extern void AkTimelineEventPlayableBehavior_SeekToTime_m346ADD9328D2C6EDDDC012DBF16B3FA933B7D068 (void);
extern void AkTimelineEventPlayableBehavior__ctor_m09443B376464C3E546ED860AB3F82486614BAEBA (void);
extern void AkTimelineEventPlayable_UnityEngine_Timeline_ITimelineClipAsset_get_clipCaps_mFC41A273AFC4D16BD1C1841FBBAD33C335F1EABC (void);
extern void AkTimelineEventPlayable_CreatePlayable_mFD492A91398189786420E3AF8E0E4948EB6B4CA8 (void);
extern void AkTimelineEventPlayable__ctor_m14F5EBF02E63E6EE9AB7EC1D0932B348B71B8112 (void);
extern void AkTimelineEventTrack_CreateTrackMixer_mC0634E1A5BB7447D8601EF2477FECEFA8A41BF1E (void);
extern void AkTimelineEventTrack__ctor_mA40DF0B4AF0C0CA7BDF606A2F7A96EAFC608C9B2 (void);
extern void AkTimelineRtpcPlayableBehaviour_set_RTPC_m944BB6F5C73EAF491DEE83C0491E40B5F7FB7C48 (void);
extern void AkTimelineRtpcPlayableBehaviour_get_RTPC_mCDA1801BFC40B407AB5E0F322D96E6474E02E028 (void);
extern void AkTimelineRtpcPlayableBehaviour_set_setGlobally_m7AF3D85FC51C079A15ABDCA6E27C52A7341AA98D (void);
extern void AkTimelineRtpcPlayableBehaviour_get_setGlobally_m0730E13DBF0E86E418958FD3762920879601FFD4 (void);
extern void AkTimelineRtpcPlayableBehaviour_set_gameObject_m2E2DA77B5DB71AC0A9B31BA5BF1C057AFB08BCD9 (void);
extern void AkTimelineRtpcPlayableBehaviour_get_gameObject_m894D0A5EC16CBF8E43953A382815B9AFB1194459 (void);
extern void AkTimelineRtpcPlayableBehaviour_ProcessFrame_m6A17461D0E5B85F83BE471D0DF5978A26993D1CC (void);
extern void AkTimelineRtpcPlayableBehaviour__ctor_mD476C42C4B887E7C3129C2B4B52FAADC245CCD48 (void);
extern void AkTimelineRtpcPlayable_SetupClipDisplay_m38AB2CF753A41244AF7C8DCFF167BF1954E767CC (void);
extern void AkTimelineRtpcPlayable_get_owningClip_mDCFA3F492FD3D23811B7F60BE5E18CCC3B65C7C6 (void);
extern void AkTimelineRtpcPlayable_set_owningClip_m166D2608BAB25D2F22A4A271E19E30AB66A20127 (void);
extern void AkTimelineRtpcPlayable_UnityEngine_Timeline_ITimelineClipAsset_get_clipCaps_m1927DAF55DC06320B3075F80F3C5C4A0B25329D0 (void);
extern void AkTimelineRtpcPlayable_CreatePlayable_m64AD80241AE91D1FAC25966DCDA790E1A5503D00 (void);
extern void AkTimelineRtpcPlayable__ctor_mFBE6A95E6A65DF17ECBA9ACE87550C5EB61D9320 (void);
extern void AkTimelineRtpcTrack_CreateTrackMixer_m4244B081579314295AC568AF882DBF811A17FA99 (void);
extern void AkTimelineRtpcTrack_OnValidate_mA7783CB54F05744D3FABE3D30E8C17EDC42F474F (void);
extern void AkTimelineRtpcTrack__ctor_m69D4A3C72FABE72D19A35F2F80BC67EF99542BE3 (void);
extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_m9CD81C29C4C290F753A5C1FD4A198DC34BF792C5 (void);
extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_m318B5C3404157C08DAC7C015EC187DDA41389618 (void);
static Il2CppMethodPointer s_methodPointers[87] = 
{
	AkEventPlayable_UnityEngine_Timeline_ITimelineClipAsset_get_clipCaps_m7B4D2A34DF3E15533D7E7F0FAA4709DCCD84245C,
	AkEventPlayable_CreatePlayable_m506E7CF9541880AD9E76A3CEFEB45C71C12E8ADF,
	AkEventPlayable__ctor_mA62D4E890E1D437EEAE5A36C103A18441A15B887,
	AkEventPlayableBehavior_CallbackHandler_m13357825BA759F877AD71AEB773D23B1AF140642,
	AkEventPlayableBehavior_IsScrubbing_m0FA4BC4A874AD64EC61ED5579A2DF6A4CE590A95,
	AkEventPlayableBehavior_PrepareFrame_mBC6C810E400309DAAD0913C651E10F8969FD3EE6,
	AkEventPlayableBehavior_OnBehaviourPlay_mC133D52570753336F0C2F3E1A424DC436CAADB10,
	AkEventPlayableBehavior_OnBehaviourPause_m58C91AE1A48D642B81974A54C5ABB493E4794C9E,
	AkEventPlayableBehavior_ProcessFrame_mCB02DCBD55CCCD9C84C63083C68715341EF9F9EF,
	AkEventPlayableBehavior_ShouldPlay_mFA953F7B91688A9D65EE7F4801F2A3B00CCF2969,
	AkEventPlayableBehavior_CheckForFadeInFadeOut_m1F6BE0593ABD5CF3DCB46974CB0B2648EDC296DE,
	AkEventPlayableBehavior_CheckForFadeOut_mD1F47ABFC917D804A556F7FBB17DB6DF125628E6,
	AkEventPlayableBehavior_TriggerFadeIn_mD344A9234668794A190DBA1B4803FA30200E8DA2,
	AkEventPlayableBehavior_TriggerFadeOut_mB83E888D846FC803D522EF99F89DBDB098F4E23A,
	AkEventPlayableBehavior_StopEvent_m300D04ACCC6FE99C08A7A4724E0579EC96CA90D9,
	AkEventPlayableBehavior_PostEvent_m951673CF050842474010CCF0151A866EFBE4CE2C,
	AkEventPlayableBehavior_PlayEvent_mE1CB01B699C9A4BA97BCEF843B2FD137FEF25EA6,
	AkEventPlayableBehavior_RetriggerEvent_mDD448BC817B9537D15FB3A0E9714CFD16ED78AAE,
	AkEventPlayableBehavior_GetProportionalTime_mC1A23679BE86F00F68E91C85FEE6B6BFCE76BBFB,
	AkEventPlayableBehavior_SeekToTime_m3AF2CCA85D795BBA6450661C7266AEEB027FF06E,
	AkEventPlayableBehavior__ctor_m50093E06D02D4CDAD7488D6C1614C6E2AE933176,
	AkEventTrack_CreateTrackMixer_mCC5DD95BDCDBEE1EA47A29ACE681355762900EB9,
	AkEventTrack__ctor_m5FB29D6FE3F24B8158D3CA546B5723006210987A,
	AkRTPCPlayable_get_Parameter_m1227B0CB28D68156B94E1981D692A9A81E5F9BCE,
	AkRTPCPlayable_set_Parameter_m023C242D9E4133746C45CD80C2B7DAD3DFD3835B,
	AkRTPCPlayable_get_OwningClip_m2967A0971C1065E3810F6C5FD772D3A904C4A30A,
	AkRTPCPlayable_set_OwningClip_m4BE02E52381D65FB720B8717316A40BBE99DC10F,
	AkRTPCPlayable_UnityEngine_Timeline_ITimelineClipAsset_get_clipCaps_mF18FBC99DCABA2594CF639E13D45C41D6BF7922E,
	AkRTPCPlayable_CreatePlayable_m99FC9532FE231A0076775E8D82F98BF34BC3A21F,
	AkRTPCPlayable__ctor_mB3D67F352631CA3DD05E47A16074FD1584D09688,
	AkRTPCPlayableBehaviour_set_setRTPCGlobally_m11AA8E8DD40643A5C711BF7F9A935532024461F2,
	AkRTPCPlayableBehaviour_get_setRTPCGlobally_m180C13EF70E81A79A4AE7619BED691E382045225,
	AkRTPCPlayableBehaviour_set_overrideTrackObject_m63901DA8AED2B9C6AC21F5ECA29881CE1DFE45D7,
	AkRTPCPlayableBehaviour_get_overrideTrackObject_m353710DF51B7FB2C6323C519A232B91253C37D22,
	AkRTPCPlayableBehaviour_set_rtpcObject_m562440E072A5AEF30418A5D8900A532D254D8B1F,
	AkRTPCPlayableBehaviour_get_rtpcObject_m381B4BC3D817190BC100797A5A81D3292435D6D5,
	AkRTPCPlayableBehaviour_set_parameter_m4012D94C6DF095347DCAD51CB259BE3F8C3E0ED1,
	AkRTPCPlayableBehaviour_get_parameter_mE3F553CF9F070CC73B9AFCF389C018BCBB144673,
	AkRTPCPlayableBehaviour_ProcessFrame_mE820F43E9119BD6663A93B287399CD40CFD19BF6,
	AkRTPCPlayableBehaviour__ctor_m04E5CD5CE3F0119758A47A41C9137CD060CBBE17,
	AkRTPCTrack_CreateTrackMixer_m355B2A8295870EA9434E26B0A1C54FC93EF2C5B2,
	AkRTPCTrack_setPlayableProperties_mF5A061895DFB4EB68D31FEAAB22820F6C82BF80F,
	AkRTPCTrack_OnValidate_m1A5921A246CE5BF5F61DF5895505A94BB0FB4C23,
	AkRTPCTrack__ctor_mEAD02E6A7696D4017359207D7CFB8EAB9D2118F5,
	AkTimelineEventPlayableBehavior_CallbackHandler_mAA902A794801C40E80E9D09E695B18F0D467CBA1,
	AkTimelineEventPlayableBehavior_IsScrubbing_mF624A75A753F486954ADC68E62CCE3BFC88B622A,
	AkTimelineEventPlayableBehavior_PrintInfo_mFA9C11A3A2CDE9565F0CC966A3EBFBAEB3BA8944,
	AkTimelineEventPlayableBehavior_PrepareFrame_m904709D7AF182412E75AD75032BAD698962CA1CF,
	AkTimelineEventPlayableBehavior_OnBehaviourPlay_m62EE668C2075628AA942BD1F124CB119BC5DC4EE,
	AkTimelineEventPlayableBehavior_OnBehaviourPause_m2F77425A343E73BCB0188897DD329E01DE824832,
	AkTimelineEventPlayableBehavior_ProcessFrame_m971A7977DD8B6AFD114C49933AFD62E2EC020D9F,
	AkTimelineEventPlayableBehavior_ShouldPlay_m79053DC1E5E77A0DA587A26A8CB5D53208E63369,
	AkTimelineEventPlayableBehavior_CheckForFadeInFadeOut_mDD3B5B7CEA6EB1D0894F310A72B9D009D2462890,
	AkTimelineEventPlayableBehavior_CheckForFadeOut_mF001B870B4EC2830B2FC689CDF0FA8C347D17701,
	AkTimelineEventPlayableBehavior_TriggerFadeIn_mBE1F44048FCCF42B64F67EFD42379E5D5F7FBDEA,
	AkTimelineEventPlayableBehavior_TriggerFadeOut_m91499ABBCCD6DAEE9E837725ACA2A37B9DAF0573,
	AkTimelineEventPlayableBehavior_StopEvent_m324780F319715BFE4F312A727556BB512B786E4D,
	AkTimelineEventPlayableBehavior_PostEvent_mD6A7A2732C9A1AD238AB42EA2CD31ADD98510EF8,
	AkTimelineEventPlayableBehavior_PlayEvent_m25D7FD11A08F2C30F464F04FAC44CF6387B38879,
	AkTimelineEventPlayableBehavior_RetriggerEvent_m178C80896DC28E291FAFD3B882903B6516EDAF83,
	AkTimelineEventPlayableBehavior_GetProportionalTime_m5D4640D0758D84A8AEE4E16FE1B2FED383967414,
	AkTimelineEventPlayableBehavior_SeekToTime_m346ADD9328D2C6EDDDC012DBF16B3FA933B7D068,
	AkTimelineEventPlayableBehavior__ctor_m09443B376464C3E546ED860AB3F82486614BAEBA,
	AkTimelineEventPlayable_UnityEngine_Timeline_ITimelineClipAsset_get_clipCaps_mFC41A273AFC4D16BD1C1841FBBAD33C335F1EABC,
	AkTimelineEventPlayable_CreatePlayable_mFD492A91398189786420E3AF8E0E4948EB6B4CA8,
	AkTimelineEventPlayable__ctor_m14F5EBF02E63E6EE9AB7EC1D0932B348B71B8112,
	AkTimelineEventTrack_CreateTrackMixer_mC0634E1A5BB7447D8601EF2477FECEFA8A41BF1E,
	AkTimelineEventTrack__ctor_mA40DF0B4AF0C0CA7BDF606A2F7A96EAFC608C9B2,
	AkTimelineRtpcPlayableBehaviour_set_RTPC_m944BB6F5C73EAF491DEE83C0491E40B5F7FB7C48,
	AkTimelineRtpcPlayableBehaviour_get_RTPC_mCDA1801BFC40B407AB5E0F322D96E6474E02E028,
	AkTimelineRtpcPlayableBehaviour_set_setGlobally_m7AF3D85FC51C079A15ABDCA6E27C52A7341AA98D,
	AkTimelineRtpcPlayableBehaviour_get_setGlobally_m0730E13DBF0E86E418958FD3762920879601FFD4,
	AkTimelineRtpcPlayableBehaviour_set_gameObject_m2E2DA77B5DB71AC0A9B31BA5BF1C057AFB08BCD9,
	AkTimelineRtpcPlayableBehaviour_get_gameObject_m894D0A5EC16CBF8E43953A382815B9AFB1194459,
	AkTimelineRtpcPlayableBehaviour_ProcessFrame_m6A17461D0E5B85F83BE471D0DF5978A26993D1CC,
	AkTimelineRtpcPlayableBehaviour__ctor_mD476C42C4B887E7C3129C2B4B52FAADC245CCD48,
	AkTimelineRtpcPlayable_SetupClipDisplay_m38AB2CF753A41244AF7C8DCFF167BF1954E767CC,
	AkTimelineRtpcPlayable_get_owningClip_mDCFA3F492FD3D23811B7F60BE5E18CCC3B65C7C6,
	AkTimelineRtpcPlayable_set_owningClip_m166D2608BAB25D2F22A4A271E19E30AB66A20127,
	AkTimelineRtpcPlayable_UnityEngine_Timeline_ITimelineClipAsset_get_clipCaps_m1927DAF55DC06320B3075F80F3C5C4A0B25329D0,
	AkTimelineRtpcPlayable_CreatePlayable_m64AD80241AE91D1FAC25966DCDA790E1A5503D00,
	AkTimelineRtpcPlayable__ctor_mFBE6A95E6A65DF17ECBA9ACE87550C5EB61D9320,
	AkTimelineRtpcTrack_CreateTrackMixer_m4244B081579314295AC568AF882DBF811A17FA99,
	AkTimelineRtpcTrack_OnValidate_mA7783CB54F05744D3FABE3D30E8C17EDC42F474F,
	AkTimelineRtpcTrack__ctor_m69D4A3C72FABE72D19A35F2F80BC67EF99542BE3,
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_m9CD81C29C4C290F753A5C1FD4A198DC34BF792C5,
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_m318B5C3404157C08DAC7C015EC187DDA41389618,
};
static const int32_t s_InvokerIndices[87] = 
{
	12996,
	4525,
	13298,
	2739,
	7655,
	5737,
	5737,
	5737,
	2826,
	7743,
	10690,
	5736,
	10690,
	10690,
	10629,
	12815,
	13298,
	10690,
	9458,
	9458,
	13298,
	2444,
	13298,
	13052,
	10682,
	13052,
	10682,
	12996,
	4525,
	13298,
	10442,
	12815,
	10442,
	12815,
	10682,
	13052,
	10682,
	13052,
	2826,
	13298,
	2444,
	13298,
	13298,
	13298,
	2739,
	3528,
	2795,
	5737,
	5737,
	5737,
	2826,
	7743,
	10690,
	5736,
	10690,
	10690,
	10629,
	12815,
	13298,
	10690,
	9458,
	9458,
	13298,
	12996,
	4525,
	13298,
	2444,
	13298,
	10682,
	13052,
	10442,
	12815,
	10682,
	13052,
	2826,
	13298,
	13298,
	13052,
	10682,
	12996,
	4525,
	13298,
	2444,
	13298,
	13298,
	21376,
	13298,
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_AK_Wwise_Unity_Timeline_CodeGenModule;
const Il2CppCodeGenModule g_AK_Wwise_Unity_Timeline_CodeGenModule = 
{
	"AK.Wwise.Unity.Timeline.dll",
	87,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};

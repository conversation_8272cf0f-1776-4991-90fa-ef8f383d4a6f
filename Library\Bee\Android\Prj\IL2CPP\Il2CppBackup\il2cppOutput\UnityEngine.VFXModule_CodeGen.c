﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void VFXEventAttribute__ctor_m08E26E4F79DA1062FC43501FDFE00B9EF3ED3AE1 (void);
extern void VFXEventAttribute__ctor_mF978CE9C956D15F2E4CE56B0071FB4928D90AE3C (void);
extern void VFXEventAttribute_CreateEventAttributeWrapper_m8875BE7EF5B016C001E79C5CC228969A98EA846A (void);
extern void VFXEventAttribute_SetWrapValue_mB74CE4A4E6203AA7E95F9446AC2CC043E21CB757 (void);
extern void VFXEventAttribute_Internal_Create_m2ABF5098E27A4F13603C974ECE4819F6257FA157 (void);
extern void VFXEventAttribute_Internal_InstanciateVFXEventAttribute_m67D4914621592C8A2F54808E0F77F15831A63FE6 (void);
extern void VFXEventAttribute_Internal_InitFromAsset_mFA13F75CCECE6D2DAD270E37126F211724D5EFD2 (void);
extern void VFXEventAttribute_get_vfxAsset_m5A8B68B6EA494BA64ABAFA3915EB806B29FC2A68 (void);
extern void VFXEventAttribute_Release_m959FB9B7EEC401D763A16AC1188F18B7F5B2D8F2 (void);
extern void VFXEventAttribute_Finalize_m7922B5B3EF84DB5BE447C5A6F4F6B5F00B3E4AA4 (void);
extern void VFXEventAttribute_Dispose_m172293D888316328F19F4C7E03D50CD178E0EB36 (void);
extern void VFXEventAttribute_Internal_Destroy_m2D0838414E77C04FBD44CEB8B7F825C0C4EBC30E (void);
extern void VFXEventAttribute_HasBool_m289E4E4D6D93F21BF4BC11E493D774E8DF948708 (void);
extern void VFXEventAttribute_HasInt_m9DD77A5D17F290859F15C5306847345DB434ED2F (void);
extern void VFXEventAttribute_HasUint_mE792032D519321C2C85E19DBB84338B8F138A7F4 (void);
extern void VFXEventAttribute_HasFloat_mF3A88D85B0A13A73F1735E6E91F1ADD33B6F598B (void);
extern void VFXEventAttribute_HasVector2_m6ECE42A96943962A41866E0FC9DA4CF735ADCA00 (void);
extern void VFXEventAttribute_HasVector3_m22043F5E468E1AD708AF9671163457F9E15683BD (void);
extern void VFXEventAttribute_HasVector4_mDCC54A83398009A616F78DB228DFEB04DE03B706 (void);
extern void VFXEventAttribute_SetBool_mBD3076A39A3A230C00B72EFF2A7F91CB4B548238 (void);
extern void VFXEventAttribute_SetInt_mC3236E37E1E64CAD49F30436A90972C4F23B901A (void);
extern void VFXEventAttribute_SetUint_mEB03AF9A2CB1AC5E8CB49166B29E982C39084DB8 (void);
extern void VFXEventAttribute_SetFloat_m6A47F4AF467EEAA5637887DF9E1BCE54A99DF309 (void);
extern void VFXEventAttribute_SetVector2_mC626E39E61B5DD4C00DB1DA787DEAA131DFD3DAD (void);
extern void VFXEventAttribute_SetVector3_mDC5A96D198F7FE6C422D68AF09EA319A25E90154 (void);
extern void VFXEventAttribute_SetVector4_m600C101B1D5D647ABA6DAC0AF4AE0DF94EC040D3 (void);
extern void VFXEventAttribute_SetVector2_Injected_m66D45C99B65CA21F6E3D6E8B586DCF29654C1585 (void);
extern void VFXEventAttribute_SetVector3_Injected_m6DB077BC2E3EBCF6B82E63D360CBB26A4806A8EC (void);
extern void VFXEventAttribute_SetVector4_Injected_m9EE37FB6F03DAC9DE9B07659F641B1875558C4B5 (void);
extern void VFXExpressionValues__ctor_mB5923888F1BBEEE55955E24514A69629D932DDD0 (void);
extern void VFXExpressionValues_CreateExpressionValuesWrapper_m94D1AD2FD2FF7D005B0AEF2D22333D321631AACE (void);
extern void VFXExpressionValues_GetBool_m7B4F46E1448103DA73BD6F7ADE9A5B105654B50B (void);
extern void VFXExpressionValues_GetInt_mA3669A82A8C5A438E0FBF3FC91FA0328E99F0A76 (void);
extern void VFXExpressionValues_GetUInt_mDDA11571F6AD189F564820CA73C9FB7EE89FDE98 (void);
extern void VFXExpressionValues_GetFloat_mC5C19CEE3CB1E5B7626F82C9EBE103CB3C266910 (void);
extern void VFXExpressionValues_GetVector3_m88F0FA30F13980FB30BFCD224A41469E46870225 (void);
extern void VFXExpressionValues_GetVector3_Injected_mF4A6CA5723359E4167EA4059420CC903F240F87C (void);
extern void VFXManager_get_runtimeResources_m8849FA094016FA370407278A80A04A4E90D50F30 (void);
extern void VFXManager_get_fixedTimeStep_m675FCEA0D1F90BFE9683D2DA56AED1A8C0FD1828 (void);
extern void VFXManager_get_maxDeltaTime_m1F1D08FB77264FB5CAD06C8D517362F9A7762D86 (void);
extern void VFXManager_get_maxScrubTime_m92732C852F164459EAE6A6E23932A5A04C5278FB (void);
extern void VFXManager_PrepareCamera_m81F8E530644DBCE5C8BF9CB4D66AF83B828EC8C1 (void);
extern void VFXManager_PrepareCamera_mDF07F46C4ED37B8EFF785C9AE55BEFFA9C133923 (void);
extern void VFXManager_ProcessCameraCommand_m58F378DDAE272DC813C6E8D7C8E4926D709A0BA1 (void);
extern void VFXManager_Internal_ProcessCameraCommand_mD1E50A787623A201B74EA5C6B68B02A35CA1DFC9 (void);
extern void VFXManager__cctor_mAD02D20E3B33A9A3C291A9C79D3B243AA54CADC0 (void);
extern void VFXManager_PrepareCamera_Injected_mECDFA74216D498F70504FB519940CA329C057B63 (void);
extern void VFXManager_Internal_ProcessCameraCommand_Injected_m6967D890450FC77F1D2E9EF55421A1A515268445 (void);
extern void VFXSpawnerCallbacks__ctor_mF8C2AB380D2D25B5309E22DA5A0F48ECE40BAAEB (void);
extern void VFXSpawnerState__ctor_mDF51A64C76B610ED8B1BF46C3CB6F8224B246392 (void);
extern void VFXSpawnerState_CreateSpawnerStateWrapper_mDB5B5B796BD1A60A20A1EDC2F959490804D0E0EA (void);
extern void VFXSpawnerState_PrepareWrapper_mB72BD83CBCF670DCF0FB464BBEAFBB2E6D285D5F (void);
extern void VFXSpawnerState_SetWrapValue_m84E0796288A032B26ADCEF35AE94F5CC95A3FAB5 (void);
extern void VFXSpawnerState_Release_m59F57A6E6C6C8FCA65E61C9D856847493997DBF8 (void);
extern void VFXSpawnerState_Finalize_mAE69A225C7EC591B6B1DE3B01560F5639C6A7839 (void);
extern void VFXSpawnerState_Dispose_m7098E99835290AAF0DFFE32EE2D901CA455975A4 (void);
extern void VFXSpawnerState_Internal_Destroy_m09736C48CDFFFECFDCFEBF16A32E1C98695680AE (void);
extern void VFXSpawnerState_get_playing_mF93076BD39062877B5AE809B4B923476F639DE1A (void);
extern void VFXSpawnerState_set_playing_m2CB517A6452705EFA615F5312189E6A13683150E (void);
extern void VFXSpawnerState_get_loopState_m7772B23A36F8AF90A30844BF8E1072DA37645AB6 (void);
extern void VFXSpawnerState_set_loopState_m60BFACFC86635E9F3C961443E1158E86DEE059B1 (void);
extern void VFXSpawnerState_get_spawnCount_m7C33E71B76E4EF9D137E6CA153972798A29E6C04 (void);
extern void VFXSpawnerState_set_spawnCount_m1F087947EA21F5C4881F505158F671C27E16025E (void);
extern void VFXSpawnerState_get_deltaTime_mC442B9EFD84D8E4D14DEF6F270BC19FCB545D6FC (void);
extern void VFXSpawnerState_get_totalTime_m0AE0B61EF265C63786C60B27B26A8F63983EE2EC (void);
extern void VFXSpawnerState_set_totalTime_m6D299C81029427816C68E0C498C500E84F96C4F2 (void);
extern void VFXSpawnerState_Internal_GetVFXEventAttribute_m3C67CBBF3EB78ABD7766795012FD1FC1483A0B77 (void);
extern void VFXSpawnerState_get_vfxEventAttribute_m10DF79A8C2F210641F761F72C8CD3FB18D4F419B (void);
extern void VisualEffectObject__ctor_m2D49AA821FEFA36E48409C03455DE173BCEB3837 (void);
extern void VisualEffectAsset__ctor_m84C59D5BB45858C0EA43C5C2E9A9DAA644CA3680 (void);
extern void VisualEffectAsset__cctor_mFA21B1B8EA5A9FA1BDB43654B4F0CB67E923931A (void);
extern void VFXOutputEventArgs_get_nameId_m0B162A66638231784DBB03323377A33C144616CF (void);
extern void VFXOutputEventArgs_get_eventAttribute_m38C5944E1F1FC4355BB632849C6C7D56AFA4C6B0 (void);
extern void VFXOutputEventArgs__ctor_m4A6030F4BF7E27F5F682E0FC4211F53DF866ED56 (void);
extern void VisualEffect_set_pause_mB154E19F4A1D606382C874AB0052CC9574EA5C78 (void);
extern void VisualEffect_get_startSeed_mC05EAFF5C89A8130105C60CECBB4BA3DD8C074E0 (void);
extern void VisualEffect_set_startSeed_m619D72F4C305CD63DDB445F89A1F7CEA42833F33 (void);
extern void VisualEffect_get_resetSeedOnPlay_mD2A2271EB89165038CE83B59019EADFC0BEA637B (void);
extern void VisualEffect_set_resetSeedOnPlay_mDCAA0B2A10AAA44E65745A9644D0F09FD322CB7F (void);
extern void VisualEffect_get_visualEffectAsset_m301FCE98B138CAB2E16B46CE365538ECA8AF5F00 (void);
extern void VisualEffect_CreateVFXEventAttribute_mC4611FC064DF24028BA3E60D3D450B95541BEB6E (void);
extern void VisualEffect_CheckValidVFXEventAttribute_m94E56940DEE1E1ABA5AFBBC9F89CD2227078C38C (void);
extern void VisualEffect_SendEventFromScript_mF66550CD5B358474F957125EC4177986B661DAA0 (void);
extern void VisualEffect_SendEvent_m4796EBECD8CAA5FFD03EACA27049B34F4965A74F (void);
extern void VisualEffect_SendEvent_m8FE48624C9E9D21E654CF35D168E7CAE3E884D9C (void);
extern void VisualEffect_SendEvent_m070D5CDE7555F6B203E6A8DCF879BC3403DCA15B (void);
extern void VisualEffect_Reinit_m878F28F31EDA0C1364B31DC990A0B05BB7F61AF1 (void);
extern void VisualEffect_HasBool_m880EB204D4723EB3C06B2E34A52486E678A752AA (void);
extern void VisualEffect_HasInt_m1F5851C9BEBD2A887EFF69694465401BDCD94789 (void);
extern void VisualEffect_HasUInt_mE29D528AF718CB8C1C67491540ADC43B34D0C03E (void);
extern void VisualEffect_HasFloat_m9F9969BD960A358735387BB53762466E14D289C6 (void);
extern void VisualEffect_HasVector3_mADAFDFDB7950BEF5AFFB2CBD59E620B79F5431C6 (void);
extern void VisualEffect_HasVector4_m131E022D4B376C67DA7A0CAC662BA6DE91D3E40E (void);
extern void VisualEffect_HasTexture_m9EE102518FBB3B2ACB6F1DED92638F7FD95AE2FE (void);
extern void VisualEffect_SetBool_m835964F6BF4751B1B517E03F3676552164E7927D (void);
extern void VisualEffect_SetInt_m7F486C2E01DCAACE8D0A43661C8750822CEBD720 (void);
extern void VisualEffect_SetUInt_m1A351AACE75FDAE82CF0C777F66D14D629B57B31 (void);
extern void VisualEffect_SetFloat_m0AF9AD3C2CB14F8004B9F3BE3ECCCA74E3315BA2 (void);
extern void VisualEffect_SetVector2_m4DDC038B4B9FC98288DE4F864FF31F80D0B568A6 (void);
extern void VisualEffect_SetVector3_m9DEA0241E8C6768309AEDC1319F3BD84EAB8D246 (void);
extern void VisualEffect_SetVector4_m94086CD93C6C371BD5C3D8A3E815C034DD02E4D1 (void);
extern void VisualEffect_SetTexture_m96338B6E07291B861827B204CAE5654386D91E2C (void);
extern void VisualEffect_GetFloat_m7DE09883BFE78C2AC343FE6EE3A7C84704121F2D (void);
extern void VisualEffect_HasUInt_mECAE541A87CE6A7470ACA49C53FF5D5E11DF1047 (void);
extern void VisualEffect_HasFloat_m96F1E6C9BB23E20D1BB730D3356E3E794D410B25 (void);
extern void VisualEffect_HasVector4_m9BD809A0E57D0083CC65C3F8240FF3A55F866811 (void);
extern void VisualEffect_HasTexture_mC57802FAF381382F66E367BC2CAA41E6BA335D2D (void);
extern void VisualEffect_SetInt_m7FB97FFD6FB9808763E644D9E8CE2F5174ED8079 (void);
extern void VisualEffect_SetUInt_m73FB569FA8DABE4CF67EB6D4E2D21A539CE43EAC (void);
extern void VisualEffect_SetFloat_mB59095AC406A373BC587551B5A4781719F608F51 (void);
extern void VisualEffect_SetVector2_mF54B2151B9D4C3F34FC572FADFD23A12ED702E02 (void);
extern void VisualEffect_SetVector3_mD65A5136EEA283522C089904956C42B419382AE1 (void);
extern void VisualEffect_SetVector4_m2689F7AA23636CA94447027AFE213A03F51B261C (void);
extern void VisualEffect_SetTexture_m500CB940B0427A4015A3B9E0FDE98F4F30E415BD (void);
extern void VisualEffect_SetBool_m2AE92C97A9290E260655D557C4E68B2947E72964 (void);
extern void VisualEffect_get_time_mA25FD6698B47E3B62F587CD885D4B64D857C722E (void);
extern void VisualEffect_Simulate_mC347313CB7CBC9439F65F88FC490E29FFE7B2E74 (void);
extern void VisualEffect_InvokeGetCachedEventAttributeForOutputEvent_Internal_mB2B1867606653A162B31F890AFFAFF2D4569904E (void);
extern void VisualEffect_InvokeOutputEventReceived_Internal_m8F6FCF09BC8F3774FE1DD70A296182909A9CBCA7 (void);
extern void VisualEffect__ctor_mBF32DAA70D29991F80F29881C60A8D37356FD01B (void);
extern void VisualEffect_SetVector2_Injected_m6801EA3951578066EA401E4069EFC45E7EAC9B6B (void);
extern void VisualEffect_SetVector3_Injected_m70BB92E49B2DE1EAF30CD49903D973F58D22031C (void);
extern void VisualEffect_SetVector4_Injected_m798157124FFD71E650FB57F24F8BD2E3D45EAF5E (void);
static Il2CppMethodPointer s_methodPointers[126] = 
{
	VFXEventAttribute__ctor_m08E26E4F79DA1062FC43501FDFE00B9EF3ED3AE1,
	VFXEventAttribute__ctor_mF978CE9C956D15F2E4CE56B0071FB4928D90AE3C,
	VFXEventAttribute_CreateEventAttributeWrapper_m8875BE7EF5B016C001E79C5CC228969A98EA846A,
	VFXEventAttribute_SetWrapValue_mB74CE4A4E6203AA7E95F9446AC2CC043E21CB757,
	VFXEventAttribute_Internal_Create_m2ABF5098E27A4F13603C974ECE4819F6257FA157,
	VFXEventAttribute_Internal_InstanciateVFXEventAttribute_m67D4914621592C8A2F54808E0F77F15831A63FE6,
	VFXEventAttribute_Internal_InitFromAsset_mFA13F75CCECE6D2DAD270E37126F211724D5EFD2,
	VFXEventAttribute_get_vfxAsset_m5A8B68B6EA494BA64ABAFA3915EB806B29FC2A68,
	VFXEventAttribute_Release_m959FB9B7EEC401D763A16AC1188F18B7F5B2D8F2,
	VFXEventAttribute_Finalize_m7922B5B3EF84DB5BE447C5A6F4F6B5F00B3E4AA4,
	VFXEventAttribute_Dispose_m172293D888316328F19F4C7E03D50CD178E0EB36,
	VFXEventAttribute_Internal_Destroy_m2D0838414E77C04FBD44CEB8B7F825C0C4EBC30E,
	VFXEventAttribute_HasBool_m289E4E4D6D93F21BF4BC11E493D774E8DF948708,
	VFXEventAttribute_HasInt_m9DD77A5D17F290859F15C5306847345DB434ED2F,
	VFXEventAttribute_HasUint_mE792032D519321C2C85E19DBB84338B8F138A7F4,
	VFXEventAttribute_HasFloat_mF3A88D85B0A13A73F1735E6E91F1ADD33B6F598B,
	VFXEventAttribute_HasVector2_m6ECE42A96943962A41866E0FC9DA4CF735ADCA00,
	VFXEventAttribute_HasVector3_m22043F5E468E1AD708AF9671163457F9E15683BD,
	VFXEventAttribute_HasVector4_mDCC54A83398009A616F78DB228DFEB04DE03B706,
	VFXEventAttribute_SetBool_mBD3076A39A3A230C00B72EFF2A7F91CB4B548238,
	VFXEventAttribute_SetInt_mC3236E37E1E64CAD49F30436A90972C4F23B901A,
	VFXEventAttribute_SetUint_mEB03AF9A2CB1AC5E8CB49166B29E982C39084DB8,
	VFXEventAttribute_SetFloat_m6A47F4AF467EEAA5637887DF9E1BCE54A99DF309,
	VFXEventAttribute_SetVector2_mC626E39E61B5DD4C00DB1DA787DEAA131DFD3DAD,
	VFXEventAttribute_SetVector3_mDC5A96D198F7FE6C422D68AF09EA319A25E90154,
	VFXEventAttribute_SetVector4_m600C101B1D5D647ABA6DAC0AF4AE0DF94EC040D3,
	VFXEventAttribute_SetVector2_Injected_m66D45C99B65CA21F6E3D6E8B586DCF29654C1585,
	VFXEventAttribute_SetVector3_Injected_m6DB077BC2E3EBCF6B82E63D360CBB26A4806A8EC,
	VFXEventAttribute_SetVector4_Injected_m9EE37FB6F03DAC9DE9B07659F641B1875558C4B5,
	VFXExpressionValues__ctor_mB5923888F1BBEEE55955E24514A69629D932DDD0,
	VFXExpressionValues_CreateExpressionValuesWrapper_m94D1AD2FD2FF7D005B0AEF2D22333D321631AACE,
	VFXExpressionValues_GetBool_m7B4F46E1448103DA73BD6F7ADE9A5B105654B50B,
	VFXExpressionValues_GetInt_mA3669A82A8C5A438E0FBF3FC91FA0328E99F0A76,
	VFXExpressionValues_GetUInt_mDDA11571F6AD189F564820CA73C9FB7EE89FDE98,
	VFXExpressionValues_GetFloat_mC5C19CEE3CB1E5B7626F82C9EBE103CB3C266910,
	VFXExpressionValues_GetVector3_m88F0FA30F13980FB30BFCD224A41469E46870225,
	VFXExpressionValues_GetVector3_Injected_mF4A6CA5723359E4167EA4059420CC903F240F87C,
	VFXManager_get_runtimeResources_m8849FA094016FA370407278A80A04A4E90D50F30,
	VFXManager_get_fixedTimeStep_m675FCEA0D1F90BFE9683D2DA56AED1A8C0FD1828,
	VFXManager_get_maxDeltaTime_m1F1D08FB77264FB5CAD06C8D517362F9A7762D86,
	VFXManager_get_maxScrubTime_m92732C852F164459EAE6A6E23932A5A04C5278FB,
	VFXManager_PrepareCamera_m81F8E530644DBCE5C8BF9CB4D66AF83B828EC8C1,
	VFXManager_PrepareCamera_mDF07F46C4ED37B8EFF785C9AE55BEFFA9C133923,
	VFXManager_ProcessCameraCommand_m58F378DDAE272DC813C6E8D7C8E4926D709A0BA1,
	VFXManager_Internal_ProcessCameraCommand_mD1E50A787623A201B74EA5C6B68B02A35CA1DFC9,
	VFXManager__cctor_mAD02D20E3B33A9A3C291A9C79D3B243AA54CADC0,
	VFXManager_PrepareCamera_Injected_mECDFA74216D498F70504FB519940CA329C057B63,
	VFXManager_Internal_ProcessCameraCommand_Injected_m6967D890450FC77F1D2E9EF55421A1A515268445,
	NULL,
	NULL,
	NULL,
	VFXSpawnerCallbacks__ctor_mF8C2AB380D2D25B5309E22DA5A0F48ECE40BAAEB,
	VFXSpawnerState__ctor_mDF51A64C76B610ED8B1BF46C3CB6F8224B246392,
	VFXSpawnerState_CreateSpawnerStateWrapper_mDB5B5B796BD1A60A20A1EDC2F959490804D0E0EA,
	VFXSpawnerState_PrepareWrapper_mB72BD83CBCF670DCF0FB464BBEAFBB2E6D285D5F,
	VFXSpawnerState_SetWrapValue_m84E0796288A032B26ADCEF35AE94F5CC95A3FAB5,
	VFXSpawnerState_Release_m59F57A6E6C6C8FCA65E61C9D856847493997DBF8,
	VFXSpawnerState_Finalize_mAE69A225C7EC591B6B1DE3B01560F5639C6A7839,
	VFXSpawnerState_Dispose_m7098E99835290AAF0DFFE32EE2D901CA455975A4,
	VFXSpawnerState_Internal_Destroy_m09736C48CDFFFECFDCFEBF16A32E1C98695680AE,
	VFXSpawnerState_get_playing_mF93076BD39062877B5AE809B4B923476F639DE1A,
	VFXSpawnerState_set_playing_m2CB517A6452705EFA615F5312189E6A13683150E,
	VFXSpawnerState_get_loopState_m7772B23A36F8AF90A30844BF8E1072DA37645AB6,
	VFXSpawnerState_set_loopState_m60BFACFC86635E9F3C961443E1158E86DEE059B1,
	VFXSpawnerState_get_spawnCount_m7C33E71B76E4EF9D137E6CA153972798A29E6C04,
	VFXSpawnerState_set_spawnCount_m1F087947EA21F5C4881F505158F671C27E16025E,
	VFXSpawnerState_get_deltaTime_mC442B9EFD84D8E4D14DEF6F270BC19FCB545D6FC,
	VFXSpawnerState_get_totalTime_m0AE0B61EF265C63786C60B27B26A8F63983EE2EC,
	VFXSpawnerState_set_totalTime_m6D299C81029427816C68E0C498C500E84F96C4F2,
	VFXSpawnerState_Internal_GetVFXEventAttribute_m3C67CBBF3EB78ABD7766795012FD1FC1483A0B77,
	VFXSpawnerState_get_vfxEventAttribute_m10DF79A8C2F210641F761F72C8CD3FB18D4F419B,
	VisualEffectObject__ctor_m2D49AA821FEFA36E48409C03455DE173BCEB3837,
	VisualEffectAsset__ctor_m84C59D5BB45858C0EA43C5C2E9A9DAA644CA3680,
	VisualEffectAsset__cctor_mFA21B1B8EA5A9FA1BDB43654B4F0CB67E923931A,
	VFXOutputEventArgs_get_nameId_m0B162A66638231784DBB03323377A33C144616CF,
	VFXOutputEventArgs_get_eventAttribute_m38C5944E1F1FC4355BB632849C6C7D56AFA4C6B0,
	VFXOutputEventArgs__ctor_m4A6030F4BF7E27F5F682E0FC4211F53DF866ED56,
	VisualEffect_set_pause_mB154E19F4A1D606382C874AB0052CC9574EA5C78,
	VisualEffect_get_startSeed_mC05EAFF5C89A8130105C60CECBB4BA3DD8C074E0,
	VisualEffect_set_startSeed_m619D72F4C305CD63DDB445F89A1F7CEA42833F33,
	VisualEffect_get_resetSeedOnPlay_mD2A2271EB89165038CE83B59019EADFC0BEA637B,
	VisualEffect_set_resetSeedOnPlay_mDCAA0B2A10AAA44E65745A9644D0F09FD322CB7F,
	VisualEffect_get_visualEffectAsset_m301FCE98B138CAB2E16B46CE365538ECA8AF5F00,
	VisualEffect_CreateVFXEventAttribute_mC4611FC064DF24028BA3E60D3D450B95541BEB6E,
	VisualEffect_CheckValidVFXEventAttribute_m94E56940DEE1E1ABA5AFBBC9F89CD2227078C38C,
	VisualEffect_SendEventFromScript_mF66550CD5B358474F957125EC4177986B661DAA0,
	VisualEffect_SendEvent_m4796EBECD8CAA5FFD03EACA27049B34F4965A74F,
	VisualEffect_SendEvent_m8FE48624C9E9D21E654CF35D168E7CAE3E884D9C,
	VisualEffect_SendEvent_m070D5CDE7555F6B203E6A8DCF879BC3403DCA15B,
	VisualEffect_Reinit_m878F28F31EDA0C1364B31DC990A0B05BB7F61AF1,
	VisualEffect_HasBool_m880EB204D4723EB3C06B2E34A52486E678A752AA,
	VisualEffect_HasInt_m1F5851C9BEBD2A887EFF69694465401BDCD94789,
	VisualEffect_HasUInt_mE29D528AF718CB8C1C67491540ADC43B34D0C03E,
	VisualEffect_HasFloat_m9F9969BD960A358735387BB53762466E14D289C6,
	VisualEffect_HasVector3_mADAFDFDB7950BEF5AFFB2CBD59E620B79F5431C6,
	VisualEffect_HasVector4_m131E022D4B376C67DA7A0CAC662BA6DE91D3E40E,
	VisualEffect_HasTexture_m9EE102518FBB3B2ACB6F1DED92638F7FD95AE2FE,
	VisualEffect_SetBool_m835964F6BF4751B1B517E03F3676552164E7927D,
	VisualEffect_SetInt_m7F486C2E01DCAACE8D0A43661C8750822CEBD720,
	VisualEffect_SetUInt_m1A351AACE75FDAE82CF0C777F66D14D629B57B31,
	VisualEffect_SetFloat_m0AF9AD3C2CB14F8004B9F3BE3ECCCA74E3315BA2,
	VisualEffect_SetVector2_m4DDC038B4B9FC98288DE4F864FF31F80D0B568A6,
	VisualEffect_SetVector3_m9DEA0241E8C6768309AEDC1319F3BD84EAB8D246,
	VisualEffect_SetVector4_m94086CD93C6C371BD5C3D8A3E815C034DD02E4D1,
	VisualEffect_SetTexture_m96338B6E07291B861827B204CAE5654386D91E2C,
	VisualEffect_GetFloat_m7DE09883BFE78C2AC343FE6EE3A7C84704121F2D,
	VisualEffect_HasUInt_mECAE541A87CE6A7470ACA49C53FF5D5E11DF1047,
	VisualEffect_HasFloat_m96F1E6C9BB23E20D1BB730D3356E3E794D410B25,
	VisualEffect_HasVector4_m9BD809A0E57D0083CC65C3F8240FF3A55F866811,
	VisualEffect_HasTexture_mC57802FAF381382F66E367BC2CAA41E6BA335D2D,
	VisualEffect_SetInt_m7FB97FFD6FB9808763E644D9E8CE2F5174ED8079,
	VisualEffect_SetUInt_m73FB569FA8DABE4CF67EB6D4E2D21A539CE43EAC,
	VisualEffect_SetFloat_mB59095AC406A373BC587551B5A4781719F608F51,
	VisualEffect_SetVector2_mF54B2151B9D4C3F34FC572FADFD23A12ED702E02,
	VisualEffect_SetVector3_mD65A5136EEA283522C089904956C42B419382AE1,
	VisualEffect_SetVector4_m2689F7AA23636CA94447027AFE213A03F51B261C,
	VisualEffect_SetTexture_m500CB940B0427A4015A3B9E0FDE98F4F30E415BD,
	VisualEffect_SetBool_m2AE92C97A9290E260655D557C4E68B2947E72964,
	VisualEffect_get_time_mA25FD6698B47E3B62F587CD885D4B64D857C722E,
	VisualEffect_Simulate_mC347313CB7CBC9439F65F88FC490E29FFE7B2E74,
	VisualEffect_InvokeGetCachedEventAttributeForOutputEvent_Internal_mB2B1867606653A162B31F890AFFAFF2D4569904E,
	VisualEffect_InvokeOutputEventReceived_Internal_m8F6FCF09BC8F3774FE1DD70A296182909A9CBCA7,
	VisualEffect__ctor_mBF32DAA70D29991F80F29881C60A8D37356FD01B,
	VisualEffect_SetVector2_Injected_m6801EA3951578066EA401E4069EFC45E7EAC9B6B,
	VisualEffect_SetVector3_Injected_m70BB92E49B2DE1EAF30CD49903D973F58D22031C,
	VisualEffect_SetVector4_Injected_m798157124FFD71E650FB57F24F8BD2E3D45EAF5E,
};
extern void VFXOutputEventArgs_get_nameId_m0B162A66638231784DBB03323377A33C144616CF_AdjustorThunk (void);
extern void VFXOutputEventArgs_get_eventAttribute_m38C5944E1F1FC4355BB632849C6C7D56AFA4C6B0_AdjustorThunk (void);
extern void VFXOutputEventArgs__ctor_m4A6030F4BF7E27F5F682E0FC4211F53DF866ED56_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[3] = 
{
	{ 0x0600004B, VFXOutputEventArgs_get_nameId_m0B162A66638231784DBB03323377A33C144616CF_AdjustorThunk },
	{ 0x0600004C, VFXOutputEventArgs_get_eventAttribute_m38C5944E1F1FC4355BB632849C6C7D56AFA4C6B0_AdjustorThunk },
	{ 0x0600004D, VFXOutputEventArgs__ctor_m4A6030F4BF7E27F5F682E0FC4211F53DF866ED56_AdjustorThunk },
};
static const int32_t s_InvokerIndices[126] = 
{
	2684,
	13298,
	21274,
	10633,
	21265,
	20515,
	10682,
	13052,
	13298,
	13298,
	13298,
	20842,
	7685,
	7685,
	7685,
	7685,
	7685,
	7685,
	7685,
	5184,
	5266,
	5423,
	5375,
	5428,
	5433,
	5438,
	5167,
	5167,
	5167,
	13298,
	20513,
	7685,
	8801,
	9562,
	9456,
	9598,
	5167,
	21274,
	21334,
	21334,
	21334,
	20847,
	18827,
	15677,
	15678,
	21355,
	18793,
	15653,
	0,
	0,
	0,
	13298,
	5630,
	21274,
	13298,
	5632,
	13298,
	13298,
	13298,
	20842,
	12815,
	10442,
	12996,
	10629,
	13195,
	10823,
	13195,
	13195,
	10823,
	13052,
	13052,
	13298,
	13298,
	21355,
	12996,
	13052,
	5309,
	10442,
	13261,
	10891,
	12815,
	10442,
	13052,
	13052,
	10682,
	5309,
	5309,
	5688,
	10629,
	10442,
	7685,
	7685,
	7685,
	7685,
	7685,
	7685,
	7685,
	5184,
	5266,
	5423,
	5375,
	5428,
	5433,
	5438,
	5309,
	9456,
	7736,
	7736,
	7736,
	7736,
	5681,
	5712,
	5703,
	5715,
	5716,
	5717,
	5688,
	5666,
	13195,
	5786,
	20515,
	18807,
	13298,
	5167,
	5167,
	5167,
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_VFXModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_VFXModule_CodeGenModule = 
{
	"UnityEngine.VFXModule.dll",
	126,
	s_methodPointers,
	3,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};

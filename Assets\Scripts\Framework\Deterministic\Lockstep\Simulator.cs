using System.Collections.Generic;
using RGame.Message;

namespace RGame.Framework
{

    /// <summary>
    /// 上行: 表现层IInputService->发出帧命令->Simulator缓存帧命令->INetService网络同步帧命令->服务器
    /// 下行: 服务器->INetService接受帧命令->Simulator PushFrame->驱动帧命令->逻辑层IFrameService
    /// </summary>
    public class Simulator
    {
        public byte LocalActorId
        {
            get; 
            private set;
        }

        public bool IsRunning
        {
            get; 
            private set;
        }

        private INetService _netService = null;
        private World _world = new World();
        private FrameManager _frameManager = new FrameManager();
        private readonly List<ICmd> _localActorCommandBuffer = new List<ICmd>();

        private ObjectPoolManager _objectPoolManager = null;

        public void Init(ServiceContainer serviceContainer, byte localActorId)
        {
            RegisterMsgHandler();

            LocalActorId = localActorId;
            _objectPoolManager = ObjectPoolManager.GetInstance();
            _netService = serviceContainer.GetUnderlyingService<INetService>();

            _frameManager.Init();
            _world.Init(serviceContainer);
        }


        public void Reset()
        {
            UnRegisterMsgHandler();

            IsRunning = false;

            _world.Reset();
            _frameManager.Reset();
        }

        public void StartSimulator()
        {
            IsRunning = true;
        }

        public void EndSimulator()
        {
            IsRunning = false;
        }

        public void Update(float deltaTime)
        {
            if (!IsRunning)
            {
                return;
            }

            SendPlayerInputs();
            OnPlayModeUpdate();
        }

        private void SendPlayerInputs()
        {
            int commandCount = _localActorCommandBuffer.Count;
            if (commandCount <= 0)
            {
                return;
            }

            _netService.SendUserInput(LocalActorId, _localActorCommandBuffer);

            for (int i = 0; i < commandCount; ++i)
            {
                if (_localActorCommandBuffer[i] is ICommand command)
                {
                    command.Reset();
                    _objectPoolManager.Return(command);
                }
            }
            _localActorCommandBuffer.Clear();
        }

        private void OnPlayModeUpdate()
        {
            //TODO::LockstepUtil.ClientLogicFrameTick大于_frameManager.RecivedFrameTick一定值，重连
            while (_world.CurTick < _frameManager.LastRecivedFrameTick)
            {
                var curTick = _world.CurTick + 1;
                Frame srvFrame = _frameManager.GetServerFrame(curTick);
                if (srvFrame == null)
                {
                    return;
                }

                RunStep(srvFrame);
            }
        }

        private void RunStep(Frame frame)
        {
            _world.FrameBegin();
            ProcessInputQueue(frame);
            _world.Step();
            _world.FrameEnd();
        }

        private void ProcessInputQueue(Frame frame)
        {
            var inputs = frame.Inputs;

            for (int i = 0, lengthI = inputs.Count; i < lengthI; ++i)
            {
                PlayerInput input = inputs[i];
                List<ICmd> commands = input.Commands;
                if (0 == commands.Count)
                {
                    continue;
                }

                byte inputActorID = input.ActorId;
                _netService.ProcessUserInput(inputActorID, commands);
                // for (int j = 0, lengthJ = commands.Count; j < lengthJ; ++j)
                // {
                //     _netService.ProcessUserInput(inputActorID, commands[j]);
                // }
            }
        }

        private void HandleGameLogicMsg_SendCommand(RGameMessage msg)
        {
            CommandMsgBody commandMsgBody = (CommandMsgBody)msg.MessgaeBody;
            if (LocalActorId == commandMsgBody.ActorId)
            {
                _localActorCommandBuffer.Add(commandMsgBody.Command);
            }
        }

        private void HandleGameLogicMsg_SendHashCode(RGameMessage msg)
        {
            HashCodeMsgBody hashCodeMsgBody = (HashCodeMsgBody)msg.MessgaeBody;
            _netService.SendHashCode(hashCodeMsgBody.Tick, hashCodeMsgBody.HashCode);
        }

        public void RegisterMsgHandler()
        {
            MessageDispatcher<int, RGameMessage>.RegisterHandler(MessageDispatcherID.GameLogicMsg, MessageID.GameLogicMsg_Lockstep_SendCommand, HandleGameLogicMsg_SendCommand);
            MessageDispatcher<int, RGameMessage>.RegisterHandler(MessageDispatcherID.GameLogicMsg, MessageID.GameLogicMsg_Lockstep_SendHashCode, HandleGameLogicMsg_SendHashCode);
        }

        public void UnRegisterMsgHandler()
        {
            MessageDispatcher<int, RGameMessage>.UnregisterHandler(MessageDispatcherID.GameLogicMsg, MessageID.GameLogicMsg_Lockstep_SendCommand, HandleGameLogicMsg_SendCommand);
            MessageDispatcher<int, RGameMessage>.UnregisterHandler(MessageDispatcherID.GameLogicMsg, MessageID.GameLogicMsg_Lockstep_SendHashCode, HandleGameLogicMsg_SendHashCode);
        }

    }
}

using UnityEngine;
using UnityEngine.UI;

[RequireComponent(typeof(CanvasRenderer))]
[AddComponentMenu("UI/Bitmap Text")]
public class UIBitmapText : MaskableGraphic, ILayoutElement
{
    [Header("Text")]
    [Serial<PERSON><PERSON>ield, TextArea(3, 10)] private string m_Text = "Sample Text";
    [SerializeField] private BitmapFont m_Font;
    [SerializeField] private BitmapTextSettings m_Settings = new BitmapTextSettings();

    [Header("Layout")]
    [SerializeField] private Vector2 m_Pivot = new Vector2(0.5f, 0.5f);

    // Cached text info
    private BitmapTextInfo m_TextInfo;
    private bool m_TextDirty = true;

    // ILayoutElement properties
    private float m_PreferredWidth = -1f;
    private float m_PreferredHeight = -1f;

    protected UIBitmapText()
    {
        useLegacyMeshGeneration = false;
    }

    public string text
    {
        get => m_Text;
        set
        {
            if (m_Text != value)
            {
                m_Text = value;
                SetTextDirty();
            }
        }
    }

    public BitmapFont font
    {
        get => m_Font;
        set
        {
            if (m_Font != value)
            {
                m_Font = value;
                SetTextDirty();
                SetMaterialDirty();
            }
        }
    }

    public BitmapTextSettings settings
    {
        get => m_Settings;
        set
        {
            if (m_Settings != value)
            {
                m_Settings = value ?? new BitmapTextSettings();
                SetTextDirty();
            }
        }
    }

    public Vector2 pivot
    {
        get => m_Pivot;
        set
        {
            if (m_Pivot != value)
            {
                m_Pivot = value;
                SetVerticesDirty();
            }
        }
    }

    public override Texture mainTexture
    {
        get
        {
            if (m_Font != null && m_Font.atlas != null)
                return m_Font.atlas;
            return s_WhiteTexture;
        }
    }

    public override Material material
    {
        get
        {
            if (m_Font != null && m_Font.material != null)
                return m_Font.material;
            return defaultMaterial;
        }
        set
        {
            base.material = value;
        }
    }

    protected override void OnEnable()
    {
        base.OnEnable();
        SetTextDirty();
    }

    protected override void OnRectTransformDimensionsChange()
    {
        base.OnRectTransformDimensionsChange();
        SetTextDirty();
    }

#if UNITY_EDITOR
    protected override void OnValidate()
    {
        base.OnValidate();
        SetTextDirty();
    }
#endif

    private void SetTextDirty()
    {
        if (!IsActive())
            return;

        m_TextDirty = true;
        m_PreferredWidth = -1f;
        m_PreferredHeight = -1f;
        SetVerticesDirty();
        SetLayoutDirty();
    }

    private void UpdateTextInfo()
    {
        if (!m_TextDirty || m_Font == null)
            return;

        // Update settings with rect transform size if needed
        var rectSize = rectTransform.rect.size;
        if (m_Settings.maxSize.x <= 0) m_Settings.maxSize = new Vector2(rectSize.x, m_Settings.maxSize.y);
        if (m_Settings.maxSize.y <= 0) m_Settings.maxSize = new Vector2(m_Settings.maxSize.x, rectSize.y);

        m_TextInfo = BitmapTextGenerator.GenerateText(m_Text, m_Font, m_Settings, color);
        m_TextDirty = false;
    }

    protected override void OnPopulateMesh(VertexHelper toFill)
    {
        UpdateTextInfo();
        BitmapTextGenerator.PopulateMesh(toFill, m_TextInfo, m_Pivot);
    }

    // ILayoutElement implementation
    public virtual void CalculateLayoutInputHorizontal()
    {
        if (m_Font == null)
        {
            m_PreferredWidth = 0f;
            return;
        }

        var preferredSize = BitmapTextGenerator.GetPreferredSize(m_Text, m_Font, m_Settings);
        m_PreferredWidth = preferredSize.x;
    }

    public virtual void CalculateLayoutInputVertical()
    {
        if (m_Font == null)
        {
            m_PreferredHeight = 0f;
            return;
        }

        var preferredSize = BitmapTextGenerator.GetPreferredSize(m_Text, m_Font, m_Settings);
        m_PreferredHeight = preferredSize.y;
    }

    public virtual float minWidth => 0f;
    public virtual float preferredWidth => m_PreferredWidth >= 0 ? m_PreferredWidth : 0f;
    public virtual float flexibleWidth => -1f;
    public virtual float minHeight => 0f;
    public virtual float preferredHeight => m_PreferredHeight >= 0 ? m_PreferredHeight : 0f;
    public virtual float flexibleHeight => -1f;
    public virtual int layoutPriority => 0;
}

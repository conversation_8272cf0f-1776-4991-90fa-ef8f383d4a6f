using System.Collections.Generic;
using UnityEngine;

[CreateAssetMenu(fileName = "New Bitmap Font", menuName = "UI/Bitmap Font")]
public class BitmapFont : ScriptableObject
{
    [Header("Font Settings")]
    [SerializeField] private string m_FontName = "Bitmap Font";
    [SerializeField] private float m_LineHeight = 32f;
    [SerializeField] private float m_BaseLine = 24f;
    [SerializeField] private float m_SpaceWidth = 8f;

    [Header("Atlas")]
    [SerializeField] private Texture2D m_Atlas;
    [SerializeField] private Material m_Material;

    [Header("Glyphs")]
    [SerializeField] private List<BitmapGlyph> m_Glyphs = new List<BitmapGlyph>();

    // Runtime lookup cache
    private Dictionary<string, BitmapGlyph> m_GlyphLookup;
    private Dictionary<char, BitmapGlyph> m_CharLookup;

    public string fontName => m_FontName;
    public float lineHeight => m_LineHeight;
    public float baseLine => m_BaseLine;
    public float spaceWidth => m_SpaceWidth;
    public Texture2D atlas => m_Atlas;
    public Material material => m_Material;
    public List<BitmapGlyph> glyphs => m_Glyphs;

    /// <summary>
    /// Get glyph by string identifier (sprite name)
    /// </summary>
    public BitmapGlyph GetGlyph(string glyphName)
    {
        if (m_GlyphLookup == null)
            BuildLookupTables();

        m_GlyphLookup.TryGetValue(glyphName, out BitmapGlyph glyph);
        return glyph;
    }

    /// <summary>
    /// Get glyph by character
    /// </summary>
    public BitmapGlyph GetGlyph(char character)
    {
        if (m_CharLookup == null)
            BuildLookupTables();

        m_CharLookup.TryGetValue(character, out BitmapGlyph glyph);
        return glyph;
    }

    /// <summary>
    /// Check if font contains glyph for character
    /// </summary>
    public bool HasGlyph(char character)
    {
        if (m_CharLookup == null)
            BuildLookupTables();

        return m_CharLookup.ContainsKey(character);
    }

    /// <summary>
    /// Check if font contains glyph by name
    /// </summary>
    public bool HasGlyph(string glyphName)
    {
        if (m_GlyphLookup == null)
            BuildLookupTables();

        return m_GlyphLookup.ContainsKey(glyphName);
    }

    /// <summary>
    /// Add or update a glyph
    /// </summary>
    public void SetGlyph(BitmapGlyph glyph)
    {
        if (glyph == null) return;

        // Find existing glyph with same name or character
        int existingIndex = -1;
        for (int i = 0; i < m_Glyphs.Count; i++)
        {
            if (m_Glyphs[i].glyphName == glyph.glyphName ||
                (glyph.character != '\0' && m_Glyphs[i].character == glyph.character))
            {
                existingIndex = i;
                break;
            }
        }

        if (existingIndex >= 0)
        {
            m_Glyphs[existingIndex] = glyph;
        }
        else
        {
            m_Glyphs.Add(glyph);
        }

        // Clear lookup cache to force rebuild
        m_GlyphLookup = null;
        m_CharLookup = null;
    }

    /// <summary>
    /// Remove glyph by name
    /// </summary>
    public bool RemoveGlyph(string glyphName)
    {
        for (int i = 0; i < m_Glyphs.Count; i++)
        {
            if (m_Glyphs[i].glyphName == glyphName)
            {
                m_Glyphs.RemoveAt(i);
                m_GlyphLookup = null;
                m_CharLookup = null;
                return true;
            }
        }
        return false;
    }

    /// <summary>
    /// Build lookup tables for fast glyph access
    /// </summary>
    private void BuildLookupTables()
    {
        m_GlyphLookup = new Dictionary<string, BitmapGlyph>();
        m_CharLookup = new Dictionary<char, BitmapGlyph>();

        foreach (var glyph in m_Glyphs)
        {
            if (glyph == null) continue;

            // Add to name lookup
            if (!string.IsNullOrEmpty(glyph.glyphName))
            {
                m_GlyphLookup[glyph.glyphName] = glyph;
            }

            // Add to character lookup
            if (glyph.character != '\0')
            {
                m_CharLookup[glyph.character] = glyph;
            }
        }
    }

    /// <summary>
    /// Clear lookup cache (call when glyphs are modified externally)
    /// </summary>
    public void InvalidateLookupCache()
    {
        m_GlyphLookup = null;
        m_CharLookup = null;
    }

    private void OnValidate()
    {
        // Clear cache when values change in inspector
        InvalidateLookupCache();
    }
}

-target:library
-out:"Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp.dll"
-refout:"Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp.ref.dll"
-define:UNITY_2022_3_57
-define:UNITY_2022_3
-define:UNITY_2022
-define:UNITY_5_3_OR_NEWER
-define:UNITY_5_4_OR_NEWER
-define:UNITY_5_5_OR_NEWER
-define:UNITY_5_6_OR_NEWER
-define:UNITY_2017_1_OR_NEWER
-define:UNITY_2017_2_OR_NEWER
-define:UNITY_2017_3_OR_NEWER
-define:UNITY_2017_4_OR_NEWER
-define:UNITY_2018_1_OR_NEWER
-define:UNITY_2018_2_OR_NEWER
-define:UNITY_2018_3_OR_NEWER
-define:UNITY_2018_4_OR_NEWER
-define:UNITY_2019_1_OR_NEWER
-define:UNITY_2019_2_OR_NEWER
-define:UNITY_2019_3_OR_NEWER
-define:UNITY_2019_4_OR_NEWER
-define:UNITY_2020_1_OR_NEWER
-define:UNITY_2020_2_OR_NEWER
-define:UNITY_2020_3_OR_NEWER
-define:UNITY_2021_1_OR_NEWER
-define:UNITY_2021_2_OR_NEWER
-define:UNITY_2021_3_OR_NEWER
-define:UNITY_2022_1_OR_NEWER
-define:UNITY_2022_2_OR_NEWER
-define:UNITY_2022_3_OR_NEWER
-define:UNITY_INCLUDE_TESTS
-define:ENABLE_AR
-define:ENABLE_AUDIO
-define:ENABLE_CACHING
-define:ENABLE_CLOTH
-define:ENABLE_EVENT_QUEUE
-define:ENABLE_MICROPHONE
-define:ENABLE_MULTIPLE_DISPLAYS
-define:ENABLE_PHYSICS
-define:ENABLE_TEXTURE_STREAMING
-define:ENABLE_LZMA
-define:ENABLE_UNITYEVENTS
-define:ENABLE_VR
-define:ENABLE_WEBCAM
-define:ENABLE_UNITYWEBREQUEST
-define:ENABLE_WWW
-define:ENABLE_CLOUD_SERVICES
-define:ENABLE_CLOUD_SERVICES_ADS
-define:ENABLE_CLOUD_SERVICES_USE_WEBREQUEST
-define:ENABLE_CLOUD_SERVICES_CRASH_REPORTING
-define:ENABLE_CLOUD_SERVICES_NATIVE_CRASH_REPORTING
-define:ENABLE_CLOUD_SERVICES_PURCHASING
-define:ENABLE_CLOUD_SERVICES_ANALYTICS
-define:ENABLE_CLOUD_SERVICES_BUILD
-define:ENABLE_EDITOR_GAME_SERVICES
-define:ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT
-define:ENABLE_CLOUD_LICENSE
-define:ENABLE_EDITOR_HUB_LICENSE
-define:ENABLE_WEBSOCKET_CLIENT
-define:ENABLE_GENERATE_NATIVE_PLUGINS_FOR_ASSEMBLIES_API
-define:ENABLE_DIRECTOR_AUDIO
-define:ENABLE_DIRECTOR_TEXTURE
-define:ENABLE_MANAGED_JOBS
-define:ENABLE_MANAGED_TRANSFORM_JOBS
-define:ENABLE_MANAGED_ANIMATION_JOBS
-define:ENABLE_MANAGED_AUDIO_JOBS
-define:ENABLE_RUNTIME_PERMISSIONS
-define:ENABLE_ENGINE_CODE_STRIPPING
-define:ENABLE_ONSCREEN_KEYBOARD
-define:ENABLE_MANAGED_UNITYTLS
-define:INCLUDE_DYNAMIC_GI
-define:ENABLE_SCRIPTING_GC_WBARRIERS
-define:PLATFORM_SUPPORTS_MONO
-define:ENABLE_VIDEO
-define:ENABLE_ACCELERATOR_CLIENT_DEBUGGING
-define:ENABLE_NAVIGATION_PACKAGE_DEBUG_VISUALIZATION
-define:ENABLE_NAVIGATION_HEIGHTMESH_RUNTIME_SUPPORT
-define:ENABLE_NAVIGATION_UI_REQUIRES_PACKAGE
-define:PLATFORM_ANDROID
-define:TEXTCORE_1_0_OR_NEWER
-define:UNITY_ANDROID
-define:UNITY_ANDROID_API
-define:ENABLE_EGL
-define:ENABLE_NETWORK
-define:ENABLE_RUNTIME_GI
-define:ENABLE_CRUNCH_TEXTURE_COMPRESSION
-define:UNITY_CAN_SHOW_SPLASH_SCREEN
-define:UNITY_HAS_GOOGLEVR
-define:UNITY_HAS_TANGO
-define:ENABLE_SPATIALTRACKING
-define:ENABLE_ETC_COMPRESSION
-define:PLATFORM_EXTENDS_VULKAN_DEVICE
-define:PLATFORM_HAS_MULTIPLE_SWAPCHAINS
-define:UNITY_ANDROID_SUPPORTS_SHADOWFILES
-define:PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP
-define:ENABLE_UNITYADS_RUNTIME
-define:UNITY_UNITYADS_API
-define:ENABLE_MONO
-define:NET_STANDARD_2_0
-define:NET_STANDARD
-define:NET_STANDARD_2_1
-define:NETSTANDARD
-define:NETSTANDARD2_1
-define:ENABLE_PROFILER
-define:DEBUG
-define:TRACE
-define:UNITY_ASSERTIONS
-define:UNITY_EDITOR
-define:UNITY_EDITOR_64
-define:UNITY_EDITOR_WIN
-define:ENABLE_UNITY_COLLECTIONS_CHECKS
-define:ENABLE_BURST_AOT
-define:UNITY_TEAM_LICENSE
-define:ENABLE_CUSTOM_RENDER_TEXTURE
-define:ENABLE_DIRECTOR
-define:ENABLE_LOCALIZATION
-define:ENABLE_SPRITES
-define:ENABLE_TERRAIN
-define:ENABLE_TILEMAP
-define:ENABLE_TIMELINE
-define:ENABLE_LEGACY_INPUT_MANAGER
-define:TEXTCORE_FONT_ENGINE_1_5_OR_NEWER
-define:TEXTCORE_FONT_ENGINE_1_6_OR_NEWER
-define:ENABLE_SPAN_T
-define:WWISE_ADDRESSABLES_POST_2023
-define:CSHARP_7_OR_LATER
-define:CSHARP_7_3_OR_NEWER
-r:"Assets/Plugins/Clipper2Lib.dll"
-r:"Assets/Plugins/Google.FlatBuffers.dll"
-r:"Assets/Plugins/Microsoft.IO.RecyclableMemoryStream.dll"
-r:"Assets/Plugins/NLog.dll"
-r:"Assets/Plugins/System.Runtime.CompilerServices.Unsafe.dll"
-r:"Assets/StandaloneFileBrowser/Plugins/Ookii.Dialogs.dll"
-r:"Assets/StandaloneFileBrowser/Plugins/System.Windows.Forms.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.CoreModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.DeviceSimulatorModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.DiagnosticsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.EditorToolbarModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.GraphViewModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.PresetsUIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.QuickSearchModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.SceneTemplateModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.SceneViewModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.TextCoreFontEngineModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.TextCoreTextEngineModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.UIBuilderModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsSamplesModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.UnityConnectModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.AccessibilityModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.AIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.AndroidJNIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.AnimationModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ARModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.AssetBundleModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.AudioModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ClothModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ContentLoadModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.CoreModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.CrashReportingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.DirectorModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.DSPGraphModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.GameCenterModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.GIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.GridModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.HotReloadModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ImageConversionModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.IMGUIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.InputLegacyModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.InputModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.JSONSerializeModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.LocalizationModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ParticleSystemModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.PerformanceReportingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.Physics2DModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.PhysicsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ProfilerModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.PropertiesModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ScreenCaptureModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.SharedInternalsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteMaskModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteShapeModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.StreamingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.SubstanceModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.SubsystemsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainPhysicsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreFontEngineModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreTextEngineModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TextRenderingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TilemapModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TLSModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UIElementsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UmbraModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsCommonModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityConnectModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityCurlModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityTestProtocolModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAssetBundleModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAudioModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestTextureModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestWWWModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.VehiclesModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.VFXModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.VideoModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.VRModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.WindModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.XRModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/mscorlib.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.ComponentModel.Composition.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Core.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Data.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Drawing.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.IO.Compression.FileSystem.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Net.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Numerics.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Runtime.Serialization.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.ServiceModel.Web.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Transactions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Web.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Windows.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Xml.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Xml.Linq.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Xml.Serialization.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/Microsoft.Win32.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.AppContext.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Buffers.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.Concurrent.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.NonGeneric.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.Specialized.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.EventBasedAsync.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.TypeConverter.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Console.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Data.Common.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Contracts.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Debug.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.FileVersionInfo.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Process.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.StackTrace.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.TextWriterTraceListener.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Tools.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.TraceSource.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Tracing.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Drawing.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Dynamic.Runtime.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.Calendars.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Compression.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Compression.ZipFile.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.DriveInfo.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.Watcher.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.IsolatedStorage.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.MemoryMappedFiles.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Pipes.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.UnmanagedMemoryStream.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Expressions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Parallel.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Queryable.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Memory.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Http.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.NameResolution.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.NetworkInformation.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Ping.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Requests.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Security.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Sockets.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebHeaderCollection.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebSockets.Client.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebSockets.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Numerics.Vectors.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ObjectModel.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.DispatchProxy.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.ILGeneration.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.Lightweight.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.Reader.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.ResourceManager.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.Writer.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.CompilerServices.VisualC.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Handles.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.InteropServices.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.InteropServices.RuntimeInformation.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Numerics.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Formatters.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Json.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Xml.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Claims.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Algorithms.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Csp.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Encoding.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.X509Certificates.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Principal.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.SecureString.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Text.Encoding.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Text.Encoding.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Text.RegularExpressions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Overlapped.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.Parallel.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Thread.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.ThreadPool.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Timer.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ValueTuple.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.ReaderWriter.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XDocument.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XmlDocument.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XmlSerializer.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XPath.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XPath.XDocument.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/Extensions/2.0.0/System.Runtime.InteropServices.WindowsRuntime.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/NetStandard/ref/2.1.0/netstandard.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/PlaybackEngines/AndroidPlayer/Unity.Android.Gradle.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/PlaybackEngines/AndroidPlayer/Unity.Android.GradleProject.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/PlaybackEngines/AndroidPlayer/Unity.Android.Types.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.Common.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.Xcode.dll"
-r:"Library/PackageCache/com.unity.collections@1.5.1/Unity.Collections.LowLevel.ILSupport/Unity.Collections.LowLevel.ILSupport.dll"
-r:"Library/PackageCache/com.unity.nuget.mono-cecil@1.11.4/Mono.Cecil.dll"
-r:"Library/PackageCache/com.unity.visualscripting@1.9.4/Runtime/VisualScripting.Flow/Dependencies/NCalc/Unity.VisualScripting.Antlr3.Runtime.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Ak.Wwise.Api.WAAPI.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/AK.Wwise.Unity.API.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/AK.Wwise.Unity.API.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/AK.Wwise.Unity.API.WwiseTypes.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/AK.Wwise.Unity.MonoBehaviour.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/AK.Wwise.Unity.MonoBehaviour.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/AK.Wwise.Unity.Timeline.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/AK.Wwise.Unity.Timeline.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/AllIn1VfxAssmebly.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/AllIn1VfxDemoScriptAssemblies.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/AllIn1VfxTexDemoAssembly.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp-firstpass.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/CallbackManager.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Events.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/KinoBloom.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/LitMotion.Animation.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/LitMotion.Animation.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/LitMotion.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/LitMotion.Extensions.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/LitMotion.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Localization.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Luban.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Luban.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/MeshUI.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Polyperfect.Common.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Polyperfect.Common.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Polyperfect.War.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Polyperfect.War.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/PPv2URPConverters.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/SimpleTouch.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Singleton.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/TaskScheduler.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/ToonyColorsPro.Demo.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/ToonyColorsPro.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/ToonyColorsPro.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/ToonyColorsPro2.Demo.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.2D.Sprite.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Addressables.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Addressables.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Burst.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Collections.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Mathematics.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Mathematics.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.PlasticSCM.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipeline.Universal.ShaderLibrary.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Core.ShaderLibrary.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Config.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.RenderPipelines.Universal.Shaders.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ResourceManager.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Rider.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ScriptableBuildPipeline.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ScriptableBuildPipeline.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Searcher.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.ShaderGraph.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.TextMeshPro.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.TextMeshPro.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Timeline.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.Timeline.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualEffectGraph.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualEffectGraph.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Core.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Flow.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Flow.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.SettingsProvider.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.Shared.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.State.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualScripting.State.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VisualStudio.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/Unity.VSCode.Editor.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/UnityEditor.UI.ref.dll"
-r:"Library/Bee/artifacts/1300b0aEDbg.dag/UnityEngine.UI.ref.dll"
-analyzer:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/Tools/Unity.SourceGenerators/Unity.Properties.SourceGenerator.dll"
-analyzer:"C:/Program Files/Unity/Hub/Editor/2022.3.57f1c1/Editor/Data/Tools/Unity.SourceGenerators/Unity.SourceGenerators.dll"
"Assets/Arts/Archanor/Stylized Fire FX/Demo/Scripts/ButtonScript.cs"
"Assets/Arts/Archanor/Stylized Fire FX/Demo/Scripts/DragMouseOrbit.cs"
"Assets/Arts/Archanor/Stylized Fire FX/Demo/Scripts/FireProjectile.cs"
"Assets/Arts/Archanor/Stylized Fire FX/Demo/Scripts/LoadSceneOnClick.cs"
"Assets/Arts/Archanor/Stylized Fire FX/Demo/Scripts/ProjectileScript.cs"
"Assets/Arts/Archanor/Stylized Fire FX/Scripts/lightScript.cs"
"Assets/Arts/Archanor/Stylized Fire FX/Scripts/loopScript.cs"
"Assets/Arts/SetCartoonTanks/Demo_Game_Scene/Scripts/SCT_CameraControl.cs"
"Assets/Arts/SetCartoonTanks/Demo_Game_Scene/Scripts/SCT_Shell.cs"
"Assets/Arts/SetCartoonTanks/Demo_Game_Scene/Scripts/SCT_TankMovement.cs"
"Assets/Arts/SetCartoonTanks/Demo_Game_Scene/Scripts/SCT_Tower_Cannon.cs"
"Assets/Arts/SetCartoonTanks/Demo_Table_Animations/Scripts/Anims_Scripts_SCT.cs"
"Assets/Arts/SetCartoonTanks/Demo_Table_Animations/Scripts/SCAirCrafts_Actions.cs"
"Assets/Arts/SetCartoonTanks/Demo_Table_Animations/Scripts/SCAirCrafts_Ui_Char_Panel.cs"
"Assets/Arts/SetCartoonTanks/Demo_Table_Animations/Scripts/SCShips_Actions.cs"
"Assets/Arts/SetCartoonTanks/Demo_Table_Animations/Scripts/SCShips_Ui_Char_Panel.cs"
"Assets/Arts/SetCartoonTanks/Demo_Table_Animations/Scripts/SCT_Actions.cs"
"Assets/Arts/SetCartoonTanks/Demo_Table_Animations/Scripts/SCT_CharacterViewer.cs"
"Assets/Arts/SetCartoonTanks/Demo_Table_Animations/Scripts/SCT_Ui_Char_Panel.cs"
"Assets/Arts/UNI VFX/Common/Scripts/UNI_SimpleCameraController.cs"
"Assets/Arts/Vefects/Explosions VFX URP/Demo/Resources/Scripts/SC_Vefects_Delete_After_Time.cs"
"Assets/Arts/Vefects/Explosions VFX URP/Demo/Resources/Scripts/SC_Vefects_Easy_Spawn.cs"
"Assets/Buildings_constructor/Components/Doors_and_Windows/Doors.cs"
"Assets/Buildings_constructor/Scripts/Controller.cs"
"Assets/Buildings_constructor/Scripts/Cycle_moving.cs"
"Assets/Buildings_constructor/Scripts/Rotate.cs"
"Assets/Buildings_constructor/Scripts/TopDpwnCamera.cs"
"Assets/BulletEffectCore/packs/WebDemoAssets/scripts/CameraShake.cs"
"Assets/BulletEffectCore/packs/WebDemoAssets/scripts/CameraShakeCaller.cs"
"Assets/BulletEffectCore/packs/WebDemoAssets/scripts/CameraShakeProjectile.cs"
"Assets/BulletEffectCore/packs/WebDemoAssets/scripts/EffectActor.cs"
"Assets/BulletEffectCore/packs/WebDemoAssets/scripts/ExplodingProjectile.cs"
"Assets/BulletEffectCore/packs/WebDemoAssets/scripts/Projectile.cs"
"Assets/BulletEffectCore/packs/WebDemoAssets/scripts/projectileActor.cs"
"Assets/BulletEffectCore/scripts/destroyMe.cs"
"Assets/BulletEffectCore/scripts/particleColorChangerMaster.cs"
"Assets/EffectCore/packs/WebDemoAssets/scripts/ECelectricActor.cs"
"Assets/EffectCore/scripts/ECparticleLinerendererColorChangerMaster.cs"
"Assets/Engine Particle/Script/Exit.cs"
"Assets/Engine Particle/Script/ExtendedFlycam.cs"
"Assets/Engine Particle/Script/Particles.cs"
"Assets/Engine Particle/Script/UserCamera.cs"
"Assets/ExplsionFx/packs/StylizedExplosionPack1/WebDemo/scripts/ExplodingProjectileExplosion1.cs"
"Assets/ExplsionFx/packs/StylizedExplosionPack1/WebDemo/scripts/instantiateEffectCallerExplosion1.cs"
"Assets/ExplsionFx/packs/StylizedExplosionPack1/WebDemo/scripts/projectileActorExplosion1.cs"
"Assets/ExplsionFx/packs/WebDemoAssets/scripts/ECCameraShake.cs"
"Assets/ExplsionFx/packs/WebDemoAssets/scripts/ECCameraShakeCaller.cs"
"Assets/ExplsionFx/packs/WebDemoAssets/scripts/ECCameraShakeProjectile.cs"
"Assets/ExplsionFx/packs/WebDemoAssets/scripts/ECEffectActor.cs"
"Assets/ExplsionFx/packs/WebDemoAssets/scripts/ECExplodingProjectile.cs"
"Assets/ExplsionFx/packs/WebDemoAssets/scripts/ECProjectile.cs"
"Assets/ExplsionFx/packs/WebDemoAssets/scripts/ECprojectileActor.cs"
"Assets/ExplsionFx/scripts/ECdestroyMe.cs"
"Assets/ExplsionFx/scripts/ECparticleColorChangerMaster.cs"
"Assets/FogOfWar/Demo/Scripts/BlinkingRevealer.cs"
"Assets/FogOfWar/Demo/Scripts/FowCharacterDemo.cs"
"Assets/FogOfWar/Demo/Scripts/TeamsDemo.cs"
"Assets/FogOfWar/Scripts/Built-In (legacy) RP/FOWImageEffect.cs"
"Assets/FogOfWar/Scripts/Extras/MiniMapFrustum.cs"
"Assets/FogOfWar/Scripts/FogOfWarWorld.cs"
"Assets/FogOfWar/Scripts/GenerateInvertedCollider.cs"
"Assets/FogOfWar/Scripts/Hiders/FogOfWarHider.cs"
"Assets/FogOfWar/Scripts/Hiders/HiderBehavior.cs"
"Assets/FogOfWar/Scripts/Hiders/HiderDisableObjects.cs"
"Assets/FogOfWar/Scripts/Hiders/HiderDisableRenderers.cs"
"Assets/FogOfWar/Scripts/Hiders/HiderToggleObjects.cs"
"Assets/FogOfWar/Scripts/Hiders/PartialHider.cs"
"Assets/FogOfWar/Scripts/Hiders/PartialHiderRegisterer.cs"
"Assets/FogOfWar/Scripts/Revealers/FogOfWarRevealer.cs"
"Assets/FogOfWar/Scripts/Revealers/FogOfWarRevealer2D.cs"
"Assets/FogOfWar/Scripts/Revealers/FogOfWarRevealer3D.cs"
"Assets/FogOfWar/Scripts/Revealers/RevealerDebug.cs"
"Assets/FogOfWar/Scripts/UnityBugWorkaround.cs"
"Assets/FogOfWar/Scripts/URP/FogOfWarPass.cs"
"Assets/FogOfWar/Scripts/URP/FogOfWarRenderFeature.cs"
"Assets/GabrielAguiarProductions/Script/ParticleSystemController/ParticleSystemController.cs"
"Assets/GabrielAguiarProductions/Script/ParticleSystemController/ParticleSystemControllerEditor.cs"
"Assets/GabrielAguiarProductions/Script/ParticleSystemController/SaveParticleSystemScript.cs"
"Assets/GabrielAguiarProductions/Script/ParticleSystemController/Serializables.cs"
"Assets/GabrielAguiarProductions/Script/SimpleCameraController.cs"
"Assets/GabrielAguiarProductions/Script/SimpleCameraShake.cs"
"Assets/GabrielAguiarProductions/Script/SpawnAbilities.cs"
"Assets/Hovl Studio/Toon Projectiles 2/Demo scene/DemoShooting.cs"
"Assets/Hovl Studio/Toon Projectiles 2/Scripts/AutoDestroyPS.cs"
"Assets/Hovl Studio/Toon Projectiles 2/Scripts/ProjectileMover.cs"
"Assets/Low Poly Rocks Pack/Bonus Assets/Scripts/CameraControl.cs"
"Assets/Low Poly Rocks Pack/Bonus Assets/Scripts/CloudsControl.cs"
"Assets/Low Poly Rocks Pack/Bonus Assets/Scripts/FireLightControl.cs"
"Assets/Low Poly Rocks Pack/Bonus Assets/Scripts/SunControl.cs"
"Assets/MagicArsenal/Demo/Scripts/MagicBeamScript.cs"
"Assets/MagicArsenal/Demo/Scripts/MagicDragMouseOrbit.cs"
"Assets/MagicArsenal/Demo/Scripts/MagicEffectCycler.cs"
"Assets/MagicArsenal/Demo/Scripts/MagicFireProjectile.cs"
"Assets/MagicArsenal/Demo/Scripts/MagicLoadSceneOnClick.cs"
"Assets/MagicArsenal/Demo/Scripts/MagicLoopScript.cs"
"Assets/MagicArsenal/Demo/Scripts/MagicProjectileScript.cs"
"Assets/MagicArsenal/Effects/Scripts/MagicBeamStatic.cs"
"Assets/MagicArsenal/Effects/Scripts/MagicLightFade.cs"
"Assets/MagicArsenal/Effects/Scripts/MagicLightFlicker.cs"
"Assets/MagicArsenal/Effects/Scripts/MagicRotation.cs"
"Assets/Real Fire & Smoke/Demo/Scripts/RealFireDragMouseOrbit.cs"
"Assets/Real Fire & Smoke/Demo/Scripts/RealFireLoopScript.cs"
"Assets/Real Fire & Smoke/Demo/Scripts/RealFireSceneSelect.cs"
"Assets/Real Fire & Smoke/Scripts/RealFireLightFade.cs"
"Assets/Real Fire & Smoke/Scripts/RealFireLightFlicker.cs"
"Assets/Redclue/ShieldFX_02/Scripts_ShieldFX02/CameraController.cs"
"Assets/Redclue/ShieldFX_02/Scripts_ShieldFX02/CollisonShieldInstantiate.cs"
"Assets/Redclue/ShieldFX_02/Scripts_ShieldFX02/Gun Scripts/Fire.cs"
"Assets/Redclue/ShieldFX_02/Scripts_ShieldFX02/Gun Scripts/Rotate.cs"
"Assets/Redclue/ShieldFX_02/Scripts_ShieldFX02/Gun Scripts/Spawn.cs"
"Assets/Redclue/ShieldFX_02/Scripts_ShieldFX02/PreviousAndNext_Shield02fx.cs"
"Assets/Resources/Prefabs/FX/FlashEffect.cs"
"Assets/RTS Effects/Demo/Scripts/RTSClickSpawn.cs"
"Assets/RTS Effects/Demo/Scripts/RTSLoopFX.cs"
"Assets/RTS Effects/Demo/Scripts/RTSSceneSelect.cs"
"Assets/RTS Effects/Scripts/AnimatedUVs.cs"
"Assets/Scripts/Audio/AudioMgr.cs"
"Assets/Scripts/Audio/AudioProvider.cs"
"Assets/Scripts/Audio/DebugAudio.cs"
"Assets/Scripts/AutoGen/Luban/gameplay/Area.cs"
"Assets/Scripts/AutoGen/Luban/gameplay/ArmorType.cs"
"Assets/Scripts/AutoGen/Luban/gameplay/CardFaction.cs"
"Assets/Scripts/AutoGen/Luban/gameplay/CardKind.cs"
"Assets/Scripts/AutoGen/Luban/gameplay/CardQuality.cs"
"Assets/Scripts/AutoGen/Luban/gameplay/FogVisibilityType.cs"
"Assets/Scripts/AutoGen/Luban/gameplay/PlaceRect.cs"
"Assets/Scripts/AutoGen/Luban/gameplay/PlanType.cs"
"Assets/Scripts/AutoGen/Luban/gameplay/ResCard.cs"
"Assets/Scripts/AutoGen/Luban/gameplay/ResUnit.cs"
"Assets/Scripts/AutoGen/Luban/gameplay/TbCard.cs"
"Assets/Scripts/AutoGen/Luban/gameplay/TbUnit.cs"
"Assets/Scripts/AutoGen/Luban/gameplay/UnitCategory.cs"
"Assets/Scripts/AutoGen/Luban/gameplay/UnitMover.cs"
"Assets/Scripts/AutoGen/Luban/gameplay/UnitType.cs"
"Assets/Scripts/AutoGen/Luban/gameplay/WeaponSet.cs"
"Assets/Scripts/AutoGen/Luban/Int2.cs"
"Assets/Scripts/AutoGen/Luban/ResWhiteList.cs"
"Assets/Scripts/AutoGen/Luban/Tables.cs"
"Assets/Scripts/AutoGen/Luban/vector2.cs"
"Assets/Scripts/AutoGen/Luban/vector3.cs"
"Assets/Scripts/AutoGen/Luban/vector4.cs"
"Assets/Scripts/AutoGen/Table/GlobalConst_generated.cs"
"Assets/Scripts/AutoGen/Table/Global_generated.cs"
"Assets/Scripts/AutoGen/Table/IResTable.cs"
"Assets/Scripts/AutoGen/Table/ResCamp_generated.cs"
"Assets/Scripts/AutoGen/Table/ResCard_generated.cs"
"Assets/Scripts/AutoGen/Table/ResDamage_generated.cs"
"Assets/Scripts/AutoGen/Table/ResGlobal_generated.cs"
"Assets/Scripts/AutoGen/Table/ResHero_generated.cs"
"Assets/Scripts/AutoGen/Table/ResMap_generated.cs"
"Assets/Scripts/AutoGen/Table/ResMode_generated.cs"
"Assets/Scripts/AutoGen/Table/ResUnit_generated.cs"
"Assets/Scripts/AutoGen/Table/ResWeapon_generated.cs"
"Assets/Scripts/Config/DataTable/DataMgr.cs"
"Assets/Scripts/Config/DataTable/DataTableLoader.cs"
"Assets/Scripts/Config/DataTable/ResUtility.cs"
"Assets/Scripts/Config/EnvDef.cs"
"Assets/Scripts/Config/GameplayConstDef.cs"
"Assets/Scripts/Debug/DebugMeshCanvas.cs"
"Assets/Scripts/Framework/AssetManagement/Runtime/AssetHandle.cs"
"Assets/Scripts/Framework/AssetManagement/Runtime/AssetLoader.cs"
"Assets/Scripts/Framework/AssetManagement/Runtime/AssetLoaderImpl.cs"
"Assets/Scripts/Framework/AssetManagement/Runtime/AssetManifest.cs"
"Assets/Scripts/Framework/AssetManagement/Runtime/AssetManifestSetting.cs"
"Assets/Scripts/Framework/AssetManagement/Runtime/AssetOperation.cs"
"Assets/Scripts/Framework/AssetManagement/Runtime/AssetReference.cs"
"Assets/Scripts/Framework/AssetManagement/Runtime/AssetRequest.cs"
"Assets/Scripts/Framework/AssetManagement/Runtime/AssetUtility.cs"
"Assets/Scripts/Framework/AssetManagement/Runtime/LowLevelLoader.cs"
"Assets/Scripts/Framework/AssetManagement/Runtime/Ref.cs"
"Assets/Scripts/Framework/Collections/BitMask.cs"
"Assets/Scripts/Framework/Collections/Fixed16List.cs"
"Assets/Scripts/Framework/Collections/Fixed32List.cs"
"Assets/Scripts/Framework/Collections/Fixed64List.cs"
"Assets/Scripts/Framework/Core/ExtensionUtils.cs"
"Assets/Scripts/Framework/Core/IMessage.cs"
"Assets/Scripts/Framework/Core/Logger.cs"
"Assets/Scripts/Framework/Core/MessageDefine.cs"
"Assets/Scripts/Framework/Core/MessageDispatcher.cs"
"Assets/Scripts/Framework/Core/MessageQueue.cs"
"Assets/Scripts/Framework/Core/ObjectPoolManager.cs"
"Assets/Scripts/Framework/Core/ResourcesManager.cs"
"Assets/Scripts/Framework/Core/TimeHelper.cs"
"Assets/Scripts/Framework/Core/UnityObjectPool.cs"
"Assets/Scripts/Framework/Deterministic/Lockstep/AIService.cs"
"Assets/Scripts/Framework/Deterministic/Lockstep/Frame.cs"
"Assets/Scripts/Framework/Deterministic/Lockstep/FrameManager.cs"
"Assets/Scripts/Framework/Deterministic/Lockstep/INetService.cs"
"Assets/Scripts/Framework/Deterministic/Lockstep/IService.cs"
"Assets/Scripts/Framework/Deterministic/Lockstep/LockstepController.cs"
"Assets/Scripts/Framework/Deterministic/Lockstep/LockstepMessageBody.cs"
"Assets/Scripts/Framework/Deterministic/Lockstep/LockstepUtil.cs"
"Assets/Scripts/Framework/Deterministic/Lockstep/NetService.cs"
"Assets/Scripts/Framework/Deterministic/Lockstep/PlayerInput.cs"
"Assets/Scripts/Framework/Deterministic/Lockstep/ServiceContainer.cs"
"Assets/Scripts/Framework/Deterministic/Lockstep/Simulator.cs"
"Assets/Scripts/Framework/Deterministic/Lockstep/World.cs"
"Assets/Scripts/Framework/Deterministic/Math/Fix64.cs"
"Assets/Scripts/Framework/Deterministic/Math/Fix64Quaternion.cs"
"Assets/Scripts/Framework/Deterministic/Math/Fix64SinLut.cs"
"Assets/Scripts/Framework/Deterministic/Math/Fix64TanLut.cs"
"Assets/Scripts/Framework/Deterministic/Math/Fix64Util.cs"
"Assets/Scripts/Framework/Deterministic/Math/Fix64Vector2.cs"
"Assets/Scripts/Framework/Deterministic/Math/Fix64Vector3.cs"
"Assets/Scripts/Framework/Game/Manager.cs"
"Assets/Scripts/Framework/PropertyAttributes/EnumFlag.cs"
"Assets/Scripts/Framework/UI/Common/Animation/UIAnimation.cs"
"Assets/Scripts/Framework/UI/Common/Animation/UIAnimationKeyFrame.cs"
"Assets/Scripts/Framework/UI/Common/Animation/UIAnimationProperty.cs"
"Assets/Scripts/Framework/UI/Common/Animation/UIAnimationTrack.cs"
"Assets/Scripts/Framework/UI/Common/AssetCollection/AssetCollection.cs"
"Assets/Scripts/Framework/UI/Common/AssetCollection/SpriteCollection.cs"
"Assets/Scripts/Framework/UI/Common/BitmapText/BitmapFont.cs"
"Assets/Scripts/Framework/UI/Common/BitmapText/BitmapGlyph.cs"
"Assets/Scripts/Framework/UI/Common/BitmapText/BitmapTextGenerator.cs"
"Assets/Scripts/Framework/UI/Common/BitmapText/BitmapTextSettings.cs"
"Assets/Scripts/Framework/UI/Common/BitmapText/MeshBitmapText.cs"
"Assets/Scripts/Framework/UI/Common/BitmapText/UIBitmapText.cs"
"Assets/Scripts/Framework/UI/Common/ListView/Attributes/AssetRefAttribute.cs"
"Assets/Scripts/Framework/UI/Common/ListView/Core/Easings.cs"
"Assets/Scripts/Framework/UI/Common/ListView/Core/ListView.cs"
"Assets/Scripts/Framework/UI/Common/ListView/Core/ListViewBase.cs"
"Assets/Scripts/Framework/UI/Common/ListView/Core/ListViewInputHandler.cs"
"Assets/Scripts/Framework/UI/Common/ListView/Core/ListViewItem.cs"
"Assets/Scripts/Framework/UI/Common/ListView/Core/MListView.cs"
"Assets/Scripts/Framework/UI/Common/ListView/Core/MListViewItem.cs"
"Assets/Scripts/Framework/UI/Common/ListView/Extension/Effect/DragListViewDisableDragIfFits.cs"
"Assets/Scripts/Framework/UI/Common/ListView/Extension/Effect/ListViewScrollEventDispatcher.cs"
"Assets/Scripts/Framework/UI/Common/ListView/Extension/Effect/ListViewScrollEventReceiver.cs"
"Assets/Scripts/Framework/UI/Common/ListView/Extension/Effect/ReceiverGraphicRaycaster.cs"
"Assets/Scripts/Framework/UI/Common/ListView/Extension/Effect/ReceiverSizeDelta.cs"
"Assets/Scripts/Framework/UI/Common/ListView/Extension/GridView.cs"
"Assets/Scripts/Framework/UI/Common/ListView/Extension/ListViewDragHandler.cs"
"Assets/Scripts/Framework/UI/Common/ListView/Extension/ListViewLayoutElement.cs"
"Assets/Scripts/Framework/UI/Common/ListView/Extension/MListViewSwipeHandler.cs"
"Assets/Scripts/Framework/UI/Common/ListView/Extension/NoTemplate/GeneralDragListView.cs"
"Assets/Scripts/Framework/UI/Common/ListView/Extension/NoTemplate/GeneralGridView.cs"
"Assets/Scripts/Framework/UI/Common/MeshEffect/CircleMeshEffect.cs"
"Assets/Scripts/Framework/UI/Common/MeshEffect/MaskMeshEffect.cs"
"Assets/Scripts/Framework/UI/Common/MeshEffect/MirrorMeshEffect.cs"
"Assets/Scripts/Framework/UI/Common/MeshEffect/TextGradientEffect.cs"
"Assets/Scripts/Framework/UI/Common/Resolution/ResolutionMonitor.cs"
"Assets/Scripts/Framework/UI/Components/UIViewActive.cs"
"Assets/Scripts/Framework/UI/Components/UIViewAnimation.cs"
"Assets/Scripts/Framework/UI/Components/UIViewBGM.cs"
"Assets/Scripts/Framework/UI/Components/UIViewDepth.cs"
"Assets/Scripts/Framework/UI/Components/UIViewEnable.cs"
"Assets/Scripts/Framework/UI/Components/UIViewRaycaster.cs"
"Assets/Scripts/Framework/UI/Components/UIViewScripting.cs"
"Assets/Scripts/Framework/UI/Interfaces/IProperty.cs"
"Assets/Scripts/Framework/UI/Interfaces/IViewComponent.cs"
"Assets/Scripts/Framework/UI/Interfaces/IViewHolder.cs"
"Assets/Scripts/Framework/UI/Model/PropertyCollection.cs"
"Assets/Scripts/Framework/UI/Model/TProperty.cs"
"Assets/Scripts/Framework/UI/Model/UIModel.cs"
"Assets/Scripts/Framework/UI/UIMgr.cs"
"Assets/Scripts/Framework/UI/UIRoot.cs"
"Assets/Scripts/Framework/UI/View/UIView.cs"
"Assets/Scripts/Framework/UI/View/UIViewComponent.cs"
"Assets/Scripts/Framework/UI/View/UIViewConfig.cs"
"Assets/Scripts/GameFlow/Datas/IGameData.cs"
"Assets/Scripts/GameFlow/Datas/SaveData/CardSelectTempData.cs"
"Assets/Scripts/GameFlow/Datas/SaveData/GlobalSaveData.cs"
"Assets/Scripts/GameFlow/Datas/SaveData/SaveDataBase.cs"
"Assets/Scripts/GameFlow/Datas/ServerData/Player.cs"
"Assets/Scripts/GameFlow/Datas/ServerData/RoleData.cs"
"Assets/Scripts/GameFlow/Datas/ServerData/RoomData.cs"
"Assets/Scripts/GameFlow/Game.cs"
"Assets/Scripts/GameFlow/GameStateController.cs"
"Assets/Scripts/GameFlow/States/eGameEventIDs.cs"
"Assets/Scripts/GameFlow/States/GameContext.cs"
"Assets/Scripts/GameFlow/States/GameStateBase.cs"
"Assets/Scripts/GameFlow/States/GameStateBattle.cs"
"Assets/Scripts/GameFlow/States/GameStateBattleFinish.cs"
"Assets/Scripts/GameFlow/States/GameStateBattleLoading.cs"
"Assets/Scripts/GameFlow/States/GameStateIdle.cs"
"Assets/Scripts/GameFlow/States/GameStateInitialize.cs"
"Assets/Scripts/GameFlow/States/GameStateLogin.cs"
"Assets/Scripts/GameFlow/States/GameStateRoom.cs"
"Assets/Scripts/GameFlow/States/SubStates/LoginSubState_Connect.cs"
"Assets/Scripts/GameFlow/Systems/BGMSystem.cs"
"Assets/Scripts/GameFlow/Systems/DataProviderSystem.cs"
"Assets/Scripts/GameFlow/Systems/IGameSystem.cs"
"Assets/Scripts/GameFlow/Systems/LoginSystem.cs"
"Assets/Scripts/GameFlow/Systems/NetworkSystems/BattleNetworkSystem.cs"
"Assets/Scripts/GameFlow/Systems/NetworkSystems/BattleNetworkSystem_NetService.cs"
"Assets/Scripts/GameFlow/Systems/NetworkSystems/BattleNetworkSystem_Request.cs"
"Assets/Scripts/GameFlow/Systems/NetworkSystems/BattleNetworkSystem_Response.cs"
"Assets/Scripts/GameFlow/Systems/NetworkSystems/LobbyNetworkSystem.cs"
"Assets/Scripts/GameFlow/Systems/NetworkSystems/NetworkSystemBase.cs"
"Assets/Scripts/GameFlow/Systems/PlayerSystem.cs"
"Assets/Scripts/GameFlow/Systems/RecordSystem.cs"
"Assets/Scripts/GameFlow/Systems/RegisterGameSystemAttribute.cs"
"Assets/Scripts/GameFlow/Systems/ResultSystem.cs"
"Assets/Scripts/GameFlow/Systems/RoomSystem.cs"
"Assets/Scripts/GameFlow/Systems/SaveDataSystem.cs"
"Assets/Scripts/GameModules/AroundGame/BattleFinish/UIBattleFinishLogic.cs"
"Assets/Scripts/GameModules/AroundGame/BattleLoading/UIBattleLoadingView.cs"
"Assets/Scripts/GameModules/AroundGame/BattlePrepare/CardSelect/UICampItem.cs"
"Assets/Scripts/GameModules/AroundGame/BattlePrepare/CardSelect/UICardGroupItem.cs"
"Assets/Scripts/GameModules/AroundGame/BattlePrepare/CardSelect/UICardSelectionLogic.cs"
"Assets/Scripts/GameModules/AroundGame/BattlePrepare/CardSelect/UICardTempInfo.cs"
"Assets/Scripts/GameModules/AroundGame/BattlePrepare/CardSelect/UICardTitleItem.cs"
"Assets/Scripts/GameModules/AroundGame/BattlePrepare/UIHeroSelectFilter.cs"
"Assets/Scripts/GameModules/AroundGame/BattlePrepare/UIHeroSelectItem.cs"
"Assets/Scripts/GameModules/AroundGame/BattlePrepare/UIHeroSelectView.cs"
"Assets/Scripts/GameModules/AroundGame/Lobby/UILobbyView.cs"
"Assets/Scripts/GameModules/AroundGame/Lobby/UIRoomItemCtrl.cs"
"Assets/Scripts/GameModules/AroundGame/Lobby/UIRoomListCtrl.cs"
"Assets/Scripts/GameModules/AroundGame/Login/UICreateRoleView.cs"
"Assets/Scripts/GameModules/AroundGame/Login/UILoginView.cs"
"Assets/Scripts/GameModules/AroundGame/PreLogin/UIPreLoginView.cs"
"Assets/Scripts/GameModules/AroundGame/Room/UIRoomCampPlayerItem.cs"
"Assets/Scripts/GameModules/AroundGame/Room/UIRoomCampView.cs"
"Assets/Scripts/GameModules/AroundGame/Room/UIRoomView.cs"
"Assets/Scripts/GameModules/Common/Components/Blurs/UIAreaBlurRect.cs"
"Assets/Scripts/GameModules/Common/Components/Blurs/UITextureBlur.cs"
"Assets/Scripts/GameModules/Common/Components/ContentProvider.cs"
"Assets/Scripts/GameModules/Common/Components/DontDestroyOnLoad.cs"
"Assets/Scripts/GameModules/Common/Components/DragTarget.cs"
"Assets/Scripts/GameModules/Common/Components/LayoutElementEx.cs"
"Assets/Scripts/GameModules/Common/Components/LayoutElementImageFill.cs"
"Assets/Scripts/GameModules/Common/Components/PlayerHeadCtrl.cs"
"Assets/Scripts/GameModules/Common/Components/SimpleVideoController.cs"
"Assets/Scripts/GameModules/Common/Components/UIRawImageScreenMatch.cs"
"Assets/Scripts/GameModules/Common/Components/UIRaycastElement.cs"
"Assets/Scripts/GameModules/Common/Components/UIRelativeDepth.cs"
"Assets/Scripts/GameModules/Common/EventsDefine/GameplayEvent.cs"
"Assets/Scripts/GameModules/Common/EventsDefine/GameStateEvent.cs"
"Assets/Scripts/GameModules/Common/EventsDefine/GMRetEvent.cs"
"Assets/Scripts/GameModules/Common/GM/Commands/CommonCommands.cs"
"Assets/Scripts/GameModules/Common/GM/Commands/ExampleCommands.cs"
"Assets/Scripts/GameModules/Common/GM/Commands/GameplayCommands.cs"
"Assets/Scripts/GameModules/Common/GM/GMAttributes.cs"
"Assets/Scripts/GameModules/Common/GM/GMCommand.cs"
"Assets/Scripts/GameModules/Common/GM/GMSystem.cs"
"Assets/Scripts/GameModules/Common/Utilities/AnimUtility.cs"
"Assets/Scripts/GameModules/Common/Utilities/SecurityUtility.cs"
"Assets/Scripts/GameModules/Common/Utilities/StringFormatter.cs"
"Assets/Scripts/GameModules/Common/Utilities/StringUtility.cs"
"Assets/Scripts/GameModules/Common/Utilities/UIUtility.cs"
"Assets/Scripts/GameModules/Common/Views/UICommonBorderTips/UICommonBorderTipsView.cs"
"Assets/Scripts/GameModules/Common/Views/UICommonBox/UICommonBoxView.cs"
"Assets/Scripts/GameModules/Common/Views/UICommonNetworkMask/UINetworkMaskView.cs"
"Assets/Scripts/GameModules/Common/Views/UICommonTips/UICommonTipsView.cs"
"Assets/Scripts/GameModules/Common/Views/UIGM/CachedInputField.cs"
"Assets/Scripts/GameModules/Common/Views/UIGM/UIBaseGMPanel.cs"
"Assets/Scripts/GameModules/Common/Views/UIGM/UIGMButtonEntry.cs"
"Assets/Scripts/GameModules/Common/Views/UIGM/UIGMCategoryItem.cs"
"Assets/Scripts/GameModules/Common/Views/UIGM/UIGMContainerItem.cs"
"Assets/Scripts/GameModules/Common/Views/UIGM/UIGMItemCtrls/UIGMButtonItem.cs"
"Assets/Scripts/GameModules/Common/Views/UIGM/UIGMItemCtrls/UIGMDropdownItem.cs"
"Assets/Scripts/GameModules/Common/Views/UIGM/UIGMItemCtrls/UIGMSliderItem.cs"
"Assets/Scripts/GameModules/Common/Views/UIGM/UIGMItemCtrls/UIGMToggleItem.cs"
"Assets/Scripts/GameModules/Common/Views/UIGM/UIGMLogic.cs"
"Assets/Scripts/GameModules/Common/Views/UIGM/UIGMUinText.cs"
"Assets/Scripts/GameModules/Common/Views/UIGM/UIInputGMPanel.cs"
"Assets/Scripts/GameModules/Gen/UINames.cs"
"Assets/Scripts/GameModules/InGame/Message_InGameUI.cs"
"Assets/Scripts/GameModules/InGame/TestSolider.cs"
"Assets/Scripts/GameModules/InGame/UnitTestHelper.cs"
"Assets/Scripts/GameModules/InGame/Views/InputSystem/BuildingPolicy.cs"
"Assets/Scripts/GameModules/InGame/Views/InputSystem/GameInputSystem.cs"
"Assets/Scripts/GameModules/InGame/Views/InputSystem/Joystick/BaseJoystick.cs"
"Assets/Scripts/GameModules/InGame/Views/InputSystem/Joystick/UICardJoystick.cs"
"Assets/Scripts/GameModules/InGame/Views/InputSystem/Joystick/UIJoystick.cs"
"Assets/Scripts/GameModules/InGame/Views/UIBattleBroadcast/BattleEventSystem.cs"
"Assets/Scripts/GameModules/InGame/Views/UIBattleBroadcast/UIBattleBroadcastCtrl.cs"
"Assets/Scripts/GameModules/InGame/Views/UIBattleBroadcast/UIBroadcastItemBase.cs"
"Assets/Scripts/GameModules/InGame/Views/UIBattleMain/CardController/UICardCancelCtrl.cs"
"Assets/Scripts/GameModules/InGame/Views/UIBattleMain/CardController/UICardFrameApplier.cs"
"Assets/Scripts/GameModules/InGame/Views/UIBattleMain/CardController/UICardItemCtrl.cs"
"Assets/Scripts/GameModules/InGame/Views/UIBattleMain/CardController/UICardItemDataListener.cs"
"Assets/Scripts/GameModules/InGame/Views/UIBattleMain/CardController/UICardLayoutApplier.cs"
"Assets/Scripts/GameModules/InGame/Views/UIBattleMain/CardController/UICardMaterialApplier.cs"
"Assets/Scripts/GameModules/InGame/Views/UIBattleMain/CardController/UICardParent.cs"
"Assets/Scripts/GameModules/InGame/Views/UIBattleMain/FloatingText/UIFloatingTextSpawner.cs"
"Assets/Scripts/GameModules/InGame/Views/UIBattleMain/PhaseController/UIBattlePhaseApplier.cs"
"Assets/Scripts/GameModules/InGame/Views/UIBattleMain/PressTips/UIPressTips.cs"
"Assets/Scripts/GameModules/InGame/Views/UIBattleMain/PressTips/UIPressTipsEventHandler.cs"
"Assets/Scripts/GameModules/InGame/Views/UIBattleMain/Temp/UICardSelectionTemp.cs"
"Assets/Scripts/GameModules/InGame/Views/UIBattleMain/UIBattleMainView.cs"
"Assets/Scripts/GameModules/InGame/Views/UIBattleMain/UIBattleMainViewTemp.cs"
"Assets/Scripts/GameModules/InGame/Views/UIBattleMain/UIBattleModel.cs"
"Assets/Scripts/GameModules/InGame/Views/UIBattleMain/UIGameStats.cs"
"Assets/Scripts/GameModules/InGame/Views/UIBattleMain/UIMoveJoystick.cs"
"Assets/Scripts/GameModules/InGame/Views/UIBattleMain/UIProgressBarMaanager.cs"
"Assets/Scripts/GameModules/InGame/Views/UIBattleMain/UISkillItemCtrl.cs"
"Assets/Scripts/Gameplay/Bootstrap/ClientBootstrap.cs"
"Assets/Scripts/Gameplay/Bootstrap/IBootstrap.cs"
"Assets/Scripts/Gameplay/Bootstrap/ServerBootstrap.cs"
"Assets/Scripts/Gameplay/Bootstrap/WorldInitializeData.cs"
"Assets/Scripts/Gameplay/Cameras/IRgMainCameraComponent.cs"
"Assets/Scripts/Gameplay/Cameras/RgMainCamera.cs"
"Assets/Scripts/Gameplay/Cameras/RgMainCameraAdaptScreenComponent.cs"
"Assets/Scripts/Gameplay/Cameras/RgMainCameraAnimationComponent.cs"
"Assets/Scripts/Gameplay/Cameras/RgMainCameraJumpComponent.cs"
"Assets/Scripts/Gameplay/Cameras/RgMainCameraLimitComponent.cs"
"Assets/Scripts/Gameplay/Cameras/RgMainCameraManager.cs"
"Assets/Scripts/Gameplay/Cameras/RgMainCameraMoveComponent.cs"
"Assets/Scripts/Gameplay/Cameras/RgMainCameraZoomComponent.cs"
"Assets/Scripts/Gameplay/Core/Common/Battle/BattleTypeDef.cs"
"Assets/Scripts/Gameplay/Core/Common/Battle/BuildingTypeDef.cs"
"Assets/Scripts/Gameplay/Core/Common/Battle/EffectTypeDef.cs"
"Assets/Scripts/Gameplay/Core/Common/Buff/BuffTypeDef.cs"
"Assets/Scripts/Gameplay/Core/Common/Buff/IBuff.cs"
"Assets/Scripts/Gameplay/Core/Common/Buff/IBuffController.cs"
"Assets/Scripts/Gameplay/Core/Common/Collision/RgCollisionType.cs"
"Assets/Scripts/Gameplay/Core/Common/Core/Crc32.cs"
"Assets/Scripts/Gameplay/Core/Common/Core/Rectangle.cs"
"Assets/Scripts/Gameplay/Core/Common/Core/RttiClass.cs"
"Assets/Scripts/Gameplay/Core/Common/Core/Singleton.cs"
"Assets/Scripts/Gameplay/Core/Common/Core/Variant.cs"
"Assets/Scripts/Gameplay/Core/Common/Curve/BezierCurve3D.cs"
"Assets/Scripts/Gameplay/Core/Common/Curve/BezierPoint3D.cs"
"Assets/Scripts/Gameplay/Core/Common/Entity/RgEntity.cs"
"Assets/Scripts/Gameplay/Core/Common/Entity/RgEntityManager.cs"
"Assets/Scripts/Gameplay/Core/Common/Entity/RgEntityTags.cs"
"Assets/Scripts/Gameplay/Core/Common/Entity/RgManager.cs"
"Assets/Scripts/Gameplay/Core/Common/Entity/RgWorld.cs"
"Assets/Scripts/Gameplay/Core/Common/ExtType/ExtTypeUtils.cs"
"Assets/Scripts/Gameplay/Core/Common/ExtType/Int2.cs"
"Assets/Scripts/Gameplay/Core/Common/FixMath/F32.cs"
"Assets/Scripts/Gameplay/Core/Common/FixMath/F32Vec2.cs"
"Assets/Scripts/Gameplay/Core/Common/FixMath/F32Vec3.cs"
"Assets/Scripts/Gameplay/Core/Common/FixMath/F32Vec4.cs"
"Assets/Scripts/Gameplay/Core/Common/FixMath/F64.cs"
"Assets/Scripts/Gameplay/Core/Common/FixMath/F64Matrix.cs"
"Assets/Scripts/Gameplay/Core/Common/FixMath/F64Quat.cs"
"Assets/Scripts/Gameplay/Core/Common/FixMath/F64Vec2.cs"
"Assets/Scripts/Gameplay/Core/Common/FixMath/F64Vec3.cs"
"Assets/Scripts/Gameplay/Core/Common/FixMath/F64Vec4.cs"
"Assets/Scripts/Gameplay/Core/Common/FixPointCS/Fixed32.cs"
"Assets/Scripts/Gameplay/Core/Common/FixPointCS/Fixed64.cs"
"Assets/Scripts/Gameplay/Core/Common/FixPointCS/FixedUtil.cs"
"Assets/Scripts/Gameplay/Core/Common/GlobalVaraint.cs"
"Assets/Scripts/Gameplay/Core/Common/Map/MapGridCell.cs"
"Assets/Scripts/Gameplay/Core/Common/Map/MapGridUtil.cs"
"Assets/Scripts/Gameplay/Core/Common/Message/Messages/RgEntityMessages.cs"
"Assets/Scripts/Gameplay/Core/Common/Message/Messages/RgStateMesssages.cs"
"Assets/Scripts/Gameplay/Core/Common/Message/RgGameplayMsgBody.cs"
"Assets/Scripts/Gameplay/Core/Common/Message/RgMessage.cs"
"Assets/Scripts/Gameplay/Core/Common/Message/RgMessageDispatcher.cs"
"Assets/Scripts/Gameplay/Core/Common/Message/RgMessageHandler.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/Crowds/AbstractCrowdEntity.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/Crowds/AvoidanceQuerySystem.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/Crowds/EntityAnnotationServerice.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/Crowds/Flowfield/FlowfieldManager.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/Crowds/Flowfield/FlowfieldTile.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/Crowds/ICollisionEvent.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/Crowds/ICrowdEntityActor.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/Crowds/ILocalBoundaryQuerier.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/Crowds/IMovableEntityManager.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/Crowds/IPathwayQuerier.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/Crowds/Map.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/Crowds/MovableEntity.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/Crowds/MovableEntityDebuger.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/Crowds/MovableEntityManager.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/Crowds/MoveStrategy/AbstractMoveStrategy.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/Crowds/MoveStrategy/AttackMoveStrategy.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/Crowds/MoveStrategy/FollowPathMoveStrategy.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/Crowds/MoveStrategy/IdleMoveStrategy.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/Crowds/MoveStrategy/IMoveStrategy.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/Crowds/Recorder.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/Crowds/RecordOperations.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/Crowds/SteeringForce/AbstractSteeringForce.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/Crowds/SteeringForce/ArriveTargetForce.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/Crowds/SteeringForce/AvoidIdleNeighborForce.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/Crowds/SteeringForce/AvoidMoveNeighborForce.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/Crowds/SteeringForce/AvoidObstacleForce.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/Crowds/SteeringForce/FlockAlignmentForce.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/Crowds/SteeringForce/FlockCohesionForce.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/Crowds/SteeringForce/FlockSeparationForce.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/Crowds/SteeringForce/FollowPathForce.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/Crowds/SteeringForce/ForwardMoveForce.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/Crowds/SteeringForce/ISteeringForce.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/Crowds/UnMovableEntity.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/Main/PathfindingMoudle.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/SharpSteer2/BaseVehicle.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/SharpSteer2/Database/IProximityDatabase.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/SharpSteer2/Database/ITokenForProximityDatabase.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/SharpSteer2/Database/LocalityQueryDatabase.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/SharpSteer2/Database/LocalityQueryProximityDatabase.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/SharpSteer2/Helpers/Colors.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/SharpSteer2/Helpers/LocalSpaceBasisHelpers.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/SharpSteer2/Helpers/MatrixHelpers.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/SharpSteer2/Helpers/PathwayHelpers.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/SharpSteer2/Helpers/RandomHelpers.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/SharpSteer2/Helpers/Utilities.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/SharpSteer2/Helpers/Vector3Helpers.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/SharpSteer2/Helpers/VehicleHelpers.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/SharpSteer2/IAnnotationService.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/SharpSteer2/IFlowField.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/SharpSteer2/ILocalSpaceBasis.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/SharpSteer2/IVehicle.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/SharpSteer2/LocalSpace.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/SharpSteer2/NullAnnotationService.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/SharpSteer2/Obstacles/BoxObstacle.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/SharpSteer2/Obstacles/IObstacle.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/SharpSteer2/Obstacles/LocalSpaceObstacle.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/SharpSteer2/Obstacles/Obstacle.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/SharpSteer2/Obstacles/ObstacleGroup.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/SharpSteer2/Obstacles/PlaneObstacle.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/SharpSteer2/Obstacles/RectangleObstacle.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/SharpSteer2/Obstacles/SphericalObstacle.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/SharpSteer2/Pathway/GatewayPathway.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/SharpSteer2/Pathway/IPathway.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/SharpSteer2/Pathway/PolylinePathway.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/SharpSteer2/Pathway/TrianglePathway.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/SharpSteer2/SimpleVehicle.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/SharpSteer2/SteerLibrary.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/Triangulation/AI/AStar.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/Triangulation/AI/Funnel.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/Triangulation/AI/PathFinder.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/Triangulation/Data/Constants.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/Triangulation/Data/ConstraintSegment.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/Triangulation/Data/ConstraintShape.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/Triangulation/Data/Edge.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/Triangulation/Data/Face.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/Triangulation/Data/Mesh.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/Triangulation/Data/Obstacle.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/Triangulation/Data/Vertex.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/Triangulation/Factories/RectMesh.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/Triangulation/Iterators/FromEdgeToRotatedEdges.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/Triangulation/Iterators/FromFaceToInnerEdges.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/Triangulation/Iterators/FromFaceToInnerVertices.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/Triangulation/Iterators/FromFaceToNeighbourFaces.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/Triangulation/Iterators/FromMeshToFaces.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/Triangulation/Iterators/FromMeshToVertices.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/Triangulation/Iterators/FromVertexToHoldingFaces.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/Triangulation/Iterators/FromVertexToIncomingEdges.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/Triangulation/Iterators/FromVertexToNeighbourVertices.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/Triangulation/Iterators/FromVertexToOutgoingEdges.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/Triangulation/Math/Geom2D.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/Triangulation/Math/Matrix2D.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/Triangulation/Math/RandGenerator.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/Triangulation/View/SimpleView.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/Util/Crc32.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/Util/Debug.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/Util/Draw.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/Util/Geometry.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/Util/TypeCast.cs"
"Assets/Scripts/Gameplay/Core/Common/Pathfinding/Util/UniqueId.cs"
"Assets/Scripts/Gameplay/Core/Common/Physics/FixedMath.Net/src/Fix64.cs"
"Assets/Scripts/Gameplay/Core/Common/Physics/FixedMath.Net/src/Fix64SinLut.cs"
"Assets/Scripts/Gameplay/Core/Common/Physics/FixedMath.Net/src/Fix64TanLut.cs"
"Assets/Scripts/Gameplay/Core/Common/Physics/VolatilePhysics/Extensions/VoltExplosion.cs"
"Assets/Scripts/Gameplay/Core/Common/Physics/VolatilePhysics/Gizmos/Colors.cs"
"Assets/Scripts/Gameplay/Core/Common/Physics/VolatilePhysics/Gizmos/IGizmosDrawer.cs"
"Assets/Scripts/Gameplay/Core/Common/Physics/VolatilePhysics/Internals/Axis.cs"
"Assets/Scripts/Gameplay/Core/Common/Physics/VolatilePhysics/Internals/Broadphase/IBroadphase.cs"
"Assets/Scripts/Gameplay/Core/Common/Physics/VolatilePhysics/Internals/Broadphase/NaiveBroadphase.cs"
"Assets/Scripts/Gameplay/Core/Common/Physics/VolatilePhysics/Internals/Broadphase/TreeBroadphase.cs"
"Assets/Scripts/Gameplay/Core/Common/Physics/VolatilePhysics/Internals/CheapList.cs"
"Assets/Scripts/Gameplay/Core/Common/Physics/VolatilePhysics/Internals/Collision/Collision.cs"
"Assets/Scripts/Gameplay/Core/Common/Physics/VolatilePhysics/Internals/Collision/Contact.cs"
"Assets/Scripts/Gameplay/Core/Common/Physics/VolatilePhysics/Internals/Collision/ContactManager.cs"
"Assets/Scripts/Gameplay/Core/Common/Physics/VolatilePhysics/Internals/Collision/IContactListener.cs"
"Assets/Scripts/Gameplay/Core/Common/Physics/VolatilePhysics/Internals/Collision/Manifold.cs"
"Assets/Scripts/Gameplay/Core/Common/Physics/VolatilePhysics/Internals/History/HistoryBuffer.cs"
"Assets/Scripts/Gameplay/Core/Common/Physics/VolatilePhysics/Internals/History/HistoryRecord.cs"
"Assets/Scripts/Gameplay/Core/Common/Physics/VolatilePhysics/Internals/IIndexedValue.cs"
"Assets/Scripts/Gameplay/Core/Common/Physics/VolatilePhysics/RayCast/VoltRayCast.cs"
"Assets/Scripts/Gameplay/Core/Common/Physics/VolatilePhysics/RayCast/VoltRayResult.cs"
"Assets/Scripts/Gameplay/Core/Common/Physics/VolatilePhysics/Shapes/VoltCircle.cs"
"Assets/Scripts/Gameplay/Core/Common/Physics/VolatilePhysics/Shapes/VoltPolygon.cs"
"Assets/Scripts/Gameplay/Core/Common/Physics/VolatilePhysics/Unity/VolatileUtility.cs"
"Assets/Scripts/Gameplay/Core/Common/Physics/VolatilePhysics/Util/Pooling/IVoltPoolable.cs"
"Assets/Scripts/Gameplay/Core/Common/Physics/VolatilePhysics/Util/Pooling/VoltPool.cs"
"Assets/Scripts/Gameplay/Core/Common/Physics/VolatilePhysics/Util/VoltDebug.cs"
"Assets/Scripts/Gameplay/Core/Common/Physics/VolatilePhysics/Util/VoltUtil.cs"
"Assets/Scripts/Gameplay/Core/Common/Physics/VolatilePhysics/VoltAABB.cs"
"Assets/Scripts/Gameplay/Core/Common/Physics/VolatilePhysics/VoltBody.cs"
"Assets/Scripts/Gameplay/Core/Common/Physics/VolatilePhysics/VoltBuffer.cs"
"Assets/Scripts/Gameplay/Core/Common/Physics/VolatilePhysics/VoltConfig.cs"
"Assets/Scripts/Gameplay/Core/Common/Physics/VolatilePhysics/VoltMath.cs"
"Assets/Scripts/Gameplay/Core/Common/Physics/VolatilePhysics/VoltShape.cs"
"Assets/Scripts/Gameplay/Core/Common/Physics/VolatilePhysics/VoltVector2.cs"
"Assets/Scripts/Gameplay/Core/Common/Physics/VolatilePhysics/VoltWorld.cs"
"Assets/Scripts/Gameplay/Core/Common/Serialize/RgArchive.cs"
"Assets/Scripts/Gameplay/Core/Common/Template/RgTemplate.cs"
"Assets/Scripts/Gameplay/Core/Common/Template/RgTemplateManager.cs"
"Assets/Scripts/Gameplay/Core/Common/Timer/RgTimerManager.cs"
"Assets/Scripts/Gameplay/Core/Common/uPools/Runtime/External/Addressables/AddressableGameObjectPool.cs"
"Assets/Scripts/Gameplay/Core/Common/uPools/Runtime/External/Addressables/AsyncAddressableGameObjectPool.cs"
"Assets/Scripts/Gameplay/Core/Common/uPools/Runtime/External/UniTask/AsyncObjectPool.cs"
"Assets/Scripts/Gameplay/Core/Common/uPools/Runtime/External/UniTask/AsyncObjectPoolBase.cs"
"Assets/Scripts/Gameplay/Core/Common/uPools/Runtime/External/UniTask/IAsyncObjectPool.cs"
"Assets/Scripts/Gameplay/Core/Common/uPools/Runtime/Internal/PoolCallbackHelper.cs"
"Assets/Scripts/Gameplay/Core/Common/uPools/Runtime/IObjectPool.cs"
"Assets/Scripts/Gameplay/Core/Common/uPools/Runtime/IPoolCallbackReceiver.cs"
"Assets/Scripts/Gameplay/Core/Common/uPools/Runtime/ObjectPool.cs"
"Assets/Scripts/Gameplay/Core/Common/uPools/Runtime/ObjectPoolBase.cs"
"Assets/Scripts/Gameplay/Core/Common/uPools/Runtime/SharedGameObjectPool.cs"
"Assets/Scripts/Gameplay/Core/Common/Utils/DebugUtils.cs"
"Assets/Scripts/Gameplay/Core/Common/Utils/GeometryUtils.cs"
"Assets/Scripts/Gameplay/Core/Common/Utils/GizmosUtils.cs"
"Assets/Scripts/Gameplay/Core/Common/Utils/MathUtils.cs"
"Assets/Scripts/Gameplay/Core/Common/Utils/Vec3Utils.cs"
"Assets/Scripts/Gameplay/Core/Logic/Battle/Effect/RgEffectBase.cs"
"Assets/Scripts/Gameplay/Core/Logic/Battle/Effect/RgEffect_Buff.cs"
"Assets/Scripts/Gameplay/Core/Logic/Battle/Effect/RgEffect_ChangeAttribute.cs"
"Assets/Scripts/Gameplay/Core/Logic/Battle/Effect/RgEffect_ChangeDamageResistance.cs"
"Assets/Scripts/Gameplay/Core/Logic/Battle/Effect/RgEffect_CreateEffectField.cs"
"Assets/Scripts/Gameplay/Core/Logic/Battle/Effect/RgEffect_CreateEntity.cs"
"Assets/Scripts/Gameplay/Core/Logic/Battle/Effect/RgEffect_ExposeSight.cs"
"Assets/Scripts/Gameplay/Core/Logic/Battle/Effect/RgEffect_ModifyEntityState.cs"
"Assets/Scripts/Gameplay/Core/Logic/Battle/Effect/RgEffect_Repair.cs"
"Assets/Scripts/Gameplay/Core/Logic/Battle/Effect/RgEffect_TakeDamage.cs"
"Assets/Scripts/Gameplay/Core/Logic/Battle/Effect/RgEffect​Field.cs"
"Assets/Scripts/Gameplay/Core/Logic/Battle/RgDamage.cs"
"Assets/Scripts/Gameplay/Core/Logic/Battle/RgEffectFieldManager.cs"
"Assets/Scripts/Gameplay/Core/Logic/Battle/Weapon/RgWeaponBase.cs"
"Assets/Scripts/Gameplay/Core/Logic/Battle/Weapon/RgWeaponBomb.cs"
"Assets/Scripts/Gameplay/Core/Logic/Battle/Weapon/RgWeaponCollection.cs"
"Assets/Scripts/Gameplay/Core/Logic/Battle/Weapon/RgWeaponDefault.cs"
"Assets/Scripts/Gameplay/Core/Logic/Battle/Weapon/RgWeaponExplode.cs"
"Assets/Scripts/Gameplay/Core/Logic/Battle/Weapon/RgWeaponMedical.cs"
"Assets/Scripts/Gameplay/Core/Logic/Battle/Weapon/RgWeaponMortar.cs"
"Assets/Scripts/Gameplay/Core/Logic/Battle/Weapon/RgWeaponRifle.cs"
"Assets/Scripts/Gameplay/Core/Logic/Battle/Weapon/RgWeaponRocket.cs"
"Assets/Scripts/Gameplay/Core/Logic/Battle/Weapon/RgWeaponTurret.cs"
"Assets/Scripts/Gameplay/Core/Logic/Battle/Weapon/RgWeaponTurretGun.cs"
"Assets/Scripts/Gameplay/Core/Logic/Battle/Weapon/RgWeaponTurretProjectile.cs"
"Assets/Scripts/Gameplay/Core/Logic/Brain/RgAIBrain.cs"
"Assets/Scripts/Gameplay/Core/Logic/Brain/RgAIBrainAircraft.cs"
"Assets/Scripts/Gameplay/Core/Logic/Brain/RgAIBrainCarrier.cs"
"Assets/Scripts/Gameplay/Core/Logic/Brain/RgAIBrainCarrierTransportPlane.cs"
"Assets/Scripts/Gameplay/Core/Logic/Brain/RgAIBrainMedical.cs"
"Assets/Scripts/Gameplay/Core/Logic/Brain/RgAIBrainParatrooper.cs"
"Assets/Scripts/Gameplay/Core/Logic/Brain/RgAiBrainUnitFuShe.cs"
"Assets/Scripts/Gameplay/Core/Logic/Brain/RgFollowMarchingRoute.cs"
"Assets/Scripts/Gameplay/Core/Logic/Brain/RgMarchingRouteManager.cs"
"Assets/Scripts/Gameplay/Core/Logic/Collision/RgCollisionManager.cs"
"Assets/Scripts/Gameplay/Core/Logic/Crowd/Gizmos/UnityGizmosDrawer.cs"
"Assets/Scripts/Gameplay/Core/Logic/Crowd/Gizmos/UnityPhysicsGizmosDrawer.cs"
"Assets/Scripts/Gameplay/Core/Logic/Crowd/RgCrowdEntityManager.cs"
"Assets/Scripts/Gameplay/Core/Logic/Crowd/RgObstacleAvoidanceDebugData.cs"
"Assets/Scripts/Gameplay/Core/Logic/Crowd/RgObstacleAvoidanceParams.cs"
"Assets/Scripts/Gameplay/Core/Logic/Crowd/RgObstacleAvoidanceQuery.cs"
"Assets/Scripts/Gameplay/Core/Logic/Crowd/RgObstacleCircle.cs"
"Assets/Scripts/Gameplay/Core/Logic/Crowd/RgObstacleSegment.cs"
"Assets/Scripts/Gameplay/Core/Logic/DataTable/RgAIBrainTemplates.cs"
"Assets/Scripts/Gameplay/Core/Logic/DataTable/RgArmorTemplates.cs"
"Assets/Scripts/Gameplay/Core/Logic/DataTable/RgBuffTemplates.cs"
"Assets/Scripts/Gameplay/Core/Logic/DataTable/RgBuildingTemplates.cs"
"Assets/Scripts/Gameplay/Core/Logic/DataTable/RgCrowdEntityMoverTemplates.cs"
"Assets/Scripts/Gameplay/Core/Logic/DataTable/RgEffectFieldTemplates.cs"
"Assets/Scripts/Gameplay/Core/Logic/DataTable/RgEffectTemplates.cs"
"Assets/Scripts/Gameplay/Core/Logic/DataTable/RgEntityAirplaneTemplates.cs"
"Assets/Scripts/Gameplay/Core/Logic/DataTable/RgEntityAirportTemplates.cs"
"Assets/Scripts/Gameplay/Core/Logic/DataTable/RgEntityCarrierTemplates.cs"
"Assets/Scripts/Gameplay/Core/Logic/DataTable/RgEntityGroupTemplates.cs"
"Assets/Scripts/Gameplay/Core/Logic/DataTable/RgEntityMovableTemplates.cs"
"Assets/Scripts/Gameplay/Core/Logic/DataTable/RgEntityProjectileTemplates.cs"
"Assets/Scripts/Gameplay/Core/Logic/DataTable/RgEntityPropertyTemplates.cs"
"Assets/Scripts/Gameplay/Core/Logic/DataTable/RgEntitySkillTemplates.cs"
"Assets/Scripts/Gameplay/Core/Logic/DataTable/RgEntityTemplates.cs"
"Assets/Scripts/Gameplay/Core/Logic/DataTable/RgEntityTurretTemplates.cs"
"Assets/Scripts/Gameplay/Core/Logic/DataTable/RgFlowFieldTemplates.cs"
"Assets/Scripts/Gameplay/Core/Logic/DataTable/RgFlyTowardsPositionTemplates.cs"
"Assets/Scripts/Gameplay/Core/Logic/DataTable/RgFollowSegmentPathTemplates.cs"
"Assets/Scripts/Gameplay/Core/Logic/DataTable/RgFormationTemplates.cs"
"Assets/Scripts/Gameplay/Core/Logic/DataTable/RgGameTemplates.cs"
"Assets/Scripts/Gameplay/Core/Logic/DataTable/RgGlobalEntityTemplates.cs"
"Assets/Scripts/Gameplay/Core/Logic/DataTable/RgLogicPartTemplates.cs"
"Assets/Scripts/Gameplay/Core/Logic/DataTable/RgMapInfoTemplates.cs"
"Assets/Scripts/Gameplay/Core/Logic/DataTable/RgMoveModeTemplates.cs"
"Assets/Scripts/Gameplay/Core/Logic/DataTable/RgMoverTargetTemplates.cs"
"Assets/Scripts/Gameplay/Core/Logic/DataTable/RgPathFollowMoverTemplates.cs"
"Assets/Scripts/Gameplay/Core/Logic/DataTable/RgPlayerTemplates.cs"
"Assets/Scripts/Gameplay/Core/Logic/DataTable/RgProjectileMoverTemplates.cs"
"Assets/Scripts/Gameplay/Core/Logic/DataTable/RgSearchConditionTemplates.cs"
"Assets/Scripts/Gameplay/Core/Logic/DataTable/RgSnapshotTemplates.cs"
"Assets/Scripts/Gameplay/Core/Logic/DataTable/RgStateMachineTemplates.cs"
"Assets/Scripts/Gameplay/Core/Logic/DataTable/RgVisionTemplates.cs"
"Assets/Scripts/Gameplay/Core/Logic/DataTable/RgWeaponTemplates.cs"
"Assets/Scripts/Gameplay/Core/Logic/Entity/Buff/RgBuff.cs"
"Assets/Scripts/Gameplay/Core/Logic/Entity/Buff/RgBuffController.cs"
"Assets/Scripts/Gameplay/Core/Logic/Entity/Building/RgEntity_Building.cs"
"Assets/Scripts/Gameplay/Core/Logic/Entity/Building/RgEntity_Building_Base.cs"
"Assets/Scripts/Gameplay/Core/Logic/Entity/Building/RgEntity_Building_CloningVats.cs"
"Assets/Scripts/Gameplay/Core/Logic/Entity/Building/RgEntity_Building_ControlPoint.cs"
"Assets/Scripts/Gameplay/Core/Logic/Entity/Building/RgEntity_Building_Factory.cs"
"Assets/Scripts/Gameplay/Core/Logic/Entity/Building/RgEntity_Building_OilDerrick.cs"
"Assets/Scripts/Gameplay/Core/Logic/Entity/Building/RgEntity_Building_Producer.cs"
"Assets/Scripts/Gameplay/Core/Logic/Entity/Building/RgEntity_Building_Skill.cs"
"Assets/Scripts/Gameplay/Core/Logic/Entity/Building/RgEntity_Building_Skill_AutomaticallyAddBuff.cs"
"Assets/Scripts/Gameplay/Core/Logic/Entity/RgEntityActor.cs"
"Assets/Scripts/Gameplay/Core/Logic/Entity/RgEntityAirplane.cs"
"Assets/Scripts/Gameplay/Core/Logic/Entity/RgEntityAirport.cs"
"Assets/Scripts/Gameplay/Core/Logic/Entity/RgEntityCarrier.cs"
"Assets/Scripts/Gameplay/Core/Logic/Entity/RgEntityCellManager.cs"
"Assets/Scripts/Gameplay/Core/Logic/Entity/RgEntityMovable.cs"
"Assets/Scripts/Gameplay/Core/Logic/Entity/RgEntityProjectile.cs"
"Assets/Scripts/Gameplay/Core/Logic/Entity/RgEntityProperty.cs"
"Assets/Scripts/Gameplay/Core/Logic/Entity/RgEntityState.cs"
"Assets/Scripts/Gameplay/Core/Logic/Entity/RgEntityTemplateStruct.cs"
"Assets/Scripts/Gameplay/Core/Logic/Entity/RgLogicEntityManager.cs"
"Assets/Scripts/Gameplay/Core/Logic/Entity/Skill/RgEntitySkill.cs"
"Assets/Scripts/Gameplay/Core/Logic/Entity/Skill/RgEntitySkill_AtomicBomb.cs"
"Assets/Scripts/Gameplay/Core/Logic/Entity/Skill/RgEntitySkill_CallTransportPlane.cs"
"Assets/Scripts/Gameplay/Core/Logic/Entity/Skill/RgEntitySkill_EmergencyMobilization.cs"
"Assets/Scripts/Gameplay/Core/Logic/Entity/Skill/RgEntitySkill_ExecuteBuff.cs"
"Assets/Scripts/Gameplay/Core/Logic/Entity/Skill/RgEntitySkill_MissileStrike.cs"
"Assets/Scripts/Gameplay/Core/Logic/Entity/Skill/RgSkillManager.cs"
"Assets/Scripts/Gameplay/Core/Logic/GM/RgGMCheatManager.cs"
"Assets/Scripts/Gameplay/Core/Logic/Group/RgEntityGroup.cs"
"Assets/Scripts/Gameplay/Core/Logic/Group/RgFormation.cs"
"Assets/Scripts/Gameplay/Core/Logic/Group/RgFormation_FromGeneratedPoints.cs"
"Assets/Scripts/Gameplay/Core/Logic/Group/RgGroupManger.cs"
"Assets/Scripts/Gameplay/Core/Logic/Group/RgTemplateFromation.cs"
"Assets/Scripts/Gameplay/Core/Logic/LogicParts/RgLogicPart.cs"
"Assets/Scripts/Gameplay/Core/Logic/LogicParts/RgLogicPart_ExecuteBuff.cs"
"Assets/Scripts/Gameplay/Core/Logic/LogicParts/RgLogicPart_ExecuteEffect.cs"
"Assets/Scripts/Gameplay/Core/Logic/LogicParts/RgLogicPart_ExpandTerritory.cs"
"Assets/Scripts/Gameplay/Core/Logic/LogicParts/RgLogicPart_Producer.cs"
"Assets/Scripts/Gameplay/Core/Logic/LogicParts/RgLogicPart_WeaponProxy.cs"
"Assets/Scripts/Gameplay/Core/Logic/Map/RgMapLogic.cs"
"Assets/Scripts/Gameplay/Core/Logic/Map/RgMapManager.cs"
"Assets/Scripts/Gameplay/Core/Logic/Map/RgMapSearchManager.cs"
"Assets/Scripts/Gameplay/Core/Logic/Map/RgVision.cs"
"Assets/Scripts/Gameplay/Core/Logic/Map/RgVisionManager.cs"
"Assets/Scripts/Gameplay/Core/Logic/MapBlock/RgInfluenceMap.cs"
"Assets/Scripts/Gameplay/Core/Logic/MapBlock/RgMapBlock.cs"
"Assets/Scripts/Gameplay/Core/Logic/Message/RgLogicMessages.cs"
"Assets/Scripts/Gameplay/Core/Logic/Mover/Interpolation/RgBezierSplineInterpolation.cs"
"Assets/Scripts/Gameplay/Core/Logic/Mover/Interpolation/RgCatmullRomSplineInterpolation.cs"
"Assets/Scripts/Gameplay/Core/Logic/Mover/Interpolation/RgHermiteSplineInterpolation.cs"
"Assets/Scripts/Gameplay/Core/Logic/Mover/Interpolation/RgInterpolation.cs"
"Assets/Scripts/Gameplay/Core/Logic/Mover/MoveMode/RgEntityMoveMode.cs"
"Assets/Scripts/Gameplay/Core/Logic/Mover/MoveMode/RgPhysicsSimulationMoveMode.cs"
"Assets/Scripts/Gameplay/Core/Logic/Mover/MoveMode/RgPhysicsSweepMoveMode.cs"
"Assets/Scripts/Gameplay/Core/Logic/Mover/RgCrowdEntityMover.cs"
"Assets/Scripts/Gameplay/Core/Logic/Mover/RgCurvePathMover.cs"
"Assets/Scripts/Gameplay/Core/Logic/Mover/RgFlyTowardsPosition.cs"
"Assets/Scripts/Gameplay/Core/Logic/Mover/RgFollowSegmentPath.cs"
"Assets/Scripts/Gameplay/Core/Logic/Mover/RgMover.cs"
"Assets/Scripts/Gameplay/Core/Logic/Mover/RgMoveTarget.cs"
"Assets/Scripts/Gameplay/Core/Logic/Mover/RgPathFollowMover.cs"
"Assets/Scripts/Gameplay/Core/Logic/Mover/RgPhysicsMover.cs"
"Assets/Scripts/Gameplay/Core/Logic/Mover/RgProjectileMover.cs"
"Assets/Scripts/Gameplay/Core/Logic/Mover/RgSteerBehaviorMover.cs"
"Assets/Scripts/Gameplay/Core/Logic/Path/RgFlowField.cs"
"Assets/Scripts/Gameplay/Core/Logic/Path/RgPath.cs"
"Assets/Scripts/Gameplay/Core/Logic/Path/RgPathFinder.cs"
"Assets/Scripts/Gameplay/Core/Logic/Path/RgSegmentPath.cs"
"Assets/Scripts/Gameplay/Core/Logic/RgGameManager.cs"
"Assets/Scripts/Gameplay/Core/Logic/RgLogicManager.cs"
"Assets/Scripts/Gameplay/Core/Logic/RgLogicMessageManager.cs"
"Assets/Scripts/Gameplay/Core/Logic/RgLogicWorld.cs"
"Assets/Scripts/Gameplay/Core/Logic/SearchCondition/RgSearchCondition.cs"
"Assets/Scripts/Gameplay/Core/Logic/SearchCondition/RgSearchConditionCombine.cs"
"Assets/Scripts/Gameplay/Core/Logic/SearchCondition/RgSearchConditionFilter.cs"
"Assets/Scripts/Gameplay/Core/Logic/Shape/RgCircleShape.cs"
"Assets/Scripts/Gameplay/Core/Logic/Shape/RgOBBShape.cs"
"Assets/Scripts/Gameplay/Core/Logic/Shape/RgShape.cs"
"Assets/Scripts/Gameplay/Core/Logic/StateMachine/AtomAbilities/RgAbilityAttack.cs"
"Assets/Scripts/Gameplay/Core/Logic/StateMachine/AtomAbilities/RgAbilityBase.cs"
"Assets/Scripts/Gameplay/Core/Logic/StateMachine/AtomAbilities/RgAbilityIdle.cs"
"Assets/Scripts/Gameplay/Core/Logic/StateMachine/AtomAbilities/RgAbilityMove.cs"
"Assets/Scripts/Gameplay/Core/Logic/StateMachine/RgStateMachine.cs"
"Assets/Scripts/Gameplay/Core/Logic/Sync/RgSyncLogBuffer.cs"
"Assets/Scripts/Gameplay/Core/Logic/Sync/RingBuffer.cs"
"Assets/Scripts/Gameplay/Core/Logic/Team/RgPlayer.cs"
"Assets/Scripts/Gameplay/Core/Logic/Team/RgPlayerManager.cs"
"Assets/Scripts/Gameplay/Core/Logic/Team/RgTeamManager.cs"
"Assets/Scripts/Gameplay/Input/BinarySerializer.cs"
"Assets/Scripts/Gameplay/Input/CmdDefine.cs"
"Assets/Scripts/Gameplay/Input/CmdMgr.cs"
"Assets/Scripts/Gameplay/Input/Cmds/BuildingCmd.cs"
"Assets/Scripts/Gameplay/Input/Cmds/CastSkillCmd.cs"
"Assets/Scripts/Gameplay/Input/Cmds/CreateEntityCmd.cs"
"Assets/Scripts/Gameplay/Input/Cmds/GMCmd.cs"
"Assets/Scripts/Gameplay/Input/Cmds/MoveCmd.cs"
"Assets/Scripts/Gameplay/Input/Cmds/SpawnUnitCmd.cs"
"Assets/Scripts/Gameplay/Input/CmdService.cs"
"Assets/Scripts/Gameplay/Render/Animation/IRgAnimationInterface.cs"
"Assets/Scripts/Gameplay/Render/Animation/RgAnimationManager_Airplane.cs"
"Assets/Scripts/Gameplay/Render/Animation/RgAnimationManager_AtomicBombLauncher.cs"
"Assets/Scripts/Gameplay/Render/Animation/RgAnimationManager_Base.cs"
"Assets/Scripts/Gameplay/Render/Animation/RgAnimationManager_CartoonTank.cs"
"Assets/Scripts/Gameplay/Render/Animation/RgAnimationManager_Solider.cs"
"Assets/Scripts/Gameplay/Render/Audio/RgAudioBroadcastPlayer.cs"
"Assets/Scripts/Gameplay/Render/Audio/RgAudioConfig.cs"
"Assets/Scripts/Gameplay/Render/Audio/RgAudioEffectPlayer.cs"
"Assets/Scripts/Gameplay/Render/Audio/RgAudioPlayer.cs"
"Assets/Scripts/Gameplay/Render/Audio/RgEntityRender_AudioPlayerLogic.cs"
"Assets/Scripts/Gameplay/Render/Audio/RgMonoAudioPlayer.cs"
"Assets/Scripts/Gameplay/Render/Converter/IRgEntityConverter.cs"
"Assets/Scripts/Gameplay/Render/Converter/Part/Beam/IBeamEffectInterface.cs"
"Assets/Scripts/Gameplay/Render/Converter/Part/Beam/RgBeamEffect_Default.cs"
"Assets/Scripts/Gameplay/Render/Converter/Part/Beam/RgBeamEffect_MagicBeamStatic.cs"
"Assets/Scripts/Gameplay/Render/Converter/Part/RgAiportParkingSpot.cs"
"Assets/Scripts/Gameplay/Render/Converter/Part/RgAirplanePart.cs"
"Assets/Scripts/Gameplay/Render/Converter/Part/RgAirportPart.cs"
"Assets/Scripts/Gameplay/Render/Converter/Part/RgAudioPart.cs"
"Assets/Scripts/Gameplay/Render/Converter/Part/RgEntityPart.cs"
"Assets/Scripts/Gameplay/Render/Converter/Part/RgHitBoxPart.cs"
"Assets/Scripts/Gameplay/Render/Converter/Part/RgMeshRendererPart.cs"
"Assets/Scripts/Gameplay/Render/Converter/Part/RgParachutePart.cs"
"Assets/Scripts/Gameplay/Render/Converter/Part/RgPhysicsPart.cs"
"Assets/Scripts/Gameplay/Render/Converter/Part/RgSegmentPathCollectionPart.cs"
"Assets/Scripts/Gameplay/Render/Converter/Part/RgSpawnInfoPart.cs"
"Assets/Scripts/Gameplay/Render/Converter/Part/RgSpecialEffectPart.cs"
"Assets/Scripts/Gameplay/Render/Converter/Part/Skill/RgBuildingSkillPart.cs"
"Assets/Scripts/Gameplay/Render/Converter/Part/Skill/RgSkillAtomicBombPart.cs"
"Assets/Scripts/Gameplay/Render/Converter/Part/Skill/RgSkillFrozenPart.cs"
"Assets/Scripts/Gameplay/Render/Converter/Part/Weapon/RgBulletTrajectory.cs"
"Assets/Scripts/Gameplay/Render/Converter/Part/Weapon/RgWeaponBombPart.cs"
"Assets/Scripts/Gameplay/Render/Converter/Part/Weapon/RgWeaponElectromagneticEffectPart.cs"
"Assets/Scripts/Gameplay/Render/Converter/Part/Weapon/RgWeaponExplodePart.cs"
"Assets/Scripts/Gameplay/Render/Converter/Part/Weapon/RgWeaponGunPart.cs"
"Assets/Scripts/Gameplay/Render/Converter/Part/Weapon/RgWeaponMortarPart.cs"
"Assets/Scripts/Gameplay/Render/Converter/Part/Weapon/RgWeaponPart.cs"
"Assets/Scripts/Gameplay/Render/Converter/Part/Weapon/RgWeaponRocketPart.cs"
"Assets/Scripts/Gameplay/Render/Converter/Part/Weapon/RgWeaponTeslaTowerPart.cs"
"Assets/Scripts/Gameplay/Render/Converter/Part/Weapon/RgWeaponTrajectory.cs"
"Assets/Scripts/Gameplay/Render/Converter/Part/Weapon/RgWeaponTurretPart.cs"
"Assets/Scripts/Gameplay/Render/Converter/RgEntitySpecialEffects.cs"
"Assets/Scripts/Gameplay/Render/Converter/RgRenderTemplateManager.cs"
"Assets/Scripts/Gameplay/Render/Debug/DrawLogicGunDebuger.cs"
"Assets/Scripts/Gameplay/Render/Debug/LockTransformDebuger.cs"
"Assets/Scripts/Gameplay/Render/Debug/NeverDestroyDebuger.cs"
"Assets/Scripts/Gameplay/Render/Debug/PlayAnimationDebuger.cs"
"Assets/Scripts/Gameplay/Render/Debug/StartPauseDebuger.cs"
"Assets/Scripts/Gameplay/Render/Effect/RgDestructionExplosion.cs"
"Assets/Scripts/Gameplay/Render/Effect/RgSimulateSpecialEffect.cs"
"Assets/Scripts/Gameplay/Render/Effect/RgSpecialEffectSocket.cs"
"Assets/Scripts/Gameplay/Render/Effect/RgSpecialEffectSocketHub.cs"
"Assets/Scripts/Gameplay/Render/Entity/Building/RgEntityRender_Building.cs"
"Assets/Scripts/Gameplay/Render/Entity/Building/RgEntityRender_Building_Base.cs"
"Assets/Scripts/Gameplay/Render/Entity/Building/RgEntityRender_Building_ControlPoint.cs"
"Assets/Scripts/Gameplay/Render/Entity/Building/RgEntityRender_Building_Factory.cs"
"Assets/Scripts/Gameplay/Render/Entity/Building/RgEntityRender_Building_Skill.cs"
"Assets/Scripts/Gameplay/Render/Entity/Building/RgEntityRender_Snapshot.cs"
"Assets/Scripts/Gameplay/Render/Entity/RgEntityComponent.cs"
"Assets/Scripts/Gameplay/Render/Entity/RgEntityRender.cs"
"Assets/Scripts/Gameplay/Render/Entity/RgEntityRender_Actor.cs"
"Assets/Scripts/Gameplay/Render/Entity/RgEntityRender_Airplane.cs"
"Assets/Scripts/Gameplay/Render/Entity/RgEntityRender_Airport.cs"
"Assets/Scripts/Gameplay/Render/Entity/RgEntityRender_Buff.cs"
"Assets/Scripts/Gameplay/Render/Entity/RgEntityRender_Carrier.cs"
"Assets/Scripts/Gameplay/Render/Entity/RgEntityRender_EffectField.cs"
"Assets/Scripts/Gameplay/Render/Entity/RgEntityRender_Human.cs"
"Assets/Scripts/Gameplay/Render/Entity/RgEntityRender_Paratrooper.cs"
"Assets/Scripts/Gameplay/Render/Entity/RgEntityRender_Projectile.cs"
"Assets/Scripts/Gameplay/Render/Entity/RgEntityRender_RocketHuman.cs"
"Assets/Scripts/Gameplay/Render/Entity/RgEntityRender_Skill.cs"
"Assets/Scripts/Gameplay/Render/Entity/RgEntityRender_Tank.cs"
"Assets/Scripts/Gameplay/Render/Entity/RgEntityRender_Turret.cs"
"Assets/Scripts/Gameplay/Render/Entity/RgRenderEntityCatalog.cs"
"Assets/Scripts/Gameplay/Render/Entity/RgRenderEntityManager.cs"
"Assets/Scripts/Gameplay/Render/Game/RgGameAPI.cs"
"Assets/Scripts/Gameplay/Render/HUD/FloatingText/UIFloatingBase.cs"
"Assets/Scripts/Gameplay/Render/HUD/FloatingText/UIFloatingText.cs"
"Assets/Scripts/Gameplay/Render/HUD/HeadBar/RgEntityHeadBar.cs"
"Assets/Scripts/Gameplay/Render/HUD/HeadBar/UIHeadBarBase.cs"
"Assets/Scripts/Gameplay/Render/HUD/HeadBar/UIHeadBarExplode.cs"
"Assets/Scripts/Gameplay/Render/HUD/HeadBar/UIHeadBarFactory.cs"
"Assets/Scripts/Gameplay/Render/HUD/HeadBar/UIHeadBarSkill.cs"
"Assets/Scripts/Gameplay/Render/HUD/Text/RgEntityTextDisplay.cs"
"Assets/Scripts/Gameplay/Render/Map/Building/RgBuildingArea.cs"
"Assets/Scripts/Gameplay/Render/Map/Building/RgBuildingGridCell.cs"
"Assets/Scripts/Gameplay/Render/Map/Building/RgBuildingManager.cs"
"Assets/Scripts/Gameplay/Render/Map/Building/RgBuildingPlacementHandler.cs"
"Assets/Scripts/Gameplay/Render/Map/Building/RgBuildingPlacer.cs"
"Assets/Scripts/Gameplay/Render/Map/RgMap.cs"
"Assets/Scripts/Gameplay/Render/RgRenderMessageManager.cs"
"Assets/Scripts/Gameplay/Render/RgRenderWorld.cs"
"Assets/Scripts/Gameplay/Render/Selection/RgBoxSelector.cs"
"Assets/Scripts/Gameplay/Render/Selection/RgSelectionManager.cs"
"Assets/Scripts/Gameplay/Render/Selection/RgSelectionView.cs"
"Assets/Scripts/Gameplay/Render/Shape/RgOBBShape.cs"
"Assets/Scripts/Gameplay/Render/Shape/RgShape.cs"
"Assets/Scripts/Gameplay/Render/Skill/Effect/RgSkillCaster_RangeEffect.cs"
"Assets/Scripts/Gameplay/Render/Skill/RgSkillCaster.cs"
"Assets/Scripts/Gameplay/Render/Skill/RgSkillCaster_AtomicBomb.cs"
"Assets/Scripts/Gameplay/Render/Skill/RgSkillCaster_BoostMorale.cs"
"Assets/Scripts/Gameplay/Render/Skill/RgSkillCaster_CallTransportPlane.cs"
"Assets/Scripts/Gameplay/Render/Skill/RgSkillCaster_EmergencyMobilization.cs"
"Assets/Scripts/Gameplay/Render/Skill/RgSkillCaster_Frozen.cs"
"Assets/Scripts/Gameplay/Render/Skill/RgSkillCaster_MissileStrike.cs"
"Assets/Scripts/Gameplay/Render/Skill/RgSkillCaster_NoTarget.cs"
"Assets/Scripts/Gameplay/Render/Skill/RgSkillCaster_Repair.cs"
"Assets/Scripts/Gameplay/Render/Skill/RgSkillCaster_SelectTarget.cs"
"Assets/Scripts/Gameplay/Render/Spawn/RgSpawnPoint.cs"
"Assets/Scripts/Gameplay/Render/uPool/Runtime/GameObjectPool.cs"
"Assets/Scripts/Gameplay/Render/Utils/GizmosUtils.cs"
"Assets/Scripts/Gameplay/Render/Utils/MathUtils.cs"
"Assets/Scripts/Gameplay/UnitTest/APITest/QuaternionTest.cs"
"Assets/Scripts/Gameplay/UnitTest/APITest/SegmentAABBIntersection.cs"
"Assets/Scripts/Gameplay/UnitTest/APITest/TestCatmullRomSpline.cs"
"Assets/Scripts/Gameplay/UnitTest/RgAvoidanceTest.cs"
"Assets/Scripts/Gameplay/UnitTest/RgDTCrowdTest.cs"
"Assets/Scripts/Gameplay/UnitTest/RgFormationTest.cs"
"Assets/Scripts/Gameplay/UnitTest/RgLockstepCmdTest.cs"
"Assets/Scripts/Gameplay/UnitTest/RgLockstepTest.cs"
"Assets/Scripts/Gameplay/UnitTest/RgLockstepTest_InputService.cs"
"Assets/Scripts/Gameplay/UnitTest/RgPathFinderTest.cs"
"Assets/Scripts/Gameplay/UnitTest/RgPhysicsTest.cs"
"Assets/Scripts/Gameplay/UnitTest/RgSkillCasterHub.cs"
"Assets/Scripts/Gameplay/UnitTest/RgStateMachineTest.cs"
"Assets/Scripts/Gameplay/UnitTest/RgUnitTest.cs"
"Assets/Scripts/Gameplay/UnitTest/RgUnitTest_API.cs"
"Assets/Scripts/Gameplay/UnitTest/RgUnitTest_GUI.cs"
"Assets/Scripts/Gameplay/UnitTest/RgUnitTest_SkillCaster.cs"
"Assets/Scripts/Gameplay/Utils/FPSUtil.cs"
"Assets/Scripts/Gameplay/Utils/NetMode.cs"
"Assets/Scripts/Gameplay/Utils/RGamePoolingBehaviour.cs"
"Assets/Scripts/Message/IRGameMessgaeBody.cs"
"Assets/Scripts/Message/RGameMessage.cs"
"Assets/Scripts/Message/RGameMsgDef.cs"
"Assets/Scripts/Message/RGameMsgDispatcher.cs"
"Assets/Scripts/Message/RGameMsgManager.cs"
"Assets/Scripts/Network/Core/FrameRecord/FrameRecord.cs"
"Assets/Scripts/Network/Core/FrameRecord/RecordRoomService.cs"
"Assets/Scripts/Network/Core/IPEndPointNonAlloc.cs"
"Assets/Scripts/Network/Core/KCP/KCP.cs"
"Assets/Scripts/Network/Core/KCP/KCPRoomService.cs"
"Assets/Scripts/Network/Core/KCP/KCPService.cs"
"Assets/Scripts/Network/Core/Message/MsgBinding.cs"
"Assets/Scripts/Network/Core/Message/MsgDef.cs"
"Assets/Scripts/Network/Core/Message/NetworkMsg.cs"
"Assets/Scripts/Network/Core/Message/NetworkSrvMsgManager.cs"
"Assets/Scripts/Network/Core/NetworkService.cs"
"Assets/Scripts/Network/Core/NetworkThread.cs"
"Assets/Scripts/Network/Core/SocketNonAlloc.cs"
"Assets/Scripts/Network/Core/TCP/TCPService.cs"
"Assets/Scripts/Network/NetworkConfig.cs"
"Assets/Scripts/Network/NetworkHelper.cs"
"Assets/Scripts/Network/NetworkManager.cs"
"Assets/Scripts/Network/Protocols/Battle/Command.cs"
"Assets/Scripts/Network/Protocols/Battle/FrameHashNtf.cs"
"Assets/Scripts/Network/Protocols/Battle/FrameNtf.cs"
"Assets/Scripts/Network/Protocols/Battle/GameStartNtf.cs"
"Assets/Scripts/Network/Protocols/Battle/JoinGameNtf.cs"
"Assets/Scripts/Network/Protocols/Battle/SendHashCodeReq.cs"
"Assets/Scripts/Network/Protocols/Battle/SendInputReq.cs"
"Assets/Scripts/Network/Protocols/Battle/UserInput.cs"
"Assets/Scripts/Network/Protocols/comm/AccountType.cs"
"Assets/Scripts/Network/Protocols/comm/AreaStatus.cs"
"Assets/Scripts/Network/Protocols/comm/Camp.cs"
"Assets/Scripts/Network/Protocols/comm/CardGroupInfo.cs"
"Assets/Scripts/Network/Protocols/comm/CardGroupItem.cs"
"Assets/Scripts/Network/Protocols/comm/CustomRoomStatus.cs"
"Assets/Scripts/Network/Protocols/comm/GameMode.cs"
"Assets/Scripts/Network/Protocols/comm/GameResult.cs"
"Assets/Scripts/Network/Protocols/comm/GenderType.cs"
"Assets/Scripts/Network/Protocols/comm/GidStatus.cs"
"Assets/Scripts/Network/Protocols/comm/RetCode.cs"
"Assets/Scripts/Network/Protocols/comm/RoleAcc.cs"
"Assets/Scripts/Network/Protocols/comm/RoleLoc.cs"
"Assets/Scripts/Network/Protocols/comm/RoomMode.cs"
"Assets/Scripts/Network/Protocols/comm/RoomStatus.cs"
"Assets/Scripts/Network/Protocols/cs/AreaInfo.cs"
"Assets/Scripts/Network/Protocols/cs/CardUnlockC.cs"
"Assets/Scripts/Network/Protocols/cs/CardUnlockS.cs"
"Assets/Scripts/Network/Protocols/cs/CardUpdateAction.cs"
"Assets/Scripts/Network/Protocols/cs/CardUpdateS.cs"
"Assets/Scripts/Network/Protocols/cs/CMode.cs"
"Assets/Scripts/Network/Protocols/cs/CSCardInfo.cs"
"Assets/Scripts/Network/Protocols/cs/CSCmd.cs"
"Assets/Scripts/Network/Protocols/cs/CSCustomRoomBaseInfo.cs"
"Assets/Scripts/Network/Protocols/cs/CSCustomRoomInfo.cs"
"Assets/Scripts/Network/Protocols/cs/CSCustomRoomPlayerInfo.cs"
"Assets/Scripts/Network/Protocols/cs/CSHeroInfo.cs"
"Assets/Scripts/Network/Protocols/cs/CustomRoomAddAIC.cs"
"Assets/Scripts/Network/Protocols/cs/CustomRoomAddAIS.cs"
"Assets/Scripts/Network/Protocols/cs/CustomRoomChangePosC.cs"
"Assets/Scripts/Network/Protocols/cs/CustomRoomChangePosS.cs"
"Assets/Scripts/Network/Protocols/cs/CustomRoomCreateC.cs"
"Assets/Scripts/Network/Protocols/cs/CustomRoomCreateS.cs"
"Assets/Scripts/Network/Protocols/cs/CustomRoomExitC.cs"
"Assets/Scripts/Network/Protocols/cs/CustomRoomExitS.cs"
"Assets/Scripts/Network/Protocols/cs/CustomRoomInfoC.cs"
"Assets/Scripts/Network/Protocols/cs/CustomRoomInfoS.cs"
"Assets/Scripts/Network/Protocols/cs/CustomRoomJoinC.cs"
"Assets/Scripts/Network/Protocols/cs/CustomRoomJoinS.cs"
"Assets/Scripts/Network/Protocols/cs/CustomRoomListC.cs"
"Assets/Scripts/Network/Protocols/cs/CustomRoomListS.cs"
"Assets/Scripts/Network/Protocols/cs/CustomRoomStartC.cs"
"Assets/Scripts/Network/Protocols/cs/CustomRoomStartS.cs"
"Assets/Scripts/Network/Protocols/cs/CustomRoomUpdateReason.cs"
"Assets/Scripts/Network/Protocols/cs/EMode.cs"
"Assets/Scripts/Network/Protocols/cs/GameEndS.cs"
"Assets/Scripts/Network/Protocols/cs/GameInfo.cs"
"Assets/Scripts/Network/Protocols/cs/GamePlayer.cs"
"Assets/Scripts/Network/Protocols/cs/GamePlayerStat.cs"
"Assets/Scripts/Network/Protocols/cs/GameResult.cs"
"Assets/Scripts/Network/Protocols/cs/GameStartInfo.cs"
"Assets/Scripts/Network/Protocols/cs/GameStartS.cs"
"Assets/Scripts/Network/Protocols/cs/GetAreaListC.cs"
"Assets/Scripts/Network/Protocols/cs/GetAreaListS.cs"
"Assets/Scripts/Network/Protocols/cs/GetCardGroupC.cs"
"Assets/Scripts/Network/Protocols/cs/GetCardGroupS.cs"
"Assets/Scripts/Network/Protocols/cs/GetCardListC.cs"
"Assets/Scripts/Network/Protocols/cs/GetCardListS.cs"
"Assets/Scripts/Network/Protocols/cs/GetCmdSeqC.cs"
"Assets/Scripts/Network/Protocols/cs/GetCmdSeqS.cs"
"Assets/Scripts/Network/Protocols/cs/GetGameInfoC.cs"
"Assets/Scripts/Network/Protocols/cs/GetGameInfoS.cs"
"Assets/Scripts/Network/Protocols/cs/GetGidsC.cs"
"Assets/Scripts/Network/Protocols/cs/GetGidsS.cs"
"Assets/Scripts/Network/Protocols/cs/GetHeroListC.cs"
"Assets/Scripts/Network/Protocols/cs/GetHeroListS.cs"
"Assets/Scripts/Network/Protocols/cs/GetHistoryRoundListC.cs"
"Assets/Scripts/Network/Protocols/cs/GetHistoryRoundListS.cs"
"Assets/Scripts/Network/Protocols/cs/GetRoleBaseAttrC.cs"
"Assets/Scripts/Network/Protocols/cs/GetRoleBaseAttrS.cs"
"Assets/Scripts/Network/Protocols/cs/GetRoleC.cs"
"Assets/Scripts/Network/Protocols/cs/GetRoleExtAttrC.cs"
"Assets/Scripts/Network/Protocols/cs/GetRoleExtAttrS.cs"
"Assets/Scripts/Network/Protocols/cs/GetRoleS.cs"
"Assets/Scripts/Network/Protocols/cs/GetRoleSimpleC.cs"
"Assets/Scripts/Network/Protocols/cs/GetRoleSimpleS.cs"
"Assets/Scripts/Network/Protocols/cs/GidInfo.cs"
"Assets/Scripts/Network/Protocols/cs/GMC.cs"
"Assets/Scripts/Network/Protocols/cs/GMS.cs"
"Assets/Scripts/Network/Protocols/cs/HeartbeatC.cs"
"Assets/Scripts/Network/Protocols/cs/HeartbeatS.cs"
"Assets/Scripts/Network/Protocols/cs/HeroUnlockC.cs"
"Assets/Scripts/Network/Protocols/cs/HeroUnlockS.cs"
"Assets/Scripts/Network/Protocols/cs/HeroUpdateAction.cs"
"Assets/Scripts/Network/Protocols/cs/HeroUpdateS.cs"
"Assets/Scripts/Network/Protocols/cs/HistoryRoundSimple.cs"
"Assets/Scripts/Network/Protocols/cs/KickOffS.cs"
"Assets/Scripts/Network/Protocols/cs/RoleBaseAttr.cs"
"Assets/Scripts/Network/Protocols/cs/RoleCmdSeq.cs"
"Assets/Scripts/Network/Protocols/cs/RoleCmdSeqItem.cs"
"Assets/Scripts/Network/Protocols/cs/RoleExtAttr.cs"
"Assets/Scripts/Network/Protocols/cs/RoleSimpleDataBase.cs"
"Assets/Scripts/Network/Protocols/cs/RoleSimpleDataExt.cs"
"Assets/Scripts/Network/Protocols/cs/RoleSimpleDataType.cs"
"Assets/Scripts/Network/Protocols/cs/SetCardGroupC.cs"
"Assets/Scripts/Network/Protocols/cs/SetCardGroupS.cs"
"Assets/Scripts/Network/Protocols/cs/StartupS.cs"
"Assets/Scripts/Network/Protocols/cs/UpdateClientDataC.cs"
"Assets/Scripts/Network/Protocols/cs/UpdateClientDataS.cs"
"Assets/Scripts/Network/Protocols/Framework/Ack.cs"
"Assets/Scripts/Network/Protocols/Framework/Any.cs"
"Assets/Scripts/Network/Protocols/Framework/CheckLatency.cs"
"Assets/Scripts/Network/Protocols/Framework/Color.cs"
"Assets/Scripts/Network/Protocols/Framework/Equip.cs"
"Assets/Scripts/Network/Protocols/Framework/Fin.cs"
"Assets/Scripts/Network/Protocols/Framework/HeartBeat.cs"
"Assets/Scripts/Network/Protocols/Framework/Syn.cs"
"Assets/Scripts/Network/Protocols/Framework/SyncTime.cs"
"Assets/Scripts/Network/Protocols/Framework/Vec1.cs"
"Assets/Scripts/Network/Protocols/Framework/Vec2.cs"
"Assets/Scripts/Network/Protocols/Framework/Vec3.cs"
"Assets/Scripts/Network/Protocols/Framework/Weapon.cs"
"Assets/Scripts/Network/Protocols/PtoHelper_Lobby.cs"
"Assets/Scripts/Network/Protocols/PtoHelper_Room.cs"
"Assets/Scripts/Network/Protocols/Room/CachedFrameInputUnit.cs"
"Assets/Scripts/Network/Protocols/Room/CreateRoomReq.cs"
"Assets/Scripts/Network/Protocols/Room/EnterRoomReq.cs"
"Assets/Scripts/Network/Protocols/Room/FrameInputUnit.cs"
"Assets/Scripts/Network/Protocols/Room/GameStartReq.cs"
"Assets/Scripts/Network/Protocols/Room/GameStartRsp.cs"
"Assets/Scripts/Network/Protocols/Room/JoinRoomRsp.cs"
"Assets/Scripts/Network/Protocols/Room/ListRoomReq.cs"
"Assets/Scripts/Network/Protocols/Room/ListRoomRsp.cs"
"Assets/Scripts/Network/Protocols/Room/RoomAddAIReq.cs"
"Assets/Scripts/Network/Protocols/Room/RoomAddAIRsp.cs"
"Assets/Scripts/Network/Protocols/Room/RoomBaseInfo.cs"
"Assets/Scripts/Network/Protocols/Room/RoomBatchFrameReq.cs"
"Assets/Scripts/Network/Protocols/Room/RoomBatchFrameRsp.cs"
"Assets/Scripts/Network/Protocols/Room/RoomEnterReq.cs"
"Assets/Scripts/Network/Protocols/Room/RoomEnterRsp.cs"
"Assets/Scripts/Network/Protocols/Room/RoomEnterWay.cs"
"Assets/Scripts/Network/Protocols/Room/RoomExitReq.cs"
"Assets/Scripts/Network/Protocols/Room/RoomExitRsp.cs"
"Assets/Scripts/Network/Protocols/Room/RoomFinishReq.cs"
"Assets/Scripts/Network/Protocols/Room/RoomFrameCheckReq.cs"
"Assets/Scripts/Network/Protocols/Room/RoomFrameCheckRsp.cs"
"Assets/Scripts/Network/Protocols/Room/RoomFrameInput.cs"
"Assets/Scripts/Network/Protocols/Room/RoomFrameSync.cs"
"Assets/Scripts/Network/Protocols/Room/RoomHeartbeatReq.cs"
"Assets/Scripts/Network/Protocols/Room/RoomHeartbeatRsp.cs"
"Assets/Scripts/Network/Protocols/Room/RoomInfo.cs"
"Assets/Scripts/Network/Protocols/Room/RoomInfoReq.cs"
"Assets/Scripts/Network/Protocols/Room/RoomInfoSync.cs"
"Assets/Scripts/Network/Protocols/Room/RoomLoadingProgress.cs"
"Assets/Scripts/Network/Protocols/Room/RoomLoadingSync.cs"
"Assets/Scripts/Network/Protocols/Room/RoomPlayerInfo.cs"
"Assets/Scripts/Network/Protocols/Room/RoomPlayerProgress.cs"
"Assets/Scripts/Network/Protocols/Room/RoomPlayerStatusSync.cs"
"Assets/Scripts/Network/Protocols/Room/RoomReq.cs"
"Assets/Scripts/Network/Protocols/Room/RoomReqBody.cs"
"Assets/Scripts/Network/Protocols/Room/RoomResultSync.cs"
"Assets/Scripts/Network/Protocols/Room/RoomRsp.cs"
"Assets/Scripts/Network/Protocols/Room/RoomRspBody.cs"
"Assets/Scripts/Network/Protocols/Room/RoomSetCardGroupReq.cs"
"Assets/Scripts/Network/Protocols/Room/RoomSetCardGroupRsp.cs"
"Assets/Scripts/Network/Protocols/Room/RoomStartReq.cs"
"Assets/Scripts/Network/Protocols/Room/RoomStartRsp.cs"
"Assets/Scripts/Network/Protocols/Room/RoomStatusSync.cs"
"Assets/Scripts/Network/Protocols/Room/SyncRoomInfoRsp.cs"
"Assets/Scripts/Network/Protocols/Room/UnitCardInfo.cs"
"Assets/Scripts/Network/Protocols/Room/UpdateRoomInfoReq.cs"
"Assets/StandaloneFileBrowser/IStandaloneFileBrowser.cs"
"Assets/StandaloneFileBrowser/Sample/BasicSample.cs"
"Assets/StandaloneFileBrowser/Sample/CanvasSampleOpenFileImage.cs"
"Assets/StandaloneFileBrowser/Sample/CanvasSampleOpenFileText.cs"
"Assets/StandaloneFileBrowser/Sample/CanvasSampleOpenFileTextMultiple.cs"
"Assets/StandaloneFileBrowser/Sample/CanvasSampleSaveFileImage.cs"
"Assets/StandaloneFileBrowser/Sample/CanvasSampleSaveFileText.cs"
"Assets/StandaloneFileBrowser/StandaloneFileBrowser.cs"
"Assets/StandaloneFileBrowser/StandaloneFileBrowserEditor.cs"
"Assets/StandaloneFileBrowser/StandaloneFileBrowserLinux.cs"
"Assets/StandaloneFileBrowser/StandaloneFileBrowserMac.cs"
"Assets/StandaloneFileBrowser/StandaloneFileBrowserWindows.cs"
"Assets/StylizedTanks_StarterKit/Scripts/TankController.cs"
"Assets/StylizedTanks_StarterKit/Scripts/TankControllerSkinned.cs"
"Assets/Tools/TextureUnpacker/TextureUnpacker.cs"
"Assets/TutorialInfo/Scripts/Readme.cs"
"Assets/Unluck Software/Demo Resources/Scripts/EnableSelectedGameObject.cs"
"Assets/Unluck Software/Demo Resources/Scripts/HideCursor.cs"
"Assets/Unluck Software/Demo Resources/Scripts/MoveInACircle.cs"
"Assets/Unluck Software/Demo Resources/Scripts/SmoothCameraOrbit.cs"
"Assets/Unluck Software/Scripts/AnimatedLight.cs"
-langversion:9.0
/unsafe+
/deterministic
/optimize-
/debug:portable
/nologo
/RuntimeMetadataVersion:v4.0.30319
/nowarn:0169
/nowarn:0649
/nowarn:0282
/nowarn:1701
/nowarn:1702
/utf8output
/preferreduilang:en-US
/additionalfile:"Library/Bee/artifacts/1300b0aEDbg.dag/Assembly-CSharp.UnityAdditionalFile.txt"
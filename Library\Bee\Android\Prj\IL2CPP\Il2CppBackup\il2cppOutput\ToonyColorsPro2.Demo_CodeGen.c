﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void TCP2_Demo_InvertedMaskImage_get_materialForRendering_mE6D20D0BF359E6A29A4463D80DDDE635A3A64676 (void);
extern void TCP2_Demo_InvertedMaskImage__ctor_mBF9F07418B0A1488CB00805F84295B20BBBA8F43 (void);
extern void TCP2_Demo_AutoRotate_Update_m983B3CDA6BC4C2C2A12AC0AA959E165E7F63A14A (void);
extern void TCP2_Demo_AutoRotate__ctor_m9D9B08BA53910C031D365C2D4F5F1459E49C07F7 (void);
extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_m7EFFDA9D626A1B28484D88FB2A017868621C9986 (void);
extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_m6D5CE17ABCBA7F5477680D6504B602B42A9D966D (void);
extern void TCP2_Demo_Interactive_Content__ctor_mCF833CA38BB27622809CDEDFF284848797ADC474 (void);
extern void TCP2_Demo_Camera_Awake_m857DAA5D39F5EA96A0F960E8AC34C808A0EACDA2 (void);
extern void TCP2_Demo_Camera_OnEnable_m0887A044219D30A034E5161177AFE14F208E79D3 (void);
extern void TCP2_Demo_Camera_Update_mB8ECA5C4116C1205E93147D5450F4D4E3BA1027D (void);
extern void TCP2_Demo_Camera_ResetView_mC8F7614A51119FFA3DA106C260B5F228FAFFBEC4 (void);
extern void TCP2_Demo_Camera__ctor_mD04CF39BBB59E4BE650E48041DFF845C539CBFAD (void);
extern void TCP2_Demo_Cat_get_rotateLights_m3A32B1CC6DE916DB9BD1716B3FAF44D0DAA1DF43 (void);
extern void TCP2_Demo_Cat_set_rotateLights_m4B2F94CFC770E4CAF8A59FDCC8424254648B7050 (void);
extern void TCP2_Demo_Cat_get_rotatePointLights_mAE55D513CB5B0105AC0384264BD2427DA98EB75F (void);
extern void TCP2_Demo_Cat_set_rotatePointLights_m8CAB1BDBC35823CD2865B0DC31D31AA19DC4E1EB (void);
extern void TCP2_Demo_Cat_Awake_m05ABDCEE2044551D64C786468002A5CF545023C0 (void);
extern void TCP2_Demo_Cat_Update_mF8B01AC1CD595A65A70EF5E400179F86CE29EB2E (void);
extern void TCP2_Demo_Cat_SetAmbience_m397FAFA3DFB308432E9DBA3D8BDEB775BE0A3E75 (void);
extern void TCP2_Demo_Cat_SetStyle_m16AB7E5F7AE90AF427A7505E8A7EC16CE4AC8184 (void);
extern void TCP2_Demo_Cat_SetFlat_mE5C6D1579A1B929713DBEED2E6FE482F9F90885C (void);
extern void TCP2_Demo_Cat_SetCat_mEEF87FCB5B591B11542C0F761BB6C55ECE64F3EA (void);
extern void TCP2_Demo_Cat_SetLightShadows_m5E18AE338416D93893C3A5A33B8C03DD1E4A9DBA (void);
extern void TCP2_Demo_Cat_SetAnimation_mB10EF283487405C5CF920562B7AF074B932BCC71 (void);
extern void TCP2_Demo_Cat_SetAnimationSpeed_mE6D2A9050BD9821B19FE265FEBFC7E0EDC693BCD (void);
extern void TCP2_Demo_Cat_PauseUnpauseAnimation_m925FF5D515C6E3CC83442DDC0A653B21C1F9258A (void);
extern void TCP2_Demo_Cat_UpdateAnimSpeed_m29E412E2CFC17CBD9B84B5C8C59587EA82FA2578 (void);
extern void TCP2_Demo_Cat_PlayCurrentAnimation_mC37138636FD93A65612133FCEB0F04C587209BA3 (void);
extern void TCP2_Demo_Cat__ctor_m99BF76C49D02A71537FF4709BB795FAAE01CD63F (void);
extern void Ambience__ctor_m5601C23B50F4280C794D44D7ED78AE71E7DE2F1B (void);
extern void ShaderStyle__ctor_mBEFF2326F14897001F92CEE9909CEB837058E79A (void);
extern void CharacterSettings__ctor_mE4058A189A3E15DB0FA8FFF3152435316F8A2D6F (void);
extern void TCP2_Demo_GammaLinear__ctor_m4E65CE4583DE6CBEE0A9F26C7C8AE1601E514E34 (void);
extern void LightSettings__ctor_m8D3F5D85F351467D26C45EA94F3120FFC51A09AD (void);
extern void MaterialSettings__ctor_m52F4DB8DA37A9F9A4D19A9EA764A8510AB05606B (void);
extern void TCP2_Demo_AnimateMaterial_Awake_m42FE1D1A0566C0CF7B12EA402860F251CB9AD00F (void);
extern void TCP2_Demo_AnimateMaterial_Update_mB1520538DA9AD39A8077F97383B739BEEE94E9C4 (void);
extern void TCP2_Demo_AnimateMaterial__ctor_m316A06AED413AFBB4E9BF8E9DB06D565ACBA8529 (void);
extern void AnimatedProperty_Init_mF9E052E9520363DDFE2799CC180A5C02E9AC8F48 (void);
extern void AnimatedProperty_Update_mEE0EC4A2D2F47211D74ECBA00487BCCCF3437668 (void);
extern void AnimatedProperty__ctor_m4CCD97DFFE803EC8559AF20811287A5FC5B0844C (void);
extern void TCP2_Demo_Interactive_Awake_m0114F7A0BC3A752F157A807E5F70D93757318C33 (void);
extern void TCP2_Demo_Interactive_LateUpdate_m79A81E79CDC4A1D51D7F141586D39F75F97CFF85 (void);
extern void TCP2_Demo_Interactive_HandleKeyboard_m8A1EFA7533315A83902C0BBFCCDB2AB84348E309 (void);
extern void TCP2_Demo_Interactive_PrevHighlight_m21348200BDBD1B4E01B8957FB4792AD4A6E0BB93 (void);
extern void TCP2_Demo_Interactive_NextHighlight_m7E479C0F3BCE3E038CE39ABC6E6CF17514F48B6F (void);
extern void TCP2_Demo_Interactive_UpdateViewToCurrentContent_m522349C9F6D20CDDC63EA3E3C68F8C9E4A9474B0 (void);
extern void TCP2_Demo_Interactive_PlaceLine_mDA643C21BA431A12B31099C50538E6A05E04B7E3 (void);
extern void TCP2_Demo_Interactive_ResetView_mDA99336859AA2946D2D90ECFD805CB4C1FE13AEA (void);
extern void TCP2_Demo_Interactive_CR_ResetCamPos_m1072A96E99DB6E7CA74784E16E80DFCD7C8C73D9 (void);
extern void TCP2_Demo_Interactive_CR_MoveToContent_mC42E377253ADE0188F20C2AE122FCEAA3E396790 (void);
extern void TCP2_Demo_Interactive_OnSelectLightingSettings_m5FC680CE96EF10C879ADE1EAFDE68D9B70729B24 (void);
extern void TCP2_Demo_Interactive_HideInfoBox_mB1E2031D5C31BDBEEC3837B9377A4F789D5B3502 (void);
extern void TCP2_Demo_Interactive__ctor_m07AA50DA27BC12C2A03BF410FC0CB7E589B53FD6 (void);
extern void U3CU3Ec__DisplayClass25_0__ctor_m4C6D5957E4AC4E2561D2241FFBFAC517E2E4B825 (void);
extern void U3CU3Ec__DisplayClass25_0_U3CAwakeU3Eb__0_m6278DEF27CF0FB9CC95E295C10FB722AB6CEA0D2 (void);
extern void U3CCR_MoveToContentU3Ed__35__ctor_mE2F7FECAE8FF56B25342F9C3651895198C9B8CE8 (void);
extern void U3CCR_MoveToContentU3Ed__35_System_IDisposable_Dispose_mAFB0146BC13CD2BCCEA173A299C859E10D997B21 (void);
extern void U3CCR_MoveToContentU3Ed__35_MoveNext_m626E54FCCAF61D6B791797D1EDF6D3A6DAF52B85 (void);
extern void U3CCR_MoveToContentU3Ed__35_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m37F75E4132672F55F267C3E8E82637B1AD5F15D3 (void);
extern void U3CCR_MoveToContentU3Ed__35_System_Collections_IEnumerator_Reset_m73046DDC2212AA340B99D7E6858AFF1F8546D46C (void);
extern void U3CCR_MoveToContentU3Ed__35_System_Collections_IEnumerator_get_Current_m0D15BD881C81694302E1B7676B24979DEA8DE762 (void);
extern void U3CCR_ResetCamPosU3Ed__33__ctor_m4E348FAA43126971717AEF9E1EA1EC9FA7022FEA (void);
extern void U3CCR_ResetCamPosU3Ed__33_System_IDisposable_Dispose_mA34BBC37E7CF77DE2274E4E3B870DD1CF9CB03C6 (void);
extern void U3CCR_ResetCamPosU3Ed__33_MoveNext_m76DBA9415DE3CFF9BE3165960CF42B19BDB46845 (void);
extern void U3CCR_ResetCamPosU3Ed__33_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m5BA64DBE87F8C31F4908BDC9EB61306F51BA8293 (void);
extern void U3CCR_ResetCamPosU3Ed__33_System_Collections_IEnumerator_Reset_m2EBC8F50A8E4F83637CC47BE0BF9C544A6FF6DC4 (void);
extern void U3CCR_ResetCamPosU3Ed__33_System_Collections_IEnumerator_get_Current_m3037B2710B6A1DD694AA325F7EDF36B8247904ED (void);
extern void TCP2_Demo_Interactive_Environment_ApplyEnvironment_mFD147601DE9207A3B87DC5F46831570B014FFBDA (void);
extern void TCP2_Demo_Interactive_Environment__ctor_mE1E4C2002A9B22C3D7432D867C4709F6497837C3 (void);
extern void TCP2_Demo_Awake_m3294ABAC139F50F30BDEECF45C06649AF53DA843 (void);
extern void TCP2_Demo_OnDestroy_mB298D4E04DF785BB2DB66A419CFD161B5CD0B477 (void);
extern void TCP2_Demo_OnGUI_m2479623D671CE00FECE654672FC6D01FD55E1F43 (void);
extern void TCP2_Demo_UnityDiffuseShader_m492FFDFBD098665C2CB6FD68E37FDBC30A2CBA41 (void);
extern void TCP2_Demo_UpdateShader_m6107CAFF5694DFD7EE72B7510F2845F163040447 (void);
extern void TCP2_Demo_RimOutlineColor_mB9073DEFAFBA780557D809A23CDE4DBDD972784E (void);
extern void TCP2_Demo_RestoreRimColors_mD17485C1C6B9F80EA2994CF5833D11AB83327368 (void);
extern void TCP2_Demo_ToggleKeyword_m6729BD38B5A46D7D9EBE59A4025CF580B7A681C2 (void);
extern void TCP2_Demo_PrevRamp_m2B41EACFEDCACDA2AC2DCE7F027C94D2C557ABE7 (void);
extern void TCP2_Demo_NextRamp_m407C9F468599A8CA1074295F3C629B1828CDE08F (void);
extern void TCP2_Demo__ctor_mF61E268BFBB5C0D9912849784F9502B676FB999C (void);
extern void TCP2_Demo_PBS_set_ShowPointLights_m24769D241AD8F04C601AF9F8A129750879114FD9 (void);
extern void TCP2_Demo_PBS_set_ShowDirLight_mBF1ECD3FCA37C4E8CCB74A142B2C6E1C8D288361 (void);
extern void TCP2_Demo_PBS_get_RotatePointLights_mAD419152B7082B0AA6A692D04CAA65289A4FA016 (void);
extern void TCP2_Demo_PBS_set_RotatePointLights_m4A9CD8A884F44611B83B1B48A3AF714F5871EF34 (void);
extern void TCP2_Demo_PBS_get_UseOutline_m42513C1C500DC8C199DA6180D7FB61D8FE6BA5DA (void);
extern void TCP2_Demo_PBS_set_UseOutline_m922838EFD87394E33892516B08DEB02535F3BE95 (void);
extern void TCP2_Demo_PBS_set_UseRampTexture_mF3DAF18452E81786D57D78F4392C2D41E0C898E9 (void);
extern void TCP2_Demo_PBS_set_UseStylizedFresnel_mCF9F9FF04E3F3BD15D70202255317618BA9E30EF (void);
extern void TCP2_Demo_PBS_set_UseStylizedSpecular_m29A48B7517A061F186569994727BBD0921BACF95 (void);
extern void TCP2_Demo_PBS_Awake_m1FEB07A01A8CE4B62C9F2E908FCB09F252701E06 (void);
extern void TCP2_Demo_PBS_Update_m2CB1BDD7225E5AC803A2C9CA0B9F27FB806DBA8C (void);
extern void TCP2_Demo_PBS_ToggleShader_m2188612EBF385176C4D5EEBC9B9B1ECDD8625CEA (void);
extern void TCP2_Demo_PBS_NextSky_m39A3219BE60CEFF3BB0081F15157ABBDDF2D279A (void);
extern void TCP2_Demo_PBS_PrevSky_m150E67BF9EF37E599E664D7D4E554292C4BD3501 (void);
extern void TCP2_Demo_PBS_NextRamp_mD8D4CA91CC11CF3A540965794E6E2BFD853101E3 (void);
extern void TCP2_Demo_PBS_PrevRamp_m9F1E6B16E6E825C67B66F77329DF1C97D7F17C06 (void);
extern void TCP2_Demo_PBS_SetMetallic_mF987D3B79EC7FC9A6A2E9E457030AD9AA34E2832 (void);
extern void TCP2_Demo_PBS_SetSmoothness_mEFB2C622B1F49902DDFC988F41CF6FEF58911430 (void);
extern void TCP2_Demo_PBS_SetBumpScale_mE8C6C490B5B333CD76D7EA43B8B792F5770B021A (void);
extern void TCP2_Demo_PBS_SetRampThreshold_m91E8389CD97EC4249427125B8642000394819A7C (void);
extern void TCP2_Demo_PBS_SetRampSmooth_mA177A621D665CE068154F34D6A2F51A9B603F902 (void);
extern void TCP2_Demo_PBS_SetRampSmoothAdd_mA1361D93117BCEAC8E662A81D7AE06BAE52E297F (void);
extern void TCP2_Demo_PBS_UpdateRamp_mC6AE87D30B3D0D71B5EAC5B343B62D514532D808 (void);
extern void TCP2_Demo_PBS_UpdateSky_mAD73EAC90CAE1D6FE1711E3A8D57E4827075CD35 (void);
extern void TCP2_Demo_PBS_ShowUnityStandardShader_mC3F7B50DF880F795A2C86A6B5D9BFFDD6E259A0B (void);
extern void TCP2_Demo_PBS_ShowTCP2Shader_mA8739E0DB5AD7199E158A17E069CC1882EAB163A (void);
extern void TCP2_Demo_PBS_ToggleKeyword_m3D2B850C875DFECD5AB1497C7913AF48AF132CE2 (void);
extern void TCP2_Demo_PBS__ctor_m2C540100E5261B87AE4C5C6979EFFF208E5C4B0F (void);
extern void SkyboxSetting__ctor_m73629F7CFC27397153C1CD9120B4CBE00CEEFAA2 (void);
extern void TCP2_Demo_PBS_View_Awake_mEEDB188CE7229F3C10484432BBBF8382303C0E15 (void);
extern void TCP2_Demo_PBS_View_OnEnable_m47F79D8CF5AB1D6B2BF30136C6B9CD465D66A334 (void);
extern void TCP2_Demo_PBS_View_Update_m441B2976ED65CCD053E4ADB3372DE71B016C31E0 (void);
extern void TCP2_Demo_PBS_View_ResetView_m795A13C2B8C62A8D2C2E7637D878C1E12C708268 (void);
extern void TCP2_Demo_PBS_View__ctor_m1F6B1483468B6B8769250BA6D84DE82F80AD5729 (void);
extern void TCP2_Demo_View_Awake_mA3E864058BF32F08DE33DA8A6093956458353930 (void);
extern void TCP2_Demo_View_OnEnable_m2597D7959C06F2D95452078133E965C05E14E1DE (void);
extern void TCP2_Demo_View_Update_mE96BC9CF10AB3CDD9B4A91EB3ADFB83FFCDAE1D9 (void);
extern void TCP2_Demo_View_ResetView_m14BB208732E55F37B2B6070EFB16CBD117751C11 (void);
extern void TCP2_Demo_View__ctor_m25096E9308B50284FD61A4542948EE724311D773 (void);
static Il2CppMethodPointer s_methodPointers[120] = 
{
	TCP2_Demo_InvertedMaskImage_get_materialForRendering_mE6D20D0BF359E6A29A4463D80DDDE635A3A64676,
	TCP2_Demo_InvertedMaskImage__ctor_mBF9F07418B0A1488CB00805F84295B20BBBA8F43,
	TCP2_Demo_AutoRotate_Update_m983B3CDA6BC4C2C2A12AC0AA959E165E7F63A14A,
	TCP2_Demo_AutoRotate__ctor_m9D9B08BA53910C031D365C2D4F5F1459E49C07F7,
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_m7EFFDA9D626A1B28484D88FB2A017868621C9986,
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_m6D5CE17ABCBA7F5477680D6504B602B42A9D966D,
	TCP2_Demo_Interactive_Content__ctor_mCF833CA38BB27622809CDEDFF284848797ADC474,
	TCP2_Demo_Camera_Awake_m857DAA5D39F5EA96A0F960E8AC34C808A0EACDA2,
	TCP2_Demo_Camera_OnEnable_m0887A044219D30A034E5161177AFE14F208E79D3,
	TCP2_Demo_Camera_Update_mB8ECA5C4116C1205E93147D5450F4D4E3BA1027D,
	TCP2_Demo_Camera_ResetView_mC8F7614A51119FFA3DA106C260B5F228FAFFBEC4,
	TCP2_Demo_Camera__ctor_mD04CF39BBB59E4BE650E48041DFF845C539CBFAD,
	TCP2_Demo_Cat_get_rotateLights_m3A32B1CC6DE916DB9BD1716B3FAF44D0DAA1DF43,
	TCP2_Demo_Cat_set_rotateLights_m4B2F94CFC770E4CAF8A59FDCC8424254648B7050,
	TCP2_Demo_Cat_get_rotatePointLights_mAE55D513CB5B0105AC0384264BD2427DA98EB75F,
	TCP2_Demo_Cat_set_rotatePointLights_m8CAB1BDBC35823CD2865B0DC31D31AA19DC4E1EB,
	TCP2_Demo_Cat_Awake_m05ABDCEE2044551D64C786468002A5CF545023C0,
	TCP2_Demo_Cat_Update_mF8B01AC1CD595A65A70EF5E400179F86CE29EB2E,
	TCP2_Demo_Cat_SetAmbience_m397FAFA3DFB308432E9DBA3D8BDEB775BE0A3E75,
	TCP2_Demo_Cat_SetStyle_m16AB7E5F7AE90AF427A7505E8A7EC16CE4AC8184,
	TCP2_Demo_Cat_SetFlat_mE5C6D1579A1B929713DBEED2E6FE482F9F90885C,
	TCP2_Demo_Cat_SetCat_mEEF87FCB5B591B11542C0F761BB6C55ECE64F3EA,
	TCP2_Demo_Cat_SetLightShadows_m5E18AE338416D93893C3A5A33B8C03DD1E4A9DBA,
	TCP2_Demo_Cat_SetAnimation_mB10EF283487405C5CF920562B7AF074B932BCC71,
	TCP2_Demo_Cat_SetAnimationSpeed_mE6D2A9050BD9821B19FE265FEBFC7E0EDC693BCD,
	TCP2_Demo_Cat_PauseUnpauseAnimation_m925FF5D515C6E3CC83442DDC0A653B21C1F9258A,
	TCP2_Demo_Cat_UpdateAnimSpeed_m29E412E2CFC17CBD9B84B5C8C59587EA82FA2578,
	TCP2_Demo_Cat_PlayCurrentAnimation_mC37138636FD93A65612133FCEB0F04C587209BA3,
	TCP2_Demo_Cat__ctor_m99BF76C49D02A71537FF4709BB795FAAE01CD63F,
	Ambience__ctor_m5601C23B50F4280C794D44D7ED78AE71E7DE2F1B,
	ShaderStyle__ctor_mBEFF2326F14897001F92CEE9909CEB837058E79A,
	CharacterSettings__ctor_mE4058A189A3E15DB0FA8FFF3152435316F8A2D6F,
	TCP2_Demo_GammaLinear__ctor_m4E65CE4583DE6CBEE0A9F26C7C8AE1601E514E34,
	LightSettings__ctor_m8D3F5D85F351467D26C45EA94F3120FFC51A09AD,
	MaterialSettings__ctor_m52F4DB8DA37A9F9A4D19A9EA764A8510AB05606B,
	TCP2_Demo_AnimateMaterial_Awake_m42FE1D1A0566C0CF7B12EA402860F251CB9AD00F,
	TCP2_Demo_AnimateMaterial_Update_mB1520538DA9AD39A8077F97383B739BEEE94E9C4,
	TCP2_Demo_AnimateMaterial__ctor_m316A06AED413AFBB4E9BF8E9DB06D565ACBA8529,
	AnimatedProperty_Init_mF9E052E9520363DDFE2799CC180A5C02E9AC8F48,
	AnimatedProperty_Update_mEE0EC4A2D2F47211D74ECBA00487BCCCF3437668,
	AnimatedProperty__ctor_m4CCD97DFFE803EC8559AF20811287A5FC5B0844C,
	TCP2_Demo_Interactive_Awake_m0114F7A0BC3A752F157A807E5F70D93757318C33,
	TCP2_Demo_Interactive_LateUpdate_m79A81E79CDC4A1D51D7F141586D39F75F97CFF85,
	TCP2_Demo_Interactive_HandleKeyboard_m8A1EFA7533315A83902C0BBFCCDB2AB84348E309,
	TCP2_Demo_Interactive_PrevHighlight_m21348200BDBD1B4E01B8957FB4792AD4A6E0BB93,
	TCP2_Demo_Interactive_NextHighlight_m7E479C0F3BCE3E038CE39ABC6E6CF17514F48B6F,
	TCP2_Demo_Interactive_UpdateViewToCurrentContent_m522349C9F6D20CDDC63EA3E3C68F8C9E4A9474B0,
	TCP2_Demo_Interactive_PlaceLine_mDA643C21BA431A12B31099C50538E6A05E04B7E3,
	TCP2_Demo_Interactive_ResetView_mDA99336859AA2946D2D90ECFD805CB4C1FE13AEA,
	TCP2_Demo_Interactive_CR_ResetCamPos_m1072A96E99DB6E7CA74784E16E80DFCD7C8C73D9,
	TCP2_Demo_Interactive_CR_MoveToContent_mC42E377253ADE0188F20C2AE122FCEAA3E396790,
	TCP2_Demo_Interactive_OnSelectLightingSettings_m5FC680CE96EF10C879ADE1EAFDE68D9B70729B24,
	TCP2_Demo_Interactive_HideInfoBox_mB1E2031D5C31BDBEEC3837B9377A4F789D5B3502,
	TCP2_Demo_Interactive__ctor_m07AA50DA27BC12C2A03BF410FC0CB7E589B53FD6,
	U3CU3Ec__DisplayClass25_0__ctor_m4C6D5957E4AC4E2561D2241FFBFAC517E2E4B825,
	U3CU3Ec__DisplayClass25_0_U3CAwakeU3Eb__0_m6278DEF27CF0FB9CC95E295C10FB722AB6CEA0D2,
	U3CCR_MoveToContentU3Ed__35__ctor_mE2F7FECAE8FF56B25342F9C3651895198C9B8CE8,
	U3CCR_MoveToContentU3Ed__35_System_IDisposable_Dispose_mAFB0146BC13CD2BCCEA173A299C859E10D997B21,
	U3CCR_MoveToContentU3Ed__35_MoveNext_m626E54FCCAF61D6B791797D1EDF6D3A6DAF52B85,
	U3CCR_MoveToContentU3Ed__35_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m37F75E4132672F55F267C3E8E82637B1AD5F15D3,
	U3CCR_MoveToContentU3Ed__35_System_Collections_IEnumerator_Reset_m73046DDC2212AA340B99D7E6858AFF1F8546D46C,
	U3CCR_MoveToContentU3Ed__35_System_Collections_IEnumerator_get_Current_m0D15BD881C81694302E1B7676B24979DEA8DE762,
	U3CCR_ResetCamPosU3Ed__33__ctor_m4E348FAA43126971717AEF9E1EA1EC9FA7022FEA,
	U3CCR_ResetCamPosU3Ed__33_System_IDisposable_Dispose_mA34BBC37E7CF77DE2274E4E3B870DD1CF9CB03C6,
	U3CCR_ResetCamPosU3Ed__33_MoveNext_m76DBA9415DE3CFF9BE3165960CF42B19BDB46845,
	U3CCR_ResetCamPosU3Ed__33_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m5BA64DBE87F8C31F4908BDC9EB61306F51BA8293,
	U3CCR_ResetCamPosU3Ed__33_System_Collections_IEnumerator_Reset_m2EBC8F50A8E4F83637CC47BE0BF9C544A6FF6DC4,
	U3CCR_ResetCamPosU3Ed__33_System_Collections_IEnumerator_get_Current_m3037B2710B6A1DD694AA325F7EDF36B8247904ED,
	TCP2_Demo_Interactive_Environment_ApplyEnvironment_mFD147601DE9207A3B87DC5F46831570B014FFBDA,
	TCP2_Demo_Interactive_Environment__ctor_mE1E4C2002A9B22C3D7432D867C4709F6497837C3,
	TCP2_Demo_Awake_m3294ABAC139F50F30BDEECF45C06649AF53DA843,
	TCP2_Demo_OnDestroy_mB298D4E04DF785BB2DB66A419CFD161B5CD0B477,
	TCP2_Demo_OnGUI_m2479623D671CE00FECE654672FC6D01FD55E1F43,
	TCP2_Demo_UnityDiffuseShader_m492FFDFBD098665C2CB6FD68E37FDBC30A2CBA41,
	TCP2_Demo_UpdateShader_m6107CAFF5694DFD7EE72B7510F2845F163040447,
	TCP2_Demo_RimOutlineColor_mB9073DEFAFBA780557D809A23CDE4DBDD972784E,
	TCP2_Demo_RestoreRimColors_mD17485C1C6B9F80EA2994CF5833D11AB83327368,
	TCP2_Demo_ToggleKeyword_m6729BD38B5A46D7D9EBE59A4025CF580B7A681C2,
	TCP2_Demo_PrevRamp_m2B41EACFEDCACDA2AC2DCE7F027C94D2C557ABE7,
	TCP2_Demo_NextRamp_m407C9F468599A8CA1074295F3C629B1828CDE08F,
	TCP2_Demo__ctor_mF61E268BFBB5C0D9912849784F9502B676FB999C,
	TCP2_Demo_PBS_set_ShowPointLights_m24769D241AD8F04C601AF9F8A129750879114FD9,
	TCP2_Demo_PBS_set_ShowDirLight_mBF1ECD3FCA37C4E8CCB74A142B2C6E1C8D288361,
	TCP2_Demo_PBS_get_RotatePointLights_mAD419152B7082B0AA6A692D04CAA65289A4FA016,
	TCP2_Demo_PBS_set_RotatePointLights_m4A9CD8A884F44611B83B1B48A3AF714F5871EF34,
	TCP2_Demo_PBS_get_UseOutline_m42513C1C500DC8C199DA6180D7FB61D8FE6BA5DA,
	TCP2_Demo_PBS_set_UseOutline_m922838EFD87394E33892516B08DEB02535F3BE95,
	TCP2_Demo_PBS_set_UseRampTexture_mF3DAF18452E81786D57D78F4392C2D41E0C898E9,
	TCP2_Demo_PBS_set_UseStylizedFresnel_mCF9F9FF04E3F3BD15D70202255317618BA9E30EF,
	TCP2_Demo_PBS_set_UseStylizedSpecular_m29A48B7517A061F186569994727BBD0921BACF95,
	TCP2_Demo_PBS_Awake_m1FEB07A01A8CE4B62C9F2E908FCB09F252701E06,
	TCP2_Demo_PBS_Update_m2CB1BDD7225E5AC803A2C9CA0B9F27FB806DBA8C,
	TCP2_Demo_PBS_ToggleShader_m2188612EBF385176C4D5EEBC9B9B1ECDD8625CEA,
	TCP2_Demo_PBS_NextSky_m39A3219BE60CEFF3BB0081F15157ABBDDF2D279A,
	TCP2_Demo_PBS_PrevSky_m150E67BF9EF37E599E664D7D4E554292C4BD3501,
	TCP2_Demo_PBS_NextRamp_mD8D4CA91CC11CF3A540965794E6E2BFD853101E3,
	TCP2_Demo_PBS_PrevRamp_m9F1E6B16E6E825C67B66F77329DF1C97D7F17C06,
	TCP2_Demo_PBS_SetMetallic_mF987D3B79EC7FC9A6A2E9E457030AD9AA34E2832,
	TCP2_Demo_PBS_SetSmoothness_mEFB2C622B1F49902DDFC988F41CF6FEF58911430,
	TCP2_Demo_PBS_SetBumpScale_mE8C6C490B5B333CD76D7EA43B8B792F5770B021A,
	TCP2_Demo_PBS_SetRampThreshold_m91E8389CD97EC4249427125B8642000394819A7C,
	TCP2_Demo_PBS_SetRampSmooth_mA177A621D665CE068154F34D6A2F51A9B603F902,
	TCP2_Demo_PBS_SetRampSmoothAdd_mA1361D93117BCEAC8E662A81D7AE06BAE52E297F,
	TCP2_Demo_PBS_UpdateRamp_mC6AE87D30B3D0D71B5EAC5B343B62D514532D808,
	TCP2_Demo_PBS_UpdateSky_mAD73EAC90CAE1D6FE1711E3A8D57E4827075CD35,
	TCP2_Demo_PBS_ShowUnityStandardShader_mC3F7B50DF880F795A2C86A6B5D9BFFDD6E259A0B,
	TCP2_Demo_PBS_ShowTCP2Shader_mA8739E0DB5AD7199E158A17E069CC1882EAB163A,
	TCP2_Demo_PBS_ToggleKeyword_m3D2B850C875DFECD5AB1497C7913AF48AF132CE2,
	TCP2_Demo_PBS__ctor_m2C540100E5261B87AE4C5C6979EFFF208E5C4B0F,
	SkyboxSetting__ctor_m73629F7CFC27397153C1CD9120B4CBE00CEEFAA2,
	TCP2_Demo_PBS_View_Awake_mEEDB188CE7229F3C10484432BBBF8382303C0E15,
	TCP2_Demo_PBS_View_OnEnable_m47F79D8CF5AB1D6B2BF30136C6B9CD465D66A334,
	TCP2_Demo_PBS_View_Update_m441B2976ED65CCD053E4ADB3372DE71B016C31E0,
	TCP2_Demo_PBS_View_ResetView_m795A13C2B8C62A8D2C2E7637D878C1E12C708268,
	TCP2_Demo_PBS_View__ctor_m1F6B1483468B6B8769250BA6D84DE82F80AD5729,
	TCP2_Demo_View_Awake_mA3E864058BF32F08DE33DA8A6093956458353930,
	TCP2_Demo_View_OnEnable_m2597D7959C06F2D95452078133E965C05E14E1DE,
	TCP2_Demo_View_Update_mE96BC9CF10AB3CDD9B4A91EB3ADFB83FFCDAE1D9,
	TCP2_Demo_View_ResetView_m14BB208732E55F37B2B6070EFB16CBD117751C11,
	TCP2_Demo_View__ctor_m25096E9308B50284FD61A4542948EE724311D773,
};
static const int32_t s_InvokerIndices[120] = 
{
	13052,
	13298,
	13298,
	13298,
	21397,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	12815,
	10442,
	12815,
	10442,
	13298,
	13298,
	10629,
	10629,
	10442,
	10442,
	10442,
	10629,
	10823,
	10442,
	13298,
	10823,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	10682,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	10823,
	2890,
	13298,
	13052,
	9272,
	10629,
	13298,
	13298,
	13298,
	13298,
	10629,
	13298,
	12815,
	13052,
	13298,
	13052,
	10629,
	13298,
	12815,
	13052,
	13298,
	13052,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	2710,
	13298,
	13298,
	13298,
	10442,
	10442,
	12815,
	10442,
	12815,
	10442,
	10442,
	10442,
	10442,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	10823,
	10823,
	10823,
	10823,
	10823,
	10823,
	13298,
	13298,
	13298,
	13298,
	2710,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_ToonyColorsPro2_Demo_CodeGenModule;
const Il2CppCodeGenModule g_ToonyColorsPro2_Demo_CodeGenModule = 
{
	"ToonyColorsPro2.Demo.dll",
	120,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};

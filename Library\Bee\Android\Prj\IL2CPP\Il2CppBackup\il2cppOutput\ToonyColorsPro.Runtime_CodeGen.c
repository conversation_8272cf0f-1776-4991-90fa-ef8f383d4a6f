﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_mD8D6A4F6D912B7AD3ABB42F6285AAA8BFDC13D9C (void);
extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_mB2FE215C5975DA0307EE5A8744A1DDB895EA2A18 (void);
extern void TCP2_CameraDepth_OnEnable_m2DCBD808E3D7E9E183355EECF13CF0FBB5A637D0 (void);
extern void TCP2_CameraDepth_OnValidate_m3B695760C6EB8D61BAE02AEF84A4AE616F88F20B (void);
extern void TCP2_CameraDepth_SetCameraDepth_mB3A224FDCFFAC5AB96B190D30BB2B41E0CE51441 (void);
extern void TCP2_CameraDepth__ctor_mF571A846A30030BB518CC5C990B75BF2728572DF (void);
extern void TCP2_GetPosOnWater_LateUpdate_m20072EE7F7E759C6B2B5AD7DC958D839CD15E74F (void);
extern void TCP2_GetPosOnWater_GetPositionOnWater_mD8E5940462F4FF4F18FF94C754B96068EF77F4CB (void);
extern void TCP2_GetPosOnWater_GetNormalOnWater_mFCFCCF422D5C271B4DB3D451527ADA2026B0F266 (void);
extern void TCP2_GetPosOnWater__ctor_m2AB9931D7BE111125731BE959060CF45965F5F4F (void);
extern void TCP2_GetVertexWavesPosition_LateUpdate_mC364F6282C101FFE2968258946439E008C144F8D (void);
extern void TCP2_GetVertexWavesPosition_CalculateSinePosition_m53AE2DD58DB1AC8DA2A353C178D4F043A6A75D81 (void);
extern void TCP2_GetVertexWavesPosition_CalculateSineNormal_m2CD1B559FB1B6342C0D37CA3CC5DAB8B5678EFEF (void);
extern void TCP2_GetVertexWavesPosition_GetPositionOnWater_SG2_m3AE19B5EE299D432B9CD08ED2B998E3F3D5F0070 (void);
extern void TCP2_GetVertexWavesPosition_GetNormalOnWater_SG2_m68339EE9562658B60D0FEA84A84D227DB0289992 (void);
extern void TCP2_GetVertexWavesPosition__ctor_m72C28EAB273764836C3546E743BC6325401DCE49 (void);
extern void TCP2_GetVertexWavesPosition__cctor_m003CEFE27F1C3005AEED418C3821FAE44583BC59 (void);
extern void TCP2_PlanarReflection_get_ShaderID_ReflectionTex_m56E78BBCE25DAFF87FEE7FD8A2831F918F84B124 (void);
extern void TCP2_PlanarReflection_get_ShaderID_ReflectionDepthTex_mA4D9AB261F8369C429B72CD6DC811B331EA5D0D2 (void);
extern void TCP2_PlanarReflection_get_ShaderID_ReflectivePlaneY_m5BD4FD60BE5D9E4CDC43CDCF7C7FE46BE5B0D205 (void);
extern void TCP2_PlanarReflection_get_ShaderID_ReflectionDepthRange_mE7B06E2DCEED01ACE49728291886D9B9E9C00800 (void);
extern void TCP2_PlanarReflection_get_ShaderID_UseReflectionDepth_mBE5B33FC55A2969E12CFE9C348012732A67DB845 (void);
extern void TCP2_PlanarReflection_OnValidate_mD359A860E6C3CA734D6F679E317B9A87335308D3 (void);
extern void TCP2_PlanarReflection_OnEnable_m410E1A5E0D4211EB67F46AE16D66F129FBCA7DCF (void);
extern void TCP2_PlanarReflection_OnDisable_m10FC5C022E9D909DBCD4EE31457FA4DCEE775BA4 (void);
extern void TCP2_PlanarReflection_UpdateRenderTexture_m68EC26E08F46066F16884B9CA041AACCBB6F87E5 (void);
extern void TCP2_PlanarReflection_ClearRenderTexture_m6EF08D43A78324DA76B09AB04BB2063D5F7B1B0A (void);
extern void TCP2_PlanarReflection_UpdateCommandBuffer_m992291EDB61AEA40C332DA5FC2E53C9E72E58D8F (void);
extern void TCP2_PlanarReflection_ClearCommandBuffer_mE31CD5BC181ECC6CE57A89FE91FC54D7599A40AF (void);
extern void TCP2_PlanarReflection_BeginCameraRendering_Bultin_m40D10D9F9A22782C50A54511880CC575F7C97655 (void);
extern void TCP2_PlanarReflection_BeginCameraRendering_URP_m2551C1514321AD13604A589B5EA33ED4D8B53D25 (void);
extern void TCP2_PlanarReflection_RenderPlanarReflection_mE12045E540118DE967537D65D974D3F5650587E7 (void);
extern void TCP2_PlanarReflection_RenderPlanarReflection_m25DFF719711FC13622E2F44BC6DDB0527B98AD30 (void);
extern void TCP2_PlanarReflection_CameraSpacePlane_mA6702267FBBA6A838A9E4254F1479407E43FA086 (void);
extern void TCP2_PlanarReflection_CalculateReflectionMatrix_m6583F25BD212A78F820579D3C46367BEACA0F755 (void);
extern void TCP2_PlanarReflection__ctor_m87E2C90CA9C833B5B30AED8F8989F93C81552477 (void);
extern void TCP2_PlanarReflection__cctor_m78017627D581CE9DC88156EA0A0590A9F5E69932 (void);
extern void TCP2_RuntimeUtils_GetShaderWithKeywords_mE11554E03F5B2A42F0233E7128B06AF30D30D69A (void);
extern void TCP2_RuntimeUtils__cctor_m2467276D4A212FAFCCEA6835C4CE135F41C67AA4 (void);
extern void TCP2_ShaderUpdateUnityTime_LateUpdate_m8E1AC06A13572836D0D66470EF350465BD0AE355 (void);
extern void TCP2_ShaderUpdateUnityTime__ctor_mDF471253BFD5B945AAA49F1558489D2A816936F2 (void);
extern void TCP2_ShaderUpdateUnityTime__cctor_m3172D7CFE4DA56B6B1265CE3343B76216F5DD2CB (void);
static Il2CppMethodPointer s_methodPointers[42] = 
{
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_mD8D6A4F6D912B7AD3ABB42F6285AAA8BFDC13D9C,
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_mB2FE215C5975DA0307EE5A8744A1DDB895EA2A18,
	TCP2_CameraDepth_OnEnable_m2DCBD808E3D7E9E183355EECF13CF0FBB5A637D0,
	TCP2_CameraDepth_OnValidate_m3B695760C6EB8D61BAE02AEF84A4AE616F88F20B,
	TCP2_CameraDepth_SetCameraDepth_mB3A224FDCFFAC5AB96B190D30BB2B41E0CE51441,
	TCP2_CameraDepth__ctor_mF571A846A30030BB518CC5C990B75BF2728572DF,
	TCP2_GetPosOnWater_LateUpdate_m20072EE7F7E759C6B2B5AD7DC958D839CD15E74F,
	TCP2_GetPosOnWater_GetPositionOnWater_mD8E5940462F4FF4F18FF94C754B96068EF77F4CB,
	TCP2_GetPosOnWater_GetNormalOnWater_mFCFCCF422D5C271B4DB3D451527ADA2026B0F266,
	TCP2_GetPosOnWater__ctor_m2AB9931D7BE111125731BE959060CF45965F5F4F,
	TCP2_GetVertexWavesPosition_LateUpdate_mC364F6282C101FFE2968258946439E008C144F8D,
	TCP2_GetVertexWavesPosition_CalculateSinePosition_m53AE2DD58DB1AC8DA2A353C178D4F043A6A75D81,
	TCP2_GetVertexWavesPosition_CalculateSineNormal_m2CD1B559FB1B6342C0D37CA3CC5DAB8B5678EFEF,
	TCP2_GetVertexWavesPosition_GetPositionOnWater_SG2_m3AE19B5EE299D432B9CD08ED2B998E3F3D5F0070,
	TCP2_GetVertexWavesPosition_GetNormalOnWater_SG2_m68339EE9562658B60D0FEA84A84D227DB0289992,
	TCP2_GetVertexWavesPosition__ctor_m72C28EAB273764836C3546E743BC6325401DCE49,
	TCP2_GetVertexWavesPosition__cctor_m003CEFE27F1C3005AEED418C3821FAE44583BC59,
	TCP2_PlanarReflection_get_ShaderID_ReflectionTex_m56E78BBCE25DAFF87FEE7FD8A2831F918F84B124,
	TCP2_PlanarReflection_get_ShaderID_ReflectionDepthTex_mA4D9AB261F8369C429B72CD6DC811B331EA5D0D2,
	TCP2_PlanarReflection_get_ShaderID_ReflectivePlaneY_m5BD4FD60BE5D9E4CDC43CDCF7C7FE46BE5B0D205,
	TCP2_PlanarReflection_get_ShaderID_ReflectionDepthRange_mE7B06E2DCEED01ACE49728291886D9B9E9C00800,
	TCP2_PlanarReflection_get_ShaderID_UseReflectionDepth_mBE5B33FC55A2969E12CFE9C348012732A67DB845,
	TCP2_PlanarReflection_OnValidate_mD359A860E6C3CA734D6F679E317B9A87335308D3,
	TCP2_PlanarReflection_OnEnable_m410E1A5E0D4211EB67F46AE16D66F129FBCA7DCF,
	TCP2_PlanarReflection_OnDisable_m10FC5C022E9D909DBCD4EE31457FA4DCEE775BA4,
	TCP2_PlanarReflection_UpdateRenderTexture_m68EC26E08F46066F16884B9CA041AACCBB6F87E5,
	TCP2_PlanarReflection_ClearRenderTexture_m6EF08D43A78324DA76B09AB04BB2063D5F7B1B0A,
	TCP2_PlanarReflection_UpdateCommandBuffer_m992291EDB61AEA40C332DA5FC2E53C9E72E58D8F,
	TCP2_PlanarReflection_ClearCommandBuffer_mE31CD5BC181ECC6CE57A89FE91FC54D7599A40AF,
	TCP2_PlanarReflection_BeginCameraRendering_Bultin_m40D10D9F9A22782C50A54511880CC575F7C97655,
	TCP2_PlanarReflection_BeginCameraRendering_URP_m2551C1514321AD13604A589B5EA33ED4D8B53D25,
	TCP2_PlanarReflection_RenderPlanarReflection_mE12045E540118DE967537D65D974D3F5650587E7,
	TCP2_PlanarReflection_RenderPlanarReflection_m25DFF719711FC13622E2F44BC6DDB0527B98AD30,
	TCP2_PlanarReflection_CameraSpacePlane_mA6702267FBBA6A838A9E4254F1479407E43FA086,
	TCP2_PlanarReflection_CalculateReflectionMatrix_m6583F25BD212A78F820579D3C46367BEACA0F755,
	TCP2_PlanarReflection__ctor_m87E2C90CA9C833B5B30AED8F8989F93C81552477,
	TCP2_PlanarReflection__cctor_m78017627D581CE9DC88156EA0A0590A9F5E69932,
	TCP2_RuntimeUtils_GetShaderWithKeywords_mE11554E03F5B2A42F0233E7128B06AF30D30D69A,
	TCP2_RuntimeUtils__cctor_m2467276D4A212FAFCCEA6835C4CE135F41C67AA4,
	TCP2_ShaderUpdateUnityTime_LateUpdate_m8E1AC06A13572836D0D66470EF350465BD0AE355,
	TCP2_ShaderUpdateUnityTime__ctor_mDF471253BFD5B945AAA49F1558489D2A816936F2,
	TCP2_ShaderUpdateUnityTime__cctor_m3172D7CFE4DA56B6B1265CE3343B76216F5DD2CB,
};
static const int32_t s_InvokerIndices[42] = 
{
	21396,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	9604,
	9604,
	13298,
	13298,
	797,
	797,
	4670,
	4670,
	13298,
	21355,
	21263,
	21263,
	21263,
	21263,
	21263,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	10682,
	5778,
	10682,
	5778,
	1719,
	18516,
	13298,
	21355,
	20515,
	21355,
	13298,
	13298,
	21355,
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_ToonyColorsPro_Runtime_CodeGenModule;
const Il2CppCodeGenModule g_ToonyColorsPro_Runtime_CodeGenModule = 
{
	"ToonyColorsPro.Runtime.dll",
	42,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};

﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_m564F24ACFEA522CC301200C564353DBD40286B07 (void);
extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_mFCEEFC861E4F9EDB450802A81ED0911D6F779F8E (void);
extern void Finger_get_state_m812016FD4AFDECF6F08E0011AFE7E09F4BA93A7D (void);
extern void Finger_set_state_m496CDBF5130ABB33CB2DE4CE3D458DA92A02B18A (void);
extern void Finger_get_lastState_mFE618D5B1F679CD097822524430FCC7AAB61811E (void);
extern void Finger_set_lastState_m632AE93F19BA6FB11C6C38138F852F4C94B12BCA (void);
extern void Finger_get_index_mC06528C915844D87E8DB1AB8282667856733F816 (void);
extern void Finger_set_index_m29E24C2ACFC92E1AE6AF91E9FCADD9B12D253B31 (void);
extern void Finger_get_fingerId_m6DBB39CBC887BAEE6815383E9095FDF7C9EB5DD7 (void);
extern void Finger_set_fingerId_m1FF76B25E1F89C7F8A9EAEA171F3C5338BA94C7C (void);
extern void Finger_get_startPosition_m145EB6FE7F3955A72B6808889D483CE5D3F32A17 (void);
extern void Finger_set_startPosition_mE12A9B74F21D9B66FB9B6F67FEA1D5DE8483B36A (void);
extern void Finger_get_position_mD7E4F25C5EC08239EA13505CED61A2E2A7223E93 (void);
extern void Finger_set_position_m377C1C4ADC62778CA08627A068DA396D4DB04C7B (void);
extern void Finger_get_lastPosition_m6C385CAF630D4AEEC49C233097D5C5A0E4F651F7 (void);
extern void Finger_set_lastPosition_m74327C5F1FF0FA9A7434D7FB7BBC9BFC135FEA2A (void);
extern void Finger_get_deltaPosition_mFF81C99D713BCAD5170BC781EC4A5B39FF2692E3 (void);
extern void Finger_set_deltaPosition_mD5D4C0FA06DB9577FB53FFE7962AA94C662A6C78 (void);
extern void Finger_get_currentStateDuration_mD0A03B3889C5FC15D90D7D482569D44F8949E234 (void);
extern void Finger_set_currentStateDuration_m94BDC11890516A0E7342F2DBB75BDBE4C3848C1C (void);
extern void Finger__ctor_m3705D1A51634F357E3AE9EFAE4E0C1BF185F5A79 (void);
extern void Finger_Reset_m8AA9BB5E549A0C766CE9994C43243CAC507CBE7E (void);
extern void Finger_Update_m8A7DBD8E7C51BEBAC9510DDB6D74631921D0573E (void);
extern void Finger_get_Moved_m49BF5CE5299489209594FFEEAD82F5932854CD68 (void);
extern void Finger_set_Moved_m9CF46413BEAAD5E8E6DD4867E8E99388CEFFF8CE (void);
extern void Finger_get_WasDown_m237B3519DD64E0ECEC87ECBCD3061645862D9D25 (void);
extern void Finger_get_WasMoving_m7ED124C626CFA72C1DCD7C365F529BFD49834570 (void);
extern void Finger_get_WasStationary_mFF49E4D74D9A2848CB787EAB1185EDD0BD94AC99 (void);
extern void Finger_get_IsDown_mE1A0287C740B3E37E75F541F79730C49E31E43CD (void);
extern void Finger_get_IsMoving_m64569640AD76608F9AED5FE4B3A32CB0978DA48F (void);
extern void Finger_get_IsStationary_mEAEBF6AC6728498061C9E7ECC20FF8FA8DE60D6B (void);
extern void FingerTouchEvent_get_finger_m9FE6E136CA29BC49889868DAEE4B764092D28843 (void);
extern void FingerTouchEvent_set_finger_m5415B5FACC66E0442B88CE0371ED517C31B884C3 (void);
extern void FingerTouchEvent__ctor_m3E8F05B22D939D32A0D0E7E8094CD012142D884B (void);
extern void FingerMotionEvent_get_phase_m382EE2A04D7A21A48078F74A4262EBFB290E68A3 (void);
extern void FingerMotionEvent_set_phase_m7BAB2F38CFEE73443835EAE8815CC64BCB7CB782 (void);
extern void FingerMotionEvent__ctor_m8EA10E74A28A021BCDA16851CD99305E67FB7AB3 (void);
extern void BaseInput_ProcessFingerEvent_m24AC7960A0D255CB4660A38436D4ECA5D6696965 (void);
extern void BaseInput_FingerDownDetect_m85C3143AA65DD55F8D814009E69987D6C2128C1E (void);
extern void BaseInput_FingerUpDetect_m85145B7062E17C4E70925B1FF7AE35940DC0C84B (void);
extern void BaseInput_FingerMotionDetect_mCDC757C25A4037792BD59A93CD7A39404315C08D (void);
extern void BaseInput__ctor_mB6868ADDD8A711942038C76BF195BDDF092ADD34 (void);
extern void TouchInput_Update_m06E3A199603EA55BD90766C191DB7329690A1213 (void);
extern void TouchInput__ctor_m6E613CB00B7819C21FE35D7C3CD54CEB961EB376 (void);
extern void MouseInput_Update_mB57E2E8F5293F5C1E7461583DAED83B26D5F7582 (void);
extern void MouseInput__ctor_m32B1FFB988204A5F86316FBB5E255BC01A19D662 (void);
extern void TouchSystem_add_onTouchBegan_mA28750785276F381DAAD08F08988DBA440F0A58F (void);
extern void TouchSystem_remove_onTouchBegan_m113B6467C1144BE2EE7F790768D6E4D29907B858 (void);
extern void TouchSystem_add_onTouchMoved_m3E8BEF833C4D9899E3154CA18E047C98BB38E244 (void);
extern void TouchSystem_remove_onTouchMoved_mBBED6A01619E86B8888668E8B20CE8DFDE1B96EF (void);
extern void TouchSystem_add_onTouchStationary_m1333F178F707435EEFD189A29BE4058E35149AD9 (void);
extern void TouchSystem_remove_onTouchStationary_m0F0C0285C1789CD109B7CEC9775DB750C06B26DF (void);
extern void TouchSystem_add_onTouchEnded_m9729108953ADA35687DFD161187C528CF22E4AF0 (void);
extern void TouchSystem_remove_onTouchEnded_m7C759B5981235F37D2280C3A95DA032D67352D89 (void);
extern void TouchSystem_add_onTouchCanceled_m3460ECB73F0331674084779E2AB41C584B6D7640 (void);
extern void TouchSystem_remove_onTouchCanceled_mF38F32046D4457A8755562A6C5A12A3F83D9E220 (void);
extern void TouchSystem_add_onMouseMove_m9D56943AD0784F26B578D6684F823FE21D20D2A8 (void);
extern void TouchSystem_remove_onMouseMove_mF108DB89D3F374F149A69E5B9A522EEA5FC31C34 (void);
extern void TouchSystem_get_IsEnabled_mA9E66B9FBB3257435EE78472D1AED7E9D2E3ABDA (void);
extern void TouchSystem_set_IsEnabled_mF77C83E99B5BA493D9B9E7EFCD5DE4DE3D87B09F (void);
extern void TouchSystem__ctor_m5A6CE395609E76073CE87AFA629CABF25E48C849 (void);
extern void TouchSystem_Reset_m02A68AE3AA2871E72DD1EBFF973C84715C71F3E3 (void);
extern void TouchSystem_OnTouchBegan_mC66AB49CD896F6A6D269F96B43C347AC2FB87989 (void);
extern void TouchSystem_OnTouchMoved_m2DCD48D75A308C714E5CE32E4D2699998213DF33 (void);
extern void TouchSystem_OnTouchStationary_m6A121B7F5B3168946089FF18D66534AF206C741F (void);
extern void TouchSystem_OnTouchEnded_mEE172ACC2021B4F930507388362C4B8258BB4E2F (void);
extern void TouchSystem_OnTouchCanceled_mC3CF47578D002BAE807B5555CA26950B7A9BD7AB (void);
extern void TouchSystem_OnMouseMove_m29102FF4E1122CCD48DF0713C0384050498E45B1 (void);
extern void TouchSystem_Update_m96A2A02D383B08DE9A4FCB8777885EC17B2ACC00 (void);
extern void TouchSystem_get_IsAnyFingerDown_mD8CC65BEC5775BC68623FA8BC05AF53390068399 (void);
extern void TouchSystem_GetFinger_m68BB32C0E4CF8DE2BD9B2FAE1008A6DA6BE92447 (void);
extern void TouchSystem_GetFingerTouchEvent_m8586CA78A0B7E12ACF5F8165AB114C2DF69CA1C0 (void);
extern void TouchSystem_GetFingerMotionEvent_m5F6EF7B406DE7BE437CDB89235E4F5244DFC875B (void);
extern void TouchSystem_GetFingerByID_m58A4BA1C79F5710B7140450880F0F3EFF247FC8C (void);
extern void FingerEventHandler__ctor_m669822C432A87AC6BBD4722944404DDDA1DD2544 (void);
extern void FingerEventHandler_Invoke_mA20FAE4131B5472E16C4D31CCD3C0C5514F0DAEF (void);
extern void FingerEventHandler_BeginInvoke_mDB3AB14294345203982F8D2A4D7F8C762149BC6E (void);
extern void FingerEventHandler_EndInvoke_m33A342396E28C25FBBDDA3E2EFA692AA6FB3AFE0 (void);
extern void MouseEventHandler__ctor_mEE0BB11D23A343C6A8699F352D29A59C7EF1A773 (void);
extern void MouseEventHandler_Invoke_m7768333417BCEA31AD71AA27D5ED80ED6CB56D49 (void);
extern void MouseEventHandler_BeginInvoke_mD633EAAA68460879E021416D51341C685E88F249 (void);
extern void MouseEventHandler_EndInvoke_m3A755D246163047889D5799FC95E64E1AD17422B (void);
static Il2CppMethodPointer s_methodPointers[85] = 
{
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_m564F24ACFEA522CC301200C564353DBD40286B07,
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_mFCEEFC861E4F9EDB450802A81ED0911D6F779F8E,
	Finger_get_state_m812016FD4AFDECF6F08E0011AFE7E09F4BA93A7D,
	Finger_set_state_m496CDBF5130ABB33CB2DE4CE3D458DA92A02B18A,
	Finger_get_lastState_mFE618D5B1F679CD097822524430FCC7AAB61811E,
	Finger_set_lastState_m632AE93F19BA6FB11C6C38138F852F4C94B12BCA,
	Finger_get_index_mC06528C915844D87E8DB1AB8282667856733F816,
	Finger_set_index_m29E24C2ACFC92E1AE6AF91E9FCADD9B12D253B31,
	Finger_get_fingerId_m6DBB39CBC887BAEE6815383E9095FDF7C9EB5DD7,
	Finger_set_fingerId_m1FF76B25E1F89C7F8A9EAEA171F3C5338BA94C7C,
	Finger_get_startPosition_m145EB6FE7F3955A72B6808889D483CE5D3F32A17,
	Finger_set_startPosition_mE12A9B74F21D9B66FB9B6F67FEA1D5DE8483B36A,
	Finger_get_position_mD7E4F25C5EC08239EA13505CED61A2E2A7223E93,
	Finger_set_position_m377C1C4ADC62778CA08627A068DA396D4DB04C7B,
	Finger_get_lastPosition_m6C385CAF630D4AEEC49C233097D5C5A0E4F651F7,
	Finger_set_lastPosition_m74327C5F1FF0FA9A7434D7FB7BBC9BFC135FEA2A,
	Finger_get_deltaPosition_mFF81C99D713BCAD5170BC781EC4A5B39FF2692E3,
	Finger_set_deltaPosition_mD5D4C0FA06DB9577FB53FFE7962AA94C662A6C78,
	Finger_get_currentStateDuration_mD0A03B3889C5FC15D90D7D482569D44F8949E234,
	Finger_set_currentStateDuration_m94BDC11890516A0E7342F2DBB75BDBE4C3848C1C,
	Finger__ctor_m3705D1A51634F357E3AE9EFAE4E0C1BF185F5A79,
	Finger_Reset_m8AA9BB5E549A0C766CE9994C43243CAC507CBE7E,
	Finger_Update_m8A7DBD8E7C51BEBAC9510DDB6D74631921D0573E,
	Finger_get_Moved_m49BF5CE5299489209594FFEEAD82F5932854CD68,
	Finger_set_Moved_m9CF46413BEAAD5E8E6DD4867E8E99388CEFFF8CE,
	Finger_get_WasDown_m237B3519DD64E0ECEC87ECBCD3061645862D9D25,
	Finger_get_WasMoving_m7ED124C626CFA72C1DCD7C365F529BFD49834570,
	Finger_get_WasStationary_mFF49E4D74D9A2848CB787EAB1185EDD0BD94AC99,
	Finger_get_IsDown_mE1A0287C740B3E37E75F541F79730C49E31E43CD,
	Finger_get_IsMoving_m64569640AD76608F9AED5FE4B3A32CB0978DA48F,
	Finger_get_IsStationary_mEAEBF6AC6728498061C9E7ECC20FF8FA8DE60D6B,
	FingerTouchEvent_get_finger_m9FE6E136CA29BC49889868DAEE4B764092D28843,
	FingerTouchEvent_set_finger_m5415B5FACC66E0442B88CE0371ED517C31B884C3,
	FingerTouchEvent__ctor_m3E8F05B22D939D32A0D0E7E8094CD012142D884B,
	FingerMotionEvent_get_phase_m382EE2A04D7A21A48078F74A4262EBFB290E68A3,
	FingerMotionEvent_set_phase_m7BAB2F38CFEE73443835EAE8815CC64BCB7CB782,
	FingerMotionEvent__ctor_m8EA10E74A28A021BCDA16851CD99305E67FB7AB3,
	NULL,
	BaseInput_ProcessFingerEvent_m24AC7960A0D255CB4660A38436D4ECA5D6696965,
	BaseInput_FingerDownDetect_m85C3143AA65DD55F8D814009E69987D6C2128C1E,
	BaseInput_FingerUpDetect_m85145B7062E17C4E70925B1FF7AE35940DC0C84B,
	BaseInput_FingerMotionDetect_mCDC757C25A4037792BD59A93CD7A39404315C08D,
	BaseInput__ctor_mB6868ADDD8A711942038C76BF195BDDF092ADD34,
	TouchInput_Update_m06E3A199603EA55BD90766C191DB7329690A1213,
	TouchInput__ctor_m6E613CB00B7819C21FE35D7C3CD54CEB961EB376,
	MouseInput_Update_mB57E2E8F5293F5C1E7461583DAED83B26D5F7582,
	MouseInput__ctor_m32B1FFB988204A5F86316FBB5E255BC01A19D662,
	TouchSystem_add_onTouchBegan_mA28750785276F381DAAD08F08988DBA440F0A58F,
	TouchSystem_remove_onTouchBegan_m113B6467C1144BE2EE7F790768D6E4D29907B858,
	TouchSystem_add_onTouchMoved_m3E8BEF833C4D9899E3154CA18E047C98BB38E244,
	TouchSystem_remove_onTouchMoved_mBBED6A01619E86B8888668E8B20CE8DFDE1B96EF,
	TouchSystem_add_onTouchStationary_m1333F178F707435EEFD189A29BE4058E35149AD9,
	TouchSystem_remove_onTouchStationary_m0F0C0285C1789CD109B7CEC9775DB750C06B26DF,
	TouchSystem_add_onTouchEnded_m9729108953ADA35687DFD161187C528CF22E4AF0,
	TouchSystem_remove_onTouchEnded_m7C759B5981235F37D2280C3A95DA032D67352D89,
	TouchSystem_add_onTouchCanceled_m3460ECB73F0331674084779E2AB41C584B6D7640,
	TouchSystem_remove_onTouchCanceled_mF38F32046D4457A8755562A6C5A12A3F83D9E220,
	TouchSystem_add_onMouseMove_m9D56943AD0784F26B578D6684F823FE21D20D2A8,
	TouchSystem_remove_onMouseMove_mF108DB89D3F374F149A69E5B9A522EEA5FC31C34,
	TouchSystem_get_IsEnabled_mA9E66B9FBB3257435EE78472D1AED7E9D2E3ABDA,
	TouchSystem_set_IsEnabled_mF77C83E99B5BA493D9B9E7EFCD5DE4DE3D87B09F,
	TouchSystem__ctor_m5A6CE395609E76073CE87AFA629CABF25E48C849,
	NULL,
	NULL,
	TouchSystem_Reset_m02A68AE3AA2871E72DD1EBFF973C84715C71F3E3,
	TouchSystem_OnTouchBegan_mC66AB49CD896F6A6D269F96B43C347AC2FB87989,
	TouchSystem_OnTouchMoved_m2DCD48D75A308C714E5CE32E4D2699998213DF33,
	TouchSystem_OnTouchStationary_m6A121B7F5B3168946089FF18D66534AF206C741F,
	TouchSystem_OnTouchEnded_mEE172ACC2021B4F930507388362C4B8258BB4E2F,
	TouchSystem_OnTouchCanceled_mC3CF47578D002BAE807B5555CA26950B7A9BD7AB,
	TouchSystem_OnMouseMove_m29102FF4E1122CCD48DF0713C0384050498E45B1,
	TouchSystem_Update_m96A2A02D383B08DE9A4FCB8777885EC17B2ACC00,
	TouchSystem_get_IsAnyFingerDown_mD8CC65BEC5775BC68623FA8BC05AF53390068399,
	TouchSystem_GetFinger_m68BB32C0E4CF8DE2BD9B2FAE1008A6DA6BE92447,
	TouchSystem_GetFingerTouchEvent_m8586CA78A0B7E12ACF5F8165AB114C2DF69CA1C0,
	TouchSystem_GetFingerMotionEvent_m5F6EF7B406DE7BE437CDB89235E4F5244DFC875B,
	TouchSystem_GetFingerByID_m58A4BA1C79F5710B7140450880F0F3EFF247FC8C,
	FingerEventHandler__ctor_m669822C432A87AC6BBD4722944404DDDA1DD2544,
	FingerEventHandler_Invoke_mA20FAE4131B5472E16C4D31CCD3C0C5514F0DAEF,
	FingerEventHandler_BeginInvoke_mDB3AB14294345203982F8D2A4D7F8C762149BC6E,
	FingerEventHandler_EndInvoke_m33A342396E28C25FBBDDA3E2EFA692AA6FB3AFE0,
	MouseEventHandler__ctor_mEE0BB11D23A343C6A8699F352D29A59C7EF1A773,
	MouseEventHandler_Invoke_m7768333417BCEA31AD71AA27D5ED80ED6CB56D49,
	MouseEventHandler_BeginInvoke_mD633EAAA68460879E021416D51341C685E88F249,
	MouseEventHandler_EndInvoke_m3A755D246163047889D5799FC95E64E1AD17422B,
};
static const int32_t s_InvokerIndices[85] = 
{
	21393,
	13298,
	12996,
	10629,
	12996,
	10629,
	12996,
	10629,
	12996,
	10629,
	13275,
	10907,
	13275,
	10907,
	13275,
	10907,
	13275,
	10907,
	13195,
	10823,
	10629,
	13298,
	2866,
	12815,
	10442,
	12815,
	12815,
	12815,
	12815,
	12815,
	12815,
	13052,
	10682,
	13298,
	12996,
	10629,
	13298,
	0,
	5688,
	5688,
	5688,
	5688,
	13298,
	10682,
	13298,
	10682,
	13298,
	10682,
	10682,
	10682,
	10682,
	10682,
	10682,
	10682,
	10682,
	10682,
	10682,
	10682,
	10682,
	12815,
	10442,
	13298,
	0,
	0,
	13298,
	10682,
	10682,
	10682,
	10682,
	10682,
	10907,
	13298,
	12815,
	9267,
	9267,
	9267,
	9267,
	5684,
	10682,
	2408,
	10682,
	5684,
	10907,
	2426,
	10682,
};
static const Il2CppTokenRangePair s_rgctxIndices[2] = 
{
	{ 0x0600003F, { 0, 4 } },
	{ 0x06000040, { 4, 2 } },
};
extern const uint32_t g_rgctx_TU5BU5D_t6665403105A99701E422133CDCA21A97662A9A8D;
extern const uint32_t g_rgctx_Activator_CreateInstance_TisT_tB7184CC595FEA5DA43F6E9A968BC2C8776BCF29F_mE681C98FEDA43A230BC6F2B5C57A3A4524FD11B3;
extern const uint32_t g_rgctx_T_tB7184CC595FEA5DA43F6E9A968BC2C8776BCF29F;
extern const uint32_t g_rgctx_TU5BU5D_t6665403105A99701E422133CDCA21A97662A9A8D;
extern const uint32_t g_rgctx_TU5BU5D_t7CF3CFB87AD2A2014E3A43B5E43877DF44E46A22;
extern const uint32_t g_rgctx_T_t6A1E224D0847095ACD44EB0A155EC0C8D0C1A2F2;
static const Il2CppRGCTXDefinition s_rgctxValues[6] = 
{
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_t6665403105A99701E422133CDCA21A97662A9A8D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Activator_CreateInstance_TisT_tB7184CC595FEA5DA43F6E9A968BC2C8776BCF29F_mE681C98FEDA43A230BC6F2B5C57A3A4524FD11B3 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tB7184CC595FEA5DA43F6E9A968BC2C8776BCF29F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_t6665403105A99701E422133CDCA21A97662A9A8D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_t7CF3CFB87AD2A2014E3A43B5E43877DF44E46A22 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t6A1E224D0847095ACD44EB0A155EC0C8D0C1A2F2 },
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_SimpleTouch_CodeGenModule;
const Il2CppCodeGenModule g_SimpleTouch_CodeGenModule = 
{
	"SimpleTouch.dll",
	85,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	2,
	s_rgctxIndices,
	6,
	s_rgctxValues,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};

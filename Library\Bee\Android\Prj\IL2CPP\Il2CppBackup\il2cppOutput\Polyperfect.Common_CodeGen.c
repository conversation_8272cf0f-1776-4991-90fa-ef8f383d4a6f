﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_mEB723F46D0D9B1A638EF89A634A1E049690D734F (void);
extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_m56B0FA79453C41073F905A425E5D028A6CA64822 (void);
extern void DisableInInspectorAttribute__ctor_m9A093D6BFC0DF5B6529DBC842CE1B0BDED72B031 (void);
extern void HighlightNullAttribute__ctor_m519A283E4AB7A52FF25CDA177A950C7B2C0E8484 (void);
extern void HighPriorityAttribute__ctor_m6094E1E3C03DC07C201F5E69CDF4556284DFF96F (void);
extern void LowPriorityAttribute__ctor_m265BD9A3BD11879CCFCFF9C457B9009F2F3F88D8 (void);
extern void TypeExtensions_GetFieldViaPath_mDB2192953C74588538715F0EDDA8B20D75337FDC (void);
extern void TypeExtensions_GetPropertyViaPath_m2150248D2E2BDB1A94888591D07B707D4CC8A475 (void);
extern void TypeExtensions_GetBindingFlags_mC47D943AA00CF7F9D00F877783CBFEB3B90CD50D (void);
extern void TypeExtensions_IsHighPriority_mE20F06A5241ED65020C9A21A4A50B3A428AEF58F (void);
extern void TypeExtensions_IsLowPriority_m5307D6075D96B1751F16AAAF37C60A47DDCC1872 (void);
extern void TypeExtensions_IsHighlightNull_mAC7156569D2B24F71B97CF447DECF77E2B1AD94D (void);
extern void TypeExtensions_IsDisableInInspector_m14F394F3541B05F7528AE6AC0756197D60BE27DD (void);
extern void TypeExtensions_GetRequiredTypes_m7757459B51848AC7D2EBD80B9459971774CC979B (void);
extern void PolyMono_ForceEditorRedraw_m9015C2209ECED93B012946268FE40D655996B58E (void);
extern void PolyMono__ctor_m72500E47CBB1A9891978157FDFAC7BBD7D7B09DF (void);
extern void PolyObject__ctor_m24263AED347935C24F14A4E0B5ADC398233DAA3E (void);
extern void Destroyer_get___Usage_m13107DAEA4515033F0EBBF4574CC0721C66D10BE (void);
extern void Destroyer_DestroyThis_mE94D6702BC30BB1CC4132E7E79CD7247E7F95F51 (void);
extern void Destroyer_DestroyThisAfter_m3A99649D90739B80BA70F97961D850C693F3F70B (void);
extern void Destroyer_DestroyThat_mD5B8BB6BC903B5F53CC319887C554904AE052A1B (void);
extern void Destroyer_Start_mCECE9E86F67C4F40F4C082775A88A9DDFEC04493 (void);
extern void Destroyer_Update_m76479A0B481DD7300516600CB73D0AEECDC79B43 (void);
extern void Destroyer__ctor_m5A3680CA188D81742D9A2D9426798B7BABCB380C (void);
extern void InitialForce_get___Usage_m2C1F7560521F76417749489F0B09CB9CC4E24170 (void);
extern void InitialForce_Start_m8966B0E40F5CDB565EB51D14D9C3693AAFFC80A0 (void);
extern void InitialForce__ctor_m5988235D4C73487941163D40D9C007F12ABC615C (void);
extern void Printer_get___Usage_mA43700EE2E112A78962E5BA26F251A3EBEEF31D9 (void);
extern void Printer_Print_m589FE1610B7C207D5371BD6E845C5A80AB1F9054 (void);
extern void Printer_PrintWarning_m5F9436FCDBC79E674A12576235DE6279C16DAC29 (void);
extern void Printer_PrintError_m5B66610C4DC110A8CA58B3D8D3FB14A0DE181626 (void);
extern void Printer__ctor_m0163BBE0E344A993CB6CA0B8E95A29140D6F6ED2 (void);
extern void SimpleEvents_get___Usage_m14A4C483A5FF80C6BA5AABC939B038F9F6B1FB5A (void);
extern void SimpleEvents_Awake_m54ADD406885261B996C3490F2BBE101E10653E89 (void);
extern void SimpleEvents_Start_mD56A12D2794DFB93259A24F445E5A27045F5F7B9 (void);
extern void SimpleEvents_OnEnable_m159C824275C7342BE74DECC1364738E294FB9F36 (void);
extern void SimpleEvents_OnDisable_mE3CA765881C7CA628E686EB0774BC1473F85049A (void);
extern void SimpleEvents_OnDestroy_m6D1B48263CF3C4AD4403F2AC3A3E50ABBD42CB4E (void);
extern void SimpleEvents__ctor_m4484CCA8141EF59082AD39DAB709C01BAF6BE122 (void);
extern void SimpleOscillate_get___Usage_mF9AB70A7E73E7C1E31750226317F2D8458703932 (void);
extern void SimpleOscillate_Start_mAFB2D9DBF5E3C1E298493D6C72662599D8C08F9B (void);
extern void SimpleOscillate_Update_m73F741294B0C96A546E87CBCB4F72DCCA456DB16 (void);
extern void SimpleOscillate__ctor_mF014D6A315499180E2AC1F8356AAE68569560B4C (void);
extern void SimpleRotate_get___Usage_m17EEB2ABD633D62EF1D13F0E73D0D36A4B75C49C (void);
extern void SimpleRotate_Start_m0D186DF18A3A36223A1ED37D2A34F0A36691A376 (void);
extern void SimpleRotate_Update_m52D038050BC66631DCBB5C06C528B8FF652B1C3B (void);
extern void SimpleRotate__ctor_m64910507766BC5BB94A112C1AA710F9AA8DC6D2B (void);
extern void SimpleTranslate_get___Usage_mF94F68210CE155133CFF8A02A8EDD677A82A8859 (void);
extern void SimpleTranslate_Update_m9204FC946485F8DA5C0BAB1950A13880991319F3 (void);
extern void SimpleTranslate__ctor_m2814C2F3322C88F5D80EB083FAC4FF912DDC9D66 (void);
extern void UI_Constants__cctor_mA03C686D2D0C2CB239976F230827EE1253658396 (void);
extern void VisualElementExtensions_SetRadius_m3D156A6A214CB5D6C3133654AF9FC85E255843BD (void);
extern void VisualElementExtensions_SetMargin_mAF0F564F3517FD79415AAC842046EDBF0289445E (void);
extern void VisualElementExtensions_SetBorderWidth_m74D7ED28C72961F567D82A9B38D262D99FBBED11 (void);
extern void VisualElementExtensions_SetBorderColor_m1EAE0D9480460A97605587CA70920B3A5AF0A7F0 (void);
extern void VisualElementExtensions_SetRow_m79D115AB939C31E8290EEA6BE42B505F89C6F558 (void);
extern void VisualElementExtensions_SetColumn_m0523912510590BAE9A44CE5BCAC75FB621262E12 (void);
extern void VisualElementExtensions_Show_mE14FE26F8C790292E7D7E41962CC89A0BD6ED525 (void);
extern void VisualElementExtensions_Hide_m21A461CCC2172FA3FF874377DCF4348178AD7089 (void);
extern void VisualElementExtensions_DisplayIf_mB9B12E17B1697116D4587E668D90206AF345A1AB (void);
extern void VisualElementExtensions_DoubleDelay_mECAF7C18EA25FCA7883CE3652CDF2F08D0FA0524 (void);
extern void U3CU3Ec__DisplayClass17_0__ctor_m34A44D59C1746627CE72C6C5EBF3906667C1C804 (void);
extern void U3CU3Ec__DisplayClass17_0_U3CDoubleDelayU3Eb__0_mED113760AD5B6E95F6B38526A7D9EC5F2943A1BD (void);
static Il2CppMethodPointer s_methodPointers[75] = 
{
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_mEB723F46D0D9B1A638EF89A634A1E049690D734F,
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_m56B0FA79453C41073F905A425E5D028A6CA64822,
	DisableInInspectorAttribute__ctor_m9A093D6BFC0DF5B6529DBC842CE1B0BDED72B031,
	HighlightNullAttribute__ctor_m519A283E4AB7A52FF25CDA177A950C7B2C0E8484,
	HighPriorityAttribute__ctor_m6094E1E3C03DC07C201F5E69CDF4556284DFF96F,
	LowPriorityAttribute__ctor_m265BD9A3BD11879CCFCFF9C457B9009F2F3F88D8,
	NULL,
	NULL,
	TypeExtensions_GetFieldViaPath_mDB2192953C74588538715F0EDDA8B20D75337FDC,
	TypeExtensions_GetPropertyViaPath_m2150248D2E2BDB1A94888591D07B707D4CC8A475,
	TypeExtensions_GetBindingFlags_mC47D943AA00CF7F9D00F877783CBFEB3B90CD50D,
	TypeExtensions_IsHighPriority_mE20F06A5241ED65020C9A21A4A50B3A428AEF58F,
	TypeExtensions_IsLowPriority_m5307D6075D96B1751F16AAAF37C60A47DDCC1872,
	TypeExtensions_IsHighlightNull_mAC7156569D2B24F71B97CF447DECF77E2B1AD94D,
	TypeExtensions_IsDisableInInspector_m14F394F3541B05F7528AE6AC0756197D60BE27DD,
	TypeExtensions_GetRequiredTypes_m7757459B51848AC7D2EBD80B9459971774CC979B,
	NULL,
	PolyMono_ForceEditorRedraw_m9015C2209ECED93B012946268FE40D655996B58E,
	PolyMono__ctor_m72500E47CBB1A9891978157FDFAC7BBD7D7B09DF,
	NULL,
	PolyObject__ctor_m24263AED347935C24F14A4E0B5ADC398233DAA3E,
	Destroyer_get___Usage_m13107DAEA4515033F0EBBF4574CC0721C66D10BE,
	Destroyer_DestroyThis_mE94D6702BC30BB1CC4132E7E79CD7247E7F95F51,
	Destroyer_DestroyThisAfter_m3A99649D90739B80BA70F97961D850C693F3F70B,
	Destroyer_DestroyThat_mD5B8BB6BC903B5F53CC319887C554904AE052A1B,
	Destroyer_Start_mCECE9E86F67C4F40F4C082775A88A9DDFEC04493,
	Destroyer_Update_m76479A0B481DD7300516600CB73D0AEECDC79B43,
	Destroyer__ctor_m5A3680CA188D81742D9A2D9426798B7BABCB380C,
	InitialForce_get___Usage_m2C1F7560521F76417749489F0B09CB9CC4E24170,
	InitialForce_Start_m8966B0E40F5CDB565EB51D14D9C3693AAFFC80A0,
	InitialForce__ctor_m5988235D4C73487941163D40D9C007F12ABC615C,
	Printer_get___Usage_mA43700EE2E112A78962E5BA26F251A3EBEEF31D9,
	Printer_Print_m589FE1610B7C207D5371BD6E845C5A80AB1F9054,
	Printer_PrintWarning_m5F9436FCDBC79E674A12576235DE6279C16DAC29,
	Printer_PrintError_m5B66610C4DC110A8CA58B3D8D3FB14A0DE181626,
	Printer__ctor_m0163BBE0E344A993CB6CA0B8E95A29140D6F6ED2,
	SimpleEvents_get___Usage_m14A4C483A5FF80C6BA5AABC939B038F9F6B1FB5A,
	SimpleEvents_Awake_m54ADD406885261B996C3490F2BBE101E10653E89,
	SimpleEvents_Start_mD56A12D2794DFB93259A24F445E5A27045F5F7B9,
	SimpleEvents_OnEnable_m159C824275C7342BE74DECC1364738E294FB9F36,
	SimpleEvents_OnDisable_mE3CA765881C7CA628E686EB0774BC1473F85049A,
	SimpleEvents_OnDestroy_m6D1B48263CF3C4AD4403F2AC3A3E50ABBD42CB4E,
	SimpleEvents__ctor_m4484CCA8141EF59082AD39DAB709C01BAF6BE122,
	SimpleOscillate_get___Usage_mF9AB70A7E73E7C1E31750226317F2D8458703932,
	SimpleOscillate_Start_mAFB2D9DBF5E3C1E298493D6C72662599D8C08F9B,
	SimpleOscillate_Update_m73F741294B0C96A546E87CBCB4F72DCCA456DB16,
	SimpleOscillate__ctor_mF014D6A315499180E2AC1F8356AAE68569560B4C,
	SimpleRotate_get___Usage_m17EEB2ABD633D62EF1D13F0E73D0D36A4B75C49C,
	SimpleRotate_Start_m0D186DF18A3A36223A1ED37D2A34F0A36691A376,
	SimpleRotate_Update_m52D038050BC66631DCBB5C06C528B8FF652B1C3B,
	SimpleRotate__ctor_m64910507766BC5BB94A112C1AA710F9AA8DC6D2B,
	SimpleTranslate_get___Usage_mF94F68210CE155133CFF8A02A8EDD677A82A8859,
	SimpleTranslate_Update_m9204FC946485F8DA5C0BAB1950A13880991319F3,
	SimpleTranslate__ctor_m2814C2F3322C88F5D80EB083FAC4FF912DDC9D66,
	UI_Constants__cctor_mA03C686D2D0C2CB239976F230827EE1253658396,
	VisualElementExtensions_SetRadius_m3D156A6A214CB5D6C3133654AF9FC85E255843BD,
	NULL,
	NULL,
	NULL,
	VisualElementExtensions_SetMargin_mAF0F564F3517FD79415AAC842046EDBF0289445E,
	VisualElementExtensions_SetBorderWidth_m74D7ED28C72961F567D82A9B38D262D99FBBED11,
	VisualElementExtensions_SetBorderColor_m1EAE0D9480460A97605587CA70920B3A5AF0A7F0,
	NULL,
	NULL,
	NULL,
	VisualElementExtensions_SetRow_m79D115AB939C31E8290EEA6BE42B505F89C6F558,
	NULL,
	VisualElementExtensions_SetColumn_m0523912510590BAE9A44CE5BCAC75FB621262E12,
	VisualElementExtensions_Show_mE14FE26F8C790292E7D7E41962CC89A0BD6ED525,
	VisualElementExtensions_Hide_m21A461CCC2172FA3FF874377DCF4348178AD7089,
	VisualElementExtensions_DisplayIf_mB9B12E17B1697116D4587E668D90206AF345A1AB,
	NULL,
	VisualElementExtensions_DoubleDelay_mECAF7C18EA25FCA7883CE3652CDF2F08D0FA0524,
	U3CU3Ec__DisplayClass17_0__ctor_m34A44D59C1746627CE72C6C5EBF3906667C1C804,
	U3CU3Ec__DisplayClass17_0_U3CDoubleDelayU3Eb__0_mED113760AD5B6E95F6B38526A7D9EC5F2943A1BD,
};
static const int32_t s_InvokerIndices[75] = 
{
	21391,
	13298,
	13298,
	13298,
	13298,
	13298,
	0,
	0,
	18006,
	18006,
	21263,
	19891,
	19891,
	19891,
	19891,
	20515,
	0,
	13298,
	13298,
	0,
	13298,
	13052,
	13298,
	10823,
	10682,
	13298,
	13298,
	13298,
	13052,
	13298,
	13298,
	13052,
	10682,
	10682,
	10682,
	13298,
	13052,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13052,
	13298,
	13298,
	13298,
	13052,
	13298,
	13298,
	13298,
	13052,
	13298,
	13298,
	21355,
	18820,
	0,
	0,
	0,
	18820,
	18820,
	18796,
	0,
	0,
	0,
	20515,
	0,
	20515,
	20847,
	20847,
	18795,
	0,
	18006,
	13298,
	13298,
};
static const Il2CppTokenRangePair s_rgctxIndices[10] = 
{
	{ 0x06000007, { 0, 7 } },
	{ 0x06000008, { 7, 7 } },
	{ 0x06000039, { 14, 1 } },
	{ 0x0600003A, { 15, 1 } },
	{ 0x0600003B, { 16, 1 } },
	{ 0x0600003F, { 17, 1 } },
	{ 0x06000040, { 18, 1 } },
	{ 0x06000041, { 19, 1 } },
	{ 0x06000043, { 20, 1 } },
	{ 0x06000048, { 21, 1 } },
};
extern const uint32_t g_rgctx_T_t899691A6EAD045C0E7068579C6E3D5307CF8CBAC;
extern const uint32_t g_rgctx_IEnumerable_1_t75AC0C9CD3AE577691038897D62A61B681B77749;
extern const uint32_t g_rgctx_IEnumerable_1_GetEnumerator_mDB696201BF2B820F41085B0965D30769A120AF50;
extern const uint32_t g_rgctx_IEnumerator_1_tE4A0E8396F4DA61C3BF3A21CC0DC801A7B608D79;
extern const uint32_t g_rgctx_IEnumerator_1_get_Current_m72879F13C9BA874034666FD0CE531AB1DB7090FD;
extern const uint32_t g_rgctx_Func_2_t6C24DBD8339149CC8E5B2A2F2A399FF9961F8526;
extern const uint32_t g_rgctx_Func_2_Invoke_m883E4E79A38F3BDA33E5167A776F1B73A10A218E;
extern const uint32_t g_rgctx_T_t3114ED7C00D43750CE108B4FB2BB96C4B9DEBE95;
extern const uint32_t g_rgctx_IEnumerable_1_t87DC3C1F04894A0FF81445F6923E4ECA452F92DD;
extern const uint32_t g_rgctx_IEnumerable_1_GetEnumerator_m57C55332B205FAEA6D465FAF1473B48E5862DE42;
extern const uint32_t g_rgctx_IEnumerator_1_tE0EE4C34A98DB4CCF74BC6DC30990CDF75AFBD5A;
extern const uint32_t g_rgctx_IEnumerator_1_get_Current_m3D065F606EA8C2C015872F4C1E28AA8285D5BFC5;
extern const uint32_t g_rgctx_Func_2_t35E1C0A6FEA2891893D391336D142D7406C0707E;
extern const uint32_t g_rgctx_Func_2_Invoke_m6BF883065D7E90F611D7921243FF773C157C6C3A;
extern const uint32_t g_rgctx_T_t3D7B541228F2FAEC630C2845D384B2D7F950E01C;
extern const uint32_t g_rgctx_T_t80C1EB387309A25090C8E8F9F83884644547BBA4;
extern const uint32_t g_rgctx_T_t3EC14877A78EEA74A579CC8AFD71898C471B3F31;
extern const uint32_t g_rgctx_T_t548899B9B7353E186F0DF51D980D573B10C46A30;
extern const uint32_t g_rgctx_T_t715683981232878B041207CFB449C0C4206C010A;
extern const uint32_t g_rgctx_T_t3391F80B01F57CD41DC8A808A07DB428C325F03C;
extern const uint32_t g_rgctx_T_t209F0F6505E68B859B239790289F46A80A13BF94;
extern const uint32_t g_rgctx_T_tFCBEA18B13FE11DD79A1CCF8323CF68A623FABE8;
static const Il2CppRGCTXDefinition s_rgctxValues[22] = 
{
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t899691A6EAD045C0E7068579C6E3D5307CF8CBAC },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerable_1_t75AC0C9CD3AE577691038897D62A61B681B77749 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IEnumerable_1_GetEnumerator_mDB696201BF2B820F41085B0965D30769A120AF50 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerator_1_tE4A0E8396F4DA61C3BF3A21CC0DC801A7B608D79 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IEnumerator_1_get_Current_m72879F13C9BA874034666FD0CE531AB1DB7090FD },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_2_t6C24DBD8339149CC8E5B2A2F2A399FF9961F8526 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Func_2_Invoke_m883E4E79A38F3BDA33E5167A776F1B73A10A218E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t3114ED7C00D43750CE108B4FB2BB96C4B9DEBE95 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerable_1_t87DC3C1F04894A0FF81445F6923E4ECA452F92DD },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IEnumerable_1_GetEnumerator_m57C55332B205FAEA6D465FAF1473B48E5862DE42 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerator_1_tE0EE4C34A98DB4CCF74BC6DC30990CDF75AFBD5A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IEnumerator_1_get_Current_m3D065F606EA8C2C015872F4C1E28AA8285D5BFC5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_2_t35E1C0A6FEA2891893D391336D142D7406C0707E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Func_2_Invoke_m6BF883065D7E90F611D7921243FF773C157C6C3A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t3D7B541228F2FAEC630C2845D384B2D7F950E01C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t80C1EB387309A25090C8E8F9F83884644547BBA4 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t3EC14877A78EEA74A579CC8AFD71898C471B3F31 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t548899B9B7353E186F0DF51D980D573B10C46A30 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t715683981232878B041207CFB449C0C4206C010A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t3391F80B01F57CD41DC8A808A07DB428C325F03C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t209F0F6505E68B859B239790289F46A80A13BF94 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tFCBEA18B13FE11DD79A1CCF8323CF68A623FABE8 },
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_Polyperfect_Common_CodeGenModule;
const Il2CppCodeGenModule g_Polyperfect_Common_CodeGenModule = 
{
	"Polyperfect.Common.dll",
	75,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	10,
	s_rgctxIndices,
	22,
	s_rgctxValues,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};

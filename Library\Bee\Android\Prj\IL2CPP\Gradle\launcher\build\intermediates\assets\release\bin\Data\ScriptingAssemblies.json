{"names": ["UnityEngine.dll", "UnityEngine.AIModule.dll", "UnityEngine.ARModule.dll", "UnityEngine.AccessibilityModule.dll", "UnityEngine.AndroidJNIModule.dll", "UnityEngine.AnimationModule.dll", "UnityEngine.AssetBundleModule.dll", "UnityEngine.AudioModule.dll", "UnityEngine.ClothModule.dll", "UnityEngine.ContentLoadModule.dll", "UnityEngine.CoreModule.dll", "UnityEngine.CrashReportingModule.dll", "UnityEngine.DSPGraphModule.dll", "UnityEngine.DirectorModule.dll", "UnityEngine.GIModule.dll", "UnityEngine.GameCenterModule.dll", "UnityEngine.GridModule.dll", "UnityEngine.HotReloadModule.dll", "UnityEngine.IMGUIModule.dll", "UnityEngine.ImageConversionModule.dll", "UnityEngine.InputModule.dll", "UnityEngine.InputLegacyModule.dll", "UnityEngine.JSONSerializeModule.dll", "UnityEngine.LocalizationModule.dll", "UnityEngine.ParticleSystemModule.dll", "UnityEngine.PerformanceReportingModule.dll", "UnityEngine.PhysicsModule.dll", "UnityEngine.Physics2DModule.dll", "UnityEngine.ProfilerModule.dll", "UnityEngine.PropertiesModule.dll", "UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll", "UnityEngine.ScreenCaptureModule.dll", "UnityEngine.SharedInternalsModule.dll", "UnityEngine.SpriteMaskModule.dll", "UnityEngine.SpriteShapeModule.dll", "UnityEngine.StreamingModule.dll", "UnityEngine.SubstanceModule.dll", "UnityEngine.SubsystemsModule.dll", "UnityEngine.TLSModule.dll", "UnityEngine.TerrainModule.dll", "UnityEngine.TerrainPhysicsModule.dll", "UnityEngine.TextCoreFontEngineModule.dll", "UnityEngine.TextCoreTextEngineModule.dll", "UnityEngine.TextRenderingModule.dll", "UnityEngine.TilemapModule.dll", "UnityEngine.UIModule.dll", "UnityEngine.UIElementsModule.dll", "UnityEngine.UmbraModule.dll", "UnityEngine.UnityAnalyticsModule.dll", "UnityEngine.UnityAnalyticsCommonModule.dll", "UnityEngine.UnityConnectModule.dll", "UnityEngine.UnityCurlModule.dll", "UnityEngine.UnityTestProtocolModule.dll", "UnityEngine.UnityWebRequestModule.dll", "UnityEngine.UnityWebRequestAssetBundleModule.dll", "UnityEngine.UnityWebRequestAudioModule.dll", "UnityEngine.UnityWebRequestTextureModule.dll", "UnityEngine.UnityWebRequestWWWModule.dll", "UnityEngine.VFXModule.dll", "UnityEngine.VRModule.dll", "UnityEngine.VehiclesModule.dll", "UnityEngine.VideoModule.dll", "UnityEngine.WindModule.dll", "UnityEngine.XRModule.dll", "Assembly-CSharp-firstpass.dll", "Assembly-CSharp.dll", "AK.Wwise.Unity.Timeline.dll", "Unity.VisualEffectGraph.Runtime.dll", "Unity.RenderPipelines.Core.Runtime.dll", "AllIn1VfxTexDemoAssembly.dll", "Unity.ResourceManager.dll", "SimpleTouch.dll", "Unity.ScriptableBuildPipeline.dll", "Singleton.dll", "AK.Wwise.Unity.API.dll", "Unity.RenderPipelines.Universal.Config.Runtime.dll", "Unity.VisualScripting.Flow.dll", "KinoBloom.Runtime.dll", "AK.Wwise.Unity.MonoBehaviour.dll", "CallbackManager.dll", "Unity.RenderPipelines.Core.ShaderLibrary.dll", "Unity.Collections.dll", "Events.dll", "Polyperfect.War.dll", "Ak.Wwise.Api.WAAPI.dll", "ToonyColorsPro2.Demo.dll", "Unity.TextMeshPro.dll", "LitMotion.Extensions.dll", "LitMotion.Animation.dll", "AllIn1VfxAssmebly.dll", "Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll", "AllIn1VfxDemoScriptAssemblies.dll", "Unity.RenderPipelines.Universal.Runtime.dll", "Unity.Burst.dll", "Luban.Runtime.dll", "Unity.VisualScripting.Core.dll", "UnityEngine.UI.dll", "Unity.RenderPipeline.Universal.ShaderLibrary.dll", "Localization.Runtime.dll", "Unity.Timeline.dll", "AK.Wwise.Unity.API.WwiseTypes.dll", "LitMotion.dll", "ToonyColorsPro.Runtime.dll", "Unity.Mathematics.dll", "Unity.RenderPipelines.Universal.Shaders.dll", "Polyperfect.Common.dll", "MeshUI.dll", "Unity.VisualScripting.State.dll", "TaskScheduler.dll", "Unity.Addressables.dll", "Google.FlatBuffers.dll", "Unity.Collections.LowLevel.ILSupport.dll", "NLog.dll", "Unity.VisualScripting.Antlr3.Runtime.dll", "Unity.Burst.Unsafe.dll", "Clipper2Lib.dll", "System.Runtime.CompilerServices.Unsafe.dll", "Microsoft.IO.RecyclableMemoryStream.dll"], "types": [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16]}
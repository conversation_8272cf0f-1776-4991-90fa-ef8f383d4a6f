﻿#include "pch-cpp.hpp"

#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include <limits>


template <typename T1>
struct VirtualActionInvoker1
{
	typedef void (*Action)(void*, T1, const RuntimeMethod*);

	static inline void Invoke (Il2CppMethodSlot slot, RuntimeObject* obj, T1 p1)
	{
		const VirtualInvokeData& invokeData = il2cpp_codegen_get_virtual_invoke_data(slot, obj);
		((Action)invokeData.methodPtr)(obj, p1, invokeData.method);
	}
};
template <typename R>
struct VirtualFuncInvoker0
{
	typedef R (*Func)(void*, const RuntimeMethod*);

	static inline R Invoke (Il2CppMethodSlot slot, RuntimeObject* obj)
	{
		const VirtualInvokeData& invokeData = il2cpp_codegen_get_virtual_invoke_data(slot, obj);
		return ((Func)invokeData.methodPtr)(obj, invokeData.method);
	}
};
template <typename R, typename T1>
struct VirtualFuncInvoker1
{
	typedef R (*Func)(void*, T1, const RuntimeMethod*);

	static inline R Invoke (Il2CppMethodSlot slot, RuntimeObject* obj, T1 p1)
	{
		const VirtualInvokeData& invokeData = il2cpp_codegen_get_virtual_invoke_data(slot, obj);
		return ((Func)invokeData.methodPtr)(obj, p1, invokeData.method);
	}
};
template <typename R, typename T1, typename T2>
struct VirtualFuncInvoker2
{
	typedef R (*Func)(void*, T1, T2, const RuntimeMethod*);

	static inline R Invoke (Il2CppMethodSlot slot, RuntimeObject* obj, T1 p1, T2 p2)
	{
		const VirtualInvokeData& invokeData = il2cpp_codegen_get_virtual_invoke_data(slot, obj);
		return ((Func)invokeData.methodPtr)(obj, p1, p2, invokeData.method);
	}
};
template <typename R, typename T1, typename T2, typename T3, typename T4>
struct VirtualFuncInvoker4
{
	typedef R (*Func)(void*, T1, T2, T3, T4, const RuntimeMethod*);

	static inline R Invoke (Il2CppMethodSlot slot, RuntimeObject* obj, T1 p1, T2 p2, T3 p3, T4 p4)
	{
		const VirtualInvokeData& invokeData = il2cpp_codegen_get_virtual_invoke_data(slot, obj);
		return ((Func)invokeData.methodPtr)(obj, p1, p2, p3, p4, invokeData.method);
	}
};
template <typename R, typename T1>
struct GenericVirtualFuncInvoker1
{
	typedef R (*Func)(void*, T1, const RuntimeMethod*);

	static inline R Invoke (const RuntimeMethod* method, RuntimeObject* obj, T1 p1)
	{
		VirtualInvokeData invokeData;
		il2cpp_codegen_get_generic_virtual_invoke_data(method, obj, &invokeData);
		return ((Func)invokeData.methodPtr)(obj, p1, invokeData.method);
	}
};
template <typename R, typename T1, typename T2>
struct GenericVirtualFuncInvoker2
{
	typedef R (*Func)(void*, T1, T2, const RuntimeMethod*);

	static inline R Invoke (const RuntimeMethod* method, RuntimeObject* obj, T1 p1, T2 p2)
	{
		VirtualInvokeData invokeData;
		il2cpp_codegen_get_generic_virtual_invoke_data(method, obj, &invokeData);
		return ((Func)invokeData.methodPtr)(obj, p1, p2, invokeData.method);
	}
};
template <typename R, typename T1>
struct InterfaceFuncInvoker1
{
	typedef R (*Func)(void*, T1, const RuntimeMethod*);

	static inline R Invoke (Il2CppMethodSlot slot, RuntimeClass* declaringInterface, RuntimeObject* obj, T1 p1)
	{
		const VirtualInvokeData& invokeData = il2cpp_codegen_get_interface_invoke_data(slot, obj, declaringInterface);
		return ((Func)invokeData.methodPtr)(obj, p1, invokeData.method);
	}
};
template <typename R, typename T1, typename T2>
struct InterfaceFuncInvoker2
{
	typedef R (*Func)(void*, T1, T2, const RuntimeMethod*);

	static inline R Invoke (Il2CppMethodSlot slot, RuntimeClass* declaringInterface, RuntimeObject* obj, T1 p1, T2 p2)
	{
		const VirtualInvokeData& invokeData = il2cpp_codegen_get_interface_invoke_data(slot, obj, declaringInterface);
		return ((Func)invokeData.methodPtr)(obj, p1, p2, invokeData.method);
	}
};
template <typename R, typename T1>
struct GenericInterfaceFuncInvoker1
{
	typedef R (*Func)(void*, T1, const RuntimeMethod*);

	static inline R Invoke (const RuntimeMethod* method, RuntimeObject* obj, T1 p1)
	{
		VirtualInvokeData invokeData;
		il2cpp_codegen_get_generic_interface_invoke_data(method, obj, &invokeData);
		return ((Func)invokeData.methodPtr)(obj, p1, invokeData.method);
	}
};
template <typename R, typename T1, typename T2>
struct GenericInterfaceFuncInvoker2
{
	typedef R (*Func)(void*, T1, T2, const RuntimeMethod*);

	static inline R Invoke (const RuntimeMethod* method, RuntimeObject* obj, T1 p1, T2 p2)
	{
		VirtualInvokeData invokeData;
		il2cpp_codegen_get_generic_interface_invoke_data(method, obj, &invokeData);
		return ((Func)invokeData.methodPtr)(obj, p1, p2, invokeData.method);
	}
};

struct Dictionary_2_t87EDE08B2E48F793A22DE50D6B3CC2E7EBB2DB54;
struct Dictionary_2_t5C96F4B6841710A9013966F76224BAE01FB4B4D1;
struct Dictionary_2_tBA1E03CB53B3CA099FA6591BF882ED2ACA81B41C;
struct Dictionary_2_tEE02057113A88692CEC84BBCEB9E74DC6CC243E0;
struct Dictionary_2_t384D2A0FF42BF1302A3F513FC32DB105F1CD5455;
struct IEqualityComparer_1_tAE94C8F24AD5B94D4EE85CA9FC59E3409D41CAF7;
struct IEqualityComparer_1_t0C79004BFE79D9DBCE6C2250109D31D468A9A68E;
struct KeyCollection_t9CA2658D7703CF12201322B2207C2A72A0982086;
struct KeyCollection_tE2F56A37776137F5D3C1BA6C49999EF1EC0E216F;
struct ValueCollection_tFA5EC6EB07D465AB4794D2AF8F1F1B641C6E15C3;
struct ValueCollection_tAD56DF94D7373625A22E80E942606DC8509CB6D2;
struct EntryU5BU5D_t1AFC81F86E7AD779AE2DD7AE79C1FDE089872DDB;
struct EntryU5BU5D_tD80C7495BA8BE64BC139A54FA961AB3EC66D7319;
struct ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031;
struct CharU5BU5D_t799905CF001DD5F13F7DBB310181FC4D8B7D0AAB;
struct DelegateU5BU5D_tC5AB7E8F745616680F337909D3A8E6C722CDF771;
struct Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C;
struct IntPtrU5BU5D_tFD177F8C806A6921AD7150264CCC62FA00CAD832;
struct StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF;
struct TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB;
struct ArgumentException_tAD90411542A20A9C72D5CDA3A84181D8B947A263;
struct ArgumentOutOfRangeException_tEA2822DAF62B10EEED00E0E3A341D4BAF78CF85F;
struct Binder_t91BFCE95A7057FADF4D8A1A342AFE52872246235;
struct ByteArrayAllocator_tCC5100EDA86D010D5A482B26279BB51ADA09B633;
struct ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641;
struct ByteBufferAllocator_tCC19A5DCD32535F213BA3EDECBB08A7F9EFD6185;
struct CodePageDataItem_t52460FA30AE37F4F26ACB81055E58002262F19F2;
struct DecoderFallback_t7324102215E4ED41EC065C02EB501CB0BC23CD90;
struct Delegate_t;
struct DelegateData_t9B286B493293CD2D23A5B2B5EF0E5B1324C2B77E;
struct EncoderFallback_tD2C40CE114AA9D8E1F7196608B2D088548015293;
struct Encoding_t65CDEF28CF20A7B8C92E85A4E808920C2465F095;
struct Exception_t;
struct FlatBufferBuilder_t23EDF7F7B5CFBF2A0BBCC2CCE6D14C02BE050CB0;
struct FlatBuffersWarpObject_tB42739917C0D90B1960CCFA4D658EA84ED269C6F;
struct IDictionary_t6D03155AF1FA9083817AA5B6AD7DEEACC26AB220;
struct IndexOutOfRangeException_t7ECB35264FB6CA8FAA516BD958F4B2ADC78E8A82;
struct InvalidOperationException_t5DDE4D49B7405FAAB1E4576F4715A42A3FAD4BAB;
struct MemberFilter_tF644F1AE82F611B677CE1964D5A3277DDA21D553;
struct MethodInfo_t;
struct Options_t2C469D7B05D4F50066C5316A778A04C4DF53F088;
struct SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6;
struct String_t;
struct Type_t;
struct Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864;
struct VerifyTableAction_t4EE7F0547499A39B8176F648F806406AB26487AB;
struct VerifyUnionAction_t41231FB98151184E37EF5626A85224925AC03F23;
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915;

IL2CPP_EXTERN_C RuntimeClass* ArgumentException_tAD90411542A20A9C72D5CDA3A84181D8B947A263_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* ArgumentOutOfRangeException_tEA2822DAF62B10EEED00E0E3A341D4BAF78CF85F_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* ByteArrayAllocator_tCC5100EDA86D010D5A482B26279BB51ADA09B633_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Console_t5EDF9498D011BD48287171978EDBBA6964829C3E_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Convert_t7097FF336D592F7C06D88A98349A44646F91EFFC_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Dictionary_2_t384D2A0FF42BF1302A3F513FC32DB105F1CD5455_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Exception_t_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* IndexOutOfRangeException_t7ECB35264FB6CA8FAA516BD958F4B2ADC78E8A82_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* InvalidOperationException_t5DDE4D49B7405FAAB1E4576F4715A42A3FAD4BAB_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Options_t2C469D7B05D4F50066C5316A778A04C4DF53F088_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* String_t_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Type_t_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* VerifyTableAction_t4EE7F0547499A39B8176F648F806406AB26487AB_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeField* Struct_t80ED167B1083AFA71911B4732EFBAC86E1D17CA0____U3CbbU3Ek__BackingField_FieldInfo_var;
IL2CPP_EXTERN_C RuntimeField* Table_t2274EC2D38DA186BEAB1F75E04F8401533DB9815____U3CbbU3Ek__BackingField_FieldInfo_var;
IL2CPP_EXTERN_C String_t* _stringLiteral1B9CAE50CA32CE68AA9F00077C1C9A89639E7214;
IL2CPP_EXTERN_C String_t* _stringLiteral2205190534B4BE18F7F950DFE08B06C83FB45673;
IL2CPP_EXTERN_C String_t* _stringLiteral2BDD4D5F5701CF6858D53C77EF8AA4E2BB1DE24B;
IL2CPP_EXTERN_C String_t* _stringLiteral3228EDC2035E419A5666E21CC182B6179D1E2E00;
IL2CPP_EXTERN_C String_t* _stringLiteral42AE8812EFD84589E3176177109FE0F328DC8CBB;
IL2CPP_EXTERN_C String_t* _stringLiteral44372B7E3F3252B67169F88E9A2F221315E6A635;
IL2CPP_EXTERN_C String_t* _stringLiteral4B33AF065A94EDF87433444D126A8EA5295CF031;
IL2CPP_EXTERN_C String_t* _stringLiteral78DEF0AE63CBB76D037109AC2BDC00D5F2AEEBD5;
IL2CPP_EXTERN_C String_t* _stringLiteralD1680709E54C5E6335DFDBFD1FE2B1A90F93EBBF;
IL2CPP_EXTERN_C const RuntimeMethod* ByteArrayAllocator_GrowFront_mFD75F8C69430AD2566AA13EC8885AFEAD545633C_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ByteBuffer_ToArray_TisByte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_m69965C32A36797FCB564A989DC566EE255B4DFB1_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Dictionary_2_Add_mD091A436B1895D7A2D6C1848C6C84487C5873904_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Dictionary_2_Clear_mA0B5ABBC9D22FF2ADCBD908A1BCD8AAA183BEDF8_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Dictionary_2__ctor_m0AF6A9E01056850C9F7AE464B4099CA3F6D5E8EC_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* FlatBufferBuilder_AddOffset_m7A64D677E7BE356B284C1DD84A7FD6E8C7DDCA27_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* FlatBufferBuilder_EndTable_m176D804DB1A019B3AF4735EBE7FECDD766E4CA73_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* FlatBufferBuilder_Nested_m45AE882A96E0B4309916DD3095168360CBFAE397_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* FlatBufferBuilder_NotNested_m550AD28D180BF3E1C547BB535382BCE59B9BADB2_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* FlatBufferBuilder_Slot_mCF8F59D561C30C152A31A9754AA66D7A4E205B46_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* FlatBufferBuilder_StartTable_m9EB86E88900E2C9419CFF54C05518FA2C89121ED_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* MemoryMarshal_GetReference_TisByte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_m81BE3C6812CE881C00AAA80CCFC9349F754F63A6_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* MemoryMarshal_GetReference_TisByte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_m9D86D3A7A7F6A344D16464E6638E2BEAD3F4BC90_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* MemoryMarshal_Read_TisUInt16_tF4C148C876015C212FD72652D0B6ED8CC247A455_mE522E06A28DD43DAED8B42666149274B433F5317_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* MemoryMarshal_Read_TisUInt32_t1833D51FFA667B18A5AA4B8D34DE284F8495D29B_m56C749731FAD055AC5894D97F107FF8E5C6A13AE_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* MemoryMarshal_Read_TisUInt64_t8F12534CC8FC4B5860F2A2CD1EE79D322E7A41AF_m4B7CEC36F79FB7BE35EB5ADC5B3F9B03A427FAD0_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* MemoryMarshal_Write_TisUInt16_tF4C148C876015C212FD72652D0B6ED8CC247A455_m1CB9CA69ED7F15E1AED66F7E98D918E1DF0CB96A_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* MemoryMarshal_Write_TisUInt32_t1833D51FFA667B18A5AA4B8D34DE284F8495D29B_m2BD505F8011143B77CB9A048F4C97C64130CB6DB_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* MemoryMarshal_Write_TisUInt64_t8F12534CC8FC4B5860F2A2CD1EE79D322E7A41AF_m369E3011D17AFC47FE8EBD775DA05E3F136D3EA3_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Memory_1_CopyTo_mF4EEE351944C3FF14C3EC6576AD5FD3CF72D850C_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Memory_1_get_Length_mA63190F8E6F8A531AA0A55447D48210D8083B714_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Memory_1_get_Span_mA0CAB13956D6FA3BBF9F9176CB647933F88E034E_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Memory_1_op_Implicit_m40D10666CBBA1748CA281BAB5EDA02E8525D1B3C_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ReadOnlySpan_1_Slice_mC8B7C665F49384744642F03EA355239F0E4AF966_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ReadOnlySpan_1_get_Length_m54864A0BB817050A9110E85BB5FB31EF63699982_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* RuntimeHelpers_GetSubArray_TisByte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_m6131C565343BE25EB8BD5A32EFB89FD9C1E68BD2_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Span_1_Slice_m720734AA48ECB663CAA0594530927B9015A64341_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Span_1_Slice_m9D8BA8245B8DC9BFB4A4164759CBAAEAD1318CD6_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Span_1_get_Length_m8E944E4954E037877A25B9FF6B901F1F901D4769_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Span_1_op_Implicit_mD249188242C0C9D3192A31E9F7FA74C683F05B84_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Verifier_BufferHasIdentifier_m47A5B9E1944079E3B21B67886594DD78A79F9F76_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Verifier_CheckStringFunc_mA69C648CF9A9C67C33D1FAA25111ECE5EB1EE237_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeType* Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* Char_t521A6F19B456D956AF452D926C32709DC03D6B17_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* Double_tE150EF3D1D43DEE85D533810AB4C742307EEDE5F_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* Int16_tB8EF286A9C33492FA6E6D6E67320BE93E794A175_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* Int64_t092CFB123BE63C28ACDAF65C68F21A526050DBA3_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* SByte_tFEFFEF5D2FEBF5207950AE6FAC150FC53B668DB5_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* Single_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* Struct_t80ED167B1083AFA71911B4732EFBAC86E1D17CA0_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* Table_t2274EC2D38DA186BEAB1F75E04F8401533DB9815_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* UInt16_tF4C148C876015C212FD72652D0B6ED8CC247A455_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* UInt32_t1833D51FFA667B18A5AA4B8D34DE284F8495D29B_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* UInt64_t8F12534CC8FC4B5860F2A2CD1EE79D322E7A41AF_0_0_0_var;
struct Delegate_t_marshaled_com;
struct Delegate_t_marshaled_pinvoke;
struct Exception_t_marshaled_com;
struct Exception_t_marshaled_pinvoke;

struct ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031;
struct DelegateU5BU5D_tC5AB7E8F745616680F337909D3A8E6C722CDF771;
struct Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C;

IL2CPP_EXTERN_C_BEGIN
IL2CPP_EXTERN_C_END

#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
struct U3CModuleU3E_tFFA48EE44CD49BBE40C73F56110D5A29126EA11E 
{
};
struct Dictionary_2_tEE02057113A88692CEC84BBCEB9E74DC6CC243E0  : public RuntimeObject
{
	Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ____buckets;
	EntryU5BU5D_t1AFC81F86E7AD779AE2DD7AE79C1FDE089872DDB* ____entries;
	int32_t ____count;
	int32_t ____freeList;
	int32_t ____freeCount;
	int32_t ____version;
	RuntimeObject* ____comparer;
	KeyCollection_t9CA2658D7703CF12201322B2207C2A72A0982086* ____keys;
	ValueCollection_tFA5EC6EB07D465AB4794D2AF8F1F1B641C6E15C3* ____values;
	RuntimeObject* ____syncRoot;
};
struct Dictionary_2_t384D2A0FF42BF1302A3F513FC32DB105F1CD5455  : public RuntimeObject
{
	Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ____buckets;
	EntryU5BU5D_tD80C7495BA8BE64BC139A54FA961AB3EC66D7319* ____entries;
	int32_t ____count;
	int32_t ____freeList;
	int32_t ____freeCount;
	int32_t ____version;
	RuntimeObject* ____comparer;
	KeyCollection_tE2F56A37776137F5D3C1BA6C49999EF1EC0E216F* ____keys;
	ValueCollection_tAD56DF94D7373625A22E80E942606DC8509CB6D2* ____values;
	RuntimeObject* ____syncRoot;
};
struct MemoryManager_1_tB90442C8E0A1B9C0F8A3B603FD50501A1BADAC6E  : public RuntimeObject
{
};
struct BitConverter_t6E99605185963BC12B3D369E13F2B88997E64A27  : public RuntimeObject
{
};
struct ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641  : public RuntimeObject
{
	ByteBufferAllocator_tCC19A5DCD32535F213BA3EDECBB08A7F9EFD6185* ____buffer;
	int32_t ____pos;
};
struct ByteBufferAllocator_tCC19A5DCD32535F213BA3EDECBB08A7F9EFD6185  : public RuntimeObject
{
	int32_t ___U3CLengthU3Ek__BackingField;
};
struct Encoding_t65CDEF28CF20A7B8C92E85A4E808920C2465F095  : public RuntimeObject
{
	int32_t ___m_codePage;
	CodePageDataItem_t52460FA30AE37F4F26ACB81055E58002262F19F2* ___dataItem;
	bool ___m_deserializedFromEverett;
	bool ___m_isReadOnly;
	EncoderFallback_tD2C40CE114AA9D8E1F7196608B2D088548015293* ___encoderFallback;
	DecoderFallback_t7324102215E4ED41EC065C02EB501CB0BC23CD90* ___decoderFallback;
};
struct FlatBufferBuilder_t23EDF7F7B5CFBF2A0BBCC2CCE6D14C02BE050CB0  : public RuntimeObject
{
	int32_t ____space;
	ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* ____bb;
	int32_t ____minAlign;
	Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ____vtable;
	int32_t ____vtableSize;
	int32_t ____objectStart;
	Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ____vtables;
	int32_t ____numVtables;
	int32_t ____vectorNumElems;
	Dictionary_2_tEE02057113A88692CEC84BBCEB9E74DC6CC243E0* ____sharedStringMap;
	bool ___U3CForceDefaultsU3Ek__BackingField;
};
struct FlatBufferConstants_tC8C204AE80F667D3C1B415D0B5013CA73C8B2A2E  : public RuntimeObject
{
};
struct FlatBuffersWarpObject_tB42739917C0D90B1960CCFA4D658EA84ED269C6F  : public RuntimeObject
{
};
struct MemberInfo_t  : public RuntimeObject
{
};
struct Options_t2C469D7B05D4F50066C5316A778A04C4DF53F088  : public RuntimeObject
{
	int32_t ___max_depth;
	int32_t ___max_tables;
	bool ___string_end_check;
	bool ___alignment_check;
};
struct String_t  : public RuntimeObject
{
	int32_t ____stringLength;
	Il2CppChar ____firstChar;
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F  : public RuntimeObject
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_pinvoke
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_com
{
};
struct Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864  : public RuntimeObject
{
	ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* ___verifier_buffer;
	Options_t2C469D7B05D4F50066C5316A778A04C4DF53F088* ___verifier_options;
	int32_t ___depth_cnt;
	int32_t ___num_tables_cnt;
};
struct Memory_1_tB7CEF4416F5014E364267478CEF016A4AC5C0036 
{
	RuntimeObject* ____object;
	int32_t ____index;
	int32_t ____length;
};
#ifndef Memory_1_t56F63672B8E752B13E0BBBBD034BA3C1F6CFDC17_marshaled_pinvoke_define
#define Memory_1_t56F63672B8E752B13E0BBBBD034BA3C1F6CFDC17_marshaled_pinvoke_define
struct Memory_1_t56F63672B8E752B13E0BBBBD034BA3C1F6CFDC17_marshaled_pinvoke
{
	Il2CppIUnknown* ____object;
	int32_t ____index;
	int32_t ____length;
};
#endif
#ifndef Memory_1_t56F63672B8E752B13E0BBBBD034BA3C1F6CFDC17_marshaled_com_define
#define Memory_1_t56F63672B8E752B13E0BBBBD034BA3C1F6CFDC17_marshaled_com_define
struct Memory_1_t56F63672B8E752B13E0BBBBD034BA3C1F6CFDC17_marshaled_com
{
	Il2CppIUnknown* ____object;
	int32_t ____index;
	int32_t ____length;
};
#endif
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22 
{
	bool ___m_value;
};
struct Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3 
{
	uint8_t ___m_value;
};
struct Char_t521A6F19B456D956AF452D926C32709DC03D6B17 
{
	Il2CppChar ___m_value;
};
struct Double_tE150EF3D1D43DEE85D533810AB4C742307EEDE5F 
{
	double ___m_value;
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2  : public ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F
{
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2_marshaled_pinvoke
{
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2_marshaled_com
{
};
struct Index_t5614B28257879876E049288D09C47602B22F2301 
{
	int32_t ____value;
};
struct Int16_tB8EF286A9C33492FA6E6D6E67320BE93E794A175 
{
	int16_t ___m_value;
};
struct Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C 
{
	int32_t ___m_value;
};
struct Int64_t092CFB123BE63C28ACDAF65C68F21A526050DBA3 
{
	int64_t ___m_value;
};
struct IntPtr_t 
{
	void* ___m_value;
};
struct SByte_tFEFFEF5D2FEBF5207950AE6FAC150FC53B668DB5 
{
	int8_t ___m_value;
};
struct Single_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C 
{
	float ___m_value;
};
struct StringOffset_t5F8A2A2DB7065B675FCE7E3337C174214CC4FB4E 
{
	int32_t ___Value;
};
struct Struct_t80ED167B1083AFA71911B4732EFBAC86E1D17CA0 
{
	int32_t ___U3Cbb_posU3Ek__BackingField;
	ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* ___U3CbbU3Ek__BackingField;
};
struct Struct_t80ED167B1083AFA71911B4732EFBAC86E1D17CA0_marshaled_pinvoke
{
	int32_t ___U3Cbb_posU3Ek__BackingField;
	ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* ___U3CbbU3Ek__BackingField;
};
struct Struct_t80ED167B1083AFA71911B4732EFBAC86E1D17CA0_marshaled_com
{
	int32_t ___U3Cbb_posU3Ek__BackingField;
	ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* ___U3CbbU3Ek__BackingField;
};
struct Table_t2274EC2D38DA186BEAB1F75E04F8401533DB9815 
{
	int32_t ___U3Cbb_posU3Ek__BackingField;
	ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* ___U3CbbU3Ek__BackingField;
};
struct Table_t2274EC2D38DA186BEAB1F75E04F8401533DB9815_marshaled_pinvoke
{
	int32_t ___U3Cbb_posU3Ek__BackingField;
	ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* ___U3CbbU3Ek__BackingField;
};
struct Table_t2274EC2D38DA186BEAB1F75E04F8401533DB9815_marshaled_com
{
	int32_t ___U3Cbb_posU3Ek__BackingField;
	ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* ___U3CbbU3Ek__BackingField;
};
struct UInt16_tF4C148C876015C212FD72652D0B6ED8CC247A455 
{
	uint16_t ___m_value;
};
struct UInt32_t1833D51FFA667B18A5AA4B8D34DE284F8495D29B 
{
	uint32_t ___m_value;
};
struct UInt64_t8F12534CC8FC4B5860F2A2CD1EE79D322E7A41AF 
{
	uint64_t ___m_value;
};
struct VectorOffset_t25D49B46B3BAE5ABE18C23B5FF581A20699F96F4 
{
	int32_t ___Value;
};
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915 
{
	union
	{
		struct
		{
		};
		uint8_t Void_t4861ACF8F4594C3437BB48B6E56783494B843915__padding[1];
	};
};
struct checkElementStruct_tCF75802323BACA21C0D2C66C2531CC19F664B49B 
{
	bool ___elementValid;
	uint32_t ___elementOffset;
};
struct checkElementStruct_tCF75802323BACA21C0D2C66C2531CC19F664B49B_marshaled_pinvoke
{
	int32_t ___elementValid;
	uint32_t ___elementOffset;
};
struct checkElementStruct_tCF75802323BACA21C0D2C66C2531CC19F664B49B_marshaled_com
{
	int32_t ___elementValid;
	uint32_t ___elementOffset;
};
struct ByReference_1_t9C85BCCAAF8C525B6C06B07E922D8D217BE8D6FC 
{
	intptr_t ____value;
};
struct ByteArrayAllocator_tCC5100EDA86D010D5A482B26279BB51ADA09B633  : public ByteBufferAllocator_tCC19A5DCD32535F213BA3EDECBB08A7F9EFD6185
{
	Memory_1_tB7CEF4416F5014E364267478CEF016A4AC5C0036 ____buffer;
};
struct Delegate_t  : public RuntimeObject
{
	intptr_t ___method_ptr;
	intptr_t ___invoke_impl;
	RuntimeObject* ___m_target;
	intptr_t ___method;
	intptr_t ___delegate_trampoline;
	intptr_t ___extra_arg;
	intptr_t ___method_code;
	intptr_t ___interp_method;
	intptr_t ___interp_invoke_impl;
	MethodInfo_t* ___method_info;
	MethodInfo_t* ___original_method_info;
	DelegateData_t9B286B493293CD2D23A5B2B5EF0E5B1324C2B77E* ___data;
	bool ___method_is_virtual;
};
struct Delegate_t_marshaled_pinvoke
{
	intptr_t ___method_ptr;
	intptr_t ___invoke_impl;
	Il2CppIUnknown* ___m_target;
	intptr_t ___method;
	intptr_t ___delegate_trampoline;
	intptr_t ___extra_arg;
	intptr_t ___method_code;
	intptr_t ___interp_method;
	intptr_t ___interp_invoke_impl;
	MethodInfo_t* ___method_info;
	MethodInfo_t* ___original_method_info;
	DelegateData_t9B286B493293CD2D23A5B2B5EF0E5B1324C2B77E* ___data;
	int32_t ___method_is_virtual;
};
struct Delegate_t_marshaled_com
{
	intptr_t ___method_ptr;
	intptr_t ___invoke_impl;
	Il2CppIUnknown* ___m_target;
	intptr_t ___method;
	intptr_t ___delegate_trampoline;
	intptr_t ___extra_arg;
	intptr_t ___method_code;
	intptr_t ___interp_method;
	intptr_t ___interp_invoke_impl;
	MethodInfo_t* ___method_info;
	MethodInfo_t* ___original_method_info;
	DelegateData_t9B286B493293CD2D23A5B2B5EF0E5B1324C2B77E* ___data;
	int32_t ___method_is_virtual;
};
struct Exception_t  : public RuntimeObject
{
	String_t* ____className;
	String_t* ____message;
	RuntimeObject* ____data;
	Exception_t* ____innerException;
	String_t* ____helpURL;
	RuntimeObject* ____stackTrace;
	String_t* ____stackTraceString;
	String_t* ____remoteStackTraceString;
	int32_t ____remoteStackIndex;
	RuntimeObject* ____dynamicMethods;
	int32_t ____HResult;
	String_t* ____source;
	SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6* ____safeSerializationManager;
	StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF* ___captured_traces;
	IntPtrU5BU5D_tFD177F8C806A6921AD7150264CCC62FA00CAD832* ___native_trace_ips;
	int32_t ___caught_in_unmanaged;
};
struct Exception_t_marshaled_pinvoke
{
	char* ____className;
	char* ____message;
	RuntimeObject* ____data;
	Exception_t_marshaled_pinvoke* ____innerException;
	char* ____helpURL;
	Il2CppIUnknown* ____stackTrace;
	char* ____stackTraceString;
	char* ____remoteStackTraceString;
	int32_t ____remoteStackIndex;
	Il2CppIUnknown* ____dynamicMethods;
	int32_t ____HResult;
	char* ____source;
	SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6* ____safeSerializationManager;
	StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF* ___captured_traces;
	Il2CppSafeArray* ___native_trace_ips;
	int32_t ___caught_in_unmanaged;
};
struct Exception_t_marshaled_com
{
	Il2CppChar* ____className;
	Il2CppChar* ____message;
	RuntimeObject* ____data;
	Exception_t_marshaled_com* ____innerException;
	Il2CppChar* ____helpURL;
	Il2CppIUnknown* ____stackTrace;
	Il2CppChar* ____stackTraceString;
	Il2CppChar* ____remoteStackTraceString;
	int32_t ____remoteStackIndex;
	Il2CppIUnknown* ____dynamicMethods;
	int32_t ____HResult;
	Il2CppChar* ____source;
	SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6* ____safeSerializationManager;
	StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF* ___captured_traces;
	Il2CppSafeArray* ___native_trace_ips;
	int32_t ___caught_in_unmanaged;
};
struct ExceptionArgument_t60E7F8D9DE5362CBE9365893983C30302D83B778 
{
	int32_t ___value__;
};
struct Range_t2E1D6269D6D386B091A4D3DD347B8D4C5A35F95F 
{
	Index_t5614B28257879876E049288D09C47602B22F2301 ___U3CStartU3Ek__BackingField;
	Index_t5614B28257879876E049288D09C47602B22F2301 ___U3CEndU3Ek__BackingField;
};
struct RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B 
{
	intptr_t ___value;
};
struct RawData_t37CAF2D3F74B7723974ED7CEEE9B297D8FA64ED0  : public RuntimeObject
{
	intptr_t ___Bounds;
	intptr_t ___Count;
	uint8_t ___Data;
};
struct RawData_t37CAF2D3F74B7723974ED7CEEE9B297D8FA64ED0_marshaled_pinvoke
{
	intptr_t ___Bounds;
	intptr_t ___Count;
	uint8_t ___Data;
};
struct RawData_t37CAF2D3F74B7723974ED7CEEE9B297D8FA64ED0_marshaled_com
{
	intptr_t ___Bounds;
	intptr_t ___Count;
	uint8_t ___Data;
};
struct ReadOnlySpan_1_tA850A6C0E88ABBA37646A078ACBC24D6D5FD9B4D 
{
	ByReference_1_t9C85BCCAAF8C525B6C06B07E922D8D217BE8D6FC ____pointer;
	int32_t ____length;
};
struct Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305 
{
	ByReference_1_t9C85BCCAAF8C525B6C06B07E922D8D217BE8D6FC ____pointer;
	int32_t ____length;
};
struct MulticastDelegate_t  : public Delegate_t
{
	DelegateU5BU5D_tC5AB7E8F745616680F337909D3A8E6C722CDF771* ___delegates;
};
struct MulticastDelegate_t_marshaled_pinvoke : public Delegate_t_marshaled_pinvoke
{
	Delegate_t_marshaled_pinvoke** ___delegates;
};
struct MulticastDelegate_t_marshaled_com : public Delegate_t_marshaled_com
{
	Delegate_t_marshaled_com** ___delegates;
};
struct SystemException_tCC48D868298F4C0705279823E34B00F4FBDB7295  : public Exception_t
{
};
struct Type_t  : public MemberInfo_t
{
	RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B ____impl;
};
struct ArgumentException_tAD90411542A20A9C72D5CDA3A84181D8B947A263  : public SystemException_tCC48D868298F4C0705279823E34B00F4FBDB7295
{
	String_t* ____paramName;
};
struct IndexOutOfRangeException_t7ECB35264FB6CA8FAA516BD958F4B2ADC78E8A82  : public SystemException_tCC48D868298F4C0705279823E34B00F4FBDB7295
{
};
struct InvalidOperationException_t5DDE4D49B7405FAAB1E4576F4715A42A3FAD4BAB  : public SystemException_tCC48D868298F4C0705279823E34B00F4FBDB7295
{
};
struct VerifyTableAction_t4EE7F0547499A39B8176F648F806406AB26487AB  : public MulticastDelegate_t
{
};
struct VerifyUnionAction_t41231FB98151184E37EF5626A85224925AC03F23  : public MulticastDelegate_t
{
};
struct ArgumentOutOfRangeException_tEA2822DAF62B10EEED00E0E3A341D4BAF78CF85F  : public ArgumentException_tAD90411542A20A9C72D5CDA3A84181D8B947A263
{
	RuntimeObject* ____actualValue;
};
struct BitConverter_t6E99605185963BC12B3D369E13F2B88997E64A27_StaticFields
{
	bool ___IsLittleEndian;
};
struct ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641_StaticFields
{
	Dictionary_2_t384D2A0FF42BF1302A3F513FC32DB105F1CD5455* ___genericSizes;
};
struct Encoding_t65CDEF28CF20A7B8C92E85A4E808920C2465F095_StaticFields
{
	Encoding_t65CDEF28CF20A7B8C92E85A4E808920C2465F095* ___defaultEncoding;
	Encoding_t65CDEF28CF20A7B8C92E85A4E808920C2465F095* ___unicodeEncoding;
	Encoding_t65CDEF28CF20A7B8C92E85A4E808920C2465F095* ___bigEndianUnicode;
	Encoding_t65CDEF28CF20A7B8C92E85A4E808920C2465F095* ___utf7Encoding;
	Encoding_t65CDEF28CF20A7B8C92E85A4E808920C2465F095* ___utf8Encoding;
	Encoding_t65CDEF28CF20A7B8C92E85A4E808920C2465F095* ___utf32Encoding;
	Encoding_t65CDEF28CF20A7B8C92E85A4E808920C2465F095* ___asciiEncoding;
	Encoding_t65CDEF28CF20A7B8C92E85A4E808920C2465F095* ___latin1Encoding;
	Dictionary_2_t87EDE08B2E48F793A22DE50D6B3CC2E7EBB2DB54* ___encodings;
	RuntimeObject* ___s_InternalSyncObject;
};
struct String_t_StaticFields
{
	String_t* ___Empty;
};
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_StaticFields
{
	String_t* ___TrueString;
	String_t* ___FalseString;
};
struct Char_t521A6F19B456D956AF452D926C32709DC03D6B17_StaticFields
{
	ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* ___s_categoryForLatin1;
};
struct IntPtr_t_StaticFields
{
	intptr_t ___Zero;
};
struct Exception_t_StaticFields
{
	RuntimeObject* ___s_EDILock;
};
struct Type_t_StaticFields
{
	Binder_t91BFCE95A7057FADF4D8A1A342AFE52872246235* ___s_defaultBinder;
	Il2CppChar ___Delimiter;
	TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* ___EmptyTypes;
	RuntimeObject* ___Missing;
	MemberFilter_tF644F1AE82F611B677CE1964D5A3277DDA21D553* ___FilterAttribute;
	MemberFilter_tF644F1AE82F611B677CE1964D5A3277DDA21D553* ___FilterName;
	MemberFilter_tF644F1AE82F611B677CE1964D5A3277DDA21D553* ___FilterNameIgnoreCase;
};
#ifdef __clang__
#pragma clang diagnostic pop
#endif
struct ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031  : public RuntimeArray
{
	ALIGN_FIELD (8) uint8_t m_Items[1];

	inline uint8_t GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline uint8_t* GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, uint8_t value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
	}
	inline uint8_t GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline uint8_t* GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, uint8_t value)
	{
		m_Items[index] = value;
	}
};
struct Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C  : public RuntimeArray
{
	ALIGN_FIELD (8) int32_t m_Items[1];

	inline int32_t GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline int32_t* GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, int32_t value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
	}
	inline int32_t GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline int32_t* GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, int32_t value)
	{
		m_Items[index] = value;
	}
};
struct DelegateU5BU5D_tC5AB7E8F745616680F337909D3A8E6C722CDF771  : public RuntimeArray
{
	ALIGN_FIELD (8) Delegate_t* m_Items[1];

	inline Delegate_t* GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline Delegate_t** GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, Delegate_t* value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
	inline Delegate_t* GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline Delegate_t** GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, Delegate_t* value)
	{
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
};


IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Memory_1_tB7CEF4416F5014E364267478CEF016A4AC5C0036 Memory_1_op_Implicit_m40D10666CBBA1748CA281BAB5EDA02E8525D1B3C_gshared (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* ___0_array, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* RuntimeHelpers_GetSubArray_TisByte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_m6131C565343BE25EB8BD5A32EFB89FD9C1E68BD2_gshared (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* ___0_array, Range_t2E1D6269D6D386B091A4D3DD347B8D4C5A35F95F ___1_range, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Memory_1_CopyTo_mF4EEE351944C3FF14C3EC6576AD5FD3CF72D850C_gshared (Memory_1_tB7CEF4416F5014E364267478CEF016A4AC5C0036* __this, Memory_1_tB7CEF4416F5014E364267478CEF016A4AC5C0036 ___0_destination, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305 Memory_1_get_Span_mA0CAB13956D6FA3BBF9F9176CB647933F88E034E_gshared_inline (Memory_1_tB7CEF4416F5014E364267478CEF016A4AC5C0036* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR ReadOnlySpan_1_tA850A6C0E88ABBA37646A078ACBC24D6D5FD9B4D Span_1_op_Implicit_mD249188242C0C9D3192A31E9F7FA74C683F05B84_gshared (Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305 ___0_span, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Memory_1_get_Length_mA63190F8E6F8A531AA0A55447D48210D8083B714_gshared (Memory_1_tB7CEF4416F5014E364267478CEF016A4AC5C0036* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* ByteBuffer_ToArray_TisByte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_m69965C32A36797FCB564A989DC566EE255B4DFB1_gshared (ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* __this, int32_t ___0_pos, int32_t ___1_len, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305 Span_1_Slice_m9D8BA8245B8DC9BFB4A4164759CBAAEAD1318CD6_gshared_inline (Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305* __this, int32_t ___0_start, int32_t ___1_length, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t Span_1_get_Length_m8E944E4954E037877A25B9FF6B901F1F901D4769_gshared_inline (Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint8_t* MemoryMarshal_GetReference_TisByte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_m81BE3C6812CE881C00AAA80CCFC9349F754F63A6_gshared (Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305 ___0_span, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305 Span_1_Slice_m720734AA48ECB663CAA0594530927B9015A64341_gshared_inline (Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305* __this, int32_t ___0_start, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR ReadOnlySpan_1_tA850A6C0E88ABBA37646A078ACBC24D6D5FD9B4D ReadOnlySpan_1_Slice_mC8B7C665F49384744642F03EA355239F0E4AF966_gshared_inline (ReadOnlySpan_1_tA850A6C0E88ABBA37646A078ACBC24D6D5FD9B4D* __this, int32_t ___0_start, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint8_t* MemoryMarshal_GetReference_TisByte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_m9D86D3A7A7F6A344D16464E6638E2BEAD3F4BC90_gshared (ReadOnlySpan_1_tA850A6C0E88ABBA37646A078ACBC24D6D5FD9B4D ___0_span, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Dictionary_2__ctor_m517E7F9D104FEAE6646EABDDC9C852510E86077C_gshared (Dictionary_2_t5C96F4B6841710A9013966F76224BAE01FB4B4D1* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Dictionary_2_Add_m63897227AFA7035F1772315ABBBE7FD0A250E10C_gshared (Dictionary_2_t5C96F4B6841710A9013966F76224BAE01FB4B4D1* __this, RuntimeObject* ___0_key, int32_t ___1_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Dictionary_2_Clear_m3D95640616D444D903C22215109B53D82971A8EE_gshared (Dictionary_2_tBA1E03CB53B3CA099FA6591BF882ED2ACA81B41C* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void MemoryMarshal_Write_TisUInt16_tF4C148C876015C212FD72652D0B6ED8CC247A455_m1CB9CA69ED7F15E1AED66F7E98D918E1DF0CB96A_gshared_inline (Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305 ___0_destination, uint16_t* ___1_value, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void MemoryMarshal_Write_TisUInt32_t1833D51FFA667B18A5AA4B8D34DE284F8495D29B_m2BD505F8011143B77CB9A048F4C97C64130CB6DB_gshared_inline (Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305 ___0_destination, uint32_t* ___1_value, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void MemoryMarshal_Write_TisUInt64_t8F12534CC8FC4B5860F2A2CD1EE79D322E7A41AF_m369E3011D17AFC47FE8EBD775DA05E3F136D3EA3_gshared_inline (Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305 ___0_destination, uint64_t* ___1_value, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint16_t MemoryMarshal_Read_TisUInt16_tF4C148C876015C212FD72652D0B6ED8CC247A455_mE522E06A28DD43DAED8B42666149274B433F5317_gshared_inline (ReadOnlySpan_1_tA850A6C0E88ABBA37646A078ACBC24D6D5FD9B4D ___0_source, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint32_t MemoryMarshal_Read_TisUInt32_t1833D51FFA667B18A5AA4B8D34DE284F8495D29B_m56C749731FAD055AC5894D97F107FF8E5C6A13AE_gshared_inline (ReadOnlySpan_1_tA850A6C0E88ABBA37646A078ACBC24D6D5FD9B4D ___0_source, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint64_t MemoryMarshal_Read_TisUInt64_t8F12534CC8FC4B5860F2A2CD1EE79D322E7A41AF_m4B7CEC36F79FB7BE35EB5ADC5B3F9B03A427FAD0_gshared_inline (ReadOnlySpan_1_tA850A6C0E88ABBA37646A078ACBC24D6D5FD9B4D ___0_source, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Span_1__ctor_m947BF95D54571BF3897F96822B7A8FDA5853497B_gshared_inline (Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305* __this, uint8_t* ___0_ptr, int32_t ___1_length, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Span_1__ctor_m698EC79E2E44AFF16BA096D0861CFB129FBF8218_gshared_inline (Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305* __this, ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* ___0_array, int32_t ___1_start, int32_t ___2_length, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void ReadOnlySpan_1__ctor_m0FC0B92549C2968E80B5F75A85F28B96DBFCFD63_gshared_inline (ReadOnlySpan_1_tA850A6C0E88ABBA37646A078ACBC24D6D5FD9B4D* __this, uint8_t* ___0_ptr, int32_t ___1_length, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t ReadOnlySpan_1_get_Length_m54864A0BB817050A9110E85BB5FB31EF63699982_gshared_inline (ReadOnlySpan_1_tA850A6C0E88ABBA37646A078ACBC24D6D5FD9B4D* __this, const RuntimeMethod* method) ;

IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2 (RuntimeObject* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ByteBufferAllocator__ctor_mA20B60936A23DFD8E7D134C8C522AB3916B91772 (ByteBufferAllocator_tCC19A5DCD32535F213BA3EDECBB08A7F9EFD6185* __this, const RuntimeMethod* method) ;
inline Memory_1_tB7CEF4416F5014E364267478CEF016A4AC5C0036 Memory_1_op_Implicit_m40D10666CBBA1748CA281BAB5EDA02E8525D1B3C (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* ___0_array, const RuntimeMethod* method)
{
	return ((  Memory_1_tB7CEF4416F5014E364267478CEF016A4AC5C0036 (*) (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*, const RuntimeMethod*))Memory_1_op_Implicit_m40D10666CBBA1748CA281BAB5EDA02E8525D1B3C_gshared)(___0_array, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ByteArrayAllocator_InitBuffer_m3A5840E5A780906C338F6D7A4F8D5612401B698A (ByteArrayAllocator_tCC5100EDA86D010D5A482B26279BB51ADA09B633* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t ByteBufferAllocator_get_Length_mC038E63A664724F0965D2B7B57899F1BDB1DCE2A_inline (ByteBufferAllocator_tCC19A5DCD32535F213BA3EDECBB08A7F9EFD6185* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Exception__ctor_m9B2BD92CD68916245A75109105D9071C9D430E7F (Exception_t* __this, String_t* ___0_message, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Index_t5614B28257879876E049288D09C47602B22F2301 Index_op_Implicit_m56AB6B8ACC5B4BFCED7FE0D75FFD117F7F9AA360 (int32_t ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Range_t2E1D6269D6D386B091A4D3DD347B8D4C5A35F95F Range_StartAt_mB7FF114A0C730EA7531982299ABB9C108C07F1D6 (Index_t5614B28257879876E049288D09C47602B22F2301 ___0_start, const RuntimeMethod* method) ;
inline ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* RuntimeHelpers_GetSubArray_TisByte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_m6131C565343BE25EB8BD5A32EFB89FD9C1E68BD2 (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* ___0_array, Range_t2E1D6269D6D386B091A4D3DD347B8D4C5A35F95F ___1_range, const RuntimeMethod* method)
{
	return ((  ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* (*) (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*, Range_t2E1D6269D6D386B091A4D3DD347B8D4C5A35F95F, const RuntimeMethod*))RuntimeHelpers_GetSubArray_TisByte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_m6131C565343BE25EB8BD5A32EFB89FD9C1E68BD2_gshared)(___0_array, ___1_range, method);
}
inline void Memory_1_CopyTo_mF4EEE351944C3FF14C3EC6576AD5FD3CF72D850C (Memory_1_tB7CEF4416F5014E364267478CEF016A4AC5C0036* __this, Memory_1_tB7CEF4416F5014E364267478CEF016A4AC5C0036 ___0_destination, const RuntimeMethod* method)
{
	((  void (*) (Memory_1_tB7CEF4416F5014E364267478CEF016A4AC5C0036*, Memory_1_tB7CEF4416F5014E364267478CEF016A4AC5C0036, const RuntimeMethod*))Memory_1_CopyTo_mF4EEE351944C3FF14C3EC6576AD5FD3CF72D850C_gshared)(__this, ___0_destination, method);
}
inline Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305 Memory_1_get_Span_mA0CAB13956D6FA3BBF9F9176CB647933F88E034E_inline (Memory_1_tB7CEF4416F5014E364267478CEF016A4AC5C0036* __this, const RuntimeMethod* method)
{
	return ((  Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305 (*) (Memory_1_tB7CEF4416F5014E364267478CEF016A4AC5C0036*, const RuntimeMethod*))Memory_1_get_Span_mA0CAB13956D6FA3BBF9F9176CB647933F88E034E_gshared_inline)(__this, method);
}
inline ReadOnlySpan_1_tA850A6C0E88ABBA37646A078ACBC24D6D5FD9B4D Span_1_op_Implicit_mD249188242C0C9D3192A31E9F7FA74C683F05B84 (Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305 ___0_span, const RuntimeMethod* method)
{
	return ((  ReadOnlySpan_1_tA850A6C0E88ABBA37646A078ACBC24D6D5FD9B4D (*) (Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305, const RuntimeMethod*))Span_1_op_Implicit_mD249188242C0C9D3192A31E9F7FA74C683F05B84_gshared)(___0_span, method);
}
inline int32_t Memory_1_get_Length_mA63190F8E6F8A531AA0A55447D48210D8083B714 (Memory_1_tB7CEF4416F5014E364267478CEF016A4AC5C0036* __this, const RuntimeMethod* method)
{
	return ((  int32_t (*) (Memory_1_tB7CEF4416F5014E364267478CEF016A4AC5C0036*, const RuntimeMethod*))Memory_1_get_Length_mA63190F8E6F8A531AA0A55447D48210D8083B714_gshared)(__this, method);
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void ByteBufferAllocator_set_Length_m17C45CA27C651A79001AED91E6AAA13B575F38C9_inline (ByteBufferAllocator_tCC19A5DCD32535F213BA3EDECBB08A7F9EFD6185* __this, int32_t ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ByteBuffer__ctor_m4B87CF31CBB963E34E96F16585FCA444DF949921 (ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* __this, ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* ___0_buffer, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ByteBuffer__ctor_mEE1915E69AC2062777C33B4393DDC957749C9FF5 (ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* __this, ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* ___0_buffer, int32_t ___1_pos, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ByteArrayAllocator__ctor_m64D6FFED315D68A28E03F3C7B0F57248603C0AA8 (ByteArrayAllocator_tCC5100EDA86D010D5A482B26279BB51ADA09B633* __this, ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* ___0_buffer, const RuntimeMethod* method) ;
inline ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* ByteBuffer_ToArray_TisByte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_m69965C32A36797FCB564A989DC566EE255B4DFB1 (ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* __this, int32_t ___0_pos, int32_t ___1_len, const RuntimeMethod* method)
{
	return ((  ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* (*) (ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641*, int32_t, int32_t, const RuntimeMethod*))ByteBuffer_ToArray_TisByte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_m69965C32A36797FCB564A989DC566EE255B4DFB1_gshared)(__this, ___0_pos, ___1_len, method);
}
inline Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305 Span_1_Slice_m9D8BA8245B8DC9BFB4A4164759CBAAEAD1318CD6_inline (Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305* __this, int32_t ___0_start, int32_t ___1_length, const RuntimeMethod* method)
{
	return ((  Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305 (*) (Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305*, int32_t, int32_t, const RuntimeMethod*))Span_1_Slice_m9D8BA8245B8DC9BFB4A4164759CBAAEAD1318CD6_gshared_inline)(__this, ___0_start, ___1_length, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ByteBuffer_AssertOffsetAndLength_m7DE914630999CC751A304662915A714328B53AC7 (ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* __this, int32_t ___0_offset, int32_t ___1_length, const RuntimeMethod* method) ;
inline int32_t Span_1_get_Length_m8E944E4954E037877A25B9FF6B901F1F901D4769_inline (Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305* __this, const RuntimeMethod* method)
{
	return ((  int32_t (*) (Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305*, const RuntimeMethod*))Span_1_get_Length_m8E944E4954E037877A25B9FF6B901F1F901D4769_gshared_inline)(__this, method);
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t String_get_Length_m42625D67623FA5CC7A44D47425CE86FB946542D2_inline (String_t* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t RuntimeHelpers_get_OffsetToStringData_m90A5D27EF88BE9432BF7093B7D7E7A0ACB0A8FBD (const RuntimeMethod* method) ;
inline uint8_t* MemoryMarshal_GetReference_TisByte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_m81BE3C6812CE881C00AAA80CCFC9349F754F63A6 (Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305 ___0_span, const RuntimeMethod* method)
{
	return ((  uint8_t* (*) (Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305, const RuntimeMethod*))MemoryMarshal_GetReference_TisByte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_m81BE3C6812CE881C00AAA80CCFC9349F754F63A6_gshared)(___0_span, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Encoding_t65CDEF28CF20A7B8C92E85A4E808920C2465F095* Encoding_get_UTF8_m9FA98A53CE96FD6D02982625C5246DD36C1235C9 (const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t ByteBuffer_get_Length_mD83481CCE00F09128DBAC206A094C628E635BA8B (ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ByteBuffer_PutUshort_m3FAF834DEE05A58095F7851DEA351C8AAC6B09C4 (ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* __this, int32_t ___0_offset, uint16_t ___1_value, const RuntimeMethod* method) ;
inline Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305 Span_1_Slice_m720734AA48ECB663CAA0594530927B9015A64341_inline (Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305* __this, int32_t ___0_start, const RuntimeMethod* method)
{
	return ((  Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305 (*) (Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305*, int32_t, const RuntimeMethod*))Span_1_Slice_m720734AA48ECB663CAA0594530927B9015A64341_gshared_inline)(__this, ___0_start, method);
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void BinaryPrimitives_WriteUInt16LittleEndian_mEA236B05E65D485C1934CCC155A6AF8A5C66773A_inline (Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305 ___0_destination, uint16_t ___1_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ByteBuffer_PutUint_m4FE24EA7B76E8405CD82B65841609BBBAF6C9F4C (ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* __this, int32_t ___0_offset, uint32_t ___1_value, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void BinaryPrimitives_WriteUInt32LittleEndian_mF5A685773CF1618F130D77F5072BDB7C389793B1_inline (Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305 ___0_destination, uint32_t ___1_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ByteBuffer_PutUlong_m0E9B506A058766F3BB5D8BF01B10AFE695CC6C9F (ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* __this, int32_t ___0_offset, uint64_t ___1_value, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void BinaryPrimitives_WriteUInt64LittleEndian_mCC039600290A6A5201532593AF3BE095D74C1625_inline (Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305 ___0_destination, uint64_t ___1_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint32_t ByteBuffer_ReverseBytes_m36121CFC834DEE45DB23F98CA3864D14229D6DAC (uint32_t ___0_input, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint64_t ByteBuffer_ReverseBytes_mB15365C2362720EE11ACA5BE0B00B26D4CDA3DC0 (uint64_t ___0_input, const RuntimeMethod* method) ;
inline ReadOnlySpan_1_tA850A6C0E88ABBA37646A078ACBC24D6D5FD9B4D ReadOnlySpan_1_Slice_mC8B7C665F49384744642F03EA355239F0E4AF966_inline (ReadOnlySpan_1_tA850A6C0E88ABBA37646A078ACBC24D6D5FD9B4D* __this, int32_t ___0_start, const RuntimeMethod* method)
{
	return ((  ReadOnlySpan_1_tA850A6C0E88ABBA37646A078ACBC24D6D5FD9B4D (*) (ReadOnlySpan_1_tA850A6C0E88ABBA37646A078ACBC24D6D5FD9B4D*, int32_t, const RuntimeMethod*))ReadOnlySpan_1_Slice_mC8B7C665F49384744642F03EA355239F0E4AF966_gshared_inline)(__this, ___0_start, method);
}
inline uint8_t* MemoryMarshal_GetReference_TisByte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_m9D86D3A7A7F6A344D16464E6638E2BEAD3F4BC90 (ReadOnlySpan_1_tA850A6C0E88ABBA37646A078ACBC24D6D5FD9B4D ___0_span, const RuntimeMethod* method)
{
	return ((  uint8_t* (*) (ReadOnlySpan_1_tA850A6C0E88ABBA37646A078ACBC24D6D5FD9B4D, const RuntimeMethod*))MemoryMarshal_GetReference_TisByte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_m9D86D3A7A7F6A344D16464E6638E2BEAD3F4BC90_gshared)(___0_span, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* Encoding_GetString_m42BFF0862341DCD5289A7D75B5D7A22CE9690EAD (Encoding_t65CDEF28CF20A7B8C92E85A4E808920C2465F095* __this, uint8_t* ___0_bytes, int32_t ___1_byteCount, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint16_t ByteBuffer_GetUshort_m99CBB64DF8F7D844B0F6FE18671922DF28D5DBDD (ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* __this, int32_t ___0_offset, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint16_t BinaryPrimitives_ReadUInt16LittleEndian_m6233B916B888350309C273E87ED2C3F787260889_inline (ReadOnlySpan_1_tA850A6C0E88ABBA37646A078ACBC24D6D5FD9B4D ___0_source, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint32_t ByteBuffer_GetUint_m7686A15FBE8934D1AE054CDE1D18F43BA59EBE6D (ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* __this, int32_t ___0_offset, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint32_t BinaryPrimitives_ReadUInt32LittleEndian_m1D2A6AA323C53D511E84C677D1F8F17077F3B070_inline (ReadOnlySpan_1_tA850A6C0E88ABBA37646A078ACBC24D6D5FD9B4D ___0_source, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint64_t ByteBuffer_GetUlong_m5960AA0201977D5CBB9E711EB7A912E0A986F549 (ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* __this, int32_t ___0_offset, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint64_t BinaryPrimitives_ReadUInt64LittleEndian_m9F91B7C963E163D3064EA52D2C3A4075A33FB32B_inline (ReadOnlySpan_1_tA850A6C0E88ABBA37646A078ACBC24D6D5FD9B4D ___0_source, const RuntimeMethod* method) ;
inline void Dictionary_2__ctor_m0AF6A9E01056850C9F7AE464B4099CA3F6D5E8EC (Dictionary_2_t384D2A0FF42BF1302A3F513FC32DB105F1CD5455* __this, const RuntimeMethod* method)
{
	((  void (*) (Dictionary_2_t384D2A0FF42BF1302A3F513FC32DB105F1CD5455*, const RuntimeMethod*))Dictionary_2__ctor_m517E7F9D104FEAE6646EABDDC9C852510E86077C_gshared)(__this, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Type_t* Type_GetTypeFromHandle_m6062B81682F79A4D6DF2640692EE6D9987858C57 (RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B ___0_handle, const RuntimeMethod* method) ;
inline void Dictionary_2_Add_mD091A436B1895D7A2D6C1848C6C84487C5873904 (Dictionary_2_t384D2A0FF42BF1302A3F513FC32DB105F1CD5455* __this, Type_t* ___0_key, int32_t ___1_value, const RuntimeMethod* method)
{
	((  void (*) (Dictionary_2_t384D2A0FF42BF1302A3F513FC32DB105F1CD5455*, Type_t*, int32_t, const RuntimeMethod*))Dictionary_2_Add_m63897227AFA7035F1772315ABBBE7FD0A250E10C_gshared)(__this, ___0_key, ___1_value, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ByteBuffer_Reset_mC9313064E9ABBF21238DD1581864FF7621F343FE (ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* __this, const RuntimeMethod* method) ;
inline void Dictionary_2_Clear_mA0B5ABBC9D22FF2ADCBD908A1BCD8AAA183BEDF8 (Dictionary_2_tEE02057113A88692CEC84BBCEB9E74DC6CC243E0* __this, const RuntimeMethod* method)
{
	((  void (*) (Dictionary_2_tEE02057113A88692CEC84BBCEB9E74DC6CC243E0*, const RuntimeMethod*))Dictionary_2_Clear_m3D95640616D444D903C22215109B53D82971A8EE_gshared)(__this, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ByteBuffer_PutByte_mA27E6385D3CF1624F3A44D01275E631FC4F87B2E (ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* __this, int32_t ___0_offset, uint8_t ___1_value, int32_t ___2_count, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ByteBuffer_GrowFront_m0DE57B8E020B3948327E205840F49809DA043F30 (ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* __this, int32_t ___0_newSize, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FlatBufferBuilder_GrowBuffer_m880D7E362F3C37DF5D2832D6554A8AEEE73E5367 (FlatBufferBuilder_t23EDF7F7B5CFBF2A0BBCC2CCE6D14C02BE050CB0* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FlatBufferBuilder_Pad_m45B94144FC69144F0D1E1D10A7012FEA5393F964 (FlatBufferBuilder_t23EDF7F7B5CFBF2A0BBCC2CCE6D14C02BE050CB0* __this, int32_t ___0_size, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ByteBuffer_PutByte_m81927B710F29CBE2EFB0E5F298398AB7D0D26967 (ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* __this, int32_t ___0_offset, uint8_t ___1_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ByteBuffer_PutSbyte_mC6055AB467538627CB2EEA9050DA94A819EA5148 (ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* __this, int32_t ___0_offset, int8_t ___1_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ByteBuffer_PutShort_m40945E04907D64971273BA0CA8C483FEAE91F451 (ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* __this, int32_t ___0_offset, int16_t ___1_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ByteBuffer_PutInt_mF305D6CF0623CB47E43C29A1CC08BFF7D3214C69 (ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* __this, int32_t ___0_offset, int32_t ___1_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ByteBuffer_PutLong_mD69C4E9BF0355C81EB6B8829BF85161B95B02A2B (ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* __this, int32_t ___0_offset, int64_t ___1_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ByteBuffer_PutFloat_m3BD6261E6C39B5B3F51DDF8412D26901024AC600 (ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* __this, int32_t ___0_offset, float ___1_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ByteBuffer_PutDouble_mFDF587433B6198987FAF1AD26DF320F9BCA41A49 (ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* __this, int32_t ___0_offset, double ___1_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FlatBufferBuilder_Prep_m3DA1C5F1967E6C9DE1F991519E1F87EE95C35AB7 (FlatBufferBuilder_t23EDF7F7B5CFBF2A0BBCC2CCE6D14C02BE050CB0* __this, int32_t ___0_size, int32_t ___1_additionalBytes, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FlatBufferBuilder_PutBool_m9DFECA7733C9F313CBB77F2EA3D2C16A6CC4D925 (FlatBufferBuilder_t23EDF7F7B5CFBF2A0BBCC2CCE6D14C02BE050CB0* __this, bool ___0_x, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FlatBufferBuilder_PutSbyte_m4B1681B63002727EF1ACEB361FFD075CBC35EA8C (FlatBufferBuilder_t23EDF7F7B5CFBF2A0BBCC2CCE6D14C02BE050CB0* __this, int8_t ___0_x, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FlatBufferBuilder_PutByte_mEA2C1C8F64D64C81C35C1D51D33423CF608A60F3 (FlatBufferBuilder_t23EDF7F7B5CFBF2A0BBCC2CCE6D14C02BE050CB0* __this, uint8_t ___0_x, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FlatBufferBuilder_PutShort_mE92EC85294499D0DC414F376099F73C267C82D0E (FlatBufferBuilder_t23EDF7F7B5CFBF2A0BBCC2CCE6D14C02BE050CB0* __this, int16_t ___0_x, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FlatBufferBuilder_PutUshort_mA897A7DC14B66A18334B2948B58F918B1FFFF915 (FlatBufferBuilder_t23EDF7F7B5CFBF2A0BBCC2CCE6D14C02BE050CB0* __this, uint16_t ___0_x, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FlatBufferBuilder_PutInt_m3657C36B7F37D77B13786E341A742332A942C07B (FlatBufferBuilder_t23EDF7F7B5CFBF2A0BBCC2CCE6D14C02BE050CB0* __this, int32_t ___0_x, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FlatBufferBuilder_PutUint_mE4C61BE169A0CEF7FDD69C461DA07A200B5A2F6A (FlatBufferBuilder_t23EDF7F7B5CFBF2A0BBCC2CCE6D14C02BE050CB0* __this, uint32_t ___0_x, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FlatBufferBuilder_PutLong_m69929C60C71F08604C3E428A3C3C4F047CEF6F1A (FlatBufferBuilder_t23EDF7F7B5CFBF2A0BBCC2CCE6D14C02BE050CB0* __this, int64_t ___0_x, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FlatBufferBuilder_PutUlong_m38DBCF4668E499C51EB30003652730076F100680 (FlatBufferBuilder_t23EDF7F7B5CFBF2A0BBCC2CCE6D14C02BE050CB0* __this, uint64_t ___0_x, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FlatBufferBuilder_PutFloat_m4BF4065EA873C657DFC67B301F0AE81AF8A6AB5D (FlatBufferBuilder_t23EDF7F7B5CFBF2A0BBCC2CCE6D14C02BE050CB0* __this, float ___0_x, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FlatBufferBuilder_PutDouble_mD51D07A38BABE4EA8EB9689D8D5536B97851981D (FlatBufferBuilder_t23EDF7F7B5CFBF2A0BBCC2CCE6D14C02BE050CB0* __this, double ___0_x, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t FlatBufferBuilder_get_Offset_mC74D55C982A3866A2171140BFFEF663306288AE9 (FlatBufferBuilder_t23EDF7F7B5CFBF2A0BBCC2CCE6D14C02BE050CB0* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ArgumentException__ctor_m34A925BA55EC4CE4253404E363B5F6A53EB51CA3 (ArgumentException_tAD90411542A20A9C72D5CDA3A84181D8B947A263* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FlatBufferBuilder_NotNested_m550AD28D180BF3E1C547BB535382BCE59B9BADB2 (FlatBufferBuilder_t23EDF7F7B5CFBF2A0BBCC2CCE6D14C02BE050CB0* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void VectorOffset__ctor_mACFDC02BEA6B5322F5E78EA74CE311D3D313B736_inline (VectorOffset_t25D49B46B3BAE5ABE18C23B5FF581A20699F96F4* __this, int32_t ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ArgumentOutOfRangeException__ctor_mBC1D5DEEA1BA41DE77228CB27D6BAFEB6DCCBF4A (ArgumentOutOfRangeException_tEA2822DAF62B10EEED00E0E3A341D4BAF78CF85F* __this, String_t* ___0_paramName, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void IndexOutOfRangeException__ctor_mFD06819F05B815BE2D6E826D4E04F4C449D0A425 (IndexOutOfRangeException_t7ECB35264FB6CA8FAA516BD958F4B2ADC78E8A82* __this, String_t* ___0_message, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool FlatBufferBuilder_get_ForceDefaults_mA0FE8A741C81C5945948228ED35D4F7E3BECC960_inline (FlatBufferBuilder_t23EDF7F7B5CFBF2A0BBCC2CCE6D14C02BE050CB0* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FlatBufferBuilder_AddBool_m478C50A00AA0FFFC9A0551F1B14688D00E654D67 (FlatBufferBuilder_t23EDF7F7B5CFBF2A0BBCC2CCE6D14C02BE050CB0* __this, bool ___0_x, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FlatBufferBuilder_Slot_mCF8F59D561C30C152A31A9754AA66D7A4E205B46 (FlatBufferBuilder_t23EDF7F7B5CFBF2A0BBCC2CCE6D14C02BE050CB0* __this, int32_t ___0_voffset, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FlatBufferBuilder_AddSbyte_m8205DD96B17E57ECA93B00E0CF9941423E635C29 (FlatBufferBuilder_t23EDF7F7B5CFBF2A0BBCC2CCE6D14C02BE050CB0* __this, int8_t ___0_x, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FlatBufferBuilder_AddByte_m09B496156A95861FDF7841640C5C1719723A7036 (FlatBufferBuilder_t23EDF7F7B5CFBF2A0BBCC2CCE6D14C02BE050CB0* __this, uint8_t ___0_x, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FlatBufferBuilder_AddShort_mA240DA72E828B425EEF2128E7933F8E9696F5075 (FlatBufferBuilder_t23EDF7F7B5CFBF2A0BBCC2CCE6D14C02BE050CB0* __this, int16_t ___0_x, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FlatBufferBuilder_AddUshort_mFBED2384AD4CFA68C46F725B9342FA4F8AB02573 (FlatBufferBuilder_t23EDF7F7B5CFBF2A0BBCC2CCE6D14C02BE050CB0* __this, uint16_t ___0_x, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FlatBufferBuilder_AddInt_mC7ED89CD9BAD7A1F34E445DDFF066513AA2B926E (FlatBufferBuilder_t23EDF7F7B5CFBF2A0BBCC2CCE6D14C02BE050CB0* __this, int32_t ___0_x, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FlatBufferBuilder_AddUint_m5E52C37A1B3FF5FEF363B603C8E379847EBF0FF9 (FlatBufferBuilder_t23EDF7F7B5CFBF2A0BBCC2CCE6D14C02BE050CB0* __this, uint32_t ___0_x, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FlatBufferBuilder_AddLong_m7941C58219E84656250A86B5B70D5E3731753DB5 (FlatBufferBuilder_t23EDF7F7B5CFBF2A0BBCC2CCE6D14C02BE050CB0* __this, int64_t ___0_x, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FlatBufferBuilder_AddUlong_mD667451BC4F21D55EB19276E56C538BABCB0C9A3 (FlatBufferBuilder_t23EDF7F7B5CFBF2A0BBCC2CCE6D14C02BE050CB0* __this, uint64_t ___0_x, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FlatBufferBuilder_AddFloat_m631138283BEE1A25D70D62629445315F699D2C4D (FlatBufferBuilder_t23EDF7F7B5CFBF2A0BBCC2CCE6D14C02BE050CB0* __this, float ___0_x, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FlatBufferBuilder_AddDouble_mF109F698FFEF929DE54342B6A78D58F791DA7E5D (FlatBufferBuilder_t23EDF7F7B5CFBF2A0BBCC2CCE6D14C02BE050CB0* __this, double ___0_x, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FlatBufferBuilder_AddOffset_m7A64D677E7BE356B284C1DD84A7FD6E8C7DDCA27 (FlatBufferBuilder_t23EDF7F7B5CFBF2A0BBCC2CCE6D14C02BE050CB0* __this, int32_t ___0_off, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void StringOffset__ctor_m52D945D231E3E3700A8C667AB97AE4D0A10A27BB_inline (StringOffset_t5F8A2A2DB7065B675FCE7E3337C174214CC4FB4E* __this, int32_t ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FlatBufferBuilder_StartVector_m6150568905670346B15955DDA02E5A09EB816E8D (FlatBufferBuilder_t23EDF7F7B5CFBF2A0BBCC2CCE6D14C02BE050CB0* __this, int32_t ___0_elemSize, int32_t ___1_count, int32_t ___2_alignment, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ByteBuffer_PutStringUTF8_m8A9FB9D67D35A58029657802ACA2C25A64BADAA3 (ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* __this, int32_t ___0_offset, String_t* ___1_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR VectorOffset_t25D49B46B3BAE5ABE18C23B5FF581A20699F96F4 FlatBufferBuilder_EndVector_mB83D1FC66B112D7A125563866327564C1DAF0DC7 (FlatBufferBuilder_t23EDF7F7B5CFBF2A0BBCC2CCE6D14C02BE050CB0* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FlatBufferBuilder_Nested_m45AE882A96E0B4309916DD3095168360CBFAE397 (FlatBufferBuilder_t23EDF7F7B5CFBF2A0BBCC2CCE6D14C02BE050CB0* __this, int32_t ___0_obj, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void InvalidOperationException__ctor_mE4CB6F4712AB6D99A2358FBAE2E052B3EE976162 (InvalidOperationException_t5DDE4D49B7405FAAB1E4576F4715A42A3FAD4BAB* __this, String_t* ___0_message, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int16_t ByteBuffer_GetShort_m3ED68273C4B0ABA97718B68F439D042C758D084B (ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* __this, int32_t ___0_offset, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Array_Copy_m4233828B4E6288B6D815F539AAA38575DE627900 (RuntimeArray* ___0_sourceArray, RuntimeArray* ___1_destinationArray, int32_t ___2_length, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void ByteBuffer_set_Position_m0F1BC982C7D846D2F73A08D04F66D255746FCB20_inline (ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* __this, int32_t ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FlatBufferBuilder_Finish_m356784F295B0938131FC67FE20FA7D3A2E91D42E (FlatBufferBuilder_t23EDF7F7B5CFBF2A0BBCC2CCE6D14C02BE050CB0* __this, int32_t ___0_rootTable, bool ___1_sizePrefix, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Options__ctor_m443C4957B9712416B855C1E610E23CCEC9756DB7 (Options_t2C469D7B05D4F50066C5316A778A04C4DF53F088* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* Convert_ToString_m18866C40B9AB691A8DEF943323636CA70374D419 (int32_t ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* String_Concat_m9E3155FB84015C823606188F53B47CB44C444991 (String_t* ___0_str0, String_t* ___1_str1, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ArgumentException__ctor_m026938A67AF9D36BB7ED27F80425D7194B514465 (ArgumentException_tAD90411542A20A9C72D5CDA3A84181D8B947A263* __this, String_t* ___0_message, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Il2CppChar String_get_Chars_mC49DF0CD2D3BE7BE97B3AD9C995BE3094F8E36D3 (String_t* __this, int32_t ___0_index, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Convert_ToInt32_mA857F99F1CACB73D7DB85E26638E7CC1A2CD5C78 (int64_t ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int8_t ByteBuffer_GetSbyte_mC5F6FC81C592D6FDFDF41EE3EA719EEA2234295B (ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* __this, int32_t ___0_index, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Convert_ToInt32_m5ADD7A6890AE40D05444F58D72FDEC7252D6D7F2 (uint32_t ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t ByteBuffer_GetInt_mD2F94B1EFC4E1B48E6A25E9FE27A36CB691F93C7 (ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* __this, int32_t ___0_offset, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Verifier_ReadSOffsetT_m5A43060F831A70DD3ECDFA632C079B51B4C15EBE (Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864* __this, ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* ___0_buf, int32_t ___1_pos, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int16_t Convert_ToInt16_mF65D8227B8B6F0E30A135BC5F01F1562455AD382 (int32_t ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int16_t Verifier_ReadVOffsetT_m3F7EA721035EA1275A5536FA2965DC30FB246B0F (Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864* __this, ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* ___0_buf, int32_t ___1_pos, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Console_WriteLine_mCF1AA33709B943A23A5CA168DF7972F4DAB53011 (String_t* ___0_format, RuntimeObject* ___1_arg0, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int16_t Verifier_GetVRelOffset_mF26D54B2378F87DEA0C42285F40ECB98B05D2F14 (Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864* __this, int32_t ___0_pos, int16_t ___1_vtableOffset, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint32_t Convert_ToUInt32_m8754C042D71DB6C81EB54D85B73B7EC2710E4FA0 (int64_t ___0_value, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t Verifier_get_depth_m4A917A65B04FAC0E7374D664A28B7BFC089AA74E_inline (Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Options_t2C469D7B05D4F50066C5316A778A04C4DF53F088* Verifier_get_options_m5D0BEA9EBE4645B980E3066F2E42B042943D87E9_inline (Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t Options_get_maxDepth_m437B08A483A59E4600A3A2B6FE3A26E3EA8B2212_inline (Options_t2C469D7B05D4F50066C5316A778A04C4DF53F088* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t Verifier_get_numTables_mD6123A14045007BAE82B1770EB9201594F778973_inline (Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t Options_get_maxTables_mE2E55C408DAE7EB5059F6169F471ED14B93F2D40_inline (Options_t2C469D7B05D4F50066C5316A778A04C4DF53F088* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool Options_get_alignmentCheck_mC94E7A871FC44FE7505713CD0E69A9FE2BFD4EEA_inline (Options_t2C469D7B05D4F50066C5316A778A04C4DF53F088* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint64_t Convert_ToUInt64_m53C3A45C87A06F25957619222B04EABFD214373C (int32_t ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint32_t Convert_ToUInt32_m3BD840FA8B5073EDD04AD3D3A044785EB00511A3 (int32_t ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Verifier_CheckAlignment_m967E1965E15845B66E5454A303C51D46DE3FEDF5 (Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864* __this, uint32_t ___0_element, uint64_t ___1_align, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Verifier_CheckElement_m2CF35C61F826AE256B69A5EEA5719190AF474680 (Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864* __this, uint32_t ___0_pos, uint64_t ___1_elementSize, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Verifier_CheckScalar_m9799566886A1AC4D9A2A82F5093B0BA1FC8BF735 (Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864* __this, uint32_t ___0_pos, uint64_t ___1_elementSize, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint32_t Verifier_ReadUOffsetT_mFA082679C617C5C8424CDDFE3965438AB7DE85B1 (Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864* __this, ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* ___0_buf, uint32_t ___1_pos, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint32_t Convert_ToUInt32_m32FF6CCC6C935251DD40D88D6ED2C4B39C9CFA0D (uint64_t ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR checkElementStruct_tCF75802323BACA21C0D2C66C2531CC19F664B49B Verifier_CheckVectorOrString_m84A7DB3D433AC14911F20DB712CF3BB178A37FB8 (Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864* __this, uint32_t ___0_pos, uint64_t ___1_elementSize, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool Options_get_stringEndCheck_mB9217751DB5C7D10C8EDB209FC945402821A1339_inline (Options_t2C469D7B05D4F50066C5316A778A04C4DF53F088* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool VerifyTableAction_Invoke_mDD4DF94C57F6A57A87F19A8915ACD085F65FF6D4_inline (VerifyTableAction_t4EE7F0547499A39B8176F648F806406AB26487AB* __this, Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864* ___0_verifier, uint32_t ___1_tablePos, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Verifier_CheckString_m77D796D84E00F7EE4ED3B35A183B80C54C610241 (Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864* __this, uint32_t ___0_pos, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Verifier_CheckVector_m53F4531381F8067BDFEF05CE7EE4DCC55E7D4DC9 (Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864* __this, uint32_t ___0_pos, uint64_t ___1_elementSize, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Verifier_CheckIndirectOffset_m9EC00EFA29171D9BCB9D6CF2514D6C0C38E1385A (Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864* __this, uint32_t ___0_pos, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint32_t Verifier_GetIndirectOffset_mD4ABA15561C86526A192B328F49DBFBFF7BA7EBB (Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864* __this, uint32_t ___0_pos, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Verifier_BufferHasIdentifier_m47A5B9E1944079E3B21B67886594DD78A79F9F76 (Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864* __this, ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* ___0_buf, uint32_t ___1_startPos, String_t* ___2_identifier, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Verifier_CheckTable_mBD169E08464C583974B9759AE59A7FF29E2F8691 (Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864* __this, uint32_t ___0_tablePos, VerifyTableAction_t4EE7F0547499A39B8176F648F806406AB26487AB* ___1_verifyAction, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Verifier_CheckComplexity_m154BA6645F54D28747BCF950D4371C748BC4991D (Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint32_t Convert_ToUInt32_m04B73ED6E3C9D5226E7755B57B44417B58F22053 (int16_t ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint64_t Convert_ToUInt64_mBC2F335F529042F26FEE842CC38966FBDD45C007 (int16_t ___0_value, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Verifier_set_depth_m996F6606AC8B244E1D3AB94C5C32C1934B47A7C7_inline (Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864* __this, int32_t ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint32_t Verifier_GetVOffset_m75F1130DAE55F0EE4BDFE43737E53113BF25FD8F (Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864* __this, uint32_t ___0_tablePos, int16_t ___1_vtableOffset, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VerifyTableAction__ctor_m7AEEAD7ED5C1ABF23BE481BA49D8BA704E421456 (VerifyTableAction_t4EE7F0547499A39B8176F648F806406AB26487AB* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Verifier_CheckVectorOfObjects_mF84993CB74FF40A3AFA1AF020D677A7CBF4A9E6D (Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864* __this, uint32_t ___0_pos, VerifyTableAction_t4EE7F0547499A39B8176F648F806406AB26487AB* ___1_verifyAction, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint8_t ByteBuffer_Get_m4DACAF12CF9F83591DF6B595512714C893377FDB (ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* __this, int32_t ___0_index, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool VerifyUnionAction_Invoke_mF8A06007D2423423954038A65C411972E1DF6B3B_inline (VerifyUnionAction_t41231FB98151184E37EF5626A85224925AC03F23* __this, Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864* ___0_verifier, uint8_t ___1_typeId, uint32_t ___2_tablePos, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Verifier_set_numTables_mFD577C7B868B6ACE51DCAFDBE879183BBE9B08B4_inline (Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864* __this, int32_t ___0_value, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t ByteBuffer_get_Position_mAEC84EDE8EECB180F1904E4E10DFE6BF923A3F8D_inline (ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Verifier_CheckBufferFromStart_m30D3F098F0258380253601997A05C0C0E55569F4 (Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864* __this, String_t* ___0_identifier, uint32_t ___1_startPos, VerifyTableAction_t4EE7F0547499A39B8176F648F806406AB26487AB* ___2_verifyAction, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t Struct_get_bb_pos_m0DF50405A8A0FA6FCFB3FEAFCB5F7E887E3C53DB_inline (Struct_t80ED167B1083AFA71911B4732EFBAC86E1D17CA0* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Struct_set_bb_pos_mD303E870401EB784D89A1785AEA7441708DABDEB_inline (Struct_t80ED167B1083AFA71911B4732EFBAC86E1D17CA0* __this, int32_t ___0_value, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* Struct_get_bb_m023CCFFF470E9BBBFC4B198B873601E6E8ED3B7E_inline (Struct_t80ED167B1083AFA71911B4732EFBAC86E1D17CA0* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Struct_set_bb_mE86F490DC850F169136E30896C865534FB72248D_inline (Struct_t80ED167B1083AFA71911B4732EFBAC86E1D17CA0* __this, ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Struct__ctor_m2B636C66F813B59582DB736FD6BCC25E84B021E3 (Struct_t80ED167B1083AFA71911B4732EFBAC86E1D17CA0* __this, int32_t ___0__i, ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* ___1__bb, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t Table_get_bb_pos_m384022E2F07A268997C4AD3BF8344A8CE72E71C6_inline (Table_t2274EC2D38DA186BEAB1F75E04F8401533DB9815* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Table_set_bb_pos_m7ECC7E46BB126F475011359342D37F4703F9BA14_inline (Table_t2274EC2D38DA186BEAB1F75E04F8401533DB9815* __this, int32_t ___0_value, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* Table_get_bb_mA038E6852549861A3E7346E88A0AE51EF06EF928_inline (Table_t2274EC2D38DA186BEAB1F75E04F8401533DB9815* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Table_set_bb_m01B9D524A5880128E16440DCD5D7992325FBD71C_inline (Table_t2274EC2D38DA186BEAB1F75E04F8401533DB9815* __this, ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Table__ctor_m0FAB6920DFC3116EC9552DD7237AB6E84BA5CA31 (Table_t2274EC2D38DA186BEAB1F75E04F8401533DB9815* __this, int32_t ___0__i, ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* ___1__bb, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Table___offset_mD41A3FDF5139E05F3EA1BB70EED7C48D593979D6 (Table_t2274EC2D38DA186BEAB1F75E04F8401533DB9815* __this, int32_t ___0_vtableOffset, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Table___indirect_mB18E00AFF82B4E55117FE556350A8D99ABD565C1 (Table_t2274EC2D38DA186BEAB1F75E04F8401533DB9815* __this, int32_t ___0_offset, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* ByteBuffer_GetStringUTF8_m54FDA99A7BCE5B71C55BCE816CE73854FFFB0EAF (ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* __this, int32_t ___0_startPos, int32_t ___1_len, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* Table___string_mFB43D8816C093699854F13DC421C3DD6E78BEAF3 (Table_t2274EC2D38DA186BEAB1F75E04F8401533DB9815* __this, int32_t ___0_offset, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Table___vector_len_m3B0E26D29F11FD1CF988A59732A1B33D1DAE393D (Table_t2274EC2D38DA186BEAB1F75E04F8401533DB9815* __this, int32_t ___0_offset, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Table___vector_m7F98F7FF9A2160084141781A48E338392BF4E780 (Table_t2274EC2D38DA186BEAB1F75E04F8401533DB9815* __this, int32_t ___0_offset, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint16_t BinaryPrimitives_ReverseEndianness_mDBF226C2D52CAFF6DE538F8245444B5CF87A02D0_inline (uint16_t ___0_value, const RuntimeMethod* method) ;
inline void MemoryMarshal_Write_TisUInt16_tF4C148C876015C212FD72652D0B6ED8CC247A455_m1CB9CA69ED7F15E1AED66F7E98D918E1DF0CB96A_inline (Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305 ___0_destination, uint16_t* ___1_value, const RuntimeMethod* method)
{
	((  void (*) (Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305, uint16_t*, const RuntimeMethod*))MemoryMarshal_Write_TisUInt16_tF4C148C876015C212FD72652D0B6ED8CC247A455_m1CB9CA69ED7F15E1AED66F7E98D918E1DF0CB96A_gshared_inline)(___0_destination, ___1_value, method);
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint32_t BinaryPrimitives_ReverseEndianness_mCCA2099164ECA9672968898DD996A9F04B392FFF_inline (uint32_t ___0_value, const RuntimeMethod* method) ;
inline void MemoryMarshal_Write_TisUInt32_t1833D51FFA667B18A5AA4B8D34DE284F8495D29B_m2BD505F8011143B77CB9A048F4C97C64130CB6DB_inline (Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305 ___0_destination, uint32_t* ___1_value, const RuntimeMethod* method)
{
	((  void (*) (Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305, uint32_t*, const RuntimeMethod*))MemoryMarshal_Write_TisUInt32_t1833D51FFA667B18A5AA4B8D34DE284F8495D29B_m2BD505F8011143B77CB9A048F4C97C64130CB6DB_gshared_inline)(___0_destination, ___1_value, method);
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint64_t BinaryPrimitives_ReverseEndianness_mA698702D91EF4E47FF6F682E4B48F173FF376BDF_inline (uint64_t ___0_value, const RuntimeMethod* method) ;
inline void MemoryMarshal_Write_TisUInt64_t8F12534CC8FC4B5860F2A2CD1EE79D322E7A41AF_m369E3011D17AFC47FE8EBD775DA05E3F136D3EA3_inline (Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305 ___0_destination, uint64_t* ___1_value, const RuntimeMethod* method)
{
	((  void (*) (Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305, uint64_t*, const RuntimeMethod*))MemoryMarshal_Write_TisUInt64_t8F12534CC8FC4B5860F2A2CD1EE79D322E7A41AF_m369E3011D17AFC47FE8EBD775DA05E3F136D3EA3_gshared_inline)(___0_destination, ___1_value, method);
}
inline uint16_t MemoryMarshal_Read_TisUInt16_tF4C148C876015C212FD72652D0B6ED8CC247A455_mE522E06A28DD43DAED8B42666149274B433F5317_inline (ReadOnlySpan_1_tA850A6C0E88ABBA37646A078ACBC24D6D5FD9B4D ___0_source, const RuntimeMethod* method)
{
	return ((  uint16_t (*) (ReadOnlySpan_1_tA850A6C0E88ABBA37646A078ACBC24D6D5FD9B4D, const RuntimeMethod*))MemoryMarshal_Read_TisUInt16_tF4C148C876015C212FD72652D0B6ED8CC247A455_mE522E06A28DD43DAED8B42666149274B433F5317_gshared_inline)(___0_source, method);
}
inline uint32_t MemoryMarshal_Read_TisUInt32_t1833D51FFA667B18A5AA4B8D34DE284F8495D29B_m56C749731FAD055AC5894D97F107FF8E5C6A13AE_inline (ReadOnlySpan_1_tA850A6C0E88ABBA37646A078ACBC24D6D5FD9B4D ___0_source, const RuntimeMethod* method)
{
	return ((  uint32_t (*) (ReadOnlySpan_1_tA850A6C0E88ABBA37646A078ACBC24D6D5FD9B4D, const RuntimeMethod*))MemoryMarshal_Read_TisUInt32_t1833D51FFA667B18A5AA4B8D34DE284F8495D29B_m56C749731FAD055AC5894D97F107FF8E5C6A13AE_gshared_inline)(___0_source, method);
}
inline uint64_t MemoryMarshal_Read_TisUInt64_t8F12534CC8FC4B5860F2A2CD1EE79D322E7A41AF_m4B7CEC36F79FB7BE35EB5ADC5B3F9B03A427FAD0_inline (ReadOnlySpan_1_tA850A6C0E88ABBA37646A078ACBC24D6D5FD9B4D ___0_source, const RuntimeMethod* method)
{
	return ((  uint64_t (*) (ReadOnlySpan_1_tA850A6C0E88ABBA37646A078ACBC24D6D5FD9B4D, const RuntimeMethod*))MemoryMarshal_Read_TisUInt64_t8F12534CC8FC4B5860F2A2CD1EE79D322E7A41AF_m4B7CEC36F79FB7BE35EB5ADC5B3F9B03A427FAD0_gshared_inline)(___0_source, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Type_op_Equality_m99930A0E44E420A685FABA60E60BA1CC5FA0EBDC (Type_t* ___0_left, Type_t* ___1_right, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Il2CppChar* String_GetRawStringData_m87BC50B7B314C055E27A28032D1003D42FDE411D (String_t* __this, const RuntimeMethod* method) ;
inline void Span_1__ctor_m947BF95D54571BF3897F96822B7A8FDA5853497B_inline (Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305* __this, uint8_t* ___0_ptr, int32_t ___1_length, const RuntimeMethod* method)
{
	((  void (*) (Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305*, uint8_t*, int32_t, const RuntimeMethod*))Span_1__ctor_m947BF95D54571BF3897F96822B7A8FDA5853497B_gshared_inline)(__this, ___0_ptr, ___1_length, method);
}
inline void Span_1__ctor_m698EC79E2E44AFF16BA096D0861CFB129FBF8218_inline (Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305* __this, ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* ___0_array, int32_t ___1_start, int32_t ___2_length, const RuntimeMethod* method)
{
	((  void (*) (Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305*, ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*, int32_t, int32_t, const RuntimeMethod*))Span_1__ctor_m698EC79E2E44AFF16BA096D0861CFB129FBF8218_gshared_inline)(__this, ___0_array, ___1_start, ___2_length, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ThrowHelper_ThrowArgumentOutOfRangeException_mD7D90276EDCDF9394A8EA635923E3B48BB71BD56 (const RuntimeMethod* method) ;
inline void ReadOnlySpan_1__ctor_m0FC0B92549C2968E80B5F75A85F28B96DBFCFD63_inline (ReadOnlySpan_1_tA850A6C0E88ABBA37646A078ACBC24D6D5FD9B4D* __this, uint8_t* ___0_ptr, int32_t ___1_length, const RuntimeMethod* method)
{
	((  void (*) (ReadOnlySpan_1_tA850A6C0E88ABBA37646A078ACBC24D6D5FD9B4D*, uint8_t*, int32_t, const RuntimeMethod*))ReadOnlySpan_1__ctor_m0FC0B92549C2968E80B5F75A85F28B96DBFCFD63_gshared_inline)(__this, ___0_ptr, ___1_length, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ThrowHelper_ThrowArgumentOutOfRangeException_m9B335696876184D17D1F8D7AF94C1B5B0869AA97 (int32_t ___0_argument, const RuntimeMethod* method) ;
inline int32_t ReadOnlySpan_1_get_Length_m54864A0BB817050A9110E85BB5FB31EF63699982_inline (ReadOnlySpan_1_tA850A6C0E88ABBA37646A078ACBC24D6D5FD9B4D* __this, const RuntimeMethod* method)
{
	return ((  int32_t (*) (ReadOnlySpan_1_tA850A6C0E88ABBA37646A078ACBC24D6D5FD9B4D*, const RuntimeMethod*))ReadOnlySpan_1_get_Length_m54864A0BB817050A9110E85BB5FB31EF63699982_gshared_inline)(__this, method);
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint8_t* Array_GetRawSzArrayData_m2F8F5B2A381AEF971F12866D9C0A6C4FBA59F6BB_inline (RuntimeArray* __this, const RuntimeMethod* method) ;
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t ByteBufferAllocator_get_Length_mC038E63A664724F0965D2B7B57899F1BDB1DCE2A (ByteBufferAllocator_tCC19A5DCD32535F213BA3EDECBB08A7F9EFD6185* __this, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = __this->___U3CLengthU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ByteBufferAllocator_set_Length_m17C45CA27C651A79001AED91E6AAA13B575F38C9 (ByteBufferAllocator_tCC19A5DCD32535F213BA3EDECBB08A7F9EFD6185* __this, int32_t ___0_value, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = ___0_value;
		__this->___U3CLengthU3Ek__BackingField = L_0;
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ByteBufferAllocator__ctor_mA20B60936A23DFD8E7D134C8C522AB3916B91772 (ByteBufferAllocator_tCC19A5DCD32535F213BA3EDECBB08A7F9EFD6185* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ByteArrayAllocator__ctor_m64D6FFED315D68A28E03F3C7B0F57248603C0AA8 (ByteArrayAllocator_tCC5100EDA86D010D5A482B26279BB51ADA09B633* __this, ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* ___0_buffer, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Memory_1_op_Implicit_m40D10666CBBA1748CA281BAB5EDA02E8525D1B3C_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		ByteBufferAllocator__ctor_mA20B60936A23DFD8E7D134C8C522AB3916B91772(__this, NULL);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_0 = ___0_buffer;
		Memory_1_tB7CEF4416F5014E364267478CEF016A4AC5C0036 L_1;
		L_1 = Memory_1_op_Implicit_m40D10666CBBA1748CA281BAB5EDA02E8525D1B3C(L_0, Memory_1_op_Implicit_m40D10666CBBA1748CA281BAB5EDA02E8525D1B3C_RuntimeMethod_var);
		__this->____buffer = L_1;
		Il2CppCodeGenWriteBarrier((void**)&(((&__this->____buffer))->____object), (void*)NULL);
		ByteArrayAllocator_InitBuffer_m3A5840E5A780906C338F6D7A4F8D5612401B698A(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ByteArrayAllocator_Reset_m9492B10C7418A95DEA296A15064D133674A0E823 (ByteArrayAllocator_tCC5100EDA86D010D5A482B26279BB51ADA09B633* __this, Memory_1_tB7CEF4416F5014E364267478CEF016A4AC5C0036 ___0_buffer, const RuntimeMethod* method) 
{
	{
		Memory_1_tB7CEF4416F5014E364267478CEF016A4AC5C0036 L_0 = ___0_buffer;
		__this->____buffer = L_0;
		Il2CppCodeGenWriteBarrier((void**)&(((&__this->____buffer))->____object), (void*)NULL);
		ByteArrayAllocator_InitBuffer_m3A5840E5A780906C338F6D7A4F8D5612401B698A(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ByteArrayAllocator_GrowFront_mFD75F8C69430AD2566AA13EC8885AFEAD545633C (ByteArrayAllocator_tCC5100EDA86D010D5A482B26279BB51ADA09B633* __this, int32_t ___0_newSize, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Memory_1_CopyTo_mF4EEE351944C3FF14C3EC6576AD5FD3CF72D850C_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Memory_1_op_Implicit_m40D10666CBBA1748CA281BAB5EDA02E8525D1B3C_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&RuntimeHelpers_GetSubArray_TisByte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_m6131C565343BE25EB8BD5A32EFB89FD9C1E68BD2_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* V_0 = NULL;
	{
		int32_t L_0;
		L_0 = ByteBufferAllocator_get_Length_mC038E63A664724F0965D2B7B57899F1BDB1DCE2A_inline(__this, NULL);
		if (!((int64_t)(((int64_t)L_0)&((int64_t)(uint64_t)((uint32_t)((int32_t)-1073741824))))))
		{
			goto IL_001b;
		}
	}
	{
		Exception_t* L_1 = (Exception_t*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&Exception_t_il2cpp_TypeInfo_var)));
		Exception__ctor_m9B2BD92CD68916245A75109105D9071C9D430E7F(L_1, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteral2205190534B4BE18F7F950DFE08B06C83FB45673)), NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_1, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ByteArrayAllocator_GrowFront_mFD75F8C69430AD2566AA13EC8885AFEAD545633C_RuntimeMethod_var)));
	}

IL_001b:
	{
		int32_t L_2 = ___0_newSize;
		int32_t L_3;
		L_3 = ByteBufferAllocator_get_Length_mC038E63A664724F0965D2B7B57899F1BDB1DCE2A_inline(__this, NULL);
		if ((((int32_t)L_2) >= ((int32_t)L_3)))
		{
			goto IL_002f;
		}
	}
	{
		Exception_t* L_4 = (Exception_t*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&Exception_t_il2cpp_TypeInfo_var)));
		Exception__ctor_m9B2BD92CD68916245A75109105D9071C9D430E7F(L_4, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteral44372B7E3F3252B67169F88E9A2F221315E6A635)), NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_4, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ByteArrayAllocator_GrowFront_mFD75F8C69430AD2566AA13EC8885AFEAD545633C_RuntimeMethod_var)));
	}

IL_002f:
	{
		int32_t L_5 = ___0_newSize;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_6 = (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)SZArrayNew(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031_il2cpp_TypeInfo_var, (uint32_t)L_5);
		V_0 = L_6;
		Memory_1_tB7CEF4416F5014E364267478CEF016A4AC5C0036* L_7 = (Memory_1_tB7CEF4416F5014E364267478CEF016A4AC5C0036*)(&__this->____buffer);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_8 = V_0;
		int32_t L_9 = ___0_newSize;
		int32_t L_10;
		L_10 = ByteBufferAllocator_get_Length_mC038E63A664724F0965D2B7B57899F1BDB1DCE2A_inline(__this, NULL);
		Index_t5614B28257879876E049288D09C47602B22F2301 L_11;
		L_11 = Index_op_Implicit_m56AB6B8ACC5B4BFCED7FE0D75FFD117F7F9AA360(((int32_t)il2cpp_codegen_subtract(L_9, L_10)), NULL);
		Range_t2E1D6269D6D386B091A4D3DD347B8D4C5A35F95F L_12;
		L_12 = Range_StartAt_mB7FF114A0C730EA7531982299ABB9C108C07F1D6(L_11, NULL);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_13;
		L_13 = RuntimeHelpers_GetSubArray_TisByte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_m6131C565343BE25EB8BD5A32EFB89FD9C1E68BD2(L_8, L_12, RuntimeHelpers_GetSubArray_TisByte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_m6131C565343BE25EB8BD5A32EFB89FD9C1E68BD2_RuntimeMethod_var);
		Memory_1_tB7CEF4416F5014E364267478CEF016A4AC5C0036 L_14;
		L_14 = Memory_1_op_Implicit_m40D10666CBBA1748CA281BAB5EDA02E8525D1B3C(L_13, Memory_1_op_Implicit_m40D10666CBBA1748CA281BAB5EDA02E8525D1B3C_RuntimeMethod_var);
		Memory_1_CopyTo_mF4EEE351944C3FF14C3EC6576AD5FD3CF72D850C(L_7, L_14, Memory_1_CopyTo_mF4EEE351944C3FF14C3EC6576AD5FD3CF72D850C_RuntimeMethod_var);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_15 = V_0;
		Memory_1_tB7CEF4416F5014E364267478CEF016A4AC5C0036 L_16;
		L_16 = Memory_1_op_Implicit_m40D10666CBBA1748CA281BAB5EDA02E8525D1B3C(L_15, Memory_1_op_Implicit_m40D10666CBBA1748CA281BAB5EDA02E8525D1B3C_RuntimeMethod_var);
		__this->____buffer = L_16;
		Il2CppCodeGenWriteBarrier((void**)&(((&__this->____buffer))->____object), (void*)NULL);
		ByteArrayAllocator_InitBuffer_m3A5840E5A780906C338F6D7A4F8D5612401B698A(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305 ByteArrayAllocator_get_Span_mA4B4F233538BFD59293E6742D1E3A09BD2F0F134 (ByteArrayAllocator_tCC5100EDA86D010D5A482B26279BB51ADA09B633* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Memory_1_get_Span_mA0CAB13956D6FA3BBF9F9176CB647933F88E034E_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Memory_1_tB7CEF4416F5014E364267478CEF016A4AC5C0036* L_0 = (Memory_1_tB7CEF4416F5014E364267478CEF016A4AC5C0036*)(&__this->____buffer);
		Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305 L_1;
		L_1 = Memory_1_get_Span_mA0CAB13956D6FA3BBF9F9176CB647933F88E034E_inline(L_0, Memory_1_get_Span_mA0CAB13956D6FA3BBF9F9176CB647933F88E034E_RuntimeMethod_var);
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR ReadOnlySpan_1_tA850A6C0E88ABBA37646A078ACBC24D6D5FD9B4D ByteArrayAllocator_get_ReadOnlySpan_m2FA5A60F56587D45370CB9F32182AD92E54F7AB0 (ByteArrayAllocator_tCC5100EDA86D010D5A482B26279BB51ADA09B633* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Memory_1_get_Span_mA0CAB13956D6FA3BBF9F9176CB647933F88E034E_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Span_1_op_Implicit_mD249188242C0C9D3192A31E9F7FA74C683F05B84_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Memory_1_tB7CEF4416F5014E364267478CEF016A4AC5C0036* L_0 = (Memory_1_tB7CEF4416F5014E364267478CEF016A4AC5C0036*)(&__this->____buffer);
		Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305 L_1;
		L_1 = Memory_1_get_Span_mA0CAB13956D6FA3BBF9F9176CB647933F88E034E_inline(L_0, Memory_1_get_Span_mA0CAB13956D6FA3BBF9F9176CB647933F88E034E_RuntimeMethod_var);
		ReadOnlySpan_1_tA850A6C0E88ABBA37646A078ACBC24D6D5FD9B4D L_2;
		L_2 = Span_1_op_Implicit_mD249188242C0C9D3192A31E9F7FA74C683F05B84(L_1, Span_1_op_Implicit_mD249188242C0C9D3192A31E9F7FA74C683F05B84_RuntimeMethod_var);
		return L_2;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ByteArrayAllocator_InitBuffer_m3A5840E5A780906C338F6D7A4F8D5612401B698A (ByteArrayAllocator_tCC5100EDA86D010D5A482B26279BB51ADA09B633* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Memory_1_get_Length_mA63190F8E6F8A531AA0A55447D48210D8083B714_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Memory_1_tB7CEF4416F5014E364267478CEF016A4AC5C0036* L_0 = (Memory_1_tB7CEF4416F5014E364267478CEF016A4AC5C0036*)(&__this->____buffer);
		int32_t L_1;
		L_1 = Memory_1_get_Length_mA63190F8E6F8A531AA0A55447D48210D8083B714(L_0, Memory_1_get_Length_mA63190F8E6F8A531AA0A55447D48210D8083B714_RuntimeMethod_var);
		ByteBufferAllocator_set_Length_m17C45CA27C651A79001AED91E6AAA13B575F38C9_inline(__this, L_1, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ByteBuffer__ctor_mC76B5A5C27523545664570AAD1C04B663C1C503A (ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* __this, int32_t ___0_size, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		int32_t L_0 = ___0_size;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_1 = (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)SZArrayNew(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031_il2cpp_TypeInfo_var, (uint32_t)L_0);
		ByteBuffer__ctor_m4B87CF31CBB963E34E96F16585FCA444DF949921(__this, L_1, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ByteBuffer__ctor_m4B87CF31CBB963E34E96F16585FCA444DF949921 (ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* __this, ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* ___0_buffer, const RuntimeMethod* method) 
{
	{
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_0 = ___0_buffer;
		ByteBuffer__ctor_mEE1915E69AC2062777C33B4393DDC957749C9FF5(__this, L_0, 0, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ByteBuffer__ctor_mEE1915E69AC2062777C33B4393DDC957749C9FF5 (ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* __this, ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* ___0_buffer, int32_t ___1_pos, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ByteArrayAllocator_tCC5100EDA86D010D5A482B26279BB51ADA09B633_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_0 = ___0_buffer;
		ByteArrayAllocator_tCC5100EDA86D010D5A482B26279BB51ADA09B633* L_1 = (ByteArrayAllocator_tCC5100EDA86D010D5A482B26279BB51ADA09B633*)il2cpp_codegen_object_new(ByteArrayAllocator_tCC5100EDA86D010D5A482B26279BB51ADA09B633_il2cpp_TypeInfo_var);
		ByteArrayAllocator__ctor_m64D6FFED315D68A28E03F3C7B0F57248603C0AA8(L_1, L_0, NULL);
		__this->____buffer = L_1;
		Il2CppCodeGenWriteBarrier((void**)(&__this->____buffer), (void*)L_1);
		int32_t L_2 = ___1_pos;
		__this->____pos = L_2;
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t ByteBuffer_get_Position_mAEC84EDE8EECB180F1904E4E10DFE6BF923A3F8D (ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* __this, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = __this->____pos;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ByteBuffer_set_Position_m0F1BC982C7D846D2F73A08D04F66D255746FCB20 (ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* __this, int32_t ___0_value, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = ___0_value;
		__this->____pos = L_0;
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t ByteBuffer_get_Length_mD83481CCE00F09128DBAC206A094C628E635BA8B (ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* __this, const RuntimeMethod* method) 
{
	{
		ByteBufferAllocator_tCC19A5DCD32535F213BA3EDECBB08A7F9EFD6185* L_0 = __this->____buffer;
		NullCheck(L_0);
		int32_t L_1;
		L_1 = ByteBufferAllocator_get_Length_mC038E63A664724F0965D2B7B57899F1BDB1DCE2A_inline(L_0, NULL);
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ByteBuffer_Reset_mC9313064E9ABBF21238DD1581864FF7621F343FE (ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* __this, const RuntimeMethod* method) 
{
	{
		__this->____pos = 0;
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ByteBuffer_Reset_m1750E2DCC8E1C8B18CCA921D876B4A70D7035F03 (ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* __this, Memory_1_tB7CEF4416F5014E364267478CEF016A4AC5C0036 ___0_buffer, const RuntimeMethod* method) 
{
	{
		ByteBufferAllocator_tCC19A5DCD32535F213BA3EDECBB08A7F9EFD6185* L_0 = __this->____buffer;
		Memory_1_tB7CEF4416F5014E364267478CEF016A4AC5C0036 L_1 = ___0_buffer;
		NullCheck(L_0);
		VirtualActionInvoker1< Memory_1_tB7CEF4416F5014E364267478CEF016A4AC5C0036 >::Invoke(7, L_0, L_1);
		__this->____pos = 0;
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ByteBuffer_GrowFront_m0DE57B8E020B3948327E205840F49809DA043F30 (ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* __this, int32_t ___0_newSize, const RuntimeMethod* method) 
{
	{
		ByteBufferAllocator_tCC19A5DCD32535F213BA3EDECBB08A7F9EFD6185* L_0 = __this->____buffer;
		int32_t L_1 = ___0_newSize;
		NullCheck(L_0);
		VirtualActionInvoker1< int32_t >::Invoke(6, L_0, L_1);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* ByteBuffer_ToArray_mF1209C898F0DFF95A6C84A6218886D10808CB379 (ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* __this, int32_t ___0_pos, int32_t ___1_len, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ByteBuffer_ToArray_TisByte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_m69965C32A36797FCB564A989DC566EE255B4DFB1_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		int32_t L_0 = ___0_pos;
		int32_t L_1 = ___1_len;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_2;
		L_2 = ByteBuffer_ToArray_TisByte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_m69965C32A36797FCB564A989DC566EE255B4DFB1(__this, L_0, L_1, ByteBuffer_ToArray_TisByte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_m69965C32A36797FCB564A989DC566EE255B4DFB1_RuntimeMethod_var);
		return L_2;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305 ByteBuffer_ToSpan_m00B8ED7F4A80E5C7A3BF6F740394DC2B5ED6274E (ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* __this, int32_t ___0_pos, int32_t ___1_len, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Span_1_Slice_m9D8BA8245B8DC9BFB4A4164759CBAAEAD1318CD6_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		ByteBufferAllocator_tCC19A5DCD32535F213BA3EDECBB08A7F9EFD6185* L_0 = __this->____buffer;
		NullCheck(L_0);
		Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305 L_1;
		L_1 = VirtualFuncInvoker0< Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305 >::Invoke(4, L_0);
		V_0 = L_1;
		int32_t L_2 = ___0_pos;
		int32_t L_3 = ___1_len;
		Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305 L_4;
		L_4 = Span_1_Slice_m9D8BA8245B8DC9BFB4A4164759CBAAEAD1318CD6_inline((&V_0), L_2, L_3, Span_1_Slice_m9D8BA8245B8DC9BFB4A4164759CBAAEAD1318CD6_RuntimeMethod_var);
		return L_4;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint32_t ByteBuffer_ReverseBytes_m36121CFC834DEE45DB23F98CA3864D14229D6DAC (uint32_t ___0_input, const RuntimeMethod* method) 
{
	{
		uint32_t L_0 = ___0_input;
		uint32_t L_1 = ___0_input;
		uint32_t L_2 = ___0_input;
		uint32_t L_3 = ___0_input;
		return ((int32_t)(((int32_t)(((int32_t)(((int32_t)(((int32_t)((int32_t)L_0&((int32_t)255)))<<((int32_t)24)))|((int32_t)(((int32_t)((int32_t)L_1&((int32_t)65280)))<<8))))|((int32_t)((uint32_t)((int32_t)((int32_t)L_2&((int32_t)16711680)))>>8))))|((int32_t)((uint32_t)((int32_t)((int32_t)L_3&((int32_t)-16777216)))>>((int32_t)24)))));
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint64_t ByteBuffer_ReverseBytes_mB15365C2362720EE11ACA5BE0B00B26D4CDA3DC0 (uint64_t ___0_input, const RuntimeMethod* method) 
{
	{
		uint64_t L_0 = ___0_input;
		uint64_t L_1 = ___0_input;
		uint64_t L_2 = ___0_input;
		uint64_t L_3 = ___0_input;
		uint64_t L_4 = ___0_input;
		uint64_t L_5 = ___0_input;
		uint64_t L_6 = ___0_input;
		uint64_t L_7 = ___0_input;
		return ((int64_t)(((int64_t)(((int64_t)(((int64_t)(((int64_t)(((int64_t)(((int64_t)(((int64_t)(((int64_t)((int64_t)L_0&((int64_t)((int32_t)255))))<<((int32_t)56)))|((int64_t)(((int64_t)((int64_t)L_1&((int64_t)((int32_t)65280))))<<((int32_t)40)))))|((int64_t)(((int64_t)((int64_t)L_2&((int64_t)((int32_t)16711680))))<<((int32_t)24)))))|((int64_t)(((int64_t)((int64_t)L_3&((int64_t)(uint64_t)((uint32_t)((int32_t)-16777216)))))<<8))))|((int64_t)((uint64_t)((int64_t)((int64_t)L_4&((int64_t)1095216660480LL)))>>8))))|((int64_t)((uint64_t)((int64_t)((int64_t)L_5&((int64_t)280375465082880LL)))>>((int32_t)24)))))|((int64_t)((uint64_t)((int64_t)((int64_t)L_6&((int64_t)71776119061217280LL)))>>((int32_t)40)))))|((int64_t)((uint64_t)((int64_t)((int64_t)L_7&((int64_t)-72057594037927936LL)))>>((int32_t)56)))));
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ByteBuffer_AssertOffsetAndLength_m7DE914630999CC751A304662915A714328B53AC7 (ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* __this, int32_t ___0_offset, int32_t ___1_length, const RuntimeMethod* method) 
{
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ByteBuffer_PutSbyte_mC6055AB467538627CB2EEA9050DA94A819EA5148 (ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* __this, int32_t ___0_offset, int8_t ___1_value, const RuntimeMethod* method) 
{
	Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		int32_t L_0 = ___0_offset;
		ByteBuffer_AssertOffsetAndLength_m7DE914630999CC751A304662915A714328B53AC7(__this, L_0, 1, NULL);
		ByteBufferAllocator_tCC19A5DCD32535F213BA3EDECBB08A7F9EFD6185* L_1 = __this->____buffer;
		NullCheck(L_1);
		Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305 L_2;
		L_2 = VirtualFuncInvoker0< Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305 >::Invoke(4, L_1);
		V_0 = L_2;
		int32_t L_3 = ___0_offset;
		uint8_t* L_4;
		L_4 = il2cpp_span_get_item((uint8_t*)((Il2CppByReference*)&(((&V_0))->____pointer))->value, (L_3), ((&V_0))->____length);
		int8_t L_5 = ___1_value;
		*((int8_t*)L_4) = (int8_t)((int32_t)(uint8_t)L_5);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ByteBuffer_PutByte_m81927B710F29CBE2EFB0E5F298398AB7D0D26967 (ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* __this, int32_t ___0_offset, uint8_t ___1_value, const RuntimeMethod* method) 
{
	Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		int32_t L_0 = ___0_offset;
		ByteBuffer_AssertOffsetAndLength_m7DE914630999CC751A304662915A714328B53AC7(__this, L_0, 1, NULL);
		ByteBufferAllocator_tCC19A5DCD32535F213BA3EDECBB08A7F9EFD6185* L_1 = __this->____buffer;
		NullCheck(L_1);
		Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305 L_2;
		L_2 = VirtualFuncInvoker0< Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305 >::Invoke(4, L_1);
		V_0 = L_2;
		int32_t L_3 = ___0_offset;
		uint8_t* L_4;
		L_4 = il2cpp_span_get_item((uint8_t*)((Il2CppByReference*)&(((&V_0))->____pointer))->value, (L_3), ((&V_0))->____length);
		uint8_t L_5 = ___1_value;
		*((int8_t*)L_4) = (int8_t)L_5;
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ByteBuffer_PutByte_mA27E6385D3CF1624F3A44D01275E631FC4F87B2E (ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* __this, int32_t ___0_offset, uint8_t ___1_value, int32_t ___2_count, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Span_1_Slice_m9D8BA8245B8DC9BFB4A4164759CBAAEAD1318CD6_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Span_1_get_Length_m8E944E4954E037877A25B9FF6B901F1F901D4769_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305 V_0;
	memset((&V_0), 0, sizeof(V_0));
	Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305 V_1;
	memset((&V_1), 0, sizeof(V_1));
	int32_t V_2 = 0;
	{
		int32_t L_0 = ___0_offset;
		int32_t L_1 = ___2_count;
		ByteBuffer_AssertOffsetAndLength_m7DE914630999CC751A304662915A714328B53AC7(__this, L_0, L_1, NULL);
		ByteBufferAllocator_tCC19A5DCD32535F213BA3EDECBB08A7F9EFD6185* L_2 = __this->____buffer;
		NullCheck(L_2);
		Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305 L_3;
		L_3 = VirtualFuncInvoker0< Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305 >::Invoke(4, L_2);
		V_1 = L_3;
		int32_t L_4 = ___0_offset;
		int32_t L_5 = ___2_count;
		Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305 L_6;
		L_6 = Span_1_Slice_m9D8BA8245B8DC9BFB4A4164759CBAAEAD1318CD6_inline((&V_1), L_4, L_5, Span_1_Slice_m9D8BA8245B8DC9BFB4A4164759CBAAEAD1318CD6_RuntimeMethod_var);
		V_0 = L_6;
		V_2 = 0;
		goto IL_0030;
	}

IL_0022:
	{
		int32_t L_7 = V_2;
		uint8_t* L_8;
		L_8 = il2cpp_span_get_item((uint8_t*)((Il2CppByReference*)&(((&V_0))->____pointer))->value, (L_7), ((&V_0))->____length);
		uint8_t L_9 = ___1_value;
		*((int8_t*)L_8) = (int8_t)L_9;
		int32_t L_10 = V_2;
		V_2 = ((int32_t)il2cpp_codegen_add(L_10, 1));
	}

IL_0030:
	{
		int32_t L_11 = V_2;
		int32_t L_12;
		L_12 = Span_1_get_Length_m8E944E4954E037877A25B9FF6B901F1F901D4769_inline((&V_0), Span_1_get_Length_m8E944E4954E037877A25B9FF6B901F1F901D4769_RuntimeMethod_var);
		if ((((int32_t)L_11) < ((int32_t)L_12)))
		{
			goto IL_0022;
		}
	}
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ByteBuffer_PutStringUTF8_m8A9FB9D67D35A58029657802ACA2C25A64BADAA3 (ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* __this, int32_t ___0_offset, String_t* ___1_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MemoryMarshal_GetReference_TisByte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_m81BE3C6812CE881C00AAA80CCFC9349F754F63A6_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	Il2CppChar* V_0 = NULL;
	String_t* V_1 = NULL;
	uint8_t* V_2 = NULL;
	uint8_t* V_3 = NULL;
	{
		int32_t L_0 = ___0_offset;
		String_t* L_1 = ___1_value;
		NullCheck(L_1);
		int32_t L_2;
		L_2 = String_get_Length_m42625D67623FA5CC7A44D47425CE86FB946542D2_inline(L_1, NULL);
		ByteBuffer_AssertOffsetAndLength_m7DE914630999CC751A304662915A714328B53AC7(__this, L_0, L_2, NULL);
		String_t* L_3 = ___1_value;
		V_1 = L_3;
		String_t* L_4 = V_1;
		V_0 = (Il2CppChar*)((uintptr_t)L_4);
		Il2CppChar* L_5 = V_0;
		if (!L_5)
		{
			goto IL_001d;
		}
	}
	{
		Il2CppChar* L_6 = V_0;
		int32_t L_7;
		L_7 = RuntimeHelpers_get_OffsetToStringData_m90A5D27EF88BE9432BF7093B7D7E7A0ACB0A8FBD(NULL);
		V_0 = ((Il2CppChar*)il2cpp_codegen_add((intptr_t)L_6, L_7));
	}

IL_001d:
	{
		ByteBufferAllocator_tCC19A5DCD32535F213BA3EDECBB08A7F9EFD6185* L_8 = __this->____buffer;
		NullCheck(L_8);
		Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305 L_9;
		L_9 = VirtualFuncInvoker0< Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305 >::Invoke(4, L_8);
		uint8_t* L_10;
		L_10 = MemoryMarshal_GetReference_TisByte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_m81BE3C6812CE881C00AAA80CCFC9349F754F63A6(L_9, MemoryMarshal_GetReference_TisByte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_m81BE3C6812CE881C00AAA80CCFC9349F754F63A6_RuntimeMethod_var);
		V_3 = L_10;
		uint8_t* L_11 = V_3;
		V_2 = (uint8_t*)((uintptr_t)L_11);
		Encoding_t65CDEF28CF20A7B8C92E85A4E808920C2465F095* L_12;
		L_12 = Encoding_get_UTF8_m9FA98A53CE96FD6D02982625C5246DD36C1235C9(NULL);
		Il2CppChar* L_13 = V_0;
		String_t* L_14 = ___1_value;
		NullCheck(L_14);
		int32_t L_15;
		L_15 = String_get_Length_m42625D67623FA5CC7A44D47425CE86FB946542D2_inline(L_14, NULL);
		uint8_t* L_16 = V_2;
		int32_t L_17 = ___0_offset;
		int32_t L_18;
		L_18 = ByteBuffer_get_Length_mD83481CCE00F09128DBAC206A094C628E635BA8B(__this, NULL);
		int32_t L_19 = ___0_offset;
		NullCheck(L_12);
		int32_t L_20;
		L_20 = VirtualFuncInvoker4< int32_t, Il2CppChar*, int32_t, uint8_t*, int32_t >::Invoke(23, L_12, L_13, L_15, ((uint8_t*)il2cpp_codegen_add((intptr_t)L_16, L_17)), ((int32_t)il2cpp_codegen_subtract(L_18, L_19)));
		V_3 = (uint8_t*)((uintptr_t)0);
		V_1 = (String_t*)NULL;
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ByteBuffer_PutShort_m40945E04907D64971273BA0CA8C483FEAE91F451 (ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* __this, int32_t ___0_offset, int16_t ___1_value, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = ___0_offset;
		int16_t L_1 = ___1_value;
		ByteBuffer_PutUshort_m3FAF834DEE05A58095F7851DEA351C8AAC6B09C4(__this, L_0, (uint16_t)((int32_t)(uint16_t)L_1), NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ByteBuffer_PutUshort_m3FAF834DEE05A58095F7851DEA351C8AAC6B09C4 (ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* __this, int32_t ___0_offset, uint16_t ___1_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Span_1_Slice_m720734AA48ECB663CAA0594530927B9015A64341_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		int32_t L_0 = ___0_offset;
		ByteBuffer_AssertOffsetAndLength_m7DE914630999CC751A304662915A714328B53AC7(__this, L_0, 2, NULL);
		ByteBufferAllocator_tCC19A5DCD32535F213BA3EDECBB08A7F9EFD6185* L_1 = __this->____buffer;
		NullCheck(L_1);
		Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305 L_2;
		L_2 = VirtualFuncInvoker0< Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305 >::Invoke(4, L_1);
		V_0 = L_2;
		int32_t L_3 = ___0_offset;
		Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305 L_4;
		L_4 = Span_1_Slice_m720734AA48ECB663CAA0594530927B9015A64341_inline((&V_0), L_3, Span_1_Slice_m720734AA48ECB663CAA0594530927B9015A64341_RuntimeMethod_var);
		uint16_t L_5 = ___1_value;
		BinaryPrimitives_WriteUInt16LittleEndian_mEA236B05E65D485C1934CCC155A6AF8A5C66773A_inline(L_4, L_5, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ByteBuffer_PutInt_mF305D6CF0623CB47E43C29A1CC08BFF7D3214C69 (ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* __this, int32_t ___0_offset, int32_t ___1_value, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = ___0_offset;
		int32_t L_1 = ___1_value;
		ByteBuffer_PutUint_m4FE24EA7B76E8405CD82B65841609BBBAF6C9F4C(__this, L_0, L_1, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ByteBuffer_PutUint_m4FE24EA7B76E8405CD82B65841609BBBAF6C9F4C (ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* __this, int32_t ___0_offset, uint32_t ___1_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Span_1_Slice_m720734AA48ECB663CAA0594530927B9015A64341_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		int32_t L_0 = ___0_offset;
		ByteBuffer_AssertOffsetAndLength_m7DE914630999CC751A304662915A714328B53AC7(__this, L_0, 4, NULL);
		ByteBufferAllocator_tCC19A5DCD32535F213BA3EDECBB08A7F9EFD6185* L_1 = __this->____buffer;
		NullCheck(L_1);
		Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305 L_2;
		L_2 = VirtualFuncInvoker0< Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305 >::Invoke(4, L_1);
		V_0 = L_2;
		int32_t L_3 = ___0_offset;
		Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305 L_4;
		L_4 = Span_1_Slice_m720734AA48ECB663CAA0594530927B9015A64341_inline((&V_0), L_3, Span_1_Slice_m720734AA48ECB663CAA0594530927B9015A64341_RuntimeMethod_var);
		uint32_t L_5 = ___1_value;
		BinaryPrimitives_WriteUInt32LittleEndian_mF5A685773CF1618F130D77F5072BDB7C389793B1_inline(L_4, L_5, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ByteBuffer_PutLong_mD69C4E9BF0355C81EB6B8829BF85161B95B02A2B (ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* __this, int32_t ___0_offset, int64_t ___1_value, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = ___0_offset;
		int64_t L_1 = ___1_value;
		ByteBuffer_PutUlong_m0E9B506A058766F3BB5D8BF01B10AFE695CC6C9F(__this, L_0, L_1, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ByteBuffer_PutUlong_m0E9B506A058766F3BB5D8BF01B10AFE695CC6C9F (ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* __this, int32_t ___0_offset, uint64_t ___1_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Span_1_Slice_m720734AA48ECB663CAA0594530927B9015A64341_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		int32_t L_0 = ___0_offset;
		ByteBuffer_AssertOffsetAndLength_m7DE914630999CC751A304662915A714328B53AC7(__this, L_0, 8, NULL);
		ByteBufferAllocator_tCC19A5DCD32535F213BA3EDECBB08A7F9EFD6185* L_1 = __this->____buffer;
		NullCheck(L_1);
		Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305 L_2;
		L_2 = VirtualFuncInvoker0< Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305 >::Invoke(4, L_1);
		V_0 = L_2;
		int32_t L_3 = ___0_offset;
		Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305 L_4;
		L_4 = Span_1_Slice_m720734AA48ECB663CAA0594530927B9015A64341_inline((&V_0), L_3, Span_1_Slice_m720734AA48ECB663CAA0594530927B9015A64341_RuntimeMethod_var);
		uint64_t L_5 = ___1_value;
		BinaryPrimitives_WriteUInt64LittleEndian_mCC039600290A6A5201532593AF3BE095D74C1625_inline(L_4, L_5, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ByteBuffer_PutFloat_m3BD6261E6C39B5B3F51DDF8412D26901024AC600 (ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* __this, int32_t ___0_offset, float ___1_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MemoryMarshal_GetReference_TisByte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_m81BE3C6812CE881C00AAA80CCFC9349F754F63A6_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	uint8_t* V_0 = NULL;
	uint8_t* V_1 = NULL;
	{
		int32_t L_0 = ___0_offset;
		ByteBuffer_AssertOffsetAndLength_m7DE914630999CC751A304662915A714328B53AC7(__this, L_0, 4, NULL);
		ByteBufferAllocator_tCC19A5DCD32535F213BA3EDECBB08A7F9EFD6185* L_1 = __this->____buffer;
		NullCheck(L_1);
		Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305 L_2;
		L_2 = VirtualFuncInvoker0< Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305 >::Invoke(4, L_1);
		uint8_t* L_3;
		L_3 = MemoryMarshal_GetReference_TisByte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_m81BE3C6812CE881C00AAA80CCFC9349F754F63A6(L_2, MemoryMarshal_GetReference_TisByte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_m81BE3C6812CE881C00AAA80CCFC9349F754F63A6_RuntimeMethod_var);
		V_1 = L_3;
		uint8_t* L_4 = V_1;
		V_0 = (uint8_t*)((uintptr_t)L_4);
		if (!il2cpp_codegen_is_little_endian())
		{
			goto IL_002a;
		}
	}
	{
		uint8_t* L_5 = V_0;
		int32_t L_6 = ___0_offset;
		float L_7 = ___1_value;
		*((float*)((uint8_t*)il2cpp_codegen_add((intptr_t)L_5, L_6))) = (float)L_7;
		goto IL_0037;
	}

IL_002a:
	{
		uint8_t* L_8 = V_0;
		int32_t L_9 = ___0_offset;
		int32_t L_10 = *((uint32_t*)((uintptr_t)(&___1_value)));
		il2cpp_codegen_runtime_class_init_inline(ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641_il2cpp_TypeInfo_var);
		uint32_t L_11;
		L_11 = ByteBuffer_ReverseBytes_m36121CFC834DEE45DB23F98CA3864D14229D6DAC(L_10, NULL);
		*((int32_t*)((uint8_t*)il2cpp_codegen_add((intptr_t)L_8, L_9))) = (int32_t)L_11;
	}

IL_0037:
	{
		V_1 = (uint8_t*)((uintptr_t)0);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ByteBuffer_PutDouble_mFDF587433B6198987FAF1AD26DF320F9BCA41A49 (ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* __this, int32_t ___0_offset, double ___1_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MemoryMarshal_GetReference_TisByte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_m81BE3C6812CE881C00AAA80CCFC9349F754F63A6_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	uint8_t* V_0 = NULL;
	uint8_t* V_1 = NULL;
	{
		int32_t L_0 = ___0_offset;
		ByteBuffer_AssertOffsetAndLength_m7DE914630999CC751A304662915A714328B53AC7(__this, L_0, 8, NULL);
		ByteBufferAllocator_tCC19A5DCD32535F213BA3EDECBB08A7F9EFD6185* L_1 = __this->____buffer;
		NullCheck(L_1);
		Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305 L_2;
		L_2 = VirtualFuncInvoker0< Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305 >::Invoke(4, L_1);
		uint8_t* L_3;
		L_3 = MemoryMarshal_GetReference_TisByte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_m81BE3C6812CE881C00AAA80CCFC9349F754F63A6(L_2, MemoryMarshal_GetReference_TisByte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_m81BE3C6812CE881C00AAA80CCFC9349F754F63A6_RuntimeMethod_var);
		V_1 = L_3;
		uint8_t* L_4 = V_1;
		V_0 = (uint8_t*)((uintptr_t)L_4);
		if (!il2cpp_codegen_is_little_endian())
		{
			goto IL_002a;
		}
	}
	{
		uint8_t* L_5 = V_0;
		int32_t L_6 = ___0_offset;
		double L_7 = ___1_value;
		*((double*)((uint8_t*)il2cpp_codegen_add((intptr_t)L_5, L_6))) = (double)L_7;
		goto IL_0037;
	}

IL_002a:
	{
		uint8_t* L_8 = V_0;
		int32_t L_9 = ___0_offset;
		int64_t L_10 = *((int64_t*)((uintptr_t)(&___1_value)));
		il2cpp_codegen_runtime_class_init_inline(ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641_il2cpp_TypeInfo_var);
		uint64_t L_11;
		L_11 = ByteBuffer_ReverseBytes_mB15365C2362720EE11ACA5BE0B00B26D4CDA3DC0(L_10, NULL);
		*((int64_t*)((uint8_t*)il2cpp_codegen_add((intptr_t)L_8, L_9))) = (int64_t)L_11;
	}

IL_0037:
	{
		V_1 = (uint8_t*)((uintptr_t)0);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int8_t ByteBuffer_GetSbyte_mC5F6FC81C592D6FDFDF41EE3EA719EEA2234295B (ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* __this, int32_t ___0_index, const RuntimeMethod* method) 
{
	ReadOnlySpan_1_tA850A6C0E88ABBA37646A078ACBC24D6D5FD9B4D V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		int32_t L_0 = ___0_index;
		ByteBuffer_AssertOffsetAndLength_m7DE914630999CC751A304662915A714328B53AC7(__this, L_0, 1, NULL);
		ByteBufferAllocator_tCC19A5DCD32535F213BA3EDECBB08A7F9EFD6185* L_1 = __this->____buffer;
		NullCheck(L_1);
		ReadOnlySpan_1_tA850A6C0E88ABBA37646A078ACBC24D6D5FD9B4D L_2;
		L_2 = VirtualFuncInvoker0< ReadOnlySpan_1_tA850A6C0E88ABBA37646A078ACBC24D6D5FD9B4D >::Invoke(5, L_1);
		V_0 = L_2;
		int32_t L_3 = ___0_index;
		uint8_t* L_4;
		L_4 = il2cpp_span_get_item((uint8_t*)((Il2CppByReference*)&(((&V_0))->____pointer))->value, (L_3), ((&V_0))->____length);
		int32_t L_5 = *((uint8_t*)L_4);
		return ((int8_t)L_5);
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint8_t ByteBuffer_Get_m4DACAF12CF9F83591DF6B595512714C893377FDB (ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* __this, int32_t ___0_index, const RuntimeMethod* method) 
{
	ReadOnlySpan_1_tA850A6C0E88ABBA37646A078ACBC24D6D5FD9B4D V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		int32_t L_0 = ___0_index;
		ByteBuffer_AssertOffsetAndLength_m7DE914630999CC751A304662915A714328B53AC7(__this, L_0, 1, NULL);
		ByteBufferAllocator_tCC19A5DCD32535F213BA3EDECBB08A7F9EFD6185* L_1 = __this->____buffer;
		NullCheck(L_1);
		ReadOnlySpan_1_tA850A6C0E88ABBA37646A078ACBC24D6D5FD9B4D L_2;
		L_2 = VirtualFuncInvoker0< ReadOnlySpan_1_tA850A6C0E88ABBA37646A078ACBC24D6D5FD9B4D >::Invoke(5, L_1);
		V_0 = L_2;
		int32_t L_3 = ___0_index;
		uint8_t* L_4;
		L_4 = il2cpp_span_get_item((uint8_t*)((Il2CppByReference*)&(((&V_0))->____pointer))->value, (L_3), ((&V_0))->____length);
		int32_t L_5 = *((uint8_t*)L_4);
		return (uint8_t)L_5;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* ByteBuffer_GetStringUTF8_m54FDA99A7BCE5B71C55BCE816CE73854FFFB0EAF (ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* __this, int32_t ___0_startPos, int32_t ___1_len, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MemoryMarshal_GetReference_TisByte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_m9D86D3A7A7F6A344D16464E6638E2BEAD3F4BC90_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ReadOnlySpan_1_Slice_mC8B7C665F49384744642F03EA355239F0E4AF966_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	uint8_t* V_0 = NULL;
	uint8_t* V_1 = NULL;
	ReadOnlySpan_1_tA850A6C0E88ABBA37646A078ACBC24D6D5FD9B4D V_2;
	memset((&V_2), 0, sizeof(V_2));
	{
		ByteBufferAllocator_tCC19A5DCD32535F213BA3EDECBB08A7F9EFD6185* L_0 = __this->____buffer;
		NullCheck(L_0);
		ReadOnlySpan_1_tA850A6C0E88ABBA37646A078ACBC24D6D5FD9B4D L_1;
		L_1 = VirtualFuncInvoker0< ReadOnlySpan_1_tA850A6C0E88ABBA37646A078ACBC24D6D5FD9B4D >::Invoke(5, L_0);
		V_2 = L_1;
		int32_t L_2 = ___0_startPos;
		ReadOnlySpan_1_tA850A6C0E88ABBA37646A078ACBC24D6D5FD9B4D L_3;
		L_3 = ReadOnlySpan_1_Slice_mC8B7C665F49384744642F03EA355239F0E4AF966_inline((&V_2), L_2, ReadOnlySpan_1_Slice_mC8B7C665F49384744642F03EA355239F0E4AF966_RuntimeMethod_var);
		uint8_t* L_4;
		L_4 = MemoryMarshal_GetReference_TisByte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_m9D86D3A7A7F6A344D16464E6638E2BEAD3F4BC90(L_3, MemoryMarshal_GetReference_TisByte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_m9D86D3A7A7F6A344D16464E6638E2BEAD3F4BC90_RuntimeMethod_var);
		V_1 = L_4;
		uint8_t* L_5 = V_1;
		V_0 = (uint8_t*)((uintptr_t)L_5);
		Encoding_t65CDEF28CF20A7B8C92E85A4E808920C2465F095* L_6;
		L_6 = Encoding_get_UTF8_m9FA98A53CE96FD6D02982625C5246DD36C1235C9(NULL);
		uint8_t* L_7 = V_0;
		int32_t L_8 = ___1_len;
		NullCheck(L_6);
		String_t* L_9;
		L_9 = Encoding_GetString_m42BFF0862341DCD5289A7D75B5D7A22CE9690EAD(L_6, L_7, L_8, NULL);
		return L_9;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int16_t ByteBuffer_GetShort_m3ED68273C4B0ABA97718B68F439D042C758D084B (ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* __this, int32_t ___0_offset, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = ___0_offset;
		uint16_t L_1;
		L_1 = ByteBuffer_GetUshort_m99CBB64DF8F7D844B0F6FE18671922DF28D5DBDD(__this, L_0, NULL);
		return ((int16_t)L_1);
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint16_t ByteBuffer_GetUshort_m99CBB64DF8F7D844B0F6FE18671922DF28D5DBDD (ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* __this, int32_t ___0_offset, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ReadOnlySpan_1_Slice_mC8B7C665F49384744642F03EA355239F0E4AF966_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	ReadOnlySpan_1_tA850A6C0E88ABBA37646A078ACBC24D6D5FD9B4D V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		int32_t L_0 = ___0_offset;
		ByteBuffer_AssertOffsetAndLength_m7DE914630999CC751A304662915A714328B53AC7(__this, L_0, 2, NULL);
		ByteBufferAllocator_tCC19A5DCD32535F213BA3EDECBB08A7F9EFD6185* L_1 = __this->____buffer;
		NullCheck(L_1);
		ReadOnlySpan_1_tA850A6C0E88ABBA37646A078ACBC24D6D5FD9B4D L_2;
		L_2 = VirtualFuncInvoker0< ReadOnlySpan_1_tA850A6C0E88ABBA37646A078ACBC24D6D5FD9B4D >::Invoke(5, L_1);
		V_0 = L_2;
		int32_t L_3 = ___0_offset;
		ReadOnlySpan_1_tA850A6C0E88ABBA37646A078ACBC24D6D5FD9B4D L_4;
		L_4 = ReadOnlySpan_1_Slice_mC8B7C665F49384744642F03EA355239F0E4AF966_inline((&V_0), L_3, ReadOnlySpan_1_Slice_mC8B7C665F49384744642F03EA355239F0E4AF966_RuntimeMethod_var);
		uint16_t L_5;
		L_5 = BinaryPrimitives_ReadUInt16LittleEndian_m6233B916B888350309C273E87ED2C3F787260889_inline(L_4, NULL);
		return L_5;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t ByteBuffer_GetInt_mD2F94B1EFC4E1B48E6A25E9FE27A36CB691F93C7 (ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* __this, int32_t ___0_offset, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = ___0_offset;
		uint32_t L_1;
		L_1 = ByteBuffer_GetUint_m7686A15FBE8934D1AE054CDE1D18F43BA59EBE6D(__this, L_0, NULL);
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint32_t ByteBuffer_GetUint_m7686A15FBE8934D1AE054CDE1D18F43BA59EBE6D (ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* __this, int32_t ___0_offset, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ReadOnlySpan_1_Slice_mC8B7C665F49384744642F03EA355239F0E4AF966_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	ReadOnlySpan_1_tA850A6C0E88ABBA37646A078ACBC24D6D5FD9B4D V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		int32_t L_0 = ___0_offset;
		ByteBuffer_AssertOffsetAndLength_m7DE914630999CC751A304662915A714328B53AC7(__this, L_0, 4, NULL);
		ByteBufferAllocator_tCC19A5DCD32535F213BA3EDECBB08A7F9EFD6185* L_1 = __this->____buffer;
		NullCheck(L_1);
		ReadOnlySpan_1_tA850A6C0E88ABBA37646A078ACBC24D6D5FD9B4D L_2;
		L_2 = VirtualFuncInvoker0< ReadOnlySpan_1_tA850A6C0E88ABBA37646A078ACBC24D6D5FD9B4D >::Invoke(5, L_1);
		V_0 = L_2;
		int32_t L_3 = ___0_offset;
		ReadOnlySpan_1_tA850A6C0E88ABBA37646A078ACBC24D6D5FD9B4D L_4;
		L_4 = ReadOnlySpan_1_Slice_mC8B7C665F49384744642F03EA355239F0E4AF966_inline((&V_0), L_3, ReadOnlySpan_1_Slice_mC8B7C665F49384744642F03EA355239F0E4AF966_RuntimeMethod_var);
		uint32_t L_5;
		L_5 = BinaryPrimitives_ReadUInt32LittleEndian_m1D2A6AA323C53D511E84C677D1F8F17077F3B070_inline(L_4, NULL);
		return L_5;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int64_t ByteBuffer_GetLong_m0F4A58CC22C3E0FA3B0B2FD93CC45345E9FB1059 (ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* __this, int32_t ___0_offset, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = ___0_offset;
		uint64_t L_1;
		L_1 = ByteBuffer_GetUlong_m5960AA0201977D5CBB9E711EB7A912E0A986F549(__this, L_0, NULL);
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint64_t ByteBuffer_GetUlong_m5960AA0201977D5CBB9E711EB7A912E0A986F549 (ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* __this, int32_t ___0_offset, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ReadOnlySpan_1_Slice_mC8B7C665F49384744642F03EA355239F0E4AF966_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	ReadOnlySpan_1_tA850A6C0E88ABBA37646A078ACBC24D6D5FD9B4D V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		int32_t L_0 = ___0_offset;
		ByteBuffer_AssertOffsetAndLength_m7DE914630999CC751A304662915A714328B53AC7(__this, L_0, 8, NULL);
		ByteBufferAllocator_tCC19A5DCD32535F213BA3EDECBB08A7F9EFD6185* L_1 = __this->____buffer;
		NullCheck(L_1);
		ReadOnlySpan_1_tA850A6C0E88ABBA37646A078ACBC24D6D5FD9B4D L_2;
		L_2 = VirtualFuncInvoker0< ReadOnlySpan_1_tA850A6C0E88ABBA37646A078ACBC24D6D5FD9B4D >::Invoke(5, L_1);
		V_0 = L_2;
		int32_t L_3 = ___0_offset;
		ReadOnlySpan_1_tA850A6C0E88ABBA37646A078ACBC24D6D5FD9B4D L_4;
		L_4 = ReadOnlySpan_1_Slice_mC8B7C665F49384744642F03EA355239F0E4AF966_inline((&V_0), L_3, ReadOnlySpan_1_Slice_mC8B7C665F49384744642F03EA355239F0E4AF966_RuntimeMethod_var);
		uint64_t L_5;
		L_5 = BinaryPrimitives_ReadUInt64LittleEndian_m9F91B7C963E163D3064EA52D2C3A4075A33FB32B_inline(L_4, NULL);
		return L_5;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float ByteBuffer_GetFloat_m24BCA222E9470DEFB7AE68B7DF3800B187DE4AD6 (ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* __this, int32_t ___0_offset, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MemoryMarshal_GetReference_TisByte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_m9D86D3A7A7F6A344D16464E6638E2BEAD3F4BC90_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	uint8_t* V_0 = NULL;
	uint8_t* V_1 = NULL;
	uint32_t V_2 = 0;
	{
		int32_t L_0 = ___0_offset;
		ByteBuffer_AssertOffsetAndLength_m7DE914630999CC751A304662915A714328B53AC7(__this, L_0, 4, NULL);
		ByteBufferAllocator_tCC19A5DCD32535F213BA3EDECBB08A7F9EFD6185* L_1 = __this->____buffer;
		NullCheck(L_1);
		ReadOnlySpan_1_tA850A6C0E88ABBA37646A078ACBC24D6D5FD9B4D L_2;
		L_2 = VirtualFuncInvoker0< ReadOnlySpan_1_tA850A6C0E88ABBA37646A078ACBC24D6D5FD9B4D >::Invoke(5, L_1);
		uint8_t* L_3;
		L_3 = MemoryMarshal_GetReference_TisByte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_m9D86D3A7A7F6A344D16464E6638E2BEAD3F4BC90(L_2, MemoryMarshal_GetReference_TisByte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_m9D86D3A7A7F6A344D16464E6638E2BEAD3F4BC90_RuntimeMethod_var);
		V_1 = L_3;
		uint8_t* L_4 = V_1;
		V_0 = (uint8_t*)((uintptr_t)L_4);
		if (!il2cpp_codegen_is_little_endian())
		{
			goto IL_0028;
		}
	}
	{
		uint8_t* L_5 = V_0;
		int32_t L_6 = ___0_offset;
		float L_7 = *((float*)((uint8_t*)il2cpp_codegen_add((intptr_t)L_5, L_6)));
		return L_7;
	}

IL_0028:
	{
		uint8_t* L_8 = V_0;
		int32_t L_9 = ___0_offset;
		int32_t L_10 = *((uint32_t*)((uint8_t*)il2cpp_codegen_add((intptr_t)L_8, L_9)));
		il2cpp_codegen_runtime_class_init_inline(ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641_il2cpp_TypeInfo_var);
		uint32_t L_11;
		L_11 = ByteBuffer_ReverseBytes_m36121CFC834DEE45DB23F98CA3864D14229D6DAC(L_10, NULL);
		V_2 = L_11;
		float L_12 = *((float*)((uintptr_t)(&V_2)));
		return L_12;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR double ByteBuffer_GetDouble_m30822FB271051CFEC593734ED7B4E71EBD61C018 (ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* __this, int32_t ___0_offset, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MemoryMarshal_GetReference_TisByte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_m9D86D3A7A7F6A344D16464E6638E2BEAD3F4BC90_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	uint8_t* V_0 = NULL;
	uint8_t* V_1 = NULL;
	uint64_t V_2 = 0;
	{
		int32_t L_0 = ___0_offset;
		ByteBuffer_AssertOffsetAndLength_m7DE914630999CC751A304662915A714328B53AC7(__this, L_0, 8, NULL);
		ByteBufferAllocator_tCC19A5DCD32535F213BA3EDECBB08A7F9EFD6185* L_1 = __this->____buffer;
		NullCheck(L_1);
		ReadOnlySpan_1_tA850A6C0E88ABBA37646A078ACBC24D6D5FD9B4D L_2;
		L_2 = VirtualFuncInvoker0< ReadOnlySpan_1_tA850A6C0E88ABBA37646A078ACBC24D6D5FD9B4D >::Invoke(5, L_1);
		uint8_t* L_3;
		L_3 = MemoryMarshal_GetReference_TisByte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_m9D86D3A7A7F6A344D16464E6638E2BEAD3F4BC90(L_2, MemoryMarshal_GetReference_TisByte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_m9D86D3A7A7F6A344D16464E6638E2BEAD3F4BC90_RuntimeMethod_var);
		V_1 = L_3;
		uint8_t* L_4 = V_1;
		V_0 = (uint8_t*)((uintptr_t)L_4);
		if (!il2cpp_codegen_is_little_endian())
		{
			goto IL_0028;
		}
	}
	{
		uint8_t* L_5 = V_0;
		int32_t L_6 = ___0_offset;
		double L_7 = *((double*)((uint8_t*)il2cpp_codegen_add((intptr_t)L_5, L_6)));
		return L_7;
	}

IL_0028:
	{
		uint8_t* L_8 = V_0;
		int32_t L_9 = ___0_offset;
		int64_t L_10 = *((int64_t*)((uint8_t*)il2cpp_codegen_add((intptr_t)L_8, L_9)));
		il2cpp_codegen_runtime_class_init_inline(ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641_il2cpp_TypeInfo_var);
		uint64_t L_11;
		L_11 = ByteBuffer_ReverseBytes_mB15365C2362720EE11ACA5BE0B00B26D4CDA3DC0(L_10, NULL);
		V_2 = L_11;
		double L_12 = *((double*)((uintptr_t)(&V_2)));
		return L_12;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ByteBuffer__cctor_m4200E985A77D02E2BB383C7B87CB5A607CD130D4 (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Dictionary_2_Add_mD091A436B1895D7A2D6C1848C6C84487C5873904_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Dictionary_2__ctor_m0AF6A9E01056850C9F7AE464B4099CA3F6D5E8EC_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Dictionary_2_t384D2A0FF42BF1302A3F513FC32DB105F1CD5455_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Double_tE150EF3D1D43DEE85D533810AB4C742307EEDE5F_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Int16_tB8EF286A9C33492FA6E6D6E67320BE93E794A175_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Int64_t092CFB123BE63C28ACDAF65C68F21A526050DBA3_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&SByte_tFEFFEF5D2FEBF5207950AE6FAC150FC53B668DB5_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Single_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Type_t_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&UInt16_tF4C148C876015C212FD72652D0B6ED8CC247A455_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&UInt32_t1833D51FFA667B18A5AA4B8D34DE284F8495D29B_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&UInt64_t8F12534CC8FC4B5860F2A2CD1EE79D322E7A41AF_0_0_0_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Dictionary_2_t384D2A0FF42BF1302A3F513FC32DB105F1CD5455* L_0 = (Dictionary_2_t384D2A0FF42BF1302A3F513FC32DB105F1CD5455*)il2cpp_codegen_object_new(Dictionary_2_t384D2A0FF42BF1302A3F513FC32DB105F1CD5455_il2cpp_TypeInfo_var);
		Dictionary_2__ctor_m0AF6A9E01056850C9F7AE464B4099CA3F6D5E8EC(L_0, Dictionary_2__ctor_m0AF6A9E01056850C9F7AE464B4099CA3F6D5E8EC_RuntimeMethod_var);
		Dictionary_2_t384D2A0FF42BF1302A3F513FC32DB105F1CD5455* L_1 = L_0;
		RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B L_2 = { reinterpret_cast<intptr_t> (Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_0_0_0_var) };
		il2cpp_codegen_runtime_class_init_inline(Type_t_il2cpp_TypeInfo_var);
		Type_t* L_3;
		L_3 = Type_GetTypeFromHandle_m6062B81682F79A4D6DF2640692EE6D9987858C57(L_2, NULL);
		NullCheck(L_1);
		Dictionary_2_Add_mD091A436B1895D7A2D6C1848C6C84487C5873904(L_1, L_3, 1, Dictionary_2_Add_mD091A436B1895D7A2D6C1848C6C84487C5873904_RuntimeMethod_var);
		Dictionary_2_t384D2A0FF42BF1302A3F513FC32DB105F1CD5455* L_4 = L_1;
		RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B L_5 = { reinterpret_cast<intptr_t> (Single_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_0_0_0_var) };
		Type_t* L_6;
		L_6 = Type_GetTypeFromHandle_m6062B81682F79A4D6DF2640692EE6D9987858C57(L_5, NULL);
		NullCheck(L_4);
		Dictionary_2_Add_mD091A436B1895D7A2D6C1848C6C84487C5873904(L_4, L_6, 4, Dictionary_2_Add_mD091A436B1895D7A2D6C1848C6C84487C5873904_RuntimeMethod_var);
		Dictionary_2_t384D2A0FF42BF1302A3F513FC32DB105F1CD5455* L_7 = L_4;
		RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B L_8 = { reinterpret_cast<intptr_t> (Double_tE150EF3D1D43DEE85D533810AB4C742307EEDE5F_0_0_0_var) };
		Type_t* L_9;
		L_9 = Type_GetTypeFromHandle_m6062B81682F79A4D6DF2640692EE6D9987858C57(L_8, NULL);
		NullCheck(L_7);
		Dictionary_2_Add_mD091A436B1895D7A2D6C1848C6C84487C5873904(L_7, L_9, 8, Dictionary_2_Add_mD091A436B1895D7A2D6C1848C6C84487C5873904_RuntimeMethod_var);
		Dictionary_2_t384D2A0FF42BF1302A3F513FC32DB105F1CD5455* L_10 = L_7;
		RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B L_11 = { reinterpret_cast<intptr_t> (SByte_tFEFFEF5D2FEBF5207950AE6FAC150FC53B668DB5_0_0_0_var) };
		Type_t* L_12;
		L_12 = Type_GetTypeFromHandle_m6062B81682F79A4D6DF2640692EE6D9987858C57(L_11, NULL);
		NullCheck(L_10);
		Dictionary_2_Add_mD091A436B1895D7A2D6C1848C6C84487C5873904(L_10, L_12, 1, Dictionary_2_Add_mD091A436B1895D7A2D6C1848C6C84487C5873904_RuntimeMethod_var);
		Dictionary_2_t384D2A0FF42BF1302A3F513FC32DB105F1CD5455* L_13 = L_10;
		RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B L_14 = { reinterpret_cast<intptr_t> (Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_0_0_0_var) };
		Type_t* L_15;
		L_15 = Type_GetTypeFromHandle_m6062B81682F79A4D6DF2640692EE6D9987858C57(L_14, NULL);
		NullCheck(L_13);
		Dictionary_2_Add_mD091A436B1895D7A2D6C1848C6C84487C5873904(L_13, L_15, 1, Dictionary_2_Add_mD091A436B1895D7A2D6C1848C6C84487C5873904_RuntimeMethod_var);
		Dictionary_2_t384D2A0FF42BF1302A3F513FC32DB105F1CD5455* L_16 = L_13;
		RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B L_17 = { reinterpret_cast<intptr_t> (Int16_tB8EF286A9C33492FA6E6D6E67320BE93E794A175_0_0_0_var) };
		Type_t* L_18;
		L_18 = Type_GetTypeFromHandle_m6062B81682F79A4D6DF2640692EE6D9987858C57(L_17, NULL);
		NullCheck(L_16);
		Dictionary_2_Add_mD091A436B1895D7A2D6C1848C6C84487C5873904(L_16, L_18, 2, Dictionary_2_Add_mD091A436B1895D7A2D6C1848C6C84487C5873904_RuntimeMethod_var);
		Dictionary_2_t384D2A0FF42BF1302A3F513FC32DB105F1CD5455* L_19 = L_16;
		RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B L_20 = { reinterpret_cast<intptr_t> (UInt16_tF4C148C876015C212FD72652D0B6ED8CC247A455_0_0_0_var) };
		Type_t* L_21;
		L_21 = Type_GetTypeFromHandle_m6062B81682F79A4D6DF2640692EE6D9987858C57(L_20, NULL);
		NullCheck(L_19);
		Dictionary_2_Add_mD091A436B1895D7A2D6C1848C6C84487C5873904(L_19, L_21, 2, Dictionary_2_Add_mD091A436B1895D7A2D6C1848C6C84487C5873904_RuntimeMethod_var);
		Dictionary_2_t384D2A0FF42BF1302A3F513FC32DB105F1CD5455* L_22 = L_19;
		RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B L_23 = { reinterpret_cast<intptr_t> (Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_0_0_0_var) };
		Type_t* L_24;
		L_24 = Type_GetTypeFromHandle_m6062B81682F79A4D6DF2640692EE6D9987858C57(L_23, NULL);
		NullCheck(L_22);
		Dictionary_2_Add_mD091A436B1895D7A2D6C1848C6C84487C5873904(L_22, L_24, 4, Dictionary_2_Add_mD091A436B1895D7A2D6C1848C6C84487C5873904_RuntimeMethod_var);
		Dictionary_2_t384D2A0FF42BF1302A3F513FC32DB105F1CD5455* L_25 = L_22;
		RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B L_26 = { reinterpret_cast<intptr_t> (UInt32_t1833D51FFA667B18A5AA4B8D34DE284F8495D29B_0_0_0_var) };
		Type_t* L_27;
		L_27 = Type_GetTypeFromHandle_m6062B81682F79A4D6DF2640692EE6D9987858C57(L_26, NULL);
		NullCheck(L_25);
		Dictionary_2_Add_mD091A436B1895D7A2D6C1848C6C84487C5873904(L_25, L_27, 4, Dictionary_2_Add_mD091A436B1895D7A2D6C1848C6C84487C5873904_RuntimeMethod_var);
		Dictionary_2_t384D2A0FF42BF1302A3F513FC32DB105F1CD5455* L_28 = L_25;
		RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B L_29 = { reinterpret_cast<intptr_t> (UInt64_t8F12534CC8FC4B5860F2A2CD1EE79D322E7A41AF_0_0_0_var) };
		Type_t* L_30;
		L_30 = Type_GetTypeFromHandle_m6062B81682F79A4D6DF2640692EE6D9987858C57(L_29, NULL);
		NullCheck(L_28);
		Dictionary_2_Add_mD091A436B1895D7A2D6C1848C6C84487C5873904(L_28, L_30, 8, Dictionary_2_Add_mD091A436B1895D7A2D6C1848C6C84487C5873904_RuntimeMethod_var);
		Dictionary_2_t384D2A0FF42BF1302A3F513FC32DB105F1CD5455* L_31 = L_28;
		RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B L_32 = { reinterpret_cast<intptr_t> (Int64_t092CFB123BE63C28ACDAF65C68F21A526050DBA3_0_0_0_var) };
		Type_t* L_33;
		L_33 = Type_GetTypeFromHandle_m6062B81682F79A4D6DF2640692EE6D9987858C57(L_32, NULL);
		NullCheck(L_31);
		Dictionary_2_Add_mD091A436B1895D7A2D6C1848C6C84487C5873904(L_31, L_33, 8, Dictionary_2_Add_mD091A436B1895D7A2D6C1848C6C84487C5873904_RuntimeMethod_var);
		((ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641_StaticFields*)il2cpp_codegen_static_fields_for(ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641_il2cpp_TypeInfo_var))->___genericSizes = L_31;
		Il2CppCodeGenWriteBarrier((void**)(&((ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641_StaticFields*)il2cpp_codegen_static_fields_for(ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641_il2cpp_TypeInfo_var))->___genericSizes), (void*)L_31);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FlatBufferBuilder__ctor_mD9C52676666C70EDB5CB7C7A9E3BA3CD07961796 (FlatBufferBuilder_t23EDF7F7B5CFBF2A0BBCC2CCE6D14C02BE050CB0* __this, ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* ___0_buffer, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		__this->____minAlign = 1;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_0 = (Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)(Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)SZArrayNew(Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C_il2cpp_TypeInfo_var, (uint32_t)((int32_t)16));
		__this->____vtable = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->____vtable), (void*)L_0);
		__this->____vtableSize = (-1);
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_1 = (Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)(Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)SZArrayNew(Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C_il2cpp_TypeInfo_var, (uint32_t)((int32_t)16));
		__this->____vtables = L_1;
		Il2CppCodeGenWriteBarrier((void**)(&__this->____vtables), (void*)L_1);
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* L_2 = ___0_buffer;
		__this->____bb = L_2;
		Il2CppCodeGenWriteBarrier((void**)(&__this->____bb), (void*)L_2);
		ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* L_3 = ___0_buffer;
		NullCheck(L_3);
		int32_t L_4;
		L_4 = ByteBuffer_get_Length_mD83481CCE00F09128DBAC206A094C628E635BA8B(L_3, NULL);
		__this->____space = L_4;
		ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* L_5 = ___0_buffer;
		NullCheck(L_5);
		ByteBuffer_Reset_mC9313064E9ABBF21238DD1581864FF7621F343FE(L_5, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FlatBufferBuilder_Clear_mEF930BED718919B45BF5CCDDC7028D3D851FC7A4 (FlatBufferBuilder_t23EDF7F7B5CFBF2A0BBCC2CCE6D14C02BE050CB0* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Dictionary_2_Clear_mA0B5ABBC9D22FF2ADCBD908A1BCD8AAA183BEDF8_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	int32_t V_0 = 0;
	{
		ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* L_0 = __this->____bb;
		NullCheck(L_0);
		int32_t L_1;
		L_1 = ByteBuffer_get_Length_mD83481CCE00F09128DBAC206A094C628E635BA8B(L_0, NULL);
		__this->____space = L_1;
		ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* L_2 = __this->____bb;
		NullCheck(L_2);
		ByteBuffer_Reset_mC9313064E9ABBF21238DD1581864FF7621F343FE(L_2, NULL);
		__this->____minAlign = 1;
		goto IL_003e;
	}

IL_0025:
	{
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_3 = __this->____vtable;
		int32_t L_4 = __this->____vtableSize;
		V_0 = ((int32_t)il2cpp_codegen_subtract(L_4, 1));
		int32_t L_5 = V_0;
		__this->____vtableSize = L_5;
		int32_t L_6 = V_0;
		NullCheck(L_3);
		(L_3)->SetAt(static_cast<il2cpp_array_size_t>(L_6), (int32_t)0);
	}

IL_003e:
	{
		int32_t L_7 = __this->____vtableSize;
		if ((((int32_t)L_7) > ((int32_t)0)))
		{
			goto IL_0025;
		}
	}
	{
		__this->____vtableSize = (-1);
		__this->____objectStart = 0;
		__this->____numVtables = 0;
		__this->____vectorNumElems = 0;
		Dictionary_2_tEE02057113A88692CEC84BBCEB9E74DC6CC243E0* L_8 = __this->____sharedStringMap;
		if (!L_8)
		{
			goto IL_0076;
		}
	}
	{
		Dictionary_2_tEE02057113A88692CEC84BBCEB9E74DC6CC243E0* L_9 = __this->____sharedStringMap;
		NullCheck(L_9);
		Dictionary_2_Clear_mA0B5ABBC9D22FF2ADCBD908A1BCD8AAA183BEDF8(L_9, Dictionary_2_Clear_mA0B5ABBC9D22FF2ADCBD908A1BCD8AAA183BEDF8_RuntimeMethod_var);
	}

IL_0076:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool FlatBufferBuilder_get_ForceDefaults_mA0FE8A741C81C5945948228ED35D4F7E3BECC960 (FlatBufferBuilder_t23EDF7F7B5CFBF2A0BBCC2CCE6D14C02BE050CB0* __this, const RuntimeMethod* method) 
{
	{
		bool L_0 = __this->___U3CForceDefaultsU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t FlatBufferBuilder_get_Offset_mC74D55C982A3866A2171140BFFEF663306288AE9 (FlatBufferBuilder_t23EDF7F7B5CFBF2A0BBCC2CCE6D14C02BE050CB0* __this, const RuntimeMethod* method) 
{
	{
		ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* L_0 = __this->____bb;
		NullCheck(L_0);
		int32_t L_1;
		L_1 = ByteBuffer_get_Length_mD83481CCE00F09128DBAC206A094C628E635BA8B(L_0, NULL);
		int32_t L_2 = __this->____space;
		return ((int32_t)il2cpp_codegen_subtract(L_1, L_2));
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FlatBufferBuilder_Pad_m45B94144FC69144F0D1E1D10A7012FEA5393F964 (FlatBufferBuilder_t23EDF7F7B5CFBF2A0BBCC2CCE6D14C02BE050CB0* __this, int32_t ___0_size, const RuntimeMethod* method) 
{
	int32_t V_0 = 0;
	{
		ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* L_0 = __this->____bb;
		int32_t L_1 = __this->____space;
		int32_t L_2 = ___0_size;
		int32_t L_3 = ((int32_t)il2cpp_codegen_subtract(L_1, L_2));
		V_0 = L_3;
		__this->____space = L_3;
		int32_t L_4 = V_0;
		int32_t L_5 = ___0_size;
		NullCheck(L_0);
		ByteBuffer_PutByte_mA27E6385D3CF1624F3A44D01275E631FC4F87B2E(L_0, L_4, (uint8_t)0, L_5, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FlatBufferBuilder_GrowBuffer_m880D7E362F3C37DF5D2832D6554A8AEEE73E5367 (FlatBufferBuilder_t23EDF7F7B5CFBF2A0BBCC2CCE6D14C02BE050CB0* __this, const RuntimeMethod* method) 
{
	{
		ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* L_0 = __this->____bb;
		ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* L_1 = __this->____bb;
		NullCheck(L_1);
		int32_t L_2;
		L_2 = ByteBuffer_get_Length_mD83481CCE00F09128DBAC206A094C628E635BA8B(L_1, NULL);
		NullCheck(L_0);
		ByteBuffer_GrowFront_m0DE57B8E020B3948327E205840F49809DA043F30(L_0, ((int32_t)(L_2<<1)), NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FlatBufferBuilder_Prep_m3DA1C5F1967E6C9DE1F991519E1F87EE95C35AB7 (FlatBufferBuilder_t23EDF7F7B5CFBF2A0BBCC2CCE6D14C02BE050CB0* __this, int32_t ___0_size, int32_t ___1_additionalBytes, const RuntimeMethod* method) 
{
	int32_t V_0 = 0;
	int32_t V_1 = 0;
	{
		int32_t L_0 = ___0_size;
		int32_t L_1 = __this->____minAlign;
		if ((((int32_t)L_0) <= ((int32_t)L_1)))
		{
			goto IL_0010;
		}
	}
	{
		int32_t L_2 = ___0_size;
		__this->____minAlign = L_2;
	}

IL_0010:
	{
		ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* L_3 = __this->____bb;
		NullCheck(L_3);
		int32_t L_4;
		L_4 = ByteBuffer_get_Length_mD83481CCE00F09128DBAC206A094C628E635BA8B(L_3, NULL);
		int32_t L_5 = __this->____space;
		int32_t L_6 = ___1_additionalBytes;
		int32_t L_7 = ___0_size;
		V_0 = ((int32_t)(((int32_t)il2cpp_codegen_add(((~((int32_t)il2cpp_codegen_add(((int32_t)il2cpp_codegen_subtract(L_4, L_5)), L_6)))), 1))&((int32_t)il2cpp_codegen_subtract(L_7, 1))));
		goto IL_005a;
	}

IL_002e:
	{
		ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* L_8 = __this->____bb;
		NullCheck(L_8);
		int32_t L_9;
		L_9 = ByteBuffer_get_Length_mD83481CCE00F09128DBAC206A094C628E635BA8B(L_8, NULL);
		V_1 = L_9;
		FlatBufferBuilder_GrowBuffer_m880D7E362F3C37DF5D2832D6554A8AEEE73E5367(__this, NULL);
		int32_t L_10 = __this->____space;
		ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* L_11 = __this->____bb;
		NullCheck(L_11);
		int32_t L_12;
		L_12 = ByteBuffer_get_Length_mD83481CCE00F09128DBAC206A094C628E635BA8B(L_11, NULL);
		int32_t L_13 = V_1;
		__this->____space = ((int32_t)il2cpp_codegen_add(L_10, ((int32_t)il2cpp_codegen_subtract(L_12, L_13))));
	}

IL_005a:
	{
		int32_t L_14 = __this->____space;
		int32_t L_15 = V_0;
		int32_t L_16 = ___0_size;
		int32_t L_17 = ___1_additionalBytes;
		if ((((int32_t)L_14) < ((int32_t)((int32_t)il2cpp_codegen_add(((int32_t)il2cpp_codegen_add(L_15, L_16)), L_17)))))
		{
			goto IL_002e;
		}
	}
	{
		int32_t L_18 = V_0;
		if ((((int32_t)L_18) <= ((int32_t)0)))
		{
			goto IL_0072;
		}
	}
	{
		int32_t L_19 = V_0;
		FlatBufferBuilder_Pad_m45B94144FC69144F0D1E1D10A7012FEA5393F964(__this, L_19, NULL);
	}

IL_0072:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FlatBufferBuilder_PutBool_m9DFECA7733C9F313CBB77F2EA3D2C16A6CC4D925 (FlatBufferBuilder_t23EDF7F7B5CFBF2A0BBCC2CCE6D14C02BE050CB0* __this, bool ___0_x, const RuntimeMethod* method) 
{
	int32_t V_0 = 0;
	{
		ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* L_0 = __this->____bb;
		int32_t L_1 = __this->____space;
		int32_t L_2 = ((int32_t)il2cpp_codegen_subtract(L_1, 1));
		V_0 = L_2;
		__this->____space = L_2;
		int32_t L_3 = V_0;
		bool L_4 = ___0_x;
		NullCheck(L_0);
		ByteBuffer_PutByte_m81927B710F29CBE2EFB0E5F298398AB7D0D26967(L_0, L_3, (uint8_t)((int32_t)(uint8_t)((!(((uint32_t)L_4) <= ((uint32_t)0)))? 1 : 0)), NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FlatBufferBuilder_PutSbyte_m4B1681B63002727EF1ACEB361FFD075CBC35EA8C (FlatBufferBuilder_t23EDF7F7B5CFBF2A0BBCC2CCE6D14C02BE050CB0* __this, int8_t ___0_x, const RuntimeMethod* method) 
{
	int32_t V_0 = 0;
	{
		ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* L_0 = __this->____bb;
		int32_t L_1 = __this->____space;
		int32_t L_2 = ((int32_t)il2cpp_codegen_subtract(L_1, 1));
		V_0 = L_2;
		__this->____space = L_2;
		int32_t L_3 = V_0;
		int8_t L_4 = ___0_x;
		NullCheck(L_0);
		ByteBuffer_PutSbyte_mC6055AB467538627CB2EEA9050DA94A819EA5148(L_0, L_3, L_4, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FlatBufferBuilder_PutByte_mEA2C1C8F64D64C81C35C1D51D33423CF608A60F3 (FlatBufferBuilder_t23EDF7F7B5CFBF2A0BBCC2CCE6D14C02BE050CB0* __this, uint8_t ___0_x, const RuntimeMethod* method) 
{
	int32_t V_0 = 0;
	{
		ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* L_0 = __this->____bb;
		int32_t L_1 = __this->____space;
		int32_t L_2 = ((int32_t)il2cpp_codegen_subtract(L_1, 1));
		V_0 = L_2;
		__this->____space = L_2;
		int32_t L_3 = V_0;
		uint8_t L_4 = ___0_x;
		NullCheck(L_0);
		ByteBuffer_PutByte_m81927B710F29CBE2EFB0E5F298398AB7D0D26967(L_0, L_3, L_4, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FlatBufferBuilder_PutShort_mE92EC85294499D0DC414F376099F73C267C82D0E (FlatBufferBuilder_t23EDF7F7B5CFBF2A0BBCC2CCE6D14C02BE050CB0* __this, int16_t ___0_x, const RuntimeMethod* method) 
{
	int32_t V_0 = 0;
	{
		ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* L_0 = __this->____bb;
		int32_t L_1 = __this->____space;
		int32_t L_2 = ((int32_t)il2cpp_codegen_subtract(L_1, 2));
		V_0 = L_2;
		__this->____space = L_2;
		int32_t L_3 = V_0;
		int16_t L_4 = ___0_x;
		NullCheck(L_0);
		ByteBuffer_PutShort_m40945E04907D64971273BA0CA8C483FEAE91F451(L_0, L_3, L_4, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FlatBufferBuilder_PutUshort_mA897A7DC14B66A18334B2948B58F918B1FFFF915 (FlatBufferBuilder_t23EDF7F7B5CFBF2A0BBCC2CCE6D14C02BE050CB0* __this, uint16_t ___0_x, const RuntimeMethod* method) 
{
	int32_t V_0 = 0;
	{
		ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* L_0 = __this->____bb;
		int32_t L_1 = __this->____space;
		int32_t L_2 = ((int32_t)il2cpp_codegen_subtract(L_1, 2));
		V_0 = L_2;
		__this->____space = L_2;
		int32_t L_3 = V_0;
		uint16_t L_4 = ___0_x;
		NullCheck(L_0);
		ByteBuffer_PutUshort_m3FAF834DEE05A58095F7851DEA351C8AAC6B09C4(L_0, L_3, L_4, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FlatBufferBuilder_PutInt_m3657C36B7F37D77B13786E341A742332A942C07B (FlatBufferBuilder_t23EDF7F7B5CFBF2A0BBCC2CCE6D14C02BE050CB0* __this, int32_t ___0_x, const RuntimeMethod* method) 
{
	int32_t V_0 = 0;
	{
		ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* L_0 = __this->____bb;
		int32_t L_1 = __this->____space;
		int32_t L_2 = ((int32_t)il2cpp_codegen_subtract(L_1, 4));
		V_0 = L_2;
		__this->____space = L_2;
		int32_t L_3 = V_0;
		int32_t L_4 = ___0_x;
		NullCheck(L_0);
		ByteBuffer_PutInt_mF305D6CF0623CB47E43C29A1CC08BFF7D3214C69(L_0, L_3, L_4, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FlatBufferBuilder_PutUint_mE4C61BE169A0CEF7FDD69C461DA07A200B5A2F6A (FlatBufferBuilder_t23EDF7F7B5CFBF2A0BBCC2CCE6D14C02BE050CB0* __this, uint32_t ___0_x, const RuntimeMethod* method) 
{
	int32_t V_0 = 0;
	{
		ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* L_0 = __this->____bb;
		int32_t L_1 = __this->____space;
		int32_t L_2 = ((int32_t)il2cpp_codegen_subtract(L_1, 4));
		V_0 = L_2;
		__this->____space = L_2;
		int32_t L_3 = V_0;
		uint32_t L_4 = ___0_x;
		NullCheck(L_0);
		ByteBuffer_PutUint_m4FE24EA7B76E8405CD82B65841609BBBAF6C9F4C(L_0, L_3, L_4, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FlatBufferBuilder_PutLong_m69929C60C71F08604C3E428A3C3C4F047CEF6F1A (FlatBufferBuilder_t23EDF7F7B5CFBF2A0BBCC2CCE6D14C02BE050CB0* __this, int64_t ___0_x, const RuntimeMethod* method) 
{
	int32_t V_0 = 0;
	{
		ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* L_0 = __this->____bb;
		int32_t L_1 = __this->____space;
		int32_t L_2 = ((int32_t)il2cpp_codegen_subtract(L_1, 8));
		V_0 = L_2;
		__this->____space = L_2;
		int32_t L_3 = V_0;
		int64_t L_4 = ___0_x;
		NullCheck(L_0);
		ByteBuffer_PutLong_mD69C4E9BF0355C81EB6B8829BF85161B95B02A2B(L_0, L_3, L_4, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FlatBufferBuilder_PutUlong_m38DBCF4668E499C51EB30003652730076F100680 (FlatBufferBuilder_t23EDF7F7B5CFBF2A0BBCC2CCE6D14C02BE050CB0* __this, uint64_t ___0_x, const RuntimeMethod* method) 
{
	int32_t V_0 = 0;
	{
		ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* L_0 = __this->____bb;
		int32_t L_1 = __this->____space;
		int32_t L_2 = ((int32_t)il2cpp_codegen_subtract(L_1, 8));
		V_0 = L_2;
		__this->____space = L_2;
		int32_t L_3 = V_0;
		uint64_t L_4 = ___0_x;
		NullCheck(L_0);
		ByteBuffer_PutUlong_m0E9B506A058766F3BB5D8BF01B10AFE695CC6C9F(L_0, L_3, L_4, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FlatBufferBuilder_PutFloat_m4BF4065EA873C657DFC67B301F0AE81AF8A6AB5D (FlatBufferBuilder_t23EDF7F7B5CFBF2A0BBCC2CCE6D14C02BE050CB0* __this, float ___0_x, const RuntimeMethod* method) 
{
	int32_t V_0 = 0;
	{
		ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* L_0 = __this->____bb;
		int32_t L_1 = __this->____space;
		int32_t L_2 = ((int32_t)il2cpp_codegen_subtract(L_1, 4));
		V_0 = L_2;
		__this->____space = L_2;
		int32_t L_3 = V_0;
		float L_4 = ___0_x;
		NullCheck(L_0);
		ByteBuffer_PutFloat_m3BD6261E6C39B5B3F51DDF8412D26901024AC600(L_0, L_3, L_4, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FlatBufferBuilder_PutDouble_mD51D07A38BABE4EA8EB9689D8D5536B97851981D (FlatBufferBuilder_t23EDF7F7B5CFBF2A0BBCC2CCE6D14C02BE050CB0* __this, double ___0_x, const RuntimeMethod* method) 
{
	int32_t V_0 = 0;
	{
		ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* L_0 = __this->____bb;
		int32_t L_1 = __this->____space;
		int32_t L_2 = ((int32_t)il2cpp_codegen_subtract(L_1, 8));
		V_0 = L_2;
		__this->____space = L_2;
		int32_t L_3 = V_0;
		double L_4 = ___0_x;
		NullCheck(L_0);
		ByteBuffer_PutDouble_mFDF587433B6198987FAF1AD26DF320F9BCA41A49(L_0, L_3, L_4, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FlatBufferBuilder_AddBool_m478C50A00AA0FFFC9A0551F1B14688D00E654D67 (FlatBufferBuilder_t23EDF7F7B5CFBF2A0BBCC2CCE6D14C02BE050CB0* __this, bool ___0_x, const RuntimeMethod* method) 
{
	{
		FlatBufferBuilder_Prep_m3DA1C5F1967E6C9DE1F991519E1F87EE95C35AB7(__this, 1, 0, NULL);
		bool L_0 = ___0_x;
		FlatBufferBuilder_PutBool_m9DFECA7733C9F313CBB77F2EA3D2C16A6CC4D925(__this, L_0, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FlatBufferBuilder_AddSbyte_m8205DD96B17E57ECA93B00E0CF9941423E635C29 (FlatBufferBuilder_t23EDF7F7B5CFBF2A0BBCC2CCE6D14C02BE050CB0* __this, int8_t ___0_x, const RuntimeMethod* method) 
{
	{
		FlatBufferBuilder_Prep_m3DA1C5F1967E6C9DE1F991519E1F87EE95C35AB7(__this, 1, 0, NULL);
		int8_t L_0 = ___0_x;
		FlatBufferBuilder_PutSbyte_m4B1681B63002727EF1ACEB361FFD075CBC35EA8C(__this, L_0, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FlatBufferBuilder_AddByte_m09B496156A95861FDF7841640C5C1719723A7036 (FlatBufferBuilder_t23EDF7F7B5CFBF2A0BBCC2CCE6D14C02BE050CB0* __this, uint8_t ___0_x, const RuntimeMethod* method) 
{
	{
		FlatBufferBuilder_Prep_m3DA1C5F1967E6C9DE1F991519E1F87EE95C35AB7(__this, 1, 0, NULL);
		uint8_t L_0 = ___0_x;
		FlatBufferBuilder_PutByte_mEA2C1C8F64D64C81C35C1D51D33423CF608A60F3(__this, L_0, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FlatBufferBuilder_AddShort_mA240DA72E828B425EEF2128E7933F8E9696F5075 (FlatBufferBuilder_t23EDF7F7B5CFBF2A0BBCC2CCE6D14C02BE050CB0* __this, int16_t ___0_x, const RuntimeMethod* method) 
{
	{
		FlatBufferBuilder_Prep_m3DA1C5F1967E6C9DE1F991519E1F87EE95C35AB7(__this, 2, 0, NULL);
		int16_t L_0 = ___0_x;
		FlatBufferBuilder_PutShort_mE92EC85294499D0DC414F376099F73C267C82D0E(__this, L_0, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FlatBufferBuilder_AddUshort_mFBED2384AD4CFA68C46F725B9342FA4F8AB02573 (FlatBufferBuilder_t23EDF7F7B5CFBF2A0BBCC2CCE6D14C02BE050CB0* __this, uint16_t ___0_x, const RuntimeMethod* method) 
{
	{
		FlatBufferBuilder_Prep_m3DA1C5F1967E6C9DE1F991519E1F87EE95C35AB7(__this, 2, 0, NULL);
		uint16_t L_0 = ___0_x;
		FlatBufferBuilder_PutUshort_mA897A7DC14B66A18334B2948B58F918B1FFFF915(__this, L_0, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FlatBufferBuilder_AddInt_mC7ED89CD9BAD7A1F34E445DDFF066513AA2B926E (FlatBufferBuilder_t23EDF7F7B5CFBF2A0BBCC2CCE6D14C02BE050CB0* __this, int32_t ___0_x, const RuntimeMethod* method) 
{
	{
		FlatBufferBuilder_Prep_m3DA1C5F1967E6C9DE1F991519E1F87EE95C35AB7(__this, 4, 0, NULL);
		int32_t L_0 = ___0_x;
		FlatBufferBuilder_PutInt_m3657C36B7F37D77B13786E341A742332A942C07B(__this, L_0, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FlatBufferBuilder_AddUint_m5E52C37A1B3FF5FEF363B603C8E379847EBF0FF9 (FlatBufferBuilder_t23EDF7F7B5CFBF2A0BBCC2CCE6D14C02BE050CB0* __this, uint32_t ___0_x, const RuntimeMethod* method) 
{
	{
		FlatBufferBuilder_Prep_m3DA1C5F1967E6C9DE1F991519E1F87EE95C35AB7(__this, 4, 0, NULL);
		uint32_t L_0 = ___0_x;
		FlatBufferBuilder_PutUint_mE4C61BE169A0CEF7FDD69C461DA07A200B5A2F6A(__this, L_0, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FlatBufferBuilder_AddLong_m7941C58219E84656250A86B5B70D5E3731753DB5 (FlatBufferBuilder_t23EDF7F7B5CFBF2A0BBCC2CCE6D14C02BE050CB0* __this, int64_t ___0_x, const RuntimeMethod* method) 
{
	{
		FlatBufferBuilder_Prep_m3DA1C5F1967E6C9DE1F991519E1F87EE95C35AB7(__this, 8, 0, NULL);
		int64_t L_0 = ___0_x;
		FlatBufferBuilder_PutLong_m69929C60C71F08604C3E428A3C3C4F047CEF6F1A(__this, L_0, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FlatBufferBuilder_AddUlong_mD667451BC4F21D55EB19276E56C538BABCB0C9A3 (FlatBufferBuilder_t23EDF7F7B5CFBF2A0BBCC2CCE6D14C02BE050CB0* __this, uint64_t ___0_x, const RuntimeMethod* method) 
{
	{
		FlatBufferBuilder_Prep_m3DA1C5F1967E6C9DE1F991519E1F87EE95C35AB7(__this, 8, 0, NULL);
		uint64_t L_0 = ___0_x;
		FlatBufferBuilder_PutUlong_m38DBCF4668E499C51EB30003652730076F100680(__this, L_0, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FlatBufferBuilder_AddFloat_m631138283BEE1A25D70D62629445315F699D2C4D (FlatBufferBuilder_t23EDF7F7B5CFBF2A0BBCC2CCE6D14C02BE050CB0* __this, float ___0_x, const RuntimeMethod* method) 
{
	{
		FlatBufferBuilder_Prep_m3DA1C5F1967E6C9DE1F991519E1F87EE95C35AB7(__this, 4, 0, NULL);
		float L_0 = ___0_x;
		FlatBufferBuilder_PutFloat_m4BF4065EA873C657DFC67B301F0AE81AF8A6AB5D(__this, L_0, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FlatBufferBuilder_AddDouble_mF109F698FFEF929DE54342B6A78D58F791DA7E5D (FlatBufferBuilder_t23EDF7F7B5CFBF2A0BBCC2CCE6D14C02BE050CB0* __this, double ___0_x, const RuntimeMethod* method) 
{
	{
		FlatBufferBuilder_Prep_m3DA1C5F1967E6C9DE1F991519E1F87EE95C35AB7(__this, 8, 0, NULL);
		double L_0 = ___0_x;
		FlatBufferBuilder_PutDouble_mD51D07A38BABE4EA8EB9689D8D5536B97851981D(__this, L_0, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FlatBufferBuilder_AddOffset_m7A64D677E7BE356B284C1DD84A7FD6E8C7DDCA27 (FlatBufferBuilder_t23EDF7F7B5CFBF2A0BBCC2CCE6D14C02BE050CB0* __this, int32_t ___0_off, const RuntimeMethod* method) 
{
	{
		FlatBufferBuilder_Prep_m3DA1C5F1967E6C9DE1F991519E1F87EE95C35AB7(__this, 4, 0, NULL);
		int32_t L_0 = ___0_off;
		int32_t L_1;
		L_1 = FlatBufferBuilder_get_Offset_mC74D55C982A3866A2171140BFFEF663306288AE9(__this, NULL);
		if ((((int32_t)L_0) <= ((int32_t)L_1)))
		{
			goto IL_0017;
		}
	}
	{
		ArgumentException_tAD90411542A20A9C72D5CDA3A84181D8B947A263* L_2 = (ArgumentException_tAD90411542A20A9C72D5CDA3A84181D8B947A263*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ArgumentException_tAD90411542A20A9C72D5CDA3A84181D8B947A263_il2cpp_TypeInfo_var)));
		ArgumentException__ctor_m34A925BA55EC4CE4253404E363B5F6A53EB51CA3(L_2, NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_2, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&FlatBufferBuilder_AddOffset_m7A64D677E7BE356B284C1DD84A7FD6E8C7DDCA27_RuntimeMethod_var)));
	}

IL_0017:
	{
		int32_t L_3 = ___0_off;
		if (!L_3)
		{
			goto IL_0026;
		}
	}
	{
		int32_t L_4;
		L_4 = FlatBufferBuilder_get_Offset_mC74D55C982A3866A2171140BFFEF663306288AE9(__this, NULL);
		int32_t L_5 = ___0_off;
		___0_off = ((int32_t)il2cpp_codegen_add(((int32_t)il2cpp_codegen_subtract(L_4, L_5)), 4));
	}

IL_0026:
	{
		int32_t L_6 = ___0_off;
		FlatBufferBuilder_PutInt_m3657C36B7F37D77B13786E341A742332A942C07B(__this, L_6, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FlatBufferBuilder_StartVector_m6150568905670346B15955DDA02E5A09EB816E8D (FlatBufferBuilder_t23EDF7F7B5CFBF2A0BBCC2CCE6D14C02BE050CB0* __this, int32_t ___0_elemSize, int32_t ___1_count, int32_t ___2_alignment, const RuntimeMethod* method) 
{
	{
		FlatBufferBuilder_NotNested_m550AD28D180BF3E1C547BB535382BCE59B9BADB2(__this, NULL);
		int32_t L_0 = ___1_count;
		__this->____vectorNumElems = L_0;
		int32_t L_1 = ___0_elemSize;
		int32_t L_2 = ___1_count;
		FlatBufferBuilder_Prep_m3DA1C5F1967E6C9DE1F991519E1F87EE95C35AB7(__this, 4, ((int32_t)il2cpp_codegen_multiply(L_1, L_2)), NULL);
		int32_t L_3 = ___2_alignment;
		int32_t L_4 = ___0_elemSize;
		int32_t L_5 = ___1_count;
		FlatBufferBuilder_Prep_m3DA1C5F1967E6C9DE1F991519E1F87EE95C35AB7(__this, L_3, ((int32_t)il2cpp_codegen_multiply(L_4, L_5)), NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR VectorOffset_t25D49B46B3BAE5ABE18C23B5FF581A20699F96F4 FlatBufferBuilder_EndVector_mB83D1FC66B112D7A125563866327564C1DAF0DC7 (FlatBufferBuilder_t23EDF7F7B5CFBF2A0BBCC2CCE6D14C02BE050CB0* __this, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = __this->____vectorNumElems;
		FlatBufferBuilder_PutInt_m3657C36B7F37D77B13786E341A742332A942C07B(__this, L_0, NULL);
		int32_t L_1;
		L_1 = FlatBufferBuilder_get_Offset_mC74D55C982A3866A2171140BFFEF663306288AE9(__this, NULL);
		VectorOffset_t25D49B46B3BAE5ABE18C23B5FF581A20699F96F4 L_2;
		memset((&L_2), 0, sizeof(L_2));
		VectorOffset__ctor_mACFDC02BEA6B5322F5E78EA74CE311D3D313B736_inline((&L_2), L_1, NULL);
		return L_2;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FlatBufferBuilder_Nested_m45AE882A96E0B4309916DD3095168360CBFAE397 (FlatBufferBuilder_t23EDF7F7B5CFBF2A0BBCC2CCE6D14C02BE050CB0* __this, int32_t ___0_obj, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = ___0_obj;
		int32_t L_1;
		L_1 = FlatBufferBuilder_get_Offset_mC74D55C982A3866A2171140BFFEF663306288AE9(__this, NULL);
		if ((((int32_t)L_0) == ((int32_t)L_1)))
		{
			goto IL_0014;
		}
	}
	{
		Exception_t* L_2 = (Exception_t*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&Exception_t_il2cpp_TypeInfo_var)));
		Exception__ctor_m9B2BD92CD68916245A75109105D9071C9D430E7F(L_2, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteral4B33AF065A94EDF87433444D126A8EA5295CF031)), NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_2, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&FlatBufferBuilder_Nested_m45AE882A96E0B4309916DD3095168360CBFAE397_RuntimeMethod_var)));
	}

IL_0014:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FlatBufferBuilder_NotNested_m550AD28D180BF3E1C547BB535382BCE59B9BADB2 (FlatBufferBuilder_t23EDF7F7B5CFBF2A0BBCC2CCE6D14C02BE050CB0* __this, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = __this->____vtableSize;
		if ((((int32_t)L_0) < ((int32_t)0)))
		{
			goto IL_0014;
		}
	}
	{
		Exception_t* L_1 = (Exception_t*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&Exception_t_il2cpp_TypeInfo_var)));
		Exception__ctor_m9B2BD92CD68916245A75109105D9071C9D430E7F(L_1, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteral42AE8812EFD84589E3176177109FE0F328DC8CBB)), NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_1, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&FlatBufferBuilder_NotNested_m550AD28D180BF3E1C547BB535382BCE59B9BADB2_RuntimeMethod_var)));
	}

IL_0014:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FlatBufferBuilder_StartTable_m9EB86E88900E2C9419CFF54C05518FA2C89121ED (FlatBufferBuilder_t23EDF7F7B5CFBF2A0BBCC2CCE6D14C02BE050CB0* __this, int32_t ___0_numfields, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		int32_t L_0 = ___0_numfields;
		if ((((int32_t)L_0) >= ((int32_t)0)))
		{
			goto IL_000f;
		}
	}
	{
		ArgumentOutOfRangeException_tEA2822DAF62B10EEED00E0E3A341D4BAF78CF85F* L_1 = (ArgumentOutOfRangeException_tEA2822DAF62B10EEED00E0E3A341D4BAF78CF85F*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ArgumentOutOfRangeException_tEA2822DAF62B10EEED00E0E3A341D4BAF78CF85F_il2cpp_TypeInfo_var)));
		ArgumentOutOfRangeException__ctor_mBC1D5DEEA1BA41DE77228CB27D6BAFEB6DCCBF4A(L_1, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteralD1680709E54C5E6335DFDBFD1FE2B1A90F93EBBF)), NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_1, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&FlatBufferBuilder_StartTable_m9EB86E88900E2C9419CFF54C05518FA2C89121ED_RuntimeMethod_var)));
	}

IL_000f:
	{
		FlatBufferBuilder_NotNested_m550AD28D180BF3E1C547BB535382BCE59B9BADB2(__this, NULL);
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_2 = __this->____vtable;
		NullCheck(L_2);
		int32_t L_3 = ___0_numfields;
		if ((((int32_t)((int32_t)(((RuntimeArray*)L_2)->max_length))) >= ((int32_t)L_3)))
		{
			goto IL_002c;
		}
	}
	{
		int32_t L_4 = ___0_numfields;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_5 = (Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)(Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)SZArrayNew(Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C_il2cpp_TypeInfo_var, (uint32_t)L_4);
		__this->____vtable = L_5;
		Il2CppCodeGenWriteBarrier((void**)(&__this->____vtable), (void*)L_5);
	}

IL_002c:
	{
		int32_t L_6 = ___0_numfields;
		__this->____vtableSize = L_6;
		int32_t L_7;
		L_7 = FlatBufferBuilder_get_Offset_mC74D55C982A3866A2171140BFFEF663306288AE9(__this, NULL);
		__this->____objectStart = L_7;
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FlatBufferBuilder_Slot_mCF8F59D561C30C152A31A9754AA66D7A4E205B46 (FlatBufferBuilder_t23EDF7F7B5CFBF2A0BBCC2CCE6D14C02BE050CB0* __this, int32_t ___0_voffset, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = ___0_voffset;
		int32_t L_1 = __this->____vtableSize;
		if ((((int32_t)L_0) < ((int32_t)L_1)))
		{
			goto IL_0014;
		}
	}
	{
		IndexOutOfRangeException_t7ECB35264FB6CA8FAA516BD958F4B2ADC78E8A82* L_2 = (IndexOutOfRangeException_t7ECB35264FB6CA8FAA516BD958F4B2ADC78E8A82*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&IndexOutOfRangeException_t7ECB35264FB6CA8FAA516BD958F4B2ADC78E8A82_il2cpp_TypeInfo_var)));
		IndexOutOfRangeException__ctor_mFD06819F05B815BE2D6E826D4E04F4C449D0A425(L_2, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteral2BDD4D5F5701CF6858D53C77EF8AA4E2BB1DE24B)), NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_2, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&FlatBufferBuilder_Slot_mCF8F59D561C30C152A31A9754AA66D7A4E205B46_RuntimeMethod_var)));
	}

IL_0014:
	{
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_3 = __this->____vtable;
		int32_t L_4 = ___0_voffset;
		int32_t L_5;
		L_5 = FlatBufferBuilder_get_Offset_mC74D55C982A3866A2171140BFFEF663306288AE9(__this, NULL);
		NullCheck(L_3);
		(L_3)->SetAt(static_cast<il2cpp_array_size_t>(L_4), (int32_t)L_5);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FlatBufferBuilder_AddBool_m5D0F7FD6399AB4D0990CF2596D4FD15400DF61B6 (FlatBufferBuilder_t23EDF7F7B5CFBF2A0BBCC2CCE6D14C02BE050CB0* __this, int32_t ___0_o, bool ___1_x, bool ___2_d, const RuntimeMethod* method) 
{
	{
		bool L_0;
		L_0 = FlatBufferBuilder_get_ForceDefaults_mA0FE8A741C81C5945948228ED35D4F7E3BECC960_inline(__this, NULL);
		if (L_0)
		{
			goto IL_000c;
		}
	}
	{
		bool L_1 = ___1_x;
		bool L_2 = ___2_d;
		if ((((int32_t)L_1) == ((int32_t)L_2)))
		{
			goto IL_001a;
		}
	}

IL_000c:
	{
		bool L_3 = ___1_x;
		FlatBufferBuilder_AddBool_m478C50A00AA0FFFC9A0551F1B14688D00E654D67(__this, L_3, NULL);
		int32_t L_4 = ___0_o;
		FlatBufferBuilder_Slot_mCF8F59D561C30C152A31A9754AA66D7A4E205B46(__this, L_4, NULL);
	}

IL_001a:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FlatBufferBuilder_AddSbyte_mD18D3513DBC378A3887544D238D14BE9DC83732F (FlatBufferBuilder_t23EDF7F7B5CFBF2A0BBCC2CCE6D14C02BE050CB0* __this, int32_t ___0_o, int8_t ___1_x, int8_t ___2_d, const RuntimeMethod* method) 
{
	{
		bool L_0;
		L_0 = FlatBufferBuilder_get_ForceDefaults_mA0FE8A741C81C5945948228ED35D4F7E3BECC960_inline(__this, NULL);
		if (L_0)
		{
			goto IL_000c;
		}
	}
	{
		int8_t L_1 = ___1_x;
		int8_t L_2 = ___2_d;
		if ((((int32_t)L_1) == ((int32_t)L_2)))
		{
			goto IL_001a;
		}
	}

IL_000c:
	{
		int8_t L_3 = ___1_x;
		FlatBufferBuilder_AddSbyte_m8205DD96B17E57ECA93B00E0CF9941423E635C29(__this, L_3, NULL);
		int32_t L_4 = ___0_o;
		FlatBufferBuilder_Slot_mCF8F59D561C30C152A31A9754AA66D7A4E205B46(__this, L_4, NULL);
	}

IL_001a:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FlatBufferBuilder_AddByte_mA2FA2D184EF297FE203000FC60EEF2B055D26F0F (FlatBufferBuilder_t23EDF7F7B5CFBF2A0BBCC2CCE6D14C02BE050CB0* __this, int32_t ___0_o, uint8_t ___1_x, uint8_t ___2_d, const RuntimeMethod* method) 
{
	{
		bool L_0;
		L_0 = FlatBufferBuilder_get_ForceDefaults_mA0FE8A741C81C5945948228ED35D4F7E3BECC960_inline(__this, NULL);
		if (L_0)
		{
			goto IL_000c;
		}
	}
	{
		uint8_t L_1 = ___1_x;
		uint8_t L_2 = ___2_d;
		if ((((int32_t)L_1) == ((int32_t)L_2)))
		{
			goto IL_001a;
		}
	}

IL_000c:
	{
		uint8_t L_3 = ___1_x;
		FlatBufferBuilder_AddByte_m09B496156A95861FDF7841640C5C1719723A7036(__this, L_3, NULL);
		int32_t L_4 = ___0_o;
		FlatBufferBuilder_Slot_mCF8F59D561C30C152A31A9754AA66D7A4E205B46(__this, L_4, NULL);
	}

IL_001a:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FlatBufferBuilder_AddShort_m11792B784B3D896795B2E2FE4769379E915B9089 (FlatBufferBuilder_t23EDF7F7B5CFBF2A0BBCC2CCE6D14C02BE050CB0* __this, int32_t ___0_o, int16_t ___1_x, int32_t ___2_d, const RuntimeMethod* method) 
{
	{
		bool L_0;
		L_0 = FlatBufferBuilder_get_ForceDefaults_mA0FE8A741C81C5945948228ED35D4F7E3BECC960_inline(__this, NULL);
		if (L_0)
		{
			goto IL_000c;
		}
	}
	{
		int16_t L_1 = ___1_x;
		int32_t L_2 = ___2_d;
		if ((((int32_t)L_1) == ((int32_t)L_2)))
		{
			goto IL_001a;
		}
	}

IL_000c:
	{
		int16_t L_3 = ___1_x;
		FlatBufferBuilder_AddShort_mA240DA72E828B425EEF2128E7933F8E9696F5075(__this, L_3, NULL);
		int32_t L_4 = ___0_o;
		FlatBufferBuilder_Slot_mCF8F59D561C30C152A31A9754AA66D7A4E205B46(__this, L_4, NULL);
	}

IL_001a:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FlatBufferBuilder_AddUshort_m0654CFED47923CA687388B503D8D397DC650A142 (FlatBufferBuilder_t23EDF7F7B5CFBF2A0BBCC2CCE6D14C02BE050CB0* __this, int32_t ___0_o, uint16_t ___1_x, uint16_t ___2_d, const RuntimeMethod* method) 
{
	{
		bool L_0;
		L_0 = FlatBufferBuilder_get_ForceDefaults_mA0FE8A741C81C5945948228ED35D4F7E3BECC960_inline(__this, NULL);
		if (L_0)
		{
			goto IL_000c;
		}
	}
	{
		uint16_t L_1 = ___1_x;
		uint16_t L_2 = ___2_d;
		if ((((int32_t)L_1) == ((int32_t)L_2)))
		{
			goto IL_001a;
		}
	}

IL_000c:
	{
		uint16_t L_3 = ___1_x;
		FlatBufferBuilder_AddUshort_mFBED2384AD4CFA68C46F725B9342FA4F8AB02573(__this, L_3, NULL);
		int32_t L_4 = ___0_o;
		FlatBufferBuilder_Slot_mCF8F59D561C30C152A31A9754AA66D7A4E205B46(__this, L_4, NULL);
	}

IL_001a:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FlatBufferBuilder_AddInt_mCBF834EC2450E9210F91EAB87A2B825F09F08BED (FlatBufferBuilder_t23EDF7F7B5CFBF2A0BBCC2CCE6D14C02BE050CB0* __this, int32_t ___0_o, int32_t ___1_x, int32_t ___2_d, const RuntimeMethod* method) 
{
	{
		bool L_0;
		L_0 = FlatBufferBuilder_get_ForceDefaults_mA0FE8A741C81C5945948228ED35D4F7E3BECC960_inline(__this, NULL);
		if (L_0)
		{
			goto IL_000c;
		}
	}
	{
		int32_t L_1 = ___1_x;
		int32_t L_2 = ___2_d;
		if ((((int32_t)L_1) == ((int32_t)L_2)))
		{
			goto IL_001a;
		}
	}

IL_000c:
	{
		int32_t L_3 = ___1_x;
		FlatBufferBuilder_AddInt_mC7ED89CD9BAD7A1F34E445DDFF066513AA2B926E(__this, L_3, NULL);
		int32_t L_4 = ___0_o;
		FlatBufferBuilder_Slot_mCF8F59D561C30C152A31A9754AA66D7A4E205B46(__this, L_4, NULL);
	}

IL_001a:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FlatBufferBuilder_AddUint_m311E75686339879CD43C2AA8B764B92E198A98AE (FlatBufferBuilder_t23EDF7F7B5CFBF2A0BBCC2CCE6D14C02BE050CB0* __this, int32_t ___0_o, uint32_t ___1_x, uint32_t ___2_d, const RuntimeMethod* method) 
{
	{
		bool L_0;
		L_0 = FlatBufferBuilder_get_ForceDefaults_mA0FE8A741C81C5945948228ED35D4F7E3BECC960_inline(__this, NULL);
		if (L_0)
		{
			goto IL_000c;
		}
	}
	{
		uint32_t L_1 = ___1_x;
		uint32_t L_2 = ___2_d;
		if ((((int32_t)L_1) == ((int32_t)L_2)))
		{
			goto IL_001a;
		}
	}

IL_000c:
	{
		uint32_t L_3 = ___1_x;
		FlatBufferBuilder_AddUint_m5E52C37A1B3FF5FEF363B603C8E379847EBF0FF9(__this, L_3, NULL);
		int32_t L_4 = ___0_o;
		FlatBufferBuilder_Slot_mCF8F59D561C30C152A31A9754AA66D7A4E205B46(__this, L_4, NULL);
	}

IL_001a:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FlatBufferBuilder_AddLong_mD6BCBC1C0CEE0C60F862183C5E7B27936BFEC624 (FlatBufferBuilder_t23EDF7F7B5CFBF2A0BBCC2CCE6D14C02BE050CB0* __this, int32_t ___0_o, int64_t ___1_x, int64_t ___2_d, const RuntimeMethod* method) 
{
	{
		bool L_0;
		L_0 = FlatBufferBuilder_get_ForceDefaults_mA0FE8A741C81C5945948228ED35D4F7E3BECC960_inline(__this, NULL);
		if (L_0)
		{
			goto IL_000c;
		}
	}
	{
		int64_t L_1 = ___1_x;
		int64_t L_2 = ___2_d;
		if ((((int64_t)L_1) == ((int64_t)L_2)))
		{
			goto IL_001a;
		}
	}

IL_000c:
	{
		int64_t L_3 = ___1_x;
		FlatBufferBuilder_AddLong_m7941C58219E84656250A86B5B70D5E3731753DB5(__this, L_3, NULL);
		int32_t L_4 = ___0_o;
		FlatBufferBuilder_Slot_mCF8F59D561C30C152A31A9754AA66D7A4E205B46(__this, L_4, NULL);
	}

IL_001a:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FlatBufferBuilder_AddUlong_m755B6E69F62BC6D085B3C341B1A6044BC9E4265A (FlatBufferBuilder_t23EDF7F7B5CFBF2A0BBCC2CCE6D14C02BE050CB0* __this, int32_t ___0_o, uint64_t ___1_x, uint64_t ___2_d, const RuntimeMethod* method) 
{
	{
		bool L_0;
		L_0 = FlatBufferBuilder_get_ForceDefaults_mA0FE8A741C81C5945948228ED35D4F7E3BECC960_inline(__this, NULL);
		if (L_0)
		{
			goto IL_000c;
		}
	}
	{
		uint64_t L_1 = ___1_x;
		uint64_t L_2 = ___2_d;
		if ((((int64_t)L_1) == ((int64_t)L_2)))
		{
			goto IL_001a;
		}
	}

IL_000c:
	{
		uint64_t L_3 = ___1_x;
		FlatBufferBuilder_AddUlong_mD667451BC4F21D55EB19276E56C538BABCB0C9A3(__this, L_3, NULL);
		int32_t L_4 = ___0_o;
		FlatBufferBuilder_Slot_mCF8F59D561C30C152A31A9754AA66D7A4E205B46(__this, L_4, NULL);
	}

IL_001a:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FlatBufferBuilder_AddFloat_m47B8DC15B8788B1ADED0CD59E8DEA8C017ADCDEA (FlatBufferBuilder_t23EDF7F7B5CFBF2A0BBCC2CCE6D14C02BE050CB0* __this, int32_t ___0_o, float ___1_x, double ___2_d, const RuntimeMethod* method) 
{
	{
		bool L_0;
		L_0 = FlatBufferBuilder_get_ForceDefaults_mA0FE8A741C81C5945948228ED35D4F7E3BECC960_inline(__this, NULL);
		if (L_0)
		{
			goto IL_000d;
		}
	}
	{
		float L_1 = ___1_x;
		double L_2 = ___2_d;
		if ((((double)((double)L_1)) == ((double)L_2)))
		{
			goto IL_001b;
		}
	}

IL_000d:
	{
		float L_3 = ___1_x;
		FlatBufferBuilder_AddFloat_m631138283BEE1A25D70D62629445315F699D2C4D(__this, L_3, NULL);
		int32_t L_4 = ___0_o;
		FlatBufferBuilder_Slot_mCF8F59D561C30C152A31A9754AA66D7A4E205B46(__this, L_4, NULL);
	}

IL_001b:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FlatBufferBuilder_AddDouble_mD10E6A16BD9702A5F7FDA2228C3633A7B828A06B (FlatBufferBuilder_t23EDF7F7B5CFBF2A0BBCC2CCE6D14C02BE050CB0* __this, int32_t ___0_o, double ___1_x, double ___2_d, const RuntimeMethod* method) 
{
	{
		bool L_0;
		L_0 = FlatBufferBuilder_get_ForceDefaults_mA0FE8A741C81C5945948228ED35D4F7E3BECC960_inline(__this, NULL);
		if (L_0)
		{
			goto IL_000c;
		}
	}
	{
		double L_1 = ___1_x;
		double L_2 = ___2_d;
		if ((((double)L_1) == ((double)L_2)))
		{
			goto IL_001a;
		}
	}

IL_000c:
	{
		double L_3 = ___1_x;
		FlatBufferBuilder_AddDouble_mF109F698FFEF929DE54342B6A78D58F791DA7E5D(__this, L_3, NULL);
		int32_t L_4 = ___0_o;
		FlatBufferBuilder_Slot_mCF8F59D561C30C152A31A9754AA66D7A4E205B46(__this, L_4, NULL);
	}

IL_001a:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FlatBufferBuilder_AddOffset_mF017678579F65CB1B835E7A9451144D0C656086D (FlatBufferBuilder_t23EDF7F7B5CFBF2A0BBCC2CCE6D14C02BE050CB0* __this, int32_t ___0_o, int32_t ___1_x, int32_t ___2_d, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = ___1_x;
		int32_t L_1 = ___2_d;
		if ((((int32_t)L_0) == ((int32_t)L_1)))
		{
			goto IL_0012;
		}
	}
	{
		int32_t L_2 = ___1_x;
		FlatBufferBuilder_AddOffset_m7A64D677E7BE356B284C1DD84A7FD6E8C7DDCA27(__this, L_2, NULL);
		int32_t L_3 = ___0_o;
		FlatBufferBuilder_Slot_mCF8F59D561C30C152A31A9754AA66D7A4E205B46(__this, L_3, NULL);
	}

IL_0012:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR StringOffset_t5F8A2A2DB7065B675FCE7E3337C174214CC4FB4E FlatBufferBuilder_CreateString_m1A7788EF2664F6241243D3F1EB6973BBCC511F00 (FlatBufferBuilder_t23EDF7F7B5CFBF2A0BBCC2CCE6D14C02BE050CB0* __this, String_t* ___0_s, const RuntimeMethod* method) 
{
	int32_t V_0 = 0;
	int32_t V_1 = 0;
	{
		String_t* L_0 = ___0_s;
		if (L_0)
		{
			goto IL_000a;
		}
	}
	{
		StringOffset_t5F8A2A2DB7065B675FCE7E3337C174214CC4FB4E L_1;
		memset((&L_1), 0, sizeof(L_1));
		StringOffset__ctor_m52D945D231E3E3700A8C667AB97AE4D0A10A27BB_inline((&L_1), 0, NULL);
		return L_1;
	}

IL_000a:
	{
		FlatBufferBuilder_NotNested_m550AD28D180BF3E1C547BB535382BCE59B9BADB2(__this, NULL);
		FlatBufferBuilder_AddByte_m09B496156A95861FDF7841640C5C1719723A7036(__this, (uint8_t)0, NULL);
		Encoding_t65CDEF28CF20A7B8C92E85A4E808920C2465F095* L_2;
		L_2 = Encoding_get_UTF8_m9FA98A53CE96FD6D02982625C5246DD36C1235C9(NULL);
		String_t* L_3 = ___0_s;
		NullCheck(L_2);
		int32_t L_4;
		L_4 = VirtualFuncInvoker1< int32_t, String_t* >::Invoke(13, L_2, L_3);
		V_0 = L_4;
		int32_t L_5 = V_0;
		FlatBufferBuilder_StartVector_m6150568905670346B15955DDA02E5A09EB816E8D(__this, 1, L_5, 1, NULL);
		ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* L_6 = __this->____bb;
		int32_t L_7 = __this->____space;
		int32_t L_8 = V_0;
		int32_t L_9 = ((int32_t)il2cpp_codegen_subtract(L_7, L_8));
		V_1 = L_9;
		__this->____space = L_9;
		int32_t L_10 = V_1;
		String_t* L_11 = ___0_s;
		NullCheck(L_6);
		ByteBuffer_PutStringUTF8_m8A9FB9D67D35A58029657802ACA2C25A64BADAA3(L_6, L_10, L_11, NULL);
		VectorOffset_t25D49B46B3BAE5ABE18C23B5FF581A20699F96F4 L_12;
		L_12 = FlatBufferBuilder_EndVector_mB83D1FC66B112D7A125563866327564C1DAF0DC7(__this, NULL);
		int32_t L_13 = L_12.___Value;
		StringOffset_t5F8A2A2DB7065B675FCE7E3337C174214CC4FB4E L_14;
		memset((&L_14), 0, sizeof(L_14));
		StringOffset__ctor_m52D945D231E3E3700A8C667AB97AE4D0A10A27BB_inline((&L_14), L_13, NULL);
		return L_14;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FlatBufferBuilder_AddStruct_mCAD7F655DB90127B030826FF467F8A17678E4DDC (FlatBufferBuilder_t23EDF7F7B5CFBF2A0BBCC2CCE6D14C02BE050CB0* __this, int32_t ___0_voffset, int32_t ___1_x, int32_t ___2_d, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = ___1_x;
		int32_t L_1 = ___2_d;
		if ((((int32_t)L_0) == ((int32_t)L_1)))
		{
			goto IL_0012;
		}
	}
	{
		int32_t L_2 = ___1_x;
		FlatBufferBuilder_Nested_m45AE882A96E0B4309916DD3095168360CBFAE397(__this, L_2, NULL);
		int32_t L_3 = ___0_voffset;
		FlatBufferBuilder_Slot_mCF8F59D561C30C152A31A9754AA66D7A4E205B46(__this, L_3, NULL);
	}

IL_0012:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t FlatBufferBuilder_EndTable_m176D804DB1A019B3AF4735EBE7FECDD766E4CA73 (FlatBufferBuilder_t23EDF7F7B5CFBF2A0BBCC2CCE6D14C02BE050CB0* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	int32_t V_0 = 0;
	int32_t V_1 = 0;
	int32_t V_2 = 0;
	int32_t V_3 = 0;
	int16_t V_4 = 0;
	int32_t V_5 = 0;
	int32_t V_6 = 0;
	int16_t V_7 = 0;
	int32_t V_8 = 0;
	Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* V_9 = NULL;
	int32_t V_10 = 0;
	int32_t G_B10_0 = 0;
	{
		int32_t L_0 = __this->____vtableSize;
		if ((((int32_t)L_0) >= ((int32_t)0)))
		{
			goto IL_0014;
		}
	}
	{
		InvalidOperationException_t5DDE4D49B7405FAAB1E4576F4715A42A3FAD4BAB* L_1 = (InvalidOperationException_t5DDE4D49B7405FAAB1E4576F4715A42A3FAD4BAB*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&InvalidOperationException_t5DDE4D49B7405FAAB1E4576F4715A42A3FAD4BAB_il2cpp_TypeInfo_var)));
		InvalidOperationException__ctor_mE4CB6F4712AB6D99A2358FBAE2E052B3EE976162(L_1, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteral3228EDC2035E419A5666E21CC182B6179D1E2E00)), NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_1, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&FlatBufferBuilder_EndTable_m176D804DB1A019B3AF4735EBE7FECDD766E4CA73_RuntimeMethod_var)));
	}

IL_0014:
	{
		FlatBufferBuilder_AddInt_mC7ED89CD9BAD7A1F34E445DDFF066513AA2B926E(__this, 0, NULL);
		int32_t L_2;
		L_2 = FlatBufferBuilder_get_Offset_mC74D55C982A3866A2171140BFFEF663306288AE9(__this, NULL);
		V_0 = L_2;
		int32_t L_3 = __this->____vtableSize;
		V_1 = ((int32_t)il2cpp_codegen_subtract(L_3, 1));
		goto IL_0031;
	}

IL_002d:
	{
		int32_t L_4 = V_1;
		V_1 = ((int32_t)il2cpp_codegen_subtract(L_4, 1));
	}

IL_0031:
	{
		int32_t L_5 = V_1;
		if ((((int32_t)L_5) < ((int32_t)0)))
		{
			goto IL_003f;
		}
	}
	{
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_6 = __this->____vtable;
		int32_t L_7 = V_1;
		NullCheck(L_6);
		int32_t L_8 = L_7;
		int32_t L_9 = (L_6)->GetAt(static_cast<il2cpp_array_size_t>(L_8));
		if (!L_9)
		{
			goto IL_002d;
		}
	}

IL_003f:
	{
		int32_t L_10 = V_1;
		V_2 = ((int32_t)il2cpp_codegen_add(L_10, 1));
		goto IL_0074;
	}

IL_0045:
	{
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_11 = __this->____vtable;
		int32_t L_12 = V_1;
		NullCheck(L_11);
		int32_t L_13 = L_12;
		int32_t L_14 = (L_11)->GetAt(static_cast<il2cpp_array_size_t>(L_13));
		if (L_14)
		{
			goto IL_0052;
		}
	}
	{
		G_B10_0 = 0;
		goto IL_005c;
	}

IL_0052:
	{
		int32_t L_15 = V_0;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_16 = __this->____vtable;
		int32_t L_17 = V_1;
		NullCheck(L_16);
		int32_t L_18 = L_17;
		int32_t L_19 = (L_16)->GetAt(static_cast<il2cpp_array_size_t>(L_18));
		G_B10_0 = ((int32_t)il2cpp_codegen_subtract(L_15, L_19));
	}

IL_005c:
	{
		V_4 = ((int16_t)G_B10_0);
		int16_t L_20 = V_4;
		FlatBufferBuilder_AddShort_mA240DA72E828B425EEF2128E7933F8E9696F5075(__this, L_20, NULL);
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_21 = __this->____vtable;
		int32_t L_22 = V_1;
		NullCheck(L_21);
		(L_21)->SetAt(static_cast<il2cpp_array_size_t>(L_22), (int32_t)0);
		int32_t L_23 = V_1;
		V_1 = ((int32_t)il2cpp_codegen_subtract(L_23, 1));
	}

IL_0074:
	{
		int32_t L_24 = V_1;
		if ((((int32_t)L_24) >= ((int32_t)0)))
		{
			goto IL_0045;
		}
	}
	{
		int32_t L_25 = V_0;
		int32_t L_26 = __this->____objectStart;
		FlatBufferBuilder_AddShort_mA240DA72E828B425EEF2128E7933F8E9696F5075(__this, ((int16_t)((int32_t)il2cpp_codegen_subtract(L_25, L_26))), NULL);
		int32_t L_27 = V_2;
		FlatBufferBuilder_AddShort_mA240DA72E828B425EEF2128E7933F8E9696F5075(__this, ((int16_t)((int32_t)il2cpp_codegen_multiply(((int32_t)il2cpp_codegen_add(L_27, 2)), 2))), NULL);
		V_3 = 0;
		V_1 = 0;
		goto IL_011c;
	}

IL_009c:
	{
		ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* L_28 = __this->____bb;
		NullCheck(L_28);
		int32_t L_29;
		L_29 = ByteBuffer_get_Length_mD83481CCE00F09128DBAC206A094C628E635BA8B(L_28, NULL);
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_30 = __this->____vtables;
		int32_t L_31 = V_1;
		NullCheck(L_30);
		int32_t L_32 = L_31;
		int32_t L_33 = (L_30)->GetAt(static_cast<il2cpp_array_size_t>(L_32));
		V_5 = ((int32_t)il2cpp_codegen_subtract(L_29, L_33));
		int32_t L_34 = __this->____space;
		V_6 = L_34;
		ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* L_35 = __this->____bb;
		int32_t L_36 = V_5;
		NullCheck(L_35);
		int16_t L_37;
		L_37 = ByteBuffer_GetShort_m3ED68273C4B0ABA97718B68F439D042C758D084B(L_35, L_36, NULL);
		V_7 = L_37;
		int16_t L_38 = V_7;
		ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* L_39 = __this->____bb;
		int32_t L_40 = V_6;
		NullCheck(L_39);
		int16_t L_41;
		L_41 = ByteBuffer_GetShort_m3ED68273C4B0ABA97718B68F439D042C758D084B(L_39, L_40, NULL);
		if ((!(((uint32_t)L_38) == ((uint32_t)L_41))))
		{
			goto IL_0118;
		}
	}
	{
		V_8 = 2;
		goto IL_0107;
	}

IL_00df:
	{
		ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* L_42 = __this->____bb;
		int32_t L_43 = V_5;
		int32_t L_44 = V_8;
		NullCheck(L_42);
		int16_t L_45;
		L_45 = ByteBuffer_GetShort_m3ED68273C4B0ABA97718B68F439D042C758D084B(L_42, ((int32_t)il2cpp_codegen_add(L_43, L_44)), NULL);
		ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* L_46 = __this->____bb;
		int32_t L_47 = V_6;
		int32_t L_48 = V_8;
		NullCheck(L_46);
		int16_t L_49;
		L_49 = ByteBuffer_GetShort_m3ED68273C4B0ABA97718B68F439D042C758D084B(L_46, ((int32_t)il2cpp_codegen_add(L_47, L_48)), NULL);
		if ((!(((uint32_t)L_45) == ((uint32_t)L_49))))
		{
			goto IL_0118;
		}
	}
	{
		int32_t L_50 = V_8;
		V_8 = ((int32_t)il2cpp_codegen_add(L_50, 2));
	}

IL_0107:
	{
		int32_t L_51 = V_8;
		int16_t L_52 = V_7;
		if ((((int32_t)L_51) < ((int32_t)L_52)))
		{
			goto IL_00df;
		}
	}
	{
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_53 = __this->____vtables;
		int32_t L_54 = V_1;
		NullCheck(L_53);
		int32_t L_55 = L_54;
		int32_t L_56 = (L_53)->GetAt(static_cast<il2cpp_array_size_t>(L_55));
		V_3 = L_56;
		goto IL_0128;
	}

IL_0118:
	{
		int32_t L_57 = V_1;
		V_1 = ((int32_t)il2cpp_codegen_add(L_57, 1));
	}

IL_011c:
	{
		int32_t L_58 = V_1;
		int32_t L_59 = __this->____numVtables;
		if ((((int32_t)L_58) < ((int32_t)L_59)))
		{
			goto IL_009c;
		}
	}

IL_0128:
	{
		int32_t L_60 = V_3;
		if (!L_60)
		{
			goto IL_0154;
		}
	}
	{
		ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* L_61 = __this->____bb;
		NullCheck(L_61);
		int32_t L_62;
		L_62 = ByteBuffer_get_Length_mD83481CCE00F09128DBAC206A094C628E635BA8B(L_61, NULL);
		int32_t L_63 = V_0;
		__this->____space = ((int32_t)il2cpp_codegen_subtract(L_62, L_63));
		ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* L_64 = __this->____bb;
		int32_t L_65 = __this->____space;
		int32_t L_66 = V_3;
		int32_t L_67 = V_0;
		NullCheck(L_64);
		ByteBuffer_PutInt_mF305D6CF0623CB47E43C29A1CC08BFF7D3214C69(L_64, L_65, ((int32_t)il2cpp_codegen_subtract(L_66, L_67)), NULL);
		goto IL_01d1;
	}

IL_0154:
	{
		int32_t L_68 = __this->____numVtables;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_69 = __this->____vtables;
		NullCheck(L_69);
		if ((!(((uint32_t)L_68) == ((uint32_t)((int32_t)(((RuntimeArray*)L_69)->max_length))))))
		{
			goto IL_0190;
		}
	}
	{
		int32_t L_70 = __this->____numVtables;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_71 = (Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)(Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C*)SZArrayNew(Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C_il2cpp_TypeInfo_var, (uint32_t)((int32_t)il2cpp_codegen_multiply(L_70, 2)));
		V_9 = L_71;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_72 = __this->____vtables;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_73 = V_9;
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_74 = __this->____vtables;
		NullCheck(L_74);
		Array_Copy_m4233828B4E6288B6D815F539AAA38575DE627900((RuntimeArray*)L_72, (RuntimeArray*)L_73, ((int32_t)(((RuntimeArray*)L_74)->max_length)), NULL);
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_75 = V_9;
		__this->____vtables = L_75;
		Il2CppCodeGenWriteBarrier((void**)(&__this->____vtables), (void*)L_75);
	}

IL_0190:
	{
		Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* L_76 = __this->____vtables;
		int32_t L_77 = __this->____numVtables;
		V_10 = L_77;
		int32_t L_78 = V_10;
		__this->____numVtables = ((int32_t)il2cpp_codegen_add(L_78, 1));
		int32_t L_79 = V_10;
		int32_t L_80;
		L_80 = FlatBufferBuilder_get_Offset_mC74D55C982A3866A2171140BFFEF663306288AE9(__this, NULL);
		NullCheck(L_76);
		(L_76)->SetAt(static_cast<il2cpp_array_size_t>(L_79), (int32_t)L_80);
		ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* L_81 = __this->____bb;
		ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* L_82 = __this->____bb;
		NullCheck(L_82);
		int32_t L_83;
		L_83 = ByteBuffer_get_Length_mD83481CCE00F09128DBAC206A094C628E635BA8B(L_82, NULL);
		int32_t L_84 = V_0;
		int32_t L_85;
		L_85 = FlatBufferBuilder_get_Offset_mC74D55C982A3866A2171140BFFEF663306288AE9(__this, NULL);
		int32_t L_86 = V_0;
		NullCheck(L_81);
		ByteBuffer_PutInt_mF305D6CF0623CB47E43C29A1CC08BFF7D3214C69(L_81, ((int32_t)il2cpp_codegen_subtract(L_83, L_84)), ((int32_t)il2cpp_codegen_subtract(L_85, L_86)), NULL);
	}

IL_01d1:
	{
		__this->____vtableSize = (-1);
		int32_t L_87 = V_0;
		return L_87;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FlatBufferBuilder_Finish_m356784F295B0938131FC67FE20FA7D3A2E91D42E (FlatBufferBuilder_t23EDF7F7B5CFBF2A0BBCC2CCE6D14C02BE050CB0* __this, int32_t ___0_rootTable, bool ___1_sizePrefix, const RuntimeMethod* method) 
{
	int32_t G_B2_0 = 0;
	int32_t G_B2_1 = 0;
	FlatBufferBuilder_t23EDF7F7B5CFBF2A0BBCC2CCE6D14C02BE050CB0* G_B2_2 = NULL;
	int32_t G_B1_0 = 0;
	int32_t G_B1_1 = 0;
	FlatBufferBuilder_t23EDF7F7B5CFBF2A0BBCC2CCE6D14C02BE050CB0* G_B1_2 = NULL;
	int32_t G_B3_0 = 0;
	int32_t G_B3_1 = 0;
	int32_t G_B3_2 = 0;
	FlatBufferBuilder_t23EDF7F7B5CFBF2A0BBCC2CCE6D14C02BE050CB0* G_B3_3 = NULL;
	{
		int32_t L_0 = __this->____minAlign;
		bool L_1 = ___1_sizePrefix;
		if (L_1)
		{
			G_B2_0 = 4;
			G_B2_1 = L_0;
			G_B2_2 = __this;
			goto IL_000e;
		}
		G_B1_0 = 4;
		G_B1_1 = L_0;
		G_B1_2 = __this;
	}
	{
		G_B3_0 = 0;
		G_B3_1 = G_B1_0;
		G_B3_2 = G_B1_1;
		G_B3_3 = G_B1_2;
		goto IL_000f;
	}

IL_000e:
	{
		G_B3_0 = 4;
		G_B3_1 = G_B2_0;
		G_B3_2 = G_B2_1;
		G_B3_3 = G_B2_2;
	}

IL_000f:
	{
		NullCheck(G_B3_3);
		FlatBufferBuilder_Prep_m3DA1C5F1967E6C9DE1F991519E1F87EE95C35AB7(G_B3_3, G_B3_2, ((int32_t)il2cpp_codegen_add(G_B3_1, G_B3_0)), NULL);
		int32_t L_2 = ___0_rootTable;
		FlatBufferBuilder_AddOffset_m7A64D677E7BE356B284C1DD84A7FD6E8C7DDCA27(__this, L_2, NULL);
		bool L_3 = ___1_sizePrefix;
		if (!L_3)
		{
			goto IL_0037;
		}
	}
	{
		ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* L_4 = __this->____bb;
		NullCheck(L_4);
		int32_t L_5;
		L_5 = ByteBuffer_get_Length_mD83481CCE00F09128DBAC206A094C628E635BA8B(L_4, NULL);
		int32_t L_6 = __this->____space;
		FlatBufferBuilder_AddInt_mC7ED89CD9BAD7A1F34E445DDFF066513AA2B926E(__this, ((int32_t)il2cpp_codegen_subtract(L_5, L_6)), NULL);
	}

IL_0037:
	{
		ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* L_7 = __this->____bb;
		int32_t L_8 = __this->____space;
		NullCheck(L_7);
		ByteBuffer_set_Position_m0F1BC982C7D846D2F73A08D04F66D255746FCB20_inline(L_7, L_8, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FlatBufferBuilder_Finish_mAE4C42C9B9F79AF0106923368D952B1160A46F6D (FlatBufferBuilder_t23EDF7F7B5CFBF2A0BBCC2CCE6D14C02BE050CB0* __this, int32_t ___0_rootTable, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = ___0_rootTable;
		FlatBufferBuilder_Finish_m356784F295B0938131FC67FE20FA7D3A2E91D42E(__this, L_0, (bool)0, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FlatBufferBuilder_FinishSizePrefixed_m46F76A58F13D0AFA203806ED216CB8D23B7D8F72 (FlatBufferBuilder_t23EDF7F7B5CFBF2A0BBCC2CCE6D14C02BE050CB0* __this, int32_t ___0_rootTable, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = ___0_rootTable;
		FlatBufferBuilder_Finish_m356784F295B0938131FC67FE20FA7D3A2E91D42E(__this, L_0, (bool)1, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* FlatBufferBuilder_get_DataBuffer_mF063BD391805D53655476AB674FDFCD9880900D7 (FlatBufferBuilder_t23EDF7F7B5CFBF2A0BBCC2CCE6D14C02BE050CB0* __this, const RuntimeMethod* method) 
{
	{
		ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* L_0 = __this->____bb;
		return L_0;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FlatBufferConstants_FLATBUFFERS_23_5_26_m157E82FCD8C5BC7B96196D3EF099C0F9845203EC (const RuntimeMethod* method) 
{
	{
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FlatBuffersWarpObject__ctor_mB3653C6F6B9FE6D19F0373D08F7D25CF2AF69608 (FlatBuffersWarpObject_tB42739917C0D90B1960CCFA4D658EA84ED269C6F* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Options__ctor_m443C4957B9712416B855C1E610E23CCEC9756DB7 (Options_t2C469D7B05D4F50066C5316A778A04C4DF53F088* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		__this->___max_depth = ((int32_t)64);
		__this->___max_tables = ((int32_t)1000000);
		__this->___string_end_check = (bool)1;
		__this->___alignment_check = (bool)1;
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Options_get_maxDepth_m437B08A483A59E4600A3A2B6FE3A26E3EA8B2212 (Options_t2C469D7B05D4F50066C5316A778A04C4DF53F088* __this, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = __this->___max_depth;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Options_get_maxTables_mE2E55C408DAE7EB5059F6169F471ED14B93F2D40 (Options_t2C469D7B05D4F50066C5316A778A04C4DF53F088* __this, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = __this->___max_tables;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Options_get_stringEndCheck_mB9217751DB5C7D10C8EDB209FC945402821A1339 (Options_t2C469D7B05D4F50066C5316A778A04C4DF53F088* __this, const RuntimeMethod* method) 
{
	{
		bool L_0 = __this->___string_end_check;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Options_get_alignmentCheck_mC94E7A871FC44FE7505713CD0E69A9FE2BFD4EEA (Options_t2C469D7B05D4F50066C5316A778A04C4DF53F088* __this, const RuntimeMethod* method) 
{
	{
		bool L_0 = __this->___alignment_check;
		return L_0;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C void checkElementStruct_tCF75802323BACA21C0D2C66C2531CC19F664B49B_marshal_pinvoke(const checkElementStruct_tCF75802323BACA21C0D2C66C2531CC19F664B49B& unmarshaled, checkElementStruct_tCF75802323BACA21C0D2C66C2531CC19F664B49B_marshaled_pinvoke& marshaled)
{
	marshaled.___elementValid = static_cast<int32_t>(unmarshaled.___elementValid);
	marshaled.___elementOffset = unmarshaled.___elementOffset;
}
IL2CPP_EXTERN_C void checkElementStruct_tCF75802323BACA21C0D2C66C2531CC19F664B49B_marshal_pinvoke_back(const checkElementStruct_tCF75802323BACA21C0D2C66C2531CC19F664B49B_marshaled_pinvoke& marshaled, checkElementStruct_tCF75802323BACA21C0D2C66C2531CC19F664B49B& unmarshaled)
{
	bool unmarshaledelementValid_temp_0 = false;
	unmarshaledelementValid_temp_0 = static_cast<bool>(marshaled.___elementValid);
	unmarshaled.___elementValid = unmarshaledelementValid_temp_0;
	uint32_t unmarshaledelementOffset_temp_1 = 0;
	unmarshaledelementOffset_temp_1 = marshaled.___elementOffset;
	unmarshaled.___elementOffset = unmarshaledelementOffset_temp_1;
}
IL2CPP_EXTERN_C void checkElementStruct_tCF75802323BACA21C0D2C66C2531CC19F664B49B_marshal_pinvoke_cleanup(checkElementStruct_tCF75802323BACA21C0D2C66C2531CC19F664B49B_marshaled_pinvoke& marshaled)
{
}
IL2CPP_EXTERN_C void checkElementStruct_tCF75802323BACA21C0D2C66C2531CC19F664B49B_marshal_com(const checkElementStruct_tCF75802323BACA21C0D2C66C2531CC19F664B49B& unmarshaled, checkElementStruct_tCF75802323BACA21C0D2C66C2531CC19F664B49B_marshaled_com& marshaled)
{
	marshaled.___elementValid = static_cast<int32_t>(unmarshaled.___elementValid);
	marshaled.___elementOffset = unmarshaled.___elementOffset;
}
IL2CPP_EXTERN_C void checkElementStruct_tCF75802323BACA21C0D2C66C2531CC19F664B49B_marshal_com_back(const checkElementStruct_tCF75802323BACA21C0D2C66C2531CC19F664B49B_marshaled_com& marshaled, checkElementStruct_tCF75802323BACA21C0D2C66C2531CC19F664B49B& unmarshaled)
{
	bool unmarshaledelementValid_temp_0 = false;
	unmarshaledelementValid_temp_0 = static_cast<bool>(marshaled.___elementValid);
	unmarshaled.___elementValid = unmarshaledelementValid_temp_0;
	uint32_t unmarshaledelementOffset_temp_1 = 0;
	unmarshaledelementOffset_temp_1 = marshaled.___elementOffset;
	unmarshaled.___elementOffset = unmarshaledelementOffset_temp_1;
}
IL2CPP_EXTERN_C void checkElementStruct_tCF75802323BACA21C0D2C66C2531CC19F664B49B_marshal_com_cleanup(checkElementStruct_tCF75802323BACA21C0D2C66C2531CC19F664B49B_marshaled_com& marshaled)
{
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
bool VerifyTableAction_Invoke_mDD4DF94C57F6A57A87F19A8915ACD085F65FF6D4_Multicast(VerifyTableAction_t4EE7F0547499A39B8176F648F806406AB26487AB* __this, Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864* ___0_verifier, uint32_t ___1_tablePos, const RuntimeMethod* method)
{
	il2cpp_array_size_t length = __this->___delegates->max_length;
	Delegate_t** delegatesToInvoke = reinterpret_cast<Delegate_t**>(__this->___delegates->GetAddressAtUnchecked(0));
	bool retVal = false;
	for (il2cpp_array_size_t i = 0; i < length; i++)
	{
		VerifyTableAction_t4EE7F0547499A39B8176F648F806406AB26487AB* currentDelegate = reinterpret_cast<VerifyTableAction_t4EE7F0547499A39B8176F648F806406AB26487AB*>(delegatesToInvoke[i]);
		typedef bool (*FunctionPointerType) (RuntimeObject*, Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864*, uint32_t, const RuntimeMethod*);
		retVal = ((FunctionPointerType)currentDelegate->___invoke_impl)((Il2CppObject*)currentDelegate->___method_code, ___0_verifier, ___1_tablePos, reinterpret_cast<RuntimeMethod*>(currentDelegate->___method));
	}
	return retVal;
}
bool VerifyTableAction_Invoke_mDD4DF94C57F6A57A87F19A8915ACD085F65FF6D4_OpenInst(VerifyTableAction_t4EE7F0547499A39B8176F648F806406AB26487AB* __this, Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864* ___0_verifier, uint32_t ___1_tablePos, const RuntimeMethod* method)
{
	NullCheck(___0_verifier);
	typedef bool (*FunctionPointerType) (Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864*, uint32_t, const RuntimeMethod*);
	return ((FunctionPointerType)__this->___method_ptr)(___0_verifier, ___1_tablePos, method);
}
bool VerifyTableAction_Invoke_mDD4DF94C57F6A57A87F19A8915ACD085F65FF6D4_OpenStatic(VerifyTableAction_t4EE7F0547499A39B8176F648F806406AB26487AB* __this, Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864* ___0_verifier, uint32_t ___1_tablePos, const RuntimeMethod* method)
{
	typedef bool (*FunctionPointerType) (Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864*, uint32_t, const RuntimeMethod*);
	return ((FunctionPointerType)__this->___method_ptr)(___0_verifier, ___1_tablePos, method);
}
bool VerifyTableAction_Invoke_mDD4DF94C57F6A57A87F19A8915ACD085F65FF6D4_OpenVirtual(VerifyTableAction_t4EE7F0547499A39B8176F648F806406AB26487AB* __this, Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864* ___0_verifier, uint32_t ___1_tablePos, const RuntimeMethod* method)
{
	NullCheck(___0_verifier);
	return VirtualFuncInvoker1< bool, uint32_t >::Invoke(il2cpp_codegen_method_get_slot(method), ___0_verifier, ___1_tablePos);
}
bool VerifyTableAction_Invoke_mDD4DF94C57F6A57A87F19A8915ACD085F65FF6D4_OpenInterface(VerifyTableAction_t4EE7F0547499A39B8176F648F806406AB26487AB* __this, Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864* ___0_verifier, uint32_t ___1_tablePos, const RuntimeMethod* method)
{
	NullCheck(___0_verifier);
	return InterfaceFuncInvoker1< bool, uint32_t >::Invoke(il2cpp_codegen_method_get_slot(method), il2cpp_codegen_method_get_declaring_type(method), ___0_verifier, ___1_tablePos);
}
bool VerifyTableAction_Invoke_mDD4DF94C57F6A57A87F19A8915ACD085F65FF6D4_OpenGenericVirtual(VerifyTableAction_t4EE7F0547499A39B8176F648F806406AB26487AB* __this, Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864* ___0_verifier, uint32_t ___1_tablePos, const RuntimeMethod* method)
{
	NullCheck(___0_verifier);
	return GenericVirtualFuncInvoker1< bool, uint32_t >::Invoke(method, ___0_verifier, ___1_tablePos);
}
bool VerifyTableAction_Invoke_mDD4DF94C57F6A57A87F19A8915ACD085F65FF6D4_OpenGenericInterface(VerifyTableAction_t4EE7F0547499A39B8176F648F806406AB26487AB* __this, Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864* ___0_verifier, uint32_t ___1_tablePos, const RuntimeMethod* method)
{
	NullCheck(___0_verifier);
	return GenericInterfaceFuncInvoker1< bool, uint32_t >::Invoke(method, ___0_verifier, ___1_tablePos);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VerifyTableAction__ctor_m7AEEAD7ED5C1ABF23BE481BA49D8BA704E421456 (VerifyTableAction_t4EE7F0547499A39B8176F648F806406AB26487AB* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method) 
{
	__this->___method_ptr = (intptr_t)il2cpp_codegen_get_method_pointer((RuntimeMethod*)___1_method);
	__this->___method = ___1_method;
	__this->___m_target = ___0_object;
	Il2CppCodeGenWriteBarrier((void**)(&__this->___m_target), (void*)___0_object);
	int parameterCount = il2cpp_codegen_method_parameter_count((RuntimeMethod*)___1_method);
	__this->___method_code = (intptr_t)__this;
	if (MethodIsStatic((RuntimeMethod*)___1_method))
	{
		bool isOpen = parameterCount == 2;
		if (isOpen)
			__this->___invoke_impl = (intptr_t)&VerifyTableAction_Invoke_mDD4DF94C57F6A57A87F19A8915ACD085F65FF6D4_OpenStatic;
		else
			{
				__this->___invoke_impl = __this->___method_ptr;
				__this->___method_code = (intptr_t)__this->___m_target;
			}
	}
	else
	{
		bool isOpen = parameterCount == 1;
		if (isOpen)
		{
			if (__this->___method_is_virtual)
			{
				if (il2cpp_codegen_method_is_generic_instance_method((RuntimeMethod*)___1_method))
					if (il2cpp_codegen_method_is_interface_method((RuntimeMethod*)___1_method))
						__this->___invoke_impl = (intptr_t)&VerifyTableAction_Invoke_mDD4DF94C57F6A57A87F19A8915ACD085F65FF6D4_OpenGenericInterface;
					else
						__this->___invoke_impl = (intptr_t)&VerifyTableAction_Invoke_mDD4DF94C57F6A57A87F19A8915ACD085F65FF6D4_OpenGenericVirtual;
				else
					if (il2cpp_codegen_method_is_interface_method((RuntimeMethod*)___1_method))
						__this->___invoke_impl = (intptr_t)&VerifyTableAction_Invoke_mDD4DF94C57F6A57A87F19A8915ACD085F65FF6D4_OpenInterface;
					else
						__this->___invoke_impl = (intptr_t)&VerifyTableAction_Invoke_mDD4DF94C57F6A57A87F19A8915ACD085F65FF6D4_OpenVirtual;
			}
			else
			{
				__this->___invoke_impl = (intptr_t)&VerifyTableAction_Invoke_mDD4DF94C57F6A57A87F19A8915ACD085F65FF6D4_OpenInst;
			}
		}
		else
		{
			if (___0_object == NULL)
				il2cpp_codegen_raise_exception(il2cpp_codegen_get_argument_exception(NULL, "Delegate to an instance method cannot have null 'this'."), NULL);
			__this->___invoke_impl = __this->___method_ptr;
			__this->___method_code = (intptr_t)__this->___m_target;
		}
	}
	__this->___extra_arg = (intptr_t)&VerifyTableAction_Invoke_mDD4DF94C57F6A57A87F19A8915ACD085F65FF6D4_Multicast;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool VerifyTableAction_Invoke_mDD4DF94C57F6A57A87F19A8915ACD085F65FF6D4 (VerifyTableAction_t4EE7F0547499A39B8176F648F806406AB26487AB* __this, Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864* ___0_verifier, uint32_t ___1_tablePos, const RuntimeMethod* method) 
{
	typedef bool (*FunctionPointerType) (RuntimeObject*, Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864*, uint32_t, const RuntimeMethod*);
	return ((FunctionPointerType)__this->___invoke_impl)((Il2CppObject*)__this->___method_code, ___0_verifier, ___1_tablePos, reinterpret_cast<RuntimeMethod*>(__this->___method));
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
bool VerifyUnionAction_Invoke_mF8A06007D2423423954038A65C411972E1DF6B3B_Multicast(VerifyUnionAction_t41231FB98151184E37EF5626A85224925AC03F23* __this, Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864* ___0_verifier, uint8_t ___1_typeId, uint32_t ___2_tablePos, const RuntimeMethod* method)
{
	il2cpp_array_size_t length = __this->___delegates->max_length;
	Delegate_t** delegatesToInvoke = reinterpret_cast<Delegate_t**>(__this->___delegates->GetAddressAtUnchecked(0));
	bool retVal = false;
	for (il2cpp_array_size_t i = 0; i < length; i++)
	{
		VerifyUnionAction_t41231FB98151184E37EF5626A85224925AC03F23* currentDelegate = reinterpret_cast<VerifyUnionAction_t41231FB98151184E37EF5626A85224925AC03F23*>(delegatesToInvoke[i]);
		typedef bool (*FunctionPointerType) (RuntimeObject*, Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864*, uint8_t, uint32_t, const RuntimeMethod*);
		retVal = ((FunctionPointerType)currentDelegate->___invoke_impl)((Il2CppObject*)currentDelegate->___method_code, ___0_verifier, ___1_typeId, ___2_tablePos, reinterpret_cast<RuntimeMethod*>(currentDelegate->___method));
	}
	return retVal;
}
bool VerifyUnionAction_Invoke_mF8A06007D2423423954038A65C411972E1DF6B3B_OpenInst(VerifyUnionAction_t41231FB98151184E37EF5626A85224925AC03F23* __this, Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864* ___0_verifier, uint8_t ___1_typeId, uint32_t ___2_tablePos, const RuntimeMethod* method)
{
	NullCheck(___0_verifier);
	typedef bool (*FunctionPointerType) (Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864*, uint8_t, uint32_t, const RuntimeMethod*);
	return ((FunctionPointerType)__this->___method_ptr)(___0_verifier, ___1_typeId, ___2_tablePos, method);
}
bool VerifyUnionAction_Invoke_mF8A06007D2423423954038A65C411972E1DF6B3B_OpenStatic(VerifyUnionAction_t41231FB98151184E37EF5626A85224925AC03F23* __this, Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864* ___0_verifier, uint8_t ___1_typeId, uint32_t ___2_tablePos, const RuntimeMethod* method)
{
	typedef bool (*FunctionPointerType) (Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864*, uint8_t, uint32_t, const RuntimeMethod*);
	return ((FunctionPointerType)__this->___method_ptr)(___0_verifier, ___1_typeId, ___2_tablePos, method);
}
bool VerifyUnionAction_Invoke_mF8A06007D2423423954038A65C411972E1DF6B3B_OpenVirtual(VerifyUnionAction_t41231FB98151184E37EF5626A85224925AC03F23* __this, Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864* ___0_verifier, uint8_t ___1_typeId, uint32_t ___2_tablePos, const RuntimeMethod* method)
{
	NullCheck(___0_verifier);
	return VirtualFuncInvoker2< bool, uint8_t, uint32_t >::Invoke(il2cpp_codegen_method_get_slot(method), ___0_verifier, ___1_typeId, ___2_tablePos);
}
bool VerifyUnionAction_Invoke_mF8A06007D2423423954038A65C411972E1DF6B3B_OpenInterface(VerifyUnionAction_t41231FB98151184E37EF5626A85224925AC03F23* __this, Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864* ___0_verifier, uint8_t ___1_typeId, uint32_t ___2_tablePos, const RuntimeMethod* method)
{
	NullCheck(___0_verifier);
	return InterfaceFuncInvoker2< bool, uint8_t, uint32_t >::Invoke(il2cpp_codegen_method_get_slot(method), il2cpp_codegen_method_get_declaring_type(method), ___0_verifier, ___1_typeId, ___2_tablePos);
}
bool VerifyUnionAction_Invoke_mF8A06007D2423423954038A65C411972E1DF6B3B_OpenGenericVirtual(VerifyUnionAction_t41231FB98151184E37EF5626A85224925AC03F23* __this, Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864* ___0_verifier, uint8_t ___1_typeId, uint32_t ___2_tablePos, const RuntimeMethod* method)
{
	NullCheck(___0_verifier);
	return GenericVirtualFuncInvoker2< bool, uint8_t, uint32_t >::Invoke(method, ___0_verifier, ___1_typeId, ___2_tablePos);
}
bool VerifyUnionAction_Invoke_mF8A06007D2423423954038A65C411972E1DF6B3B_OpenGenericInterface(VerifyUnionAction_t41231FB98151184E37EF5626A85224925AC03F23* __this, Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864* ___0_verifier, uint8_t ___1_typeId, uint32_t ___2_tablePos, const RuntimeMethod* method)
{
	NullCheck(___0_verifier);
	return GenericInterfaceFuncInvoker2< bool, uint8_t, uint32_t >::Invoke(method, ___0_verifier, ___1_typeId, ___2_tablePos);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VerifyUnionAction__ctor_m8FE595B1CA4BF89B3520BBD1554A4FCF98D8254B (VerifyUnionAction_t41231FB98151184E37EF5626A85224925AC03F23* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method) 
{
	__this->___method_ptr = (intptr_t)il2cpp_codegen_get_method_pointer((RuntimeMethod*)___1_method);
	__this->___method = ___1_method;
	__this->___m_target = ___0_object;
	Il2CppCodeGenWriteBarrier((void**)(&__this->___m_target), (void*)___0_object);
	int parameterCount = il2cpp_codegen_method_parameter_count((RuntimeMethod*)___1_method);
	__this->___method_code = (intptr_t)__this;
	if (MethodIsStatic((RuntimeMethod*)___1_method))
	{
		bool isOpen = parameterCount == 3;
		if (isOpen)
			__this->___invoke_impl = (intptr_t)&VerifyUnionAction_Invoke_mF8A06007D2423423954038A65C411972E1DF6B3B_OpenStatic;
		else
			{
				__this->___invoke_impl = __this->___method_ptr;
				__this->___method_code = (intptr_t)__this->___m_target;
			}
	}
	else
	{
		bool isOpen = parameterCount == 2;
		if (isOpen)
		{
			if (__this->___method_is_virtual)
			{
				if (il2cpp_codegen_method_is_generic_instance_method((RuntimeMethod*)___1_method))
					if (il2cpp_codegen_method_is_interface_method((RuntimeMethod*)___1_method))
						__this->___invoke_impl = (intptr_t)&VerifyUnionAction_Invoke_mF8A06007D2423423954038A65C411972E1DF6B3B_OpenGenericInterface;
					else
						__this->___invoke_impl = (intptr_t)&VerifyUnionAction_Invoke_mF8A06007D2423423954038A65C411972E1DF6B3B_OpenGenericVirtual;
				else
					if (il2cpp_codegen_method_is_interface_method((RuntimeMethod*)___1_method))
						__this->___invoke_impl = (intptr_t)&VerifyUnionAction_Invoke_mF8A06007D2423423954038A65C411972E1DF6B3B_OpenInterface;
					else
						__this->___invoke_impl = (intptr_t)&VerifyUnionAction_Invoke_mF8A06007D2423423954038A65C411972E1DF6B3B_OpenVirtual;
			}
			else
			{
				__this->___invoke_impl = (intptr_t)&VerifyUnionAction_Invoke_mF8A06007D2423423954038A65C411972E1DF6B3B_OpenInst;
			}
		}
		else
		{
			if (___0_object == NULL)
				il2cpp_codegen_raise_exception(il2cpp_codegen_get_argument_exception(NULL, "Delegate to an instance method cannot have null 'this'."), NULL);
			__this->___invoke_impl = __this->___method_ptr;
			__this->___method_code = (intptr_t)__this->___m_target;
		}
	}
	__this->___extra_arg = (intptr_t)&VerifyUnionAction_Invoke_mF8A06007D2423423954038A65C411972E1DF6B3B_Multicast;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool VerifyUnionAction_Invoke_mF8A06007D2423423954038A65C411972E1DF6B3B (VerifyUnionAction_t41231FB98151184E37EF5626A85224925AC03F23* __this, Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864* ___0_verifier, uint8_t ___1_typeId, uint32_t ___2_tablePos, const RuntimeMethod* method) 
{
	typedef bool (*FunctionPointerType) (RuntimeObject*, Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864*, uint8_t, uint32_t, const RuntimeMethod*);
	return ((FunctionPointerType)__this->___invoke_impl)((Il2CppObject*)__this->___method_code, ___0_verifier, ___1_typeId, ___2_tablePos, reinterpret_cast<RuntimeMethod*>(__this->___method));
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Verifier__ctor_mFF28952281B211ADE655D82536791DEFC321B649 (Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864* __this, ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* ___0_buf, Options_t2C469D7B05D4F50066C5316A778A04C4DF53F088* ___1_options, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Options_t2C469D7B05D4F50066C5316A778A04C4DF53F088_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Options_t2C469D7B05D4F50066C5316A778A04C4DF53F088* G_B2_0 = NULL;
	Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864* G_B2_1 = NULL;
	Options_t2C469D7B05D4F50066C5316A778A04C4DF53F088* G_B1_0 = NULL;
	Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864* G_B1_1 = NULL;
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* L_0 = ___0_buf;
		__this->___verifier_buffer = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___verifier_buffer), (void*)L_0);
		Options_t2C469D7B05D4F50066C5316A778A04C4DF53F088* L_1 = ___1_options;
		Options_t2C469D7B05D4F50066C5316A778A04C4DF53F088* L_2 = L_1;
		if (L_2)
		{
			G_B2_0 = L_2;
			G_B2_1 = __this;
			goto IL_0018;
		}
		G_B1_0 = L_2;
		G_B1_1 = __this;
	}
	{
		Options_t2C469D7B05D4F50066C5316A778A04C4DF53F088* L_3 = (Options_t2C469D7B05D4F50066C5316A778A04C4DF53F088*)il2cpp_codegen_object_new(Options_t2C469D7B05D4F50066C5316A778A04C4DF53F088_il2cpp_TypeInfo_var);
		Options__ctor_m443C4957B9712416B855C1E610E23CCEC9756DB7(L_3, NULL);
		G_B2_0 = L_3;
		G_B2_1 = G_B1_1;
	}

IL_0018:
	{
		NullCheck(G_B2_1);
		G_B2_1->___verifier_options = G_B2_0;
		Il2CppCodeGenWriteBarrier((void**)(&G_B2_1->___verifier_options), (void*)G_B2_0);
		__this->___depth_cnt = 0;
		__this->___num_tables_cnt = 0;
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Options_t2C469D7B05D4F50066C5316A778A04C4DF53F088* Verifier_get_options_m5D0BEA9EBE4645B980E3066F2E42B042943D87E9 (Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864* __this, const RuntimeMethod* method) 
{
	{
		Options_t2C469D7B05D4F50066C5316A778A04C4DF53F088* L_0 = __this->___verifier_options;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Verifier_get_depth_m4A917A65B04FAC0E7374D664A28B7BFC089AA74E (Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864* __this, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = __this->___depth_cnt;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Verifier_set_depth_m996F6606AC8B244E1D3AB94C5C32C1934B47A7C7 (Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864* __this, int32_t ___0_value, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = ___0_value;
		__this->___depth_cnt = L_0;
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Verifier_get_numTables_mD6123A14045007BAE82B1770EB9201594F778973 (Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864* __this, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = __this->___num_tables_cnt;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Verifier_set_numTables_mFD577C7B868B6ACE51DCAFDBE879183BBE9B08B4 (Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864* __this, int32_t ___0_value, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = ___0_value;
		__this->___num_tables_cnt = L_0;
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Verifier_BufferHasIdentifier_m47A5B9E1944079E3B21B67886594DD78A79F9F76 (Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864* __this, ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* ___0_buf, uint32_t ___1_startPos, String_t* ___2_identifier, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Convert_t7097FF336D592F7C06D88A98349A44646F91EFFC_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	int32_t V_0 = 0;
	{
		String_t* L_0 = ___2_identifier;
		NullCheck(L_0);
		int32_t L_1;
		L_1 = String_get_Length_m42625D67623FA5CC7A44D47425CE86FB946542D2_inline(L_0, NULL);
		if ((((int32_t)L_1) == ((int32_t)4)))
		{
			goto IL_001f;
		}
	}
	{
		il2cpp_codegen_runtime_class_init_inline(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&Convert_t7097FF336D592F7C06D88A98349A44646F91EFFC_il2cpp_TypeInfo_var)));
		String_t* L_2;
		L_2 = Convert_ToString_m18866C40B9AB691A8DEF943323636CA70374D419(4, NULL);
		String_t* L_3;
		L_3 = String_Concat_m9E3155FB84015C823606188F53B47CB44C444991(((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteral78DEF0AE63CBB76D037109AC2BDC00D5F2AEEBD5)), L_2, NULL);
		ArgumentException_tAD90411542A20A9C72D5CDA3A84181D8B947A263* L_4 = (ArgumentException_tAD90411542A20A9C72D5CDA3A84181D8B947A263*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ArgumentException_tAD90411542A20A9C72D5CDA3A84181D8B947A263_il2cpp_TypeInfo_var)));
		ArgumentException__ctor_m026938A67AF9D36BB7ED27F80425D7194B514465(L_4, L_3, NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_4, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&Verifier_BufferHasIdentifier_m47A5B9E1944079E3B21B67886594DD78A79F9F76_RuntimeMethod_var)));
	}

IL_001f:
	{
		V_0 = 0;
		goto IL_004a;
	}

IL_0023:
	{
		String_t* L_5 = ___2_identifier;
		int32_t L_6 = V_0;
		NullCheck(L_5);
		Il2CppChar L_7;
		L_7 = String_get_Chars_mC49DF0CD2D3BE7BE97B3AD9C995BE3094F8E36D3(L_5, L_6, NULL);
		ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* L_8 = __this->___verifier_buffer;
		int32_t L_9 = V_0;
		uint32_t L_10 = ___1_startPos;
		il2cpp_codegen_runtime_class_init_inline(Convert_t7097FF336D592F7C06D88A98349A44646F91EFFC_il2cpp_TypeInfo_var);
		int32_t L_11;
		L_11 = Convert_ToInt32_mA857F99F1CACB73D7DB85E26638E7CC1A2CD5C78(((int64_t)il2cpp_codegen_add(((int64_t)((int32_t)il2cpp_codegen_add(4, L_9))), ((int64_t)(uint64_t)L_10))), NULL);
		NullCheck(L_8);
		int8_t L_12;
		L_12 = ByteBuffer_GetSbyte_mC5F6FC81C592D6FDFDF41EE3EA719EEA2234295B(L_8, L_11, NULL);
		if ((((int32_t)((int8_t)L_7)) == ((int32_t)L_12)))
		{
			goto IL_0046;
		}
	}
	{
		return (bool)0;
	}

IL_0046:
	{
		int32_t L_13 = V_0;
		V_0 = ((int32_t)il2cpp_codegen_add(L_13, 1));
	}

IL_004a:
	{
		int32_t L_14 = V_0;
		if ((((int32_t)L_14) < ((int32_t)4)))
		{
			goto IL_0023;
		}
	}
	{
		return (bool)1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint32_t Verifier_ReadUOffsetT_mFA082679C617C5C8424CDDFE3965438AB7DE85B1 (Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864* __this, ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* ___0_buf, uint32_t ___1_pos, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Convert_t7097FF336D592F7C06D88A98349A44646F91EFFC_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* L_0 = ___0_buf;
		uint32_t L_1 = ___1_pos;
		il2cpp_codegen_runtime_class_init_inline(Convert_t7097FF336D592F7C06D88A98349A44646F91EFFC_il2cpp_TypeInfo_var);
		int32_t L_2;
		L_2 = Convert_ToInt32_m5ADD7A6890AE40D05444F58D72FDEC7252D6D7F2(L_1, NULL);
		NullCheck(L_0);
		uint32_t L_3;
		L_3 = ByteBuffer_GetUint_m7686A15FBE8934D1AE054CDE1D18F43BA59EBE6D(L_0, L_2, NULL);
		return L_3;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Verifier_ReadSOffsetT_m5A43060F831A70DD3ECDFA632C079B51B4C15EBE (Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864* __this, ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* ___0_buf, int32_t ___1_pos, const RuntimeMethod* method) 
{
	{
		ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* L_0 = ___0_buf;
		int32_t L_1 = ___1_pos;
		NullCheck(L_0);
		int32_t L_2;
		L_2 = ByteBuffer_GetInt_mD2F94B1EFC4E1B48E6A25E9FE27A36CB691F93C7(L_0, L_1, NULL);
		return L_2;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int16_t Verifier_ReadVOffsetT_m3F7EA721035EA1275A5536FA2965DC30FB246B0F (Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864* __this, ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* ___0_buf, int32_t ___1_pos, const RuntimeMethod* method) 
{
	{
		ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* L_0 = ___0_buf;
		int32_t L_1 = ___1_pos;
		NullCheck(L_0);
		int16_t L_2;
		L_2 = ByteBuffer_GetShort_m3ED68273C4B0ABA97718B68F439D042C758D084B(L_0, L_1, NULL);
		return L_2;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int16_t Verifier_GetVRelOffset_mF26D54B2378F87DEA0C42285F40ECB98B05D2F14 (Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864* __this, int32_t ___0_pos, int16_t ___1_vtableOffset, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Convert_t7097FF336D592F7C06D88A98349A44646F91EFFC_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	int16_t V_0 = 0;
	int16_t V_1 = 0;
	Exception_t* V_2 = NULL;
	int16_t V_3 = 0;
	il2cpp::utils::ExceptionSupportStack<RuntimeObject*, 1> __active_exceptions;
	{
		V_0 = (int16_t)0;
	}
	try
	{
		{
			int32_t L_0 = ___0_pos;
			ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* L_1 = __this->___verifier_buffer;
			int32_t L_2 = ___0_pos;
			int32_t L_3;
			L_3 = Verifier_ReadSOffsetT_m5A43060F831A70DD3ECDFA632C079B51B4C15EBE(__this, L_1, L_2, NULL);
			il2cpp_codegen_runtime_class_init_inline(Convert_t7097FF336D592F7C06D88A98349A44646F91EFFC_il2cpp_TypeInfo_var);
			int16_t L_4;
			L_4 = Convert_ToInt16_mF65D8227B8B6F0E30A135BC5F01F1562455AD382(((int32_t)il2cpp_codegen_subtract(L_0, L_3)), NULL);
			V_1 = L_4;
			int16_t L_5 = ___1_vtableOffset;
			ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* L_6 = __this->___verifier_buffer;
			int16_t L_7 = V_1;
			int16_t L_8;
			L_8 = Verifier_ReadVOffsetT_m3F7EA721035EA1275A5536FA2965DC30FB246B0F(__this, L_6, L_7, NULL);
			if ((((int32_t)L_5) >= ((int32_t)L_8)))
			{
				goto IL_0039_1;
			}
		}
		{
			ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* L_9 = __this->___verifier_buffer;
			int16_t L_10 = V_1;
			int16_t L_11 = ___1_vtableOffset;
			int16_t L_12;
			L_12 = Verifier_ReadVOffsetT_m3F7EA721035EA1275A5536FA2965DC30FB246B0F(__this, L_9, ((int32_t)il2cpp_codegen_add((int32_t)L_10, (int32_t)L_11)), NULL);
			V_0 = L_12;
			goto IL_003b_1;
		}

IL_0039_1:
		{
			V_0 = (int16_t)0;
		}

IL_003b_1:
		{
			goto IL_004d;
		}
	}
	catch(Il2CppExceptionWrapper& e)
	{
		if(il2cpp_codegen_class_is_assignable_from (((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&Exception_t_il2cpp_TypeInfo_var)), il2cpp_codegen_object_class(e.ex)))
		{
			IL2CPP_PUSH_ACTIVE_EXCEPTION(e.ex);
			goto CATCH_003d;
		}
		throw e;
	}

CATCH_003d:
	{
		Exception_t* L_13 = ((Exception_t*)IL2CPP_GET_ACTIVE_EXCEPTION(Exception_t*));;
		V_2 = L_13;
		Exception_t* L_14 = V_2;
		il2cpp_codegen_runtime_class_init_inline(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&Console_t5EDF9498D011BD48287171978EDBBA6964829C3E_il2cpp_TypeInfo_var)));
		Console_WriteLine_mCF1AA33709B943A23A5CA168DF7972F4DAB53011(((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteral1B9CAE50CA32CE68AA9F00077C1C9A89639E7214)), L_14, NULL);
		int16_t L_15 = V_0;
		V_3 = L_15;
		IL2CPP_POP_ACTIVE_EXCEPTION(Exception_t*);
		goto IL_004f;
	}

IL_004d:
	{
		int16_t L_16 = V_0;
		return L_16;
	}

IL_004f:
	{
		int16_t L_17 = V_3;
		return L_17;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint32_t Verifier_GetVOffset_m75F1130DAE55F0EE4BDFE43737E53113BF25FD8F (Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864* __this, uint32_t ___0_tablePos, int16_t ___1_vtableOffset, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Convert_t7097FF336D592F7C06D88A98349A44646F91EFFC_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	uint32_t V_0 = 0;
	int16_t V_1 = 0;
	{
		V_0 = 0;
		uint32_t L_0 = ___0_tablePos;
		il2cpp_codegen_runtime_class_init_inline(Convert_t7097FF336D592F7C06D88A98349A44646F91EFFC_il2cpp_TypeInfo_var);
		int32_t L_1;
		L_1 = Convert_ToInt32_m5ADD7A6890AE40D05444F58D72FDEC7252D6D7F2(L_0, NULL);
		int16_t L_2 = ___1_vtableOffset;
		int16_t L_3;
		L_3 = Verifier_GetVRelOffset_mF26D54B2378F87DEA0C42285F40ECB98B05D2F14(__this, L_1, L_2, NULL);
		V_1 = L_3;
		int16_t L_4 = V_1;
		if (!L_4)
		{
			goto IL_0020;
		}
	}
	{
		uint32_t L_5 = ___0_tablePos;
		int16_t L_6 = V_1;
		il2cpp_codegen_runtime_class_init_inline(Convert_t7097FF336D592F7C06D88A98349A44646F91EFFC_il2cpp_TypeInfo_var);
		uint32_t L_7;
		L_7 = Convert_ToUInt32_m8754C042D71DB6C81EB54D85B73B7EC2710E4FA0(((int64_t)il2cpp_codegen_add(((int64_t)(uint64_t)L_5), ((int64_t)L_6))), NULL);
		V_0 = L_7;
		goto IL_0022;
	}

IL_0020:
	{
		V_0 = 0;
	}

IL_0022:
	{
		uint32_t L_8 = V_0;
		return L_8;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Verifier_CheckComplexity_m154BA6645F54D28747BCF950D4371C748BC4991D (Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864* __this, const RuntimeMethod* method) 
{
	{
		int32_t L_0;
		L_0 = Verifier_get_depth_m4A917A65B04FAC0E7374D664A28B7BFC089AA74E_inline(__this, NULL);
		Options_t2C469D7B05D4F50066C5316A778A04C4DF53F088* L_1;
		L_1 = Verifier_get_options_m5D0BEA9EBE4645B980E3066F2E42B042943D87E9_inline(__this, NULL);
		NullCheck(L_1);
		int32_t L_2;
		L_2 = Options_get_maxDepth_m437B08A483A59E4600A3A2B6FE3A26E3EA8B2212_inline(L_1, NULL);
		if ((((int32_t)L_0) > ((int32_t)L_2)))
		{
			goto IL_002a;
		}
	}
	{
		int32_t L_3;
		L_3 = Verifier_get_numTables_mD6123A14045007BAE82B1770EB9201594F778973_inline(__this, NULL);
		Options_t2C469D7B05D4F50066C5316A778A04C4DF53F088* L_4;
		L_4 = Verifier_get_options_m5D0BEA9EBE4645B980E3066F2E42B042943D87E9_inline(__this, NULL);
		NullCheck(L_4);
		int32_t L_5;
		L_5 = Options_get_maxTables_mE2E55C408DAE7EB5059F6169F471ED14B93F2D40_inline(L_4, NULL);
		return (bool)((((int32_t)((((int32_t)L_3) > ((int32_t)L_5))? 1 : 0)) == ((int32_t)0))? 1 : 0);
	}

IL_002a:
	{
		return (bool)0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Verifier_CheckAlignment_m967E1965E15845B66E5454A303C51D46DE3FEDF5 (Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864* __this, uint32_t ___0_element, uint64_t ___1_align, const RuntimeMethod* method) 
{
	{
		uint32_t L_0 = ___0_element;
		uint64_t L_1 = ___1_align;
		if (!((int64_t)(((int64_t)(uint64_t)L_0)&((int64_t)il2cpp_codegen_subtract((int64_t)L_1, ((int64_t)1))))))
		{
			goto IL_0018;
		}
	}
	{
		Options_t2C469D7B05D4F50066C5316A778A04C4DF53F088* L_2;
		L_2 = Verifier_get_options_m5D0BEA9EBE4645B980E3066F2E42B042943D87E9_inline(__this, NULL);
		NullCheck(L_2);
		bool L_3;
		L_3 = Options_get_alignmentCheck_mC94E7A871FC44FE7505713CD0E69A9FE2BFD4EEA_inline(L_2, NULL);
		return (bool)((((int32_t)L_3) == ((int32_t)0))? 1 : 0);
	}

IL_0018:
	{
		return (bool)1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Verifier_CheckElement_m2CF35C61F826AE256B69A5EEA5719190AF474680 (Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864* __this, uint32_t ___0_pos, uint64_t ___1_elementSize, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Convert_t7097FF336D592F7C06D88A98349A44646F91EFFC_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		uint64_t L_0 = ___1_elementSize;
		ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* L_1 = __this->___verifier_buffer;
		NullCheck(L_1);
		int32_t L_2;
		L_2 = ByteBuffer_get_Length_mD83481CCE00F09128DBAC206A094C628E635BA8B(L_1, NULL);
		il2cpp_codegen_runtime_class_init_inline(Convert_t7097FF336D592F7C06D88A98349A44646F91EFFC_il2cpp_TypeInfo_var);
		uint64_t L_3;
		L_3 = Convert_ToUInt64_m53C3A45C87A06F25957619222B04EABFD214373C(L_2, NULL);
		if ((!(((uint64_t)L_0) < ((uint64_t)L_3))))
		{
			goto IL_002e;
		}
	}
	{
		uint32_t L_4 = ___0_pos;
		ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* L_5 = __this->___verifier_buffer;
		NullCheck(L_5);
		int32_t L_6;
		L_6 = ByteBuffer_get_Length_mD83481CCE00F09128DBAC206A094C628E635BA8B(L_5, NULL);
		il2cpp_codegen_runtime_class_init_inline(Convert_t7097FF336D592F7C06D88A98349A44646F91EFFC_il2cpp_TypeInfo_var);
		uint32_t L_7;
		L_7 = Convert_ToUInt32_m3BD840FA8B5073EDD04AD3D3A044785EB00511A3(L_6, NULL);
		uint64_t L_8 = ___1_elementSize;
		return (bool)((((int32_t)((!(((uint64_t)((int64_t)(uint64_t)L_4)) <= ((uint64_t)((int64_t)il2cpp_codegen_subtract(((int64_t)(uint64_t)L_7), (int64_t)L_8)))))? 1 : 0)) == ((int32_t)0))? 1 : 0);
	}

IL_002e:
	{
		return (bool)0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Verifier_CheckScalar_m9799566886A1AC4D9A2A82F5093B0BA1FC8BF735 (Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864* __this, uint32_t ___0_pos, uint64_t ___1_elementSize, const RuntimeMethod* method) 
{
	{
		uint32_t L_0 = ___0_pos;
		uint64_t L_1 = ___1_elementSize;
		bool L_2;
		L_2 = Verifier_CheckAlignment_m967E1965E15845B66E5454A303C51D46DE3FEDF5(__this, L_0, L_1, NULL);
		if (!L_2)
		{
			goto IL_0013;
		}
	}
	{
		uint32_t L_3 = ___0_pos;
		uint64_t L_4 = ___1_elementSize;
		bool L_5;
		L_5 = Verifier_CheckElement_m2CF35C61F826AE256B69A5EEA5719190AF474680(__this, L_3, L_4, NULL);
		return L_5;
	}

IL_0013:
	{
		return (bool)0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR checkElementStruct_tCF75802323BACA21C0D2C66C2531CC19F664B49B Verifier_CheckVectorOrString_m84A7DB3D433AC14911F20DB712CF3BB178A37FB8 (Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864* __this, uint32_t ___0_pos, uint64_t ___1_elementSize, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Convert_t7097FF336D592F7C06D88A98349A44646F91EFFC_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	checkElementStruct_tCF75802323BACA21C0D2C66C2531CC19F664B49B V_0;
	memset((&V_0), 0, sizeof(V_0));
	uint32_t V_1 = 0;
	uint32_t V_2 = 0;
	uint64_t V_3 = 0;
	uint32_t V_4 = 0;
	uint32_t V_5 = 0;
	checkElementStruct_tCF75802323BACA21C0D2C66C2531CC19F664B49B V_6;
	memset((&V_6), 0, sizeof(V_6));
	{
		il2cpp_codegen_initobj((&V_6), sizeof(checkElementStruct_tCF75802323BACA21C0D2C66C2531CC19F664B49B));
		(&V_6)->___elementValid = (bool)0;
		(&V_6)->___elementOffset = 0;
		checkElementStruct_tCF75802323BACA21C0D2C66C2531CC19F664B49B L_0 = V_6;
		V_0 = L_0;
		uint32_t L_1 = ___0_pos;
		V_1 = L_1;
		uint32_t L_2 = V_1;
		bool L_3;
		L_3 = Verifier_CheckScalar_m9799566886A1AC4D9A2A82F5093B0BA1FC8BF735(__this, L_2, ((int64_t)4), NULL);
		if (L_3)
		{
			goto IL_002a;
		}
	}
	{
		checkElementStruct_tCF75802323BACA21C0D2C66C2531CC19F664B49B L_4 = V_0;
		return L_4;
	}

IL_002a:
	{
		ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* L_5 = __this->___verifier_buffer;
		uint32_t L_6 = V_1;
		uint32_t L_7;
		L_7 = Verifier_ReadUOffsetT_mFA082679C617C5C8424CDDFE3965438AB7DE85B1(__this, L_5, L_6, NULL);
		V_2 = L_7;
		uint64_t L_8 = ___1_elementSize;
		V_3 = ((int64_t)((uint64_t)(int64_t)((int64_t)((int32_t)2147483647LL))/(uint64_t)(int64_t)L_8));
		uint32_t L_9 = V_2;
		uint64_t L_10 = V_3;
		if ((!(((uint64_t)((int64_t)(uint64_t)L_9)) >= ((uint64_t)L_10))))
		{
			goto IL_0048;
		}
	}
	{
		checkElementStruct_tCF75802323BACA21C0D2C66C2531CC19F664B49B L_11 = V_0;
		return L_11;
	}

IL_0048:
	{
		uint64_t L_12 = ___1_elementSize;
		il2cpp_codegen_runtime_class_init_inline(Convert_t7097FF336D592F7C06D88A98349A44646F91EFFC_il2cpp_TypeInfo_var);
		uint32_t L_13;
		L_13 = Convert_ToUInt32_m32FF6CCC6C935251DD40D88D6ED2C4B39C9CFA0D(L_12, NULL);
		uint32_t L_14 = V_2;
		V_4 = ((int32_t)il2cpp_codegen_add(4, ((int32_t)il2cpp_codegen_multiply((int32_t)L_13, (int32_t)L_14))));
		uint32_t L_15 = V_1;
		uint32_t L_16 = V_4;
		V_5 = ((int32_t)il2cpp_codegen_add((int32_t)L_15, (int32_t)L_16));
		uint32_t L_17 = V_1;
		uint32_t L_18 = V_4;
		bool L_19;
		L_19 = Verifier_CheckElement_m2CF35C61F826AE256B69A5EEA5719190AF474680(__this, L_17, ((int64_t)(uint64_t)L_18), NULL);
		(&V_0)->___elementValid = L_19;
		uint32_t L_20 = V_5;
		(&V_0)->___elementOffset = L_20;
		checkElementStruct_tCF75802323BACA21C0D2C66C2531CC19F664B49B L_21 = V_0;
		return L_21;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Verifier_CheckString_m77D796D84E00F7EE4ED3B35A183B80C54C610241 (Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864* __this, uint32_t ___0_pos, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Convert_t7097FF336D592F7C06D88A98349A44646F91EFFC_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	checkElementStruct_tCF75802323BACA21C0D2C66C2531CC19F664B49B V_0;
	memset((&V_0), 0, sizeof(V_0));
	checkElementStruct_tCF75802323BACA21C0D2C66C2531CC19F664B49B* G_B3_0 = NULL;
	checkElementStruct_tCF75802323BACA21C0D2C66C2531CC19F664B49B* G_B2_0 = NULL;
	int32_t G_B4_0 = 0;
	checkElementStruct_tCF75802323BACA21C0D2C66C2531CC19F664B49B* G_B4_1 = NULL;
	checkElementStruct_tCF75802323BACA21C0D2C66C2531CC19F664B49B* G_B6_0 = NULL;
	checkElementStruct_tCF75802323BACA21C0D2C66C2531CC19F664B49B* G_B5_0 = NULL;
	int32_t G_B7_0 = 0;
	checkElementStruct_tCF75802323BACA21C0D2C66C2531CC19F664B49B* G_B7_1 = NULL;
	{
		uint32_t L_0 = ___0_pos;
		checkElementStruct_tCF75802323BACA21C0D2C66C2531CC19F664B49B L_1;
		L_1 = Verifier_CheckVectorOrString_m84A7DB3D433AC14911F20DB712CF3BB178A37FB8(__this, L_0, ((int64_t)1), NULL);
		V_0 = L_1;
		Options_t2C469D7B05D4F50066C5316A778A04C4DF53F088* L_2;
		L_2 = Verifier_get_options_m5D0BEA9EBE4645B980E3066F2E42B042943D87E9_inline(__this, NULL);
		NullCheck(L_2);
		bool L_3;
		L_3 = Options_get_stringEndCheck_mB9217751DB5C7D10C8EDB209FC945402821A1339_inline(L_2, NULL);
		if (!L_3)
		{
			goto IL_0062;
		}
	}
	{
		checkElementStruct_tCF75802323BACA21C0D2C66C2531CC19F664B49B L_4 = V_0;
		bool L_5 = L_4.___elementValid;
		if (!L_5)
		{
			G_B3_0 = (&V_0);
			goto IL_0031;
		}
		G_B2_0 = (&V_0);
	}
	{
		checkElementStruct_tCF75802323BACA21C0D2C66C2531CC19F664B49B L_6 = V_0;
		uint32_t L_7 = L_6.___elementOffset;
		bool L_8;
		L_8 = Verifier_CheckScalar_m9799566886A1AC4D9A2A82F5093B0BA1FC8BF735(__this, L_7, ((int64_t)1), NULL);
		G_B4_0 = ((int32_t)(L_8));
		G_B4_1 = G_B2_0;
		goto IL_0032;
	}

IL_0031:
	{
		G_B4_0 = 0;
		G_B4_1 = G_B3_0;
	}

IL_0032:
	{
		G_B4_1->___elementValid = (bool)G_B4_0;
		checkElementStruct_tCF75802323BACA21C0D2C66C2531CC19F664B49B L_9 = V_0;
		bool L_10 = L_9.___elementValid;
		if (!L_10)
		{
			G_B6_0 = (&V_0);
			goto IL_005c;
		}
		G_B5_0 = (&V_0);
	}
	{
		ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* L_11 = __this->___verifier_buffer;
		checkElementStruct_tCF75802323BACA21C0D2C66C2531CC19F664B49B L_12 = V_0;
		uint32_t L_13 = L_12.___elementOffset;
		il2cpp_codegen_runtime_class_init_inline(Convert_t7097FF336D592F7C06D88A98349A44646F91EFFC_il2cpp_TypeInfo_var);
		int32_t L_14;
		L_14 = Convert_ToInt32_m5ADD7A6890AE40D05444F58D72FDEC7252D6D7F2(L_13, NULL);
		NullCheck(L_11);
		int8_t L_15;
		L_15 = ByteBuffer_GetSbyte_mC5F6FC81C592D6FDFDF41EE3EA719EEA2234295B(L_11, L_14, NULL);
		G_B7_0 = ((((int32_t)L_15) == ((int32_t)0))? 1 : 0);
		G_B7_1 = G_B5_0;
		goto IL_005d;
	}

IL_005c:
	{
		G_B7_0 = 0;
		G_B7_1 = G_B6_0;
	}

IL_005d:
	{
		G_B7_1->___elementValid = (bool)G_B7_0;
	}

IL_0062:
	{
		checkElementStruct_tCF75802323BACA21C0D2C66C2531CC19F664B49B L_16 = V_0;
		bool L_17 = L_16.___elementValid;
		return L_17;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Verifier_CheckVector_m53F4531381F8067BDFEF05CE7EE4DCC55E7D4DC9 (Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864* __this, uint32_t ___0_pos, uint64_t ___1_elementSize, const RuntimeMethod* method) 
{
	{
		uint32_t L_0 = ___0_pos;
		uint64_t L_1 = ___1_elementSize;
		checkElementStruct_tCF75802323BACA21C0D2C66C2531CC19F664B49B L_2;
		L_2 = Verifier_CheckVectorOrString_m84A7DB3D433AC14911F20DB712CF3BB178A37FB8(__this, L_0, L_1, NULL);
		bool L_3 = L_2.___elementValid;
		return L_3;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Verifier_CheckTable_mBD169E08464C583974B9759AE59A7FF29E2F8691 (Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864* __this, uint32_t ___0_tablePos, VerifyTableAction_t4EE7F0547499A39B8176F648F806406AB26487AB* ___1_verifyAction, const RuntimeMethod* method) 
{
	{
		VerifyTableAction_t4EE7F0547499A39B8176F648F806406AB26487AB* L_0 = ___1_verifyAction;
		uint32_t L_1 = ___0_tablePos;
		NullCheck(L_0);
		bool L_2;
		L_2 = VerifyTableAction_Invoke_mDD4DF94C57F6A57A87F19A8915ACD085F65FF6D4_inline(L_0, __this, L_1, NULL);
		return L_2;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Verifier_CheckStringFunc_mA69C648CF9A9C67C33D1FAA25111ECE5EB1EE237 (Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864* __this, Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864* ___0_verifier, uint32_t ___1_pos, const RuntimeMethod* method) 
{
	{
		Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864* L_0 = ___0_verifier;
		uint32_t L_1 = ___1_pos;
		NullCheck(L_0);
		bool L_2;
		L_2 = Verifier_CheckString_m77D796D84E00F7EE4ED3B35A183B80C54C610241(L_0, L_1, NULL);
		return L_2;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Verifier_CheckVectorOfObjects_mF84993CB74FF40A3AFA1AF020D677A7CBF4A9E6D (Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864* __this, uint32_t ___0_pos, VerifyTableAction_t4EE7F0547499A39B8176F648F806406AB26487AB* ___1_verifyAction, const RuntimeMethod* method) 
{
	uint32_t V_0 = 0;
	uint32_t V_1 = 0;
	uint32_t V_2 = 0;
	uint32_t V_3 = 0;
	uint32_t V_4 = 0;
	{
		uint32_t L_0 = ___0_pos;
		bool L_1;
		L_1 = Verifier_CheckVector_m53F4531381F8067BDFEF05CE7EE4DCC55E7D4DC9(__this, L_0, ((int64_t)4), NULL);
		if (L_1)
		{
			goto IL_000d;
		}
	}
	{
		return (bool)0;
	}

IL_000d:
	{
		ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* L_2 = __this->___verifier_buffer;
		uint32_t L_3 = ___0_pos;
		uint32_t L_4;
		L_4 = Verifier_ReadUOffsetT_mFA082679C617C5C8424CDDFE3965438AB7DE85B1(__this, L_2, L_3, NULL);
		V_0 = L_4;
		uint32_t L_5 = ___0_pos;
		V_1 = ((int32_t)il2cpp_codegen_add((int32_t)L_5, 4));
		V_2 = 0;
		V_3 = 0;
		goto IL_0050;
	}

IL_0025:
	{
		uint32_t L_6 = V_1;
		uint32_t L_7 = V_3;
		V_2 = ((int32_t)il2cpp_codegen_add((int32_t)L_6, ((int32_t)il2cpp_codegen_multiply((int32_t)L_7, 4))));
		uint32_t L_8 = V_2;
		bool L_9;
		L_9 = Verifier_CheckIndirectOffset_m9EC00EFA29171D9BCB9D6CF2514D6C0C38E1385A(__this, L_8, NULL);
		if (L_9)
		{
			goto IL_0036;
		}
	}
	{
		return (bool)0;
	}

IL_0036:
	{
		uint32_t L_10 = V_2;
		uint32_t L_11;
		L_11 = Verifier_GetIndirectOffset_mD4ABA15561C86526A192B328F49DBFBFF7BA7EBB(__this, L_10, NULL);
		V_4 = L_11;
		VerifyTableAction_t4EE7F0547499A39B8176F648F806406AB26487AB* L_12 = ___1_verifyAction;
		uint32_t L_13 = V_4;
		NullCheck(L_12);
		bool L_14;
		L_14 = VerifyTableAction_Invoke_mDD4DF94C57F6A57A87F19A8915ACD085F65FF6D4_inline(L_12, __this, L_13, NULL);
		if (L_14)
		{
			goto IL_004c;
		}
	}
	{
		return (bool)0;
	}

IL_004c:
	{
		uint32_t L_15 = V_3;
		V_3 = ((int32_t)il2cpp_codegen_add((int32_t)L_15, 1));
	}

IL_0050:
	{
		uint32_t L_16 = V_3;
		uint32_t L_17 = V_0;
		if ((!(((uint32_t)L_16) >= ((uint32_t)L_17))))
		{
			goto IL_0025;
		}
	}
	{
		return (bool)1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Verifier_CheckIndirectOffset_m9EC00EFA29171D9BCB9D6CF2514D6C0C38E1385A (Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864* __this, uint32_t ___0_pos, const RuntimeMethod* method) 
{
	uint32_t V_0 = 0;
	{
		uint32_t L_0 = ___0_pos;
		bool L_1;
		L_1 = Verifier_CheckScalar_m9799566886A1AC4D9A2A82F5093B0BA1FC8BF735(__this, L_0, ((int64_t)4), NULL);
		if (L_1)
		{
			goto IL_000d;
		}
	}
	{
		return (bool)0;
	}

IL_000d:
	{
		ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* L_2 = __this->___verifier_buffer;
		uint32_t L_3 = ___0_pos;
		uint32_t L_4;
		L_4 = Verifier_ReadUOffsetT_mFA082679C617C5C8424CDDFE3965438AB7DE85B1(__this, L_2, L_3, NULL);
		V_0 = L_4;
		uint32_t L_5 = V_0;
		if (!L_5)
		{
			goto IL_0026;
		}
	}
	{
		uint32_t L_6 = V_0;
		if ((!(((uint32_t)L_6) >= ((uint32_t)((int32_t)2147483647LL)))))
		{
			goto IL_0028;
		}
	}

IL_0026:
	{
		return (bool)0;
	}

IL_0028:
	{
		uint32_t L_7 = ___0_pos;
		uint32_t L_8 = V_0;
		bool L_9;
		L_9 = Verifier_CheckElement_m2CF35C61F826AE256B69A5EEA5719190AF474680(__this, ((int32_t)il2cpp_codegen_add((int32_t)L_7, (int32_t)L_8)), ((int64_t)1), NULL);
		return L_9;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Verifier_CheckBufferFromStart_m30D3F098F0258380253601997A05C0C0E55569F4 (Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864* __this, String_t* ___0_identifier, uint32_t ___1_startPos, VerifyTableAction_t4EE7F0547499A39B8176F648F806406AB26487AB* ___2_verifyAction, const RuntimeMethod* method) 
{
	uint32_t V_0 = 0;
	{
		String_t* L_0 = ___0_identifier;
		if (!L_0)
		{
			goto IL_002b;
		}
	}
	{
		String_t* L_1 = ___0_identifier;
		NullCheck(L_1);
		int32_t L_2;
		L_2 = String_get_Length_m42625D67623FA5CC7A44D47425CE86FB946542D2_inline(L_1, NULL);
		if (L_2)
		{
			goto IL_002b;
		}
	}
	{
		ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* L_3 = __this->___verifier_buffer;
		NullCheck(L_3);
		int32_t L_4;
		L_4 = ByteBuffer_get_Length_mD83481CCE00F09128DBAC206A094C628E635BA8B(L_3, NULL);
		if ((((int32_t)L_4) < ((int32_t)8)))
		{
			goto IL_0029;
		}
	}
	{
		ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* L_5 = __this->___verifier_buffer;
		uint32_t L_6 = ___1_startPos;
		String_t* L_7 = ___0_identifier;
		bool L_8;
		L_8 = Verifier_BufferHasIdentifier_m47A5B9E1944079E3B21B67886594DD78A79F9F76(__this, L_5, L_6, L_7, NULL);
		if (L_8)
		{
			goto IL_002b;
		}
	}

IL_0029:
	{
		return (bool)0;
	}

IL_002b:
	{
		uint32_t L_9 = ___1_startPos;
		bool L_10;
		L_10 = Verifier_CheckIndirectOffset_m9EC00EFA29171D9BCB9D6CF2514D6C0C38E1385A(__this, L_9, NULL);
		if (L_10)
		{
			goto IL_0036;
		}
	}
	{
		return (bool)0;
	}

IL_0036:
	{
		uint32_t L_11 = ___1_startPos;
		uint32_t L_12;
		L_12 = Verifier_GetIndirectOffset_mD4ABA15561C86526A192B328F49DBFBFF7BA7EBB(__this, L_11, NULL);
		V_0 = L_12;
		uint32_t L_13 = V_0;
		VerifyTableAction_t4EE7F0547499A39B8176F648F806406AB26487AB* L_14 = ___2_verifyAction;
		bool L_15;
		L_15 = Verifier_CheckTable_mBD169E08464C583974B9759AE59A7FF29E2F8691(__this, L_13, L_14, NULL);
		return L_15;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint32_t Verifier_GetIndirectOffset_mD4ABA15561C86526A192B328F49DBFBFF7BA7EBB (Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864* __this, uint32_t ___0_pos, const RuntimeMethod* method) 
{
	{
		uint32_t L_0 = ___0_pos;
		ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* L_1 = __this->___verifier_buffer;
		uint32_t L_2 = ___0_pos;
		uint32_t L_3;
		L_3 = Verifier_ReadUOffsetT_mFA082679C617C5C8424CDDFE3965438AB7DE85B1(__this, L_1, L_2, NULL);
		return ((int32_t)il2cpp_codegen_add((int32_t)L_0, (int32_t)L_3));
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Verifier_VerifyTableStart_m59CE4D2B5F4D49AEF99EB014DFC079FC6F068434 (Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864* __this, uint32_t ___0_tablePos, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Convert_t7097FF336D592F7C06D88A98349A44646F91EFFC_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	uint32_t V_0 = 0;
	{
		int32_t L_0 = __this->___depth_cnt;
		__this->___depth_cnt = ((int32_t)il2cpp_codegen_add(L_0, 1));
		int32_t L_1 = __this->___num_tables_cnt;
		__this->___num_tables_cnt = ((int32_t)il2cpp_codegen_add(L_1, 1));
		uint32_t L_2 = ___0_tablePos;
		bool L_3;
		L_3 = Verifier_CheckScalar_m9799566886A1AC4D9A2A82F5093B0BA1FC8BF735(__this, L_2, ((int64_t)4), NULL);
		if (L_3)
		{
			goto IL_0029;
		}
	}
	{
		return (bool)0;
	}

IL_0029:
	{
		uint32_t L_4 = ___0_tablePos;
		ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* L_5 = __this->___verifier_buffer;
		uint32_t L_6 = ___0_tablePos;
		il2cpp_codegen_runtime_class_init_inline(Convert_t7097FF336D592F7C06D88A98349A44646F91EFFC_il2cpp_TypeInfo_var);
		int32_t L_7;
		L_7 = Convert_ToInt32_m5ADD7A6890AE40D05444F58D72FDEC7252D6D7F2(L_6, NULL);
		int32_t L_8;
		L_8 = Verifier_ReadSOffsetT_m5A43060F831A70DD3ECDFA632C079B51B4C15EBE(__this, L_5, L_7, NULL);
		V_0 = ((int32_t)(uint32_t)((int64_t)il2cpp_codegen_subtract(((int64_t)(uint64_t)L_4), ((int64_t)L_8))));
		bool L_9;
		L_9 = Verifier_CheckComplexity_m154BA6645F54D28747BCF950D4371C748BC4991D(__this, NULL);
		if (!L_9)
		{
			goto IL_0094;
		}
	}
	{
		uint32_t L_10 = V_0;
		bool L_11;
		L_11 = Verifier_CheckScalar_m9799566886A1AC4D9A2A82F5093B0BA1FC8BF735(__this, L_10, ((int64_t)2), NULL);
		if (!L_11)
		{
			goto IL_0094;
		}
	}
	{
		ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* L_12 = __this->___verifier_buffer;
		uint32_t L_13 = V_0;
		il2cpp_codegen_runtime_class_init_inline(Convert_t7097FF336D592F7C06D88A98349A44646F91EFFC_il2cpp_TypeInfo_var);
		int32_t L_14;
		L_14 = Convert_ToInt32_m5ADD7A6890AE40D05444F58D72FDEC7252D6D7F2(L_13, NULL);
		int16_t L_15;
		L_15 = Verifier_ReadVOffsetT_m3F7EA721035EA1275A5536FA2965DC30FB246B0F(__this, L_12, L_14, NULL);
		uint32_t L_16;
		L_16 = Convert_ToUInt32_m04B73ED6E3C9D5226E7755B57B44417B58F22053(L_15, NULL);
		bool L_17;
		L_17 = Verifier_CheckAlignment_m967E1965E15845B66E5454A303C51D46DE3FEDF5(__this, L_16, ((int64_t)2), NULL);
		if (!L_17)
		{
			goto IL_0094;
		}
	}
	{
		uint32_t L_18 = V_0;
		ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* L_19 = __this->___verifier_buffer;
		uint32_t L_20 = V_0;
		il2cpp_codegen_runtime_class_init_inline(Convert_t7097FF336D592F7C06D88A98349A44646F91EFFC_il2cpp_TypeInfo_var);
		int32_t L_21;
		L_21 = Convert_ToInt32_m5ADD7A6890AE40D05444F58D72FDEC7252D6D7F2(L_20, NULL);
		int16_t L_22;
		L_22 = Verifier_ReadVOffsetT_m3F7EA721035EA1275A5536FA2965DC30FB246B0F(__this, L_19, L_21, NULL);
		uint64_t L_23;
		L_23 = Convert_ToUInt64_mBC2F335F529042F26FEE842CC38966FBDD45C007(L_22, NULL);
		bool L_24;
		L_24 = Verifier_CheckElement_m2CF35C61F826AE256B69A5EEA5719190AF474680(__this, L_18, L_23, NULL);
		return L_24;
	}

IL_0094:
	{
		return (bool)0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Verifier_VerifyTableEnd_m2C2757CE64B2651A96C0E0064503BA7C09E6ADB3 (Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864* __this, uint32_t ___0_tablePos, const RuntimeMethod* method) 
{
	int32_t V_0 = 0;
	{
		int32_t L_0;
		L_0 = Verifier_get_depth_m4A917A65B04FAC0E7374D664A28B7BFC089AA74E_inline(__this, NULL);
		V_0 = L_0;
		int32_t L_1 = V_0;
		Verifier_set_depth_m996F6606AC8B244E1D3AB94C5C32C1934B47A7C7_inline(__this, ((int32_t)il2cpp_codegen_subtract(L_1, 1)), NULL);
		return (bool)1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Verifier_VerifyField_m8A9C01B059B20EAD3C2A4333216302312FCF2163 (Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864* __this, uint32_t ___0_tablePos, int16_t ___1_offsetId, uint64_t ___2_elementSize, uint64_t ___3_align, bool ___4_required, const RuntimeMethod* method) 
{
	uint32_t V_0 = 0;
	{
		uint32_t L_0 = ___0_tablePos;
		int16_t L_1 = ___1_offsetId;
		uint32_t L_2;
		L_2 = Verifier_GetVOffset_m75F1130DAE55F0EE4BDFE43737E53113BF25FD8F(__this, L_0, L_1, NULL);
		V_0 = L_2;
		uint32_t L_3 = V_0;
		if (!L_3)
		{
			goto IL_0022;
		}
	}
	{
		uint32_t L_4 = V_0;
		uint64_t L_5 = ___3_align;
		bool L_6;
		L_6 = Verifier_CheckAlignment_m967E1965E15845B66E5454A303C51D46DE3FEDF5(__this, L_4, L_5, NULL);
		if (!L_6)
		{
			goto IL_0020;
		}
	}
	{
		uint32_t L_7 = V_0;
		uint64_t L_8 = ___2_elementSize;
		bool L_9;
		L_9 = Verifier_CheckElement_m2CF35C61F826AE256B69A5EEA5719190AF474680(__this, L_7, L_8, NULL);
		return L_9;
	}

IL_0020:
	{
		return (bool)0;
	}

IL_0022:
	{
		bool L_10 = ___4_required;
		return (bool)((((int32_t)L_10) == ((int32_t)0))? 1 : 0);
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Verifier_VerifyString_m7B29C44FC2B8366930C88194E580DCA5CF202175 (Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864* __this, uint32_t ___0_tablePos, int16_t ___1_vOffset, bool ___2_required, const RuntimeMethod* method) 
{
	uint32_t V_0 = 0;
	uint32_t V_1 = 0;
	{
		uint32_t L_0 = ___0_tablePos;
		int16_t L_1 = ___1_vOffset;
		uint32_t L_2;
		L_2 = Verifier_GetVOffset_m75F1130DAE55F0EE4BDFE43737E53113BF25FD8F(__this, L_0, L_1, NULL);
		V_0 = L_2;
		uint32_t L_3 = V_0;
		if (L_3)
		{
			goto IL_0011;
		}
	}
	{
		bool L_4 = ___2_required;
		return (bool)((((int32_t)L_4) == ((int32_t)0))? 1 : 0);
	}

IL_0011:
	{
		uint32_t L_5 = V_0;
		bool L_6;
		L_6 = Verifier_CheckIndirectOffset_m9EC00EFA29171D9BCB9D6CF2514D6C0C38E1385A(__this, L_5, NULL);
		if (L_6)
		{
			goto IL_001c;
		}
	}
	{
		return (bool)0;
	}

IL_001c:
	{
		uint32_t L_7 = V_0;
		uint32_t L_8;
		L_8 = Verifier_GetIndirectOffset_mD4ABA15561C86526A192B328F49DBFBFF7BA7EBB(__this, L_7, NULL);
		V_1 = L_8;
		uint32_t L_9 = V_1;
		bool L_10;
		L_10 = Verifier_CheckString_m77D796D84E00F7EE4ED3B35A183B80C54C610241(__this, L_9, NULL);
		return L_10;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Verifier_VerifyVectorOfData_m35B995B5DC3DD8BB45C3DA30223C1CDD6FBFC79F (Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864* __this, uint32_t ___0_tablePos, int16_t ___1_vOffset, uint64_t ___2_elementSize, bool ___3_required, const RuntimeMethod* method) 
{
	uint32_t V_0 = 0;
	uint32_t V_1 = 0;
	{
		uint32_t L_0 = ___0_tablePos;
		int16_t L_1 = ___1_vOffset;
		uint32_t L_2;
		L_2 = Verifier_GetVOffset_m75F1130DAE55F0EE4BDFE43737E53113BF25FD8F(__this, L_0, L_1, NULL);
		V_0 = L_2;
		uint32_t L_3 = V_0;
		if (L_3)
		{
			goto IL_0012;
		}
	}
	{
		bool L_4 = ___3_required;
		return (bool)((((int32_t)L_4) == ((int32_t)0))? 1 : 0);
	}

IL_0012:
	{
		uint32_t L_5 = V_0;
		bool L_6;
		L_6 = Verifier_CheckIndirectOffset_m9EC00EFA29171D9BCB9D6CF2514D6C0C38E1385A(__this, L_5, NULL);
		if (L_6)
		{
			goto IL_001d;
		}
	}
	{
		return (bool)0;
	}

IL_001d:
	{
		uint32_t L_7 = V_0;
		uint32_t L_8;
		L_8 = Verifier_GetIndirectOffset_mD4ABA15561C86526A192B328F49DBFBFF7BA7EBB(__this, L_7, NULL);
		V_1 = L_8;
		uint32_t L_9 = V_1;
		uint64_t L_10 = ___2_elementSize;
		bool L_11;
		L_11 = Verifier_CheckVector_m53F4531381F8067BDFEF05CE7EE4DCC55E7D4DC9(__this, L_9, L_10, NULL);
		return L_11;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Verifier_VerifyVectorOfStrings_m2425C566B672088FD5A52C252B32781A3720FBEF (Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864* __this, uint32_t ___0_tablePos, int16_t ___1_offsetId, bool ___2_required, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Verifier_CheckStringFunc_mA69C648CF9A9C67C33D1FAA25111ECE5EB1EE237_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&VerifyTableAction_t4EE7F0547499A39B8176F648F806406AB26487AB_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	uint32_t V_0 = 0;
	uint32_t V_1 = 0;
	{
		uint32_t L_0 = ___0_tablePos;
		int16_t L_1 = ___1_offsetId;
		uint32_t L_2;
		L_2 = Verifier_GetVOffset_m75F1130DAE55F0EE4BDFE43737E53113BF25FD8F(__this, L_0, L_1, NULL);
		V_0 = L_2;
		uint32_t L_3 = V_0;
		if (L_3)
		{
			goto IL_0011;
		}
	}
	{
		bool L_4 = ___2_required;
		return (bool)((((int32_t)L_4) == ((int32_t)0))? 1 : 0);
	}

IL_0011:
	{
		uint32_t L_5 = V_0;
		bool L_6;
		L_6 = Verifier_CheckIndirectOffset_m9EC00EFA29171D9BCB9D6CF2514D6C0C38E1385A(__this, L_5, NULL);
		if (L_6)
		{
			goto IL_001c;
		}
	}
	{
		return (bool)0;
	}

IL_001c:
	{
		uint32_t L_7 = V_0;
		uint32_t L_8;
		L_8 = Verifier_GetIndirectOffset_mD4ABA15561C86526A192B328F49DBFBFF7BA7EBB(__this, L_7, NULL);
		V_1 = L_8;
		uint32_t L_9 = V_1;
		VerifyTableAction_t4EE7F0547499A39B8176F648F806406AB26487AB* L_10 = (VerifyTableAction_t4EE7F0547499A39B8176F648F806406AB26487AB*)il2cpp_codegen_object_new(VerifyTableAction_t4EE7F0547499A39B8176F648F806406AB26487AB_il2cpp_TypeInfo_var);
		VerifyTableAction__ctor_m7AEEAD7ED5C1ABF23BE481BA49D8BA704E421456(L_10, __this, (intptr_t)((void*)Verifier_CheckStringFunc_mA69C648CF9A9C67C33D1FAA25111ECE5EB1EE237_RuntimeMethod_var), NULL);
		bool L_11;
		L_11 = Verifier_CheckVectorOfObjects_mF84993CB74FF40A3AFA1AF020D677A7CBF4A9E6D(__this, L_9, L_10, NULL);
		return L_11;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Verifier_VerifyVectorOfTables_mA744774DCAAF3279E0BF4C17F3156EE9CAFDBF25 (Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864* __this, uint32_t ___0_tablePos, int16_t ___1_offsetId, VerifyTableAction_t4EE7F0547499A39B8176F648F806406AB26487AB* ___2_verifyAction, bool ___3_required, const RuntimeMethod* method) 
{
	uint32_t V_0 = 0;
	uint32_t V_1 = 0;
	{
		uint32_t L_0 = ___0_tablePos;
		int16_t L_1 = ___1_offsetId;
		uint32_t L_2;
		L_2 = Verifier_GetVOffset_m75F1130DAE55F0EE4BDFE43737E53113BF25FD8F(__this, L_0, L_1, NULL);
		V_0 = L_2;
		uint32_t L_3 = V_0;
		if (L_3)
		{
			goto IL_0012;
		}
	}
	{
		bool L_4 = ___3_required;
		return (bool)((((int32_t)L_4) == ((int32_t)0))? 1 : 0);
	}

IL_0012:
	{
		uint32_t L_5 = V_0;
		bool L_6;
		L_6 = Verifier_CheckIndirectOffset_m9EC00EFA29171D9BCB9D6CF2514D6C0C38E1385A(__this, L_5, NULL);
		if (L_6)
		{
			goto IL_001d;
		}
	}
	{
		return (bool)0;
	}

IL_001d:
	{
		uint32_t L_7 = V_0;
		uint32_t L_8;
		L_8 = Verifier_GetIndirectOffset_mD4ABA15561C86526A192B328F49DBFBFF7BA7EBB(__this, L_7, NULL);
		V_1 = L_8;
		uint32_t L_9 = V_1;
		VerifyTableAction_t4EE7F0547499A39B8176F648F806406AB26487AB* L_10 = ___2_verifyAction;
		bool L_11;
		L_11 = Verifier_CheckVectorOfObjects_mF84993CB74FF40A3AFA1AF020D677A7CBF4A9E6D(__this, L_9, L_10, NULL);
		return L_11;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Verifier_VerifyTable_m7858D788213DE336FA2AFD350195CE9F2746DAAF (Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864* __this, uint32_t ___0_tablePos, int16_t ___1_offsetId, VerifyTableAction_t4EE7F0547499A39B8176F648F806406AB26487AB* ___2_verifyAction, bool ___3_required, const RuntimeMethod* method) 
{
	uint32_t V_0 = 0;
	uint32_t V_1 = 0;
	{
		uint32_t L_0 = ___0_tablePos;
		int16_t L_1 = ___1_offsetId;
		uint32_t L_2;
		L_2 = Verifier_GetVOffset_m75F1130DAE55F0EE4BDFE43737E53113BF25FD8F(__this, L_0, L_1, NULL);
		V_0 = L_2;
		uint32_t L_3 = V_0;
		if (L_3)
		{
			goto IL_0012;
		}
	}
	{
		bool L_4 = ___3_required;
		return (bool)((((int32_t)L_4) == ((int32_t)0))? 1 : 0);
	}

IL_0012:
	{
		uint32_t L_5 = V_0;
		bool L_6;
		L_6 = Verifier_CheckIndirectOffset_m9EC00EFA29171D9BCB9D6CF2514D6C0C38E1385A(__this, L_5, NULL);
		if (L_6)
		{
			goto IL_001d;
		}
	}
	{
		return (bool)0;
	}

IL_001d:
	{
		uint32_t L_7 = V_0;
		uint32_t L_8;
		L_8 = Verifier_GetIndirectOffset_mD4ABA15561C86526A192B328F49DBFBFF7BA7EBB(__this, L_7, NULL);
		V_1 = L_8;
		uint32_t L_9 = V_1;
		VerifyTableAction_t4EE7F0547499A39B8176F648F806406AB26487AB* L_10 = ___2_verifyAction;
		bool L_11;
		L_11 = Verifier_CheckTable_mBD169E08464C583974B9759AE59A7FF29E2F8691(__this, L_9, L_10, NULL);
		return L_11;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Verifier_VerifyUnion_m28185E914E664B188CB475970C126B9F72668D88 (Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864* __this, uint32_t ___0_tablePos, int16_t ___1_typeIdVOffset, int16_t ___2_valueVOffset, VerifyUnionAction_t41231FB98151184E37EF5626A85224925AC03F23* ___3_verifyAction, bool ___4_required, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Convert_t7097FF336D592F7C06D88A98349A44646F91EFFC_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	uint32_t V_0 = 0;
	uint8_t V_1 = 0x0;
	uint32_t V_2 = 0;
	{
		uint32_t L_0 = ___0_tablePos;
		int16_t L_1 = ___1_typeIdVOffset;
		uint32_t L_2;
		L_2 = Verifier_GetVOffset_m75F1130DAE55F0EE4BDFE43737E53113BF25FD8F(__this, L_0, L_1, NULL);
		V_0 = L_2;
		uint32_t L_3 = V_0;
		if (L_3)
		{
			goto IL_0012;
		}
	}
	{
		bool L_4 = ___4_required;
		return (bool)((((int32_t)L_4) == ((int32_t)0))? 1 : 0);
	}

IL_0012:
	{
		uint32_t L_5 = V_0;
		bool L_6;
		L_6 = Verifier_CheckAlignment_m967E1965E15845B66E5454A303C51D46DE3FEDF5(__this, L_5, ((int64_t)1), NULL);
		if (!L_6)
		{
			goto IL_0028;
		}
	}
	{
		uint32_t L_7 = V_0;
		bool L_8;
		L_8 = Verifier_CheckElement_m2CF35C61F826AE256B69A5EEA5719190AF474680(__this, L_7, ((int64_t)1), NULL);
		if (L_8)
		{
			goto IL_002a;
		}
	}

IL_0028:
	{
		return (bool)0;
	}

IL_002a:
	{
		uint32_t L_9 = ___0_tablePos;
		int16_t L_10 = ___2_valueVOffset;
		uint32_t L_11;
		L_11 = Verifier_GetVOffset_m75F1130DAE55F0EE4BDFE43737E53113BF25FD8F(__this, L_9, L_10, NULL);
		V_0 = L_11;
		ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* L_12 = __this->___verifier_buffer;
		uint32_t L_13 = V_0;
		il2cpp_codegen_runtime_class_init_inline(Convert_t7097FF336D592F7C06D88A98349A44646F91EFFC_il2cpp_TypeInfo_var);
		int32_t L_14;
		L_14 = Convert_ToInt32_m5ADD7A6890AE40D05444F58D72FDEC7252D6D7F2(L_13, NULL);
		NullCheck(L_12);
		uint8_t L_15;
		L_15 = ByteBuffer_Get_m4DACAF12CF9F83591DF6B595512714C893377FDB(L_12, L_14, NULL);
		V_1 = L_15;
		uint32_t L_16 = V_0;
		if (L_16)
		{
			goto IL_0062;
		}
	}
	{
		VerifyUnionAction_t41231FB98151184E37EF5626A85224925AC03F23* L_17 = ___3_verifyAction;
		uint8_t L_18 = V_1;
		ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* L_19 = __this->___verifier_buffer;
		NullCheck(L_19);
		int32_t L_20;
		L_20 = ByteBuffer_get_Length_mD83481CCE00F09128DBAC206A094C628E635BA8B(L_19, NULL);
		il2cpp_codegen_runtime_class_init_inline(Convert_t7097FF336D592F7C06D88A98349A44646F91EFFC_il2cpp_TypeInfo_var);
		uint32_t L_21;
		L_21 = Convert_ToUInt32_m3BD840FA8B5073EDD04AD3D3A044785EB00511A3(L_20, NULL);
		NullCheck(L_17);
		bool L_22;
		L_22 = VerifyUnionAction_Invoke_mF8A06007D2423423954038A65C411972E1DF6B3B_inline(L_17, __this, L_18, L_21, NULL);
		return L_22;
	}

IL_0062:
	{
		uint32_t L_23 = V_0;
		bool L_24;
		L_24 = Verifier_CheckIndirectOffset_m9EC00EFA29171D9BCB9D6CF2514D6C0C38E1385A(__this, L_23, NULL);
		if (L_24)
		{
			goto IL_006d;
		}
	}
	{
		return (bool)0;
	}

IL_006d:
	{
		uint32_t L_25 = V_0;
		uint32_t L_26;
		L_26 = Verifier_GetIndirectOffset_mD4ABA15561C86526A192B328F49DBFBFF7BA7EBB(__this, L_25, NULL);
		V_2 = L_26;
		VerifyUnionAction_t41231FB98151184E37EF5626A85224925AC03F23* L_27 = ___3_verifyAction;
		uint8_t L_28 = V_1;
		uint32_t L_29 = V_2;
		NullCheck(L_27);
		bool L_30;
		L_30 = VerifyUnionAction_Invoke_mF8A06007D2423423954038A65C411972E1DF6B3B_inline(L_27, __this, L_28, L_29, NULL);
		return L_30;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Verifier_VerifyBuffer_m3DB092B9C098476FF3A74E39671C11A2FF4ABAFE (Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864* __this, String_t* ___0_identifier, bool ___1_sizePrefixed, VerifyTableAction_t4EE7F0547499A39B8176F648F806406AB26487AB* ___2_verifyAction, const RuntimeMethod* method) 
{
	uint32_t V_0 = 0;
	{
		Verifier_set_depth_m996F6606AC8B244E1D3AB94C5C32C1934B47A7C7_inline(__this, 0, NULL);
		Verifier_set_numTables_mFD577C7B868B6ACE51DCAFDBE879183BBE9B08B4_inline(__this, 0, NULL);
		ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* L_0 = __this->___verifier_buffer;
		NullCheck(L_0);
		int32_t L_1;
		L_1 = ByteBuffer_get_Position_mAEC84EDE8EECB180F1904E4E10DFE6BF923A3F8D_inline(L_0, NULL);
		V_0 = L_1;
		bool L_2 = ___1_sizePrefixed;
		if (!L_2)
		{
			goto IL_006a;
		}
	}
	{
		ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* L_3 = __this->___verifier_buffer;
		NullCheck(L_3);
		int32_t L_4;
		L_4 = ByteBuffer_get_Position_mAEC84EDE8EECB180F1904E4E10DFE6BF923A3F8D_inline(L_3, NULL);
		V_0 = ((int32_t)il2cpp_codegen_add(L_4, 4));
		ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* L_5 = __this->___verifier_buffer;
		NullCheck(L_5);
		int32_t L_6;
		L_6 = ByteBuffer_get_Position_mAEC84EDE8EECB180F1904E4E10DFE6BF923A3F8D_inline(L_5, NULL);
		bool L_7;
		L_7 = Verifier_CheckScalar_m9799566886A1AC4D9A2A82F5093B0BA1FC8BF735(__this, L_6, ((int64_t)4), NULL);
		if (L_7)
		{
			goto IL_0042;
		}
	}
	{
		return (bool)0;
	}

IL_0042:
	{
		ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* L_8 = __this->___verifier_buffer;
		ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* L_9 = __this->___verifier_buffer;
		NullCheck(L_9);
		int32_t L_10;
		L_10 = ByteBuffer_get_Position_mAEC84EDE8EECB180F1904E4E10DFE6BF923A3F8D_inline(L_9, NULL);
		uint32_t L_11;
		L_11 = Verifier_ReadUOffsetT_mFA082679C617C5C8424CDDFE3965438AB7DE85B1(__this, L_8, L_10, NULL);
		ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* L_12 = __this->___verifier_buffer;
		NullCheck(L_12);
		int32_t L_13;
		L_13 = ByteBuffer_get_Length_mD83481CCE00F09128DBAC206A094C628E635BA8B(L_12, NULL);
		uint32_t L_14 = V_0;
		if ((((int32_t)L_11) == ((int32_t)((int32_t)il2cpp_codegen_subtract(L_13, (int32_t)L_14)))))
		{
			goto IL_006a;
		}
	}
	{
		return (bool)0;
	}

IL_006a:
	{
		String_t* L_15 = ___0_identifier;
		uint32_t L_16 = V_0;
		VerifyTableAction_t4EE7F0547499A39B8176F648F806406AB26487AB* L_17 = ___2_verifyAction;
		bool L_18;
		L_18 = Verifier_CheckBufferFromStart_m30D3F098F0258380253601997A05C0C0E55569F4(__this, L_15, L_16, L_17, NULL);
		return L_18;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void StringOffset__ctor_m52D945D231E3E3700A8C667AB97AE4D0A10A27BB (StringOffset_t5F8A2A2DB7065B675FCE7E3337C174214CC4FB4E* __this, int32_t ___0_value, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = ___0_value;
		__this->___Value = L_0;
		return;
	}
}
IL2CPP_EXTERN_C  void StringOffset__ctor_m52D945D231E3E3700A8C667AB97AE4D0A10A27BB_AdjustorThunk (RuntimeObject* __this, int32_t ___0_value, const RuntimeMethod* method)
{
	StringOffset_t5F8A2A2DB7065B675FCE7E3337C174214CC4FB4E* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<StringOffset_t5F8A2A2DB7065B675FCE7E3337C174214CC4FB4E*>(__this + _offset);
	StringOffset__ctor_m52D945D231E3E3700A8C667AB97AE4D0A10A27BB_inline(_thisAdjusted, ___0_value, method);
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VectorOffset__ctor_mACFDC02BEA6B5322F5E78EA74CE311D3D313B736 (VectorOffset_t25D49B46B3BAE5ABE18C23B5FF581A20699F96F4* __this, int32_t ___0_value, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = ___0_value;
		__this->___Value = L_0;
		return;
	}
}
IL2CPP_EXTERN_C  void VectorOffset__ctor_mACFDC02BEA6B5322F5E78EA74CE311D3D313B736_AdjustorThunk (RuntimeObject* __this, int32_t ___0_value, const RuntimeMethod* method)
{
	VectorOffset_t25D49B46B3BAE5ABE18C23B5FF581A20699F96F4* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<VectorOffset_t25D49B46B3BAE5ABE18C23B5FF581A20699F96F4*>(__this + _offset);
	VectorOffset__ctor_mACFDC02BEA6B5322F5E78EA74CE311D3D313B736_inline(_thisAdjusted, ___0_value, method);
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C void Struct_t80ED167B1083AFA71911B4732EFBAC86E1D17CA0_marshal_pinvoke(const Struct_t80ED167B1083AFA71911B4732EFBAC86E1D17CA0& unmarshaled, Struct_t80ED167B1083AFA71911B4732EFBAC86E1D17CA0_marshaled_pinvoke& marshaled)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Struct_t80ED167B1083AFA71911B4732EFBAC86E1D17CA0_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Struct_t80ED167B1083AFA71911B4732EFBAC86E1D17CA0____U3CbbU3Ek__BackingField_FieldInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Exception_t* ___U3CbbU3Ek__BackingFieldException = il2cpp_codegen_get_marshal_directive_exception("Cannot marshal field '%s' of type '%s': Reference type field marshaling is not supported.", Struct_t80ED167B1083AFA71911B4732EFBAC86E1D17CA0____U3CbbU3Ek__BackingField_FieldInfo_var, Struct_t80ED167B1083AFA71911B4732EFBAC86E1D17CA0_0_0_0_var);
	IL2CPP_RAISE_MANAGED_EXCEPTION(___U3CbbU3Ek__BackingFieldException, NULL);
}
IL2CPP_EXTERN_C void Struct_t80ED167B1083AFA71911B4732EFBAC86E1D17CA0_marshal_pinvoke_back(const Struct_t80ED167B1083AFA71911B4732EFBAC86E1D17CA0_marshaled_pinvoke& marshaled, Struct_t80ED167B1083AFA71911B4732EFBAC86E1D17CA0& unmarshaled)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Struct_t80ED167B1083AFA71911B4732EFBAC86E1D17CA0_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Struct_t80ED167B1083AFA71911B4732EFBAC86E1D17CA0____U3CbbU3Ek__BackingField_FieldInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Exception_t* ___U3CbbU3Ek__BackingFieldException = il2cpp_codegen_get_marshal_directive_exception("Cannot marshal field '%s' of type '%s': Reference type field marshaling is not supported.", Struct_t80ED167B1083AFA71911B4732EFBAC86E1D17CA0____U3CbbU3Ek__BackingField_FieldInfo_var, Struct_t80ED167B1083AFA71911B4732EFBAC86E1D17CA0_0_0_0_var);
	IL2CPP_RAISE_MANAGED_EXCEPTION(___U3CbbU3Ek__BackingFieldException, NULL);
}
IL2CPP_EXTERN_C void Struct_t80ED167B1083AFA71911B4732EFBAC86E1D17CA0_marshal_pinvoke_cleanup(Struct_t80ED167B1083AFA71911B4732EFBAC86E1D17CA0_marshaled_pinvoke& marshaled)
{
}
IL2CPP_EXTERN_C void Struct_t80ED167B1083AFA71911B4732EFBAC86E1D17CA0_marshal_com(const Struct_t80ED167B1083AFA71911B4732EFBAC86E1D17CA0& unmarshaled, Struct_t80ED167B1083AFA71911B4732EFBAC86E1D17CA0_marshaled_com& marshaled)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Struct_t80ED167B1083AFA71911B4732EFBAC86E1D17CA0_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Struct_t80ED167B1083AFA71911B4732EFBAC86E1D17CA0____U3CbbU3Ek__BackingField_FieldInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Exception_t* ___U3CbbU3Ek__BackingFieldException = il2cpp_codegen_get_marshal_directive_exception("Cannot marshal field '%s' of type '%s': Reference type field marshaling is not supported.", Struct_t80ED167B1083AFA71911B4732EFBAC86E1D17CA0____U3CbbU3Ek__BackingField_FieldInfo_var, Struct_t80ED167B1083AFA71911B4732EFBAC86E1D17CA0_0_0_0_var);
	IL2CPP_RAISE_MANAGED_EXCEPTION(___U3CbbU3Ek__BackingFieldException, NULL);
}
IL2CPP_EXTERN_C void Struct_t80ED167B1083AFA71911B4732EFBAC86E1D17CA0_marshal_com_back(const Struct_t80ED167B1083AFA71911B4732EFBAC86E1D17CA0_marshaled_com& marshaled, Struct_t80ED167B1083AFA71911B4732EFBAC86E1D17CA0& unmarshaled)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Struct_t80ED167B1083AFA71911B4732EFBAC86E1D17CA0_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Struct_t80ED167B1083AFA71911B4732EFBAC86E1D17CA0____U3CbbU3Ek__BackingField_FieldInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Exception_t* ___U3CbbU3Ek__BackingFieldException = il2cpp_codegen_get_marshal_directive_exception("Cannot marshal field '%s' of type '%s': Reference type field marshaling is not supported.", Struct_t80ED167B1083AFA71911B4732EFBAC86E1D17CA0____U3CbbU3Ek__BackingField_FieldInfo_var, Struct_t80ED167B1083AFA71911B4732EFBAC86E1D17CA0_0_0_0_var);
	IL2CPP_RAISE_MANAGED_EXCEPTION(___U3CbbU3Ek__BackingFieldException, NULL);
}
IL2CPP_EXTERN_C void Struct_t80ED167B1083AFA71911B4732EFBAC86E1D17CA0_marshal_com_cleanup(Struct_t80ED167B1083AFA71911B4732EFBAC86E1D17CA0_marshaled_com& marshaled)
{
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Struct_get_bb_pos_m0DF50405A8A0FA6FCFB3FEAFCB5F7E887E3C53DB (Struct_t80ED167B1083AFA71911B4732EFBAC86E1D17CA0* __this, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = __this->___U3Cbb_posU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_EXTERN_C  int32_t Struct_get_bb_pos_m0DF50405A8A0FA6FCFB3FEAFCB5F7E887E3C53DB_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	Struct_t80ED167B1083AFA71911B4732EFBAC86E1D17CA0* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<Struct_t80ED167B1083AFA71911B4732EFBAC86E1D17CA0*>(__this + _offset);
	int32_t _returnValue;
	_returnValue = Struct_get_bb_pos_m0DF50405A8A0FA6FCFB3FEAFCB5F7E887E3C53DB_inline(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Struct_set_bb_pos_mD303E870401EB784D89A1785AEA7441708DABDEB (Struct_t80ED167B1083AFA71911B4732EFBAC86E1D17CA0* __this, int32_t ___0_value, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = ___0_value;
		__this->___U3Cbb_posU3Ek__BackingField = L_0;
		return;
	}
}
IL2CPP_EXTERN_C  void Struct_set_bb_pos_mD303E870401EB784D89A1785AEA7441708DABDEB_AdjustorThunk (RuntimeObject* __this, int32_t ___0_value, const RuntimeMethod* method)
{
	Struct_t80ED167B1083AFA71911B4732EFBAC86E1D17CA0* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<Struct_t80ED167B1083AFA71911B4732EFBAC86E1D17CA0*>(__this + _offset);
	Struct_set_bb_pos_mD303E870401EB784D89A1785AEA7441708DABDEB_inline(_thisAdjusted, ___0_value, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* Struct_get_bb_m023CCFFF470E9BBBFC4B198B873601E6E8ED3B7E (Struct_t80ED167B1083AFA71911B4732EFBAC86E1D17CA0* __this, const RuntimeMethod* method) 
{
	{
		ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* L_0 = __this->___U3CbbU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_EXTERN_C  ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* Struct_get_bb_m023CCFFF470E9BBBFC4B198B873601E6E8ED3B7E_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	Struct_t80ED167B1083AFA71911B4732EFBAC86E1D17CA0* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<Struct_t80ED167B1083AFA71911B4732EFBAC86E1D17CA0*>(__this + _offset);
	ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* _returnValue;
	_returnValue = Struct_get_bb_m023CCFFF470E9BBBFC4B198B873601E6E8ED3B7E_inline(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Struct_set_bb_mE86F490DC850F169136E30896C865534FB72248D (Struct_t80ED167B1083AFA71911B4732EFBAC86E1D17CA0* __this, ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* ___0_value, const RuntimeMethod* method) 
{
	{
		ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* L_0 = ___0_value;
		__this->___U3CbbU3Ek__BackingField = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___U3CbbU3Ek__BackingField), (void*)L_0);
		return;
	}
}
IL2CPP_EXTERN_C  void Struct_set_bb_mE86F490DC850F169136E30896C865534FB72248D_AdjustorThunk (RuntimeObject* __this, ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* ___0_value, const RuntimeMethod* method)
{
	Struct_t80ED167B1083AFA71911B4732EFBAC86E1D17CA0* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<Struct_t80ED167B1083AFA71911B4732EFBAC86E1D17CA0*>(__this + _offset);
	Struct_set_bb_mE86F490DC850F169136E30896C865534FB72248D_inline(_thisAdjusted, ___0_value, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Struct__ctor_m2B636C66F813B59582DB736FD6BCC25E84B021E3 (Struct_t80ED167B1083AFA71911B4732EFBAC86E1D17CA0* __this, int32_t ___0__i, ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* ___1__bb, const RuntimeMethod* method) 
{
	{
		il2cpp_codegen_initobj(__this, sizeof(Struct_t80ED167B1083AFA71911B4732EFBAC86E1D17CA0));
		ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* L_0 = ___1__bb;
		Struct_set_bb_mE86F490DC850F169136E30896C865534FB72248D_inline(__this, L_0, NULL);
		int32_t L_1 = ___0__i;
		Struct_set_bb_pos_mD303E870401EB784D89A1785AEA7441708DABDEB_inline(__this, L_1, NULL);
		return;
	}
}
IL2CPP_EXTERN_C  void Struct__ctor_m2B636C66F813B59582DB736FD6BCC25E84B021E3_AdjustorThunk (RuntimeObject* __this, int32_t ___0__i, ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* ___1__bb, const RuntimeMethod* method)
{
	Struct_t80ED167B1083AFA71911B4732EFBAC86E1D17CA0* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<Struct_t80ED167B1083AFA71911B4732EFBAC86E1D17CA0*>(__this + _offset);
	Struct__ctor_m2B636C66F813B59582DB736FD6BCC25E84B021E3(_thisAdjusted, ___0__i, ___1__bb, method);
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C void Table_t2274EC2D38DA186BEAB1F75E04F8401533DB9815_marshal_pinvoke(const Table_t2274EC2D38DA186BEAB1F75E04F8401533DB9815& unmarshaled, Table_t2274EC2D38DA186BEAB1F75E04F8401533DB9815_marshaled_pinvoke& marshaled)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Table_t2274EC2D38DA186BEAB1F75E04F8401533DB9815_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Table_t2274EC2D38DA186BEAB1F75E04F8401533DB9815____U3CbbU3Ek__BackingField_FieldInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Exception_t* ___U3CbbU3Ek__BackingFieldException = il2cpp_codegen_get_marshal_directive_exception("Cannot marshal field '%s' of type '%s': Reference type field marshaling is not supported.", Table_t2274EC2D38DA186BEAB1F75E04F8401533DB9815____U3CbbU3Ek__BackingField_FieldInfo_var, Table_t2274EC2D38DA186BEAB1F75E04F8401533DB9815_0_0_0_var);
	IL2CPP_RAISE_MANAGED_EXCEPTION(___U3CbbU3Ek__BackingFieldException, NULL);
}
IL2CPP_EXTERN_C void Table_t2274EC2D38DA186BEAB1F75E04F8401533DB9815_marshal_pinvoke_back(const Table_t2274EC2D38DA186BEAB1F75E04F8401533DB9815_marshaled_pinvoke& marshaled, Table_t2274EC2D38DA186BEAB1F75E04F8401533DB9815& unmarshaled)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Table_t2274EC2D38DA186BEAB1F75E04F8401533DB9815_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Table_t2274EC2D38DA186BEAB1F75E04F8401533DB9815____U3CbbU3Ek__BackingField_FieldInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Exception_t* ___U3CbbU3Ek__BackingFieldException = il2cpp_codegen_get_marshal_directive_exception("Cannot marshal field '%s' of type '%s': Reference type field marshaling is not supported.", Table_t2274EC2D38DA186BEAB1F75E04F8401533DB9815____U3CbbU3Ek__BackingField_FieldInfo_var, Table_t2274EC2D38DA186BEAB1F75E04F8401533DB9815_0_0_0_var);
	IL2CPP_RAISE_MANAGED_EXCEPTION(___U3CbbU3Ek__BackingFieldException, NULL);
}
IL2CPP_EXTERN_C void Table_t2274EC2D38DA186BEAB1F75E04F8401533DB9815_marshal_pinvoke_cleanup(Table_t2274EC2D38DA186BEAB1F75E04F8401533DB9815_marshaled_pinvoke& marshaled)
{
}
IL2CPP_EXTERN_C void Table_t2274EC2D38DA186BEAB1F75E04F8401533DB9815_marshal_com(const Table_t2274EC2D38DA186BEAB1F75E04F8401533DB9815& unmarshaled, Table_t2274EC2D38DA186BEAB1F75E04F8401533DB9815_marshaled_com& marshaled)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Table_t2274EC2D38DA186BEAB1F75E04F8401533DB9815_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Table_t2274EC2D38DA186BEAB1F75E04F8401533DB9815____U3CbbU3Ek__BackingField_FieldInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Exception_t* ___U3CbbU3Ek__BackingFieldException = il2cpp_codegen_get_marshal_directive_exception("Cannot marshal field '%s' of type '%s': Reference type field marshaling is not supported.", Table_t2274EC2D38DA186BEAB1F75E04F8401533DB9815____U3CbbU3Ek__BackingField_FieldInfo_var, Table_t2274EC2D38DA186BEAB1F75E04F8401533DB9815_0_0_0_var);
	IL2CPP_RAISE_MANAGED_EXCEPTION(___U3CbbU3Ek__BackingFieldException, NULL);
}
IL2CPP_EXTERN_C void Table_t2274EC2D38DA186BEAB1F75E04F8401533DB9815_marshal_com_back(const Table_t2274EC2D38DA186BEAB1F75E04F8401533DB9815_marshaled_com& marshaled, Table_t2274EC2D38DA186BEAB1F75E04F8401533DB9815& unmarshaled)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Table_t2274EC2D38DA186BEAB1F75E04F8401533DB9815_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Table_t2274EC2D38DA186BEAB1F75E04F8401533DB9815____U3CbbU3Ek__BackingField_FieldInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Exception_t* ___U3CbbU3Ek__BackingFieldException = il2cpp_codegen_get_marshal_directive_exception("Cannot marshal field '%s' of type '%s': Reference type field marshaling is not supported.", Table_t2274EC2D38DA186BEAB1F75E04F8401533DB9815____U3CbbU3Ek__BackingField_FieldInfo_var, Table_t2274EC2D38DA186BEAB1F75E04F8401533DB9815_0_0_0_var);
	IL2CPP_RAISE_MANAGED_EXCEPTION(___U3CbbU3Ek__BackingFieldException, NULL);
}
IL2CPP_EXTERN_C void Table_t2274EC2D38DA186BEAB1F75E04F8401533DB9815_marshal_com_cleanup(Table_t2274EC2D38DA186BEAB1F75E04F8401533DB9815_marshaled_com& marshaled)
{
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Table_get_bb_pos_m384022E2F07A268997C4AD3BF8344A8CE72E71C6 (Table_t2274EC2D38DA186BEAB1F75E04F8401533DB9815* __this, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = __this->___U3Cbb_posU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_EXTERN_C  int32_t Table_get_bb_pos_m384022E2F07A268997C4AD3BF8344A8CE72E71C6_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	Table_t2274EC2D38DA186BEAB1F75E04F8401533DB9815* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<Table_t2274EC2D38DA186BEAB1F75E04F8401533DB9815*>(__this + _offset);
	int32_t _returnValue;
	_returnValue = Table_get_bb_pos_m384022E2F07A268997C4AD3BF8344A8CE72E71C6_inline(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Table_set_bb_pos_m7ECC7E46BB126F475011359342D37F4703F9BA14 (Table_t2274EC2D38DA186BEAB1F75E04F8401533DB9815* __this, int32_t ___0_value, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = ___0_value;
		__this->___U3Cbb_posU3Ek__BackingField = L_0;
		return;
	}
}
IL2CPP_EXTERN_C  void Table_set_bb_pos_m7ECC7E46BB126F475011359342D37F4703F9BA14_AdjustorThunk (RuntimeObject* __this, int32_t ___0_value, const RuntimeMethod* method)
{
	Table_t2274EC2D38DA186BEAB1F75E04F8401533DB9815* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<Table_t2274EC2D38DA186BEAB1F75E04F8401533DB9815*>(__this + _offset);
	Table_set_bb_pos_m7ECC7E46BB126F475011359342D37F4703F9BA14_inline(_thisAdjusted, ___0_value, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* Table_get_bb_mA038E6852549861A3E7346E88A0AE51EF06EF928 (Table_t2274EC2D38DA186BEAB1F75E04F8401533DB9815* __this, const RuntimeMethod* method) 
{
	{
		ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* L_0 = __this->___U3CbbU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_EXTERN_C  ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* Table_get_bb_mA038E6852549861A3E7346E88A0AE51EF06EF928_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	Table_t2274EC2D38DA186BEAB1F75E04F8401533DB9815* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<Table_t2274EC2D38DA186BEAB1F75E04F8401533DB9815*>(__this + _offset);
	ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* _returnValue;
	_returnValue = Table_get_bb_mA038E6852549861A3E7346E88A0AE51EF06EF928_inline(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Table_set_bb_m01B9D524A5880128E16440DCD5D7992325FBD71C (Table_t2274EC2D38DA186BEAB1F75E04F8401533DB9815* __this, ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* ___0_value, const RuntimeMethod* method) 
{
	{
		ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* L_0 = ___0_value;
		__this->___U3CbbU3Ek__BackingField = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___U3CbbU3Ek__BackingField), (void*)L_0);
		return;
	}
}
IL2CPP_EXTERN_C  void Table_set_bb_m01B9D524A5880128E16440DCD5D7992325FBD71C_AdjustorThunk (RuntimeObject* __this, ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* ___0_value, const RuntimeMethod* method)
{
	Table_t2274EC2D38DA186BEAB1F75E04F8401533DB9815* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<Table_t2274EC2D38DA186BEAB1F75E04F8401533DB9815*>(__this + _offset);
	Table_set_bb_m01B9D524A5880128E16440DCD5D7992325FBD71C_inline(_thisAdjusted, ___0_value, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Table__ctor_m0FAB6920DFC3116EC9552DD7237AB6E84BA5CA31 (Table_t2274EC2D38DA186BEAB1F75E04F8401533DB9815* __this, int32_t ___0__i, ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* ___1__bb, const RuntimeMethod* method) 
{
	{
		il2cpp_codegen_initobj(__this, sizeof(Table_t2274EC2D38DA186BEAB1F75E04F8401533DB9815));
		ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* L_0 = ___1__bb;
		Table_set_bb_m01B9D524A5880128E16440DCD5D7992325FBD71C_inline(__this, L_0, NULL);
		int32_t L_1 = ___0__i;
		Table_set_bb_pos_m7ECC7E46BB126F475011359342D37F4703F9BA14_inline(__this, L_1, NULL);
		return;
	}
}
IL2CPP_EXTERN_C  void Table__ctor_m0FAB6920DFC3116EC9552DD7237AB6E84BA5CA31_AdjustorThunk (RuntimeObject* __this, int32_t ___0__i, ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* ___1__bb, const RuntimeMethod* method)
{
	Table_t2274EC2D38DA186BEAB1F75E04F8401533DB9815* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<Table_t2274EC2D38DA186BEAB1F75E04F8401533DB9815*>(__this + _offset);
	Table__ctor_m0FAB6920DFC3116EC9552DD7237AB6E84BA5CA31(_thisAdjusted, ___0__i, ___1__bb, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Table___offset_mD41A3FDF5139E05F3EA1BB70EED7C48D593979D6 (Table_t2274EC2D38DA186BEAB1F75E04F8401533DB9815* __this, int32_t ___0_vtableOffset, const RuntimeMethod* method) 
{
	int32_t V_0 = 0;
	{
		int32_t L_0;
		L_0 = Table_get_bb_pos_m384022E2F07A268997C4AD3BF8344A8CE72E71C6_inline(__this, NULL);
		ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* L_1;
		L_1 = Table_get_bb_mA038E6852549861A3E7346E88A0AE51EF06EF928_inline(__this, NULL);
		int32_t L_2;
		L_2 = Table_get_bb_pos_m384022E2F07A268997C4AD3BF8344A8CE72E71C6_inline(__this, NULL);
		NullCheck(L_1);
		int32_t L_3;
		L_3 = ByteBuffer_GetInt_mD2F94B1EFC4E1B48E6A25E9FE27A36CB691F93C7(L_1, L_2, NULL);
		V_0 = ((int32_t)il2cpp_codegen_subtract(L_0, L_3));
		int32_t L_4 = ___0_vtableOffset;
		ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* L_5;
		L_5 = Table_get_bb_mA038E6852549861A3E7346E88A0AE51EF06EF928_inline(__this, NULL);
		int32_t L_6 = V_0;
		NullCheck(L_5);
		int16_t L_7;
		L_7 = ByteBuffer_GetShort_m3ED68273C4B0ABA97718B68F439D042C758D084B(L_5, L_6, NULL);
		if ((((int32_t)L_4) < ((int32_t)L_7)))
		{
			goto IL_002a;
		}
	}
	{
		return 0;
	}

IL_002a:
	{
		ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* L_8;
		L_8 = Table_get_bb_mA038E6852549861A3E7346E88A0AE51EF06EF928_inline(__this, NULL);
		int32_t L_9 = V_0;
		int32_t L_10 = ___0_vtableOffset;
		NullCheck(L_8);
		int16_t L_11;
		L_11 = ByteBuffer_GetShort_m3ED68273C4B0ABA97718B68F439D042C758D084B(L_8, ((int32_t)il2cpp_codegen_add(L_9, L_10)), NULL);
		return L_11;
	}
}
IL2CPP_EXTERN_C  int32_t Table___offset_mD41A3FDF5139E05F3EA1BB70EED7C48D593979D6_AdjustorThunk (RuntimeObject* __this, int32_t ___0_vtableOffset, const RuntimeMethod* method)
{
	Table_t2274EC2D38DA186BEAB1F75E04F8401533DB9815* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<Table_t2274EC2D38DA186BEAB1F75E04F8401533DB9815*>(__this + _offset);
	int32_t _returnValue;
	_returnValue = Table___offset_mD41A3FDF5139E05F3EA1BB70EED7C48D593979D6(_thisAdjusted, ___0_vtableOffset, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Table___indirect_mB18E00AFF82B4E55117FE556350A8D99ABD565C1 (Table_t2274EC2D38DA186BEAB1F75E04F8401533DB9815* __this, int32_t ___0_offset, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = ___0_offset;
		ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* L_1;
		L_1 = Table_get_bb_mA038E6852549861A3E7346E88A0AE51EF06EF928_inline(__this, NULL);
		int32_t L_2 = ___0_offset;
		NullCheck(L_1);
		int32_t L_3;
		L_3 = ByteBuffer_GetInt_mD2F94B1EFC4E1B48E6A25E9FE27A36CB691F93C7(L_1, L_2, NULL);
		return ((int32_t)il2cpp_codegen_add(L_0, L_3));
	}
}
IL2CPP_EXTERN_C  int32_t Table___indirect_mB18E00AFF82B4E55117FE556350A8D99ABD565C1_AdjustorThunk (RuntimeObject* __this, int32_t ___0_offset, const RuntimeMethod* method)
{
	Table_t2274EC2D38DA186BEAB1F75E04F8401533DB9815* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<Table_t2274EC2D38DA186BEAB1F75E04F8401533DB9815*>(__this + _offset);
	int32_t _returnValue;
	_returnValue = Table___indirect_mB18E00AFF82B4E55117FE556350A8D99ABD565C1(_thisAdjusted, ___0_offset, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Table___indirect_m758EB41423E5A20EC6F796196BE58FE1226C7913 (int32_t ___0_offset, ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* ___1_bb, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = ___0_offset;
		ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* L_1 = ___1_bb;
		int32_t L_2 = ___0_offset;
		NullCheck(L_1);
		int32_t L_3;
		L_3 = ByteBuffer_GetInt_mD2F94B1EFC4E1B48E6A25E9FE27A36CB691F93C7(L_1, L_2, NULL);
		return ((int32_t)il2cpp_codegen_add(L_0, L_3));
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* Table___string_mFB43D8816C093699854F13DC421C3DD6E78BEAF3 (Table_t2274EC2D38DA186BEAB1F75E04F8401533DB9815* __this, int32_t ___0_offset, const RuntimeMethod* method) 
{
	int32_t V_0 = 0;
	int32_t V_1 = 0;
	int32_t V_2 = 0;
	{
		ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* L_0;
		L_0 = Table_get_bb_mA038E6852549861A3E7346E88A0AE51EF06EF928_inline(__this, NULL);
		int32_t L_1 = ___0_offset;
		NullCheck(L_0);
		int32_t L_2;
		L_2 = ByteBuffer_GetInt_mD2F94B1EFC4E1B48E6A25E9FE27A36CB691F93C7(L_0, L_1, NULL);
		V_0 = L_2;
		int32_t L_3 = V_0;
		if (L_3)
		{
			goto IL_0012;
		}
	}
	{
		return (String_t*)NULL;
	}

IL_0012:
	{
		int32_t L_4 = ___0_offset;
		int32_t L_5 = V_0;
		___0_offset = ((int32_t)il2cpp_codegen_add(L_4, L_5));
		ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* L_6;
		L_6 = Table_get_bb_mA038E6852549861A3E7346E88A0AE51EF06EF928_inline(__this, NULL);
		int32_t L_7 = ___0_offset;
		NullCheck(L_6);
		int32_t L_8;
		L_8 = ByteBuffer_GetInt_mD2F94B1EFC4E1B48E6A25E9FE27A36CB691F93C7(L_6, L_7, NULL);
		V_1 = L_8;
		int32_t L_9 = ___0_offset;
		V_2 = ((int32_t)il2cpp_codegen_add(L_9, 4));
		ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* L_10;
		L_10 = Table_get_bb_mA038E6852549861A3E7346E88A0AE51EF06EF928_inline(__this, NULL);
		int32_t L_11 = V_2;
		int32_t L_12 = V_1;
		NullCheck(L_10);
		String_t* L_13;
		L_13 = ByteBuffer_GetStringUTF8_m54FDA99A7BCE5B71C55BCE816CE73854FFFB0EAF(L_10, L_11, L_12, NULL);
		return L_13;
	}
}
IL2CPP_EXTERN_C  String_t* Table___string_mFB43D8816C093699854F13DC421C3DD6E78BEAF3_AdjustorThunk (RuntimeObject* __this, int32_t ___0_offset, const RuntimeMethod* method)
{
	Table_t2274EC2D38DA186BEAB1F75E04F8401533DB9815* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<Table_t2274EC2D38DA186BEAB1F75E04F8401533DB9815*>(__this + _offset);
	String_t* _returnValue;
	_returnValue = Table___string_mFB43D8816C093699854F13DC421C3DD6E78BEAF3(_thisAdjusted, ___0_offset, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Table___vector_len_m3B0E26D29F11FD1CF988A59732A1B33D1DAE393D (Table_t2274EC2D38DA186BEAB1F75E04F8401533DB9815* __this, int32_t ___0_offset, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = ___0_offset;
		int32_t L_1;
		L_1 = Table_get_bb_pos_m384022E2F07A268997C4AD3BF8344A8CE72E71C6_inline(__this, NULL);
		___0_offset = ((int32_t)il2cpp_codegen_add(L_0, L_1));
		int32_t L_2 = ___0_offset;
		ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* L_3;
		L_3 = Table_get_bb_mA038E6852549861A3E7346E88A0AE51EF06EF928_inline(__this, NULL);
		int32_t L_4 = ___0_offset;
		NullCheck(L_3);
		int32_t L_5;
		L_5 = ByteBuffer_GetInt_mD2F94B1EFC4E1B48E6A25E9FE27A36CB691F93C7(L_3, L_4, NULL);
		___0_offset = ((int32_t)il2cpp_codegen_add(L_2, L_5));
		ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* L_6;
		L_6 = Table_get_bb_mA038E6852549861A3E7346E88A0AE51EF06EF928_inline(__this, NULL);
		int32_t L_7 = ___0_offset;
		NullCheck(L_6);
		int32_t L_8;
		L_8 = ByteBuffer_GetInt_mD2F94B1EFC4E1B48E6A25E9FE27A36CB691F93C7(L_6, L_7, NULL);
		return L_8;
	}
}
IL2CPP_EXTERN_C  int32_t Table___vector_len_m3B0E26D29F11FD1CF988A59732A1B33D1DAE393D_AdjustorThunk (RuntimeObject* __this, int32_t ___0_offset, const RuntimeMethod* method)
{
	Table_t2274EC2D38DA186BEAB1F75E04F8401533DB9815* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<Table_t2274EC2D38DA186BEAB1F75E04F8401533DB9815*>(__this + _offset);
	int32_t _returnValue;
	_returnValue = Table___vector_len_m3B0E26D29F11FD1CF988A59732A1B33D1DAE393D(_thisAdjusted, ___0_offset, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Table___vector_m7F98F7FF9A2160084141781A48E338392BF4E780 (Table_t2274EC2D38DA186BEAB1F75E04F8401533DB9815* __this, int32_t ___0_offset, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = ___0_offset;
		int32_t L_1;
		L_1 = Table_get_bb_pos_m384022E2F07A268997C4AD3BF8344A8CE72E71C6_inline(__this, NULL);
		___0_offset = ((int32_t)il2cpp_codegen_add(L_0, L_1));
		int32_t L_2 = ___0_offset;
		ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* L_3;
		L_3 = Table_get_bb_mA038E6852549861A3E7346E88A0AE51EF06EF928_inline(__this, NULL);
		int32_t L_4 = ___0_offset;
		NullCheck(L_3);
		int32_t L_5;
		L_5 = ByteBuffer_GetInt_mD2F94B1EFC4E1B48E6A25E9FE27A36CB691F93C7(L_3, L_4, NULL);
		return ((int32_t)il2cpp_codegen_add(((int32_t)il2cpp_codegen_add(L_2, L_5)), 4));
	}
}
IL2CPP_EXTERN_C  int32_t Table___vector_m7F98F7FF9A2160084141781A48E338392BF4E780_AdjustorThunk (RuntimeObject* __this, int32_t ___0_offset, const RuntimeMethod* method)
{
	Table_t2274EC2D38DA186BEAB1F75E04F8401533DB9815* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<Table_t2274EC2D38DA186BEAB1F75E04F8401533DB9815*>(__this + _offset);
	int32_t _returnValue;
	_returnValue = Table___vector_m7F98F7FF9A2160084141781A48E338392BF4E780(_thisAdjusted, ___0_offset, method);
	return _returnValue;
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t ByteBufferAllocator_get_Length_mC038E63A664724F0965D2B7B57899F1BDB1DCE2A_inline (ByteBufferAllocator_tCC19A5DCD32535F213BA3EDECBB08A7F9EFD6185* __this, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = __this->___U3CLengthU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void ByteBufferAllocator_set_Length_m17C45CA27C651A79001AED91E6AAA13B575F38C9_inline (ByteBufferAllocator_tCC19A5DCD32535F213BA3EDECBB08A7F9EFD6185* __this, int32_t ___0_value, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = ___0_value;
		__this->___U3CLengthU3Ek__BackingField = L_0;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t String_get_Length_m42625D67623FA5CC7A44D47425CE86FB946542D2_inline (String_t* __this, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = __this->____stringLength;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void BinaryPrimitives_WriteUInt16LittleEndian_mEA236B05E65D485C1934CCC155A6AF8A5C66773A_inline (Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305 ___0_destination, uint16_t ___1_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MemoryMarshal_Write_TisUInt16_tF4C148C876015C212FD72652D0B6ED8CC247A455_m1CB9CA69ED7F15E1AED66F7E98D918E1DF0CB96A_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		if (il2cpp_codegen_is_little_endian())
		{
			goto IL_000f;
		}
	}
	{
		uint16_t L_0 = ___1_value;
		uint16_t L_1;
		L_1 = BinaryPrimitives_ReverseEndianness_mDBF226C2D52CAFF6DE538F8245444B5CF87A02D0_inline(L_0, NULL);
		___1_value = L_1;
	}

IL_000f:
	{
		Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305 L_2 = ___0_destination;
		MemoryMarshal_Write_TisUInt16_tF4C148C876015C212FD72652D0B6ED8CC247A455_m1CB9CA69ED7F15E1AED66F7E98D918E1DF0CB96A_inline(L_2, (&___1_value), MemoryMarshal_Write_TisUInt16_tF4C148C876015C212FD72652D0B6ED8CC247A455_m1CB9CA69ED7F15E1AED66F7E98D918E1DF0CB96A_RuntimeMethod_var);
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void BinaryPrimitives_WriteUInt32LittleEndian_mF5A685773CF1618F130D77F5072BDB7C389793B1_inline (Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305 ___0_destination, uint32_t ___1_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MemoryMarshal_Write_TisUInt32_t1833D51FFA667B18A5AA4B8D34DE284F8495D29B_m2BD505F8011143B77CB9A048F4C97C64130CB6DB_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		if (il2cpp_codegen_is_little_endian())
		{
			goto IL_000f;
		}
	}
	{
		uint32_t L_0 = ___1_value;
		uint32_t L_1;
		L_1 = BinaryPrimitives_ReverseEndianness_mCCA2099164ECA9672968898DD996A9F04B392FFF_inline(L_0, NULL);
		___1_value = L_1;
	}

IL_000f:
	{
		Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305 L_2 = ___0_destination;
		MemoryMarshal_Write_TisUInt32_t1833D51FFA667B18A5AA4B8D34DE284F8495D29B_m2BD505F8011143B77CB9A048F4C97C64130CB6DB_inline(L_2, (&___1_value), MemoryMarshal_Write_TisUInt32_t1833D51FFA667B18A5AA4B8D34DE284F8495D29B_m2BD505F8011143B77CB9A048F4C97C64130CB6DB_RuntimeMethod_var);
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void BinaryPrimitives_WriteUInt64LittleEndian_mCC039600290A6A5201532593AF3BE095D74C1625_inline (Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305 ___0_destination, uint64_t ___1_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MemoryMarshal_Write_TisUInt64_t8F12534CC8FC4B5860F2A2CD1EE79D322E7A41AF_m369E3011D17AFC47FE8EBD775DA05E3F136D3EA3_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		if (il2cpp_codegen_is_little_endian())
		{
			goto IL_000f;
		}
	}
	{
		uint64_t L_0 = ___1_value;
		uint64_t L_1;
		L_1 = BinaryPrimitives_ReverseEndianness_mA698702D91EF4E47FF6F682E4B48F173FF376BDF_inline(L_0, NULL);
		___1_value = L_1;
	}

IL_000f:
	{
		Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305 L_2 = ___0_destination;
		MemoryMarshal_Write_TisUInt64_t8F12534CC8FC4B5860F2A2CD1EE79D322E7A41AF_m369E3011D17AFC47FE8EBD775DA05E3F136D3EA3_inline(L_2, (&___1_value), MemoryMarshal_Write_TisUInt64_t8F12534CC8FC4B5860F2A2CD1EE79D322E7A41AF_m369E3011D17AFC47FE8EBD775DA05E3F136D3EA3_RuntimeMethod_var);
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint16_t BinaryPrimitives_ReadUInt16LittleEndian_m6233B916B888350309C273E87ED2C3F787260889_inline (ReadOnlySpan_1_tA850A6C0E88ABBA37646A078ACBC24D6D5FD9B4D ___0_source, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MemoryMarshal_Read_TisUInt16_tF4C148C876015C212FD72652D0B6ED8CC247A455_mE522E06A28DD43DAED8B42666149274B433F5317_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	uint16_t V_0 = 0;
	{
		ReadOnlySpan_1_tA850A6C0E88ABBA37646A078ACBC24D6D5FD9B4D L_0 = ___0_source;
		uint16_t L_1;
		L_1 = MemoryMarshal_Read_TisUInt16_tF4C148C876015C212FD72652D0B6ED8CC247A455_mE522E06A28DD43DAED8B42666149274B433F5317_inline(L_0, MemoryMarshal_Read_TisUInt16_tF4C148C876015C212FD72652D0B6ED8CC247A455_mE522E06A28DD43DAED8B42666149274B433F5317_RuntimeMethod_var);
		V_0 = L_1;
		if (il2cpp_codegen_is_little_endian())
		{
			goto IL_0015;
		}
	}
	{
		uint16_t L_2 = V_0;
		uint16_t L_3;
		L_3 = BinaryPrimitives_ReverseEndianness_mDBF226C2D52CAFF6DE538F8245444B5CF87A02D0_inline(L_2, NULL);
		V_0 = L_3;
	}

IL_0015:
	{
		uint16_t L_4 = V_0;
		return L_4;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint32_t BinaryPrimitives_ReadUInt32LittleEndian_m1D2A6AA323C53D511E84C677D1F8F17077F3B070_inline (ReadOnlySpan_1_tA850A6C0E88ABBA37646A078ACBC24D6D5FD9B4D ___0_source, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MemoryMarshal_Read_TisUInt32_t1833D51FFA667B18A5AA4B8D34DE284F8495D29B_m56C749731FAD055AC5894D97F107FF8E5C6A13AE_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	uint32_t V_0 = 0;
	{
		ReadOnlySpan_1_tA850A6C0E88ABBA37646A078ACBC24D6D5FD9B4D L_0 = ___0_source;
		uint32_t L_1;
		L_1 = MemoryMarshal_Read_TisUInt32_t1833D51FFA667B18A5AA4B8D34DE284F8495D29B_m56C749731FAD055AC5894D97F107FF8E5C6A13AE_inline(L_0, MemoryMarshal_Read_TisUInt32_t1833D51FFA667B18A5AA4B8D34DE284F8495D29B_m56C749731FAD055AC5894D97F107FF8E5C6A13AE_RuntimeMethod_var);
		V_0 = L_1;
		if (il2cpp_codegen_is_little_endian())
		{
			goto IL_0015;
		}
	}
	{
		uint32_t L_2 = V_0;
		uint32_t L_3;
		L_3 = BinaryPrimitives_ReverseEndianness_mCCA2099164ECA9672968898DD996A9F04B392FFF_inline(L_2, NULL);
		V_0 = L_3;
	}

IL_0015:
	{
		uint32_t L_4 = V_0;
		return L_4;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint64_t BinaryPrimitives_ReadUInt64LittleEndian_m9F91B7C963E163D3064EA52D2C3A4075A33FB32B_inline (ReadOnlySpan_1_tA850A6C0E88ABBA37646A078ACBC24D6D5FD9B4D ___0_source, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MemoryMarshal_Read_TisUInt64_t8F12534CC8FC4B5860F2A2CD1EE79D322E7A41AF_m4B7CEC36F79FB7BE35EB5ADC5B3F9B03A427FAD0_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	uint64_t V_0 = 0;
	{
		ReadOnlySpan_1_tA850A6C0E88ABBA37646A078ACBC24D6D5FD9B4D L_0 = ___0_source;
		uint64_t L_1;
		L_1 = MemoryMarshal_Read_TisUInt64_t8F12534CC8FC4B5860F2A2CD1EE79D322E7A41AF_m4B7CEC36F79FB7BE35EB5ADC5B3F9B03A427FAD0_inline(L_0, MemoryMarshal_Read_TisUInt64_t8F12534CC8FC4B5860F2A2CD1EE79D322E7A41AF_m4B7CEC36F79FB7BE35EB5ADC5B3F9B03A427FAD0_RuntimeMethod_var);
		V_0 = L_1;
		if (il2cpp_codegen_is_little_endian())
		{
			goto IL_0015;
		}
	}
	{
		uint64_t L_2 = V_0;
		uint64_t L_3;
		L_3 = BinaryPrimitives_ReverseEndianness_mA698702D91EF4E47FF6F682E4B48F173FF376BDF_inline(L_2, NULL);
		V_0 = L_3;
	}

IL_0015:
	{
		uint64_t L_4 = V_0;
		return L_4;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void VectorOffset__ctor_mACFDC02BEA6B5322F5E78EA74CE311D3D313B736_inline (VectorOffset_t25D49B46B3BAE5ABE18C23B5FF581A20699F96F4* __this, int32_t ___0_value, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = ___0_value;
		__this->___Value = L_0;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool FlatBufferBuilder_get_ForceDefaults_mA0FE8A741C81C5945948228ED35D4F7E3BECC960_inline (FlatBufferBuilder_t23EDF7F7B5CFBF2A0BBCC2CCE6D14C02BE050CB0* __this, const RuntimeMethod* method) 
{
	{
		bool L_0 = __this->___U3CForceDefaultsU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void StringOffset__ctor_m52D945D231E3E3700A8C667AB97AE4D0A10A27BB_inline (StringOffset_t5F8A2A2DB7065B675FCE7E3337C174214CC4FB4E* __this, int32_t ___0_value, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = ___0_value;
		__this->___Value = L_0;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void ByteBuffer_set_Position_m0F1BC982C7D846D2F73A08D04F66D255746FCB20_inline (ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* __this, int32_t ___0_value, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = ___0_value;
		__this->____pos = L_0;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t Verifier_get_depth_m4A917A65B04FAC0E7374D664A28B7BFC089AA74E_inline (Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864* __this, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = __this->___depth_cnt;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Options_t2C469D7B05D4F50066C5316A778A04C4DF53F088* Verifier_get_options_m5D0BEA9EBE4645B980E3066F2E42B042943D87E9_inline (Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864* __this, const RuntimeMethod* method) 
{
	{
		Options_t2C469D7B05D4F50066C5316A778A04C4DF53F088* L_0 = __this->___verifier_options;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t Options_get_maxDepth_m437B08A483A59E4600A3A2B6FE3A26E3EA8B2212_inline (Options_t2C469D7B05D4F50066C5316A778A04C4DF53F088* __this, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = __this->___max_depth;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t Verifier_get_numTables_mD6123A14045007BAE82B1770EB9201594F778973_inline (Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864* __this, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = __this->___num_tables_cnt;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t Options_get_maxTables_mE2E55C408DAE7EB5059F6169F471ED14B93F2D40_inline (Options_t2C469D7B05D4F50066C5316A778A04C4DF53F088* __this, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = __this->___max_tables;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool Options_get_alignmentCheck_mC94E7A871FC44FE7505713CD0E69A9FE2BFD4EEA_inline (Options_t2C469D7B05D4F50066C5316A778A04C4DF53F088* __this, const RuntimeMethod* method) 
{
	{
		bool L_0 = __this->___alignment_check;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool Options_get_stringEndCheck_mB9217751DB5C7D10C8EDB209FC945402821A1339_inline (Options_t2C469D7B05D4F50066C5316A778A04C4DF53F088* __this, const RuntimeMethod* method) 
{
	{
		bool L_0 = __this->___string_end_check;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool VerifyTableAction_Invoke_mDD4DF94C57F6A57A87F19A8915ACD085F65FF6D4_inline (VerifyTableAction_t4EE7F0547499A39B8176F648F806406AB26487AB* __this, Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864* ___0_verifier, uint32_t ___1_tablePos, const RuntimeMethod* method) 
{
	typedef bool (*FunctionPointerType) (RuntimeObject*, Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864*, uint32_t, const RuntimeMethod*);
	return ((FunctionPointerType)__this->___invoke_impl)((Il2CppObject*)__this->___method_code, ___0_verifier, ___1_tablePos, reinterpret_cast<RuntimeMethod*>(__this->___method));
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Verifier_set_depth_m996F6606AC8B244E1D3AB94C5C32C1934B47A7C7_inline (Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864* __this, int32_t ___0_value, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = ___0_value;
		__this->___depth_cnt = L_0;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool VerifyUnionAction_Invoke_mF8A06007D2423423954038A65C411972E1DF6B3B_inline (VerifyUnionAction_t41231FB98151184E37EF5626A85224925AC03F23* __this, Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864* ___0_verifier, uint8_t ___1_typeId, uint32_t ___2_tablePos, const RuntimeMethod* method) 
{
	typedef bool (*FunctionPointerType) (RuntimeObject*, Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864*, uint8_t, uint32_t, const RuntimeMethod*);
	return ((FunctionPointerType)__this->___invoke_impl)((Il2CppObject*)__this->___method_code, ___0_verifier, ___1_typeId, ___2_tablePos, reinterpret_cast<RuntimeMethod*>(__this->___method));
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Verifier_set_numTables_mFD577C7B868B6ACE51DCAFDBE879183BBE9B08B4_inline (Verifier_tC47666C4CDBD5E94284CCC57648983C8C2734864* __this, int32_t ___0_value, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = ___0_value;
		__this->___num_tables_cnt = L_0;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t ByteBuffer_get_Position_mAEC84EDE8EECB180F1904E4E10DFE6BF923A3F8D_inline (ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* __this, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = __this->____pos;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t Struct_get_bb_pos_m0DF50405A8A0FA6FCFB3FEAFCB5F7E887E3C53DB_inline (Struct_t80ED167B1083AFA71911B4732EFBAC86E1D17CA0* __this, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = __this->___U3Cbb_posU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Struct_set_bb_pos_mD303E870401EB784D89A1785AEA7441708DABDEB_inline (Struct_t80ED167B1083AFA71911B4732EFBAC86E1D17CA0* __this, int32_t ___0_value, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = ___0_value;
		__this->___U3Cbb_posU3Ek__BackingField = L_0;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* Struct_get_bb_m023CCFFF470E9BBBFC4B198B873601E6E8ED3B7E_inline (Struct_t80ED167B1083AFA71911B4732EFBAC86E1D17CA0* __this, const RuntimeMethod* method) 
{
	{
		ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* L_0 = __this->___U3CbbU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Struct_set_bb_mE86F490DC850F169136E30896C865534FB72248D_inline (Struct_t80ED167B1083AFA71911B4732EFBAC86E1D17CA0* __this, ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* ___0_value, const RuntimeMethod* method) 
{
	{
		ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* L_0 = ___0_value;
		__this->___U3CbbU3Ek__BackingField = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___U3CbbU3Ek__BackingField), (void*)L_0);
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t Table_get_bb_pos_m384022E2F07A268997C4AD3BF8344A8CE72E71C6_inline (Table_t2274EC2D38DA186BEAB1F75E04F8401533DB9815* __this, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = __this->___U3Cbb_posU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Table_set_bb_pos_m7ECC7E46BB126F475011359342D37F4703F9BA14_inline (Table_t2274EC2D38DA186BEAB1F75E04F8401533DB9815* __this, int32_t ___0_value, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = ___0_value;
		__this->___U3Cbb_posU3Ek__BackingField = L_0;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* Table_get_bb_mA038E6852549861A3E7346E88A0AE51EF06EF928_inline (Table_t2274EC2D38DA186BEAB1F75E04F8401533DB9815* __this, const RuntimeMethod* method) 
{
	{
		ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* L_0 = __this->___U3CbbU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Table_set_bb_m01B9D524A5880128E16440DCD5D7992325FBD71C_inline (Table_t2274EC2D38DA186BEAB1F75E04F8401533DB9815* __this, ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* ___0_value, const RuntimeMethod* method) 
{
	{
		ByteBuffer_t36EE8DFD50C98FB783E760559EB165AC93D61641* L_0 = ___0_value;
		__this->___U3CbbU3Ek__BackingField = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___U3CbbU3Ek__BackingField), (void*)L_0);
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305 Memory_1_get_Span_mA0CAB13956D6FA3BBF9F9176CB647933F88E034E_gshared_inline (Memory_1_tB7CEF4416F5014E364267478CEF016A4AC5C0036* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Char_t521A6F19B456D956AF452D926C32709DC03D6B17_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&String_t_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Type_t_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305 V_0;
	memset((&V_0), 0, sizeof(V_0));
	String_t* V_1 = NULL;
	{
		int32_t L_0 = __this->____index;
		if ((((int32_t)L_0) >= ((int32_t)0)))
		{
			goto IL_0034;
		}
	}
	{
		RuntimeObject* L_1 = __this->____object;
		NullCheck(((MemoryManager_1_tB90442C8E0A1B9C0F8A3B603FD50501A1BADAC6E*)CastclassClass((RuntimeObject*)L_1, il2cpp_rgctx_data(InitializedTypeInfo(method->klass)->rgctx_data, 20))));
		Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305 L_2;
		L_2 = VirtualFuncInvoker0< Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305 >::Invoke(4, ((MemoryManager_1_tB90442C8E0A1B9C0F8A3B603FD50501A1BADAC6E*)CastclassClass((RuntimeObject*)L_1, il2cpp_rgctx_data(InitializedTypeInfo(method->klass)->rgctx_data, 20))));
		V_0 = L_2;
		int32_t L_3 = __this->____index;
		int32_t L_4 = __this->____length;
		Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305 L_5;
		L_5 = Span_1_Slice_m9D8BA8245B8DC9BFB4A4164759CBAAEAD1318CD6_inline((&V_0), ((int32_t)(L_3&((int32_t)2147483647LL))), L_4, il2cpp_rgctx_method(InitializedTypeInfo(method->klass)->rgctx_data, 22));
		return L_5;
	}

IL_0034:
	{
		RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B L_6 = { reinterpret_cast<intptr_t> (il2cpp_rgctx_type(InitializedTypeInfo(method->klass)->rgctx_data, 14)) };
		il2cpp_codegen_runtime_class_init_inline(Type_t_il2cpp_TypeInfo_var);
		Type_t* L_7;
		L_7 = Type_GetTypeFromHandle_m6062B81682F79A4D6DF2640692EE6D9987858C57(L_6, NULL);
		RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B L_8 = { reinterpret_cast<intptr_t> (Char_t521A6F19B456D956AF452D926C32709DC03D6B17_0_0_0_var) };
		Type_t* L_9;
		L_9 = Type_GetTypeFromHandle_m6062B81682F79A4D6DF2640692EE6D9987858C57(L_8, NULL);
		bool L_10;
		L_10 = Type_op_Equality_m99930A0E44E420A685FABA60E60BA1CC5FA0EBDC(L_7, L_9, NULL);
		if (!L_10)
		{
			goto IL_0089;
		}
	}
	{
		RuntimeObject* L_11 = __this->____object;
		V_1 = ((String_t*)IsInstSealed((RuntimeObject*)L_11, String_t_il2cpp_TypeInfo_var));
		String_t* L_12 = V_1;
		if (!L_12)
		{
			goto IL_0089;
		}
	}
	{
		String_t* L_13 = V_1;
		NullCheck(L_13);
		Il2CppChar* L_14;
		L_14 = String_GetRawStringData_m87BC50B7B314C055E27A28032D1003D42FDE411D(L_13, NULL);
		uint8_t* L_15;
		L_15 = il2cpp_unsafe_as_ref<uint8_t>(L_14);
		String_t* L_16 = V_1;
		NullCheck(L_16);
		int32_t L_17;
		L_17 = String_get_Length_m42625D67623FA5CC7A44D47425CE86FB946542D2_inline(L_16, NULL);
		Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305 L_18;
		memset((&L_18), 0, sizeof(L_18));
		Span_1__ctor_m947BF95D54571BF3897F96822B7A8FDA5853497B_inline((&L_18), L_15, L_17, il2cpp_rgctx_method(InitializedTypeInfo(method->klass)->rgctx_data, 25));
		V_0 = L_18;
		int32_t L_19 = __this->____index;
		int32_t L_20 = __this->____length;
		Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305 L_21;
		L_21 = Span_1_Slice_m9D8BA8245B8DC9BFB4A4164759CBAAEAD1318CD6_inline((&V_0), L_19, L_20, il2cpp_rgctx_method(InitializedTypeInfo(method->klass)->rgctx_data, 22));
		return L_21;
	}

IL_0089:
	{
		RuntimeObject* L_22 = __this->____object;
		if (!L_22)
		{
			goto IL_00b4;
		}
	}
	{
		RuntimeObject* L_23 = __this->____object;
		int32_t L_24 = __this->____index;
		int32_t L_25 = __this->____length;
		Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305 L_26;
		memset((&L_26), 0, sizeof(L_26));
		Span_1__ctor_m698EC79E2E44AFF16BA096D0861CFB129FBF8218_inline((&L_26), ((ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)Castclass((RuntimeObject*)L_23, il2cpp_rgctx_data(InitializedTypeInfo(method->klass)->rgctx_data, 0))), L_24, ((int32_t)(L_25&((int32_t)2147483647LL))), il2cpp_rgctx_method(InitializedTypeInfo(method->klass)->rgctx_data, 26));
		return L_26;
	}

IL_00b4:
	{
		il2cpp_codegen_initobj((&V_0), sizeof(Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305));
		Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305 L_27 = V_0;
		return L_27;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305 Span_1_Slice_m9D8BA8245B8DC9BFB4A4164759CBAAEAD1318CD6_gshared_inline (Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305* __this, int32_t ___0_start, int32_t ___1_length, const RuntimeMethod* method) 
{
	ByReference_1_t9C85BCCAAF8C525B6C06B07E922D8D217BE8D6FC V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		int32_t L_0 = ___0_start;
		int32_t L_1 = __this->____length;
		if ((!(((uint32_t)L_0) <= ((uint32_t)L_1))))
		{
			goto IL_0014;
		}
	}
	{
		int32_t L_2 = ___1_length;
		int32_t L_3 = __this->____length;
		int32_t L_4 = ___0_start;
		if ((!(((uint32_t)L_2) > ((uint32_t)((int32_t)il2cpp_codegen_subtract(L_3, L_4))))))
		{
			goto IL_0019;
		}
	}

IL_0014:
	{
		ThrowHelper_ThrowArgumentOutOfRangeException_mD7D90276EDCDF9394A8EA635923E3B48BB71BD56(NULL);
	}

IL_0019:
	{
		ByReference_1_t9C85BCCAAF8C525B6C06B07E922D8D217BE8D6FC L_5 = __this->____pointer;
		V_0 = L_5;
		uint8_t* L_6;
		L_6 = IL2CPP_BY_REFERENCE_GET_VALUE(uint8_t, (Il2CppByReference*)(&V_0));
		int32_t L_7 = ___0_start;
		uint8_t* L_8;
		L_8 = il2cpp_unsafe_add<uint8_t,int32_t>(L_6, L_7);
		int32_t L_9 = ___1_length;
		Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305 L_10;
		memset((&L_10), 0, sizeof(L_10));
		Span_1__ctor_m947BF95D54571BF3897F96822B7A8FDA5853497B_inline((&L_10), L_8, L_9, il2cpp_rgctx_method(InitializedTypeInfo(method->klass)->rgctx_data, 18));
		return L_10;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t Span_1_get_Length_m8E944E4954E037877A25B9FF6B901F1F901D4769_gshared_inline (Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305* __this, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = __this->____length;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305 Span_1_Slice_m720734AA48ECB663CAA0594530927B9015A64341_gshared_inline (Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305* __this, int32_t ___0_start, const RuntimeMethod* method) 
{
	ByReference_1_t9C85BCCAAF8C525B6C06B07E922D8D217BE8D6FC V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		int32_t L_0 = ___0_start;
		int32_t L_1 = __this->____length;
		if ((!(((uint32_t)L_0) > ((uint32_t)L_1))))
		{
			goto IL_000e;
		}
	}
	{
		ThrowHelper_ThrowArgumentOutOfRangeException_mD7D90276EDCDF9394A8EA635923E3B48BB71BD56(NULL);
	}

IL_000e:
	{
		ByReference_1_t9C85BCCAAF8C525B6C06B07E922D8D217BE8D6FC L_2 = __this->____pointer;
		V_0 = L_2;
		uint8_t* L_3;
		L_3 = IL2CPP_BY_REFERENCE_GET_VALUE(uint8_t, (Il2CppByReference*)(&V_0));
		int32_t L_4 = ___0_start;
		uint8_t* L_5;
		L_5 = il2cpp_unsafe_add<uint8_t,int32_t>(L_3, L_4);
		int32_t L_6 = __this->____length;
		int32_t L_7 = ___0_start;
		Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305 L_8;
		memset((&L_8), 0, sizeof(L_8));
		Span_1__ctor_m947BF95D54571BF3897F96822B7A8FDA5853497B_inline((&L_8), L_5, ((int32_t)il2cpp_codegen_subtract(L_6, L_7)), il2cpp_rgctx_method(InitializedTypeInfo(method->klass)->rgctx_data, 18));
		return L_8;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR ReadOnlySpan_1_tA850A6C0E88ABBA37646A078ACBC24D6D5FD9B4D ReadOnlySpan_1_Slice_mC8B7C665F49384744642F03EA355239F0E4AF966_gshared_inline (ReadOnlySpan_1_tA850A6C0E88ABBA37646A078ACBC24D6D5FD9B4D* __this, int32_t ___0_start, const RuntimeMethod* method) 
{
	ByReference_1_t9C85BCCAAF8C525B6C06B07E922D8D217BE8D6FC V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		int32_t L_0 = ___0_start;
		int32_t L_1 = __this->____length;
		if ((!(((uint32_t)L_0) > ((uint32_t)L_1))))
		{
			goto IL_000e;
		}
	}
	{
		ThrowHelper_ThrowArgumentOutOfRangeException_mD7D90276EDCDF9394A8EA635923E3B48BB71BD56(NULL);
	}

IL_000e:
	{
		ByReference_1_t9C85BCCAAF8C525B6C06B07E922D8D217BE8D6FC L_2 = __this->____pointer;
		V_0 = L_2;
		uint8_t* L_3;
		L_3 = IL2CPP_BY_REFERENCE_GET_VALUE(uint8_t, (Il2CppByReference*)(&V_0));
		int32_t L_4 = ___0_start;
		uint8_t* L_5;
		L_5 = il2cpp_unsafe_add<uint8_t,int32_t>(L_3, L_4);
		int32_t L_6 = __this->____length;
		int32_t L_7 = ___0_start;
		ReadOnlySpan_1_tA850A6C0E88ABBA37646A078ACBC24D6D5FD9B4D L_8;
		memset((&L_8), 0, sizeof(L_8));
		ReadOnlySpan_1__ctor_m0FC0B92549C2968E80B5F75A85F28B96DBFCFD63_inline((&L_8), L_5, ((int32_t)il2cpp_codegen_subtract(L_6, L_7)), il2cpp_rgctx_method(InitializedTypeInfo(method->klass)->rgctx_data, 15));
		return L_8;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint16_t BinaryPrimitives_ReverseEndianness_mDBF226C2D52CAFF6DE538F8245444B5CF87A02D0_inline (uint16_t ___0_value, const RuntimeMethod* method) 
{
	{
		uint16_t L_0 = ___0_value;
		uint16_t L_1 = ___0_value;
		return (uint16_t)((int32_t)(uint16_t)((int32_t)il2cpp_codegen_add(((int32_t)((int32_t)L_0>>8)), ((int32_t)((int32_t)L_1<<8)))));
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint32_t BinaryPrimitives_ReverseEndianness_mCCA2099164ECA9672968898DD996A9F04B392FFF_inline (uint32_t ___0_value, const RuntimeMethod* method) 
{
	uint32_t V_0 = 0;
	uint32_t V_1 = 0;
	{
		uint32_t L_0 = ___0_value;
		V_0 = ((int32_t)((int32_t)L_0&((int32_t)16711935)));
		uint32_t L_1 = ___0_value;
		V_1 = ((int32_t)((int32_t)L_1&((int32_t)-16711936)));
		uint32_t L_2 = V_0;
		uint32_t L_3 = V_0;
		uint32_t L_4 = V_1;
		uint32_t L_5 = V_1;
		return ((int32_t)il2cpp_codegen_add(((int32_t)(((int32_t)((uint32_t)L_2>>8))|((int32_t)((int32_t)L_3<<((int32_t)24))))), ((int32_t)(((int32_t)((int32_t)L_4<<8))|((int32_t)((uint32_t)L_5>>((int32_t)24)))))));
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint64_t BinaryPrimitives_ReverseEndianness_mA698702D91EF4E47FF6F682E4B48F173FF376BDF_inline (uint64_t ___0_value, const RuntimeMethod* method) 
{
	{
		uint64_t L_0 = ___0_value;
		uint32_t L_1;
		L_1 = BinaryPrimitives_ReverseEndianness_mCCA2099164ECA9672968898DD996A9F04B392FFF_inline(((int32_t)(uint32_t)L_0), NULL);
		uint64_t L_2 = ___0_value;
		uint32_t L_3;
		L_3 = BinaryPrimitives_ReverseEndianness_mCCA2099164ECA9672968898DD996A9F04B392FFF_inline(((int32_t)(uint32_t)((int64_t)((uint64_t)L_2>>((int32_t)32)))), NULL);
		return ((int64_t)il2cpp_codegen_add(((int64_t)(((int64_t)(uint64_t)L_1)<<((int32_t)32))), ((int64_t)(uint64_t)L_3)));
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void MemoryMarshal_Write_TisUInt16_tF4C148C876015C212FD72652D0B6ED8CC247A455_m1CB9CA69ED7F15E1AED66F7E98D918E1DF0CB96A_gshared_inline (Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305 ___0_destination, uint16_t* ___1_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MemoryMarshal_GetReference_TisByte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_m81BE3C6812CE881C00AAA80CCFC9349F754F63A6_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Span_1_get_Length_m8E944E4954E037877A25B9FF6B901F1F901D4769_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		goto IL_0016;
	}

IL_0016:
	{
		int32_t L_0;
		L_0 = il2cpp_unsafe_sizeof<uint16_t>();
		int32_t L_1;
		L_1 = Span_1_get_Length_m8E944E4954E037877A25B9FF6B901F1F901D4769_inline((&___0_destination), Span_1_get_Length_m8E944E4954E037877A25B9FF6B901F1F901D4769_RuntimeMethod_var);
		if ((!(((uint32_t)L_0) > ((uint32_t)L_1))))
		{
			goto IL_002b;
		}
	}
	{
		ThrowHelper_ThrowArgumentOutOfRangeException_m9B335696876184D17D1F8D7AF94C1B5B0869AA97((int32_t)((int32_t)28), NULL);
	}

IL_002b:
	{
		Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305 L_2 = ___0_destination;
		uint8_t* L_3;
		L_3 = MemoryMarshal_GetReference_TisByte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_m81BE3C6812CE881C00AAA80CCFC9349F754F63A6(L_2, MemoryMarshal_GetReference_TisByte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_m81BE3C6812CE881C00AAA80CCFC9349F754F63A6_RuntimeMethod_var);
		uint16_t* L_4 = ___1_value;
		uint16_t L_5 = (*(uint16_t*)L_4);
		il2cpp_unsafe_write_unaligned(L_3, L_5);
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void MemoryMarshal_Write_TisUInt32_t1833D51FFA667B18A5AA4B8D34DE284F8495D29B_m2BD505F8011143B77CB9A048F4C97C64130CB6DB_gshared_inline (Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305 ___0_destination, uint32_t* ___1_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MemoryMarshal_GetReference_TisByte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_m81BE3C6812CE881C00AAA80CCFC9349F754F63A6_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Span_1_get_Length_m8E944E4954E037877A25B9FF6B901F1F901D4769_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		goto IL_0016;
	}

IL_0016:
	{
		int32_t L_0;
		L_0 = il2cpp_unsafe_sizeof<uint32_t>();
		int32_t L_1;
		L_1 = Span_1_get_Length_m8E944E4954E037877A25B9FF6B901F1F901D4769_inline((&___0_destination), Span_1_get_Length_m8E944E4954E037877A25B9FF6B901F1F901D4769_RuntimeMethod_var);
		if ((!(((uint32_t)L_0) > ((uint32_t)L_1))))
		{
			goto IL_002b;
		}
	}
	{
		ThrowHelper_ThrowArgumentOutOfRangeException_m9B335696876184D17D1F8D7AF94C1B5B0869AA97((int32_t)((int32_t)28), NULL);
	}

IL_002b:
	{
		Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305 L_2 = ___0_destination;
		uint8_t* L_3;
		L_3 = MemoryMarshal_GetReference_TisByte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_m81BE3C6812CE881C00AAA80CCFC9349F754F63A6(L_2, MemoryMarshal_GetReference_TisByte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_m81BE3C6812CE881C00AAA80CCFC9349F754F63A6_RuntimeMethod_var);
		uint32_t* L_4 = ___1_value;
		uint32_t L_5 = (*(uint32_t*)L_4);
		il2cpp_unsafe_write_unaligned(L_3, L_5);
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void MemoryMarshal_Write_TisUInt64_t8F12534CC8FC4B5860F2A2CD1EE79D322E7A41AF_m369E3011D17AFC47FE8EBD775DA05E3F136D3EA3_gshared_inline (Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305 ___0_destination, uint64_t* ___1_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MemoryMarshal_GetReference_TisByte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_m81BE3C6812CE881C00AAA80CCFC9349F754F63A6_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Span_1_get_Length_m8E944E4954E037877A25B9FF6B901F1F901D4769_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		goto IL_0016;
	}

IL_0016:
	{
		int32_t L_0;
		L_0 = il2cpp_unsafe_sizeof<uint64_t>();
		int32_t L_1;
		L_1 = Span_1_get_Length_m8E944E4954E037877A25B9FF6B901F1F901D4769_inline((&___0_destination), Span_1_get_Length_m8E944E4954E037877A25B9FF6B901F1F901D4769_RuntimeMethod_var);
		if ((!(((uint32_t)L_0) > ((uint32_t)L_1))))
		{
			goto IL_002b;
		}
	}
	{
		ThrowHelper_ThrowArgumentOutOfRangeException_m9B335696876184D17D1F8D7AF94C1B5B0869AA97((int32_t)((int32_t)28), NULL);
	}

IL_002b:
	{
		Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305 L_2 = ___0_destination;
		uint8_t* L_3;
		L_3 = MemoryMarshal_GetReference_TisByte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_m81BE3C6812CE881C00AAA80CCFC9349F754F63A6(L_2, MemoryMarshal_GetReference_TisByte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_m81BE3C6812CE881C00AAA80CCFC9349F754F63A6_RuntimeMethod_var);
		uint64_t* L_4 = ___1_value;
		uint64_t L_5 = (*(uint64_t*)L_4);
		il2cpp_unsafe_write_unaligned(L_3, L_5);
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint16_t MemoryMarshal_Read_TisUInt16_tF4C148C876015C212FD72652D0B6ED8CC247A455_mE522E06A28DD43DAED8B42666149274B433F5317_gshared_inline (ReadOnlySpan_1_tA850A6C0E88ABBA37646A078ACBC24D6D5FD9B4D ___0_source, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MemoryMarshal_GetReference_TisByte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_m9D86D3A7A7F6A344D16464E6638E2BEAD3F4BC90_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ReadOnlySpan_1_get_Length_m54864A0BB817050A9110E85BB5FB31EF63699982_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		goto IL_0016;
	}

IL_0016:
	{
		int32_t L_0;
		L_0 = il2cpp_unsafe_sizeof<uint16_t>();
		int32_t L_1;
		L_1 = ReadOnlySpan_1_get_Length_m54864A0BB817050A9110E85BB5FB31EF63699982_inline((&___0_source), ReadOnlySpan_1_get_Length_m54864A0BB817050A9110E85BB5FB31EF63699982_RuntimeMethod_var);
		if ((((int32_t)L_0) <= ((int32_t)L_1)))
		{
			goto IL_002b;
		}
	}
	{
		ThrowHelper_ThrowArgumentOutOfRangeException_m9B335696876184D17D1F8D7AF94C1B5B0869AA97((int32_t)((int32_t)28), NULL);
	}

IL_002b:
	{
		ReadOnlySpan_1_tA850A6C0E88ABBA37646A078ACBC24D6D5FD9B4D L_2 = ___0_source;
		uint8_t* L_3;
		L_3 = MemoryMarshal_GetReference_TisByte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_m9D86D3A7A7F6A344D16464E6638E2BEAD3F4BC90(L_2, MemoryMarshal_GetReference_TisByte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_m9D86D3A7A7F6A344D16464E6638E2BEAD3F4BC90_RuntimeMethod_var);
		uint16_t L_4;
		L_4 = il2cpp_unsafe_read_unaligned<uint16_t>(L_3);
		return L_4;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint32_t MemoryMarshal_Read_TisUInt32_t1833D51FFA667B18A5AA4B8D34DE284F8495D29B_m56C749731FAD055AC5894D97F107FF8E5C6A13AE_gshared_inline (ReadOnlySpan_1_tA850A6C0E88ABBA37646A078ACBC24D6D5FD9B4D ___0_source, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MemoryMarshal_GetReference_TisByte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_m9D86D3A7A7F6A344D16464E6638E2BEAD3F4BC90_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ReadOnlySpan_1_get_Length_m54864A0BB817050A9110E85BB5FB31EF63699982_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		goto IL_0016;
	}

IL_0016:
	{
		int32_t L_0;
		L_0 = il2cpp_unsafe_sizeof<uint32_t>();
		int32_t L_1;
		L_1 = ReadOnlySpan_1_get_Length_m54864A0BB817050A9110E85BB5FB31EF63699982_inline((&___0_source), ReadOnlySpan_1_get_Length_m54864A0BB817050A9110E85BB5FB31EF63699982_RuntimeMethod_var);
		if ((((int32_t)L_0) <= ((int32_t)L_1)))
		{
			goto IL_002b;
		}
	}
	{
		ThrowHelper_ThrowArgumentOutOfRangeException_m9B335696876184D17D1F8D7AF94C1B5B0869AA97((int32_t)((int32_t)28), NULL);
	}

IL_002b:
	{
		ReadOnlySpan_1_tA850A6C0E88ABBA37646A078ACBC24D6D5FD9B4D L_2 = ___0_source;
		uint8_t* L_3;
		L_3 = MemoryMarshal_GetReference_TisByte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_m9D86D3A7A7F6A344D16464E6638E2BEAD3F4BC90(L_2, MemoryMarshal_GetReference_TisByte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_m9D86D3A7A7F6A344D16464E6638E2BEAD3F4BC90_RuntimeMethod_var);
		uint32_t L_4;
		L_4 = il2cpp_unsafe_read_unaligned<uint32_t>(L_3);
		return L_4;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint64_t MemoryMarshal_Read_TisUInt64_t8F12534CC8FC4B5860F2A2CD1EE79D322E7A41AF_m4B7CEC36F79FB7BE35EB5ADC5B3F9B03A427FAD0_gshared_inline (ReadOnlySpan_1_tA850A6C0E88ABBA37646A078ACBC24D6D5FD9B4D ___0_source, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MemoryMarshal_GetReference_TisByte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_m9D86D3A7A7F6A344D16464E6638E2BEAD3F4BC90_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ReadOnlySpan_1_get_Length_m54864A0BB817050A9110E85BB5FB31EF63699982_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		goto IL_0016;
	}

IL_0016:
	{
		int32_t L_0;
		L_0 = il2cpp_unsafe_sizeof<uint64_t>();
		int32_t L_1;
		L_1 = ReadOnlySpan_1_get_Length_m54864A0BB817050A9110E85BB5FB31EF63699982_inline((&___0_source), ReadOnlySpan_1_get_Length_m54864A0BB817050A9110E85BB5FB31EF63699982_RuntimeMethod_var);
		if ((((int32_t)L_0) <= ((int32_t)L_1)))
		{
			goto IL_002b;
		}
	}
	{
		ThrowHelper_ThrowArgumentOutOfRangeException_m9B335696876184D17D1F8D7AF94C1B5B0869AA97((int32_t)((int32_t)28), NULL);
	}

IL_002b:
	{
		ReadOnlySpan_1_tA850A6C0E88ABBA37646A078ACBC24D6D5FD9B4D L_2 = ___0_source;
		uint8_t* L_3;
		L_3 = MemoryMarshal_GetReference_TisByte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_m9D86D3A7A7F6A344D16464E6638E2BEAD3F4BC90(L_2, MemoryMarshal_GetReference_TisByte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_m9D86D3A7A7F6A344D16464E6638E2BEAD3F4BC90_RuntimeMethod_var);
		uint64_t L_4;
		L_4 = il2cpp_unsafe_read_unaligned<uint64_t>(L_3);
		return L_4;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Span_1__ctor_m947BF95D54571BF3897F96822B7A8FDA5853497B_gshared_inline (Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305* __this, uint8_t* ___0_ptr, int32_t ___1_length, const RuntimeMethod* method) 
{
	{
		uint8_t* L_0 = ___0_ptr;
		ByReference_1_t9C85BCCAAF8C525B6C06B07E922D8D217BE8D6FC L_1;
		memset((&L_1), 0, sizeof(L_1));
		il2cpp_codegen_by_reference_constructor((Il2CppByReference*)(&L_1), L_0);
		__this->____pointer = L_1;
		int32_t L_2 = ___1_length;
		__this->____length = L_2;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Span_1__ctor_m698EC79E2E44AFF16BA096D0861CFB129FBF8218_gshared_inline (Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305* __this, ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* ___0_array, int32_t ___1_start, int32_t ___2_length, const RuntimeMethod* method) 
{
	uint8_t V_0 = 0x0;
	{
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_0 = ___0_array;
		if (L_0)
		{
			goto IL_0016;
		}
	}
	{
		int32_t L_1 = ___1_start;
		if (L_1)
		{
			goto IL_0009;
		}
	}
	{
		int32_t L_2 = ___2_length;
		if (!L_2)
		{
			goto IL_000e;
		}
	}

IL_0009:
	{
		ThrowHelper_ThrowArgumentOutOfRangeException_mD7D90276EDCDF9394A8EA635923E3B48BB71BD56(NULL);
	}

IL_000e:
	{
		il2cpp_codegen_initobj(__this, sizeof(Span_1_tDADAC65069DFE6B57C458109115ECD795ED39305));
		return;
	}

IL_0016:
	{
		il2cpp_codegen_initobj((&V_0), sizeof(uint8_t));
		goto IL_0042;
	}

IL_0042:
	{
		int32_t L_4 = ___1_start;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_5 = ___0_array;
		NullCheck(L_5);
		if ((!(((uint32_t)L_4) <= ((uint32_t)((int32_t)(((RuntimeArray*)L_5)->max_length))))))
		{
			goto IL_0050;
		}
	}
	{
		int32_t L_6 = ___2_length;
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_7 = ___0_array;
		NullCheck(L_7);
		int32_t L_8 = ___1_start;
		if ((!(((uint32_t)L_6) > ((uint32_t)((int32_t)il2cpp_codegen_subtract(((int32_t)(((RuntimeArray*)L_7)->max_length)), L_8))))))
		{
			goto IL_0055;
		}
	}

IL_0050:
	{
		ThrowHelper_ThrowArgumentOutOfRangeException_mD7D90276EDCDF9394A8EA635923E3B48BB71BD56(NULL);
	}

IL_0055:
	{
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_9 = ___0_array;
		NullCheck((RuntimeArray*)L_9);
		uint8_t* L_10;
		L_10 = Array_GetRawSzArrayData_m2F8F5B2A381AEF971F12866D9C0A6C4FBA59F6BB_inline((RuntimeArray*)L_9, NULL);
		uint8_t* L_11;
		L_11 = il2cpp_unsafe_as_ref<uint8_t>(L_10);
		int32_t L_12 = ___1_start;
		uint8_t* L_13;
		L_13 = il2cpp_unsafe_add<uint8_t,int32_t>(L_11, L_12);
		ByReference_1_t9C85BCCAAF8C525B6C06B07E922D8D217BE8D6FC L_14;
		memset((&L_14), 0, sizeof(L_14));
		il2cpp_codegen_by_reference_constructor((Il2CppByReference*)(&L_14), L_13);
		__this->____pointer = L_14;
		int32_t L_15 = ___2_length;
		__this->____length = L_15;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void ReadOnlySpan_1__ctor_m0FC0B92549C2968E80B5F75A85F28B96DBFCFD63_gshared_inline (ReadOnlySpan_1_tA850A6C0E88ABBA37646A078ACBC24D6D5FD9B4D* __this, uint8_t* ___0_ptr, int32_t ___1_length, const RuntimeMethod* method) 
{
	{
		uint8_t* L_0 = ___0_ptr;
		ByReference_1_t9C85BCCAAF8C525B6C06B07E922D8D217BE8D6FC L_1;
		memset((&L_1), 0, sizeof(L_1));
		il2cpp_codegen_by_reference_constructor((Il2CppByReference*)(&L_1), L_0);
		__this->____pointer = L_1;
		int32_t L_2 = ___1_length;
		__this->____length = L_2;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint8_t* Array_GetRawSzArrayData_m2F8F5B2A381AEF971F12866D9C0A6C4FBA59F6BB_inline (RuntimeArray* __this, const RuntimeMethod* method) 
{
	{
		RawData_t37CAF2D3F74B7723974ED7CEEE9B297D8FA64ED0* L_0;
		L_0 = il2cpp_unsafe_as<RawData_t37CAF2D3F74B7723974ED7CEEE9B297D8FA64ED0*>(__this);
		NullCheck(L_0);
		uint8_t* L_1 = (uint8_t*)(&L_0->___Data);
		return L_1;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t ReadOnlySpan_1_get_Length_m54864A0BB817050A9110E85BB5FB31EF63699982_gshared_inline (ReadOnlySpan_1_tA850A6C0E88ABBA37646A078ACBC24D6D5FD9B4D* __this, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = __this->____length;
		return L_0;
	}
}

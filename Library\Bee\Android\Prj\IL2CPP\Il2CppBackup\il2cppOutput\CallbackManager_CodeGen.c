﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_m9E1171D3B13A6FBC7EE20648D271AF3E76DB42B2 (void);
extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_m43F5330E7D7DE9DBD8911622268D046C9AADD27F (void);
extern void CallbackHandle__ctor_m97D3F2122D4D4B7C29D896ABD6CFD1A0A59F4F5A (void);
extern void CallbackHandle_get_IsValid_mE586BDFFBE7C43187113900DF7F1CEA541BCF7D5 (void);
extern void CallbackHandle_get_IsScheduled_m0631C17FD5DAD74B9813E5AFC1CB252E348F1606 (void);
extern void CallbackHandle_Cancel_m93E2C960EF412C0FF80D2A17690F0F0905582CF4 (void);
extern void CallbackHandle_get_Invalid_mE4F35DC8D50CEFB2A060DD143AE61619169FA9CD (void);
extern void CallbackManager_ScheduleInternal_m154FC729A38E3C5A06491802E29BB0202ABC6B83 (void);
extern void CallbackManager_ScheduleDelayed_m3A806D1328E82A8855EF2EBBC78CC69190C44271 (void);
extern void CallbackManager_ScheduleDelayed_m02C498F3ACD8BCB4C8903C0B90DDD4983114D65D (void);
extern void CallbackManager_ScheduleTick_m5A33E4AC1506D69768C391AD82453FE5B029321A (void);
extern void CallbackManager_SchedulePerUpdate_m573ECC524C493CB40D7F7B9353600B2C7CFA2CF1 (void);
extern void CallbackManager_SchedulePerLateUpdate_mED89E248ACFA62446D47D06FF0582B9D9874D336 (void);
extern void CallbackManager_IsScheduled_m2E4F185FA620760970FEEA6525EE13539F893C7E (void);
extern void CallbackManager_Cancel_m1ECC8FD014AC3696596474119492EA2FC8A7ECDF (void);
extern void CallbackManager_Clear_m5906EDAB09F274110C1215280074D5C7F5B03788 (void);
extern void CallbackManager_ClearAll_m0D452895F49356DC933A9858D4F42DA2B02A8631 (void);
extern void CallbackManager_Tick_m58BC1D9D775B8808A439F1D1619009F19FE9FDA7 (void);
extern void CallbackManager_LateTick_m4A7494B7DE2B34EEA097692E72F94CA845A4A486 (void);
extern void CallbackManager_Invoke_m8E23D655062E2C22BFADA57F555C57470E3D1B58 (void);
extern void CallbackManager__ctor_m11AEDA56FDCDB6D3D53C7E6E336A27E4A373C10B (void);
extern void CallbackManager__cctor_m01BB5757690E8A266451D9CB2DB36220D0ED6D43 (void);
static Il2CppMethodPointer s_methodPointers[22] = 
{
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_m9E1171D3B13A6FBC7EE20648D271AF3E76DB42B2,
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_m43F5330E7D7DE9DBD8911622268D046C9AADD27F,
	CallbackHandle__ctor_m97D3F2122D4D4B7C29D896ABD6CFD1A0A59F4F5A,
	CallbackHandle_get_IsValid_mE586BDFFBE7C43187113900DF7F1CEA541BCF7D5,
	CallbackHandle_get_IsScheduled_m0631C17FD5DAD74B9813E5AFC1CB252E348F1606,
	CallbackHandle_Cancel_m93E2C960EF412C0FF80D2A17690F0F0905582CF4,
	CallbackHandle_get_Invalid_mE4F35DC8D50CEFB2A060DD143AE61619169FA9CD,
	CallbackManager_ScheduleInternal_m154FC729A38E3C5A06491802E29BB0202ABC6B83,
	CallbackManager_ScheduleDelayed_m3A806D1328E82A8855EF2EBBC78CC69190C44271,
	CallbackManager_ScheduleDelayed_m02C498F3ACD8BCB4C8903C0B90DDD4983114D65D,
	CallbackManager_ScheduleTick_m5A33E4AC1506D69768C391AD82453FE5B029321A,
	CallbackManager_SchedulePerUpdate_m573ECC524C493CB40D7F7B9353600B2C7CFA2CF1,
	CallbackManager_SchedulePerLateUpdate_mED89E248ACFA62446D47D06FF0582B9D9874D336,
	CallbackManager_IsScheduled_m2E4F185FA620760970FEEA6525EE13539F893C7E,
	CallbackManager_Cancel_m1ECC8FD014AC3696596474119492EA2FC8A7ECDF,
	CallbackManager_Clear_m5906EDAB09F274110C1215280074D5C7F5B03788,
	CallbackManager_ClearAll_m0D452895F49356DC933A9858D4F42DA2B02A8631,
	CallbackManager_Tick_m58BC1D9D775B8808A439F1D1619009F19FE9FDA7,
	CallbackManager_LateTick_m4A7494B7DE2B34EEA097692E72F94CA845A4A486,
	CallbackManager_Invoke_m8E23D655062E2C22BFADA57F555C57470E3D1B58,
	CallbackManager__ctor_m11AEDA56FDCDB6D3D53C7E6E336A27E4A373C10B,
	CallbackManager__cctor_m01BB5757690E8A266451D9CB2DB36220D0ED6D43,
};
extern void CallbackHandle__ctor_m97D3F2122D4D4B7C29D896ABD6CFD1A0A59F4F5A_AdjustorThunk (void);
extern void CallbackHandle_get_IsValid_mE586BDFFBE7C43187113900DF7F1CEA541BCF7D5_AdjustorThunk (void);
extern void CallbackHandle_get_IsScheduled_m0631C17FD5DAD74B9813E5AFC1CB252E348F1606_AdjustorThunk (void);
extern void CallbackHandle_Cancel_m93E2C960EF412C0FF80D2A17690F0F0905582CF4_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[4] = 
{
	{ 0x06000003, CallbackHandle__ctor_m97D3F2122D4D4B7C29D896ABD6CFD1A0A59F4F5A_AdjustorThunk },
	{ 0x06000004, CallbackHandle_get_IsValid_mE586BDFFBE7C43187113900DF7F1CEA541BCF7D5_AdjustorThunk },
	{ 0x06000005, CallbackHandle_get_IsScheduled_m0631C17FD5DAD74B9813E5AFC1CB252E348F1606_AdjustorThunk },
	{ 0x06000006, CallbackHandle_Cancel_m93E2C960EF412C0FF80D2A17690F0F0905582CF4_AdjustorThunk },
};
static const int32_t s_InvokerIndices[22] = 
{
	21383,
	13298,
	5814,
	12815,
	12815,
	12815,
	21226,
	14953,
	17582,
	17581,
	16064,
	19920,
	19920,
	17552,
	17552,
	20840,
	21355,
	20858,
	20858,
	18533,
	13298,
	21355,
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_CallbackManager_CodeGenModule;
const Il2CppCodeGenModule g_CallbackManager_CodeGenModule = 
{
	"CallbackManager.dll",
	22,
	s_methodPointers,
	4,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};

﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void EmbeddedAttribute__ctor_m39F8955D3CAA6D35B42041FED974306634793777 (void);
extern void IsUnmanagedAttribute__ctor_mCD8BDE5DA7D8EDF3274B3BCE585D4A51EA3485E0 (void);
extern void NullableAttribute__ctor_m06B77230B4325A6A416336AA8363BB57921490D7 (void);
extern void NullableAttribute__ctor_mFD2321DE13A7804C6B01E7098336588965E0EFB0 (void);
extern void NullableContextAttribute__ctor_mE9E14DB6C4C74F266FF435FDDB1B3B42E5680A06 (void);
extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_m624B00A92F855C9F3A0D8856E91DE539B9252246 (void);
extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_mE347810C69832D5D5E588963A68B82BD26BB5254 (void);
extern void IsExternalInit__ctor_m6B50CAF49263FE40FC436D9531290B0B924572E6 (void);
extern void CompositeMotionHandle__ctor_m2FD83EF385A003E8A7C2E990E57BEF7E94A93395 (void);
extern void CompositeMotionHandle__ctor_m419923AFEC9A2703AB08B6AB55324EC98BC62204 (void);
extern void CompositeMotionHandle_Cancel_m4CE5A6E2F9A8F0EB1AF62EF943E1E2A64FA4F038 (void);
extern void CompositeMotionHandle_Complete_m607679A03DFE58C08E744E22CCC2E370619BF07C (void);
extern void CompositeMotionHandle_Add_m4AA1F3801646AADB12067164270FC54EB77B9C52 (void);
extern void CompositeMotionHandle_GetEnumerator_mFA1476472CD17CF6D3B4F4A5C224525E04E757A5 (void);
extern void CompositeMotionHandle_System_Collections_Generic_IEnumerableU3CLitMotion_MotionHandleU3E_GetEnumerator_m04FB019154BE4BF75C0A90451534BF1DE4F5AEF6 (void);
extern void CompositeMotionHandle_System_Collections_IEnumerable_GetEnumerator_mBDD56F2DC6628EE7257F7BAFE7B5C7264F29E4EE (void);
extern void CompositeMotionHandle_Clear_m1B73033633ADC986924809FA8146E299A1C4242E (void);
extern void CompositeMotionHandle_Contains_m5FEC4DDB63AB82BE41867E725DCE33A1DC9D53DF (void);
extern void CompositeMotionHandle_CopyTo_m6403FFA5387993AAC68815FAB2F348687D6D0E8C (void);
extern void CompositeMotionHandle_Remove_mD9C112A181B962D59485EAB214FE5F5C4B59B007 (void);
extern void CompositeMotionHandle_get_Count_m8D8414DACC80117CCA6218ECE619DE80AB97F31B (void);
extern void CompositeMotionHandle_get_IsReadOnly_m496F4B118D462B8ED6D5967CA9D1BF446CFEADED (void);
extern void EaseUtility_Evaluate_m81A1498D9148087F83081BED41176712D7EC4E50 (void);
extern void EaseUtility_Linear_m00A57B978A75E1E640560BAE98B9EA91A07C705A (void);
extern void EaseUtility_InSine_m7FEEFBB83B4BCED4835A1CF99842B20A30F28EB7 (void);
extern void EaseUtility_OutSine_mE68C4CDD6E3D797C5B1964C7521710C999D565EA (void);
extern void EaseUtility_InOutSine_mCCA7CE4A26F911E03A1C1154C30A444C76AAEA2E (void);
extern void EaseUtility_InQuad_mFE6A7E74381ED3AD1B62ADCBEAD8F3BA9357F27B (void);
extern void EaseUtility_OutQuad_m8635DAD3E9BD74B36306F2C7729703C4E4CA8D99 (void);
extern void EaseUtility_InOutQuad_m7053A93BD95B360C07DF92BC597887477115CB09 (void);
extern void EaseUtility_InCubic_m928EA66C9143E1E137CB9819EEDB267EBC78C36F (void);
extern void EaseUtility_OutCubic_m5DA6B7198991B83FDFB9BA8F364AD33B9DF6E857 (void);
extern void EaseUtility_InOutCubic_m268E1E91373C97CC6362BEE0FA49360E325859D8 (void);
extern void EaseUtility_InQuart_m6995541D4934D84D95E346282FD538338041A07A (void);
extern void EaseUtility_OutQuart_mE5F66CF137A0FB6FA7F6E278A5FCECF1BB85D433 (void);
extern void EaseUtility_InOutQuart_m258BB06B4C4A49B03C68044B1011D60FC4034BF2 (void);
extern void EaseUtility_InQuint_m10B3508D95937CAC2A6500E05FBA0B4395EC846E (void);
extern void EaseUtility_OutQuint_m54E678520C35631272E10A4D1B0A8E8F1B8DE06E (void);
extern void EaseUtility_InOutQuint_mFCF8225B768BFE9230350EEEDDA8D4572653DE7B (void);
extern void EaseUtility_InExpo_mA18BC119B8C77F34595909562A2BF20A65EAAE4C (void);
extern void EaseUtility_OutExpo_mAC94B51A5DEBA90AC1577C7E8C4DFFAA41AD2066 (void);
extern void EaseUtility_InOutExpo_m355AA513CEFD290593A5A5544890AC5EC4B80218 (void);
extern void EaseUtility_InCirc_m45E62F2F14B8C29A1A940AECC6DEB1D390B32B47 (void);
extern void EaseUtility_OutCirc_mF8088DB3630F22511FC3A6E9061B2A2B16FD48E9 (void);
extern void EaseUtility_InOutCirc_m20443F829D616DC8E1C58A45F3557386CC0C8B24 (void);
extern void EaseUtility_InBack_m6B27BF84B926D9DD4A2099811EA47B0E94DDA759 (void);
extern void EaseUtility_OutBack_m3D0647DCA04638845A724F59ACC4477B9A6990D6 (void);
extern void EaseUtility_InOutBack_mD9F0B9EEA96CB10596D564AE8933529C5BDFA7DE (void);
extern void EaseUtility_InElastic_mAE9C169A1DA1AD8040C665AB058A0727DB76681A (void);
extern void EaseUtility_OutElastic_m69F10A66DBCA0A9F654D204E8E427A12D1C0DD40 (void);
extern void EaseUtility_InOutElastic_mBF22D575517637937A1FF39019281764DA225C6F (void);
extern void EaseUtility_InBounce_m1C65CA782377D33773D09ADA73A62828C2866D54 (void);
extern void EaseUtility_OutBounce_m59C37B5D636D1D5CD1313B04FF128D5E81BC002C (void);
extern void EaseUtility_InOutBounce_m04F9CB3735BE5D54981A8AF7AB4D2CB7BAB97AF0 (void);
extern void EaseUtility_EvaluateU24BurstManaged_m37B727D1797E8E01DC2993193BD49E7100BA0229 (void);
extern void EaseUtility_LinearU24BurstManaged_mA53F1FD9802771E5DF225D3945028E362CE3DEFA (void);
extern void EaseUtility_InSineU24BurstManaged_m79794B7E30AED11DD4D25FCE32131A576D8733DF (void);
extern void EaseUtility_OutSineU24BurstManaged_m7D84E50A5E16E6EED45088057FC58B9C7FDF3EB6 (void);
extern void EaseUtility_InOutSineU24BurstManaged_mD0E6A86F42B0C33304A3132FD7356A37CB1A1A00 (void);
extern void EaseUtility_InQuadU24BurstManaged_mE2559E983436EF783D288D9735687AF0AD542C5E (void);
extern void EaseUtility_OutQuadU24BurstManaged_m88A3FE89EE38BE83D128A109AEAC8ADEA8D546E5 (void);
extern void EaseUtility_InOutQuadU24BurstManaged_m95F5460C4B814B7D7A524AEBF048D64EDC74939D (void);
extern void EaseUtility_InCubicU24BurstManaged_mB93D4DAC0E3F527A1518D74EBD91DBDF54B01AED (void);
extern void EaseUtility_OutCubicU24BurstManaged_mAC9D20D60845A5BC315ACFEE2E975C7931C642D8 (void);
extern void EaseUtility_InOutCubicU24BurstManaged_mC7D857670884BA35596F318F237E749E23E521E1 (void);
extern void EaseUtility_InQuartU24BurstManaged_mA04D2F8AFD0B4CFC1ECC0E464031ED68153ABCF8 (void);
extern void EaseUtility_OutQuartU24BurstManaged_m8101D875A5612324C498F99EC0F8749DC5E4E7BE (void);
extern void EaseUtility_InOutQuartU24BurstManaged_mC8E88C51F3CF95C6912E9C46307F68681A42E200 (void);
extern void EaseUtility_InQuintU24BurstManaged_m73C6CFC8AD1850FE1AC2D57D9FDE3EEF9E25BB8F (void);
extern void EaseUtility_OutQuintU24BurstManaged_m3598300EE3AA9DDD9B518C0153975EF54EC5F924 (void);
extern void EaseUtility_InOutQuintU24BurstManaged_mD957BD5DD3B2116D6FB3ACD51BA92A7E120A8DFC (void);
extern void EaseUtility_InExpoU24BurstManaged_mFDDEF8B6B7B6E1FDFDD5F190A67FCE06D7D3522F (void);
extern void EaseUtility_OutExpoU24BurstManaged_m0B64E597E904EA1F1ADBD292D38DC05F347F2C36 (void);
extern void EaseUtility_InOutExpoU24BurstManaged_m37C13C1C5B5877D87BA2D638C615874DD6A32461 (void);
extern void EaseUtility_InCircU24BurstManaged_m5DEEF8C773679EBCFAA1355CB0D82BE9F02CE5D9 (void);
extern void EaseUtility_OutCircU24BurstManaged_mFE1F05EDF2B476EF2F951D982A306510AA5C862A (void);
extern void EaseUtility_InOutCircU24BurstManaged_mFA01A23B2C1C2842DAFB68420378FF2E9A7FFED4 (void);
extern void EaseUtility_InBackU24BurstManaged_mE2B941EA16A22D124B00A05DE81EA66A50A5CB3E (void);
extern void EaseUtility_OutBackU24BurstManaged_m3DE81FBEE889E4E8ACF21523C24B5B468F9296EF (void);
extern void EaseUtility_InOutBackU24BurstManaged_m1D2FDC9782453FC0B9BF5C7A070DD73CC7031804 (void);
extern void EaseUtility_InElasticU24BurstManaged_m41E5DFC00E0D8B6E1AD11157BC083D308EF8971B (void);
extern void EaseUtility_OutElasticU24BurstManaged_m67EC5EB5312C08AB72C0FCC735D697DD1BC2E737 (void);
extern void EaseUtility_InOutElasticU24BurstManaged_mE3D883D9792030893CF68E02D09A40EA050A0D59 (void);
extern void EaseUtility_InBounceU24BurstManaged_m29EC768A139F54748A58AF51A293DDD337D8954B (void);
extern void EaseUtility_OutBounceU24BurstManaged_m4A940AC936E10F31C616DC812C32F320B1F39DA8 (void);
extern void EaseUtility_InOutBounceU24BurstManaged_m5AEE3957059401D38E48397D44D5E46A954B17A0 (void);
extern void Evaluate_00000017U24PostfixBurstDelegate__ctor_mA8B6F3C581D4C1BBC1EB7EAA8C8A3556BA06FB7C (void);
extern void Evaluate_00000017U24PostfixBurstDelegate_Invoke_m5FD51B653A835EC879DED4185A3A554401034C10 (void);
extern void Evaluate_00000017U24PostfixBurstDelegate_BeginInvoke_m0433F393B5DA46658602D327C43C2A1995AA7D18 (void);
extern void Evaluate_00000017U24PostfixBurstDelegate_EndInvoke_m9FC3B9BE3A068782132C28A87C84BF3AE438FB75 (void);
extern void Evaluate_00000017U24BurstDirectCall_GetFunctionPointerDiscard_m8F304EC90F99FE1256CAFAA637920283E5DF9DB1 (void);
extern void Evaluate_00000017U24BurstDirectCall_GetFunctionPointer_m82E4DC31FD9387B986860BCCC66DA012573CA2BC (void);
extern void Evaluate_00000017U24BurstDirectCall_Constructor_m823C5FC211A8EBDA7CF844C55A18EA285CF5F494 (void);
extern void Evaluate_00000017U24BurstDirectCall_Initialize_m73B29872A69F1A37A25F69BB3741B2BB5356F8D5 (void);
extern void Evaluate_00000017U24BurstDirectCall__cctor_mEA49049BF96AF874C7D1BB615E63AFABBA493D8F (void);
extern void Evaluate_00000017U24BurstDirectCall_Invoke_mF2B1A76D272F3E2114E74F16D06A5E770F380858 (void);
extern void Linear_00000018U24PostfixBurstDelegate__ctor_mFEB2830DCC8851BAAD8E89192065CDC51723FB24 (void);
extern void Linear_00000018U24PostfixBurstDelegate_Invoke_m4FA7DA4FD0A4CD423F0083BA9D81C2C94620FF1F (void);
extern void Linear_00000018U24PostfixBurstDelegate_BeginInvoke_mE6A1655599C82C46AB90AD55613F6E6A3A478D9A (void);
extern void Linear_00000018U24PostfixBurstDelegate_EndInvoke_m2A9C919B5137B9182663A63E96B0BCE394A42D36 (void);
extern void Linear_00000018U24BurstDirectCall_GetFunctionPointerDiscard_m15582DB91F989D9C63A0B5C99C8B081BA95133BF (void);
extern void Linear_00000018U24BurstDirectCall_GetFunctionPointer_m6B9EC0D19213C8ABD05D20C49495A118201550FE (void);
extern void Linear_00000018U24BurstDirectCall_Constructor_m6394D3776269312A251D843499BCD8DE3BD4D5CD (void);
extern void Linear_00000018U24BurstDirectCall_Initialize_m07D72DAB7B8C8A37351D49C6A328345B5C4349EC (void);
extern void Linear_00000018U24BurstDirectCall__cctor_mB0D91B70456F5717E1CDA25A471FF0478F029110 (void);
extern void Linear_00000018U24BurstDirectCall_Invoke_m2305B8DC38FF3CFBFDDCC02D0012B4687E90A72F (void);
extern void InSine_00000019U24PostfixBurstDelegate__ctor_m0ACFA728FF2A15507E46C17C47ED2947FBC20B04 (void);
extern void InSine_00000019U24PostfixBurstDelegate_Invoke_m61C8A6A292F9EEA56DD331BDA87FB9A6DE78D47F (void);
extern void InSine_00000019U24PostfixBurstDelegate_BeginInvoke_m54660470F1C59FA75F3743208192D845D882F000 (void);
extern void InSine_00000019U24PostfixBurstDelegate_EndInvoke_mB700E473E0E640A0F215A32D1F3ECBAA091B7DC8 (void);
extern void InSine_00000019U24BurstDirectCall_GetFunctionPointerDiscard_mBDAD7DDB2C7B2421E9C50833B1C78AE8BE835D30 (void);
extern void InSine_00000019U24BurstDirectCall_GetFunctionPointer_m3E04CECB19C5CD82A38A4EA722E915312C471D0C (void);
extern void InSine_00000019U24BurstDirectCall_Constructor_m0480743FDD49929FA39EE2D2D6665409AA26CA00 (void);
extern void InSine_00000019U24BurstDirectCall_Initialize_m9A79E3B4FB6972202AAF694DF098B2275FC60DD5 (void);
extern void InSine_00000019U24BurstDirectCall__cctor_m5FC4CD9FA8B636D3A3FB1ADE127838993188ABBA (void);
extern void InSine_00000019U24BurstDirectCall_Invoke_m7487D8E7BBFF4F0D619AF9CAFA28BF31864EDF91 (void);
extern void OutSine_0000001AU24PostfixBurstDelegate__ctor_m1E9BEBBDA1EE7111EE2E6C714DB97D5141685684 (void);
extern void OutSine_0000001AU24PostfixBurstDelegate_Invoke_m828B4B73E5E4424CC67A812AB46776134F675570 (void);
extern void OutSine_0000001AU24PostfixBurstDelegate_BeginInvoke_m0C52B3A28A72A588257123C79175597811CC5087 (void);
extern void OutSine_0000001AU24PostfixBurstDelegate_EndInvoke_m5CEA1F1E84F2DE25B3D3776A90CD471574F3A056 (void);
extern void OutSine_0000001AU24BurstDirectCall_GetFunctionPointerDiscard_mCE319D3991B24B1B284505F9F226CF63AC0BA307 (void);
extern void OutSine_0000001AU24BurstDirectCall_GetFunctionPointer_m79A4F62E747D002F7708FCE36875903A71CC5721 (void);
extern void OutSine_0000001AU24BurstDirectCall_Constructor_m35B531816C2CED2B4099CDE758ED6150EE91B621 (void);
extern void OutSine_0000001AU24BurstDirectCall_Initialize_m341CE941054D4BCC04B867F2D1B2A64DF9078120 (void);
extern void OutSine_0000001AU24BurstDirectCall__cctor_mB8AAFA612654C5B6152C8CB539141BB3238BDBA2 (void);
extern void OutSine_0000001AU24BurstDirectCall_Invoke_m577BFCE2BB2640DA532DE34EE344629A91759365 (void);
extern void InOutSine_0000001BU24PostfixBurstDelegate__ctor_mD041C3B88BC631A2B3F77A0B333E51283B7B5BF5 (void);
extern void InOutSine_0000001BU24PostfixBurstDelegate_Invoke_mE7C7A91EBE784856EC763611D6B84FC8CDAC98EA (void);
extern void InOutSine_0000001BU24PostfixBurstDelegate_BeginInvoke_m273C343A615A77AF042E2924EFD236242EFDD1A5 (void);
extern void InOutSine_0000001BU24PostfixBurstDelegate_EndInvoke_m89FA44281A3AAB7B8152443843E26F8C89927568 (void);
extern void InOutSine_0000001BU24BurstDirectCall_GetFunctionPointerDiscard_mCE2DE6A6A3058F6FB7D101B4F33ACEBE6F7F5ADC (void);
extern void InOutSine_0000001BU24BurstDirectCall_GetFunctionPointer_m9DFBACE3F363841ED009CD3D5DCD8D3C05304FB4 (void);
extern void InOutSine_0000001BU24BurstDirectCall_Constructor_m0A004EAFFB9EAD7670CFA2DEB8AF6F3B98F3A10A (void);
extern void InOutSine_0000001BU24BurstDirectCall_Initialize_m662A6CB6D62FF9616B54662FCEBD5DB3199B490E (void);
extern void InOutSine_0000001BU24BurstDirectCall__cctor_m0E679F15E2742A117CE3BE0C975D6CC5DF7888AC (void);
extern void InOutSine_0000001BU24BurstDirectCall_Invoke_m998F29B4D52FD7247F1090F87CD2A4D7E03714C6 (void);
extern void InQuad_0000001CU24PostfixBurstDelegate__ctor_m0DA21D23C71D85B3B8576DB1CD68412106253EA8 (void);
extern void InQuad_0000001CU24PostfixBurstDelegate_Invoke_mB193C40C938D4240684933E1FFF2E9C9B0399674 (void);
extern void InQuad_0000001CU24PostfixBurstDelegate_BeginInvoke_m554BBA360A5C6002AF77C4430A6B64C9FAC75926 (void);
extern void InQuad_0000001CU24PostfixBurstDelegate_EndInvoke_m54EBBF0A8A43BC069DDA44325BCA7A759DEA5BD5 (void);
extern void InQuad_0000001CU24BurstDirectCall_GetFunctionPointerDiscard_mC96BA92222425FE2E730D93E7A9113F67908C5ED (void);
extern void InQuad_0000001CU24BurstDirectCall_GetFunctionPointer_m9767A1616FDBFB005F6E0CC16AE38DCC55D43B8B (void);
extern void InQuad_0000001CU24BurstDirectCall_Constructor_m4EFEACE0F36AF58C976FE24203C74AD9DE0D17D5 (void);
extern void InQuad_0000001CU24BurstDirectCall_Initialize_mFBB410B7A168EA3406C57D79A13EF126DE0B9C50 (void);
extern void InQuad_0000001CU24BurstDirectCall__cctor_m19139CD780ECB8834A36D8E52E1AFC9770E7E8A1 (void);
extern void InQuad_0000001CU24BurstDirectCall_Invoke_mF82213577E5DD90E6585835ADC1510E240BE3492 (void);
extern void OutQuad_0000001DU24PostfixBurstDelegate__ctor_mE39E274DA4B4181E335845C99515C15E4FA16FFC (void);
extern void OutQuad_0000001DU24PostfixBurstDelegate_Invoke_m78FB5740F0433AA5BA22407AC24874D6FFD019B1 (void);
extern void OutQuad_0000001DU24PostfixBurstDelegate_BeginInvoke_mEF648BEE4AEFDDACA85CD7F09FFB1B6846CE6350 (void);
extern void OutQuad_0000001DU24PostfixBurstDelegate_EndInvoke_m07DE1CA14267B8EABE1EF59351D1C354C6902A67 (void);
extern void OutQuad_0000001DU24BurstDirectCall_GetFunctionPointerDiscard_mD2D862E38773BAEF02548E13FC25FFF99FC933A8 (void);
extern void OutQuad_0000001DU24BurstDirectCall_GetFunctionPointer_mA9F349FF6131D2158861C01FA77A176E339EB1D1 (void);
extern void OutQuad_0000001DU24BurstDirectCall_Constructor_m99DC0FA879BFE5596CFBAF2D87B0B2C44762FF0F (void);
extern void OutQuad_0000001DU24BurstDirectCall_Initialize_m98764AA42F2D539F5F8868964C6EACD7B846CBB5 (void);
extern void OutQuad_0000001DU24BurstDirectCall__cctor_mB27FE099CEC2B0C253C03763BCB4C078615FFC66 (void);
extern void OutQuad_0000001DU24BurstDirectCall_Invoke_mB62D0B90B985D9056396D2E2D5AF5D3B96A7A1A2 (void);
extern void InOutQuad_0000001EU24PostfixBurstDelegate__ctor_m61550E61C9DC9FB8AD19980F88C5F129E2644A1B (void);
extern void InOutQuad_0000001EU24PostfixBurstDelegate_Invoke_m6F7DB9FB48A126ED08C33A9B9DB629A6287D71B2 (void);
extern void InOutQuad_0000001EU24PostfixBurstDelegate_BeginInvoke_m57D8CE764383C4DD8A2B03FCBD8CC508059B826A (void);
extern void InOutQuad_0000001EU24PostfixBurstDelegate_EndInvoke_m35EAC423A6AD2E79F6695E2A2EBED448F4B0C547 (void);
extern void InOutQuad_0000001EU24BurstDirectCall_GetFunctionPointerDiscard_m1506F5A84504B973B09B3270319CF222D0862FCC (void);
extern void InOutQuad_0000001EU24BurstDirectCall_GetFunctionPointer_m9E740FCF1C9771DCB4715A6EB135EA4B1D1FE14A (void);
extern void InOutQuad_0000001EU24BurstDirectCall_Constructor_mC89DB31D8E042CC0796F00E9F04EF0800EC0AE55 (void);
extern void InOutQuad_0000001EU24BurstDirectCall_Initialize_mDA0E753E71AD2E14289A7DEB41105ED2B0E91F05 (void);
extern void InOutQuad_0000001EU24BurstDirectCall__cctor_m896D2541852A840388954577FEE43BFDFEE5F719 (void);
extern void InOutQuad_0000001EU24BurstDirectCall_Invoke_m48194FDA00A44A5207135822F436929065079DC1 (void);
extern void InCubic_0000001FU24PostfixBurstDelegate__ctor_m4BDD7767C61DBD13C6F9ED8E477F04F7A0642422 (void);
extern void InCubic_0000001FU24PostfixBurstDelegate_Invoke_m3C63866647BAAD977206AB266F85C1C2620A9C1A (void);
extern void InCubic_0000001FU24PostfixBurstDelegate_BeginInvoke_mBFE1748F622C04DB1FAC935D5FC175F36EDBFA61 (void);
extern void InCubic_0000001FU24PostfixBurstDelegate_EndInvoke_m23BED6EBBA5A6B02439D6C6150F20C557B0BA664 (void);
extern void InCubic_0000001FU24BurstDirectCall_GetFunctionPointerDiscard_m4D31B1531F8506833E807686F987D0B6680163A1 (void);
extern void InCubic_0000001FU24BurstDirectCall_GetFunctionPointer_m20181FE3DC7F20F3EDF048C5029994D11809453D (void);
extern void InCubic_0000001FU24BurstDirectCall_Constructor_m6A420AE7E18F2E592B2356B6C0030BEB001800AD (void);
extern void InCubic_0000001FU24BurstDirectCall_Initialize_m4BD8A803D6C90AF193B32FD3E6246AB59EB763C5 (void);
extern void InCubic_0000001FU24BurstDirectCall__cctor_m14BD21730F966995BFF0BA23376F67B2C2302086 (void);
extern void InCubic_0000001FU24BurstDirectCall_Invoke_mFFE55402E241BDBC65A992B86BC3A8293CC869AA (void);
extern void OutCubic_00000020U24PostfixBurstDelegate__ctor_mB2A20246233EEE7D6A51DF33C8C513BF27C88E57 (void);
extern void OutCubic_00000020U24PostfixBurstDelegate_Invoke_m06DCB994A864BE732AC25A4A7481F90CFEE33FA7 (void);
extern void OutCubic_00000020U24PostfixBurstDelegate_BeginInvoke_m4C782DBF7D64DE232F45481F98BFA811FF8E9ED9 (void);
extern void OutCubic_00000020U24PostfixBurstDelegate_EndInvoke_m881B0ACE7BFE49DB2986B899252EC0DE0ACC36AB (void);
extern void OutCubic_00000020U24BurstDirectCall_GetFunctionPointerDiscard_m87F2B1E7C3457FEAFF93A8D8B53741FA0ED7EA26 (void);
extern void OutCubic_00000020U24BurstDirectCall_GetFunctionPointer_mB2A96CE1B30B4286A450217D63493F7E014B3819 (void);
extern void OutCubic_00000020U24BurstDirectCall_Constructor_m40F465A0F71468C8E36B139E2906DBF6072938E4 (void);
extern void OutCubic_00000020U24BurstDirectCall_Initialize_m3A4BCB7A03846C5E080B51DEAE0EA9893766B288 (void);
extern void OutCubic_00000020U24BurstDirectCall__cctor_mEFF1C9DA10D6E97ECCFD7593D99529D474A04813 (void);
extern void OutCubic_00000020U24BurstDirectCall_Invoke_mD635835D832401F35FEA473C35EF716E0FFC2084 (void);
extern void InOutCubic_00000021U24PostfixBurstDelegate__ctor_mFAD2CECA6C5C6271ECDB74257F9F53FB9EEC4363 (void);
extern void InOutCubic_00000021U24PostfixBurstDelegate_Invoke_mD5941E47AE99BBC7247E78205F232A77EF6238EB (void);
extern void InOutCubic_00000021U24PostfixBurstDelegate_BeginInvoke_mA8D44ED54EF7F2C2046668F847B9AF6ED257B894 (void);
extern void InOutCubic_00000021U24PostfixBurstDelegate_EndInvoke_mFAA7380F86F012B119548F80B0D8D7C9DF0F2B3E (void);
extern void InOutCubic_00000021U24BurstDirectCall_GetFunctionPointerDiscard_mC8D46EE39391BFBF9A9B091868AA2EC9240E8CDD (void);
extern void InOutCubic_00000021U24BurstDirectCall_GetFunctionPointer_m844884537D5698BDF5ACA73A6A84C268807B0EEC (void);
extern void InOutCubic_00000021U24BurstDirectCall_Constructor_mFC53C4C889596EC4C435E147B9A9C43BB654F434 (void);
extern void InOutCubic_00000021U24BurstDirectCall_Initialize_mDD93943E92CDBBD10557126D91C0D38E0EA5F707 (void);
extern void InOutCubic_00000021U24BurstDirectCall__cctor_mE92F5DA3ECA970911C74BDB16C00A92EAF55B5BA (void);
extern void InOutCubic_00000021U24BurstDirectCall_Invoke_mAC8D389FD345ABA480661F2226ACD19F8DD7CC28 (void);
extern void InQuart_00000022U24PostfixBurstDelegate__ctor_m9EA8C1A00D1E812B6FCACE4AB77E16018673B18D (void);
extern void InQuart_00000022U24PostfixBurstDelegate_Invoke_m09227E477D28E52211CEDFB7AD6737CDFEE5090F (void);
extern void InQuart_00000022U24PostfixBurstDelegate_BeginInvoke_m49573A0EF5916519A82E70B57C3881CBDD134456 (void);
extern void InQuart_00000022U24PostfixBurstDelegate_EndInvoke_m6EBA2EF36970F714203873E2044D5B89D04E5847 (void);
extern void InQuart_00000022U24BurstDirectCall_GetFunctionPointerDiscard_m91C887233E19927748CD7DEC0B1C2B39B811D750 (void);
extern void InQuart_00000022U24BurstDirectCall_GetFunctionPointer_m12E723F2A3E826814DD92B618A4402D3CDCC146F (void);
extern void InQuart_00000022U24BurstDirectCall_Constructor_mEF5C43305DB791FCEC0E4ED7A5175F47108BAEC0 (void);
extern void InQuart_00000022U24BurstDirectCall_Initialize_m5B717C60C093105EF488FA117D0095AF5F457807 (void);
extern void InQuart_00000022U24BurstDirectCall__cctor_m5E709D862C2DBFF0F36388A0E0A049B69A155F16 (void);
extern void InQuart_00000022U24BurstDirectCall_Invoke_m36710A56C6B77A3C97EF9986500BE0ED689E8414 (void);
extern void OutQuart_00000023U24PostfixBurstDelegate__ctor_m45491E2D1AB09CE1C4A626218BF27AE1FE19A036 (void);
extern void OutQuart_00000023U24PostfixBurstDelegate_Invoke_m96D1E0C563C26E0922B1A9024368C6A19B506E83 (void);
extern void OutQuart_00000023U24PostfixBurstDelegate_BeginInvoke_mB973F1BD1F8538AE535FD9ACDEA1DE2B7F8CAA77 (void);
extern void OutQuart_00000023U24PostfixBurstDelegate_EndInvoke_mF59BC9E52F6E5AF707893B4DF9BC0D272FB436F3 (void);
extern void OutQuart_00000023U24BurstDirectCall_GetFunctionPointerDiscard_m35303DED055FD197B3FF3D7A050586E15FB061B1 (void);
extern void OutQuart_00000023U24BurstDirectCall_GetFunctionPointer_m2BC2AE52B48A3116B3466D596321F39FF68059CC (void);
extern void OutQuart_00000023U24BurstDirectCall_Constructor_m6D84325979CA7CBDD1A4CB178CB2240C479CDB17 (void);
extern void OutQuart_00000023U24BurstDirectCall_Initialize_m5F31644011D73B37B0CC0950672928AF5A065ABA (void);
extern void OutQuart_00000023U24BurstDirectCall__cctor_m36B3B1F3D13C20B797429CE157149032A059DCF2 (void);
extern void OutQuart_00000023U24BurstDirectCall_Invoke_m8DA570E4BE3B60BE68BF9996A650ED00B70CDE40 (void);
extern void InOutQuart_00000024U24PostfixBurstDelegate__ctor_m700853B58354C18F90A0C2810E3D99E7C3A3C379 (void);
extern void InOutQuart_00000024U24PostfixBurstDelegate_Invoke_mD305A394BAADA81E75DA9E87669985CAEDAA7E61 (void);
extern void InOutQuart_00000024U24PostfixBurstDelegate_BeginInvoke_mCF022D461D07D5249930977CC27E23A1E48E3A09 (void);
extern void InOutQuart_00000024U24PostfixBurstDelegate_EndInvoke_mD6C1316E9F7AEA476B1E954E8E286C7E04223664 (void);
extern void InOutQuart_00000024U24BurstDirectCall_GetFunctionPointerDiscard_mC8F0CA17239FB94AC2889D2428F2091A2BA2AA23 (void);
extern void InOutQuart_00000024U24BurstDirectCall_GetFunctionPointer_mA95276C6BD0065DB03A4C3904FAA95A4CCA7F739 (void);
extern void InOutQuart_00000024U24BurstDirectCall_Constructor_m0DD6D07F2358EE907AA232AB03CD7057506456A7 (void);
extern void InOutQuart_00000024U24BurstDirectCall_Initialize_m5ED48E032250606549AE657B3ED77A1EC05AED31 (void);
extern void InOutQuart_00000024U24BurstDirectCall__cctor_m09D2424782F87D76480B5D379EEF3981914193F9 (void);
extern void InOutQuart_00000024U24BurstDirectCall_Invoke_mEA3BDA921C6A495084F34DF472A95B270F44266D (void);
extern void InQuint_00000025U24PostfixBurstDelegate__ctor_m09E03F490FFAED047E003817204356FE0D03DD47 (void);
extern void InQuint_00000025U24PostfixBurstDelegate_Invoke_m2526AC125F4E8D32C0EBEDDCD481D05B2993B99C (void);
extern void InQuint_00000025U24PostfixBurstDelegate_BeginInvoke_mEC5323A6EDB20136854F91CCB73E60A43EC7BF63 (void);
extern void InQuint_00000025U24PostfixBurstDelegate_EndInvoke_m660CE3D35FA28F49CA450E4775B635692B9098F6 (void);
extern void InQuint_00000025U24BurstDirectCall_GetFunctionPointerDiscard_m4ED02AC6FB29B91FDD8C570CEE16DEA8B1317BFD (void);
extern void InQuint_00000025U24BurstDirectCall_GetFunctionPointer_mB8A2318586087C091BA991C9E9D3F51DC5948505 (void);
extern void InQuint_00000025U24BurstDirectCall_Constructor_m47B560B9ABE8BF20E75F54815826F508556174CD (void);
extern void InQuint_00000025U24BurstDirectCall_Initialize_mC05B2CD56E4BF295F87C7CC777551E6634A6E4A3 (void);
extern void InQuint_00000025U24BurstDirectCall__cctor_mD053A75373E0435A1A3D551FA2F01E1489F6EEAB (void);
extern void InQuint_00000025U24BurstDirectCall_Invoke_m09E79D8D2CF112265B40A8FC83732E7007AEA7D6 (void);
extern void OutQuint_00000026U24PostfixBurstDelegate__ctor_m41673773748DAEC533433F03D3CDB7DDF2799D22 (void);
extern void OutQuint_00000026U24PostfixBurstDelegate_Invoke_mDB985E25D67C99FDDA98A2EE4883199F31B199D9 (void);
extern void OutQuint_00000026U24PostfixBurstDelegate_BeginInvoke_mCE4751EFA293D8CF031056601238917925DB65A1 (void);
extern void OutQuint_00000026U24PostfixBurstDelegate_EndInvoke_m4F9A5E4A7B86B686C46033A893EF8566608332E3 (void);
extern void OutQuint_00000026U24BurstDirectCall_GetFunctionPointerDiscard_m14FA5963A083CBD953C5E85560022AEF9802414D (void);
extern void OutQuint_00000026U24BurstDirectCall_GetFunctionPointer_m90F8E1820D16493AFA2E38A7797BA80386BEF602 (void);
extern void OutQuint_00000026U24BurstDirectCall_Constructor_m657B0FDA7A705A7DFE6C1D7410E1BFFE4BBB4D7F (void);
extern void OutQuint_00000026U24BurstDirectCall_Initialize_m5D8AE96C8DE38C2B6EBDE4F134DC8717B13BFF99 (void);
extern void OutQuint_00000026U24BurstDirectCall__cctor_m1A4C7A2E88C7F9F949E7AE7FBC04A14AC5BA3E93 (void);
extern void OutQuint_00000026U24BurstDirectCall_Invoke_m7B594A388DF7AEEA181F5B046AEE939F5F734A05 (void);
extern void InOutQuint_00000027U24PostfixBurstDelegate__ctor_m8249FA5D5B9934C8B610E677BB5E0B0B41C5A411 (void);
extern void InOutQuint_00000027U24PostfixBurstDelegate_Invoke_mA999C0197E7DF56C1A480B757E34FA4C491A85D3 (void);
extern void InOutQuint_00000027U24PostfixBurstDelegate_BeginInvoke_mA6AD69E1975588BCA15B5D0749BAF02894BD55B7 (void);
extern void InOutQuint_00000027U24PostfixBurstDelegate_EndInvoke_m11FA324BA9C755DCC258F71689A85D949C28EBBE (void);
extern void InOutQuint_00000027U24BurstDirectCall_GetFunctionPointerDiscard_m3E546001713D68B546228C786FB1557E94BE07F0 (void);
extern void InOutQuint_00000027U24BurstDirectCall_GetFunctionPointer_mBFC9FEE4D02C81FCCCFE6C1BA8ABE2A38BA3953C (void);
extern void InOutQuint_00000027U24BurstDirectCall_Constructor_m9E31905F2E5E17E856307B3CA06CB2E525411780 (void);
extern void InOutQuint_00000027U24BurstDirectCall_Initialize_m0F519DE5C5B69CDE0661AC7A3AD95B1626D379E6 (void);
extern void InOutQuint_00000027U24BurstDirectCall__cctor_m4FFBCC4B4190649F1C6639082165E64C44410EA5 (void);
extern void InOutQuint_00000027U24BurstDirectCall_Invoke_m576757B01DA2949D479369DEB81704F264350C87 (void);
extern void InExpo_00000028U24PostfixBurstDelegate__ctor_m7C63E1AA9C0492BB8EC8DCCE1E70B2B4216030C6 (void);
extern void InExpo_00000028U24PostfixBurstDelegate_Invoke_m9A8F9C65413499827E3A18888136D0596F2AFFED (void);
extern void InExpo_00000028U24PostfixBurstDelegate_BeginInvoke_m21ED0FF2AC737E124BE5EDA328E57254A91D16CB (void);
extern void InExpo_00000028U24PostfixBurstDelegate_EndInvoke_mF426B9F1161A64E52D8EBB9372F58FD732F8BE48 (void);
extern void InExpo_00000028U24BurstDirectCall_GetFunctionPointerDiscard_mE525B83AA6D223B180C55821A03589DE7774662A (void);
extern void InExpo_00000028U24BurstDirectCall_GetFunctionPointer_m8566D6B2812C1029674C7000CA7E34D9618A99D3 (void);
extern void InExpo_00000028U24BurstDirectCall_Constructor_m11B7836C0F1330863EA5C7E204507679E4A01A16 (void);
extern void InExpo_00000028U24BurstDirectCall_Initialize_m06512EF68DD7E1852E9A2F7845603E5616B869AB (void);
extern void InExpo_00000028U24BurstDirectCall__cctor_mB07F88EF10F46D940D83772292735F999C532F43 (void);
extern void InExpo_00000028U24BurstDirectCall_Invoke_m5B526CB09F2C8880C3962D9D01EDB244285CCE26 (void);
extern void OutExpo_00000029U24PostfixBurstDelegate__ctor_m05D33E20D81C48E59EE4A74B4A354B5F734B470A (void);
extern void OutExpo_00000029U24PostfixBurstDelegate_Invoke_m06A177564E864064F4374AFD2E9A44C013236F4A (void);
extern void OutExpo_00000029U24PostfixBurstDelegate_BeginInvoke_m7DF87884696AE72986AB9D9748851B81FF1F5E04 (void);
extern void OutExpo_00000029U24PostfixBurstDelegate_EndInvoke_m8D74F5171FD710BC44E3094DDAA3BE7645F4169D (void);
extern void OutExpo_00000029U24BurstDirectCall_GetFunctionPointerDiscard_mD6B45297510E9A2CBAB5ED85F666A469FDEB2874 (void);
extern void OutExpo_00000029U24BurstDirectCall_GetFunctionPointer_m81B3D27160DE394DEA9BFDA6840923D813E925FF (void);
extern void OutExpo_00000029U24BurstDirectCall_Constructor_mFDB3E1170CD02A54D8E8CF0635E0C2704EE15ECA (void);
extern void OutExpo_00000029U24BurstDirectCall_Initialize_mECE79C68410EA2B36F2578E94D5B6E74EF367908 (void);
extern void OutExpo_00000029U24BurstDirectCall__cctor_m3BD5D71612A836EF092FABEDD9CED6EED0334369 (void);
extern void OutExpo_00000029U24BurstDirectCall_Invoke_m8753A962FA74A918D9011825508848545DA19E28 (void);
extern void InOutExpo_0000002AU24PostfixBurstDelegate__ctor_mA955D8F8954AB3E4F296DCB91CFBDEC3D22E9E24 (void);
extern void InOutExpo_0000002AU24PostfixBurstDelegate_Invoke_m33E536B26D7E9A58D518397000EBC77DF16F605B (void);
extern void InOutExpo_0000002AU24PostfixBurstDelegate_BeginInvoke_m2C96FF2E82A12CA14FC4E1B981AE51218175729D (void);
extern void InOutExpo_0000002AU24PostfixBurstDelegate_EndInvoke_mC4E7A0D7C84DE7B923AFC8D06514E3427C2880A1 (void);
extern void InOutExpo_0000002AU24BurstDirectCall_GetFunctionPointerDiscard_mDFB69570ABEB135D239A1A008FF97DB92E8FBE69 (void);
extern void InOutExpo_0000002AU24BurstDirectCall_GetFunctionPointer_m99DD4FC2264C96798B7FE6CBD53734B6574B7EA4 (void);
extern void InOutExpo_0000002AU24BurstDirectCall_Constructor_m8AA15D0A746246ED9B68E5FACAB6CC7D17100997 (void);
extern void InOutExpo_0000002AU24BurstDirectCall_Initialize_m66FA612BA8000700C0BA41381673181A51BA52BB (void);
extern void InOutExpo_0000002AU24BurstDirectCall__cctor_m3F3C228A254339C26CA2F18E47EFD16844B71956 (void);
extern void InOutExpo_0000002AU24BurstDirectCall_Invoke_m43BB8BBCE9077AF42E187E32FE311966DE4CCED6 (void);
extern void InCirc_0000002BU24PostfixBurstDelegate__ctor_m7A9B562460439E217B55EBA143008D01B5F52567 (void);
extern void InCirc_0000002BU24PostfixBurstDelegate_Invoke_m652D00C838590B59FB8672203FA6A9FA8EDDED92 (void);
extern void InCirc_0000002BU24PostfixBurstDelegate_BeginInvoke_m3E4F4D1CB5B50CE7F7938DA87F55BBB54965B60D (void);
extern void InCirc_0000002BU24PostfixBurstDelegate_EndInvoke_m145783899C374D22AD57FF5B4BA78BD40231C4F2 (void);
extern void InCirc_0000002BU24BurstDirectCall_GetFunctionPointerDiscard_m4307A55F67B6EEA10B415FA40147DBD79D2738B8 (void);
extern void InCirc_0000002BU24BurstDirectCall_GetFunctionPointer_m55C913856E54D27E49318B2FA47A7C32FA8B3D48 (void);
extern void InCirc_0000002BU24BurstDirectCall_Constructor_mBA0ACF0F07B048E460DBE9A10CECA7FC68860F52 (void);
extern void InCirc_0000002BU24BurstDirectCall_Initialize_mEAF4847D40CA6637B9E6B4802708AF440ACE65BC (void);
extern void InCirc_0000002BU24BurstDirectCall__cctor_mEF980206273777EBC9C81D8DF42F46090AB0C76A (void);
extern void InCirc_0000002BU24BurstDirectCall_Invoke_mE4F25E3B259CB3BB508F3AC3188F907DA3828D67 (void);
extern void OutCirc_0000002CU24PostfixBurstDelegate__ctor_m646599C15E83E910ECBF993A65DE9D21EE52B5FB (void);
extern void OutCirc_0000002CU24PostfixBurstDelegate_Invoke_m07503BBA4F11499827E7684D2A8CE92DA7344892 (void);
extern void OutCirc_0000002CU24PostfixBurstDelegate_BeginInvoke_mAEC3DD694EE3909362E88D86FBFF6FC7800080B2 (void);
extern void OutCirc_0000002CU24PostfixBurstDelegate_EndInvoke_m15755DF0F75928595A3F408C8E0E092345287D7C (void);
extern void OutCirc_0000002CU24BurstDirectCall_GetFunctionPointerDiscard_m869BF1C22F56D950C39FA49E26FFD67596FB55CA (void);
extern void OutCirc_0000002CU24BurstDirectCall_GetFunctionPointer_mBFDDFA9CA25D0C6E36B43F46429AE315AC92458E (void);
extern void OutCirc_0000002CU24BurstDirectCall_Constructor_mF11BB25E0271C68F3DC0D4262E4369645FA8C498 (void);
extern void OutCirc_0000002CU24BurstDirectCall_Initialize_m190237ACA2439EEB47D3F635A33C7E27FD3E86A6 (void);
extern void OutCirc_0000002CU24BurstDirectCall__cctor_m13FB0937551BF019A851C8539ACCB00E18849DDD (void);
extern void OutCirc_0000002CU24BurstDirectCall_Invoke_mF0455CD7944D635ABEF383ACE6C6601AE8538A0D (void);
extern void InOutCirc_0000002DU24PostfixBurstDelegate__ctor_mE0EC577CAABEBB6351DE16291B6F9934A76359D3 (void);
extern void InOutCirc_0000002DU24PostfixBurstDelegate_Invoke_mF1C7C56775C55C1222682BFEF7F67CA3ED6F32D7 (void);
extern void InOutCirc_0000002DU24PostfixBurstDelegate_BeginInvoke_mDA52E437F87711B375B44C93ABE47217BB01445A (void);
extern void InOutCirc_0000002DU24PostfixBurstDelegate_EndInvoke_mB594373B9E967E5823659D410691F22577ABDEFD (void);
extern void InOutCirc_0000002DU24BurstDirectCall_GetFunctionPointerDiscard_m158C8DB868C8189991C54A9DBCC7CCE012D5460C (void);
extern void InOutCirc_0000002DU24BurstDirectCall_GetFunctionPointer_m8800CA32F53205F5EA12FEAAFBE63151BE9513BB (void);
extern void InOutCirc_0000002DU24BurstDirectCall_Constructor_m070A9E01A300B9A29AD302418CDA57434389217C (void);
extern void InOutCirc_0000002DU24BurstDirectCall_Initialize_mE0FE5794DE611BA0FDDC24FD3C20F8D82FBB3A9F (void);
extern void InOutCirc_0000002DU24BurstDirectCall__cctor_m8A6446B205641D221ACD9B320C7C995DFA78BF66 (void);
extern void InOutCirc_0000002DU24BurstDirectCall_Invoke_m6399115BF9180D4F0B2EDDF4606339EDD6E3BF9E (void);
extern void InBack_0000002EU24PostfixBurstDelegate__ctor_m3098F47E5F17CC52CDF2FE3B48BFACBC666C35E6 (void);
extern void InBack_0000002EU24PostfixBurstDelegate_Invoke_m8191E8FDACC2BA6471DF5D7B16E6F861BBE3DFBE (void);
extern void InBack_0000002EU24PostfixBurstDelegate_BeginInvoke_m3471BFBC1589920AC01476AFB7346ADF46D8EB82 (void);
extern void InBack_0000002EU24PostfixBurstDelegate_EndInvoke_m22AED87C0A7BAD74135AEBA0610C1FFFC7DFE888 (void);
extern void InBack_0000002EU24BurstDirectCall_GetFunctionPointerDiscard_m4E57E277EC11E49557BA90A514D5A950B80F09CA (void);
extern void InBack_0000002EU24BurstDirectCall_GetFunctionPointer_m4B79669D215367DB1C7F3E2E19ADC1241BB2B3CF (void);
extern void InBack_0000002EU24BurstDirectCall_Constructor_m61D0876FA76ADA82F28F638962E9BEEEF8C85540 (void);
extern void InBack_0000002EU24BurstDirectCall_Initialize_m483E51460C9F55308A6975205EBD8AB125C8D6BC (void);
extern void InBack_0000002EU24BurstDirectCall__cctor_mC83F598CB9CFB95B34582216CB445D2089F4A2C1 (void);
extern void InBack_0000002EU24BurstDirectCall_Invoke_m6A4F01D15E5A6AC556B7AAFEBBE48A491870232E (void);
extern void OutBack_0000002FU24PostfixBurstDelegate__ctor_m2386C5EAB5555DB36700973ECF8E6CDEC6F252A6 (void);
extern void OutBack_0000002FU24PostfixBurstDelegate_Invoke_m4AA6DD82D2D128094996052BEA85798905A9C1D2 (void);
extern void OutBack_0000002FU24PostfixBurstDelegate_BeginInvoke_mAE98A3CBA92252CFA09D5D80A53048E7BA92E98D (void);
extern void OutBack_0000002FU24PostfixBurstDelegate_EndInvoke_m2B03B3C78EFACD90066379789BA18B233D32560A (void);
extern void OutBack_0000002FU24BurstDirectCall_GetFunctionPointerDiscard_m688E52DBE866D3932276EB36586A31766B5B4B30 (void);
extern void OutBack_0000002FU24BurstDirectCall_GetFunctionPointer_m0CD5F4F1C1BCDABF8F1A9ED0C632B3A292BEE5D1 (void);
extern void OutBack_0000002FU24BurstDirectCall_Constructor_mCD82122CA1C8686ED3E9281DFFAE5566E130B6F9 (void);
extern void OutBack_0000002FU24BurstDirectCall_Initialize_mC9724F4ABC441DB3ADA0AB1B1C4602B27219CD86 (void);
extern void OutBack_0000002FU24BurstDirectCall__cctor_m1A3B2D784A0B0C5006E5FED0BB7C010C76E3ECAD (void);
extern void OutBack_0000002FU24BurstDirectCall_Invoke_mABE9CF2945F72A44E5632BBD4B5626CCB61E9538 (void);
extern void InOutBack_00000030U24PostfixBurstDelegate__ctor_mAACD786A575A99C0C1D5EB2A51B5BD28BEE05978 (void);
extern void InOutBack_00000030U24PostfixBurstDelegate_Invoke_m47CAA49A1275CFD1A47B5F5316A24C4C45B5A987 (void);
extern void InOutBack_00000030U24PostfixBurstDelegate_BeginInvoke_mC96FD2185B4EBCDBF994BFA11EE0E7A1C939FEE5 (void);
extern void InOutBack_00000030U24PostfixBurstDelegate_EndInvoke_m63ACE6C2FB627CECAE739D993C1C342A9859682D (void);
extern void InOutBack_00000030U24BurstDirectCall_GetFunctionPointerDiscard_m576BD7DA5024F350DA651F18F4014EA96EC0EF43 (void);
extern void InOutBack_00000030U24BurstDirectCall_GetFunctionPointer_m10A5F8E30CB96FD6D2E25C76D4114FA04F5397EF (void);
extern void InOutBack_00000030U24BurstDirectCall_Constructor_mA515E54AD4E641A3ED34D6F24D33F99429AB396D (void);
extern void InOutBack_00000030U24BurstDirectCall_Initialize_m393B4F3AFDB22CAFAB68ACDA8C98D6F547C8E2B0 (void);
extern void InOutBack_00000030U24BurstDirectCall__cctor_m2159DC2E9FC2B3F764B9556B5C8FBDA9EE05BEF9 (void);
extern void InOutBack_00000030U24BurstDirectCall_Invoke_m8E11B842AC98A85B48B76431C40E84FE9E0E9030 (void);
extern void InElastic_00000031U24PostfixBurstDelegate__ctor_m98C4CC3EBC98659585CC985345C85419EA2139FC (void);
extern void InElastic_00000031U24PostfixBurstDelegate_Invoke_m031F91CBF735130411CE217AFC34C991CF19EA48 (void);
extern void InElastic_00000031U24PostfixBurstDelegate_BeginInvoke_m995FF185567E95962D71BFDDA4EDDE78264CF4B5 (void);
extern void InElastic_00000031U24PostfixBurstDelegate_EndInvoke_m63E1895654C3CEED71B547CBB1A0F1C5091988D1 (void);
extern void InElastic_00000031U24BurstDirectCall_GetFunctionPointerDiscard_m2539F89F6D9643715AFCB1D2557A67B3EF0AB31B (void);
extern void InElastic_00000031U24BurstDirectCall_GetFunctionPointer_m581DBF0DC537E9E437ECBD867740D120F39DF463 (void);
extern void InElastic_00000031U24BurstDirectCall_Constructor_mD7D9BE3E101D4A65B0A529E6B5644E35E801D2DD (void);
extern void InElastic_00000031U24BurstDirectCall_Initialize_m7E4248893D43B2D24F03633659349E7B1DB084E2 (void);
extern void InElastic_00000031U24BurstDirectCall__cctor_m00627F7A3EC0DCE9C5811349810D4F8801729F3E (void);
extern void InElastic_00000031U24BurstDirectCall_Invoke_m689125905209D18666A62A2F7502A1F8E027C2A4 (void);
extern void OutElastic_00000032U24PostfixBurstDelegate__ctor_m666214622229A9517B8F2895A3D6D94A209D697B (void);
extern void OutElastic_00000032U24PostfixBurstDelegate_Invoke_m67207E639E44FA39F5136DB9A39EC69456E58317 (void);
extern void OutElastic_00000032U24PostfixBurstDelegate_BeginInvoke_m838C258356F04229863F41F8B1676A3A74D92AA4 (void);
extern void OutElastic_00000032U24PostfixBurstDelegate_EndInvoke_m3E106787AD735EFA403BB5472425C577A10519D0 (void);
extern void OutElastic_00000032U24BurstDirectCall_GetFunctionPointerDiscard_m628D3E4C0E675939339D0F13384BFC7636694C13 (void);
extern void OutElastic_00000032U24BurstDirectCall_GetFunctionPointer_m4A3636A401338888F15855BF06CF09F97930142F (void);
extern void OutElastic_00000032U24BurstDirectCall_Constructor_m10D830A847698739A3B2A65DD5124CDFE9C42838 (void);
extern void OutElastic_00000032U24BurstDirectCall_Initialize_m0522E9F5E8C811686D6C6B483E53D12F58C89859 (void);
extern void OutElastic_00000032U24BurstDirectCall__cctor_mF057AC435A9CDB3DF3D67421240C5DE5DE605006 (void);
extern void OutElastic_00000032U24BurstDirectCall_Invoke_mB9FE2791A873FB431B83221C825EB996BEFFB491 (void);
extern void InOutElastic_00000033U24PostfixBurstDelegate__ctor_mE3E8951858CE7AD40C9AA6F77BCE513ED73ED4F2 (void);
extern void InOutElastic_00000033U24PostfixBurstDelegate_Invoke_m6A23437992CE120BBA7568D21A6D34B67C88A432 (void);
extern void InOutElastic_00000033U24PostfixBurstDelegate_BeginInvoke_mA33B80E243083697E16CA003C4A0B35D644FEF44 (void);
extern void InOutElastic_00000033U24PostfixBurstDelegate_EndInvoke_m1CAA5D6CD8E33A555D3FD38E7B524C57CFD92B79 (void);
extern void InOutElastic_00000033U24BurstDirectCall_GetFunctionPointerDiscard_mFCC2404BD72EAA69B020DA5D39C303334B4D6EC2 (void);
extern void InOutElastic_00000033U24BurstDirectCall_GetFunctionPointer_m39B1A1F61907FB7A926DCE7E380F4C20B6F40FCC (void);
extern void InOutElastic_00000033U24BurstDirectCall_Constructor_m4635065A64B739E483E46A0B7C75F7CDD10E8C3E (void);
extern void InOutElastic_00000033U24BurstDirectCall_Initialize_mDF306F86225971E6E36BEEAFDEACE23FF3972901 (void);
extern void InOutElastic_00000033U24BurstDirectCall__cctor_mF719301B80775E98EC74837DF94B8AF95A0C1B5E (void);
extern void InOutElastic_00000033U24BurstDirectCall_Invoke_m7FAEE9030EB1EEFE167FE21762B432F4C11B58D8 (void);
extern void InBounce_00000034U24PostfixBurstDelegate__ctor_m2399325CEB1196321E9016680D2E5E750B7E1DB7 (void);
extern void InBounce_00000034U24PostfixBurstDelegate_Invoke_m40A3F91BD8629A2540B48DABEAF9E4D2DE4D712A (void);
extern void InBounce_00000034U24PostfixBurstDelegate_BeginInvoke_mB3BE2654C1C5D0A8B5F6907C038CBA0578DFF27C (void);
extern void InBounce_00000034U24PostfixBurstDelegate_EndInvoke_m546E7F1C177A5D931CF14AD5F331D39FFC1D150F (void);
extern void InBounce_00000034U24BurstDirectCall_GetFunctionPointerDiscard_mBDF143BB8A1A1B2BC4BD48169CFDD46641545091 (void);
extern void InBounce_00000034U24BurstDirectCall_GetFunctionPointer_m7F6FAD99C9E6C2B6E5CF489C5BA6F2E00775C593 (void);
extern void InBounce_00000034U24BurstDirectCall_Constructor_mDDFEC7EFDD9D19043FC31F4340AB01119EF2D8A4 (void);
extern void InBounce_00000034U24BurstDirectCall_Initialize_mC1086AF50C6350A74BAD56DC03803E85380066A0 (void);
extern void InBounce_00000034U24BurstDirectCall__cctor_m272A847D7AD87AD0991C7C35AA08A927DBEB0B28 (void);
extern void InBounce_00000034U24BurstDirectCall_Invoke_m36ABC346F9613CA5F88A91F3CA6BCF4AB00F9F95 (void);
extern void OutBounce_00000035U24PostfixBurstDelegate__ctor_m96D54DC983841ECCBFCB6F90B0B09B32C4D1AAB8 (void);
extern void OutBounce_00000035U24PostfixBurstDelegate_Invoke_mF11E4D95E839DD7F4CAF3FEAF69221D60531541F (void);
extern void OutBounce_00000035U24PostfixBurstDelegate_BeginInvoke_m1E482D87A39397692D7088F1FD536226F6D9EEC8 (void);
extern void OutBounce_00000035U24PostfixBurstDelegate_EndInvoke_m3B134D562C5E1F4EEBD394EADB0D8569D790DB36 (void);
extern void OutBounce_00000035U24BurstDirectCall_GetFunctionPointerDiscard_m5857ACB2AF3E929ADE3359BC7B66E85F80505DC8 (void);
extern void OutBounce_00000035U24BurstDirectCall_GetFunctionPointer_mE741195E3A8D79BA07E141517A9E637B15A7DD64 (void);
extern void OutBounce_00000035U24BurstDirectCall_Constructor_m23609828F3448393C693D63FE7504B647F376B3F (void);
extern void OutBounce_00000035U24BurstDirectCall_Initialize_m6DD5A36BBD27F4C739F79C5243124090E9839C02 (void);
extern void OutBounce_00000035U24BurstDirectCall__cctor_mE73584C39A3AD468839439305A1C27A585E4FA64 (void);
extern void OutBounce_00000035U24BurstDirectCall_Invoke_m21F068F74E7EF2BB260A5942267AEE679FA0C6E4 (void);
extern void InOutBounce_00000036U24PostfixBurstDelegate__ctor_mE688CB4DF052BE8A532A0EB9C43EAB1BA784389D (void);
extern void InOutBounce_00000036U24PostfixBurstDelegate_Invoke_m38FC06755DD9C9F58148DD627FC79E809E240714 (void);
extern void InOutBounce_00000036U24PostfixBurstDelegate_BeginInvoke_m80D3E722757EA033125F83F5E130E59ED516696F (void);
extern void InOutBounce_00000036U24PostfixBurstDelegate_EndInvoke_m0A168AAD6B48F1728BD2F21326D1EC75A8C9FEB5 (void);
extern void InOutBounce_00000036U24BurstDirectCall_GetFunctionPointerDiscard_m22271C64E789531C0D925E540ED93036F07E3247 (void);
extern void InOutBounce_00000036U24BurstDirectCall_GetFunctionPointer_m6E557E26E734D288FF7334FB5DACB154C9D13C8A (void);
extern void InOutBounce_00000036U24BurstDirectCall_Constructor_m894FC992441639A575AD1F12F8570CF99D97AEA4 (void);
extern void InOutBounce_00000036U24BurstDirectCall_Initialize_mE59CA6EE1BF0DD229B34F028C68F568EE6A3827F (void);
extern void InOutBounce_00000036U24BurstDirectCall__cctor_mAE3FA4CCAFBAE260883869B655989D24F0FEC30F (void);
extern void InOutBounce_00000036U24BurstDirectCall_Invoke_mD2E268A662DC8BD846EF1F190C89D423135D556E (void);
extern void ArrayHelper_EnsureBufferCapacity_mF22F65DA72F76C3FD3F8E14286FEA2D1EB40C6CF (void);
extern void ArrayHelper_FastResize_mAED65F4307F535154F8D54F691E976F0D7CE32C5 (void);
extern void Box_Create_m439F69D7165CEC55E57E832A192202D7FAAA5E4E (void);
extern void Box_Create_mC1736654ECE08299633A59C3833B413F63C271D3 (void);
extern void Box__cctor_m81D0A3258AF2D1DA0A2DF913257A7C63F62060E7 (void);
extern void Error_Format_mCA7E93BC39656DE151F38E7EAD5E25568AA064C2 (void);
extern void Error_Argument_m1191EA42C71D91FEA4D2E16AAFA87738F62C32FE (void);
extern void Error_ArgumentNull_m41C1B297D31423BA533FC9B6DF7D7CF4046E9BDA (void);
extern void Error_MotionNotExists_mADC1B81D88E6BEAA6C8AAD7F28014791F98B403C (void);
extern void Error_MotionHasBeenCanceledOrCompleted_m3C3454BF847862B0F6A8865028A63D439F10BB22 (void);
extern void Error_MotionIsInSequence_m043546EF891B183DFC74DC8F7296634959B44FCE (void);
extern void FixedStringHelper_GetScrambleChar_mE80AE9E83FC5E7941D0EF3CE275DCC6A91229922 (void);
extern void FixedStringHelper_GetUtf8CharCount_m9D5B6C6440937FEF2D4DF12848E2921D4F531953 (void);
extern void FixedStringHelper_GetRuneOf_mA63C25E8D560B2E6331D400A04FC7AC535CAE4D1 (void);
extern void FixedStringHelper_Interpolate_m3CB56363D8515E498CAE3943B278AC4B980C8EDB (void);
extern void FixedStringHelper_FillText_m7D9D1596D71EA99AB4A7A303D862167D1ACB9215 (void);
extern void FixedStringHelper_FillRichText_mA11CCE3CEFC2AF229423FBDA68A62C451B25FCDB (void);
extern void FixedStringHelper_FillScrambleChars_m03D12896FC0C2C5401D443BE69F488335926A2AD (void);
extern void FixedStringHelper_SliceSymbols_m9F7A8AE327F0BDDC9A6B4A4E7785061ECA81AE75 (void);
extern void FixedStringHelper_GetUtf8CharCount_m73D0837E13C8258F7C73D0AC6882BAD1CB59EBE4 (void);
extern void FixedStringHelper_GetRuneOf_m3FDB4B3B3658B1650AD2C40E3795FDD2BF05F771 (void);
extern void FixedStringHelper_Interpolate_m501C917268A6F366CE197E792BCEBC99B4BAE3A6 (void);
extern void FixedStringHelper_FillText_mB3EA616ADA4865446CDD0605CF5E134AB31912E3 (void);
extern void FixedStringHelper_FillRichText_mA5BFBCD898278B4B08F7BB89AF6270F6FF236DA5 (void);
extern void FixedStringHelper_FillScrambleChars_mA6A407E6898E82C0481C68F9F9F1AA647021D6EA (void);
extern void FixedStringHelper_SliceSymbols_mB75F4FD7885E68FB1B2D8821C97BD231696DDBB8 (void);
extern void FixedStringHelper_GetUtf8CharCount_m395D732466B111DCF2187871EE9AF35802D89E08 (void);
extern void FixedStringHelper_GetRuneOf_m1C8AF748094BB6F3EED03E6A22055BC16AB2A3AF (void);
extern void FixedStringHelper_Interpolate_m38748B2753DFF9DBB8131EFAE60349D700550808 (void);
extern void FixedStringHelper_FillText_mC7321D86FCDE8CDD9ADC18C719413535B20A8A3C (void);
extern void FixedStringHelper_FillRichText_m694355439E2778A4A08B3CA7185BD33A462C37E1 (void);
extern void FixedStringHelper_FillScrambleChars_m03FB25FD724EDB3BABD4829FD49892C0F68BFFA4 (void);
extern void FixedStringHelper_SliceSymbols_m9DA36DD179F9413120B3E3E08B0B3D2DDC4BAB42 (void);
extern void FixedStringHelper_GetUtf8CharCount_mFEBFE66A2581B05E4413D751905A87AA23D4875D (void);
extern void FixedStringHelper_GetRuneOf_mD88D2508FDCF5C9D143C86DC72D4AB03A019CD63 (void);
extern void FixedStringHelper_Interpolate_m3FD45393BF75D45F8EC53D3993160DCB1CC87D20 (void);
extern void FixedStringHelper_FillText_m5D8127161FDF592434E91BE534646DCE176EC48C (void);
extern void FixedStringHelper_FillRichText_mF13D30235157E1F8AA3186025FAE9FA452A7941C (void);
extern void FixedStringHelper_FillScrambleChars_m7802A351117410543210FA564DA7610025EF6C51 (void);
extern void FixedStringHelper_SliceSymbols_mE4B911AB8613F1741A9FAC492C6C08CF32DD550A (void);
extern void FixedStringHelper_GetUtf8CharCount_mE00685F29DA06F54BC83A062CE8B98CF620A4330 (void);
extern void FixedStringHelper_GetRuneOf_mC998ACF50CA3470D4EC2F8DEA8530C95AF192D71 (void);
extern void FixedStringHelper_Interpolate_m991CE1A016A7BDBE6AD81012865B387C93689D60 (void);
extern void FixedStringHelper_FillText_m9CE062C75E3915F62E769EBC97C56428CA28B648 (void);
extern void FixedStringHelper_FillRichText_m332081CBAC1EA954CE6D459960C41F4BE82B9F36 (void);
extern void FixedStringHelper_FillScrambleChars_m46B11F3B7893D2EFF8168B88927507C691AB79A3 (void);
extern void FixedStringHelper_SliceSymbols_m7D4680FA59DA77656CF529942EC97D6E78FAF448 (void);
extern void FixedStringHelper__cctor_mD56E259CC104DDAC6CF63F91953DB826C0CA8F88 (void);
extern void FixedStringHelper_GetUtf8CharCountU24BurstManaged_m33B38FCB3E7111320D2D2106BE893A3BF4F6CC84 (void);
extern void FixedStringHelper_InterpolateU24BurstManaged_m8F4F09B244811500C4EBA05A459E9459C97F1C85 (void);
extern void FixedStringHelper_GetUtf8CharCountU24BurstManaged_m2E5B858B966719B8085A37309EE713EA9F379FFF (void);
extern void FixedStringHelper_InterpolateU24BurstManaged_mDAE091CC82888DABBD443652F14C44BA8F8ED309 (void);
extern void FixedStringHelper_GetUtf8CharCountU24BurstManaged_mAEA74933B2CEF3A011DC6DF5EE72C23AD6D36624 (void);
extern void FixedStringHelper_InterpolateU24BurstManaged_mF588200F8B214479CC063D77BC9052BA128E6EE9 (void);
extern void FixedStringHelper_GetUtf8CharCountU24BurstManaged_m3E68A3454D35102186AB0B874529542C875A6B9D (void);
extern void FixedStringHelper_InterpolateU24BurstManaged_m99E3BD93F4817C77BE0F1B684DB9829CED241C6E (void);
extern void FixedStringHelper_GetUtf8CharCountU24BurstManaged_m89E2CC92B55135BE5BF9C1E5FC8C2AF2FF33E9FB (void);
extern void FixedStringHelper_InterpolateU24BurstManaged_m15D8247FA0A74A989DD95EB5D9F1E45845C1B908 (void);
extern void GetUtf8CharCount_00000054U24PostfixBurstDelegate__ctor_m6041EA0AC523B405A4CFDAA4F59FAA57BC6E92A5 (void);
extern void GetUtf8CharCount_00000054U24PostfixBurstDelegate_Invoke_m36E5B0B6B59379CF4EF6DCFA650419B728AFDEC8 (void);
extern void GetUtf8CharCount_00000054U24PostfixBurstDelegate_BeginInvoke_mCF333B262E84DAC440E2216AC3D7BBDC9F447719 (void);
extern void GetUtf8CharCount_00000054U24PostfixBurstDelegate_EndInvoke_mA4394C03654F5CB43ADBE31875C65BFDB711B3D4 (void);
extern void GetUtf8CharCount_00000054U24BurstDirectCall_GetFunctionPointerDiscard_m45FD32FF6CC732CEFC707012994D05F8E8CC4799 (void);
extern void GetUtf8CharCount_00000054U24BurstDirectCall_GetFunctionPointer_m140B1BE6D656F1016CCD42E6B3DB2EF202184858 (void);
extern void GetUtf8CharCount_00000054U24BurstDirectCall_Constructor_m0450E94A2145EBCA89C8D9627CE48F06D1F67AE8 (void);
extern void GetUtf8CharCount_00000054U24BurstDirectCall_Initialize_m395D7D761CA42D4FB9A6843122F2D779F2ADD603 (void);
extern void GetUtf8CharCount_00000054U24BurstDirectCall__cctor_mD2D4948A3023BBDB32ACFE3CB3CA36242CAE103C (void);
extern void GetUtf8CharCount_00000054U24BurstDirectCall_Invoke_m49633D214D601BECF2755A3EF8925157B792A7AE (void);
extern void Interpolate_00000056U24PostfixBurstDelegate__ctor_m005487C1D3A0D4C545CD0712C1E1EBD8D0BBD0AC (void);
extern void Interpolate_00000056U24PostfixBurstDelegate_Invoke_mBF73032FBF543D4EB4430BFDF39D759B659E895C (void);
extern void Interpolate_00000056U24PostfixBurstDelegate_BeginInvoke_mB2E02977BDB10A87B4CCD6ED3A8178087B031F8B (void);
extern void Interpolate_00000056U24PostfixBurstDelegate_EndInvoke_mB04788B0BA7848C7371A24F06B5407E1C71B47B0 (void);
extern void Interpolate_00000056U24BurstDirectCall_GetFunctionPointerDiscard_m17959F9E7B1508ACC5B790E882053F0F6A7C5700 (void);
extern void Interpolate_00000056U24BurstDirectCall_GetFunctionPointer_mEA4DBB3CE11C0787CA12D09A8ED63F4786E4A928 (void);
extern void Interpolate_00000056U24BurstDirectCall_Constructor_m7932CF0C84CC16F3D197743F34B499DE83F04DD4 (void);
extern void Interpolate_00000056U24BurstDirectCall_Initialize_m91B79A73238628B12A618FAB1EE4B34572378A91 (void);
extern void Interpolate_00000056U24BurstDirectCall__cctor_mAE3A9AEAE6FAC42188DA3254CCA3375B6BC0B512 (void);
extern void Interpolate_00000056U24BurstDirectCall_Invoke_m250E57BEC92B5DCF9527FC5978BB36CBEFB5D43A (void);
extern void GetUtf8CharCount_0000005BU24PostfixBurstDelegate__ctor_mEDE51D84F64CDF1C193CBD02DCCDDA72A8357C9D (void);
extern void GetUtf8CharCount_0000005BU24PostfixBurstDelegate_Invoke_mE13C20E9EFF2EDA191839A292FA8307AB43088FF (void);
extern void GetUtf8CharCount_0000005BU24PostfixBurstDelegate_BeginInvoke_m33B47E1B48AF2C5FA72A01BDFF8FBD752DA59945 (void);
extern void GetUtf8CharCount_0000005BU24PostfixBurstDelegate_EndInvoke_m8CDE6F07805D364931FEAEBBDEE917DCA5C53202 (void);
extern void GetUtf8CharCount_0000005BU24BurstDirectCall_GetFunctionPointerDiscard_m19AB959B2D0F609BE6207590F250B8BF8384035E (void);
extern void GetUtf8CharCount_0000005BU24BurstDirectCall_GetFunctionPointer_mF71898B17A38B0CFF5B34B10E60194ABF467C059 (void);
extern void GetUtf8CharCount_0000005BU24BurstDirectCall_Constructor_m948D21255E4FB499DCD3640377BEE436C74544B4 (void);
extern void GetUtf8CharCount_0000005BU24BurstDirectCall_Initialize_m78829C817F4BC28716C599C091E48BAA34626EE1 (void);
extern void GetUtf8CharCount_0000005BU24BurstDirectCall__cctor_mD71AB53977BDAE558D564E7E0AF3E69C9F2250B9 (void);
extern void GetUtf8CharCount_0000005BU24BurstDirectCall_Invoke_m4616139239633EBAB8BB6BED79D38FD0A8B0443B (void);
extern void Interpolate_0000005DU24PostfixBurstDelegate__ctor_m5B86779F3B9E30AEDB93DAF7EA62CA4CC985EE75 (void);
extern void Interpolate_0000005DU24PostfixBurstDelegate_Invoke_m7C73636D7D8B6DB45552E92CAD39C1CFB12DADFC (void);
extern void Interpolate_0000005DU24PostfixBurstDelegate_BeginInvoke_mA2324437380D5C0E55759871D2A98BA76E48F9BC (void);
extern void Interpolate_0000005DU24PostfixBurstDelegate_EndInvoke_mBB2E27AAC57265D393D3E8929150E517336F685E (void);
extern void Interpolate_0000005DU24BurstDirectCall_GetFunctionPointerDiscard_mBF7DB9E08DE3F2F293C199C151F5A84CE642430A (void);
extern void Interpolate_0000005DU24BurstDirectCall_GetFunctionPointer_m268118846C5A8C0C7DE4B4FFBBFDC61B34FF32E9 (void);
extern void Interpolate_0000005DU24BurstDirectCall_Constructor_mFCEEA6B3AEE353A6F789BAC3914791D1D8A1D25C (void);
extern void Interpolate_0000005DU24BurstDirectCall_Initialize_m665EE9EABC86816190C6FBE93276D1CB5B5D20DD (void);
extern void Interpolate_0000005DU24BurstDirectCall__cctor_m942A7518A8A3051990E8A46F5915FC7F3AF06D8C (void);
extern void Interpolate_0000005DU24BurstDirectCall_Invoke_mE73EC445B02C885A450CADE1CD6C9CABE709ABBD (void);
extern void GetUtf8CharCount_00000062U24PostfixBurstDelegate__ctor_m0529FB7A7FF7D6A72441C64DE212BE5E3714A144 (void);
extern void GetUtf8CharCount_00000062U24PostfixBurstDelegate_Invoke_mF56F9B4155326EAD3DB1C8FD6D77133EC82DA501 (void);
extern void GetUtf8CharCount_00000062U24PostfixBurstDelegate_BeginInvoke_m718F448C48E8651FED2E6F0B1AD9F86C203C0D66 (void);
extern void GetUtf8CharCount_00000062U24PostfixBurstDelegate_EndInvoke_m27EAF6E2DCF48BDDBC5B49685391CA193F981776 (void);
extern void GetUtf8CharCount_00000062U24BurstDirectCall_GetFunctionPointerDiscard_m42FDB1C08FBFDEC60B54E97795FB925D62F604C7 (void);
extern void GetUtf8CharCount_00000062U24BurstDirectCall_GetFunctionPointer_m6A5B1B0509A5A5E680A7EDD4B4260DA63AC0F694 (void);
extern void GetUtf8CharCount_00000062U24BurstDirectCall_Constructor_mBEED39AB02298735A04E7995E46CC9A46DCDE47B (void);
extern void GetUtf8CharCount_00000062U24BurstDirectCall_Initialize_mF9146318DC41EC73E4D94829A943E2AB650AD271 (void);
extern void GetUtf8CharCount_00000062U24BurstDirectCall__cctor_m8DBBB60FC2CC4901E74DF7EF1DE788C88DB37B0D (void);
extern void GetUtf8CharCount_00000062U24BurstDirectCall_Invoke_m69AADEC80068521FCE8E5C8E093EF5106EB1A27E (void);
extern void Interpolate_00000064U24PostfixBurstDelegate__ctor_mB739B7EA79C225DE564230205979C9549B56C3F3 (void);
extern void Interpolate_00000064U24PostfixBurstDelegate_Invoke_mAE1F2F6F6F792AC1127F12F4ED2714E96847BA20 (void);
extern void Interpolate_00000064U24PostfixBurstDelegate_BeginInvoke_m8EB6A54184C9F355387D49808C73317C5DF21617 (void);
extern void Interpolate_00000064U24PostfixBurstDelegate_EndInvoke_m138B878DC5FD9FAA17948CC9F37E9291DE1137C6 (void);
extern void Interpolate_00000064U24BurstDirectCall_GetFunctionPointerDiscard_m3990F2DD81C95A2A5E42AE13470FE1116991FF3E (void);
extern void Interpolate_00000064U24BurstDirectCall_GetFunctionPointer_m521EC6AC38A3728FAA5F5E60AC21D16B27ED3223 (void);
extern void Interpolate_00000064U24BurstDirectCall_Constructor_mF67C21BF6FDE1D1EAA8814B4C21051B1DDC8C013 (void);
extern void Interpolate_00000064U24BurstDirectCall_Initialize_mD80B2BA2BD9C6059F79705ADF77BA8E4C7A032FF (void);
extern void Interpolate_00000064U24BurstDirectCall__cctor_m486A3BCED0D8F220A21C7C3CB226ECBBD8CCFAA3 (void);
extern void Interpolate_00000064U24BurstDirectCall_Invoke_m3449C6EC0667EA358827B66E6FC219A0706C286D (void);
extern void GetUtf8CharCount_00000069U24PostfixBurstDelegate__ctor_m2D1790D86685C94BCA99BE73C64F6AD704AD999F (void);
extern void GetUtf8CharCount_00000069U24PostfixBurstDelegate_Invoke_m4FCD8FEE981229F79C8CF4FAEAA743D49546B21F (void);
extern void GetUtf8CharCount_00000069U24PostfixBurstDelegate_BeginInvoke_mAC67321B315A9E81E77080927E3A8D9900C0FE9B (void);
extern void GetUtf8CharCount_00000069U24PostfixBurstDelegate_EndInvoke_mF56CCFDFEE7DC5123EC9027A3F55E27D0314C284 (void);
extern void GetUtf8CharCount_00000069U24BurstDirectCall_GetFunctionPointerDiscard_mF6B295FE4A1EA67A01D17C06A3C8CD84E9E81561 (void);
extern void GetUtf8CharCount_00000069U24BurstDirectCall_GetFunctionPointer_mE0ADF3571453DB6A03DB9FF9E3480F09626F6FC9 (void);
extern void GetUtf8CharCount_00000069U24BurstDirectCall_Constructor_mA942C8EA2C39571323B62BFB2B534493719A49BA (void);
extern void GetUtf8CharCount_00000069U24BurstDirectCall_Initialize_mE289B4A41818417817B3423C4BCBB1F820A5B7A8 (void);
extern void GetUtf8CharCount_00000069U24BurstDirectCall__cctor_mA0F7101D737C5500FC8E40B79976A02B7D641438 (void);
extern void GetUtf8CharCount_00000069U24BurstDirectCall_Invoke_m115C6C904CA64010D08000C8CBCA52B4ED4C1640 (void);
extern void Interpolate_0000006BU24PostfixBurstDelegate__ctor_mDE408891A70CE5646443E975BBBBE6D9795E9564 (void);
extern void Interpolate_0000006BU24PostfixBurstDelegate_Invoke_m8C973BC99E6CF4B945B2460DCE7D8B707594EB90 (void);
extern void Interpolate_0000006BU24PostfixBurstDelegate_BeginInvoke_m2C9FFC9EBB1EA704879BAA5C9E90D27B9C405599 (void);
extern void Interpolate_0000006BU24PostfixBurstDelegate_EndInvoke_m83A477D4786ACB52914E8F7139C7432816BF85BC (void);
extern void Interpolate_0000006BU24BurstDirectCall_GetFunctionPointerDiscard_m514D4C32DBA08AA647C39D8DDD96BE6DDB47649D (void);
extern void Interpolate_0000006BU24BurstDirectCall_GetFunctionPointer_m574A1B355FFD8C8A6334EFAD160516660BFB7B1C (void);
extern void Interpolate_0000006BU24BurstDirectCall_Constructor_m88B9C613B32A9F37E5A629E83C06CFF34AA4F67D (void);
extern void Interpolate_0000006BU24BurstDirectCall_Initialize_m0EF95D8DF397390BF5ABA357F52AB5CAC422B875 (void);
extern void Interpolate_0000006BU24BurstDirectCall__cctor_mFF36E67AA47EDE6A0FB7B2048B3C2FE58329E860 (void);
extern void Interpolate_0000006BU24BurstDirectCall_Invoke_mD15D327400D225664AFB10B75C9818821A8CF7D5 (void);
extern void GetUtf8CharCount_00000070U24PostfixBurstDelegate__ctor_m38A5F6AF460C6EBBF7D76A7766D29EEC3414124B (void);
extern void GetUtf8CharCount_00000070U24PostfixBurstDelegate_Invoke_m8C2E08DB5A2E7EE2802E5964CF00C78BF0A2AA23 (void);
extern void GetUtf8CharCount_00000070U24PostfixBurstDelegate_BeginInvoke_m56A765F089421DF36C0F672BDCE859E33B9593C5 (void);
extern void GetUtf8CharCount_00000070U24PostfixBurstDelegate_EndInvoke_m748B7DFA3FA01BE5415A2316C08C6C752F848EAE (void);
extern void GetUtf8CharCount_00000070U24BurstDirectCall_GetFunctionPointerDiscard_m3A8DB731D29A6E4377CE5FADA202E5D73C950B39 (void);
extern void GetUtf8CharCount_00000070U24BurstDirectCall_GetFunctionPointer_mBBD736120DA22EC80A9C5884EF39860BA680F188 (void);
extern void GetUtf8CharCount_00000070U24BurstDirectCall_Constructor_m2309F30D6E0F06F17C683123C36B0D5BDE06BE64 (void);
extern void GetUtf8CharCount_00000070U24BurstDirectCall_Initialize_m5B1A1A7D47C97CFAADC097B6C5E4E8FA6371BCCA (void);
extern void GetUtf8CharCount_00000070U24BurstDirectCall__cctor_m56E9247DBD538CC3F585D6E98DFBE28141C66D87 (void);
extern void GetUtf8CharCount_00000070U24BurstDirectCall_Invoke_m92DE72BC9F966607139FAE3EBFE312E3A749F67E (void);
extern void Interpolate_00000072U24PostfixBurstDelegate__ctor_m7EAA3A35BEC5612BD0438CAF52C26D404196E9E5 (void);
extern void Interpolate_00000072U24PostfixBurstDelegate_Invoke_mC28A2A5B98FEAF538B7C4B315C212BB64ADBFDB4 (void);
extern void Interpolate_00000072U24PostfixBurstDelegate_BeginInvoke_mA41379D0FF69121305386F996201822758041A55 (void);
extern void Interpolate_00000072U24PostfixBurstDelegate_EndInvoke_mA0E96EF11E7BD5AD6C63EBC1808C6625E20CC793 (void);
extern void Interpolate_00000072U24BurstDirectCall_GetFunctionPointerDiscard_mACE8D24570A4660EEAE4B6891D4986AC84A79B50 (void);
extern void Interpolate_00000072U24BurstDirectCall_GetFunctionPointer_mFF4A21F4C63A196CB77E7801FC763DCAD891FBBC (void);
extern void Interpolate_00000072U24BurstDirectCall_Constructor_mD416FF61748512C899497A6F43E4B44DB262BC8E (void);
extern void Interpolate_00000072U24BurstDirectCall_Initialize_mD117F843860D20C34B5D59C4E31FCAFA68E440BB (void);
extern void Interpolate_00000072U24BurstDirectCall__cctor_m68A882AEF0DBEE590B6AA0736B9E4BF904D1E020 (void);
extern void Interpolate_00000072U24BurstDirectCall_Invoke_m241DEF0EFAE37E7EBB1374C46873F31BE2238ED3 (void);
extern void ManagedMotionData_InvokeOnCancel_m73240D0D9B2ACE57785592A78ADA22910624A9BF (void);
extern void ManagedMotionData_InvokeOnComplete_m5CDE09F72CF3E6B4701EE5AFA09AF288C1F3A6E5 (void);
extern void ManagedMotionData_InvokeOnLoopComplete_m0AB33D63FC13012AC691C4F93D444AF4E5342D65 (void);
extern void MotionData_get_TimeSinceStart_m98F66E013764C57EEB2D4D31F5C0EBF3D51F8828 (void);
extern void MotionData_Update_m5B3BC2C8E45492DB4682325ACCFBC36BE289538F (void);
extern void MotionData_Complete_m734EE33FAC8939EC5F4DFADDD1BA62B42DE7F1C0 (void);
extern void MotionData_GetClampedCompletedLoops_mB421308254CDA470E5BFB7E6B13DE758B4A5B837 (void);
extern void MotionData_GetEasedValue_mDE2CC8C13F67B1B2D52651726F564695895E9360 (void);
extern void MotionState_get_WasStatusChanged_m9618DFD6BA628253A183FE3C50464674CDCEA95A (void);
extern void MotionState_get_WasLoopCompleted_m9721F9B229DD0C67B41514B12C81B9F9AA926B8C (void);
extern void MotionParameters_get_TotalDuration_mB7A505F6BCE808D3071AF959E08FC6B1ECCD9047 (void);
extern void MotionHandleLinker_Register_mE38DFBF513DC0FFE77F26B888E936CD44EB53EA6 (void);
extern void MotionHandleLinker_OnDisable_mDDF2CE467D10D992A189ACAE16CD85FB0FD1361C (void);
extern void MotionHandleLinker_OnDestroy_m64BAA94D1126CEC5ED6FF0937465DD910F731101 (void);
extern void MotionHandleLinker__ctor_m0AD731C8D1FA4F1699EC3597B15CC1F7D8AC8683 (void);
extern void MotionManager_get_MotionTypeCount_m747275F1C694CA07B477CA9E940FCFE9C5051A5F (void);
extern void MotionManager_set_MotionTypeCount_m62CC04C3C1B4AD79629B2F921D9E4665DD783340 (void);
extern void MotionManager_GetDataRef_m51519EC669AF4A72192ECD1B44279959134E8CBE (void);
extern void MotionManager_GetManagedDataRef_m56749CE994D083F22E7FF3458B71A9B04C3FFDAF (void);
extern void MotionManager_GetDebugInfo_m3D283A75EC7440238ECC61CF14EC00285B30D3AB (void);
extern void MotionManager_Complete_m2E77217D54FCF03CDCBE5F628990FA6C9FC64859 (void);
extern void MotionManager_TryComplete_m7BECEFD3EB4321B741D8ABDB3B999A2B065BD611 (void);
extern void MotionManager_Cancel_mB9BE86260528DFA714770AB0212729CD4726E4F1 (void);
extern void MotionManager_TryCancel_m3F8F15BDC21AEBB3EEDB58CC70B604A8C9EE972A (void);
extern void MotionManager_IsActive_mF4852406A0DD2636E235C9D61F213B0F33063258 (void);
extern void MotionManager_IsPlaying_m73338D2E14A34995AB74EB371311EB23611F819C (void);
extern void MotionManager_SetTime_mCA80BF9864CB8C61964C1AE742068029E3619682 (void);
extern void MotionManager_AddToSequence_m186353CB9EB053D8AF67815013CE37602D327DB0 (void);
extern void MotionManager_GetMotionType_m92CC11F730DDD74C17C0040F35B3B23ED422168A (void);
extern void MotionManager_CheckTypeId_mEFEB7AD899D19579BD664224BD414C6A366792CB (void);
extern void MotionDebugInfo_get_EqualityContract_m8AC08AB50D4ECDB64DC198F74A524461BAA726CF (void);
extern void MotionDebugInfo_ToString_m8B304B456099E348430301E13657461FC73F7C1A (void);
extern void MotionDebugInfo_PrintMembers_m05CD9A9D04490B0880585FB6908063073740987F (void);
extern void MotionDebugInfo_op_Inequality_mEEEFF7436034AD45AAAB38E7A1A06ADEE1CF7C67 (void);
extern void MotionDebugInfo_op_Equality_mE54C528DD0B27CED2FC4A2AE17F831A88958018C (void);
extern void MotionDebugInfo_GetHashCode_mB69D5E3C742E396399D0D5A9B05548AF150B39D6 (void);
extern void MotionDebugInfo_Equals_m9658FAF9F09235C9CEC4C9713F98FE44618E1C1B (void);
extern void MotionDebugInfo_Equals_m88DF65BC06170F09C2BCF6E483FAD39776AA85E7 (void);
extern void MotionDebugInfo_U3CCloneU3EU24_mDD0D18EF003C9EF2056E96FD12FB59752867FE2C (void);
extern void MotionDebugInfo__ctor_m8EFEF8142374DE6DBAEC9F35DF69CDAC42AF983A (void);
extern void MotionDebugInfo__ctor_mD0436D1CCB3B2E357110455A51A6F80EAE89A23B (void);
extern void PlayerLoopHelper_add_OnInitialization_mF7E009A43A8F6F01ABD2E8DAF60ACCDD79896BB2 (void);
extern void PlayerLoopHelper_remove_OnInitialization_m3334632B149F9E16A6070B28E815C88EDBAEB235 (void);
extern void PlayerLoopHelper_add_OnEarlyUpdate_m0C345297B551BA599AAAF21FCE9A6CF1398E6E57 (void);
extern void PlayerLoopHelper_remove_OnEarlyUpdate_m3A5867C6A857C6B9E743F95632969BDF0652C46E (void);
extern void PlayerLoopHelper_add_OnFixedUpdate_mFE95ED7DF1598DFF799D9242F92203D5BE4259CB (void);
extern void PlayerLoopHelper_remove_OnFixedUpdate_m1E66E94A0E6A30D0275E50AEC061A59772FDF32D (void);
extern void PlayerLoopHelper_add_OnPreUpdate_m2BD427F082819A22CC8B0069D064C933321C17C3 (void);
extern void PlayerLoopHelper_remove_OnPreUpdate_m615CB0A1D8D3DE73AD83E0E7004100DF66C666DB (void);
extern void PlayerLoopHelper_add_OnUpdate_mF1902FE292B2DEC2790E4D6C81E5B4A5A449A159 (void);
extern void PlayerLoopHelper_remove_OnUpdate_m48E7BEC798CBE7F6F74790BB4DD2AF49B0F6ABE0 (void);
extern void PlayerLoopHelper_add_OnPreLateUpdate_m05F01A8B248045F7F14088D60A680109982700E1 (void);
extern void PlayerLoopHelper_remove_OnPreLateUpdate_mACAA88D3BBACED3CABC5225C4316B3D76246DFF2 (void);
extern void PlayerLoopHelper_add_OnPostLateUpdate_m6858C8FCE634E8205361D5E0E46E03EB056FB872 (void);
extern void PlayerLoopHelper_remove_OnPostLateUpdate_mE902B3EAAA05933426AB4E9CA6D26254059B0890 (void);
extern void PlayerLoopHelper_add_OnTimeUpdate_m5BEA07CF6C8B710F77BD4B4EF861AA088075180E (void);
extern void PlayerLoopHelper_remove_OnTimeUpdate_mBE589904899E9B92CD70F77B9D361A888C271BBC (void);
extern void PlayerLoopHelper_Init_m9ABA6A108AD94794C34E196F726980295A136727 (void);
extern void PlayerLoopHelper_Initialize_m8B8E24D064532DDC3CC260DD920F3105075D06BA (void);
extern void PlayerLoopHelper_InsertLoop_m8DB8425DD3866B598AA965ECF64474E491DB24F7 (void);
extern void PlayerLoopHelper_FindLoopSystemIndex_m5F564D4474F1835C2CA53106CF8AC582FF15E03A (void);
extern void PlayerLoopHelper_InsertRunner_m4DBAF2DFCA0FF9A11BE7E03B5AB27658AFB8C254 (void);
extern void U3CU3Ec__cctor_m8C857AC171AE9EF434D2B205299C9961F949FE09 (void);
extern void U3CU3Ec__ctor_mD5E0B818D0A5B158D0D4154EA7F3D8E5A2097F9A (void);
extern void U3CU3Ec_U3CInitU3Eb__25_0_m7ADEE451C4DB0FC7AB677F6E41A6FE777276ECC9 (void);
extern void U3CU3Ec_U3CInitU3Eb__25_1_m017A1529995E50E2C0184DFC76EFBF9E54C0BD03 (void);
extern void U3CU3Ec_U3CInitU3Eb__25_2_mF636189B9B3BBBB028926C0BA7C5EF46FEEB4893 (void);
extern void U3CU3Ec_U3CInitU3Eb__25_3_m6BA627FCAC7DA587C2904996A639DD2F50D29A13 (void);
extern void U3CU3Ec_U3CInitU3Eb__25_4_m378BF418289DE09847484A648E4C8BFED4559162 (void);
extern void U3CU3Ec_U3CInitU3Eb__25_5_m20FD33C16BFCD5A8F76FCBC1E4C04DF3F6A64436 (void);
extern void U3CU3Ec_U3CInitU3Eb__25_6_m2038A7EE618E69D8F0456DCD367C2957B8D16BA6 (void);
extern void U3CU3Ec_U3CInitU3Eb__25_7_mD2DC8735BD62E1CAC6890A209CABC8B051179FA7 (void);
extern void U3CU3Ec_U3CInitializeU3Eb__26_0_mE46DCB5B9449456D27993068E85B508C8927A072 (void);
extern void U3CU3Ec_U3CInitializeU3Eb__26_1_m6A8D71D2B30B8294B654CEAA58A1CA7215B1094D (void);
extern void U3CU3Ec_U3CInitializeU3Eb__26_2_m38DCEDC1BC57EEA96063D092B2E5889295035C25 (void);
extern void U3CU3Ec_U3CInitializeU3Eb__26_3_m9D8D7BEA0C3D7586CB7516ECD634823AF1B9C688 (void);
extern void U3CU3Ec_U3CInitializeU3Eb__26_4_m1CC6A133965E18E920E4BF1115D3F0C4C86A51A5 (void);
extern void U3CU3Ec_U3CInitializeU3Eb__26_5_m64A223761A6978C7EDA3D6B53EADB3B9F96713E7 (void);
extern void U3CU3Ec_U3CInitializeU3Eb__26_6_m9F6789B3A346068E7EEB9C2C17EB5C98770F98E5 (void);
extern void U3CU3Ec_U3CInitializeU3Eb__26_7_mC4D811659D2E61FFB507AE0898D4F0C76EFD798C (void);
extern void U3CU3Ec__DisplayClass29_0__ctor_m0EA35057BD72E9B5BE70FBA7AE32B93BC7B358E7 (void);
extern void U3CU3Ec__DisplayClass29_0_U3CInsertRunnerU3Eb__0_mEC0B2B4112F3AC39A2F4A8631A709FB8FB7C4191 (void);
extern void PlayerLoopMotionScheduler__ctor_mA7245EDD1685E56EB3B0F436AC0BEEB84E56FBEB (void);
extern void RandomHelper_NextFloat_m3338E86CBEB4C5EDC0FE480B5C0740FC95788E2E (void);
extern void RandomHelper_NextFloat2_mFD2CD5917D9198E64B3D25BECA110516042FDF2A (void);
extern void RandomHelper_NextFloat3_m2A1A7A5F2408BD0BE0D364C7346B74B521C82F2E (void);
extern void RandomHelper_GetHash_mA7D5AA198E0AD1DE48661A2EDE1B9434823E00F7 (void);
extern void RandomHelper_Create_m54A191408DF34050ADB6054F24211E2709EC04F3 (void);
extern void RewindableAllocatorFactory_CreateAllocator_mDC1E8527A976304B6E5E2DBA18C4DBB935650AC7 (void);
extern void RewindableAllocatorFactory_Initialize_mD79E43076F88992056BE03CDE7AD3AB5C23E05AA (void);
extern void RewindableAllocatorFactory_Dispose_mE7E72976F3D27BA456F680633E3C6B6CFAF1B954 (void);
extern void RewindableAllocatorFactory__cctor_mE7C238E2892A34E5FAD306E74DE33A233C75E152 (void);
extern void RichTextParser_GetSymbols_mF811BDC1CA5D24DDD4E4968D2B619C5D03B31284 (void);
extern void RichTextParser_GetSymbols_mCF57E23A10E222A500E2C53E75EB8EB46ADCF91C (void);
extern void RichTextParser_GetSymbols_m4FE46D9436A58AA65CC64074DA4FD2774C66C94E (void);
extern void RichTextParser_GetSymbols_mEDE88F65916187F0FBEA7A59A3294DD355264E7F (void);
extern void RichTextParser_GetSymbols_m3533F464A56BA61B156FC081183736B6286E93A2 (void);
extern void RichTextParser_GetSymbolsU24BurstManaged_mCB0B02787E7F5738B17A69FD97FF45FF39110BEC (void);
extern void RichTextParser_GetSymbolsU24BurstManaged_mE61FC9690103F5DA82A3D7C2C6A5707C151D1C21 (void);
extern void RichTextParser_GetSymbolsU24BurstManaged_m0A527CFAE7EAFAA07848BCC986B59BFACC567B7F (void);
extern void RichTextParser_GetSymbolsU24BurstManaged_mE7DB939561F00F90E19ECDC6A16E5FC8EDA80CAA (void);
extern void RichTextParser_GetSymbolsU24BurstManaged_mEB863F174B9C009A9703556847E133C2415ED6CD (void);
extern void GetSymbols_000000FFU24PostfixBurstDelegate__ctor_m35D7369B1C4771CEADB6ACB00C4C15695762B2AD (void);
extern void GetSymbols_000000FFU24PostfixBurstDelegate_Invoke_m5870226E9743CC454174201646B085EBC4BC8404 (void);
extern void GetSymbols_000000FFU24PostfixBurstDelegate_BeginInvoke_m48984B629FA7B2F64AF27114B92C75C1BBBC5AA2 (void);
extern void GetSymbols_000000FFU24PostfixBurstDelegate_EndInvoke_mD30CBEBB4DBF3D11442C287D9D28FB5E122193E9 (void);
extern void GetSymbols_000000FFU24BurstDirectCall_GetFunctionPointerDiscard_m81E66EEFDD104C0CCF2F9DEA4473F8323E176B69 (void);
extern void GetSymbols_000000FFU24BurstDirectCall_GetFunctionPointer_m1F6C61C45B83BFFAA470E1201F39B25A2BF02C9F (void);
extern void GetSymbols_000000FFU24BurstDirectCall_Constructor_m11F671F66254090E963F22EE59C6D05DB6CC3B58 (void);
extern void GetSymbols_000000FFU24BurstDirectCall_Initialize_m1584FC0A852EBFEA6637076F430F8828C40CAF81 (void);
extern void GetSymbols_000000FFU24BurstDirectCall__cctor_mD74EF8FF8C262E15585A3151022C710E47BCD837 (void);
extern void GetSymbols_000000FFU24BurstDirectCall_Invoke_mFCB7C675827F21C3D281DA277E7EFB98A2051759 (void);
extern void GetSymbols_00000100U24PostfixBurstDelegate__ctor_mA8EB54347DA52A2BA3D67472865AA32398B443A0 (void);
extern void GetSymbols_00000100U24PostfixBurstDelegate_Invoke_m3C4B7AFE9E9E8C65453B932FA0E064994F78D756 (void);
extern void GetSymbols_00000100U24PostfixBurstDelegate_BeginInvoke_m0DECF8E5CE9BD6A81248045DA4DC024D673DF245 (void);
extern void GetSymbols_00000100U24PostfixBurstDelegate_EndInvoke_m069893E24F6C894FB62AC2205C149C34A13698E1 (void);
extern void GetSymbols_00000100U24BurstDirectCall_GetFunctionPointerDiscard_mACD2A59355FB58E0783BA27A388296DBEAC5DC69 (void);
extern void GetSymbols_00000100U24BurstDirectCall_GetFunctionPointer_m9698ABFEB9E864F9845A6AF0C3A8D3A8ED8FC7C1 (void);
extern void GetSymbols_00000100U24BurstDirectCall_Constructor_mFBCA8F53EF9E29EFBAAF4076BB33A21DD444ABC5 (void);
extern void GetSymbols_00000100U24BurstDirectCall_Initialize_m2ED4FC75FB80B60DDDE99BA83EC0EF77CEA3E10F (void);
extern void GetSymbols_00000100U24BurstDirectCall__cctor_mDBB7B2BF251D044240EA9FFE42D5727F458C8B01 (void);
extern void GetSymbols_00000100U24BurstDirectCall_Invoke_m3AD225C16366327E15508EC0D090CCF496E0C062 (void);
extern void GetSymbols_00000101U24PostfixBurstDelegate__ctor_mED5A4CD31BE93723E41823D71F1557F177442B82 (void);
extern void GetSymbols_00000101U24PostfixBurstDelegate_Invoke_m174779ADA36D148A20AAE80451E38B7CAA7DFCC1 (void);
extern void GetSymbols_00000101U24PostfixBurstDelegate_BeginInvoke_mBA5022300B52AE062646BD1F3926650FAB098C9F (void);
extern void GetSymbols_00000101U24PostfixBurstDelegate_EndInvoke_m22062317688E0D31191DC4DB1C84283CE2CF4CEE (void);
extern void GetSymbols_00000101U24BurstDirectCall_GetFunctionPointerDiscard_m61B28F524C8083FC80A12F871A862905B0A86982 (void);
extern void GetSymbols_00000101U24BurstDirectCall_GetFunctionPointer_mA946F913D8C14F49AAF7EF9ED5C62DAB44D5CFE1 (void);
extern void GetSymbols_00000101U24BurstDirectCall_Constructor_mC2C5C6C63B2FD6B1533E850169BA5504A2B31D53 (void);
extern void GetSymbols_00000101U24BurstDirectCall_Initialize_m8E5F9C59B4BA80DCB52DFC367E8FFB5A928F0A6D (void);
extern void GetSymbols_00000101U24BurstDirectCall__cctor_mE1A8FB7CB412C08843658E1E2E3078BC1C2E687F (void);
extern void GetSymbols_00000101U24BurstDirectCall_Invoke_m170AE72CFE768FA07C14AC6B6B9125A033218E4A (void);
extern void GetSymbols_00000102U24PostfixBurstDelegate__ctor_m5EB1C8BC271ACC37534494E0C4E1388F0AE675A5 (void);
extern void GetSymbols_00000102U24PostfixBurstDelegate_Invoke_mD3800FB30468B6C33B91706CF2793821C8F4C613 (void);
extern void GetSymbols_00000102U24PostfixBurstDelegate_BeginInvoke_m1BF80F27D6A2E40728379DEFD1F74E8ACC9B7EFA (void);
extern void GetSymbols_00000102U24PostfixBurstDelegate_EndInvoke_mBDDA0F1C24D6989F2925E28AB4EBDE3D3C222DE0 (void);
extern void GetSymbols_00000102U24BurstDirectCall_GetFunctionPointerDiscard_mF5A12E3FEFBB855DDA31AF34593C2E529BF9E39A (void);
extern void GetSymbols_00000102U24BurstDirectCall_GetFunctionPointer_m36F5FB80BF437D598C379FF58741347171DEB656 (void);
extern void GetSymbols_00000102U24BurstDirectCall_Constructor_m0FE05B97F17E84AC5E44217CA8C35766EDECDE42 (void);
extern void GetSymbols_00000102U24BurstDirectCall_Initialize_m64F3F419D1DD270C0B8DD290D08690B5C2FD9694 (void);
extern void GetSymbols_00000102U24BurstDirectCall__cctor_m32B09B5A980322FF13F99D2B2408C3CDD05E8BA7 (void);
extern void GetSymbols_00000102U24BurstDirectCall_Invoke_m0D709CF5777A1908B0F5B585B0DF03FCDE7F9C05 (void);
extern void GetSymbols_00000103U24PostfixBurstDelegate__ctor_mF9C892DB1635DE1251A5407429C59A89AE46D1AA (void);
extern void GetSymbols_00000103U24PostfixBurstDelegate_Invoke_m50C0F2B4A6E2129AF545EE4DB3B7CAC3E5C3F948 (void);
extern void GetSymbols_00000103U24PostfixBurstDelegate_BeginInvoke_m977DD135D92279BD0BB8EB5CBE2A8C549FB9D9D3 (void);
extern void GetSymbols_00000103U24PostfixBurstDelegate_EndInvoke_m9858543A920459969DE0C7F4D4F7291C3CD5816C (void);
extern void GetSymbols_00000103U24BurstDirectCall_GetFunctionPointerDiscard_m52BCAE4326B7251EC2706C2BF1533B2ABE34C120 (void);
extern void GetSymbols_00000103U24BurstDirectCall_GetFunctionPointer_mBA715D0D9DDBB94A9024FF9B2506EEE55ED2C378 (void);
extern void GetSymbols_00000103U24BurstDirectCall_Constructor_mEA7987C4FED3B450636315B983ABCBB89C995374 (void);
extern void GetSymbols_00000103U24BurstDirectCall_Initialize_m237CF8923A1A190F1EBD84C7A8176CEA2AF6B882 (void);
extern void GetSymbols_00000103U24BurstDirectCall__cctor_m680E754F1455A271CC3CF4C6EEDEA17C4DCF008B (void);
extern void GetSymbols_00000103U24BurstDirectCall_Invoke_mFEB202B8ECE58396A2AE273ED633AE865347FD84 (void);
extern void RichTextSymbol32Bytes__ctor_m65F97C28A14578AC22B6A40B08A34A1C30D3976F (void);
extern void RichTextSymbol64Bytes__ctor_m11C4E0E45BB649E0CA9F39316211600F61633141 (void);
extern void RichTextSymbol128Bytes__ctor_mE728D7EE402D4EBF8445D4439494F167EA3BD6DE (void);
extern void RichTextSymbol512Bytes__ctor_mCC11833B715889839D15FEA95AEC3775A3F93DC0 (void);
extern void RichTextSymbol4096Bytes__ctor_mC0EDF5BBF1438668B5C2528CCA56122EB039D7A2 (void);
extern void SparseIndex_get_Index_m7BD01B8761FC3B8060E009BBB229CEE4061D6712 (void);
extern void SparseIndex_get_Version_m0485D8AA73020E06308E9D86963AB6F8ADE78D0D (void);
extern void SparseIndex__ctor_mE2B01B52B0AAF9C71B3A3348D72571D1F459EBF6 (void);
extern void SparseIndex_Equals_m796B8C7DD085DDE7E0D5A96E6AFF1FC6428C5774 (void);
extern void SparseIndex_Equals_m6BD153A61F484812F71DCE2BED5DA7A5A23D3960 (void);
extern void SparseIndex_GetHashCode_m219B7BA026D67923B2C8AA5F5B065535559D1C20 (void);
extern void SparseSetCore_get_Capacity_m19216C0D96DEB93B27BFEECFCB25359937022F3B (void);
extern void SparseSetCore__ctor_mB5E552E9C3313F0B9E3665B22A58FD367F254AEF (void);
extern void SparseSetCore_EnsureCapacity_mA7A43DB29C33527D4F0CA5AD784D3EB7882D9263 (void);
extern void SparseSetCore_Alloc_m999734CBD13AF233BB03B873D596C80C3AB0E250 (void);
extern void SparseSetCore_Free_m9A287946704E380B12E7752060A66DADFA5361D0 (void);
extern void SparseSetCore_GetSlotRefUnchecked_mE1AE7761033A09AD4A57D0454325803FC7F88CFD (void);
extern void SparseSetCore_Reset_m7B88F0AE3BA9306CCA105631FBE24194A8DA6E19 (void);
extern void Slot_Equals_m0BA52037992DC515671A8820F41CBF40A02A7AAF (void);
extern void Slot_Equals_m771BA52BD099581D2888B34D1D0E21EF771364C1 (void);
extern void Slot_GetHashCode_m65E4135D452912AF96D1F2671C9D7C00AD331B1B (void);
extern void MotionTaskSourceBase__ctor_mDE26FF8D3E95E91306C0C0016BCB04B4D63A50DA (void);
extern void MotionTaskSourceBase_OnCancelCallbackDelegate_mE79D5648898486724111143DD3B70CE0A63345E7 (void);
extern void MotionTaskSourceBase_OnCompleteCallbackDelegate_m9D6F37645AB1AA5ADECA2FFFD4BE11C3554B753F (void);
extern void MotionTaskSourceBase_OnCanceledTokenReceived_mB5D401090779BF95E3641448D549BD7C942CBF49 (void);
extern void MotionTaskSourceBase_Initialize_m01AB7793837D080FD456897CBFE918C7EE878510 (void);
extern void MotionTaskSourceBase_ResetFields_m8A6D35B8FCD92741CEC0A00AB1F09FD903ACB920 (void);
extern void MotionTaskSourceBase_RestoreOriginalCallback_m68136C9AEE435B9E6969CC75347D0057BD45C5CF (void);
extern void MotionTaskSourceBase_DisposeRegistration_m176979D137681D051A6CFCD4753CD2BE57C421A6 (void);
extern void MotionTaskSourceBase_RegisterWithoutCaptureExecutionContext_m90EBDDE21DB05AD0B291EE2759422EC51751B5AD (void);
extern void U3CU3Ec__cctor_mAB541A66D5F9B999819297CEBC15F3FACE88F8AD (void);
extern void U3CU3Ec__ctor_mF3738D2A3CDA7EFE80DC93D2E52F02139C8E8B4B (void);
extern void U3CU3Ec_U3CInitializeU3Eb__15_0_mABBB9414B8074B78B5BA1189D8066DEF79E11611 (void);
extern void ValueTaskMotionTaskSource_get_NextNode_mADC41651D9A7E00C28870C09754884E38F0D0693 (void);
extern void ValueTaskMotionTaskSource_FromCanceled_m5E4E640B7B81B53A9E31E04BEA10E4F8CB2E29D5 (void);
extern void ValueTaskMotionTaskSource__ctor_m5118C6DCA8548CA61F251FD94036686C96876DD1 (void);
extern void ValueTaskMotionTaskSource_Create_m41A7960567AFE08EF0A3E42CAE4215EF311C3F26 (void);
extern void ValueTaskMotionTaskSource_get_Version_mA827658763305BC0E4AC5B5416830BA9436F1C79 (void);
extern void ValueTaskMotionTaskSource_GetResult_m8C5F4793F01F1C7207761CD1896939A2E1BF80B4 (void);
extern void ValueTaskMotionTaskSource_GetStatus_mDB1A7F7017B4598431C86B9F2EEE4D036860F4D6 (void);
extern void ValueTaskMotionTaskSource_OnCompleted_m95A60E7B4AFD1C9C5868A4735AE01043AAE983F9 (void);
extern void ValueTaskMotionTaskSource_TryReturn_mD076EF733024FAEBA325BF87C231483AF4B41EE2 (void);
extern void ValueTaskMotionTaskSource_SetTaskCanceled_m54DAF29A8E6605F5A2A0DEF8289B3BEEDF1CC5AC (void);
extern void ValueTaskMotionTaskSource_SetTaskCompleted_m732D8B92D776DD35AC6F2DB2C26E9FDF38688999 (void);
extern void Utf16StringHelper_WriteInt32_m5503DF7D43D0975805353F589E8CE2DCE4C9DF32 (void);
extern void Utf16StringHelper_WriteInt64_m4B80B2DF130D854A0E6029FD00F816CD4E67A728 (void);
extern void VibrationHelper_EvaluateStrength_m3403B4C78E150127098C54564958BAF73D16D992 (void);
extern void VibrationHelper_EvaluateStrength_mA5678D93AF9F64ED6E8051FC0A5A58B6AAB3F8E1 (void);
extern void VibrationHelper_EvaluateStrength_m4320BE7D6BBF388BD0B42AE304B5C0F6A97BBE8B (void);
extern void VibrationHelper_EvaluateStrengthU24BurstManaged_mC2660554696E318D3C458E591DA2B15E1E1B2905 (void);
extern void VibrationHelper_EvaluateStrengthU24BurstManaged_m6CB7181449A6AF2BBF24554953CD0621524CFA8D (void);
extern void VibrationHelper_EvaluateStrengthU24BurstManaged_m31ADF1B6C8639D9235BD21CF0FF3B9C4D3A5F488 (void);
extern void EvaluateStrength_0000013CU24PostfixBurstDelegate__ctor_mFC689B94DD838B5D0C926DA1ED6D779B148B02E7 (void);
extern void EvaluateStrength_0000013CU24PostfixBurstDelegate_Invoke_m61FAF065CA8EA778116C0FBB999F39A7FF53AD29 (void);
extern void EvaluateStrength_0000013CU24PostfixBurstDelegate_BeginInvoke_mD54B92DA9260ACC44ECD7412B3FA55CABC059D70 (void);
extern void EvaluateStrength_0000013CU24PostfixBurstDelegate_EndInvoke_m5A76D3052CD1F5E15EEB4A640BA08D73AF72ECDB (void);
extern void EvaluateStrength_0000013CU24BurstDirectCall_GetFunctionPointerDiscard_mC257A47E9EF0E58451A5B63255486A7843623E62 (void);
extern void EvaluateStrength_0000013CU24BurstDirectCall_GetFunctionPointer_m4342FEB3727AF5F09786E844E38479A795C91F73 (void);
extern void EvaluateStrength_0000013CU24BurstDirectCall_Constructor_m3DB9D988397658A201E8B3C5AA366325BAFB03C8 (void);
extern void EvaluateStrength_0000013CU24BurstDirectCall_Initialize_m20B96811D1602889A500937829D56485E9E6E724 (void);
extern void EvaluateStrength_0000013CU24BurstDirectCall__cctor_m9672A5A9F0424B145B37656955349649CF0866A6 (void);
extern void EvaluateStrength_0000013CU24BurstDirectCall_Invoke_mC1EF7CD01A1ED1AE41F14187A44B17F3FE4AAB01 (void);
extern void EvaluateStrength_0000013DU24PostfixBurstDelegate__ctor_mA6FB2A0DD25A9BF1227492A0042F872AF6F01EFC (void);
extern void EvaluateStrength_0000013DU24PostfixBurstDelegate_Invoke_m2565EF59316CC0B54FCC75797FB416963D534F3D (void);
extern void EvaluateStrength_0000013DU24PostfixBurstDelegate_BeginInvoke_m0CE994405A29AB6D609170928CF1BC952BC16679 (void);
extern void EvaluateStrength_0000013DU24PostfixBurstDelegate_EndInvoke_mAC675A5C376FA859C7F900CA454610CF9D574DF6 (void);
extern void EvaluateStrength_0000013DU24BurstDirectCall_GetFunctionPointerDiscard_m41815647534E7BA134366249AD59C73FB90D2FCF (void);
extern void EvaluateStrength_0000013DU24BurstDirectCall_GetFunctionPointer_m9DA9FB2566E0C9436313A3AC4CA89AD21ABB0310 (void);
extern void EvaluateStrength_0000013DU24BurstDirectCall_Constructor_m63F935CC23BA99A31B6F0D2D1431C66DDC9630C0 (void);
extern void EvaluateStrength_0000013DU24BurstDirectCall_Initialize_m42B5EF430CBAB349D430D9BB62F70E5D22D9BEB4 (void);
extern void EvaluateStrength_0000013DU24BurstDirectCall__cctor_m59F3BFBE6CF5A246B6BF14449CD43DF2C4CCA748 (void);
extern void EvaluateStrength_0000013DU24BurstDirectCall_Invoke_mF70ED19B9D1E67BF66C6D6046551292B6051AB43 (void);
extern void EvaluateStrength_0000013EU24PostfixBurstDelegate__ctor_m743D199AAEE73373A802197F9601047EB74588FC (void);
extern void EvaluateStrength_0000013EU24PostfixBurstDelegate_Invoke_mA6581987E30BAAEFB5A34BE3736111031FAA7AA1 (void);
extern void EvaluateStrength_0000013EU24PostfixBurstDelegate_BeginInvoke_mC6F2A3F0A03982CB6ADAFB43B7955B143EB2B868 (void);
extern void EvaluateStrength_0000013EU24PostfixBurstDelegate_EndInvoke_mA34BAA5307E73725C0A0C9A589AB670ADD068564 (void);
extern void EvaluateStrength_0000013EU24BurstDirectCall_GetFunctionPointerDiscard_m0A35D4BDF9941EF62259AA2174C274C248A402EC (void);
extern void EvaluateStrength_0000013EU24BurstDirectCall_GetFunctionPointer_m9EC99BB41A96C45883DA1FE0E70BB8543D402AC1 (void);
extern void EvaluateStrength_0000013EU24BurstDirectCall_Constructor_mB0330B1C41A2E300165C0F0311B5AFEFD501405A (void);
extern void EvaluateStrength_0000013EU24BurstDirectCall_Initialize_mF52F84C3B486548D79FD7108F9E585AEE9F1CF09 (void);
extern void EvaluateStrength_0000013EU24BurstDirectCall__cctor_m478DD233E429A10ECD9854BC19985F200ECC9732 (void);
extern void EvaluateStrength_0000013EU24BurstDirectCall_Invoke_m5A17142F554F7A4F327CA494968C10F65A57CB7C (void);
extern void LMotion_Create_m884E0C4AC225B788D89F2B25D83F7EB2E7FE1E98 (void);
extern void LMotion_Create_m517FBCC3BD5ECC979383FCC7943110D14E5EE36E (void);
extern void LMotion_Create_m180EB5F367C7EDEE8C452DCEAEB78431D030D1E1 (void);
extern void LMotion_Create_m849FDE0B3D5DEE6774FAF0025236E4DD45B88337 (void);
extern void LMotion_Create_m31516B0BF62B9BAD6A0978421594D0B4D8F18EF5 (void);
extern void LMotion_Create_m83753C8E7B5BF9ABF78650B5F836D07021CD5946 (void);
extern void LMotion_Create_mC5D9269621FB71AEB3137C1DCB92096711B2A23C (void);
extern void LMotion_Create_mBCA5F2907A857315D71EF7E092A750D8B9F6BE88 (void);
extern void LMotion_Create_m8065923572DCD9524BAE89BC98359A60B7F5CB35 (void);
extern void LMotion_Create_m1F281B66F4663FB3FCF2E4858EBA483A3F32C988 (void);
extern void LMotion_Create_m1230D7B6B33815B60855574A7C701E6417B70E21 (void);
extern void LMotion_Create_m4DF0C3700CF0BE5882812680494FA01DBA2A4C8F (void);
extern void LMotion_Create_m92953C81956017A2AB06007C7B0EA592823E6105 (void);
extern void LMotion_Create_mC414812AA38059BD27BC5ECE7EB4215EE03CA515 (void);
extern void LMotion_Create_m8790BE937C5ECB6FD36B940BBC9423C764518C0C (void);
extern void LMotion_Create_m22181B98BC002E019AF3390195EEEEEB19E8885A (void);
extern void LMotion_Create_m8C8259D6597DBA9870425866C398B238084E07D4 (void);
extern void LMotion_Create_m506B55888E00518E7CD61E94F83217966FDDD490 (void);
extern void LMotion_Create_m387B82FF21BD62CE51B27F45FBD4A595397EE738 (void);
extern void LMotion_Create_mE3F389D3E8DE131E279F1EF8FB640348EE8C07E7 (void);
extern void String_Create32Bytes_m349D3A5E8A7ED0F40311BC35968AA437AFA6D195 (void);
extern void String_Create64Bytes_m317E44A5DD660D18D7FD9790E5A26C329D3DFD07 (void);
extern void String_Create128Bytes_m9CEEB6499428C9AC05E3FE4CAA0D56D3C9D53569 (void);
extern void String_Create512Bytes_m2DE88330438B0F2AAF20404D180D01BF0A7442A5 (void);
extern void String_Create4096Bytes_m110098DF117C3B892712AF008D1013C41C982433 (void);
extern void Punch_Create_m1C0F2D96AF2DC5C9256CC9E73E1502DDCE7DC577 (void);
extern void Punch_Create_mA52D22B300A7CE1B5829ECA40FCE9018B1B43A8D (void);
extern void Punch_Create_m93A806B10294CBFA987FA6DE6864B6605E7087B8 (void);
extern void Shake_Create_m89CACFDC3328848EC44C772FFC660F52836D21CA (void);
extern void Shake_Create_m031D45A64CCA566D8B68579359CA77B939FE3FA4 (void);
extern void Shake_Create_m85CD99D2246C46A6BC47E12583BFAA47CA511E2E (void);
extern void LSequence_Create_m4AB8986238844E5CCD72599F17C69FC3D67E6B53 (void);
extern void ManualMotionDispatcherScheduler__ctor_m5A0F8586CE31B77612E7809E688F7384E2172A80 (void);
extern void ManualMotionDispatcher_get_Scheduler_mDA6267E1838BD6EAB158348BF5BF46B2EDB3E180 (void);
extern void ManualMotionDispatcher__ctor_mED177318B9F656B56D5180F20BFF0952FD7F3DF8 (void);
extern void ManualMotionDispatcher_get_Time_mC8705337A0D3B7C8FF5880645EFE958BF6308E5E (void);
extern void ManualMotionDispatcher_set_Time_mF1D618EF9AFFA3733FEF55B766279DD08DEF636C (void);
extern void ManualMotionDispatcher_Update_mBD27C444FDA89E556E99B11EABDD8A3E62D60635 (void);
extern void ManualMotionDispatcher_Reset_mEDA9419EC6809C8F16BF786533B201F7251D4524 (void);
extern void ManualMotionDispatcher__cctor_m53F10854712984DD054B08BF01A09AF0CC720F60 (void);
extern void MotionAwaiter_get_IsCompleted_m286644973703C945DFDEBF59ECBFEB9813E55A6C (void);
extern void MotionAwaiter__ctor_m39C511FEAC16D5589DB9A0DB04C1BAEF6509CE3B (void);
extern void MotionAwaiter_GetAwaiter_m5E1F76DB7C9DD5B3334F3E8C71DF46038D1BCCA1 (void);
extern void MotionAwaiter_GetResult_m582D3877D66D6496EA719A34312D4B263C4F2D2E (void);
extern void MotionAwaiter_OnCompleted_mE62AD33CA015865A8F3819534883404785ADA914 (void);
extern void MotionAwaiter_UnsafeOnCompleted_mF5C13B5A490B2A7E1C332891A7F694960288F74B (void);
extern void MotionDebugger_get_Items_m9F9B3F15E3B731FA7B81D7591B94D5DFAC8DF7E7 (void);
extern void MotionDebugger_AddTracking_m5DAB6174F7F49BCC3F5C64BC81A53CCF88C5B475 (void);
extern void MotionDebugger_Clear_m0996C960732DE00EEB696BDC477F74C226F1A46F (void);
extern void MotionDebugger__cctor_mC08FEDC0C2C9B9D0DA4C1DB742A107C6C40604BB (void);
extern void TrackingState__ctor_m4711431852DC019F73EA6CF301CD51BB8E060603 (void);
extern void TrackingState_Create_mFF465389166D3F31A9D864F930F24B4A05610772 (void);
extern void TrackingState_OnComplete_m495084703674BC85A99E3CF9D217FB2175E63DFA (void);
extern void TrackingState_OnCancel_m29CC264821EBF18044AB869EA101E2CC3E04B06E (void);
extern void TrackingState_Release_mD041EE277ADB7A1968C8CAAC763827895FF570B8 (void);
extern void TrackingState__cctor_mAD072A768969C5AFE7CCF7E46BE6DAAB65FB6A6A (void);
extern void MotionDispatcher_GetRunnerList_m40E9CED6758AF301598CD53D997D5E1894AAA14C (void);
extern void MotionDispatcher_Init_mA4EEF2C32CA4FE2D9FB604CA9CE1FA68D97D8E0E (void);
extern void MotionDispatcher_RegisterUnhandledExceptionHandler_m15D93B3F8B72A537BF94F01C316AD26B30B3C943 (void);
extern void MotionDispatcher_GetUnhandledExceptionHandler_m6040B471623FBA1B3355419491D59F1D35A78988 (void);
extern void MotionDispatcher_DefaultUnhandledExceptionHandler_m81C50F0472143FD82E68CC325B0F1D0FFCA6FCE5 (void);
extern void MotionDispatcher_Clear_mD5E17CBE98861101D5A82B700EE5C0FAEBD9F0C6 (void);
extern void MotionDispatcher_Update_mAFFFA9313E42DA91CBA5C1D0E38E29819BBE76EB (void);
extern void MotionDispatcher__cctor_mCB19B9D9CE57CCF91562DF5D6B6D67BA1A98D2F7 (void);
extern void MotionHandle_get_Time_mA0FDC83A5CA764224292035D1B92DD35A0737C69 (void);
extern void MotionHandle_set_Time_mD678AA94DB0E174FC2EB977EBE10986DC62DEA9B (void);
extern void MotionHandle_get_Delay_m79CC0D8F78C3B8E93C5DBBC5932A1C4FA22B453E (void);
extern void MotionHandle_get_Duration_mFEF79D284F2ABF0AE9170303CCE7F0FC61443E06 (void);
extern void MotionHandle_get_TotalDuration_m03E9F23F0E3E2FA0D53FA6F796579DB2833E15DF (void);
extern void MotionHandle_get_Loops_mD5ADE9962E0D6770C65D0B25BB8240340AC323FF (void);
extern void MotionHandle_get_CompletedLoops_m8CF0BFB384A65D617D69D195DE7637FC03673E40 (void);
extern void MotionHandle_get_PlaybackSpeed_m810CA94487003021B21169CE4C00AB0D2B57B8C0 (void);
extern void MotionHandle_set_PlaybackSpeed_m2CE0693B92A83C716EA4BF498A5E9C57AC71833E (void);
extern void MotionHandle_ToString_mE2F8F39BF927477327C69AFEC8BB1A6D9FA0625E (void);
extern void MotionHandle_Equals_m717AA4FEC4A00726F8C0DBB28ED51B2FF53A539F (void);
extern void MotionHandle_Equals_mA77A264C0AE2A05C893C0BBF7D2C1CA0315AFE2B (void);
extern void MotionHandle_GetHashCode_m201415482C5E4AE8A0E14879A905ACD71E0615B7 (void);
extern void MotionHandle_op_Equality_m6DB3B9B5E94F7A6B823D32C1915C23D56BD4596F (void);
extern void MotionHandle_op_Inequality_mA10995A1D792EBC24B65ACF1E04DB5FE692CD796 (void);
extern void MotionHandle__cctor_m6EC4D9946D3E3FB66D8BBC54CF0754F8AFB840A3 (void);
extern void MotionHandleExtensions_IsActive_m33A1DAE84888047637F589CFF54E458F2CC0EED2 (void);
extern void MotionHandleExtensions_IsPlaying_m6CB941850BF2A95AB88CA4B5BD4FD3FB78F43DCF (void);
extern void MotionHandleExtensions_GetDebugName_m1774A9241B214D957DC092C0EE872E2C68B7521E (void);
extern void MotionHandleExtensions_Preserve_m6B6F0C6EB75391E011DF1CBA1BA86DE2EC6AFA0D (void);
extern void MotionHandleExtensions_Complete_mF5D7A9986BE92CE0BD7D9D75B2B44F1A03EF3EE1 (void);
extern void MotionHandleExtensions_TryComplete_m6E5C4F018592F928CBA2FD1626DBE6D25A2472E1 (void);
extern void MotionHandleExtensions_Cancel_m5579E51C6A834B5ABB60E24ABA3335FB5B20FB1A (void);
extern void MotionHandleExtensions_TryCancel_m5407A0C8EEB83CEEA742485A104889A7E14CD090 (void);
extern void MotionHandleExtensions_AddTo_m4DB59A863D05996F25A16D2B7606B1D321445AAA (void);
extern void MotionHandleExtensions_AddTo_m7EACE88B8C59AED0CACF8ABCA5399A18DBD0C467 (void);
extern void MotionHandleExtensions_AddTo_m199E55A3AA807B114B2D483D30BF5DE103F63E25 (void);
extern void MotionHandleExtensions_AddTo_m157E2523FE1C79CB51B4B4508494B3C86F20D8CF (void);
extern void MotionHandleExtensions_AddTo_m283DDF5E9C70CE6F23E421669E4E23A42D4B210F (void);
extern void MotionHandleExtensions_AddTo_m9E34DE43A46F37B34421CA362ABD52266C0FC802 (void);
extern void MotionHandleExtensions_ToDisposable_mC877F7C7380A66F9D1D7919DFF3F2C225715252E (void);
extern void MotionHandleExtensions_ToYieldInstruction_m684470126F34C5FD8CAB5F129E039E7D7320C5C8 (void);
extern void MotionHandleExtensions_GetAwaiter_mBFC32CC47DB96BA1F95E9C1DD2E334762ABE5BC0 (void);
extern void MotionHandleExtensions_ToValueTask_m718D96C505389D216CE5D037E8F4F9EB4ABC7A00 (void);
extern void MotionHandleExtensions_ToValueTask_m8186DA2E302CD1B90B63CD345D2760E362D1643C (void);
extern void MotionHandleExtensions_ToValueTask_m8BB0FFC8AB860477212B4C17962007AB4855E8E8 (void);
extern void U3CU3Ec__DisplayClass13_0__ctor_m113E1EAB9CE5E547BAFCFC0A3B7151977922D581 (void);
extern void U3CU3Ec__DisplayClass13_0_U3CAddToU3Eb__0_m5A3ADD556B45F3559D237B06F6387FAF3272A62C (void);
extern void U3CToYieldInstructionU3Ed__16__ctor_mC4BB59F45B71FD724B861CB31FFA4B95F52355E1 (void);
extern void U3CToYieldInstructionU3Ed__16_System_IDisposable_Dispose_m22ED70E79C1C7F65AB7FD83C9DC623038A45EC66 (void);
extern void U3CToYieldInstructionU3Ed__16_MoveNext_m1172E8BA5D948873E9FADC7874A9A6DA213C12DF (void);
extern void U3CToYieldInstructionU3Ed__16_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m9B802B483017A470D0F42BDDB4AB42942985C94C (void);
extern void U3CToYieldInstructionU3Ed__16_System_Collections_IEnumerator_Reset_m0EF976428B8F6C12E6091A8AE601F8B3C0695441 (void);
extern void U3CToYieldInstructionU3Ed__16_System_Collections_IEnumerator_get_Current_m1FE9BC6F118592849BD83DF81158FDE83C3FFA65 (void);
extern void MotionHandleDisposable__ctor_mF6F3BBA22676AA3A3B1080BD6367CE793CF03510 (void);
extern void MotionHandleDisposable_Dispose_mED3A3802E6070C9E18AD2B6247B83EE2A50427EF (void);
extern void MotionScheduler__cctor_m182608815E4A08BF5065FA06FB11A3DBD18BBD8E (void);
extern void MotionScheduler_get_DefaultScheduler_m1815603ECA3208DFD19FE6160C8FF824784E9FAB (void);
extern void MotionScheduler_set_DefaultScheduler_mD9E66D27D9EE86E6B057015FF2379F5F09EB2CDC (void);
extern void MotionScheduler_get_Manual_mC3FBB830D70F29E21449C438E28356C6BE0767E0 (void);
extern void MotionSequenceBuilderSource_Rent_mFD9F0CD221C1F19E1B5AA78E71B96A0F998998B1 (void);
extern void MotionSequenceBuilderSource_Return_mB7C4A940320B11A206DD16CE5A8BFE57EF029477 (void);
extern void MotionSequenceBuilderSource_get_NextNode_m5307BB8F5153C038D14BECF363E30C6C304FED88 (void);
extern void MotionSequenceBuilderSource_get_Version_m30FFE7A03660C1A54B825928AB06334AAA776F09 (void);
extern void MotionSequenceBuilderSource_Append_m26ED24A0AE99FDC09E3EAB380B000BC22067DD14 (void);
extern void MotionSequenceBuilderSource_AppendInterval_mCEEA7E58ACCD60864A3C443FA5DC3D4945177984 (void);
extern void MotionSequenceBuilderSource_Insert_m4CA78FFDBD5371F9AEC866B65E26D8F8EFC34B0D (void);
extern void MotionSequenceBuilderSource_Join_m6936393518F9AFCBCDC9F49F5BA293A92E16AA2C (void);
extern void MotionSequenceBuilderSource_Schedule_mA0B2FCFA3B935D201CE4255FA2EECAB77FC52F5A (void);
extern void MotionSequenceBuilderSource_AddItem_m5A687E01F68FDE7D69F5AA20E8E0A7E10763254C (void);
extern void MotionSequenceBuilderSource__ctor_m51440B0EBD5B944F41E11E45F33CBF3DAD1F01C3 (void);
extern void U3CU3Ec__cctor_mFB5341778716A16E6B509BBF24B177D1F66059A6 (void);
extern void U3CU3Ec__ctor_mE5DF0B4E09139D2C68EA184881D670AADC4431CF (void);
extern void U3CU3Ec_U3CScheduleU3Eb__18_0_mCD4B60654C842A3C70E6AEB6A99E9A60683D7EDF (void);
extern void MotionSequenceBuilder__ctor_m3B3813AFEE55253DDC6A81DFAF99DDFA94F5B0C6 (void);
extern void MotionSequenceBuilder_Append_m1B1458BF99FA0A758F5202BCAFCF1FDB6397407F (void);
extern void MotionSequenceBuilder_AppendInterval_m31D45D0D37C90D214776825F47C2B0C7F209757C (void);
extern void MotionSequenceBuilder_Insert_m81CF3B3C1F3DA5B975028BDCA01547DE35A12604 (void);
extern void MotionSequenceBuilder_Join_m342181FC806C2381E1A27CFAB1F1903115D5A1C5 (void);
extern void MotionSequenceBuilder_Run_m8470B91A3EE5610052CCAA58145AFFD2015833B7 (void);
extern void MotionSequenceBuilder_Run_m72BD27AF38A04BA10134D3D64B1A4942F8235A3E (void);
extern void MotionSequenceBuilder_Dispose_m3AC2152DD6D49C7AF09FF581B7AEBF47D5D901BE (void);
extern void MotionSequenceBuilder_CheckIsDisposed_mA0B1F56B0D2534199606ED88E68FC884BD67A4BF (void);
extern void MotionSequenceItem__ctor_mB684C8F3E987F664437269051E91FA23626C5A14 (void);
extern void MotionSequenceItem_CompareTo_m65A6CD68890C8BC64BE0149E42F11941291DE7DA (void);
extern void MotionSequenceSource_Rent_m553276BE459CFECC2A7C198229933276603A1A61 (void);
extern void MotionSequenceSource_Return_m9829EA197BC76C394F84E5EAE56E761E1A8D8D7E (void);
extern void MotionSequenceSource_Initialize_mB357DE96A390F6B804EC10990231A5E5ED152A09 (void);
extern void MotionSequenceSource__ctor_m1F22BE9600D570C30CE48A3414A793A6511CE639 (void);
extern void MotionSequenceSource_get_NextNode_mAA88FA5B53FB345E22A516E714ED2678CC4B1EFF (void);
extern void MotionSequenceSource_get_OnCompleteDelegate_mFF954EEC5BCF6E694C9804559123E98CB7CAE73F (void);
extern void MotionSequenceSource_get_OnCancelDelegate_m26A69BE7F229D7040148F00756045A9E2A76136B (void);
extern void MotionSequenceSource_get_Items_m4C36054CF49A92D16FE583F603372FB3E11C502F (void);
extern void MotionSequenceSource_get_Time_m423B68AE3BD7DF25D8D392ECA45F436EA0045C53 (void);
extern void MotionSequenceSource_set_Time_m1EA183FF9B1A75D22F440E13FD291A4ADC5B8D95 (void);
extern void MotionSequenceSource_get_Duration_m69B061B0C7A061103304BDD8799B6F2FA300225E (void);
extern void MotionSequenceSource_OnComplete_mF3FAF075694366743FF8B1BCA78659443383D452 (void);
extern void MotionSequenceSource_OnCancel_m99D5C894389B3BFE0F407EC0FAD4CB92D24400E2 (void);
extern void IntegerOptions_Equals_mB8DE550CAC68A2F0029A1B9874E791296CDFA19A (void);
extern void IntegerOptions_Equals_mE2E60F034BA74E42A4A3B9E083E07689FE57B409 (void);
extern void IntegerOptions_GetHashCode_mBB87BD24FB83C50E33BB9E977E81114CD966F98A (void);
extern void NoOptions_Equals_m385B96E4BB896392A8219EC298ABC8845ED1C92F (void);
extern void NoOptions_Equals_m8A4E39BEFD271327062C569AF079453F9F3CF612 (void);
extern void NoOptions_GetHashCode_m28C2C72A5509EAB1ACAA3B12A2F7DF26903E33F3 (void);
extern void PunchOptions_get_Default_mFE71526DEAE4CADD8B402E23BAFE9B15446BF644 (void);
extern void PunchOptions_Equals_m11D7621E6C9FE1E0D50B120F403B484164004A8B (void);
extern void PunchOptions_Equals_mD36EFF3523E00BF3996DCD90E529037F29ED0043 (void);
extern void PunchOptions_GetHashCode_m060EE8928378E41058941BFC6D58324B45E615C7 (void);
extern void ShakeOptions_get_Default_m46F7A2C9095360F0811CF0CF284DE7EB435002E1 (void);
extern void ShakeOptions_Equals_mD48E7AC97860E50E55C02DF0BD5952C9E4736C99 (void);
extern void ShakeOptions_Equals_m18AA941F8A34D3CBB611936E96C80BD1B03BD8B1 (void);
extern void ShakeOptions_GetHashCode_m836C3BA106E9A4C82EC6663C135D7F5679352161 (void);
extern void StringOptions_Equals_m94F9D676B065FF992DF8B874BB4DECC43D087223 (void);
extern void StringOptions_Equals_m0B6CB436B80F81E1F52B76B9A7E970BE1EFAE19C (void);
extern void StringOptions_GetHashCode_m2C4406DD93543B5109AFB9867E94B7098A335871 (void);
extern void NativeAnimationCurve__ctor_m3AD50473C7E68E08F43BDA4D59FF9DCA8EC4ADA4 (void);
extern void NativeAnimationCurve__ctor_m79972267B1924C44D34A2CAA6BCC66DA165E18C2 (void);
extern void NativeAnimationCurve_CopyFrom_m1AAC67EAF4342DB533B3AD7589CE1ED98222D903 (void);
extern void NativeAnimationCurve_CopyFrom_m7BA8522B4BF457E0D81780864E70DFD81004CE24 (void);
extern void NativeAnimationCurve_Dispose_m1C0E5159BB7F29FAD12B387C593A5C3A4E088E3C (void);
extern void NativeAnimationCurve_get_IsCreated_mA83C5407F24D940BE28F8628B6A4132577415688 (void);
extern void NativeAnimationCurve_Evaluate_m7ED7143984E71AF465407ABAA94BE754CBF8A8A4 (void);
extern void KeyframeComparer_Compare_m08A8F446BB3CEF5FBC791F447A11D880D5268AF9 (void);
extern void NativeAnimationCurveHelper_Evaluate_mDA20E15BF8B10E2B1E8022B3365FC496D30B0A03 (void);
extern void NativeAnimationCurveHelper_GetInsertionIndexForSortedArray_m35EA56701C3B8A8027A8F537D149CA7FED1F1BD9 (void);
extern void NativeAnimationCurveHelper_WrapTime_mB7735E2D4FD377EE079CC0C3BD22567D4F7ACEA5 (void);
extern void NativeAnimationCurveHelper_Evaluate_m2A41626246695AD866A3C02BE7AE9254528A5F4D (void);
extern void NativeAnimationCurveHelper_CubicBezier_m1BA414098EC0B22AAA4A2AD63087FEBF9F4B2C98 (void);
extern void NativeAnimationCurveHelper_CubicBezier_m509E0FA73ABAFBD67964CAA99A50B3DCF5077590 (void);
extern void NativeAnimationCurveHelper_DeCasteljauBezier_m57AD9BDE0A4CF3E2AC5DB2FE083A299B82362E11 (void);
extern void NativeAnimationCurveHelper_GetTWithBisectionMethod_m56CCFE0B0D3BE3688391771C237207FCB6A78EAB (void);
extern void NativeAnimationCurveHelper_GetTWithNewtonMethod_mC15A48755C1EA8E2293556D369786A2ECEB646C5 (void);
extern void NativeAnimationCurveHelper_UseNewtonMethod_m42A79906266AC12BE4D3B40367A68198771282B0 (void);
extern void NativeAnimationCurveHelper_UpdateTLimits_m56F166F058062B3011C386479C3811F9EE81D9B3 (void);
extern void NativeAnimationCurveHelper__cctor_m0C5868589D947F9A9A938529677AD35AA898680C (void);
extern void NativeAnimationCurveHelper_EvaluateU24BurstManaged_mD2F7A19582DB62B776C1F54EC2EC5BD37F28744A (void);
extern void Evaluate_00000270U24PostfixBurstDelegate__ctor_m3195CB6E88759859A843E532C3C2EC47BF7E1383 (void);
extern void Evaluate_00000270U24PostfixBurstDelegate_Invoke_mCA65ABDAADC587B1CFB539EE1DF35563275661F9 (void);
extern void Evaluate_00000270U24PostfixBurstDelegate_BeginInvoke_m0C2CAF12626C1963FE66D2F60726773D50FDC254 (void);
extern void Evaluate_00000270U24PostfixBurstDelegate_EndInvoke_mD3BBF78138A5B3740C3CA5BBF26678E11CDEBE50 (void);
extern void Evaluate_00000270U24BurstDirectCall_GetFunctionPointerDiscard_m331C80116DC537869642D4D0AA3745377EDD8EB4 (void);
extern void Evaluate_00000270U24BurstDirectCall_GetFunctionPointer_m9555A7F8B025BEFDFBD68A762BC4F8341E7F2F2A (void);
extern void Evaluate_00000270U24BurstDirectCall_Constructor_m864A87F46921571A08D6397825C6295A9202CD71 (void);
extern void Evaluate_00000270U24BurstDirectCall_Initialize_mB5E123DDD95A3482B3B7A6EF37A3D91DE48B3BE0 (void);
extern void Evaluate_00000270U24BurstDirectCall__cctor_m18AE0388A082839AB4392B241813BC5EA9E2E93A (void);
extern void Evaluate_00000270U24BurstDirectCall_Invoke_m564E1DDB2BA6412A80E9AD1D2A7922C4C9AC5164 (void);
extern void UnsafeAnimationCurve__ctor_mD81A51444ED8815EB361446BD52D8B7C3057FB29 (void);
extern void UnsafeAnimationCurve__ctor_m5761C59D0B100C1DA7DCA474DBA7A6DE4C99FE4B (void);
extern void UnsafeAnimationCurve_CopyFrom_m6554425C2394D531B1DA0EDD75C604295F9D32A2 (void);
extern void UnsafeAnimationCurve_CopyFrom_m25A0FC5288CB8D79418DC4A9D3D1C6851144437D (void);
extern void UnsafeAnimationCurve_Dispose_mE82134542407CBEB297210496545BD033B35FF8A (void);
extern void UnsafeAnimationCurve_get_IsCreated_m58C6CEF87EA2812D477E04F3BCBC0EA474CBB33E (void);
extern void UnsafeAnimationCurve_Evaluate_mBBFB7BCA744F345B181678223D5B3A7AF5FF1848 (void);
extern void FixedString32BytesMotionAdapter_Evaluate_m0C65C871422A2CDA8442392C70C50A27A3596C25 (void);
extern void FixedString32BytesMotionAdapter_LitMotion_IMotionAdapterU3CUnity_Collections_FixedString32BytesU2CLitMotion_StringOptionsU3E_Evaluate_m61FA07A7B8CF5A5BE9FCE9705FC3838B4292A5D1 (void);
extern void FixedString64BytesMotionAdapter_Evaluate_mE8E866842E525C6138219634E1E63721F5D8E1BF (void);
extern void FixedString64BytesMotionAdapter_LitMotion_IMotionAdapterU3CUnity_Collections_FixedString64BytesU2CLitMotion_StringOptionsU3E_Evaluate_mE85D4981C07EA5E5CDCBF97B40C61373A08DF5BB (void);
extern void FixedString128BytesMotionAdapter_Evaluate_m68E4B7360276AEA24A98254DC080748626C8449F (void);
extern void FixedString128BytesMotionAdapter_LitMotion_IMotionAdapterU3CUnity_Collections_FixedString128BytesU2CLitMotion_StringOptionsU3E_Evaluate_mD07ECCA1245B5B63DD5721B04374A9200F64F2EE (void);
extern void FixedString512BytesMotionAdapter_Evaluate_m85725AEDFBE845AB737702D09A8A8FA22982C01A (void);
extern void FixedString512BytesMotionAdapter_LitMotion_IMotionAdapterU3CUnity_Collections_FixedString512BytesU2CLitMotion_StringOptionsU3E_Evaluate_mE3ED5A53934BC012C266B801697A7FAEA505124E (void);
extern void FixedString4096BytesMotionAdapter_Evaluate_mC0B0006D3CCF6558DCA63E2B18397F5A7300D6FA (void);
extern void FixedString4096BytesMotionAdapter_LitMotion_IMotionAdapterU3CUnity_Collections_FixedString4096BytesU2CLitMotion_StringOptionsU3E_Evaluate_mA1AF5EF5902D3179F87770FA99CECAEA5BD3E649 (void);
extern void FloatMotionAdapter_Evaluate_m0EB9B9667D4CA9372C0A655011E801B42AD89B97 (void);
extern void FloatMotionAdapter_LitMotion_IMotionAdapterU3CSystem_SingleU2CLitMotion_NoOptionsU3E_Evaluate_m01273A2B00AFF360ADEEFFF3CE4E735C6AC16A8F (void);
extern void DoubleMotionAdapter_Evaluate_m9B514CA6D899B8257367F83BA263AD718C0E1FB3 (void);
extern void DoubleMotionAdapter_LitMotion_IMotionAdapterU3CSystem_DoubleU2CLitMotion_NoOptionsU3E_Evaluate_m7D22941DAF061BC51970DB13B73736D1204F29F9 (void);
extern void IntMotionAdapter_Evaluate_mFCE0298265BE98980C3CD7BF2628C2324FC650EF (void);
extern void IntMotionAdapter_LitMotion_IMotionAdapterU3CSystem_Int32U2CLitMotion_IntegerOptionsU3E_Evaluate_m26B7335E3AE26159BC42584B2B7DB524FA731640 (void);
extern void LongMotionAdapter_Evaluate_m85E43341837F672CE4B30AAD4C93AF6A40210672 (void);
extern void LongMotionAdapter_LitMotion_IMotionAdapterU3CSystem_Int64U2CLitMotion_IntegerOptionsU3E_Evaluate_m16A1ED25B8CD0960EB5F3EDA04603F6B09F593FF (void);
extern void FloatPunchMotionAdapter_Evaluate_m9759F0984D1C3796E16898DBC3E261935085AEA8 (void);
extern void FloatPunchMotionAdapter_LitMotion_IMotionAdapterU3CSystem_SingleU2CLitMotion_PunchOptionsU3E_Evaluate_m895D07F474710AC4079D4C9198E791FA1DF606AA (void);
extern void Vector2PunchMotionAdapter_Evaluate_m5F60BB55D5AF8B5B730625D640F04590DB6AAF86 (void);
extern void Vector2PunchMotionAdapter_LitMotion_IMotionAdapterU3CUnityEngine_Vector2U2CLitMotion_PunchOptionsU3E_Evaluate_m6A9A74F78DC48EB4D8743C1C48FCF9A0A74CACB7 (void);
extern void Vector3PunchMotionAdapter_Evaluate_mB42E328BF54522FDF1B02E28C6327BDBE9C2689A (void);
extern void Vector3PunchMotionAdapter_LitMotion_IMotionAdapterU3CUnityEngine_Vector3U2CLitMotion_PunchOptionsU3E_Evaluate_m508441802C077334FD42461FCDBB2FB9BFE67C8F (void);
extern void FloatShakeMotionAdapter_Evaluate_mC1BC8EC25DC1320FFBECA93770526D82C880AD55 (void);
extern void FloatShakeMotionAdapter_LitMotion_IMotionAdapterU3CSystem_SingleU2CLitMotion_ShakeOptionsU3E_Evaluate_m2879D969F6B0EA80B72DA8E80A5114A518C4B744 (void);
extern void Vector2ShakeMotionAdapter_Evaluate_m50827B1AA3F46AEA42C9B6D4093F77553A0AF05F (void);
extern void Vector2ShakeMotionAdapter_LitMotion_IMotionAdapterU3CUnityEngine_Vector2U2CLitMotion_ShakeOptionsU3E_Evaluate_m3010C121CA81FF149803E1630A3DEEF37C2039D1 (void);
extern void Vector3ShakeMotionAdapter_Evaluate_m67B9299405876AB5E5AA404EDF4C8D3CFF9F986C (void);
extern void Vector3ShakeMotionAdapter_LitMotion_IMotionAdapterU3CUnityEngine_Vector3U2CLitMotion_ShakeOptionsU3E_Evaluate_m36C70009C85C54A7C481A22E67C5798FCA704DD1 (void);
extern void Vector2MotionAdapter_Evaluate_mA06C68E46BA054C94F5F7C5878412C5B444296CD (void);
extern void Vector2MotionAdapter_LitMotion_IMotionAdapterU3CUnityEngine_Vector2U2CLitMotion_NoOptionsU3E_Evaluate_m8327E209843A8543301B74545AB3CABCE9606A41 (void);
extern void Vector3MotionAdapter_Evaluate_m909830664A98630176F9D226B3274D5EE14312EB (void);
extern void Vector3MotionAdapter_LitMotion_IMotionAdapterU3CUnityEngine_Vector3U2CLitMotion_NoOptionsU3E_Evaluate_m546FEB420E0FEF59364CB2E8C29C89997787B048 (void);
extern void Vector4MotionAdapter_Evaluate_m10FF8F5E90EC8DBD0AE8A53583C199D871682C82 (void);
extern void Vector4MotionAdapter_LitMotion_IMotionAdapterU3CUnityEngine_Vector4U2CLitMotion_NoOptionsU3E_Evaluate_m5FC2465D84F86A4883BC16259504B0D3FDFED933 (void);
extern void QuaternionMotionAdapter_Evaluate_mE5FE0E632D5815981970F997C2C4DF4CDDA337A5 (void);
extern void QuaternionMotionAdapter_LitMotion_IMotionAdapterU3CUnityEngine_QuaternionU2CLitMotion_NoOptionsU3E_Evaluate_m247E676A6BD2BC48DC6F18BC6216FE636DE38058 (void);
extern void ColorMotionAdapter_Evaluate_mAF212E5E5ED689641D29C3BD79DDAE16C2CE4F04 (void);
extern void ColorMotionAdapter_LitMotion_IMotionAdapterU3CUnityEngine_ColorU2CLitMotion_NoOptionsU3E_Evaluate_m47C20EA6F76C9CAB31F112E1E397F76F6BFD78F0 (void);
extern void RectMotionAdapter_Evaluate_m41DF28786C24A9F27A88C18C763C3E2D4F9F6843 (void);
extern void RectMotionAdapter_LitMotion_IMotionAdapterU3CUnityEngine_RectU2CLitMotion_NoOptionsU3E_Evaluate_mFFB30B245698D7885FEAB8A144F9A32EC3504B20 (void);
extern void __JobReflectionRegistrationOutput__1815361719_CreateJobReflectionData_mB027C9E04EF8BD510D6E2D649862D0D3796328E3 (void);
extern void __JobReflectionRegistrationOutput__1815361719_EarlyInit_m2D390F4E4794047E7FF12FE9F1BD077C788BA9D6 (void);
extern void U24BurstDirectCallInitializer_Initialize_m42FD7453A84042DA9C3F80DE55FDC866865E93EE (void);
static Il2CppMethodPointer s_methodPointers[1248] = 
{
	EmbeddedAttribute__ctor_m39F8955D3CAA6D35B42041FED974306634793777,
	IsUnmanagedAttribute__ctor_mCD8BDE5DA7D8EDF3274B3BCE585D4A51EA3485E0,
	NullableAttribute__ctor_m06B77230B4325A6A416336AA8363BB57921490D7,
	NullableAttribute__ctor_mFD2321DE13A7804C6B01E7098336588965E0EFB0,
	NullableContextAttribute__ctor_mE9E14DB6C4C74F266FF435FDDB1B3B42E5680A06,
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_m624B00A92F855C9F3A0D8856E91DE539B9252246,
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_mE347810C69832D5D5E588963A68B82BD26BB5254,
	IsExternalInit__ctor_m6B50CAF49263FE40FC436D9531290B0B924572E6,
	CompositeMotionHandle__ctor_m2FD83EF385A003E8A7C2E990E57BEF7E94A93395,
	CompositeMotionHandle__ctor_m419923AFEC9A2703AB08B6AB55324EC98BC62204,
	CompositeMotionHandle_Cancel_m4CE5A6E2F9A8F0EB1AF62EF943E1E2A64FA4F038,
	CompositeMotionHandle_Complete_m607679A03DFE58C08E744E22CCC2E370619BF07C,
	CompositeMotionHandle_Add_m4AA1F3801646AADB12067164270FC54EB77B9C52,
	CompositeMotionHandle_GetEnumerator_mFA1476472CD17CF6D3B4F4A5C224525E04E757A5,
	CompositeMotionHandle_System_Collections_Generic_IEnumerableU3CLitMotion_MotionHandleU3E_GetEnumerator_m04FB019154BE4BF75C0A90451534BF1DE4F5AEF6,
	CompositeMotionHandle_System_Collections_IEnumerable_GetEnumerator_mBDD56F2DC6628EE7257F7BAFE7B5C7264F29E4EE,
	CompositeMotionHandle_Clear_m1B73033633ADC986924809FA8146E299A1C4242E,
	CompositeMotionHandle_Contains_m5FEC4DDB63AB82BE41867E725DCE33A1DC9D53DF,
	CompositeMotionHandle_CopyTo_m6403FFA5387993AAC68815FAB2F348687D6D0E8C,
	CompositeMotionHandle_Remove_mD9C112A181B962D59485EAB214FE5F5C4B59B007,
	CompositeMotionHandle_get_Count_m8D8414DACC80117CCA6218ECE619DE80AB97F31B,
	CompositeMotionHandle_get_IsReadOnly_m496F4B118D462B8ED6D5967CA9D1BF446CFEADED,
	EaseUtility_Evaluate_m81A1498D9148087F83081BED41176712D7EC4E50,
	EaseUtility_Linear_m00A57B978A75E1E640560BAE98B9EA91A07C705A,
	EaseUtility_InSine_m7FEEFBB83B4BCED4835A1CF99842B20A30F28EB7,
	EaseUtility_OutSine_mE68C4CDD6E3D797C5B1964C7521710C999D565EA,
	EaseUtility_InOutSine_mCCA7CE4A26F911E03A1C1154C30A444C76AAEA2E,
	EaseUtility_InQuad_mFE6A7E74381ED3AD1B62ADCBEAD8F3BA9357F27B,
	EaseUtility_OutQuad_m8635DAD3E9BD74B36306F2C7729703C4E4CA8D99,
	EaseUtility_InOutQuad_m7053A93BD95B360C07DF92BC597887477115CB09,
	EaseUtility_InCubic_m928EA66C9143E1E137CB9819EEDB267EBC78C36F,
	EaseUtility_OutCubic_m5DA6B7198991B83FDFB9BA8F364AD33B9DF6E857,
	EaseUtility_InOutCubic_m268E1E91373C97CC6362BEE0FA49360E325859D8,
	EaseUtility_InQuart_m6995541D4934D84D95E346282FD538338041A07A,
	EaseUtility_OutQuart_mE5F66CF137A0FB6FA7F6E278A5FCECF1BB85D433,
	EaseUtility_InOutQuart_m258BB06B4C4A49B03C68044B1011D60FC4034BF2,
	EaseUtility_InQuint_m10B3508D95937CAC2A6500E05FBA0B4395EC846E,
	EaseUtility_OutQuint_m54E678520C35631272E10A4D1B0A8E8F1B8DE06E,
	EaseUtility_InOutQuint_mFCF8225B768BFE9230350EEEDDA8D4572653DE7B,
	EaseUtility_InExpo_mA18BC119B8C77F34595909562A2BF20A65EAAE4C,
	EaseUtility_OutExpo_mAC94B51A5DEBA90AC1577C7E8C4DFFAA41AD2066,
	EaseUtility_InOutExpo_m355AA513CEFD290593A5A5544890AC5EC4B80218,
	EaseUtility_InCirc_m45E62F2F14B8C29A1A940AECC6DEB1D390B32B47,
	EaseUtility_OutCirc_mF8088DB3630F22511FC3A6E9061B2A2B16FD48E9,
	EaseUtility_InOutCirc_m20443F829D616DC8E1C58A45F3557386CC0C8B24,
	EaseUtility_InBack_m6B27BF84B926D9DD4A2099811EA47B0E94DDA759,
	EaseUtility_OutBack_m3D0647DCA04638845A724F59ACC4477B9A6990D6,
	EaseUtility_InOutBack_mD9F0B9EEA96CB10596D564AE8933529C5BDFA7DE,
	EaseUtility_InElastic_mAE9C169A1DA1AD8040C665AB058A0727DB76681A,
	EaseUtility_OutElastic_m69F10A66DBCA0A9F654D204E8E427A12D1C0DD40,
	EaseUtility_InOutElastic_mBF22D575517637937A1FF39019281764DA225C6F,
	EaseUtility_InBounce_m1C65CA782377D33773D09ADA73A62828C2866D54,
	EaseUtility_OutBounce_m59C37B5D636D1D5CD1313B04FF128D5E81BC002C,
	EaseUtility_InOutBounce_m04F9CB3735BE5D54981A8AF7AB4D2CB7BAB97AF0,
	EaseUtility_EvaluateU24BurstManaged_m37B727D1797E8E01DC2993193BD49E7100BA0229,
	EaseUtility_LinearU24BurstManaged_mA53F1FD9802771E5DF225D3945028E362CE3DEFA,
	EaseUtility_InSineU24BurstManaged_m79794B7E30AED11DD4D25FCE32131A576D8733DF,
	EaseUtility_OutSineU24BurstManaged_m7D84E50A5E16E6EED45088057FC58B9C7FDF3EB6,
	EaseUtility_InOutSineU24BurstManaged_mD0E6A86F42B0C33304A3132FD7356A37CB1A1A00,
	EaseUtility_InQuadU24BurstManaged_mE2559E983436EF783D288D9735687AF0AD542C5E,
	EaseUtility_OutQuadU24BurstManaged_m88A3FE89EE38BE83D128A109AEAC8ADEA8D546E5,
	EaseUtility_InOutQuadU24BurstManaged_m95F5460C4B814B7D7A524AEBF048D64EDC74939D,
	EaseUtility_InCubicU24BurstManaged_mB93D4DAC0E3F527A1518D74EBD91DBDF54B01AED,
	EaseUtility_OutCubicU24BurstManaged_mAC9D20D60845A5BC315ACFEE2E975C7931C642D8,
	EaseUtility_InOutCubicU24BurstManaged_mC7D857670884BA35596F318F237E749E23E521E1,
	EaseUtility_InQuartU24BurstManaged_mA04D2F8AFD0B4CFC1ECC0E464031ED68153ABCF8,
	EaseUtility_OutQuartU24BurstManaged_m8101D875A5612324C498F99EC0F8749DC5E4E7BE,
	EaseUtility_InOutQuartU24BurstManaged_mC8E88C51F3CF95C6912E9C46307F68681A42E200,
	EaseUtility_InQuintU24BurstManaged_m73C6CFC8AD1850FE1AC2D57D9FDE3EEF9E25BB8F,
	EaseUtility_OutQuintU24BurstManaged_m3598300EE3AA9DDD9B518C0153975EF54EC5F924,
	EaseUtility_InOutQuintU24BurstManaged_mD957BD5DD3B2116D6FB3ACD51BA92A7E120A8DFC,
	EaseUtility_InExpoU24BurstManaged_mFDDEF8B6B7B6E1FDFDD5F190A67FCE06D7D3522F,
	EaseUtility_OutExpoU24BurstManaged_m0B64E597E904EA1F1ADBD292D38DC05F347F2C36,
	EaseUtility_InOutExpoU24BurstManaged_m37C13C1C5B5877D87BA2D638C615874DD6A32461,
	EaseUtility_InCircU24BurstManaged_m5DEEF8C773679EBCFAA1355CB0D82BE9F02CE5D9,
	EaseUtility_OutCircU24BurstManaged_mFE1F05EDF2B476EF2F951D982A306510AA5C862A,
	EaseUtility_InOutCircU24BurstManaged_mFA01A23B2C1C2842DAFB68420378FF2E9A7FFED4,
	EaseUtility_InBackU24BurstManaged_mE2B941EA16A22D124B00A05DE81EA66A50A5CB3E,
	EaseUtility_OutBackU24BurstManaged_m3DE81FBEE889E4E8ACF21523C24B5B468F9296EF,
	EaseUtility_InOutBackU24BurstManaged_m1D2FDC9782453FC0B9BF5C7A070DD73CC7031804,
	EaseUtility_InElasticU24BurstManaged_m41E5DFC00E0D8B6E1AD11157BC083D308EF8971B,
	EaseUtility_OutElasticU24BurstManaged_m67EC5EB5312C08AB72C0FCC735D697DD1BC2E737,
	EaseUtility_InOutElasticU24BurstManaged_mE3D883D9792030893CF68E02D09A40EA050A0D59,
	EaseUtility_InBounceU24BurstManaged_m29EC768A139F54748A58AF51A293DDD337D8954B,
	EaseUtility_OutBounceU24BurstManaged_m4A940AC936E10F31C616DC812C32F320B1F39DA8,
	EaseUtility_InOutBounceU24BurstManaged_m5AEE3957059401D38E48397D44D5E46A954B17A0,
	Evaluate_00000017U24PostfixBurstDelegate__ctor_mA8B6F3C581D4C1BBC1EB7EAA8C8A3556BA06FB7C,
	Evaluate_00000017U24PostfixBurstDelegate_Invoke_m5FD51B653A835EC879DED4185A3A554401034C10,
	Evaluate_00000017U24PostfixBurstDelegate_BeginInvoke_m0433F393B5DA46658602D327C43C2A1995AA7D18,
	Evaluate_00000017U24PostfixBurstDelegate_EndInvoke_m9FC3B9BE3A068782132C28A87C84BF3AE438FB75,
	Evaluate_00000017U24BurstDirectCall_GetFunctionPointerDiscard_m8F304EC90F99FE1256CAFAA637920283E5DF9DB1,
	Evaluate_00000017U24BurstDirectCall_GetFunctionPointer_m82E4DC31FD9387B986860BCCC66DA012573CA2BC,
	Evaluate_00000017U24BurstDirectCall_Constructor_m823C5FC211A8EBDA7CF844C55A18EA285CF5F494,
	Evaluate_00000017U24BurstDirectCall_Initialize_m73B29872A69F1A37A25F69BB3741B2BB5356F8D5,
	Evaluate_00000017U24BurstDirectCall__cctor_mEA49049BF96AF874C7D1BB615E63AFABBA493D8F,
	Evaluate_00000017U24BurstDirectCall_Invoke_mF2B1A76D272F3E2114E74F16D06A5E770F380858,
	Linear_00000018U24PostfixBurstDelegate__ctor_mFEB2830DCC8851BAAD8E89192065CDC51723FB24,
	Linear_00000018U24PostfixBurstDelegate_Invoke_m4FA7DA4FD0A4CD423F0083BA9D81C2C94620FF1F,
	Linear_00000018U24PostfixBurstDelegate_BeginInvoke_mE6A1655599C82C46AB90AD55613F6E6A3A478D9A,
	Linear_00000018U24PostfixBurstDelegate_EndInvoke_m2A9C919B5137B9182663A63E96B0BCE394A42D36,
	Linear_00000018U24BurstDirectCall_GetFunctionPointerDiscard_m15582DB91F989D9C63A0B5C99C8B081BA95133BF,
	Linear_00000018U24BurstDirectCall_GetFunctionPointer_m6B9EC0D19213C8ABD05D20C49495A118201550FE,
	Linear_00000018U24BurstDirectCall_Constructor_m6394D3776269312A251D843499BCD8DE3BD4D5CD,
	Linear_00000018U24BurstDirectCall_Initialize_m07D72DAB7B8C8A37351D49C6A328345B5C4349EC,
	Linear_00000018U24BurstDirectCall__cctor_mB0D91B70456F5717E1CDA25A471FF0478F029110,
	Linear_00000018U24BurstDirectCall_Invoke_m2305B8DC38FF3CFBFDDCC02D0012B4687E90A72F,
	InSine_00000019U24PostfixBurstDelegate__ctor_m0ACFA728FF2A15507E46C17C47ED2947FBC20B04,
	InSine_00000019U24PostfixBurstDelegate_Invoke_m61C8A6A292F9EEA56DD331BDA87FB9A6DE78D47F,
	InSine_00000019U24PostfixBurstDelegate_BeginInvoke_m54660470F1C59FA75F3743208192D845D882F000,
	InSine_00000019U24PostfixBurstDelegate_EndInvoke_mB700E473E0E640A0F215A32D1F3ECBAA091B7DC8,
	InSine_00000019U24BurstDirectCall_GetFunctionPointerDiscard_mBDAD7DDB2C7B2421E9C50833B1C78AE8BE835D30,
	InSine_00000019U24BurstDirectCall_GetFunctionPointer_m3E04CECB19C5CD82A38A4EA722E915312C471D0C,
	InSine_00000019U24BurstDirectCall_Constructor_m0480743FDD49929FA39EE2D2D6665409AA26CA00,
	InSine_00000019U24BurstDirectCall_Initialize_m9A79E3B4FB6972202AAF694DF098B2275FC60DD5,
	InSine_00000019U24BurstDirectCall__cctor_m5FC4CD9FA8B636D3A3FB1ADE127838993188ABBA,
	InSine_00000019U24BurstDirectCall_Invoke_m7487D8E7BBFF4F0D619AF9CAFA28BF31864EDF91,
	OutSine_0000001AU24PostfixBurstDelegate__ctor_m1E9BEBBDA1EE7111EE2E6C714DB97D5141685684,
	OutSine_0000001AU24PostfixBurstDelegate_Invoke_m828B4B73E5E4424CC67A812AB46776134F675570,
	OutSine_0000001AU24PostfixBurstDelegate_BeginInvoke_m0C52B3A28A72A588257123C79175597811CC5087,
	OutSine_0000001AU24PostfixBurstDelegate_EndInvoke_m5CEA1F1E84F2DE25B3D3776A90CD471574F3A056,
	OutSine_0000001AU24BurstDirectCall_GetFunctionPointerDiscard_mCE319D3991B24B1B284505F9F226CF63AC0BA307,
	OutSine_0000001AU24BurstDirectCall_GetFunctionPointer_m79A4F62E747D002F7708FCE36875903A71CC5721,
	OutSine_0000001AU24BurstDirectCall_Constructor_m35B531816C2CED2B4099CDE758ED6150EE91B621,
	OutSine_0000001AU24BurstDirectCall_Initialize_m341CE941054D4BCC04B867F2D1B2A64DF9078120,
	OutSine_0000001AU24BurstDirectCall__cctor_mB8AAFA612654C5B6152C8CB539141BB3238BDBA2,
	OutSine_0000001AU24BurstDirectCall_Invoke_m577BFCE2BB2640DA532DE34EE344629A91759365,
	InOutSine_0000001BU24PostfixBurstDelegate__ctor_mD041C3B88BC631A2B3F77A0B333E51283B7B5BF5,
	InOutSine_0000001BU24PostfixBurstDelegate_Invoke_mE7C7A91EBE784856EC763611D6B84FC8CDAC98EA,
	InOutSine_0000001BU24PostfixBurstDelegate_BeginInvoke_m273C343A615A77AF042E2924EFD236242EFDD1A5,
	InOutSine_0000001BU24PostfixBurstDelegate_EndInvoke_m89FA44281A3AAB7B8152443843E26F8C89927568,
	InOutSine_0000001BU24BurstDirectCall_GetFunctionPointerDiscard_mCE2DE6A6A3058F6FB7D101B4F33ACEBE6F7F5ADC,
	InOutSine_0000001BU24BurstDirectCall_GetFunctionPointer_m9DFBACE3F363841ED009CD3D5DCD8D3C05304FB4,
	InOutSine_0000001BU24BurstDirectCall_Constructor_m0A004EAFFB9EAD7670CFA2DEB8AF6F3B98F3A10A,
	InOutSine_0000001BU24BurstDirectCall_Initialize_m662A6CB6D62FF9616B54662FCEBD5DB3199B490E,
	InOutSine_0000001BU24BurstDirectCall__cctor_m0E679F15E2742A117CE3BE0C975D6CC5DF7888AC,
	InOutSine_0000001BU24BurstDirectCall_Invoke_m998F29B4D52FD7247F1090F87CD2A4D7E03714C6,
	InQuad_0000001CU24PostfixBurstDelegate__ctor_m0DA21D23C71D85B3B8576DB1CD68412106253EA8,
	InQuad_0000001CU24PostfixBurstDelegate_Invoke_mB193C40C938D4240684933E1FFF2E9C9B0399674,
	InQuad_0000001CU24PostfixBurstDelegate_BeginInvoke_m554BBA360A5C6002AF77C4430A6B64C9FAC75926,
	InQuad_0000001CU24PostfixBurstDelegate_EndInvoke_m54EBBF0A8A43BC069DDA44325BCA7A759DEA5BD5,
	InQuad_0000001CU24BurstDirectCall_GetFunctionPointerDiscard_mC96BA92222425FE2E730D93E7A9113F67908C5ED,
	InQuad_0000001CU24BurstDirectCall_GetFunctionPointer_m9767A1616FDBFB005F6E0CC16AE38DCC55D43B8B,
	InQuad_0000001CU24BurstDirectCall_Constructor_m4EFEACE0F36AF58C976FE24203C74AD9DE0D17D5,
	InQuad_0000001CU24BurstDirectCall_Initialize_mFBB410B7A168EA3406C57D79A13EF126DE0B9C50,
	InQuad_0000001CU24BurstDirectCall__cctor_m19139CD780ECB8834A36D8E52E1AFC9770E7E8A1,
	InQuad_0000001CU24BurstDirectCall_Invoke_mF82213577E5DD90E6585835ADC1510E240BE3492,
	OutQuad_0000001DU24PostfixBurstDelegate__ctor_mE39E274DA4B4181E335845C99515C15E4FA16FFC,
	OutQuad_0000001DU24PostfixBurstDelegate_Invoke_m78FB5740F0433AA5BA22407AC24874D6FFD019B1,
	OutQuad_0000001DU24PostfixBurstDelegate_BeginInvoke_mEF648BEE4AEFDDACA85CD7F09FFB1B6846CE6350,
	OutQuad_0000001DU24PostfixBurstDelegate_EndInvoke_m07DE1CA14267B8EABE1EF59351D1C354C6902A67,
	OutQuad_0000001DU24BurstDirectCall_GetFunctionPointerDiscard_mD2D862E38773BAEF02548E13FC25FFF99FC933A8,
	OutQuad_0000001DU24BurstDirectCall_GetFunctionPointer_mA9F349FF6131D2158861C01FA77A176E339EB1D1,
	OutQuad_0000001DU24BurstDirectCall_Constructor_m99DC0FA879BFE5596CFBAF2D87B0B2C44762FF0F,
	OutQuad_0000001DU24BurstDirectCall_Initialize_m98764AA42F2D539F5F8868964C6EACD7B846CBB5,
	OutQuad_0000001DU24BurstDirectCall__cctor_mB27FE099CEC2B0C253C03763BCB4C078615FFC66,
	OutQuad_0000001DU24BurstDirectCall_Invoke_mB62D0B90B985D9056396D2E2D5AF5D3B96A7A1A2,
	InOutQuad_0000001EU24PostfixBurstDelegate__ctor_m61550E61C9DC9FB8AD19980F88C5F129E2644A1B,
	InOutQuad_0000001EU24PostfixBurstDelegate_Invoke_m6F7DB9FB48A126ED08C33A9B9DB629A6287D71B2,
	InOutQuad_0000001EU24PostfixBurstDelegate_BeginInvoke_m57D8CE764383C4DD8A2B03FCBD8CC508059B826A,
	InOutQuad_0000001EU24PostfixBurstDelegate_EndInvoke_m35EAC423A6AD2E79F6695E2A2EBED448F4B0C547,
	InOutQuad_0000001EU24BurstDirectCall_GetFunctionPointerDiscard_m1506F5A84504B973B09B3270319CF222D0862FCC,
	InOutQuad_0000001EU24BurstDirectCall_GetFunctionPointer_m9E740FCF1C9771DCB4715A6EB135EA4B1D1FE14A,
	InOutQuad_0000001EU24BurstDirectCall_Constructor_mC89DB31D8E042CC0796F00E9F04EF0800EC0AE55,
	InOutQuad_0000001EU24BurstDirectCall_Initialize_mDA0E753E71AD2E14289A7DEB41105ED2B0E91F05,
	InOutQuad_0000001EU24BurstDirectCall__cctor_m896D2541852A840388954577FEE43BFDFEE5F719,
	InOutQuad_0000001EU24BurstDirectCall_Invoke_m48194FDA00A44A5207135822F436929065079DC1,
	InCubic_0000001FU24PostfixBurstDelegate__ctor_m4BDD7767C61DBD13C6F9ED8E477F04F7A0642422,
	InCubic_0000001FU24PostfixBurstDelegate_Invoke_m3C63866647BAAD977206AB266F85C1C2620A9C1A,
	InCubic_0000001FU24PostfixBurstDelegate_BeginInvoke_mBFE1748F622C04DB1FAC935D5FC175F36EDBFA61,
	InCubic_0000001FU24PostfixBurstDelegate_EndInvoke_m23BED6EBBA5A6B02439D6C6150F20C557B0BA664,
	InCubic_0000001FU24BurstDirectCall_GetFunctionPointerDiscard_m4D31B1531F8506833E807686F987D0B6680163A1,
	InCubic_0000001FU24BurstDirectCall_GetFunctionPointer_m20181FE3DC7F20F3EDF048C5029994D11809453D,
	InCubic_0000001FU24BurstDirectCall_Constructor_m6A420AE7E18F2E592B2356B6C0030BEB001800AD,
	InCubic_0000001FU24BurstDirectCall_Initialize_m4BD8A803D6C90AF193B32FD3E6246AB59EB763C5,
	InCubic_0000001FU24BurstDirectCall__cctor_m14BD21730F966995BFF0BA23376F67B2C2302086,
	InCubic_0000001FU24BurstDirectCall_Invoke_mFFE55402E241BDBC65A992B86BC3A8293CC869AA,
	OutCubic_00000020U24PostfixBurstDelegate__ctor_mB2A20246233EEE7D6A51DF33C8C513BF27C88E57,
	OutCubic_00000020U24PostfixBurstDelegate_Invoke_m06DCB994A864BE732AC25A4A7481F90CFEE33FA7,
	OutCubic_00000020U24PostfixBurstDelegate_BeginInvoke_m4C782DBF7D64DE232F45481F98BFA811FF8E9ED9,
	OutCubic_00000020U24PostfixBurstDelegate_EndInvoke_m881B0ACE7BFE49DB2986B899252EC0DE0ACC36AB,
	OutCubic_00000020U24BurstDirectCall_GetFunctionPointerDiscard_m87F2B1E7C3457FEAFF93A8D8B53741FA0ED7EA26,
	OutCubic_00000020U24BurstDirectCall_GetFunctionPointer_mB2A96CE1B30B4286A450217D63493F7E014B3819,
	OutCubic_00000020U24BurstDirectCall_Constructor_m40F465A0F71468C8E36B139E2906DBF6072938E4,
	OutCubic_00000020U24BurstDirectCall_Initialize_m3A4BCB7A03846C5E080B51DEAE0EA9893766B288,
	OutCubic_00000020U24BurstDirectCall__cctor_mEFF1C9DA10D6E97ECCFD7593D99529D474A04813,
	OutCubic_00000020U24BurstDirectCall_Invoke_mD635835D832401F35FEA473C35EF716E0FFC2084,
	InOutCubic_00000021U24PostfixBurstDelegate__ctor_mFAD2CECA6C5C6271ECDB74257F9F53FB9EEC4363,
	InOutCubic_00000021U24PostfixBurstDelegate_Invoke_mD5941E47AE99BBC7247E78205F232A77EF6238EB,
	InOutCubic_00000021U24PostfixBurstDelegate_BeginInvoke_mA8D44ED54EF7F2C2046668F847B9AF6ED257B894,
	InOutCubic_00000021U24PostfixBurstDelegate_EndInvoke_mFAA7380F86F012B119548F80B0D8D7C9DF0F2B3E,
	InOutCubic_00000021U24BurstDirectCall_GetFunctionPointerDiscard_mC8D46EE39391BFBF9A9B091868AA2EC9240E8CDD,
	InOutCubic_00000021U24BurstDirectCall_GetFunctionPointer_m844884537D5698BDF5ACA73A6A84C268807B0EEC,
	InOutCubic_00000021U24BurstDirectCall_Constructor_mFC53C4C889596EC4C435E147B9A9C43BB654F434,
	InOutCubic_00000021U24BurstDirectCall_Initialize_mDD93943E92CDBBD10557126D91C0D38E0EA5F707,
	InOutCubic_00000021U24BurstDirectCall__cctor_mE92F5DA3ECA970911C74BDB16C00A92EAF55B5BA,
	InOutCubic_00000021U24BurstDirectCall_Invoke_mAC8D389FD345ABA480661F2226ACD19F8DD7CC28,
	InQuart_00000022U24PostfixBurstDelegate__ctor_m9EA8C1A00D1E812B6FCACE4AB77E16018673B18D,
	InQuart_00000022U24PostfixBurstDelegate_Invoke_m09227E477D28E52211CEDFB7AD6737CDFEE5090F,
	InQuart_00000022U24PostfixBurstDelegate_BeginInvoke_m49573A0EF5916519A82E70B57C3881CBDD134456,
	InQuart_00000022U24PostfixBurstDelegate_EndInvoke_m6EBA2EF36970F714203873E2044D5B89D04E5847,
	InQuart_00000022U24BurstDirectCall_GetFunctionPointerDiscard_m91C887233E19927748CD7DEC0B1C2B39B811D750,
	InQuart_00000022U24BurstDirectCall_GetFunctionPointer_m12E723F2A3E826814DD92B618A4402D3CDCC146F,
	InQuart_00000022U24BurstDirectCall_Constructor_mEF5C43305DB791FCEC0E4ED7A5175F47108BAEC0,
	InQuart_00000022U24BurstDirectCall_Initialize_m5B717C60C093105EF488FA117D0095AF5F457807,
	InQuart_00000022U24BurstDirectCall__cctor_m5E709D862C2DBFF0F36388A0E0A049B69A155F16,
	InQuart_00000022U24BurstDirectCall_Invoke_m36710A56C6B77A3C97EF9986500BE0ED689E8414,
	OutQuart_00000023U24PostfixBurstDelegate__ctor_m45491E2D1AB09CE1C4A626218BF27AE1FE19A036,
	OutQuart_00000023U24PostfixBurstDelegate_Invoke_m96D1E0C563C26E0922B1A9024368C6A19B506E83,
	OutQuart_00000023U24PostfixBurstDelegate_BeginInvoke_mB973F1BD1F8538AE535FD9ACDEA1DE2B7F8CAA77,
	OutQuart_00000023U24PostfixBurstDelegate_EndInvoke_mF59BC9E52F6E5AF707893B4DF9BC0D272FB436F3,
	OutQuart_00000023U24BurstDirectCall_GetFunctionPointerDiscard_m35303DED055FD197B3FF3D7A050586E15FB061B1,
	OutQuart_00000023U24BurstDirectCall_GetFunctionPointer_m2BC2AE52B48A3116B3466D596321F39FF68059CC,
	OutQuart_00000023U24BurstDirectCall_Constructor_m6D84325979CA7CBDD1A4CB178CB2240C479CDB17,
	OutQuart_00000023U24BurstDirectCall_Initialize_m5F31644011D73B37B0CC0950672928AF5A065ABA,
	OutQuart_00000023U24BurstDirectCall__cctor_m36B3B1F3D13C20B797429CE157149032A059DCF2,
	OutQuart_00000023U24BurstDirectCall_Invoke_m8DA570E4BE3B60BE68BF9996A650ED00B70CDE40,
	InOutQuart_00000024U24PostfixBurstDelegate__ctor_m700853B58354C18F90A0C2810E3D99E7C3A3C379,
	InOutQuart_00000024U24PostfixBurstDelegate_Invoke_mD305A394BAADA81E75DA9E87669985CAEDAA7E61,
	InOutQuart_00000024U24PostfixBurstDelegate_BeginInvoke_mCF022D461D07D5249930977CC27E23A1E48E3A09,
	InOutQuart_00000024U24PostfixBurstDelegate_EndInvoke_mD6C1316E9F7AEA476B1E954E8E286C7E04223664,
	InOutQuart_00000024U24BurstDirectCall_GetFunctionPointerDiscard_mC8F0CA17239FB94AC2889D2428F2091A2BA2AA23,
	InOutQuart_00000024U24BurstDirectCall_GetFunctionPointer_mA95276C6BD0065DB03A4C3904FAA95A4CCA7F739,
	InOutQuart_00000024U24BurstDirectCall_Constructor_m0DD6D07F2358EE907AA232AB03CD7057506456A7,
	InOutQuart_00000024U24BurstDirectCall_Initialize_m5ED48E032250606549AE657B3ED77A1EC05AED31,
	InOutQuart_00000024U24BurstDirectCall__cctor_m09D2424782F87D76480B5D379EEF3981914193F9,
	InOutQuart_00000024U24BurstDirectCall_Invoke_mEA3BDA921C6A495084F34DF472A95B270F44266D,
	InQuint_00000025U24PostfixBurstDelegate__ctor_m09E03F490FFAED047E003817204356FE0D03DD47,
	InQuint_00000025U24PostfixBurstDelegate_Invoke_m2526AC125F4E8D32C0EBEDDCD481D05B2993B99C,
	InQuint_00000025U24PostfixBurstDelegate_BeginInvoke_mEC5323A6EDB20136854F91CCB73E60A43EC7BF63,
	InQuint_00000025U24PostfixBurstDelegate_EndInvoke_m660CE3D35FA28F49CA450E4775B635692B9098F6,
	InQuint_00000025U24BurstDirectCall_GetFunctionPointerDiscard_m4ED02AC6FB29B91FDD8C570CEE16DEA8B1317BFD,
	InQuint_00000025U24BurstDirectCall_GetFunctionPointer_mB8A2318586087C091BA991C9E9D3F51DC5948505,
	InQuint_00000025U24BurstDirectCall_Constructor_m47B560B9ABE8BF20E75F54815826F508556174CD,
	InQuint_00000025U24BurstDirectCall_Initialize_mC05B2CD56E4BF295F87C7CC777551E6634A6E4A3,
	InQuint_00000025U24BurstDirectCall__cctor_mD053A75373E0435A1A3D551FA2F01E1489F6EEAB,
	InQuint_00000025U24BurstDirectCall_Invoke_m09E79D8D2CF112265B40A8FC83732E7007AEA7D6,
	OutQuint_00000026U24PostfixBurstDelegate__ctor_m41673773748DAEC533433F03D3CDB7DDF2799D22,
	OutQuint_00000026U24PostfixBurstDelegate_Invoke_mDB985E25D67C99FDDA98A2EE4883199F31B199D9,
	OutQuint_00000026U24PostfixBurstDelegate_BeginInvoke_mCE4751EFA293D8CF031056601238917925DB65A1,
	OutQuint_00000026U24PostfixBurstDelegate_EndInvoke_m4F9A5E4A7B86B686C46033A893EF8566608332E3,
	OutQuint_00000026U24BurstDirectCall_GetFunctionPointerDiscard_m14FA5963A083CBD953C5E85560022AEF9802414D,
	OutQuint_00000026U24BurstDirectCall_GetFunctionPointer_m90F8E1820D16493AFA2E38A7797BA80386BEF602,
	OutQuint_00000026U24BurstDirectCall_Constructor_m657B0FDA7A705A7DFE6C1D7410E1BFFE4BBB4D7F,
	OutQuint_00000026U24BurstDirectCall_Initialize_m5D8AE96C8DE38C2B6EBDE4F134DC8717B13BFF99,
	OutQuint_00000026U24BurstDirectCall__cctor_m1A4C7A2E88C7F9F949E7AE7FBC04A14AC5BA3E93,
	OutQuint_00000026U24BurstDirectCall_Invoke_m7B594A388DF7AEEA181F5B046AEE939F5F734A05,
	InOutQuint_00000027U24PostfixBurstDelegate__ctor_m8249FA5D5B9934C8B610E677BB5E0B0B41C5A411,
	InOutQuint_00000027U24PostfixBurstDelegate_Invoke_mA999C0197E7DF56C1A480B757E34FA4C491A85D3,
	InOutQuint_00000027U24PostfixBurstDelegate_BeginInvoke_mA6AD69E1975588BCA15B5D0749BAF02894BD55B7,
	InOutQuint_00000027U24PostfixBurstDelegate_EndInvoke_m11FA324BA9C755DCC258F71689A85D949C28EBBE,
	InOutQuint_00000027U24BurstDirectCall_GetFunctionPointerDiscard_m3E546001713D68B546228C786FB1557E94BE07F0,
	InOutQuint_00000027U24BurstDirectCall_GetFunctionPointer_mBFC9FEE4D02C81FCCCFE6C1BA8ABE2A38BA3953C,
	InOutQuint_00000027U24BurstDirectCall_Constructor_m9E31905F2E5E17E856307B3CA06CB2E525411780,
	InOutQuint_00000027U24BurstDirectCall_Initialize_m0F519DE5C5B69CDE0661AC7A3AD95B1626D379E6,
	InOutQuint_00000027U24BurstDirectCall__cctor_m4FFBCC4B4190649F1C6639082165E64C44410EA5,
	InOutQuint_00000027U24BurstDirectCall_Invoke_m576757B01DA2949D479369DEB81704F264350C87,
	InExpo_00000028U24PostfixBurstDelegate__ctor_m7C63E1AA9C0492BB8EC8DCCE1E70B2B4216030C6,
	InExpo_00000028U24PostfixBurstDelegate_Invoke_m9A8F9C65413499827E3A18888136D0596F2AFFED,
	InExpo_00000028U24PostfixBurstDelegate_BeginInvoke_m21ED0FF2AC737E124BE5EDA328E57254A91D16CB,
	InExpo_00000028U24PostfixBurstDelegate_EndInvoke_mF426B9F1161A64E52D8EBB9372F58FD732F8BE48,
	InExpo_00000028U24BurstDirectCall_GetFunctionPointerDiscard_mE525B83AA6D223B180C55821A03589DE7774662A,
	InExpo_00000028U24BurstDirectCall_GetFunctionPointer_m8566D6B2812C1029674C7000CA7E34D9618A99D3,
	InExpo_00000028U24BurstDirectCall_Constructor_m11B7836C0F1330863EA5C7E204507679E4A01A16,
	InExpo_00000028U24BurstDirectCall_Initialize_m06512EF68DD7E1852E9A2F7845603E5616B869AB,
	InExpo_00000028U24BurstDirectCall__cctor_mB07F88EF10F46D940D83772292735F999C532F43,
	InExpo_00000028U24BurstDirectCall_Invoke_m5B526CB09F2C8880C3962D9D01EDB244285CCE26,
	OutExpo_00000029U24PostfixBurstDelegate__ctor_m05D33E20D81C48E59EE4A74B4A354B5F734B470A,
	OutExpo_00000029U24PostfixBurstDelegate_Invoke_m06A177564E864064F4374AFD2E9A44C013236F4A,
	OutExpo_00000029U24PostfixBurstDelegate_BeginInvoke_m7DF87884696AE72986AB9D9748851B81FF1F5E04,
	OutExpo_00000029U24PostfixBurstDelegate_EndInvoke_m8D74F5171FD710BC44E3094DDAA3BE7645F4169D,
	OutExpo_00000029U24BurstDirectCall_GetFunctionPointerDiscard_mD6B45297510E9A2CBAB5ED85F666A469FDEB2874,
	OutExpo_00000029U24BurstDirectCall_GetFunctionPointer_m81B3D27160DE394DEA9BFDA6840923D813E925FF,
	OutExpo_00000029U24BurstDirectCall_Constructor_mFDB3E1170CD02A54D8E8CF0635E0C2704EE15ECA,
	OutExpo_00000029U24BurstDirectCall_Initialize_mECE79C68410EA2B36F2578E94D5B6E74EF367908,
	OutExpo_00000029U24BurstDirectCall__cctor_m3BD5D71612A836EF092FABEDD9CED6EED0334369,
	OutExpo_00000029U24BurstDirectCall_Invoke_m8753A962FA74A918D9011825508848545DA19E28,
	InOutExpo_0000002AU24PostfixBurstDelegate__ctor_mA955D8F8954AB3E4F296DCB91CFBDEC3D22E9E24,
	InOutExpo_0000002AU24PostfixBurstDelegate_Invoke_m33E536B26D7E9A58D518397000EBC77DF16F605B,
	InOutExpo_0000002AU24PostfixBurstDelegate_BeginInvoke_m2C96FF2E82A12CA14FC4E1B981AE51218175729D,
	InOutExpo_0000002AU24PostfixBurstDelegate_EndInvoke_mC4E7A0D7C84DE7B923AFC8D06514E3427C2880A1,
	InOutExpo_0000002AU24BurstDirectCall_GetFunctionPointerDiscard_mDFB69570ABEB135D239A1A008FF97DB92E8FBE69,
	InOutExpo_0000002AU24BurstDirectCall_GetFunctionPointer_m99DD4FC2264C96798B7FE6CBD53734B6574B7EA4,
	InOutExpo_0000002AU24BurstDirectCall_Constructor_m8AA15D0A746246ED9B68E5FACAB6CC7D17100997,
	InOutExpo_0000002AU24BurstDirectCall_Initialize_m66FA612BA8000700C0BA41381673181A51BA52BB,
	InOutExpo_0000002AU24BurstDirectCall__cctor_m3F3C228A254339C26CA2F18E47EFD16844B71956,
	InOutExpo_0000002AU24BurstDirectCall_Invoke_m43BB8BBCE9077AF42E187E32FE311966DE4CCED6,
	InCirc_0000002BU24PostfixBurstDelegate__ctor_m7A9B562460439E217B55EBA143008D01B5F52567,
	InCirc_0000002BU24PostfixBurstDelegate_Invoke_m652D00C838590B59FB8672203FA6A9FA8EDDED92,
	InCirc_0000002BU24PostfixBurstDelegate_BeginInvoke_m3E4F4D1CB5B50CE7F7938DA87F55BBB54965B60D,
	InCirc_0000002BU24PostfixBurstDelegate_EndInvoke_m145783899C374D22AD57FF5B4BA78BD40231C4F2,
	InCirc_0000002BU24BurstDirectCall_GetFunctionPointerDiscard_m4307A55F67B6EEA10B415FA40147DBD79D2738B8,
	InCirc_0000002BU24BurstDirectCall_GetFunctionPointer_m55C913856E54D27E49318B2FA47A7C32FA8B3D48,
	InCirc_0000002BU24BurstDirectCall_Constructor_mBA0ACF0F07B048E460DBE9A10CECA7FC68860F52,
	InCirc_0000002BU24BurstDirectCall_Initialize_mEAF4847D40CA6637B9E6B4802708AF440ACE65BC,
	InCirc_0000002BU24BurstDirectCall__cctor_mEF980206273777EBC9C81D8DF42F46090AB0C76A,
	InCirc_0000002BU24BurstDirectCall_Invoke_mE4F25E3B259CB3BB508F3AC3188F907DA3828D67,
	OutCirc_0000002CU24PostfixBurstDelegate__ctor_m646599C15E83E910ECBF993A65DE9D21EE52B5FB,
	OutCirc_0000002CU24PostfixBurstDelegate_Invoke_m07503BBA4F11499827E7684D2A8CE92DA7344892,
	OutCirc_0000002CU24PostfixBurstDelegate_BeginInvoke_mAEC3DD694EE3909362E88D86FBFF6FC7800080B2,
	OutCirc_0000002CU24PostfixBurstDelegate_EndInvoke_m15755DF0F75928595A3F408C8E0E092345287D7C,
	OutCirc_0000002CU24BurstDirectCall_GetFunctionPointerDiscard_m869BF1C22F56D950C39FA49E26FFD67596FB55CA,
	OutCirc_0000002CU24BurstDirectCall_GetFunctionPointer_mBFDDFA9CA25D0C6E36B43F46429AE315AC92458E,
	OutCirc_0000002CU24BurstDirectCall_Constructor_mF11BB25E0271C68F3DC0D4262E4369645FA8C498,
	OutCirc_0000002CU24BurstDirectCall_Initialize_m190237ACA2439EEB47D3F635A33C7E27FD3E86A6,
	OutCirc_0000002CU24BurstDirectCall__cctor_m13FB0937551BF019A851C8539ACCB00E18849DDD,
	OutCirc_0000002CU24BurstDirectCall_Invoke_mF0455CD7944D635ABEF383ACE6C6601AE8538A0D,
	InOutCirc_0000002DU24PostfixBurstDelegate__ctor_mE0EC577CAABEBB6351DE16291B6F9934A76359D3,
	InOutCirc_0000002DU24PostfixBurstDelegate_Invoke_mF1C7C56775C55C1222682BFEF7F67CA3ED6F32D7,
	InOutCirc_0000002DU24PostfixBurstDelegate_BeginInvoke_mDA52E437F87711B375B44C93ABE47217BB01445A,
	InOutCirc_0000002DU24PostfixBurstDelegate_EndInvoke_mB594373B9E967E5823659D410691F22577ABDEFD,
	InOutCirc_0000002DU24BurstDirectCall_GetFunctionPointerDiscard_m158C8DB868C8189991C54A9DBCC7CCE012D5460C,
	InOutCirc_0000002DU24BurstDirectCall_GetFunctionPointer_m8800CA32F53205F5EA12FEAAFBE63151BE9513BB,
	InOutCirc_0000002DU24BurstDirectCall_Constructor_m070A9E01A300B9A29AD302418CDA57434389217C,
	InOutCirc_0000002DU24BurstDirectCall_Initialize_mE0FE5794DE611BA0FDDC24FD3C20F8D82FBB3A9F,
	InOutCirc_0000002DU24BurstDirectCall__cctor_m8A6446B205641D221ACD9B320C7C995DFA78BF66,
	InOutCirc_0000002DU24BurstDirectCall_Invoke_m6399115BF9180D4F0B2EDDF4606339EDD6E3BF9E,
	InBack_0000002EU24PostfixBurstDelegate__ctor_m3098F47E5F17CC52CDF2FE3B48BFACBC666C35E6,
	InBack_0000002EU24PostfixBurstDelegate_Invoke_m8191E8FDACC2BA6471DF5D7B16E6F861BBE3DFBE,
	InBack_0000002EU24PostfixBurstDelegate_BeginInvoke_m3471BFBC1589920AC01476AFB7346ADF46D8EB82,
	InBack_0000002EU24PostfixBurstDelegate_EndInvoke_m22AED87C0A7BAD74135AEBA0610C1FFFC7DFE888,
	InBack_0000002EU24BurstDirectCall_GetFunctionPointerDiscard_m4E57E277EC11E49557BA90A514D5A950B80F09CA,
	InBack_0000002EU24BurstDirectCall_GetFunctionPointer_m4B79669D215367DB1C7F3E2E19ADC1241BB2B3CF,
	InBack_0000002EU24BurstDirectCall_Constructor_m61D0876FA76ADA82F28F638962E9BEEEF8C85540,
	InBack_0000002EU24BurstDirectCall_Initialize_m483E51460C9F55308A6975205EBD8AB125C8D6BC,
	InBack_0000002EU24BurstDirectCall__cctor_mC83F598CB9CFB95B34582216CB445D2089F4A2C1,
	InBack_0000002EU24BurstDirectCall_Invoke_m6A4F01D15E5A6AC556B7AAFEBBE48A491870232E,
	OutBack_0000002FU24PostfixBurstDelegate__ctor_m2386C5EAB5555DB36700973ECF8E6CDEC6F252A6,
	OutBack_0000002FU24PostfixBurstDelegate_Invoke_m4AA6DD82D2D128094996052BEA85798905A9C1D2,
	OutBack_0000002FU24PostfixBurstDelegate_BeginInvoke_mAE98A3CBA92252CFA09D5D80A53048E7BA92E98D,
	OutBack_0000002FU24PostfixBurstDelegate_EndInvoke_m2B03B3C78EFACD90066379789BA18B233D32560A,
	OutBack_0000002FU24BurstDirectCall_GetFunctionPointerDiscard_m688E52DBE866D3932276EB36586A31766B5B4B30,
	OutBack_0000002FU24BurstDirectCall_GetFunctionPointer_m0CD5F4F1C1BCDABF8F1A9ED0C632B3A292BEE5D1,
	OutBack_0000002FU24BurstDirectCall_Constructor_mCD82122CA1C8686ED3E9281DFFAE5566E130B6F9,
	OutBack_0000002FU24BurstDirectCall_Initialize_mC9724F4ABC441DB3ADA0AB1B1C4602B27219CD86,
	OutBack_0000002FU24BurstDirectCall__cctor_m1A3B2D784A0B0C5006E5FED0BB7C010C76E3ECAD,
	OutBack_0000002FU24BurstDirectCall_Invoke_mABE9CF2945F72A44E5632BBD4B5626CCB61E9538,
	InOutBack_00000030U24PostfixBurstDelegate__ctor_mAACD786A575A99C0C1D5EB2A51B5BD28BEE05978,
	InOutBack_00000030U24PostfixBurstDelegate_Invoke_m47CAA49A1275CFD1A47B5F5316A24C4C45B5A987,
	InOutBack_00000030U24PostfixBurstDelegate_BeginInvoke_mC96FD2185B4EBCDBF994BFA11EE0E7A1C939FEE5,
	InOutBack_00000030U24PostfixBurstDelegate_EndInvoke_m63ACE6C2FB627CECAE739D993C1C342A9859682D,
	InOutBack_00000030U24BurstDirectCall_GetFunctionPointerDiscard_m576BD7DA5024F350DA651F18F4014EA96EC0EF43,
	InOutBack_00000030U24BurstDirectCall_GetFunctionPointer_m10A5F8E30CB96FD6D2E25C76D4114FA04F5397EF,
	InOutBack_00000030U24BurstDirectCall_Constructor_mA515E54AD4E641A3ED34D6F24D33F99429AB396D,
	InOutBack_00000030U24BurstDirectCall_Initialize_m393B4F3AFDB22CAFAB68ACDA8C98D6F547C8E2B0,
	InOutBack_00000030U24BurstDirectCall__cctor_m2159DC2E9FC2B3F764B9556B5C8FBDA9EE05BEF9,
	InOutBack_00000030U24BurstDirectCall_Invoke_m8E11B842AC98A85B48B76431C40E84FE9E0E9030,
	InElastic_00000031U24PostfixBurstDelegate__ctor_m98C4CC3EBC98659585CC985345C85419EA2139FC,
	InElastic_00000031U24PostfixBurstDelegate_Invoke_m031F91CBF735130411CE217AFC34C991CF19EA48,
	InElastic_00000031U24PostfixBurstDelegate_BeginInvoke_m995FF185567E95962D71BFDDA4EDDE78264CF4B5,
	InElastic_00000031U24PostfixBurstDelegate_EndInvoke_m63E1895654C3CEED71B547CBB1A0F1C5091988D1,
	InElastic_00000031U24BurstDirectCall_GetFunctionPointerDiscard_m2539F89F6D9643715AFCB1D2557A67B3EF0AB31B,
	InElastic_00000031U24BurstDirectCall_GetFunctionPointer_m581DBF0DC537E9E437ECBD867740D120F39DF463,
	InElastic_00000031U24BurstDirectCall_Constructor_mD7D9BE3E101D4A65B0A529E6B5644E35E801D2DD,
	InElastic_00000031U24BurstDirectCall_Initialize_m7E4248893D43B2D24F03633659349E7B1DB084E2,
	InElastic_00000031U24BurstDirectCall__cctor_m00627F7A3EC0DCE9C5811349810D4F8801729F3E,
	InElastic_00000031U24BurstDirectCall_Invoke_m689125905209D18666A62A2F7502A1F8E027C2A4,
	OutElastic_00000032U24PostfixBurstDelegate__ctor_m666214622229A9517B8F2895A3D6D94A209D697B,
	OutElastic_00000032U24PostfixBurstDelegate_Invoke_m67207E639E44FA39F5136DB9A39EC69456E58317,
	OutElastic_00000032U24PostfixBurstDelegate_BeginInvoke_m838C258356F04229863F41F8B1676A3A74D92AA4,
	OutElastic_00000032U24PostfixBurstDelegate_EndInvoke_m3E106787AD735EFA403BB5472425C577A10519D0,
	OutElastic_00000032U24BurstDirectCall_GetFunctionPointerDiscard_m628D3E4C0E675939339D0F13384BFC7636694C13,
	OutElastic_00000032U24BurstDirectCall_GetFunctionPointer_m4A3636A401338888F15855BF06CF09F97930142F,
	OutElastic_00000032U24BurstDirectCall_Constructor_m10D830A847698739A3B2A65DD5124CDFE9C42838,
	OutElastic_00000032U24BurstDirectCall_Initialize_m0522E9F5E8C811686D6C6B483E53D12F58C89859,
	OutElastic_00000032U24BurstDirectCall__cctor_mF057AC435A9CDB3DF3D67421240C5DE5DE605006,
	OutElastic_00000032U24BurstDirectCall_Invoke_mB9FE2791A873FB431B83221C825EB996BEFFB491,
	InOutElastic_00000033U24PostfixBurstDelegate__ctor_mE3E8951858CE7AD40C9AA6F77BCE513ED73ED4F2,
	InOutElastic_00000033U24PostfixBurstDelegate_Invoke_m6A23437992CE120BBA7568D21A6D34B67C88A432,
	InOutElastic_00000033U24PostfixBurstDelegate_BeginInvoke_mA33B80E243083697E16CA003C4A0B35D644FEF44,
	InOutElastic_00000033U24PostfixBurstDelegate_EndInvoke_m1CAA5D6CD8E33A555D3FD38E7B524C57CFD92B79,
	InOutElastic_00000033U24BurstDirectCall_GetFunctionPointerDiscard_mFCC2404BD72EAA69B020DA5D39C303334B4D6EC2,
	InOutElastic_00000033U24BurstDirectCall_GetFunctionPointer_m39B1A1F61907FB7A926DCE7E380F4C20B6F40FCC,
	InOutElastic_00000033U24BurstDirectCall_Constructor_m4635065A64B739E483E46A0B7C75F7CDD10E8C3E,
	InOutElastic_00000033U24BurstDirectCall_Initialize_mDF306F86225971E6E36BEEAFDEACE23FF3972901,
	InOutElastic_00000033U24BurstDirectCall__cctor_mF719301B80775E98EC74837DF94B8AF95A0C1B5E,
	InOutElastic_00000033U24BurstDirectCall_Invoke_m7FAEE9030EB1EEFE167FE21762B432F4C11B58D8,
	InBounce_00000034U24PostfixBurstDelegate__ctor_m2399325CEB1196321E9016680D2E5E750B7E1DB7,
	InBounce_00000034U24PostfixBurstDelegate_Invoke_m40A3F91BD8629A2540B48DABEAF9E4D2DE4D712A,
	InBounce_00000034U24PostfixBurstDelegate_BeginInvoke_mB3BE2654C1C5D0A8B5F6907C038CBA0578DFF27C,
	InBounce_00000034U24PostfixBurstDelegate_EndInvoke_m546E7F1C177A5D931CF14AD5F331D39FFC1D150F,
	InBounce_00000034U24BurstDirectCall_GetFunctionPointerDiscard_mBDF143BB8A1A1B2BC4BD48169CFDD46641545091,
	InBounce_00000034U24BurstDirectCall_GetFunctionPointer_m7F6FAD99C9E6C2B6E5CF489C5BA6F2E00775C593,
	InBounce_00000034U24BurstDirectCall_Constructor_mDDFEC7EFDD9D19043FC31F4340AB01119EF2D8A4,
	InBounce_00000034U24BurstDirectCall_Initialize_mC1086AF50C6350A74BAD56DC03803E85380066A0,
	InBounce_00000034U24BurstDirectCall__cctor_m272A847D7AD87AD0991C7C35AA08A927DBEB0B28,
	InBounce_00000034U24BurstDirectCall_Invoke_m36ABC346F9613CA5F88A91F3CA6BCF4AB00F9F95,
	OutBounce_00000035U24PostfixBurstDelegate__ctor_m96D54DC983841ECCBFCB6F90B0B09B32C4D1AAB8,
	OutBounce_00000035U24PostfixBurstDelegate_Invoke_mF11E4D95E839DD7F4CAF3FEAF69221D60531541F,
	OutBounce_00000035U24PostfixBurstDelegate_BeginInvoke_m1E482D87A39397692D7088F1FD536226F6D9EEC8,
	OutBounce_00000035U24PostfixBurstDelegate_EndInvoke_m3B134D562C5E1F4EEBD394EADB0D8569D790DB36,
	OutBounce_00000035U24BurstDirectCall_GetFunctionPointerDiscard_m5857ACB2AF3E929ADE3359BC7B66E85F80505DC8,
	OutBounce_00000035U24BurstDirectCall_GetFunctionPointer_mE741195E3A8D79BA07E141517A9E637B15A7DD64,
	OutBounce_00000035U24BurstDirectCall_Constructor_m23609828F3448393C693D63FE7504B647F376B3F,
	OutBounce_00000035U24BurstDirectCall_Initialize_m6DD5A36BBD27F4C739F79C5243124090E9839C02,
	OutBounce_00000035U24BurstDirectCall__cctor_mE73584C39A3AD468839439305A1C27A585E4FA64,
	OutBounce_00000035U24BurstDirectCall_Invoke_m21F068F74E7EF2BB260A5942267AEE679FA0C6E4,
	InOutBounce_00000036U24PostfixBurstDelegate__ctor_mE688CB4DF052BE8A532A0EB9C43EAB1BA784389D,
	InOutBounce_00000036U24PostfixBurstDelegate_Invoke_m38FC06755DD9C9F58148DD627FC79E809E240714,
	InOutBounce_00000036U24PostfixBurstDelegate_BeginInvoke_m80D3E722757EA033125F83F5E130E59ED516696F,
	InOutBounce_00000036U24PostfixBurstDelegate_EndInvoke_m0A168AAD6B48F1728BD2F21326D1EC75A8C9FEB5,
	InOutBounce_00000036U24BurstDirectCall_GetFunctionPointerDiscard_m22271C64E789531C0D925E540ED93036F07E3247,
	InOutBounce_00000036U24BurstDirectCall_GetFunctionPointer_m6E557E26E734D288FF7334FB5DACB154C9D13C8A,
	InOutBounce_00000036U24BurstDirectCall_Constructor_m894FC992441639A575AD1F12F8570CF99D97AEA4,
	InOutBounce_00000036U24BurstDirectCall_Initialize_mE59CA6EE1BF0DD229B34F028C68F568EE6A3827F,
	InOutBounce_00000036U24BurstDirectCall__cctor_mAE3FA4CCAFBAE260883869B655989D24F0FEC30F,
	InOutBounce_00000036U24BurstDirectCall_Invoke_mD2E268A662DC8BD846EF1F190C89D423135D556E,
	NULL,
	NULL,
	NULL,
	ArrayHelper_EnsureBufferCapacity_mF22F65DA72F76C3FD3F8E14286FEA2D1EB40C6CF,
	ArrayHelper_FastResize_mAED65F4307F535154F8D54F691E976F0D7CE32C5,
	NULL,
	Box_Create_m439F69D7165CEC55E57E832A192202D7FAAA5E4E,
	Box_Create_mC1736654ECE08299633A59C3833B413F63C271D3,
	Box__cctor_m81D0A3258AF2D1DA0A2DF913257A7C63F62060E7,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	Error_Format_mCA7E93BC39656DE151F38E7EAD5E25568AA064C2,
	Error_Argument_m1191EA42C71D91FEA4D2E16AAFA87738F62C32FE,
	Error_ArgumentNull_m41C1B297D31423BA533FC9B6DF7D7CF4046E9BDA,
	Error_MotionNotExists_mADC1B81D88E6BEAA6C8AAD7F28014791F98B403C,
	Error_MotionHasBeenCanceledOrCompleted_m3C3454BF847862B0F6A8865028A63D439F10BB22,
	Error_MotionIsInSequence_m043546EF891B183DFC74DC8F7296634959B44FCE,
	FixedStringHelper_GetScrambleChar_mE80AE9E83FC5E7941D0EF3CE275DCC6A91229922,
	FixedStringHelper_GetUtf8CharCount_m9D5B6C6440937FEF2D4DF12848E2921D4F531953,
	FixedStringHelper_GetRuneOf_mA63C25E8D560B2E6331D400A04FC7AC535CAE4D1,
	FixedStringHelper_Interpolate_m3CB56363D8515E498CAE3943B278AC4B980C8EDB,
	FixedStringHelper_FillText_m7D9D1596D71EA99AB4A7A303D862167D1ACB9215,
	FixedStringHelper_FillRichText_mA11CCE3CEFC2AF229423FBDA68A62C451B25FCDB,
	FixedStringHelper_FillScrambleChars_m03D12896FC0C2C5401D443BE69F488335926A2AD,
	FixedStringHelper_SliceSymbols_m9F7A8AE327F0BDDC9A6B4A4E7785061ECA81AE75,
	FixedStringHelper_GetUtf8CharCount_m73D0837E13C8258F7C73D0AC6882BAD1CB59EBE4,
	FixedStringHelper_GetRuneOf_m3FDB4B3B3658B1650AD2C40E3795FDD2BF05F771,
	FixedStringHelper_Interpolate_m501C917268A6F366CE197E792BCEBC99B4BAE3A6,
	FixedStringHelper_FillText_mB3EA616ADA4865446CDD0605CF5E134AB31912E3,
	FixedStringHelper_FillRichText_mA5BFBCD898278B4B08F7BB89AF6270F6FF236DA5,
	FixedStringHelper_FillScrambleChars_mA6A407E6898E82C0481C68F9F9F1AA647021D6EA,
	FixedStringHelper_SliceSymbols_mB75F4FD7885E68FB1B2D8821C97BD231696DDBB8,
	FixedStringHelper_GetUtf8CharCount_m395D732466B111DCF2187871EE9AF35802D89E08,
	FixedStringHelper_GetRuneOf_m1C8AF748094BB6F3EED03E6A22055BC16AB2A3AF,
	FixedStringHelper_Interpolate_m38748B2753DFF9DBB8131EFAE60349D700550808,
	FixedStringHelper_FillText_mC7321D86FCDE8CDD9ADC18C719413535B20A8A3C,
	FixedStringHelper_FillRichText_m694355439E2778A4A08B3CA7185BD33A462C37E1,
	FixedStringHelper_FillScrambleChars_m03FB25FD724EDB3BABD4829FD49892C0F68BFFA4,
	FixedStringHelper_SliceSymbols_m9DA36DD179F9413120B3E3E08B0B3D2DDC4BAB42,
	FixedStringHelper_GetUtf8CharCount_mFEBFE66A2581B05E4413D751905A87AA23D4875D,
	FixedStringHelper_GetRuneOf_mD88D2508FDCF5C9D143C86DC72D4AB03A019CD63,
	FixedStringHelper_Interpolate_m3FD45393BF75D45F8EC53D3993160DCB1CC87D20,
	FixedStringHelper_FillText_m5D8127161FDF592434E91BE534646DCE176EC48C,
	FixedStringHelper_FillRichText_mF13D30235157E1F8AA3186025FAE9FA452A7941C,
	FixedStringHelper_FillScrambleChars_m7802A351117410543210FA564DA7610025EF6C51,
	FixedStringHelper_SliceSymbols_mE4B911AB8613F1741A9FAC492C6C08CF32DD550A,
	FixedStringHelper_GetUtf8CharCount_mE00685F29DA06F54BC83A062CE8B98CF620A4330,
	FixedStringHelper_GetRuneOf_mC998ACF50CA3470D4EC2F8DEA8530C95AF192D71,
	FixedStringHelper_Interpolate_m991CE1A016A7BDBE6AD81012865B387C93689D60,
	FixedStringHelper_FillText_m9CE062C75E3915F62E769EBC97C56428CA28B648,
	FixedStringHelper_FillRichText_m332081CBAC1EA954CE6D459960C41F4BE82B9F36,
	FixedStringHelper_FillScrambleChars_m46B11F3B7893D2EFF8168B88927507C691AB79A3,
	FixedStringHelper_SliceSymbols_m7D4680FA59DA77656CF529942EC97D6E78FAF448,
	FixedStringHelper__cctor_mD56E259CC104DDAC6CF63F91953DB826C0CA8F88,
	FixedStringHelper_GetUtf8CharCountU24BurstManaged_m33B38FCB3E7111320D2D2106BE893A3BF4F6CC84,
	FixedStringHelper_InterpolateU24BurstManaged_m8F4F09B244811500C4EBA05A459E9459C97F1C85,
	FixedStringHelper_GetUtf8CharCountU24BurstManaged_m2E5B858B966719B8085A37309EE713EA9F379FFF,
	FixedStringHelper_InterpolateU24BurstManaged_mDAE091CC82888DABBD443652F14C44BA8F8ED309,
	FixedStringHelper_GetUtf8CharCountU24BurstManaged_mAEA74933B2CEF3A011DC6DF5EE72C23AD6D36624,
	FixedStringHelper_InterpolateU24BurstManaged_mF588200F8B214479CC063D77BC9052BA128E6EE9,
	FixedStringHelper_GetUtf8CharCountU24BurstManaged_m3E68A3454D35102186AB0B874529542C875A6B9D,
	FixedStringHelper_InterpolateU24BurstManaged_m99E3BD93F4817C77BE0F1B684DB9829CED241C6E,
	FixedStringHelper_GetUtf8CharCountU24BurstManaged_m89E2CC92B55135BE5BF9C1E5FC8C2AF2FF33E9FB,
	FixedStringHelper_InterpolateU24BurstManaged_m15D8247FA0A74A989DD95EB5D9F1E45845C1B908,
	GetUtf8CharCount_00000054U24PostfixBurstDelegate__ctor_m6041EA0AC523B405A4CFDAA4F59FAA57BC6E92A5,
	GetUtf8CharCount_00000054U24PostfixBurstDelegate_Invoke_m36E5B0B6B59379CF4EF6DCFA650419B728AFDEC8,
	GetUtf8CharCount_00000054U24PostfixBurstDelegate_BeginInvoke_mCF333B262E84DAC440E2216AC3D7BBDC9F447719,
	GetUtf8CharCount_00000054U24PostfixBurstDelegate_EndInvoke_mA4394C03654F5CB43ADBE31875C65BFDB711B3D4,
	GetUtf8CharCount_00000054U24BurstDirectCall_GetFunctionPointerDiscard_m45FD32FF6CC732CEFC707012994D05F8E8CC4799,
	GetUtf8CharCount_00000054U24BurstDirectCall_GetFunctionPointer_m140B1BE6D656F1016CCD42E6B3DB2EF202184858,
	GetUtf8CharCount_00000054U24BurstDirectCall_Constructor_m0450E94A2145EBCA89C8D9627CE48F06D1F67AE8,
	GetUtf8CharCount_00000054U24BurstDirectCall_Initialize_m395D7D761CA42D4FB9A6843122F2D779F2ADD603,
	GetUtf8CharCount_00000054U24BurstDirectCall__cctor_mD2D4948A3023BBDB32ACFE3CB3CA36242CAE103C,
	GetUtf8CharCount_00000054U24BurstDirectCall_Invoke_m49633D214D601BECF2755A3EF8925157B792A7AE,
	Interpolate_00000056U24PostfixBurstDelegate__ctor_m005487C1D3A0D4C545CD0712C1E1EBD8D0BBD0AC,
	Interpolate_00000056U24PostfixBurstDelegate_Invoke_mBF73032FBF543D4EB4430BFDF39D759B659E895C,
	Interpolate_00000056U24PostfixBurstDelegate_BeginInvoke_mB2E02977BDB10A87B4CCD6ED3A8178087B031F8B,
	Interpolate_00000056U24PostfixBurstDelegate_EndInvoke_mB04788B0BA7848C7371A24F06B5407E1C71B47B0,
	Interpolate_00000056U24BurstDirectCall_GetFunctionPointerDiscard_m17959F9E7B1508ACC5B790E882053F0F6A7C5700,
	Interpolate_00000056U24BurstDirectCall_GetFunctionPointer_mEA4DBB3CE11C0787CA12D09A8ED63F4786E4A928,
	Interpolate_00000056U24BurstDirectCall_Constructor_m7932CF0C84CC16F3D197743F34B499DE83F04DD4,
	Interpolate_00000056U24BurstDirectCall_Initialize_m91B79A73238628B12A618FAB1EE4B34572378A91,
	Interpolate_00000056U24BurstDirectCall__cctor_mAE3A9AEAE6FAC42188DA3254CCA3375B6BC0B512,
	Interpolate_00000056U24BurstDirectCall_Invoke_m250E57BEC92B5DCF9527FC5978BB36CBEFB5D43A,
	GetUtf8CharCount_0000005BU24PostfixBurstDelegate__ctor_mEDE51D84F64CDF1C193CBD02DCCDDA72A8357C9D,
	GetUtf8CharCount_0000005BU24PostfixBurstDelegate_Invoke_mE13C20E9EFF2EDA191839A292FA8307AB43088FF,
	GetUtf8CharCount_0000005BU24PostfixBurstDelegate_BeginInvoke_m33B47E1B48AF2C5FA72A01BDFF8FBD752DA59945,
	GetUtf8CharCount_0000005BU24PostfixBurstDelegate_EndInvoke_m8CDE6F07805D364931FEAEBBDEE917DCA5C53202,
	GetUtf8CharCount_0000005BU24BurstDirectCall_GetFunctionPointerDiscard_m19AB959B2D0F609BE6207590F250B8BF8384035E,
	GetUtf8CharCount_0000005BU24BurstDirectCall_GetFunctionPointer_mF71898B17A38B0CFF5B34B10E60194ABF467C059,
	GetUtf8CharCount_0000005BU24BurstDirectCall_Constructor_m948D21255E4FB499DCD3640377BEE436C74544B4,
	GetUtf8CharCount_0000005BU24BurstDirectCall_Initialize_m78829C817F4BC28716C599C091E48BAA34626EE1,
	GetUtf8CharCount_0000005BU24BurstDirectCall__cctor_mD71AB53977BDAE558D564E7E0AF3E69C9F2250B9,
	GetUtf8CharCount_0000005BU24BurstDirectCall_Invoke_m4616139239633EBAB8BB6BED79D38FD0A8B0443B,
	Interpolate_0000005DU24PostfixBurstDelegate__ctor_m5B86779F3B9E30AEDB93DAF7EA62CA4CC985EE75,
	Interpolate_0000005DU24PostfixBurstDelegate_Invoke_m7C73636D7D8B6DB45552E92CAD39C1CFB12DADFC,
	Interpolate_0000005DU24PostfixBurstDelegate_BeginInvoke_mA2324437380D5C0E55759871D2A98BA76E48F9BC,
	Interpolate_0000005DU24PostfixBurstDelegate_EndInvoke_mBB2E27AAC57265D393D3E8929150E517336F685E,
	Interpolate_0000005DU24BurstDirectCall_GetFunctionPointerDiscard_mBF7DB9E08DE3F2F293C199C151F5A84CE642430A,
	Interpolate_0000005DU24BurstDirectCall_GetFunctionPointer_m268118846C5A8C0C7DE4B4FFBBFDC61B34FF32E9,
	Interpolate_0000005DU24BurstDirectCall_Constructor_mFCEEA6B3AEE353A6F789BAC3914791D1D8A1D25C,
	Interpolate_0000005DU24BurstDirectCall_Initialize_m665EE9EABC86816190C6FBE93276D1CB5B5D20DD,
	Interpolate_0000005DU24BurstDirectCall__cctor_m942A7518A8A3051990E8A46F5915FC7F3AF06D8C,
	Interpolate_0000005DU24BurstDirectCall_Invoke_mE73EC445B02C885A450CADE1CD6C9CABE709ABBD,
	GetUtf8CharCount_00000062U24PostfixBurstDelegate__ctor_m0529FB7A7FF7D6A72441C64DE212BE5E3714A144,
	GetUtf8CharCount_00000062U24PostfixBurstDelegate_Invoke_mF56F9B4155326EAD3DB1C8FD6D77133EC82DA501,
	GetUtf8CharCount_00000062U24PostfixBurstDelegate_BeginInvoke_m718F448C48E8651FED2E6F0B1AD9F86C203C0D66,
	GetUtf8CharCount_00000062U24PostfixBurstDelegate_EndInvoke_m27EAF6E2DCF48BDDBC5B49685391CA193F981776,
	GetUtf8CharCount_00000062U24BurstDirectCall_GetFunctionPointerDiscard_m42FDB1C08FBFDEC60B54E97795FB925D62F604C7,
	GetUtf8CharCount_00000062U24BurstDirectCall_GetFunctionPointer_m6A5B1B0509A5A5E680A7EDD4B4260DA63AC0F694,
	GetUtf8CharCount_00000062U24BurstDirectCall_Constructor_mBEED39AB02298735A04E7995E46CC9A46DCDE47B,
	GetUtf8CharCount_00000062U24BurstDirectCall_Initialize_mF9146318DC41EC73E4D94829A943E2AB650AD271,
	GetUtf8CharCount_00000062U24BurstDirectCall__cctor_m8DBBB60FC2CC4901E74DF7EF1DE788C88DB37B0D,
	GetUtf8CharCount_00000062U24BurstDirectCall_Invoke_m69AADEC80068521FCE8E5C8E093EF5106EB1A27E,
	Interpolate_00000064U24PostfixBurstDelegate__ctor_mB739B7EA79C225DE564230205979C9549B56C3F3,
	Interpolate_00000064U24PostfixBurstDelegate_Invoke_mAE1F2F6F6F792AC1127F12F4ED2714E96847BA20,
	Interpolate_00000064U24PostfixBurstDelegate_BeginInvoke_m8EB6A54184C9F355387D49808C73317C5DF21617,
	Interpolate_00000064U24PostfixBurstDelegate_EndInvoke_m138B878DC5FD9FAA17948CC9F37E9291DE1137C6,
	Interpolate_00000064U24BurstDirectCall_GetFunctionPointerDiscard_m3990F2DD81C95A2A5E42AE13470FE1116991FF3E,
	Interpolate_00000064U24BurstDirectCall_GetFunctionPointer_m521EC6AC38A3728FAA5F5E60AC21D16B27ED3223,
	Interpolate_00000064U24BurstDirectCall_Constructor_mF67C21BF6FDE1D1EAA8814B4C21051B1DDC8C013,
	Interpolate_00000064U24BurstDirectCall_Initialize_mD80B2BA2BD9C6059F79705ADF77BA8E4C7A032FF,
	Interpolate_00000064U24BurstDirectCall__cctor_m486A3BCED0D8F220A21C7C3CB226ECBBD8CCFAA3,
	Interpolate_00000064U24BurstDirectCall_Invoke_m3449C6EC0667EA358827B66E6FC219A0706C286D,
	GetUtf8CharCount_00000069U24PostfixBurstDelegate__ctor_m2D1790D86685C94BCA99BE73C64F6AD704AD999F,
	GetUtf8CharCount_00000069U24PostfixBurstDelegate_Invoke_m4FCD8FEE981229F79C8CF4FAEAA743D49546B21F,
	GetUtf8CharCount_00000069U24PostfixBurstDelegate_BeginInvoke_mAC67321B315A9E81E77080927E3A8D9900C0FE9B,
	GetUtf8CharCount_00000069U24PostfixBurstDelegate_EndInvoke_mF56CCFDFEE7DC5123EC9027A3F55E27D0314C284,
	GetUtf8CharCount_00000069U24BurstDirectCall_GetFunctionPointerDiscard_mF6B295FE4A1EA67A01D17C06A3C8CD84E9E81561,
	GetUtf8CharCount_00000069U24BurstDirectCall_GetFunctionPointer_mE0ADF3571453DB6A03DB9FF9E3480F09626F6FC9,
	GetUtf8CharCount_00000069U24BurstDirectCall_Constructor_mA942C8EA2C39571323B62BFB2B534493719A49BA,
	GetUtf8CharCount_00000069U24BurstDirectCall_Initialize_mE289B4A41818417817B3423C4BCBB1F820A5B7A8,
	GetUtf8CharCount_00000069U24BurstDirectCall__cctor_mA0F7101D737C5500FC8E40B79976A02B7D641438,
	GetUtf8CharCount_00000069U24BurstDirectCall_Invoke_m115C6C904CA64010D08000C8CBCA52B4ED4C1640,
	Interpolate_0000006BU24PostfixBurstDelegate__ctor_mDE408891A70CE5646443E975BBBBE6D9795E9564,
	Interpolate_0000006BU24PostfixBurstDelegate_Invoke_m8C973BC99E6CF4B945B2460DCE7D8B707594EB90,
	Interpolate_0000006BU24PostfixBurstDelegate_BeginInvoke_m2C9FFC9EBB1EA704879BAA5C9E90D27B9C405599,
	Interpolate_0000006BU24PostfixBurstDelegate_EndInvoke_m83A477D4786ACB52914E8F7139C7432816BF85BC,
	Interpolate_0000006BU24BurstDirectCall_GetFunctionPointerDiscard_m514D4C32DBA08AA647C39D8DDD96BE6DDB47649D,
	Interpolate_0000006BU24BurstDirectCall_GetFunctionPointer_m574A1B355FFD8C8A6334EFAD160516660BFB7B1C,
	Interpolate_0000006BU24BurstDirectCall_Constructor_m88B9C613B32A9F37E5A629E83C06CFF34AA4F67D,
	Interpolate_0000006BU24BurstDirectCall_Initialize_m0EF95D8DF397390BF5ABA357F52AB5CAC422B875,
	Interpolate_0000006BU24BurstDirectCall__cctor_mFF36E67AA47EDE6A0FB7B2048B3C2FE58329E860,
	Interpolate_0000006BU24BurstDirectCall_Invoke_mD15D327400D225664AFB10B75C9818821A8CF7D5,
	GetUtf8CharCount_00000070U24PostfixBurstDelegate__ctor_m38A5F6AF460C6EBBF7D76A7766D29EEC3414124B,
	GetUtf8CharCount_00000070U24PostfixBurstDelegate_Invoke_m8C2E08DB5A2E7EE2802E5964CF00C78BF0A2AA23,
	GetUtf8CharCount_00000070U24PostfixBurstDelegate_BeginInvoke_m56A765F089421DF36C0F672BDCE859E33B9593C5,
	GetUtf8CharCount_00000070U24PostfixBurstDelegate_EndInvoke_m748B7DFA3FA01BE5415A2316C08C6C752F848EAE,
	GetUtf8CharCount_00000070U24BurstDirectCall_GetFunctionPointerDiscard_m3A8DB731D29A6E4377CE5FADA202E5D73C950B39,
	GetUtf8CharCount_00000070U24BurstDirectCall_GetFunctionPointer_mBBD736120DA22EC80A9C5884EF39860BA680F188,
	GetUtf8CharCount_00000070U24BurstDirectCall_Constructor_m2309F30D6E0F06F17C683123C36B0D5BDE06BE64,
	GetUtf8CharCount_00000070U24BurstDirectCall_Initialize_m5B1A1A7D47C97CFAADC097B6C5E4E8FA6371BCCA,
	GetUtf8CharCount_00000070U24BurstDirectCall__cctor_m56E9247DBD538CC3F585D6E98DFBE28141C66D87,
	GetUtf8CharCount_00000070U24BurstDirectCall_Invoke_m92DE72BC9F966607139FAE3EBFE312E3A749F67E,
	Interpolate_00000072U24PostfixBurstDelegate__ctor_m7EAA3A35BEC5612BD0438CAF52C26D404196E9E5,
	Interpolate_00000072U24PostfixBurstDelegate_Invoke_mC28A2A5B98FEAF538B7C4B315C212BB64ADBFDB4,
	Interpolate_00000072U24PostfixBurstDelegate_BeginInvoke_mA41379D0FF69121305386F996201822758041A55,
	Interpolate_00000072U24PostfixBurstDelegate_EndInvoke_mA0E96EF11E7BD5AD6C63EBC1808C6625E20CC793,
	Interpolate_00000072U24BurstDirectCall_GetFunctionPointerDiscard_mACE8D24570A4660EEAE4B6891D4986AC84A79B50,
	Interpolate_00000072U24BurstDirectCall_GetFunctionPointer_mFF4A21F4C63A196CB77E7801FC763DCAD891FBBC,
	Interpolate_00000072U24BurstDirectCall_Constructor_mD416FF61748512C899497A6F43E4B44DB262BC8E,
	Interpolate_00000072U24BurstDirectCall_Initialize_mD117F843860D20C34B5D59C4E31FCAFA68E440BB,
	Interpolate_00000072U24BurstDirectCall__cctor_m68A882AEF0DBEE590B6AA0736B9E4BF904D1E020,
	Interpolate_00000072U24BurstDirectCall_Invoke_m241DEF0EFAE37E7EBB1374C46873F31BE2238ED3,
	NULL,
	ManagedMotionData_InvokeOnCancel_m73240D0D9B2ACE57785592A78ADA22910624A9BF,
	ManagedMotionData_InvokeOnComplete_m5CDE09F72CF3E6B4701EE5AFA09AF288C1F3A6E5,
	ManagedMotionData_InvokeOnLoopComplete_m0AB33D63FC13012AC691C4F93D444AF4E5342D65,
	MotionData_get_TimeSinceStart_m98F66E013764C57EEB2D4D31F5C0EBF3D51F8828,
	MotionData_Update_m5B3BC2C8E45492DB4682325ACCFBC36BE289538F,
	MotionData_Complete_m734EE33FAC8939EC5F4DFADDD1BA62B42DE7F1C0,
	MotionData_GetClampedCompletedLoops_mB421308254CDA470E5BFB7E6B13DE758B4A5B837,
	MotionData_GetEasedValue_mDE2CC8C13F67B1B2D52651726F564695895E9360,
	MotionState_get_WasStatusChanged_m9618DFD6BA628253A183FE3C50464674CDCEA95A,
	MotionState_get_WasLoopCompleted_m9721F9B229DD0C67B41514B12C81B9F9AA926B8C,
	MotionParameters_get_TotalDuration_mB7A505F6BCE808D3071AF959E08FC6B1ECCD9047,
	NULL,
	NULL,
	MotionHandleLinker_Register_mE38DFBF513DC0FFE77F26B888E936CD44EB53EA6,
	MotionHandleLinker_OnDisable_mDDF2CE467D10D992A189ACAE16CD85FB0FD1361C,
	MotionHandleLinker_OnDestroy_m64BAA94D1126CEC5ED6FF0937465DD910F731101,
	MotionHandleLinker__ctor_m0AD731C8D1FA4F1699EC3597B15CC1F7D8AC8683,
	MotionManager_get_MotionTypeCount_m747275F1C694CA07B477CA9E940FCFE9C5051A5F,
	MotionManager_set_MotionTypeCount_m62CC04C3C1B4AD79629B2F921D9E4665DD783340,
	NULL,
	MotionManager_GetDataRef_m51519EC669AF4A72192ECD1B44279959134E8CBE,
	MotionManager_GetManagedDataRef_m56749CE994D083F22E7FF3458B71A9B04C3FFDAF,
	MotionManager_GetDebugInfo_m3D283A75EC7440238ECC61CF14EC00285B30D3AB,
	MotionManager_Complete_m2E77217D54FCF03CDCBE5F628990FA6C9FC64859,
	MotionManager_TryComplete_m7BECEFD3EB4321B741D8ABDB3B999A2B065BD611,
	MotionManager_Cancel_mB9BE86260528DFA714770AB0212729CD4726E4F1,
	MotionManager_TryCancel_m3F8F15BDC21AEBB3EEDB58CC70B604A8C9EE972A,
	MotionManager_IsActive_mF4852406A0DD2636E235C9D61F213B0F33063258,
	MotionManager_IsPlaying_m73338D2E14A34995AB74EB371311EB23611F819C,
	MotionManager_SetTime_mCA80BF9864CB8C61964C1AE742068029E3619682,
	MotionManager_AddToSequence_m186353CB9EB053D8AF67815013CE37602D327DB0,
	MotionManager_GetMotionType_m92CC11F730DDD74C17C0040F35B3B23ED422168A,
	MotionManager_CheckTypeId_mEFEB7AD899D19579BD664224BD414C6A366792CB,
	MotionDebugInfo_get_EqualityContract_m8AC08AB50D4ECDB64DC198F74A524461BAA726CF,
	MotionDebugInfo_ToString_m8B304B456099E348430301E13657461FC73F7C1A,
	MotionDebugInfo_PrintMembers_m05CD9A9D04490B0880585FB6908063073740987F,
	MotionDebugInfo_op_Inequality_mEEEFF7436034AD45AAAB38E7A1A06ADEE1CF7C67,
	MotionDebugInfo_op_Equality_mE54C528DD0B27CED2FC4A2AE17F831A88958018C,
	MotionDebugInfo_GetHashCode_mB69D5E3C742E396399D0D5A9B05548AF150B39D6,
	MotionDebugInfo_Equals_m9658FAF9F09235C9CEC4C9713F98FE44618E1C1B,
	MotionDebugInfo_Equals_m88DF65BC06170F09C2BCF6E483FAD39776AA85E7,
	MotionDebugInfo_U3CCloneU3EU24_mDD0D18EF003C9EF2056E96FD12FB59752867FE2C,
	MotionDebugInfo__ctor_m8EFEF8142374DE6DBAEC9F35DF69CDAC42AF983A,
	MotionDebugInfo__ctor_mD0436D1CCB3B2E357110455A51A6F80EAE89A23B,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	PlayerLoopHelper_add_OnInitialization_mF7E009A43A8F6F01ABD2E8DAF60ACCDD79896BB2,
	PlayerLoopHelper_remove_OnInitialization_m3334632B149F9E16A6070B28E815C88EDBAEB235,
	PlayerLoopHelper_add_OnEarlyUpdate_m0C345297B551BA599AAAF21FCE9A6CF1398E6E57,
	PlayerLoopHelper_remove_OnEarlyUpdate_m3A5867C6A857C6B9E743F95632969BDF0652C46E,
	PlayerLoopHelper_add_OnFixedUpdate_mFE95ED7DF1598DFF799D9242F92203D5BE4259CB,
	PlayerLoopHelper_remove_OnFixedUpdate_m1E66E94A0E6A30D0275E50AEC061A59772FDF32D,
	PlayerLoopHelper_add_OnPreUpdate_m2BD427F082819A22CC8B0069D064C933321C17C3,
	PlayerLoopHelper_remove_OnPreUpdate_m615CB0A1D8D3DE73AD83E0E7004100DF66C666DB,
	PlayerLoopHelper_add_OnUpdate_mF1902FE292B2DEC2790E4D6C81E5B4A5A449A159,
	PlayerLoopHelper_remove_OnUpdate_m48E7BEC798CBE7F6F74790BB4DD2AF49B0F6ABE0,
	PlayerLoopHelper_add_OnPreLateUpdate_m05F01A8B248045F7F14088D60A680109982700E1,
	PlayerLoopHelper_remove_OnPreLateUpdate_mACAA88D3BBACED3CABC5225C4316B3D76246DFF2,
	PlayerLoopHelper_add_OnPostLateUpdate_m6858C8FCE634E8205361D5E0E46E03EB056FB872,
	PlayerLoopHelper_remove_OnPostLateUpdate_mE902B3EAAA05933426AB4E9CA6D26254059B0890,
	PlayerLoopHelper_add_OnTimeUpdate_m5BEA07CF6C8B710F77BD4B4EF861AA088075180E,
	PlayerLoopHelper_remove_OnTimeUpdate_mBE589904899E9B92CD70F77B9D361A888C271BBC,
	PlayerLoopHelper_Init_m9ABA6A108AD94794C34E196F726980295A136727,
	PlayerLoopHelper_Initialize_m8B8E24D064532DDC3CC260DD920F3105075D06BA,
	PlayerLoopHelper_InsertLoop_m8DB8425DD3866B598AA965ECF64474E491DB24F7,
	PlayerLoopHelper_FindLoopSystemIndex_m5F564D4474F1835C2CA53106CF8AC582FF15E03A,
	PlayerLoopHelper_InsertRunner_m4DBAF2DFCA0FF9A11BE7E03B5AB27658AFB8C254,
	U3CU3Ec__cctor_m8C857AC171AE9EF434D2B205299C9961F949FE09,
	U3CU3Ec__ctor_mD5E0B818D0A5B158D0D4154EA7F3D8E5A2097F9A,
	U3CU3Ec_U3CInitU3Eb__25_0_m7ADEE451C4DB0FC7AB677F6E41A6FE777276ECC9,
	U3CU3Ec_U3CInitU3Eb__25_1_m017A1529995E50E2C0184DFC76EFBF9E54C0BD03,
	U3CU3Ec_U3CInitU3Eb__25_2_mF636189B9B3BBBB028926C0BA7C5EF46FEEB4893,
	U3CU3Ec_U3CInitU3Eb__25_3_m6BA627FCAC7DA587C2904996A639DD2F50D29A13,
	U3CU3Ec_U3CInitU3Eb__25_4_m378BF418289DE09847484A648E4C8BFED4559162,
	U3CU3Ec_U3CInitU3Eb__25_5_m20FD33C16BFCD5A8F76FCBC1E4C04DF3F6A64436,
	U3CU3Ec_U3CInitU3Eb__25_6_m2038A7EE618E69D8F0456DCD367C2957B8D16BA6,
	U3CU3Ec_U3CInitU3Eb__25_7_mD2DC8735BD62E1CAC6890A209CABC8B051179FA7,
	U3CU3Ec_U3CInitializeU3Eb__26_0_mE46DCB5B9449456D27993068E85B508C8927A072,
	U3CU3Ec_U3CInitializeU3Eb__26_1_m6A8D71D2B30B8294B654CEAA58A1CA7215B1094D,
	U3CU3Ec_U3CInitializeU3Eb__26_2_m38DCEDC1BC57EEA96063D092B2E5889295035C25,
	U3CU3Ec_U3CInitializeU3Eb__26_3_m9D8D7BEA0C3D7586CB7516ECD634823AF1B9C688,
	U3CU3Ec_U3CInitializeU3Eb__26_4_m1CC6A133965E18E920E4BF1115D3F0C4C86A51A5,
	U3CU3Ec_U3CInitializeU3Eb__26_5_m64A223761A6978C7EDA3D6B53EADB3B9F96713E7,
	U3CU3Ec_U3CInitializeU3Eb__26_6_m9F6789B3A346068E7EEB9C2C17EB5C98770F98E5,
	U3CU3Ec_U3CInitializeU3Eb__26_7_mC4D811659D2E61FFB507AE0898D4F0C76EFD798C,
	U3CU3Ec__DisplayClass29_0__ctor_m0EA35057BD72E9B5BE70FBA7AE32B93BC7B358E7,
	U3CU3Ec__DisplayClass29_0_U3CInsertRunnerU3Eb__0_mEC0B2B4112F3AC39A2F4A8631A709FB8FB7C4191,
	PlayerLoopMotionScheduler__ctor_mA7245EDD1685E56EB3B0F436AC0BEEB84E56FBEB,
	NULL,
	RandomHelper_NextFloat_m3338E86CBEB4C5EDC0FE480B5C0740FC95788E2E,
	RandomHelper_NextFloat2_mFD2CD5917D9198E64B3D25BECA110516042FDF2A,
	RandomHelper_NextFloat3_m2A1A7A5F2408BD0BE0D364C7346B74B521C82F2E,
	RandomHelper_GetHash_mA7D5AA198E0AD1DE48661A2EDE1B9434823E00F7,
	RandomHelper_Create_m54A191408DF34050ADB6054F24211E2709EC04F3,
	RewindableAllocatorFactory_CreateAllocator_mDC1E8527A976304B6E5E2DBA18C4DBB935650AC7,
	RewindableAllocatorFactory_Initialize_mD79E43076F88992056BE03CDE7AD3AB5C23E05AA,
	RewindableAllocatorFactory_Dispose_mE7E72976F3D27BA456F680633E3C6B6CFAF1B954,
	RewindableAllocatorFactory__cctor_mE7C238E2892A34E5FAD306E74DE33A233C75E152,
	RichTextParser_GetSymbols_mF811BDC1CA5D24DDD4E4968D2B619C5D03B31284,
	RichTextParser_GetSymbols_mCF57E23A10E222A500E2C53E75EB8EB46ADCF91C,
	RichTextParser_GetSymbols_m4FE46D9436A58AA65CC64074DA4FD2774C66C94E,
	RichTextParser_GetSymbols_mEDE88F65916187F0FBEA7A59A3294DD355264E7F,
	RichTextParser_GetSymbols_m3533F464A56BA61B156FC081183736B6286E93A2,
	RichTextParser_GetSymbolsU24BurstManaged_mCB0B02787E7F5738B17A69FD97FF45FF39110BEC,
	RichTextParser_GetSymbolsU24BurstManaged_mE61FC9690103F5DA82A3D7C2C6A5707C151D1C21,
	RichTextParser_GetSymbolsU24BurstManaged_m0A527CFAE7EAFAA07848BCC986B59BFACC567B7F,
	RichTextParser_GetSymbolsU24BurstManaged_mE7DB939561F00F90E19ECDC6A16E5FC8EDA80CAA,
	RichTextParser_GetSymbolsU24BurstManaged_mEB863F174B9C009A9703556847E133C2415ED6CD,
	GetSymbols_000000FFU24PostfixBurstDelegate__ctor_m35D7369B1C4771CEADB6ACB00C4C15695762B2AD,
	GetSymbols_000000FFU24PostfixBurstDelegate_Invoke_m5870226E9743CC454174201646B085EBC4BC8404,
	GetSymbols_000000FFU24PostfixBurstDelegate_BeginInvoke_m48984B629FA7B2F64AF27114B92C75C1BBBC5AA2,
	GetSymbols_000000FFU24PostfixBurstDelegate_EndInvoke_mD30CBEBB4DBF3D11442C287D9D28FB5E122193E9,
	GetSymbols_000000FFU24BurstDirectCall_GetFunctionPointerDiscard_m81E66EEFDD104C0CCF2F9DEA4473F8323E176B69,
	GetSymbols_000000FFU24BurstDirectCall_GetFunctionPointer_m1F6C61C45B83BFFAA470E1201F39B25A2BF02C9F,
	GetSymbols_000000FFU24BurstDirectCall_Constructor_m11F671F66254090E963F22EE59C6D05DB6CC3B58,
	GetSymbols_000000FFU24BurstDirectCall_Initialize_m1584FC0A852EBFEA6637076F430F8828C40CAF81,
	GetSymbols_000000FFU24BurstDirectCall__cctor_mD74EF8FF8C262E15585A3151022C710E47BCD837,
	GetSymbols_000000FFU24BurstDirectCall_Invoke_mFCB7C675827F21C3D281DA277E7EFB98A2051759,
	GetSymbols_00000100U24PostfixBurstDelegate__ctor_mA8EB54347DA52A2BA3D67472865AA32398B443A0,
	GetSymbols_00000100U24PostfixBurstDelegate_Invoke_m3C4B7AFE9E9E8C65453B932FA0E064994F78D756,
	GetSymbols_00000100U24PostfixBurstDelegate_BeginInvoke_m0DECF8E5CE9BD6A81248045DA4DC024D673DF245,
	GetSymbols_00000100U24PostfixBurstDelegate_EndInvoke_m069893E24F6C894FB62AC2205C149C34A13698E1,
	GetSymbols_00000100U24BurstDirectCall_GetFunctionPointerDiscard_mACD2A59355FB58E0783BA27A388296DBEAC5DC69,
	GetSymbols_00000100U24BurstDirectCall_GetFunctionPointer_m9698ABFEB9E864F9845A6AF0C3A8D3A8ED8FC7C1,
	GetSymbols_00000100U24BurstDirectCall_Constructor_mFBCA8F53EF9E29EFBAAF4076BB33A21DD444ABC5,
	GetSymbols_00000100U24BurstDirectCall_Initialize_m2ED4FC75FB80B60DDDE99BA83EC0EF77CEA3E10F,
	GetSymbols_00000100U24BurstDirectCall__cctor_mDBB7B2BF251D044240EA9FFE42D5727F458C8B01,
	GetSymbols_00000100U24BurstDirectCall_Invoke_m3AD225C16366327E15508EC0D090CCF496E0C062,
	GetSymbols_00000101U24PostfixBurstDelegate__ctor_mED5A4CD31BE93723E41823D71F1557F177442B82,
	GetSymbols_00000101U24PostfixBurstDelegate_Invoke_m174779ADA36D148A20AAE80451E38B7CAA7DFCC1,
	GetSymbols_00000101U24PostfixBurstDelegate_BeginInvoke_mBA5022300B52AE062646BD1F3926650FAB098C9F,
	GetSymbols_00000101U24PostfixBurstDelegate_EndInvoke_m22062317688E0D31191DC4DB1C84283CE2CF4CEE,
	GetSymbols_00000101U24BurstDirectCall_GetFunctionPointerDiscard_m61B28F524C8083FC80A12F871A862905B0A86982,
	GetSymbols_00000101U24BurstDirectCall_GetFunctionPointer_mA946F913D8C14F49AAF7EF9ED5C62DAB44D5CFE1,
	GetSymbols_00000101U24BurstDirectCall_Constructor_mC2C5C6C63B2FD6B1533E850169BA5504A2B31D53,
	GetSymbols_00000101U24BurstDirectCall_Initialize_m8E5F9C59B4BA80DCB52DFC367E8FFB5A928F0A6D,
	GetSymbols_00000101U24BurstDirectCall__cctor_mE1A8FB7CB412C08843658E1E2E3078BC1C2E687F,
	GetSymbols_00000101U24BurstDirectCall_Invoke_m170AE72CFE768FA07C14AC6B6B9125A033218E4A,
	GetSymbols_00000102U24PostfixBurstDelegate__ctor_m5EB1C8BC271ACC37534494E0C4E1388F0AE675A5,
	GetSymbols_00000102U24PostfixBurstDelegate_Invoke_mD3800FB30468B6C33B91706CF2793821C8F4C613,
	GetSymbols_00000102U24PostfixBurstDelegate_BeginInvoke_m1BF80F27D6A2E40728379DEFD1F74E8ACC9B7EFA,
	GetSymbols_00000102U24PostfixBurstDelegate_EndInvoke_mBDDA0F1C24D6989F2925E28AB4EBDE3D3C222DE0,
	GetSymbols_00000102U24BurstDirectCall_GetFunctionPointerDiscard_mF5A12E3FEFBB855DDA31AF34593C2E529BF9E39A,
	GetSymbols_00000102U24BurstDirectCall_GetFunctionPointer_m36F5FB80BF437D598C379FF58741347171DEB656,
	GetSymbols_00000102U24BurstDirectCall_Constructor_m0FE05B97F17E84AC5E44217CA8C35766EDECDE42,
	GetSymbols_00000102U24BurstDirectCall_Initialize_m64F3F419D1DD270C0B8DD290D08690B5C2FD9694,
	GetSymbols_00000102U24BurstDirectCall__cctor_m32B09B5A980322FF13F99D2B2408C3CDD05E8BA7,
	GetSymbols_00000102U24BurstDirectCall_Invoke_m0D709CF5777A1908B0F5B585B0DF03FCDE7F9C05,
	GetSymbols_00000103U24PostfixBurstDelegate__ctor_mF9C892DB1635DE1251A5407429C59A89AE46D1AA,
	GetSymbols_00000103U24PostfixBurstDelegate_Invoke_m50C0F2B4A6E2129AF545EE4DB3B7CAC3E5C3F948,
	GetSymbols_00000103U24PostfixBurstDelegate_BeginInvoke_m977DD135D92279BD0BB8EB5CBE2A8C549FB9D9D3,
	GetSymbols_00000103U24PostfixBurstDelegate_EndInvoke_m9858543A920459969DE0C7F4D4F7291C3CD5816C,
	GetSymbols_00000103U24BurstDirectCall_GetFunctionPointerDiscard_m52BCAE4326B7251EC2706C2BF1533B2ABE34C120,
	GetSymbols_00000103U24BurstDirectCall_GetFunctionPointer_mBA715D0D9DDBB94A9024FF9B2506EEE55ED2C378,
	GetSymbols_00000103U24BurstDirectCall_Constructor_mEA7987C4FED3B450636315B983ABCBB89C995374,
	GetSymbols_00000103U24BurstDirectCall_Initialize_m237CF8923A1A190F1EBD84C7A8176CEA2AF6B882,
	GetSymbols_00000103U24BurstDirectCall__cctor_m680E754F1455A271CC3CF4C6EEDEA17C4DCF008B,
	GetSymbols_00000103U24BurstDirectCall_Invoke_mFEB202B8ECE58396A2AE273ED633AE865347FD84,
	RichTextSymbol32Bytes__ctor_m65F97C28A14578AC22B6A40B08A34A1C30D3976F,
	RichTextSymbol64Bytes__ctor_m11C4E0E45BB649E0CA9F39316211600F61633141,
	RichTextSymbol128Bytes__ctor_mE728D7EE402D4EBF8445D4439494F167EA3BD6DE,
	RichTextSymbol512Bytes__ctor_mCC11833B715889839D15FEA95AEC3775A3F93DC0,
	RichTextSymbol4096Bytes__ctor_mC0EDF5BBF1438668B5C2528CCA56122EB039D7A2,
	SparseIndex_get_Index_m7BD01B8761FC3B8060E009BBB229CEE4061D6712,
	SparseIndex_get_Version_m0485D8AA73020E06308E9D86963AB6F8ADE78D0D,
	SparseIndex__ctor_mE2B01B52B0AAF9C71B3A3348D72571D1F459EBF6,
	SparseIndex_Equals_m796B8C7DD085DDE7E0D5A96E6AFF1FC6428C5774,
	SparseIndex_Equals_m6BD153A61F484812F71DCE2BED5DA7A5A23D3960,
	SparseIndex_GetHashCode_m219B7BA026D67923B2C8AA5F5B065535559D1C20,
	SparseSetCore_get_Capacity_m19216C0D96DEB93B27BFEECFCB25359937022F3B,
	SparseSetCore__ctor_mB5E552E9C3313F0B9E3665B22A58FD367F254AEF,
	SparseSetCore_EnsureCapacity_mA7A43DB29C33527D4F0CA5AD784D3EB7882D9263,
	SparseSetCore_Alloc_m999734CBD13AF233BB03B873D596C80C3AB0E250,
	SparseSetCore_Free_m9A287946704E380B12E7752060A66DADFA5361D0,
	SparseSetCore_GetSlotRefUnchecked_mE1AE7761033A09AD4A57D0454325803FC7F88CFD,
	SparseSetCore_Reset_m7B88F0AE3BA9306CCA105631FBE24194A8DA6E19,
	Slot_Equals_m0BA52037992DC515671A8820F41CBF40A02A7AAF,
	Slot_Equals_m771BA52BD099581D2888B34D1D0E21EF771364C1,
	Slot_GetHashCode_m65E4135D452912AF96D1F2671C9D7C00AD331B1B,
	MotionTaskSourceBase__ctor_mDE26FF8D3E95E91306C0C0016BCB04B4D63A50DA,
	NULL,
	NULL,
	MotionTaskSourceBase_OnCancelCallbackDelegate_mE79D5648898486724111143DD3B70CE0A63345E7,
	MotionTaskSourceBase_OnCompleteCallbackDelegate_m9D6F37645AB1AA5ADECA2FFFD4BE11C3554B753F,
	MotionTaskSourceBase_OnCanceledTokenReceived_mB5D401090779BF95E3641448D549BD7C942CBF49,
	MotionTaskSourceBase_Initialize_m01AB7793837D080FD456897CBFE918C7EE878510,
	MotionTaskSourceBase_ResetFields_m8A6D35B8FCD92741CEC0A00AB1F09FD903ACB920,
	MotionTaskSourceBase_RestoreOriginalCallback_m68136C9AEE435B9E6969CC75347D0057BD45C5CF,
	MotionTaskSourceBase_DisposeRegistration_m176979D137681D051A6CFCD4753CD2BE57C421A6,
	MotionTaskSourceBase_RegisterWithoutCaptureExecutionContext_m90EBDDE21DB05AD0B291EE2759422EC51751B5AD,
	U3CU3Ec__cctor_mAB541A66D5F9B999819297CEBC15F3FACE88F8AD,
	U3CU3Ec__ctor_mF3738D2A3CDA7EFE80DC93D2E52F02139C8E8B4B,
	U3CU3Ec_U3CInitializeU3Eb__15_0_mABBB9414B8074B78B5BA1189D8066DEF79E11611,
	ValueTaskMotionTaskSource_get_NextNode_mADC41651D9A7E00C28870C09754884E38F0D0693,
	ValueTaskMotionTaskSource_FromCanceled_m5E4E640B7B81B53A9E31E04BEA10E4F8CB2E29D5,
	ValueTaskMotionTaskSource__ctor_m5118C6DCA8548CA61F251FD94036686C96876DD1,
	ValueTaskMotionTaskSource_Create_m41A7960567AFE08EF0A3E42CAE4215EF311C3F26,
	ValueTaskMotionTaskSource_get_Version_mA827658763305BC0E4AC5B5416830BA9436F1C79,
	ValueTaskMotionTaskSource_GetResult_m8C5F4793F01F1C7207761CD1896939A2E1BF80B4,
	ValueTaskMotionTaskSource_GetStatus_mDB1A7F7017B4598431C86B9F2EEE4D036860F4D6,
	ValueTaskMotionTaskSource_OnCompleted_m95A60E7B4AFD1C9C5868A4735AE01043AAE983F9,
	ValueTaskMotionTaskSource_TryReturn_mD076EF733024FAEBA325BF87C231483AF4B41EE2,
	ValueTaskMotionTaskSource_SetTaskCanceled_m54DAF29A8E6605F5A2A0DEF8289B3BEEDF1CC5AC,
	ValueTaskMotionTaskSource_SetTaskCompleted_m732D8B92D776DD35AC6F2DB2C26E9FDF38688999,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	Utf16StringHelper_WriteInt32_m5503DF7D43D0975805353F589E8CE2DCE4C9DF32,
	Utf16StringHelper_WriteInt64_m4B80B2DF130D854A0E6029FD00F816CD4E67A728,
	VibrationHelper_EvaluateStrength_m3403B4C78E150127098C54564958BAF73D16D992,
	VibrationHelper_EvaluateStrength_mA5678D93AF9F64ED6E8051FC0A5A58B6AAB3F8E1,
	VibrationHelper_EvaluateStrength_m4320BE7D6BBF388BD0B42AE304B5C0F6A97BBE8B,
	VibrationHelper_EvaluateStrengthU24BurstManaged_mC2660554696E318D3C458E591DA2B15E1E1B2905,
	VibrationHelper_EvaluateStrengthU24BurstManaged_m6CB7181449A6AF2BBF24554953CD0621524CFA8D,
	VibrationHelper_EvaluateStrengthU24BurstManaged_m31ADF1B6C8639D9235BD21CF0FF3B9C4D3A5F488,
	EvaluateStrength_0000013CU24PostfixBurstDelegate__ctor_mFC689B94DD838B5D0C926DA1ED6D779B148B02E7,
	EvaluateStrength_0000013CU24PostfixBurstDelegate_Invoke_m61FAF065CA8EA778116C0FBB999F39A7FF53AD29,
	EvaluateStrength_0000013CU24PostfixBurstDelegate_BeginInvoke_mD54B92DA9260ACC44ECD7412B3FA55CABC059D70,
	EvaluateStrength_0000013CU24PostfixBurstDelegate_EndInvoke_m5A76D3052CD1F5E15EEB4A640BA08D73AF72ECDB,
	EvaluateStrength_0000013CU24BurstDirectCall_GetFunctionPointerDiscard_mC257A47E9EF0E58451A5B63255486A7843623E62,
	EvaluateStrength_0000013CU24BurstDirectCall_GetFunctionPointer_m4342FEB3727AF5F09786E844E38479A795C91F73,
	EvaluateStrength_0000013CU24BurstDirectCall_Constructor_m3DB9D988397658A201E8B3C5AA366325BAFB03C8,
	EvaluateStrength_0000013CU24BurstDirectCall_Initialize_m20B96811D1602889A500937829D56485E9E6E724,
	EvaluateStrength_0000013CU24BurstDirectCall__cctor_m9672A5A9F0424B145B37656955349649CF0866A6,
	EvaluateStrength_0000013CU24BurstDirectCall_Invoke_mC1EF7CD01A1ED1AE41F14187A44B17F3FE4AAB01,
	EvaluateStrength_0000013DU24PostfixBurstDelegate__ctor_mA6FB2A0DD25A9BF1227492A0042F872AF6F01EFC,
	EvaluateStrength_0000013DU24PostfixBurstDelegate_Invoke_m2565EF59316CC0B54FCC75797FB416963D534F3D,
	EvaluateStrength_0000013DU24PostfixBurstDelegate_BeginInvoke_m0CE994405A29AB6D609170928CF1BC952BC16679,
	EvaluateStrength_0000013DU24PostfixBurstDelegate_EndInvoke_mAC675A5C376FA859C7F900CA454610CF9D574DF6,
	EvaluateStrength_0000013DU24BurstDirectCall_GetFunctionPointerDiscard_m41815647534E7BA134366249AD59C73FB90D2FCF,
	EvaluateStrength_0000013DU24BurstDirectCall_GetFunctionPointer_m9DA9FB2566E0C9436313A3AC4CA89AD21ABB0310,
	EvaluateStrength_0000013DU24BurstDirectCall_Constructor_m63F935CC23BA99A31B6F0D2D1431C66DDC9630C0,
	EvaluateStrength_0000013DU24BurstDirectCall_Initialize_m42B5EF430CBAB349D430D9BB62F70E5D22D9BEB4,
	EvaluateStrength_0000013DU24BurstDirectCall__cctor_m59F3BFBE6CF5A246B6BF14449CD43DF2C4CCA748,
	EvaluateStrength_0000013DU24BurstDirectCall_Invoke_mF70ED19B9D1E67BF66C6D6046551292B6051AB43,
	EvaluateStrength_0000013EU24PostfixBurstDelegate__ctor_m743D199AAEE73373A802197F9601047EB74588FC,
	EvaluateStrength_0000013EU24PostfixBurstDelegate_Invoke_mA6581987E30BAAEFB5A34BE3736111031FAA7AA1,
	EvaluateStrength_0000013EU24PostfixBurstDelegate_BeginInvoke_mC6F2A3F0A03982CB6ADAFB43B7955B143EB2B868,
	EvaluateStrength_0000013EU24PostfixBurstDelegate_EndInvoke_mA34BAA5307E73725C0A0C9A589AB670ADD068564,
	EvaluateStrength_0000013EU24BurstDirectCall_GetFunctionPointerDiscard_m0A35D4BDF9941EF62259AA2174C274C248A402EC,
	EvaluateStrength_0000013EU24BurstDirectCall_GetFunctionPointer_m9EC99BB41A96C45883DA1FE0E70BB8543D402AC1,
	EvaluateStrength_0000013EU24BurstDirectCall_Constructor_mB0330B1C41A2E300165C0F0311B5AFEFD501405A,
	EvaluateStrength_0000013EU24BurstDirectCall_Initialize_mF52F84C3B486548D79FD7108F9E585AEE9F1CF09,
	EvaluateStrength_0000013EU24BurstDirectCall__cctor_m478DD233E429A10ECD9854BC19985F200ECC9732,
	EvaluateStrength_0000013EU24BurstDirectCall_Invoke_m5A17142F554F7A4F327CA494968C10F65A57CB7C,
	LMotion_Create_m884E0C4AC225B788D89F2B25D83F7EB2E7FE1E98,
	LMotion_Create_m517FBCC3BD5ECC979383FCC7943110D14E5EE36E,
	LMotion_Create_m180EB5F367C7EDEE8C452DCEAEB78431D030D1E1,
	LMotion_Create_m849FDE0B3D5DEE6774FAF0025236E4DD45B88337,
	LMotion_Create_m31516B0BF62B9BAD6A0978421594D0B4D8F18EF5,
	LMotion_Create_m83753C8E7B5BF9ABF78650B5F836D07021CD5946,
	LMotion_Create_mC5D9269621FB71AEB3137C1DCB92096711B2A23C,
	LMotion_Create_mBCA5F2907A857315D71EF7E092A750D8B9F6BE88,
	LMotion_Create_m8065923572DCD9524BAE89BC98359A60B7F5CB35,
	LMotion_Create_m1F281B66F4663FB3FCF2E4858EBA483A3F32C988,
	NULL,
	LMotion_Create_m1230D7B6B33815B60855574A7C701E6417B70E21,
	LMotion_Create_m4DF0C3700CF0BE5882812680494FA01DBA2A4C8F,
	LMotion_Create_m92953C81956017A2AB06007C7B0EA592823E6105,
	LMotion_Create_mC414812AA38059BD27BC5ECE7EB4215EE03CA515,
	LMotion_Create_m8790BE937C5ECB6FD36B940BBC9423C764518C0C,
	LMotion_Create_m22181B98BC002E019AF3390195EEEEEB19E8885A,
	LMotion_Create_m8C8259D6597DBA9870425866C398B238084E07D4,
	LMotion_Create_m506B55888E00518E7CD61E94F83217966FDDD490,
	LMotion_Create_m387B82FF21BD62CE51B27F45FBD4A595397EE738,
	LMotion_Create_mE3F389D3E8DE131E279F1EF8FB640348EE8C07E7,
	NULL,
	String_Create32Bytes_m349D3A5E8A7ED0F40311BC35968AA437AFA6D195,
	String_Create64Bytes_m317E44A5DD660D18D7FD9790E5A26C329D3DFD07,
	String_Create128Bytes_m9CEEB6499428C9AC05E3FE4CAA0D56D3C9D53569,
	String_Create512Bytes_m2DE88330438B0F2AAF20404D180D01BF0A7442A5,
	String_Create4096Bytes_m110098DF117C3B892712AF008D1013C41C982433,
	Punch_Create_m1C0F2D96AF2DC5C9256CC9E73E1502DDCE7DC577,
	Punch_Create_mA52D22B300A7CE1B5829ECA40FCE9018B1B43A8D,
	Punch_Create_m93A806B10294CBFA987FA6DE6864B6605E7087B8,
	Shake_Create_m89CACFDC3328848EC44C772FFC660F52836D21CA,
	Shake_Create_m031D45A64CCA566D8B68579359CA77B939FE3FA4,
	Shake_Create_m85CD99D2246C46A6BC47E12583BFAA47CA511E2E,
	LSequence_Create_m4AB8986238844E5CCD72599F17C69FC3D67E6B53,
	ManualMotionDispatcherScheduler__ctor_m5A0F8586CE31B77612E7809E688F7384E2172A80,
	NULL,
	ManualMotionDispatcher_get_Scheduler_mDA6267E1838BD6EAB158348BF5BF46B2EDB3E180,
	ManualMotionDispatcher__ctor_mED177318B9F656B56D5180F20BFF0952FD7F3DF8,
	ManualMotionDispatcher_get_Time_mC8705337A0D3B7C8FF5880645EFE958BF6308E5E,
	ManualMotionDispatcher_set_Time_mF1D618EF9AFFA3733FEF55B766279DD08DEF636C,
	NULL,
	ManualMotionDispatcher_Update_mBD27C444FDA89E556E99B11EABDD8A3E62D60635,
	ManualMotionDispatcher_Reset_mEDA9419EC6809C8F16BF786533B201F7251D4524,
	NULL,
	ManualMotionDispatcher__cctor_m53F10854712984DD054B08BF01A09AF0CC720F60,
	MotionAwaiter_get_IsCompleted_m286644973703C945DFDEBF59ECBFEB9813E55A6C,
	MotionAwaiter__ctor_m39C511FEAC16D5589DB9A0DB04C1BAEF6509CE3B,
	MotionAwaiter_GetAwaiter_m5E1F76DB7C9DD5B3334F3E8C71DF46038D1BCCA1,
	MotionAwaiter_GetResult_m582D3877D66D6496EA719A34312D4B263C4F2D2E,
	MotionAwaiter_OnCompleted_mE62AD33CA015865A8F3819534883404785ADA914,
	MotionAwaiter_UnsafeOnCompleted_mF5C13B5A490B2A7E1C332891A7F694960288F74B,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	MotionDebugger_get_Items_m9F9B3F15E3B731FA7B81D7591B94D5DFAC8DF7E7,
	MotionDebugger_AddTracking_m5DAB6174F7F49BCC3F5C64BC81A53CCF88C5B475,
	MotionDebugger_Clear_m0996C960732DE00EEB696BDC477F74C226F1A46F,
	MotionDebugger__cctor_mC08FEDC0C2C9B9D0DA4C1DB742A107C6C40604BB,
	TrackingState__ctor_m4711431852DC019F73EA6CF301CD51BB8E060603,
	TrackingState_Create_mFF465389166D3F31A9D864F930F24B4A05610772,
	TrackingState_OnComplete_m495084703674BC85A99E3CF9D217FB2175E63DFA,
	TrackingState_OnCancel_m29CC264821EBF18044AB869EA101E2CC3E04B06E,
	TrackingState_Release_mD041EE277ADB7A1968C8CAAC763827895FF570B8,
	TrackingState__cctor_mAD072A768969C5AFE7CCF7E46BE6DAAB65FB6A6A,
	MotionDispatcher_GetRunnerList_m40E9CED6758AF301598CD53D997D5E1894AAA14C,
	MotionDispatcher_Init_mA4EEF2C32CA4FE2D9FB604CA9CE1FA68D97D8E0E,
	MotionDispatcher_RegisterUnhandledExceptionHandler_m15D93B3F8B72A537BF94F01C316AD26B30B3C943,
	MotionDispatcher_GetUnhandledExceptionHandler_m6040B471623FBA1B3355419491D59F1D35A78988,
	MotionDispatcher_DefaultUnhandledExceptionHandler_m81C50F0472143FD82E68CC325B0F1D0FFCA6FCE5,
	MotionDispatcher_Clear_mD5E17CBE98861101D5A82B700EE5C0FAEBD9F0C6,
	NULL,
	NULL,
	MotionDispatcher_Update_mAFFFA9313E42DA91CBA5C1D0E38E29819BBE76EB,
	MotionDispatcher__cctor_mCB19B9D9CE57CCF91562DF5D6B6D67BA1A98D2F7,
	NULL,
	NULL,
	NULL,
	NULL,
	MotionHandle_get_Time_mA0FDC83A5CA764224292035D1B92DD35A0737C69,
	MotionHandle_set_Time_mD678AA94DB0E174FC2EB977EBE10986DC62DEA9B,
	MotionHandle_get_Delay_m79CC0D8F78C3B8E93C5DBBC5932A1C4FA22B453E,
	MotionHandle_get_Duration_mFEF79D284F2ABF0AE9170303CCE7F0FC61443E06,
	MotionHandle_get_TotalDuration_m03E9F23F0E3E2FA0D53FA6F796579DB2833E15DF,
	MotionHandle_get_Loops_mD5ADE9962E0D6770C65D0B25BB8240340AC323FF,
	MotionHandle_get_CompletedLoops_m8CF0BFB384A65D617D69D195DE7637FC03673E40,
	MotionHandle_get_PlaybackSpeed_m810CA94487003021B21169CE4C00AB0D2B57B8C0,
	MotionHandle_set_PlaybackSpeed_m2CE0693B92A83C716EA4BF498A5E9C57AC71833E,
	MotionHandle_ToString_mE2F8F39BF927477327C69AFEC8BB1A6D9FA0625E,
	MotionHandle_Equals_m717AA4FEC4A00726F8C0DBB28ED51B2FF53A539F,
	MotionHandle_Equals_mA77A264C0AE2A05C893C0BBF7D2C1CA0315AFE2B,
	MotionHandle_GetHashCode_m201415482C5E4AE8A0E14879A905ACD71E0615B7,
	MotionHandle_op_Equality_m6DB3B9B5E94F7A6B823D32C1915C23D56BD4596F,
	MotionHandle_op_Inequality_mA10995A1D792EBC24B65ACF1E04DB5FE692CD796,
	MotionHandle__cctor_m6EC4D9946D3E3FB66D8BBC54CF0754F8AFB840A3,
	MotionHandleExtensions_IsActive_m33A1DAE84888047637F589CFF54E458F2CC0EED2,
	MotionHandleExtensions_IsPlaying_m6CB941850BF2A95AB88CA4B5BD4FD3FB78F43DCF,
	MotionHandleExtensions_GetDebugName_m1774A9241B214D957DC092C0EE872E2C68B7521E,
	MotionHandleExtensions_Preserve_m6B6F0C6EB75391E011DF1CBA1BA86DE2EC6AFA0D,
	MotionHandleExtensions_Complete_mF5D7A9986BE92CE0BD7D9D75B2B44F1A03EF3EE1,
	MotionHandleExtensions_TryComplete_m6E5C4F018592F928CBA2FD1626DBE6D25A2472E1,
	MotionHandleExtensions_Cancel_m5579E51C6A834B5ABB60E24ABA3335FB5B20FB1A,
	MotionHandleExtensions_TryCancel_m5407A0C8EEB83CEEA742485A104889A7E14CD090,
	MotionHandleExtensions_AddTo_m4DB59A863D05996F25A16D2B7606B1D321445AAA,
	MotionHandleExtensions_AddTo_m7EACE88B8C59AED0CACF8ABCA5399A18DBD0C467,
	MotionHandleExtensions_AddTo_m199E55A3AA807B114B2D483D30BF5DE103F63E25,
	MotionHandleExtensions_AddTo_m157E2523FE1C79CB51B4B4508494B3C86F20D8CF,
	MotionHandleExtensions_AddTo_m283DDF5E9C70CE6F23E421669E4E23A42D4B210F,
	MotionHandleExtensions_AddTo_m9E34DE43A46F37B34421CA362ABD52266C0FC802,
	NULL,
	MotionHandleExtensions_ToDisposable_mC877F7C7380A66F9D1D7919DFF3F2C225715252E,
	MotionHandleExtensions_ToYieldInstruction_m684470126F34C5FD8CAB5F129E039E7D7320C5C8,
	MotionHandleExtensions_GetAwaiter_mBFC32CC47DB96BA1F95E9C1DD2E334762ABE5BC0,
	MotionHandleExtensions_ToValueTask_m718D96C505389D216CE5D037E8F4F9EB4ABC7A00,
	MotionHandleExtensions_ToValueTask_m8186DA2E302CD1B90B63CD345D2760E362D1643C,
	MotionHandleExtensions_ToValueTask_m8BB0FFC8AB860477212B4C17962007AB4855E8E8,
	U3CU3Ec__DisplayClass13_0__ctor_m113E1EAB9CE5E547BAFCFC0A3B7151977922D581,
	U3CU3Ec__DisplayClass13_0_U3CAddToU3Eb__0_m5A3ADD556B45F3559D237B06F6387FAF3272A62C,
	U3CToYieldInstructionU3Ed__16__ctor_mC4BB59F45B71FD724B861CB31FFA4B95F52355E1,
	U3CToYieldInstructionU3Ed__16_System_IDisposable_Dispose_m22ED70E79C1C7F65AB7FD83C9DC623038A45EC66,
	U3CToYieldInstructionU3Ed__16_MoveNext_m1172E8BA5D948873E9FADC7874A9A6DA213C12DF,
	U3CToYieldInstructionU3Ed__16_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m9B802B483017A470D0F42BDDB4AB42942985C94C,
	U3CToYieldInstructionU3Ed__16_System_Collections_IEnumerator_Reset_m0EF976428B8F6C12E6091A8AE601F8B3C0695441,
	U3CToYieldInstructionU3Ed__16_System_Collections_IEnumerator_get_Current_m1FE9BC6F118592849BD83DF81158FDE83C3FFA65,
	MotionHandleDisposable__ctor_mF6F3BBA22676AA3A3B1080BD6367CE793CF03510,
	MotionHandleDisposable_Dispose_mED3A3802E6070C9E18AD2B6247B83EE2A50427EF,
	MotionScheduler__cctor_m182608815E4A08BF5065FA06FB11A3DBD18BBD8E,
	MotionScheduler_get_DefaultScheduler_m1815603ECA3208DFD19FE6160C8FF824784E9FAB,
	MotionScheduler_set_DefaultScheduler_mD9E66D27D9EE86E6B057015FF2379F5F09EB2CDC,
	MotionScheduler_get_Manual_mC3FBB830D70F29E21449C438E28356C6BE0767E0,
	MotionSequenceBuilderSource_Rent_mFD9F0CD221C1F19E1B5AA78E71B96A0F998998B1,
	MotionSequenceBuilderSource_Return_mB7C4A940320B11A206DD16CE5A8BFE57EF029477,
	MotionSequenceBuilderSource_get_NextNode_m5307BB8F5153C038D14BECF363E30C6C304FED88,
	MotionSequenceBuilderSource_get_Version_m30FFE7A03660C1A54B825928AB06334AAA776F09,
	MotionSequenceBuilderSource_Append_m26ED24A0AE99FDC09E3EAB380B000BC22067DD14,
	MotionSequenceBuilderSource_AppendInterval_mCEEA7E58ACCD60864A3C443FA5DC3D4945177984,
	MotionSequenceBuilderSource_Insert_m4CA78FFDBD5371F9AEC866B65E26D8F8EFC34B0D,
	MotionSequenceBuilderSource_Join_m6936393518F9AFCBCDC9F49F5BA293A92E16AA2C,
	MotionSequenceBuilderSource_Schedule_mA0B2FCFA3B935D201CE4255FA2EECAB77FC52F5A,
	MotionSequenceBuilderSource_AddItem_m5A687E01F68FDE7D69F5AA20E8E0A7E10763254C,
	MotionSequenceBuilderSource__ctor_m51440B0EBD5B944F41E11E45F33CBF3DAD1F01C3,
	U3CU3Ec__cctor_mFB5341778716A16E6B509BBF24B177D1F66059A6,
	U3CU3Ec__ctor_mE5DF0B4E09139D2C68EA184881D670AADC4431CF,
	U3CU3Ec_U3CScheduleU3Eb__18_0_mCD4B60654C842A3C70E6AEB6A99E9A60683D7EDF,
	MotionSequenceBuilder__ctor_m3B3813AFEE55253DDC6A81DFAF99DDFA94F5B0C6,
	MotionSequenceBuilder_Append_m1B1458BF99FA0A758F5202BCAFCF1FDB6397407F,
	MotionSequenceBuilder_AppendInterval_m31D45D0D37C90D214776825F47C2B0C7F209757C,
	MotionSequenceBuilder_Insert_m81CF3B3C1F3DA5B975028BDCA01547DE35A12604,
	MotionSequenceBuilder_Join_m342181FC806C2381E1A27CFAB1F1903115D5A1C5,
	MotionSequenceBuilder_Run_m8470B91A3EE5610052CCAA58145AFFD2015833B7,
	MotionSequenceBuilder_Run_m72BD27AF38A04BA10134D3D64B1A4942F8235A3E,
	MotionSequenceBuilder_Dispose_m3AC2152DD6D49C7AF09FF581B7AEBF47D5D901BE,
	MotionSequenceBuilder_CheckIsDisposed_mA0B1F56B0D2534199606ED88E68FC884BD67A4BF,
	MotionSequenceItem__ctor_mB684C8F3E987F664437269051E91FA23626C5A14,
	MotionSequenceItem_CompareTo_m65A6CD68890C8BC64BE0149E42F11941291DE7DA,
	MotionSequenceSource_Rent_m553276BE459CFECC2A7C198229933276603A1A61,
	MotionSequenceSource_Return_m9829EA197BC76C394F84E5EAE56E761E1A8D8D7E,
	MotionSequenceSource_Initialize_mB357DE96A390F6B804EC10990231A5E5ED152A09,
	MotionSequenceSource__ctor_m1F22BE9600D570C30CE48A3414A793A6511CE639,
	MotionSequenceSource_get_NextNode_mAA88FA5B53FB345E22A516E714ED2678CC4B1EFF,
	MotionSequenceSource_get_OnCompleteDelegate_mFF954EEC5BCF6E694C9804559123E98CB7CAE73F,
	MotionSequenceSource_get_OnCancelDelegate_m26A69BE7F229D7040148F00756045A9E2A76136B,
	MotionSequenceSource_get_Items_m4C36054CF49A92D16FE583F603372FB3E11C502F,
	MotionSequenceSource_get_Time_m423B68AE3BD7DF25D8D392ECA45F436EA0045C53,
	MotionSequenceSource_set_Time_m1EA183FF9B1A75D22F440E13FD291A4ADC5B8D95,
	MotionSequenceSource_get_Duration_m69B061B0C7A061103304BDD8799B6F2FA300225E,
	MotionSequenceSource_OnComplete_mF3FAF075694366743FF8B1BCA78659443383D452,
	MotionSequenceSource_OnCancel_m99D5C894389B3BFE0F407EC0FAD4CB92D24400E2,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	IntegerOptions_Equals_mB8DE550CAC68A2F0029A1B9874E791296CDFA19A,
	IntegerOptions_Equals_mE2E60F034BA74E42A4A3B9E083E07689FE57B409,
	IntegerOptions_GetHashCode_mBB87BD24FB83C50E33BB9E977E81114CD966F98A,
	NoOptions_Equals_m385B96E4BB896392A8219EC298ABC8845ED1C92F,
	NoOptions_Equals_m8A4E39BEFD271327062C569AF079453F9F3CF612,
	NoOptions_GetHashCode_m28C2C72A5509EAB1ACAA3B12A2F7DF26903E33F3,
	PunchOptions_get_Default_mFE71526DEAE4CADD8B402E23BAFE9B15446BF644,
	PunchOptions_Equals_m11D7621E6C9FE1E0D50B120F403B484164004A8B,
	PunchOptions_Equals_mD36EFF3523E00BF3996DCD90E529037F29ED0043,
	PunchOptions_GetHashCode_m060EE8928378E41058941BFC6D58324B45E615C7,
	ShakeOptions_get_Default_m46F7A2C9095360F0811CF0CF284DE7EB435002E1,
	ShakeOptions_Equals_mD48E7AC97860E50E55C02DF0BD5952C9E4736C99,
	ShakeOptions_Equals_m18AA941F8A34D3CBB611936E96C80BD1B03BD8B1,
	ShakeOptions_GetHashCode_m836C3BA106E9A4C82EC6663C135D7F5679352161,
	StringOptions_Equals_m94F9D676B065FF992DF8B874BB4DECC43D087223,
	StringOptions_Equals_m0B6CB436B80F81E1F52B76B9A7E970BE1EFAE19C,
	StringOptions_GetHashCode_m2C4406DD93543B5109AFB9867E94B7098A335871,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NativeAnimationCurve__ctor_m3AD50473C7E68E08F43BDA4D59FF9DCA8EC4ADA4,
	NativeAnimationCurve__ctor_m79972267B1924C44D34A2CAA6BCC66DA165E18C2,
	NativeAnimationCurve_CopyFrom_m1AAC67EAF4342DB533B3AD7589CE1ED98222D903,
	NativeAnimationCurve_CopyFrom_m7BA8522B4BF457E0D81780864E70DFD81004CE24,
	NativeAnimationCurve_Dispose_m1C0E5159BB7F29FAD12B387C593A5C3A4E088E3C,
	NativeAnimationCurve_get_IsCreated_mA83C5407F24D940BE28F8628B6A4132577415688,
	NativeAnimationCurve_Evaluate_m7ED7143984E71AF465407ABAA94BE754CBF8A8A4,
	KeyframeComparer_Compare_m08A8F446BB3CEF5FBC791F447A11D880D5268AF9,
	NativeAnimationCurveHelper_Evaluate_mDA20E15BF8B10E2B1E8022B3365FC496D30B0A03,
	NativeAnimationCurveHelper_GetInsertionIndexForSortedArray_m35EA56701C3B8A8027A8F537D149CA7FED1F1BD9,
	NativeAnimationCurveHelper_WrapTime_mB7735E2D4FD377EE079CC0C3BD22567D4F7ACEA5,
	NativeAnimationCurveHelper_Evaluate_m2A41626246695AD866A3C02BE7AE9254528A5F4D,
	NativeAnimationCurveHelper_CubicBezier_m1BA414098EC0B22AAA4A2AD63087FEBF9F4B2C98,
	NativeAnimationCurveHelper_CubicBezier_m509E0FA73ABAFBD67964CAA99A50B3DCF5077590,
	NativeAnimationCurveHelper_DeCasteljauBezier_m57AD9BDE0A4CF3E2AC5DB2FE083A299B82362E11,
	NativeAnimationCurveHelper_GetTWithBisectionMethod_m56CCFE0B0D3BE3688391771C237207FCB6A78EAB,
	NativeAnimationCurveHelper_GetTWithNewtonMethod_mC15A48755C1EA8E2293556D369786A2ECEB646C5,
	NativeAnimationCurveHelper_UseNewtonMethod_m42A79906266AC12BE4D3B40367A68198771282B0,
	NativeAnimationCurveHelper_UpdateTLimits_m56F166F058062B3011C386479C3811F9EE81D9B3,
	NativeAnimationCurveHelper__cctor_m0C5868589D947F9A9A938529677AD35AA898680C,
	NativeAnimationCurveHelper_EvaluateU24BurstManaged_mD2F7A19582DB62B776C1F54EC2EC5BD37F28744A,
	Evaluate_00000270U24PostfixBurstDelegate__ctor_m3195CB6E88759859A843E532C3C2EC47BF7E1383,
	Evaluate_00000270U24PostfixBurstDelegate_Invoke_mCA65ABDAADC587B1CFB539EE1DF35563275661F9,
	Evaluate_00000270U24PostfixBurstDelegate_BeginInvoke_m0C2CAF12626C1963FE66D2F60726773D50FDC254,
	Evaluate_00000270U24PostfixBurstDelegate_EndInvoke_mD3BBF78138A5B3740C3CA5BBF26678E11CDEBE50,
	Evaluate_00000270U24BurstDirectCall_GetFunctionPointerDiscard_m331C80116DC537869642D4D0AA3745377EDD8EB4,
	Evaluate_00000270U24BurstDirectCall_GetFunctionPointer_m9555A7F8B025BEFDFBD68A762BC4F8341E7F2F2A,
	Evaluate_00000270U24BurstDirectCall_Constructor_m864A87F46921571A08D6397825C6295A9202CD71,
	Evaluate_00000270U24BurstDirectCall_Initialize_mB5E123DDD95A3482B3B7A6EF37A3D91DE48B3BE0,
	Evaluate_00000270U24BurstDirectCall__cctor_m18AE0388A082839AB4392B241813BC5EA9E2E93A,
	Evaluate_00000270U24BurstDirectCall_Invoke_m564E1DDB2BA6412A80E9AD1D2A7922C4C9AC5164,
	UnsafeAnimationCurve__ctor_mD81A51444ED8815EB361446BD52D8B7C3057FB29,
	UnsafeAnimationCurve__ctor_m5761C59D0B100C1DA7DCA474DBA7A6DE4C99FE4B,
	UnsafeAnimationCurve_CopyFrom_m6554425C2394D531B1DA0EDD75C604295F9D32A2,
	UnsafeAnimationCurve_CopyFrom_m25A0FC5288CB8D79418DC4A9D3D1C6851144437D,
	UnsafeAnimationCurve_Dispose_mE82134542407CBEB297210496545BD033B35FF8A,
	UnsafeAnimationCurve_get_IsCreated_m58C6CEF87EA2812D477E04F3BCBC0EA474CBB33E,
	UnsafeAnimationCurve_Evaluate_mBBFB7BCA744F345B181678223D5B3A7AF5FF1848,
	FixedString32BytesMotionAdapter_Evaluate_m0C65C871422A2CDA8442392C70C50A27A3596C25,
	FixedString32BytesMotionAdapter_LitMotion_IMotionAdapterU3CUnity_Collections_FixedString32BytesU2CLitMotion_StringOptionsU3E_Evaluate_m61FA07A7B8CF5A5BE9FCE9705FC3838B4292A5D1,
	FixedString64BytesMotionAdapter_Evaluate_mE8E866842E525C6138219634E1E63721F5D8E1BF,
	FixedString64BytesMotionAdapter_LitMotion_IMotionAdapterU3CUnity_Collections_FixedString64BytesU2CLitMotion_StringOptionsU3E_Evaluate_mE85D4981C07EA5E5CDCBF97B40C61373A08DF5BB,
	FixedString128BytesMotionAdapter_Evaluate_m68E4B7360276AEA24A98254DC080748626C8449F,
	FixedString128BytesMotionAdapter_LitMotion_IMotionAdapterU3CUnity_Collections_FixedString128BytesU2CLitMotion_StringOptionsU3E_Evaluate_mD07ECCA1245B5B63DD5721B04374A9200F64F2EE,
	FixedString512BytesMotionAdapter_Evaluate_m85725AEDFBE845AB737702D09A8A8FA22982C01A,
	FixedString512BytesMotionAdapter_LitMotion_IMotionAdapterU3CUnity_Collections_FixedString512BytesU2CLitMotion_StringOptionsU3E_Evaluate_mE3ED5A53934BC012C266B801697A7FAEA505124E,
	FixedString4096BytesMotionAdapter_Evaluate_mC0B0006D3CCF6558DCA63E2B18397F5A7300D6FA,
	FixedString4096BytesMotionAdapter_LitMotion_IMotionAdapterU3CUnity_Collections_FixedString4096BytesU2CLitMotion_StringOptionsU3E_Evaluate_mA1AF5EF5902D3179F87770FA99CECAEA5BD3E649,
	FloatMotionAdapter_Evaluate_m0EB9B9667D4CA9372C0A655011E801B42AD89B97,
	FloatMotionAdapter_LitMotion_IMotionAdapterU3CSystem_SingleU2CLitMotion_NoOptionsU3E_Evaluate_m01273A2B00AFF360ADEEFFF3CE4E735C6AC16A8F,
	DoubleMotionAdapter_Evaluate_m9B514CA6D899B8257367F83BA263AD718C0E1FB3,
	DoubleMotionAdapter_LitMotion_IMotionAdapterU3CSystem_DoubleU2CLitMotion_NoOptionsU3E_Evaluate_m7D22941DAF061BC51970DB13B73736D1204F29F9,
	IntMotionAdapter_Evaluate_mFCE0298265BE98980C3CD7BF2628C2324FC650EF,
	IntMotionAdapter_LitMotion_IMotionAdapterU3CSystem_Int32U2CLitMotion_IntegerOptionsU3E_Evaluate_m26B7335E3AE26159BC42584B2B7DB524FA731640,
	LongMotionAdapter_Evaluate_m85E43341837F672CE4B30AAD4C93AF6A40210672,
	LongMotionAdapter_LitMotion_IMotionAdapterU3CSystem_Int64U2CLitMotion_IntegerOptionsU3E_Evaluate_m16A1ED25B8CD0960EB5F3EDA04603F6B09F593FF,
	FloatPunchMotionAdapter_Evaluate_m9759F0984D1C3796E16898DBC3E261935085AEA8,
	FloatPunchMotionAdapter_LitMotion_IMotionAdapterU3CSystem_SingleU2CLitMotion_PunchOptionsU3E_Evaluate_m895D07F474710AC4079D4C9198E791FA1DF606AA,
	Vector2PunchMotionAdapter_Evaluate_m5F60BB55D5AF8B5B730625D640F04590DB6AAF86,
	Vector2PunchMotionAdapter_LitMotion_IMotionAdapterU3CUnityEngine_Vector2U2CLitMotion_PunchOptionsU3E_Evaluate_m6A9A74F78DC48EB4D8743C1C48FCF9A0A74CACB7,
	Vector3PunchMotionAdapter_Evaluate_mB42E328BF54522FDF1B02E28C6327BDBE9C2689A,
	Vector3PunchMotionAdapter_LitMotion_IMotionAdapterU3CUnityEngine_Vector3U2CLitMotion_PunchOptionsU3E_Evaluate_m508441802C077334FD42461FCDBB2FB9BFE67C8F,
	FloatShakeMotionAdapter_Evaluate_mC1BC8EC25DC1320FFBECA93770526D82C880AD55,
	FloatShakeMotionAdapter_LitMotion_IMotionAdapterU3CSystem_SingleU2CLitMotion_ShakeOptionsU3E_Evaluate_m2879D969F6B0EA80B72DA8E80A5114A518C4B744,
	Vector2ShakeMotionAdapter_Evaluate_m50827B1AA3F46AEA42C9B6D4093F77553A0AF05F,
	Vector2ShakeMotionAdapter_LitMotion_IMotionAdapterU3CUnityEngine_Vector2U2CLitMotion_ShakeOptionsU3E_Evaluate_m3010C121CA81FF149803E1630A3DEEF37C2039D1,
	Vector3ShakeMotionAdapter_Evaluate_m67B9299405876AB5E5AA404EDF4C8D3CFF9F986C,
	Vector3ShakeMotionAdapter_LitMotion_IMotionAdapterU3CUnityEngine_Vector3U2CLitMotion_ShakeOptionsU3E_Evaluate_m36C70009C85C54A7C481A22E67C5798FCA704DD1,
	Vector2MotionAdapter_Evaluate_mA06C68E46BA054C94F5F7C5878412C5B444296CD,
	Vector2MotionAdapter_LitMotion_IMotionAdapterU3CUnityEngine_Vector2U2CLitMotion_NoOptionsU3E_Evaluate_m8327E209843A8543301B74545AB3CABCE9606A41,
	Vector3MotionAdapter_Evaluate_m909830664A98630176F9D226B3274D5EE14312EB,
	Vector3MotionAdapter_LitMotion_IMotionAdapterU3CUnityEngine_Vector3U2CLitMotion_NoOptionsU3E_Evaluate_m546FEB420E0FEF59364CB2E8C29C89997787B048,
	Vector4MotionAdapter_Evaluate_m10FF8F5E90EC8DBD0AE8A53583C199D871682C82,
	Vector4MotionAdapter_LitMotion_IMotionAdapterU3CUnityEngine_Vector4U2CLitMotion_NoOptionsU3E_Evaluate_m5FC2465D84F86A4883BC16259504B0D3FDFED933,
	QuaternionMotionAdapter_Evaluate_mE5FE0E632D5815981970F997C2C4DF4CDDA337A5,
	QuaternionMotionAdapter_LitMotion_IMotionAdapterU3CUnityEngine_QuaternionU2CLitMotion_NoOptionsU3E_Evaluate_m247E676A6BD2BC48DC6F18BC6216FE636DE38058,
	ColorMotionAdapter_Evaluate_mAF212E5E5ED689641D29C3BD79DDAE16C2CE4F04,
	ColorMotionAdapter_LitMotion_IMotionAdapterU3CUnityEngine_ColorU2CLitMotion_NoOptionsU3E_Evaluate_m47C20EA6F76C9CAB31F112E1E397F76F6BFD78F0,
	RectMotionAdapter_Evaluate_m41DF28786C24A9F27A88C18C763C3E2D4F9F6843,
	RectMotionAdapter_LitMotion_IMotionAdapterU3CUnityEngine_RectU2CLitMotion_NoOptionsU3E_Evaluate_mFFB30B245698D7885FEAB8A144F9A32EC3504B20,
	__JobReflectionRegistrationOutput__1815361719_CreateJobReflectionData_mB027C9E04EF8BD510D6E2D649862D0D3796328E3,
	__JobReflectionRegistrationOutput__1815361719_EarlyInit_m2D390F4E4794047E7FF12FE9F1BD077C788BA9D6,
	U24BurstDirectCallInitializer_Initialize_m42FD7453A84042DA9C3F80DE55FDC866865E93EE,
};
extern void ManagedMotionData_InvokeOnCancel_m73240D0D9B2ACE57785592A78ADA22910624A9BF_AdjustorThunk (void);
extern void ManagedMotionData_InvokeOnComplete_m5CDE09F72CF3E6B4701EE5AFA09AF288C1F3A6E5_AdjustorThunk (void);
extern void ManagedMotionData_InvokeOnLoopComplete_m0AB33D63FC13012AC691C4F93D444AF4E5342D65_AdjustorThunk (void);
extern void MotionData_get_TimeSinceStart_m98F66E013764C57EEB2D4D31F5C0EBF3D51F8828_AdjustorThunk (void);
extern void MotionData_Update_m5B3BC2C8E45492DB4682325ACCFBC36BE289538F_AdjustorThunk (void);
extern void MotionData_Complete_m734EE33FAC8939EC5F4DFADDD1BA62B42DE7F1C0_AdjustorThunk (void);
extern void MotionData_GetClampedCompletedLoops_mB421308254CDA470E5BFB7E6B13DE758B4A5B837_AdjustorThunk (void);
extern void MotionData_GetEasedValue_mDE2CC8C13F67B1B2D52651726F564695895E9360_AdjustorThunk (void);
extern void MotionState_get_WasStatusChanged_m9618DFD6BA628253A183FE3C50464674CDCEA95A_AdjustorThunk (void);
extern void MotionState_get_WasLoopCompleted_m9721F9B229DD0C67B41514B12C81B9F9AA926B8C_AdjustorThunk (void);
extern void MotionParameters_get_TotalDuration_mB7A505F6BCE808D3071AF959E08FC6B1ECCD9047_AdjustorThunk (void);
extern void RichTextSymbol32Bytes__ctor_m65F97C28A14578AC22B6A40B08A34A1C30D3976F_AdjustorThunk (void);
extern void RichTextSymbol64Bytes__ctor_m11C4E0E45BB649E0CA9F39316211600F61633141_AdjustorThunk (void);
extern void RichTextSymbol128Bytes__ctor_mE728D7EE402D4EBF8445D4439494F167EA3BD6DE_AdjustorThunk (void);
extern void RichTextSymbol512Bytes__ctor_mCC11833B715889839D15FEA95AEC3775A3F93DC0_AdjustorThunk (void);
extern void RichTextSymbol4096Bytes__ctor_mC0EDF5BBF1438668B5C2528CCA56122EB039D7A2_AdjustorThunk (void);
extern void SparseIndex_get_Index_m7BD01B8761FC3B8060E009BBB229CEE4061D6712_AdjustorThunk (void);
extern void SparseIndex_get_Version_m0485D8AA73020E06308E9D86963AB6F8ADE78D0D_AdjustorThunk (void);
extern void SparseIndex__ctor_mE2B01B52B0AAF9C71B3A3348D72571D1F459EBF6_AdjustorThunk (void);
extern void SparseIndex_Equals_m796B8C7DD085DDE7E0D5A96E6AFF1FC6428C5774_AdjustorThunk (void);
extern void SparseIndex_Equals_m6BD153A61F484812F71DCE2BED5DA7A5A23D3960_AdjustorThunk (void);
extern void SparseIndex_GetHashCode_m219B7BA026D67923B2C8AA5F5B065535559D1C20_AdjustorThunk (void);
extern void Slot_Equals_m0BA52037992DC515671A8820F41CBF40A02A7AAF_AdjustorThunk (void);
extern void Slot_Equals_m771BA52BD099581D2888B34D1D0E21EF771364C1_AdjustorThunk (void);
extern void Slot_GetHashCode_m65E4135D452912AF96D1F2671C9D7C00AD331B1B_AdjustorThunk (void);
extern void MotionAwaiter_get_IsCompleted_m286644973703C945DFDEBF59ECBFEB9813E55A6C_AdjustorThunk (void);
extern void MotionAwaiter__ctor_m39C511FEAC16D5589DB9A0DB04C1BAEF6509CE3B_AdjustorThunk (void);
extern void MotionAwaiter_GetAwaiter_m5E1F76DB7C9DD5B3334F3E8C71DF46038D1BCCA1_AdjustorThunk (void);
extern void MotionAwaiter_GetResult_m582D3877D66D6496EA719A34312D4B263C4F2D2E_AdjustorThunk (void);
extern void MotionAwaiter_OnCompleted_mE62AD33CA015865A8F3819534883404785ADA914_AdjustorThunk (void);
extern void MotionAwaiter_UnsafeOnCompleted_mF5C13B5A490B2A7E1C332891A7F694960288F74B_AdjustorThunk (void);
extern void MotionHandle_get_Time_mA0FDC83A5CA764224292035D1B92DD35A0737C69_AdjustorThunk (void);
extern void MotionHandle_set_Time_mD678AA94DB0E174FC2EB977EBE10986DC62DEA9B_AdjustorThunk (void);
extern void MotionHandle_get_Delay_m79CC0D8F78C3B8E93C5DBBC5932A1C4FA22B453E_AdjustorThunk (void);
extern void MotionHandle_get_Duration_mFEF79D284F2ABF0AE9170303CCE7F0FC61443E06_AdjustorThunk (void);
extern void MotionHandle_get_TotalDuration_m03E9F23F0E3E2FA0D53FA6F796579DB2833E15DF_AdjustorThunk (void);
extern void MotionHandle_get_Loops_mD5ADE9962E0D6770C65D0B25BB8240340AC323FF_AdjustorThunk (void);
extern void MotionHandle_get_CompletedLoops_m8CF0BFB384A65D617D69D195DE7637FC03673E40_AdjustorThunk (void);
extern void MotionHandle_get_PlaybackSpeed_m810CA94487003021B21169CE4C00AB0D2B57B8C0_AdjustorThunk (void);
extern void MotionHandle_set_PlaybackSpeed_m2CE0693B92A83C716EA4BF498A5E9C57AC71833E_AdjustorThunk (void);
extern void MotionHandle_ToString_mE2F8F39BF927477327C69AFEC8BB1A6D9FA0625E_AdjustorThunk (void);
extern void MotionHandle_Equals_m717AA4FEC4A00726F8C0DBB28ED51B2FF53A539F_AdjustorThunk (void);
extern void MotionHandle_Equals_mA77A264C0AE2A05C893C0BBF7D2C1CA0315AFE2B_AdjustorThunk (void);
extern void MotionHandle_GetHashCode_m201415482C5E4AE8A0E14879A905ACD71E0615B7_AdjustorThunk (void);
extern void MotionSequenceBuilder__ctor_m3B3813AFEE55253DDC6A81DFAF99DDFA94F5B0C6_AdjustorThunk (void);
extern void MotionSequenceBuilder_Append_m1B1458BF99FA0A758F5202BCAFCF1FDB6397407F_AdjustorThunk (void);
extern void MotionSequenceBuilder_AppendInterval_m31D45D0D37C90D214776825F47C2B0C7F209757C_AdjustorThunk (void);
extern void MotionSequenceBuilder_Insert_m81CF3B3C1F3DA5B975028BDCA01547DE35A12604_AdjustorThunk (void);
extern void MotionSequenceBuilder_Join_m342181FC806C2381E1A27CFAB1F1903115D5A1C5_AdjustorThunk (void);
extern void MotionSequenceBuilder_Run_m8470B91A3EE5610052CCAA58145AFFD2015833B7_AdjustorThunk (void);
extern void MotionSequenceBuilder_Run_m72BD27AF38A04BA10134D3D64B1A4942F8235A3E_AdjustorThunk (void);
extern void MotionSequenceBuilder_Dispose_m3AC2152DD6D49C7AF09FF581B7AEBF47D5D901BE_AdjustorThunk (void);
extern void MotionSequenceBuilder_CheckIsDisposed_mA0B1F56B0D2534199606ED88E68FC884BD67A4BF_AdjustorThunk (void);
extern void MotionSequenceItem__ctor_mB684C8F3E987F664437269051E91FA23626C5A14_AdjustorThunk (void);
extern void MotionSequenceItem_CompareTo_m65A6CD68890C8BC64BE0149E42F11941291DE7DA_AdjustorThunk (void);
extern void IntegerOptions_Equals_mB8DE550CAC68A2F0029A1B9874E791296CDFA19A_AdjustorThunk (void);
extern void IntegerOptions_Equals_mE2E60F034BA74E42A4A3B9E083E07689FE57B409_AdjustorThunk (void);
extern void IntegerOptions_GetHashCode_mBB87BD24FB83C50E33BB9E977E81114CD966F98A_AdjustorThunk (void);
extern void NoOptions_Equals_m385B96E4BB896392A8219EC298ABC8845ED1C92F_AdjustorThunk (void);
extern void NoOptions_Equals_m8A4E39BEFD271327062C569AF079453F9F3CF612_AdjustorThunk (void);
extern void NoOptions_GetHashCode_m28C2C72A5509EAB1ACAA3B12A2F7DF26903E33F3_AdjustorThunk (void);
extern void PunchOptions_Equals_m11D7621E6C9FE1E0D50B120F403B484164004A8B_AdjustorThunk (void);
extern void PunchOptions_Equals_mD36EFF3523E00BF3996DCD90E529037F29ED0043_AdjustorThunk (void);
extern void PunchOptions_GetHashCode_m060EE8928378E41058941BFC6D58324B45E615C7_AdjustorThunk (void);
extern void ShakeOptions_Equals_mD48E7AC97860E50E55C02DF0BD5952C9E4736C99_AdjustorThunk (void);
extern void ShakeOptions_Equals_m18AA941F8A34D3CBB611936E96C80BD1B03BD8B1_AdjustorThunk (void);
extern void ShakeOptions_GetHashCode_m836C3BA106E9A4C82EC6663C135D7F5679352161_AdjustorThunk (void);
extern void StringOptions_Equals_m94F9D676B065FF992DF8B874BB4DECC43D087223_AdjustorThunk (void);
extern void StringOptions_Equals_m0B6CB436B80F81E1F52B76B9A7E970BE1EFAE19C_AdjustorThunk (void);
extern void StringOptions_GetHashCode_m2C4406DD93543B5109AFB9867E94B7098A335871_AdjustorThunk (void);
extern void NativeAnimationCurve__ctor_m3AD50473C7E68E08F43BDA4D59FF9DCA8EC4ADA4_AdjustorThunk (void);
extern void NativeAnimationCurve__ctor_m79972267B1924C44D34A2CAA6BCC66DA165E18C2_AdjustorThunk (void);
extern void NativeAnimationCurve_CopyFrom_m1AAC67EAF4342DB533B3AD7589CE1ED98222D903_AdjustorThunk (void);
extern void NativeAnimationCurve_CopyFrom_m7BA8522B4BF457E0D81780864E70DFD81004CE24_AdjustorThunk (void);
extern void NativeAnimationCurve_Dispose_m1C0E5159BB7F29FAD12B387C593A5C3A4E088E3C_AdjustorThunk (void);
extern void NativeAnimationCurve_get_IsCreated_mA83C5407F24D940BE28F8628B6A4132577415688_AdjustorThunk (void);
extern void NativeAnimationCurve_Evaluate_m7ED7143984E71AF465407ABAA94BE754CBF8A8A4_AdjustorThunk (void);
extern void KeyframeComparer_Compare_m08A8F446BB3CEF5FBC791F447A11D880D5268AF9_AdjustorThunk (void);
extern void UnsafeAnimationCurve__ctor_mD81A51444ED8815EB361446BD52D8B7C3057FB29_AdjustorThunk (void);
extern void UnsafeAnimationCurve__ctor_m5761C59D0B100C1DA7DCA474DBA7A6DE4C99FE4B_AdjustorThunk (void);
extern void UnsafeAnimationCurve_CopyFrom_m6554425C2394D531B1DA0EDD75C604295F9D32A2_AdjustorThunk (void);
extern void UnsafeAnimationCurve_CopyFrom_m25A0FC5288CB8D79418DC4A9D3D1C6851144437D_AdjustorThunk (void);
extern void UnsafeAnimationCurve_Dispose_mE82134542407CBEB297210496545BD033B35FF8A_AdjustorThunk (void);
extern void UnsafeAnimationCurve_get_IsCreated_m58C6CEF87EA2812D477E04F3BCBC0EA474CBB33E_AdjustorThunk (void);
extern void UnsafeAnimationCurve_Evaluate_mBBFB7BCA744F345B181678223D5B3A7AF5FF1848_AdjustorThunk (void);
extern void FixedString32BytesMotionAdapter_Evaluate_m0C65C871422A2CDA8442392C70C50A27A3596C25_AdjustorThunk (void);
extern void FixedString32BytesMotionAdapter_LitMotion_IMotionAdapterU3CUnity_Collections_FixedString32BytesU2CLitMotion_StringOptionsU3E_Evaluate_m61FA07A7B8CF5A5BE9FCE9705FC3838B4292A5D1_AdjustorThunk (void);
extern void FixedString64BytesMotionAdapter_Evaluate_mE8E866842E525C6138219634E1E63721F5D8E1BF_AdjustorThunk (void);
extern void FixedString64BytesMotionAdapter_LitMotion_IMotionAdapterU3CUnity_Collections_FixedString64BytesU2CLitMotion_StringOptionsU3E_Evaluate_mE85D4981C07EA5E5CDCBF97B40C61373A08DF5BB_AdjustorThunk (void);
extern void FixedString128BytesMotionAdapter_Evaluate_m68E4B7360276AEA24A98254DC080748626C8449F_AdjustorThunk (void);
extern void FixedString128BytesMotionAdapter_LitMotion_IMotionAdapterU3CUnity_Collections_FixedString128BytesU2CLitMotion_StringOptionsU3E_Evaluate_mD07ECCA1245B5B63DD5721B04374A9200F64F2EE_AdjustorThunk (void);
extern void FixedString512BytesMotionAdapter_Evaluate_m85725AEDFBE845AB737702D09A8A8FA22982C01A_AdjustorThunk (void);
extern void FixedString512BytesMotionAdapter_LitMotion_IMotionAdapterU3CUnity_Collections_FixedString512BytesU2CLitMotion_StringOptionsU3E_Evaluate_mE3ED5A53934BC012C266B801697A7FAEA505124E_AdjustorThunk (void);
extern void FixedString4096BytesMotionAdapter_Evaluate_mC0B0006D3CCF6558DCA63E2B18397F5A7300D6FA_AdjustorThunk (void);
extern void FixedString4096BytesMotionAdapter_LitMotion_IMotionAdapterU3CUnity_Collections_FixedString4096BytesU2CLitMotion_StringOptionsU3E_Evaluate_mA1AF5EF5902D3179F87770FA99CECAEA5BD3E649_AdjustorThunk (void);
extern void FloatMotionAdapter_Evaluate_m0EB9B9667D4CA9372C0A655011E801B42AD89B97_AdjustorThunk (void);
extern void FloatMotionAdapter_LitMotion_IMotionAdapterU3CSystem_SingleU2CLitMotion_NoOptionsU3E_Evaluate_m01273A2B00AFF360ADEEFFF3CE4E735C6AC16A8F_AdjustorThunk (void);
extern void DoubleMotionAdapter_Evaluate_m9B514CA6D899B8257367F83BA263AD718C0E1FB3_AdjustorThunk (void);
extern void DoubleMotionAdapter_LitMotion_IMotionAdapterU3CSystem_DoubleU2CLitMotion_NoOptionsU3E_Evaluate_m7D22941DAF061BC51970DB13B73736D1204F29F9_AdjustorThunk (void);
extern void IntMotionAdapter_Evaluate_mFCE0298265BE98980C3CD7BF2628C2324FC650EF_AdjustorThunk (void);
extern void IntMotionAdapter_LitMotion_IMotionAdapterU3CSystem_Int32U2CLitMotion_IntegerOptionsU3E_Evaluate_m26B7335E3AE26159BC42584B2B7DB524FA731640_AdjustorThunk (void);
extern void LongMotionAdapter_Evaluate_m85E43341837F672CE4B30AAD4C93AF6A40210672_AdjustorThunk (void);
extern void LongMotionAdapter_LitMotion_IMotionAdapterU3CSystem_Int64U2CLitMotion_IntegerOptionsU3E_Evaluate_m16A1ED25B8CD0960EB5F3EDA04603F6B09F593FF_AdjustorThunk (void);
extern void FloatPunchMotionAdapter_Evaluate_m9759F0984D1C3796E16898DBC3E261935085AEA8_AdjustorThunk (void);
extern void FloatPunchMotionAdapter_LitMotion_IMotionAdapterU3CSystem_SingleU2CLitMotion_PunchOptionsU3E_Evaluate_m895D07F474710AC4079D4C9198E791FA1DF606AA_AdjustorThunk (void);
extern void Vector2PunchMotionAdapter_Evaluate_m5F60BB55D5AF8B5B730625D640F04590DB6AAF86_AdjustorThunk (void);
extern void Vector2PunchMotionAdapter_LitMotion_IMotionAdapterU3CUnityEngine_Vector2U2CLitMotion_PunchOptionsU3E_Evaluate_m6A9A74F78DC48EB4D8743C1C48FCF9A0A74CACB7_AdjustorThunk (void);
extern void Vector3PunchMotionAdapter_Evaluate_mB42E328BF54522FDF1B02E28C6327BDBE9C2689A_AdjustorThunk (void);
extern void Vector3PunchMotionAdapter_LitMotion_IMotionAdapterU3CUnityEngine_Vector3U2CLitMotion_PunchOptionsU3E_Evaluate_m508441802C077334FD42461FCDBB2FB9BFE67C8F_AdjustorThunk (void);
extern void FloatShakeMotionAdapter_Evaluate_mC1BC8EC25DC1320FFBECA93770526D82C880AD55_AdjustorThunk (void);
extern void FloatShakeMotionAdapter_LitMotion_IMotionAdapterU3CSystem_SingleU2CLitMotion_ShakeOptionsU3E_Evaluate_m2879D969F6B0EA80B72DA8E80A5114A518C4B744_AdjustorThunk (void);
extern void Vector2ShakeMotionAdapter_Evaluate_m50827B1AA3F46AEA42C9B6D4093F77553A0AF05F_AdjustorThunk (void);
extern void Vector2ShakeMotionAdapter_LitMotion_IMotionAdapterU3CUnityEngine_Vector2U2CLitMotion_ShakeOptionsU3E_Evaluate_m3010C121CA81FF149803E1630A3DEEF37C2039D1_AdjustorThunk (void);
extern void Vector3ShakeMotionAdapter_Evaluate_m67B9299405876AB5E5AA404EDF4C8D3CFF9F986C_AdjustorThunk (void);
extern void Vector3ShakeMotionAdapter_LitMotion_IMotionAdapterU3CUnityEngine_Vector3U2CLitMotion_ShakeOptionsU3E_Evaluate_m36C70009C85C54A7C481A22E67C5798FCA704DD1_AdjustorThunk (void);
extern void Vector2MotionAdapter_Evaluate_mA06C68E46BA054C94F5F7C5878412C5B444296CD_AdjustorThunk (void);
extern void Vector2MotionAdapter_LitMotion_IMotionAdapterU3CUnityEngine_Vector2U2CLitMotion_NoOptionsU3E_Evaluate_m8327E209843A8543301B74545AB3CABCE9606A41_AdjustorThunk (void);
extern void Vector3MotionAdapter_Evaluate_m909830664A98630176F9D226B3274D5EE14312EB_AdjustorThunk (void);
extern void Vector3MotionAdapter_LitMotion_IMotionAdapterU3CUnityEngine_Vector3U2CLitMotion_NoOptionsU3E_Evaluate_m546FEB420E0FEF59364CB2E8C29C89997787B048_AdjustorThunk (void);
extern void Vector4MotionAdapter_Evaluate_m10FF8F5E90EC8DBD0AE8A53583C199D871682C82_AdjustorThunk (void);
extern void Vector4MotionAdapter_LitMotion_IMotionAdapterU3CUnityEngine_Vector4U2CLitMotion_NoOptionsU3E_Evaluate_m5FC2465D84F86A4883BC16259504B0D3FDFED933_AdjustorThunk (void);
extern void QuaternionMotionAdapter_Evaluate_mE5FE0E632D5815981970F997C2C4DF4CDDA337A5_AdjustorThunk (void);
extern void QuaternionMotionAdapter_LitMotion_IMotionAdapterU3CUnityEngine_QuaternionU2CLitMotion_NoOptionsU3E_Evaluate_m247E676A6BD2BC48DC6F18BC6216FE636DE38058_AdjustorThunk (void);
extern void ColorMotionAdapter_Evaluate_mAF212E5E5ED689641D29C3BD79DDAE16C2CE4F04_AdjustorThunk (void);
extern void ColorMotionAdapter_LitMotion_IMotionAdapterU3CUnityEngine_ColorU2CLitMotion_NoOptionsU3E_Evaluate_m47C20EA6F76C9CAB31F112E1E397F76F6BFD78F0_AdjustorThunk (void);
extern void RectMotionAdapter_Evaluate_m41DF28786C24A9F27A88C18C763C3E2D4F9F6843_AdjustorThunk (void);
extern void RectMotionAdapter_LitMotion_IMotionAdapterU3CUnityEngine_RectU2CLitMotion_NoOptionsU3E_Evaluate_mFFB30B245698D7885FEAB8A144F9A32EC3504B20_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[127] = 
{
	{ 0x06000247, ManagedMotionData_InvokeOnCancel_m73240D0D9B2ACE57785592A78ADA22910624A9BF_AdjustorThunk },
	{ 0x06000248, ManagedMotionData_InvokeOnComplete_m5CDE09F72CF3E6B4701EE5AFA09AF288C1F3A6E5_AdjustorThunk },
	{ 0x06000249, ManagedMotionData_InvokeOnLoopComplete_m0AB33D63FC13012AC691C4F93D444AF4E5342D65_AdjustorThunk },
	{ 0x0600024A, MotionData_get_TimeSinceStart_m98F66E013764C57EEB2D4D31F5C0EBF3D51F8828_AdjustorThunk },
	{ 0x0600024B, MotionData_Update_m5B3BC2C8E45492DB4682325ACCFBC36BE289538F_AdjustorThunk },
	{ 0x0600024C, MotionData_Complete_m734EE33FAC8939EC5F4DFADDD1BA62B42DE7F1C0_AdjustorThunk },
	{ 0x0600024D, MotionData_GetClampedCompletedLoops_mB421308254CDA470E5BFB7E6B13DE758B4A5B837_AdjustorThunk },
	{ 0x0600024E, MotionData_GetEasedValue_mDE2CC8C13F67B1B2D52651726F564695895E9360_AdjustorThunk },
	{ 0x0600024F, MotionState_get_WasStatusChanged_m9618DFD6BA628253A183FE3C50464674CDCEA95A_AdjustorThunk },
	{ 0x06000250, MotionState_get_WasLoopCompleted_m9721F9B229DD0C67B41514B12C81B9F9AA926B8C_AdjustorThunk },
	{ 0x06000251, MotionParameters_get_TotalDuration_mB7A505F6BCE808D3071AF959E08FC6B1ECCD9047_AdjustorThunk },
	{ 0x06000309, RichTextSymbol32Bytes__ctor_m65F97C28A14578AC22B6A40B08A34A1C30D3976F_AdjustorThunk },
	{ 0x0600030A, RichTextSymbol64Bytes__ctor_m11C4E0E45BB649E0CA9F39316211600F61633141_AdjustorThunk },
	{ 0x0600030B, RichTextSymbol128Bytes__ctor_mE728D7EE402D4EBF8445D4439494F167EA3BD6DE_AdjustorThunk },
	{ 0x0600030C, RichTextSymbol512Bytes__ctor_mCC11833B715889839D15FEA95AEC3775A3F93DC0_AdjustorThunk },
	{ 0x0600030D, RichTextSymbol4096Bytes__ctor_mC0EDF5BBF1438668B5C2528CCA56122EB039D7A2_AdjustorThunk },
	{ 0x0600030E, SparseIndex_get_Index_m7BD01B8761FC3B8060E009BBB229CEE4061D6712_AdjustorThunk },
	{ 0x0600030F, SparseIndex_get_Version_m0485D8AA73020E06308E9D86963AB6F8ADE78D0D_AdjustorThunk },
	{ 0x06000310, SparseIndex__ctor_mE2B01B52B0AAF9C71B3A3348D72571D1F459EBF6_AdjustorThunk },
	{ 0x06000311, SparseIndex_Equals_m796B8C7DD085DDE7E0D5A96E6AFF1FC6428C5774_AdjustorThunk },
	{ 0x06000312, SparseIndex_Equals_m6BD153A61F484812F71DCE2BED5DA7A5A23D3960_AdjustorThunk },
	{ 0x06000313, SparseIndex_GetHashCode_m219B7BA026D67923B2C8AA5F5B065535559D1C20_AdjustorThunk },
	{ 0x0600031B, Slot_Equals_m0BA52037992DC515671A8820F41CBF40A02A7AAF_AdjustorThunk },
	{ 0x0600031C, Slot_Equals_m771BA52BD099581D2888B34D1D0E21EF771364C1_AdjustorThunk },
	{ 0x0600031D, Slot_GetHashCode_m65E4135D452912AF96D1F2671C9D7C00AD331B1B_AdjustorThunk },
	{ 0x06000392, MotionAwaiter_get_IsCompleted_m286644973703C945DFDEBF59ECBFEB9813E55A6C_AdjustorThunk },
	{ 0x06000393, MotionAwaiter__ctor_m39C511FEAC16D5589DB9A0DB04C1BAEF6509CE3B_AdjustorThunk },
	{ 0x06000394, MotionAwaiter_GetAwaiter_m5E1F76DB7C9DD5B3334F3E8C71DF46038D1BCCA1_AdjustorThunk },
	{ 0x06000395, MotionAwaiter_GetResult_m582D3877D66D6496EA719A34312D4B263C4F2D2E_AdjustorThunk },
	{ 0x06000396, MotionAwaiter_OnCompleted_mE62AD33CA015865A8F3819534883404785ADA914_AdjustorThunk },
	{ 0x06000397, MotionAwaiter_UnsafeOnCompleted_mF5C13B5A490B2A7E1C332891A7F694960288F74B_AdjustorThunk },
	{ 0x060003DD, MotionHandle_get_Time_mA0FDC83A5CA764224292035D1B92DD35A0737C69_AdjustorThunk },
	{ 0x060003DE, MotionHandle_set_Time_mD678AA94DB0E174FC2EB977EBE10986DC62DEA9B_AdjustorThunk },
	{ 0x060003DF, MotionHandle_get_Delay_m79CC0D8F78C3B8E93C5DBBC5932A1C4FA22B453E_AdjustorThunk },
	{ 0x060003E0, MotionHandle_get_Duration_mFEF79D284F2ABF0AE9170303CCE7F0FC61443E06_AdjustorThunk },
	{ 0x060003E1, MotionHandle_get_TotalDuration_m03E9F23F0E3E2FA0D53FA6F796579DB2833E15DF_AdjustorThunk },
	{ 0x060003E2, MotionHandle_get_Loops_mD5ADE9962E0D6770C65D0B25BB8240340AC323FF_AdjustorThunk },
	{ 0x060003E3, MotionHandle_get_CompletedLoops_m8CF0BFB384A65D617D69D195DE7637FC03673E40_AdjustorThunk },
	{ 0x060003E4, MotionHandle_get_PlaybackSpeed_m810CA94487003021B21169CE4C00AB0D2B57B8C0_AdjustorThunk },
	{ 0x060003E5, MotionHandle_set_PlaybackSpeed_m2CE0693B92A83C716EA4BF498A5E9C57AC71833E_AdjustorThunk },
	{ 0x060003E6, MotionHandle_ToString_mE2F8F39BF927477327C69AFEC8BB1A6D9FA0625E_AdjustorThunk },
	{ 0x060003E7, MotionHandle_Equals_m717AA4FEC4A00726F8C0DBB28ED51B2FF53A539F_AdjustorThunk },
	{ 0x060003E8, MotionHandle_Equals_mA77A264C0AE2A05C893C0BBF7D2C1CA0315AFE2B_AdjustorThunk },
	{ 0x060003E9, MotionHandle_GetHashCode_m201415482C5E4AE8A0E14879A905ACD71E0615B7_AdjustorThunk },
	{ 0x0600041E, MotionSequenceBuilder__ctor_m3B3813AFEE55253DDC6A81DFAF99DDFA94F5B0C6_AdjustorThunk },
	{ 0x0600041F, MotionSequenceBuilder_Append_m1B1458BF99FA0A758F5202BCAFCF1FDB6397407F_AdjustorThunk },
	{ 0x06000420, MotionSequenceBuilder_AppendInterval_m31D45D0D37C90D214776825F47C2B0C7F209757C_AdjustorThunk },
	{ 0x06000421, MotionSequenceBuilder_Insert_m81CF3B3C1F3DA5B975028BDCA01547DE35A12604_AdjustorThunk },
	{ 0x06000422, MotionSequenceBuilder_Join_m342181FC806C2381E1A27CFAB1F1903115D5A1C5_AdjustorThunk },
	{ 0x06000423, MotionSequenceBuilder_Run_m8470B91A3EE5610052CCAA58145AFFD2015833B7_AdjustorThunk },
	{ 0x06000424, MotionSequenceBuilder_Run_m72BD27AF38A04BA10134D3D64B1A4942F8235A3E_AdjustorThunk },
	{ 0x06000425, MotionSequenceBuilder_Dispose_m3AC2152DD6D49C7AF09FF581B7AEBF47D5D901BE_AdjustorThunk },
	{ 0x06000426, MotionSequenceBuilder_CheckIsDisposed_mA0B1F56B0D2534199606ED88E68FC884BD67A4BF_AdjustorThunk },
	{ 0x06000427, MotionSequenceItem__ctor_mB684C8F3E987F664437269051E91FA23626C5A14_AdjustorThunk },
	{ 0x06000428, MotionSequenceItem_CompareTo_m65A6CD68890C8BC64BE0149E42F11941291DE7DA_AdjustorThunk },
	{ 0x0600045E, IntegerOptions_Equals_mB8DE550CAC68A2F0029A1B9874E791296CDFA19A_AdjustorThunk },
	{ 0x0600045F, IntegerOptions_Equals_mE2E60F034BA74E42A4A3B9E083E07689FE57B409_AdjustorThunk },
	{ 0x06000460, IntegerOptions_GetHashCode_mBB87BD24FB83C50E33BB9E977E81114CD966F98A_AdjustorThunk },
	{ 0x06000461, NoOptions_Equals_m385B96E4BB896392A8219EC298ABC8845ED1C92F_AdjustorThunk },
	{ 0x06000462, NoOptions_Equals_m8A4E39BEFD271327062C569AF079453F9F3CF612_AdjustorThunk },
	{ 0x06000463, NoOptions_GetHashCode_m28C2C72A5509EAB1ACAA3B12A2F7DF26903E33F3_AdjustorThunk },
	{ 0x06000465, PunchOptions_Equals_m11D7621E6C9FE1E0D50B120F403B484164004A8B_AdjustorThunk },
	{ 0x06000466, PunchOptions_Equals_mD36EFF3523E00BF3996DCD90E529037F29ED0043_AdjustorThunk },
	{ 0x06000467, PunchOptions_GetHashCode_m060EE8928378E41058941BFC6D58324B45E615C7_AdjustorThunk },
	{ 0x06000469, ShakeOptions_Equals_mD48E7AC97860E50E55C02DF0BD5952C9E4736C99_AdjustorThunk },
	{ 0x0600046A, ShakeOptions_Equals_m18AA941F8A34D3CBB611936E96C80BD1B03BD8B1_AdjustorThunk },
	{ 0x0600046B, ShakeOptions_GetHashCode_m836C3BA106E9A4C82EC6663C135D7F5679352161_AdjustorThunk },
	{ 0x0600046C, StringOptions_Equals_m94F9D676B065FF992DF8B874BB4DECC43D087223_AdjustorThunk },
	{ 0x0600046D, StringOptions_Equals_m0B6CB436B80F81E1F52B76B9A7E970BE1EFAE19C_AdjustorThunk },
	{ 0x0600046E, StringOptions_GetHashCode_m2C4406DD93543B5109AFB9867E94B7098A335871_AdjustorThunk },
	{ 0x0600048E, NativeAnimationCurve__ctor_m3AD50473C7E68E08F43BDA4D59FF9DCA8EC4ADA4_AdjustorThunk },
	{ 0x0600048F, NativeAnimationCurve__ctor_m79972267B1924C44D34A2CAA6BCC66DA165E18C2_AdjustorThunk },
	{ 0x06000490, NativeAnimationCurve_CopyFrom_m1AAC67EAF4342DB533B3AD7589CE1ED98222D903_AdjustorThunk },
	{ 0x06000491, NativeAnimationCurve_CopyFrom_m7BA8522B4BF457E0D81780864E70DFD81004CE24_AdjustorThunk },
	{ 0x06000492, NativeAnimationCurve_Dispose_m1C0E5159BB7F29FAD12B387C593A5C3A4E088E3C_AdjustorThunk },
	{ 0x06000493, NativeAnimationCurve_get_IsCreated_mA83C5407F24D940BE28F8628B6A4132577415688_AdjustorThunk },
	{ 0x06000494, NativeAnimationCurve_Evaluate_m7ED7143984E71AF465407ABAA94BE754CBF8A8A4_AdjustorThunk },
	{ 0x06000495, KeyframeComparer_Compare_m08A8F446BB3CEF5FBC791F447A11D880D5268AF9_AdjustorThunk },
	{ 0x060004AD, UnsafeAnimationCurve__ctor_mD81A51444ED8815EB361446BD52D8B7C3057FB29_AdjustorThunk },
	{ 0x060004AE, UnsafeAnimationCurve__ctor_m5761C59D0B100C1DA7DCA474DBA7A6DE4C99FE4B_AdjustorThunk },
	{ 0x060004AF, UnsafeAnimationCurve_CopyFrom_m6554425C2394D531B1DA0EDD75C604295F9D32A2_AdjustorThunk },
	{ 0x060004B0, UnsafeAnimationCurve_CopyFrom_m25A0FC5288CB8D79418DC4A9D3D1C6851144437D_AdjustorThunk },
	{ 0x060004B1, UnsafeAnimationCurve_Dispose_mE82134542407CBEB297210496545BD033B35FF8A_AdjustorThunk },
	{ 0x060004B2, UnsafeAnimationCurve_get_IsCreated_m58C6CEF87EA2812D477E04F3BCBC0EA474CBB33E_AdjustorThunk },
	{ 0x060004B3, UnsafeAnimationCurve_Evaluate_mBBFB7BCA744F345B181678223D5B3A7AF5FF1848_AdjustorThunk },
	{ 0x060004B4, FixedString32BytesMotionAdapter_Evaluate_m0C65C871422A2CDA8442392C70C50A27A3596C25_AdjustorThunk },
	{ 0x060004B5, FixedString32BytesMotionAdapter_LitMotion_IMotionAdapterU3CUnity_Collections_FixedString32BytesU2CLitMotion_StringOptionsU3E_Evaluate_m61FA07A7B8CF5A5BE9FCE9705FC3838B4292A5D1_AdjustorThunk },
	{ 0x060004B6, FixedString64BytesMotionAdapter_Evaluate_mE8E866842E525C6138219634E1E63721F5D8E1BF_AdjustorThunk },
	{ 0x060004B7, FixedString64BytesMotionAdapter_LitMotion_IMotionAdapterU3CUnity_Collections_FixedString64BytesU2CLitMotion_StringOptionsU3E_Evaluate_mE85D4981C07EA5E5CDCBF97B40C61373A08DF5BB_AdjustorThunk },
	{ 0x060004B8, FixedString128BytesMotionAdapter_Evaluate_m68E4B7360276AEA24A98254DC080748626C8449F_AdjustorThunk },
	{ 0x060004B9, FixedString128BytesMotionAdapter_LitMotion_IMotionAdapterU3CUnity_Collections_FixedString128BytesU2CLitMotion_StringOptionsU3E_Evaluate_mD07ECCA1245B5B63DD5721B04374A9200F64F2EE_AdjustorThunk },
	{ 0x060004BA, FixedString512BytesMotionAdapter_Evaluate_m85725AEDFBE845AB737702D09A8A8FA22982C01A_AdjustorThunk },
	{ 0x060004BB, FixedString512BytesMotionAdapter_LitMotion_IMotionAdapterU3CUnity_Collections_FixedString512BytesU2CLitMotion_StringOptionsU3E_Evaluate_mE3ED5A53934BC012C266B801697A7FAEA505124E_AdjustorThunk },
	{ 0x060004BC, FixedString4096BytesMotionAdapter_Evaluate_mC0B0006D3CCF6558DCA63E2B18397F5A7300D6FA_AdjustorThunk },
	{ 0x060004BD, FixedString4096BytesMotionAdapter_LitMotion_IMotionAdapterU3CUnity_Collections_FixedString4096BytesU2CLitMotion_StringOptionsU3E_Evaluate_mA1AF5EF5902D3179F87770FA99CECAEA5BD3E649_AdjustorThunk },
	{ 0x060004BE, FloatMotionAdapter_Evaluate_m0EB9B9667D4CA9372C0A655011E801B42AD89B97_AdjustorThunk },
	{ 0x060004BF, FloatMotionAdapter_LitMotion_IMotionAdapterU3CSystem_SingleU2CLitMotion_NoOptionsU3E_Evaluate_m01273A2B00AFF360ADEEFFF3CE4E735C6AC16A8F_AdjustorThunk },
	{ 0x060004C0, DoubleMotionAdapter_Evaluate_m9B514CA6D899B8257367F83BA263AD718C0E1FB3_AdjustorThunk },
	{ 0x060004C1, DoubleMotionAdapter_LitMotion_IMotionAdapterU3CSystem_DoubleU2CLitMotion_NoOptionsU3E_Evaluate_m7D22941DAF061BC51970DB13B73736D1204F29F9_AdjustorThunk },
	{ 0x060004C2, IntMotionAdapter_Evaluate_mFCE0298265BE98980C3CD7BF2628C2324FC650EF_AdjustorThunk },
	{ 0x060004C3, IntMotionAdapter_LitMotion_IMotionAdapterU3CSystem_Int32U2CLitMotion_IntegerOptionsU3E_Evaluate_m26B7335E3AE26159BC42584B2B7DB524FA731640_AdjustorThunk },
	{ 0x060004C4, LongMotionAdapter_Evaluate_m85E43341837F672CE4B30AAD4C93AF6A40210672_AdjustorThunk },
	{ 0x060004C5, LongMotionAdapter_LitMotion_IMotionAdapterU3CSystem_Int64U2CLitMotion_IntegerOptionsU3E_Evaluate_m16A1ED25B8CD0960EB5F3EDA04603F6B09F593FF_AdjustorThunk },
	{ 0x060004C6, FloatPunchMotionAdapter_Evaluate_m9759F0984D1C3796E16898DBC3E261935085AEA8_AdjustorThunk },
	{ 0x060004C7, FloatPunchMotionAdapter_LitMotion_IMotionAdapterU3CSystem_SingleU2CLitMotion_PunchOptionsU3E_Evaluate_m895D07F474710AC4079D4C9198E791FA1DF606AA_AdjustorThunk },
	{ 0x060004C8, Vector2PunchMotionAdapter_Evaluate_m5F60BB55D5AF8B5B730625D640F04590DB6AAF86_AdjustorThunk },
	{ 0x060004C9, Vector2PunchMotionAdapter_LitMotion_IMotionAdapterU3CUnityEngine_Vector2U2CLitMotion_PunchOptionsU3E_Evaluate_m6A9A74F78DC48EB4D8743C1C48FCF9A0A74CACB7_AdjustorThunk },
	{ 0x060004CA, Vector3PunchMotionAdapter_Evaluate_mB42E328BF54522FDF1B02E28C6327BDBE9C2689A_AdjustorThunk },
	{ 0x060004CB, Vector3PunchMotionAdapter_LitMotion_IMotionAdapterU3CUnityEngine_Vector3U2CLitMotion_PunchOptionsU3E_Evaluate_m508441802C077334FD42461FCDBB2FB9BFE67C8F_AdjustorThunk },
	{ 0x060004CC, FloatShakeMotionAdapter_Evaluate_mC1BC8EC25DC1320FFBECA93770526D82C880AD55_AdjustorThunk },
	{ 0x060004CD, FloatShakeMotionAdapter_LitMotion_IMotionAdapterU3CSystem_SingleU2CLitMotion_ShakeOptionsU3E_Evaluate_m2879D969F6B0EA80B72DA8E80A5114A518C4B744_AdjustorThunk },
	{ 0x060004CE, Vector2ShakeMotionAdapter_Evaluate_m50827B1AA3F46AEA42C9B6D4093F77553A0AF05F_AdjustorThunk },
	{ 0x060004CF, Vector2ShakeMotionAdapter_LitMotion_IMotionAdapterU3CUnityEngine_Vector2U2CLitMotion_ShakeOptionsU3E_Evaluate_m3010C121CA81FF149803E1630A3DEEF37C2039D1_AdjustorThunk },
	{ 0x060004D0, Vector3ShakeMotionAdapter_Evaluate_m67B9299405876AB5E5AA404EDF4C8D3CFF9F986C_AdjustorThunk },
	{ 0x060004D1, Vector3ShakeMotionAdapter_LitMotion_IMotionAdapterU3CUnityEngine_Vector3U2CLitMotion_ShakeOptionsU3E_Evaluate_m36C70009C85C54A7C481A22E67C5798FCA704DD1_AdjustorThunk },
	{ 0x060004D2, Vector2MotionAdapter_Evaluate_mA06C68E46BA054C94F5F7C5878412C5B444296CD_AdjustorThunk },
	{ 0x060004D3, Vector2MotionAdapter_LitMotion_IMotionAdapterU3CUnityEngine_Vector2U2CLitMotion_NoOptionsU3E_Evaluate_m8327E209843A8543301B74545AB3CABCE9606A41_AdjustorThunk },
	{ 0x060004D4, Vector3MotionAdapter_Evaluate_m909830664A98630176F9D226B3274D5EE14312EB_AdjustorThunk },
	{ 0x060004D5, Vector3MotionAdapter_LitMotion_IMotionAdapterU3CUnityEngine_Vector3U2CLitMotion_NoOptionsU3E_Evaluate_m546FEB420E0FEF59364CB2E8C29C89997787B048_AdjustorThunk },
	{ 0x060004D6, Vector4MotionAdapter_Evaluate_m10FF8F5E90EC8DBD0AE8A53583C199D871682C82_AdjustorThunk },
	{ 0x060004D7, Vector4MotionAdapter_LitMotion_IMotionAdapterU3CUnityEngine_Vector4U2CLitMotion_NoOptionsU3E_Evaluate_m5FC2465D84F86A4883BC16259504B0D3FDFED933_AdjustorThunk },
	{ 0x060004D8, QuaternionMotionAdapter_Evaluate_mE5FE0E632D5815981970F997C2C4DF4CDDA337A5_AdjustorThunk },
	{ 0x060004D9, QuaternionMotionAdapter_LitMotion_IMotionAdapterU3CUnityEngine_QuaternionU2CLitMotion_NoOptionsU3E_Evaluate_m247E676A6BD2BC48DC6F18BC6216FE636DE38058_AdjustorThunk },
	{ 0x060004DA, ColorMotionAdapter_Evaluate_mAF212E5E5ED689641D29C3BD79DDAE16C2CE4F04_AdjustorThunk },
	{ 0x060004DB, ColorMotionAdapter_LitMotion_IMotionAdapterU3CUnityEngine_ColorU2CLitMotion_NoOptionsU3E_Evaluate_m47C20EA6F76C9CAB31F112E1E397F76F6BFD78F0_AdjustorThunk },
	{ 0x060004DC, RectMotionAdapter_Evaluate_m41DF28786C24A9F27A88C18C763C3E2D4F9F6843_AdjustorThunk },
	{ 0x060004DD, RectMotionAdapter_LitMotion_IMotionAdapterU3CUnityEngine_RectU2CLitMotion_NoOptionsU3E_Evaluate_mFFB30B245698D7885FEAB8A144F9A32EC3504B20_AdjustorThunk },
};
static const int32_t s_InvokerIndices[1248] = 
{
	13298,
	13298,
	10442,
	10682,
	10442,
	21386,
	13298,
	13298,
	13298,
	10629,
	13298,
	13298,
	10674,
	11757,
	13052,
	13052,
	13298,
	7727,
	5681,
	7727,
	12996,
	12815,
	18171,
	20667,
	20667,
	20667,
	20667,
	20667,
	20667,
	20667,
	20667,
	20667,
	20667,
	20667,
	20667,
	20667,
	20667,
	20667,
	20667,
	20667,
	20667,
	20667,
	20667,
	20667,
	20667,
	20667,
	20667,
	20667,
	20667,
	20667,
	20667,
	20667,
	20667,
	20667,
	18171,
	20667,
	20667,
	20667,
	20667,
	20667,
	20667,
	20667,
	20667,
	20667,
	20667,
	20667,
	20667,
	20667,
	20667,
	20667,
	20667,
	20667,
	20667,
	20667,
	20667,
	20667,
	20667,
	20667,
	20667,
	20667,
	20667,
	20667,
	20667,
	20667,
	20667,
	20667,
	5684,
	4612,
	1674,
	9457,
	20831,
	21265,
	21355,
	21355,
	21355,
	18171,
	5684,
	9460,
	2420,
	9457,
	20831,
	21265,
	21355,
	21355,
	21355,
	20667,
	5684,
	9460,
	2420,
	9457,
	20831,
	21265,
	21355,
	21355,
	21355,
	20667,
	5684,
	9460,
	2420,
	9457,
	20831,
	21265,
	21355,
	21355,
	21355,
	20667,
	5684,
	9460,
	2420,
	9457,
	20831,
	21265,
	21355,
	21355,
	21355,
	20667,
	5684,
	9460,
	2420,
	9457,
	20831,
	21265,
	21355,
	21355,
	21355,
	20667,
	5684,
	9460,
	2420,
	9457,
	20831,
	21265,
	21355,
	21355,
	21355,
	20667,
	5684,
	9460,
	2420,
	9457,
	20831,
	21265,
	21355,
	21355,
	21355,
	20667,
	5684,
	9460,
	2420,
	9457,
	20831,
	21265,
	21355,
	21355,
	21355,
	20667,
	5684,
	9460,
	2420,
	9457,
	20831,
	21265,
	21355,
	21355,
	21355,
	20667,
	5684,
	9460,
	2420,
	9457,
	20831,
	21265,
	21355,
	21355,
	21355,
	20667,
	5684,
	9460,
	2420,
	9457,
	20831,
	21265,
	21355,
	21355,
	21355,
	20667,
	5684,
	9460,
	2420,
	9457,
	20831,
	21265,
	21355,
	21355,
	21355,
	20667,
	5684,
	9460,
	2420,
	9457,
	20831,
	21265,
	21355,
	21355,
	21355,
	20667,
	5684,
	9460,
	2420,
	9457,
	20831,
	21265,
	21355,
	21355,
	21355,
	20667,
	5684,
	9460,
	2420,
	9457,
	20831,
	21265,
	21355,
	21355,
	21355,
	20667,
	5684,
	9460,
	2420,
	9457,
	20831,
	21265,
	21355,
	21355,
	21355,
	20667,
	5684,
	9460,
	2420,
	9457,
	20831,
	21265,
	21355,
	21355,
	21355,
	20667,
	5684,
	9460,
	2420,
	9457,
	20831,
	21265,
	21355,
	21355,
	21355,
	20667,
	5684,
	9460,
	2420,
	9457,
	20831,
	21265,
	21355,
	21355,
	21355,
	20667,
	5684,
	9460,
	2420,
	9457,
	20831,
	21265,
	21355,
	21355,
	21355,
	20667,
	5684,
	9460,
	2420,
	9457,
	20831,
	21265,
	21355,
	21355,
	21355,
	20667,
	5684,
	9460,
	2420,
	9457,
	20831,
	21265,
	21355,
	21355,
	21355,
	20667,
	5684,
	9460,
	2420,
	9457,
	20831,
	21265,
	21355,
	21355,
	21355,
	20667,
	5684,
	9460,
	2420,
	9457,
	20831,
	21265,
	21355,
	21355,
	21355,
	20667,
	5684,
	9460,
	2420,
	9457,
	20831,
	21265,
	21355,
	21355,
	21355,
	20667,
	5684,
	9460,
	2420,
	9457,
	20831,
	21265,
	21355,
	21355,
	21355,
	20667,
	5684,
	9460,
	2420,
	9457,
	20831,
	21265,
	21355,
	21355,
	21355,
	20667,
	5684,
	9460,
	2420,
	9457,
	20831,
	21265,
	21355,
	21355,
	21355,
	20667,
	5684,
	9460,
	2420,
	9457,
	20831,
	21265,
	21355,
	21355,
	21355,
	20667,
	5684,
	9460,
	2420,
	9457,
	20831,
	21265,
	21355,
	21355,
	21355,
	20667,
	5684,
	9460,
	2420,
	9457,
	20831,
	21265,
	21355,
	21355,
	21355,
	20667,
	0,
	0,
	0,
	18506,
	18506,
	0,
	20511,
	20498,
	21355,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	21355,
	20847,
	20847,
	21355,
	21355,
	21355,
	18212,
	20193,
	18949,
	13691,
	13798,
	13623,
	14679,
	14981,
	20193,
	18949,
	13691,
	13798,
	13623,
	14679,
	14984,
	20193,
	18949,
	13691,
	13798,
	13623,
	14679,
	14980,
	20193,
	18949,
	13691,
	13798,
	13623,
	14679,
	14983,
	20193,
	18949,
	13691,
	13798,
	13623,
	14679,
	14982,
	21355,
	20193,
	13691,
	20193,
	13691,
	20193,
	13691,
	20193,
	13691,
	20193,
	13691,
	5684,
	8701,
	2358,
	8845,
	20831,
	21265,
	21355,
	21355,
	21355,
	20193,
	5684,
	137,
	36,
	10682,
	20831,
	21265,
	21355,
	21355,
	21355,
	13691,
	5684,
	8701,
	2358,
	8845,
	20831,
	21265,
	21355,
	21355,
	21355,
	20193,
	5684,
	137,
	36,
	10682,
	20831,
	21265,
	21355,
	21355,
	21355,
	13691,
	5684,
	8701,
	2358,
	8845,
	20831,
	21265,
	21355,
	21355,
	21355,
	20193,
	5684,
	137,
	36,
	10682,
	20831,
	21265,
	21355,
	21355,
	21355,
	13691,
	5684,
	8701,
	2358,
	8845,
	20831,
	21265,
	21355,
	21355,
	21355,
	20193,
	5684,
	137,
	36,
	10682,
	20831,
	21265,
	21355,
	21355,
	21355,
	13691,
	5684,
	8701,
	2358,
	8845,
	20831,
	21265,
	21355,
	21355,
	21355,
	20193,
	5684,
	137,
	36,
	10682,
	20831,
	21265,
	21355,
	21355,
	21355,
	13691,
	0,
	13298,
	13298,
	10629,
	12875,
	4749,
	10415,
	8801,
	9460,
	12815,
	12815,
	12875,
	0,
	0,
	5649,
	13298,
	13298,
	13298,
	21263,
	20840,
	0,
	17109,
	17109,
	20514,
	18554,
	17465,
	18554,
	17465,
	19889,
	19889,
	16872,
	18553,
	19777,
	20831,
	13052,
	13052,
	7736,
	17478,
	17478,
	12996,
	7736,
	7736,
	13052,
	10682,
	13298,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	20847,
	20847,
	20847,
	20847,
	20847,
	20847,
	20847,
	20847,
	20847,
	20847,
	20847,
	20847,
	20847,
	20847,
	20847,
	20847,
	21355,
	20831,
	15670,
	17842,
	16440,
	21355,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	7748,
	5184,
	0,
	15506,
	15724,
	15725,
	18234,
	18067,
	20933,
	21355,
	21355,
	21355,
	15550,
	15550,
	15550,
	15550,
	15550,
	15550,
	15550,
	15550,
	15550,
	15550,
	5684,
	1735,
	341,
	10682,
	20831,
	21265,
	21355,
	21355,
	21355,
	15550,
	5684,
	1735,
	341,
	10682,
	20831,
	21265,
	21355,
	21355,
	21355,
	15550,
	5684,
	1735,
	341,
	10682,
	20831,
	21265,
	21355,
	21355,
	21355,
	15550,
	5684,
	1735,
	341,
	10682,
	20831,
	21265,
	21355,
	21355,
	21355,
	15550,
	5684,
	1735,
	341,
	10682,
	20831,
	21265,
	21355,
	21355,
	21355,
	15550,
	4721,
	4721,
	4721,
	4721,
	4721,
	12996,
	12996,
	5266,
	7736,
	7827,
	12996,
	12996,
	10629,
	10629,
	9469,
	10828,
	6810,
	13298,
	8053,
	7736,
	12996,
	13298,
	0,
	0,
	13298,
	13298,
	18554,
	1831,
	13298,
	10442,
	13298,
	16065,
	21355,
	13298,
	10682,
	12792,
	20496,
	13298,
	14438,
	12994,
	10627,
	8799,
	1920,
	12815,
	10451,
	13298,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	16712,
	16713,
	14657,
	14657,
	14657,
	14657,
	14657,
	14657,
	5684,
	800,
	207,
	10682,
	20831,
	21265,
	21355,
	21355,
	21355,
	14657,
	5684,
	800,
	207,
	10682,
	20831,
	21265,
	21355,
	21355,
	21355,
	14657,
	5684,
	800,
	207,
	10682,
	20831,
	21265,
	21355,
	21355,
	21355,
	14657,
	15752,
	15737,
	15744,
	15746,
	15758,
	15764,
	15770,
	15748,
	15735,
	15750,
	0,
	19039,
	19033,
	19035,
	19036,
	19042,
	19045,
	19048,
	19037,
	19032,
	19038,
	0,
	15739,
	15742,
	15738,
	15741,
	15740,
	15754,
	15760,
	15766,
	15756,
	15762,
	15768,
	21271,
	10682,
	0,
	13052,
	13298,
	12875,
	10506,
	0,
	10506,
	13298,
	0,
	21355,
	12815,
	10674,
	13044,
	13298,
	10682,
	10682,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	21274,
	16873,
	21355,
	21355,
	13298,
	21274,
	13298,
	13298,
	13298,
	21355,
	19844,
	21355,
	20847,
	21274,
	20847,
	21355,
	0,
	0,
	20840,
	21355,
	0,
	0,
	0,
	0,
	12875,
	10506,
	13195,
	13195,
	12875,
	12996,
	12996,
	13195,
	10823,
	13052,
	7727,
	7736,
	12996,
	17466,
	17466,
	21355,
	19889,
	19889,
	20514,
	20294,
	20845,
	19889,
	20845,
	19889,
	17960,
	17960,
	16383,
	17960,
	16383,
	17960,
	0,
	17988,
	20514,
	20292,
	18255,
	16542,
	15526,
	13298,
	13298,
	10629,
	13298,
	12815,
	13052,
	13298,
	13052,
	5649,
	13298,
	21355,
	21274,
	20847,
	21274,
	21274,
	20847,
	12792,
	13260,
	10674,
	10506,
	4752,
	10674,
	9220,
	10415,
	13298,
	21355,
	13298,
	4753,
	10682,
	9222,
	9223,
	4441,
	9222,
	13045,
	9220,
	13298,
	13298,
	4752,
	8838,
	21274,
	20847,
	1832,
	13298,
	12792,
	13052,
	13052,
	12649,
	12875,
	10506,
	12875,
	13298,
	13298,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	7690,
	7736,
	12996,
	7733,
	7736,
	12996,
	21289,
	7753,
	7736,
	12996,
	21333,
	7821,
	7736,
	12996,
	7832,
	7736,
	12996,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	10958,
	5720,
	10682,
	10415,
	13298,
	12815,
	9460,
	4133,
	14484,
	16165,
	14484,
	16508,
	18156,
	18156,
	16497,
	14033,
	13832,
	13962,
	14828,
	21355,
	14484,
	5684,
	792,
	208,
	9457,
	20831,
	21265,
	21355,
	21355,
	21355,
	14484,
	10958,
	5720,
	10682,
	10415,
	13298,
	12815,
	9460,
	1054,
	1054,
	1057,
	1057,
	1053,
	1053,
	1056,
	1056,
	1055,
	1055,
	1692,
	1692,
	1046,
	1046,
	1059,
	1059,
	1618,
	1618,
	1692,
	1692,
	1712,
	1712,
	1716,
	1716,
	1692,
	1692,
	1712,
	1712,
	1716,
	1716,
	1712,
	1712,
	1716,
	1716,
	1718,
	1718,
	1687,
	1687,
	1038,
	1038,
	1690,
	1690,
	21355,
	21355,
	21355,
};
static const Il2CppTokenRangePair s_rgctxIndices[47] = 
{
	{ 0x02000054, { 13, 16 } },
	{ 0x0200006F, { 48, 6 } },
	{ 0x02000075, { 59, 37 } },
	{ 0x0200009E, { 100, 25 } },
	{ 0x020000B1, { 171, 5 } },
	{ 0x020000B2, { 176, 32 } },
	{ 0x020000B4, { 261, 9 } },
	{ 0x020000B8, { 282, 7 } },
	{ 0x020000B9, { 289, 9 } },
	{ 0x020000C7, { 302, 36 } },
	{ 0x020000C8, { 338, 10 } },
	{ 0x020000D1, { 348, 15 } },
	{ 0x020000D2, { 363, 15 } },
	{ 0x020000D4, { 378, 6 } },
	{ 0x06000199, { 0, 3 } },
	{ 0x0600019C, { 3, 10 } },
	{ 0x060001AC, { 29, 1 } },
	{ 0x06000246, { 30, 18 } },
	{ 0x06000252, { 54, 2 } },
	{ 0x06000253, { 56, 2 } },
	{ 0x0600025A, { 58, 1 } },
	{ 0x060002C3, { 96, 4 } },
	{ 0x0600036F, { 125, 7 } },
	{ 0x0600037A, { 132, 22 } },
	{ 0x06000388, { 154, 6 } },
	{ 0x0600038D, { 160, 5 } },
	{ 0x06000390, { 165, 6 } },
	{ 0x060003AB, { 208, 3 } },
	{ 0x060003AC, { 211, 4 } },
	{ 0x060003AD, { 215, 5 } },
	{ 0x060003B2, { 220, 2 } },
	{ 0x060003B3, { 222, 3 } },
	{ 0x060003B4, { 225, 4 } },
	{ 0x060003B7, { 229, 12 } },
	{ 0x060003B8, { 241, 2 } },
	{ 0x060003B9, { 243, 2 } },
	{ 0x060003BA, { 245, 2 } },
	{ 0x060003BB, { 247, 2 } },
	{ 0x060003BC, { 249, 2 } },
	{ 0x060003BD, { 251, 2 } },
	{ 0x060003BE, { 253, 2 } },
	{ 0x060003BF, { 255, 2 } },
	{ 0x060003C0, { 257, 2 } },
	{ 0x060003C1, { 259, 2 } },
	{ 0x060003D5, { 270, 4 } },
	{ 0x060003D6, { 274, 8 } },
	{ 0x060003FB, { 298, 4 } },
};
extern const uint32_t g_rgctx_TU5BU5DU26_t246B6DF1040A397580C131C860B2697744F9A607;
extern const uint32_t g_rgctx_TU5BU5D_tBA3B5E1C54C90C4697A121BE4C93988364789669;
extern const uint32_t g_rgctx_Array_Resize_TisT_tA5BA1D991045FF5EC1C84644E0E2F6016E80DFDF_m8E8639C5617BD37DE73C44FCA5B06CC904C86613;
extern const uint32_t g_rgctx_T_t60D300FC0D8F1AD2ED84A372F7CB18B2829E92F5;
extern const uint32_t g_rgctx_T_t60D300FC0D8F1AD2ED84A372F7CB18B2829E92F5;
extern const uint32_t g_rgctx_UnsafeUtility_As_TisT_t60D300FC0D8F1AD2ED84A372F7CB18B2829E92F5_TisInt32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_m9D99D6B331CAC1AA20CE2E11D4968C61873195EA;
extern const uint32_t g_rgctx_TU26_tB393A59662D3B252A70725EA56085AB212FAE48E;
extern const uint32_t g_rgctx_UnsafeUtility_As_TisBox_1_tE0B3BE351988D69686BD0F6C83AB4F5058DEDB9C_TisBox_1_tABA9158D6F12AC59959EB16951E931419B2FA0B9_mDA71779F497F53B17F87C113A071B5975A18AE86;
extern const uint32_t g_rgctx_Box_1U26_t0BA89BDC48B44311D3FADC5AEEAFA7CECA8B0DC1;
extern const uint32_t g_rgctx_UnsafeUtility_As_TisT_t60D300FC0D8F1AD2ED84A372F7CB18B2829E92F5_TisBoolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_m32150EC8025D5E583A029E9291BBCF7CAA21AF1C;
extern const uint32_t g_rgctx_UnsafeUtility_As_TisBox_1_t389E6FBACC16884E8E4BE1CD8A119E7EC5E2CE16_TisBox_1_tABA9158D6F12AC59959EB16951E931419B2FA0B9_m152281F4F9F58F8F6204C9C50609F15D35F94FD8;
extern const uint32_t g_rgctx_Box_1_tABA9158D6F12AC59959EB16951E931419B2FA0B9;
extern const uint32_t g_rgctx_Box_1__ctor_m13AAB7B53D54C606D300D079F2318E9151CA61DA;
extern const uint32_t g_rgctx_Box_1_t10EA1FD35F12A8287E1430FB50424815C5614E51;
extern const uint32_t g_rgctx_T_tDAA37F07255E0EE930F48DAC6F75EA8717D8B873;
extern const uint32_t g_rgctx_Box_1_t10EA1FD35F12A8287E1430FB50424815C5614E51;
extern const uint32_t g_rgctx_Box_1_PrintMembers_m525FC4FC14E450C7E877FE6CAF146C93D2ED622B;
extern const uint32_t g_rgctx_Box_1_get_Value_m9D384B82FE7EF426C85857B16ADE4A09A25EBA7E;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_tDAA37F07255E0EE930F48DAC6F75EA8717D8B873_Object_ToString_mF8AC1EB9D85AB52EC8FD8B8BDD131E855E69673F;
extern const uint32_t g_rgctx_Box_1_op_Equality_m4E0DEED249007EFCEDB6ED48AE12CF2C8A8AAC20;
extern const uint32_t g_rgctx_Box_1_t10EA1FD35F12A8287E1430FB50424815C5614E51;
extern const uint32_t g_rgctx_Box_1_Equals_m4764EEAD07BA28CD356467CD4BFD4E10553117AB;
extern const uint32_t g_rgctx_Box_1_get_EqualityContract_m33EEEA35094E3FDF4A65B1A04CDFA31633EE2488;
extern const uint32_t g_rgctx_EqualityComparer_1_get_Default_m04652B46C83FFD6D55D80BB25B17A3ABA7C8F045;
extern const uint32_t g_rgctx_EqualityComparer_1_tA17257FDAD2BF7B6A7171FDDCFC124EC3BD08CB5;
extern const uint32_t g_rgctx_EqualityComparer_1_tA17257FDAD2BF7B6A7171FDDCFC124EC3BD08CB5;
extern const uint32_t g_rgctx_EqualityComparer_1_GetHashCode_m794AC68FF6F0171AE7F9E05E994E7DBCA9CFFC40;
extern const uint32_t g_rgctx_EqualityComparer_1_Equals_m1A218E091D0E8CB2B2EFFBE6F6B0B1E31DE05147;
extern const uint32_t g_rgctx_Box_1__ctor_m994D72D5742C3F89D01B28E86E350B0180474018;
extern const uint32_t g_rgctx_T_t75E90C34BC998623C53C1D045B12056B3A1861CE;
extern const uint32_t g_rgctx_UnsafeUtility_As_TisRuntimeObject_TisAction_1_t8CFFE77212518FDC506EE81B34902555F63F3175_m8BCF39451FCC8183136BC240FC2DA7A0C84D9C82;
extern const uint32_t g_rgctx_Action_1U26_tEC78818540223EF3451DDD3F57F804F934F727C2;
extern const uint32_t g_rgctx_TValueU26_tC41EB1C685250544C256511AC50D8B233484F7C1;
extern const uint32_t g_rgctx_TValue_t696A8B67057927C9B7B9DE40DC8086419CA43B8A;
extern const uint32_t g_rgctx_Action_1_t8CFFE77212518FDC506EE81B34902555F63F3175;
extern const uint32_t g_rgctx_Action_1_Invoke_mC9224C36C078C499C5F2056C94C9B417A841B409;
extern const uint32_t g_rgctx_UnsafeUtility_As_TisRuntimeObject_TisAction_2_t994004148B3942BA8BFCA297F9628AD62EF09A9B_m5AEAF76BB0805D2474E7974B52C1F785B8466DEF;
extern const uint32_t g_rgctx_Action_2U26_tE8CE44D87BE40EE658DD983C4D3FF6CD5BD5216C;
extern const uint32_t g_rgctx_Action_2_t994004148B3942BA8BFCA297F9628AD62EF09A9B;
extern const uint32_t g_rgctx_Action_2_Invoke_mBC454D1A9B70572A47D9827C1B0BE42457AA7D0F;
extern const uint32_t g_rgctx_UnsafeUtility_As_TisRuntimeObject_TisAction_3_tAD8DE3ABC9AF2654813970E485DFA7D117960EFA_m760328628879E58F3B837329E10E96CAF6C4C2C6;
extern const uint32_t g_rgctx_Action_3U26_tBED281DA6A4547EA08F57F4B2A85B002FCB8B23D;
extern const uint32_t g_rgctx_Action_3_tAD8DE3ABC9AF2654813970E485DFA7D117960EFA;
extern const uint32_t g_rgctx_Action_3_Invoke_m45B97579AE0748BC25287AF9C62328C3CAAEB2DB;
extern const uint32_t g_rgctx_UnsafeUtility_As_TisRuntimeObject_TisAction_4_tC1A76B09E8AEBE041E7AD6020950BE0C0D8D24A0_m63A4BE41C4DBED6FE22F60D57E07FF59D85F8D57;
extern const uint32_t g_rgctx_Action_4U26_tEE377B34A7BC5467B589F6B7152491A31838C0CB;
extern const uint32_t g_rgctx_Action_4_tC1A76B09E8AEBE041E7AD6020950BE0C0D8D24A0;
extern const uint32_t g_rgctx_Action_4_Invoke_m07EF8A44A62C013BB5EF09E5A61C6C40FBD18CCB;
extern const uint32_t g_rgctx_MotionData_2_t24E3B8A65702EAF4AF499269AD50DE0FFAD2D6C7;
extern const uint32_t g_rgctx_TValueU26_t4D06DB393053A972CDF53AEBDF9CBA640EB679E4;
extern const uint32_t g_rgctx_TValue_t5DB091263F57EFE5D2DB5C224B1A743445F49AEB;
extern const uint32_t g_rgctx_TOptions_tA627D666DAF9C330C5BFC57F47DC451A4C3A78B1;
extern const uint32_t g_rgctx_IMotionAdapter_2_t49F9E324649FE806D677D0982DFF3FF74AD36279;
extern const uint32_t g_rgctx_TOptionsU26_tBD61E54F2DBFBEF6C5D328BF26043709CCF6FEB4;
extern const uint32_t g_rgctx_TAdapter_tCB8BA45C499F086C5B2DEEAE593BA298EEB6C133;
extern const Il2CppRGCTXConstrainedData g_rgctx_TAdapter_tCB8BA45C499F086C5B2DEEAE593BA298EEB6C133_IMotionAdapter_2_Evaluate_m57347361CBD4E951BA54E9B5DE5238D426DF3E00;
extern const uint32_t g_rgctx_TAdapter_t62A030644BB6DBE8CF7C819CCA04FEE8AB1FF52A;
extern const Il2CppRGCTXConstrainedData g_rgctx_TAdapter_t62A030644BB6DBE8CF7C819CCA04FEE8AB1FF52A_IMotionAdapter_2_Evaluate_m57347361CBD4E951BA54E9B5DE5238D426DF3E00;
extern const uint32_t g_rgctx_MotionStorage_3_tE978EAF0BDE8D7311544F95BF45C98352B5DC9AC;
extern const uint32_t g_rgctx_MotionStorage_3_t76C5A9E7B0969FD6C1A916BDAA4828621279DDC6;
extern const uint32_t g_rgctx_MotionData_2U5BU5D_tC2470D62D4137B2724C9185D9E9CBDA0F5793862;
extern const uint32_t g_rgctx_MotionData_2U5BU5D_tC2470D62D4137B2724C9185D9E9CBDA0F5793862;
extern const uint32_t g_rgctx_MemoryExtensions_AsSpan_TisMotionData_2_t88D068F0BF9E991E625D6777D0B47DD8F25143B2_m86A53DE4768657E3E8B8F7613B1666C2D27EC0DF;
extern const uint32_t g_rgctx_Span_1_tB3548232950D0038AE20A8806B41D8245E356486;
extern const uint32_t g_rgctx_ArrayHelper_EnsureCapacity_TisMotionData_2_t88D068F0BF9E991E625D6777D0B47DD8F25143B2_mDC581F751CE1782192EBF9F184054343210D18EB;
extern const uint32_t g_rgctx_MotionData_2U5BU5DU26_tA746CF1A77EA3A81B97B1D6C1201DBBB772C96B2;
extern const uint32_t g_rgctx_MotionStorage_3_EnsureCapacity_m6133D3469E56AC9045B50572C4E9183151759517;
extern const uint32_t g_rgctx_MotionBuilder_3U26_tCA7842717B13DA2C594F428F3B7CC9F920EBB52A;
extern const uint32_t g_rgctx_MotionBuilder_3_t1CB03B115D61E7C1E705F576B919B1CCEAC3FB9B;
extern const uint32_t g_rgctx_MotionBuilderBuffer_2_t11D8E5187256555714BFB33C9D5036735AB9997B;
extern const uint32_t g_rgctx_MotionData_2_t88D068F0BF9E991E625D6777D0B47DD8F25143B2;
extern const uint32_t g_rgctx_TValue_t137D4A4A58FA21D390E1D949B36249A934E636D5;
extern const uint32_t g_rgctx_TOptions_t334A3BEF59CEF84728ED1B7ECD2A0D4DD18DB20E;
extern const uint32_t g_rgctx_TAdapter_tAC08577EF2AD31E1DAB836D8AA8274043F5B5948;
extern const uint32_t g_rgctx_IMotionAdapter_2_t7BCA2F3D4C7032CBB5AE957D132932CB8F4F5A02;
extern const Il2CppRGCTXConstrainedData g_rgctx_TAdapter_tAC08577EF2AD31E1DAB836D8AA8274043F5B5948_IMotionAdapter_2_Evaluate_m9132244D37C8EA43417D157F1205E9E3C62F30C4;
extern const uint32_t g_rgctx_TValueU26_t76AF8D51CDAB540E05A6449E6C9657B402F04567;
extern const uint32_t g_rgctx_TOptionsU26_tA5F562DF7918135F5186926C9592E97242A3395B;
extern const uint32_t g_rgctx_ManagedMotionData_UpdateUnsafe_TisTValue_t137D4A4A58FA21D390E1D949B36249A934E636D5_m663D6B71AD4F03C74D9E3C60A3CD4A23402EFD4E;
extern const uint32_t g_rgctx_MotionStorage_3_get_Id_mD0B128D4B33A3CD02AED1C3A13B290C120AC5C5C;
extern const uint32_t g_rgctx_MotionData_2U26_t15A6FA3889EFA6EE83CD600C06E2F1E04AB5E7C2;
extern const uint32_t g_rgctx_MotionStorage_3_RemoveAt_m3EDD3764D4E0D20E1B1655A23530DAD061BF43BF;
extern const uint32_t g_rgctx_MotionStorage_3_IsDenseIndexOutOfRange_m1589947B65EC9869ADB701071FAC54A17E034285;
extern const uint32_t g_rgctx_MotionStorage_3_IsInvalidVersion_mFC0D3AC58F3534E98AC71D046F8BA81156A82189;
extern const uint32_t g_rgctx_MotionStorage_3_t76C5A9E7B0969FD6C1A916BDAA4828621279DDC6;
extern const uint32_t g_rgctx_MotionStorage_3_TryCancelCore_m3A8EA16052867888F5B836711C70A9345DE7143D;
extern const uint32_t g_rgctx_MotionStorage_3_TryCompleteCore_mA2D020599DBBBE1E14FFD05DBD558B5DA74242AA;
extern const uint32_t g_rgctx_MotionData_2_Complete_TisTAdapter_tAC08577EF2AD31E1DAB836D8AA8274043F5B5948_m05BF9E489D8AC86C63EC04C7DC9C589B70DAA589;
extern const uint32_t g_rgctx_MotionData_2_t88D068F0BF9E991E625D6777D0B47DD8F25143B2;
extern const uint32_t g_rgctx_MotionData_2_Update_TisTAdapter_tAC08577EF2AD31E1DAB836D8AA8274043F5B5948_mF7095880AF0CEAA0C111FC2C24C057850418DB96;
extern const uint32_t g_rgctx_MotionData_2U2A_t003928D8AA9B1C8E4FE8FB966267D9F28DBE4CFB;
extern const uint32_t g_rgctx_MotionData_2U5BU5D_tC2470D62D4137B2724C9185D9E9CBDA0F5793862;
extern const uint32_t g_rgctx_MotionStorage_3_GetSlotWithVarify_m9B46AB7E6687C294319D1FCAA027E7B80193DFEF;
extern const uint32_t g_rgctx_UnsafeUtility_As_TisMotionData_2_t88D068F0BF9E991E625D6777D0B47DD8F25143B2_TisMotionData_tA820A6BD10DEA3326A5D51B503754F003CE7D399_m1D95FD4820EE7779B5FD7E3F73B9043864818D7F;
extern const uint32_t g_rgctx_Span_1_Clear_m965EF961FA0A3447A873700189889BCFCADB9DCF;
extern const uint32_t g_rgctx_Span_1_tB3548232950D0038AE20A8806B41D8245E356486;
extern const uint32_t g_rgctx_MotionBuilder_3U26_t3D78D23260BAA37193E25D4815BB508125F4C29D;
extern const uint32_t g_rgctx_MotionBuilder_3_t3E3267A1727C3A2025A8DCA1E34AFA4F98A91BEE;
extern const uint32_t g_rgctx_MotionBuilderBuffer_2_tF074201FC37F16D7AABEE68F099BFEA2E0076575;
extern const uint32_t g_rgctx_MotionDispatcher_Schedule_TisTValue_t317FC7C090A0FF942895CDA0B712A1811BF43A6B_TisTOptions_t31A18381174B8E08002F6128ED8C0543F2109B25_TisTAdapter_t943ED92EAB85AC67E923ED0580F25F718521CF3C_m79557F8343770DB77950343D4690246A469BC27B;
extern const uint32_t g_rgctx_MotionStorage_3_t33058A65F5AE7133E0415E82B8B55B4110F947DD;
extern const uint32_t g_rgctx_UpdateRunner_3_tD7F388D3415B5396E5B3196774ABBC874C11A85B;
extern const uint32_t g_rgctx_MotionStorage_3_get_Count_mA9688AB0B886EACE1466FBBE87D9EB1B37C70701;
extern const uint32_t g_rgctx_NativeArray_1__ctor_m906D68D33017796D2EA5CFF99A936EDFF5B50E61;
extern const uint32_t g_rgctx_NativeArray_1_t7B18748E158D4D288DFD3649013C59B59E7D09A9;
extern const uint32_t g_rgctx_MotionStorage_3_GetDataSpan_m48762A262CF69492C8EF824DCFC008F1F9D9AD12;
extern const uint32_t g_rgctx_Span_1_t764DB703C7C07B0CF1D3A0106C2CA25C004773AF;
extern const uint32_t g_rgctx_Span_1_GetPinnableReference_mC3874BA61228F472C81945E09379AE913C4A6820;
extern const uint32_t g_rgctx_Span_1_t764DB703C7C07B0CF1D3A0106C2CA25C004773AF;
extern const uint32_t g_rgctx_MotionData_2U26_t2E2EC5D0562D0987695984CE74BBDF56770E5389;
extern const uint32_t g_rgctx_MotionUpdateJob_3_t9AAC77599EDBA12B675B899F2F59BC7EDE4A1690;
extern const uint32_t g_rgctx_MotionData_2U2A_tF847BF6EEE418D61486F47CA5D97120AA8D563E2;
extern const uint32_t g_rgctx_NativeArray_1_t7B18748E158D4D288DFD3649013C59B59E7D09A9;
extern const uint32_t g_rgctx_IJobParallelForExtensions_Schedule_TisMotionUpdateJob_3_t9AAC77599EDBA12B675B899F2F59BC7EDE4A1690_mABDA500936FBAE8DF5DD8E5CC91B1E20BB59BC03;
extern const uint32_t g_rgctx_MotionStorage_3_GetManagedDataSpan_m622BCADD6CB92367124A3FF69395AB0194D42085;
extern const uint32_t g_rgctx_NativeArrayUnsafeUtility_GetUnsafePtr_TisTValue_t18BA5193C0D831097546BD9B7D61BCF523A27554_m9B599F0EDAE136D4CCFA673EA291EA2DA8A14D23;
extern const uint32_t g_rgctx_MotionData_2_t77A99471D622BCFFE8E82688738CC5C30464485D;
extern const uint32_t g_rgctx_TValue_t18BA5193C0D831097546BD9B7D61BCF523A27554;
extern const uint32_t g_rgctx_ManagedMotionData_UpdateUnsafe_TisTValue_t18BA5193C0D831097546BD9B7D61BCF523A27554_m9EA9ADB4F488A2E13CF67C7E1932AC2C2AEEFCDA;
extern const uint32_t g_rgctx_TValueU26_tE15F01D69303187DAF6FFCB70BEA4514852AC9C6;
extern const uint32_t g_rgctx_MotionStorage_3_RemoveAll_m65FE1F9C5F77C79C6922A8EAEDB1598AAB6E6BDA;
extern const Il2CppRGCTXConstrainedData g_rgctx_NativeArray_1_t7B18748E158D4D288DFD3649013C59B59E7D09A9_IDisposable_Dispose_m3C902735BE731EE30AC1185E7AEF6ACE7A9D9CC7;
extern const uint32_t g_rgctx_MotionData_2U26_t2E2EC5D0562D0987695984CE74BBDF56770E5389;
extern const uint32_t g_rgctx_TValueU2A_t47A224A9B1D42686B0D9FDE3B393A696C6A56445;
extern const uint32_t g_rgctx_MotionStorage_3_Reset_mBF7AC6AF337F17EB119922312654FCEB5D19B908;
extern const uint32_t g_rgctx_MotionBuilderBuffer_2_Rent_m03C64A25582813C5F05F40EEABA514E1E9B2052E;
extern const uint32_t g_rgctx_MotionBuilderBuffer_2_t8225B819A97A0AD1C8518806780A5A924E49015B;
extern const uint32_t g_rgctx_MotionBuilderBuffer_2_t8225B819A97A0AD1C8518806780A5A924E49015B;
extern const uint32_t g_rgctx_TValueU26_t9C22A69D921C34F97FF35F037298B400C008C2F8;
extern const uint32_t g_rgctx_TValue_t01064EEF66279658D57126D6ED3348BAD3C911F5;
extern const uint32_t g_rgctx_MotionBuilder_3_tAAC26859FB33A51F00C16EF791ACD3BFC5DBD149;
extern const uint32_t g_rgctx_MotionBuilder_3__ctor_m5AC7E1F4544FB1922EF28D3A9DA1EA864AA2164F;
extern const uint32_t g_rgctx_MotionBuilderBuffer_2_Rent_mAA09AC1969854ACA565D4271A33806A4AB29A4CD;
extern const uint32_t g_rgctx_MotionBuilderBuffer_2_t8FAED72C63998391D0BBB7A10058BF556839AD51;
extern const uint32_t g_rgctx_MotionBuilderBuffer_2_t8FAED72C63998391D0BBB7A10058BF556839AD51;
extern const uint32_t g_rgctx_MotionSettings_2_tC39109B1C764A14B3582EB4038AEE757A03FEBE0;
extern const uint32_t g_rgctx_MotionSettings_2_get_StartValue_mB6A8F9397DE97210244903466BFD5230ACE98CAB;
extern const uint32_t g_rgctx_TValue_tCEF93DEEDF70B13C5BC51B5783A112D0F9153EA7;
extern const uint32_t g_rgctx_MotionSettings_2_get_EndValue_mEE8888C850E3D94558D9C09FE84085826444869A;
extern const uint32_t g_rgctx_MotionSettings_2_get_Duration_mF385027D54C0E66894FC355EF96CFB97F701FC93;
extern const uint32_t g_rgctx_MotionSettings_2_get_Options_mBF36EDF7A0DB8C5AB8C9EFA61A9374FE3E2350B7;
extern const uint32_t g_rgctx_TOptions_tE15C88D1F53253E2AC1BFF3DCDB6A88E5DEF1CE7;
extern const uint32_t g_rgctx_MotionSettings_2_get_Ease_mF58A1AF95A34770B80608BEB6D5D1A0677E364CD;
extern const uint32_t g_rgctx_MotionSettings_2_get_CustomEaseCurve_m197E23100EBAB706A7305DB65E9F347C9F9CDC71;
extern const uint32_t g_rgctx_MotionSettings_2_get_Delay_mC0164A850167FDF3357918044B8FBFD90DF4B129;
extern const uint32_t g_rgctx_MotionSettings_2_get_DelayType_m4264B44278B927D06014340C92F60C2F5640F0D7;
extern const uint32_t g_rgctx_MotionSettings_2_get_Loops_mE21A6C4927BA4D1DC3AFB88C6674842EE9355906;
extern const uint32_t g_rgctx_MotionSettings_2_get_LoopType_mD0054C596D23D7A6C7C08144DCE49944E6D3051D;
extern const uint32_t g_rgctx_MotionSettings_2_get_CancelOnError_m28B8CA10BE64C822969CFDE74063B17E9BBE9B44;
extern const uint32_t g_rgctx_MotionSettings_2_get_SkipValuesDuringDelay_mA41A19EF4AF89EA5CF482DC30766D22F57472A9D;
extern const uint32_t g_rgctx_MotionSettings_2_get_ImmediateBind_mC566FA037B58DD91D879B8A0A2D6CCB0FD57131A;
extern const uint32_t g_rgctx_MotionSettings_2_get_Scheduler_mB15A16459B2DD02A84C0981362251C800464E99F;
extern const uint32_t g_rgctx_MotionBuilder_3_tE9F995E4A376BE65AF7D7B50E661FA318C3EEB84;
extern const uint32_t g_rgctx_MotionBuilder_3__ctor_mE28EF14A5B86C1ADE6D76C0014D6079CF3A45045;
extern const uint32_t g_rgctx_ManualMotionDispatcher_GetOrCreateRunner_TisTValue_t9216B5CEF65406961E29E9312F86678F9079FF2C_TisTOptions_t5D3752881CB637460CF482C4BB235120BB57FE65_TisTAdapter_t67A1D9EBE6127BC102FDBBBE69708FCA746848B9_m878B8B560080D75B3A096C56DB378D1CED84A1E7;
extern const uint32_t g_rgctx_UpdateRunner_3_t8233BAD64D4E73290A2FF1C64695344F23F94A86;
extern const uint32_t g_rgctx_UpdateRunner_3_get_Storage_mC8DAB3CC1ADC167FEBE79C2F0A4D35568DD84643;
extern const uint32_t g_rgctx_MotionStorage_3_t39FFDFA6E6BB263F1355C36F900FB40CE96C549E;
extern const uint32_t g_rgctx_MotionBuilder_3U26_tD2F0233375B18D68D3D657086A0E225A4AF6E2D7;
extern const uint32_t g_rgctx_MotionStorage_3_Create_m2BD44A72B76B127CC9E23DA372D94F26A8D55696;
extern const uint32_t g_rgctx_ManualMotionDispatcher_GetOrCreateRunner_TisTValue_t394A4DA14B2BD540EFF3369227282CBBC7935765_TisTOptions_t43E3C6F7DF19CF6C5D2F3A12DBCD6F8B2C723173_TisTAdapter_t7D27F0B5F78D03C8B4B197089D0A3F40AD0ACF02_m33FB59F3BC01FB3F2798804F573A8A76031C71B8;
extern const uint32_t g_rgctx_UpdateRunner_3_t5494EC0E24313D693430CEFB49696708F150F58E;
extern const uint32_t g_rgctx_UpdateRunner_3_get_Storage_mAD2AEB7E1DD4C2B7A4890ADE8F84900B1CBF8BEA;
extern const uint32_t g_rgctx_MotionStorage_3_t12328989A7405AFC28DE86478584A2DEC8858824;
extern const uint32_t g_rgctx_MotionStorage_3_EnsureCapacity_mDDDC7C585DA5E9C1DE7A3CF513F5C86673329444;
extern const uint32_t g_rgctx_ValueTuple_3_tE2E021ADD1C0967EDE9ED03F003A1F34DF379A54;
extern const uint32_t g_rgctx_MotionStorage_3_tCB028FC3B2B83C30DF7D55B84FA81F9CEB4DFF52;
extern const uint32_t g_rgctx_MotionStorage_3__ctor_m646B487C679D54ED872121B99CA5948709BA757C;
extern const uint32_t g_rgctx_MotionManager_Register_TisTValue_t872866ACEB6F566A28D3F5C26E6611C10AD7B7EC_TisTOptions_tE1214E1A34EFD75B7C29A699DE2C41C8BA0C6C8A_TisTAdapter_tE7E3B5FDBC85E7233532135C31CDC4E4B58016D9_m41AECEDF6D3319598FF75C3042718F4596DD277B;
extern const uint32_t g_rgctx_UpdateRunner_3_t0B7CA27870E6B50608F0AD4386D6B472AA013E44;
extern const uint32_t g_rgctx_UpdateRunner_3__ctor_mC59AB2878B166EB3DABF0C87314DACCF924DC59A;
extern const uint32_t g_rgctx_MotionBuilderBuffer_2_t563E945503317148ED5A6647716AFE4F6F53906B;
extern const uint32_t g_rgctx_MotionBuilderBuffer_2_t563E945503317148ED5A6647716AFE4F6F53906B;
extern const uint32_t g_rgctx_MotionBuilderBuffer_2__ctor_m0081588D56A2DEBCCFB02C167E26CAAD6815BC28;
extern const uint32_t g_rgctx_TValue_tDA548C81049EA5B413C1485E73F9AADB7300DE41;
extern const uint32_t g_rgctx_TOptions_tE94C02533EE055456E4C179A64137FC51EBE9D05;
extern const uint32_t g_rgctx_MotionBuilderBuffer_2_tDFBA431286F7981B910F1DBCFAD53944153E11E4;
extern const uint32_t g_rgctx_MotionBuilder_3_t3B668D1CFD4A196527E63367B1329C977F309220;
extern const uint32_t g_rgctx_MotionBuilder_3_CheckEaseType_m86EFD4CCF2DFFAA1E4C30F8E101EF9DF3ADF91AE;
extern const uint32_t g_rgctx_MotionBuilder_3_t3B668D1CFD4A196527E63367B1329C977F309220;
extern const uint32_t g_rgctx_MotionBuilder_3_CheckBuffer_mA9AB7CF7175368E9C439326AD259CA025D7F68D1;
extern const uint32_t g_rgctx_TOptions_t0262A3541C70FBFDD6DDA1755B36A9F1FA7088C6;
extern const uint32_t g_rgctx_MotionBuilder_3_ScheduleMotion_m7564037E6815C53D0E7682E58642181A6156ADEA;
extern const uint32_t g_rgctx_Action_1_t03E06CC34AA77E343E271F41E785D197DBD439F3;
extern const uint32_t g_rgctx_MotionBuilder_3_SetCallbackData_m6A9E594398AC2C7185BE034E9BCADC8D7BA629CB;
extern const uint32_t g_rgctx_MotionSettings_2_t248A6D461159790B0133EECA29CE2A53A733202F;
extern const uint32_t g_rgctx_MotionSettings_2__ctor_m2AE121D6A75FD758AE4D6C6954FB227F1B89FF27;
extern const uint32_t g_rgctx_TValue_tC6FBFB87FABE9A0591050D55A71FACC6D35B874F;
extern const uint32_t g_rgctx_MotionSettings_2_set_StartValue_m9DDC97D31B68208E2A48E7545DADA9A37CE10D5E;
extern const uint32_t g_rgctx_MotionSettings_2_set_EndValue_mB290F08AC8E32A6D9A12C58EF22C1FA6DA136B38;
extern const uint32_t g_rgctx_MotionSettings_2_set_Duration_mD6CD85EC0019278075D67F13AD4B09A79A4D81B0;
extern const uint32_t g_rgctx_MotionSettings_2_set_Options_m66F426E51E9A25B8CD78ACF021278BED1F3D0D01;
extern const uint32_t g_rgctx_MotionSettings_2_set_Ease_mB3448D43D99E9B0A099E7B67A25FD6A9578E4547;
extern const uint32_t g_rgctx_MotionSettings_2_set_CustomEaseCurve_m1D8FD5D76FC7FA597675014296FA47DB360D68D0;
extern const uint32_t g_rgctx_MotionSettings_2_set_Delay_m89D2B3002BEDFBF1395F6C2017CD30214BBA067C;
extern const uint32_t g_rgctx_MotionSettings_2_set_DelayType_mCECE9C0F71A40DE0A5763655132BCCD5875436C4;
extern const uint32_t g_rgctx_MotionSettings_2_set_Loops_mBD88A5A129BDD5C9375F8BB9E749D35FD3FA4F5E;
extern const uint32_t g_rgctx_MotionSettings_2_set_LoopType_m97D2237A3C900736DA3D7A9B7783910D898BE1ED;
extern const uint32_t g_rgctx_MotionSettings_2_set_CancelOnError_m6069F9194098D8B3CA8CF936C21EDFDE3617F4D7;
extern const uint32_t g_rgctx_MotionSettings_2_set_SkipValuesDuringDelay_mD9FD3FACAB8298EF387E3DF49B85302E6A271475;
extern const uint32_t g_rgctx_MotionSettings_2_set_ImmediateBind_m17F9B4B7A0226E0682C0733F20BDE56D5228512F;
extern const uint32_t g_rgctx_MotionSettings_2_set_Scheduler_mE7B3B2AF4E47034755F97485BA51D90988ACF1B1;
extern const uint32_t g_rgctx_MotionDispatcher_Schedule_TisTValue_tC6FBFB87FABE9A0591050D55A71FACC6D35B874F_TisTOptions_t0262A3541C70FBFDD6DDA1755B36A9F1FA7088C6_TisTAdapter_t546391DCDEF282613CF80FC035F68F6E986D7269_m56D42F6B548913CD252504E2F88DFFF1C0AD8079;
extern const uint32_t g_rgctx_MotionBuilder_3U26_tBAE15FC4E205BE22FFA0611FB325098188F7B14F;
extern const uint32_t g_rgctx_IMotionScheduler_Schedule_TisTValue_tC6FBFB87FABE9A0591050D55A71FACC6D35B874F_TisTOptions_t0262A3541C70FBFDD6DDA1755B36A9F1FA7088C6_TisTAdapter_t546391DCDEF282613CF80FC035F68F6E986D7269_m2D18DD6AA59242D317C760ABBC137BA410557FC7;
extern const uint32_t g_rgctx_MotionBuilder_3_Dispose_mD48B9998A9DD75936AE6D3D119F2791FC32A58F7;
extern const uint32_t g_rgctx_MotionBuilderBuffer_2_Return_m115E6023F72DACBD3325E28E00185E2A58B811BE;
extern const uint32_t g_rgctx_MotionBuilderBuffer_2_tDFBA431286F7981B910F1DBCFAD53944153E11E4;
extern const uint32_t g_rgctx_TState_t7BEE902212D714B987621FF68AAB593876CFA77D;
extern const uint32_t g_rgctx_Action_2_t8F4245804B3DEE583B16E89E77BBAF081616464B;
extern const uint32_t g_rgctx_MotionBuilder_3_SetCallbackData_TisTState_t7BEE902212D714B987621FF68AAB593876CFA77D_m593AD4DA76B41BAB537828FF2552D3FC37955E86;
extern const uint32_t g_rgctx_TState0_tAF23D0A5CA498DAFD5E84411B9EC7854DEC609EB;
extern const uint32_t g_rgctx_TState1_t10E024BF5A5F78FF13E05DEB21384627FA768D2B;
extern const uint32_t g_rgctx_Action_3_t438E82AD4D37B29E12426F518A223381C4DC65E6;
extern const uint32_t g_rgctx_MotionBuilder_3_SetCallbackData_TisTState0_tAF23D0A5CA498DAFD5E84411B9EC7854DEC609EB_TisTState1_t10E024BF5A5F78FF13E05DEB21384627FA768D2B_m0888C00FF21EFEA5C37317E00916F40DC1DBDEAE;
extern const uint32_t g_rgctx_TState0_tD0A690A78074F627A9B75C0E232F25CE59E5D9A9;
extern const uint32_t g_rgctx_TState1_tBBE47C6896AC59FEC7A3D880A364F63D30D04D96;
extern const uint32_t g_rgctx_TState2_t6143DD2D434A019B86E991C819E77A2FB797F363;
extern const uint32_t g_rgctx_Action_4_tAB546CFD92C21B39BBCE604D13265C346C9AFABD;
extern const uint32_t g_rgctx_MotionBuilder_3_SetCallbackData_TisTState0_tD0A690A78074F627A9B75C0E232F25CE59E5D9A9_TisTState1_tBBE47C6896AC59FEC7A3D880A364F63D30D04D96_TisTState2_t6143DD2D434A019B86E991C819E77A2FB797F363_m049A22A776426FC8FDB749BC51868DC9A2EC855B;
extern const uint32_t g_rgctx_TState_t1A4DD74565CBFCE8CBD9D31DE2C36107C630F2A5;
extern const uint32_t g_rgctx_Action_2_t7A390EB76C0C8AEB58E13BAD38B6F594EB981515;
extern const uint32_t g_rgctx_TState0_t4365811C40E16C3E275DBCBA20E6A8AD380E68B5;
extern const uint32_t g_rgctx_TState1_t9977CE02BC596E09ACF633732FE21878E399A7C6;
extern const uint32_t g_rgctx_Action_3_t2F9E32955519899D9F16A9B7C8C3605E72D5AB5C;
extern const uint32_t g_rgctx_TState0_t92AF529295CCC3646E5C4D8428CB5E401B05172B;
extern const uint32_t g_rgctx_TState1_t6117A73DAB1799BA23DE939DD1797DAC3F87AAA8;
extern const uint32_t g_rgctx_TState2_t0BD34D0ECD5211C299DEAFB9D6C2723E7F1B8434;
extern const uint32_t g_rgctx_Action_4_t9FEEBDE92B5AB9B33272EA2202F33B0E1BC6F3F5;
extern const uint32_t g_rgctx_MotionBuilder_3_t631B9A8191991AE37B521463DE2A4B50DE54CAB8;
extern const uint32_t g_rgctx_TState_tD7CA9D11216A5B060F2FDEB7AE401A1D405A9A2C;
extern const uint32_t g_rgctx_Box_Create_TisTState_tD7CA9D11216A5B060F2FDEB7AE401A1D405A9A2C_mB9279EDD349BA89C8E377BA5053CD1A977083D57;
extern const uint32_t g_rgctx_Box_1_t90F0BDEDDD772D99A4E785BF1F999CC401411E84;
extern const uint32_t g_rgctx_Action_2_tBE10CE10C695335C1A64BD882239055D80297CCE;
extern const uint32_t g_rgctx_U3CU3Ec__0_4_t45E701CBB49D4F4877C779B16DB9F466B6147362;
extern const uint32_t g_rgctx_Action_3_t00894A0BFE843ABA10B31A8ACDE7F71EF4FA8911;
extern const uint32_t g_rgctx_U3CU3Ec__0_4_t45E701CBB49D4F4877C779B16DB9F466B6147362;
extern const uint32_t g_rgctx_U3CU3Ec__0_4_U3CBindU3Eb__0_0_mB533DA22D20DF6FABA89A85AACFCCE0B9EF33A9C;
extern const uint32_t g_rgctx_Action_3__ctor_m7E984667278F33CC87CC9AED1D91D6B81F11AF7A;
extern const uint32_t g_rgctx_MotionBuilder_3_Bind_TisBox_1_t90F0BDEDDD772D99A4E785BF1F999CC401411E84_TisAction_2_tBE10CE10C695335C1A64BD882239055D80297CCE_m58BB091DED524DE95A6918FE37353BBED50BCA21;
extern const uint32_t g_rgctx_MotionBuilder_3_t631B9A8191991AE37B521463DE2A4B50DE54CAB8;
extern const uint32_t g_rgctx_MotionBuilder_3_t15B6CCDFC528C373EC054A2141E3D998778EF9E6;
extern const uint32_t g_rgctx_MotionBuilderBuffer_2_t656FAE292B85EA84EA0BE7E06281D469D6C565A5;
extern const uint32_t g_rgctx_MotionBuilder_3_t21437EC561F795504385F83181EBBBD8BA58C148;
extern const uint32_t g_rgctx_MotionBuilderBuffer_2_t260CA1A3476E5A25CCC657F07525D66EDFCF3FFD;
extern const uint32_t g_rgctx_MotionBuilder_3_tBD986E4D8C6149BD74CC83A509FC6CBA97FE3196;
extern const uint32_t g_rgctx_MotionBuilderBuffer_2_t6B0CF5D50002A230F8CD024FE63528E2E4049431;
extern const uint32_t g_rgctx_MotionBuilder_3_t77B15752B05F003965B094202DD9A12C21BA561F;
extern const uint32_t g_rgctx_MotionBuilderBuffer_2_t5F4A89E16D6F1E6B7AC6336E772C768AA0475DCF;
extern const uint32_t g_rgctx_MotionBuilder_3_t698BD3421F14261FA43EF6DB839C5C7103E6C404;
extern const uint32_t g_rgctx_MotionBuilderBuffer_2_t011CC8460EA8D86BE3689E15CA7ACBF0C7907FF7;
extern const uint32_t g_rgctx_MotionBuilder_3_tDA630F21301093A8EC7DF8BC3CAEDCCC2F7A849E;
extern const uint32_t g_rgctx_MotionBuilderBuffer_2_t2F45206F1B4848E8035EEF882D62068415910776;
extern const uint32_t g_rgctx_MotionBuilder_3_t0094B3660054CBCC2B45A4F1DB126FA83C569D23;
extern const uint32_t g_rgctx_MotionBuilderBuffer_2_t2DB125088E017D7C5F884282D6F66E4ED5C85D4B;
extern const uint32_t g_rgctx_MotionBuilder_3_t4F3B337A52DE845CC8E2803FB6EE1A3B8088A7B7;
extern const uint32_t g_rgctx_MotionBuilderBuffer_2_t13E53A4749210D50C6B809BEC02030D2C73E9BA5;
extern const uint32_t g_rgctx_MotionBuilder_3_tE37FF25C9B19B713EE10979AF325B5729053A9BB;
extern const uint32_t g_rgctx_MotionBuilderBuffer_2_tB9BC3806D381B896C270A9D0C53E2B49B97A613D;
extern const uint32_t g_rgctx_MotionBuilder_3_t1ACDE836D480A5CABB632BB01CFDC1B200A4CF10;
extern const uint32_t g_rgctx_MotionBuilderBuffer_2_t09411546F24D05906B04AF52BE1E2DC80A550E5B;
extern const uint32_t g_rgctx_U3CU3Ec__0_4_t25FF19A1400EAC2DC1C909F88665B697B3EDD403;
extern const uint32_t g_rgctx_U3CU3Ec__0_4__ctor_m67754364E459D6A89C830E9062A74C3F31EA0F53;
extern const uint32_t g_rgctx_U3CU3Ec__0_4_t25FF19A1400EAC2DC1C909F88665B697B3EDD403;
extern const uint32_t g_rgctx_Action_2_tD5AFD65A867E1FD070956EF076B5D264A4377786;
extern const uint32_t g_rgctx_TValue_t58DB69397872FF375D2FCDBEDDFE65DC9DD327E6;
extern const uint32_t g_rgctx_Box_1_t4E445DD3794823FA945D5607C59BB0FE82A38358;
extern const uint32_t g_rgctx_Box_1_get_Value_m9E7E186E728B9934A4416E86276D161A440887CB;
extern const uint32_t g_rgctx_TState_tF7DFAD173D29B2D0F7951F62E7099008637DE882;
extern const uint32_t g_rgctx_Action_2_Invoke_m0932E6E11EC2431C22794322C6F4F744E9CAA900;
extern const uint32_t g_rgctx_StorageCache_3_GetOrCreate_mC07EFD7FA515F0BDE025E7E77FFEF025EA73E0C8;
extern const uint32_t g_rgctx_StorageCache_3_tC6C3B6CC34516F722EDC4F3235F691AC3B8A176E;
extern const uint32_t g_rgctx_MotionStorage_3_tAFD95A9A2BC68B1F2E8A62D428888ECC92C471F1;
extern const uint32_t g_rgctx_MotionStorage_3_EnsureCapacity_m7941FFA9AA1B6CE7FCCDBAD76033B0B7957F76F5;
extern const uint32_t g_rgctx_StorageCache_3_GetOrCreate_mF4E5BF02A9232850FC0B3452E203ED5B46972A54;
extern const uint32_t g_rgctx_StorageCache_3_tE5907C47EA6FDE016D494EA7E6B9A70656FC6F52;
extern const uint32_t g_rgctx_MotionStorage_3_t0487965E2BC852F653D9925BC879B9A5B23B80E5;
extern const uint32_t g_rgctx_RunnerCache_3_GetOrCreate_mDE57F181CFB71E212F675067F945CB8DC7C9AA29;
extern const uint32_t g_rgctx_RunnerCache_3_tE94FDBB49E1771CFD2D85BEF468775B96795CC8C;
extern const uint32_t g_rgctx_ValueTuple_2_tBB2089AF102E52F8FB7F272095684FF716B79579;
extern const uint32_t g_rgctx_MotionBuilder_3U26_t725E3100355F107CAF9230A27E8197D97E2B77BC;
extern const uint32_t g_rgctx_MotionStorage_3_Create_m0A38A5CBCFA0CACADCEC5983275349E1CC07014B;
extern const uint32_t g_rgctx_StorageCache_3_tFBCF967DB7361C8F9757951C14E04A007692F321;
extern const uint32_t g_rgctx_MotionStorage_3_tA2E42D19EE4A5A0EA5EE414C1A72FA8B9B1F83E6;
extern const uint32_t g_rgctx_StorageCache_3_tFBCF967DB7361C8F9757951C14E04A007692F321;
extern const uint32_t g_rgctx_StorageCache_3_CreateIfNull_m98054427AA82D4625053D307264C01AC1E7B7568;
extern const uint32_t g_rgctx_MotionStorage_3U26_t1417711E768396069A71BF5BA20B4AEBCBBAC931;
extern const uint32_t g_rgctx_MotionStorage_3__ctor_m5CA39C380A20CB160449899E3B37DDCCBECE20B3;
extern const uint32_t g_rgctx_MotionManager_Register_TisTValue_t0AB293BAF36DA03CF03407CEB8C37909F824C703_TisTOptions_t0E3E96015F2FCA2C77D8A54BF9823FE6ED879A92_TisTAdapter_tD95F3E7D36DD6A9F3B0E2C93880B31B3F3D4448D_mE31DF48FE0ECDCA3D4F97B31A059F622165E2820;
extern const uint32_t g_rgctx_RunnerCache_3_t18593FBA8994C513AB65340838129BCB09765D5B;
extern const uint32_t g_rgctx_UpdateRunner_3_t497E6864F8BBAE12192E3600CEDF333094AC101B;
extern const uint32_t g_rgctx_RunnerCache_3_t18593FBA8994C513AB65340838129BCB09765D5B;
extern const uint32_t g_rgctx_MotionStorage_3_t80A5DB68A214178D198B653D425800E8992E90E6;
extern const uint32_t g_rgctx_RunnerCache_3_CreateIfNull_mD2C279C5F6F6252973651070CD51620A13A5D3BD;
extern const uint32_t g_rgctx_UpdateRunner_3U26_t3455DFD36BF3B43B331ABC1D125CFA1D7338289F;
extern const uint32_t g_rgctx_ValueTuple_2_t565B12E22CA6735840754940CF93CFB547631A03;
extern const uint32_t g_rgctx_UpdateRunner_3__ctor_mE8B3219E0505F7AB2FD82A85B92C92C9C9F9D842;
extern const uint32_t g_rgctx_ValueTuple_2__ctor_mBE2CC3BCAF288729B9681615B7A6E17A03CA6E27;
extern const uint32_t g_rgctx_GameObject_TryGetComponent_TisTComponent_tB64ECA36B24587EBD45A3AB56A9C2AF4BAA78824_mFF67E4458FBF5977D969D85C04F1E30B8380B7E2;
extern const uint32_t g_rgctx_TComponentU26_t160C0D4684B0E4D67B7E99C0578A088A160B1F26;
extern const uint32_t g_rgctx_GameObject_AddComponent_TisTComponent_tB64ECA36B24587EBD45A3AB56A9C2AF4BAA78824_m8CE05521D0F7B4B2E1E50EC1FC86B8F73A0A8F43;
extern const uint32_t g_rgctx_TComponent_tB64ECA36B24587EBD45A3AB56A9C2AF4BAA78824;
extern const uint32_t g_rgctx_MotionSettings_2_tE365165659234016D9886FA392F2586107B4031C;
extern const uint32_t g_rgctx_MotionSettings_2_tE365165659234016D9886FA392F2586107B4031C;
extern const uint32_t g_rgctx_TValue_tEE83F5BAD64E26F73726BBA9D7D6BE5D9935191D;
extern const uint32_t g_rgctx_TOptions_tB47AB4A64506B96A709374B49D6516C454A68096;
extern const uint32_t g_rgctx_MotionSettings_2_PrintMembers_m1B4411F24EE2A38713CB76A8E7AAF58F0C882496;
extern const uint32_t g_rgctx_MotionSettings_2_get_StartValue_mA7AE064CAD8E8B7070A34A71FF31FCCA47E0FF12;
extern const Il2CppRGCTXConstrainedData g_rgctx_TValue_tEE83F5BAD64E26F73726BBA9D7D6BE5D9935191D_Object_ToString_mF8AC1EB9D85AB52EC8FD8B8BDD131E855E69673F;
extern const uint32_t g_rgctx_MotionSettings_2_get_EndValue_mAAFAB593452997722D9D55C9732F99B44A3F98C2;
extern const uint32_t g_rgctx_MotionSettings_2_get_Duration_m452CB0750323D4E1A6BA9D616C6BAC4B68DCEBBC;
extern const uint32_t g_rgctx_MotionSettings_2_get_Options_m7E81D98083862B00C0329FDB67370AA80E848DB9;
extern const Il2CppRGCTXConstrainedData g_rgctx_TOptions_tB47AB4A64506B96A709374B49D6516C454A68096_Object_ToString_mF8AC1EB9D85AB52EC8FD8B8BDD131E855E69673F;
extern const uint32_t g_rgctx_MotionSettings_2_get_Ease_m5D6E591218C861F31E3A6DB6793D49AE6C6E6D66;
extern const uint32_t g_rgctx_MotionSettings_2_get_CustomEaseCurve_m2338836C98B9D1A0A36ADDD6F173B9890C632C7C;
extern const uint32_t g_rgctx_MotionSettings_2_get_Delay_m4DDB1EAD2CDF0AB2AC072C7D88A4F36DB1EFCBFB;
extern const uint32_t g_rgctx_MotionSettings_2_get_DelayType_m5A7459F37064257EC291AB5A9F67EB9A38A2774D;
extern const uint32_t g_rgctx_MotionSettings_2_get_Loops_mCD65486D24428495C99E92AF2D12BFDFACEEFA98;
extern const uint32_t g_rgctx_MotionSettings_2_get_LoopType_m6B2F8030764F91EFCC3B25D2CB18C8822CC9620F;
extern const uint32_t g_rgctx_MotionSettings_2_get_CancelOnError_m79081DEF838C4248AA6039F7D4FBFA339965BFD8;
extern const uint32_t g_rgctx_MotionSettings_2_get_SkipValuesDuringDelay_mC4B371AF8C41353C0C6A5F236170C4D26B548544;
extern const uint32_t g_rgctx_MotionSettings_2_get_ImmediateBind_m559E3BD8D356E3FAEED793A89046D98017A54C8B;
extern const uint32_t g_rgctx_MotionSettings_2_get_Scheduler_m7DE1CF01490CC551CA60AEC52A1B2013AAFCA8AB;
extern const uint32_t g_rgctx_MotionSettings_2_op_Equality_mAF5181E3016CA28E5D4A5A5B0D601844089C017C;
extern const uint32_t g_rgctx_MotionSettings_2_tE365165659234016D9886FA392F2586107B4031C;
extern const uint32_t g_rgctx_MotionSettings_2_Equals_m56A2DFDF8C409C83A96D5E71DA771C491AA3993D;
extern const uint32_t g_rgctx_MotionSettings_2_get_EqualityContract_mA51978561636E442F044AEBE3A8C265DD6BC8F54;
extern const uint32_t g_rgctx_EqualityComparer_1_get_Default_m5B987852830BA2BA553C0039A1F277458138F2B7;
extern const uint32_t g_rgctx_EqualityComparer_1_t364DA90AEA9BAE0D74D13280F8F92791F5676894;
extern const uint32_t g_rgctx_EqualityComparer_1_t364DA90AEA9BAE0D74D13280F8F92791F5676894;
extern const uint32_t g_rgctx_EqualityComparer_1_GetHashCode_m19235633CDDAD1C6273751689B8C3FAD0ECCC3B9;
extern const uint32_t g_rgctx_EqualityComparer_1_get_Default_m4108F60CC7A09D6B94E4FC11461CB28452D7A28C;
extern const uint32_t g_rgctx_EqualityComparer_1_t20F0CC32110B451B6BDA3D0A410270C9563AAEBB;
extern const uint32_t g_rgctx_EqualityComparer_1_t20F0CC32110B451B6BDA3D0A410270C9563AAEBB;
extern const uint32_t g_rgctx_EqualityComparer_1_GetHashCode_mC058B556EC09D92EA51B80AFF3F1641F221B7C6F;
extern const uint32_t g_rgctx_EqualityComparer_1_Equals_m87A62EAFC8B4BFD01145AB2B67DA999D29D8C2DB;
extern const uint32_t g_rgctx_EqualityComparer_1_Equals_mCC09DA2742612D20F09C70DF30ABF0D92CB70C8F;
extern const uint32_t g_rgctx_MotionSettings_2__ctor_m0A5B1E112129B375C938D1427EF102B0AC1BFA81;
extern const uint32_t g_rgctx_MotionUpdateJob_3_t5C0EB023B4FAC9EEEC15BDD3C0043469D195A310;
extern const uint32_t g_rgctx_MotionData_2U2A_t6D63577FE9C0F111935D595BFC58A00B15A84D2A;
extern const uint32_t g_rgctx_MotionData_2_t9574DD79DB8EE69E9A907B274A45FE79DC337988;
extern const uint32_t g_rgctx_MotionData_2_Update_TisTAdapter_tCF5BA0BD4995BFEADC330F5F98FB0F836DEF094C_mE3198C40C2444B6006BA5BD7368DBEA88EAC8832;
extern const uint32_t g_rgctx_MotionData_2_t9574DD79DB8EE69E9A907B274A45FE79DC337988;
extern const uint32_t g_rgctx_TValueU26_tB4CA077F1961E3F5874069D9A91BFE5FAE9DA336;
extern const uint32_t g_rgctx_NativeArray_1_tC0822F6C50E0BE1136BA87CEFC779E8F7AAEAA41;
extern const uint32_t g_rgctx_NativeArray_1_set_Item_m39FBA53932D0FB02E46E32789FFF1DE1A24EADA8;
extern const uint32_t g_rgctx_NativeArray_1_tC0822F6C50E0BE1136BA87CEFC779E8F7AAEAA41;
extern const uint32_t g_rgctx_TValue_t99C7E5DC2E916D39BC5F9EF36B410BFFE7D7C47E;
extern const uint32_t g_rgctx_SerializableMotionSettings_2_t51519C3416DEEBF53954216B57FB41D6DAE73574;
extern const uint32_t g_rgctx_MotionSettings_2_get_Scheduler_mBE78D34F760AA808CECF1810F0833FEBC7726155;
extern const uint32_t g_rgctx_SerializableMotionSettings_2_SetScheduler_mB41D3CEA5EC6596513ED44FD1A9E8E81008577C7;
extern const uint32_t g_rgctx_SerializableMotionSettings_2_t51519C3416DEEBF53954216B57FB41D6DAE73574;
extern const uint32_t g_rgctx_MotionSettings_2_t3540C8CDBB7247B6871738967CB75E4E08A7AD87;
extern const uint32_t g_rgctx_MotionSettings_2_PrintMembers_m9553F632CE130B397D914A099793452F25A25869;
extern const uint32_t g_rgctx_SerializableMotionSettings_2_op_Equality_mDCDF8F20E62BD4A750E34D92C37A8713BFE8CEA0;
extern const uint32_t g_rgctx_SerializableMotionSettings_2_t51519C3416DEEBF53954216B57FB41D6DAE73574;
extern const uint32_t g_rgctx_MotionSettings_2_t3540C8CDBB7247B6871738967CB75E4E08A7AD87;
extern const uint32_t g_rgctx_SerializableMotionSettings_2_Equals_m889AE111B7F0446982EFE019B012BA385A862E6B;
extern const uint32_t g_rgctx_MotionSettings_2_GetHashCode_m2323E7CF97FF2E83F0A3B88AB857256586E2E014;
extern const uint32_t g_rgctx_MotionSettings_2_Equals_m6A979E423F0EBE8505A298D81CD1F1C10A0E108E;
extern const uint32_t g_rgctx_SerializableMotionSettings_2__ctor_m42EF927E6870100EB89B97C5C104F2EA6ED29100;
extern const uint32_t g_rgctx_MotionSettings_2__ctor_m6C11F7B43DE4FE9B7AB3212F97CBE7A259496D80;
extern const uint32_t g_rgctx_MotionSettings_2__ctor_m9F1BF7D72BB9BAF669F15C8CA840B13212133341;
extern const uint32_t g_rgctx_FastListCore_1_t15126739A48DA6CD13BC207823AB58ADE8D9EBBD;
extern const uint32_t g_rgctx_TU5BU5D_t23A55C3E09CC0E649A76D8AD3D2D38C5DF4C521B;
extern const uint32_t g_rgctx_TU5BU5D_t23A55C3E09CC0E649A76D8AD3D2D38C5DF4C521B;
extern const uint32_t g_rgctx_Array_Resize_TisT_t6FF8E55FBB86D3FD386F14CDF7E7A6B0492B9AB7_mE18815E2B2A3D1C88510B444E89FE01D389B4459;
extern const uint32_t g_rgctx_TU5BU5DU26_tC86644BC5FF0255D1183F3AABB99FF6EFB0996AF;
extern const uint32_t g_rgctx_T_t6FF8E55FBB86D3FD386F14CDF7E7A6B0492B9AB7;
extern const uint32_t g_rgctx_Error_IsNull_TisTU5BU5D_t23A55C3E09CC0E649A76D8AD3D2D38C5DF4C521B_m4E0C337BDD42D937A1D6381F83CB5DDA7B242047;
extern const uint32_t g_rgctx_FastListCore_1_CheckIndex_mAEF78C419533C9311A861B195231B54B311CA8A9;
extern const uint32_t g_rgctx_FastListCore_1_t15126739A48DA6CD13BC207823AB58ADE8D9EBBD;
extern const uint32_t g_rgctx_MemoryExtensions_AsSpan_TisT_t6FF8E55FBB86D3FD386F14CDF7E7A6B0492B9AB7_m1415BD9E0896960A392E765F13D2CCC23D6BFED4;
extern const uint32_t g_rgctx_Span_1_tF9105F54D8C2493CFC7C2D55DA58B7BE751285BE;
extern const uint32_t g_rgctx_Span_1_Clear_m485B9E078FC42AD62B24F181F534703CCAA1B599;
extern const uint32_t g_rgctx_Span_1_tF9105F54D8C2493CFC7C2D55DA58B7BE751285BE;
extern const uint32_t g_rgctx_MemoryExtensions_AsSpan_TisT_t6FF8E55FBB86D3FD386F14CDF7E7A6B0492B9AB7_m1E23CB34720CEA25D4D89E5394C985FD5802E6E9;
extern const uint32_t g_rgctx_Span_1_get_Empty_mA16AE368E9DAA321DCF9470D8435BEBDDF340F49;
extern const uint32_t g_rgctx_LinkedPool_1_t02209D1B4EB125EB7B068F980FC271FED9E9ACE1;
extern const uint32_t g_rgctx_T_tC6AF8D975E17A929C5EE0C743D177FB9E46C0900;
extern const uint32_t g_rgctx_ILinkedPoolNode_1_tE31B9AADC020EDD6C3464A16295514474BA96934;
extern const uint32_t g_rgctx_ILinkedPoolNode_1_get_NextNode_m77257AD9A11CDE71261F1EC6F139B9B21A338F15;
extern const uint32_t g_rgctx_TU26_tA750FD10321F653118AE9489B8F806500C748962;
extern const uint32_t g_rgctx_LinkedPool_1_t02209D1B4EB125EB7B068F980FC271FED9E9ACE1;
static const Il2CppRGCTXDefinition s_rgctxValues[384] = 
{
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5DU26_t246B6DF1040A397580C131C860B2697744F9A607 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_tBA3B5E1C54C90C4697A121BE4C93988364789669 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Array_Resize_TisT_tA5BA1D991045FF5EC1C84644E0E2F6016E80DFDF_m8E8639C5617BD37DE73C44FCA5B06CC904C86613 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_t60D300FC0D8F1AD2ED84A372F7CB18B2829E92F5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t60D300FC0D8F1AD2ED84A372F7CB18B2829E92F5 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_As_TisT_t60D300FC0D8F1AD2ED84A372F7CB18B2829E92F5_TisInt32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_m9D99D6B331CAC1AA20CE2E11D4968C61873195EA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_tB393A59662D3B252A70725EA56085AB212FAE48E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_As_TisBox_1_tE0B3BE351988D69686BD0F6C83AB4F5058DEDB9C_TisBox_1_tABA9158D6F12AC59959EB16951E931419B2FA0B9_mDA71779F497F53B17F87C113A071B5975A18AE86 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Box_1U26_t0BA89BDC48B44311D3FADC5AEEAFA7CECA8B0DC1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_As_TisT_t60D300FC0D8F1AD2ED84A372F7CB18B2829E92F5_TisBoolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_m32150EC8025D5E583A029E9291BBCF7CAA21AF1C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_As_TisBox_1_t389E6FBACC16884E8E4BE1CD8A119E7EC5E2CE16_TisBox_1_tABA9158D6F12AC59959EB16951E931419B2FA0B9_m152281F4F9F58F8F6204C9C50609F15D35F94FD8 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Box_1_tABA9158D6F12AC59959EB16951E931419B2FA0B9 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Box_1__ctor_m13AAB7B53D54C606D300D079F2318E9151CA61DA },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_Box_1_t10EA1FD35F12A8287E1430FB50424815C5614E51 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tDAA37F07255E0EE930F48DAC6F75EA8717D8B873 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Box_1_t10EA1FD35F12A8287E1430FB50424815C5614E51 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Box_1_PrintMembers_m525FC4FC14E450C7E877FE6CAF146C93D2ED622B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Box_1_get_Value_m9D384B82FE7EF426C85857B16ADE4A09A25EBA7E },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_tDAA37F07255E0EE930F48DAC6F75EA8717D8B873_Object_ToString_mF8AC1EB9D85AB52EC8FD8B8BDD131E855E69673F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Box_1_op_Equality_m4E0DEED249007EFCEDB6ED48AE12CF2C8A8AAC20 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Box_1_t10EA1FD35F12A8287E1430FB50424815C5614E51 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Box_1_Equals_m4764EEAD07BA28CD356467CD4BFD4E10553117AB },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Box_1_get_EqualityContract_m33EEEA35094E3FDF4A65B1A04CDFA31633EE2488 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_EqualityComparer_1_get_Default_m04652B46C83FFD6D55D80BB25B17A3ABA7C8F045 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_EqualityComparer_1_tA17257FDAD2BF7B6A7171FDDCFC124EC3BD08CB5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_EqualityComparer_1_tA17257FDAD2BF7B6A7171FDDCFC124EC3BD08CB5 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_EqualityComparer_1_GetHashCode_m794AC68FF6F0171AE7F9E05E994E7DBCA9CFFC40 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_EqualityComparer_1_Equals_m1A218E091D0E8CB2B2EFFBE6F6B0B1E31DE05147 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Box_1__ctor_m994D72D5742C3F89D01B28E86E350B0180474018 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t75E90C34BC998623C53C1D045B12056B3A1861CE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_As_TisRuntimeObject_TisAction_1_t8CFFE77212518FDC506EE81B34902555F63F3175_m8BCF39451FCC8183136BC240FC2DA7A0C84D9C82 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_1U26_tEC78818540223EF3451DDD3F57F804F934F727C2 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TValueU26_tC41EB1C685250544C256511AC50D8B233484F7C1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TValue_t696A8B67057927C9B7B9DE40DC8086419CA43B8A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_1_t8CFFE77212518FDC506EE81B34902555F63F3175 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Action_1_Invoke_mC9224C36C078C499C5F2056C94C9B417A841B409 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_As_TisRuntimeObject_TisAction_2_t994004148B3942BA8BFCA297F9628AD62EF09A9B_m5AEAF76BB0805D2474E7974B52C1F785B8466DEF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_2U26_tE8CE44D87BE40EE658DD983C4D3FF6CD5BD5216C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_2_t994004148B3942BA8BFCA297F9628AD62EF09A9B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Action_2_Invoke_mBC454D1A9B70572A47D9827C1B0BE42457AA7D0F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_As_TisRuntimeObject_TisAction_3_tAD8DE3ABC9AF2654813970E485DFA7D117960EFA_m760328628879E58F3B837329E10E96CAF6C4C2C6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_3U26_tBED281DA6A4547EA08F57F4B2A85B002FCB8B23D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_3_tAD8DE3ABC9AF2654813970E485DFA7D117960EFA },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Action_3_Invoke_m45B97579AE0748BC25287AF9C62328C3CAAEB2DB },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_As_TisRuntimeObject_TisAction_4_tC1A76B09E8AEBE041E7AD6020950BE0C0D8D24A0_m63A4BE41C4DBED6FE22F60D57E07FF59D85F8D57 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_4U26_tEE377B34A7BC5467B589F6B7152491A31838C0CB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_4_tC1A76B09E8AEBE041E7AD6020950BE0C0D8D24A0 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Action_4_Invoke_m07EF8A44A62C013BB5EF09E5A61C6C40FBD18CCB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MotionData_2_t24E3B8A65702EAF4AF499269AD50DE0FFAD2D6C7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TValueU26_t4D06DB393053A972CDF53AEBDF9CBA640EB679E4 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TValue_t5DB091263F57EFE5D2DB5C224B1A743445F49AEB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TOptions_tA627D666DAF9C330C5BFC57F47DC451A4C3A78B1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IMotionAdapter_2_t49F9E324649FE806D677D0982DFF3FF74AD36279 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TOptionsU26_tBD61E54F2DBFBEF6C5D328BF26043709CCF6FEB4 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TAdapter_tCB8BA45C499F086C5B2DEEAE593BA298EEB6C133 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_TAdapter_tCB8BA45C499F086C5B2DEEAE593BA298EEB6C133_IMotionAdapter_2_Evaluate_m57347361CBD4E951BA54E9B5DE5238D426DF3E00 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TAdapter_t62A030644BB6DBE8CF7C819CCA04FEE8AB1FF52A },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_TAdapter_t62A030644BB6DBE8CF7C819CCA04FEE8AB1FF52A_IMotionAdapter_2_Evaluate_m57347361CBD4E951BA54E9B5DE5238D426DF3E00 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MotionStorage_3_tE978EAF0BDE8D7311544F95BF45C98352B5DC9AC },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MotionStorage_3_t76C5A9E7B0969FD6C1A916BDAA4828621279DDC6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MotionData_2U5BU5D_tC2470D62D4137B2724C9185D9E9CBDA0F5793862 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MotionData_2U5BU5D_tC2470D62D4137B2724C9185D9E9CBDA0F5793862 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MemoryExtensions_AsSpan_TisMotionData_2_t88D068F0BF9E991E625D6777D0B47DD8F25143B2_m86A53DE4768657E3E8B8F7613B1666C2D27EC0DF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Span_1_tB3548232950D0038AE20A8806B41D8245E356486 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ArrayHelper_EnsureCapacity_TisMotionData_2_t88D068F0BF9E991E625D6777D0B47DD8F25143B2_mDC581F751CE1782192EBF9F184054343210D18EB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MotionData_2U5BU5DU26_tA746CF1A77EA3A81B97B1D6C1201DBBB772C96B2 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MotionStorage_3_EnsureCapacity_m6133D3469E56AC9045B50572C4E9183151759517 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MotionBuilder_3U26_tCA7842717B13DA2C594F428F3B7CC9F920EBB52A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MotionBuilder_3_t1CB03B115D61E7C1E705F576B919B1CCEAC3FB9B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MotionBuilderBuffer_2_t11D8E5187256555714BFB33C9D5036735AB9997B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MotionData_2_t88D068F0BF9E991E625D6777D0B47DD8F25143B2 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TValue_t137D4A4A58FA21D390E1D949B36249A934E636D5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TOptions_t334A3BEF59CEF84728ED1B7ECD2A0D4DD18DB20E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TAdapter_tAC08577EF2AD31E1DAB836D8AA8274043F5B5948 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IMotionAdapter_2_t7BCA2F3D4C7032CBB5AE957D132932CB8F4F5A02 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_TAdapter_tAC08577EF2AD31E1DAB836D8AA8274043F5B5948_IMotionAdapter_2_Evaluate_m9132244D37C8EA43417D157F1205E9E3C62F30C4 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TValueU26_t76AF8D51CDAB540E05A6449E6C9657B402F04567 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TOptionsU26_tA5F562DF7918135F5186926C9592E97242A3395B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ManagedMotionData_UpdateUnsafe_TisTValue_t137D4A4A58FA21D390E1D949B36249A934E636D5_m663D6B71AD4F03C74D9E3C60A3CD4A23402EFD4E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MotionStorage_3_get_Id_mD0B128D4B33A3CD02AED1C3A13B290C120AC5C5C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MotionData_2U26_t15A6FA3889EFA6EE83CD600C06E2F1E04AB5E7C2 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MotionStorage_3_RemoveAt_m3EDD3764D4E0D20E1B1655A23530DAD061BF43BF },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MotionStorage_3_IsDenseIndexOutOfRange_m1589947B65EC9869ADB701071FAC54A17E034285 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MotionStorage_3_IsInvalidVersion_mFC0D3AC58F3534E98AC71D046F8BA81156A82189 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MotionStorage_3_t76C5A9E7B0969FD6C1A916BDAA4828621279DDC6 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MotionStorage_3_TryCancelCore_m3A8EA16052867888F5B836711C70A9345DE7143D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MotionStorage_3_TryCompleteCore_mA2D020599DBBBE1E14FFD05DBD558B5DA74242AA },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MotionData_2_Complete_TisTAdapter_tAC08577EF2AD31E1DAB836D8AA8274043F5B5948_m05BF9E489D8AC86C63EC04C7DC9C589B70DAA589 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MotionData_2_t88D068F0BF9E991E625D6777D0B47DD8F25143B2 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MotionData_2_Update_TisTAdapter_tAC08577EF2AD31E1DAB836D8AA8274043F5B5948_mF7095880AF0CEAA0C111FC2C24C057850418DB96 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MotionData_2U2A_t003928D8AA9B1C8E4FE8FB966267D9F28DBE4CFB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MotionData_2U5BU5D_tC2470D62D4137B2724C9185D9E9CBDA0F5793862 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MotionStorage_3_GetSlotWithVarify_m9B46AB7E6687C294319D1FCAA027E7B80193DFEF },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_As_TisMotionData_2_t88D068F0BF9E991E625D6777D0B47DD8F25143B2_TisMotionData_tA820A6BD10DEA3326A5D51B503754F003CE7D399_m1D95FD4820EE7779B5FD7E3F73B9043864818D7F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Span_1_Clear_m965EF961FA0A3447A873700189889BCFCADB9DCF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Span_1_tB3548232950D0038AE20A8806B41D8245E356486 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MotionBuilder_3U26_t3D78D23260BAA37193E25D4815BB508125F4C29D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MotionBuilder_3_t3E3267A1727C3A2025A8DCA1E34AFA4F98A91BEE },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MotionBuilderBuffer_2_tF074201FC37F16D7AABEE68F099BFEA2E0076575 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MotionDispatcher_Schedule_TisTValue_t317FC7C090A0FF942895CDA0B712A1811BF43A6B_TisTOptions_t31A18381174B8E08002F6128ED8C0543F2109B25_TisTAdapter_t943ED92EAB85AC67E923ED0580F25F718521CF3C_m79557F8343770DB77950343D4690246A469BC27B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MotionStorage_3_t33058A65F5AE7133E0415E82B8B55B4110F947DD },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UpdateRunner_3_tD7F388D3415B5396E5B3196774ABBC874C11A85B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MotionStorage_3_get_Count_mA9688AB0B886EACE1466FBBE87D9EB1B37C70701 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1__ctor_m906D68D33017796D2EA5CFF99A936EDFF5B50E61 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_t7B18748E158D4D288DFD3649013C59B59E7D09A9 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MotionStorage_3_GetDataSpan_m48762A262CF69492C8EF824DCFC008F1F9D9AD12 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Span_1_t764DB703C7C07B0CF1D3A0106C2CA25C004773AF },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Span_1_GetPinnableReference_mC3874BA61228F472C81945E09379AE913C4A6820 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Span_1_t764DB703C7C07B0CF1D3A0106C2CA25C004773AF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MotionData_2U26_t2E2EC5D0562D0987695984CE74BBDF56770E5389 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MotionUpdateJob_3_t9AAC77599EDBA12B675B899F2F59BC7EDE4A1690 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MotionData_2U2A_tF847BF6EEE418D61486F47CA5D97120AA8D563E2 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_t7B18748E158D4D288DFD3649013C59B59E7D09A9 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IJobParallelForExtensions_Schedule_TisMotionUpdateJob_3_t9AAC77599EDBA12B675B899F2F59BC7EDE4A1690_mABDA500936FBAE8DF5DD8E5CC91B1E20BB59BC03 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MotionStorage_3_GetManagedDataSpan_m622BCADD6CB92367124A3FF69395AB0194D42085 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArrayUnsafeUtility_GetUnsafePtr_TisTValue_t18BA5193C0D831097546BD9B7D61BCF523A27554_m9B599F0EDAE136D4CCFA673EA291EA2DA8A14D23 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MotionData_2_t77A99471D622BCFFE8E82688738CC5C30464485D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TValue_t18BA5193C0D831097546BD9B7D61BCF523A27554 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ManagedMotionData_UpdateUnsafe_TisTValue_t18BA5193C0D831097546BD9B7D61BCF523A27554_m9EA9ADB4F488A2E13CF67C7E1932AC2C2AEEFCDA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TValueU26_tE15F01D69303187DAF6FFCB70BEA4514852AC9C6 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MotionStorage_3_RemoveAll_m65FE1F9C5F77C79C6922A8EAEDB1598AAB6E6BDA },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_NativeArray_1_t7B18748E158D4D288DFD3649013C59B59E7D09A9_IDisposable_Dispose_m3C902735BE731EE30AC1185E7AEF6ACE7A9D9CC7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MotionData_2U26_t2E2EC5D0562D0987695984CE74BBDF56770E5389 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TValueU2A_t47A224A9B1D42686B0D9FDE3B393A696C6A56445 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MotionStorage_3_Reset_mBF7AC6AF337F17EB119922312654FCEB5D19B908 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MotionBuilderBuffer_2_Rent_m03C64A25582813C5F05F40EEABA514E1E9B2052E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MotionBuilderBuffer_2_t8225B819A97A0AD1C8518806780A5A924E49015B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MotionBuilderBuffer_2_t8225B819A97A0AD1C8518806780A5A924E49015B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TValueU26_t9C22A69D921C34F97FF35F037298B400C008C2F8 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TValue_t01064EEF66279658D57126D6ED3348BAD3C911F5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MotionBuilder_3_tAAC26859FB33A51F00C16EF791ACD3BFC5DBD149 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MotionBuilder_3__ctor_m5AC7E1F4544FB1922EF28D3A9DA1EA864AA2164F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MotionBuilderBuffer_2_Rent_mAA09AC1969854ACA565D4271A33806A4AB29A4CD },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MotionBuilderBuffer_2_t8FAED72C63998391D0BBB7A10058BF556839AD51 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MotionBuilderBuffer_2_t8FAED72C63998391D0BBB7A10058BF556839AD51 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MotionSettings_2_tC39109B1C764A14B3582EB4038AEE757A03FEBE0 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MotionSettings_2_get_StartValue_mB6A8F9397DE97210244903466BFD5230ACE98CAB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TValue_tCEF93DEEDF70B13C5BC51B5783A112D0F9153EA7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MotionSettings_2_get_EndValue_mEE8888C850E3D94558D9C09FE84085826444869A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MotionSettings_2_get_Duration_mF385027D54C0E66894FC355EF96CFB97F701FC93 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MotionSettings_2_get_Options_mBF36EDF7A0DB8C5AB8C9EFA61A9374FE3E2350B7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TOptions_tE15C88D1F53253E2AC1BFF3DCDB6A88E5DEF1CE7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MotionSettings_2_get_Ease_mF58A1AF95A34770B80608BEB6D5D1A0677E364CD },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MotionSettings_2_get_CustomEaseCurve_m197E23100EBAB706A7305DB65E9F347C9F9CDC71 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MotionSettings_2_get_Delay_mC0164A850167FDF3357918044B8FBFD90DF4B129 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MotionSettings_2_get_DelayType_m4264B44278B927D06014340C92F60C2F5640F0D7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MotionSettings_2_get_Loops_mE21A6C4927BA4D1DC3AFB88C6674842EE9355906 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MotionSettings_2_get_LoopType_mD0054C596D23D7A6C7C08144DCE49944E6D3051D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MotionSettings_2_get_CancelOnError_m28B8CA10BE64C822969CFDE74063B17E9BBE9B44 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MotionSettings_2_get_SkipValuesDuringDelay_mA41A19EF4AF89EA5CF482DC30766D22F57472A9D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MotionSettings_2_get_ImmediateBind_mC566FA037B58DD91D879B8A0A2D6CCB0FD57131A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MotionSettings_2_get_Scheduler_mB15A16459B2DD02A84C0981362251C800464E99F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MotionBuilder_3_tE9F995E4A376BE65AF7D7B50E661FA318C3EEB84 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MotionBuilder_3__ctor_mE28EF14A5B86C1ADE6D76C0014D6079CF3A45045 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ManualMotionDispatcher_GetOrCreateRunner_TisTValue_t9216B5CEF65406961E29E9312F86678F9079FF2C_TisTOptions_t5D3752881CB637460CF482C4BB235120BB57FE65_TisTAdapter_t67A1D9EBE6127BC102FDBBBE69708FCA746848B9_m878B8B560080D75B3A096C56DB378D1CED84A1E7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UpdateRunner_3_t8233BAD64D4E73290A2FF1C64695344F23F94A86 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UpdateRunner_3_get_Storage_mC8DAB3CC1ADC167FEBE79C2F0A4D35568DD84643 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MotionStorage_3_t39FFDFA6E6BB263F1355C36F900FB40CE96C549E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MotionBuilder_3U26_tD2F0233375B18D68D3D657086A0E225A4AF6E2D7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MotionStorage_3_Create_m2BD44A72B76B127CC9E23DA372D94F26A8D55696 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ManualMotionDispatcher_GetOrCreateRunner_TisTValue_t394A4DA14B2BD540EFF3369227282CBBC7935765_TisTOptions_t43E3C6F7DF19CF6C5D2F3A12DBCD6F8B2C723173_TisTAdapter_t7D27F0B5F78D03C8B4B197089D0A3F40AD0ACF02_m33FB59F3BC01FB3F2798804F573A8A76031C71B8 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UpdateRunner_3_t5494EC0E24313D693430CEFB49696708F150F58E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UpdateRunner_3_get_Storage_mAD2AEB7E1DD4C2B7A4890ADE8F84900B1CBF8BEA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MotionStorage_3_t12328989A7405AFC28DE86478584A2DEC8858824 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MotionStorage_3_EnsureCapacity_mDDDC7C585DA5E9C1DE7A3CF513F5C86673329444 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_ValueTuple_3_tE2E021ADD1C0967EDE9ED03F003A1F34DF379A54 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MotionStorage_3_tCB028FC3B2B83C30DF7D55B84FA81F9CEB4DFF52 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MotionStorage_3__ctor_m646B487C679D54ED872121B99CA5948709BA757C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MotionManager_Register_TisTValue_t872866ACEB6F566A28D3F5C26E6611C10AD7B7EC_TisTOptions_tE1214E1A34EFD75B7C29A699DE2C41C8BA0C6C8A_TisTAdapter_tE7E3B5FDBC85E7233532135C31CDC4E4B58016D9_m41AECEDF6D3319598FF75C3042718F4596DD277B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UpdateRunner_3_t0B7CA27870E6B50608F0AD4386D6B472AA013E44 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UpdateRunner_3__ctor_mC59AB2878B166EB3DABF0C87314DACCF924DC59A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MotionBuilderBuffer_2_t563E945503317148ED5A6647716AFE4F6F53906B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MotionBuilderBuffer_2_t563E945503317148ED5A6647716AFE4F6F53906B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MotionBuilderBuffer_2__ctor_m0081588D56A2DEBCCFB02C167E26CAAD6815BC28 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TValue_tDA548C81049EA5B413C1485E73F9AADB7300DE41 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TOptions_tE94C02533EE055456E4C179A64137FC51EBE9D05 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MotionBuilderBuffer_2_tDFBA431286F7981B910F1DBCFAD53944153E11E4 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MotionBuilder_3_t3B668D1CFD4A196527E63367B1329C977F309220 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MotionBuilder_3_CheckEaseType_m86EFD4CCF2DFFAA1E4C30F8E101EF9DF3ADF91AE },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MotionBuilder_3_t3B668D1CFD4A196527E63367B1329C977F309220 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MotionBuilder_3_CheckBuffer_mA9AB7CF7175368E9C439326AD259CA025D7F68D1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TOptions_t0262A3541C70FBFDD6DDA1755B36A9F1FA7088C6 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MotionBuilder_3_ScheduleMotion_m7564037E6815C53D0E7682E58642181A6156ADEA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_1_t03E06CC34AA77E343E271F41E785D197DBD439F3 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MotionBuilder_3_SetCallbackData_m6A9E594398AC2C7185BE034E9BCADC8D7BA629CB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MotionSettings_2_t248A6D461159790B0133EECA29CE2A53A733202F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MotionSettings_2__ctor_m2AE121D6A75FD758AE4D6C6954FB227F1B89FF27 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TValue_tC6FBFB87FABE9A0591050D55A71FACC6D35B874F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MotionSettings_2_set_StartValue_m9DDC97D31B68208E2A48E7545DADA9A37CE10D5E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MotionSettings_2_set_EndValue_mB290F08AC8E32A6D9A12C58EF22C1FA6DA136B38 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MotionSettings_2_set_Duration_mD6CD85EC0019278075D67F13AD4B09A79A4D81B0 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MotionSettings_2_set_Options_m66F426E51E9A25B8CD78ACF021278BED1F3D0D01 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MotionSettings_2_set_Ease_mB3448D43D99E9B0A099E7B67A25FD6A9578E4547 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MotionSettings_2_set_CustomEaseCurve_m1D8FD5D76FC7FA597675014296FA47DB360D68D0 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MotionSettings_2_set_Delay_m89D2B3002BEDFBF1395F6C2017CD30214BBA067C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MotionSettings_2_set_DelayType_mCECE9C0F71A40DE0A5763655132BCCD5875436C4 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MotionSettings_2_set_Loops_mBD88A5A129BDD5C9375F8BB9E749D35FD3FA4F5E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MotionSettings_2_set_LoopType_m97D2237A3C900736DA3D7A9B7783910D898BE1ED },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MotionSettings_2_set_CancelOnError_m6069F9194098D8B3CA8CF936C21EDFDE3617F4D7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MotionSettings_2_set_SkipValuesDuringDelay_mD9FD3FACAB8298EF387E3DF49B85302E6A271475 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MotionSettings_2_set_ImmediateBind_m17F9B4B7A0226E0682C0733F20BDE56D5228512F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MotionSettings_2_set_Scheduler_mE7B3B2AF4E47034755F97485BA51D90988ACF1B1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MotionDispatcher_Schedule_TisTValue_tC6FBFB87FABE9A0591050D55A71FACC6D35B874F_TisTOptions_t0262A3541C70FBFDD6DDA1755B36A9F1FA7088C6_TisTAdapter_t546391DCDEF282613CF80FC035F68F6E986D7269_m56D42F6B548913CD252504E2F88DFFF1C0AD8079 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MotionBuilder_3U26_tBAE15FC4E205BE22FFA0611FB325098188F7B14F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IMotionScheduler_Schedule_TisTValue_tC6FBFB87FABE9A0591050D55A71FACC6D35B874F_TisTOptions_t0262A3541C70FBFDD6DDA1755B36A9F1FA7088C6_TisTAdapter_t546391DCDEF282613CF80FC035F68F6E986D7269_m2D18DD6AA59242D317C760ABBC137BA410557FC7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MotionBuilder_3_Dispose_mD48B9998A9DD75936AE6D3D119F2791FC32A58F7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MotionBuilderBuffer_2_Return_m115E6023F72DACBD3325E28E00185E2A58B811BE },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MotionBuilderBuffer_2_tDFBA431286F7981B910F1DBCFAD53944153E11E4 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TState_t7BEE902212D714B987621FF68AAB593876CFA77D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_2_t8F4245804B3DEE583B16E89E77BBAF081616464B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MotionBuilder_3_SetCallbackData_TisTState_t7BEE902212D714B987621FF68AAB593876CFA77D_m593AD4DA76B41BAB537828FF2552D3FC37955E86 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TState0_tAF23D0A5CA498DAFD5E84411B9EC7854DEC609EB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TState1_t10E024BF5A5F78FF13E05DEB21384627FA768D2B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_3_t438E82AD4D37B29E12426F518A223381C4DC65E6 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MotionBuilder_3_SetCallbackData_TisTState0_tAF23D0A5CA498DAFD5E84411B9EC7854DEC609EB_TisTState1_t10E024BF5A5F78FF13E05DEB21384627FA768D2B_m0888C00FF21EFEA5C37317E00916F40DC1DBDEAE },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TState0_tD0A690A78074F627A9B75C0E232F25CE59E5D9A9 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TState1_tBBE47C6896AC59FEC7A3D880A364F63D30D04D96 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TState2_t6143DD2D434A019B86E991C819E77A2FB797F363 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_4_tAB546CFD92C21B39BBCE604D13265C346C9AFABD },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MotionBuilder_3_SetCallbackData_TisTState0_tD0A690A78074F627A9B75C0E232F25CE59E5D9A9_TisTState1_tBBE47C6896AC59FEC7A3D880A364F63D30D04D96_TisTState2_t6143DD2D434A019B86E991C819E77A2FB797F363_m049A22A776426FC8FDB749BC51868DC9A2EC855B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TState_t1A4DD74565CBFCE8CBD9D31DE2C36107C630F2A5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_2_t7A390EB76C0C8AEB58E13BAD38B6F594EB981515 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TState0_t4365811C40E16C3E275DBCBA20E6A8AD380E68B5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TState1_t9977CE02BC596E09ACF633732FE21878E399A7C6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_3_t2F9E32955519899D9F16A9B7C8C3605E72D5AB5C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TState0_t92AF529295CCC3646E5C4D8428CB5E401B05172B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TState1_t6117A73DAB1799BA23DE939DD1797DAC3F87AAA8 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TState2_t0BD34D0ECD5211C299DEAFB9D6C2723E7F1B8434 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_4_t9FEEBDE92B5AB9B33272EA2202F33B0E1BC6F3F5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MotionBuilder_3_t631B9A8191991AE37B521463DE2A4B50DE54CAB8 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TState_tD7CA9D11216A5B060F2FDEB7AE401A1D405A9A2C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Box_Create_TisTState_tD7CA9D11216A5B060F2FDEB7AE401A1D405A9A2C_mB9279EDD349BA89C8E377BA5053CD1A977083D57 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Box_1_t90F0BDEDDD772D99A4E785BF1F999CC401411E84 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_2_tBE10CE10C695335C1A64BD882239055D80297CCE },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec__0_4_t45E701CBB49D4F4877C779B16DB9F466B6147362 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_3_t00894A0BFE843ABA10B31A8ACDE7F71EF4FA8911 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec__0_4_t45E701CBB49D4F4877C779B16DB9F466B6147362 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec__0_4_U3CBindU3Eb__0_0_mB533DA22D20DF6FABA89A85AACFCCE0B9EF33A9C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Action_3__ctor_m7E984667278F33CC87CC9AED1D91D6B81F11AF7A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MotionBuilder_3_Bind_TisBox_1_t90F0BDEDDD772D99A4E785BF1F999CC401411E84_TisAction_2_tBE10CE10C695335C1A64BD882239055D80297CCE_m58BB091DED524DE95A6918FE37353BBED50BCA21 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MotionBuilder_3_t631B9A8191991AE37B521463DE2A4B50DE54CAB8 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MotionBuilder_3_t15B6CCDFC528C373EC054A2141E3D998778EF9E6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MotionBuilderBuffer_2_t656FAE292B85EA84EA0BE7E06281D469D6C565A5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MotionBuilder_3_t21437EC561F795504385F83181EBBBD8BA58C148 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MotionBuilderBuffer_2_t260CA1A3476E5A25CCC657F07525D66EDFCF3FFD },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MotionBuilder_3_tBD986E4D8C6149BD74CC83A509FC6CBA97FE3196 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MotionBuilderBuffer_2_t6B0CF5D50002A230F8CD024FE63528E2E4049431 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MotionBuilder_3_t77B15752B05F003965B094202DD9A12C21BA561F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MotionBuilderBuffer_2_t5F4A89E16D6F1E6B7AC6336E772C768AA0475DCF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MotionBuilder_3_t698BD3421F14261FA43EF6DB839C5C7103E6C404 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MotionBuilderBuffer_2_t011CC8460EA8D86BE3689E15CA7ACBF0C7907FF7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MotionBuilder_3_tDA630F21301093A8EC7DF8BC3CAEDCCC2F7A849E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MotionBuilderBuffer_2_t2F45206F1B4848E8035EEF882D62068415910776 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MotionBuilder_3_t0094B3660054CBCC2B45A4F1DB126FA83C569D23 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MotionBuilderBuffer_2_t2DB125088E017D7C5F884282D6F66E4ED5C85D4B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MotionBuilder_3_t4F3B337A52DE845CC8E2803FB6EE1A3B8088A7B7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MotionBuilderBuffer_2_t13E53A4749210D50C6B809BEC02030D2C73E9BA5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MotionBuilder_3_tE37FF25C9B19B713EE10979AF325B5729053A9BB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MotionBuilderBuffer_2_tB9BC3806D381B896C270A9D0C53E2B49B97A613D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MotionBuilder_3_t1ACDE836D480A5CABB632BB01CFDC1B200A4CF10 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MotionBuilderBuffer_2_t09411546F24D05906B04AF52BE1E2DC80A550E5B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec__0_4_t25FF19A1400EAC2DC1C909F88665B697B3EDD403 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec__0_4__ctor_m67754364E459D6A89C830E9062A74C3F31EA0F53 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec__0_4_t25FF19A1400EAC2DC1C909F88665B697B3EDD403 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_2_tD5AFD65A867E1FD070956EF076B5D264A4377786 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TValue_t58DB69397872FF375D2FCDBEDDFE65DC9DD327E6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Box_1_t4E445DD3794823FA945D5607C59BB0FE82A38358 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Box_1_get_Value_m9E7E186E728B9934A4416E86276D161A440887CB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TState_tF7DFAD173D29B2D0F7951F62E7099008637DE882 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Action_2_Invoke_m0932E6E11EC2431C22794322C6F4F744E9CAA900 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_StorageCache_3_GetOrCreate_mC07EFD7FA515F0BDE025E7E77FFEF025EA73E0C8 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_StorageCache_3_tC6C3B6CC34516F722EDC4F3235F691AC3B8A176E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MotionStorage_3_tAFD95A9A2BC68B1F2E8A62D428888ECC92C471F1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MotionStorage_3_EnsureCapacity_m7941FFA9AA1B6CE7FCCDBAD76033B0B7957F76F5 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_StorageCache_3_GetOrCreate_mF4E5BF02A9232850FC0B3452E203ED5B46972A54 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_StorageCache_3_tE5907C47EA6FDE016D494EA7E6B9A70656FC6F52 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MotionStorage_3_t0487965E2BC852F653D9925BC879B9A5B23B80E5 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_RunnerCache_3_GetOrCreate_mDE57F181CFB71E212F675067F945CB8DC7C9AA29 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_RunnerCache_3_tE94FDBB49E1771CFD2D85BEF468775B96795CC8C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ValueTuple_2_tBB2089AF102E52F8FB7F272095684FF716B79579 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MotionBuilder_3U26_t725E3100355F107CAF9230A27E8197D97E2B77BC },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MotionStorage_3_Create_m0A38A5CBCFA0CACADCEC5983275349E1CC07014B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_StorageCache_3_tFBCF967DB7361C8F9757951C14E04A007692F321 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MotionStorage_3_tA2E42D19EE4A5A0EA5EE414C1A72FA8B9B1F83E6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_StorageCache_3_tFBCF967DB7361C8F9757951C14E04A007692F321 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_StorageCache_3_CreateIfNull_m98054427AA82D4625053D307264C01AC1E7B7568 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MotionStorage_3U26_t1417711E768396069A71BF5BA20B4AEBCBBAC931 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MotionStorage_3__ctor_m5CA39C380A20CB160449899E3B37DDCCBECE20B3 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MotionManager_Register_TisTValue_t0AB293BAF36DA03CF03407CEB8C37909F824C703_TisTOptions_t0E3E96015F2FCA2C77D8A54BF9823FE6ED879A92_TisTAdapter_tD95F3E7D36DD6A9F3B0E2C93880B31B3F3D4448D_mE31DF48FE0ECDCA3D4F97B31A059F622165E2820 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_RunnerCache_3_t18593FBA8994C513AB65340838129BCB09765D5B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UpdateRunner_3_t497E6864F8BBAE12192E3600CEDF333094AC101B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_RunnerCache_3_t18593FBA8994C513AB65340838129BCB09765D5B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MotionStorage_3_t80A5DB68A214178D198B653D425800E8992E90E6 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_RunnerCache_3_CreateIfNull_mD2C279C5F6F6252973651070CD51620A13A5D3BD },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UpdateRunner_3U26_t3455DFD36BF3B43B331ABC1D125CFA1D7338289F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ValueTuple_2_t565B12E22CA6735840754940CF93CFB547631A03 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UpdateRunner_3__ctor_mE8B3219E0505F7AB2FD82A85B92C92C9C9F9D842 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ValueTuple_2__ctor_mBE2CC3BCAF288729B9681615B7A6E17A03CA6E27 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_GameObject_TryGetComponent_TisTComponent_tB64ECA36B24587EBD45A3AB56A9C2AF4BAA78824_mFF67E4458FBF5977D969D85C04F1E30B8380B7E2 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TComponentU26_t160C0D4684B0E4D67B7E99C0578A088A160B1F26 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_GameObject_AddComponent_TisTComponent_tB64ECA36B24587EBD45A3AB56A9C2AF4BAA78824_m8CE05521D0F7B4B2E1E50EC1FC86B8F73A0A8F43 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TComponent_tB64ECA36B24587EBD45A3AB56A9C2AF4BAA78824 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_MotionSettings_2_tE365165659234016D9886FA392F2586107B4031C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MotionSettings_2_tE365165659234016D9886FA392F2586107B4031C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TValue_tEE83F5BAD64E26F73726BBA9D7D6BE5D9935191D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TOptions_tB47AB4A64506B96A709374B49D6516C454A68096 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MotionSettings_2_PrintMembers_m1B4411F24EE2A38713CB76A8E7AAF58F0C882496 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MotionSettings_2_get_StartValue_mA7AE064CAD8E8B7070A34A71FF31FCCA47E0FF12 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_TValue_tEE83F5BAD64E26F73726BBA9D7D6BE5D9935191D_Object_ToString_mF8AC1EB9D85AB52EC8FD8B8BDD131E855E69673F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MotionSettings_2_get_EndValue_mAAFAB593452997722D9D55C9732F99B44A3F98C2 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MotionSettings_2_get_Duration_m452CB0750323D4E1A6BA9D616C6BAC4B68DCEBBC },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MotionSettings_2_get_Options_m7E81D98083862B00C0329FDB67370AA80E848DB9 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_TOptions_tB47AB4A64506B96A709374B49D6516C454A68096_Object_ToString_mF8AC1EB9D85AB52EC8FD8B8BDD131E855E69673F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MotionSettings_2_get_Ease_m5D6E591218C861F31E3A6DB6793D49AE6C6E6D66 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MotionSettings_2_get_CustomEaseCurve_m2338836C98B9D1A0A36ADDD6F173B9890C632C7C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MotionSettings_2_get_Delay_m4DDB1EAD2CDF0AB2AC072C7D88A4F36DB1EFCBFB },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MotionSettings_2_get_DelayType_m5A7459F37064257EC291AB5A9F67EB9A38A2774D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MotionSettings_2_get_Loops_mCD65486D24428495C99E92AF2D12BFDFACEEFA98 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MotionSettings_2_get_LoopType_m6B2F8030764F91EFCC3B25D2CB18C8822CC9620F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MotionSettings_2_get_CancelOnError_m79081DEF838C4248AA6039F7D4FBFA339965BFD8 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MotionSettings_2_get_SkipValuesDuringDelay_mC4B371AF8C41353C0C6A5F236170C4D26B548544 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MotionSettings_2_get_ImmediateBind_m559E3BD8D356E3FAEED793A89046D98017A54C8B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MotionSettings_2_get_Scheduler_m7DE1CF01490CC551CA60AEC52A1B2013AAFCA8AB },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MotionSettings_2_op_Equality_mAF5181E3016CA28E5D4A5A5B0D601844089C017C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MotionSettings_2_tE365165659234016D9886FA392F2586107B4031C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MotionSettings_2_Equals_m56A2DFDF8C409C83A96D5E71DA771C491AA3993D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MotionSettings_2_get_EqualityContract_mA51978561636E442F044AEBE3A8C265DD6BC8F54 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_EqualityComparer_1_get_Default_m5B987852830BA2BA553C0039A1F277458138F2B7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_EqualityComparer_1_t364DA90AEA9BAE0D74D13280F8F92791F5676894 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_EqualityComparer_1_t364DA90AEA9BAE0D74D13280F8F92791F5676894 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_EqualityComparer_1_GetHashCode_m19235633CDDAD1C6273751689B8C3FAD0ECCC3B9 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_EqualityComparer_1_get_Default_m4108F60CC7A09D6B94E4FC11461CB28452D7A28C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_EqualityComparer_1_t20F0CC32110B451B6BDA3D0A410270C9563AAEBB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_EqualityComparer_1_t20F0CC32110B451B6BDA3D0A410270C9563AAEBB },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_EqualityComparer_1_GetHashCode_mC058B556EC09D92EA51B80AFF3F1641F221B7C6F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_EqualityComparer_1_Equals_m87A62EAFC8B4BFD01145AB2B67DA999D29D8C2DB },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_EqualityComparer_1_Equals_mCC09DA2742612D20F09C70DF30ABF0D92CB70C8F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MotionSettings_2__ctor_m0A5B1E112129B375C938D1427EF102B0AC1BFA81 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MotionUpdateJob_3_t5C0EB023B4FAC9EEEC15BDD3C0043469D195A310 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MotionData_2U2A_t6D63577FE9C0F111935D595BFC58A00B15A84D2A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MotionData_2_t9574DD79DB8EE69E9A907B274A45FE79DC337988 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MotionData_2_Update_TisTAdapter_tCF5BA0BD4995BFEADC330F5F98FB0F836DEF094C_mE3198C40C2444B6006BA5BD7368DBEA88EAC8832 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MotionData_2_t9574DD79DB8EE69E9A907B274A45FE79DC337988 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TValueU26_tB4CA077F1961E3F5874069D9A91BFE5FAE9DA336 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_tC0822F6C50E0BE1136BA87CEFC779E8F7AAEAA41 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_set_Item_m39FBA53932D0FB02E46E32789FFF1DE1A24EADA8 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_tC0822F6C50E0BE1136BA87CEFC779E8F7AAEAA41 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TValue_t99C7E5DC2E916D39BC5F9EF36B410BFFE7D7C47E },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_SerializableMotionSettings_2_t51519C3416DEEBF53954216B57FB41D6DAE73574 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MotionSettings_2_get_Scheduler_mBE78D34F760AA808CECF1810F0833FEBC7726155 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SerializableMotionSettings_2_SetScheduler_mB41D3CEA5EC6596513ED44FD1A9E8E81008577C7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_SerializableMotionSettings_2_t51519C3416DEEBF53954216B57FB41D6DAE73574 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MotionSettings_2_t3540C8CDBB7247B6871738967CB75E4E08A7AD87 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MotionSettings_2_PrintMembers_m9553F632CE130B397D914A099793452F25A25869 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SerializableMotionSettings_2_op_Equality_mDCDF8F20E62BD4A750E34D92C37A8713BFE8CEA0 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_SerializableMotionSettings_2_t51519C3416DEEBF53954216B57FB41D6DAE73574 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MotionSettings_2_t3540C8CDBB7247B6871738967CB75E4E08A7AD87 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SerializableMotionSettings_2_Equals_m889AE111B7F0446982EFE019B012BA385A862E6B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MotionSettings_2_GetHashCode_m2323E7CF97FF2E83F0A3B88AB857256586E2E014 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MotionSettings_2_Equals_m6A979E423F0EBE8505A298D81CD1F1C10A0E108E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SerializableMotionSettings_2__ctor_m42EF927E6870100EB89B97C5C104F2EA6ED29100 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MotionSettings_2__ctor_m6C11F7B43DE4FE9B7AB3212F97CBE7A259496D80 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MotionSettings_2__ctor_m9F1BF7D72BB9BAF669F15C8CA840B13212133341 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FastListCore_1_t15126739A48DA6CD13BC207823AB58ADE8D9EBBD },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_t23A55C3E09CC0E649A76D8AD3D2D38C5DF4C521B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_t23A55C3E09CC0E649A76D8AD3D2D38C5DF4C521B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Array_Resize_TisT_t6FF8E55FBB86D3FD386F14CDF7E7A6B0492B9AB7_mE18815E2B2A3D1C88510B444E89FE01D389B4459 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5DU26_tC86644BC5FF0255D1183F3AABB99FF6EFB0996AF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t6FF8E55FBB86D3FD386F14CDF7E7A6B0492B9AB7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Error_IsNull_TisTU5BU5D_t23A55C3E09CC0E649A76D8AD3D2D38C5DF4C521B_m4E0C337BDD42D937A1D6381F83CB5DDA7B242047 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FastListCore_1_CheckIndex_mAEF78C419533C9311A861B195231B54B311CA8A9 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FastListCore_1_t15126739A48DA6CD13BC207823AB58ADE8D9EBBD },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MemoryExtensions_AsSpan_TisT_t6FF8E55FBB86D3FD386F14CDF7E7A6B0492B9AB7_m1415BD9E0896960A392E765F13D2CCC23D6BFED4 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Span_1_tF9105F54D8C2493CFC7C2D55DA58B7BE751285BE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Span_1_Clear_m485B9E078FC42AD62B24F181F534703CCAA1B599 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Span_1_tF9105F54D8C2493CFC7C2D55DA58B7BE751285BE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MemoryExtensions_AsSpan_TisT_t6FF8E55FBB86D3FD386F14CDF7E7A6B0492B9AB7_m1E23CB34720CEA25D4D89E5394C985FD5802E6E9 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Span_1_get_Empty_mA16AE368E9DAA321DCF9470D8435BEBDDF340F49 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_LinkedPool_1_t02209D1B4EB125EB7B068F980FC271FED9E9ACE1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tC6AF8D975E17A929C5EE0C743D177FB9E46C0900 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ILinkedPoolNode_1_tE31B9AADC020EDD6C3464A16295514474BA96934 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ILinkedPoolNode_1_get_NextNode_m77257AD9A11CDE71261F1EC6F139B9B21A338F15 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_tA750FD10321F653118AE9489B8F806500C748962 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_LinkedPool_1_t02209D1B4EB125EB7B068F980FC271FED9E9ACE1 },
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_LitMotion_CodeGenModule;
const Il2CppCodeGenModule g_LitMotion_CodeGenModule = 
{
	"LitMotion.dll",
	1248,
	s_methodPointers,
	127,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	47,
	s_rgctxIndices,
	384,
	s_rgctxValues,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};

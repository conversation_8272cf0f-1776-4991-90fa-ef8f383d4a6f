﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void NavMeshAgent_set_nextPosition_m588339359E961F44B20AE429C4CB8434D65AAC52 (void);
extern void NavMeshAgent_get_steeringTarget_mA2DB66413FC7628DCDB1DECF38269A70EC60351C (void);
extern void NavMeshAgent_set_autoBraking_m3BBDC74E6ADC3EB8BE0381C97B1D8715C1BC5283 (void);
extern void NavMeshAgent_SetPath_m52F66FD27D02DE634BAF3AC2B521017B4299DB8C (void);
extern void NavMeshAgent_set_path_mAD6A4742FA25F24F5711D293DAB7DE56F8EF0156 (void);
extern void NavMeshAgent_CalculatePath_mF692688572E61C3B9FE776AA3784E14A08C259C2 (void);
extern void NavMeshAgent_CalculatePathInternal_m022C56D89B194E8EAD260A2E2CEEA100024AE004 (void);
extern void NavMeshAgent_get_updatePosition_m7A3ECBAC5345D34EF8896E7ACEDB9E459FFBD9AB (void);
extern void NavMeshAgent_set_updatePosition_m25CA3F441A2EEC82096B4BCFAD4E5FDC10867372 (void);
extern void NavMeshAgent_get_updateRotation_m931B37F6BA4D560A9838ACD89A1470975E4D8F4D (void);
extern void NavMeshAgent_set_updateRotation_mBF6EDBC9BBAF32490229D7DD6BC821A420C3399D (void);
extern void NavMeshAgent_get_isOnNavMesh_m2463F49C3F37E9D3F68C04300FE5AF310C924405 (void);
extern void NavMeshAgent__ctor_m585725EF2A2A569E59283223CFF1BE6FF9A44EED (void);
extern void NavMeshAgent_set_nextPosition_Injected_m2BE7FC0BB19304F18AC625CC08CE9E64974386AB (void);
extern void NavMeshAgent_get_steeringTarget_Injected_m00761894AB2F3B67A8775949DBF465506CD78DE1 (void);
extern void NavMeshAgent_CalculatePathInternal_Injected_m07BFBF7B199DE22CB3AB819BACA56953D2C56C06 (void);
extern void NavMesh_Internal_CallOnNavMeshPreUpdate_m2A62DB32F5E1435F527AD8A59A882B9F2A193177 (void);
extern void OnNavMeshPreUpdate__ctor_m7142A3AA991BE50B637A16D946AB7604C64EF9BA (void);
extern void OnNavMeshPreUpdate_Invoke_mFB224B9BBF9C78B7F39AA91A047F175C69897914 (void);
extern void NavMeshPath__ctor_mEA40BFC2492814FFC97A71C3AEC2154A9415C37F (void);
extern void NavMeshPath_Finalize_mB151BFBD5D7E65C343415B6B332A58504F12AF77 (void);
extern void NavMeshPath_InitializeNavMeshPath_m14652B99A3EFB12B428AC2C959A629EE906DE5F1 (void);
extern void NavMeshPath_DestroyNavMeshPath_mAB640913E8A9F1BE03EF9103FF34D5F4C5EBE3F7 (void);
extern void NavMeshPath_ClearCornersInternal_m2310C5CB9B4EB2B3C4685476B2CF8440ED369606 (void);
extern void NavMeshPath_ClearCorners_m8633C3989850C01982EBD3D4BC70E85AF461CE5B (void);
static Il2CppMethodPointer s_methodPointers[25] = 
{
	NavMeshAgent_set_nextPosition_m588339359E961F44B20AE429C4CB8434D65AAC52,
	NavMeshAgent_get_steeringTarget_mA2DB66413FC7628DCDB1DECF38269A70EC60351C,
	NavMeshAgent_set_autoBraking_m3BBDC74E6ADC3EB8BE0381C97B1D8715C1BC5283,
	NavMeshAgent_SetPath_m52F66FD27D02DE634BAF3AC2B521017B4299DB8C,
	NavMeshAgent_set_path_mAD6A4742FA25F24F5711D293DAB7DE56F8EF0156,
	NavMeshAgent_CalculatePath_mF692688572E61C3B9FE776AA3784E14A08C259C2,
	NavMeshAgent_CalculatePathInternal_m022C56D89B194E8EAD260A2E2CEEA100024AE004,
	NavMeshAgent_get_updatePosition_m7A3ECBAC5345D34EF8896E7ACEDB9E459FFBD9AB,
	NavMeshAgent_set_updatePosition_m25CA3F441A2EEC82096B4BCFAD4E5FDC10867372,
	NavMeshAgent_get_updateRotation_m931B37F6BA4D560A9838ACD89A1470975E4D8F4D,
	NavMeshAgent_set_updateRotation_mBF6EDBC9BBAF32490229D7DD6BC821A420C3399D,
	NavMeshAgent_get_isOnNavMesh_m2463F49C3F37E9D3F68C04300FE5AF310C924405,
	NavMeshAgent__ctor_m585725EF2A2A569E59283223CFF1BE6FF9A44EED,
	NavMeshAgent_set_nextPosition_Injected_m2BE7FC0BB19304F18AC625CC08CE9E64974386AB,
	NavMeshAgent_get_steeringTarget_Injected_m00761894AB2F3B67A8775949DBF465506CD78DE1,
	NavMeshAgent_CalculatePathInternal_Injected_m07BFBF7B199DE22CB3AB819BACA56953D2C56C06,
	NavMesh_Internal_CallOnNavMeshPreUpdate_m2A62DB32F5E1435F527AD8A59A882B9F2A193177,
	OnNavMeshPreUpdate__ctor_m7142A3AA991BE50B637A16D946AB7604C64EF9BA,
	OnNavMeshPreUpdate_Invoke_mFB224B9BBF9C78B7F39AA91A047F175C69897914,
	NavMeshPath__ctor_mEA40BFC2492814FFC97A71C3AEC2154A9415C37F,
	NavMeshPath_Finalize_mB151BFBD5D7E65C343415B6B332A58504F12AF77,
	NavMeshPath_InitializeNavMeshPath_m14652B99A3EFB12B428AC2C959A629EE906DE5F1,
	NavMeshPath_DestroyNavMeshPath_mAB640913E8A9F1BE03EF9103FF34D5F4C5EBE3F7,
	NavMeshPath_ClearCornersInternal_m2310C5CB9B4EB2B3C4685476B2CF8440ED369606,
	NavMeshPath_ClearCorners_m8633C3989850C01982EBD3D4BC70E85AF461CE5B,
};
static const int32_t s_InvokerIndices[25] = 
{
	10912,
	13280,
	10442,
	7736,
	10682,
	3650,
	3650,
	12815,
	10442,
	12815,
	10442,
	12815,
	13298,
	10415,
	10415,
	3334,
	21355,
	5684,
	13298,
	13298,
	13298,
	21265,
	20842,
	13298,
	13298,
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_AIModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_AIModule_CodeGenModule = 
{
	"UnityEngine.AIModule.dll",
	25,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};

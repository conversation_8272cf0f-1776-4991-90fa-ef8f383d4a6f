﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void RaycastHelpers_IsNonDynamicColliderInRange_mF4D4FB271950B5C808F9465F648D67EB9F68DD59 (void);
extern void RaycastHelpers__cctor_mC84972D5A2D68C6AF31E6901045D9F6B5A59A3CD (void);
extern void DestroyTime_Start_mD6673F78C6E9BCD2E86C9C895330369499E0B5F8 (void);
extern void DestroyTime_DestroyTimer_mAFEC73AAEA54C0CD54E7D7528683C1A2D4D09EA0 (void);
extern void DestroyTime__ctor_m8CFE074330AB2ED362EA976620EAA1A8501D6492 (void);
extern void U3CDestroyTimerU3Ed__2__ctor_m861B868BF5D562956024F53CCF13C48E8D90383C (void);
extern void U3CDestroyTimerU3Ed__2_System_IDisposable_Dispose_m4E2E05004A3460619986F30F8C872BCF83D51111 (void);
extern void U3CDestroyTimerU3Ed__2_MoveNext_mCD6F3F56006731299869344A9B3D9A62C8FD6878 (void);
extern void U3CDestroyTimerU3Ed__2_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m9C73DEFBA82E7427FBA91F41A358775B19897BD9 (void);
extern void U3CDestroyTimerU3Ed__2_System_Collections_IEnumerator_Reset_m67C48AF5EA7006F7CADCF9F7D9DD9FD31BC7A799 (void);
extern void U3CDestroyTimerU3Ed__2_System_Collections_IEnumerator_get_Current_m9337F51A2CDFB070F05EE62DDE2FB2C3545CAEB6 (void);
extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_m36F6C5E69A390BA21CE99727AC71905EABC52FEF (void);
extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_mA967CAFAF2D0E0FF3C4EABA8BDF3691DCCCE3745 (void);
extern void Ammo_UseEffector_get___Usage_m0E71B3E7C52247530A60B8B5E071431BECD3900E (void);
extern void Ammo_UseEffector_Initialize_m3370BEFE6F16CF03ACED1C58CB1FB0D9606D4163 (void);
extern void Ammo_UseEffector_CheckCondition_m21B0C96DD75F408C29625E667C10EAAC69E13143 (void);
extern void Ammo_UseEffector_OnUse_mA1AFEB49A31D3F7F044764B51F71C296E5723FF4 (void);
extern void Ammo_UseEffector__ctor_mF3D31852DEC8EF5B599E9D8BE9EFD6DB10C2461E (void);
extern void Ammo_UseEffector__cctor_m21A157443B55665528367A0713195C4338142308 (void);
extern void AnimationEvent_Sender_UseEffect_get___Usage_m4923F0F5085C6F62E3A872BCE31F05C95DED0BC8 (void);
extern void AnimationEvent_Sender_UseEffect_Initialize_m2D5B5229C148345046C3769DCA92376F5349C885 (void);
extern void AnimationEvent_Sender_UseEffect_OnUse_m87025FC602556E2F4AE44365162CFDC3BA0C65A3 (void);
extern void AnimationEvent_Sender_UseEffect__ctor_mD17D5C12ACCBA829637FA4C4D205A3ED41D17B26 (void);
extern void AnimatorIK_GunAimer_get___Usage_mBF28F99D1554ABB196DC082156E78E6CB052B3A9 (void);
extern void AnimatorIK_GunAimer_get_animator_m16F2DCE8CAC561A686860E82B6A683B36AE76C38 (void);
extern void AnimatorIK_GunAimer_Awake_m0D209C4EC661568F9532B62993DF4F37AC236018 (void);
extern void AnimatorIK_GunAimer_OnEnable_m16BF95A6CEA420884269AF1A9D45A4ADF4AD02F9 (void);
extern void AnimatorIK_GunAimer_OnDisable_m7A20F5B5761C9C917FF7FE4FB9866894C1A618DB (void);
extern void AnimatorIK_GunAimer_HandleBeginAim_mED490C9A6B63FC0F53CAF4A028EE7D990C694998 (void);
extern void AnimatorIK_GunAimer_HandleEndAim_m644F0421D47C6571AE01E7F2161F7090B41489F4 (void);
extern void AnimatorIK_GunAimer_HandleAnimatorIK_mA453CC18D93EA081C2FB338E4D3D387687109336 (void);
extern void AnimatorIK_GunAimer_get_CurrentAimPosition_mCF809E9D7A10E9AD1450D9B566745DBB8888604D (void);
extern void AnimatorIK_GunAimer__ctor_mB6993777802B4EDDE4458F9F208EE69B8C3DF8BA (void);
extern void AnimatorIK_GunAimer_U3CAwakeU3Eb__11_0_m6DFD87ED5CCEEC6E2BA24D6A49D3F8AD89F0F5D4 (void);
extern void AnimatorIK_GunAimer_U3CAwakeU3Eb__11_1_mA6EF435F045C9D6B5A55189B69893BBF92516A77 (void);
extern void AnimatorIK_GunAimer_U3CAwakeU3Eb__11_2_m3655B9E2CD034643FEFDBB54E3E36894B6211EF4 (void);
extern void AnimatorIK_GunAimer_U3CAwakeU3Eb__11_3_m335B0C1D7A563E6AA6394602ABA380A4ABADB09A (void);
extern void AnimatorIK_GunAimer_U3CAwakeU3Eb__11_4_m1362753DC741433EA2CDFDB105714621E11E473B (void);
extern void AnimatorIK_GunAimer_U3CAwakeU3Eb__11_5_m30C270A8C2BA831C298448AD0D7200DF86C799B1 (void);
extern void AnimatorIK_GunAimer_U3CAwakeU3Eb__11_6_m273A7E16C21D04865DA679C06D3DC79EB971B613 (void);
extern void CameraPriority_UseEffect_get___Usage_mF31043A6F21D0DE3B6530B89937E5D079920AF95 (void);
extern void CameraPriority_UseEffect_OnUse_mF4CC9FDCF461C4EBB4D8CDD1AFDFFFACF2927082 (void);
extern void CameraPriority_UseEffect__ctor_mAD8483CCD694659F6DCC84AA3AB9E9E9AC83B55A (void);
extern void CapabilityUser_Awake_mC7A30F96ED75EC302BC2E70046A3D86459490B77 (void);
extern void CapabilityUser_Update_m022ED15456915BB3C41C02AC5F55FEC252529503 (void);
extern void CapabilityUser__ctor_m4E2A60A6D0370D4A707958593B5E644EF999D681 (void);
extern void Cooldown_UseEffector_get___Usage_m8150A8ABBDCB8167AD0C2ED7EA505DD9C71CC296 (void);
extern void Cooldown_UseEffector_get_IsCooledDown_m6ED8BBA53B475FD00194E5A74427B66AB9EAF639 (void);
extern void Cooldown_UseEffector_Initialize_m3975B80D15E15C7A18AB49A77A3E7D7E2E2CF08F (void);
extern void Cooldown_UseEffector_Update_m68744D21FD6D5D620D732D9534C88BFABE84DA4F (void);
extern void Cooldown_UseEffector_HandleCooldownComplete_m95F3D87EA820BCB8D425D038505CD34B9A2B53C9 (void);
extern void Cooldown_UseEffector_CheckCondition_m16737B9D7243C1A36AE34FF75DF7F7C55B18BC1D (void);
extern void Cooldown_UseEffector_OnUse_m47C59C8B2EAA0F6E74760AAAE5B391DB178AF511 (void);
extern void Cooldown_UseEffector_RegisterCooldownBeginCallback_m076AFF8ADB27454C384E016D7756009E53FE72E3 (void);
extern void Cooldown_UseEffector_UnregisterCooldownBeginCallback_mBA7488114DF4DBED4E775AD6CD7F75CD15C91888 (void);
extern void Cooldown_UseEffector_RegisterCooldownCompleteCallback_m40D63C7CE914FF459D5D61E9FD4834DEA321810E (void);
extern void Cooldown_UseEffector_UnregisterCooldownCompleteCallback_m23C8A603DAB2ED9651D0948DF89239499E3D3FCA (void);
extern void Cooldown_UseEffector_get_FractionalAmount_m267F6B67F4F8B6D69F995295336C6E24B454111A (void);
extern void Cooldown_UseEffector__ctor_m6186376B764B3C3166A6F5F7EA1DEDF248E7297A (void);
extern void Cooldown_UseEffector__cctor_m0323E878834D8410CE6270B12CB3DC1032CC791B (void);
extern void CopyInputFromUser_UseEffect_get___Usage_m4C035DF6D4A5FAE55207F90F9CE128F09EEE6580 (void);
extern void CopyInputFromUser_UseEffect_Initialize_m422F4A315E1DDE11AA8FB4A99463B1BC52C2B1FC (void);
extern void CopyInputFromUser_UseEffect_OnUse_mE9F68D90A4AE142F72C2F4BED3696A479FFD7E15 (void);
extern void CopyInputFromUser_UseEffect_Update_mBC7482457BA3C7B5D0E004E28434815CE4D3BC66 (void);
extern void CopyInputFromUser_UseEffect__ctor_mDC0760127065FE70FBF2783CD2C9BE45144C6F72 (void);
extern void CopyInputFromUser_UseEffect_U3CInitializeU3Eb__5_0_mD268C9DD3AFA565EFB08A937E8D49A3C4BF05A45 (void);
extern void Energy_UseEffector_get___Usage_mDE582A15375DE362B6D37B3D10EA25B07AB66B90 (void);
extern void Energy_UseEffector_Initialize_m9B8B9F52DFBDAD78511BB98A8576B21DE3F3E9B3 (void);
extern void Energy_UseEffector_CheckCondition_m025F8818677D003EFCE6CD34F65520904B62BBDD (void);
extern void Energy_UseEffector_OnUse_m06DEDE0BE4B400C87DACE140184448738A76CC7D (void);
extern void Energy_UseEffector__ctor_m14980499D4D25D2A29B6A32D5F0E9AC03BEFA2D9 (void);
extern void Energy_UseEffector__cctor_mEEE38D85414D614612FDE4018BC376491DFD400E (void);
extern void GunAimer_get_activeAimer_mDFAC1B385BD2A5C611D6DDBAC739B6874DC1AE5D (void);
extern void GunAimer_set_activeAimer_m75468BED3D548624300E94FD3F4CE3A7BC1A74BC (void);
extern void GunAimer_SetAimingFunction_mCCE3BF96EBF69764BDC4DCAAE29D3A037140972A (void);
extern void GunAimer_get_TargetAimPosition_mC68CE94CE469EB7B49F0F113DCBFD38AF5AAB320 (void);
extern void GunAimer_HandleBeginAim_mF4A1DD6CA9B0FCA931676F17F94990203655FCFC (void);
extern void GunAimer_HandleEndAim_mEF4F6282DED97A085AE0891BE0AF61CA0D31CFEE (void);
extern void GunAimer_RegisterBeginAimingCallback_mDF1E11FF1298B315C41BB7C0AB3175A242D06F3B (void);
extern void GunAimer_UnregisterBeginAimingCallback_mBA296FF7B7D62EEE0FBFD1FFB02EC0CCE693C1F4 (void);
extern void GunAimer_RegisterEndAimingCallback_mD7246A365F22369CB0E581A3F7478036C8E34F53 (void);
extern void GunAimer_UnregisterEndAimingCallback_m55FD1974290D2E2E1D7D45886D6DF4CD82592E6A (void);
extern void GunAimer__ctor_mAE14EBDC1B705EE593A553DBC8924FE7CCC73877 (void);
extern void InheritAiming_get___Usage_m5F301393B27E873218681BDABCF79377394960DD (void);
extern void InheritAiming_Awake_m6D13156D2F08B207AAFB384D930685C91E0D7374 (void);
extern void InheritAiming_HandleRiderExit_m2A722B9A6AEE7C5C372563398C87F1787A8B06AD (void);
extern void InheritAiming_HandleRiderEnter_mFE9938A9A4326522FA6F168B2E5DFF81D546153E (void);
extern void InheritAiming_SetAimFromAvailable_mB5360F78B8B13A541C49B7E320FC8E76FDD04942 (void);
extern void InheritAiming__ctor_mE967F913284532DB099899231A208945C3153A58 (void);
extern void U3CU3Ec__cctor_m17B6E726F8DD18323EA3C206A5210BCD0B818376 (void);
extern void U3CU3Ec__ctor_m467F308CEEB24BDB29873D332FF46D22A5598D80 (void);
extern void U3CU3Ec_U3CSetAimFromAvailableU3Eb__7_0_m4847EDA415D711E9A8B907B6E0EB33411E3FF33D (void);
extern void U3CU3Ec_U3CSetAimFromAvailableU3Eb__7_1_mA0F3BEB02DFCA8D154D952AD67A6A6C593771B14 (void);
extern void InputCollector_get___Usage_m578C4159A51F00730DCB59AE24CF75BF066244AC (void);
extern void InputCollector_get_Buttons_m9C1470CE250A3742CEE7F6685572EE622AC80355 (void);
extern void InputCollector_get_Axes_m8EA78032D0FDEBA5666C9622A1994C1BA177673B (void);
extern void InputCollector_add_OnUpdate_mD444761947F848F065033F9BFDAFD7147CC94376 (void);
extern void InputCollector_remove_OnUpdate_m68B0EA704076FE433C5EB1F189BCBF1E7CCA78DE (void);
extern void InputCollector_Update_mBC7E1856AC64839F70D4CA037271C81124BB571C (void);
extern void InputCollector_CopyFrom_mE1E7B9CE94C206170E999A4C5A75355D53553033 (void);
extern void InputCollector_ResetToDefaults_m3E67B40BB7FA7582644AD4BBB8D047705DE978F4 (void);
extern void InputCollector_get_Item_m839B9B9F3C41C0C411607B7BA50FE965E317B3B8 (void);
extern void InputCollector_get_Item_m61BEB9E908C93ECA8C6951718094A2636AFE0A91 (void);
extern void InputCollector__ctor_m2995677A1B7997260903E6836EEFEE740978A1A3 (void);
extern void Knockback_get___Usage_m73C9C568EC565618B89E567573670726084968EA (void);
extern void Knockback_Awake_m6673FC577196B10CC65E9D1A8CA633060E22DFFF (void);
extern void Knockback_OnEnable_m4D0D886823D00F3078AE4CEAC8EED37F329D935F (void);
extern void Knockback_OnDisable_mAA3F78AEFB5DCF0D543215E1B5DA5FB8F2FDCCDF (void);
extern void Knockback_HandleDamageReceived_m8C1F930D69F8DDA3792AD2EE26190A4136FF9B50 (void);
extern void Knockback__ctor_mAC6C3761E25A2EC9C0B2F4D31B69EBFE0ACFC367 (void);
extern void Knockback_UseEffector_get___Usage_m02789F75376761059E53F6E395F30D970179AB3E (void);
extern void Knockback_UseEffector_get_usedTransform_mEF735E09FC44C5A30CFEE6BD9C22315F2A7CAC7B (void);
extern void Knockback_UseEffector_Initialize_m5E55AA144DF7F76A3E2512A28F3D1DAE17AF2C6A (void);
extern void Knockback_UseEffector_OnUse_mEE4C1ADEE7B946BC873473D5DB1DB3AD1399CAF4 (void);
extern void Knockback_UseEffector__ctor_m151C980D86C0A1FC870F182762B6E86D035C8901 (void);
extern void MouseAndCamera_AimEffector_get___Usage_m37FD2C28D550881AE8EE05552ECEBAF9DB7E96C4 (void);
extern void MouseAndCamera_AimEffector_Awake_m28A93E22FC588E66C785A9764B2F269FDF7EA853 (void);
extern void MouseAndCamera_AimEffector_OnEnable_m7BF664A88FE05CE1F3286386D7B4DED870915D0D (void);
extern void MouseAndCamera_AimEffector_GetAimPosition_mE19E83FAEB22901EB8AB68D3C945F8FF1BDD503C (void);
extern void MouseAndCamera_AimEffector__ctor_m7077215BBD3932976274C680B4B1D7091CF512DB (void);
extern void MultiSpawner_UseEffect_get___Usage_mB8AB916489C9E9F847AE247EDD04F67AFF425FDB (void);
extern void MultiSpawner_UseEffect_Initialize_mFED81DD5BFAC64C4C6B133A2862226B4F200BB22 (void);
extern void MultiSpawner_UseEffect_IncrementIndex_m1D6AE26258AF4C15217144B6C2390C3F023E01A0 (void);
extern void MultiSpawner_UseEffect_OnUse_m9D9757BB62AD2DEE2294ED534A82972889FE1645 (void);
extern void MultiSpawner_UseEffect_RegisterSpawnCallback_mC064EE5BE2C839DB3246DEBC506FA63A3B57A397 (void);
extern void MultiSpawner_UseEffect_UnregisterSpawnCallback_m284AFE3C731B9E550692A9E45A5BA3EE5FAD19E2 (void);
extern void MultiSpawner_UseEffect__ctor_m69321DF844FC06BCAD73113E0371A31A9B1596FC (void);
extern void PlayerInputs_get___Usage_m94AF7EC567AC90D8FBB986ADF74540CDD365879D (void);
extern void PlayerInputs_Awake_mF0DD77C3474AA358F625DDF004DFE00FDE4D5E15 (void);
extern void PlayerInputs_Update_mD0626787EAF4BBF03A375F45721DD3EF20E175F8 (void);
extern void PlayerInputs__ctor_m1F4D681AA3CED945BFFFDF3D5A19A1761F44B4FA (void);
extern void RandomUsableGiver_get___Usage_m292A989FEF69382608B654425FD27BCD953EAFD6 (void);
extern void RandomUsableGiver_Start_mCC9A170D5424C737C88204B60D763BE00532803F (void);
extern void RandomUsableGiver__ctor_mE25521B4D832994C5A07C651FE8DCFA464FDAB42 (void);
extern void ReloadUser_get___Usage_m9DB1257956592A99CA50844E6D787DD5C13D1DC8 (void);
extern void ReloadUser_CheckCondition_m6181756FE82661E5EC42EED0578B6BBB75A200B9 (void);
extern void ReloadUser__ctor_m67F5422E91F6D05962C1183C470A2DAD44E88BE1 (void);
extern void Reload_AnimationEvent_Sender_get___Usage_m8C941E599D6D99C3E5614FCB81AA20A0EA433769 (void);
extern void Reload_AnimationEvent_Sender_Awake_m02FC0A1F04B612562339DABDE1499CC550623D9A (void);
extern void Reload_AnimationEvent_Sender_HandleReloadCancel_m165B6F6B84BD7D71408979A4AE491F11D43C2160 (void);
extern void Reload_AnimationEvent_Sender_HandleReloadBegin_m1D10DE51232B845C96EDA562B2D4634DDE97ADE8 (void);
extern void Reload_AnimationEvent_Sender_HandleReloadComplete_mFBFB74AF6981BAF90E1E0CA3F83185EB97217BD0 (void);
extern void Reload_AnimationEvent_Sender__ctor_m1570B0AAAC812DC5B50FF81FD6366E1EB30B17DB (void);
extern void Reload_UseEffector_get___Usage_m872AA113569BD49E134A62023FFD7C432ACB3E09 (void);
extern void Reload_UseEffector_Initialize_m81483EEBBC66E440FEF84BCF75D7B7EB7B679085 (void);
extern void Reload_UseEffector_CheckCondition_m8E924600E383A65D1745502E9245571E7383C749 (void);
extern void Reload_UseEffector_OnEnable_mE7383AE29B6FAA75F4CD52176ED74D556C62E791 (void);
extern void Reload_UseEffector_OnDisable_m8E031B0AC32E7CF3D47430C9DBFACD50DF244285 (void);
extern void Reload_UseEffector_OnUse_m612A6AA0FD90F4A2F33CFFF89A90BC72EB223E0C (void);
extern void Reload_UseEffector_reloadRoutine_m7805C5F162F96D5682BE1C0D28044823EEB1F2C7 (void);
extern void Reload_UseEffector_RegisterBeginReloadCallback_mE43064C3DD45EC2338E95A7933936AE8CF493CB9 (void);
extern void Reload_UseEffector_UnregisterBeginReloadCallback_m9C498ECEF7F33FD94812E9BE24B824D37FA4361E (void);
extern void Reload_UseEffector_RegisterEndReloadCallback_m5E18C6420FCE9BD3D0F0CA335114E82B935B5287 (void);
extern void Reload_UseEffector_UnregisterEndReloadCallback_m8841FBAA2AE5F83F377A4C3A495A5F7F3C3B39C3 (void);
extern void Reload_UseEffector_RegisterCancelReloadCallback_m473FD0FC6ACDAE9000DA5C53C136E8836D1600A4 (void);
extern void Reload_UseEffector_UnregisterCancelReloadCallback_mC062EEC137A18BA9D2F868A2CAC775FD3B74180F (void);
extern void Reload_UseEffector__ctor_m2F622561A1BFCA3D1C6520E69A9F406909FFE69B (void);
extern void Reload_UseEffector__cctor_mD2E76717A191C73DA600C9B44AE9396CAB6D43E8 (void);
extern void U3CreloadRoutineU3Ed__17__ctor_m27C80C37CF20CD988BBEA45F3FF7805B63D53014 (void);
extern void U3CreloadRoutineU3Ed__17_System_IDisposable_Dispose_m5E20691E511A64D46312C3CC9B60D55A3F329528 (void);
extern void U3CreloadRoutineU3Ed__17_MoveNext_mF3057D56CC6ED471BCC60C364BAB71632E9AC966 (void);
extern void U3CreloadRoutineU3Ed__17_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mC2B6A11D5BF086E017DB43B0928E20CAE3CDA3A6 (void);
extern void U3CreloadRoutineU3Ed__17_System_Collections_IEnumerator_Reset_mBA80846F7853DD89E49325DC0CA61AC9572E14C5 (void);
extern void U3CreloadRoutineU3Ed__17_System_Collections_IEnumerator_get_Current_mFC0F7196D8607239FEF22E82F7B63FE49BB911A4 (void);
extern void RespawnAtStart_get___Usage_mA7697681A4316C9195509065ACC287BA93BD12C7 (void);
extern void RespawnAtStart_Start_m83CC51E6735B73700F512F48C1007BFEDDDE1C00 (void);
extern void RespawnAtStart_HandleRespawn_mFB1FD04365C4084B52931A50A909A6F8E26D8A96 (void);
extern void RespawnAtStart__ctor_mF4E428378A35FDA69433673C67CB36228761E19C (void);
extern void Respawner_get___Usage_m08869B8D073E4AC6BEED5ACE34C9E4E67F65950B (void);
extern void Respawner_RegisterRespawnCallback_m46EFBA05DCB309608EF1CB0DD875DC53FA7A476E (void);
extern void Respawner_UnregisterRespawnCallback_m3FE0BB718A772DE21EDB41523E7EF3668A44D51B (void);
extern void Respawner_Respawn_m76DA094374FAC7039C59E37278441B8D0EF161B0 (void);
extern void Respawner__ctor_m1129F16A445FC90DA0A6BDB186246E2B82885922 (void);
extern void RideEnter_UseEffector_get___Usage_mCFD8FAFF68A35AEA0AABEEC296C66B6EB5098BC6 (void);
extern void RideEnter_UseEffector_Initialize_m100D1D77E2C3942B715541DA7352CC289566384A (void);
extern void RideEnter_UseEffector_CheckCondition_mC88AC280C2748E9F73FAB2C0B4CA4A3C4FE4CDC5 (void);
extern void RideEnter_UseEffector_OnUse_mBF5B3A7C660EB25FD5D6F4FAE38AAFEE5D31A661 (void);
extern void RideEnter_UseEffector__ctor_m34377E71212269926C47F84230DC67B55AB7BA44 (void);
extern void RideEnter_UseEffector__cctor_mEB2890E2E4013BE0C8D6FCA0E89B12AD0E72E6F2 (void);
extern void RideExit_UseEffector_get___Usage_m55AF21037FDF63B2624339866B12079E207245EE (void);
extern void RideExit_UseEffector_Initialize_m325AA3692BE96D90D26B4E5F6892E3FE0DBA21E4 (void);
extern void RideExit_UseEffector_CheckCondition_mD5516F19B0FB532484C33B78588C3F7402671839 (void);
extern void RideExit_UseEffector_OnUse_m4058117ACC60A88E014738842C6EF147D8AF5452 (void);
extern void RideExit_UseEffector__ctor_mD9999D8118956E8A4021B8C424610D156012C6B3 (void);
extern void RideExit_UseEffector__cctor_mB667D73DFAB5EBC4D3E0B1D91930D1AC9C05AFE7 (void);
extern void ShootUser_get___Usage_m8DFB0C1EB93E6C5C3990AB099B66924F206DC152 (void);
extern void ShootUser_CheckCondition_m0BC551372080761157C5EC8237C20D41470E0E83 (void);
extern void ShootUser__ctor_mBF52D26ABA981D11B749D4E071309F007F97CDA6 (void);
extern void SoldierMovementFromInputs_get___Usage_mE08C2FE16D7E3B6C888ECE54EC052E1F73A789F4 (void);
extern void SoldierMovementFromInputs_get_isGrounded_m3F4CACFA8658BB7BE453FB02858318A781AE093E (void);
extern void SoldierMovementFromInputs_get_IsRunning_mCB0D4E9E800F76BAE8BF261F096022BDE55B517F (void);
extern void SoldierMovementFromInputs_get_Velocity_mCC9DCBA007A6C5E3B747C2B23C7BF2BB3538BC13 (void);
extern void SoldierMovementFromInputs_set_Velocity_mA42126E739F505225428F829CE8594AA039F3E18 (void);
extern void SoldierMovementFromInputs_get_LocalVelocity_mF625D55222600BBB494E521E2C94FBFC862D2288 (void);
extern void SoldierMovementFromInputs_Awake_m55EBDC94786F5E124C665A204715F989BB7AF035 (void);
extern void SoldierMovementFromInputs_HandleOptionalComponents_m2A377C2533B0DDDC5A7A6EA379E6688810E3F4E5 (void);
extern void SoldierMovementFromInputs_Update_mDF9E92E96D422060A0EEE67A4DD3DA139E532402 (void);
extern void SoldierMovementFromInputs_OnDisable_mC1450432A8F95696A965713D89A8F310DAA28F3B (void);
extern void SoldierMovementFromInputs_RegisterJumpCallback_mE439CED5EABE5BB1951E562311D94A529A81701E (void);
extern void SoldierMovementFromInputs_UnregisterJumpCallback_m9789312F1D6CF12FCE71B16552E808C56618BC71 (void);
extern void SoldierMovementFromInputs__ctor_m95FF8809975BCCEB48483E3D9B539BC46D2FFC18 (void);
extern void SoldierMovementFromInputs_U3CHandleOptionalComponentsU3Eb__24_0_mFD2260E74DA836937F3626026C4240AE218E619A (void);
extern void SoldierMovementFromInputs_U3CHandleOptionalComponentsU3Eb__24_1_mC0EF5F13D0FDF4F63E768B474858A1FC911C8FBD (void);
extern void SoldierMovementFromInputs_U3CHandleOptionalComponentsU3Eb__24_2_mDB2D50A18BB8FBCE1799EF5E14807C7AD1FDE62B (void);
extern void SoldierMovementFromInputs_U3CHandleOptionalComponentsU3Eb__24_3_mEF26E4748AB7D722EBA2453DD5F2E4B4175B1335 (void);
extern void SoldierMovementFromInputs_U3CHandleOptionalComponentsU3Eb__24_4_mA3E2A9028E7B878049475693E4DDEBBE9A9F80DC (void);
extern void SoldierMovementFromInputs_U3CHandleOptionalComponentsU3Eb__24_5_mEEF4F5B86B8535E88D77A4357AF62D870729386E (void);
extern void Soldier_UsableHolder_get___Usage_mDD0D59BEFE028C5256A699630F8E5262F25ECB18 (void);
extern void Soldier_UsableHolder_Reset_m47E58A897A370F64151E03FA5DB12E050D9C8A90 (void);
extern void Soldier_UsableHolder_Start_m816C424DE553FBA38D4A85FB5A98AD121D39D54E (void);
extern void Soldier_UsableHolder_HandleChange_m56E49481CD51A1D592246BC070F3B5E811B7D150 (void);
extern void Soldier_UsableHolder_CreateUsable_m186C514598E329BEAA1A17FF10D0AA5AA111FFBE (void);
extern void Soldier_UsableHolder__ctor_m320171F3AF800481E941264C132F66ED80B91317 (void);
extern void U3CU3Ec__cctor_mD6890FBCC928E2823B9191FB27C347AD13853F52 (void);
extern void U3CU3Ec__ctor_m32624B04F2ECD5E2BEECAF33AD3AE068BA042AE2 (void);
extern void U3CU3Ec_U3CResetU3Eb__3_0_mC60E68C4181A1C8CE12B9390BC9550E49AED4C46 (void);
extern void Spawner_UseEffect_get___Usage_m2F2ABB0AD0EC1F918397C0CA05FA04E96F4B2F2C (void);
extern void Spawner_UseEffect_get_SpawnAtTransform_mACFC3F0F168F41E72DE9A05D5D7AC1B22449AC98 (void);
extern void Spawner_UseEffect_Initialize_mAD21DB73B88B2CA79F47EE2EFD770630A315AE60 (void);
extern void Spawner_UseEffect_OnUse_mCBF22D32E708556946FC687D1C0A3FE3D239DBA4 (void);
extern void Spawner_UseEffect_RegisterSpawnCallback_m77121397302634D504D18AB86FFB71B35BDCF680 (void);
extern void Spawner_UseEffect_UnregisterSpawnCallback_mD7551ACDFCC2F37423EDBD6F37EB0C59B1DAAC52 (void);
extern void Spawner_UseEffect__ctor_m83941919BCAE20C2F3E93646ED887C5288B47267 (void);
extern void StandardRider_get___Usage_m779DD3AFF670DB6CBFACF896EE55B322298CF47F (void);
extern void StandardRider_Awake_mE8A7302987CCBC6663D8123BA449B61643D68A51 (void);
extern void StandardRider_HandleEnterRideable_m0CAC899F6F6DA4F503261973805A6A1CA1691141 (void);
extern void StandardRider_HandleExitRideable_mCE22501D7D318D4265BBD55612C59A08EC1C6E8F (void);
extern void StandardRider__ctor_mF383031339B38BB78EF3EEC88B63497FAD6074D9 (void);
extern void U3CU3Ec__cctor_m91613CD06184F30DA4AE482EC7049046F0009CE3 (void);
extern void U3CU3Ec__ctor_m42B4C7FE67C523310B5305948BD464806BC32C6D (void);
extern void U3CU3Ec_U3CHandleExitRideableU3Eb__7_0_m276A6E93837FEE817ED6D6CA4D23B168512B3B88 (void);
extern void TiltTowardsPosition_Input_get___Usage_m2829472A55D0BEAECA121E2A5572116F85D429A9 (void);
extern void TiltTowardsPosition_Input_get_TargetPosition_m0EF95B68D2DBEF9A2946B2F2AF997811888E7AFD (void);
extern void TiltTowardsPosition_Input_set_TargetPosition_m579F9079506B35AF2B837725840053092D5FBBA5 (void);
extern void TiltTowardsPosition_Input_get_IsSeeking_m80FC06BD506BC1FB0393980F9B3DEC63C2FCDEAE (void);
extern void TiltTowardsPosition_Input_set_IsSeeking_mE8304B31B31B14924DB2E7015BEA641D4596AB23 (void);
extern void TiltTowardsPosition_Input_Awake_m00232CAB502C0C02353AB2B479F02DFF71F79277 (void);
extern void TiltTowardsPosition_Input_Start_m8D385642A3C5BCC83AC82AB3B064862F36746242 (void);
extern void TiltTowardsPosition_Input_Update_m82383ED3117CD03EB7B9CDB35A5C18C05B6E9DD4 (void);
extern void TiltTowardsPosition_Input_Arrived_mCF139DD6DB00916BF7C05852968A77E7AFFFC466 (void);
extern void TiltTowardsPosition_Input_GetFlattenedDisplacement_m43341EDA5D817FE4149C64F7A4B3C06E450BFAD1 (void);
extern void TiltTowardsPosition_Input_get_isUsingPathing_m4ADDCF6E2F533FB473E5A141E22177B85B3A2352 (void);
extern void TiltTowardsPosition_Input_SetTarget_m40611F45514234BB006A5CA71D56742BB5E9E7DA (void);
extern void TiltTowardsPosition_Input_DoPath_m9C30DF0F496D56FB8019B41DFAE4B7E9919BA843 (void);
extern void TiltTowardsPosition_Input_RegisterReachedDestinationCallback_m5B040F31B2DC75C66719644369686CD3F83624D3 (void);
extern void TiltTowardsPosition_Input__ctor_mA4346D788DFBAB92E5F4232248850828FA4B518C (void);
extern void U3CU3Ec__cctor_mFBBB90FEB0BAD1D5536D287E517ABD8B4787D4FC (void);
extern void U3CU3Ec__ctor_mDA2F15F9AE0A7A94FE16D9F4A7B278369F0049F7 (void);
extern void U3CU3Ec_U3CArrivedU3Eb__18_0_m20A525EBBE10C08441AE05D7D407902421E8B249 (void);
extern void U3CU3Ec_U3CSetTargetU3Eb__24_0_m809E1C77A9A444CD3D22602B4C837D10560E289D (void);
extern void U3CU3Ec_U3C_ctorU3Eb__27_0_m9D3D83B879CE852117817DDAA8664585587D22DA (void);
extern void Turret_GunAimer_get___Usage_mB84BEC76DB6A10F9A1DAC53C34A10EAB4FC9AAE9 (void);
extern void Turret_GunAimer_OnValidate_m8B08A93FDD22D64885D61988D5C4DBFBE006DA21 (void);
extern void Turret_GunAimer_Awake_m9CF28076CFF58E107BDF5AF59904D08C6D2320BC (void);
extern void Turret_GunAimer_OnEnable_mE457419D1370C6DDB4DEED5AD2AF65414203B428 (void);
extern void Turret_GunAimer_OnDisable_m5CA69B3B94E5BEF830476D86F622113045A9B32D (void);
extern void Turret_GunAimer_Update_m6020D5C4E2709AC07A8ACF2EECBD28C0C84C892D (void);
extern void Turret_GunAimer_get_CurrentAimPosition_mFD8D41512DDBC16B4A91439801EFF585B91E8E6C (void);
extern void Turret_GunAimer__ctor_mDC1679809DA53AE8E468B2040DD32D1459E6F83B (void);
extern void Usable_get___Usage_m056C2F1DF0B953561B983E3A72FB3CD1420FA7A5 (void);
extern void Usable_OnValidate_mFBCFE1B708BF631C43A05308A802EEB8B6725B67 (void);
extern void Usable_Awake_m7EEF5625EF7D2B677845493782AA39A800904D0F (void);
extern void Usable_TryInitialize_m6B183850A5CB739232B40682EF101E7DA1AA1CBD (void);
extern void Usable_RegisterCondition_mDD6C4C2B8B8CA038C3EFBCEA3F4AD9C03A4EDCA3 (void);
extern void Usable_UnregisterCondition_mA6648F651DFD408849133AFF98CFFB35DB564A84 (void);
extern void Usable_RegisterEffect_m22384AA245F5B8A6AC4449138DA7A7E2F73D3FC4 (void);
extern void Usable_UnregisterEffect_mFF5F4E218388DF751CE407C0998D1CC5C25438D6 (void);
extern void Usable_TryUse_mA87C60AA2C787E8F6D0033279FBC403A674C8C75 (void);
extern void Usable_CanUse_mA5CC666397CD8B04B09B8E3BBB20EAB00D863A78 (void);
extern void Usable_CheckConditions_mB60F51823368EBC2307EAE835FA6DDBB67BBEF60 (void);
extern void Usable_HasCapability_m06FBE9723C009ED1F592395B334F798B0CE4C0A7 (void);
extern void Usable_CheckCondition_m7DD065F050D8366D3E8630542F61ACE568296CEC (void);
extern void Usable_RegisterUseCallback_mAA315DBC6CE7E54FD5D9CCF791F743D585DE86A8 (void);
extern void Usable_UnregisterUseCallback_m0FCB909354E27CB35407ED6C3D7440DE6D1595E9 (void);
extern void Usable__ctor_mEB934BD4ACD59FE4FB2EB887874375C14BEB64C8 (void);
extern void Usable__cctor_m4BD411EC8D31CF54E23AF50C6B077F564ACE6E39 (void);
extern void U3CU3Ec__cctor_m793ADBEEAED255CEF3078201EFA8399B16F5ED32 (void);
extern void U3CU3Ec__ctor_m329C31F89AF08F1346320099DE5096EC476B58E8 (void);
extern void U3CU3Ec_U3COnValidateU3Eb__11_0_m9B49A7E8A9EED6613BE44024ABF86340D0F2BB4F (void);
extern void U3CU3Ec_U3COnValidateU3Eb__11_1_m2BE6793347EC584A31919911F3C1D8D1AB5721BD (void);
extern void U3CU3Ec__DisplayClass22_0__ctor_m5DC6BEBABBC5BC6000BB1CCD1C38DEA405E5FE28 (void);
extern void U3CU3Ec__DisplayClass22_0_U3CCheckConditionsU3Eb__0_mF1AB5966B6FB11E9CFA51EC7FE8778C0A55B02C8 (void);
extern void Vehicle_UsableHolder_get___Usage_m6EE59F00C1A3503AF0CC61F5CC37252B91D1B196 (void);
extern void Vehicle_UsableHolder_Start_m6F1FC0C3B4450CE55E94EC1A91E9B0601F137DC2 (void);
extern void Vehicle_UsableHolder_Use_mBCCCE718A9DD7A0F3C942FE0076EB0A2F9760E7C (void);
extern void Vehicle_UsableHolder_GetUser_m969A76F4C41CCB39F4D2336D025D34866EB0D708 (void);
extern void Vehicle_UsableHolder_RegisterUseCallback_mEFFAFA86A2FE6F52E079F463B62F3371529F2B1F (void);
extern void Vehicle_UsableHolder_UnregisterUseCallback_mB5886EAE453390B9FF3F884BBC98411634178A94 (void);
extern void Vehicle_UsableHolder_RegisterUseFailureCallback_mE3BB28D4C78202378D2641D0609B8E853E0B668C (void);
extern void Vehicle_UsableHolder_UnregisterUseFailureCallback_mE1C8EFBC3320DA605EA09D549A35338890241690 (void);
extern void Vehicle_UsableHolder_CheckConditions_m15E053942655A9E7213A53D5189A83D64876F2A4 (void);
extern void Vehicle_UsableHolder__ctor_mEFCB878F38B43CFA9FEBF1BB381B11F0CB7B5059 (void);
extern void Vehicle_UsableHolder_U3CStartU3Eb__6_0_m8263A8998402632EA4ABBE383F6C0CDF8DD47B76 (void);
extern void U3CU3Ec__DisplayClass13_0__ctor_m1DA92F9A89EB35F0D3DA0EA1E1D0F73DB8DFDE3E (void);
extern void U3CU3Ec__DisplayClass13_0_U3CCheckConditionsU3Eb__0_m3234F7111F47A86BED3BFDDA10F978B69E6CE8B0 (void);
extern void BaseEvent__ctor_m3EC56ABEA6769A0F31612C856FCD37FB90798369 (void);
extern void DynamicUsableHolder_get_Usable_m47C85E90C4F28456D6B1DF5BAC3274CF87A915D7 (void);
extern void DynamicUsableHolder_set_Usable_mAC8C5ACB1A9D5D451C0860C9B2C27EC59CFFEBCC (void);
extern void DynamicUsableHolder_Awake_m2A34913D565DA062147A018C46D9B45A7C88C04F (void);
extern void DynamicUsableHolder_ActivateAndDeactivate_mC5391A134ABEB4B6F82EBED66CF20B1D1942D290 (void);
extern void DynamicUsableHolder_AimAt_m6A70492BA050AE3E2F8C0AC95ED05C23A9269FEB (void);
extern void DynamicUsableHolder_SetUsableLocalRotation_m88332182B6307721A81216ADE40E5524AF728F43 (void);
extern void DynamicUsableHolder_ClearUsableRotation_m11DDAEF03B6C550B76951638B17A84385A154EE4 (void);
extern void DynamicUsableHolder_Use_mAF454A55371683E5448CED4CE715AA9E5B594EEE (void);
extern void DynamicUsableHolder_RegisterUseCallback_m9287B4197108C85125F4B48B566A7246D7112A6F (void);
extern void DynamicUsableHolder_UnregisterUseCallback_m58CADCF619F1D8425B400AF7260F9749BEF1E2EA (void);
extern void DynamicUsableHolder_RegisterUseFailureCallback_m6F4C633DE661B39255B26AD31887A94D8139D99C (void);
extern void DynamicUsableHolder_UnregisterUseFailureCallback_mAD6905181B69EADA942408EFA2F41FDFEE7F35E4 (void);
extern void DynamicUsableHolder_CheckConditions_mA45DBDE0070902BC2EDCEF58535EDB9CDAC0B307 (void);
extern void DynamicUsableHolder__ctor_mC0F8F8C1EB74DE71D4D4C9363B867242456A7936 (void);
extern void FloatReservoir_ConsumeIfPossible_mD3576C19F0B1AEA1D36D168418BDABDB5BACABF3 (void);
extern void FloatReservoir_get_FractionalAmount_mB860E09BB968719F0F5A4A3BFC9846C66423A717 (void);
extern void FloatReservoir_doExtractPossible_m5A646233A5CEAA74B9CBA523867D2060811E634F (void);
extern void FloatReservoir_CanFullyExtract_m13AAC1DECC2C444139F1288D110DA53139939829 (void);
extern void FloatReservoir_doInsertPossible_m36B8E603C424BBBA6DD3B3A4B1848010762879FB (void);
extern void FloatReservoir_CanFullyInsert_m149086B02E388E81A5CD721FC3000166489F8F67 (void);
extern void FloatReservoir__ctor_m55EA71AA381040252AD62FC94600850921B04C21 (void);
extern void FuzzyComponent_Awake_mFE5EABD0DE6B1DF5BB2046E7C9DE7C8FFAA7DEDE (void);
extern void FuzzyComponent_GetWeight_m12FE33AD06D47CDA31C148FD047284C4D92D3820 (void);
extern void FuzzyComponent_get_IsActive_mDD86493D7A6A92ED3C4BF5BB813343E0391EEBBE (void);
extern void FuzzyComponent_set_IsActive_mF6F9A76981291DC62C1F73A5016C82D5D0FBA7E7 (void);
extern void FuzzyComponent_OnEnable_m5B154B22E526EB4C35D366C3DC95AB6FAF46A124 (void);
extern void FuzzyComponent_OnDisable_m0139D078D363966BDE320AA223D3317F6B918196 (void);
extern void FuzzyComponent_OnEnabled_m43BB8CA0845AFB9D0EA891D8019656760F4BCAFD (void);
extern void FuzzyComponent_OnDisabled_m939F06960AB3804E4F96B5F039402AD9EB8A1303 (void);
extern void FuzzyComponent_OnActivate_m5811B009ACED7257537C913AC739D099A95EDB3C (void);
extern void FuzzyComponent_OnDeactivate_m97D2AC6AD8E7301EC6139F062BC898886ED78629 (void);
extern void FuzzyComponent_TryActivate_m18CD08B851EC81DCE5511E2D0870EAAF88437C27 (void);
extern void FuzzyComponent_TryDeactivate_mE7008B26BB2E998FB74E16C526D9364D324BC350 (void);
extern void FuzzyComponent__ctor_m25C9A9330C4FF65B9F23895834F6486EDE8D1E61 (void);
extern void FuzzyStateExtensions_SetActiveState_mAE696AF75A1BE708323FE6800E9DA2E880AE8E66 (void);
extern void Pickup_Awake_mFC6C330AAA9C7B64D13F662EC7C1B8AF7AA6A99E (void);
extern void Pickup_GetPickUp_m06797A303180BE5CE06D5BED3978DF5B41B781B0 (void);
extern void Pickup__ctor_mD1B7E738B0B399C5363B3A33B1C1BF4A61156EC7 (void);
extern void Pickup_U3CAwakeU3Eb__2_0_m25A64B68CFD20AD9E1F47DE4354BC8A136BB5304 (void);
extern void PickupCollector__ctor_m1A03DDCD14254817A3CA050D55D2688F40B8A563 (void);
extern void Reservoir__ctor_mCE3392BF96108B6D2E355348F56E72BEC511089A (void);
extern void RiderBase_get_checkTransform_m7BED960029869BCA7844EB3992E77D661BA491A0 (void);
extern void RiderBase_get_isRiding_mBF43DCFE3678200BBD2A175C508B0A27F390227D (void);
extern void RiderBase_Awake_m6134CF96D2D86A9B60F8B77D776C530CCB88852A (void);
extern void RiderBase_OnEnable_mFEB736D4F59C95033B4FE7DC80C24767C0F35055 (void);
extern void RiderBase_OnDisable_m1D77D78767C4FF10C2C55C7A5BA65866C212D743 (void);
extern void RiderBase_HandleInteract_mAB00F2721F03BC1C9D710A5B2FF39A583D63EC0B (void);
extern void RiderBase_Update_m84E4537A13649F91C7105CD0F77D57F6CAD89845 (void);
extern void RiderBase_DoCheck_mF9DB02B7F37538073AE8AF0EBCD687623BFCA6DA (void);
extern void RiderBase_EnterRide_m3EA4A16EC1E1DEC4B0B37F72CBF64FD53802E35B (void);
extern void RiderBase_ExitRide_mC25770FC2420D8F88545B43749BECA018D85045B (void);
extern void RiderBase_RegisterEnterCallback_m295454252E8BB89B13A143190F430720473B349E (void);
extern void RiderBase_RegisterExitCallback_m1E2C4E0691D9AA3E93858EAC843395985CC1302C (void);
extern void RiderBase_UnregisterEnterCallback_m188CB529351DE12242E2E2CAB1FBC91F3D2F7EC0 (void);
extern void RiderBase_UnregisterExitCallback_m2800AABB0FD4843373C8177D4469E7752448FE6C (void);
extern void RiderBase__ctor_mDDAF0FD00FB6B1D22BB3B5501AED4D3DC86628BF (void);
extern void RiderBase__cctor_m2C2CEBA85895F166DE2BB986667676A309E16A33 (void);
extern void U3CU3Ec__cctor_m2D59570100343F0B531695B247B2CF00D1F1AD99 (void);
extern void U3CU3Ec__ctor_mF333CC3EF20EB31BB8B8945C94D165530505B032 (void);
extern void U3CU3Ec_U3CExitRideU3Eb__26_0_mFC36F9ECA1161DE54B938FB967921FBE298848E9 (void);
extern void U3CU3Ec_U3CExitRideU3Eb__26_1_mACB653E3E7488BF60C771B5945D1E7D49DE17C79 (void);
extern void Tracker__ctor_m66EE6911664A8E53A804343CD14BCBAC26469416 (void);
extern void UseCondition_Awake_m361BF2A79FF130BD450CB91153CA64B659AD2B4E (void);
extern void UseCondition_Initialize_m4E0150D0E964B452E256736907B789F7D6B411DC (void);
extern void UseCondition_OnEnable_mC048BE7EAC44324EE4619CD0320726467FA0A51A (void);
extern void UseCondition_OnDisable_m6532BC3FB08416FA3389CB51F42757478D5033BA (void);
extern void UseCondition__ctor_m500C5852831DC3470DAD39457F900F2D477A06FD (void);
extern void UseEffect_Awake_m38015C355A1DC275BE401EBB51AA3467FC78CFBC (void);
extern void UseEffect_Initialize_mC3316F3F9F607EF19A89D1294C5457EB29A67B46 (void);
extern void UseEffect_OnEnable_mC69C5FD4D6B2DDA49CBA609778B7CBCB154734FC (void);
extern void UseEffect_OnDisable_m1621A4CEA1BFB69FE7DE1F7E9ED3FC7E09D293ED (void);
extern void UseEffect__ctor_m1F17804A40880735A32EFF7D3361975FEC279E54 (void);
extern void UseEffector_Awake_mA7888F82C703E7E736C6E0464535D7383B57940B (void);
extern void UseEffector_Initialize_mFAFD36F53F7187FA1F54752CC12452F37FE82B89 (void);
extern void UseEffector_OnEnable_m98E759907C0DC10E189B1166946F57BB9B89B2E3 (void);
extern void UseEffector_OnDisable_m3166E2467C93837DCAFB4D28A33389D8E4A2B9BB (void);
extern void UseEffector__ctor_m3B5CC4992612B4E6D9A8A21713D6394CA81A2DD5 (void);
extern void AggressionTarget_get___Usage_mB0581D2CD984CC0AB3C04E7FDF9C0056C2CD7CC9 (void);
extern void AggressionTarget_get_Target_mABD905B8323DD0F53897EAE951FAE33514498248 (void);
extern void AggressionTarget_set_Target_mE4DF5309270CA57759F3F316556E32C1F1CD80FE (void);
extern void AggressionTarget_Awake_mBC50D7BEF0370936CE11B5FD6C18B0FABEEC485F (void);
extern void AggressionTarget_Update_m9E8805EDFC5A85D5533AF5D680C6BF6C034BDB06 (void);
extern void AggressionTarget_get_CanShoot_m8D5D2B2B1CE7EAE6969073D80DFEF3900EA8D46E (void);
extern void AggressionTarget_set_CanShoot_mB196E28EB3EBDEB3728143A7F5488DC2B3063027 (void);
extern void AggressionTarget_get_CanShootAndVisible_m4D51B92F6913DF441E741DFB98FFDF31AE09EB59 (void);
extern void AggressionTarget_set_CanShootAndVisible_m7058CD78D1CDE1950197ABCA7348643723FAA3B8 (void);
extern void AggressionTarget__ctor_mC308D7616C427EC7AA30F8D0D96949CCF0CE1CDD (void);
extern void U3CU3Ec__cctor_m21CBB73125A8C5037AEC18F34185E2C1A2FBE146 (void);
extern void U3CU3Ec__ctor_m9423320E2FC416756C57CB2812872AC6F0FDF375 (void);
extern void U3CU3Ec_U3CUpdateU3Eb__9_0_m71BDD9158AA7F948E77BEA5EF9F3B0489CC28EAB (void);
extern void AmmoSeek_AI_get_FuzzyLayer_m0705D2E3A9536ECAEB0C4E39D74B19D24535C1ED (void);
extern void AmmoSeek_AI_get___Usage_m703C702400071103403111044485A700ADE35949 (void);
extern void AmmoSeek_AI__ctor_m2042B5C20AD14D1F47F7307ADBD230A1E857DCE2 (void);
extern void FactionReference_IsHostileTo_mC056D21BEC48887ECDC457535E53D62438BDBF26 (void);
extern void FactionReference_IsAlliedWith_mB56CDAE7CD079B4F17F80918EAA2D6403C94B35C (void);
extern void FactionReference__ctor_mD0111AF6BC0099C1D6BDA3026ACAD6CAECBAA1F2 (void);
extern void FuzzyMachine_get___Usage_m78968EB823815CDA3202B3E8EEB389D89649C1CC (void);
extern void FuzzyMachine_get_FuzzyStates_m09286854F0DA56CF4D4AF647F193AF075865C607 (void);
extern void FuzzyMachine_GetActiveState_m3E5899A06A4CED53ABBE7F9B7FCFB7A5ABB71BA2 (void);
extern void FuzzyMachine_GetActiveStates_m27061741962F7D885419EE85875939C5AAB3E0A0 (void);
extern void FuzzyMachine_Awake_mEC2E3A80FB6A245BF96D9C11443CA9928D6E058D (void);
extern void FuzzyMachine_HandleDeath_m4772C0BF7FB80139C8A69C16F1554F9C26E99ADC (void);
extern void FuzzyMachine_OnDisable_m9F1D3C709CA0455DDFFFE56E3DF44A85FC9008B5 (void);
extern void FuzzyMachine_DeactivateAll_m419FD975A8BDD2E326D88A58E1B3D445F2539CDB (void);
extern void FuzzyMachine_RegisterFuzzyState_mC341A91758C0E51E01B0821CA85AF8A4D115E411 (void);
extern void FuzzyMachine_UnregisterFuzzyState_m8DF01F9C585DB89CA9780E8252BA26D346232701 (void);
extern void FuzzyMachine_Update_m80C897366F20C8CD6281EACA5EA254040D9F56A1 (void);
extern void FuzzyMachine__ctor_mEA00C4CE53B0B3C1EAB96BE17BBB61832BD9BA83 (void);
extern void StateEnterEvent__ctor_m198F6230C7B129EB6D164B264955195B01E4B55A (void);
extern void U3CU3Ec__cctor_m5EC22FCA61324600323C61E92F5291EDF82CC049 (void);
extern void U3CU3Ec__ctor_m51520DA9445C01762227F82BE4E2063417A265BC (void);
extern void U3CU3Ec_U3CDeactivateAllU3Eb__13_0_m9D8C2B3910C869B0CA91B7DDC421FCF89DF2ECA2 (void);
extern void U3CU3Ec_U3CUpdateU3Eb__16_0_m676C7F3534730EF7099325AEE6D391F44089389F (void);
extern void GrudgeHolder_get___Usage_mA179036E56026F2083E180F6A3679B5F938F50C8 (void);
extern void GrudgeHolder_get_GrudgeTarget_m271D9D9E0338CBED15674F926C1EB231981DDFEE (void);
extern void GrudgeHolder_Awake_mB99B3FF933D1C19422F90731D52F31532F2DBF0C (void);
extern void GrudgeHolder_Update_mDB5DAAFDB44731F686EC865B28E211E76D3A619A (void);
extern void GrudgeHolder_GetWeight_m440623811DEE24045AD41293D4030EA15C7C619C (void);
extern void GrudgeHolder_CalculateWeight_m58810D17711B92222DA3D19A88E443DEA500C06A (void);
extern void GrudgeHolder_OnEnable_m28AA1DD83E4A5ADA82C552A5AF0CA738675DABA4 (void);
extern void GrudgeHolder_OnDisable_m5E3421CCE19160DEABBB4467517A7C0AAC266B72 (void);
extern void GrudgeHolder_HandleDamageReceived_mF45360643BC4F5FC7206F794C4AEFAD293E1A07E (void);
extern void GrudgeHolder__ctor_m1E3C45F9C3832E6BB47369B3D1F3A5628FC1D7AE (void);
extern void GrudgeHolder__cctor_m4D1A3AE748DD9DF532ACE31D9EED28A3A0557AE9 (void);
extern void U3CU3Ec__cctor_mDB61A0695634EB612F003E91F93048ECE4133328 (void);
extern void U3CU3Ec__ctor_m0A5047A0774BE7B0EE387ABDBF34F44DA8098024 (void);
extern void U3CU3Ec_U3CUpdateU3Eb__13_0_mDAAF76A35EE5ED1B735FBF80DBE0EA5F2952FA04 (void);
extern void GrudgeTarget_AI_get___Usage_m06C638A8F6BC81A5504C66CC35CE69F97CED3F0F (void);
extern void GrudgeTarget_AI_get_FuzzyLayer_m84D2AE00B82AFDD286BE271087435EE17F50CBF1 (void);
extern void GrudgeTarget_AI_Initialize_mDB7CCEA56DFEBFA92F386DCB5D1692DAD0039DC3 (void);
extern void GrudgeTarget_AI_CalculateWeight_m114B977155A8F143DBCB307245A84A392753676C (void);
extern void GrudgeTarget_AI_ActiveUpdate_m9CE23BD416C1665DEBEE6707CBB147FAF28DDFD2 (void);
extern void GrudgeTarget_AI_HandleActivate_mCF2CACFF3834B30481A60D75C18DBC36B2B51588 (void);
extern void GrudgeTarget_AI_HandleDeactivate_mE7BA03AB99278B6E1E41CDC7FC16910696280056 (void);
extern void GrudgeTarget_AI__ctor_m14E0E718E9B696F73FDC7B9B1B894BCF74858121 (void);
extern void HostileTargeter_AI_get___Usage_m5AC8CE40C32197B99C00A601864E8DA8406016A7 (void);
extern void HostileTargeter_AI_get_FuzzyLayer_m74697172B8166CCD233BD4233DA95592058CA555 (void);
extern void HostileTargeter_AI_Reset_m76253B7A8986582C4E3BB001AF2C6E24EE5DE0A0 (void);
extern void HostileTargeter_AI_Initialize_m4372C8A45260B47639102D0DA4837989FEF68822 (void);
extern void HostileTargeter_AI_CalculateWeight_mB9A81C5617C0F08E644599783B09FB1F9671AE8C (void);
extern void HostileTargeter_AI_ActiveUpdate_mA6CA9697A2571832ECB392D0180F4A2D5D5296C4 (void);
extern void HostileTargeter_AI_HandleActivate_m7BAC0948CFE901786247E62BBC6B4187721B1B0F (void);
extern void HostileTargeter_AI_HandleDeactivate_m4C9A1588C7038DE38C7A9859C3649DD320CC6BB3 (void);
extern void HostileTargeter_AI__ctor_m999609BC6E08687E19F874945CE6C26FC908B44D (void);
extern void U3CU3Ec__cctor_m50D8967ECFCD5B0D406C3BB093CF7B0C4EFAF6FA (void);
extern void U3CU3Ec__ctor_m451BFB79768BC11A2FD8EB1A06A7F7FE36986FD1 (void);
extern void U3CU3Ec_U3CActiveUpdateU3Eb__16_1_m06040BA886A352FFF1EB7864EFF397BC715EEC1B (void);
extern void U3CU3Ec_U3CActiveUpdateU3Eb__16_2_m27F9B8E5632B4E7EECA4EDAE08C742BE0828D3C4 (void);
extern void U3CU3Ec__DisplayClass16_0__ctor_m1A6B930AD0EBBAC9CB76C719D0CADFB16D4752DE (void);
extern void U3CU3Ec__DisplayClass16_0_U3CActiveUpdateU3Eb__0_m223F619239BC8092548FB89385CC52473CCBF47F (void);
extern void MedkitSeek_AI_get_FuzzyLayer_m088A8FEECDD449A0577959CCA64000BCE2DB3DC5 (void);
extern void MedkitSeek_AI_get___Usage_m91CFEF4163B6638705906F43008B0CF6A6B2F3EE (void);
extern void MedkitSeek_AI__ctor_mB0284401B620D88466DB1ADD8CF9633C1744730B (void);
extern void ObjectiveSeeker_AI_get___Usage_mEB5C6BEEA192B3BA3F98347EF951F340974CFF46 (void);
extern void ObjectiveSeeker_AI_get_FuzzyLayer_m5CEAE91F5F7FF038AD1909CD256D0C9000F5F828 (void);
extern void ObjectiveSeeker_AI__ctor_m0929D710EDC9269177F853F29FAA944E0DC4B851 (void);
extern void Patrol_AI_get___Usage_m95883191F75DA2A46285E543FEA32B904C98C538 (void);
extern void Patrol_AI_get_FuzzyLayer_m83589EC90D837C4BBD5BA23EF4E8560C7857277B (void);
extern void Patrol_AI_Initialize_m73D2277765D84E2006582BE22BAFB089834448DA (void);
extern void Patrol_AI_CalculateWeight_m679ABFA7C31521CB056006F819BB5B46327E39D5 (void);
extern void Patrol_AI_ActiveUpdate_mAE3585BA3D50337204E938CDECD3AD4A303B4241 (void);
extern void Patrol_AI_HandleActivate_mECD176005A95D630DE31D85CA6788BB8A97EF9E9 (void);
extern void Patrol_AI_SetNextTarget_m847E6BCF41E36F44C68830F5D06B6BD06EE923B3 (void);
extern void Patrol_AI_NavigateToClosest_mBE366BCA858533E38721AA2130B323143158BEE2 (void);
extern void Patrol_AI_HandleDeactivate_m0F2BF9C39D6F9005031E99BAC816145D3F019D0A (void);
extern void Patrol_AI__ctor_mEC078047F368D036F9FDEEDFFF50D9BE6115FCCF (void);
extern void Reload_AI_get___Usage_m1CBAEF9B7DF926C6D9D114567927AE20FE98E5D9 (void);
extern void Reload_AI_get_FuzzyLayer_mFDB565E68ADE0C550934474D30C25129538C3B67 (void);
extern void Reload_AI_Reset_m1A1CD79C099D0546BCA99B960AFB0395D34FD23B (void);
extern void Reload_AI_Initialize_m652816043F5E1DD6B42E5129536B1059A1D9240F (void);
extern void Reload_AI_CalculateWeight_mEF3B667706D88CA900CE541BF4EBDACA3EB0261C (void);
extern void Reload_AI_ActiveUpdate_mFB84663EA3387417B3D40BB1355F1DD0B0F7B336 (void);
extern void Reload_AI_ActivateReload_m34EE4CDADA98436C3AB3235C32DF984C6552AE87 (void);
extern void Reload_AI_HandleActivate_m36C72C0BC41B6E7BEDA3454A39D7D4A8DB4B6230 (void);
extern void Reload_AI_HandleDeactivate_mD7AE26F01D97FAC9FE1346F41C2BAF22A2303403 (void);
extern void Reload_AI__ctor_m31563D4987824E31C8C96E76E018C17E6AD6B0D8 (void);
extern void U3CActivateReloadU3Ed__11__ctor_m968CDE04619B681C250C76ADB4927B5410F59830 (void);
extern void U3CActivateReloadU3Ed__11_System_IDisposable_Dispose_m4DB5FAF848448D5A9AAFFB877E83D827C4113A3F (void);
extern void U3CActivateReloadU3Ed__11_MoveNext_m54DBD419ABB569535D8D9E3060309690BDEB9445 (void);
extern void U3CActivateReloadU3Ed__11_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mD7A9383E6E6E08550627DEDB3FEBE3FEEE76C8A0 (void);
extern void U3CActivateReloadU3Ed__11_System_Collections_IEnumerator_Reset_m69F5F383AEF8E87DD1F5969275181D0DF6A3E118 (void);
extern void U3CActivateReloadU3Ed__11_System_Collections_IEnumerator_get_Current_m3B1F34F3B488B25CC42B5CD8F15A84BCC6FCDD8F (void);
extern void ShootTargetSeek_AI_get_FuzzyLayer_m0E9B68597627FB95698B8106A83C2E7A4F4A86B8 (void);
extern void ShootTargetSeek_AI_get___Usage_m47195A77EA304064C4B428935A26DC19F4970478 (void);
extern void ShootTargetSeek_AI_Initialize_mC1A0F48C25EEC2B399384B7446B5BB052C1F65A1 (void);
extern void ShootTargetSeek_AI_CalculateWeight_m67FB6B59F4AECCEBCD247D437CF954702E8F5CEA (void);
extern void ShootTargetSeek_AI_ActiveUpdate_m70CEB202D9AC066A64066D3D82C7F67AD51395D3 (void);
extern void ShootTargetSeek_AI_HandleActivate_m59230B20673B85D5DD0D81A8D2016243892EF361 (void);
extern void ShootTargetSeek_AI_HandleDeactivate_mAF35FFAA502AA5548890184AC7608242C0AE717A (void);
extern void ShootTargetSeek_AI__ctor_m127CF7FEA6E9818BECCDEC50178C70E9FBC71449 (void);
extern void TargetableBase_get_Position_m8C9D0EAE9A63A894DA0D8A59787C09EF4ABD10D3 (void);
extern void TargetableBase_set_Position_m5E4AA788A21F3D9BD5AAE11D456B7710C3ACE319 (void);
extern void TargetableBase_Awake_m2D710E997761C68E57FA64FA01E93F8E8F47E37E (void);
extern void TargetableBase_Update_m239433FF40FE540213D6B67F1CF9551B078EE8C5 (void);
extern void TargetableBase_OnEnable_m6B5EF58B6AED9928FDFA80FC215AFED862B53ABD (void);
extern void TargetableBase_OnDisable_m968D82145BFC9773837E54C5F1D4562440D8AABA (void);
extern void TargetableBase__ctor_m947F135B4CAE0570154F96C8E16648BA80F2B7F2 (void);
extern void TargetableBase_Polyperfect_War_ITargetable_get_gameObject_mE90B6CFDC8B3B21185FABFCBF9D393AD76FDE4FF (void);
extern void TargetShooter_AI_get___Usage_mBF0CB6C3E41FB31452F7923EC610A6B78A623079 (void);
extern void TargetShooter_AI_get_FuzzyLayer_mA69CB9849699557DD36AB53D3E120BB51BDFC9C6 (void);
extern void TargetShooter_AI_Initialize_m3904ABF442DD8546139F302A2689287C73FBC8F4 (void);
extern void TargetShooter_AI_CalculateWeight_m8277F1F2DA855DCFEAF3EFA09ACD64880727D3C7 (void);
extern void TargetShooter_AI_ActiveUpdate_mA004A3AB6E2A102F5E6413318350D3D92F59F06F (void);
extern void TargetShooter_AI_HandleActivate_m7AF3ECA4339C64437E91EF18F36F63399177A731 (void);
extern void TargetShooter_AI_HandleDeactivate_mB9268586E90D93B9303ACB1D0AA96D699E87E6D1 (void);
extern void TargetShooter_AI_GetAimPosition_m9B2978E71EBF737C3D276C4E00616A17024BAD2A (void);
extern void TargetShooter_AI__ctor_m79273FA2FD5F3A0883D1C00D7896793A45DA79FD (void);
extern void UsableSeek_AI_get_FuzzyLayer_m2E6694F6083496C18A130BCBC05ABD9B65C6FB56 (void);
extern void UsableSeek_AI_get___Usage_m95519DF201306063581A11D47022AA152A847675 (void);
extern void UsableSeek_AI_Reset_mB33B78D37C74723BFF6AEB63F466F397C879C2A8 (void);
extern void UsableSeek_AI__ctor_mAE7EFEBCC85FA1150E7859ACAF3F296E744A177A (void);
extern void Wander_AI_get_FuzzyLayer_mC2B74B65938470D7074F11252ACF5C9BAB82E90A (void);
extern void Wander_AI_get___Usage_mE7646FAF0AC8A6FE0E97E7D4BC02C76320B3D072 (void);
extern void Wander_AI_CalculateWeight_mC90203F7F374E7B0B52E52D71DA929C7C3BB5B42 (void);
extern void Wander_AI_Initialize_mFE3BBDA3A8109761AE3AB9CFA5A2F8886828A3D2 (void);
extern void Wander_AI_ActiveUpdate_m0A7809421A46FB7755FF6E8442CBB28403B178C7 (void);
extern void Wander_AI_HandleActivate_mE00F9952189A7B6278C7E7264073C9C5063D3DB0 (void);
extern void Wander_AI_HandleDeactivate_m5F56D5AB80FBEC7E0DA21C7171CCE1F3FA51D8E2 (void);
extern void Wander_AI_Reset_m67EC1C457F94FDE665893B70C72FB238D1B3AD0F (void);
extern void Wander_AI_SetWander_m3EE6BA5E50142B21C8DE49C337C325732E460CAA (void);
extern void Wander_AI_Wait_mF1D7C16BB378237B57009F6B9D8A8A6F37136E49 (void);
extern void Wander_AI_WaitCoroutine_m1DFD661689880B6C12E52BF0B2DE4B58B44C8FD1 (void);
extern void Wander_AI__ctor_m078B0803F41E717EECDB6CDDEE03B8745C24E760 (void);
extern void U3CU3Ec__DisplayClass17_0__ctor_m144D0E4A866514794444F08DC18D6C8A564F6F23 (void);
extern void U3CU3Ec__DisplayClass17_0_U3CSetWanderU3Eb__0_mC20B18676B6B39CE89A146AF8F7250C749A792B7 (void);
extern void U3CWaitCoroutineU3Ed__19__ctor_mC34E34A850369983385B2865547883E1FBEF26AC (void);
extern void U3CWaitCoroutineU3Ed__19_System_IDisposable_Dispose_m418A9F7970BA8121F2A7E4A0AAEB053DB44391C7 (void);
extern void U3CWaitCoroutineU3Ed__19_MoveNext_m6CFE65EB97CAFFE6156392834ED6B7A3F31361F3 (void);
extern void U3CWaitCoroutineU3Ed__19_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mF1E56436CF3EFCB8B479D0097F28356DDA9A120F (void);
extern void U3CWaitCoroutineU3Ed__19_System_Collections_IEnumerator_Reset_m17C0E0031833A8C023D4DAFB5655BD33CA3E1402 (void);
extern void U3CWaitCoroutineU3Ed__19_System_Collections_IEnumerator_get_Current_m276C056670851B70B190CA0FF26432F30AD1DA13 (void);
extern void Ammo_Carrier_get___Usage_mEA7C1C7B8FA9D59658E5625AFDE3891355EF4797 (void);
extern void Ammo_Carrier_get_NonzeroContainedTypes_m1D1D271DDD739ED765DCD1B776AA6EFB8E639746 (void);
extern void Ammo_Carrier_Awake_mB083B92745351AFFA5D340F34CBA322F81C94DA7 (void);
extern void Ammo_Carrier_SupportsAmmo_mF09822D6DA34DB9703A4836ACE7994A9DA34A7E1 (void);
extern void Ammo_Carrier_GetRoundCount_m205F0C33FEAC9A01C8C5C320B5482A48FF90E665 (void);
extern void Ammo_Carrier_TryAddRounds_mB0B53D7280DBB9F38C783E5CEA1D94FF44DF8C85 (void);
extern void Ammo_Carrier_TryAddAmmoSupport_m687B0C62FC9D4F6DB7175C8AA1FEB657C6EA1DA9 (void);
extern void Ammo_Carrier_TakeRounds_mBE46EEE27F33688963B86B7030FDAE5BAF13C6A6 (void);
extern void Ammo_Carrier__ctor_mF062B6B89BE5B514A3AC23939F3B905B996C7817 (void);
extern void U3CU3Ec__cctor_mAA3CC51343AF763B7541CEC52F22734972816C7F (void);
extern void U3CU3Ec__ctor_m7C8F9DBFF02E917699C26650A44CDC44F9E40B4A (void);
extern void U3CU3Ec_U3Cget_NonzeroContainedTypesU3Eb__6_0_mD1208421D8803469F2E9C5D76057A5C9EB591C1E (void);
extern void U3CU3Ec_U3Cget_NonzeroContainedTypesU3Eb__6_1_m15247DFDB52623C4C3DA34782FB9C19068D94B7F (void);
extern void Rider_Carrier_get___Usage_mCCBBC01656D47ADA4AF3AC19EC9D06878F51F159 (void);
extern void Rider_Carrier_get_Riders_m43600B4EF62F01651A51CE8F0C890F92883BB77E (void);
extern void Rider_Carrier_Awake_m92BEF2A62B1C450FD185C1F3D4728DF0BBFDB72B (void);
extern void Rider_Carrier_AddRider_m73C81787E2AE82854F0DAAA6B89B9FDCBDA9C6BB (void);
extern void Rider_Carrier_RemoveRider_mB95E18163A282FE4FC7BC5B7D020975C94C2733C (void);
extern void Rider_Carrier_OnDisable_m3C6F1A81320969516C01481E8D08C9530DC01089 (void);
extern void Rider_Carrier_RegisterRiderEnterCallback_m86A0F29C2E23529C47AFA3B3775568205602F132 (void);
extern void Rider_Carrier_RegisterRiderExitCallback_mE6F26E0ED2F037C5154DDA211C17A6AA7054541A (void);
extern void Rider_Carrier_UnregisterRiderEnterCallback_m23DB8B1620632D6E0D097E513B6383C3649B1572 (void);
extern void Rider_Carrier_UnregisterRiderExitCallback_m088DF01DBB92D8F32BB4282B31251990726720D0 (void);
extern void Rider_Carrier_HasRider_m9DEABB50CEFC4AE501A8592FCE436B614FA50DCB (void);
extern void Rider_Carrier_SupportsRider_m6AB6CE164CB4E6A162DE6FC338AF1087439EA4DF (void);
extern void Rider_Carrier_EjectAll_m4284676285F8CE7F3671735AF8AD223F5789AC01 (void);
extern void Rider_Carrier_GetSlot_m7523D325B214891831C62F3A98BF800FC9CF71FB (void);
extern void Rider_Carrier__ctor_m061DEF1D2C9DCE6FB3C18964E48FF0D9DE98A872 (void);
extern void Rider_Carrier_Polyperfect_War_IRideable_get_gameObject_mF46BDD33B53AE0D6EC53356442363576D3F2E573 (void);
extern void U3CU3Ec__DisplayClass11_0__ctor_mEB5A3C72304A591593ADDB813ECFABF6F1678898 (void);
extern void U3CU3Ec__DisplayClass11_0_U3CAwakeU3Eb__0_mE49B2384FF8E64D5F4DDEDE7928BBCC1F50E8228 (void);
extern void Usable_Carrier_get_UsableInstances_m4AAE9C46D80B21D3095B485F7E75BBD678DFA9D2 (void);
extern void Usable_Carrier_Awake_m13F0FD97EDD6E84CF057F300F09ABC042078BBD1 (void);
extern void Usable_Carrier_Start_mF6C39C85A225BED23E717D07F3FEEC6CB32A5D4D (void);
extern void Usable_Carrier_IncrementUsableIndex_mD28F8656FE84A893C22C6117CD76C78A057D3CCF (void);
extern void Usable_Carrier_SetUpUsables_m8DCDB2BBF56719F01FCD3757CAA1133BEEE01E4A (void);
extern void Usable_Carrier_HasUsable_mAB75E03BB34650197B8BC2398BD6332266811C7D (void);
extern void Usable_Carrier_AddUsable_m637182B3B5644FC1285864938CD26E650CFAD04F (void);
extern void Usable_Carrier_AddUsableWithoutNotify_mE0864CE667A6B3F663A24E4CFC499E04302FDA33 (void);
extern void Usable_Carrier_SetUsableIndex_mF98C049A59A32345679114335DB98F92C3AFFD2F (void);
extern void Usable_Carrier_RegisterChangeCallback_m9718784ED880DF0BE442953A666447E0BCCF2909 (void);
extern void Usable_Carrier_UnregisterChangeCallback_m57E30BF0D7870C82CD539B8E2268AE557C3FEB74 (void);
extern void Usable_Carrier_GetInitializingEvent_m325A6DADD211FEABC496737DA0CC6C2916E28124 (void);
extern void Usable_Carrier_get___Usage_mEC08CD93F641A06633D1393CF83926CC3B0952D7 (void);
extern void Usable_Carrier__ctor_mC8E1EFB80A8E9378F5BA73DEC48AC4B01BED9373 (void);
extern void Usable_Carrier_U3CStartU3Eb__9_0_m264D1E025C6DB360A3CD994DF274E6C50312F168 (void);
extern void Usable_Carrier_U3CStartU3Eb__9_1_mF4C7F76DF06736539F7BB7F99D9F8008704B5E97 (void);
extern void Usable_Carrier_U3CStartU3Eb__9_2_m79FA017BC3E8B11312B9E9BA9D944EE5B639EF96 (void);
extern void ActiveAmmoInfo__ctor_m48E79771AFD56B76BCD19F4F7D646D3544257205 (void);
extern void AmmoInfo__ctor_m27F80CC108BB8674130B5D351F3465868AA0C866 (void);
extern void AmmoInfo_ToString_mDBC79047138F45541D775EB88C6353639557AFA0 (void);
extern void AmmoType__ctor_m56303F018B5082FB29E0C4C9E869B3648CED8DA6 (void);
extern void CoroutineReference_get_Coroutine_m7524C8642757CF74247096C4C3579BD312736C38 (void);
extern void CoroutineReference_set_Coroutine_mFC4247025543455F4A928B651B8408BC3FB84254 (void);
extern void CoroutineReference__ctor_m757B081CD9E84632D0A2799C2245AECC937179F6 (void);
extern void DamageContext_get_DamageAmount_mDC75FCA4EAB0A5D6310D1C79F9D7020BD1DF9178 (void);
extern void DamageContext_set_DamageAmount_mF57E0C5506BF63EC24FC0E1040A8366186709C39 (void);
extern void DamageContext_get_ImpactVector_m3D9419597E7BCEE7A687BDF216963DD78C6DC09A (void);
extern void DamageContext_set_ImpactVector_m1F515B5CBE319400CC9EA03C103DE6BC352CF184 (void);
extern void DamageContext_get_ImpactPosition_m31758F4C02F236C7F43E46D09878595E62E21F42 (void);
extern void DamageContext_set_ImpactPosition_m355D3107835CA3563DD8B3B8DE90C11FB7901D49 (void);
extern void DamageContext_get_UseContext_mEB2C77D316A4A8E6E283B2DFFCADB456DCA73AB2 (void);
extern void DamageContext_set_UseContext_m9BBC8D7263A614690B85EE792A8D5CC556E5B3CF (void);
extern void DamageContext__ctor_mB9CAD3BEC2A23085A7E301DBCDBF74E94D5DCBAE (void);
extern void Faction__ctor_mF46A8F9189CEF0D65A6CFC1737BCD96E81F09194 (void);
extern void IKWeight__ctor_m0F3AB7A526C47D0F2CD32E24040C70811B6E7D55 (void);
extern void Inputs__cctor_m233CA418B8F6A59E86A88B49F84D6B556AE43E71 (void);
extern void ButtonInputReference_get_Name_mDF7980B040765DDF20AB88D727184E16BF5CF3A8 (void);
extern void ButtonInputReference__ctor_m0DABF1C2A7252A245FD8C4D8A30B13A7D60281A0 (void);
extern void MultiAxisInputReference_get_Name_m269849DF6DAE1D0FEC69CF895AC59E2164923054 (void);
extern void MultiAxisInputReference__ctor_mCF0200526F85D9A4DA0103A53453654619D757D2 (void);
extern void ActivationDelegate__ctor_mEFC76367E549C5FD34F31F5D57ED477D994F6E05 (void);
extern void ActivationDelegate_Invoke_m71C92DA80CCB56B84D698CA7231DF10566FE6013 (void);
extern void ActivationDelegate_BeginInvoke_m110BBCB7844E74DFEF791A184A80A781445247D8 (void);
extern void ActivationDelegate_EndInvoke_m43B098775CA88F8A7CA3AAADDE6FD04F1872E1ED (void);
extern void BoolState_get_Value_m8A00CCBB3A40F72A9121A0861B1B4F84734A297E (void);
extern void BoolState_set_Value_m166D1D7B0E86B8EE62673149AAF69AE3BC1454D8 (void);
extern void BoolState_get_RecentlyActivated_m9A44F718684C2ADCFD1A43B74956C5B143793BBF (void);
extern void BoolState_Set_m0AAC6D9EA371A9BC6E55FD225EA0A3D6D5BC3E39 (void);
extern void BoolState_add_OnActivate_mF8F76FD24D715A1F1C37706D350A1C2ED353EC64 (void);
extern void BoolState_remove_OnActivate_m9E0C013F5460624A06E682595E3AF0236849C0E4 (void);
extern void BoolState_add_OnDeactivate_mA820D0C6DF8C88EAEDACC3F9BD12C4910D7F370E (void);
extern void BoolState_remove_OnDeactivate_m851A5E18D45523892627D35BAD2B88C4A2818C9D (void);
extern void BoolState__ctor_mD1AC0BB89793D7B77C4FB685E80843FB65FA7F44 (void);
extern void U3CU3Ec__cctor_m72EFF46CE8936EEAF1E85F30416C6373AE0BE7B4 (void);
extern void U3CU3Ec__ctor_m0BE6611D45E1A0FF284275CF02A127ECCD89290C (void);
extern void U3CU3Ec_U3C_ctorU3Eb__15_0_mEA9189A53E9785F354A1F4D8CE6494D6F9EB3297 (void);
extern void U3CU3Ec_U3C_ctorU3Eb__15_1_m5FF1A43BFF6D0AE166DF8E09A039AC22EEF2F40B (void);
extern void AxisInput_get_DirectVector_m5E9E448EBFBECD2F9674BFA245AF19EA8670BBCB (void);
extern void AxisInput_set_DirectVector_m04096BBEFDF32F376C5D260D3371BCECBDA86223 (void);
extern void AxisInput_get_WorldVector_m01BEACD66A2DC329E071DD7C2DB27CB14D0A9896 (void);
extern void AxisInput_set_WorldVector_m74DD752194715D2E03F4273656E9F4C499351E36 (void);
extern void AxisInput_Set_mDC68347E25EED8D4C55379A98ED4848E9FE1A2F1 (void);
extern void AxisInput__ctor_mAB1C208D28186B923F379B227026EEE80D4E568D (void);
extern void RideEnterContext__ctor_m789499FA9A2C6B8AAF5C9325472AC6D097A8F2D6 (void);
extern void RideExitContext__ctor_m6B15EBF64FBC4B14C4BA8E7A6FE885622F8785DC (void);
extern void TrackerTargetPair__ctor_m2CB4D3D378338810F9E706F55F1C2166B8FD93FB (void);
extern void TrackerTargetPair_GetHashCode_mA24496B03EB3793907D91ED250BC930A7306BB5D (void);
extern void UsableCapability__ctor_m2947B15696280A3864DFF4350A24F776B0AE23A6 (void);
extern void UseContext__ctor_m65ABBA29365AEE4CC41DEC3B7C61C27CA8D654FE (void);
extern void UseFailedContext__ctor_m39058652313C86F641E0491C7BA37193707908B4 (void);
extern void Reason__ctor_m03BA4B72699A2F2990E509E08923BBEED124D158 (void);
extern void Reason__cctor_m5C623C02D105B9F7C187B52117BE0B9CA072E737 (void);
extern void DamageUtility_AoEDamage_m3149E48E95A8D8C46EA5A260CABF507FD97C6ADC (void);
extern void DamageUtility__cctor_m103C0D35142F47F3735424B249404273160B933E (void);
extern void SpawnUtility_SpawnWithContext_mDC1C1E3D9B5CF3244D6FA1E2120878A454D9FDE6 (void);
extern void SpawnUtility_Spawn_mD6E0F0540190ED2D4FC60C9B26D87B31365FEC33 (void);
extern void TransformExtensions_AllChildren_mB96B439E8A47909A9B145AD3C37516D68C232B29 (void);
extern void U3CAllChildrenU3Ed__0__ctor_m48650B64440E3141B18D9B8FE794E933A055561D (void);
extern void U3CAllChildrenU3Ed__0_System_IDisposable_Dispose_m25AD14BBD52B2E9DE84E19D781517628A4E2F60A (void);
extern void U3CAllChildrenU3Ed__0_MoveNext_m85ACFC2685E9BB17431BFD778BEECEE75F5F56EE (void);
extern void U3CAllChildrenU3Ed__0_U3CU3Em__Finally1_m306CC2C92BBA0991F0E0D39B34DBE1A45FA8949B (void);
extern void U3CAllChildrenU3Ed__0_U3CU3Em__Finally2_mC89178FC1F453ED503E01EF5789825C24433B4B6 (void);
extern void U3CAllChildrenU3Ed__0_System_Collections_Generic_IEnumeratorU3CUnityEngine_TransformU3E_get_Current_m35EAFEF3411881406DBACFC15F918462D211B9DB (void);
extern void U3CAllChildrenU3Ed__0_System_Collections_IEnumerator_Reset_m16B81B186B95A7730B1BFA06F6849EBFADAB10C8 (void);
extern void U3CAllChildrenU3Ed__0_System_Collections_IEnumerator_get_Current_m5724E3C86FDE4899D664EB09B140EFBC207D8407 (void);
extern void U3CAllChildrenU3Ed__0_System_Collections_Generic_IEnumerableU3CUnityEngine_TransformU3E_GetEnumerator_m1FAC5E19282B873719ADC32B3DF68BC6190B0B92 (void);
extern void U3CAllChildrenU3Ed__0_System_Collections_IEnumerable_GetEnumerator_m19CE1FA659E156B0B3D7EDC6465B54CD02DBAFD8 (void);
extern void AutoRespawn_get___Usage_m5342F22A6ABA8924ECE3331527A05CE46AE3ED57 (void);
extern void AutoRespawn_Start_m292C03D0D5C2A524DDE817BCD764E306886EC706 (void);
extern void AutoRespawn_HandleAutoRespawn_m2A7CE2AAF3F8FA577AD771945E215E7D0988D49A (void);
extern void AutoRespawn_DelayRevive_m9E2740F32BD991A19CDB3506EBFB17216A730316 (void);
extern void AutoRespawn__ctor_m934A129AE8901BF940FCEE92E3C06318FEA798C0 (void);
extern void U3CDelayReviveU3Ed__6__ctor_m7BE334D629CFA65C7015A5DB5E1DABB6DD170DED (void);
extern void U3CDelayReviveU3Ed__6_System_IDisposable_Dispose_m63070B6E7D63FA8E55F015D730DEACB60B529FDC (void);
extern void U3CDelayReviveU3Ed__6_MoveNext_mE376BE9838763FC09A68E2A8F2106179E5B17C8A (void);
extern void U3CDelayReviveU3Ed__6_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m49EE96CF7EB0134057FB5A421B6527C943019384 (void);
extern void U3CDelayReviveU3Ed__6_System_Collections_IEnumerator_Reset_mD8B3B0C77CE3BE0CE757484545A20CE0822BE755 (void);
extern void U3CDelayReviveU3Ed__6_System_Collections_IEnumerator_get_Current_m8A7663304EE377C964D21CCFAD20080FBE7BA435 (void);
extern void AxisTurning_get___Usage_m1D18D0F25F60FD1522C87FBE392487E07B4ECBB9 (void);
extern void AxisTurning_Update_mEBE28210DA0F79FA3B9E2B5FB4FC540255990643 (void);
extern void AxisTurning__ctor_m70CD98427C5067D46141489389F20A4E0627B95C (void);
extern void CameraRotationMatch_Start_m3B7AD1E6813B0C9A296755CAA4CB569ADBE3F088 (void);
extern void CameraRotationMatch_LateUpdate_m62BBEBA4A4BCB42C5CC15560883904215053C187 (void);
extern void CameraRotationMatch__ctor_m9C16624BAF179F6E3F80218D9E63F282A0D704BD (void);
extern void CollisionEvents_get___Usage_mC607430BF2C594099161814E4F00079705BD0EBB (void);
extern void CollisionEvents_OnCollisionEnter_m1F2D59680AB4F15CA4D43F533A7F5C8F68AEEC6D (void);
extern void CollisionEvents_OnCollisionStay_m2077C53AF36D297093BD793A4C5285001F78DE0C (void);
extern void CollisionEvents_OnCollisionExit_m92DFC717364E671BE01000F45D87C12AAAEFF40D (void);
extern void CollisionEvents__ctor_m354604DF415526507A635DCB909C8AA93370C614 (void);
extern void ConstrainedAxisTurning_get___Usage_mC5D33389A0635C91F4A0B59A19F92A10166529E0 (void);
extern void ConstrainedAxisTurning_Start_mFAD76D3DCA6E75AC685829480100BDC39114E8C3 (void);
extern void ConstrainedAxisTurning_Update_mCC7EFF5526879E31F7997EC185BBD76DA8FD8ADD (void);
extern void ConstrainedAxisTurning__ctor_m2E8E89002984CF2BA896E8A1155AC5BC55285E7A (void);
extern void CursorLocker_Start_m2205404D8724FC7D087A543D51DFCB7292E7C3C3 (void);
extern void CursorLocker_Update_m5A5150D7259D2C09419709D980B4CDEBF2AC1334 (void);
extern void CursorLocker_LockCursor_m144754B8FAFF4541078892600663BA81D0933C1E (void);
extern void CursorLocker_UnlockCursor_mD50A31E76BA48A2D08F98762F3EECD8F7A11A708 (void);
extern void CursorLocker_CursorPosition_mE71478FBAF09227129C3A1289739125924B24D12 (void);
extern void CursorLocker__ctor_mD1E518BAFED093201ABF23F963506993AD46CB54 (void);
extern void DamageOverTime_Awake_mC1FF7A39BC4018D8227644CBB9796739BB82A8C5 (void);
extern void DamageOverTime_Update_m011D0583A6C60B5A29872DCD1C353DFDE6731B8D (void);
extern void DamageOverTime__ctor_m468965247DD87D088D25B0E7085064A9AB85171A (void);
extern void InitialVelocity_get___Usage_mC5FC9B269A4FF976944226E3FA1E7CBCDFF9B123 (void);
extern void InitialVelocity_Start_m2DCFC2EB6C2F11E54D1BF4ED8C7A1C0DE56AE3CE (void);
extern void InitialVelocity__ctor_mCA32EED7FDDFE9AAAB43154A9B211624769D7302 (void);
extern void MatchRotation_get___Usage_m781AB504A15A8744C975075968B39761E1E0A1B3 (void);
extern void MatchRotation_Update_m52284AD82B381589A55705BECF826654D4AD24B7 (void);
extern void MatchRotation__ctor_m9BFD0FB94158C1BA59FEBDA838E50A8FA485F41F (void);
extern void RandomSpawner_get___Usage_m9D22D184E392783A63149BED076BBA7A52044F09 (void);
extern void RandomSpawner_OnValidate_mE57FAF0E06A4C2AD0DB258565168EFCBB68B3604 (void);
extern void RandomSpawner_Start_mBDAA49E8A7A55216818ADAB480CB1ABC7F49C49D (void);
extern void RandomSpawner_spawnCoroutine_m4093697CFBD5FD21E968BF4D92101838AB3AE9C7 (void);
extern void RandomSpawner__ctor_m12A6B36B5951B4457769E6B2B23F625C8C15BE5F (void);
extern void U3CspawnCoroutineU3Ed__9__ctor_m4D49960E23EF3A9B861520398514B6EA8FB1D9C0 (void);
extern void U3CspawnCoroutineU3Ed__9_System_IDisposable_Dispose_m4D897C827D61A510D09A4C38A8276B390AD5B0CA (void);
extern void U3CspawnCoroutineU3Ed__9_MoveNext_m4B9F2C1A3FA0D81DFE000E4E9008CC5C336C34D5 (void);
extern void U3CspawnCoroutineU3Ed__9_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m7600C8E591C0ABBC447B0223B8AB0623135EF164 (void);
extern void U3CspawnCoroutineU3Ed__9_System_Collections_IEnumerator_Reset_mE3ED018E42BDB7E882C89C25E5ED99B3DFE4714D (void);
extern void U3CspawnCoroutineU3Ed__9_System_Collections_IEnumerator_get_Current_mA6D46DE9D3D274E3070559ACAA81BAC2F2C76915 (void);
extern void RotateToUp_get___Usage_m807C45AE72289B9CBA83E3D5605F887C87AD3E64 (void);
extern void RotateToUp_Update_mF1B0DF4D8AAA50C7D5ED5DB0CCACA658F2BDDFC7 (void);
extern void RotateToUp__ctor_m4E11169838B14559531C4793B6BC36A87A65B9E0 (void);
extern void RowSpawner_Start_m7ED042E65A031E4ECF5955180C519F52F21B8C31 (void);
extern void RowSpawner_DoSpawn_m6903414400E42BA824C4B99FA7CF6CB4C38ADFC5 (void);
extern void RowSpawner__ctor_mA8FE691059631962D8604B296873A9699F209E34 (void);
extern void Spawner_get___Usage_m83634F9CDFDACFA4711F6B041C69BB1FA6C0F73A (void);
extern void Spawner_GetToSpawn_mD2ED62484F3FEA45D57A4F2743852E01E8FE09C0 (void);
extern void Spawner_get_targetTransform_mC4A9DEBB35BF51DAE39498E587543C529744C9D7 (void);
extern void Spawner_Spawn_m5C946D2CD504878E92AEAFD1469BA425491D3F10 (void);
extern void Spawner_Spawn_m3B1DCFDADBE096D2F3925A7D705A0BE2026F7525 (void);
extern void Spawner_TrySpawnWithContext_mD6A74072A098535400266C9AD1EED7E07D039B58 (void);
extern void Spawner_SpawnAfter_mB483CE793426882DF53C96FE44DF82EA8AEC696E (void);
extern void Spawner_Spawn_mDAE799547E3E95937BCE095B4823372DC6DB6BDF (void);
extern void Spawner_Spawn_m55E50E433C39934C283E6B4575D56683B66BAD89 (void);
extern void Spawner__ctor_m10C6B478D236A252525E6AF073EB86AA37D4C1CD (void);
extern void TriggerEvents_get___Usage_m582ED5A90D620288B30BA14AE59F8AE5900ED2DC (void);
extern void TriggerEvents_OnTriggerEnter_m7821041BFB8C0BD4FB1B66E17F7AC20ACBB1A19E (void);
extern void TriggerEvents_OnTriggerStay_mCB49AD4F599AEA3603777F904B853BDCF2232A06 (void);
extern void TriggerEvents_OnTriggerExit_m647388D1E8E2483C1017D02B6116F67646AF1C37 (void);
extern void TriggerEvents_IsInLayer_mD7ADDDA3AF32A03722AE87FDCC24923FCE444519 (void);
extern void TriggerEvents__ctor_m2CBE69FF15D5561961064497E822CAD8C2481743 (void);
extern void Tween_Arbitrary_mA190C3023F6409C57AE5DC3D72E66002F195F40F (void);
extern void Tween_ArbitraryTween_mFB07EDD72F49066F6FEC6F594622D97172CE5539 (void);
extern void U3CArbitraryTweenU3Ed__1__ctor_m19EC8F11B257B62150653920A5B9235C54FCC2AB (void);
extern void U3CArbitraryTweenU3Ed__1_System_IDisposable_Dispose_m242DD17EE505EA9306CBEF43B1A83C3B2F217DE3 (void);
extern void U3CArbitraryTweenU3Ed__1_MoveNext_mA4344AA3C634D349CDE5BF7E07E1F9D020E17237 (void);
extern void U3CArbitraryTweenU3Ed__1_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mDECCBA851BC82B9AD0DEF6D964C0942B8D674E66 (void);
extern void U3CArbitraryTweenU3Ed__1_System_Collections_IEnumerator_Reset_mC51BD67E162160B11693D569E9509FC5C53D508D (void);
extern void U3CArbitraryTweenU3Ed__1_System_Collections_IEnumerator_get_Current_m7D55A097AC375A658676B21935FD236B0575F301 (void);
extern void Unparenter_get___Usage_m7A8DF9C295B5B13878A18BEF48EDF5B0B39236DF (void);
extern void Unparenter_Start_mDC05AD6871254A119CE7629F6C143D18660A187A (void);
extern void Unparenter__ctor_m4BE8B394DECB2C7CCF7C1BBFC4A80FEE876A4E87 (void);
extern void Unparenter_U3CStartU3Eb__2_0_m305EC0882B84E6642256505DAF9A0DC8A2E067ED (void);
extern void Unparenter_U3CStartU3Eb__2_1_m752572DEBFD22474A21DDEAB4D2E663C20CB4337 (void);
extern void Unparenter_U3CStartU3Eb__2_2_m858E0FF255C0058BC8026F0F7DACC0A0F28659C6 (void);
extern void AerodynamicCurves__ctor_m2A47039D1115E28CD6A815D07E52190A523CF312 (void);
extern void AimCamera_Start_mF94D38D46EF1EED49DB28241E187E70C0735C706 (void);
extern void AimCamera_Update_mE848E6D907F2123C32D2007501FAA66C74B22301 (void);
extern void AimCamera__ctor_mC17FF276ABCD805C0331319876E07B8211480CA1 (void);
extern void AntiStuckSystem_Awake_m43977CA0976F2CEDD58AA501EA3BE38D9C82B817 (void);
extern void AntiStuckSystem_FixedUpdate_mF9E38B4E09E866BCD9E0AFF1316B7B428442532F (void);
extern void AntiStuckSystem__ctor_mB069D5700D51AA6836FFEADFBDCAF98623AEF968 (void);
extern void CameraBrain_OnEnable_mBCA5F7071164B3B7B6134E9EE9AD248AD7426B4B (void);
extern void CameraBrain_AddCamera_m371D6974F5FB36C0B2D670E706868D842A4A06C2 (void);
extern void CameraBrain_Awake_m7F4F38EBF4564008C0628FADD5A4754A272CE3F1 (void);
extern void CameraBrain_OnPriorityChange_m174EA4309338DDACD676594D2760E910E7797190 (void);
extern void CameraBrain_MoveToPosition_mDAD45B9363A63B1C2926CA061184E70E28B6DEB8 (void);
extern void CameraBrain_LateUpdate_m7448C274EED2C1B2F15ECE7A255B009A730741DF (void);
extern void CameraBrain_SetParent_m08675631720F948B05CC017D2B6012906813DDCB (void);
extern void CameraBrain_HandleTargetInactive_m3F5B93E94B80F0E114B28526CB9508C9D12C3292 (void);
extern void CameraBrain__ctor_mA6E4DCA826898DDDF08B3035C6A9FF727834102D (void);
extern void U3CU3Ec__cctor_m470DD576FFF911E91F5F85E29A88124A162C99DC (void);
extern void U3CU3Ec__ctor_mACD5CECC25EF4D288429294CB14636611C638CF2 (void);
extern void U3CU3Ec_U3COnPriorityChangeU3Eb__11_0_m4D00DC362D883059C563467D223E70171A523D9C (void);
extern void U3CMoveToPositionU3Ed__12__ctor_m840A1AA33750AFAE613EFDB3A8A42D2BE16ED974 (void);
extern void U3CMoveToPositionU3Ed__12_System_IDisposable_Dispose_m9C793179F0CE37A56BAA0A0F13116993CAEA6CC8 (void);
extern void U3CMoveToPositionU3Ed__12_MoveNext_m5969ACA97E3011C05A99AB7E22B4AC0E6844B73D (void);
extern void U3CMoveToPositionU3Ed__12_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m60E78B099169DE9F1A457F5CE815F7C822DC4432 (void);
extern void U3CMoveToPositionU3Ed__12_System_Collections_IEnumerator_Reset_mF4B9D350FC7140CF5CA7BFE751E58A3BFCAF9ABA (void);
extern void U3CMoveToPositionU3Ed__12_System_Collections_IEnumerator_get_Current_mAB177E3268DCB9ADF49996A5B6B8AF6C838632F9 (void);
extern void HideObject_OnPoolSpawn_m7CD81DB2D0E82B4CED6C0E2DFC774F13F15D61F6 (void);
extern void HideObject_DestroyTimer_m60ED0152DCB7336C905088FEEBDE566C97C6D924 (void);
extern void HideObject__ctor_mEE96C2D336E58F8B265B49EB94F680A6E4AD804B (void);
extern void U3CDestroyTimerU3Ed__3__ctor_m9874F0ACC79CAF4AD666FEDFE680FE7465530EDB (void);
extern void U3CDestroyTimerU3Ed__3_System_IDisposable_Dispose_m72BA911A14D8321FE7B363FF5031C8ED23F628CD (void);
extern void U3CDestroyTimerU3Ed__3_MoveNext_m585AC7BBFD48FE85E383E688F8DF989E621C2DBC (void);
extern void U3CDestroyTimerU3Ed__3_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mD45B8C7FC168DBD0383B21B170A94EEB7DB184CD (void);
extern void U3CDestroyTimerU3Ed__3_System_Collections_IEnumerator_Reset_m138477A9BB6DFF21AE19AA54B0F63175341DC4F6 (void);
extern void U3CDestroyTimerU3Ed__3_System_Collections_IEnumerator_get_Current_m0E3A450F2F9AEF7D1A2CF27DA04CF877861628E8 (void);
extern void NavPath_GetNextTarget_mE238C38E45E1930D7C17D1DB0C50DAC7DCFFAF9F (void);
extern void NavPath_GetCurrentTarget_m985CE0830C0F36F06A4E51C994057AC37FFD22D2 (void);
extern void NavPath__ctor_m29F469CDF9DC44356BD61334A958B9F412D0ECAC (void);
extern void PoolSystem_get_Instance_mF974B13B8675920E1660D1F667C1259CB16D8824 (void);
extern void PoolSystem_Start_m0ACD3EECCC6AC4A42D35BE42C2F8ED2FE37BCA43 (void);
extern void PoolSystem_Spawn_m249C6D9E0D623C17572E015E3D9153EC2792195F (void);
extern void PoolSystem_PlaySound_mFF339A0D06BEDC5D45871FBA909CC07C5F9068B3 (void);
extern void PoolSystem_SetUpPool_m524E569188AB875ADBA791A18C8AA68883E488BF (void);
extern void PoolSystem__ctor_mA1EB453BCAC3E4AE178309156479980111EC197A (void);
extern void Pool__ctor_m3485274BA9A0CEA090894900929E7F885D8A482C (void);
extern void Pool__ctor_mE09E48D1094C984580BD956D84BCFED34018CD63 (void);
extern void StaticHelperClass_Remap_mEE77FAD90327D6C255C336D5319F39FCA181041D (void);
extern void StaticHelperClass_DamageArea_m14DC759799E2FBD1DDD8CCCB5CA58C6313BD96B6 (void);
extern void VirtualCamera_SetPriority_m3AF36F1019A2BD14D19E8CBA39D606E4230FCBDD (void);
extern void VirtualCamera_Awake_m7163AE277C57A1F837D21C89BCBAD9EF8DF03995 (void);
extern void VirtualCamera_Start_m58D5C6C06EFCEA4C3E7D657AA721E835AB419737 (void);
extern void VirtualCamera_FixedUpdate_mB83AE5B2BBC6981B2789EBBE25D6588A935D1E19 (void);
extern void VirtualCamera_Update_m2C8D13B7FEBFAC8BEE28864B1FE4DB3820244EB5 (void);
extern void VirtualCamera_UpdateCamera_m3AC38F9EEEA58D235FEEEE8E7C75119F2EDC831A (void);
extern void VirtualCamera_OnDestroy_mF41B77CFE2EECBC706E68A8CCC6273F58221FF42 (void);
extern void VirtualCamera__ctor_m9B8ABB8D07FED038E5C40194FE88B3C6661991C3 (void);
extern void ParachuteSpawner_get___Usage_m114D4E3DA666443B3A97D0A985DCB2BB4B63B402 (void);
extern void ParachuteSpawner_OnValidate_mCF9ECA9ECF83B90FCA5C449136D78EA52EDC79A0 (void);
extern void ParachuteSpawner_Start_m1F7D8514381CFF4FA345FA5721964B3CEFFBD8F2 (void);
extern void ParachuteSpawner_TrySpawnParachute_m8EE2C4927DF45C1943D5334A0835C276711F05AC (void);
extern void ParachuteSpawner_DoParachuteDelayed_m0FA73E3D9F67B70691A8D3548A7ECBCDAAE7313A (void);
extern void ParachuteSpawner_IsGroundInRange_m0E5F64D44A5C12C67E32FF7BEEFC77FB78D0440B (void);
extern void ParachuteSpawner_DoParachuteSpawn_m4A6EFDE7EF3EFB91F4E133CE15F6C8A61CF91518 (void);
extern void ParachuteSpawner__ctor_m44496714E1FFE5CD54E7E93E5370312650F8C31F (void);
extern void U3CDoParachuteDelayedU3Ed__9__ctor_mD0568B4FB5A29BE8967F2CAA8FC848223D5B8ABD (void);
extern void U3CDoParachuteDelayedU3Ed__9_System_IDisposable_Dispose_mFCD637C6A2DD756005AAC52BD017CEE6BDF0F51E (void);
extern void U3CDoParachuteDelayedU3Ed__9_MoveNext_m7B53301516DFC7F9C60BD66687FFF9886EE19164 (void);
extern void U3CDoParachuteDelayedU3Ed__9_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m59B1711451F62BCD99AFA18AD5C5315A4BB0915A (void);
extern void U3CDoParachuteDelayedU3Ed__9_System_Collections_IEnumerator_Reset_m106B7A956F0B18B8A9519782CAD5675B17E12C0B (void);
extern void U3CDoParachuteDelayedU3Ed__9_System_Collections_IEnumerator_get_Current_m629E60F5CCB3435C9C9E12B24810177C3A34B0C0 (void);
extern void SceneTargetsManager_get___Usage_m9191B9C1E9280FF4036DBA5B728CBFADA81B027C (void);
extern void SceneTargetsManager_get_Instance_m9DB3E2404BD011C93FFACEEC34B57EF11FDB14BC (void);
extern void SceneTargetsManager_RegisterTargetable_mD8F9ED8D0433C8B73E6D85BEC99BD904875F3DCE (void);
extern void SceneTargetsManager_UnregisterTargetable_m6920B5077FE1737BD7329C6EF08C6582EA8BD3FF (void);
extern void SceneTargetsManager_EnsureTargetableCollectionExists_m85EC7DC4D6E8270A838E45C0E8703B143AF40AD1 (void);
extern void SceneTargetsManager_EnsureSeekerCollectionExists_m7886304047DA6FD15F0E55EF9B997D00900C7CCA (void);
extern void SceneTargetsManager_TargetsInRange_m5B38EA6B22E400EA44D73716E89BF46EE7E7CC19 (void);
extern void SceneTargetsManager_Init_mAA870A7135DD14340CDF973F60F9A3E489E84FB1 (void);
extern void SceneTargetsManager_Update_mBF62AC05EF175E80EA5671C63213C8B37CB261AD (void);
extern void SceneTargetsManager_CopyRemoves_m64509D8C197E907DC00CF5DF1574A0CFAA54F463 (void);
extern void SceneTargetsManager_DetermineAddAndRemove_mF36B590D264A9B6AA520979A5EDDB91A2A44763F (void);
extern void SceneTargetsManager_DoAdd_m934638D5781910248DE6F663AB70B126A60A7BDE (void);
extern void SceneTargetsManager_DoRemove_mD97929EC8D9D53D482B01DE41B456B6C8CE16B09 (void);
extern void SceneTargetsManager_ConstructNewPairs_m80FD8749388F603DD817FA3A16F75229AA1390BA (void);
extern void SceneTargetsManager__ctor_m986D922344DD2D298AEC09393E235C270A0700C9 (void);
extern void U3CU3Ec__cctor_m0CB314D3B905F012D16A138267880C9DD58B2912 (void);
extern void U3CU3Ec__ctor_mE76C80CAC0C4DEE04753D9437DA213A056E2CDF8 (void);
extern void U3CU3Ec_U3CConstructNewPairsU3Eb__28_1_mA67196287BDEBA7C9B3C0B6F05E4119A92CBE75C (void);
extern void U3CU3Ec__DisplayClass28_0__ctor_mA9EC5649E34B14CB5CB91ABBC1C1A996F8BAB262 (void);
extern void U3CU3Ec__DisplayClass28_0_U3CConstructNewPairsU3Eb__0_m2681A45BE7527FFCD5B729180C4F94690B578362 (void);
extern void U3CTargetsInRangeU3Ed__18__ctor_m5F965EB95862AC41A0736BA02D2151E8C580512C (void);
extern void U3CTargetsInRangeU3Ed__18_System_IDisposable_Dispose_m448ECC2390AF1169724F81889721A0C01B2780F9 (void);
extern void U3CTargetsInRangeU3Ed__18_MoveNext_mCBAF7FF2CECA89E79DB876739B45391ED8FE2889 (void);
extern void U3CTargetsInRangeU3Ed__18_U3CU3Em__Finally1_m7EBF00C2F8270C66129AD3FB75D3B13345CF070D (void);
extern void U3CTargetsInRangeU3Ed__18_System_Collections_Generic_IEnumeratorU3CPolyperfect_War_ITargetableU3E_get_Current_m38E981215A379735A534AEE41E1853FD3AA501C1 (void);
extern void U3CTargetsInRangeU3Ed__18_System_Collections_IEnumerator_Reset_m6ECC9508627FFE0D4F36CB0720217068F2961DBE (void);
extern void U3CTargetsInRangeU3Ed__18_System_Collections_IEnumerator_get_Current_m58CAA7747AA578832C7A0FBBD6AD68EE1E93F25A (void);
extern void U3CTargetsInRangeU3Ed__18_System_Collections_Generic_IEnumerableU3CPolyperfect_War_ITargetableU3E_GetEnumerator_mBBC0C531B5A9722795DD8C508C379A75D678352C (void);
extern void U3CTargetsInRangeU3Ed__18_System_Collections_IEnumerable_GetEnumerator_mC67943BACDD21ADB1D15C7A0CE237254A1D980FD (void);
extern void SoldierAnimationManager_get___Usage_mF2AEEB6945EC16EAE4E1265C54563D28311DD498 (void);
extern void SoldierAnimationManager_get_IsDoingSomethingWithHands_m396969B616096B2067A3BA5F26B6AD4CA187778F (void);
extern void SoldierAnimationManager_Awake_m9C12D5D8F225B8A26508B2FAFF70DF4DC83E577B (void);
extern void SoldierAnimationManager_HandleJump_mB4CCF814F5D64ED8C52619268228C2AFB3D01D43 (void);
extern void SoldierAnimationManager_HandleWeaponShoot_m34930D4CBA94E888A074669D1145B158430A5EC5 (void);
extern void SoldierAnimationManager_HandleWeaponChange_m6C42C1FE73B1E1B3FBEAECF86BF1D259FD2F5907 (void);
extern void SoldierAnimationManager_Start_mE5F58B8B2E3E6C2F555EE990C3C90F1AB0461CAA (void);
extern void SoldierAnimationManager_Update_m1BA025C1169AADC4C1C7EA374CCCCFC169FE985F (void);
extern void SoldierAnimationManager__ctor_mEB14B7A6A1CE6AB572CF49311E059CE0E6AB244C (void);
extern void SoldierAnimationManager_U3CStartU3Eb__22_0_mFEF04BCBD96E570D04A7EF71A44364BC41BE9851 (void);
extern void SoldierAnimationManager_U3CStartU3Eb__22_1_m1F3021A18F0CD7A600C863D487A3B6151AD11566 (void);
extern void CarMovement_get___Usage_m8F4649EED6EC51AE48447E889979E97B11854DBA (void);
extern void CarMovement_Awake_m719DC8D893FAC2B786A7BC6C959DA7CCC0361940 (void);
extern void CarMovement_HandleChangeLights_m662FAAAE113B39BF7099377509FDD48EFC6B2CD1 (void);
extern void CarMovement_Start_m9A58DC97476260FD412F495FECE798788A97F95C (void);
extern void CarMovement_ApplyLocalPositionToVisuals_mE78C45C3A60401533BA5D557C58D0E5A23C9EB38 (void);
extern void CarMovement_FixedUpdate_m8526AE63F12A5F292282676329164D5C7D9CB31F (void);
extern void CarMovement_StopInteracting_mBD166C71769EA5EFBF133779582E11CA70EE2AB9 (void);
extern void CarMovement__ctor_m504E7B25D421AD6526ACCE5B889BFBAA1E52C4AE (void);
extern void Axle__ctor_m984E29D66BE390682E3C5035FC9C5C8196ED0D92 (void);
extern void Lights__ctor_m29EAD7DC8BDB80CFDCC5067D88D2E1CDA4E4FD07 (void);
extern void U3CU3Ec__DisplayClass15_0__ctor_mD6FCC2B4D338D05FC833F37896C773633935F8D9 (void);
extern void U3CU3Ec__DisplayClass15_0_U3CAwakeU3Eb__0_m31080A2FAFB4205E431FF46D744773A04F8078BA (void);
extern void ParachuteMovement_get___Usage_m99C0DF94207BE318CDDF4CFE299AEAB2F57B0C49 (void);
extern void ParachuteMovement_Start_m7F6D6B2F4593C100A4676528FB2ADCCAA562A03B (void);
extern void ParachuteMovement_FixedUpdate_m0878729B82E5F51E193D5B7327C341E54AD466D4 (void);
extern void ParachuteMovement__ctor_m66143B15452AF67205E35F675DD9A9C6EE270AFA (void);
extern void ParachuteMovement_U3CStartU3Eb__8_0_mE80533EB3EBAE260E54EDE18AC796FADE3E746C9 (void);
extern void Wheel__ctor_mA571E17BFBF49C2C089C4EB3C87E267757FB29D6 (void);
extern void Wing__ctor_mCE93B445F006FC0AB3CD7A410CAD58F630CD873E (void);
extern void PlaneMovement_get___Usage_mDD20F5272BF09F9624E8E7B2011FE528C3D7E617 (void);
extern void PlaneMovement_Awake_mA5F3C1D422966E593111D2B941637EA74664124A (void);
extern void PlaneMovement_HandleExit_mBB5A9D5BB90724A321D89E0FDE3F2DA9C11EE633 (void);
extern void PlaneMovement_HandleEnter_m05263C4C83DA4D610979F1C690C4916F486FDDB8 (void);
extern void PlaneMovement_Start_mCCF9CDAF26C040DB5D6461399EFF9CCB385A01E4 (void);
extern void PlaneMovement_Update_mA2F70EC714A3C4A21E6ED11C3EC40CA7EE4A113E (void);
extern void PlaneMovement_HandleLandingGear_m372B7A49B31A09DBAE62FE1AF0C9D28380C76980 (void);
extern void PlaneMovement_FixedUpdate_m3849FFC875C3B1147350D54CAE369837BA14B7A9 (void);
extern void PlaneMovement_CalculateAerodynamicForce_mAF7AB05C182F2285EC739DCF868EC4CE212F7EBF (void);
extern void PlaneMovement_ApplyLocalPositionToVisuals_mA38F84B515A6CE96CA10DCEC33DB4C03D8AF3D7D (void);
extern void PlaneMovement_Sound_mB21D2AC30591E83FAE4E65C4D4C6AAD1EFAEEC9E (void);
extern void PlaneMovement_GroundCheck_m36260FAA427A5C41663918ED042FB03C71657BE3 (void);
extern void PlaneMovement_WaitForWheelsDown_m881ADE1D47C47F4D1BE1D5F2A184B5F3B72BEB4F (void);
extern void PlaneMovement__ctor_m504C411C2074BF71AB38C0FA5BB3E0B7D1DC2B84 (void);
extern void U3CWaitForWheelsDownU3Ed__63__ctor_m1E05CDF883D5281CAF8EBED24507A998291604D3 (void);
extern void U3CWaitForWheelsDownU3Ed__63_System_IDisposable_Dispose_m22C8EAB020C441BC49D408226675434DA9855A71 (void);
extern void U3CWaitForWheelsDownU3Ed__63_MoveNext_mCCBB4B17A516CD083AE801EE11B1A878789E3C55 (void);
extern void U3CWaitForWheelsDownU3Ed__63_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mF16E6F4A5DB54CFD904D713206AE396E591CCB33 (void);
extern void U3CWaitForWheelsDownU3Ed__63_System_Collections_IEnumerator_Reset_m40C492D264A1FB0AAA4AEBFB2BBE69220D2EEAAE (void);
extern void U3CWaitForWheelsDownU3Ed__63_System_Collections_IEnumerator_get_Current_mF0FA3882CF2B5E280C41623E53395663FEDCFAF5 (void);
extern void TankWheel__ctor_m3C9C7842FA2A1124F5C949CD4863F422B22BBC8C (void);
extern void CosmeticWheel__ctor_mFD3AE96B61010E3CB27AF845DE1F21FC0419F377 (void);
extern void TankMovement_get___Usage_m9EC0277FEC490311F6F57C48EBC8B230167999E1 (void);
extern void TankMovement_get_canGetOut_m0EE3021D1D65C26B02C07D200AA23F2C81B3B413 (void);
extern void TankMovement_Awake_m199709ACDF4F1B8592082BDA99950E5583E355F5 (void);
extern void TankMovement_Start_m00E2F3CB96A48ED2C439D9EF93C78244AB0AD9A8 (void);
extern void TankMovement_Update_mAD109D4E583783162D0C547A6620AA5F3F7BF606 (void);
extern void TankMovement_FixedUpdate_mA8CE06E1AB4DCC0CF21557FE78B4037187C2BCCA (void);
extern void TankMovement_GetOutOfTank_m4D2FC029C6B76E5318F0574A542650761A2E6B3C (void);
extern void TankMovement_ApplyLocalPositionToVisuals_mDF6890FC55F89BB8E677B0C09361B3E6A228A566 (void);
extern void TankMovement_Sound_m061DEFD17EF700F2400B60EA9565E1037DA82997 (void);
extern void TankMovement__ctor_m269C7DF78733D7E27A1C610AFBBE0D21636C7F1C (void);
extern void TankMovement_U3CAwakeU3Eb__25_0_m75BC5BB54A50E6CEAF3AA0E55DD771EC47096309 (void);
extern void TankMovement_U3CAwakeU3Eb__25_1_m633ABAD1E1B2F36108056E7C440FE9E8D101521A (void);
extern void TankMovement_U3CAwakeU3Eb__25_2_mC0EF78FFF988DFEE8B5A972A6371A04F00F45C35 (void);
extern void AudioParams__ctor_m4738F44774B4C54A1046152EED76E4C32FD1F630 (void);
extern void AmmoBox_Pickup_get___Usage_m98FCEDA9403518BEB9C598A0838F9ED001827A87 (void);
extern void AmmoBox_Pickup_Initialize_m9BD75EEBDDDC051233A9554582673D5350EDDDFD (void);
extern void AmmoBox_Pickup__ctor_mB617457F9D144C866C755216598F9FD384CAED54 (void);
extern void Ammo_PickupCollector_Awake_mA3FFED954FBA741CEE8671A533EE46A88BBF0788 (void);
extern void Ammo_PickupCollector_PickUp_m1D1726AEDBC0C20B89081BF0362D960F74F05C5A (void);
extern void Ammo_PickupCollector_CanPickup_m9597DB086137368C5AEB7C734EF9F75CDD1FD95B (void);
extern void Ammo_PickupCollector_get___Usage_mDC14FFF5B7D11B37F55AC76ED14D5724F0D59E68 (void);
extern void Ammo_PickupCollector__ctor_mA69C0513C7B816CF3CC1FAF88ABA7AB762661F1A (void);
extern void Medkit_Pickup_get_IsInstant_m8F9E7DC365532858B852B699D63B1A90DFD8D98D (void);
extern void Medkit_Pickup_get___Usage_m0572C66ABB9C185C134CF00014204297D281EEE6 (void);
extern void Medkit_Pickup_Initialize_m408B2F1DDCB91D8FBFA5D3994F0B520B6F8E7C38 (void);
extern void Medkit_Pickup__ctor_mF694F6EAF81D7E3AE27CDCB8D70EF9BF3D13CA9E (void);
extern void Medkit_PickupCollector_Awake_m483BC0010ED393013923F238E59E6D00F1C49A60 (void);
extern void Medkit_PickupCollector_PickUp_m7097623954ABFE32907616196A16E1EFA645D209 (void);
extern void Medkit_PickupCollector_CanPickup_m4A58DCE97303C61B9D05F76F78E625B163045488 (void);
extern void Medkit_PickupCollector_get___Usage_m94C333A181C9FA4F5877182E9145EF4CF2BCBF6E (void);
extern void Medkit_PickupCollector__ctor_mB1820681491905FD4910B4F797BB89710AF4FB87 (void);
extern void Usable_Pickup_get___Usage_mB3EE3016C31ABD8EA6680BFEE0FEF5FEBB77AE4E (void);
extern void Usable_Pickup_Initialize_m0345E39946EAEF8B9D7F2BE53AE1CB24D799E476 (void);
extern void Usable_Pickup__ctor_m5F8A82E034C8DDF70FB2C514B07B0BE387F5CE22 (void);
extern void Usable_PickupCollector_Awake_mFCDCB88802357A54EA485CE49E785541A382EC38 (void);
extern void Usable_PickupCollector_PickUp_mE1E755BDDD5DF036A8A727EBAF24DB4DB20844D6 (void);
extern void Usable_PickupCollector_CanPickup_m5C278E6F6A45A16D92F8D342427ADEF435E73A5C (void);
extern void Usable_PickupCollector_get___Usage_m7A398457F01FB8CD635682663CB5D76B8737BF56 (void);
extern void Usable_PickupCollector__ctor_m50C4E7FC0E2AA248AE24B77936C9BA6077E5A9F8 (void);
extern void AmmoReservoir_Proxy_get___Usage_m04FB41B0897F84048C07CA80101F258566D93D46 (void);
extern void AmmoReservoir_Proxy_get_Ammo_m0941C9067BE408437B9AC525CC00F1145C42480E (void);
extern void AmmoReservoir_Proxy_set_Ammo_mA97CE1388E67AF308381CFCA94D79A883EECE9F6 (void);
extern void AmmoReservoir_Proxy_Awake_m1BB493B4A7904EBFD28B4332AABE57164AD85737 (void);
extern void AmmoReservoir_Proxy_HandleUsableChange_m075C3BCE898A3AC6D69B0E8C95D0215914D072D5 (void);
extern void AmmoReservoir_Proxy_HandleAmmoChange_mED7F13612229428ABCCA533862E99346B80091B5 (void);
extern void AmmoReservoir_Proxy_UpdateAmmo_m4C3104FE52298EADBA409CF8B53FC16BBBEF60A9 (void);
extern void AmmoReservoir_Proxy__ctor_m7D4DAC343FB0DEE9479B0E191DD677C5ABC5F1D9 (void);
extern void AnimatorIKCallback_Forwarder_RegisterIKCallback_mB02F4F527B7345B067CA5EB29B7A5773F6AF755B (void);
extern void AnimatorIKCallback_Forwarder_UnregisterIKCallback_mBD2D8FBC4F0C33951562D4D2FD1BF054DCA5EA12 (void);
extern void AnimatorIKCallback_Forwarder_OnAnimatorIK_m0D8082997897FAA3B544D24AB31696355116AB57 (void);
extern void AnimatorIKCallback_Forwarder__ctor_m2C6DFAAE5ED6B6630FAD3B9C0C40B2DE6FDE1BF7 (void);
extern void U3CU3Ec__cctor_mA984C9B89C784CA1B2D7DBE23B43860CBE34BDB1 (void);
extern void U3CU3Ec__ctor_mB47DAB6D80C497A5A6F18A8E3832C96C9ADD5267 (void);
extern void U3CU3Ec_U3C_ctorU3Eb__4_0_mB33A79FB8985C89BC9554CA5454CE6DE97078048 (void);
extern void Animator_Proxy_get___Usage_m45C1CE35381397AEBD510BB08455C44C0271DB8F (void);
extern void Animator_Proxy_get_Animator_mC6B532ADEF79648EA2CB8354E62EC12E4DF9BD85 (void);
extern void Animator_Proxy_set_Animator_m720B03E281DD9ACD072276AC0DD0DF9EF7A84802 (void);
extern void Animator_Proxy_Awake_m31ACF1C1F9168CDB357C4148FD0A2188426E1FF8 (void);
extern void Animator_Proxy_RegisterIKCallback_m0D3EE40C3650ECE5F7E0A7623C5483BDA3E2AB95 (void);
extern void Animator_Proxy_UnregisterIKCallback_m06FA4169ACB865A4C3057D351419CA1B9BFA845C (void);
extern void Animator_Proxy_OnEnable_mDFC3461F782086663808C564EAC301CF522434CD (void);
extern void Animator_Proxy_OnDisable_mE8421B759FCFBD5AB18E5DF157CAF2DFEE72F545 (void);
extern void Animator_Proxy__ctor_m5F3393476588EDBB0F2D0AF9C67136DB41ED0D40 (void);
extern void UseContext_Holder_get___Usage_mDC8C9C175725F0DA820C50273F7B0EF30C2CE813 (void);
extern void UseContext_Holder_get_Context_m0D7898E7239760520883712F17B388C6686FDF93 (void);
extern void UseContext_Holder_set_Context_m15EC3D6AFD59C081CD15A7D1C1F9B56E33783DF3 (void);
extern void UseContext_Holder_Receive_m6A490077D9AF66FC4439BB3562DE3F0CBBB50C50 (void);
extern void UseContext_Holder__ctor_m693719C8377154B36E219B60D9C5D84DF223B396 (void);
extern void Ammo_Receiver_get___Usage_m033499F9D830E66A14043193435B581519177A2C (void);
extern void Ammo_Receiver_RegisterInsertConsumerCallback_m8BA439CFA193F25B636DE1556D7DF6762C059D5D (void);
extern void Ammo_Receiver_UnregisterInsertConsumerCallback_m3C0AF379885027EFDD4D1E44C9710126E0CB9711 (void);
extern void Ammo_Receiver_RegisterExtractConsumerCallback_mD6C0F7D644B92F749298475513DFC00F92903C77 (void);
extern void Ammo_Receiver_UnregisterExtractConsumerCallback_m05C17F4846154CEDDAA363DA320CEB4DD83D6767 (void);
extern void Ammo_Receiver_InsertPossible_m64704A11E30167F4D8ECA2F9776D2EC75CE89D09 (void);
extern void Ammo_Receiver_ExtractAllAmmo_mB1B39204C2A086653B35D0BB445F913E8F40837A (void);
extern void Ammo_Receiver__ctor_mD8CDB9DA8ECA284EB48EA41BA9586D94CDBC1D66 (void);
extern void U3CU3Ec__cctor_mD24D3BB089EB5634F8BBC4D484F3E1C756A68928 (void);
extern void U3CU3Ec__ctor_m267A2CA6C88F1C71543070E078D4369D604D894F (void);
extern void U3CU3Ec_U3CExtractAllAmmoU3Eb__9_0_m756D47AED9B12AF2EBA668A92CEB6BA3C10193DF (void);
extern void AnimationEvent_Receiver_Awake_m1E55AF9CDF535D713FE61AA0C780ADC8B98151BB (void);
extern void AnimationEvent_Receiver_SetTrigger_mB85AB91B15223C8C3EDE369330FC4C8F725057DF (void);
extern void AnimationEvent_Receiver_SetBool_mF88BB0B75AFA356D413B9CA50EE93B87A51BEBA3 (void);
extern void AnimationEvent_Receiver_SetTrue_mA8CC876555A61F651ADAB5D052FA8F95BB409F3A (void);
extern void AnimationEvent_Receiver_SetFalse_m5F6907B835A80EEC9A530EED2C5FC441CD6FB5B8 (void);
extern void AnimationEvent_Receiver__ctor_mDDF6843346BB1DBCC57A7BF5EA6CCA5FAC619236 (void);
extern void Damage_Receiver_get___Usage_m61476756CC0FB128998615DF819FA4A1491977BB (void);
extern void Damage_Receiver_RegisterDamageReceivedCallback_mB705CDBA22B834E56E2CC31744A8856EB038AF5E (void);
extern void Damage_Receiver_UnregisterDamageReceivedCallback_mFE449327BD55FA465D68D7EA755CA602527BD4DF (void);
extern void Damage_Receiver_TakeDamage_m83C3AF9CC42512953A3EC1ECBF836B4C3F84C755 (void);
extern void Damage_Receiver_TakeDamage_mB5878FCA8C466643AEF4309AF81C3365E78D3817 (void);
extern void Damage_Receiver__ctor_m33E8748D50134B37158AC8913D759640DE27B587 (void);
extern void Ammo_Reservoir_get___Usage_m460FDEDD54829DC67BE7B126067DDD3A91F0A3CC (void);
extern void Ammo_Reservoir_get_AmmoType_mB08B6033710B345723B9D5C4C873FF4462CCC37B (void);
extern void Ammo_Reservoir_Awake_mA670E818A2ECC550F5E28A2D2D1B25F3C2C2EE4B (void);
extern void Ammo_Reservoir_OnEnable_mF1460C84F4763BF14E39293E6D85DC5F1F8EF460 (void);
extern void Ammo_Reservoir_OnDisable_mBD46A5291F337F9D95FAB854133361E5F91D9D0C (void);
extern void Ammo_Reservoir_ReceiveAndOutputRemainder_m5751ED44C7194902812D8D35A7CAB8B61BEC4839 (void);
extern void Ammo_Reservoir_ExtractAll_m561A0EC90FF4E5274E0858F239E3280E5434C133 (void);
extern void Ammo_Reservoir_Reset_m7019E15070CB0C47D841B98DFEEB3AF822816512 (void);
extern void Ammo_Reservoir__ctor_mF7A0A2997522B818AE143F5794E93A3B082E0D0C (void);
extern void Energy_Reservoir_get___Usage_m254E4DD8F72CBD186E430F19B4610091B2EA3BE2 (void);
extern void Energy_Reservoir_Update_mD343A22E28DE2C4740A14962357AB077306456AA (void);
extern void Energy_Reservoir_TrySetEnergy_m25B91E218EAACF9F4A87A3AAAC1CC25FDC526813 (void);
extern void Energy_Reservoir__ctor_m1E62281D72627E980AB2D70C0D04DB2A25673E5A (void);
extern void Health_Reservoir_get___Usage_mDB857E15E02B7206FDE7CD66AD6FB2686D33C224 (void);
extern void Health_Reservoir_get_MaxHealth_mD3CD57EF2F85BF71E56E993EDC7C8C3D5B347106 (void);
extern void Health_Reservoir_get_HealthFraction_mCED3DFE1A5B8F30C9A16351CB94994FFED23E897 (void);
extern void Health_Reservoir_get_IsDead_m9C9456E81A5BA3328E1CA023615935AB1BA086C5 (void);
extern void Health_Reservoir_get_IsAlive_mE56A77EC3B62C14FD5993931FCE707F454569897 (void);
extern void Health_Reservoir_Reset_m76CFC31998402D70D720DF007DBB03311ADE3E5B (void);
extern void Health_Reservoir_get_Health_m8CF7495EA588EA4184C245298DFDB49BA78B4987 (void);
extern void Health_Reservoir_set_Health_m4095815491674E03F5261A012461797AB7488EF0 (void);
extern void Health_Reservoir_Start_m41A2DA321D91591E2F11E962B892D48CDEF88132 (void);
extern void Health_Reservoir_Update_m192BBB1EB36C81219BA54285A02D68BD671380B0 (void);
extern void Health_Reservoir_HandleRespawn_mAA321F735917C3B9A8C147582410BB75831DAA19 (void);
extern void Health_Reservoir_HandleRevive_m6653A0BC211185617FB0028927C0004CBB811820 (void);
extern void Health_Reservoir_HandleDeath_mB89523B6DBF26CD5BFC0FF4F84809C1FA50C59D1 (void);
extern void Health_Reservoir_ReceiveDamage_mB87DEBE2C0B949782E523E70F43719E8EF1D49AA (void);
extern void Health_Reservoir_RegisterDeathCallback_m3A18BBD4660D3F2FCF08332A4D405F716E168B65 (void);
extern void Health_Reservoir_UnregisterDeathCallback_m141D4636B46EFE0FEB05E24027EFAEC56E9D5C40 (void);
extern void Health_Reservoir_RegisterReviveCallback_mE2AC0A9D006B33A93D7936B7AB14FD686C4BC970 (void);
extern void Health_Reservoir_UnregisterReviveCallback_m452CABD820D64A46F07CF60038FECB0F7A40EA3E (void);
extern void Health_Reservoir_UseMedkit_mEB9986EC0B77761017A6C05E400925CEA6F53ACE (void);
extern void Health_Reservoir_StopHealRoutines_m9201955C0DC623CA820DC41B8F7DBD44135D0CA0 (void);
extern void Health_Reservoir_HealOverTime_mFF40CAC78563D604A5392FF0211617EE7300C650 (void);
extern void Health_Reservoir__ctor_m88729C8B2DF907C580AF9242BBFB7A5D8D9A1A6B (void);
extern void Health_Reservoir_U3CStartU3Eb__16_0_m6137C2EDE538D98F9971200F089ACA6DCB755447 (void);
extern void U3CHealOverTimeU3Ed__29__ctor_mB4F3DB3418939FD7257B0F22278D9531CC3FA2C4 (void);
extern void U3CHealOverTimeU3Ed__29_System_IDisposable_Dispose_mC54ADB94A150820213254FC664A87A170928864B (void);
extern void U3CHealOverTimeU3Ed__29_MoveNext_mF2C2C8A7B6FD68737247ED237468611F3B0B16FE (void);
extern void U3CHealOverTimeU3Ed__29_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mD41D0E968DDE5DB9E66CB28DC5098E8FAB352821 (void);
extern void U3CHealOverTimeU3Ed__29_System_Collections_IEnumerator_Reset_mF69EB1A5AD86B2DC3CE74D22200D1A4515DA9618 (void);
extern void U3CHealOverTimeU3Ed__29_System_Collections_IEnumerator_get_Current_mD79A89D05F7B30A5703913CBAFB7B56B5BB6E042 (void);
extern void Int_Reservoir_TryUse_m5BD9166C4851323AC87B16F21112FC2E6B80454E (void);
extern void Int_Reservoir_get_FractionalAmount_m890134F7B8A3D704225469DD37508F1EE186123C (void);
extern void Int_Reservoir_doExtractPossible_m053BDC283CD6BF1ADBC9B827BEEA8789D76217EC (void);
extern void Int_Reservoir_CanFullyExtract_m14193D273495C9914E8E6B206F487E7C21A54174 (void);
extern void Int_Reservoir_IsAtMax_m9A9D0EA8E1E9F4396CA7038AE83F9ABF47F63E70 (void);
extern void Int_Reservoir_doInsertPossible_m1EDAFB7CED253B1BC4723A742F424799CAE0E168 (void);
extern void Int_Reservoir_CanFullyInsert_m5E4A55C2B47B274B0D39E17CCCBBD482228B6CA8 (void);
extern void Int_Reservoir__ctor_mC5C2BD2B0F6354EC545527BD0DA23B39542CCBA0 (void);
extern void AnimationEvent_Sender_get___Usage_m886DB640AF3C8B8DA1EF4935A8FBEFC0D1F75663 (void);
extern void AnimationEvent_Sender_SetParamName_mFF2313FA9F9F53DF5459976FBB0DC047C8EE5B84 (void);
extern void AnimationEvent_Sender_SendSetTrue_mCD1949A7ABFB58D0A19A99A054CA21FF07492044 (void);
extern void AnimationEvent_Sender_SendSetFalse_m35B30C5A768FB884989144C8152A12D78A02D48A (void);
extern void AnimationEvent_Sender_SendSetTrigger_m08CE4033A784D30E9C108F201F775CF4CF2788DD (void);
extern void AnimationEvent_Sender_EnsureReceiver_m2D03736097E6095DEFC2F3BEA187085846759CDE (void);
extern void AnimationEvent_Sender__ctor_m84A3A1AA3F4ADECE4933C8EBC2ABC7F132A6CF3E (void);
extern void AreaOfEffect_Damage_Sender_get___Usage_mD187F894A41FFE9EE9AAD8825ED11ADF4A941736 (void);
extern void AreaOfEffect_Damage_Sender_Start_m280972DB7B441CBD4A674D10F8A8D448AB013CA8 (void);
extern void AreaOfEffect_Damage_Sender_SendDamage_m1B27EA81DB396A7FBA5BB6DEE6795CFCEBE8DFAB (void);
extern void AreaOfEffect_Damage_Sender__ctor_m008E25DF8E4992567F02FC756FD9164A15204E2C (void);
extern void ContextDrivenAreaOfEffect_Damage_Sender_get___Usage_mCD9AA3692539530B9CEF1775594E714B939E3E25 (void);
extern void ContextDrivenAreaOfEffect_Damage_Sender_Receive_m6BB566B3DD2364205949EDE8B7287F83EBC083DA (void);
extern void ContextDrivenAreaOfEffect_Damage_Sender_OnDrawGizmosSelected_m4A33F079713102E04CB7884FCB9BBED1F3DB58D8 (void);
extern void ContextDrivenAreaOfEffect_Damage_Sender__ctor_m874A6E9F3D4FD45E6DD15C85E3287A0DD3F0D432 (void);
extern void OnCollide_Damage_Sender_get___Usage_m6657B2C59698E38F34343CB293A1A9B1DABD4493 (void);
extern void OnCollide_Damage_Sender_OnCollisionEnter_m58B79AE55A9285A3DBEC887DB10768A61F048D2D (void);
extern void OnCollide_Damage_Sender__ctor_m19C1AABB10D7A25209DF6935A609FFF5BF255F5A (void);
extern void Ally_Tracker_Start_m3F48025E287677F9F4FA8EDF930B8126CC79D071 (void);
extern void Ally_Tracker_get___Usage_m4A2014965BAA91DFC1C2F1A2388E3F4F10E16438 (void);
extern void Ally_Tracker_ShouldTrack_mDA84B6B9851F647722695C3F23DC9B6D5F5F4F9B (void);
extern void Ally_Tracker__ctor_m5B61991A4E42DD142BF8A75D71368B492748E4D2 (void);
extern void Commander_Target_get___Usage_m78869208EAA759E363C1769EFC33C664322A8BC3 (void);
extern void Commander_Target_get_Faction_m0FDAE38D1225AFC5156DEBF5F55EEAAD2F766C84 (void);
extern void Commander_Target_Initialize_m872EA829BE2B55334A9B91E39BF7455F6A962647 (void);
extern void Commander_Target__ctor_m9799C1A7BA11FD26ABC9C31FE28BBAD7600CBC8D (void);
extern void Commander_Tracker_get___Usage_m03081D94D4D5F01E5963EDB14F37DA8E3ADDED1F (void);
extern void Commander_Tracker_Awake_m08BD5B9D6E3EE3D56C27196B2D1602123379D070 (void);
extern void Commander_Tracker_ShouldTrack_m74697BC078465A872B421DDF545F692CFB7ED485 (void);
extern void Commander_Tracker__ctor_mA683684DD115DC641A178A2BA0B58C3E3E67CA27 (void);
extern void Faction_Target_get_AttachedFaction_m63D45D470F271795A883EB0F97941E568C92F4E5 (void);
extern void Faction_Target_set_AttachedFaction_m762E6CB3B0586ED56E80FF5FD268C40B6E4625BA (void);
extern void Faction_Target_get___Usage_m404268F865F7A171DFFB4325979074C4640708BF (void);
extern void Faction_Target_Initialize_m32571503DAC8CF019EA1933E4317CD80F3C45028 (void);
extern void Faction_Target__ctor_mAF93580B4D67CB1E79A1563CC3BE8238A0AC90A0 (void);
extern void Hostile_Tracker_get___Usage_mFCA8AA8B2B4F3C8009E6B0C201350472B2303D17 (void);
extern void Hostile_Tracker_Awake_m2DD8EF78D69368C1BA37D74BA413F2B185CFFD1F (void);
extern void Hostile_Tracker_ShouldTrack_mCA08EF00833E49C716B53CDAB4706DBCAE630B98 (void);
extern void Hostile_Tracker__ctor_m719561F682229595E58100ECCAF62FE9293F5027 (void);
extern void Medkit_Tracker_get___Usage_m0BB0BFDAFAB3EFB0947FEB18E89FB55FE301226A (void);
extern void Medkit_Tracker_ShouldTrack_m48D4BE65110EBA007FEF14CC1F8134CC9A91C183 (void);
extern void Medkit_Tracker_Awake_mCDF0EA45E999FB3D90C6D62ACAEC49554CF4DF4F (void);
extern void Medkit_Tracker__ctor_m16A8895F7EE6FA597EE3295B8E1C9CA5AC9B8BAB (void);
extern void Objective_Target_get___Usage_mDE8D8D55101FEC59E5CC8527C8F61060847FCF7F (void);
extern void Objective_Target_IsObjectiveForFaction_m34DD6C5CF070821827DD88359942802DB2DAD823 (void);
extern void Objective_Target_Initialize_m4AA0D2F8FA1EF68DC851B08D485B19140063E297 (void);
extern void Objective_Target__ctor_mC957012D96CA62818D81EF14CFCDC04872FC08E2 (void);
extern void Objective_Tracker_get___Usage_mB3A89E8D30EBE5B7A90DD533DCF2510E1BA51951 (void);
extern void Objective_Tracker_Awake_m0204B484E531432CD2F176E1528DB399C6C13173 (void);
extern void Objective_Tracker_ShouldTrack_m79AC50F5203E33F22E829A38C77203597816059D (void);
extern void Objective_Tracker__ctor_m76798210A4FCF676201798CFC7CF9918CCCC1CC0 (void);
extern void Shootable_Target_get___Usage_m98978836796D2BC54B345441B14B56C42C79E487 (void);
extern void Shootable_Target_get_AttachedHealth_m0C7143F66DE2936A6B3D8CD2241F947DB3392A06 (void);
extern void Shootable_Target_set_AttachedHealth_mB12AD832FCF92571C3FA31B259B486F38217BFF7 (void);
extern void Shootable_Target_Initialize_m4FAB781F3CB444F7DA20648405C8CFFE5946C8F5 (void);
extern void Shootable_Target__ctor_m21AB54F581350E7941C83D967FBB5084A577E6D1 (void);
extern void SupportedAmmoPack_Tracker_get___Usage_m88D07C2CCAE098CE7D178789BF5F0CA7912A3D75 (void);
extern void SupportedAmmoPack_Tracker_Awake_mCD0F78F643EA7093554AD89258C98E616C6551F3 (void);
extern void SupportedAmmoPack_Tracker_ShouldTrack_mEDFCBFE587F9B3C418DE3965DAB7C98789B91BBA (void);
extern void SupportedAmmoPack_Tracker__ctor_mC66EA2E7AB703D4CE4A32492B2A356D388C8C422 (void);
extern void Supported_UsablePickup_Tracker_Awake_mF74DB7404F5327E350FEE8D6117AAA8E2340145E (void);
extern void Supported_UsablePickup_Tracker_get___Usage_m7C0CD60A9544F61A3210A75204C686FAEA63640A (void);
extern void Supported_UsablePickup_Tracker_ShouldTrack_m215E6BD3519A3D2091B7B606F68496935DE37AA3 (void);
extern void Supported_UsablePickup_Tracker__ctor_m3C6939668C829C88DFA26E2A5A27405D68E30284 (void);
extern void AmmoText_get___Usage_mE5325C5F8AB72D3D964E69BFAFAE2AA34331289C (void);
extern void AmmoText_Initialize_m702CC50A1C91C881DB950EC3809CE72E238DE051 (void);
extern void AmmoText_HandleValueChange_m6F09BB613A596AEF3ED89682DCF1A14266A5890A (void);
extern void AmmoText__ctor_m1D173F6C3139698C1BC264771C80D6ACD59F5B16 (void);
extern void FadeOnRespawn_get___Usage_m70D6A7AD03041F850164CF8B4C94BDB910BE5CA9 (void);
extern void FadeOnRespawn_Start_m1E492F787F82F17127CBF77A3CD83567C7D7FC0B (void);
extern void FadeOnRespawn_HandleRespawn_m7376A484581AC8AFD0782BB80748930B61673453 (void);
extern void FadeOnRespawn_HandleDeath_m78E1F54F59E5493252B4B3EC1BF9D517F111D3D9 (void);
extern void FadeOnRespawn__ctor_m0B9FA463D6220EABFEA754109F41FBFE3F070F2B (void);
extern void U3CU3Ec__cctor_mDAA5B5E0C7B3ED3A287A671C4B60CD7F1CA7C92E (void);
extern void U3CU3Ec__ctor_m9CED193E9B58B5BEE6816FC3536CA3AECF7E6CD8 (void);
extern void U3CU3Ec_U3CHandleRespawnU3Eb__4_0_mD4FF6C3FD70F46C6E0F1990AE4627292574F5E0D (void);
extern void U3CU3Ec_U3CHandleDeathU3Eb__5_0_m5A08A861D791D2D9682384F4B4061D37A6A2BC5C (void);
extern void FailureConditionMonitor_Start_m00FFFC124D4ADD913A0389FB7F3B4CFFDAB5079E (void);
extern void FailureConditionMonitor__ctor_mBB4B87F816E5C871231CEFCC8B7031DC3F527006 (void);
extern void FailureConditionMonitor_U3CStartU3Eb__2_0_mAC34E67A625A64740DC73E433A61A6A2F58D2A00 (void);
extern void FillByFraction_get___Usage_m3DD3F26C7C29803015CC3DCBB3D36FDA440FA9C7 (void);
extern void FillByFraction_get_SourceAsFraction_m222AA17A51425B4E09185784EE7CEC222FED5D1B (void);
extern void FillByFraction_Awake_mC15B647F8F15E97FCB793B59E0CAE45A0EB3A4C5 (void);
extern void FillByFraction_OnValidate_mFB14CD1652013E18856452022C28407821EBE8B1 (void);
extern void FillByFraction_Update_m2879EEDC057248E141CD09CDC107C56516B5A0AC (void);
extern void FillByFraction_UpdateFill_mEC0D12DFF42F3F701B4B406ABEFF7ECCD1C19031 (void);
extern void FillByFraction__ctor_mE8BBA822073B980BC58A9013DF4E74640B1AABF9 (void);
extern void GraphicFader_get___Usage_m1FAB322A485704BF45235308F3036B1BB2A2004E (void);
extern void GraphicFader_Awake_m1379F8E02E934523C49A173FCF43C7FFE5192D6B (void);
extern void GraphicFader_Fadeout_mC3F53C5219284AEFDFD4D52F736D4D10EE6DD699 (void);
extern void GraphicFader_Fadeout_m5357C3F89EFB59307E23BC216F634CCD4104A15B (void);
extern void GraphicFader_FadeIn_m6A0C2E59F7A89DF4D44213131A3DB7D0099CDC9E (void);
extern void GraphicFader_FadeTo_mC294F04401D910207977D0C14BF3A97C9DDD6947 (void);
extern void GraphicFader_FadeTo_m06B31D0E503ADFEBF9717A08A95C748001DDBD5B (void);
extern void GraphicFader_FadeFor_m73B11984C6CD35A764B8B69F192736B1EDF42FAA (void);
extern void GraphicFader_FadeForCoroutine_m689A9520F1CBDB5763EFEEDB0A527CD4E4091A0C (void);
extern void GraphicFader__ctor_mBF30F02C075B65933A3BCB23A359451088666ED5 (void);
extern void GraphicFader_U3CFadeoutU3Eb__5_0_m8663DB97117789A2E4C76BB664BE6ECADBD88A5C (void);
extern void GraphicFader_U3CFadeForCoroutineU3Eb__11_1_m4D605F1BED403F9D5B02768AFB4F7F859B2C6BDE (void);
extern void U3CU3Ec__cctor_mF01B4E2401AA69392F965F993264327325F2CD95 (void);
extern void U3CU3Ec__ctor_mF46D6A4FA94A19E37D62F5B282AA4A00520548B4 (void);
extern void U3CU3Ec_U3CFadeInU3Eb__7_0_mC15C8105AACF146D60CEC6D33BFD77A8505F8E84 (void);
extern void U3CU3Ec_U3CFadeForCoroutineU3Eb__11_0_mA58B22E650BFE83BB666DF34C02980F9C943DBA5 (void);
extern void U3CU3Ec__DisplayClass8_0__ctor_m5609593759E8EC1A08DA40ACBD6C14B95ADD3593 (void);
extern void U3CU3Ec__DisplayClass8_0_U3CFadeToU3Eb__0_mD6E066948910C10C953B2A83D371DF3111604E5D (void);
extern void U3CU3Ec__DisplayClass9_0__ctor_m4D290AADB34AE140BD94CCA5908478381504B30A (void);
extern void U3CU3Ec__DisplayClass9_0_U3CFadeToU3Eb__0_mCEE00E9BF6265EB8D9EDEF2136F454584BCF74D0 (void);
extern void U3CFadeForCoroutineU3Ed__11__ctor_m77C8B44B00B5A1FF3DE627D70098851A589589C2 (void);
extern void U3CFadeForCoroutineU3Ed__11_System_IDisposable_Dispose_m5F9DB6C218546669BF0AD7326A17BC05AF906D72 (void);
extern void U3CFadeForCoroutineU3Ed__11_MoveNext_m924692DB9840938DA7C8A914D830B1E9064EEAFA (void);
extern void U3CFadeForCoroutineU3Ed__11_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mE682A1E9EF81DDEAB1495B51CAB9D76D34C04031 (void);
extern void U3CFadeForCoroutineU3Ed__11_System_Collections_IEnumerator_Reset_m42AC008D5F22E11D47EBD0E98E613E1717BBCB2D (void);
extern void U3CFadeForCoroutineU3Ed__11_System_Collections_IEnumerator_get_Current_m08BF13B30A5DE7FECBEC44991B203E6039037D62 (void);
extern void HealthBar_get___Usage_mAB2866A2F287759A5A1F778D2C8BC79B325BC96E (void);
extern void HealthBar_Initialize_m985C715DFC69E9DB795D5263D244DBEBCD0BEB6D (void);
extern void HealthBar_HandleValueChange_mF5005CBB006351CFD3B2B2A3131B13E3804CBF70 (void);
extern void HealthBar__ctor_m496F76F61BFAE1354DFFA863635440500A41CCA4 (void);
extern void HealthImageSwap_get___Usage_mA1A20C9CA612CFDFDB31A1F96D01E15ECCCDCAC5 (void);
extern void HealthImageSwap_OnValidate_m8D8637BFD4DB74413D9BA52CEA28F71FADC76CFA (void);
extern void HealthImageSwap_Initialize_mE0E00FA3AB4459BD86FB7238E75582702E82CA18 (void);
extern void HealthImageSwap_HandleValueChange_m2169B116D474E1D543737A8D1EFD7DDD560FE959 (void);
extern void HealthImageSwap_GetSpriteForValue_mF552F31166A9ED8325BF364F2AA759DE4356E441 (void);
extern void HealthImageSwap__ctor_mC08B5078A7CF544D59AC8EE51CDF4E025C8FCA7C (void);
extern void U3CU3Ec__cctor_m6075FB1DE996C4D75D21C823175F33912518E4A4 (void);
extern void U3CU3Ec__ctor_mE4880F5BE2D8B4AC43BE66205683D9297A477388 (void);
extern void U3CU3Ec_U3COnValidateU3Eb__5_0_m014EA6DD29991B9C1CD8900E1B4A1F91B5E72842 (void);
extern void HealthText_get___Usage_m4D3EC1E17EDF13F9132259E53C3C6EFA6550A44A (void);
extern void HealthText_Initialize_mEBCD43B4C83E35AC8D40693B81F90B43E1F92525 (void);
extern void HealthText_HandleValueChange_mC1DF77637D1E26C0BCC34BFC6F78BFFA48501CEF (void);
extern void HealthText__ctor_mEB1764B8F6374C25A733B4DB8673A62542CCA949 (void);
extern void InsufficientAmmo_Monitor_get___Usage_m5F519CD32E6EBB596B950E56B4418EFB4DD82706 (void);
extern void InsufficientAmmo_Monitor_ConditionMet_m7158A667274D052CFE4D1A22F16E540516E05319 (void);
extern void InsufficientAmmo_Monitor__ctor_mAD5F69146E8130844AA318D459E68CFA0DE61FC8 (void);
extern void NoReplacementAmmo_Monitor_get___Usage_m030ADC7C0C141A71CD907133C3EDF82B23AE28BD (void);
extern void NoReplacementAmmo_Monitor_ConditionMet_m5DD64887784C5EABE023AE29DD623C1679AC8BC9 (void);
extern void NoReplacementAmmo_Monitor__ctor_m2BC42C2BA6D8F8CA4451AE4B65EF31CFFCFE56BB (void);
extern void ScreenSpaceReticle_get___Usage_mD13EBAC0D80DD57A39F9C69795E16C0BDE395E86 (void);
extern void ScreenSpaceReticle_Awake_mAADE2EC200FEB8FE4BE5CD0A5E8D9D73F3EF4CA4 (void);
extern void ScreenSpaceReticle_FixedUpdate_m020F127C965BC75C76EBF6D955C4927AE0D6CBC7 (void);
extern void ScreenSpaceReticle__ctor_mE3807D255CBFF86E13D5F95569601ABD78B6E103 (void);
extern void SpeedText_get___Usage_m0FCC4DA56B5ED75E9614A12F6228BF452344B7A4 (void);
extern void SpeedText_Start_m270056ADCB7CE70CF5EF6BECC09E37751144FDB6 (void);
extern void SpeedText_Update_m0980EBB3DE949DADCC1FAE2D7E9439197FDB6C1F (void);
extern void SpeedText__ctor_m4B1A6F79A903BF978E1595DA2D02375DCACF3502 (void);
static Il2CppMethodPointer s_methodPointers[1290] = 
{
	RaycastHelpers_IsNonDynamicColliderInRange_mF4D4FB271950B5C808F9465F648D67EB9F68DD59,
	RaycastHelpers__cctor_mC84972D5A2D68C6AF31E6901045D9F6B5A59A3CD,
	DestroyTime_Start_mD6673F78C6E9BCD2E86C9C895330369499E0B5F8,
	DestroyTime_DestroyTimer_mAFEC73AAEA54C0CD54E7D7528683C1A2D4D09EA0,
	DestroyTime__ctor_m8CFE074330AB2ED362EA976620EAA1A8501D6492,
	U3CDestroyTimerU3Ed__2__ctor_m861B868BF5D562956024F53CCF13C48E8D90383C,
	U3CDestroyTimerU3Ed__2_System_IDisposable_Dispose_m4E2E05004A3460619986F30F8C872BCF83D51111,
	U3CDestroyTimerU3Ed__2_MoveNext_mCD6F3F56006731299869344A9B3D9A62C8FD6878,
	U3CDestroyTimerU3Ed__2_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m9C73DEFBA82E7427FBA91F41A358775B19897BD9,
	U3CDestroyTimerU3Ed__2_System_Collections_IEnumerator_Reset_m67C48AF5EA7006F7CADCF9F7D9DD9FD31BC7A799,
	U3CDestroyTimerU3Ed__2_System_Collections_IEnumerator_get_Current_m9337F51A2CDFB070F05EE62DDE2FB2C3545CAEB6,
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_m36F6C5E69A390BA21CE99727AC71905EABC52FEF,
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_mA967CAFAF2D0E0FF3C4EABA8BDF3691DCCCE3745,
	Ammo_UseEffector_get___Usage_m0E71B3E7C52247530A60B8B5E071431BECD3900E,
	Ammo_UseEffector_Initialize_m3370BEFE6F16CF03ACED1C58CB1FB0D9606D4163,
	Ammo_UseEffector_CheckCondition_m21B0C96DD75F408C29625E667C10EAAC69E13143,
	Ammo_UseEffector_OnUse_mA1AFEB49A31D3F7F044764B51F71C296E5723FF4,
	Ammo_UseEffector__ctor_mF3D31852DEC8EF5B599E9D8BE9EFD6DB10C2461E,
	Ammo_UseEffector__cctor_m21A157443B55665528367A0713195C4338142308,
	AnimationEvent_Sender_UseEffect_get___Usage_m4923F0F5085C6F62E3A872BCE31F05C95DED0BC8,
	AnimationEvent_Sender_UseEffect_Initialize_m2D5B5229C148345046C3769DCA92376F5349C885,
	AnimationEvent_Sender_UseEffect_OnUse_m87025FC602556E2F4AE44365162CFDC3BA0C65A3,
	AnimationEvent_Sender_UseEffect__ctor_mD17D5C12ACCBA829637FA4C4D205A3ED41D17B26,
	AnimatorIK_GunAimer_get___Usage_mBF28F99D1554ABB196DC082156E78E6CB052B3A9,
	AnimatorIK_GunAimer_get_animator_m16F2DCE8CAC561A686860E82B6A683B36AE76C38,
	AnimatorIK_GunAimer_Awake_m0D209C4EC661568F9532B62993DF4F37AC236018,
	AnimatorIK_GunAimer_OnEnable_m16BF95A6CEA420884269AF1A9D45A4ADF4AD02F9,
	AnimatorIK_GunAimer_OnDisable_m7A20F5B5761C9C917FF7FE4FB9866894C1A618DB,
	AnimatorIK_GunAimer_HandleBeginAim_mED490C9A6B63FC0F53CAF4A028EE7D990C694998,
	AnimatorIK_GunAimer_HandleEndAim_m644F0421D47C6571AE01E7F2161F7090B41489F4,
	AnimatorIK_GunAimer_HandleAnimatorIK_mA453CC18D93EA081C2FB338E4D3D387687109336,
	AnimatorIK_GunAimer_get_CurrentAimPosition_mCF809E9D7A10E9AD1450D9B566745DBB8888604D,
	AnimatorIK_GunAimer__ctor_mB6993777802B4EDDE4458F9F208EE69B8C3DF8BA,
	AnimatorIK_GunAimer_U3CAwakeU3Eb__11_0_m6DFD87ED5CCEEC6E2BA24D6A49D3F8AD89F0F5D4,
	AnimatorIK_GunAimer_U3CAwakeU3Eb__11_1_mA6EF435F045C9D6B5A55189B69893BBF92516A77,
	AnimatorIK_GunAimer_U3CAwakeU3Eb__11_2_m3655B9E2CD034643FEFDBB54E3E36894B6211EF4,
	AnimatorIK_GunAimer_U3CAwakeU3Eb__11_3_m335B0C1D7A563E6AA6394602ABA380A4ABADB09A,
	AnimatorIK_GunAimer_U3CAwakeU3Eb__11_4_m1362753DC741433EA2CDFDB105714621E11E473B,
	AnimatorIK_GunAimer_U3CAwakeU3Eb__11_5_m30C270A8C2BA831C298448AD0D7200DF86C799B1,
	AnimatorIK_GunAimer_U3CAwakeU3Eb__11_6_m273A7E16C21D04865DA679C06D3DC79EB971B613,
	CameraPriority_UseEffect_get___Usage_mF31043A6F21D0DE3B6530B89937E5D079920AF95,
	CameraPriority_UseEffect_OnUse_mF4CC9FDCF461C4EBB4D8CDD1AFDFFFACF2927082,
	CameraPriority_UseEffect__ctor_mAD8483CCD694659F6DCC84AA3AB9E9E9AC83B55A,
	CapabilityUser_Awake_mC7A30F96ED75EC302BC2E70046A3D86459490B77,
	CapabilityUser_Update_m022ED15456915BB3C41C02AC5F55FEC252529503,
	NULL,
	CapabilityUser__ctor_m4E2A60A6D0370D4A707958593B5E644EF999D681,
	Cooldown_UseEffector_get___Usage_m8150A8ABBDCB8167AD0C2ED7EA505DD9C71CC296,
	Cooldown_UseEffector_get_IsCooledDown_m6ED8BBA53B475FD00194E5A74427B66AB9EAF639,
	Cooldown_UseEffector_Initialize_m3975B80D15E15C7A18AB49A77A3E7D7E2E2CF08F,
	Cooldown_UseEffector_Update_m68744D21FD6D5D620D732D9534C88BFABE84DA4F,
	Cooldown_UseEffector_HandleCooldownComplete_m95F3D87EA820BCB8D425D038505CD34B9A2B53C9,
	Cooldown_UseEffector_CheckCondition_m16737B9D7243C1A36AE34FF75DF7F7C55B18BC1D,
	Cooldown_UseEffector_OnUse_m47C59C8B2EAA0F6E74760AAAE5B391DB178AF511,
	Cooldown_UseEffector_RegisterCooldownBeginCallback_m076AFF8ADB27454C384E016D7756009E53FE72E3,
	Cooldown_UseEffector_UnregisterCooldownBeginCallback_mBA7488114DF4DBED4E775AD6CD7F75CD15C91888,
	Cooldown_UseEffector_RegisterCooldownCompleteCallback_m40D63C7CE914FF459D5D61E9FD4834DEA321810E,
	Cooldown_UseEffector_UnregisterCooldownCompleteCallback_m23C8A603DAB2ED9651D0948DF89239499E3D3FCA,
	Cooldown_UseEffector_get_FractionalAmount_m267F6B67F4F8B6D69F995295336C6E24B454111A,
	Cooldown_UseEffector__ctor_m6186376B764B3C3166A6F5F7EA1DEDF248E7297A,
	Cooldown_UseEffector__cctor_m0323E878834D8410CE6270B12CB3DC1032CC791B,
	CopyInputFromUser_UseEffect_get___Usage_m4C035DF6D4A5FAE55207F90F9CE128F09EEE6580,
	CopyInputFromUser_UseEffect_Initialize_m422F4A315E1DDE11AA8FB4A99463B1BC52C2B1FC,
	CopyInputFromUser_UseEffect_OnUse_mE9F68D90A4AE142F72C2F4BED3696A479FFD7E15,
	CopyInputFromUser_UseEffect_Update_mBC7482457BA3C7B5D0E004E28434815CE4D3BC66,
	CopyInputFromUser_UseEffect__ctor_mDC0760127065FE70FBF2783CD2C9BE45144C6F72,
	CopyInputFromUser_UseEffect_U3CInitializeU3Eb__5_0_mD268C9DD3AFA565EFB08A937E8D49A3C4BF05A45,
	Energy_UseEffector_get___Usage_mDE582A15375DE362B6D37B3D10EA25B07AB66B90,
	Energy_UseEffector_Initialize_m9B8B9F52DFBDAD78511BB98A8576B21DE3F3E9B3,
	Energy_UseEffector_CheckCondition_m025F8818677D003EFCE6CD34F65520904B62BBDD,
	Energy_UseEffector_OnUse_m06DEDE0BE4B400C87DACE140184448738A76CC7D,
	Energy_UseEffector__ctor_m14980499D4D25D2A29B6A32D5F0E9AC03BEFA2D9,
	Energy_UseEffector__cctor_mEEE38D85414D614612FDE4018BC376491DFD400E,
	GunAimer_get_activeAimer_mDFAC1B385BD2A5C611D6DDBAC739B6874DC1AE5D,
	GunAimer_set_activeAimer_m75468BED3D548624300E94FD3F4CE3A7BC1A74BC,
	GunAimer_SetAimingFunction_mCCE3BF96EBF69764BDC4DCAAE29D3A037140972A,
	GunAimer_get_TargetAimPosition_mC68CE94CE469EB7B49F0F113DCBFD38AF5AAB320,
	NULL,
	GunAimer_HandleBeginAim_mF4A1DD6CA9B0FCA931676F17F94990203655FCFC,
	GunAimer_HandleEndAim_mEF4F6282DED97A085AE0891BE0AF61CA0D31CFEE,
	GunAimer_RegisterBeginAimingCallback_mDF1E11FF1298B315C41BB7C0AB3175A242D06F3B,
	GunAimer_UnregisterBeginAimingCallback_mBA296FF7B7D62EEE0FBFD1FFB02EC0CCE693C1F4,
	GunAimer_RegisterEndAimingCallback_mD7246A365F22369CB0E581A3F7478036C8E34F53,
	GunAimer_UnregisterEndAimingCallback_m55FD1974290D2E2E1D7D45886D6DF4CD82592E6A,
	GunAimer__ctor_mAE14EBDC1B705EE593A553DBC8924FE7CCC73877,
	InheritAiming_get___Usage_m5F301393B27E873218681BDABCF79377394960DD,
	InheritAiming_Awake_m6D13156D2F08B207AAFB384D930685C91E0D7374,
	InheritAiming_HandleRiderExit_m2A722B9A6AEE7C5C372563398C87F1787A8B06AD,
	InheritAiming_HandleRiderEnter_mFE9938A9A4326522FA6F168B2E5DFF81D546153E,
	InheritAiming_SetAimFromAvailable_mB5360F78B8B13A541C49B7E320FC8E76FDD04942,
	InheritAiming__ctor_mE967F913284532DB099899231A208945C3153A58,
	U3CU3Ec__cctor_m17B6E726F8DD18323EA3C206A5210BCD0B818376,
	U3CU3Ec__ctor_m467F308CEEB24BDB29873D332FF46D22A5598D80,
	U3CU3Ec_U3CSetAimFromAvailableU3Eb__7_0_m4847EDA415D711E9A8B907B6E0EB33411E3FF33D,
	U3CU3Ec_U3CSetAimFromAvailableU3Eb__7_1_mA0F3BEB02DFCA8D154D952AD67A6A6C593771B14,
	InputCollector_get___Usage_m578C4159A51F00730DCB59AE24CF75BF066244AC,
	InputCollector_get_Buttons_m9C1470CE250A3742CEE7F6685572EE622AC80355,
	InputCollector_get_Axes_m8EA78032D0FDEBA5666C9622A1994C1BA177673B,
	InputCollector_add_OnUpdate_mD444761947F848F065033F9BFDAFD7147CC94376,
	InputCollector_remove_OnUpdate_m68B0EA704076FE433C5EB1F189BCBF1E7CCA78DE,
	InputCollector_Update_mBC7E1856AC64839F70D4CA037271C81124BB571C,
	InputCollector_CopyFrom_mE1E7B9CE94C206170E999A4C5A75355D53553033,
	InputCollector_ResetToDefaults_m3E67B40BB7FA7582644AD4BBB8D047705DE978F4,
	InputCollector_get_Item_m839B9B9F3C41C0C411607B7BA50FE965E317B3B8,
	InputCollector_get_Item_m61BEB9E908C93ECA8C6951718094A2636AFE0A91,
	InputCollector__ctor_m2995677A1B7997260903E6836EEFEE740978A1A3,
	Knockback_get___Usage_m73C9C568EC565618B89E567573670726084968EA,
	Knockback_Awake_m6673FC577196B10CC65E9D1A8CA633060E22DFFF,
	Knockback_OnEnable_m4D0D886823D00F3078AE4CEAC8EED37F329D935F,
	Knockback_OnDisable_mAA3F78AEFB5DCF0D543215E1B5DA5FB8F2FDCCDF,
	Knockback_HandleDamageReceived_m8C1F930D69F8DDA3792AD2EE26190A4136FF9B50,
	Knockback__ctor_mAC6C3761E25A2EC9C0B2F4D31B69EBFE0ACFC367,
	Knockback_UseEffector_get___Usage_m02789F75376761059E53F6E395F30D970179AB3E,
	Knockback_UseEffector_get_usedTransform_mEF735E09FC44C5A30CFEE6BD9C22315F2A7CAC7B,
	Knockback_UseEffector_Initialize_m5E55AA144DF7F76A3E2512A28F3D1DAE17AF2C6A,
	Knockback_UseEffector_OnUse_mEE4C1ADEE7B946BC873473D5DB1DB3AD1399CAF4,
	Knockback_UseEffector__ctor_m151C980D86C0A1FC870F182762B6E86D035C8901,
	MouseAndCamera_AimEffector_get___Usage_m37FD2C28D550881AE8EE05552ECEBAF9DB7E96C4,
	MouseAndCamera_AimEffector_Awake_m28A93E22FC588E66C785A9764B2F269FDF7EA853,
	MouseAndCamera_AimEffector_OnEnable_m7BF664A88FE05CE1F3286386D7B4DED870915D0D,
	MouseAndCamera_AimEffector_GetAimPosition_mE19E83FAEB22901EB8AB68D3C945F8FF1BDD503C,
	MouseAndCamera_AimEffector__ctor_m7077215BBD3932976274C680B4B1D7091CF512DB,
	MultiSpawner_UseEffect_get___Usage_mB8AB916489C9E9F847AE247EDD04F67AFF425FDB,
	MultiSpawner_UseEffect_Initialize_mFED81DD5BFAC64C4C6B133A2862226B4F200BB22,
	MultiSpawner_UseEffect_IncrementIndex_m1D6AE26258AF4C15217144B6C2390C3F023E01A0,
	MultiSpawner_UseEffect_OnUse_m9D9757BB62AD2DEE2294ED534A82972889FE1645,
	MultiSpawner_UseEffect_RegisterSpawnCallback_mC064EE5BE2C839DB3246DEBC506FA63A3B57A397,
	MultiSpawner_UseEffect_UnregisterSpawnCallback_m284AFE3C731B9E550692A9E45A5BA3EE5FAD19E2,
	MultiSpawner_UseEffect__ctor_m69321DF844FC06BCAD73113E0371A31A9B1596FC,
	PlayerInputs_get___Usage_m94AF7EC567AC90D8FBB986ADF74540CDD365879D,
	PlayerInputs_Awake_mF0DD77C3474AA358F625DDF004DFE00FDE4D5E15,
	PlayerInputs_Update_mD0626787EAF4BBF03A375F45721DD3EF20E175F8,
	PlayerInputs__ctor_m1F4D681AA3CED945BFFFDF3D5A19A1761F44B4FA,
	RandomUsableGiver_get___Usage_m292A989FEF69382608B654425FD27BCD953EAFD6,
	RandomUsableGiver_Start_mCC9A170D5424C737C88204B60D763BE00532803F,
	RandomUsableGiver__ctor_mE25521B4D832994C5A07C651FE8DCFA464FDAB42,
	ReloadUser_get___Usage_m9DB1257956592A99CA50844E6D787DD5C13D1DC8,
	ReloadUser_CheckCondition_m6181756FE82661E5EC42EED0578B6BBB75A200B9,
	ReloadUser__ctor_m67F5422E91F6D05962C1183C470A2DAD44E88BE1,
	Reload_AnimationEvent_Sender_get___Usage_m8C941E599D6D99C3E5614FCB81AA20A0EA433769,
	Reload_AnimationEvent_Sender_Awake_m02FC0A1F04B612562339DABDE1499CC550623D9A,
	Reload_AnimationEvent_Sender_HandleReloadCancel_m165B6F6B84BD7D71408979A4AE491F11D43C2160,
	Reload_AnimationEvent_Sender_HandleReloadBegin_m1D10DE51232B845C96EDA562B2D4634DDE97ADE8,
	Reload_AnimationEvent_Sender_HandleReloadComplete_mFBFB74AF6981BAF90E1E0CA3F83185EB97217BD0,
	Reload_AnimationEvent_Sender__ctor_m1570B0AAAC812DC5B50FF81FD6366E1EB30B17DB,
	Reload_UseEffector_get___Usage_m872AA113569BD49E134A62023FFD7C432ACB3E09,
	Reload_UseEffector_Initialize_m81483EEBBC66E440FEF84BCF75D7B7EB7B679085,
	Reload_UseEffector_CheckCondition_m8E924600E383A65D1745502E9245571E7383C749,
	Reload_UseEffector_OnEnable_mE7383AE29B6FAA75F4CD52176ED74D556C62E791,
	Reload_UseEffector_OnDisable_m8E031B0AC32E7CF3D47430C9DBFACD50DF244285,
	Reload_UseEffector_OnUse_m612A6AA0FD90F4A2F33CFFF89A90BC72EB223E0C,
	Reload_UseEffector_reloadRoutine_m7805C5F162F96D5682BE1C0D28044823EEB1F2C7,
	Reload_UseEffector_RegisterBeginReloadCallback_mE43064C3DD45EC2338E95A7933936AE8CF493CB9,
	Reload_UseEffector_UnregisterBeginReloadCallback_m9C498ECEF7F33FD94812E9BE24B824D37FA4361E,
	Reload_UseEffector_RegisterEndReloadCallback_m5E18C6420FCE9BD3D0F0CA335114E82B935B5287,
	Reload_UseEffector_UnregisterEndReloadCallback_m8841FBAA2AE5F83F377A4C3A495A5F7F3C3B39C3,
	Reload_UseEffector_RegisterCancelReloadCallback_m473FD0FC6ACDAE9000DA5C53C136E8836D1600A4,
	Reload_UseEffector_UnregisterCancelReloadCallback_mC062EEC137A18BA9D2F868A2CAC775FD3B74180F,
	Reload_UseEffector__ctor_m2F622561A1BFCA3D1C6520E69A9F406909FFE69B,
	Reload_UseEffector__cctor_mD2E76717A191C73DA600C9B44AE9396CAB6D43E8,
	U3CreloadRoutineU3Ed__17__ctor_m27C80C37CF20CD988BBEA45F3FF7805B63D53014,
	U3CreloadRoutineU3Ed__17_System_IDisposable_Dispose_m5E20691E511A64D46312C3CC9B60D55A3F329528,
	U3CreloadRoutineU3Ed__17_MoveNext_mF3057D56CC6ED471BCC60C364BAB71632E9AC966,
	U3CreloadRoutineU3Ed__17_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mC2B6A11D5BF086E017DB43B0928E20CAE3CDA3A6,
	U3CreloadRoutineU3Ed__17_System_Collections_IEnumerator_Reset_mBA80846F7853DD89E49325DC0CA61AC9572E14C5,
	U3CreloadRoutineU3Ed__17_System_Collections_IEnumerator_get_Current_mFC0F7196D8607239FEF22E82F7B63FE49BB911A4,
	RespawnAtStart_get___Usage_mA7697681A4316C9195509065ACC287BA93BD12C7,
	RespawnAtStart_Start_m83CC51E6735B73700F512F48C1007BFEDDDE1C00,
	RespawnAtStart_HandleRespawn_mFB1FD04365C4084B52931A50A909A6F8E26D8A96,
	RespawnAtStart__ctor_mF4E428378A35FDA69433673C67CB36228761E19C,
	Respawner_get___Usage_m08869B8D073E4AC6BEED5ACE34C9E4E67F65950B,
	Respawner_RegisterRespawnCallback_m46EFBA05DCB309608EF1CB0DD875DC53FA7A476E,
	Respawner_UnregisterRespawnCallback_m3FE0BB718A772DE21EDB41523E7EF3668A44D51B,
	Respawner_Respawn_m76DA094374FAC7039C59E37278441B8D0EF161B0,
	Respawner__ctor_m1129F16A445FC90DA0A6BDB186246E2B82885922,
	RideEnter_UseEffector_get___Usage_mCFD8FAFF68A35AEA0AABEEC296C66B6EB5098BC6,
	RideEnter_UseEffector_Initialize_m100D1D77E2C3942B715541DA7352CC289566384A,
	RideEnter_UseEffector_CheckCondition_mC88AC280C2748E9F73FAB2C0B4CA4A3C4FE4CDC5,
	RideEnter_UseEffector_OnUse_mBF5B3A7C660EB25FD5D6F4FAE38AAFEE5D31A661,
	RideEnter_UseEffector__ctor_m34377E71212269926C47F84230DC67B55AB7BA44,
	RideEnter_UseEffector__cctor_mEB2890E2E4013BE0C8D6FCA0E89B12AD0E72E6F2,
	RideExit_UseEffector_get___Usage_m55AF21037FDF63B2624339866B12079E207245EE,
	RideExit_UseEffector_Initialize_m325AA3692BE96D90D26B4E5F6892E3FE0DBA21E4,
	RideExit_UseEffector_CheckCondition_mD5516F19B0FB532484C33B78588C3F7402671839,
	RideExit_UseEffector_OnUse_m4058117ACC60A88E014738842C6EF147D8AF5452,
	RideExit_UseEffector__ctor_mD9999D8118956E8A4021B8C424610D156012C6B3,
	RideExit_UseEffector__cctor_mB667D73DFAB5EBC4D3E0B1D91930D1AC9C05AFE7,
	ShootUser_get___Usage_m8DFB0C1EB93E6C5C3990AB099B66924F206DC152,
	ShootUser_CheckCondition_m0BC551372080761157C5EC8237C20D41470E0E83,
	ShootUser__ctor_mBF52D26ABA981D11B749D4E071309F007F97CDA6,
	SoldierMovementFromInputs_get___Usage_mE08C2FE16D7E3B6C888ECE54EC052E1F73A789F4,
	SoldierMovementFromInputs_get_isGrounded_m3F4CACFA8658BB7BE453FB02858318A781AE093E,
	SoldierMovementFromInputs_get_IsRunning_mCB0D4E9E800F76BAE8BF261F096022BDE55B517F,
	SoldierMovementFromInputs_get_Velocity_mCC9DCBA007A6C5E3B747C2B23C7BF2BB3538BC13,
	SoldierMovementFromInputs_set_Velocity_mA42126E739F505225428F829CE8594AA039F3E18,
	SoldierMovementFromInputs_get_LocalVelocity_mF625D55222600BBB494E521E2C94FBFC862D2288,
	SoldierMovementFromInputs_Awake_m55EBDC94786F5E124C665A204715F989BB7AF035,
	SoldierMovementFromInputs_HandleOptionalComponents_m2A377C2533B0DDDC5A7A6EA379E6688810E3F4E5,
	SoldierMovementFromInputs_Update_mDF9E92E96D422060A0EEE67A4DD3DA139E532402,
	SoldierMovementFromInputs_OnDisable_mC1450432A8F95696A965713D89A8F310DAA28F3B,
	SoldierMovementFromInputs_RegisterJumpCallback_mE439CED5EABE5BB1951E562311D94A529A81701E,
	SoldierMovementFromInputs_UnregisterJumpCallback_m9789312F1D6CF12FCE71B16552E808C56618BC71,
	SoldierMovementFromInputs__ctor_m95FF8809975BCCEB48483E3D9B539BC46D2FFC18,
	SoldierMovementFromInputs_U3CHandleOptionalComponentsU3Eb__24_0_mFD2260E74DA836937F3626026C4240AE218E619A,
	SoldierMovementFromInputs_U3CHandleOptionalComponentsU3Eb__24_1_mC0EF5F13D0FDF4F63E768B474858A1FC911C8FBD,
	SoldierMovementFromInputs_U3CHandleOptionalComponentsU3Eb__24_2_mDB2D50A18BB8FBCE1799EF5E14807C7AD1FDE62B,
	SoldierMovementFromInputs_U3CHandleOptionalComponentsU3Eb__24_3_mEF26E4748AB7D722EBA2453DD5F2E4B4175B1335,
	SoldierMovementFromInputs_U3CHandleOptionalComponentsU3Eb__24_4_mA3E2A9028E7B878049475693E4DDEBBE9A9F80DC,
	SoldierMovementFromInputs_U3CHandleOptionalComponentsU3Eb__24_5_mEEF4F5B86B8535E88D77A4357AF62D870729386E,
	Soldier_UsableHolder_get___Usage_mDD0D59BEFE028C5256A699630F8E5262F25ECB18,
	Soldier_UsableHolder_Reset_m47E58A897A370F64151E03FA5DB12E050D9C8A90,
	Soldier_UsableHolder_Start_m816C424DE553FBA38D4A85FB5A98AD121D39D54E,
	Soldier_UsableHolder_HandleChange_m56E49481CD51A1D592246BC070F3B5E811B7D150,
	Soldier_UsableHolder_CreateUsable_m186C514598E329BEAA1A17FF10D0AA5AA111FFBE,
	Soldier_UsableHolder__ctor_m320171F3AF800481E941264C132F66ED80B91317,
	U3CU3Ec__cctor_mD6890FBCC928E2823B9191FB27C347AD13853F52,
	U3CU3Ec__ctor_m32624B04F2ECD5E2BEECAF33AD3AE068BA042AE2,
	U3CU3Ec_U3CResetU3Eb__3_0_mC60E68C4181A1C8CE12B9390BC9550E49AED4C46,
	Spawner_UseEffect_get___Usage_m2F2ABB0AD0EC1F918397C0CA05FA04E96F4B2F2C,
	Spawner_UseEffect_get_SpawnAtTransform_mACFC3F0F168F41E72DE9A05D5D7AC1B22449AC98,
	Spawner_UseEffect_Initialize_mAD21DB73B88B2CA79F47EE2EFD770630A315AE60,
	Spawner_UseEffect_OnUse_mCBF22D32E708556946FC687D1C0A3FE3D239DBA4,
	Spawner_UseEffect_RegisterSpawnCallback_m77121397302634D504D18AB86FFB71B35BDCF680,
	Spawner_UseEffect_UnregisterSpawnCallback_mD7551ACDFCC2F37423EDBD6F37EB0C59B1DAAC52,
	Spawner_UseEffect__ctor_m83941919BCAE20C2F3E93646ED887C5288B47267,
	StandardRider_get___Usage_m779DD3AFF670DB6CBFACF896EE55B322298CF47F,
	StandardRider_Awake_mE8A7302987CCBC6663D8123BA449B61643D68A51,
	StandardRider_HandleEnterRideable_m0CAC899F6F6DA4F503261973805A6A1CA1691141,
	StandardRider_HandleExitRideable_mCE22501D7D318D4265BBD55612C59A08EC1C6E8F,
	StandardRider__ctor_mF383031339B38BB78EF3EEC88B63497FAD6074D9,
	U3CU3Ec__cctor_m91613CD06184F30DA4AE482EC7049046F0009CE3,
	U3CU3Ec__ctor_m42B4C7FE67C523310B5305948BD464806BC32C6D,
	U3CU3Ec_U3CHandleExitRideableU3Eb__7_0_m276A6E93837FEE817ED6D6CA4D23B168512B3B88,
	TiltTowardsPosition_Input_get___Usage_m2829472A55D0BEAECA121E2A5572116F85D429A9,
	TiltTowardsPosition_Input_get_TargetPosition_m0EF95B68D2DBEF9A2946B2F2AF997811888E7AFD,
	TiltTowardsPosition_Input_set_TargetPosition_m579F9079506B35AF2B837725840053092D5FBBA5,
	TiltTowardsPosition_Input_get_IsSeeking_m80FC06BD506BC1FB0393980F9B3DEC63C2FCDEAE,
	TiltTowardsPosition_Input_set_IsSeeking_mE8304B31B31B14924DB2E7015BEA641D4596AB23,
	TiltTowardsPosition_Input_Awake_m00232CAB502C0C02353AB2B479F02DFF71F79277,
	TiltTowardsPosition_Input_Start_m8D385642A3C5BCC83AC82AB3B064862F36746242,
	TiltTowardsPosition_Input_Update_m82383ED3117CD03EB7B9CDB35A5C18C05B6E9DD4,
	TiltTowardsPosition_Input_Arrived_mCF139DD6DB00916BF7C05852968A77E7AFFFC466,
	TiltTowardsPosition_Input_GetFlattenedDisplacement_m43341EDA5D817FE4149C64F7A4B3C06E450BFAD1,
	TiltTowardsPosition_Input_get_isUsingPathing_m4ADDCF6E2F533FB473E5A141E22177B85B3A2352,
	TiltTowardsPosition_Input_SetTarget_m40611F45514234BB006A5CA71D56742BB5E9E7DA,
	TiltTowardsPosition_Input_DoPath_m9C30DF0F496D56FB8019B41DFAE4B7E9919BA843,
	TiltTowardsPosition_Input_RegisterReachedDestinationCallback_m5B040F31B2DC75C66719644369686CD3F83624D3,
	TiltTowardsPosition_Input__ctor_mA4346D788DFBAB92E5F4232248850828FA4B518C,
	U3CU3Ec__cctor_mFBBB90FEB0BAD1D5536D287E517ABD8B4787D4FC,
	U3CU3Ec__ctor_mDA2F15F9AE0A7A94FE16D9F4A7B278369F0049F7,
	U3CU3Ec_U3CArrivedU3Eb__18_0_m20A525EBBE10C08441AE05D7D407902421E8B249,
	U3CU3Ec_U3CSetTargetU3Eb__24_0_m809E1C77A9A444CD3D22602B4C837D10560E289D,
	U3CU3Ec_U3C_ctorU3Eb__27_0_m9D3D83B879CE852117817DDAA8664585587D22DA,
	Turret_GunAimer_get___Usage_mB84BEC76DB6A10F9A1DAC53C34A10EAB4FC9AAE9,
	Turret_GunAimer_OnValidate_m8B08A93FDD22D64885D61988D5C4DBFBE006DA21,
	Turret_GunAimer_Awake_m9CF28076CFF58E107BDF5AF59904D08C6D2320BC,
	Turret_GunAimer_OnEnable_mE457419D1370C6DDB4DEED5AD2AF65414203B428,
	Turret_GunAimer_OnDisable_m5CA69B3B94E5BEF830476D86F622113045A9B32D,
	Turret_GunAimer_Update_m6020D5C4E2709AC07A8ACF2EECBD28C0C84C892D,
	Turret_GunAimer_get_CurrentAimPosition_mFD8D41512DDBC16B4A91439801EFF585B91E8E6C,
	Turret_GunAimer__ctor_mDC1679809DA53AE8E468B2040DD32D1459E6F83B,
	Usable_get___Usage_m056C2F1DF0B953561B983E3A72FB3CD1420FA7A5,
	Usable_OnValidate_mFBCFE1B708BF631C43A05308A802EEB8B6725B67,
	Usable_Awake_m7EEF5625EF7D2B677845493782AA39A800904D0F,
	Usable_TryInitialize_m6B183850A5CB739232B40682EF101E7DA1AA1CBD,
	Usable_RegisterCondition_mDD6C4C2B8B8CA038C3EFBCEA3F4AD9C03A4EDCA3,
	Usable_UnregisterCondition_mA6648F651DFD408849133AFF98CFFB35DB564A84,
	Usable_RegisterEffect_m22384AA245F5B8A6AC4449138DA7A7E2F73D3FC4,
	Usable_UnregisterEffect_mFF5F4E218388DF751CE407C0998D1CC5C25438D6,
	Usable_TryUse_mA87C60AA2C787E8F6D0033279FBC403A674C8C75,
	Usable_CanUse_mA5CC666397CD8B04B09B8E3BBB20EAB00D863A78,
	Usable_CheckConditions_mB60F51823368EBC2307EAE835FA6DDBB67BBEF60,
	Usable_HasCapability_m06FBE9723C009ED1F592395B334F798B0CE4C0A7,
	Usable_CheckCondition_m7DD065F050D8366D3E8630542F61ACE568296CEC,
	Usable_RegisterUseCallback_mAA315DBC6CE7E54FD5D9CCF791F743D585DE86A8,
	Usable_UnregisterUseCallback_m0FCB909354E27CB35407ED6C3D7440DE6D1595E9,
	Usable__ctor_mEB934BD4ACD59FE4FB2EB887874375C14BEB64C8,
	Usable__cctor_m4BD411EC8D31CF54E23AF50C6B077F564ACE6E39,
	U3CU3Ec__cctor_m793ADBEEAED255CEF3078201EFA8399B16F5ED32,
	U3CU3Ec__ctor_m329C31F89AF08F1346320099DE5096EC476B58E8,
	U3CU3Ec_U3COnValidateU3Eb__11_0_m9B49A7E8A9EED6613BE44024ABF86340D0F2BB4F,
	U3CU3Ec_U3COnValidateU3Eb__11_1_m2BE6793347EC584A31919911F3C1D8D1AB5721BD,
	U3CU3Ec__DisplayClass22_0__ctor_m5DC6BEBABBC5BC6000BB1CCD1C38DEA405E5FE28,
	U3CU3Ec__DisplayClass22_0_U3CCheckConditionsU3Eb__0_mF1AB5966B6FB11E9CFA51EC7FE8778C0A55B02C8,
	Vehicle_UsableHolder_get___Usage_m6EE59F00C1A3503AF0CC61F5CC37252B91D1B196,
	Vehicle_UsableHolder_Start_m6F1FC0C3B4450CE55E94EC1A91E9B0601F137DC2,
	Vehicle_UsableHolder_Use_mBCCCE718A9DD7A0F3C942FE0076EB0A2F9760E7C,
	Vehicle_UsableHolder_GetUser_m969A76F4C41CCB39F4D2336D025D34866EB0D708,
	Vehicle_UsableHolder_RegisterUseCallback_mEFFAFA86A2FE6F52E079F463B62F3371529F2B1F,
	Vehicle_UsableHolder_UnregisterUseCallback_mB5886EAE453390B9FF3F884BBC98411634178A94,
	Vehicle_UsableHolder_RegisterUseFailureCallback_mE3BB28D4C78202378D2641D0609B8E853E0B668C,
	Vehicle_UsableHolder_UnregisterUseFailureCallback_mE1C8EFBC3320DA605EA09D549A35338890241690,
	Vehicle_UsableHolder_CheckConditions_m15E053942655A9E7213A53D5189A83D64876F2A4,
	Vehicle_UsableHolder__ctor_mEFCB878F38B43CFA9FEBF1BB381B11F0CB7B5059,
	Vehicle_UsableHolder_U3CStartU3Eb__6_0_m8263A8998402632EA4ABBE383F6C0CDF8DD47B76,
	U3CU3Ec__DisplayClass13_0__ctor_m1DA92F9A89EB35F0D3DA0EA1E1D0F73DB8DFDE3E,
	U3CU3Ec__DisplayClass13_0_U3CCheckConditionsU3Eb__0_m3234F7111F47A86BED3BFDDA10F978B69E6CE8B0,
	NULL,
	BaseEvent__ctor_m3EC56ABEA6769A0F31612C856FCD37FB90798369,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	DynamicUsableHolder_get_Usable_m47C85E90C4F28456D6B1DF5BAC3274CF87A915D7,
	DynamicUsableHolder_set_Usable_mAC8C5ACB1A9D5D451C0860C9B2C27EC59CFFEBCC,
	DynamicUsableHolder_Awake_m2A34913D565DA062147A018C46D9B45A7C88C04F,
	DynamicUsableHolder_ActivateAndDeactivate_mC5391A134ABEB4B6F82EBED66CF20B1D1942D290,
	DynamicUsableHolder_AimAt_m6A70492BA050AE3E2F8C0AC95ED05C23A9269FEB,
	DynamicUsableHolder_SetUsableLocalRotation_m88332182B6307721A81216ADE40E5524AF728F43,
	DynamicUsableHolder_ClearUsableRotation_m11DDAEF03B6C550B76951638B17A84385A154EE4,
	DynamicUsableHolder_Use_mAF454A55371683E5448CED4CE715AA9E5B594EEE,
	NULL,
	DynamicUsableHolder_RegisterUseCallback_m9287B4197108C85125F4B48B566A7246D7112A6F,
	DynamicUsableHolder_UnregisterUseCallback_m58CADCF619F1D8425B400AF7260F9749BEF1E2EA,
	DynamicUsableHolder_RegisterUseFailureCallback_m6F4C633DE661B39255B26AD31887A94D8139D99C,
	DynamicUsableHolder_UnregisterUseFailureCallback_mAD6905181B69EADA942408EFA2F41FDFEE7F35E4,
	DynamicUsableHolder_CheckConditions_mA45DBDE0070902BC2EDCEF58535EDB9CDAC0B307,
	DynamicUsableHolder__ctor_mC0F8F8C1EB74DE71D4D4C9363B867242456A7936,
	FloatReservoir_ConsumeIfPossible_mD3576C19F0B1AEA1D36D168418BDABDB5BACABF3,
	FloatReservoir_get_FractionalAmount_mB860E09BB968719F0F5A4A3BFC9846C66423A717,
	FloatReservoir_doExtractPossible_m5A646233A5CEAA74B9CBA523867D2060811E634F,
	FloatReservoir_CanFullyExtract_m13AAC1DECC2C444139F1288D110DA53139939829,
	FloatReservoir_doInsertPossible_m36B8E603C424BBBA6DD3B3A4B1848010762879FB,
	FloatReservoir_CanFullyInsert_m149086B02E388E81A5CD721FC3000166489F8F67,
	FloatReservoir__ctor_m55EA71AA381040252AD62FC94600850921B04C21,
	NULL,
	FuzzyComponent_Awake_mFE5EABD0DE6B1DF5BB2046E7C9DE7C8FFAA7DEDE,
	FuzzyComponent_GetWeight_m12FE33AD06D47CDA31C148FD047284C4D92D3820,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	FuzzyComponent_get_IsActive_mDD86493D7A6A92ED3C4BF5BB813343E0391EEBBE,
	FuzzyComponent_set_IsActive_mF6F9A76981291DC62C1F73A5016C82D5D0FBA7E7,
	FuzzyComponent_OnEnable_m5B154B22E526EB4C35D366C3DC95AB6FAF46A124,
	FuzzyComponent_OnDisable_m0139D078D363966BDE320AA223D3317F6B918196,
	FuzzyComponent_OnEnabled_m43BB8CA0845AFB9D0EA891D8019656760F4BCAFD,
	FuzzyComponent_OnDisabled_m939F06960AB3804E4F96B5F039402AD9EB8A1303,
	FuzzyComponent_OnActivate_m5811B009ACED7257537C913AC739D099A95EDB3C,
	FuzzyComponent_OnDeactivate_m97D2AC6AD8E7301EC6139F062BC898886ED78629,
	FuzzyComponent_TryActivate_m18CD08B851EC81DCE5511E2D0870EAAF88437C27,
	FuzzyComponent_TryDeactivate_mE7008B26BB2E998FB74E16C526D9364D324BC350,
	FuzzyComponent__ctor_m25C9A9330C4FF65B9F23895834F6486EDE8D1E61,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	FuzzyStateExtensions_SetActiveState_mAE696AF75A1BE708323FE6800E9DA2E880AE8E66,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	Pickup_Awake_mFC6C330AAA9C7B64D13F662EC7C1B8AF7AA6A99E,
	Pickup_GetPickUp_m06797A303180BE5CE06D5BED3978DF5B41B781B0,
	Pickup__ctor_mD1B7E738B0B399C5363B3A33B1C1BF4A61156EC7,
	Pickup_U3CAwakeU3Eb__2_0_m25A64B68CFD20AD9E1F47DE4354BC8A136BB5304,
	NULL,
	NULL,
	NULL,
	NULL,
	PickupCollector__ctor_m1A03DDCD14254817A3CA050D55D2688F40B8A563,
	NULL,
	NULL,
	NULL,
	Reservoir__ctor_mCE3392BF96108B6D2E355348F56E72BEC511089A,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	RiderBase_get_checkTransform_m7BED960029869BCA7844EB3992E77D661BA491A0,
	RiderBase_get_isRiding_mBF43DCFE3678200BBD2A175C508B0A27F390227D,
	RiderBase_Awake_m6134CF96D2D86A9B60F8B77D776C530CCB88852A,
	RiderBase_OnEnable_mFEB736D4F59C95033B4FE7DC80C24767C0F35055,
	RiderBase_OnDisable_m1D77D78767C4FF10C2C55C7A5BA65866C212D743,
	RiderBase_HandleInteract_mAB00F2721F03BC1C9D710A5B2FF39A583D63EC0B,
	RiderBase_Update_m84E4537A13649F91C7105CD0F77D57F6CAD89845,
	RiderBase_DoCheck_mF9DB02B7F37538073AE8AF0EBCD687623BFCA6DA,
	RiderBase_EnterRide_m3EA4A16EC1E1DEC4B0B37F72CBF64FD53802E35B,
	RiderBase_ExitRide_mC25770FC2420D8F88545B43749BECA018D85045B,
	NULL,
	NULL,
	RiderBase_RegisterEnterCallback_m295454252E8BB89B13A143190F430720473B349E,
	RiderBase_RegisterExitCallback_m1E2C4E0691D9AA3E93858EAC843395985CC1302C,
	RiderBase_UnregisterEnterCallback_m188CB529351DE12242E2E2CAB1FBC91F3D2F7EC0,
	RiderBase_UnregisterExitCallback_m2800AABB0FD4843373C8177D4469E7752448FE6C,
	RiderBase__ctor_mDDAF0FD00FB6B1D22BB3B5501AED4D3DC86628BF,
	RiderBase__cctor_m2C2CEBA85895F166DE2BB986667676A309E16A33,
	U3CU3Ec__cctor_m2D59570100343F0B531695B247B2CF00D1F1AD99,
	U3CU3Ec__ctor_mF333CC3EF20EB31BB8B8945C94D165530505B032,
	U3CU3Ec_U3CExitRideU3Eb__26_0_mFC36F9ECA1161DE54B938FB967921FBE298848E9,
	U3CU3Ec_U3CExitRideU3Eb__26_1_mACB653E3E7488BF60C771B5945D1E7D49DE17C79,
	NULL,
	NULL,
	NULL,
	NULL,
	Tracker__ctor_m66EE6911664A8E53A804343CD14BCBAC26469416,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	UseCondition_Awake_m361BF2A79FF130BD450CB91153CA64B659AD2B4E,
	UseCondition_Initialize_m4E0150D0E964B452E256736907B789F7D6B411DC,
	UseCondition_OnEnable_mC048BE7EAC44324EE4619CD0320726467FA0A51A,
	UseCondition_OnDisable_m6532BC3FB08416FA3389CB51F42757478D5033BA,
	NULL,
	UseCondition__ctor_m500C5852831DC3470DAD39457F900F2D477A06FD,
	NULL,
	UseEffect_Awake_m38015C355A1DC275BE401EBB51AA3467FC78CFBC,
	UseEffect_Initialize_mC3316F3F9F607EF19A89D1294C5457EB29A67B46,
	UseEffect_OnEnable_mC69C5FD4D6B2DDA49CBA609778B7CBCB154734FC,
	UseEffect_OnDisable_m1621A4CEA1BFB69FE7DE1F7E9ED3FC7E09D293ED,
	UseEffect__ctor_m1F17804A40880735A32EFF7D3361975FEC279E54,
	UseEffector_Awake_mA7888F82C703E7E736C6E0464535D7383B57940B,
	NULL,
	UseEffector_Initialize_mFAFD36F53F7187FA1F54752CC12452F37FE82B89,
	NULL,
	UseEffector_OnEnable_m98E759907C0DC10E189B1166946F57BB9B89B2E3,
	UseEffector_OnDisable_m3166E2467C93837DCAFB4D28A33389D8E4A2B9BB,
	UseEffector__ctor_m3B5CC4992612B4E6D9A8A21713D6394CA81A2DD5,
	AggressionTarget_get___Usage_mB0581D2CD984CC0AB3C04E7FDF9C0056C2CD7CC9,
	AggressionTarget_get_Target_mABD905B8323DD0F53897EAE951FAE33514498248,
	AggressionTarget_set_Target_mE4DF5309270CA57759F3F316556E32C1F1CD80FE,
	AggressionTarget_Awake_mBC50D7BEF0370936CE11B5FD6C18B0FABEEC485F,
	AggressionTarget_Update_m9E8805EDFC5A85D5533AF5D680C6BF6C034BDB06,
	AggressionTarget_get_CanShoot_m8D5D2B2B1CE7EAE6969073D80DFEF3900EA8D46E,
	AggressionTarget_set_CanShoot_mB196E28EB3EBDEB3728143A7F5488DC2B3063027,
	AggressionTarget_get_CanShootAndVisible_m4D51B92F6913DF441E741DFB98FFDF31AE09EB59,
	AggressionTarget_set_CanShootAndVisible_m7058CD78D1CDE1950197ABCA7348643723FAA3B8,
	AggressionTarget__ctor_mC308D7616C427EC7AA30F8D0D96949CCF0CE1CDD,
	U3CU3Ec__cctor_m21CBB73125A8C5037AEC18F34185E2C1A2FBE146,
	U3CU3Ec__ctor_m9423320E2FC416756C57CB2812872AC6F0FDF375,
	U3CU3Ec_U3CUpdateU3Eb__9_0_m71BDD9158AA7F948E77BEA5EF9F3B0489CC28EAB,
	AmmoSeek_AI_get_FuzzyLayer_m0705D2E3A9536ECAEB0C4E39D74B19D24535C1ED,
	AmmoSeek_AI_get___Usage_m703C702400071103403111044485A700ADE35949,
	AmmoSeek_AI__ctor_m2042B5C20AD14D1F47F7307ADBD230A1E857DCE2,
	FactionReference_IsHostileTo_mC056D21BEC48887ECDC457535E53D62438BDBF26,
	FactionReference_IsAlliedWith_mB56CDAE7CD079B4F17F80918EAA2D6403C94B35C,
	FactionReference__ctor_mD0111AF6BC0099C1D6BDA3026ACAD6CAECBAA1F2,
	FuzzyMachine_get___Usage_m78968EB823815CDA3202B3E8EEB389D89649C1CC,
	FuzzyMachine_get_FuzzyStates_m09286854F0DA56CF4D4AF647F193AF075865C607,
	FuzzyMachine_GetActiveState_m3E5899A06A4CED53ABBE7F9B7FCFB7A5ABB71BA2,
	FuzzyMachine_GetActiveStates_m27061741962F7D885419EE85875939C5AAB3E0A0,
	FuzzyMachine_Awake_mEC2E3A80FB6A245BF96D9C11443CA9928D6E058D,
	FuzzyMachine_HandleDeath_m4772C0BF7FB80139C8A69C16F1554F9C26E99ADC,
	FuzzyMachine_OnDisable_m9F1D3C709CA0455DDFFFE56E3DF44A85FC9008B5,
	FuzzyMachine_DeactivateAll_m419FD975A8BDD2E326D88A58E1B3D445F2539CDB,
	FuzzyMachine_RegisterFuzzyState_mC341A91758C0E51E01B0821CA85AF8A4D115E411,
	FuzzyMachine_UnregisterFuzzyState_m8DF01F9C585DB89CA9780E8252BA26D346232701,
	FuzzyMachine_Update_m80C897366F20C8CD6281EACA5EA254040D9F56A1,
	FuzzyMachine__ctor_mEA00C4CE53B0B3C1EAB96BE17BBB61832BD9BA83,
	StateEnterEvent__ctor_m198F6230C7B129EB6D164B264955195B01E4B55A,
	U3CU3Ec__cctor_m5EC22FCA61324600323C61E92F5291EDF82CC049,
	U3CU3Ec__ctor_m51520DA9445C01762227F82BE4E2063417A265BC,
	U3CU3Ec_U3CDeactivateAllU3Eb__13_0_m9D8C2B3910C869B0CA91B7DDC421FCF89DF2ECA2,
	U3CU3Ec_U3CUpdateU3Eb__16_0_m676C7F3534730EF7099325AEE6D391F44089389F,
	GrudgeHolder_get___Usage_mA179036E56026F2083E180F6A3679B5F938F50C8,
	GrudgeHolder_get_GrudgeTarget_m271D9D9E0338CBED15674F926C1EB231981DDFEE,
	GrudgeHolder_Awake_mB99B3FF933D1C19422F90731D52F31532F2DBF0C,
	GrudgeHolder_Update_mDB5DAAFDB44731F686EC865B28E211E76D3A619A,
	GrudgeHolder_GetWeight_m440623811DEE24045AD41293D4030EA15C7C619C,
	GrudgeHolder_CalculateWeight_m58810D17711B92222DA3D19A88E443DEA500C06A,
	GrudgeHolder_OnEnable_m28AA1DD83E4A5ADA82C552A5AF0CA738675DABA4,
	GrudgeHolder_OnDisable_m5E3421CCE19160DEABBB4467517A7C0AAC266B72,
	GrudgeHolder_HandleDamageReceived_mF45360643BC4F5FC7206F794C4AEFAD293E1A07E,
	GrudgeHolder__ctor_m1E3C45F9C3832E6BB47369B3D1F3A5628FC1D7AE,
	GrudgeHolder__cctor_m4D1A3AE748DD9DF532ACE31D9EED28A3A0557AE9,
	U3CU3Ec__cctor_mDB61A0695634EB612F003E91F93048ECE4133328,
	U3CU3Ec__ctor_m0A5047A0774BE7B0EE387ABDBF34F44DA8098024,
	U3CU3Ec_U3CUpdateU3Eb__13_0_mDAAF76A35EE5ED1B735FBF80DBE0EA5F2952FA04,
	GrudgeTarget_AI_get___Usage_m06C638A8F6BC81A5504C66CC35CE69F97CED3F0F,
	GrudgeTarget_AI_get_FuzzyLayer_m84D2AE00B82AFDD286BE271087435EE17F50CBF1,
	GrudgeTarget_AI_Initialize_mDB7CCEA56DFEBFA92F386DCB5D1692DAD0039DC3,
	GrudgeTarget_AI_CalculateWeight_m114B977155A8F143DBCB307245A84A392753676C,
	GrudgeTarget_AI_ActiveUpdate_m9CE23BD416C1665DEBEE6707CBB147FAF28DDFD2,
	GrudgeTarget_AI_HandleActivate_mCF2CACFF3834B30481A60D75C18DBC36B2B51588,
	GrudgeTarget_AI_HandleDeactivate_mE7BA03AB99278B6E1E41CDC7FC16910696280056,
	GrudgeTarget_AI__ctor_m14E0E718E9B696F73FDC7B9B1B894BCF74858121,
	HostileTargeter_AI_get___Usage_m5AC8CE40C32197B99C00A601864E8DA8406016A7,
	HostileTargeter_AI_get_FuzzyLayer_m74697172B8166CCD233BD4233DA95592058CA555,
	HostileTargeter_AI_Reset_m76253B7A8986582C4E3BB001AF2C6E24EE5DE0A0,
	HostileTargeter_AI_Initialize_m4372C8A45260B47639102D0DA4837989FEF68822,
	HostileTargeter_AI_CalculateWeight_mB9A81C5617C0F08E644599783B09FB1F9671AE8C,
	HostileTargeter_AI_ActiveUpdate_mA6CA9697A2571832ECB392D0180F4A2D5D5296C4,
	HostileTargeter_AI_HandleActivate_m7BAC0948CFE901786247E62BBC6B4187721B1B0F,
	HostileTargeter_AI_HandleDeactivate_m4C9A1588C7038DE38C7A9859C3649DD320CC6BB3,
	HostileTargeter_AI__ctor_m999609BC6E08687E19F874945CE6C26FC908B44D,
	U3CU3Ec__cctor_m50D8967ECFCD5B0D406C3BB093CF7B0C4EFAF6FA,
	U3CU3Ec__ctor_m451BFB79768BC11A2FD8EB1A06A7F7FE36986FD1,
	U3CU3Ec_U3CActiveUpdateU3Eb__16_1_m06040BA886A352FFF1EB7864EFF397BC715EEC1B,
	U3CU3Ec_U3CActiveUpdateU3Eb__16_2_m27F9B8E5632B4E7EECA4EDAE08C742BE0828D3C4,
	U3CU3Ec__DisplayClass16_0__ctor_m1A6B930AD0EBBAC9CB76C719D0CADFB16D4752DE,
	U3CU3Ec__DisplayClass16_0_U3CActiveUpdateU3Eb__0_m223F619239BC8092548FB89385CC52473CCBF47F,
	MedkitSeek_AI_get_FuzzyLayer_m088A8FEECDD449A0577959CCA64000BCE2DB3DC5,
	MedkitSeek_AI_get___Usage_m91CFEF4163B6638705906F43008B0CF6A6B2F3EE,
	MedkitSeek_AI__ctor_mB0284401B620D88466DB1ADD8CF9633C1744730B,
	ObjectiveSeeker_AI_get___Usage_mEB5C6BEEA192B3BA3F98347EF951F340974CFF46,
	ObjectiveSeeker_AI_get_FuzzyLayer_m5CEAE91F5F7FF038AD1909CD256D0C9000F5F828,
	ObjectiveSeeker_AI__ctor_m0929D710EDC9269177F853F29FAA944E0DC4B851,
	Patrol_AI_get___Usage_m95883191F75DA2A46285E543FEA32B904C98C538,
	Patrol_AI_get_FuzzyLayer_m83589EC90D837C4BBD5BA23EF4E8560C7857277B,
	Patrol_AI_Initialize_m73D2277765D84E2006582BE22BAFB089834448DA,
	Patrol_AI_CalculateWeight_m679ABFA7C31521CB056006F819BB5B46327E39D5,
	Patrol_AI_ActiveUpdate_mAE3585BA3D50337204E938CDECD3AD4A303B4241,
	Patrol_AI_HandleActivate_mECD176005A95D630DE31D85CA6788BB8A97EF9E9,
	Patrol_AI_SetNextTarget_m847E6BCF41E36F44C68830F5D06B6BD06EE923B3,
	Patrol_AI_NavigateToClosest_mBE366BCA858533E38721AA2130B323143158BEE2,
	Patrol_AI_HandleDeactivate_m0F2BF9C39D6F9005031E99BAC816145D3F019D0A,
	Patrol_AI__ctor_mEC078047F368D036F9FDEEDFFF50D9BE6115FCCF,
	Reload_AI_get___Usage_m1CBAEF9B7DF926C6D9D114567927AE20FE98E5D9,
	Reload_AI_get_FuzzyLayer_mFDB565E68ADE0C550934474D30C25129538C3B67,
	Reload_AI_Reset_m1A1CD79C099D0546BCA99B960AFB0395D34FD23B,
	Reload_AI_Initialize_m652816043F5E1DD6B42E5129536B1059A1D9240F,
	Reload_AI_CalculateWeight_mEF3B667706D88CA900CE541BF4EBDACA3EB0261C,
	Reload_AI_ActiveUpdate_mFB84663EA3387417B3D40BB1355F1DD0B0F7B336,
	Reload_AI_ActivateReload_m34EE4CDADA98436C3AB3235C32DF984C6552AE87,
	Reload_AI_HandleActivate_m36C72C0BC41B6E7BEDA3454A39D7D4A8DB4B6230,
	Reload_AI_HandleDeactivate_mD7AE26F01D97FAC9FE1346F41C2BAF22A2303403,
	Reload_AI__ctor_m31563D4987824E31C8C96E76E018C17E6AD6B0D8,
	U3CActivateReloadU3Ed__11__ctor_m968CDE04619B681C250C76ADB4927B5410F59830,
	U3CActivateReloadU3Ed__11_System_IDisposable_Dispose_m4DB5FAF848448D5A9AAFFB877E83D827C4113A3F,
	U3CActivateReloadU3Ed__11_MoveNext_m54DBD419ABB569535D8D9E3060309690BDEB9445,
	U3CActivateReloadU3Ed__11_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mD7A9383E6E6E08550627DEDB3FEBE3FEEE76C8A0,
	U3CActivateReloadU3Ed__11_System_Collections_IEnumerator_Reset_m69F5F383AEF8E87DD1F5969275181D0DF6A3E118,
	U3CActivateReloadU3Ed__11_System_Collections_IEnumerator_get_Current_m3B1F34F3B488B25CC42B5CD8F15A84BCC6FCDD8F,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	ShootTargetSeek_AI_get_FuzzyLayer_m0E9B68597627FB95698B8106A83C2E7A4F4A86B8,
	ShootTargetSeek_AI_get___Usage_m47195A77EA304064C4B428935A26DC19F4970478,
	ShootTargetSeek_AI_Initialize_mC1A0F48C25EEC2B399384B7446B5BB052C1F65A1,
	ShootTargetSeek_AI_CalculateWeight_m67FB6B59F4AECCEBCD247D437CF954702E8F5CEA,
	ShootTargetSeek_AI_ActiveUpdate_m70CEB202D9AC066A64066D3D82C7F67AD51395D3,
	ShootTargetSeek_AI_HandleActivate_m59230B20673B85D5DD0D81A8D2016243892EF361,
	ShootTargetSeek_AI_HandleDeactivate_mAF35FFAA502AA5548890184AC7608242C0AE717A,
	ShootTargetSeek_AI__ctor_m127CF7FEA6E9818BECCDEC50178C70E9FBC71449,
	TargetableBase_get_Position_m8C9D0EAE9A63A894DA0D8A59787C09EF4ABD10D3,
	TargetableBase_set_Position_m5E4AA788A21F3D9BD5AAE11D456B7710C3ACE319,
	TargetableBase_Awake_m2D710E997761C68E57FA64FA01E93F8E8F47E37E,
	NULL,
	TargetableBase_Update_m239433FF40FE540213D6B67F1CF9551B078EE8C5,
	TargetableBase_OnEnable_m6B5EF58B6AED9928FDFA80FC215AFED862B53ABD,
	TargetableBase_OnDisable_m968D82145BFC9773837E54C5F1D4562440D8AABA,
	TargetableBase__ctor_m947F135B4CAE0570154F96C8E16648BA80F2B7F2,
	TargetableBase_Polyperfect_War_ITargetable_get_gameObject_mE90B6CFDC8B3B21185FABFCBF9D393AD76FDE4FF,
	TargetShooter_AI_get___Usage_mBF0CB6C3E41FB31452F7923EC610A6B78A623079,
	TargetShooter_AI_get_FuzzyLayer_mA69CB9849699557DD36AB53D3E120BB51BDFC9C6,
	TargetShooter_AI_Initialize_m3904ABF442DD8546139F302A2689287C73FBC8F4,
	TargetShooter_AI_CalculateWeight_m8277F1F2DA855DCFEAF3EFA09ACD64880727D3C7,
	TargetShooter_AI_ActiveUpdate_mA004A3AB6E2A102F5E6413318350D3D92F59F06F,
	TargetShooter_AI_HandleActivate_m7AF3ECA4339C64437E91EF18F36F63399177A731,
	TargetShooter_AI_HandleDeactivate_mB9268586E90D93B9303ACB1D0AA96D699E87E6D1,
	TargetShooter_AI_GetAimPosition_m9B2978E71EBF737C3D276C4E00616A17024BAD2A,
	TargetShooter_AI__ctor_m79273FA2FD5F3A0883D1C00D7896793A45DA79FD,
	UsableSeek_AI_get_FuzzyLayer_m2E6694F6083496C18A130BCBC05ABD9B65C6FB56,
	UsableSeek_AI_get___Usage_m95519DF201306063581A11D47022AA152A847675,
	UsableSeek_AI_Reset_mB33B78D37C74723BFF6AEB63F466F397C879C2A8,
	UsableSeek_AI__ctor_mAE7EFEBCC85FA1150E7859ACAF3F296E744A177A,
	Wander_AI_get_FuzzyLayer_mC2B74B65938470D7074F11252ACF5C9BAB82E90A,
	Wander_AI_get___Usage_mE7646FAF0AC8A6FE0E97E7D4BC02C76320B3D072,
	Wander_AI_CalculateWeight_mC90203F7F374E7B0B52E52D71DA929C7C3BB5B42,
	Wander_AI_Initialize_mFE3BBDA3A8109761AE3AB9CFA5A2F8886828A3D2,
	Wander_AI_ActiveUpdate_m0A7809421A46FB7755FF6E8442CBB28403B178C7,
	Wander_AI_HandleActivate_mE00F9952189A7B6278C7E7264073C9C5063D3DB0,
	Wander_AI_HandleDeactivate_m5F56D5AB80FBEC7E0DA21C7171CCE1F3FA51D8E2,
	Wander_AI_Reset_m67EC1C457F94FDE665893B70C72FB238D1B3AD0F,
	Wander_AI_SetWander_m3EE6BA5E50142B21C8DE49C337C325732E460CAA,
	Wander_AI_Wait_mF1D7C16BB378237B57009F6B9D8A8A6F37136E49,
	Wander_AI_WaitCoroutine_m1DFD661689880B6C12E52BF0B2DE4B58B44C8FD1,
	Wander_AI__ctor_m078B0803F41E717EECDB6CDDEE03B8745C24E760,
	U3CU3Ec__DisplayClass17_0__ctor_m144D0E4A866514794444F08DC18D6C8A564F6F23,
	U3CU3Ec__DisplayClass17_0_U3CSetWanderU3Eb__0_mC20B18676B6B39CE89A146AF8F7250C749A792B7,
	U3CWaitCoroutineU3Ed__19__ctor_mC34E34A850369983385B2865547883E1FBEF26AC,
	U3CWaitCoroutineU3Ed__19_System_IDisposable_Dispose_m418A9F7970BA8121F2A7E4A0AAEB053DB44391C7,
	U3CWaitCoroutineU3Ed__19_MoveNext_m6CFE65EB97CAFFE6156392834ED6B7A3F31361F3,
	U3CWaitCoroutineU3Ed__19_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mF1E56436CF3EFCB8B479D0097F28356DDA9A120F,
	U3CWaitCoroutineU3Ed__19_System_Collections_IEnumerator_Reset_m17C0E0031833A8C023D4DAFB5655BD33CA3E1402,
	U3CWaitCoroutineU3Ed__19_System_Collections_IEnumerator_get_Current_m276C056670851B70B190CA0FF26432F30AD1DA13,
	Ammo_Carrier_get___Usage_mEA7C1C7B8FA9D59658E5625AFDE3891355EF4797,
	Ammo_Carrier_get_NonzeroContainedTypes_m1D1D271DDD739ED765DCD1B776AA6EFB8E639746,
	Ammo_Carrier_Awake_mB083B92745351AFFA5D340F34CBA322F81C94DA7,
	Ammo_Carrier_SupportsAmmo_mF09822D6DA34DB9703A4836ACE7994A9DA34A7E1,
	Ammo_Carrier_GetRoundCount_m205F0C33FEAC9A01C8C5C320B5482A48FF90E665,
	Ammo_Carrier_TryAddRounds_mB0B53D7280DBB9F38C783E5CEA1D94FF44DF8C85,
	Ammo_Carrier_TryAddAmmoSupport_m687B0C62FC9D4F6DB7175C8AA1FEB657C6EA1DA9,
	Ammo_Carrier_TakeRounds_mBE46EEE27F33688963B86B7030FDAE5BAF13C6A6,
	Ammo_Carrier__ctor_mF062B6B89BE5B514A3AC23939F3B905B996C7817,
	U3CU3Ec__cctor_mAA3CC51343AF763B7541CEC52F22734972816C7F,
	U3CU3Ec__ctor_m7C8F9DBFF02E917699C26650A44CDC44F9E40B4A,
	U3CU3Ec_U3Cget_NonzeroContainedTypesU3Eb__6_0_mD1208421D8803469F2E9C5D76057A5C9EB591C1E,
	U3CU3Ec_U3Cget_NonzeroContainedTypesU3Eb__6_1_m15247DFDB52623C4C3DA34782FB9C19068D94B7F,
	Rider_Carrier_get___Usage_mCCBBC01656D47ADA4AF3AC19EC9D06878F51F159,
	Rider_Carrier_get_Riders_m43600B4EF62F01651A51CE8F0C890F92883BB77E,
	Rider_Carrier_Awake_m92BEF2A62B1C450FD185C1F3D4728DF0BBFDB72B,
	Rider_Carrier_AddRider_m73C81787E2AE82854F0DAAA6B89B9FDCBDA9C6BB,
	Rider_Carrier_RemoveRider_mB95E18163A282FE4FC7BC5B7D020975C94C2733C,
	Rider_Carrier_OnDisable_m3C6F1A81320969516C01481E8D08C9530DC01089,
	Rider_Carrier_RegisterRiderEnterCallback_m86A0F29C2E23529C47AFA3B3775568205602F132,
	Rider_Carrier_RegisterRiderExitCallback_mE6F26E0ED2F037C5154DDA211C17A6AA7054541A,
	Rider_Carrier_UnregisterRiderEnterCallback_m23DB8B1620632D6E0D097E513B6383C3649B1572,
	Rider_Carrier_UnregisterRiderExitCallback_m088DF01DBB92D8F32BB4282B31251990726720D0,
	Rider_Carrier_HasRider_m9DEABB50CEFC4AE501A8592FCE436B614FA50DCB,
	Rider_Carrier_SupportsRider_m6AB6CE164CB4E6A162DE6FC338AF1087439EA4DF,
	Rider_Carrier_EjectAll_m4284676285F8CE7F3671735AF8AD223F5789AC01,
	Rider_Carrier_GetSlot_m7523D325B214891831C62F3A98BF800FC9CF71FB,
	Rider_Carrier__ctor_m061DEF1D2C9DCE6FB3C18964E48FF0D9DE98A872,
	Rider_Carrier_Polyperfect_War_IRideable_get_gameObject_mF46BDD33B53AE0D6EC53356442363576D3F2E573,
	U3CU3Ec__DisplayClass11_0__ctor_mEB5A3C72304A591593ADDB813ECFABF6F1678898,
	U3CU3Ec__DisplayClass11_0_U3CAwakeU3Eb__0_mE49B2384FF8E64D5F4DDEDE7928BBCC1F50E8228,
	Usable_Carrier_get_UsableInstances_m4AAE9C46D80B21D3095B485F7E75BBD678DFA9D2,
	Usable_Carrier_Awake_m13F0FD97EDD6E84CF057F300F09ABC042078BBD1,
	Usable_Carrier_Start_mF6C39C85A225BED23E717D07F3FEEC6CB32A5D4D,
	Usable_Carrier_IncrementUsableIndex_mD28F8656FE84A893C22C6117CD76C78A057D3CCF,
	Usable_Carrier_SetUpUsables_m8DCDB2BBF56719F01FCD3757CAA1133BEEE01E4A,
	Usable_Carrier_HasUsable_mAB75E03BB34650197B8BC2398BD6332266811C7D,
	Usable_Carrier_AddUsable_m637182B3B5644FC1285864938CD26E650CFAD04F,
	Usable_Carrier_AddUsableWithoutNotify_mE0864CE667A6B3F663A24E4CFC499E04302FDA33,
	Usable_Carrier_SetUsableIndex_mF98C049A59A32345679114335DB98F92C3AFFD2F,
	Usable_Carrier_RegisterChangeCallback_m9718784ED880DF0BE442953A666447E0BCCF2909,
	Usable_Carrier_UnregisterChangeCallback_m57E30BF0D7870C82CD539B8E2268AE557C3FEB74,
	Usable_Carrier_GetInitializingEvent_m325A6DADD211FEABC496737DA0CC6C2916E28124,
	Usable_Carrier_get___Usage_mEC08CD93F641A06633D1393CF83926CC3B0952D7,
	Usable_Carrier__ctor_mC8E1EFB80A8E9378F5BA73DEC48AC4B01BED9373,
	Usable_Carrier_U3CStartU3Eb__9_0_m264D1E025C6DB360A3CD994DF274E6C50312F168,
	Usable_Carrier_U3CStartU3Eb__9_1_mF4C7F76DF06736539F7BB7F99D9F8008704B5E97,
	Usable_Carrier_U3CStartU3Eb__9_2_m79FA017BC3E8B11312B9E9BA9D944EE5B639EF96,
	ActiveAmmoInfo__ctor_m48E79771AFD56B76BCD19F4F7D646D3544257205,
	AmmoInfo__ctor_m27F80CC108BB8674130B5D351F3465868AA0C866,
	AmmoInfo_ToString_mDBC79047138F45541D775EB88C6353639557AFA0,
	AmmoType__ctor_m56303F018B5082FB29E0C4C9E869B3648CED8DA6,
	NULL,
	CoroutineReference_get_Coroutine_m7524C8642757CF74247096C4C3579BD312736C38,
	CoroutineReference_set_Coroutine_mFC4247025543455F4A928B651B8408BC3FB84254,
	CoroutineReference__ctor_m757B081CD9E84632D0A2799C2245AECC937179F6,
	DamageContext_get_DamageAmount_mDC75FCA4EAB0A5D6310D1C79F9D7020BD1DF9178,
	DamageContext_set_DamageAmount_mF57E0C5506BF63EC24FC0E1040A8366186709C39,
	DamageContext_get_ImpactVector_m3D9419597E7BCEE7A687BDF216963DD78C6DC09A,
	DamageContext_set_ImpactVector_m1F515B5CBE319400CC9EA03C103DE6BC352CF184,
	DamageContext_get_ImpactPosition_m31758F4C02F236C7F43E46D09878595E62E21F42,
	DamageContext_set_ImpactPosition_m355D3107835CA3563DD8B3B8DE90C11FB7901D49,
	DamageContext_get_UseContext_mEB2C77D316A4A8E6E283B2DFFCADB456DCA73AB2,
	DamageContext_set_UseContext_m9BBC8D7263A614690B85EE792A8D5CC556E5B3CF,
	DamageContext__ctor_mB9CAD3BEC2A23085A7E301DBCDBF74E94D5DCBAE,
	Faction__ctor_mF46A8F9189CEF0D65A6CFC1737BCD96E81F09194,
	IKWeight__ctor_m0F3AB7A526C47D0F2CD32E24040C70811B6E7D55,
	Inputs__cctor_m233CA418B8F6A59E86A88B49F84D6B556AE43E71,
	ButtonInputReference_get_Name_mDF7980B040765DDF20AB88D727184E16BF5CF3A8,
	ButtonInputReference__ctor_m0DABF1C2A7252A245FD8C4D8A30B13A7D60281A0,
	MultiAxisInputReference_get_Name_m269849DF6DAE1D0FEC69CF895AC59E2164923054,
	MultiAxisInputReference__ctor_mCF0200526F85D9A4DA0103A53453654619D757D2,
	ActivationDelegate__ctor_mEFC76367E549C5FD34F31F5D57ED477D994F6E05,
	ActivationDelegate_Invoke_m71C92DA80CCB56B84D698CA7231DF10566FE6013,
	ActivationDelegate_BeginInvoke_m110BBCB7844E74DFEF791A184A80A781445247D8,
	ActivationDelegate_EndInvoke_m43B098775CA88F8A7CA3AAADDE6FD04F1872E1ED,
	BoolState_get_Value_m8A00CCBB3A40F72A9121A0861B1B4F84734A297E,
	BoolState_set_Value_m166D1D7B0E86B8EE62673149AAF69AE3BC1454D8,
	BoolState_get_RecentlyActivated_m9A44F718684C2ADCFD1A43B74956C5B143793BBF,
	BoolState_Set_m0AAC6D9EA371A9BC6E55FD225EA0A3D6D5BC3E39,
	BoolState_add_OnActivate_mF8F76FD24D715A1F1C37706D350A1C2ED353EC64,
	BoolState_remove_OnActivate_m9E0C013F5460624A06E682595E3AF0236849C0E4,
	BoolState_add_OnDeactivate_mA820D0C6DF8C88EAEDACC3F9BD12C4910D7F370E,
	BoolState_remove_OnDeactivate_m851A5E18D45523892627D35BAD2B88C4A2818C9D,
	BoolState__ctor_mD1AC0BB89793D7B77C4FB685E80843FB65FA7F44,
	U3CU3Ec__cctor_m72EFF46CE8936EEAF1E85F30416C6373AE0BE7B4,
	U3CU3Ec__ctor_m0BE6611D45E1A0FF284275CF02A127ECCD89290C,
	U3CU3Ec_U3C_ctorU3Eb__15_0_mEA9189A53E9785F354A1F4D8CE6494D6F9EB3297,
	U3CU3Ec_U3C_ctorU3Eb__15_1_m5FF1A43BFF6D0AE166DF8E09A039AC22EEF2F40B,
	AxisInput_get_DirectVector_m5E9E448EBFBECD2F9674BFA245AF19EA8670BBCB,
	AxisInput_set_DirectVector_m04096BBEFDF32F376C5D260D3371BCECBDA86223,
	AxisInput_get_WorldVector_m01BEACD66A2DC329E071DD7C2DB27CB14D0A9896,
	AxisInput_set_WorldVector_m74DD752194715D2E03F4273656E9F4C499351E36,
	AxisInput_Set_mDC68347E25EED8D4C55379A98ED4848E9FE1A2F1,
	AxisInput__ctor_mAB1C208D28186B923F379B227026EEE80D4E568D,
	RideEnterContext__ctor_m789499FA9A2C6B8AAF5C9325472AC6D097A8F2D6,
	RideExitContext__ctor_m6B15EBF64FBC4B14C4BA8E7A6FE885622F8785DC,
	TrackerTargetPair__ctor_m2CB4D3D378338810F9E706F55F1C2166B8FD93FB,
	TrackerTargetPair_GetHashCode_mA24496B03EB3793907D91ED250BC930A7306BB5D,
	UsableCapability__ctor_m2947B15696280A3864DFF4350A24F776B0AE23A6,
	UseContext__ctor_m65ABBA29365AEE4CC41DEC3B7C61C27CA8D654FE,
	UseFailedContext__ctor_m39058652313C86F641E0491C7BA37193707908B4,
	Reason__ctor_m03BA4B72699A2F2990E509E08923BBEED124D158,
	Reason__cctor_m5C623C02D105B9F7C187B52117BE0B9CA072E737,
	DamageUtility_AoEDamage_m3149E48E95A8D8C46EA5A260CABF507FD97C6ADC,
	DamageUtility__cctor_m103C0D35142F47F3735424B249404273160B933E,
	NULL,
	NULL,
	SpawnUtility_SpawnWithContext_mDC1C1E3D9B5CF3244D6FA1E2120878A454D9FDE6,
	SpawnUtility_Spawn_mD6E0F0540190ED2D4FC60C9B26D87B31365FEC33,
	TransformExtensions_AllChildren_mB96B439E8A47909A9B145AD3C37516D68C232B29,
	U3CAllChildrenU3Ed__0__ctor_m48650B64440E3141B18D9B8FE794E933A055561D,
	U3CAllChildrenU3Ed__0_System_IDisposable_Dispose_m25AD14BBD52B2E9DE84E19D781517628A4E2F60A,
	U3CAllChildrenU3Ed__0_MoveNext_m85ACFC2685E9BB17431BFD778BEECEE75F5F56EE,
	U3CAllChildrenU3Ed__0_U3CU3Em__Finally1_m306CC2C92BBA0991F0E0D39B34DBE1A45FA8949B,
	U3CAllChildrenU3Ed__0_U3CU3Em__Finally2_mC89178FC1F453ED503E01EF5789825C24433B4B6,
	U3CAllChildrenU3Ed__0_System_Collections_Generic_IEnumeratorU3CUnityEngine_TransformU3E_get_Current_m35EAFEF3411881406DBACFC15F918462D211B9DB,
	U3CAllChildrenU3Ed__0_System_Collections_IEnumerator_Reset_m16B81B186B95A7730B1BFA06F6849EBFADAB10C8,
	U3CAllChildrenU3Ed__0_System_Collections_IEnumerator_get_Current_m5724E3C86FDE4899D664EB09B140EFBC207D8407,
	U3CAllChildrenU3Ed__0_System_Collections_Generic_IEnumerableU3CUnityEngine_TransformU3E_GetEnumerator_m1FAC5E19282B873719ADC32B3DF68BC6190B0B92,
	U3CAllChildrenU3Ed__0_System_Collections_IEnumerable_GetEnumerator_m19CE1FA659E156B0B3D7EDC6465B54CD02DBAFD8,
	AutoRespawn_get___Usage_m5342F22A6ABA8924ECE3331527A05CE46AE3ED57,
	AutoRespawn_Start_m292C03D0D5C2A524DDE817BCD764E306886EC706,
	AutoRespawn_HandleAutoRespawn_m2A7CE2AAF3F8FA577AD771945E215E7D0988D49A,
	AutoRespawn_DelayRevive_m9E2740F32BD991A19CDB3506EBFB17216A730316,
	AutoRespawn__ctor_m934A129AE8901BF940FCEE92E3C06318FEA798C0,
	U3CDelayReviveU3Ed__6__ctor_m7BE334D629CFA65C7015A5DB5E1DABB6DD170DED,
	U3CDelayReviveU3Ed__6_System_IDisposable_Dispose_m63070B6E7D63FA8E55F015D730DEACB60B529FDC,
	U3CDelayReviveU3Ed__6_MoveNext_mE376BE9838763FC09A68E2A8F2106179E5B17C8A,
	U3CDelayReviveU3Ed__6_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m49EE96CF7EB0134057FB5A421B6527C943019384,
	U3CDelayReviveU3Ed__6_System_Collections_IEnumerator_Reset_mD8B3B0C77CE3BE0CE757484545A20CE0822BE755,
	U3CDelayReviveU3Ed__6_System_Collections_IEnumerator_get_Current_m8A7663304EE377C964D21CCFAD20080FBE7BA435,
	AxisTurning_get___Usage_m1D18D0F25F60FD1522C87FBE392487E07B4ECBB9,
	AxisTurning_Update_mEBE28210DA0F79FA3B9E2B5FB4FC540255990643,
	AxisTurning__ctor_m70CD98427C5067D46141489389F20A4E0627B95C,
	CameraRotationMatch_Start_m3B7AD1E6813B0C9A296755CAA4CB569ADBE3F088,
	CameraRotationMatch_LateUpdate_m62BBEBA4A4BCB42C5CC15560883904215053C187,
	CameraRotationMatch__ctor_m9C16624BAF179F6E3F80218D9E63F282A0D704BD,
	CollisionEvents_get___Usage_mC607430BF2C594099161814E4F00079705BD0EBB,
	CollisionEvents_OnCollisionEnter_m1F2D59680AB4F15CA4D43F533A7F5C8F68AEEC6D,
	CollisionEvents_OnCollisionStay_m2077C53AF36D297093BD793A4C5285001F78DE0C,
	CollisionEvents_OnCollisionExit_m92DFC717364E671BE01000F45D87C12AAAEFF40D,
	CollisionEvents__ctor_m354604DF415526507A635DCB909C8AA93370C614,
	ConstrainedAxisTurning_get___Usage_mC5D33389A0635C91F4A0B59A19F92A10166529E0,
	ConstrainedAxisTurning_Start_mFAD76D3DCA6E75AC685829480100BDC39114E8C3,
	ConstrainedAxisTurning_Update_mCC7EFF5526879E31F7997EC185BBD76DA8FD8ADD,
	ConstrainedAxisTurning__ctor_m2E8E89002984CF2BA896E8A1155AC5BC55285E7A,
	CursorLocker_Start_m2205404D8724FC7D087A543D51DFCB7292E7C3C3,
	CursorLocker_Update_m5A5150D7259D2C09419709D980B4CDEBF2AC1334,
	CursorLocker_LockCursor_m144754B8FAFF4541078892600663BA81D0933C1E,
	CursorLocker_UnlockCursor_mD50A31E76BA48A2D08F98762F3EECD8F7A11A708,
	CursorLocker_CursorPosition_mE71478FBAF09227129C3A1289739125924B24D12,
	CursorLocker__ctor_mD1E518BAFED093201ABF23F963506993AD46CB54,
	DamageOverTime_Awake_mC1FF7A39BC4018D8227644CBB9796739BB82A8C5,
	DamageOverTime_Update_m011D0583A6C60B5A29872DCD1C353DFDE6731B8D,
	DamageOverTime__ctor_m468965247DD87D088D25B0E7085064A9AB85171A,
	InitialVelocity_get___Usage_mC5FC9B269A4FF976944226E3FA1E7CBCDFF9B123,
	InitialVelocity_Start_m2DCFC2EB6C2F11E54D1BF4ED8C7A1C0DE56AE3CE,
	InitialVelocity__ctor_mCA32EED7FDDFE9AAAB43154A9B211624769D7302,
	MatchRotation_get___Usage_m781AB504A15A8744C975075968B39761E1E0A1B3,
	MatchRotation_Update_m52284AD82B381589A55705BECF826654D4AD24B7,
	MatchRotation__ctor_m9BFD0FB94158C1BA59FEBDA838E50A8FA485F41F,
	RandomSpawner_get___Usage_m9D22D184E392783A63149BED076BBA7A52044F09,
	RandomSpawner_OnValidate_mE57FAF0E06A4C2AD0DB258565168EFCBB68B3604,
	RandomSpawner_Start_mBDAA49E8A7A55216818ADAB480CB1ABC7F49C49D,
	RandomSpawner_spawnCoroutine_m4093697CFBD5FD21E968BF4D92101838AB3AE9C7,
	RandomSpawner__ctor_m12A6B36B5951B4457769E6B2B23F625C8C15BE5F,
	U3CspawnCoroutineU3Ed__9__ctor_m4D49960E23EF3A9B861520398514B6EA8FB1D9C0,
	U3CspawnCoroutineU3Ed__9_System_IDisposable_Dispose_m4D897C827D61A510D09A4C38A8276B390AD5B0CA,
	U3CspawnCoroutineU3Ed__9_MoveNext_m4B9F2C1A3FA0D81DFE000E4E9008CC5C336C34D5,
	U3CspawnCoroutineU3Ed__9_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m7600C8E591C0ABBC447B0223B8AB0623135EF164,
	U3CspawnCoroutineU3Ed__9_System_Collections_IEnumerator_Reset_mE3ED018E42BDB7E882C89C25E5ED99B3DFE4714D,
	U3CspawnCoroutineU3Ed__9_System_Collections_IEnumerator_get_Current_mA6D46DE9D3D274E3070559ACAA81BAC2F2C76915,
	RotateToUp_get___Usage_m807C45AE72289B9CBA83E3D5605F887C87AD3E64,
	RotateToUp_Update_mF1B0DF4D8AAA50C7D5ED5DB0CCACA658F2BDDFC7,
	RotateToUp__ctor_m4E11169838B14559531C4793B6BC36A87A65B9E0,
	RowSpawner_Start_m7ED042E65A031E4ECF5955180C519F52F21B8C31,
	RowSpawner_DoSpawn_m6903414400E42BA824C4B99FA7CF6CB4C38ADFC5,
	RowSpawner__ctor_mA8FE691059631962D8604B296873A9699F209E34,
	Spawner_get___Usage_m83634F9CDFDACFA4711F6B041C69BB1FA6C0F73A,
	Spawner_GetToSpawn_mD2ED62484F3FEA45D57A4F2743852E01E8FE09C0,
	Spawner_get_targetTransform_mC4A9DEBB35BF51DAE39498E587543C529744C9D7,
	Spawner_Spawn_m5C946D2CD504878E92AEAFD1469BA425491D3F10,
	Spawner_Spawn_m3B1DCFDADBE096D2F3925A7D705A0BE2026F7525,
	Spawner_TrySpawnWithContext_mD6A74072A098535400266C9AD1EED7E07D039B58,
	Spawner_SpawnAfter_mB483CE793426882DF53C96FE44DF82EA8AEC696E,
	Spawner_Spawn_mDAE799547E3E95937BCE095B4823372DC6DB6BDF,
	Spawner_Spawn_m55E50E433C39934C283E6B4575D56683B66BAD89,
	Spawner__ctor_m10C6B478D236A252525E6AF073EB86AA37D4C1CD,
	TriggerEvents_get___Usage_m582ED5A90D620288B30BA14AE59F8AE5900ED2DC,
	TriggerEvents_OnTriggerEnter_m7821041BFB8C0BD4FB1B66E17F7AC20ACBB1A19E,
	TriggerEvents_OnTriggerStay_mCB49AD4F599AEA3603777F904B853BDCF2232A06,
	TriggerEvents_OnTriggerExit_m647388D1E8E2483C1017D02B6116F67646AF1C37,
	TriggerEvents_IsInLayer_mD7ADDDA3AF32A03722AE87FDCC24923FCE444519,
	TriggerEvents__ctor_m2CBE69FF15D5561961064497E822CAD8C2481743,
	Tween_Arbitrary_mA190C3023F6409C57AE5DC3D72E66002F195F40F,
	Tween_ArbitraryTween_mFB07EDD72F49066F6FEC6F594622D97172CE5539,
	U3CArbitraryTweenU3Ed__1__ctor_m19EC8F11B257B62150653920A5B9235C54FCC2AB,
	U3CArbitraryTweenU3Ed__1_System_IDisposable_Dispose_m242DD17EE505EA9306CBEF43B1A83C3B2F217DE3,
	U3CArbitraryTweenU3Ed__1_MoveNext_mA4344AA3C634D349CDE5BF7E07E1F9D020E17237,
	U3CArbitraryTweenU3Ed__1_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mDECCBA851BC82B9AD0DEF6D964C0942B8D674E66,
	U3CArbitraryTweenU3Ed__1_System_Collections_IEnumerator_Reset_mC51BD67E162160B11693D569E9509FC5C53D508D,
	U3CArbitraryTweenU3Ed__1_System_Collections_IEnumerator_get_Current_m7D55A097AC375A658676B21935FD236B0575F301,
	Unparenter_get___Usage_m7A8DF9C295B5B13878A18BEF48EDF5B0B39236DF,
	Unparenter_Start_mDC05AD6871254A119CE7629F6C143D18660A187A,
	Unparenter__ctor_m4BE8B394DECB2C7CCF7C1BBFC4A80FEE876A4E87,
	Unparenter_U3CStartU3Eb__2_0_m305EC0882B84E6642256505DAF9A0DC8A2E067ED,
	Unparenter_U3CStartU3Eb__2_1_m752572DEBFD22474A21DDEAB4D2E663C20CB4337,
	Unparenter_U3CStartU3Eb__2_2_m858E0FF255C0058BC8026F0F7DACC0A0F28659C6,
	AerodynamicCurves__ctor_m2A47039D1115E28CD6A815D07E52190A523CF312,
	AimCamera_Start_mF94D38D46EF1EED49DB28241E187E70C0735C706,
	AimCamera_Update_mE848E6D907F2123C32D2007501FAA66C74B22301,
	AimCamera__ctor_mC17FF276ABCD805C0331319876E07B8211480CA1,
	AntiStuckSystem_Awake_m43977CA0976F2CEDD58AA501EA3BE38D9C82B817,
	AntiStuckSystem_FixedUpdate_mF9E38B4E09E866BCD9E0AFF1316B7B428442532F,
	AntiStuckSystem__ctor_mB069D5700D51AA6836FFEADFBDCAF98623AEF968,
	CameraBrain_OnEnable_mBCA5F7071164B3B7B6134E9EE9AD248AD7426B4B,
	CameraBrain_AddCamera_m371D6974F5FB36C0B2D670E706868D842A4A06C2,
	CameraBrain_Awake_m7F4F38EBF4564008C0628FADD5A4754A272CE3F1,
	CameraBrain_OnPriorityChange_m174EA4309338DDACD676594D2760E910E7797190,
	CameraBrain_MoveToPosition_mDAD45B9363A63B1C2926CA061184E70E28B6DEB8,
	CameraBrain_LateUpdate_m7448C274EED2C1B2F15ECE7A255B009A730741DF,
	CameraBrain_SetParent_m08675631720F948B05CC017D2B6012906813DDCB,
	CameraBrain_HandleTargetInactive_m3F5B93E94B80F0E114B28526CB9508C9D12C3292,
	CameraBrain__ctor_mA6E4DCA826898DDDF08B3035C6A9FF727834102D,
	U3CU3Ec__cctor_m470DD576FFF911E91F5F85E29A88124A162C99DC,
	U3CU3Ec__ctor_mACD5CECC25EF4D288429294CB14636611C638CF2,
	U3CU3Ec_U3COnPriorityChangeU3Eb__11_0_m4D00DC362D883059C563467D223E70171A523D9C,
	U3CMoveToPositionU3Ed__12__ctor_m840A1AA33750AFAE613EFDB3A8A42D2BE16ED974,
	U3CMoveToPositionU3Ed__12_System_IDisposable_Dispose_m9C793179F0CE37A56BAA0A0F13116993CAEA6CC8,
	U3CMoveToPositionU3Ed__12_MoveNext_m5969ACA97E3011C05A99AB7E22B4AC0E6844B73D,
	U3CMoveToPositionU3Ed__12_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m60E78B099169DE9F1A457F5CE815F7C822DC4432,
	U3CMoveToPositionU3Ed__12_System_Collections_IEnumerator_Reset_mF4B9D350FC7140CF5CA7BFE751E58A3BFCAF9ABA,
	U3CMoveToPositionU3Ed__12_System_Collections_IEnumerator_get_Current_mAB177E3268DCB9ADF49996A5B6B8AF6C838632F9,
	HideObject_OnPoolSpawn_m7CD81DB2D0E82B4CED6C0E2DFC774F13F15D61F6,
	HideObject_DestroyTimer_m60ED0152DCB7336C905088FEEBDE566C97C6D924,
	HideObject__ctor_mEE96C2D336E58F8B265B49EB94F680A6E4AD804B,
	U3CDestroyTimerU3Ed__3__ctor_m9874F0ACC79CAF4AD666FEDFE680FE7465530EDB,
	U3CDestroyTimerU3Ed__3_System_IDisposable_Dispose_m72BA911A14D8321FE7B363FF5031C8ED23F628CD,
	U3CDestroyTimerU3Ed__3_MoveNext_m585AC7BBFD48FE85E383E688F8DF989E621C2DBC,
	U3CDestroyTimerU3Ed__3_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mD45B8C7FC168DBD0383B21B170A94EEB7DB184CD,
	U3CDestroyTimerU3Ed__3_System_Collections_IEnumerator_Reset_m138477A9BB6DFF21AE19AA54B0F63175341DC4F6,
	U3CDestroyTimerU3Ed__3_System_Collections_IEnumerator_get_Current_m0E3A450F2F9AEF7D1A2CF27DA04CF877861628E8,
	NULL,
	NavPath_GetNextTarget_mE238C38E45E1930D7C17D1DB0C50DAC7DCFFAF9F,
	NavPath_GetCurrentTarget_m985CE0830C0F36F06A4E51C994057AC37FFD22D2,
	NavPath__ctor_m29F469CDF9DC44356BD61334A958B9F412D0ECAC,
	PoolSystem_get_Instance_mF974B13B8675920E1660D1F667C1259CB16D8824,
	PoolSystem_Start_m0ACD3EECCC6AC4A42D35BE42C2F8ED2FE37BCA43,
	PoolSystem_Spawn_m249C6D9E0D623C17572E015E3D9153EC2792195F,
	PoolSystem_PlaySound_mFF339A0D06BEDC5D45871FBA909CC07C5F9068B3,
	PoolSystem_SetUpPool_m524E569188AB875ADBA791A18C8AA68883E488BF,
	PoolSystem__ctor_mA1EB453BCAC3E4AE178309156479980111EC197A,
	Pool__ctor_m3485274BA9A0CEA090894900929E7F885D8A482C,
	Pool__ctor_mE09E48D1094C984580BD956D84BCFED34018CD63,
	StaticHelperClass_Remap_mEE77FAD90327D6C255C336D5319F39FCA181041D,
	StaticHelperClass_DamageArea_m14DC759799E2FBD1DDD8CCCB5CA58C6313BD96B6,
	VirtualCamera_SetPriority_m3AF36F1019A2BD14D19E8CBA39D606E4230FCBDD,
	VirtualCamera_Awake_m7163AE277C57A1F837D21C89BCBAD9EF8DF03995,
	VirtualCamera_Start_m58D5C6C06EFCEA4C3E7D657AA721E835AB419737,
	VirtualCamera_FixedUpdate_mB83AE5B2BBC6981B2789EBBE25D6588A935D1E19,
	VirtualCamera_Update_m2C8D13B7FEBFAC8BEE28864B1FE4DB3820244EB5,
	VirtualCamera_UpdateCamera_m3AC38F9EEEA58D235FEEEE8E7C75119F2EDC831A,
	VirtualCamera_OnDestroy_mF41B77CFE2EECBC706E68A8CCC6273F58221FF42,
	VirtualCamera__ctor_m9B8ABB8D07FED038E5C40194FE88B3C6661991C3,
	ParachuteSpawner_get___Usage_m114D4E3DA666443B3A97D0A985DCB2BB4B63B402,
	ParachuteSpawner_OnValidate_mCF9ECA9ECF83B90FCA5C449136D78EA52EDC79A0,
	ParachuteSpawner_Start_m1F7D8514381CFF4FA345FA5721964B3CEFFBD8F2,
	ParachuteSpawner_TrySpawnParachute_m8EE2C4927DF45C1943D5334A0835C276711F05AC,
	ParachuteSpawner_DoParachuteDelayed_m0FA73E3D9F67B70691A8D3548A7ECBCDAAE7313A,
	ParachuteSpawner_IsGroundInRange_m0E5F64D44A5C12C67E32FF7BEEFC77FB78D0440B,
	ParachuteSpawner_DoParachuteSpawn_m4A6EFDE7EF3EFB91F4E133CE15F6C8A61CF91518,
	ParachuteSpawner__ctor_m44496714E1FFE5CD54E7E93E5370312650F8C31F,
	U3CDoParachuteDelayedU3Ed__9__ctor_mD0568B4FB5A29BE8967F2CAA8FC848223D5B8ABD,
	U3CDoParachuteDelayedU3Ed__9_System_IDisposable_Dispose_mFCD637C6A2DD756005AAC52BD017CEE6BDF0F51E,
	U3CDoParachuteDelayedU3Ed__9_MoveNext_m7B53301516DFC7F9C60BD66687FFF9886EE19164,
	U3CDoParachuteDelayedU3Ed__9_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m59B1711451F62BCD99AFA18AD5C5315A4BB0915A,
	U3CDoParachuteDelayedU3Ed__9_System_Collections_IEnumerator_Reset_m106B7A956F0B18B8A9519782CAD5675B17E12C0B,
	U3CDoParachuteDelayedU3Ed__9_System_Collections_IEnumerator_get_Current_m629E60F5CCB3435C9C9E12B24810177C3A34B0C0,
	SceneTargetsManager_get___Usage_m9191B9C1E9280FF4036DBA5B728CBFADA81B027C,
	SceneTargetsManager_get_Instance_m9DB3E2404BD011C93FFACEEC34B57EF11FDB14BC,
	SceneTargetsManager_RegisterTargetable_mD8F9ED8D0433C8B73E6D85BEC99BD904875F3DCE,
	SceneTargetsManager_UnregisterTargetable_m6920B5077FE1737BD7329C6EF08C6582EA8BD3FF,
	SceneTargetsManager_EnsureTargetableCollectionExists_m85EC7DC4D6E8270A838E45C0E8703B143AF40AD1,
	NULL,
	SceneTargetsManager_EnsureSeekerCollectionExists_m7886304047DA6FD15F0E55EF9B997D00900C7CCA,
	NULL,
	SceneTargetsManager_TargetsInRange_m5B38EA6B22E400EA44D73716E89BF46EE7E7CC19,
	SceneTargetsManager_Init_mAA870A7135DD14340CDF973F60F9A3E489E84FB1,
	SceneTargetsManager_Update_mBF62AC05EF175E80EA5671C63213C8B37CB261AD,
	SceneTargetsManager_CopyRemoves_m64509D8C197E907DC00CF5DF1574A0CFAA54F463,
	SceneTargetsManager_DetermineAddAndRemove_mF36B590D264A9B6AA520979A5EDDB91A2A44763F,
	SceneTargetsManager_DoAdd_m934638D5781910248DE6F663AB70B126A60A7BDE,
	SceneTargetsManager_DoRemove_mD97929EC8D9D53D482B01DE41B456B6C8CE16B09,
	SceneTargetsManager_ConstructNewPairs_m80FD8749388F603DD817FA3A16F75229AA1390BA,
	SceneTargetsManager__ctor_m986D922344DD2D298AEC09393E235C270A0700C9,
	NULL,
	U3CU3Ec__cctor_m0CB314D3B905F012D16A138267880C9DD58B2912,
	U3CU3Ec__ctor_mE76C80CAC0C4DEE04753D9437DA213A056E2CDF8,
	U3CU3Ec_U3CConstructNewPairsU3Eb__28_1_mA67196287BDEBA7C9B3C0B6F05E4119A92CBE75C,
	NULL,
	NULL,
	U3CU3Ec__DisplayClass28_0__ctor_mA9EC5649E34B14CB5CB91ABBC1C1A996F8BAB262,
	U3CU3Ec__DisplayClass28_0_U3CConstructNewPairsU3Eb__0_m2681A45BE7527FFCD5B729180C4F94690B578362,
	U3CTargetsInRangeU3Ed__18__ctor_m5F965EB95862AC41A0736BA02D2151E8C580512C,
	U3CTargetsInRangeU3Ed__18_System_IDisposable_Dispose_m448ECC2390AF1169724F81889721A0C01B2780F9,
	U3CTargetsInRangeU3Ed__18_MoveNext_mCBAF7FF2CECA89E79DB876739B45391ED8FE2889,
	U3CTargetsInRangeU3Ed__18_U3CU3Em__Finally1_m7EBF00C2F8270C66129AD3FB75D3B13345CF070D,
	U3CTargetsInRangeU3Ed__18_System_Collections_Generic_IEnumeratorU3CPolyperfect_War_ITargetableU3E_get_Current_m38E981215A379735A534AEE41E1853FD3AA501C1,
	U3CTargetsInRangeU3Ed__18_System_Collections_IEnumerator_Reset_m6ECC9508627FFE0D4F36CB0720217068F2961DBE,
	U3CTargetsInRangeU3Ed__18_System_Collections_IEnumerator_get_Current_m58CAA7747AA578832C7A0FBBD6AD68EE1E93F25A,
	U3CTargetsInRangeU3Ed__18_System_Collections_Generic_IEnumerableU3CPolyperfect_War_ITargetableU3E_GetEnumerator_mBBC0C531B5A9722795DD8C508C379A75D678352C,
	U3CTargetsInRangeU3Ed__18_System_Collections_IEnumerable_GetEnumerator_mC67943BACDD21ADB1D15C7A0CE237254A1D980FD,
	SoldierAnimationManager_get___Usage_mF2AEEB6945EC16EAE4E1265C54563D28311DD498,
	SoldierAnimationManager_get_IsDoingSomethingWithHands_m396969B616096B2067A3BA5F26B6AD4CA187778F,
	SoldierAnimationManager_Awake_m9C12D5D8F225B8A26508B2FAFF70DF4DC83E577B,
	SoldierAnimationManager_HandleJump_mB4CCF814F5D64ED8C52619268228C2AFB3D01D43,
	SoldierAnimationManager_HandleWeaponShoot_m34930D4CBA94E888A074669D1145B158430A5EC5,
	SoldierAnimationManager_HandleWeaponChange_m6C42C1FE73B1E1B3FBEAECF86BF1D259FD2F5907,
	SoldierAnimationManager_Start_mE5F58B8B2E3E6C2F555EE990C3C90F1AB0461CAA,
	SoldierAnimationManager_Update_m1BA025C1169AADC4C1C7EA374CCCCFC169FE985F,
	SoldierAnimationManager__ctor_mEB14B7A6A1CE6AB572CF49311E059CE0E6AB244C,
	SoldierAnimationManager_U3CStartU3Eb__22_0_mFEF04BCBD96E570D04A7EF71A44364BC41BE9851,
	SoldierAnimationManager_U3CStartU3Eb__22_1_m1F3021A18F0CD7A600C863D487A3B6151AD11566,
	CarMovement_get___Usage_m8F4649EED6EC51AE48447E889979E97B11854DBA,
	CarMovement_Awake_m719DC8D893FAC2B786A7BC6C959DA7CCC0361940,
	CarMovement_HandleChangeLights_m662FAAAE113B39BF7099377509FDD48EFC6B2CD1,
	CarMovement_Start_m9A58DC97476260FD412F495FECE798788A97F95C,
	CarMovement_ApplyLocalPositionToVisuals_mE78C45C3A60401533BA5D557C58D0E5A23C9EB38,
	CarMovement_FixedUpdate_m8526AE63F12A5F292282676329164D5C7D9CB31F,
	CarMovement_StopInteracting_mBD166C71769EA5EFBF133779582E11CA70EE2AB9,
	CarMovement__ctor_m504E7B25D421AD6526ACCE5B889BFBAA1E52C4AE,
	Axle__ctor_m984E29D66BE390682E3C5035FC9C5C8196ED0D92,
	Lights__ctor_m29EAD7DC8BDB80CFDCC5067D88D2E1CDA4E4FD07,
	U3CU3Ec__DisplayClass15_0__ctor_mD6FCC2B4D338D05FC833F37896C773633935F8D9,
	U3CU3Ec__DisplayClass15_0_U3CAwakeU3Eb__0_m31080A2FAFB4205E431FF46D744773A04F8078BA,
	ParachuteMovement_get___Usage_m99C0DF94207BE318CDDF4CFE299AEAB2F57B0C49,
	ParachuteMovement_Start_m7F6D6B2F4593C100A4676528FB2ADCCAA562A03B,
	ParachuteMovement_FixedUpdate_m0878729B82E5F51E193D5B7327C341E54AD466D4,
	ParachuteMovement__ctor_m66143B15452AF67205E35F675DD9A9C6EE270AFA,
	ParachuteMovement_U3CStartU3Eb__8_0_mE80533EB3EBAE260E54EDE18AC796FADE3E746C9,
	Wheel__ctor_mA571E17BFBF49C2C089C4EB3C87E267757FB29D6,
	Wing__ctor_mCE93B445F006FC0AB3CD7A410CAD58F630CD873E,
	PlaneMovement_get___Usage_mDD20F5272BF09F9624E8E7B2011FE528C3D7E617,
	PlaneMovement_Awake_mA5F3C1D422966E593111D2B941637EA74664124A,
	PlaneMovement_HandleExit_mBB5A9D5BB90724A321D89E0FDE3F2DA9C11EE633,
	PlaneMovement_HandleEnter_m05263C4C83DA4D610979F1C690C4916F486FDDB8,
	PlaneMovement_Start_mCCF9CDAF26C040DB5D6461399EFF9CCB385A01E4,
	PlaneMovement_Update_mA2F70EC714A3C4A21E6ED11C3EC40CA7EE4A113E,
	PlaneMovement_HandleLandingGear_m372B7A49B31A09DBAE62FE1AF0C9D28380C76980,
	PlaneMovement_FixedUpdate_m3849FFC875C3B1147350D54CAE369837BA14B7A9,
	PlaneMovement_CalculateAerodynamicForce_mAF7AB05C182F2285EC739DCF868EC4CE212F7EBF,
	PlaneMovement_ApplyLocalPositionToVisuals_mA38F84B515A6CE96CA10DCEC33DB4C03D8AF3D7D,
	PlaneMovement_Sound_mB21D2AC30591E83FAE4E65C4D4C6AAD1EFAEEC9E,
	PlaneMovement_GroundCheck_m36260FAA427A5C41663918ED042FB03C71657BE3,
	PlaneMovement_WaitForWheelsDown_m881ADE1D47C47F4D1BE1D5F2A184B5F3B72BEB4F,
	PlaneMovement__ctor_m504C411C2074BF71AB38C0FA5BB3E0B7D1DC2B84,
	U3CWaitForWheelsDownU3Ed__63__ctor_m1E05CDF883D5281CAF8EBED24507A998291604D3,
	U3CWaitForWheelsDownU3Ed__63_System_IDisposable_Dispose_m22C8EAB020C441BC49D408226675434DA9855A71,
	U3CWaitForWheelsDownU3Ed__63_MoveNext_mCCBB4B17A516CD083AE801EE11B1A878789E3C55,
	U3CWaitForWheelsDownU3Ed__63_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mF16E6F4A5DB54CFD904D713206AE396E591CCB33,
	U3CWaitForWheelsDownU3Ed__63_System_Collections_IEnumerator_Reset_m40C492D264A1FB0AAA4AEBFB2BBE69220D2EEAAE,
	U3CWaitForWheelsDownU3Ed__63_System_Collections_IEnumerator_get_Current_mF0FA3882CF2B5E280C41623E53395663FEDCFAF5,
	TankWheel__ctor_m3C9C7842FA2A1124F5C949CD4863F422B22BBC8C,
	CosmeticWheel__ctor_mFD3AE96B61010E3CB27AF845DE1F21FC0419F377,
	TankMovement_get___Usage_m9EC0277FEC490311F6F57C48EBC8B230167999E1,
	TankMovement_get_canGetOut_m0EE3021D1D65C26B02C07D200AA23F2C81B3B413,
	TankMovement_Awake_m199709ACDF4F1B8592082BDA99950E5583E355F5,
	TankMovement_Start_m00E2F3CB96A48ED2C439D9EF93C78244AB0AD9A8,
	TankMovement_Update_mAD109D4E583783162D0C547A6620AA5F3F7BF606,
	TankMovement_FixedUpdate_mA8CE06E1AB4DCC0CF21557FE78B4037187C2BCCA,
	TankMovement_GetOutOfTank_m4D2FC029C6B76E5318F0574A542650761A2E6B3C,
	TankMovement_ApplyLocalPositionToVisuals_mDF6890FC55F89BB8E677B0C09361B3E6A228A566,
	TankMovement_Sound_m061DEFD17EF700F2400B60EA9565E1037DA82997,
	TankMovement__ctor_m269C7DF78733D7E27A1C610AFBBE0D21636C7F1C,
	TankMovement_U3CAwakeU3Eb__25_0_m75BC5BB54A50E6CEAF3AA0E55DD771EC47096309,
	TankMovement_U3CAwakeU3Eb__25_1_m633ABAD1E1B2F36108056E7C440FE9E8D101521A,
	TankMovement_U3CAwakeU3Eb__25_2_mC0EF78FFF988DFEE8B5A972A6371A04F00F45C35,
	AudioParams__ctor_m4738F44774B4C54A1046152EED76E4C32FD1F630,
	AmmoBox_Pickup_get___Usage_m98FCEDA9403518BEB9C598A0838F9ED001827A87,
	AmmoBox_Pickup_Initialize_m9BD75EEBDDDC051233A9554582673D5350EDDDFD,
	AmmoBox_Pickup__ctor_mB617457F9D144C866C755216598F9FD384CAED54,
	Ammo_PickupCollector_Awake_mA3FFED954FBA741CEE8671A533EE46A88BBF0788,
	Ammo_PickupCollector_PickUp_m1D1726AEDBC0C20B89081BF0362D960F74F05C5A,
	Ammo_PickupCollector_CanPickup_m9597DB086137368C5AEB7C734EF9F75CDD1FD95B,
	Ammo_PickupCollector_get___Usage_mDC14FFF5B7D11B37F55AC76ED14D5724F0D59E68,
	Ammo_PickupCollector__ctor_mA69C0513C7B816CF3CC1FAF88ABA7AB762661F1A,
	Medkit_Pickup_get_IsInstant_m8F9E7DC365532858B852B699D63B1A90DFD8D98D,
	Medkit_Pickup_get___Usage_m0572C66ABB9C185C134CF00014204297D281EEE6,
	Medkit_Pickup_Initialize_m408B2F1DDCB91D8FBFA5D3994F0B520B6F8E7C38,
	Medkit_Pickup__ctor_mF694F6EAF81D7E3AE27CDCB8D70EF9BF3D13CA9E,
	Medkit_PickupCollector_Awake_m483BC0010ED393013923F238E59E6D00F1C49A60,
	Medkit_PickupCollector_PickUp_m7097623954ABFE32907616196A16E1EFA645D209,
	Medkit_PickupCollector_CanPickup_m4A58DCE97303C61B9D05F76F78E625B163045488,
	Medkit_PickupCollector_get___Usage_m94C333A181C9FA4F5877182E9145EF4CF2BCBF6E,
	Medkit_PickupCollector__ctor_mB1820681491905FD4910B4F797BB89710AF4FB87,
	Usable_Pickup_get___Usage_mB3EE3016C31ABD8EA6680BFEE0FEF5FEBB77AE4E,
	Usable_Pickup_Initialize_m0345E39946EAEF8B9D7F2BE53AE1CB24D799E476,
	Usable_Pickup__ctor_m5F8A82E034C8DDF70FB2C514B07B0BE387F5CE22,
	Usable_PickupCollector_Awake_mFCDCB88802357A54EA485CE49E785541A382EC38,
	Usable_PickupCollector_PickUp_mE1E755BDDD5DF036A8A727EBAF24DB4DB20844D6,
	Usable_PickupCollector_CanPickup_m5C278E6F6A45A16D92F8D342427ADEF435E73A5C,
	Usable_PickupCollector_get___Usage_m7A398457F01FB8CD635682663CB5D76B8737BF56,
	Usable_PickupCollector__ctor_m50C4E7FC0E2AA248AE24B77936C9BA6077E5A9F8,
	AmmoReservoir_Proxy_get___Usage_m04FB41B0897F84048C07CA80101F258566D93D46,
	AmmoReservoir_Proxy_get_Ammo_m0941C9067BE408437B9AC525CC00F1145C42480E,
	AmmoReservoir_Proxy_set_Ammo_mA97CE1388E67AF308381CFCA94D79A883EECE9F6,
	AmmoReservoir_Proxy_Awake_m1BB493B4A7904EBFD28B4332AABE57164AD85737,
	AmmoReservoir_Proxy_HandleUsableChange_m075C3BCE898A3AC6D69B0E8C95D0215914D072D5,
	AmmoReservoir_Proxy_HandleAmmoChange_mED7F13612229428ABCCA533862E99346B80091B5,
	AmmoReservoir_Proxy_UpdateAmmo_m4C3104FE52298EADBA409CF8B53FC16BBBEF60A9,
	AmmoReservoir_Proxy__ctor_m7D4DAC343FB0DEE9479B0E191DD677C5ABC5F1D9,
	AnimatorIKCallback_Forwarder_RegisterIKCallback_mB02F4F527B7345B067CA5EB29B7A5773F6AF755B,
	AnimatorIKCallback_Forwarder_UnregisterIKCallback_mBD2D8FBC4F0C33951562D4D2FD1BF054DCA5EA12,
	AnimatorIKCallback_Forwarder_OnAnimatorIK_m0D8082997897FAA3B544D24AB31696355116AB57,
	AnimatorIKCallback_Forwarder__ctor_m2C6DFAAE5ED6B6630FAD3B9C0C40B2DE6FDE1BF7,
	U3CU3Ec__cctor_mA984C9B89C784CA1B2D7DBE23B43860CBE34BDB1,
	U3CU3Ec__ctor_mB47DAB6D80C497A5A6F18A8E3832C96C9ADD5267,
	U3CU3Ec_U3C_ctorU3Eb__4_0_mB33A79FB8985C89BC9554CA5454CE6DE97078048,
	Animator_Proxy_get___Usage_m45C1CE35381397AEBD510BB08455C44C0271DB8F,
	Animator_Proxy_get_Animator_mC6B532ADEF79648EA2CB8354E62EC12E4DF9BD85,
	Animator_Proxy_set_Animator_m720B03E281DD9ACD072276AC0DD0DF9EF7A84802,
	Animator_Proxy_Awake_m31ACF1C1F9168CDB357C4148FD0A2188426E1FF8,
	Animator_Proxy_RegisterIKCallback_m0D3EE40C3650ECE5F7E0A7623C5483BDA3E2AB95,
	Animator_Proxy_UnregisterIKCallback_m06FA4169ACB865A4C3057D351419CA1B9BFA845C,
	Animator_Proxy_OnEnable_mDFC3461F782086663808C564EAC301CF522434CD,
	Animator_Proxy_OnDisable_mE8421B759FCFBD5AB18E5DF157CAF2DFEE72F545,
	Animator_Proxy__ctor_m5F3393476588EDBB0F2D0AF9C67136DB41ED0D40,
	UseContext_Holder_get___Usage_mDC8C9C175725F0DA820C50273F7B0EF30C2CE813,
	UseContext_Holder_get_Context_m0D7898E7239760520883712F17B388C6686FDF93,
	UseContext_Holder_set_Context_m15EC3D6AFD59C081CD15A7D1C1F9B56E33783DF3,
	UseContext_Holder_Receive_m6A490077D9AF66FC4439BB3562DE3F0CBBB50C50,
	UseContext_Holder__ctor_m693719C8377154B36E219B60D9C5D84DF223B396,
	Ammo_Receiver_get___Usage_m033499F9D830E66A14043193435B581519177A2C,
	Ammo_Receiver_RegisterInsertConsumerCallback_m8BA439CFA193F25B636DE1556D7DF6762C059D5D,
	Ammo_Receiver_UnregisterInsertConsumerCallback_m3C0AF379885027EFDD4D1E44C9710126E0CB9711,
	Ammo_Receiver_RegisterExtractConsumerCallback_mD6C0F7D644B92F749298475513DFC00F92903C77,
	Ammo_Receiver_UnregisterExtractConsumerCallback_m05C17F4846154CEDDAA363DA320CEB4DD83D6767,
	Ammo_Receiver_InsertPossible_m64704A11E30167F4D8ECA2F9776D2EC75CE89D09,
	Ammo_Receiver_ExtractAllAmmo_mB1B39204C2A086653B35D0BB445F913E8F40837A,
	Ammo_Receiver__ctor_mD8CDB9DA8ECA284EB48EA41BA9586D94CDBC1D66,
	U3CU3Ec__cctor_mD24D3BB089EB5634F8BBC4D484F3E1C756A68928,
	U3CU3Ec__ctor_m267A2CA6C88F1C71543070E078D4369D604D894F,
	U3CU3Ec_U3CExtractAllAmmoU3Eb__9_0_m756D47AED9B12AF2EBA668A92CEB6BA3C10193DF,
	AnimationEvent_Receiver_Awake_m1E55AF9CDF535D713FE61AA0C780ADC8B98151BB,
	AnimationEvent_Receiver_SetTrigger_mB85AB91B15223C8C3EDE369330FC4C8F725057DF,
	AnimationEvent_Receiver_SetBool_mF88BB0B75AFA356D413B9CA50EE93B87A51BEBA3,
	AnimationEvent_Receiver_SetTrue_mA8CC876555A61F651ADAB5D052FA8F95BB409F3A,
	AnimationEvent_Receiver_SetFalse_m5F6907B835A80EEC9A530EED2C5FC441CD6FB5B8,
	AnimationEvent_Receiver__ctor_mDDF6843346BB1DBCC57A7BF5EA6CCA5FAC619236,
	Damage_Receiver_get___Usage_m61476756CC0FB128998615DF819FA4A1491977BB,
	Damage_Receiver_RegisterDamageReceivedCallback_mB705CDBA22B834E56E2CC31744A8856EB038AF5E,
	Damage_Receiver_UnregisterDamageReceivedCallback_mFE449327BD55FA465D68D7EA755CA602527BD4DF,
	Damage_Receiver_TakeDamage_m83C3AF9CC42512953A3EC1ECBF836B4C3F84C755,
	Damage_Receiver_TakeDamage_mB5878FCA8C466643AEF4309AF81C3365E78D3817,
	Damage_Receiver__ctor_m33E8748D50134B37158AC8913D759640DE27B587,
	Ammo_Reservoir_get___Usage_m460FDEDD54829DC67BE7B126067DDD3A91F0A3CC,
	Ammo_Reservoir_get_AmmoType_mB08B6033710B345723B9D5C4C873FF4462CCC37B,
	Ammo_Reservoir_Awake_mA670E818A2ECC550F5E28A2D2D1B25F3C2C2EE4B,
	Ammo_Reservoir_OnEnable_mF1460C84F4763BF14E39293E6D85DC5F1F8EF460,
	Ammo_Reservoir_OnDisable_mBD46A5291F337F9D95FAB854133361E5F91D9D0C,
	Ammo_Reservoir_ReceiveAndOutputRemainder_m5751ED44C7194902812D8D35A7CAB8B61BEC4839,
	Ammo_Reservoir_ExtractAll_m561A0EC90FF4E5274E0858F239E3280E5434C133,
	Ammo_Reservoir_Reset_m7019E15070CB0C47D841B98DFEEB3AF822816512,
	Ammo_Reservoir__ctor_mF7A0A2997522B818AE143F5794E93A3B082E0D0C,
	Energy_Reservoir_get___Usage_m254E4DD8F72CBD186E430F19B4610091B2EA3BE2,
	Energy_Reservoir_Update_mD343A22E28DE2C4740A14962357AB077306456AA,
	Energy_Reservoir_TrySetEnergy_m25B91E218EAACF9F4A87A3AAAC1CC25FDC526813,
	Energy_Reservoir__ctor_m1E62281D72627E980AB2D70C0D04DB2A25673E5A,
	Health_Reservoir_get___Usage_mDB857E15E02B7206FDE7CD66AD6FB2686D33C224,
	Health_Reservoir_get_MaxHealth_mD3CD57EF2F85BF71E56E993EDC7C8C3D5B347106,
	Health_Reservoir_get_HealthFraction_mCED3DFE1A5B8F30C9A16351CB94994FFED23E897,
	Health_Reservoir_get_IsDead_m9C9456E81A5BA3328E1CA023615935AB1BA086C5,
	Health_Reservoir_get_IsAlive_mE56A77EC3B62C14FD5993931FCE707F454569897,
	Health_Reservoir_Reset_m76CFC31998402D70D720DF007DBB03311ADE3E5B,
	Health_Reservoir_get_Health_m8CF7495EA588EA4184C245298DFDB49BA78B4987,
	Health_Reservoir_set_Health_m4095815491674E03F5261A012461797AB7488EF0,
	Health_Reservoir_Start_m41A2DA321D91591E2F11E962B892D48CDEF88132,
	Health_Reservoir_Update_m192BBB1EB36C81219BA54285A02D68BD671380B0,
	Health_Reservoir_HandleRespawn_mAA321F735917C3B9A8C147582410BB75831DAA19,
	Health_Reservoir_HandleRevive_m6653A0BC211185617FB0028927C0004CBB811820,
	Health_Reservoir_HandleDeath_mB89523B6DBF26CD5BFC0FF4F84809C1FA50C59D1,
	Health_Reservoir_ReceiveDamage_mB87DEBE2C0B949782E523E70F43719E8EF1D49AA,
	Health_Reservoir_RegisterDeathCallback_m3A18BBD4660D3F2FCF08332A4D405F716E168B65,
	Health_Reservoir_UnregisterDeathCallback_m141D4636B46EFE0FEB05E24027EFAEC56E9D5C40,
	Health_Reservoir_RegisterReviveCallback_mE2AC0A9D006B33A93D7936B7AB14FD686C4BC970,
	Health_Reservoir_UnregisterReviveCallback_m452CABD820D64A46F07CF60038FECB0F7A40EA3E,
	Health_Reservoir_UseMedkit_mEB9986EC0B77761017A6C05E400925CEA6F53ACE,
	Health_Reservoir_StopHealRoutines_m9201955C0DC623CA820DC41B8F7DBD44135D0CA0,
	Health_Reservoir_HealOverTime_mFF40CAC78563D604A5392FF0211617EE7300C650,
	Health_Reservoir__ctor_m88729C8B2DF907C580AF9242BBFB7A5D8D9A1A6B,
	Health_Reservoir_U3CStartU3Eb__16_0_m6137C2EDE538D98F9971200F089ACA6DCB755447,
	U3CHealOverTimeU3Ed__29__ctor_mB4F3DB3418939FD7257B0F22278D9531CC3FA2C4,
	U3CHealOverTimeU3Ed__29_System_IDisposable_Dispose_mC54ADB94A150820213254FC664A87A170928864B,
	U3CHealOverTimeU3Ed__29_MoveNext_mF2C2C8A7B6FD68737247ED237468611F3B0B16FE,
	U3CHealOverTimeU3Ed__29_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mD41D0E968DDE5DB9E66CB28DC5098E8FAB352821,
	U3CHealOverTimeU3Ed__29_System_Collections_IEnumerator_Reset_mF69EB1A5AD86B2DC3CE74D22200D1A4515DA9618,
	U3CHealOverTimeU3Ed__29_System_Collections_IEnumerator_get_Current_mD79A89D05F7B30A5703913CBAFB7B56B5BB6E042,
	Int_Reservoir_TryUse_m5BD9166C4851323AC87B16F21112FC2E6B80454E,
	Int_Reservoir_get_FractionalAmount_m890134F7B8A3D704225469DD37508F1EE186123C,
	Int_Reservoir_doExtractPossible_m053BDC283CD6BF1ADBC9B827BEEA8789D76217EC,
	Int_Reservoir_CanFullyExtract_m14193D273495C9914E8E6B206F487E7C21A54174,
	Int_Reservoir_IsAtMax_m9A9D0EA8E1E9F4396CA7038AE83F9ABF47F63E70,
	Int_Reservoir_doInsertPossible_m1EDAFB7CED253B1BC4723A742F424799CAE0E168,
	Int_Reservoir_CanFullyInsert_m5E4A55C2B47B274B0D39E17CCCBBD482228B6CA8,
	Int_Reservoir__ctor_mC5C2BD2B0F6354EC545527BD0DA23B39542CCBA0,
	AnimationEvent_Sender_get___Usage_m886DB640AF3C8B8DA1EF4935A8FBEFC0D1F75663,
	AnimationEvent_Sender_SetParamName_mFF2313FA9F9F53DF5459976FBB0DC047C8EE5B84,
	AnimationEvent_Sender_SendSetTrue_mCD1949A7ABFB58D0A19A99A054CA21FF07492044,
	AnimationEvent_Sender_SendSetFalse_m35B30C5A768FB884989144C8152A12D78A02D48A,
	AnimationEvent_Sender_SendSetTrigger_m08CE4033A784D30E9C108F201F775CF4CF2788DD,
	AnimationEvent_Sender_EnsureReceiver_m2D03736097E6095DEFC2F3BEA187085846759CDE,
	AnimationEvent_Sender__ctor_m84A3A1AA3F4ADECE4933C8EBC2ABC7F132A6CF3E,
	AreaOfEffect_Damage_Sender_get___Usage_mD187F894A41FFE9EE9AAD8825ED11ADF4A941736,
	AreaOfEffect_Damage_Sender_Start_m280972DB7B441CBD4A674D10F8A8D448AB013CA8,
	AreaOfEffect_Damage_Sender_SendDamage_m1B27EA81DB396A7FBA5BB6DEE6795CFCEBE8DFAB,
	AreaOfEffect_Damage_Sender__ctor_m008E25DF8E4992567F02FC756FD9164A15204E2C,
	ContextDrivenAreaOfEffect_Damage_Sender_get___Usage_mCD9AA3692539530B9CEF1775594E714B939E3E25,
	ContextDrivenAreaOfEffect_Damage_Sender_Receive_m6BB566B3DD2364205949EDE8B7287F83EBC083DA,
	ContextDrivenAreaOfEffect_Damage_Sender_OnDrawGizmosSelected_m4A33F079713102E04CB7884FCB9BBED1F3DB58D8,
	ContextDrivenAreaOfEffect_Damage_Sender__ctor_m874A6E9F3D4FD45E6DD15C85E3287A0DD3F0D432,
	OnCollide_Damage_Sender_get___Usage_m6657B2C59698E38F34343CB293A1A9B1DABD4493,
	OnCollide_Damage_Sender_OnCollisionEnter_m58B79AE55A9285A3DBEC887DB10768A61F048D2D,
	OnCollide_Damage_Sender__ctor_m19C1AABB10D7A25209DF6935A609FFF5BF255F5A,
	Ally_Tracker_Start_m3F48025E287677F9F4FA8EDF930B8126CC79D071,
	Ally_Tracker_get___Usage_m4A2014965BAA91DFC1C2F1A2388E3F4F10E16438,
	Ally_Tracker_ShouldTrack_mDA84B6B9851F647722695C3F23DC9B6D5F5F4F9B,
	Ally_Tracker__ctor_m5B61991A4E42DD142BF8A75D71368B492748E4D2,
	Commander_Target_get___Usage_m78869208EAA759E363C1769EFC33C664322A8BC3,
	Commander_Target_get_Faction_m0FDAE38D1225AFC5156DEBF5F55EEAAD2F766C84,
	Commander_Target_Initialize_m872EA829BE2B55334A9B91E39BF7455F6A962647,
	Commander_Target__ctor_m9799C1A7BA11FD26ABC9C31FE28BBAD7600CBC8D,
	Commander_Tracker_get___Usage_m03081D94D4D5F01E5963EDB14F37DA8E3ADDED1F,
	Commander_Tracker_Awake_m08BD5B9D6E3EE3D56C27196B2D1602123379D070,
	Commander_Tracker_ShouldTrack_m74697BC078465A872B421DDF545F692CFB7ED485,
	Commander_Tracker__ctor_mA683684DD115DC641A178A2BA0B58C3E3E67CA27,
	Faction_Target_get_AttachedFaction_m63D45D470F271795A883EB0F97941E568C92F4E5,
	Faction_Target_set_AttachedFaction_m762E6CB3B0586ED56E80FF5FD268C40B6E4625BA,
	Faction_Target_get___Usage_m404268F865F7A171DFFB4325979074C4640708BF,
	Faction_Target_Initialize_m32571503DAC8CF019EA1933E4317CD80F3C45028,
	Faction_Target__ctor_mAF93580B4D67CB1E79A1563CC3BE8238A0AC90A0,
	Hostile_Tracker_get___Usage_mFCA8AA8B2B4F3C8009E6B0C201350472B2303D17,
	Hostile_Tracker_Awake_m2DD8EF78D69368C1BA37D74BA413F2B185CFFD1F,
	Hostile_Tracker_ShouldTrack_mCA08EF00833E49C716B53CDAB4706DBCAE630B98,
	Hostile_Tracker__ctor_m719561F682229595E58100ECCAF62FE9293F5027,
	Medkit_Tracker_get___Usage_m0BB0BFDAFAB3EFB0947FEB18E89FB55FE301226A,
	Medkit_Tracker_ShouldTrack_m48D4BE65110EBA007FEF14CC1F8134CC9A91C183,
	Medkit_Tracker_Awake_mCDF0EA45E999FB3D90C6D62ACAEC49554CF4DF4F,
	Medkit_Tracker__ctor_m16A8895F7EE6FA597EE3295B8E1C9CA5AC9B8BAB,
	Objective_Target_get___Usage_mDE8D8D55101FEC59E5CC8527C8F61060847FCF7F,
	Objective_Target_IsObjectiveForFaction_m34DD6C5CF070821827DD88359942802DB2DAD823,
	Objective_Target_Initialize_m4AA0D2F8FA1EF68DC851B08D485B19140063E297,
	Objective_Target__ctor_mC957012D96CA62818D81EF14CFCDC04872FC08E2,
	Objective_Tracker_get___Usage_mB3A89E8D30EBE5B7A90DD533DCF2510E1BA51951,
	Objective_Tracker_Awake_m0204B484E531432CD2F176E1528DB399C6C13173,
	Objective_Tracker_ShouldTrack_m79AC50F5203E33F22E829A38C77203597816059D,
	Objective_Tracker__ctor_m76798210A4FCF676201798CFC7CF9918CCCC1CC0,
	Shootable_Target_get___Usage_m98978836796D2BC54B345441B14B56C42C79E487,
	Shootable_Target_get_AttachedHealth_m0C7143F66DE2936A6B3D8CD2241F947DB3392A06,
	Shootable_Target_set_AttachedHealth_mB12AD832FCF92571C3FA31B259B486F38217BFF7,
	Shootable_Target_Initialize_m4FAB781F3CB444F7DA20648405C8CFFE5946C8F5,
	Shootable_Target__ctor_m21AB54F581350E7941C83D967FBB5084A577E6D1,
	SupportedAmmoPack_Tracker_get___Usage_m88D07C2CCAE098CE7D178789BF5F0CA7912A3D75,
	SupportedAmmoPack_Tracker_Awake_mCD0F78F643EA7093554AD89258C98E616C6551F3,
	SupportedAmmoPack_Tracker_ShouldTrack_mEDFCBFE587F9B3C418DE3965DAB7C98789B91BBA,
	SupportedAmmoPack_Tracker__ctor_mC66EA2E7AB703D4CE4A32492B2A356D388C8C422,
	Supported_UsablePickup_Tracker_Awake_mF74DB7404F5327E350FEE8D6117AAA8E2340145E,
	Supported_UsablePickup_Tracker_get___Usage_m7C0CD60A9544F61A3210A75204C686FAEA63640A,
	Supported_UsablePickup_Tracker_ShouldTrack_m215E6BD3519A3D2091B7B606F68496935DE37AA3,
	Supported_UsablePickup_Tracker__ctor_m3C6939668C829C88DFA26E2A5A27405D68E30284,
	AmmoText_get___Usage_mE5325C5F8AB72D3D964E69BFAFAE2AA34331289C,
	AmmoText_Initialize_m702CC50A1C91C881DB950EC3809CE72E238DE051,
	AmmoText_HandleValueChange_m6F09BB613A596AEF3ED89682DCF1A14266A5890A,
	AmmoText__ctor_m1D173F6C3139698C1BC264771C80D6ACD59F5B16,
	FadeOnRespawn_get___Usage_m70D6A7AD03041F850164CF8B4C94BDB910BE5CA9,
	FadeOnRespawn_Start_m1E492F787F82F17127CBF77A3CD83567C7D7FC0B,
	FadeOnRespawn_HandleRespawn_m7376A484581AC8AFD0782BB80748930B61673453,
	FadeOnRespawn_HandleDeath_m78E1F54F59E5493252B4B3EC1BF9D517F111D3D9,
	FadeOnRespawn__ctor_m0B9FA463D6220EABFEA754109F41FBFE3F070F2B,
	U3CU3Ec__cctor_mDAA5B5E0C7B3ED3A287A671C4B60CD7F1CA7C92E,
	U3CU3Ec__ctor_m9CED193E9B58B5BEE6816FC3536CA3AECF7E6CD8,
	U3CU3Ec_U3CHandleRespawnU3Eb__4_0_mD4FF6C3FD70F46C6E0F1990AE4627292574F5E0D,
	U3CU3Ec_U3CHandleDeathU3Eb__5_0_m5A08A861D791D2D9682384F4B4061D37A6A2BC5C,
	NULL,
	FailureConditionMonitor_Start_m00FFFC124D4ADD913A0389FB7F3B4CFFDAB5079E,
	FailureConditionMonitor__ctor_mBB4B87F816E5C871231CEFCC8B7031DC3F527006,
	FailureConditionMonitor_U3CStartU3Eb__2_0_mAC34E67A625A64740DC73E433A61A6A2F58D2A00,
	FillByFraction_get___Usage_m3DD3F26C7C29803015CC3DCBB3D36FDA440FA9C7,
	FillByFraction_get_SourceAsFraction_m222AA17A51425B4E09185784EE7CEC222FED5D1B,
	FillByFraction_Awake_mC15B647F8F15E97FCB793B59E0CAE45A0EB3A4C5,
	FillByFraction_OnValidate_mFB14CD1652013E18856452022C28407821EBE8B1,
	FillByFraction_Update_m2879EEDC057248E141CD09CDC107C56516B5A0AC,
	FillByFraction_UpdateFill_mEC0D12DFF42F3F701B4B406ABEFF7ECCD1C19031,
	FillByFraction__ctor_mE8BBA822073B980BC58A9013DF4E74640B1AABF9,
	GraphicFader_get___Usage_m1FAB322A485704BF45235308F3036B1BB2A2004E,
	GraphicFader_Awake_m1379F8E02E934523C49A173FCF43C7FFE5192D6B,
	GraphicFader_Fadeout_mC3F53C5219284AEFDFD4D52F736D4D10EE6DD699,
	GraphicFader_Fadeout_m5357C3F89EFB59307E23BC216F634CCD4104A15B,
	GraphicFader_FadeIn_m6A0C2E59F7A89DF4D44213131A3DB7D0099CDC9E,
	GraphicFader_FadeTo_mC294F04401D910207977D0C14BF3A97C9DDD6947,
	GraphicFader_FadeTo_m06B31D0E503ADFEBF9717A08A95C748001DDBD5B,
	GraphicFader_FadeFor_m73B11984C6CD35A764B8B69F192736B1EDF42FAA,
	GraphicFader_FadeForCoroutine_m689A9520F1CBDB5763EFEEDB0A527CD4E4091A0C,
	GraphicFader__ctor_mBF30F02C075B65933A3BCB23A359451088666ED5,
	GraphicFader_U3CFadeoutU3Eb__5_0_m8663DB97117789A2E4C76BB664BE6ECADBD88A5C,
	GraphicFader_U3CFadeForCoroutineU3Eb__11_1_m4D605F1BED403F9D5B02768AFB4F7F859B2C6BDE,
	U3CU3Ec__cctor_mF01B4E2401AA69392F965F993264327325F2CD95,
	U3CU3Ec__ctor_mF46D6A4FA94A19E37D62F5B282AA4A00520548B4,
	U3CU3Ec_U3CFadeInU3Eb__7_0_mC15C8105AACF146D60CEC6D33BFD77A8505F8E84,
	U3CU3Ec_U3CFadeForCoroutineU3Eb__11_0_mA58B22E650BFE83BB666DF34C02980F9C943DBA5,
	U3CU3Ec__DisplayClass8_0__ctor_m5609593759E8EC1A08DA40ACBD6C14B95ADD3593,
	U3CU3Ec__DisplayClass8_0_U3CFadeToU3Eb__0_mD6E066948910C10C953B2A83D371DF3111604E5D,
	U3CU3Ec__DisplayClass9_0__ctor_m4D290AADB34AE140BD94CCA5908478381504B30A,
	U3CU3Ec__DisplayClass9_0_U3CFadeToU3Eb__0_mCEE00E9BF6265EB8D9EDEF2136F454584BCF74D0,
	U3CFadeForCoroutineU3Ed__11__ctor_m77C8B44B00B5A1FF3DE627D70098851A589589C2,
	U3CFadeForCoroutineU3Ed__11_System_IDisposable_Dispose_m5F9DB6C218546669BF0AD7326A17BC05AF906D72,
	U3CFadeForCoroutineU3Ed__11_MoveNext_m924692DB9840938DA7C8A914D830B1E9064EEAFA,
	U3CFadeForCoroutineU3Ed__11_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mE682A1E9EF81DDEAB1495B51CAB9D76D34C04031,
	U3CFadeForCoroutineU3Ed__11_System_Collections_IEnumerator_Reset_m42AC008D5F22E11D47EBD0E98E613E1717BBCB2D,
	U3CFadeForCoroutineU3Ed__11_System_Collections_IEnumerator_get_Current_m08BF13B30A5DE7FECBEC44991B203E6039037D62,
	HealthBar_get___Usage_mAB2866A2F287759A5A1F778D2C8BC79B325BC96E,
	HealthBar_Initialize_m985C715DFC69E9DB795D5263D244DBEBCD0BEB6D,
	HealthBar_HandleValueChange_mF5005CBB006351CFD3B2B2A3131B13E3804CBF70,
	HealthBar__ctor_m496F76F61BFAE1354DFFA863635440500A41CCA4,
	HealthImageSwap_get___Usage_mA1A20C9CA612CFDFDB31A1F96D01E15ECCCDCAC5,
	HealthImageSwap_OnValidate_m8D8637BFD4DB74413D9BA52CEA28F71FADC76CFA,
	HealthImageSwap_Initialize_mE0E00FA3AB4459BD86FB7238E75582702E82CA18,
	HealthImageSwap_HandleValueChange_m2169B116D474E1D543737A8D1EFD7DDD560FE959,
	HealthImageSwap_GetSpriteForValue_mF552F31166A9ED8325BF364F2AA759DE4356E441,
	HealthImageSwap__ctor_mC08B5078A7CF544D59AC8EE51CDF4E025C8FCA7C,
	U3CU3Ec__cctor_m6075FB1DE996C4D75D21C823175F33912518E4A4,
	U3CU3Ec__ctor_mE4880F5BE2D8B4AC43BE66205683D9297A477388,
	U3CU3Ec_U3COnValidateU3Eb__5_0_m014EA6DD29991B9C1CD8900E1B4A1F91B5E72842,
	HealthText_get___Usage_m4D3EC1E17EDF13F9132259E53C3C6EFA6550A44A,
	HealthText_Initialize_mEBCD43B4C83E35AC8D40693B81F90B43E1F92525,
	HealthText_HandleValueChange_mC1DF77637D1E26C0BCC34BFC6F78BFFA48501CEF,
	HealthText__ctor_mEB1764B8F6374C25A733B4DB8673A62542CCA949,
	InsufficientAmmo_Monitor_get___Usage_m5F519CD32E6EBB596B950E56B4418EFB4DD82706,
	InsufficientAmmo_Monitor_ConditionMet_m7158A667274D052CFE4D1A22F16E540516E05319,
	InsufficientAmmo_Monitor__ctor_mAD5F69146E8130844AA318D459E68CFA0DE61FC8,
	NoReplacementAmmo_Monitor_get___Usage_m030ADC7C0C141A71CD907133C3EDF82B23AE28BD,
	NoReplacementAmmo_Monitor_ConditionMet_m5DD64887784C5EABE023AE29DD623C1679AC8BC9,
	NoReplacementAmmo_Monitor__ctor_m2BC42C2BA6D8F8CA4451AE4B65EF31CFFCFE56BB,
	ScreenSpaceReticle_get___Usage_mD13EBAC0D80DD57A39F9C69795E16C0BDE395E86,
	ScreenSpaceReticle_Awake_mAADE2EC200FEB8FE4BE5CD0A5E8D9D73F3EF4CA4,
	ScreenSpaceReticle_FixedUpdate_m020F127C965BC75C76EBF6D955C4927AE0D6CBC7,
	ScreenSpaceReticle__ctor_mE3807D255CBFF86E13D5F95569601ABD78B6E103,
	SpeedText_get___Usage_m0FCC4DA56B5ED75E9614A12F6228BF452344B7A4,
	SpeedText_Start_m270056ADCB7CE70CF5EF6BECC09E37751144FDB6,
	SpeedText_Update_m0980EBB3DE949DADCC1FAE2D7E9439197FDB6C1F,
	SpeedText__ctor_m4B1A6F79A903BF978E1595DA2D02375DCACF3502,
};
extern void ActiveAmmoInfo__ctor_m48E79771AFD56B76BCD19F4F7D646D3544257205_AdjustorThunk (void);
extern void AmmoInfo__ctor_m27F80CC108BB8674130B5D351F3465868AA0C866_AdjustorThunk (void);
extern void AmmoInfo_ToString_mDBC79047138F45541D775EB88C6353639557AFA0_AdjustorThunk (void);
extern void DamageContext_get_DamageAmount_mDC75FCA4EAB0A5D6310D1C79F9D7020BD1DF9178_AdjustorThunk (void);
extern void DamageContext_set_DamageAmount_mF57E0C5506BF63EC24FC0E1040A8366186709C39_AdjustorThunk (void);
extern void DamageContext_get_ImpactVector_m3D9419597E7BCEE7A687BDF216963DD78C6DC09A_AdjustorThunk (void);
extern void DamageContext_set_ImpactVector_m1F515B5CBE319400CC9EA03C103DE6BC352CF184_AdjustorThunk (void);
extern void DamageContext_get_ImpactPosition_m31758F4C02F236C7F43E46D09878595E62E21F42_AdjustorThunk (void);
extern void DamageContext_set_ImpactPosition_m355D3107835CA3563DD8B3B8DE90C11FB7901D49_AdjustorThunk (void);
extern void DamageContext_get_UseContext_mEB2C77D316A4A8E6E283B2DFFCADB456DCA73AB2_AdjustorThunk (void);
extern void DamageContext_set_UseContext_m9BBC8D7263A614690B85EE792A8D5CC556E5B3CF_AdjustorThunk (void);
extern void DamageContext__ctor_mB9CAD3BEC2A23085A7E301DBCDBF74E94D5DCBAE_AdjustorThunk (void);
extern void IKWeight__ctor_m0F3AB7A526C47D0F2CD32E24040C70811B6E7D55_AdjustorThunk (void);
extern void TrackerTargetPair__ctor_m2CB4D3D378338810F9E706F55F1C2166B8FD93FB_AdjustorThunk (void);
extern void TrackerTargetPair_GetHashCode_mA24496B03EB3793907D91ED250BC930A7306BB5D_AdjustorThunk (void);
extern void UseFailedContext__ctor_m39058652313C86F641E0491C7BA37193707908B4_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[16] = 
{
	{ 0x060002B0, ActiveAmmoInfo__ctor_m48E79771AFD56B76BCD19F4F7D646D3544257205_AdjustorThunk },
	{ 0x060002B1, AmmoInfo__ctor_m27F80CC108BB8674130B5D351F3465868AA0C866_AdjustorThunk },
	{ 0x060002B2, AmmoInfo_ToString_mDBC79047138F45541D775EB88C6353639557AFA0_AdjustorThunk },
	{ 0x060002B8, DamageContext_get_DamageAmount_mDC75FCA4EAB0A5D6310D1C79F9D7020BD1DF9178_AdjustorThunk },
	{ 0x060002B9, DamageContext_set_DamageAmount_mF57E0C5506BF63EC24FC0E1040A8366186709C39_AdjustorThunk },
	{ 0x060002BA, DamageContext_get_ImpactVector_m3D9419597E7BCEE7A687BDF216963DD78C6DC09A_AdjustorThunk },
	{ 0x060002BB, DamageContext_set_ImpactVector_m1F515B5CBE319400CC9EA03C103DE6BC352CF184_AdjustorThunk },
	{ 0x060002BC, DamageContext_get_ImpactPosition_m31758F4C02F236C7F43E46D09878595E62E21F42_AdjustorThunk },
	{ 0x060002BD, DamageContext_set_ImpactPosition_m355D3107835CA3563DD8B3B8DE90C11FB7901D49_AdjustorThunk },
	{ 0x060002BE, DamageContext_get_UseContext_mEB2C77D316A4A8E6E283B2DFFCADB456DCA73AB2_AdjustorThunk },
	{ 0x060002BF, DamageContext_set_UseContext_m9BBC8D7263A614690B85EE792A8D5CC556E5B3CF_AdjustorThunk },
	{ 0x060002C0, DamageContext__ctor_mB9CAD3BEC2A23085A7E301DBCDBF74E94D5DCBAE_AdjustorThunk },
	{ 0x060002C2, IKWeight__ctor_m0F3AB7A526C47D0F2CD32E24040C70811B6E7D55_AdjustorThunk },
	{ 0x060002E1, TrackerTargetPair__ctor_m2CB4D3D378338810F9E706F55F1C2166B8FD93FB_AdjustorThunk },
	{ 0x060002E2, TrackerTargetPair_GetHashCode_mA24496B03EB3793907D91ED250BC930A7306BB5D_AdjustorThunk },
	{ 0x060002E5, UseFailedContext__ctor_m39058652313C86F641E0491C7BA37193707908B4_AdjustorThunk },
};
static const int32_t s_InvokerIndices[1290] = 
{
	17499,
	21355,
	13298,
	9276,
	13298,
	10629,
	13298,
	12815,
	13052,
	13298,
	13052,
	21392,
	13298,
	13052,
	13298,
	3489,
	10682,
	13298,
	21355,
	13052,
	13298,
	10682,
	13298,
	13052,
	13052,
	13298,
	13298,
	13298,
	13298,
	13298,
	10629,
	13280,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13052,
	10682,
	13298,
	13298,
	13298,
	0,
	13298,
	13052,
	12815,
	13298,
	13298,
	13298,
	3489,
	10682,
	10682,
	10682,
	10682,
	10682,
	13195,
	13298,
	21355,
	13052,
	13298,
	10682,
	13298,
	13298,
	10682,
	13052,
	13298,
	3489,
	10682,
	13298,
	21355,
	13052,
	10682,
	10682,
	13280,
	0,
	13298,
	13298,
	10682,
	10682,
	10682,
	10682,
	13298,
	13052,
	13298,
	10682,
	10682,
	13298,
	13298,
	21355,
	13298,
	9272,
	7736,
	13052,
	13052,
	13052,
	10682,
	10682,
	13298,
	10682,
	13298,
	9272,
	9272,
	13298,
	13052,
	13298,
	13298,
	13298,
	10497,
	13298,
	13052,
	13052,
	13298,
	10682,
	13298,
	13052,
	13298,
	13298,
	13280,
	13298,
	13052,
	13298,
	13298,
	10682,
	10682,
	10682,
	13298,
	13052,
	13298,
	13298,
	13298,
	13052,
	13298,
	13298,
	13052,
	12815,
	13298,
	13052,
	13298,
	13298,
	10682,
	10682,
	13298,
	13052,
	13298,
	3489,
	13298,
	13298,
	10682,
	9272,
	10682,
	10682,
	10682,
	10682,
	10682,
	10682,
	13298,
	21355,
	10629,
	13298,
	12815,
	13052,
	13298,
	13052,
	13052,
	13298,
	13298,
	13298,
	13052,
	10682,
	10682,
	13298,
	13298,
	13052,
	13298,
	3489,
	10682,
	13298,
	21355,
	13052,
	13298,
	3489,
	10682,
	13298,
	21355,
	13052,
	12815,
	13298,
	13052,
	12815,
	12815,
	13280,
	10912,
	13280,
	13298,
	13298,
	13298,
	13298,
	10682,
	10682,
	13298,
	13298,
	13298,
	13298,
	13298,
	10682,
	10682,
	13052,
	13298,
	13298,
	9705,
	9272,
	13298,
	21355,
	13298,
	7736,
	13052,
	13052,
	13298,
	10682,
	10682,
	10682,
	13298,
	13052,
	13298,
	10682,
	10682,
	13298,
	21355,
	13298,
	7736,
	13052,
	13280,
	10912,
	12815,
	10442,
	13298,
	13298,
	13298,
	13298,
	9604,
	12815,
	2908,
	7906,
	10682,
	13298,
	21355,
	13298,
	13298,
	13298,
	13298,
	13052,
	13298,
	13298,
	13298,
	13298,
	13298,
	13280,
	13298,
	13052,
	13298,
	13298,
	13298,
	5688,
	5688,
	5688,
	5688,
	3489,
	7736,
	3500,
	7736,
	3489,
	10682,
	10682,
	13298,
	21355,
	21355,
	13298,
	9301,
	7736,
	13298,
	7736,
	13052,
	13298,
	10682,
	13052,
	10682,
	10682,
	10682,
	10682,
	3500,
	13298,
	7736,
	13298,
	7736,
	0,
	13298,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	13052,
	10682,
	13298,
	9705,
	10912,
	10702,
	13298,
	10682,
	0,
	10682,
	10682,
	10682,
	10682,
	3500,
	13298,
	7822,
	13195,
	9460,
	7822,
	9460,
	7822,
	13298,
	0,
	13298,
	13195,
	0,
	0,
	0,
	0,
	0,
	12815,
	10442,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	12815,
	12815,
	13298,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	17472,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	13298,
	10682,
	13298,
	10682,
	0,
	0,
	0,
	0,
	13298,
	0,
	0,
	0,
	13298,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	13052,
	12815,
	13298,
	13298,
	13298,
	13298,
	13298,
	12996,
	10682,
	10682,
	0,
	0,
	10682,
	10682,
	10682,
	10682,
	13298,
	21355,
	21355,
	13298,
	7736,
	7736,
	0,
	0,
	0,
	0,
	13298,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	13298,
	13298,
	13298,
	13298,
	0,
	13298,
	0,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	0,
	13298,
	0,
	13298,
	13298,
	13298,
	13052,
	13052,
	10682,
	13298,
	13298,
	12815,
	10442,
	12815,
	10442,
	13298,
	21355,
	13298,
	7736,
	12996,
	13052,
	13298,
	7736,
	7736,
	13298,
	13052,
	13052,
	9267,
	13052,
	13298,
	13298,
	13298,
	13298,
	5681,
	5681,
	13298,
	13298,
	13298,
	21355,
	13298,
	9233,
	9457,
	13052,
	13052,
	13298,
	13298,
	13195,
	9457,
	13298,
	13298,
	10497,
	13298,
	21355,
	21355,
	13298,
	9453,
	13052,
	12996,
	13298,
	13195,
	13298,
	13298,
	13298,
	13298,
	13052,
	12996,
	13298,
	13298,
	13195,
	13298,
	13298,
	13298,
	13298,
	21355,
	13298,
	9457,
	9457,
	13298,
	9457,
	12996,
	13052,
	13298,
	13052,
	12996,
	13298,
	13052,
	12996,
	13298,
	13195,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13052,
	12996,
	13298,
	13298,
	13195,
	13298,
	13052,
	13298,
	13298,
	13298,
	10629,
	13298,
	12815,
	13052,
	13298,
	13052,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	12996,
	13052,
	13298,
	13195,
	13298,
	13298,
	13298,
	13298,
	13280,
	10912,
	13298,
	0,
	13298,
	13298,
	13298,
	13298,
	13052,
	13052,
	12996,
	13298,
	13195,
	13298,
	13298,
	13298,
	13280,
	13298,
	12996,
	13052,
	13298,
	13298,
	12996,
	13052,
	13195,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	10823,
	9276,
	13298,
	13298,
	13298,
	10629,
	13298,
	12815,
	13052,
	13298,
	13052,
	13052,
	13052,
	13298,
	7736,
	8845,
	3498,
	10682,
	4159,
	13298,
	21355,
	13298,
	7071,
	9232,
	13052,
	13052,
	13298,
	10682,
	2817,
	13298,
	10682,
	10682,
	10682,
	10682,
	7736,
	7736,
	13298,
	13052,
	13298,
	13052,
	13298,
	10682,
	13052,
	13298,
	13298,
	10442,
	13298,
	7736,
	10682,
	10682,
	10629,
	10682,
	10682,
	11400,
	13052,
	13298,
	9700,
	13298,
	13298,
	2735,
	5681,
	13052,
	13298,
	0,
	13052,
	10682,
	13298,
	13195,
	10823,
	13280,
	10912,
	13280,
	10912,
	13052,
	10682,
	1984,
	13298,
	2865,
	21355,
	13052,
	10682,
	13052,
	10682,
	5684,
	13298,
	4489,
	10682,
	12815,
	10442,
	12815,
	10442,
	10682,
	10682,
	10682,
	10682,
	13298,
	21355,
	13298,
	13298,
	13298,
	13275,
	10907,
	13280,
	10912,
	5839,
	13298,
	2707,
	2817,
	5688,
	12996,
	13298,
	5688,
	5688,
	13298,
	21355,
	14823,
	21355,
	0,
	0,
	15481,
	16451,
	20515,
	10629,
	13298,
	12815,
	13298,
	13298,
	13052,
	13298,
	13052,
	13052,
	13052,
	13052,
	13298,
	13298,
	13052,
	13298,
	10629,
	13298,
	12815,
	13052,
	13298,
	13052,
	13052,
	13298,
	13298,
	13298,
	13298,
	13298,
	13052,
	10682,
	10682,
	10682,
	13298,
	13052,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	21350,
	13298,
	13298,
	13298,
	13298,
	13052,
	13298,
	13298,
	13052,
	13298,
	13298,
	13052,
	13298,
	13298,
	13052,
	13298,
	10629,
	13298,
	12815,
	13052,
	13298,
	13052,
	13052,
	13298,
	13298,
	13298,
	13298,
	13298,
	13052,
	13052,
	13052,
	13298,
	5843,
	4514,
	10823,
	10682,
	9272,
	13298,
	13052,
	10682,
	10682,
	10682,
	7736,
	13298,
	15468,
	16444,
	10629,
	13298,
	12815,
	13052,
	13298,
	13052,
	13052,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	10682,
	13298,
	13298,
	9276,
	13298,
	10682,
	13298,
	13298,
	21355,
	13298,
	7736,
	10629,
	13298,
	12815,
	13052,
	13298,
	13052,
	13298,
	9276,
	13298,
	10629,
	13298,
	12815,
	13052,
	13298,
	13052,
	0,
	9595,
	9598,
	13298,
	21274,
	13298,
	2414,
	2817,
	10682,
	13298,
	13298,
	2774,
	14485,
	16952,
	10629,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13052,
	13298,
	13298,
	13298,
	13052,
	7822,
	13298,
	13298,
	10629,
	13298,
	12815,
	13052,
	13298,
	13052,
	13052,
	21274,
	20847,
	20847,
	20847,
	0,
	20871,
	0,
	2443,
	21355,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	0,
	21355,
	13298,
	7876,
	0,
	0,
	13298,
	9532,
	10629,
	13298,
	12815,
	13298,
	13052,
	13298,
	13052,
	13052,
	13052,
	13052,
	12815,
	13298,
	13298,
	10682,
	9705,
	13298,
	13298,
	13298,
	13298,
	13298,
	13052,
	13298,
	13298,
	13298,
	10682,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	10682,
	13052,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13052,
	13298,
	10682,
	10682,
	13298,
	13298,
	13298,
	13298,
	10682,
	10682,
	13298,
	7822,
	13052,
	13298,
	10629,
	13298,
	12815,
	13052,
	13298,
	13052,
	13298,
	13298,
	13052,
	12815,
	13298,
	13298,
	13298,
	13298,
	13298,
	10682,
	13298,
	13298,
	10682,
	10682,
	10682,
	13298,
	13052,
	13298,
	13298,
	13298,
	10682,
	7736,
	13052,
	13298,
	12815,
	13052,
	13298,
	13298,
	13298,
	10682,
	7736,
	13052,
	13298,
	13052,
	13298,
	13298,
	13298,
	10682,
	7736,
	13052,
	13298,
	13052,
	12793,
	10416,
	13298,
	9705,
	9702,
	13298,
	13298,
	10682,
	10682,
	10629,
	13298,
	21355,
	13298,
	10629,
	13052,
	13052,
	10682,
	13298,
	10682,
	10682,
	13298,
	13298,
	13298,
	13052,
	13052,
	10682,
	10682,
	13298,
	13052,
	10682,
	10682,
	10682,
	10682,
	6817,
	13052,
	13298,
	21355,
	13298,
	6819,
	13298,
	10682,
	5666,
	10682,
	10682,
	13298,
	13052,
	10682,
	10682,
	10823,
	10497,
	13298,
	13052,
	13052,
	13298,
	13298,
	13298,
	8702,
	12794,
	13298,
	13298,
	13052,
	13298,
	10823,
	13298,
	13052,
	13195,
	13195,
	12815,
	12815,
	13298,
	13195,
	10823,
	13298,
	13298,
	13298,
	13298,
	13298,
	10497,
	10682,
	10682,
	10682,
	10682,
	10682,
	13298,
	2421,
	13298,
	9704,
	10629,
	13298,
	12815,
	13052,
	13298,
	13052,
	7685,
	13195,
	8801,
	7685,
	12815,
	8801,
	7685,
	13298,
	13052,
	10682,
	10682,
	10682,
	10682,
	3489,
	13298,
	13052,
	13298,
	13298,
	13298,
	13052,
	10682,
	13298,
	13298,
	13052,
	10682,
	13298,
	13298,
	13052,
	7736,
	13298,
	13052,
	13052,
	13298,
	13298,
	13052,
	13298,
	7736,
	13298,
	13052,
	10682,
	13052,
	13298,
	13298,
	13052,
	13298,
	7736,
	13298,
	13052,
	7736,
	13298,
	13298,
	13052,
	7736,
	13298,
	13298,
	13052,
	13298,
	7736,
	13298,
	13052,
	13052,
	10682,
	13298,
	13298,
	13052,
	13298,
	7736,
	13298,
	13298,
	13052,
	7736,
	13298,
	13052,
	13298,
	9701,
	13298,
	13052,
	13298,
	13298,
	13298,
	13298,
	21355,
	13298,
	13298,
	13298,
	0,
	13298,
	13298,
	10899,
	13052,
	13052,
	13298,
	13298,
	13298,
	13298,
	13298,
	13052,
	13298,
	10823,
	5783,
	10823,
	2557,
	2864,
	10823,
	9276,
	13298,
	13298,
	13298,
	21355,
	13298,
	13298,
	13298,
	13298,
	10823,
	13298,
	10823,
	10629,
	13298,
	12815,
	13052,
	13298,
	13052,
	13052,
	13298,
	9704,
	13298,
	13052,
	13298,
	13298,
	9704,
	9276,
	13298,
	21355,
	13298,
	9460,
	13052,
	13298,
	9704,
	13298,
	13052,
	7898,
	13298,
	13052,
	7898,
	13298,
	13052,
	13298,
	13298,
	13298,
	13052,
	13298,
	13298,
	13298,
};
static const Il2CppTokenRangePair s_rgctxIndices[15] = 
{
	{ 0x02000033, { 0, 12 } },
	{ 0x02000034, { 12, 24 } },
	{ 0x0200004D, { 36, 5 } },
	{ 0x02000050, { 41, 11 } },
	{ 0x02000054, { 52, 16 } },
	{ 0x0200006B, { 68, 14 } },
	{ 0x0200006C, { 82, 16 } },
	{ 0x0200006D, { 98, 2 } },
	{ 0x0200007E, { 100, 2 } },
	{ 0x020000BE, { 116, 2 } },
	{ 0x060002EA, { 102, 2 } },
	{ 0x060002EB, { 104, 3 } },
	{ 0x0600039C, { 107, 2 } },
	{ 0x0600039E, { 109, 5 } },
	{ 0x060003A8, { 114, 2 } },
};
extern const uint32_t g_rgctx_T_tB31AC15AC2C84A46443BA3482B3D991882A705B9;
extern const uint32_t g_rgctx_ChangeableBase_1_tC064C060F1499DAB412E2BFDB9CAC9A4F163367F;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_tB31AC15AC2C84A46443BA3482B3D991882A705B9_Object_Equals_m07105C4585D3FE204F2A80D58523D001DC43F63B;
extern const uint32_t g_rgctx_ChangeEvent_1__ctor_mE2C84B5ADF61BC4E5C6E76EA1CEC49AF60B9632E;
extern const uint32_t g_rgctx_ChangeEvent_1_t9A3160E94023893C77C0E0B3D3255E974D0C0399;
extern const uint32_t g_rgctx_UnityEvent_1_t5F033BAC11BAD854E094AABD69CBC354C3706A63;
extern const uint32_t g_rgctx_UnityEvent_1_Invoke_m7181B37F295B7454932D924BC00B7A5D9D52AAF5;
extern const uint32_t g_rgctx_ChangeEvent_1_t9A3160E94023893C77C0E0B3D3255E974D0C0399;
extern const uint32_t g_rgctx_UnityAction_1_t5E9A84782FC3F35D4CD30AFCBCB7F9748A3489B0;
extern const uint32_t g_rgctx_UnityEvent_1_AddListener_m1488629D4E7241099BC365538803734BAE301627;
extern const uint32_t g_rgctx_UnityEvent_1_RemoveListener_m330FD5A3B4A75F2E64D6F1617A3188BCFBEFB37F;
extern const uint32_t g_rgctx_UnityEvent_1__ctor_mE12A1A9E6B7E29AC9AFEC5AC387478D7D19C7D6C;
extern const uint32_t g_rgctx_ChangeableBase_1_t17A30F48E0D27F8B2744AC8B0F40C716CB450624;
extern const uint32_t g_rgctx_C_t25C3048407065C03C06D5D03250AD88374A66538;
extern const uint32_t g_rgctx_ChangeableBase_1_TryChangeTo_m91F8080B8AB95F7F454D64706E0455B3D495021A;
extern const uint32_t g_rgctx_ChangeableWatcher_2_get_target_m64941C3AEE58D1CC18E688DCE32F6C3ADF78E1D0;
extern const uint32_t g_rgctx_GameObject_GetComponentInParent_TisC_t25C3048407065C03C06D5D03250AD88374A66538_m0A59FAF8E36066AF42845899525108B55AEE3F06;
extern const uint32_t g_rgctx_ChangeableWatcher_2_set_target_mFCD6A82FB10F74F00A16F5E359D3B13ED3D1DC08;
extern const uint32_t g_rgctx_C_t25C3048407065C03C06D5D03250AD88374A66538;
extern const uint32_t g_rgctx_ChangeableWatcher_2_t372D697DB17A73EDDF6CD3D5CA2915E5E8BF20F9;
extern const uint32_t g_rgctx_ChangeableWatcher_2_Initialize_m412072FE391BEE7DE9BF6E9CD34FDEE9BDAE5FFA;
extern const uint32_t g_rgctx_ChangeableWatcher_2_HandleTargetChanged_mDA90F3CAE3DD1FB414DF1F2F14D3B3C85D3E19A0;
extern const uint32_t g_rgctx_UnityAction_1_t8FA7FC09C3F8415B4177B42ED8333AA6490FE0F5;
extern const uint32_t g_rgctx_UnityAction_1__ctor_m84391DCB9FB391195B5405FACB4FBD70E9D6DAE7;
extern const uint32_t g_rgctx_ChangeableBase_1_RegisterChangeCallback_m6230E5C24C07143013E95EE3B0335A8C2B226935;
extern const uint32_t g_rgctx_IChangeable_1_tE900F1AC6E50246D42AA52346AB9C7025678FF6B;
extern const uint32_t g_rgctx_IChangeable_1_GetInitializingEvent_m20832BEF1AE6E826AA354BD4749E030F0FEF66F2;
extern const uint32_t g_rgctx_ChangeEvent_1_tEFA9E76D56C48B49B4646EB76F4F905C7E3F2382;
extern const uint32_t g_rgctx_ChangeableWatcher_2_HandleValueChange_m2C042245023395350451AB9EA9A0D1333EF1A90D;
extern const uint32_t g_rgctx_UnityAction_1_t96419286C42BBBB3059846D993DA7ACD70269443;
extern const uint32_t g_rgctx_UnityAction_1__ctor_m82A6FA8E1A4CB69BA27A638E456092DF635B0F5B;
extern const uint32_t g_rgctx_IChangeable_1_RegisterChangeCallback_m2FFCCB7984A196178B9504101BC95C5D737C1B79;
extern const uint32_t g_rgctx_IChangeable_1_UnregisterChangeCallback_m532C38370FF896EAFEAF33CA3D147C8A0CBD510C;
extern const uint32_t g_rgctx_ChangeEvent_1_tBA37D3DB84DF0E9717EF53D770F0CD79D4F3A65A;
extern const uint32_t g_rgctx_ChangeableBase_1__ctor_m7AB1BB9B0B80B15859B2B386F826E199EFA13277;
extern const uint32_t g_rgctx_ChangeableBase_1_t17A30F48E0D27F8B2744AC8B0F40C716CB450624;
extern const uint32_t g_rgctx_Component_GetComponent_TisT_t1DFCDBB0AA210B6B3997DA6AA628F3EF4774C108_m8E0E360114462B4F36F6051238689566B754E96B;
extern const uint32_t g_rgctx_T_t1DFCDBB0AA210B6B3997DA6AA628F3EF4774C108;
extern const uint32_t g_rgctx_PickupCollector_1_t1CCBD7B89FC032E7F515234CFF5B5C753C70CF94;
extern const uint32_t g_rgctx_PickupCollector_1_CanPickup_m8475ED43765FF93B6F35CACE0476E5C030D48A1A;
extern const uint32_t g_rgctx_PickupCollector_1_PickUp_mFE1AFA1652D7ABE86CC80F67C52ACC153857879C;
extern const uint32_t g_rgctx_Reservoir_1_t7C3C201C930DDE6A0AFFC1AEC32C61779328F712;
extern const uint32_t g_rgctx_T_tF321D8FC5373FDC2D35F1BD541B7D512DCEFAB90;
extern const uint32_t g_rgctx_Reservoir_1_doExtractPossible_m5882907EA24715C9A9EEAFBA3A9771DFCEDF51D0;
extern const uint32_t g_rgctx_Reservoir_1_doInsertPossible_mA44B4E79E5836385A493EF4E7AB148F048980BEB;
extern const uint32_t g_rgctx_UnityEvent_1_tB13850BFD618743D67967A387E2BAB2058D6FC80;
extern const uint32_t g_rgctx_ChangeEvent_1_t99D1D2B2B8717A4989BEE54AD4818342ED9DA256;
extern const uint32_t g_rgctx_ChangeEvent_1__ctor_mCF34FD96B62C79DE05767B0C981EC1F8F2502F8D;
extern const uint32_t g_rgctx_UnityEvent_1_Invoke_m831F5E7F84925D12ACEFC46D87133B6E344658B1;
extern const uint32_t g_rgctx_UnityAction_1_t9FC18F9F7C54C3730DCF5C87C44988CCF2283359;
extern const uint32_t g_rgctx_UnityEvent_1_AddListener_m1648B3A793ACDAE3B2275D78A8B61FE6E40A89F4;
extern const uint32_t g_rgctx_UnityEvent_1_RemoveListener_m2C70AB866859415E62B1F248F74672626F7CF050;
extern const uint32_t g_rgctx_Tracker_1_tFE7C99B20BB8E095BA1ED06B9992F758D9662C76;
extern const uint32_t g_rgctx_HashSet_1_t6756989A1F95A81FACE37354277AB3B250BE2716;
extern const uint32_t g_rgctx_T_t5692A1239A1C34F18318442EEAFB45784CCFCEC2;
extern const uint32_t g_rgctx_Tracker_1_ShouldTrack_m7BDE2BE92CAF0AE5D835A3E02FB3D567730EC8D2;
extern const uint32_t g_rgctx_SceneTargetsManager_RegisterSeeker_TisT_t5692A1239A1C34F18318442EEAFB45784CCFCEC2_m14853D26585CDD3A81081B67A5DA9DD236A3D911;
extern const uint32_t g_rgctx_SceneTargetsManager_UnregisterSeeker_TisT_t5692A1239A1C34F18318442EEAFB45784CCFCEC2_m0C1899FEB5762285D53715114E245ACBEFC2F007;
extern const uint32_t g_rgctx_HashSet_1_Add_m8CC9C30247BC3B62501688ECC40D478690B210A8;
extern const uint32_t g_rgctx_UnityEvent_1_t26F64831D615BC202D5B461E5B588A9AC21F833C;
extern const uint32_t g_rgctx_UnityEvent_1_Invoke_m83C36DE0885FF5203BB909903DEAE82B6D07C44C;
extern const uint32_t g_rgctx_HashSet_1_Remove_m72E9AB042A9183CCC5DC91FB056D3BB9C8F535AA;
extern const uint32_t g_rgctx_UnityAction_1_t4753F078F72225CD5606FC8E0EEC037373B1030A;
extern const uint32_t g_rgctx_UnityEvent_1_AddListener_mA1B231BD3A7986AD370E095965286430F63281DF;
extern const uint32_t g_rgctx_UnityEvent_1_RemoveListener_mC790454ABB577C811EDE1623F704DF5C00AB331D;
extern const uint32_t g_rgctx_Tracker_1_get_GizmoColor_m1296687B25B657D9BD7020814BE9E12EF2ACBBB8;
extern const uint32_t g_rgctx_UnityEvent_1__ctor_mEC8A2379B16EBA10EC7BC7002D02CA6F37EBD256;
extern const uint32_t g_rgctx_HashSet_1__ctor_m3ABC661352F398F1D0FD1462F4485B6E62DD3EE4;
extern const uint32_t g_rgctx_SeekerBase_AI_2_Initialize_mA8696A42559589414B089709961677D450A526AF;
extern const uint32_t g_rgctx_Component_GetComponent_TisFractionalType_t08E4E05D84A70F0498596C284E6A0196F827F177_mA6DCDD03ACACC838BEDE60C5E7377A77DBE0F101;
extern const uint32_t g_rgctx_FractionalType_t08E4E05D84A70F0498596C284E6A0196F827F177;
extern const uint32_t g_rgctx_ResourceSeekerBase_AI_3_t481E283874B4202DD3410811AD985C7A1E263DC5;
extern const uint32_t g_rgctx_SeekerBase_AI_2_t2A3CFC98D8556C83209FE5FB56D2A7F28D116170;
extern const uint32_t g_rgctx_TrackerType_tA303452FBAD1231C7CDA847B17E0157B8A6F2AFC;
extern const uint32_t g_rgctx_Tracker_1_tB7101B61D378AC55A051D95EA5724879DB67923F;
extern const uint32_t g_rgctx_Tracker_1_get_Tracked_mA7C55EDD176CBCBF370B17BC6C9D9D328988F671;
extern const uint32_t g_rgctx_HashSet_1_t96CF935A0FBAFB094AE506557BE295119D697595;
extern const uint32_t g_rgctx_Enumerable_Any_TisTargetType_t157C4C0AB94E0C2213A136CFCC6EFCEC44699D7D_m488458F97159228276E8EED541A22B142CF2B9B6;
extern const uint32_t g_rgctx_IEnumerable_1_t60A77B6A8538EEA6C07280A4C3B5E6992B513A52;
extern const Il2CppRGCTXConstrainedData g_rgctx_FractionalType_t08E4E05D84A70F0498596C284E6A0196F827F177_IFractionalAmount_get_FractionalAmount_mDE437D730D0C011EB77298D62BA4CCECB29C19A2;
extern const uint32_t g_rgctx_SeekerBase_AI_2__ctor_mAAC3F8B6F6309D7F78B0F6E4DBE6A6506A369444;
extern const uint32_t g_rgctx_SeekerBase_AI_2_t2A3CFC98D8556C83209FE5FB56D2A7F28D116170;
extern const uint32_t g_rgctx_SeekerBase_AI_2_tA11E2140DB67288E134CCBFDF5972E4336F46A85;
extern const uint32_t g_rgctx_GameObject_AddComponent_TisTrackerType_t834C84C78EEAF48E47993FD3FBC0DF0076AFB517_mB80171BCFD40810A0D9D0BC37EE0572CC63187F0;
extern const uint32_t g_rgctx_TrackerType_t834C84C78EEAF48E47993FD3FBC0DF0076AFB517;
extern const uint32_t g_rgctx_TrackedType_t39A3676686CB876AFDA1EA8901B7209333ADA77D;
extern const uint32_t g_rgctx_Tracker_1_t94AA48D7BA0C5DAEA3D89C5580E07187AF750718;
extern const uint32_t g_rgctx_Tracker_1_get_Tracked_m5F6201606173061CB704822E9A41CA94B3B3F977;
extern const uint32_t g_rgctx_HashSet_1_t4FDB31EA87054F5F3C26AA779B53BDA0D8A23492;
extern const uint32_t g_rgctx_HashSet_1_Contains_m3A26E98D546BD9E8ED38820236162EE20D997B44;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass9_0_t784C78433EEC5E6D9702A5E1BFD53AFB5C880020;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass9_0__ctor_m383E3EB9F6F06C8B3E487E5711098A58D5F79C0C;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass9_0_U3CActiveUpdateU3Eb__0_m75AA8D0475EC55AA04A1F70A2B570EE9FB649F79;
extern const uint32_t g_rgctx_Func_2_t023807D49DF0685B33C6202623A31B1217B2A3DA;
extern const uint32_t g_rgctx_Func_2__ctor_m221A306A887F7E906639A74F5240E5D4CBD07DEA;
extern const uint32_t g_rgctx_IEnumerableExtensions_MinBy_TisTrackedType_t39A3676686CB876AFDA1EA8901B7209333ADA77D_m1FD6F309130D30CB87A4A2668CD9618A6229B877;
extern const uint32_t g_rgctx_IEnumerable_1_t316B9E7570C7BB3EF3FE976005B2400D6AD04AEC;
extern const uint32_t g_rgctx_Enumerable_Any_TisTrackedType_t39A3676686CB876AFDA1EA8901B7209333ADA77D_m0697E56F76A025E66836307B3FE6E264F4AF4ABD;
extern const uint32_t g_rgctx_TrackedType_t96DD65D5AD2DE7D8164A70584B98FAA1FE54A86B;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass9_0_tFDE7EAE1F4666010E0E05F9CC44481C6A8C95D6F;
extern const uint32_t g_rgctx_T_t28EF0709B82A9494F4DFE5D58BD130DB79C24EA5;
extern const uint32_t g_rgctx_ChangeEvent_1_t8446774F294227C9272FCA1184FA786A88B86E6A;
extern const uint32_t g_rgctx_GameObject_AddComponent_TisT_t3752DC1AD1DA25D282B9AD6AE4FC908C4E1D9A58_m4E6636A56CB1D68087659BB260B50C48BDE76E28;
extern const uint32_t g_rgctx_T_t3752DC1AD1DA25D282B9AD6AE4FC908C4E1D9A58;
extern const uint32_t g_rgctx_GameObject_GetComponent_TisT_t1FA047AED27D5AF8BBF0FBA78068DEE61E1EDE55_m4B82377C4DE82288C489C160BD3CD9D9D2D46F2B;
extern const uint32_t g_rgctx_T_t1FA047AED27D5AF8BBF0FBA78068DEE61E1EDE55;
extern const uint32_t g_rgctx_GameObject_AddComponent_TisT_t1FA047AED27D5AF8BBF0FBA78068DEE61E1EDE55_m09FA66028C674C199BF74574F427F8A9DA0673A1;
extern const uint32_t g_rgctx_Tracker_1_tA580543A2D2A7D4753DF77EAE1BDBF6BD482ABED;
extern const uint32_t g_rgctx_TrackerEntry_Create_TisT_t25A901CB62F3A004F3BC11DBB121D05E88E25895_m2EE06DCA30DB6384B1A5BB08EA34DC96031F2FA1;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass17_0_1_tE0FBDA1C72EA62E3E6B7BF109DAE2A7DD640965E;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass17_0_1__ctor_m63DD8A56443CF7950CBD29329B8ACAC683D643EA;
extern const uint32_t g_rgctx_Tracker_1_t2A2FED473242F0676850B2BC5FB380E60552A914;
extern const uint32_t g_rgctx_TrackerEntry_Create_TisT_t1FC1D853B1383C5F3F3771633FB65CCCD23755F0_mB76BE10C8C5B4A11AFC4FB3081B6538CDB1A6BFB;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass17_0_1_U3CUnregisterSeekerU3Eb__0_m3309796D82501CB7C866794CD6EAF32612C3BF76;
extern const uint32_t g_rgctx_Tracker_1_tB10F8450907C3A28786B0B8B34494C19C5A02291;
extern const uint32_t g_rgctx_T_tA57D6D3228821909FD720E2ABFC0861595F69EBB;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass17_0_1_t2BB4D60B11A38F65D85997F82DF5932BC2857529;
extern const uint32_t g_rgctx_Tracker_1_t3DD8E45806928FDAD5261AE5127B419CF8E4F795;
static const Il2CppRGCTXDefinition s_rgctxValues[118] = 
{
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tB31AC15AC2C84A46443BA3482B3D991882A705B9 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ChangeableBase_1_tC064C060F1499DAB412E2BFDB9CAC9A4F163367F },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_tB31AC15AC2C84A46443BA3482B3D991882A705B9_Object_Equals_m07105C4585D3FE204F2A80D58523D001DC43F63B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ChangeEvent_1__ctor_mE2C84B5ADF61BC4E5C6E76EA1CEC49AF60B9632E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ChangeEvent_1_t9A3160E94023893C77C0E0B3D3255E974D0C0399 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UnityEvent_1_t5F033BAC11BAD854E094AABD69CBC354C3706A63 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnityEvent_1_Invoke_m7181B37F295B7454932D924BC00B7A5D9D52AAF5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ChangeEvent_1_t9A3160E94023893C77C0E0B3D3255E974D0C0399 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UnityAction_1_t5E9A84782FC3F35D4CD30AFCBCB7F9748A3489B0 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnityEvent_1_AddListener_m1488629D4E7241099BC365538803734BAE301627 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnityEvent_1_RemoveListener_m330FD5A3B4A75F2E64D6F1617A3188BCFBEFB37F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnityEvent_1__ctor_mE12A1A9E6B7E29AC9AFEC5AC387478D7D19C7D6C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ChangeableBase_1_t17A30F48E0D27F8B2744AC8B0F40C716CB450624 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_C_t25C3048407065C03C06D5D03250AD88374A66538 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ChangeableBase_1_TryChangeTo_m91F8080B8AB95F7F454D64706E0455B3D495021A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ChangeableWatcher_2_get_target_m64941C3AEE58D1CC18E688DCE32F6C3ADF78E1D0 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_GameObject_GetComponentInParent_TisC_t25C3048407065C03C06D5D03250AD88374A66538_m0A59FAF8E36066AF42845899525108B55AEE3F06 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ChangeableWatcher_2_set_target_mFCD6A82FB10F74F00A16F5E359D3B13ED3D1DC08 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_C_t25C3048407065C03C06D5D03250AD88374A66538 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ChangeableWatcher_2_t372D697DB17A73EDDF6CD3D5CA2915E5E8BF20F9 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ChangeableWatcher_2_Initialize_m412072FE391BEE7DE9BF6E9CD34FDEE9BDAE5FFA },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ChangeableWatcher_2_HandleTargetChanged_mDA90F3CAE3DD1FB414DF1F2F14D3B3C85D3E19A0 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UnityAction_1_t8FA7FC09C3F8415B4177B42ED8333AA6490FE0F5 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnityAction_1__ctor_m84391DCB9FB391195B5405FACB4FBD70E9D6DAE7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ChangeableBase_1_RegisterChangeCallback_m6230E5C24C07143013E95EE3B0335A8C2B226935 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IChangeable_1_tE900F1AC6E50246D42AA52346AB9C7025678FF6B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IChangeable_1_GetInitializingEvent_m20832BEF1AE6E826AA354BD4749E030F0FEF66F2 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ChangeEvent_1_tEFA9E76D56C48B49B4646EB76F4F905C7E3F2382 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ChangeableWatcher_2_HandleValueChange_m2C042245023395350451AB9EA9A0D1333EF1A90D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UnityAction_1_t96419286C42BBBB3059846D993DA7ACD70269443 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnityAction_1__ctor_m82A6FA8E1A4CB69BA27A638E456092DF635B0F5B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IChangeable_1_RegisterChangeCallback_m2FFCCB7984A196178B9504101BC95C5D737C1B79 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IChangeable_1_UnregisterChangeCallback_m532C38370FF896EAFEAF33CA3D147C8A0CBD510C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ChangeEvent_1_tBA37D3DB84DF0E9717EF53D770F0CD79D4F3A65A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ChangeableBase_1__ctor_m7AB1BB9B0B80B15859B2B386F826E199EFA13277 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ChangeableBase_1_t17A30F48E0D27F8B2744AC8B0F40C716CB450624 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Component_GetComponent_TisT_t1DFCDBB0AA210B6B3997DA6AA628F3EF4774C108_m8E0E360114462B4F36F6051238689566B754E96B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t1DFCDBB0AA210B6B3997DA6AA628F3EF4774C108 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_PickupCollector_1_t1CCBD7B89FC032E7F515234CFF5B5C753C70CF94 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_PickupCollector_1_CanPickup_m8475ED43765FF93B6F35CACE0476E5C030D48A1A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_PickupCollector_1_PickUp_mFE1AFA1652D7ABE86CC80F67C52ACC153857879C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Reservoir_1_t7C3C201C930DDE6A0AFFC1AEC32C61779328F712 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tF321D8FC5373FDC2D35F1BD541B7D512DCEFAB90 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Reservoir_1_doExtractPossible_m5882907EA24715C9A9EEAFBA3A9771DFCEDF51D0 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Reservoir_1_doInsertPossible_mA44B4E79E5836385A493EF4E7AB148F048980BEB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UnityEvent_1_tB13850BFD618743D67967A387E2BAB2058D6FC80 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ChangeEvent_1_t99D1D2B2B8717A4989BEE54AD4818342ED9DA256 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ChangeEvent_1__ctor_mCF34FD96B62C79DE05767B0C981EC1F8F2502F8D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnityEvent_1_Invoke_m831F5E7F84925D12ACEFC46D87133B6E344658B1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UnityAction_1_t9FC18F9F7C54C3730DCF5C87C44988CCF2283359 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnityEvent_1_AddListener_m1648B3A793ACDAE3B2275D78A8B61FE6E40A89F4 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnityEvent_1_RemoveListener_m2C70AB866859415E62B1F248F74672626F7CF050 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Tracker_1_tFE7C99B20BB8E095BA1ED06B9992F758D9662C76 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_HashSet_1_t6756989A1F95A81FACE37354277AB3B250BE2716 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t5692A1239A1C34F18318442EEAFB45784CCFCEC2 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Tracker_1_ShouldTrack_m7BDE2BE92CAF0AE5D835A3E02FB3D567730EC8D2 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SceneTargetsManager_RegisterSeeker_TisT_t5692A1239A1C34F18318442EEAFB45784CCFCEC2_m14853D26585CDD3A81081B67A5DA9DD236A3D911 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SceneTargetsManager_UnregisterSeeker_TisT_t5692A1239A1C34F18318442EEAFB45784CCFCEC2_m0C1899FEB5762285D53715114E245ACBEFC2F007 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_HashSet_1_Add_m8CC9C30247BC3B62501688ECC40D478690B210A8 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UnityEvent_1_t26F64831D615BC202D5B461E5B588A9AC21F833C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnityEvent_1_Invoke_m83C36DE0885FF5203BB909903DEAE82B6D07C44C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_HashSet_1_Remove_m72E9AB042A9183CCC5DC91FB056D3BB9C8F535AA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UnityAction_1_t4753F078F72225CD5606FC8E0EEC037373B1030A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnityEvent_1_AddListener_mA1B231BD3A7986AD370E095965286430F63281DF },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnityEvent_1_RemoveListener_mC790454ABB577C811EDE1623F704DF5C00AB331D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Tracker_1_get_GizmoColor_m1296687B25B657D9BD7020814BE9E12EF2ACBBB8 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnityEvent_1__ctor_mEC8A2379B16EBA10EC7BC7002D02CA6F37EBD256 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_HashSet_1__ctor_m3ABC661352F398F1D0FD1462F4485B6E62DD3EE4 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SeekerBase_AI_2_Initialize_mA8696A42559589414B089709961677D450A526AF },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Component_GetComponent_TisFractionalType_t08E4E05D84A70F0498596C284E6A0196F827F177_mA6DCDD03ACACC838BEDE60C5E7377A77DBE0F101 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FractionalType_t08E4E05D84A70F0498596C284E6A0196F827F177 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ResourceSeekerBase_AI_3_t481E283874B4202DD3410811AD985C7A1E263DC5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_SeekerBase_AI_2_t2A3CFC98D8556C83209FE5FB56D2A7F28D116170 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TrackerType_tA303452FBAD1231C7CDA847B17E0157B8A6F2AFC },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Tracker_1_tB7101B61D378AC55A051D95EA5724879DB67923F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Tracker_1_get_Tracked_mA7C55EDD176CBCBF370B17BC6C9D9D328988F671 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_HashSet_1_t96CF935A0FBAFB094AE506557BE295119D697595 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Enumerable_Any_TisTargetType_t157C4C0AB94E0C2213A136CFCC6EFCEC44699D7D_m488458F97159228276E8EED541A22B142CF2B9B6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerable_1_t60A77B6A8538EEA6C07280A4C3B5E6992B513A52 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_FractionalType_t08E4E05D84A70F0498596C284E6A0196F827F177_IFractionalAmount_get_FractionalAmount_mDE437D730D0C011EB77298D62BA4CCECB29C19A2 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SeekerBase_AI_2__ctor_mAAC3F8B6F6309D7F78B0F6E4DBE6A6506A369444 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_SeekerBase_AI_2_t2A3CFC98D8556C83209FE5FB56D2A7F28D116170 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_SeekerBase_AI_2_tA11E2140DB67288E134CCBFDF5972E4336F46A85 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_GameObject_AddComponent_TisTrackerType_t834C84C78EEAF48E47993FD3FBC0DF0076AFB517_mB80171BCFD40810A0D9D0BC37EE0572CC63187F0 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TrackerType_t834C84C78EEAF48E47993FD3FBC0DF0076AFB517 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TrackedType_t39A3676686CB876AFDA1EA8901B7209333ADA77D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Tracker_1_t94AA48D7BA0C5DAEA3D89C5580E07187AF750718 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Tracker_1_get_Tracked_m5F6201606173061CB704822E9A41CA94B3B3F977 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_HashSet_1_t4FDB31EA87054F5F3C26AA779B53BDA0D8A23492 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_HashSet_1_Contains_m3A26E98D546BD9E8ED38820236162EE20D997B44 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec__DisplayClass9_0_t784C78433EEC5E6D9702A5E1BFD53AFB5C880020 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec__DisplayClass9_0__ctor_m383E3EB9F6F06C8B3E487E5711098A58D5F79C0C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec__DisplayClass9_0_U3CActiveUpdateU3Eb__0_m75AA8D0475EC55AA04A1F70A2B570EE9FB649F79 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_2_t023807D49DF0685B33C6202623A31B1217B2A3DA },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Func_2__ctor_m221A306A887F7E906639A74F5240E5D4CBD07DEA },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IEnumerableExtensions_MinBy_TisTrackedType_t39A3676686CB876AFDA1EA8901B7209333ADA77D_m1FD6F309130D30CB87A4A2668CD9618A6229B877 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerable_1_t316B9E7570C7BB3EF3FE976005B2400D6AD04AEC },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Enumerable_Any_TisTrackedType_t39A3676686CB876AFDA1EA8901B7209333ADA77D_m0697E56F76A025E66836307B3FE6E264F4AF4ABD },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TrackedType_t96DD65D5AD2DE7D8164A70584B98FAA1FE54A86B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec__DisplayClass9_0_tFDE7EAE1F4666010E0E05F9CC44481C6A8C95D6F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t28EF0709B82A9494F4DFE5D58BD130DB79C24EA5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ChangeEvent_1_t8446774F294227C9272FCA1184FA786A88B86E6A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_GameObject_AddComponent_TisT_t3752DC1AD1DA25D282B9AD6AE4FC908C4E1D9A58_m4E6636A56CB1D68087659BB260B50C48BDE76E28 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t3752DC1AD1DA25D282B9AD6AE4FC908C4E1D9A58 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_GameObject_GetComponent_TisT_t1FA047AED27D5AF8BBF0FBA78068DEE61E1EDE55_m4B82377C4DE82288C489C160BD3CD9D9D2D46F2B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t1FA047AED27D5AF8BBF0FBA78068DEE61E1EDE55 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_GameObject_AddComponent_TisT_t1FA047AED27D5AF8BBF0FBA78068DEE61E1EDE55_m09FA66028C674C199BF74574F427F8A9DA0673A1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Tracker_1_tA580543A2D2A7D4753DF77EAE1BDBF6BD482ABED },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TrackerEntry_Create_TisT_t25A901CB62F3A004F3BC11DBB121D05E88E25895_m2EE06DCA30DB6384B1A5BB08EA34DC96031F2FA1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec__DisplayClass17_0_1_tE0FBDA1C72EA62E3E6B7BF109DAE2A7DD640965E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec__DisplayClass17_0_1__ctor_m63DD8A56443CF7950CBD29329B8ACAC683D643EA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Tracker_1_t2A2FED473242F0676850B2BC5FB380E60552A914 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TrackerEntry_Create_TisT_t1FC1D853B1383C5F3F3771633FB65CCCD23755F0_mB76BE10C8C5B4A11AFC4FB3081B6538CDB1A6BFB },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec__DisplayClass17_0_1_U3CUnregisterSeekerU3Eb__0_m3309796D82501CB7C866794CD6EAF32612C3BF76 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Tracker_1_tB10F8450907C3A28786B0B8B34494C19C5A02291 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_tA57D6D3228821909FD720E2ABFC0861595F69EBB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec__DisplayClass17_0_1_t2BB4D60B11A38F65D85997F82DF5932BC2857529 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Tracker_1_t3DD8E45806928FDAD5261AE5127B419CF8E4F795 },
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_Polyperfect_War_CodeGenModule;
const Il2CppCodeGenModule g_Polyperfect_War_CodeGenModule = 
{
	"Polyperfect.War.dll",
	1290,
	s_methodPointers,
	16,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	15,
	s_rgctxIndices,
	118,
	s_rgctxValues,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};

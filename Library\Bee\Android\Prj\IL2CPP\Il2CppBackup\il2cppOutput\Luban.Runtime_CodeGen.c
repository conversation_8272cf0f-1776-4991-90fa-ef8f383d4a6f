﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void JSONNode_get_Item_m2789110CF7C043941B8ADE346C27D2AD7F5981D6 (void);
extern void JSONNode_get_Value_mFAA1E59DB4A4D92767636C3718E541C4881FAC2C (void);
extern void JSONNode_get_Count_mA19123F3F13787E6DD0D166AA6D6B712B10A9444 (void);
extern void JSONNode_get_IsNumber_m160EEE254980E6F06BCFCEB5492E992F05D4726B (void);
extern void JSONNode_get_IsString_m107542FC9A25BD915A96D7C971CFD6F6241BE66C (void);
extern void JSONNode_get_IsBoolean_mA0F8374F51535747BFD5F73644DB2F6E518F8795 (void);
extern void JSONNode_get_IsArray_m2F3E53642DE363005692ACAD90224733568E444D (void);
extern void JSONNode_get_IsObject_m4C8E25375A5D6E5723A0C3FC7588CFA8DFA361D8 (void);
extern void JSONNode_set_Inline_mD632F21A061B28017D822B8D01D67AA9EFA97795 (void);
extern void JSONNode_Add_m879A11B84014500D73CBAFFC2F05FB8875A37C93 (void);
extern void JSONNode_Add_m9346E62B4D6B4E4E11544206C08C6D04A4E390EC (void);
extern void JSONNode_get_Children_mF12EA954BB989D3A1BD138312EE4442DFA90262A (void);
extern void JSONNode_ToString_m5CDD027A73248D91FAD6F54411C9EE989579A49C (void);
extern void JSONNode_get_AsDouble_m03198809148461A12A92246E6BEC9971253388CF (void);
extern void JSONNode_get_AsInt_mEEAD84A7C57B395751839D4743B43FD8C4E1492C (void);
extern void JSONNode_get_AsFloat_mF9F9B03307E2CCEBC04EBEF38E6B0FB887FDFF3B (void);
extern void JSONNode_get_AsBool_m57DF879067093FE2FD9F13EF0A181A4C25E3E87E (void);
extern void JSONNode_op_Implicit_mC9FDB9C979D8D3495B64EC7896BBABB028E6DFBA (void);
extern void JSONNode_op_Implicit_m92182AB10DDBDA15C0ACD65DAB7686AC9EBBC1A2 (void);
extern void JSONNode_op_Implicit_m8B9AC587F68218D2632273D0BCA2581560305A9C (void);
extern void JSONNode_op_Implicit_m0B547315E3441F84DABFA0631791BA6B26026C0E (void);
extern void JSONNode_op_Implicit_mA64E016EBFB9AC1A1753B7EECD259D6596ECD58F (void);
extern void JSONNode_op_Implicit_mF47BAEE8FBAC7F443963385B753B916F6029CDFF (void);
extern void JSONNode_op_Implicit_mD4636D7BE4CEB59BBA765E6523EA467190D145C8 (void);
extern void JSONNode_op_Equality_mDD36B312D77EC1FC9939276C087EBA8E7B8AD92F (void);
extern void JSONNode_op_Inequality_mF703DC644DDA438C8F56773D20194C2899DDE6F4 (void);
extern void JSONNode_Equals_m2F945F67C1B2DB40C080EB1E4CFF37C4B5EF4C73 (void);
extern void JSONNode_GetHashCode_mC2348FE74B28479DE2E574EB96456B2528AF110F (void);
extern void JSONNode_get_EscapeBuilder_m438107AE7EFFD53ED93EED225A8B44CE3DA88B28 (void);
extern void JSONNode_Escape_m6646863678DB23BDEB28738CC57E452169FFB0FF (void);
extern void JSONNode_ParseElement_m5BA504C2E93CF73EA23A0AB555BD759F26481B5A (void);
extern void JSONNode_Parse_mADBE0CF264E68AFDA1B37FA20CB5FDAFD89134DB (void);
extern void JSONNode__ctor_mB710A69AF2EDA72D624E6291A6B8438316D5E8DA (void);
extern void JSONNode__cctor_m5590ECAA39E02983EFECFC2457C77E933A9454CA (void);
extern void U3Cget_ChildrenU3Ed__43__ctor_mE7537C1ADA2199B30E3DD1E586C06868E952582D (void);
extern void U3Cget_ChildrenU3Ed__43_System_IDisposable_Dispose_mE0750C6999A3B99BA5EA25CBA8EE78FC5D2E32A7 (void);
extern void U3Cget_ChildrenU3Ed__43_MoveNext_m0FA30CAB66F936066E46B3E37E076F324CCAE18D (void);
extern void U3Cget_ChildrenU3Ed__43_System_Collections_Generic_IEnumeratorU3CSimpleJSON_JSONNodeU3E_get_Current_m3348853117A1CB8C4299F7D874A05D5389D2E8A4 (void);
extern void U3Cget_ChildrenU3Ed__43_System_Collections_IEnumerator_Reset_mA525437DD47F7DFF24EC4B929622D3A7AEED6DA0 (void);
extern void U3Cget_ChildrenU3Ed__43_System_Collections_IEnumerator_get_Current_m438EAF263933605323729A301FE050F69ECC75F0 (void);
extern void U3Cget_ChildrenU3Ed__43_System_Collections_Generic_IEnumerableU3CSimpleJSON_JSONNodeU3E_GetEnumerator_mE865D7DB0E34EA57362F88DA761C43889CB795ED (void);
extern void U3Cget_ChildrenU3Ed__43_System_Collections_IEnumerable_GetEnumerator_mC817252D040A030FAE1ECA3813AF6EE051DEB131 (void);
extern void JSONArray_set_Inline_m90446F9F14C011A0544797B84BAB132E52C9DA0D (void);
extern void JSONArray_get_IsArray_mF146237B10879A0A57F792D1465137A82578B9AC (void);
extern void JSONArray_get_Item_mD02219BCD5FDEE90D2CC56ECF84622FA314D189B (void);
extern void JSONArray_get_Count_m3A177847B02DD3F048AA9E997B179F9836042C1C (void);
extern void JSONArray_Add_m2B76C3E7B373A9F338C2E0884089E0AC9A536ABA (void);
extern void JSONArray_get_Children_m147FCAA9833545DE298C6C6412D5AFF7005364E5 (void);
extern void JSONArray_WriteToStringBuilder_mABF3E6448FD36889F23F085C9FE44216B0F1D9F4 (void);
extern void JSONArray__ctor_mD53A24956E3512E4F0F8989651E9E8BA8B2257B8 (void);
extern void U3Cget_ChildrenU3Ed__24__ctor_m95EEF28AA5E9412DACD322799156F76F44D26F51 (void);
extern void U3Cget_ChildrenU3Ed__24_System_IDisposable_Dispose_m46DC15736BACD0533B79A2CED81CBC6E8F1602FA (void);
extern void U3Cget_ChildrenU3Ed__24_MoveNext_m88F1E73329046E9EA0D8238779D268D7D3CF148D (void);
extern void U3Cget_ChildrenU3Ed__24_U3CU3Em__Finally1_mCBE434BF8D79BABE04A07BDD8B884F9AC88725DE (void);
extern void U3Cget_ChildrenU3Ed__24_System_Collections_Generic_IEnumeratorU3CSimpleJSON_JSONNodeU3E_get_Current_m407DB9E152EACE8041BD776E9C43E95CCB611AA3 (void);
extern void U3Cget_ChildrenU3Ed__24_System_Collections_IEnumerator_Reset_mA18412275DA36B0363FB34F6B8892515A8714998 (void);
extern void U3Cget_ChildrenU3Ed__24_System_Collections_IEnumerator_get_Current_m678D2F3353FF941B656CD0DA2B60099241B1ED1E (void);
extern void U3Cget_ChildrenU3Ed__24_System_Collections_Generic_IEnumerableU3CSimpleJSON_JSONNodeU3E_GetEnumerator_mA138DAA861DE2241F9AFAB0F9A425A3BDF7D3A49 (void);
extern void U3Cget_ChildrenU3Ed__24_System_Collections_IEnumerable_GetEnumerator_m3235CB3BB5E5D4FD660A2DB7359F45521A6EE285 (void);
extern void JSONObject_set_Inline_mF8D7E439225CCD750F9D257A66EA937094FDEC25 (void);
extern void JSONObject_get_IsObject_mCA2563BDEBF5B61BAD1CCCFB63A33C8DB09D5B65 (void);
extern void JSONObject_get_Item_m8324E7D7055596CFF2C7A8F762575E4D2D8F32E3 (void);
extern void JSONObject_get_Count_m5AA6D6E9604751BEB349B290F1EA21BDFEA8F34D (void);
extern void JSONObject_Add_m3BE5C09B6AD2F9C2B3B00BC722E18A6308B7FDB8 (void);
extern void JSONObject_get_Children_m3DCF67F343A0363AE4FD29E6C4EB8DE71011FE14 (void);
extern void JSONObject_WriteToStringBuilder_m4DCAA68A651E392130E88C7D163EC1CC581B6122 (void);
extern void JSONObject__ctor_mD4F6D4B7B8D8D3F4FCF45B4BAC32AEB66588307A (void);
extern void U3Cget_ChildrenU3Ed__27__ctor_m18A161AD65F41D242EAC8B1AE005D17C47287B05 (void);
extern void U3Cget_ChildrenU3Ed__27_System_IDisposable_Dispose_m3DCE5C10014923EEDA1EEE48F20B376E8A60C30B (void);
extern void U3Cget_ChildrenU3Ed__27_MoveNext_mEADC2C5094DBD8D981A703FE24D0BFB022373464 (void);
extern void U3Cget_ChildrenU3Ed__27_U3CU3Em__Finally1_m8BC545E469064B06F65F22A2FDA8F9BCCC693CF0 (void);
extern void U3Cget_ChildrenU3Ed__27_System_Collections_Generic_IEnumeratorU3CSimpleJSON_JSONNodeU3E_get_Current_m7C0A551A06619EE7BB1F94EBACD7F976BB9559E3 (void);
extern void U3Cget_ChildrenU3Ed__27_System_Collections_IEnumerator_Reset_m6B6CFD88298C682F8711FDEA83945EF3CCFB89A0 (void);
extern void U3Cget_ChildrenU3Ed__27_System_Collections_IEnumerator_get_Current_m8B46E8ACFD0C4FF03BC1F82BF1236AFFA9BA0D75 (void);
extern void U3Cget_ChildrenU3Ed__27_System_Collections_Generic_IEnumerableU3CSimpleJSON_JSONNodeU3E_GetEnumerator_m7C7CEB31168871A6A9A9A8BB596BE0714969C4A2 (void);
extern void U3Cget_ChildrenU3Ed__27_System_Collections_IEnumerable_GetEnumerator_mC0508F72CC9FFC3ABB41B4092E7CA66BC28FCF14 (void);
extern void JSONString_get_IsString_m38AE2283D152FF9AFB1A8DAEA7A63F2D6DC2CB42 (void);
extern void JSONString_get_Value_m4F6921486DA8D4250F58BFAE1D7FF15F01BE2539 (void);
extern void JSONString__ctor_m67139D815079187D65873B84717A70843DD17264 (void);
extern void JSONString_WriteToStringBuilder_m7282552C4625435BA8F36E31F0CE1AF05F29E254 (void);
extern void JSONString_Equals_mA621194206EDA384DBB2C1AE955C8E9B6E2137A9 (void);
extern void JSONString_GetHashCode_m87FD358662735E617F9D2326D46E29E2B0E4935D (void);
extern void JSONNumber_get_IsNumber_mCA929E7FD0C4A4B94963549233707158E003CE7D (void);
extern void JSONNumber_get_Value_mB06E9D6AEF55D74A5779A46743C07CD8FCBE702C (void);
extern void JSONNumber_get_AsDouble_m8D3036913529A244E3945BB07FAC201EE012714D (void);
extern void JSONNumber__ctor_m7E2D0AFEE8AA0DD3E0BD39509764944024821801 (void);
extern void JSONNumber_WriteToStringBuilder_m8802E960F7B3BE574FB87E14A16062ECF45AD826 (void);
extern void JSONNumber_IsNumeric_m9C7E4C675A347170CB4627C5DC33B5AE16FC2855 (void);
extern void JSONNumber_Equals_m0D985065CA03D1987E4888485B153869199CE857 (void);
extern void JSONNumber_GetHashCode_m0FCD993684BC73D858AD59CE37551F5A81D94ECC (void);
extern void JSONBool_get_IsBoolean_m34AF989A389A5DD5642767A023EC04F5D36D13EC (void);
extern void JSONBool_get_Value_m9B2AC32C27A7BFEA9588F6862B2D4A6E9081AF87 (void);
extern void JSONBool_get_AsBool_m86C531CA66E0FBF42AC40EEA37A78D148839FFF9 (void);
extern void JSONBool__ctor_mA722148B8765BE496706554AD85D6933240357E5 (void);
extern void JSONBool_WriteToStringBuilder_mDEB7BBD882C0FADA8C00EDC2760F087453430C6D (void);
extern void JSONBool_Equals_mF18DAF386FA5E7681AB1835AAE979CB444A69BF5 (void);
extern void JSONBool_GetHashCode_mDF7AE67A5499A3C6FFFCE284AFC9CAE237C83DE4 (void);
extern void JSONNull_CreateOrGet_m920C3D38D052C4C8EDE996476F91D9913B6017F2 (void);
extern void JSONNull__ctor_m483EFD80DA95F906B6F966CB6FC63ED8194457C4 (void);
extern void JSONNull_get_Value_m7D9880D1190697362F228A228D9A4A0CA435A47F (void);
extern void JSONNull_get_AsBool_m13FF2D4FD1899F9DB55108C65BFB2E4E8CFB529B (void);
extern void JSONNull_Equals_m1E77C8048155561039974995F7B113CEBD157326 (void);
extern void JSONNull_GetHashCode_m04E8A7D1385318506DD21C50D7A38E7889357961 (void);
extern void JSONNull_WriteToStringBuilder_m4701985E7E74C6658669A3B688CBBA20BFD7DC79 (void);
extern void JSONNull__cctor_mEDAD9F5CCEF36466C93CC4AD6B3F3F93A6E73815 (void);
extern void JSONLazyCreator__ctor_m5A3DAB47229CD45650E935BDB7D0C9FACA460720 (void);
extern void JSONLazyCreator__ctor_m3965360CD8D0DEA5E2ED0B3E4981936534F30C93 (void);
extern void JSONLazyCreator_get_Item_m999F0D4444F6FFDA7CB8EAEE6BCE2C3CE0776EAA (void);
extern void JSONLazyCreator_Add_m354FAEC031F11A8DB900BE757DA40FE84F0AB380 (void);
extern void JSONLazyCreator_Add_mECF09D05C3B117E3DC340522EBC1BE0D9D990BB5 (void);
extern void JSONLazyCreator_Equals_mEF2829A9E6DB5B8EF84C8172CF806670DEE23E94 (void);
extern void JSONLazyCreator_GetHashCode_mF2FA6B801AB1CF016D02F81864A3C6192D81BE47 (void);
extern void JSONLazyCreator_get_AsInt_mA829B114ECDF70080E7493F397C64EE33527ADB5 (void);
extern void JSONLazyCreator_get_AsFloat_m82BA70F1AA960ABC9EB0D7DFC1373DCF01A12115 (void);
extern void JSONLazyCreator_get_AsDouble_mD19160DECA505875AC5A6A9BF3586839103DFBD8 (void);
extern void JSONLazyCreator_get_AsBool_mE990672E6EDD401179F238904B1F87454BD89E13 (void);
extern void JSONLazyCreator_WriteToStringBuilder_m39D5E30A28729B6533251B914C20B4A30413270E (void);
extern void JSON_Parse_mA779EFEA71F2DEEED3D0DECEFFCFA70197A72521 (void);
extern void BeanBase__ctor_mAA920112A0185B23CCF0A8A06CCDF346C019ECB4 (void);
extern void SerializationException__ctor_mE7CB71876F77ED9C1885672F7A42FC0777E595AA (void);
static Il2CppMethodPointer s_methodPointers[124] = 
{
	JSONNode_get_Item_m2789110CF7C043941B8ADE346C27D2AD7F5981D6,
	JSONNode_get_Value_mFAA1E59DB4A4D92767636C3718E541C4881FAC2C,
	JSONNode_get_Count_mA19123F3F13787E6DD0D166AA6D6B712B10A9444,
	JSONNode_get_IsNumber_m160EEE254980E6F06BCFCEB5492E992F05D4726B,
	JSONNode_get_IsString_m107542FC9A25BD915A96D7C971CFD6F6241BE66C,
	JSONNode_get_IsBoolean_mA0F8374F51535747BFD5F73644DB2F6E518F8795,
	JSONNode_get_IsArray_m2F3E53642DE363005692ACAD90224733568E444D,
	JSONNode_get_IsObject_m4C8E25375A5D6E5723A0C3FC7588CFA8DFA361D8,
	JSONNode_set_Inline_mD632F21A061B28017D822B8D01D67AA9EFA97795,
	JSONNode_Add_m879A11B84014500D73CBAFFC2F05FB8875A37C93,
	JSONNode_Add_m9346E62B4D6B4E4E11544206C08C6D04A4E390EC,
	JSONNode_get_Children_mF12EA954BB989D3A1BD138312EE4442DFA90262A,
	JSONNode_ToString_m5CDD027A73248D91FAD6F54411C9EE989579A49C,
	NULL,
	JSONNode_get_AsDouble_m03198809148461A12A92246E6BEC9971253388CF,
	JSONNode_get_AsInt_mEEAD84A7C57B395751839D4743B43FD8C4E1492C,
	JSONNode_get_AsFloat_mF9F9B03307E2CCEBC04EBEF38E6B0FB887FDFF3B,
	JSONNode_get_AsBool_m57DF879067093FE2FD9F13EF0A181A4C25E3E87E,
	JSONNode_op_Implicit_mC9FDB9C979D8D3495B64EC7896BBABB028E6DFBA,
	JSONNode_op_Implicit_m92182AB10DDBDA15C0ACD65DAB7686AC9EBBC1A2,
	JSONNode_op_Implicit_m8B9AC587F68218D2632273D0BCA2581560305A9C,
	JSONNode_op_Implicit_m0B547315E3441F84DABFA0631791BA6B26026C0E,
	JSONNode_op_Implicit_mA64E016EBFB9AC1A1753B7EECD259D6596ECD58F,
	JSONNode_op_Implicit_mF47BAEE8FBAC7F443963385B753B916F6029CDFF,
	JSONNode_op_Implicit_mD4636D7BE4CEB59BBA765E6523EA467190D145C8,
	JSONNode_op_Equality_mDD36B312D77EC1FC9939276C087EBA8E7B8AD92F,
	JSONNode_op_Inequality_mF703DC644DDA438C8F56773D20194C2899DDE6F4,
	JSONNode_Equals_m2F945F67C1B2DB40C080EB1E4CFF37C4B5EF4C73,
	JSONNode_GetHashCode_mC2348FE74B28479DE2E574EB96456B2528AF110F,
	JSONNode_get_EscapeBuilder_m438107AE7EFFD53ED93EED225A8B44CE3DA88B28,
	JSONNode_Escape_m6646863678DB23BDEB28738CC57E452169FFB0FF,
	JSONNode_ParseElement_m5BA504C2E93CF73EA23A0AB555BD759F26481B5A,
	JSONNode_Parse_mADBE0CF264E68AFDA1B37FA20CB5FDAFD89134DB,
	JSONNode__ctor_mB710A69AF2EDA72D624E6291A6B8438316D5E8DA,
	JSONNode__cctor_m5590ECAA39E02983EFECFC2457C77E933A9454CA,
	U3Cget_ChildrenU3Ed__43__ctor_mE7537C1ADA2199B30E3DD1E586C06868E952582D,
	U3Cget_ChildrenU3Ed__43_System_IDisposable_Dispose_mE0750C6999A3B99BA5EA25CBA8EE78FC5D2E32A7,
	U3Cget_ChildrenU3Ed__43_MoveNext_m0FA30CAB66F936066E46B3E37E076F324CCAE18D,
	U3Cget_ChildrenU3Ed__43_System_Collections_Generic_IEnumeratorU3CSimpleJSON_JSONNodeU3E_get_Current_m3348853117A1CB8C4299F7D874A05D5389D2E8A4,
	U3Cget_ChildrenU3Ed__43_System_Collections_IEnumerator_Reset_mA525437DD47F7DFF24EC4B929622D3A7AEED6DA0,
	U3Cget_ChildrenU3Ed__43_System_Collections_IEnumerator_get_Current_m438EAF263933605323729A301FE050F69ECC75F0,
	U3Cget_ChildrenU3Ed__43_System_Collections_Generic_IEnumerableU3CSimpleJSON_JSONNodeU3E_GetEnumerator_mE865D7DB0E34EA57362F88DA761C43889CB795ED,
	U3Cget_ChildrenU3Ed__43_System_Collections_IEnumerable_GetEnumerator_mC817252D040A030FAE1ECA3813AF6EE051DEB131,
	JSONArray_set_Inline_m90446F9F14C011A0544797B84BAB132E52C9DA0D,
	JSONArray_get_IsArray_mF146237B10879A0A57F792D1465137A82578B9AC,
	JSONArray_get_Item_mD02219BCD5FDEE90D2CC56ECF84622FA314D189B,
	JSONArray_get_Count_m3A177847B02DD3F048AA9E997B179F9836042C1C,
	JSONArray_Add_m2B76C3E7B373A9F338C2E0884089E0AC9A536ABA,
	JSONArray_get_Children_m147FCAA9833545DE298C6C6412D5AFF7005364E5,
	JSONArray_WriteToStringBuilder_mABF3E6448FD36889F23F085C9FE44216B0F1D9F4,
	JSONArray__ctor_mD53A24956E3512E4F0F8989651E9E8BA8B2257B8,
	U3Cget_ChildrenU3Ed__24__ctor_m95EEF28AA5E9412DACD322799156F76F44D26F51,
	U3Cget_ChildrenU3Ed__24_System_IDisposable_Dispose_m46DC15736BACD0533B79A2CED81CBC6E8F1602FA,
	U3Cget_ChildrenU3Ed__24_MoveNext_m88F1E73329046E9EA0D8238779D268D7D3CF148D,
	U3Cget_ChildrenU3Ed__24_U3CU3Em__Finally1_mCBE434BF8D79BABE04A07BDD8B884F9AC88725DE,
	U3Cget_ChildrenU3Ed__24_System_Collections_Generic_IEnumeratorU3CSimpleJSON_JSONNodeU3E_get_Current_m407DB9E152EACE8041BD776E9C43E95CCB611AA3,
	U3Cget_ChildrenU3Ed__24_System_Collections_IEnumerator_Reset_mA18412275DA36B0363FB34F6B8892515A8714998,
	U3Cget_ChildrenU3Ed__24_System_Collections_IEnumerator_get_Current_m678D2F3353FF941B656CD0DA2B60099241B1ED1E,
	U3Cget_ChildrenU3Ed__24_System_Collections_Generic_IEnumerableU3CSimpleJSON_JSONNodeU3E_GetEnumerator_mA138DAA861DE2241F9AFAB0F9A425A3BDF7D3A49,
	U3Cget_ChildrenU3Ed__24_System_Collections_IEnumerable_GetEnumerator_m3235CB3BB5E5D4FD660A2DB7359F45521A6EE285,
	JSONObject_set_Inline_mF8D7E439225CCD750F9D257A66EA937094FDEC25,
	JSONObject_get_IsObject_mCA2563BDEBF5B61BAD1CCCFB63A33C8DB09D5B65,
	JSONObject_get_Item_m8324E7D7055596CFF2C7A8F762575E4D2D8F32E3,
	JSONObject_get_Count_m5AA6D6E9604751BEB349B290F1EA21BDFEA8F34D,
	JSONObject_Add_m3BE5C09B6AD2F9C2B3B00BC722E18A6308B7FDB8,
	JSONObject_get_Children_m3DCF67F343A0363AE4FD29E6C4EB8DE71011FE14,
	JSONObject_WriteToStringBuilder_m4DCAA68A651E392130E88C7D163EC1CC581B6122,
	JSONObject__ctor_mD4F6D4B7B8D8D3F4FCF45B4BAC32AEB66588307A,
	U3Cget_ChildrenU3Ed__27__ctor_m18A161AD65F41D242EAC8B1AE005D17C47287B05,
	U3Cget_ChildrenU3Ed__27_System_IDisposable_Dispose_m3DCE5C10014923EEDA1EEE48F20B376E8A60C30B,
	U3Cget_ChildrenU3Ed__27_MoveNext_mEADC2C5094DBD8D981A703FE24D0BFB022373464,
	U3Cget_ChildrenU3Ed__27_U3CU3Em__Finally1_m8BC545E469064B06F65F22A2FDA8F9BCCC693CF0,
	U3Cget_ChildrenU3Ed__27_System_Collections_Generic_IEnumeratorU3CSimpleJSON_JSONNodeU3E_get_Current_m7C0A551A06619EE7BB1F94EBACD7F976BB9559E3,
	U3Cget_ChildrenU3Ed__27_System_Collections_IEnumerator_Reset_m6B6CFD88298C682F8711FDEA83945EF3CCFB89A0,
	U3Cget_ChildrenU3Ed__27_System_Collections_IEnumerator_get_Current_m8B46E8ACFD0C4FF03BC1F82BF1236AFFA9BA0D75,
	U3Cget_ChildrenU3Ed__27_System_Collections_Generic_IEnumerableU3CSimpleJSON_JSONNodeU3E_GetEnumerator_m7C7CEB31168871A6A9A9A8BB596BE0714969C4A2,
	U3Cget_ChildrenU3Ed__27_System_Collections_IEnumerable_GetEnumerator_mC0508F72CC9FFC3ABB41B4092E7CA66BC28FCF14,
	JSONString_get_IsString_m38AE2283D152FF9AFB1A8DAEA7A63F2D6DC2CB42,
	JSONString_get_Value_m4F6921486DA8D4250F58BFAE1D7FF15F01BE2539,
	JSONString__ctor_m67139D815079187D65873B84717A70843DD17264,
	JSONString_WriteToStringBuilder_m7282552C4625435BA8F36E31F0CE1AF05F29E254,
	JSONString_Equals_mA621194206EDA384DBB2C1AE955C8E9B6E2137A9,
	JSONString_GetHashCode_m87FD358662735E617F9D2326D46E29E2B0E4935D,
	JSONNumber_get_IsNumber_mCA929E7FD0C4A4B94963549233707158E003CE7D,
	JSONNumber_get_Value_mB06E9D6AEF55D74A5779A46743C07CD8FCBE702C,
	JSONNumber_get_AsDouble_m8D3036913529A244E3945BB07FAC201EE012714D,
	JSONNumber__ctor_m7E2D0AFEE8AA0DD3E0BD39509764944024821801,
	JSONNumber_WriteToStringBuilder_m8802E960F7B3BE574FB87E14A16062ECF45AD826,
	JSONNumber_IsNumeric_m9C7E4C675A347170CB4627C5DC33B5AE16FC2855,
	JSONNumber_Equals_m0D985065CA03D1987E4888485B153869199CE857,
	JSONNumber_GetHashCode_m0FCD993684BC73D858AD59CE37551F5A81D94ECC,
	JSONBool_get_IsBoolean_m34AF989A389A5DD5642767A023EC04F5D36D13EC,
	JSONBool_get_Value_m9B2AC32C27A7BFEA9588F6862B2D4A6E9081AF87,
	JSONBool_get_AsBool_m86C531CA66E0FBF42AC40EEA37A78D148839FFF9,
	JSONBool__ctor_mA722148B8765BE496706554AD85D6933240357E5,
	JSONBool_WriteToStringBuilder_mDEB7BBD882C0FADA8C00EDC2760F087453430C6D,
	JSONBool_Equals_mF18DAF386FA5E7681AB1835AAE979CB444A69BF5,
	JSONBool_GetHashCode_mDF7AE67A5499A3C6FFFCE284AFC9CAE237C83DE4,
	JSONNull_CreateOrGet_m920C3D38D052C4C8EDE996476F91D9913B6017F2,
	JSONNull__ctor_m483EFD80DA95F906B6F966CB6FC63ED8194457C4,
	JSONNull_get_Value_m7D9880D1190697362F228A228D9A4A0CA435A47F,
	JSONNull_get_AsBool_m13FF2D4FD1899F9DB55108C65BFB2E4E8CFB529B,
	JSONNull_Equals_m1E77C8048155561039974995F7B113CEBD157326,
	JSONNull_GetHashCode_m04E8A7D1385318506DD21C50D7A38E7889357961,
	JSONNull_WriteToStringBuilder_m4701985E7E74C6658669A3B688CBBA20BFD7DC79,
	JSONNull__cctor_mEDAD9F5CCEF36466C93CC4AD6B3F3F93A6E73815,
	JSONLazyCreator__ctor_m5A3DAB47229CD45650E935BDB7D0C9FACA460720,
	JSONLazyCreator__ctor_m3965360CD8D0DEA5E2ED0B3E4981936534F30C93,
	NULL,
	JSONLazyCreator_get_Item_m999F0D4444F6FFDA7CB8EAEE6BCE2C3CE0776EAA,
	JSONLazyCreator_Add_m354FAEC031F11A8DB900BE757DA40FE84F0AB380,
	JSONLazyCreator_Add_mECF09D05C3B117E3DC340522EBC1BE0D9D990BB5,
	JSONLazyCreator_Equals_mEF2829A9E6DB5B8EF84C8172CF806670DEE23E94,
	JSONLazyCreator_GetHashCode_mF2FA6B801AB1CF016D02F81864A3C6192D81BE47,
	JSONLazyCreator_get_AsInt_mA829B114ECDF70080E7493F397C64EE33527ADB5,
	JSONLazyCreator_get_AsFloat_m82BA70F1AA960ABC9EB0D7DFC1373DCF01A12115,
	JSONLazyCreator_get_AsDouble_mD19160DECA505875AC5A6A9BF3586839103DFBD8,
	JSONLazyCreator_get_AsBool_mE990672E6EDD401179F238904B1F87454BD89E13,
	JSONLazyCreator_WriteToStringBuilder_m39D5E30A28729B6533251B914C20B4A30413270E,
	JSON_Parse_mA779EFEA71F2DEEED3D0DECEFFCFA70197A72521,
	NULL,
	BeanBase__ctor_mAA920112A0185B23CCF0A8A06CCDF346C019ECB4,
	SerializationException__ctor_mE7CB71876F77ED9C1885672F7A42FC0777E595AA,
	NULL,
};
static const int32_t s_InvokerIndices[124] = 
{
	9272,
	13052,
	12996,
	12815,
	12815,
	12815,
	12815,
	12815,
	10442,
	5688,
	10682,
	13052,
	13052,
	0,
	12875,
	12996,
	13195,
	12815,
	20515,
	20515,
	20505,
	20664,
	20211,
	20498,
	19891,
	17478,
	17478,
	7736,
	12996,
	21274,
	20515,
	17991,
	20515,
	13298,
	21355,
	10629,
	13298,
	12815,
	13052,
	13298,
	13052,
	13052,
	13052,
	10442,
	12815,
	9272,
	12996,
	5688,
	13052,
	1893,
	13298,
	10629,
	13298,
	12815,
	13298,
	13052,
	13298,
	13052,
	13052,
	13052,
	10442,
	12815,
	9272,
	12996,
	5688,
	13052,
	1893,
	13298,
	10629,
	13298,
	12815,
	13298,
	13052,
	13298,
	13052,
	13052,
	13052,
	12815,
	13052,
	10682,
	1893,
	7736,
	12996,
	12815,
	13052,
	12875,
	10506,
	1893,
	19891,
	7736,
	12996,
	12815,
	13052,
	12815,
	10442,
	1893,
	7736,
	12996,
	21274,
	13298,
	13052,
	12815,
	7736,
	12996,
	1893,
	21355,
	10682,
	5688,
	0,
	9272,
	10682,
	5688,
	7736,
	12996,
	12996,
	13195,
	12875,
	12815,
	1893,
	20515,
	0,
	13298,
	13298,
	0,
};
static const Il2CppTokenRangePair s_rgctxIndices[2] = 
{
	{ 0x0600006D, { 0, 1 } },
	{ 0x0600007C, { 1, 2 } },
};
extern const uint32_t g_rgctx_T_t9EB33F2B500856AC5A922B36D794A49C0178C637;
extern const uint32_t g_rgctx_IEnumerable_1_t2E65D71C5B082474283C7F1F13560F3DDB6B9B25;
extern const uint32_t g_rgctx_String_Join_TisT_tA08C1E5907596F2084E439F75B8307F42D524DB6_m16A6A7E3775936B45315154378FC11B65E3E6EE3;
static const Il2CppRGCTXDefinition s_rgctxValues[3] = 
{
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t9EB33F2B500856AC5A922B36D794A49C0178C637 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerable_1_t2E65D71C5B082474283C7F1F13560F3DDB6B9B25 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_String_Join_TisT_tA08C1E5907596F2084E439F75B8307F42D524DB6_m16A6A7E3775936B45315154378FC11B65E3E6EE3 },
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_Luban_Runtime_CodeGenModule;
const Il2CppCodeGenModule g_Luban_Runtime_CodeGenModule = 
{
	"Luban.Runtime.dll",
	124,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	2,
	s_rgctxIndices,
	3,
	s_rgctxValues,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};

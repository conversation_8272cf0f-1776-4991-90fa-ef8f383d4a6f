using UnityEngine;
using UnityEditor;

public class BitmapTextTestWindow : EditorWindow
{
    private BitmapFont m_TestFont;
    private string m_TestText = "Hello World!";
    private BitmapTextSettings m_Settings = new BitmapTextSettings();
    private Color m_Color = Color.white;
    private Vector2 m_ScrollPosition;
    
    [MenuItem("Tools/Bitmap Text/Test Window")]
    public static void ShowWindow()
    {
        GetWindow<BitmapTextTestWindow>("Bitmap Text Test");
    }
    
    private void OnGUI()
    {
        EditorGUILayout.LabelField("Bitmap Text Test", EditorStyles.boldLabel);
        EditorGUILayout.Space();
        
        // Font selection
        m_TestFont = EditorGUILayout.ObjectField("Font", m_TestFont, typeof(BitmapFont), false) as BitmapFont;
        
        // Test text
        EditorGUILayout.LabelField("Test Text:");
        m_TestText = EditorGUILayout.TextArea(m_TestText, GUILayout.Height(60));
        
        // Color
        m_Color = EditorGUILayout.ColorField("Color", m_Color);
        
        EditorGUILayout.Space();
        
        // Settings
        EditorGUILayout.LabelField("Settings", EditorStyles.boldLabel);
        
        m_Settings.fontSize = EditorGUILayout.FloatField("Font Size", m_Settings.fontSize);
        m_Settings.alignment = (BitmapTextAlignment)EditorGUILayout.EnumPopup("Alignment", m_Settings.alignment);
        m_Settings.verticalAlignment = (BitmapTextVerticalAlignment)EditorGUILayout.EnumPopup("Vertical Alignment", m_Settings.verticalAlignment);
        m_Settings.overflow = (BitmapTextOverflow)EditorGUILayout.EnumPopup("Overflow", m_Settings.overflow);
        
        m_Settings.lineSpacing = EditorGUILayout.FloatField("Line Spacing", m_Settings.lineSpacing);
        m_Settings.characterSpacing = EditorGUILayout.FloatField("Character Spacing", m_Settings.characterSpacing);
        m_Settings.wordSpacing = EditorGUILayout.FloatField("Word Spacing", m_Settings.wordSpacing);
        
        m_Settings.maxSize = EditorGUILayout.Vector2Field("Max Size", m_Settings.maxSize);
        
        m_Settings.autoSize = EditorGUILayout.Toggle("Auto Size", m_Settings.autoSize);
        if (m_Settings.autoSize)
        {
            m_Settings.minFontSize = EditorGUILayout.FloatField("Min Font Size", m_Settings.minFontSize);
            m_Settings.maxFontSize = EditorGUILayout.FloatField("Max Font Size", m_Settings.maxFontSize);
        }
        
        m_Settings.richText = EditorGUILayout.Toggle("Rich Text", m_Settings.richText);
        
        EditorGUILayout.Space();
        
        // Test buttons
        EditorGUILayout.LabelField("Actions", EditorStyles.boldLabel);
        
        EditorGUILayout.BeginHorizontal();
        
        if (GUILayout.Button("Test Generation"))
        {
            TestTextGeneration();
        }
        
        if (GUILayout.Button("Create UI Test"))
        {
            CreateUITest();
        }
        
        if (GUILayout.Button("Create Mesh Test"))
        {
            CreateMeshTest();
        }
        
        EditorGUILayout.EndHorizontal();
        
        EditorGUILayout.Space();
        
        // Info display
        if (m_TestFont != null)
        {
            EditorGUILayout.LabelField("Font Info", EditorStyles.boldLabel);
            EditorGUILayout.LabelField($"Name: {m_TestFont.fontName}");
            EditorGUILayout.LabelField($"Line Height: {m_TestFont.lineHeight}");
            EditorGUILayout.LabelField($"Glyph Count: {m_TestFont.glyphs.Count}");
            
            var preferredSize = BitmapTextGenerator.GetPreferredSize(m_TestText, m_TestFont, m_Settings);
            EditorGUILayout.LabelField($"Preferred Size: {preferredSize.x:F1} x {preferredSize.y:F1}");
        }
    }
    
    private void TestTextGeneration()
    {
        if (m_TestFont == null)
        {
            EditorUtility.DisplayDialog("Error", "Please select a bitmap font first.", "OK");
            return;
        }
        
        var textInfo = BitmapTextGenerator.GenerateText(m_TestText, m_TestFont, m_Settings, m_Color);
        
        string info = $"Generated Text Info:\n" +
                     $"Character Count: {textInfo.characters?.Count ?? 0}\n" +
                     $"Line Count: {textInfo.lineCount}\n" +
                     $"Text Size: {textInfo.textSize.x:F1} x {textInfo.textSize.y:F1}\n" +
                     $"Actual Font Size: {textInfo.actualFontSize:F1}";
        
        EditorUtility.DisplayDialog("Text Generation Test", info, "OK");
    }
    
    private void CreateUITest()
    {
        if (m_TestFont == null)
        {
            EditorUtility.DisplayDialog("Error", "Please select a bitmap font first.", "OK");
            return;
        }
        
        // Create a canvas if none exists
        Canvas canvas = FindObjectOfType<Canvas>();
        if (canvas == null)
        {
            var canvasGO = new GameObject("Canvas");
            canvas = canvasGO.AddComponent<Canvas>();
            canvas.renderMode = RenderMode.ScreenSpaceOverlay;
            canvasGO.AddComponent<CanvasScaler>();
            canvasGO.AddComponent<GraphicRaycaster>();
        }
        
        // Create UI bitmap text
        var textGO = new GameObject("UI Bitmap Text Test");
        textGO.transform.SetParent(canvas.transform, false);
        
        var rectTransform = textGO.AddComponent<RectTransform>();
        rectTransform.anchoredPosition = Vector2.zero;
        rectTransform.sizeDelta = new Vector2(400, 200);
        
        var bitmapText = textGO.AddComponent<UIBitmapText>();
        bitmapText.font = m_TestFont;
        bitmapText.text = m_TestText;
        bitmapText.settings = new BitmapTextSettings(m_Settings);
        bitmapText.color = m_Color;
        
        Selection.activeGameObject = textGO;
        EditorGUIUtility.PingObject(textGO);
    }
    
    private void CreateMeshTest()
    {
        if (m_TestFont == null)
        {
            EditorUtility.DisplayDialog("Error", "Please select a bitmap font first.", "OK");
            return;
        }
        
        // Create mesh bitmap text
        var textGO = new GameObject("Mesh Bitmap Text Test");
        
        var meshText = textGO.AddComponent<MeshBitmapText>();
        meshText.font = m_TestFont;
        meshText.text = m_TestText;
        meshText.settings = new BitmapTextSettings(m_Settings);
        meshText.color = m_Color;
        
        // Position in scene view
        var sceneView = SceneView.lastActiveSceneView;
        if (sceneView != null)
        {
            textGO.transform.position = sceneView.camera.transform.position + sceneView.camera.transform.forward * 5f;
        }
        
        Selection.activeGameObject = textGO;
        EditorGUIUtility.PingObject(textGO);
    }
}

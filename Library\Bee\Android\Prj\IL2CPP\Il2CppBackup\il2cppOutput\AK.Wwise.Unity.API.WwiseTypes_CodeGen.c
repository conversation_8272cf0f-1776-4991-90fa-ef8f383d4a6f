﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void WwiseAcousticTextureReference_get_WwiseObjectType_mAC79B28F75932F1CA4908C1923A1118DBD4EB7B7 (void);
extern void WwiseAcousticTextureReference__ctor_m85F17C9E80B325198F10511E553A5C8C2FD5CBA3 (void);
extern void WwiseAuxBusReference_get_WwiseObjectType_m25249A7E0F5D5917F54FB0F9DD2A4BB36CDD2647 (void);
extern void WwiseAuxBusReference__ctor_mF34FD7CD291D7E40687374683BC7082C0618868E (void);
extern void WwiseBankReference_get_WwiseObjectType_m6B39EE01C52FE8B1DBCE692AFC2A4D000B505DE1 (void);
extern void WwiseBankReference__ctor_m8B36CA656F4B4217FAAB4967189D2F26B37B621E (void);
extern void WwiseEventReference_get_WwiseObjectType_mE14663741E79CB96450A20255E6BC397A21362FE (void);
extern void WwiseEventReference__ctor_m351B1BEF4B0A1EABE26AEE071A025F7A4A0A40EE (void);
extern void WwiseObjectReference_get_Guid_mC29EDF844BBBF614C843A27B3712918FA0D2F7B7 (void);
extern void WwiseObjectReference_get_ObjectName_m254DB1A2042C708D0E85691FA041732056B0E236 (void);
extern void WwiseObjectReference_get_DisplayName_mD3C6E8C17AE60F3449789B00DB13646B9BC9CD02 (void);
extern void WwiseObjectReference_get_Id_mDB6026205D9926CC2C5E8EF590E265C397085928 (void);
extern void WwiseObjectReference__ctor_mF7B17F11C8A85A33C41C1483520CB91FD8F4F2CB (void);
extern void WwiseGroupValueObjectReference_get_DisplayName_mC52BBA875E7DBA93AB58480E5F46211F2459AC23 (void);
extern void WwiseGroupValueObjectReference__ctor_m19EBF7F6E30A5CEAE1474B227FC14ED74E95B477 (void);
extern void WwiseRtpcReference_get_WwiseObjectType_mFB42AF391986743A32CCA62EF0B5042677BE9FEF (void);
extern void WwiseRtpcReference__ctor_m4D530F70CC6FD40202137BE068E9D47F9BE172B6 (void);
extern void WwiseStateGroupReference_get_WwiseObjectType_mE406C005E17C363AF2DD6463C45F9E3937E6A65A (void);
extern void WwiseStateGroupReference__ctor_m90C748605A4E0E93E71CA472FD80BDE7EAAEE0BB (void);
extern void WwiseStateReference_get_WwiseObjectType_m0B56F158229FE6C7C60F1141E6E4947878C0B39A (void);
extern void WwiseStateReference_get_GroupObjectReference_mDE7BA981D82CAF320E2C6432A60CC7F53191C858 (void);
extern void WwiseStateReference_set_GroupObjectReference_mBE5E6C65C872117D1EA7D47394ED6CB16A82A908 (void);
extern void WwiseStateReference_get_GroupWwiseObjectType_m23EFA97FE87EBE71BDB073C3F4B624627CD6D6B9 (void);
extern void WwiseStateReference__ctor_mBBB93802F6A30989DDDC16DCCC9F3DB8815BE8D7 (void);
extern void WwiseSwitchGroupReference_get_WwiseObjectType_mB165EEE2CF00F617E1BE0ACC65E860A763DD4F23 (void);
extern void WwiseSwitchGroupReference__ctor_mA3404E888594F8395B2C2D427138195EFEC977EF (void);
extern void WwiseSwitchReference_get_WwiseObjectType_mEAC2278F229767A75FF8F7A911C3331047BE6DCB (void);
extern void WwiseSwitchReference_get_GroupObjectReference_m24CFBD052A5427FBF8CBA89B500FF8A147DC0FBC (void);
extern void WwiseSwitchReference_set_GroupObjectReference_m33A2D0894A493047C82F2E53265CE539FEAFE7FF (void);
extern void WwiseSwitchReference_get_GroupWwiseObjectType_m5752C1CE6AEA178DF62511A67C8E569D40BE950D (void);
extern void WwiseSwitchReference__ctor_m2C467745EB920161D35FF023AE7567471757E725 (void);
extern void WwiseTriggerReference_get_WwiseObjectType_m8803C9A253EA822BF26C81AA98E8CD13DC4401B9 (void);
extern void WwiseTriggerReference__ctor_m5DFEF241ABB3B9C7D5363CDECD0B43397F0CAEE0 (void);
extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_m0C565E782751A6EC90FA4E2CA6ABCDE2F9CC73B8 (void);
extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_m7CD7333CF15267618390E8ADCCB9D8FF7A6BD3D5 (void);
extern void AcousticTexture_get_ObjectReference_m92CB9F8807A499091B3DE98EBCB16BDEB5DC3392 (void);
extern void AcousticTexture_set_ObjectReference_m858E4F454F2C66688EE825C1BA85C4BAA9658734 (void);
extern void AcousticTexture_get_WwiseObjectType_m5465FA69E61876EB408A308A480BC255B77E89C6 (void);
extern void AcousticTexture__ctor_m73FBEFF53010F4D0CAE40804C66C95D46E05B945 (void);
extern void AuxBus_get_ObjectReference_m8E4D9862B6815E399E6CD680445C8AC469302104 (void);
extern void AuxBus_set_ObjectReference_m42F5A4F0072621388D2C6B41A3C77BB261A24CED (void);
extern void AuxBus_get_WwiseObjectType_m70A8C32694B4D109D361669779ED977B84FB24D8 (void);
extern void AuxBus__ctor_m5C49C24879B2E40E4181EC818E753C6333537550 (void);
extern void Bank_get_WwiseObjectType_mA1D8930DC63AC13BE5D9D70E9974448937A5B805 (void);
extern void Bank_get_ObjectReference_mA8376E6267CB755C75A6912119D77FF5D88F8DB2 (void);
extern void Bank_set_ObjectReference_mBED8F9BB903F357BFC9F7CE509145D90505D213E (void);
extern void Bank_Load_m8FF0C2FD7D955C31C0B3324EC9889068D52A3C9F (void);
extern void Bank_LoadAsync_m7269E0AF1ABED95794034A1B351CC5DB19F14ADD (void);
extern void Bank_Unload_m3DCE1BDBBAB9D23E021A2CE7D1DA7CF3E5B286C3 (void);
extern void Bank__ctor_m9C950E859576ED8ECDBBEFC37E3B8709CE426597 (void);
extern void BaseGroupType_get_GroupWwiseObjectReference_mA1C43F41BFF6605956C9EE394CABBC81A6309913 (void);
extern void BaseGroupType_get_GroupId_mDA0FBE7906D24F88000CBB3061FD8E6374947813 (void);
extern void BaseGroupType_IsValid_mC45913E80625F7A4DDE1B8ECB18DF36F9FC75A05 (void);
extern void BaseGroupType_get_groupID_m0EFB336AE39F0D7A64DC0B1E437C41AE2B58B932 (void);
extern void BaseGroupType_get_groupGuid_mA0E33D967702A0759058DAC2955A8D52DEA8839A (void);
extern void BaseGroupType__ctor_mA73EB264C7D09BCA84D7BAAE6BBF98BC867C4A62 (void);
extern void BaseType_CombineHashCodes_m30D0F5605C330CC498329493FE6FAA01054A71EA (void);
extern void BaseType_get_Name_mC13DF744BDF6B174E8A3853B660DCEE3A65536A5 (void);
extern void BaseType_get_Id_m41EA46A562DBA79A842D890E2249BD2D5D0F6089 (void);
extern void BaseType_get_InvalidId_m643A75F152DA435DE631D294035F98C43840AA90 (void);
extern void BaseType_IsValid_m2A4C566B6EEDED66EEABCD3E93D134FF7D619085 (void);
extern void BaseType_Validate_m5D765406E5515C87D1CC328125311061FD573E7C (void);
extern void BaseType_Verify_m4D860DC1B07B2D25A8B82418FC27658EE526B933 (void);
extern void BaseType_ToString_m60DB1A5EB9B4C7D41F161F5BD1807D437B41EAC1 (void);
extern void BaseType_GetHashCode_m1FB30DB069A10478FC723AFF96218C2090EF54B4 (void);
extern void BaseType_get_ID_mB6BA5B55DD5EC8DE730895850D18F426D8F4B3BE (void);
extern void BaseType_get_valueGuid_m4DC74DBF1827BDCAF07B753B0A7227FA59EE7C17 (void);
extern void BaseType__ctor_mE2050374F166E7A5EBF8C37256C40FA58B53A7DD (void);
extern void CallbackFlags__ctor_m3CA14827BC2CAB58171B436A06FE0753EFC41645 (void);
extern void Event_get_PlayingId_mDE2374C1A3D92A50E427BAA3E20FFEF49E01D508 (void);
extern void Event_get_ObjectReference_mC695DF6786A36891CD11C1E3BB4AE4491727C2A0 (void);
extern void Event_set_ObjectReference_m0BCAB77D4DCA53F8DFEFC7F31A0245955FE0FF42 (void);
extern void Event_get_WwiseObjectType_m2B2658183759E173F6F0CB9698AEEAE43B82144C (void);
extern void Event_VerifyPlayingID_m183F8F694876382BDCA8EAF96664D1A90D1BC40C (void);
extern void Event_Post_mB160FB686DCE6117DFD5CF2D72C91904C38A6CE0 (void);
extern void Event_Post_m348E7C9C69B6BA8BE14B82182016C91E32BFAEB4 (void);
extern void Event_Post_m0A18301D73DCAD88910BAB669457611E78C41EE9 (void);
extern void Event_Post_m76E3DCA49B56CBCA131C3CA55E90EF2693B895A6 (void);
extern void Event_Stop_m5FFDE42704D28D5AB662676CE1490A1F8CB66412 (void);
extern void Event_ExecuteAction_mE3B7E40B2AF3A8E73A4B88BD9AD0F3522538C36A (void);
extern void Event_PostMIDI_mBA1F05F02C1198BFB42E28D165FB893622F827CF (void);
extern void Event_PostMIDI_m3C043CB84CE827D65B4DFA2ED6EDFE73339FFDED (void);
extern void Event_StopMIDI_mD41DD38EC82EB9A63D7A390AB7DA8835912020F9 (void);
extern void Event_StopMIDI_m4705DC7E41F253F339DA239DA758C777E946187B (void);
extern void Event__ctor_m5C143D10A6D17A591DEA7EE2D6437CCB9FAEDD51 (void);
extern void RTPC_get_ObjectReference_mDE3884453CDFFCAD1EBD5B9857DDC072BB9E3087 (void);
extern void RTPC_set_ObjectReference_mBCB8AE99AA50510AEDF238A10DC95FC6C7E45F47 (void);
extern void RTPC_get_WwiseObjectType_m168D727DBC1F4985091665A7AF9689BBBCF42FEB (void);
extern void RTPC_SetValue_m765C0481F6CAC3D2B9FEAB41D1774E1F98DD2B60 (void);
extern void RTPC_GetValue_m0EB3663E8462EE875A128BBD6A5F3148E35A8EF4 (void);
extern void RTPC_SetGlobalValue_m0548CCBCFF5A936F116FBFB7E6362B98A7B6F9DB (void);
extern void RTPC_GetGlobalValue_mF4657637A297B3192C0C7754F6BD81E4D1D4234E (void);
extern void RTPC__ctor_m3A875C6638149BABAEC28A4B654E7684C749798E (void);
extern void State_get_ObjectReference_m67FDA0D5CC57D778553054C04ABA28B252612E41 (void);
extern void State_set_ObjectReference_m25765C87BBD43668C8AF38E32226BFDEC05D3D95 (void);
extern void State_get_WwiseObjectType_mB9C302A006BBF7BC0C9BD8576A8BEDE23B479ED2 (void);
extern void State_get_WwiseObjectGroupType_m32EAC8FB89D01D41DAFDB42D744D5C9FA3676628 (void);
extern void State_SetValue_mB6229FBF21B5911F249B7F9F51EC639D0C5F5172 (void);
extern void State__ctor_mDC0EB5B9BC6632D388F6BBB0800AE59030B0D041 (void);
extern void Switch_get_ObjectReference_m6DB62E77FA2066B36A3B726365792810B9100877 (void);
extern void Switch_set_ObjectReference_mEBFF2FCB0E3F7D2E6A6F510B7C7AD82308423969 (void);
extern void Switch_get_WwiseObjectType_mE4EDF2461A164B6D5BA208B47CDEA4FDA0AFAAC2 (void);
extern void Switch_get_WwiseObjectGroupType_mFD2397BDF24299F3ACF9D76951E21C8072FF9F9C (void);
extern void Switch_SetValue_mFBA1D525B0A9E658CBAEB04B63F1D58260CE8C02 (void);
extern void Switch__ctor_m32308C1E0C65F7D4FF7356D967D8D8D8C9D6B69C (void);
extern void Trigger_get_ObjectReference_m49102D8B4335ECC1516607E70F68CE4BAB1C281D (void);
extern void Trigger_set_ObjectReference_mC83BF8F60A9B0DDC684FB1F59CE5E2D3C5B88A68 (void);
extern void Trigger_get_WwiseObjectType_mCD8A1EFE67A1B9973CB06CC3F71261C219C8D0C7 (void);
extern void Trigger_Post_m6391DE51F0EDF1E2A8087F879968FC4E09294B41 (void);
extern void Trigger__ctor_m8059B28C7D7B4E8E8039089DF772E90684594937 (void);
static Il2CppMethodPointer s_methodPointers[118] = 
{
	WwiseAcousticTextureReference_get_WwiseObjectType_mAC79B28F75932F1CA4908C1923A1118DBD4EB7B7,
	WwiseAcousticTextureReference__ctor_m85F17C9E80B325198F10511E553A5C8C2FD5CBA3,
	WwiseAuxBusReference_get_WwiseObjectType_m25249A7E0F5D5917F54FB0F9DD2A4BB36CDD2647,
	WwiseAuxBusReference__ctor_mF34FD7CD291D7E40687374683BC7082C0618868E,
	WwiseBankReference_get_WwiseObjectType_m6B39EE01C52FE8B1DBCE692AFC2A4D000B505DE1,
	WwiseBankReference__ctor_m8B36CA656F4B4217FAAB4967189D2F26B37B621E,
	WwiseEventReference_get_WwiseObjectType_mE14663741E79CB96450A20255E6BC397A21362FE,
	WwiseEventReference__ctor_m351B1BEF4B0A1EABE26AEE071A025F7A4A0A40EE,
	WwiseObjectReference_get_Guid_mC29EDF844BBBF614C843A27B3712918FA0D2F7B7,
	WwiseObjectReference_get_ObjectName_m254DB1A2042C708D0E85691FA041732056B0E236,
	WwiseObjectReference_get_DisplayName_mD3C6E8C17AE60F3449789B00DB13646B9BC9CD02,
	WwiseObjectReference_get_Id_mDB6026205D9926CC2C5E8EF590E265C397085928,
	NULL,
	WwiseObjectReference__ctor_mF7B17F11C8A85A33C41C1483520CB91FD8F4F2CB,
	NULL,
	NULL,
	NULL,
	WwiseGroupValueObjectReference_get_DisplayName_mC52BBA875E7DBA93AB58480E5F46211F2459AC23,
	WwiseGroupValueObjectReference__ctor_m19EBF7F6E30A5CEAE1474B227FC14ED74E95B477,
	WwiseRtpcReference_get_WwiseObjectType_mFB42AF391986743A32CCA62EF0B5042677BE9FEF,
	WwiseRtpcReference__ctor_m4D530F70CC6FD40202137BE068E9D47F9BE172B6,
	WwiseStateGroupReference_get_WwiseObjectType_mE406C005E17C363AF2DD6463C45F9E3937E6A65A,
	WwiseStateGroupReference__ctor_m90C748605A4E0E93E71CA472FD80BDE7EAAEE0BB,
	WwiseStateReference_get_WwiseObjectType_m0B56F158229FE6C7C60F1141E6E4947878C0B39A,
	WwiseStateReference_get_GroupObjectReference_mDE7BA981D82CAF320E2C6432A60CC7F53191C858,
	WwiseStateReference_set_GroupObjectReference_mBE5E6C65C872117D1EA7D47394ED6CB16A82A908,
	WwiseStateReference_get_GroupWwiseObjectType_m23EFA97FE87EBE71BDB073C3F4B624627CD6D6B9,
	WwiseStateReference__ctor_mBBB93802F6A30989DDDC16DCCC9F3DB8815BE8D7,
	WwiseSwitchGroupReference_get_WwiseObjectType_mB165EEE2CF00F617E1BE0ACC65E860A763DD4F23,
	WwiseSwitchGroupReference__ctor_mA3404E888594F8395B2C2D427138195EFEC977EF,
	WwiseSwitchReference_get_WwiseObjectType_mEAC2278F229767A75FF8F7A911C3331047BE6DCB,
	WwiseSwitchReference_get_GroupObjectReference_m24CFBD052A5427FBF8CBA89B500FF8A147DC0FBC,
	WwiseSwitchReference_set_GroupObjectReference_m33A2D0894A493047C82F2E53265CE539FEAFE7FF,
	WwiseSwitchReference_get_GroupWwiseObjectType_m5752C1CE6AEA178DF62511A67C8E569D40BE950D,
	WwiseSwitchReference__ctor_m2C467745EB920161D35FF023AE7567471757E725,
	WwiseTriggerReference_get_WwiseObjectType_m8803C9A253EA822BF26C81AA98E8CD13DC4401B9,
	WwiseTriggerReference__ctor_m5DFEF241ABB3B9C7D5363CDECD0B43397F0CAEE0,
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_m0C565E782751A6EC90FA4E2CA6ABCDE2F9CC73B8,
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_m7CD7333CF15267618390E8ADCCB9D8FF7A6BD3D5,
	AcousticTexture_get_ObjectReference_m92CB9F8807A499091B3DE98EBCB16BDEB5DC3392,
	AcousticTexture_set_ObjectReference_m858E4F454F2C66688EE825C1BA85C4BAA9658734,
	AcousticTexture_get_WwiseObjectType_m5465FA69E61876EB408A308A480BC255B77E89C6,
	AcousticTexture__ctor_m73FBEFF53010F4D0CAE40804C66C95D46E05B945,
	AuxBus_get_ObjectReference_m8E4D9862B6815E399E6CD680445C8AC469302104,
	AuxBus_set_ObjectReference_m42F5A4F0072621388D2C6B41A3C77BB261A24CED,
	AuxBus_get_WwiseObjectType_m70A8C32694B4D109D361669779ED977B84FB24D8,
	AuxBus__ctor_m5C49C24879B2E40E4181EC818E753C6333537550,
	Bank_get_WwiseObjectType_mA1D8930DC63AC13BE5D9D70E9974448937A5B805,
	Bank_get_ObjectReference_mA8376E6267CB755C75A6912119D77FF5D88F8DB2,
	Bank_set_ObjectReference_mBED8F9BB903F357BFC9F7CE509145D90505D213E,
	Bank_Load_m8FF0C2FD7D955C31C0B3324EC9889068D52A3C9F,
	Bank_LoadAsync_m7269E0AF1ABED95794034A1B351CC5DB19F14ADD,
	Bank_Unload_m3DCE1BDBBAB9D23E021A2CE7D1DA7CF3E5B286C3,
	Bank__ctor_m9C950E859576ED8ECDBBEFC37E3B8709CE426597,
	BaseGroupType_get_GroupWwiseObjectReference_mA1C43F41BFF6605956C9EE394CABBC81A6309913,
	NULL,
	BaseGroupType_get_GroupId_mDA0FBE7906D24F88000CBB3061FD8E6374947813,
	BaseGroupType_IsValid_mC45913E80625F7A4DDE1B8ECB18DF36F9FC75A05,
	BaseGroupType_get_groupID_m0EFB336AE39F0D7A64DC0B1E437C41AE2B58B932,
	BaseGroupType_get_groupGuid_mA0E33D967702A0759058DAC2955A8D52DEA8839A,
	BaseGroupType__ctor_mA73EB264C7D09BCA84D7BAAE6BBF98BC867C4A62,
	BaseType_CombineHashCodes_m30D0F5605C330CC498329493FE6FAA01054A71EA,
	NULL,
	NULL,
	NULL,
	BaseType_get_Name_mC13DF744BDF6B174E8A3853B660DCEE3A65536A5,
	BaseType_get_Id_m41EA46A562DBA79A842D890E2249BD2D5D0F6089,
	BaseType_get_InvalidId_m643A75F152DA435DE631D294035F98C43840AA90,
	BaseType_IsValid_m2A4C566B6EEDED66EEABCD3E93D134FF7D619085,
	BaseType_Validate_m5D765406E5515C87D1CC328125311061FD573E7C,
	BaseType_Verify_m4D860DC1B07B2D25A8B82418FC27658EE526B933,
	BaseType_ToString_m60DB1A5EB9B4C7D41F161F5BD1807D437B41EAC1,
	BaseType_GetHashCode_m1FB30DB069A10478FC723AFF96218C2090EF54B4,
	BaseType_get_ID_mB6BA5B55DD5EC8DE730895850D18F426D8F4B3BE,
	BaseType_get_valueGuid_m4DC74DBF1827BDCAF07B753B0A7227FA59EE7C17,
	BaseType__ctor_mE2050374F166E7A5EBF8C37256C40FA58B53A7DD,
	CallbackFlags__ctor_m3CA14827BC2CAB58171B436A06FE0753EFC41645,
	Event_get_PlayingId_mDE2374C1A3D92A50E427BAA3E20FFEF49E01D508,
	Event_get_ObjectReference_mC695DF6786A36891CD11C1E3BB4AE4491727C2A0,
	Event_set_ObjectReference_m0BCAB77D4DCA53F8DFEFC7F31A0245955FE0FF42,
	Event_get_WwiseObjectType_m2B2658183759E173F6F0CB9698AEEAE43B82144C,
	Event_VerifyPlayingID_m183F8F694876382BDCA8EAF96664D1A90D1BC40C,
	Event_Post_mB160FB686DCE6117DFD5CF2D72C91904C38A6CE0,
	Event_Post_m348E7C9C69B6BA8BE14B82182016C91E32BFAEB4,
	Event_Post_m0A18301D73DCAD88910BAB669457611E78C41EE9,
	Event_Post_m76E3DCA49B56CBCA131C3CA55E90EF2693B895A6,
	Event_Stop_m5FFDE42704D28D5AB662676CE1490A1F8CB66412,
	Event_ExecuteAction_mE3B7E40B2AF3A8E73A4B88BD9AD0F3522538C36A,
	Event_PostMIDI_mBA1F05F02C1198BFB42E28D165FB893622F827CF,
	Event_PostMIDI_m3C043CB84CE827D65B4DFA2ED6EDFE73339FFDED,
	Event_StopMIDI_mD41DD38EC82EB9A63D7A390AB7DA8835912020F9,
	Event_StopMIDI_m4705DC7E41F253F339DA239DA758C777E946187B,
	Event__ctor_m5C143D10A6D17A591DEA7EE2D6437CCB9FAEDD51,
	RTPC_get_ObjectReference_mDE3884453CDFFCAD1EBD5B9857DDC072BB9E3087,
	RTPC_set_ObjectReference_mBCB8AE99AA50510AEDF238A10DC95FC6C7E45F47,
	RTPC_get_WwiseObjectType_m168D727DBC1F4985091665A7AF9689BBBCF42FEB,
	RTPC_SetValue_m765C0481F6CAC3D2B9FEAB41D1774E1F98DD2B60,
	RTPC_GetValue_m0EB3663E8462EE875A128BBD6A5F3148E35A8EF4,
	RTPC_SetGlobalValue_m0548CCBCFF5A936F116FBFB7E6362B98A7B6F9DB,
	RTPC_GetGlobalValue_mF4657637A297B3192C0C7754F6BD81E4D1D4234E,
	RTPC__ctor_m3A875C6638149BABAEC28A4B654E7684C749798E,
	State_get_ObjectReference_m67FDA0D5CC57D778553054C04ABA28B252612E41,
	State_set_ObjectReference_m25765C87BBD43668C8AF38E32226BFDEC05D3D95,
	State_get_WwiseObjectType_mB9C302A006BBF7BC0C9BD8576A8BEDE23B479ED2,
	State_get_WwiseObjectGroupType_m32EAC8FB89D01D41DAFDB42D744D5C9FA3676628,
	State_SetValue_mB6229FBF21B5911F249B7F9F51EC639D0C5F5172,
	State__ctor_mDC0EB5B9BC6632D388F6BBB0800AE59030B0D041,
	Switch_get_ObjectReference_m6DB62E77FA2066B36A3B726365792810B9100877,
	Switch_set_ObjectReference_mEBFF2FCB0E3F7D2E6A6F510B7C7AD82308423969,
	Switch_get_WwiseObjectType_mE4EDF2461A164B6D5BA208B47CDEA4FDA0AFAAC2,
	Switch_get_WwiseObjectGroupType_mFD2397BDF24299F3ACF9D76951E21C8072FF9F9C,
	Switch_SetValue_mFBA1D525B0A9E658CBAEB04B63F1D58260CE8C02,
	Switch__ctor_m32308C1E0C65F7D4FF7356D967D8D8D8C9D6B69C,
	Trigger_get_ObjectReference_m49102D8B4335ECC1516607E70F68CE4BAB1C281D,
	Trigger_set_ObjectReference_mC83BF8F60A9B0DDC684FB1F59CE5E2D3C5B88A68,
	Trigger_get_WwiseObjectType_mCD8A1EFE67A1B9973CB06CC3F71261C219C8D0C7,
	Trigger_Post_m6391DE51F0EDF1E2A8087F879968FC4E09294B41,
	Trigger__ctor_m8059B28C7D7B4E8E8039089DF772E90684594937,
};
static const int32_t s_InvokerIndices[118] = 
{
	12996,
	13298,
	12996,
	13298,
	12996,
	13298,
	12996,
	13298,
	12976,
	13052,
	13052,
	13261,
	0,
	13298,
	0,
	0,
	0,
	13052,
	13298,
	12996,
	13298,
	12996,
	13298,
	12996,
	13052,
	10682,
	12996,
	13298,
	12996,
	13298,
	12996,
	13052,
	10682,
	12996,
	13298,
	12996,
	13298,
	21374,
	13298,
	13052,
	10682,
	12996,
	13298,
	13052,
	10682,
	12996,
	13298,
	12996,
	13052,
	10682,
	4722,
	10682,
	13298,
	13298,
	13052,
	0,
	13261,
	12815,
	12996,
	13052,
	13298,
	20211,
	0,
	0,
	0,
	13052,
	13261,
	21347,
	12815,
	12815,
	10629,
	13052,
	12996,
	12996,
	13052,
	13298,
	13298,
	13261,
	13052,
	10682,
	12996,
	10891,
	9565,
	1704,
	1705,
	9567,
	2735,
	1893,
	5688,
	2774,
	10682,
	13298,
	13298,
	13052,
	10682,
	12996,
	5703,
	9457,
	10823,
	13195,
	13298,
	13052,
	10682,
	12996,
	12996,
	13298,
	13298,
	13052,
	10682,
	12996,
	12996,
	10682,
	13298,
	13052,
	10682,
	12996,
	10682,
	13298,
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_AK_Wwise_Unity_API_WwiseTypes_CodeGenModule;
const Il2CppCodeGenModule g_AK_Wwise_Unity_API_WwiseTypes_CodeGenModule = 
{
	"AK.Wwise.Unity.API.WwiseTypes.dll",
	118,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};

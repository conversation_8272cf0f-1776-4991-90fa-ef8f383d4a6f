﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_m4031AFE657BCCFF48129E3DF955DA329882861D9 (void);
extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_mD001C0A5A5D4602942338E61395727E5E0A7C15D (void);
extern void SingletonMgr_Register_m8B9DD4685E470FBD632ADC5C06381AD0A032604A (void);
extern void SingletonMgr_Unregister_mB0FCC77E9790E0538D113D78CF8A0AC1205E3B42 (void);
extern void SingletonMgr_Clear_m88ACCFAFB8AF79024ECDB3AF31465C93EA9A9EA4 (void);
extern void SingletonMgr__cctor_mC0103DB1EEF48A15274EF43A90BBB49FA2CA2B36 (void);
static Il2CppMethodPointer s_methodPointers[17] = 
{
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_m4031AFE657BCCFF48129E3DF955DA329882861D9,
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_mD001C0A5A5D4602942338E61395727E5E0A7C15D,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	SingletonMgr_Register_m8B9DD4685E470FBD632ADC5C06381AD0A032604A,
	SingletonMgr_Unregister_mB0FCC77E9790E0538D113D78CF8A0AC1205E3B42,
	SingletonMgr_Clear_m88ACCFAFB8AF79024ECDB3AF31465C93EA9A9EA4,
	SingletonMgr__cctor_mC0103DB1EEF48A15274EF43A90BBB49FA2CA2B36,
};
static const int32_t s_InvokerIndices[17] = 
{
	21394,
	13298,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	20847,
	20847,
	21355,
	21355,
};
static const Il2CppTokenRangePair s_rgctxIndices[2] = 
{
	{ 0x02000005, { 0, 4 } },
	{ 0x02000006, { 4, 5 } },
};
extern const uint32_t g_rgctx_MonoSingleton_1_t08C305D0BE6DCE33117AF8FB74717A694DEA7F76;
extern const uint32_t g_rgctx_T_t7C7CC98A0F84FCF3CD05860279389BEEA5EB9CD4;
extern const uint32_t g_rgctx_MonoSingleton_1_t08C305D0BE6DCE33117AF8FB74717A694DEA7F76;
extern const uint32_t g_rgctx_MonoSingleton_1_Initialize_m538183D260B03E587884819E2C5F5519CF1B9625;
extern const uint32_t g_rgctx_Singleton_1_tDF8902F66FF7B9E15A3AAC1D6E95084E20AF13DA;
extern const uint32_t g_rgctx_T_tB8381E26A05B5703C16DDA7CA9E6567B6D330CEE;
extern const uint32_t g_rgctx_Singleton_1_tDF8902F66FF7B9E15A3AAC1D6E95084E20AF13DA;
extern const uint32_t g_rgctx_Activator_CreateInstance_TisT_tB8381E26A05B5703C16DDA7CA9E6567B6D330CEE_mCB5F01D48B420E2B3E1A3A3BC7E78722BBDC1406;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_tB8381E26A05B5703C16DDA7CA9E6567B6D330CEE_ISingleton_Initialize_mE9EA225872967606CA27B9F388C6C35DF0EDAB5C;
static const Il2CppRGCTXDefinition s_rgctxValues[9] = 
{
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MonoSingleton_1_t08C305D0BE6DCE33117AF8FB74717A694DEA7F76 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t7C7CC98A0F84FCF3CD05860279389BEEA5EB9CD4 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MonoSingleton_1_t08C305D0BE6DCE33117AF8FB74717A694DEA7F76 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MonoSingleton_1_Initialize_m538183D260B03E587884819E2C5F5519CF1B9625 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Singleton_1_tDF8902F66FF7B9E15A3AAC1D6E95084E20AF13DA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tB8381E26A05B5703C16DDA7CA9E6567B6D330CEE },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Singleton_1_tDF8902F66FF7B9E15A3AAC1D6E95084E20AF13DA },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Activator_CreateInstance_TisT_tB8381E26A05B5703C16DDA7CA9E6567B6D330CEE_mCB5F01D48B420E2B3E1A3A3BC7E78722BBDC1406 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_tB8381E26A05B5703C16DDA7CA9E6567B6D330CEE_ISingleton_Initialize_mE9EA225872967606CA27B9F388C6C35DF0EDAB5C },
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_Singleton_CodeGenModule;
const Il2CppCodeGenModule g_Singleton_CodeGenModule = 
{
	"Singleton.dll",
	17,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	2,
	s_rgctxIndices,
	9,
	s_rgctxValues,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};

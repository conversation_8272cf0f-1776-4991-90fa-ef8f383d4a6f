{"logs": [{"outputFile": "com.moolego.rgame.launcher-mergeReleaseResources-3:/values/values.xml", "map": [{"source": "E:\\Workspace\\RGameClient\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\launcher\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "2,3", "startColumns": "2,2", "startOffsets": "55,97", "endColumns": "40,65", "endOffsets": "93,160"}, "to": {"startLines": "9,10", "startColumns": "4,4", "startOffsets": "424,467", "endColumns": "42,67", "endOffsets": "462,530"}}, {"source": "E:\\Workspace\\RGameClient\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\build\\intermediates\\packaged_res\\release\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,11,14", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,123,169,226,282,331,377,424,524,662", "endLines": "2,3,4,5,6,7,8,10,13,17", "endColumns": "67,45,56,55,48,45,46,8,8,8", "endOffsets": "118,164,221,277,326,372,419,519,657,884"}, "to": {"startLines": "2,3,4,5,6,7,8,11,13,16", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,123,169,226,282,331,377,535,635,773", "endLines": "2,3,4,5,6,7,8,12,15,19", "endColumns": "67,45,56,55,48,45,46,8,8,8", "endOffsets": "118,164,221,277,326,372,419,630,768,995"}}]}]}
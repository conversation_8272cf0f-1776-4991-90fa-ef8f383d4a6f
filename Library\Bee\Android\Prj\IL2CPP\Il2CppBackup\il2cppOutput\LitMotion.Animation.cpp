﻿#include "pch-cpp.hpp"

#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include <limits>


struct VirtualActionInvoker0
{
	typedef void (*Action)(void*, const RuntimeMethod*);

	static inline void Invoke (Il2CppMethodSlot slot, RuntimeObject* obj)
	{
		const VirtualInvokeData& invokeData = il2cpp_codegen_get_virtual_invoke_data(slot, obj);
		((Action)invokeData.methodPtr)(obj, invokeData.method);
	}
};
template <typename T1>
struct VirtualActionInvoker1
{
	typedef void (*Action)(void*, T1, const RuntimeMethod*);

	static inline void Invoke (Il2CppMethodSlot slot, RuntimeObject* obj, T1 p1)
	{
		const VirtualInvokeData& invokeData = il2cpp_codegen_get_virtual_invoke_data(slot, obj);
		((Action)invokeData.methodPtr)(obj, p1, invokeData.method);
	}
};
template <typename R>
struct VirtualFuncInvoker0
{
	typedef R (*Func)(void*, const RuntimeMethod*);

	static inline R Invoke (Il2CppMethodSlot slot, RuntimeObject* obj)
	{
		const VirtualInvokeData& invokeData = il2cpp_codegen_get_virtual_invoke_data(slot, obj);
		return ((Func)invokeData.methodPtr)(obj, invokeData.method);
	}
};
template <typename R, typename T1>
struct InterfaceFuncInvoker1
{
	typedef R (*Func)(void*, T1, const RuntimeMethod*);

	static inline R Invoke (Il2CppMethodSlot slot, RuntimeClass* declaringInterface, RuntimeObject* obj, T1 p1)
	{
		const VirtualInvokeData& invokeData = il2cpp_codegen_get_interface_invoke_data(slot, obj, declaringInterface);
		return ((Func)invokeData.methodPtr)(obj, p1, invokeData.method);
	}
};
template <typename R, typename T1, typename T2>
struct InterfaceFuncInvoker2
{
	typedef R (*Func)(void*, T1, T2, const RuntimeMethod*);

	static inline R Invoke (Il2CppMethodSlot slot, RuntimeClass* declaringInterface, RuntimeObject* obj, T1 p1, T2 p2)
	{
		const VirtualInvokeData& invokeData = il2cpp_codegen_get_interface_invoke_data(slot, obj, declaringInterface);
		return ((Func)invokeData.methodPtr)(obj, p1, p2, invokeData.method);
	}
};
template <typename R, typename T1>
struct GenericInterfaceFuncInvoker1
{
	typedef R (*Func)(void*, T1, const RuntimeMethod*);

	static inline R Invoke (const RuntimeMethod* method, RuntimeObject* obj, T1 p1)
	{
		VirtualInvokeData invokeData;
		il2cpp_codegen_get_generic_interface_invoke_data(method, obj, &invokeData);
		return ((Func)invokeData.methodPtr)(obj, p1, invokeData.method);
	}
};

struct Action_1_tD69A6DC9FBE94131E52F5A73B2A9D4AB51EEC404;
struct Action_1_tB93AB717F9D419A1BEC832FF76E74EAA32184CC1;
struct Action_2_t61CA71EC63C565FB5A59A8A8E52B6C7336CD7A45;
struct Action_2_t02F23795BC96180858055AB64D80A7006668D4D6;
struct Action_2_t25A8547AFC85E4914143093EDA11AE27C97D9E10;
struct Action_2_t000191B3D702D8E6114AA07D7039CA5909A6BF5E;
struct ColorPropertyAnimationComponent_1_tAD1067F24BBAF944E40270FCC4CBBB6A0F82380D;
struct ColorPropertyAnimationComponent_1_t2DCF6BE5B232AEEC66DE5551BC91B57589438F4C;
struct ColorPropertyAnimationComponent_1_tE6AEF5F64546DD2C24C3A74C1AE6F63EF46DABD9;
struct ColorPropertyAnimationComponent_1_t1A004C26C26C52867C5A5F2113F7CE3672961051;
struct ColorPropertyAnimationComponent_1_tF4E4FDC0F5884AB0CB16F991028753C583BB3E44;
struct ColorPropertyAnimationComponent_1_t2D91760F9E1E05B38E267F816DBC28A81B2F74AC;
struct ColorPropertyAnimationComponent_1_t39834DD27CD732DFE4F633FFD9F2D6A086CCBB06;
struct ColorPropertyAnimationComponent_1_tF608D7D00AEB026C1B6A6C045B2055450B730FA3;
struct Dictionary_2_tABE19B9C5C52F1DE14F0D3287B2696E7D7419180;
struct FixedString512BytesPropertyAnimationComponent_1_t478A8D50B267C479B29E1ACB7AB82274236EB836;
struct FixedString512BytesPropertyAnimationComponent_1_t478DB1A1E260CF40B695F4D3603F5E706B027BDF;
struct FixedString512BytesPropertyAnimationComponent_1_t12AAC49370780B9B738527218781305E8DD49E8E;
struct FloatPropertyAnimationComponent_1_t766705FAD368B247FAB08A4A977B8B3529AC1933;
struct FloatPropertyAnimationComponent_1_t37A61899F20322CCCFA7C09CD5B7271B76DCEC17;
struct FloatPropertyAnimationComponent_1_t3EE1259037FE3CF6B6303A336C605A0F318CB5E6;
struct FloatPropertyAnimationComponent_1_tBF445C4B23C073DFA04D5E88341C03BB479265B7;
struct FloatPropertyAnimationComponent_1_t4EDF64705C1358D105E46606D44590DA7EE60C40;
struct FloatPropertyAnimationComponent_1_tFE951CD9C72E8FD2C77BE0F3B0997FF9E3D23856;
struct FloatPropertyAnimationComponent_1_t48E23230B8D939712EE3FA3062665415D1B00BAB;
struct FloatPropertyAnimationComponent_1_t1AB552982C92303A2C6CC5876C59DC138C174E43;
struct FloatPropertyAnimationComponent_1_t230707BA48829ADA4918D773DC2E3DF24F0080C7;
struct Func_3_tC721DF8CDD07ED66A4833A19A2ED2302608C906C;
struct Func_3_t6F6D9932638EA1A5A45303C6626C818C25D164E5;
struct IReadOnlyList_1_t5FF3E813F0016E1F213DC9C4D573219691851E86;
struct IntPropertyAnimationComponent_1_tB4B79248A9306E45219CF94F18CB2856CA5E3B68;
struct IntPropertyAnimationComponent_1_tD1459341AC334ADE02035F3FFB921C4DDB7859CA;
struct IntPropertyAnimationComponent_1_t41237611C68CEA9B10C6849A5D5AE94883405EE5;
struct List_1_t2CDCA768E7F493F5EDEBC75AEB200FD621354E35;
struct List_1_t58F89DEDCD7DABB0CFB009AAD9C0CFE061592252;
struct List_1_tE6BB71ABF15905EFA2BE92C38A2716547AEADB19;
struct List_1_tA25D79C6E8BB82D03B97AB0F2F2EA71EE31D2AF3;
struct MotionBuilderBuffer_2_t04E3EEA09283FAF1D9197F4BE4C39D9944C98311;
struct MotionBuilderBuffer_2_t190528172F72DD395324243FA145EE61169E32DD;
struct MotionSettings_2_t366DDAE7CADD1434DF2C190978E1C0C0B14A2316;
struct Queue_1_t7CF7AD96EFA79CC0D603DFB407D7945D8F635C5B;
struct Queue_1_tE9EF546915795972C3BFD68FBB8FA859D3BAF3B5;
struct RectPropertyAnimationComponent_1_t4A7ABE05AD402BC5405540B43257C6CAD0704C6D;
struct RectPropertyAnimationComponent_1_t250A38A245D16CE5E221D2A0697F92DCB6592D73;
struct Rigidbody2DPositionAnimationBase_2_t69CE9CD2C9AB3634CDE70A4430336030629AE27A;
struct Rigidbody2DPositionAnimationBase_2_tEC44827A997686FCA2419EFAB9EBE6C21594FA08;
struct Rigidbody2DPositionAnimationBase_2_tD610427108FEF8F8BE698CEB4A2CECD0661F7A50;
struct Rigidbody2DRotationAnimationBase_2_t86890366317CD729D92AE6912188756B638532DB;
struct Rigidbody2DRotationAnimationBase_2_tE0B9D89B886A179723638F20CECDFA9455B1FFCE;
struct Rigidbody2DRotationAnimationBase_2_tFE21E96D35A59F82D112D56BB1CD4DACE31FF142;
struct RigidbodyPositionAnimationBase_2_t3F8DF2590F9F24CFEC040865A4F75F1112C82612;
struct RigidbodyPositionAnimationBase_2_t4B7F6634781E49079650FC961B633396250CCB64;
struct RigidbodyPositionAnimationBase_2_tFF37C9F46D719B654E722FB0A986E443FEA95121;
struct RigidbodyRotationAnimationBase_2_t66E15E7EA84710E8BE780C9D318AC50C679161CF;
struct RigidbodyRotationAnimationBase_2_t4ED992FE889ACCD94A95BB34F67343C3D80FD125;
struct RigidbodyRotationAnimationBase_2_t1817E2F258DFDA6844D7130B71D79AF68737E26E;
struct SerializableMotionSettings_2_t3693954AA2D12D8178DB11DEC7E90BB1E76E77FA;
struct SerializableMotionSettings_2_t034045EE86761080D86B40BCC8800E384935F499;
struct SerializableMotionSettings_2_tF48B33D24FDF5B9DD96E1CE305D788D9D67A704E;
struct SerializableMotionSettings_2_t1FDFBAD6B5C5F21C24DBC093EFE2E877001AACA8;
struct SerializableMotionSettings_2_tB573D3BB787DCCC4166AA217DB2424C3C4A8787E;
struct SerializableMotionSettings_2_t648AF06EFD8D9425B51AB45F8F5A0558688D2CB9;
struct SerializableMotionSettings_2_tBBF740A1ABE1D1FB72291E86D0A18E1D0344615B;
struct SerializableMotionSettings_2_t56C9F73DC1787F07D5384F25A751DE5F2545FCF2;
struct SerializableMotionSettings_2_t43CB1E09C9A34B62C32B4D459EDD9F4B6B550BB0;
struct SerializableMotionSettings_2_t30307AE6A2C15AC189466AF2611AC7B1E0B9CF34;
struct SerializableMotionSettings_2_tD4D79FB48DACE0789874BFFB567CA8E370472B4F;
struct SerializableMotionSettings_2_t30E1FF6A91CF9E004A78F436437C4041445AAE30;
struct SerializableMotionSettings_2_tB03FD30C5F0B8FA9208B7DEDC0DCE8428F3D4BA3;
struct SerializableMotionSettings_2_t941B49604D2AED174AA3273AF5D5F123B4A524E0;
struct SerializableMotionSettings_2_t55B63654E284330688B1B3E6E3118556152DA199;
struct SerializableMotionSettings_2_t68BA1EF7C3F3B237B28B6AFFA427FC7555F2DD55;
struct TransformPositionAnimationBase_2_t12DE280ED40D363B87FDE5057F2A16BAE67FF802;
struct TransformPositionAnimationBase_2_tE046D85AA8257B076AEA1EC06523CD68298DC058;
struct TransformPositionAnimationBase_2_t4B424C50A5AC08FC89469AD266ABFD58DE673D3C;
struct TransformRotationAnimationBase_2_tFFAE1B23BA584F4A5C424117F0D678258C5D31F2;
struct TransformRotationAnimationBase_2_t844162742B1631A9477AD8582685D20A7F4A8563;
struct TransformRotationAnimationBase_2_t8558AFAC045C23114B034681003F2C2F07D02A8E;
struct TransformScaleAnimationBase_2_tBE02F8DFD3B63F89AD312977DEA9C0F63D75C8CA;
struct TransformScaleAnimationBase_2_t17D49FB722A1A6F77DC3B5D3EB244F546109A4B0;
struct TransformScaleAnimationBase_2_t35F2BE396D00623EE3AFF251C9245768B42F60CB;
struct TweenRunner_1_t5BB0582F926E75E2FE795492679A6CF55A4B4BC4;
struct UnityEvent_1_tF4BE0B078FD22C6D76548861637E94AB782888C9;
struct UnityEvent_1_t7EBD40037C3DBB4EEFE941AEFD2E3CA88C7382ED;
struct UnityEvent_1_t7CC0661D6B113117B4CC68761D93AC8DF5DBD66A;
struct UnityEvent_1_t04EB8F75BA20E19772BBB0023A57CC7FBAFED743;
struct UnityEvent_1_t3CE03B42D5873C0C0E0692BEE72E1E6D5399F205;
struct UnityEvent_1_tDD811EB8F49CEE97BA6DF59344DFE6C6F42553D4;
struct UnityEvent_1_t8ABE5544759145B8D7A09F1C54FFCB6907EDD56E;
struct UnityEvent_1_tC9859540CF1468306CAB6D758C0A0D95DBCEC257;
struct UnityEvent_1_t9A868DD8EBFC0D9D8134D903A170ECBDEE567932;
struct UnityEvent_1_tB42B7E8E9010FF524B45FD0EC7AD37D7D3B006AE;
struct UnityEvent_1_t7FA3641C06EC4F8BD5600438DB4CB16B7042FB59;
struct ValueAnimationComponent_3_t02507D0A207D68A1BC1701CC8EE80C6B5DAD75E0;
struct ValueAnimationComponent_3_tB17C7EB4A1D9997DB82C86BAE9692FCE017D2415;
struct ValueAnimationComponent_3_t595F5BA6422722CDF28C79CF2971881EFCC09CCD;
struct ValueAnimationComponent_3_t92CD7588E37E9CCDA6A882EE3FB36A61E34CF7D2;
struct ValueAnimationComponent_3_tCC2320109B3D50771B2F3D6B0F00DC4EDDAD7351;
struct ValueAnimationComponent_3_t4DCB9D2381C489A5D17579A5EF8F3B8E5ABA2876;
struct ValueAnimationComponent_3_t53C6B62F99FEFCE0BB2D49AF64FA7B03EFC2745D;
struct ValueAnimationComponent_3_tF4558CF2EFCE6A8F01D0043A88161A0AAD83882E;
struct Vector2PropertyAnimationComponent_1_t98697C175F4F833C0E6C686B39A0405631280CEA;
struct Vector2PropertyAnimationComponent_1_t8A177D739E23FF44639E13B99726B0A6C932D365;
struct Vector4PropertyAnimationComponent_1_tD5781699C642AFAE6FA17B1A9EC214F1A3DBDB75;
struct Vector4PropertyAnimationComponent_1_tBE0B4EE415ABAEDE12538C71AAB2A8017528ACA0;
struct TMP_TextProcessingStack_1U5BU5D_t08293E0BB072311BB96170F351D1083BCA97B9B2;
struct ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031;
struct CharU5BU5D_t799905CF001DD5F13F7DBB310181FC4D8B7D0AAB;
struct Color32U5BU5D_t38116C3E91765C4C5726CE12C77FAD7F9F737259;
struct DecimalU5BU5D_t93BA0C88FA80728F73B792EE1A5199D0C060B615;
struct DelegateU5BU5D_tC5AB7E8F745616680F337909D3A8E6C722CDF771;
struct FontWeightU5BU5D_t2A406B5BAB0DD0F06E7F1773DB062E4AF98067BA;
struct HighlightStateU5BU5D_tA878A0AF1F4F52882ACD29515AADC277EE135622;
struct HorizontalAlignmentOptionsU5BU5D_t4D185662282BFB910D8B9A8199E91578E9422658;
struct IMotionStorageU5BU5D_t436E5CDB6D8319F837EE47133B8E54906D829630;
struct Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C;
struct IntPtrU5BU5D_tFD177F8C806A6921AD7150264CCC62FA00CAD832;
struct LitMotionAnimationComponentU5BU5D_tA8718977F5DABB54F0422858CC77746CF7E85897;
struct MaterialU5BU5D_t2B1D11C42DB07A4400C0535F92DBB87A2E346D3D;
struct MaterialReferenceU5BU5D_t7491D335AB3E3E13CE9C0F5E931F396F6A02E1F2;
struct ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918;
struct RichTextTagAttributeU5BU5D_t5816316EFD8F59DBC30B9F88E15828C564E47B6D;
struct SelectableU5BU5D_t4160E135F02A40F75A63F787D36F31FEC6FE91A9;
struct SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C;
struct StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF;
struct TMP_CharacterInfoU5BU5D_t297D56FCF66DAA99D8FEA7C30F9F3926902C5B99;
struct TMP_ColorGradientU5BU5D_t2F65E8C42F268DFF33BB1392D94BCF5B5087308A;
struct TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB;
struct UIVertexU5BU5D_tBC532486B45D071A520751A90E819C77BA4E3D2F;
struct UInt32U5BU5D_t02FBD658AD156A17574ECE6106CF1FBFCC9807FA;
struct Vector2U5BU5D_tFEBBC94BCC6C9C88277BA04047D2B3FDB6ED7FDA;
struct Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C;
struct WordWrapStateU5BU5D_t473D59C9DBCC949CE72EF1EB471CBA152A6CEAC9;
struct UnicodeCharU5BU5D_t67F27D09F8EB28D2C42DFF16FE60054F157012F5;
struct Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07;
struct AnimationCurve_tCBFFAAD05CEBB35EF8D8631BD99914BE1A6BB354;
struct AnimationTriggers_tA0DC06F89C5280C6DD972F6F4C8A56D7F4F79074;
struct ArgumentException_tAD90411542A20A9C72D5CDA3A84181D8B947A263;
struct Attribute_tFDA8EFEFB0711976D22474794576DAF28F7440AA;
struct AudioSource_t871AC2272F896738252F04EE949AEF5B241D3299;
struct AudioSourcePitchAnimation_t62AB22027FB5C3AF778317F9B6E0CC0A1093D6F6;
struct AudioSourceVolumeAnimation_tD2D1289A6EBA9DE03DBB4B2E11EADC73F7B4C1BF;
struct Binder_t91BFCE95A7057FADF4D8A1A342AFE52872246235;
struct Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184;
struct CameraAspectAnimation_t7DB104395B9F6412E22D3ED76E390ECD41CF173A;
struct CameraBackgroundColorAnimation_tC9D3E651F449E23A3A476C80A1B13AFD549904C1;
struct CameraFarClipPlaneAnimation_tBFB0D31C2A77E1DDBD510EF20D65A3B0094416CE;
struct CameraFieldOfViewAnimation_t8B4975929A9FC5EB1292E0D061E461C5C500457F;
struct CameraNearClipPlaneAnimation_t31C2AEFB468ED028ABF534C762CB31DF85DA903F;
struct CameraOrthographicSizeAnimation_tF07CAB3E4E205B2FEC72C19488BAB814B8165969;
struct CameraPixelRectAnimation_tFB2DC1B4364CE768560D1DB65D91C3148520EFEB;
struct CameraRectAnimation_t27350DC7D603BA5AA49A5EEFC6FA9F8CDF070EF1;
struct CancellationTokenSource_tAAE1E0033BCFC233801F8CB4CED5C852B350CB7B;
struct Canvas_t2DB4CEFDFF732884866C83F11ABF75F5AE8FFB26;
struct CanvasGroup_t048C1461B14628CFAEBE6E7353093ADB04EBC094;
struct CanvasGroupAlphaAnimation_t117A030C75C58FCE864557262A0B9D6B6669D650;
struct CanvasRenderer_tAB9A55A976C4E3B2B37D0CE5616E5685A8B43860;
struct ColorValueAnimation_tF6326EB4342E17097D0A55A411436898AA192498;
struct DelayComponent_t02FB33B26EE45214DF563DDEFA55F59D4CBF582D;
struct Delegate_t;
struct DelegateData_t9B286B493293CD2D23A5B2B5EF0E5B1324C2B77E;
struct DoubleValueAnimation_t7880671F5296AFC7C98CC1B199266E8FD5018FBF;
struct EmbeddedAttribute_t4AF2D38B103036A7E5864EFB730772BD58CB94DE;
struct EventComponent_t25446BA52F33198616853D8AD89403122A0400FF;
struct Exception_t;
struct FloatValueAnimation_t1F8A888C348C3256D7562E59BCCE868B248CC70E;
struct FontData_tB8E562846C6CB59C43260F69AE346B9BF3157224;
struct Graphic_tCBFCA4585A19E2B75465AECFEAC43F4016BF7931;
struct GraphicColorAnimation_t42939D1590768EA413C629493D8BB748515A58DE;
struct IDictionary_t6D03155AF1FA9083817AA5B6AD7DEEACC26AB220;
struct IMotionScheduler_tF35BA86037CC995A61E69E65068D90170BB8A643;
struct IMotionStorage_tC498F012248E7231BB90DCBEDAC47F5E1A57AB7A;
struct ITextPreprocessor_tDBB49C8B68D7B80E8D233B9D9666C43981EFAAB9;
struct Image_tBC1D03F63BF71132E9A5E472B8742F172A011E7E;
struct ImageColorAlphaAnimation_t4BB2FC2C18CA060FFAD5E1D245318DD67D61E7B2;
struct ImageColorAnimation_tFFB6FA38A3ECA911EA2FCFF5F09B09870F809C7F;
struct ImageFillAmountAnimation_t05DF0A6E067043EE01BB0A2879930F5D76047CBB;
struct IntValueAnimation_t45CDBEB887CCC11D6E63FD4949100D30B9C74900;
struct InvalidOperationException_t5DDE4D49B7405FAAB1E4576F4715A42A3FAD4BAB;
struct InvokableCallList_t309E1C8C7CE885A0D2F98C84CEA77A8935688382;
struct IsUnmanagedAttribute_t3A3B7234A45164EED2468C5D59391259F9D6999B;
struct Keyframe_tB9C67DCBFE10C0AE9C52CB5C66E944255C9254F0;
struct LayoutElement_tB1F24CC11AF4AA87015C8D8EE06D22349C5BF40A;
struct LitMotionAnimation_t92F1AF7CF4859CEDD166DF7FD8091FDB2C34AF4A;
struct LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A;
struct LitMotionAnimationComponentMenuAttribute_t6C29773ED47514A8074EC34A851EFF85D572180A;
struct LongValueAnimation_t93592BE53D79469CDD3CAF211EA24407BC68FEBD;
struct Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3;
struct MaterialColorAnimation_tB8E52253F7D8EBFB2B345D4524125FF9BE2233BE;
struct MaterialFloatAnimation_t33F07CC2F42829138320058153639C8993A2E7F4;
struct MaterialIntAnimation_t743758EB02F1C6889456973EEBF255FC2EEC0F55;
struct MaterialVectorAnimation_t16760AE18B1441C66C97461AD42F886ECF16DA59;
struct MemberFilter_tF644F1AE82F611B677CE1964D5A3277DDA21D553;
struct Mesh_t6D9C539763A09BC2B12AEAEF36F6DFFC98AE63D4;
struct MethodInfo_t;
struct MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71;
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C;
struct PersistentCallGroup_tB826EDF15DC80F71BCBCD8E410FD959A04C33F25;
struct PlayLitMotionAnimationComponent_t44C212AF544D647258158A4B4979FF728DB597CE;
struct RectMask2D_tACF92BE999C791A665BD1ADEABF5BCEB82846670;
struct RectTransform_t6C5DA5E41A89E0F488B001E45E58963480E543A5;
struct RectTransformPivotAnimation_t08B1166052D57CC0DBE2F80C97A226462107161E;
struct RectTransformSizeDeltaAnimation_t0ADD7484D0932C20AD535D02E43A6D2A231D8AC3;
struct Rigidbody_t268697F5A994213ED97393309870968BC1C7393C;
struct Rigidbody2D_tBEBE9523CF4448544085AF46BF7E10AA499F320F;
struct Rigidbody2DPositionAnimation_t6384BF0D1B3C33236307B689CC87FEE9B1B1EC20;
struct Rigidbody2DPositionPunchAnimation_t4CE478F8B49D2CE9AECA0EF49DE21F695BC8AE2E;
struct Rigidbody2DPositionShakeAnimation_t83C832CD3C8323CF36DE5786F713CC66E190F2B5;
struct Rigidbody2DRotationAnimation_t1D9950D3CD8D64136141526DA47B1DD6544CAA9E;
struct Rigidbody2DRotationPunchAnimation_tF9792F42F38E6B7DBDF861BCA13DC95A15E25FF0;
struct Rigidbody2DRotationShakeAnimation_tFDF23B7A5FB3DDD00CB5060A5DCFC336B0965B8B;
struct RigidbodyPositionAnimation_tD5E8DC790768D7450851E943B0375C358EDE3153;
struct RigidbodyPositionPunchAnimation_t1E1EF5E7F5D33CF09BBA49998CEEBBEE50409E64;
struct RigidbodyPositionShakeAnimation_t835F38965062CA69323D55FD17352596B5D9E83F;
struct RigidbodyRotationAnimation_t78187C7A9D397C810CD2674EEA4C026C29A6383D;
struct RigidbodyRotationPunchAnimation_tD4DA1E4686D1038ED68A9AA90A906FCD96A6F8FE;
struct RigidbodyRotationShakeAnimation_t84A7DABACAA48B32D9F74051B32E5371DD1E5861;
struct SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6;
struct Selectable_t3251808068A17B8E92FB33590A4C2FA66D456712;
struct Slider_t87EA570E3D6556CABF57456C2F3873FFD86E652F;
struct SliderValueAnimation_t3DCA44DE2E0898CB1C626393BB657089FB9DAA8D;
struct Sprite_tAFF74BC83CD68037494CB0B4F28CBDF8971CAB99;
struct SpriteRenderer_t1DD7FE258F072E1FA87D6577BA27225892B8047B;
struct SpriteRendererColorAnimation_tB90E5AB6A31262E5049CFDA53172D0D3EEB8F766;
struct String_t;
struct StringValueAnimation_t4C21FFF21AA92CA403E695317EFDDC25F6F729A0;
struct TMPTextAnimation_t2E906E9232E91180CEC49B9D9C8EF37EE8908AF2;
struct TMPTextCharacterSpacingAnimation_t26A3F5C492B9F1B71D70B24EACEC606FA72228BD;
struct TMPTextColorAlphaAnimation_t25988B988761C4836D8EEC13DB51674914D70B98;
struct TMPTextColorAnimation_t47399F6153BA6F7A67AD03F194FF931600B1DAC4;
struct TMPTextFontSizeAnimation_t3B7B223B80DA067FF5B20970560EED95A74E0A06;
struct TMPTextLineSpacingAnimation_t1BDC9F5B32C3685301B009A9BFF9CB76407BCDA3;
struct TMPTextParagraphSpacingAnimation_t6FB4A8093F859DD882F9616613B76258C059AF93;
struct TMPTextWordSpacingAnimation_tF2EA00511F45C3E0D5CBDD069E5E08A242CC59DE;
struct TMP_Character_t7D37A55EF1A9FF6D0BFE6D50E86A00F80E7FAF35;
struct TMP_ColorGradient_t17B51752B4E9499A1FF7D875DCEC1D15A0F4AEBB;
struct TMP_FontAsset_t923BF2F78D7C5AC36376E168A1193B7CB4855160;
struct TMP_SpriteAnimator_t2E0F016A61CA343E3222FF51E7CF0E53F9F256E4;
struct TMP_SpriteAsset_t81F779E6F705CE190DC0D1F93A954CB8B1774B39;
struct TMP_Style_tA9E5B1B35EBFE24EF980CEA03251B638282E120C;
struct TMP_StyleSheet_t70C71699F5CB2D855C361DBB78A44C901236C859;
struct TMP_Text_tE8D677872D43AD4B2AAF0D6101692A17D0B251A9;
struct TMP_TextElement_t262A55214F712D4274485ABE5676E5254B84D0A5;
struct TMP_TextInfo_t09A8E906329422C3F0C059876801DD695B8D524D;
struct Text_tD60B2346DAA6666BF0D822FF607F0B220C2B9E62;
struct TextAnimation_t9777A34576E8E54B799AA1238A3B2D52E559A263;
struct TextColorAnimation_t6574309620A762556ADE0DAB9A913B1B76E0EF28;
struct TextFontSizeAnimation_t39852601D9D17EA06325D177ABFCBC4BEEE8C066;
struct TextGenerator_t85D00417640A53953556C01F9D4E7DDE1ABD8FEC;
struct Texture2D_tE6505BC111DD8A424A9DBE8E05D7D09E11FFFCF4;
struct Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1;
struct TransformPositionAnimation_tA672FD35BC9E5A38D23FC09076A16FDBCD8AD162;
struct TransformPositionPunchAnimation_t111BCFAE3984E761BF110D887515F103ACA6BEC7;
struct TransformPositionShakeAnimation_t9BC6FA5232E8683B7CE5365D2953ADB875C4402A;
struct TransformRotationAnimation_t181A786C31B7C8AC2C83152CCC24C0C82EED1785;
struct TransformRotationPunchAnimation_tDD2CD304B79268D5D844374DF14C4322733E3A01;
struct TransformRotationShakeAnimation_t1817882F0893684DF23BECBADBFD7E1458A69E1F;
struct TransformScaleAnimation_tEEEC832BD42C59D1A08B30347D67DF8C4839E102;
struct TransformScalePunchAnimation_t8C68CC300945C3F4CB4BCDA907D785A600080DDA;
struct TransformScaleShakeAnimation_tCEDC563ACEF258A7C21F2FCBF03F28648A065DA3;
struct Type_t;
struct UnityAction_t11A1F3B953B365C072A5DCC32677EE1796A962A7;
struct UnityEvent_tDC2C3548799DBC91D1E3F3DE60083A66F4751977;
struct UnitySourceGeneratedAssemblyMonoScriptTypes_v1_tEEC2B2FFC6DD357B5E6F61C238E835856299C93E;
struct Vector2ValueAnimation_tD1E052DC2FF67290D4853707C8D826CEEB001ABC;
struct Vector3ValueAnimation_t9E4D1D97B97516C598B84661502882CFC3E870EF;
struct Vector4ValueAnimation_t76776B5052E42EBBC0A23B83622EAA759229A76A;
struct VertexHelper_tB905FCB02AE67CBEE5F265FE37A5938FC5D136FE;
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915;
struct Volume_t7CAAEA22D7F13A50FAE114DE7A6986FEAC837377;
struct VolumeProfile_t9B5F2005F575A710F38A124EF81A6228CCACACE1;
struct VolumeWeightAnimation_t748E9F2603B2964DAF687456ECE284BB9FD23CF7;
struct CameraCallback_t844E527BFE37BC0495E7F67993E43C07642DA9DD;
struct CullStateChangedEvent_t6073CD0D951EC1256BF74B8F9107D68FC89B99B8;
struct ReapplyDrivenProperties_t3482EA130A01FF7EE2EEFE37F66A5215D08CFE24;
struct SliderEvent_t92A82EF6C62E15AF92B640FE2D960E877E8C6555;
struct U3CU3Ec_tDAFA5C4E997B098FAFADB5DBAD4676795BD4F04C;

IL2CPP_EXTERN_C RuntimeClass* Action_2_t000191B3D702D8E6114AA07D7039CA5909A6BF5E_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Action_2_t02F23795BC96180858055AB64D80A7006668D4D6_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* ArgumentException_tAD90411542A20A9C72D5CDA3A84181D8B947A263_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Debug_t8394C7EEAECA3689C2C9B9DE9C7166D73596276F_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Exception_t_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* FastListCore_1_t7622FA94EE4948D93B3B798B45AAE6DFAAC94519_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* FastListCore_1_tC5AF8169955F580A98B5AF92CC49BA523978EC21_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* IMotionStorage_tC498F012248E7231BB90DCBEDAC47F5E1A57AB7A_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* InvalidOperationException_t5DDE4D49B7405FAAB1E4576F4715A42A3FAD4BAB_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* MotionDebugger_t65073AE52251C2BB7A2D83C38CFE9FB805538D40_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* MotionDispatcher_t1390D0649134C8A06154D3B03FD2C726E353ED08_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* MotionManager_t55CCB0A78FD15BD0E274607AC5A3F35C3115763F_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* MotionScheduler_t98622FCF54DC19E35E9CB686D1D56DC64EE07AE5_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Queue_1_t7CF7AD96EFA79CC0D603DFB407D7945D8F635C5B_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Type_t_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* U3CU3Ec_tDAFA5C4E997B098FAFADB5DBAD4676795BD4F04C_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeField* U3CPrivateImplementationDetailsU3E_t6ECB8E8EED910A1EDD673CA4D1B8D1200B474B0B____350A400EB8CF293100456CE6B2B54169AC1689C33C50319718AAC01B4391F6B3_FieldInfo_var;
IL2CPP_EXTERN_C RuntimeField* U3CPrivateImplementationDetailsU3E_t6ECB8E8EED910A1EDD673CA4D1B8D1200B474B0B____4ACE9CA5EB9773B32830743CC01A3AFEDC23ADF9337FCE1CE0248C872EB42017_FieldInfo_var;
IL2CPP_EXTERN_C String_t* _stringLiteral0F11661CCB4BF59FC5EC30D63E4D71323F857EFE;
IL2CPP_EXTERN_C String_t* _stringLiteral47A3FAF17D89549FD0F0ECA7370B81F7C80DFCDE;
IL2CPP_EXTERN_C String_t* _stringLiteral79E0E0F15F51BD91F0DA337090C4FABDB4CD6644;
IL2CPP_EXTERN_C String_t* _stringLiteralDA39A3EE5E6B4B0D3255BFEF95601890AFD80709;
IL2CPP_EXTERN_C const RuntimeMethod* ColorPropertyAnimationComponent_1__ctor_m1AF482252E756F15D71CC78DB2C0938B29384FA6_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ColorPropertyAnimationComponent_1__ctor_m774CD3721E9CA656495B1AC9880CC105C67395F7_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ColorPropertyAnimationComponent_1__ctor_m8BE4FDFE086E6931EACF9112681328B5A24A1915_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ColorPropertyAnimationComponent_1__ctor_mA25D66433EC9D23253C25CF32C37A352ADEB804A_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ColorPropertyAnimationComponent_1__ctor_mCDCEA5280E525AC77C62F9975B1CBAC04B12DD45_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ColorPropertyAnimationComponent_1__ctor_mD6C4FA4DA1DEE210AAE15DA766F9B509CBDA935D_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ColorPropertyAnimationComponent_1__ctor_mEF8FD8C1434534785B3672D5C24EF181830F452A_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* FastListCore_1_Add_mD59F1028468A63C7DA7F6097093EFDDADD2BF0A7_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* FastListCore_1_AsSpan_mD0CAF2920CC176F2EFB6A3DE58FE4A2F0F30CCFF_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* FastListCore_1_Clear_m811C076F75B1B9EEC15258970957561A31093597_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* FastListCore_1_get_Item_m9E02DE7FC9F92F09CFAF6958B134367BB973A9F3_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* FixedString512BytesPropertyAnimationComponent_1__ctor_m5816EC6A01ADE6AB5F7F7845913E24E9F895A6D1_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* FixedString512BytesPropertyAnimationComponent_1__ctor_mA507E727510CA75D6217FB4C246A0F6F41FC8951_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* FixedStringMethods_ConvertToString_TisFixedString512Bytes_t0C425B0F2C07FEA1642C32BF8559116DF2BFF50E_mF5D257B46C5CC1CBFBC8A26BEA9F213A217A05EF_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* FloatPropertyAnimationComponent_1__ctor_m054E330B04F7CC58DB5069B067B6DE6AC4BEA62D_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* FloatPropertyAnimationComponent_1__ctor_m077CB2E8666EFD5B50DB2BC64E516558A088FB46_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* FloatPropertyAnimationComponent_1__ctor_m100EED231E7D0285C9CE776FC962DB8920AC5A3D_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* FloatPropertyAnimationComponent_1__ctor_m1DD2B509363FBC2BC129C88A860277D9028EF5CF_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* FloatPropertyAnimationComponent_1__ctor_m76B1B70CB536FF9DAD16FE9E20EE6EEDE9F0DC56_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* FloatPropertyAnimationComponent_1__ctor_mA94A443DF00FEBBCE145422ED585E95DB5DD0550_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* FloatPropertyAnimationComponent_1__ctor_mC888083E996E645550BEA6E923A8007C3C733830_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* FloatPropertyAnimationComponent_1__ctor_mFE8113C499820E874DA39EE35599E7CD5F90335A_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* IntPropertyAnimationComponent_1__ctor_m40A64E8F3FDE81C56C1CC916ED517491E25D985A_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* IntPropertyAnimationComponent_1__ctor_mED6C6022A4A66D182CE79D0E04160F1CF7D70360_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* LMotion_Create_TisFixedString512Bytes_t0C425B0F2C07FEA1642C32BF8559116DF2BFF50E_TisStringOptions_tDDA84E855CD29F2BCD9BA102EA173B219CFCADA7_TisFixedString512BytesMotionAdapter_tE0926B0F0FCE1F39802A3A7B8E7F6C7E191C023C_mB9F06C5F7EBD9AF7B4CC7B1FBDE30DF686CC5D02_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* LitMotionAnimation_MoveNextMotion_m758C60CEC79EB60134B2B283113E9F5D7D40B518_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* MemoryExtensions_Reverse_TisLitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A_m200CEA2272D4D2FB9B215FA2775C0CCE87B27A21_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* MotionBuilder_3_Bind_TisPlayLitMotionAnimationComponent_t44C212AF544D647258158A4B4979FF728DB597CE_m973634D4E62BFC53D209D9BEACC6B6BBF728B9E7_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* MotionBuilder_3_Bind_TisStringValueAnimation_t4C21FFF21AA92CA403E695317EFDDC25F6F729A0_mCE150DB267F3DE64EA2D4761334CC080CFDC6BDE_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* MotionBuilder_3_RunWithoutBinding_mAEC7D58DAFA362934E3EB208CF5CE215C5D31B52_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* MotionManager_CheckTypeId_mEFEB7AD899D19579BD664224BD414C6A366792CB_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* PlayLitMotionAnimationComponent_U3CPlayU3Eb__1_0_m8B3A9D036CC26B921B03359877171C5B8F39CF4E_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Queue_1_Clear_m54078367C450AADBA046E46201CC85502B1547B3_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Queue_1_Enqueue_mD2C036FE4D6589DF5C96C83519C1B516764E325E_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Queue_1_TryDequeue_m1451317685A7F792584DE24EE0583389B6039EAF_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Queue_1__ctor_m290A360C903C36952EBBF4E8CC6D99A2D2722C6F_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Queue_1_get_Count_mE417CA84BEE80A2B2292D64CD09543348F6BE33D_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* RectPropertyAnimationComponent_1__ctor_m5368181322126D16A737C4EA2ACBB2FC81D4DCED_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Rigidbody2DPositionAnimationBase_2__ctor_m03A6CA89E10DDD75104A6AFAD90BAA664F55B30D_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Rigidbody2DPositionAnimationBase_2__ctor_m91DBE4A1F747FD0DE57E36D5FF7A6C61AF65B093_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Rigidbody2DPositionAnimationBase_2__ctor_mC7824A99A66657C2E4F29844CE1206368D04DFA7_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Rigidbody2DRotationAnimationBase_2__ctor_m1EC94B27FCD01301E526332F2D4F024B8AF31C19_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Rigidbody2DRotationAnimationBase_2__ctor_mB0C521DFA77F00B33FA186775B0FBE7C6722E364_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Rigidbody2DRotationAnimationBase_2__ctor_mEEF666BD97FF8470CDFDC86B28CC270896E97D92_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* RigidbodyPositionAnimationBase_2__ctor_m52D79A66585233D8FF78F254A11F0CD5814D06DC_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* RigidbodyPositionAnimationBase_2__ctor_m5C9917A6D03F7F4B4EE34A03603193C670CFCFCA_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* RigidbodyPositionAnimationBase_2__ctor_mE32805C6840A3AE1B3D1E29B526B8B6207579FE7_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* RigidbodyRotationAnimationBase_2__ctor_m0FCEADC70BB2100B197645B518E21182B2D32928_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* RigidbodyRotationAnimationBase_2__ctor_m3D1D00C56767E1ECEF1B20F1722F3E83163930A7_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* RigidbodyRotationAnimationBase_2__ctor_m9E52DDCFD6232030E2B638C3FA5DF2C4222D89B6_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Span_1_get_Length_mE2128B4E1A4BFB4D166BDCD48BC921B8217B1B2D_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* TransformPositionAnimationBase_2__ctor_m8BDC2ADB7E9BD450BE42D78257850F220AABFF3C_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* TransformPositionAnimationBase_2__ctor_mEEF0C512CF0CE9AA65F5F0984ADC7F2929384839_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* TransformPositionAnimationBase_2__ctor_mFB3FBCF99341AF765414DAA4F6EA24237DEB4D97_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* TransformRotationAnimationBase_2__ctor_m386B72ED5CBE026112F0016776A5DEBFE6650100_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* TransformRotationAnimationBase_2__ctor_m7703FEF1A7410371F3F55026E386C1440E06B4F7_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* TransformRotationAnimationBase_2__ctor_mF21C092DF3DF33EE4D2AFD09A711D5620318C4D7_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* TransformScaleAnimationBase_2__ctor_m1A5814121EBBC7C79C79CA062A524047F5EBB54C_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* TransformScaleAnimationBase_2__ctor_m942D49424390A604708E7F41879DA29A8A6C3DE5_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* TransformScaleAnimationBase_2__ctor_mCCF29084DD333FEFAAEB2BDE353FB40A457694FD_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CU3Ec_U3CPlayU3Eb__2_0_mF360A40509FD2F30750DCF886F357F69F1629B7B_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* UnityEvent_1_Invoke_mA633B48B5D287DA856FB954AC3E4012487E63C15_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ValueAnimationComponent_3__ctor_m7DC5DA0DF4325BCB95A2C1AC970E867DB8C6FB49_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ValueAnimationComponent_3__ctor_m8ECB8F98AC1F6B0EA2F8DD2A20500E9FDDCBBB8E_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ValueAnimationComponent_3__ctor_mB06D7CD5E6F5AD7B33589B8DD7AFF78B507F224D_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ValueAnimationComponent_3__ctor_mB50DBC5A781213FD78CA4B6F0335327192242531_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ValueAnimationComponent_3__ctor_mBD571DA23EAF4EDA73118C65A12D269BCA79D6B8_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ValueAnimationComponent_3__ctor_mBE855EEE224D3A0F08B0CE2CD6BCC3ACC38EACC0_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ValueAnimationComponent_3__ctor_mF978B0E2507972B653807AB76DB5B6194310DB65_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ValueAnimationComponent_3__ctor_mFF142C6DE5E394B6F27CACEE067BD1C94C364B6E_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Vector2PropertyAnimationComponent_1__ctor_m56572DEAEB45BBC1F444175A1DC00589DC0CE40A_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Vector4PropertyAnimationComponent_1__ctor_m6A26AF4901445E2E2EEB09953E15E407EA5F87CD_RuntimeMethod_var;
struct Delegate_t_marshaled_com;
struct Delegate_t_marshaled_pinvoke;
struct Exception_t_marshaled_com;
struct Exception_t_marshaled_pinvoke;

struct ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031;
struct LitMotionAnimationComponentU5BU5D_tA8718977F5DABB54F0422858CC77746CF7E85897;
struct ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918;

IL2CPP_EXTERN_C_BEGIN
IL2CPP_EXTERN_C_END

#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
struct U3CModuleU3E_tD1B2E989A4ABE22D85EAE66204FE12826681D368 
{
};
struct Queue_1_t7CF7AD96EFA79CC0D603DFB407D7945D8F635C5B  : public RuntimeObject
{
	LitMotionAnimationComponentU5BU5D_tA8718977F5DABB54F0422858CC77746CF7E85897* ____array;
	int32_t ____head;
	int32_t ____tail;
	int32_t ____size;
	int32_t ____version;
	RuntimeObject* ____syncRoot;
};
struct Queue_1_tE9EF546915795972C3BFD68FBB8FA859D3BAF3B5  : public RuntimeObject
{
	ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* ____array;
	int32_t ____head;
	int32_t ____tail;
	int32_t ____size;
	int32_t ____version;
	RuntimeObject* ____syncRoot;
};
struct U3CPrivateImplementationDetailsU3E_t6ECB8E8EED910A1EDD673CA4D1B8D1200B474B0B  : public RuntimeObject
{
};
struct Attribute_tFDA8EFEFB0711976D22474794576DAF28F7440AA  : public RuntimeObject
{
};
struct MemberInfo_t  : public RuntimeObject
{
};
struct MotionDebugger_t65073AE52251C2BB7A2D83C38CFE9FB805538D40  : public RuntimeObject
{
};
struct MotionManager_t55CCB0A78FD15BD0E274607AC5A3F35C3115763F  : public RuntimeObject
{
};
struct MotionScheduler_t98622FCF54DC19E35E9CB686D1D56DC64EE07AE5  : public RuntimeObject
{
};
struct String_t  : public RuntimeObject
{
	int32_t ____stringLength;
	Il2CppChar ____firstChar;
};
struct UnityEventBase_t4968A4C72559F35C0923E4BD9C042C3A842E1DB8  : public RuntimeObject
{
	InvokableCallList_t309E1C8C7CE885A0D2F98C84CEA77A8935688382* ___m_Calls;
	PersistentCallGroup_tB826EDF15DC80F71BCBCD8E410FD959A04C33F25* ___m_PersistentCalls;
	bool ___m_CallsDirty;
};
struct UnitySourceGeneratedAssemblyMonoScriptTypes_v1_tEEC2B2FFC6DD357B5E6F61C238E835856299C93E  : public RuntimeObject
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F  : public RuntimeObject
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_pinvoke
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_com
{
};
struct U3CU3Ec_tDAFA5C4E997B098FAFADB5DBAD4676795BD4F04C  : public RuntimeObject
{
};
struct FastListCore_1_t7622FA94EE4948D93B3B798B45AAE6DFAAC94519 
{
	IMotionStorageU5BU5D_t436E5CDB6D8319F837EE47133B8E54906D829630* ___array;
	int32_t ___tailIndex;
};
struct FastListCore_1_tC5AF8169955F580A98B5AF92CC49BA523978EC21 
{
	LitMotionAnimationComponentU5BU5D_tA8718977F5DABB54F0422858CC77746CF7E85897* ___array;
	int32_t ___tailIndex;
};
struct FastListCore_1_tD253A2B097165863483F30B720AE3EDC9C7FB508 
{
	ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* ___array;
	int32_t ___tailIndex;
};
struct MotionBuilder_3_t99F4E0CCAE5DBB73925A375FDD89E2C368786FDA 
{
	uint16_t ___version;
	MotionBuilderBuffer_2_t04E3EEA09283FAF1D9197F4BE4C39D9944C98311* ___buffer;
};
struct MotionBuilder_3_tA23CEA9CCADDC2C36C8D976574E70A1D2A8909DC 
{
	uint16_t ___version;
	MotionBuilderBuffer_2_t190528172F72DD395324243FA145EE61169E32DD* ___buffer;
};
struct TMP_TextProcessingStack_1_tFBA719426D68CE1F2B5849D97AF5E5D65846290C 
{
	Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ___itemStack;
	int32_t ___index;
	int32_t ___m_DefaultItem;
	int32_t ___m_Capacity;
	int32_t ___m_RolloverSize;
	int32_t ___m_Count;
};
struct TMP_TextProcessingStack_1_t138EC06BE7F101AA0A3C8D2DC951E55AACE085E9 
{
	SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* ___itemStack;
	int32_t ___index;
	float ___m_DefaultItem;
	int32_t ___m_Capacity;
	int32_t ___m_RolloverSize;
	int32_t ___m_Count;
};
struct TMP_TextProcessingStack_1_tC8FAEB17246D3B171EFD11165A5761AE39B40D0C 
{
	TMP_ColorGradientU5BU5D_t2F65E8C42F268DFF33BB1392D94BCF5B5087308A* ___itemStack;
	int32_t ___index;
	TMP_ColorGradient_t17B51752B4E9499A1FF7D875DCEC1D15A0F4AEBB* ___m_DefaultItem;
	int32_t ___m_Capacity;
	int32_t ___m_RolloverSize;
	int32_t ___m_Count;
};
struct UnityEvent_1_tC9859540CF1468306CAB6D758C0A0D95DBCEC257  : public UnityEventBase_t4968A4C72559F35C0923E4BD9C042C3A842E1DB8
{
	ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* ___m_InvokeArray;
};
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22 
{
	bool ___m_value;
};
struct Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3 
{
	uint8_t ___m_value;
};
struct Color_tD001788D726C3A7F1379BEED0260B9591F440C1F 
{
	float ___r;
	float ___g;
	float ___b;
	float ___a;
};
struct Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B 
{
	union
	{
		#pragma pack(push, tp, 1)
		struct
		{
			int32_t ___rgba;
		};
		#pragma pack(pop, tp)
		struct
		{
			int32_t ___rgba_forAlignmentOnly;
		};
		#pragma pack(push, tp, 1)
		struct
		{
			uint8_t ___r;
		};
		#pragma pack(pop, tp)
		struct
		{
			uint8_t ___r_forAlignmentOnly;
		};
		#pragma pack(push, tp, 1)
		struct
		{
			char ___g_OffsetPadding[1];
			uint8_t ___g;
		};
		#pragma pack(pop, tp)
		struct
		{
			char ___g_OffsetPadding_forAlignmentOnly[1];
			uint8_t ___g_forAlignmentOnly;
		};
		#pragma pack(push, tp, 1)
		struct
		{
			char ___b_OffsetPadding[2];
			uint8_t ___b;
		};
		#pragma pack(pop, tp)
		struct
		{
			char ___b_OffsetPadding_forAlignmentOnly[2];
			uint8_t ___b_forAlignmentOnly;
		};
		#pragma pack(push, tp, 1)
		struct
		{
			char ___a_OffsetPadding[3];
			uint8_t ___a;
		};
		#pragma pack(pop, tp)
		struct
		{
			char ___a_OffsetPadding_forAlignmentOnly[3];
			uint8_t ___a_forAlignmentOnly;
		};
	};
};
struct DrivenRectTransformTracker_tFB0706C933E3C68E4F377C204FCEEF091F1EE0B1 
{
	union
	{
		struct
		{
		};
		uint8_t DrivenRectTransformTracker_tFB0706C933E3C68E4F377C204FCEEF091F1EE0B1__padding[1];
	};
};
struct EmbeddedAttribute_t4AF2D38B103036A7E5864EFB730772BD58CB94DE  : public Attribute_tFDA8EFEFB0711976D22474794576DAF28F7440AA
{
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2  : public ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F
{
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2_marshaled_pinvoke
{
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2_marshaled_com
{
};
#pragma pack(push, tp, 1)
struct FixedBytes16_tBBD888116CBD6329886E0FE97A82EEB4B7CB3FA0 
{
	union
	{
		struct
		{
			union
			{
				#pragma pack(push, tp, 1)
				struct
				{
					uint8_t ___byte0000;
				};
				#pragma pack(pop, tp)
				struct
				{
					uint8_t ___byte0000_forAlignmentOnly;
				};
				#pragma pack(push, tp, 1)
				struct
				{
					char ___byte0001_OffsetPadding[1];
					uint8_t ___byte0001;
				};
				#pragma pack(pop, tp)
				struct
				{
					char ___byte0001_OffsetPadding_forAlignmentOnly[1];
					uint8_t ___byte0001_forAlignmentOnly;
				};
				#pragma pack(push, tp, 1)
				struct
				{
					char ___byte0002_OffsetPadding[2];
					uint8_t ___byte0002;
				};
				#pragma pack(pop, tp)
				struct
				{
					char ___byte0002_OffsetPadding_forAlignmentOnly[2];
					uint8_t ___byte0002_forAlignmentOnly;
				};
				#pragma pack(push, tp, 1)
				struct
				{
					char ___byte0003_OffsetPadding[3];
					uint8_t ___byte0003;
				};
				#pragma pack(pop, tp)
				struct
				{
					char ___byte0003_OffsetPadding_forAlignmentOnly[3];
					uint8_t ___byte0003_forAlignmentOnly;
				};
				#pragma pack(push, tp, 1)
				struct
				{
					char ___byte0004_OffsetPadding[4];
					uint8_t ___byte0004;
				};
				#pragma pack(pop, tp)
				struct
				{
					char ___byte0004_OffsetPadding_forAlignmentOnly[4];
					uint8_t ___byte0004_forAlignmentOnly;
				};
				#pragma pack(push, tp, 1)
				struct
				{
					char ___byte0005_OffsetPadding[5];
					uint8_t ___byte0005;
				};
				#pragma pack(pop, tp)
				struct
				{
					char ___byte0005_OffsetPadding_forAlignmentOnly[5];
					uint8_t ___byte0005_forAlignmentOnly;
				};
				#pragma pack(push, tp, 1)
				struct
				{
					char ___byte0006_OffsetPadding[6];
					uint8_t ___byte0006;
				};
				#pragma pack(pop, tp)
				struct
				{
					char ___byte0006_OffsetPadding_forAlignmentOnly[6];
					uint8_t ___byte0006_forAlignmentOnly;
				};
				#pragma pack(push, tp, 1)
				struct
				{
					char ___byte0007_OffsetPadding[7];
					uint8_t ___byte0007;
				};
				#pragma pack(pop, tp)
				struct
				{
					char ___byte0007_OffsetPadding_forAlignmentOnly[7];
					uint8_t ___byte0007_forAlignmentOnly;
				};
				#pragma pack(push, tp, 1)
				struct
				{
					char ___byte0008_OffsetPadding[8];
					uint8_t ___byte0008;
				};
				#pragma pack(pop, tp)
				struct
				{
					char ___byte0008_OffsetPadding_forAlignmentOnly[8];
					uint8_t ___byte0008_forAlignmentOnly;
				};
				#pragma pack(push, tp, 1)
				struct
				{
					char ___byte0009_OffsetPadding[9];
					uint8_t ___byte0009;
				};
				#pragma pack(pop, tp)
				struct
				{
					char ___byte0009_OffsetPadding_forAlignmentOnly[9];
					uint8_t ___byte0009_forAlignmentOnly;
				};
				#pragma pack(push, tp, 1)
				struct
				{
					char ___byte0010_OffsetPadding[10];
					uint8_t ___byte0010;
				};
				#pragma pack(pop, tp)
				struct
				{
					char ___byte0010_OffsetPadding_forAlignmentOnly[10];
					uint8_t ___byte0010_forAlignmentOnly;
				};
				#pragma pack(push, tp, 1)
				struct
				{
					char ___byte0011_OffsetPadding[11];
					uint8_t ___byte0011;
				};
				#pragma pack(pop, tp)
				struct
				{
					char ___byte0011_OffsetPadding_forAlignmentOnly[11];
					uint8_t ___byte0011_forAlignmentOnly;
				};
				#pragma pack(push, tp, 1)
				struct
				{
					char ___byte0012_OffsetPadding[12];
					uint8_t ___byte0012;
				};
				#pragma pack(pop, tp)
				struct
				{
					char ___byte0012_OffsetPadding_forAlignmentOnly[12];
					uint8_t ___byte0012_forAlignmentOnly;
				};
				#pragma pack(push, tp, 1)
				struct
				{
					char ___byte0013_OffsetPadding[13];
					uint8_t ___byte0013;
				};
				#pragma pack(pop, tp)
				struct
				{
					char ___byte0013_OffsetPadding_forAlignmentOnly[13];
					uint8_t ___byte0013_forAlignmentOnly;
				};
				#pragma pack(push, tp, 1)
				struct
				{
					char ___byte0014_OffsetPadding[14];
					uint8_t ___byte0014;
				};
				#pragma pack(pop, tp)
				struct
				{
					char ___byte0014_OffsetPadding_forAlignmentOnly[14];
					uint8_t ___byte0014_forAlignmentOnly;
				};
				#pragma pack(push, tp, 1)
				struct
				{
					char ___byte0015_OffsetPadding[15];
					uint8_t ___byte0015;
				};
				#pragma pack(pop, tp)
				struct
				{
					char ___byte0015_OffsetPadding_forAlignmentOnly[15];
					uint8_t ___byte0015_forAlignmentOnly;
				};
			};
		};
		uint8_t FixedBytes16_tBBD888116CBD6329886E0FE97A82EEB4B7CB3FA0__padding[16];
	};
};
#pragma pack(pop, tp)
struct Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C 
{
	int32_t ___m_value;
};
struct IntPtr_t 
{
	void* ___m_value;
};
struct IsUnmanagedAttribute_t3A3B7234A45164EED2468C5D59391259F9D6999B  : public Attribute_tFDA8EFEFB0711976D22474794576DAF28F7440AA
{
};
struct LitMotionAnimationComponentMenuAttribute_t6C29773ED47514A8074EC34A851EFF85D572180A  : public Attribute_tFDA8EFEFB0711976D22474794576DAF28F7440AA
{
	String_t* ___U3CMenuNameU3Ek__BackingField;
};
struct ManagedMotionData_tE1DE04DA7EDE8DF722FC643EAF489B5222F396CB 
{
	bool ___CancelOnError;
	bool ___SkipValuesDuringDelay;
	uint8_t ___StateCount;
	RuntimeObject* ___State0;
	RuntimeObject* ___State1;
	RuntimeObject* ___State2;
	RuntimeObject* ___UpdateAction;
	Action_1_tD69A6DC9FBE94131E52F5A73B2A9D4AB51EEC404* ___OnLoopCompleteAction;
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___OnCompleteAction;
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___OnCancelAction;
};
struct MaterialReference_tFD98FFFBBDF168028E637446C6676507186F4D0B 
{
	int32_t ___index;
	TMP_FontAsset_t923BF2F78D7C5AC36376E168A1193B7CB4855160* ___fontAsset;
	TMP_SpriteAsset_t81F779E6F705CE190DC0D1F93A954CB8B1774B39* ___spriteAsset;
	Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* ___material;
	bool ___isDefaultMaterial;
	bool ___isFallbackMaterial;
	Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* ___fallbackMaterial;
	float ___padding;
	int32_t ___referenceCount;
};
struct MaterialReference_tFD98FFFBBDF168028E637446C6676507186F4D0B_marshaled_pinvoke
{
	int32_t ___index;
	TMP_FontAsset_t923BF2F78D7C5AC36376E168A1193B7CB4855160* ___fontAsset;
	TMP_SpriteAsset_t81F779E6F705CE190DC0D1F93A954CB8B1774B39* ___spriteAsset;
	Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* ___material;
	int32_t ___isDefaultMaterial;
	int32_t ___isFallbackMaterial;
	Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* ___fallbackMaterial;
	float ___padding;
	int32_t ___referenceCount;
};
struct MaterialReference_tFD98FFFBBDF168028E637446C6676507186F4D0B_marshaled_com
{
	int32_t ___index;
	TMP_FontAsset_t923BF2F78D7C5AC36376E168A1193B7CB4855160* ___fontAsset;
	TMP_SpriteAsset_t81F779E6F705CE190DC0D1F93A954CB8B1774B39* ___spriteAsset;
	Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* ___material;
	int32_t ___isDefaultMaterial;
	int32_t ___isFallbackMaterial;
	Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* ___fallbackMaterial;
	float ___padding;
	int32_t ___referenceCount;
};
struct Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6 
{
	float ___m00;
	float ___m10;
	float ___m20;
	float ___m30;
	float ___m01;
	float ___m11;
	float ___m21;
	float ___m31;
	float ___m02;
	float ___m12;
	float ___m22;
	float ___m32;
	float ___m03;
	float ___m13;
	float ___m23;
	float ___m33;
};
struct MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 
{
	int32_t ___StorageId;
	int32_t ___Index;
	int32_t ___Version;
};
struct NoOptions_t7F036E20F8ED9230BFEF6EDF7C39EDD013A70C13 
{
	union
	{
		struct
		{
		};
		uint8_t NoOptions_t7F036E20F8ED9230BFEF6EDF7C39EDD013A70C13__padding[1];
	};
};
struct Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D 
{
	float ___m_XMin;
	float ___m_YMin;
	float ___m_Width;
	float ___m_Height;
};
struct Single_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C 
{
	float ___m_value;
};
struct SpriteState_tC8199570BE6337FB5C49347C97892B4222E5AACD 
{
	Sprite_tAFF74BC83CD68037494CB0B4F28CBDF8971CAB99* ___m_HighlightedSprite;
	Sprite_tAFF74BC83CD68037494CB0B4F28CBDF8971CAB99* ___m_PressedSprite;
	Sprite_tAFF74BC83CD68037494CB0B4F28CBDF8971CAB99* ___m_SelectedSprite;
	Sprite_tAFF74BC83CD68037494CB0B4F28CBDF8971CAB99* ___m_DisabledSprite;
};
struct SpriteState_tC8199570BE6337FB5C49347C97892B4222E5AACD_marshaled_pinvoke
{
	Sprite_tAFF74BC83CD68037494CB0B4F28CBDF8971CAB99* ___m_HighlightedSprite;
	Sprite_tAFF74BC83CD68037494CB0B4F28CBDF8971CAB99* ___m_PressedSprite;
	Sprite_tAFF74BC83CD68037494CB0B4F28CBDF8971CAB99* ___m_SelectedSprite;
	Sprite_tAFF74BC83CD68037494CB0B4F28CBDF8971CAB99* ___m_DisabledSprite;
};
struct SpriteState_tC8199570BE6337FB5C49347C97892B4222E5AACD_marshaled_com
{
	Sprite_tAFF74BC83CD68037494CB0B4F28CBDF8971CAB99* ___m_HighlightedSprite;
	Sprite_tAFF74BC83CD68037494CB0B4F28CBDF8971CAB99* ___m_PressedSprite;
	Sprite_tAFF74BC83CD68037494CB0B4F28CBDF8971CAB99* ___m_SelectedSprite;
	Sprite_tAFF74BC83CD68037494CB0B4F28CBDF8971CAB99* ___m_DisabledSprite;
};
struct TMP_FontStyleStack_t52885F172FADBC21346C835B5302167BDA8020DC 
{
	uint8_t ___bold;
	uint8_t ___italic;
	uint8_t ___underline;
	uint8_t ___strikethrough;
	uint8_t ___highlight;
	uint8_t ___superscript;
	uint8_t ___subscript;
	uint8_t ___uppercase;
	uint8_t ___lowercase;
	uint8_t ___smallcaps;
};
struct TMP_Offset_t2262BE4E87D9662487777FF8FFE1B17B0E4438C6 
{
	float ___m_Left;
	float ___m_Right;
	float ___m_Top;
	float ___m_Bottom;
};
struct UInt16_tF4C148C876015C212FD72652D0B6ED8CC247A455 
{
	uint16_t ___m_value;
};
struct UInt64_t8F12534CC8FC4B5860F2A2CD1EE79D322E7A41AF 
{
	uint64_t ___m_value;
};
struct UnityEvent_tDC2C3548799DBC91D1E3F3DE60083A66F4751977  : public UnityEventBase_t4968A4C72559F35C0923E4BD9C042C3A842E1DB8
{
	ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* ___m_InvokeArray;
};
struct Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 
{
	float ___x;
	float ___y;
};
struct Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 
{
	float ___x;
	float ___y;
	float ___z;
};
struct Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 
{
	float ___x;
	float ___y;
	float ___z;
	float ___w;
};
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915 
{
	union
	{
		struct
		{
		};
		uint8_t Void_t4861ACF8F4594C3437BB48B6E56783494B843915__padding[1];
	};
};
#pragma pack(push, tp, 1)
struct __StaticArrayInitTypeSizeU3D1405_t68DB5F2E62461C67E9A44D9BCF56CBC3FB1C121B 
{
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D1405_t68DB5F2E62461C67E9A44D9BCF56CBC3FB1C121B__padding[1405];
	};
};
#pragma pack(pop, tp)
#pragma pack(push, tp, 1)
struct __StaticArrayInitTypeSizeU3D5393_t3A32A024DBF4CF4577A1D9D09291901427880195 
{
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D5393_t3A32A024DBF4CF4577A1D9D09291901427880195__padding[5393];
	};
};
#pragma pack(pop, tp)
struct AllocatorHandle_t3CA09720B1F89F91A8DDBA95E74C28A1EC3E3148 
{
	uint16_t ___Index;
	uint16_t ___Version;
};
struct SpecialCharacter_t6C1DBE8C490706D1620899BAB7F0B8091AD26777 
{
	TMP_Character_t7D37A55EF1A9FF6D0BFE6D50E86A00F80E7FAF35* ___character;
	TMP_FontAsset_t923BF2F78D7C5AC36376E168A1193B7CB4855160* ___fontAsset;
	Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* ___material;
	int32_t ___materialIndex;
};
struct SpecialCharacter_t6C1DBE8C490706D1620899BAB7F0B8091AD26777_marshaled_pinvoke
{
	TMP_Character_t7D37A55EF1A9FF6D0BFE6D50E86A00F80E7FAF35* ___character;
	TMP_FontAsset_t923BF2F78D7C5AC36376E168A1193B7CB4855160* ___fontAsset;
	Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* ___material;
	int32_t ___materialIndex;
};
struct SpecialCharacter_t6C1DBE8C490706D1620899BAB7F0B8091AD26777_marshaled_com
{
	TMP_Character_t7D37A55EF1A9FF6D0BFE6D50E86A00F80E7FAF35* ___character;
	TMP_FontAsset_t923BF2F78D7C5AC36376E168A1193B7CB4855160* ___fontAsset;
	Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* ___material;
	int32_t ___materialIndex;
};
struct TextBackingContainer_t33D1CE628E7B26C45EDAC1D87BEF2DD22A5C6361 
{
	UInt32U5BU5D_t02FBD658AD156A17574ECE6106CF1FBFCC9807FA* ___m_Array;
	int32_t ___m_Count;
};
struct TextBackingContainer_t33D1CE628E7B26C45EDAC1D87BEF2DD22A5C6361_marshaled_pinvoke
{
	Il2CppSafeArray* ___m_Array;
	int32_t ___m_Count;
};
struct TextBackingContainer_t33D1CE628E7B26C45EDAC1D87BEF2DD22A5C6361_marshaled_com
{
	Il2CppSafeArray* ___m_Array;
	int32_t ___m_Count;
};
struct MonoScriptData_tE93C20220981B07F2835D890E72BD7FED954612A 
{
	ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* ___FilePathsData;
	ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* ___TypesData;
	int32_t ___TotalTypes;
	int32_t ___TotalFiles;
	bool ___IsEditorOnly;
};
struct MonoScriptData_tE93C20220981B07F2835D890E72BD7FED954612A_marshaled_pinvoke
{
	Il2CppSafeArray* ___FilePathsData;
	Il2CppSafeArray* ___TypesData;
	int32_t ___TotalTypes;
	int32_t ___TotalFiles;
	int32_t ___IsEditorOnly;
};
struct MonoScriptData_tE93C20220981B07F2835D890E72BD7FED954612A_marshaled_com
{
	Il2CppSafeArray* ___FilePathsData;
	Il2CppSafeArray* ___TypesData;
	int32_t ___TotalTypes;
	int32_t ___TotalFiles;
	int32_t ___IsEditorOnly;
};
struct ByReference_1_tFF25269D410D247DBCA2AA9CEA0DBE153B705049 
{
	intptr_t ____value;
};
struct ByReference_1_t98B79BFB40A2CA0814BC183B09B4339A5EBF8524 
{
	intptr_t ____value;
};
struct TMP_TextProcessingStack_1_tF2CD5BE59E5EB22EA9E3EE3043A004EA918C4BB3 
{
	Color32U5BU5D_t38116C3E91765C4C5726CE12C77FAD7F9F737259* ___itemStack;
	int32_t ___index;
	Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B ___m_DefaultItem;
	int32_t ___m_Capacity;
	int32_t ___m_RolloverSize;
	int32_t ___m_Count;
};
struct TMP_TextProcessingStack_1_tB03E08F69415B281A5A81138F09E49EE58402DF9 
{
	MaterialReferenceU5BU5D_t7491D335AB3E3E13CE9C0F5E931F396F6A02E1F2* ___itemStack;
	int32_t ___index;
	MaterialReference_tFD98FFFBBDF168028E637446C6676507186F4D0B ___m_DefaultItem;
	int32_t ___m_Capacity;
	int32_t ___m_RolloverSize;
	int32_t ___m_Count;
};
struct UnsafeList_1_t5460E69E2F6E287ACD0577422AF7887E387EE42F 
{
	Keyframe_tB9C67DCBFE10C0AE9C52CB5C66E944255C9254F0* ___Ptr;
	int32_t ___m_length;
	int32_t ___m_capacity;
	AllocatorHandle_t3CA09720B1F89F91A8DDBA95E74C28A1EC3E3148 ___Allocator;
	int32_t ___length;
	int32_t ___capacity;
};
struct ColorBlock_tDD7C62E7AFE442652FC98F8D058CE8AE6BFD7C11 
{
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___m_NormalColor;
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___m_HighlightedColor;
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___m_PressedColor;
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___m_SelectedColor;
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___m_DisabledColor;
	float ___m_ColorMultiplier;
	float ___m_FadeDuration;
};
struct ColorMode_tA7A815AAB9F175EFBA0AE0814E55728432A880BF 
{
	int32_t ___value__;
};
struct DelayType_tD1996A2683858123E25B8AF91DE5EF35A13CBBF9 
{
	uint8_t ___value__;
};
struct Delegate_t  : public RuntimeObject
{
	intptr_t ___method_ptr;
	intptr_t ___invoke_impl;
	RuntimeObject* ___m_target;
	intptr_t ___method;
	intptr_t ___delegate_trampoline;
	intptr_t ___extra_arg;
	intptr_t ___method_code;
	intptr_t ___interp_method;
	intptr_t ___interp_invoke_impl;
	MethodInfo_t* ___method_info;
	MethodInfo_t* ___original_method_info;
	DelegateData_t9B286B493293CD2D23A5B2B5EF0E5B1324C2B77E* ___data;
	bool ___method_is_virtual;
};
struct Delegate_t_marshaled_pinvoke
{
	intptr_t ___method_ptr;
	intptr_t ___invoke_impl;
	Il2CppIUnknown* ___m_target;
	intptr_t ___method;
	intptr_t ___delegate_trampoline;
	intptr_t ___extra_arg;
	intptr_t ___method_code;
	intptr_t ___interp_method;
	intptr_t ___interp_invoke_impl;
	MethodInfo_t* ___method_info;
	MethodInfo_t* ___original_method_info;
	DelegateData_t9B286B493293CD2D23A5B2B5EF0E5B1324C2B77E* ___data;
	int32_t ___method_is_virtual;
};
struct Delegate_t_marshaled_com
{
	intptr_t ___method_ptr;
	intptr_t ___invoke_impl;
	Il2CppIUnknown* ___m_target;
	intptr_t ___method;
	intptr_t ___delegate_trampoline;
	intptr_t ___extra_arg;
	intptr_t ___method_code;
	intptr_t ___interp_method;
	intptr_t ___interp_invoke_impl;
	MethodInfo_t* ___method_info;
	MethodInfo_t* ___original_method_info;
	DelegateData_t9B286B493293CD2D23A5B2B5EF0E5B1324C2B77E* ___data;
	int32_t ___method_is_virtual;
};
struct Ease_tF36EF4D651F9AFDA12F19D9E4A205D327FBF38B5 
{
	int32_t ___value__;
};
struct Exception_t  : public RuntimeObject
{
	String_t* ____className;
	String_t* ____message;
	RuntimeObject* ____data;
	Exception_t* ____innerException;
	String_t* ____helpURL;
	RuntimeObject* ____stackTrace;
	String_t* ____stackTraceString;
	String_t* ____remoteStackTraceString;
	int32_t ____remoteStackIndex;
	RuntimeObject* ____dynamicMethods;
	int32_t ____HResult;
	String_t* ____source;
	SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6* ____safeSerializationManager;
	StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF* ___captured_traces;
	IntPtrU5BU5D_tFD177F8C806A6921AD7150264CCC62FA00CAD832* ___native_trace_ips;
	int32_t ___caught_in_unmanaged;
};
struct Exception_t_marshaled_pinvoke
{
	char* ____className;
	char* ____message;
	RuntimeObject* ____data;
	Exception_t_marshaled_pinvoke* ____innerException;
	char* ____helpURL;
	Il2CppIUnknown* ____stackTrace;
	char* ____stackTraceString;
	char* ____remoteStackTraceString;
	int32_t ____remoteStackIndex;
	Il2CppIUnknown* ____dynamicMethods;
	int32_t ____HResult;
	char* ____source;
	SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6* ____safeSerializationManager;
	StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF* ___captured_traces;
	Il2CppSafeArray* ___native_trace_ips;
	int32_t ___caught_in_unmanaged;
};
struct Exception_t_marshaled_com
{
	Il2CppChar* ____className;
	Il2CppChar* ____message;
	RuntimeObject* ____data;
	Exception_t_marshaled_com* ____innerException;
	Il2CppChar* ____helpURL;
	Il2CppIUnknown* ____stackTrace;
	Il2CppChar* ____stackTraceString;
	Il2CppChar* ____remoteStackTraceString;
	int32_t ____remoteStackIndex;
	Il2CppIUnknown* ____dynamicMethods;
	int32_t ____HResult;
	Il2CppChar* ____source;
	SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6* ____safeSerializationManager;
	StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF* ___captured_traces;
	Il2CppSafeArray* ___native_trace_ips;
	int32_t ___caught_in_unmanaged;
};
struct Extents_tA2D2F95811D0A18CB7AC3570D2D8F8CD3AF4C4A8 
{
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___min;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___max;
};
#pragma pack(push, tp, 1)
struct FixedBytes510_t95B284C3FF966246998B23701C3F0F55C6BD7973 
{
	union
	{
		struct
		{
			union
			{
				#pragma pack(push, tp, 1)
				struct
				{
					FixedBytes16_tBBD888116CBD6329886E0FE97A82EEB4B7CB3FA0 ___offset0000;
				};
				#pragma pack(pop, tp)
				struct
				{
					FixedBytes16_tBBD888116CBD6329886E0FE97A82EEB4B7CB3FA0 ___offset0000_forAlignmentOnly;
				};
				#pragma pack(push, tp, 1)
				struct
				{
					char ___offset0016_OffsetPadding[16];
					FixedBytes16_tBBD888116CBD6329886E0FE97A82EEB4B7CB3FA0 ___offset0016;
				};
				#pragma pack(pop, tp)
				struct
				{
					char ___offset0016_OffsetPadding_forAlignmentOnly[16];
					FixedBytes16_tBBD888116CBD6329886E0FE97A82EEB4B7CB3FA0 ___offset0016_forAlignmentOnly;
				};
				#pragma pack(push, tp, 1)
				struct
				{
					char ___offset0032_OffsetPadding[32];
					FixedBytes16_tBBD888116CBD6329886E0FE97A82EEB4B7CB3FA0 ___offset0032;
				};
				#pragma pack(pop, tp)
				struct
				{
					char ___offset0032_OffsetPadding_forAlignmentOnly[32];
					FixedBytes16_tBBD888116CBD6329886E0FE97A82EEB4B7CB3FA0 ___offset0032_forAlignmentOnly;
				};
				#pragma pack(push, tp, 1)
				struct
				{
					char ___offset0048_OffsetPadding[48];
					FixedBytes16_tBBD888116CBD6329886E0FE97A82EEB4B7CB3FA0 ___offset0048;
				};
				#pragma pack(pop, tp)
				struct
				{
					char ___offset0048_OffsetPadding_forAlignmentOnly[48];
					FixedBytes16_tBBD888116CBD6329886E0FE97A82EEB4B7CB3FA0 ___offset0048_forAlignmentOnly;
				};
				#pragma pack(push, tp, 1)
				struct
				{
					char ___offset0064_OffsetPadding[64];
					FixedBytes16_tBBD888116CBD6329886E0FE97A82EEB4B7CB3FA0 ___offset0064;
				};
				#pragma pack(pop, tp)
				struct
				{
					char ___offset0064_OffsetPadding_forAlignmentOnly[64];
					FixedBytes16_tBBD888116CBD6329886E0FE97A82EEB4B7CB3FA0 ___offset0064_forAlignmentOnly;
				};
				#pragma pack(push, tp, 1)
				struct
				{
					char ___offset0080_OffsetPadding[80];
					FixedBytes16_tBBD888116CBD6329886E0FE97A82EEB4B7CB3FA0 ___offset0080;
				};
				#pragma pack(pop, tp)
				struct
				{
					char ___offset0080_OffsetPadding_forAlignmentOnly[80];
					FixedBytes16_tBBD888116CBD6329886E0FE97A82EEB4B7CB3FA0 ___offset0080_forAlignmentOnly;
				};
				#pragma pack(push, tp, 1)
				struct
				{
					char ___offset0096_OffsetPadding[96];
					FixedBytes16_tBBD888116CBD6329886E0FE97A82EEB4B7CB3FA0 ___offset0096;
				};
				#pragma pack(pop, tp)
				struct
				{
					char ___offset0096_OffsetPadding_forAlignmentOnly[96];
					FixedBytes16_tBBD888116CBD6329886E0FE97A82EEB4B7CB3FA0 ___offset0096_forAlignmentOnly;
				};
				#pragma pack(push, tp, 1)
				struct
				{
					char ___offset0112_OffsetPadding[112];
					FixedBytes16_tBBD888116CBD6329886E0FE97A82EEB4B7CB3FA0 ___offset0112;
				};
				#pragma pack(pop, tp)
				struct
				{
					char ___offset0112_OffsetPadding_forAlignmentOnly[112];
					FixedBytes16_tBBD888116CBD6329886E0FE97A82EEB4B7CB3FA0 ___offset0112_forAlignmentOnly;
				};
				#pragma pack(push, tp, 1)
				struct
				{
					char ___offset0128_OffsetPadding[128];
					FixedBytes16_tBBD888116CBD6329886E0FE97A82EEB4B7CB3FA0 ___offset0128;
				};
				#pragma pack(pop, tp)
				struct
				{
					char ___offset0128_OffsetPadding_forAlignmentOnly[128];
					FixedBytes16_tBBD888116CBD6329886E0FE97A82EEB4B7CB3FA0 ___offset0128_forAlignmentOnly;
				};
				#pragma pack(push, tp, 1)
				struct
				{
					char ___offset0144_OffsetPadding[144];
					FixedBytes16_tBBD888116CBD6329886E0FE97A82EEB4B7CB3FA0 ___offset0144;
				};
				#pragma pack(pop, tp)
				struct
				{
					char ___offset0144_OffsetPadding_forAlignmentOnly[144];
					FixedBytes16_tBBD888116CBD6329886E0FE97A82EEB4B7CB3FA0 ___offset0144_forAlignmentOnly;
				};
				#pragma pack(push, tp, 1)
				struct
				{
					char ___offset0160_OffsetPadding[160];
					FixedBytes16_tBBD888116CBD6329886E0FE97A82EEB4B7CB3FA0 ___offset0160;
				};
				#pragma pack(pop, tp)
				struct
				{
					char ___offset0160_OffsetPadding_forAlignmentOnly[160];
					FixedBytes16_tBBD888116CBD6329886E0FE97A82EEB4B7CB3FA0 ___offset0160_forAlignmentOnly;
				};
				#pragma pack(push, tp, 1)
				struct
				{
					char ___offset0176_OffsetPadding[176];
					FixedBytes16_tBBD888116CBD6329886E0FE97A82EEB4B7CB3FA0 ___offset0176;
				};
				#pragma pack(pop, tp)
				struct
				{
					char ___offset0176_OffsetPadding_forAlignmentOnly[176];
					FixedBytes16_tBBD888116CBD6329886E0FE97A82EEB4B7CB3FA0 ___offset0176_forAlignmentOnly;
				};
				#pragma pack(push, tp, 1)
				struct
				{
					char ___offset0192_OffsetPadding[192];
					FixedBytes16_tBBD888116CBD6329886E0FE97A82EEB4B7CB3FA0 ___offset0192;
				};
				#pragma pack(pop, tp)
				struct
				{
					char ___offset0192_OffsetPadding_forAlignmentOnly[192];
					FixedBytes16_tBBD888116CBD6329886E0FE97A82EEB4B7CB3FA0 ___offset0192_forAlignmentOnly;
				};
				#pragma pack(push, tp, 1)
				struct
				{
					char ___offset0208_OffsetPadding[208];
					FixedBytes16_tBBD888116CBD6329886E0FE97A82EEB4B7CB3FA0 ___offset0208;
				};
				#pragma pack(pop, tp)
				struct
				{
					char ___offset0208_OffsetPadding_forAlignmentOnly[208];
					FixedBytes16_tBBD888116CBD6329886E0FE97A82EEB4B7CB3FA0 ___offset0208_forAlignmentOnly;
				};
				#pragma pack(push, tp, 1)
				struct
				{
					char ___offset0224_OffsetPadding[224];
					FixedBytes16_tBBD888116CBD6329886E0FE97A82EEB4B7CB3FA0 ___offset0224;
				};
				#pragma pack(pop, tp)
				struct
				{
					char ___offset0224_OffsetPadding_forAlignmentOnly[224];
					FixedBytes16_tBBD888116CBD6329886E0FE97A82EEB4B7CB3FA0 ___offset0224_forAlignmentOnly;
				};
				#pragma pack(push, tp, 1)
				struct
				{
					char ___offset0240_OffsetPadding[240];
					FixedBytes16_tBBD888116CBD6329886E0FE97A82EEB4B7CB3FA0 ___offset0240;
				};
				#pragma pack(pop, tp)
				struct
				{
					char ___offset0240_OffsetPadding_forAlignmentOnly[240];
					FixedBytes16_tBBD888116CBD6329886E0FE97A82EEB4B7CB3FA0 ___offset0240_forAlignmentOnly;
				};
				#pragma pack(push, tp, 1)
				struct
				{
					char ___offset0256_OffsetPadding[256];
					FixedBytes16_tBBD888116CBD6329886E0FE97A82EEB4B7CB3FA0 ___offset0256;
				};
				#pragma pack(pop, tp)
				struct
				{
					char ___offset0256_OffsetPadding_forAlignmentOnly[256];
					FixedBytes16_tBBD888116CBD6329886E0FE97A82EEB4B7CB3FA0 ___offset0256_forAlignmentOnly;
				};
				#pragma pack(push, tp, 1)
				struct
				{
					char ___offset0272_OffsetPadding[272];
					FixedBytes16_tBBD888116CBD6329886E0FE97A82EEB4B7CB3FA0 ___offset0272;
				};
				#pragma pack(pop, tp)
				struct
				{
					char ___offset0272_OffsetPadding_forAlignmentOnly[272];
					FixedBytes16_tBBD888116CBD6329886E0FE97A82EEB4B7CB3FA0 ___offset0272_forAlignmentOnly;
				};
				#pragma pack(push, tp, 1)
				struct
				{
					char ___offset0288_OffsetPadding[288];
					FixedBytes16_tBBD888116CBD6329886E0FE97A82EEB4B7CB3FA0 ___offset0288;
				};
				#pragma pack(pop, tp)
				struct
				{
					char ___offset0288_OffsetPadding_forAlignmentOnly[288];
					FixedBytes16_tBBD888116CBD6329886E0FE97A82EEB4B7CB3FA0 ___offset0288_forAlignmentOnly;
				};
				#pragma pack(push, tp, 1)
				struct
				{
					char ___offset0304_OffsetPadding[304];
					FixedBytes16_tBBD888116CBD6329886E0FE97A82EEB4B7CB3FA0 ___offset0304;
				};
				#pragma pack(pop, tp)
				struct
				{
					char ___offset0304_OffsetPadding_forAlignmentOnly[304];
					FixedBytes16_tBBD888116CBD6329886E0FE97A82EEB4B7CB3FA0 ___offset0304_forAlignmentOnly;
				};
				#pragma pack(push, tp, 1)
				struct
				{
					char ___offset0320_OffsetPadding[320];
					FixedBytes16_tBBD888116CBD6329886E0FE97A82EEB4B7CB3FA0 ___offset0320;
				};
				#pragma pack(pop, tp)
				struct
				{
					char ___offset0320_OffsetPadding_forAlignmentOnly[320];
					FixedBytes16_tBBD888116CBD6329886E0FE97A82EEB4B7CB3FA0 ___offset0320_forAlignmentOnly;
				};
				#pragma pack(push, tp, 1)
				struct
				{
					char ___offset0336_OffsetPadding[336];
					FixedBytes16_tBBD888116CBD6329886E0FE97A82EEB4B7CB3FA0 ___offset0336;
				};
				#pragma pack(pop, tp)
				struct
				{
					char ___offset0336_OffsetPadding_forAlignmentOnly[336];
					FixedBytes16_tBBD888116CBD6329886E0FE97A82EEB4B7CB3FA0 ___offset0336_forAlignmentOnly;
				};
				#pragma pack(push, tp, 1)
				struct
				{
					char ___offset0352_OffsetPadding[352];
					FixedBytes16_tBBD888116CBD6329886E0FE97A82EEB4B7CB3FA0 ___offset0352;
				};
				#pragma pack(pop, tp)
				struct
				{
					char ___offset0352_OffsetPadding_forAlignmentOnly[352];
					FixedBytes16_tBBD888116CBD6329886E0FE97A82EEB4B7CB3FA0 ___offset0352_forAlignmentOnly;
				};
				#pragma pack(push, tp, 1)
				struct
				{
					char ___offset0368_OffsetPadding[368];
					FixedBytes16_tBBD888116CBD6329886E0FE97A82EEB4B7CB3FA0 ___offset0368;
				};
				#pragma pack(pop, tp)
				struct
				{
					char ___offset0368_OffsetPadding_forAlignmentOnly[368];
					FixedBytes16_tBBD888116CBD6329886E0FE97A82EEB4B7CB3FA0 ___offset0368_forAlignmentOnly;
				};
				#pragma pack(push, tp, 1)
				struct
				{
					char ___offset0384_OffsetPadding[384];
					FixedBytes16_tBBD888116CBD6329886E0FE97A82EEB4B7CB3FA0 ___offset0384;
				};
				#pragma pack(pop, tp)
				struct
				{
					char ___offset0384_OffsetPadding_forAlignmentOnly[384];
					FixedBytes16_tBBD888116CBD6329886E0FE97A82EEB4B7CB3FA0 ___offset0384_forAlignmentOnly;
				};
				#pragma pack(push, tp, 1)
				struct
				{
					char ___offset0400_OffsetPadding[400];
					FixedBytes16_tBBD888116CBD6329886E0FE97A82EEB4B7CB3FA0 ___offset0400;
				};
				#pragma pack(pop, tp)
				struct
				{
					char ___offset0400_OffsetPadding_forAlignmentOnly[400];
					FixedBytes16_tBBD888116CBD6329886E0FE97A82EEB4B7CB3FA0 ___offset0400_forAlignmentOnly;
				};
				#pragma pack(push, tp, 1)
				struct
				{
					char ___offset0416_OffsetPadding[416];
					FixedBytes16_tBBD888116CBD6329886E0FE97A82EEB4B7CB3FA0 ___offset0416;
				};
				#pragma pack(pop, tp)
				struct
				{
					char ___offset0416_OffsetPadding_forAlignmentOnly[416];
					FixedBytes16_tBBD888116CBD6329886E0FE97A82EEB4B7CB3FA0 ___offset0416_forAlignmentOnly;
				};
				#pragma pack(push, tp, 1)
				struct
				{
					char ___offset0432_OffsetPadding[432];
					FixedBytes16_tBBD888116CBD6329886E0FE97A82EEB4B7CB3FA0 ___offset0432;
				};
				#pragma pack(pop, tp)
				struct
				{
					char ___offset0432_OffsetPadding_forAlignmentOnly[432];
					FixedBytes16_tBBD888116CBD6329886E0FE97A82EEB4B7CB3FA0 ___offset0432_forAlignmentOnly;
				};
				#pragma pack(push, tp, 1)
				struct
				{
					char ___offset0448_OffsetPadding[448];
					FixedBytes16_tBBD888116CBD6329886E0FE97A82EEB4B7CB3FA0 ___offset0448;
				};
				#pragma pack(pop, tp)
				struct
				{
					char ___offset0448_OffsetPadding_forAlignmentOnly[448];
					FixedBytes16_tBBD888116CBD6329886E0FE97A82EEB4B7CB3FA0 ___offset0448_forAlignmentOnly;
				};
				#pragma pack(push, tp, 1)
				struct
				{
					char ___offset0464_OffsetPadding[464];
					FixedBytes16_tBBD888116CBD6329886E0FE97A82EEB4B7CB3FA0 ___offset0464;
				};
				#pragma pack(pop, tp)
				struct
				{
					char ___offset0464_OffsetPadding_forAlignmentOnly[464];
					FixedBytes16_tBBD888116CBD6329886E0FE97A82EEB4B7CB3FA0 ___offset0464_forAlignmentOnly;
				};
				#pragma pack(push, tp, 1)
				struct
				{
					char ___offset0480_OffsetPadding[480];
					FixedBytes16_tBBD888116CBD6329886E0FE97A82EEB4B7CB3FA0 ___offset0480;
				};
				#pragma pack(pop, tp)
				struct
				{
					char ___offset0480_OffsetPadding_forAlignmentOnly[480];
					FixedBytes16_tBBD888116CBD6329886E0FE97A82EEB4B7CB3FA0 ___offset0480_forAlignmentOnly;
				};
				#pragma pack(push, tp, 1)
				struct
				{
					char ___byte0496_OffsetPadding[496];
					uint8_t ___byte0496;
				};
				#pragma pack(pop, tp)
				struct
				{
					char ___byte0496_OffsetPadding_forAlignmentOnly[496];
					uint8_t ___byte0496_forAlignmentOnly;
				};
				#pragma pack(push, tp, 1)
				struct
				{
					char ___byte0497_OffsetPadding[497];
					uint8_t ___byte0497;
				};
				#pragma pack(pop, tp)
				struct
				{
					char ___byte0497_OffsetPadding_forAlignmentOnly[497];
					uint8_t ___byte0497_forAlignmentOnly;
				};
				#pragma pack(push, tp, 1)
				struct
				{
					char ___byte0498_OffsetPadding[498];
					uint8_t ___byte0498;
				};
				#pragma pack(pop, tp)
				struct
				{
					char ___byte0498_OffsetPadding_forAlignmentOnly[498];
					uint8_t ___byte0498_forAlignmentOnly;
				};
				#pragma pack(push, tp, 1)
				struct
				{
					char ___byte0499_OffsetPadding[499];
					uint8_t ___byte0499;
				};
				#pragma pack(pop, tp)
				struct
				{
					char ___byte0499_OffsetPadding_forAlignmentOnly[499];
					uint8_t ___byte0499_forAlignmentOnly;
				};
				#pragma pack(push, tp, 1)
				struct
				{
					char ___byte0500_OffsetPadding[500];
					uint8_t ___byte0500;
				};
				#pragma pack(pop, tp)
				struct
				{
					char ___byte0500_OffsetPadding_forAlignmentOnly[500];
					uint8_t ___byte0500_forAlignmentOnly;
				};
				#pragma pack(push, tp, 1)
				struct
				{
					char ___byte0501_OffsetPadding[501];
					uint8_t ___byte0501;
				};
				#pragma pack(pop, tp)
				struct
				{
					char ___byte0501_OffsetPadding_forAlignmentOnly[501];
					uint8_t ___byte0501_forAlignmentOnly;
				};
				#pragma pack(push, tp, 1)
				struct
				{
					char ___byte0502_OffsetPadding[502];
					uint8_t ___byte0502;
				};
				#pragma pack(pop, tp)
				struct
				{
					char ___byte0502_OffsetPadding_forAlignmentOnly[502];
					uint8_t ___byte0502_forAlignmentOnly;
				};
				#pragma pack(push, tp, 1)
				struct
				{
					char ___byte0503_OffsetPadding[503];
					uint8_t ___byte0503;
				};
				#pragma pack(pop, tp)
				struct
				{
					char ___byte0503_OffsetPadding_forAlignmentOnly[503];
					uint8_t ___byte0503_forAlignmentOnly;
				};
				#pragma pack(push, tp, 1)
				struct
				{
					char ___byte0504_OffsetPadding[504];
					uint8_t ___byte0504;
				};
				#pragma pack(pop, tp)
				struct
				{
					char ___byte0504_OffsetPadding_forAlignmentOnly[504];
					uint8_t ___byte0504_forAlignmentOnly;
				};
				#pragma pack(push, tp, 1)
				struct
				{
					char ___byte0505_OffsetPadding[505];
					uint8_t ___byte0505;
				};
				#pragma pack(pop, tp)
				struct
				{
					char ___byte0505_OffsetPadding_forAlignmentOnly[505];
					uint8_t ___byte0505_forAlignmentOnly;
				};
				#pragma pack(push, tp, 1)
				struct
				{
					char ___byte0506_OffsetPadding[506];
					uint8_t ___byte0506;
				};
				#pragma pack(pop, tp)
				struct
				{
					char ___byte0506_OffsetPadding_forAlignmentOnly[506];
					uint8_t ___byte0506_forAlignmentOnly;
				};
				#pragma pack(push, tp, 1)
				struct
				{
					char ___byte0507_OffsetPadding[507];
					uint8_t ___byte0507;
				};
				#pragma pack(pop, tp)
				struct
				{
					char ___byte0507_OffsetPadding_forAlignmentOnly[507];
					uint8_t ___byte0507_forAlignmentOnly;
				};
				#pragma pack(push, tp, 1)
				struct
				{
					char ___byte0508_OffsetPadding[508];
					uint8_t ___byte0508;
				};
				#pragma pack(pop, tp)
				struct
				{
					char ___byte0508_OffsetPadding_forAlignmentOnly[508];
					uint8_t ___byte0508_forAlignmentOnly;
				};
				#pragma pack(push, tp, 1)
				struct
				{
					char ___byte0509_OffsetPadding[509];
					uint8_t ___byte0509;
				};
				#pragma pack(pop, tp)
				struct
				{
					char ___byte0509_OffsetPadding_forAlignmentOnly[509];
					uint8_t ___byte0509_forAlignmentOnly;
				};
			};
		};
		uint8_t FixedBytes510_t95B284C3FF966246998B23701C3F0F55C6BD7973__padding[510];
	};
};
#pragma pack(pop, tp)
#pragma pack(push, tp, 1)
struct FixedBytes62_t25CC23B7A3CF922DF0D1F0BFD5F801864D4FFD2A 
{
	union
	{
		struct
		{
			union
			{
				#pragma pack(push, tp, 1)
				struct
				{
					FixedBytes16_tBBD888116CBD6329886E0FE97A82EEB4B7CB3FA0 ___offset0000;
				};
				#pragma pack(pop, tp)
				struct
				{
					FixedBytes16_tBBD888116CBD6329886E0FE97A82EEB4B7CB3FA0 ___offset0000_forAlignmentOnly;
				};
				#pragma pack(push, tp, 1)
				struct
				{
					char ___offset0016_OffsetPadding[16];
					FixedBytes16_tBBD888116CBD6329886E0FE97A82EEB4B7CB3FA0 ___offset0016;
				};
				#pragma pack(pop, tp)
				struct
				{
					char ___offset0016_OffsetPadding_forAlignmentOnly[16];
					FixedBytes16_tBBD888116CBD6329886E0FE97A82EEB4B7CB3FA0 ___offset0016_forAlignmentOnly;
				};
				#pragma pack(push, tp, 1)
				struct
				{
					char ___offset0032_OffsetPadding[32];
					FixedBytes16_tBBD888116CBD6329886E0FE97A82EEB4B7CB3FA0 ___offset0032;
				};
				#pragma pack(pop, tp)
				struct
				{
					char ___offset0032_OffsetPadding_forAlignmentOnly[32];
					FixedBytes16_tBBD888116CBD6329886E0FE97A82EEB4B7CB3FA0 ___offset0032_forAlignmentOnly;
				};
				#pragma pack(push, tp, 1)
				struct
				{
					char ___byte0048_OffsetPadding[48];
					uint8_t ___byte0048;
				};
				#pragma pack(pop, tp)
				struct
				{
					char ___byte0048_OffsetPadding_forAlignmentOnly[48];
					uint8_t ___byte0048_forAlignmentOnly;
				};
				#pragma pack(push, tp, 1)
				struct
				{
					char ___byte0049_OffsetPadding[49];
					uint8_t ___byte0049;
				};
				#pragma pack(pop, tp)
				struct
				{
					char ___byte0049_OffsetPadding_forAlignmentOnly[49];
					uint8_t ___byte0049_forAlignmentOnly;
				};
				#pragma pack(push, tp, 1)
				struct
				{
					char ___byte0050_OffsetPadding[50];
					uint8_t ___byte0050;
				};
				#pragma pack(pop, tp)
				struct
				{
					char ___byte0050_OffsetPadding_forAlignmentOnly[50];
					uint8_t ___byte0050_forAlignmentOnly;
				};
				#pragma pack(push, tp, 1)
				struct
				{
					char ___byte0051_OffsetPadding[51];
					uint8_t ___byte0051;
				};
				#pragma pack(pop, tp)
				struct
				{
					char ___byte0051_OffsetPadding_forAlignmentOnly[51];
					uint8_t ___byte0051_forAlignmentOnly;
				};
				#pragma pack(push, tp, 1)
				struct
				{
					char ___byte0052_OffsetPadding[52];
					uint8_t ___byte0052;
				};
				#pragma pack(pop, tp)
				struct
				{
					char ___byte0052_OffsetPadding_forAlignmentOnly[52];
					uint8_t ___byte0052_forAlignmentOnly;
				};
				#pragma pack(push, tp, 1)
				struct
				{
					char ___byte0053_OffsetPadding[53];
					uint8_t ___byte0053;
				};
				#pragma pack(pop, tp)
				struct
				{
					char ___byte0053_OffsetPadding_forAlignmentOnly[53];
					uint8_t ___byte0053_forAlignmentOnly;
				};
				#pragma pack(push, tp, 1)
				struct
				{
					char ___byte0054_OffsetPadding[54];
					uint8_t ___byte0054;
				};
				#pragma pack(pop, tp)
				struct
				{
					char ___byte0054_OffsetPadding_forAlignmentOnly[54];
					uint8_t ___byte0054_forAlignmentOnly;
				};
				#pragma pack(push, tp, 1)
				struct
				{
					char ___byte0055_OffsetPadding[55];
					uint8_t ___byte0055;
				};
				#pragma pack(pop, tp)
				struct
				{
					char ___byte0055_OffsetPadding_forAlignmentOnly[55];
					uint8_t ___byte0055_forAlignmentOnly;
				};
				#pragma pack(push, tp, 1)
				struct
				{
					char ___byte0056_OffsetPadding[56];
					uint8_t ___byte0056;
				};
				#pragma pack(pop, tp)
				struct
				{
					char ___byte0056_OffsetPadding_forAlignmentOnly[56];
					uint8_t ___byte0056_forAlignmentOnly;
				};
				#pragma pack(push, tp, 1)
				struct
				{
					char ___byte0057_OffsetPadding[57];
					uint8_t ___byte0057;
				};
				#pragma pack(pop, tp)
				struct
				{
					char ___byte0057_OffsetPadding_forAlignmentOnly[57];
					uint8_t ___byte0057_forAlignmentOnly;
				};
				#pragma pack(push, tp, 1)
				struct
				{
					char ___byte0058_OffsetPadding[58];
					uint8_t ___byte0058;
				};
				#pragma pack(pop, tp)
				struct
				{
					char ___byte0058_OffsetPadding_forAlignmentOnly[58];
					uint8_t ___byte0058_forAlignmentOnly;
				};
				#pragma pack(push, tp, 1)
				struct
				{
					char ___byte0059_OffsetPadding[59];
					uint8_t ___byte0059;
				};
				#pragma pack(pop, tp)
				struct
				{
					char ___byte0059_OffsetPadding_forAlignmentOnly[59];
					uint8_t ___byte0059_forAlignmentOnly;
				};
				#pragma pack(push, tp, 1)
				struct
				{
					char ___byte0060_OffsetPadding[60];
					uint8_t ___byte0060;
				};
				#pragma pack(pop, tp)
				struct
				{
					char ___byte0060_OffsetPadding_forAlignmentOnly[60];
					uint8_t ___byte0060_forAlignmentOnly;
				};
				#pragma pack(push, tp, 1)
				struct
				{
					char ___byte0061_OffsetPadding[61];
					uint8_t ___byte0061;
				};
				#pragma pack(pop, tp)
				struct
				{
					char ___byte0061_OffsetPadding_forAlignmentOnly[61];
					uint8_t ___byte0061_forAlignmentOnly;
				};
			};
		};
		uint8_t FixedBytes62_t25CC23B7A3CF922DF0D1F0BFD5F801864D4FFD2A__padding[62];
	};
};
#pragma pack(pop, tp)
struct FontStyles_t9E611EE6BBE6E192A73EAFF7872596517C527FF5 
{
	int32_t ___value__;
};
struct FontWeight_tA2585C0A73B70D31CE71E7843149098A5E16BC80 
{
	int32_t ___value__;
};
struct HighlightState_tE4F50287E5E2E91D42AB77DEA281D88D3AD6A28B 
{
	Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B ___color;
	TMP_Offset_t2262BE4E87D9662487777FF8FFE1B17B0E4438C6 ___padding;
};
struct HorizontalAlignmentOptions_tCC21260E9FBEC656BA7783643ED5F44AFF7955A1 
{
	int32_t ___value__;
};
struct LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A  : public RuntimeObject
{
	String_t* ___displayName;
	bool ___enabled;
	MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 ___U3CTrackedHandleU3Ek__BackingField;
};
struct LoopType_t4E899D3814707E2829A8F0F5B5BB15AE6B924F1D 
{
	uint8_t ___value__;
};
struct MotionStatus_t83236EC883C79BC90C8A73C914F663747E0CB67A 
{
	uint8_t ___value__;
};
struct MotionTimeKind_tC52A6EA523692613259C10736A5E288598351BF0 
{
	uint8_t ___value__;
};
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C  : public RuntimeObject
{
	intptr_t ___m_CachedPtr;
};
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_marshaled_pinvoke
{
	intptr_t ___m_CachedPtr;
};
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_marshaled_com
{
	intptr_t ___m_CachedPtr;
};
struct PlayerLoopTiming_t81959E1FE09A2DC18137A0B1869F028DD1AC0E46 
{
	int32_t ___value__;
};
struct ProfilerMarker_tA256E18DA86EDBC5528CE066FC91C96EE86501AD 
{
	intptr_t ___m_Ptr;
};
struct RuntimeFieldHandle_t6E4C45B6D2EA12FC99185805A7E77527899B25C5 
{
	intptr_t ___value;
};
struct RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B 
{
	intptr_t ___value;
};
struct SchedulerType_tF4EC2906D48B6035EF036E2DDA64117467916F94 
{
	uint8_t ___value__;
};
struct ScrambleMode_t69563594FA3553E9F727BA2157731A82B36B2DB4 
{
	uint8_t ___value__;
};
struct TMP_TextElementType_t51EE6662436732F22C6B599F5757B7F35F706342 
{
	int32_t ___value__;
};
struct TextAlignmentOptions_tF3FA9020F7E2AF1A48660044540254009A22EF01 
{
	int32_t ___value__;
};
struct TextOverflowModes_t7DCCD00C16E3223CE50CDDCC53F785C0405BE203 
{
	int32_t ___value__;
};
struct TextRenderFlags_tE023FF398ECFE57A1DBC6FD2A1AF4AE9620F6E1C 
{
	int32_t ___value__;
};
struct TextureMappingOptions_t0E1A47C529DEB45A875486256E7026E97C940DAE 
{
	int32_t ___value__;
};
struct VertexGradient_t2C057B53C0EA6E987C2B7BAB0305E686DA1C9A8F 
{
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___topLeft;
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___topRight;
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___bottomLeft;
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___bottomRight;
};
struct VertexSortingOrder_t95B7AEDBDCAACC3459B6476E5CCC594A6422FFA8 
{
	int32_t ___value__;
};
struct VerticalAlignmentOptions_tCEF70AF60282B71AEEE14D51253CE6A61E72D855 
{
	int32_t ___value__;
};
struct WrapMode_t6C6EABC32662DF078C3C977196618603C2F3A079 
{
	int32_t ___value__;
};
struct RawData_t37CAF2D3F74B7723974ED7CEEE9B297D8FA64ED0  : public RuntimeObject
{
	intptr_t ___Bounds;
	intptr_t ___Count;
	uint8_t ___Data;
};
struct RawData_t37CAF2D3F74B7723974ED7CEEE9B297D8FA64ED0_marshaled_pinvoke
{
	intptr_t ___Bounds;
	intptr_t ___Count;
	uint8_t ___Data;
};
struct RawData_t37CAF2D3F74B7723974ED7CEEE9B297D8FA64ED0_marshaled_com
{
	intptr_t ___Bounds;
	intptr_t ___Count;
	uint8_t ___Data;
};
struct FillMethod_t36837ED12068DF1582CC20489D571B0BCAA7AD19 
{
	int32_t ___value__;
};
struct Type_t81D6F138C2FC745112D5247CD91BD483EDFFC041 
{
	int32_t ___value__;
};
struct AnimationMode_t1D121B298157A8A15F842B29A2539D5925CE18F9 
{
	int32_t ___value__;
};
struct Mode_t2D49D0E10E2FDA0026278C2400C16033888D0542 
{
	int32_t ___value__;
};
struct Transition_tF856A77C9FAC6D26EA3CA158CF68B739D35397B3 
{
	int32_t ___value__;
};
struct Direction_t4C81D17BB6C089A0EC1C4934525B86E75E693EFA 
{
	int32_t ___value__;
};
struct TextInputSources_t41387D6C9CB16E60390F47A15AEB8185BE966D26 
{
	int32_t ___value__;
};
struct MotionBuilderBuffer_2_t190528172F72DD395324243FA145EE61169E32DD  : public RuntimeObject
{
	uint16_t ___Version;
	MotionBuilderBuffer_2_t190528172F72DD395324243FA145EE61169E32DD* ___NextNode;
	float ___StartValue;
	float ___EndValue;
	NoOptions_t7F036E20F8ED9230BFEF6EDF7C39EDD013A70C13 ___Options;
	float ___Duration;
	int32_t ___Ease;
	uint8_t ___TimeKind;
	float ___Delay;
	int32_t ___Loops;
	uint8_t ___DelayType;
	uint8_t ___LoopType;
	bool ___CancelOnError;
	bool ___SkipValuesDuringDelay;
	bool ___ImmediateBind;
	RuntimeObject* ___State0;
	RuntimeObject* ___State1;
	RuntimeObject* ___State2;
	uint8_t ___StateCount;
	RuntimeObject* ___UpdateAction;
	Action_1_tD69A6DC9FBE94131E52F5A73B2A9D4AB51EEC404* ___OnLoopCompleteAction;
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___OnCompleteAction;
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___OnCancelAction;
	AnimationCurve_tCBFFAAD05CEBB35EF8D8631BD99914BE1A6BB354* ___AnimationCurve;
	RuntimeObject* ___Scheduler;
};
struct PropertyAnimationComponent_4_tCA45DED6A95CE84A368CA4F8254063DFDEF50430  : public LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A
{
	AudioSource_t871AC2272F896738252F04EE949AEF5B241D3299* ___target;
	SerializableMotionSettings_2_tBBF740A1ABE1D1FB72291E86D0A18E1D0344615B* ___settings;
	bool ___relative;
	float ___startValue;
};
struct PropertyAnimationComponent_4_tBB99184C5152D278C19B1DCFB8D5A5D544ED412F  : public LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A
{
	Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* ___target;
	SerializableMotionSettings_2_t3693954AA2D12D8178DB11DEC7E90BB1E76E77FA* ___settings;
	bool ___relative;
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___startValue;
};
struct PropertyAnimationComponent_4_t6064C157FE1A4A4F45E1AFB6FE75E039906FF546  : public LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A
{
	Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* ___target;
	SerializableMotionSettings_2_t648AF06EFD8D9425B51AB45F8F5A0558688D2CB9* ___settings;
	bool ___relative;
	Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D ___startValue;
};
struct PropertyAnimationComponent_4_t2D8062522B1FCB807975978239046A4155D39B20  : public LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A
{
	Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* ___target;
	SerializableMotionSettings_2_tBBF740A1ABE1D1FB72291E86D0A18E1D0344615B* ___settings;
	bool ___relative;
	float ___startValue;
};
struct PropertyAnimationComponent_4_t2601A4947A2D5A35A2599084810AF6453A550997  : public LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A
{
	CanvasGroup_t048C1461B14628CFAEBE6E7353093ADB04EBC094* ___target;
	SerializableMotionSettings_2_tBBF740A1ABE1D1FB72291E86D0A18E1D0344615B* ___settings;
	bool ___relative;
	float ___startValue;
};
struct PropertyAnimationComponent_4_tF6F50EDB8A7E8C29222B3AFADBAE693D16DF52B9  : public LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A
{
	Graphic_tCBFCA4585A19E2B75465AECFEAC43F4016BF7931* ___target;
	SerializableMotionSettings_2_t3693954AA2D12D8178DB11DEC7E90BB1E76E77FA* ___settings;
	bool ___relative;
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___startValue;
};
struct PropertyAnimationComponent_4_t70828D7BD0C4515F113C031602DE064B14E54D9D  : public LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A
{
	Image_tBC1D03F63BF71132E9A5E472B8742F172A011E7E* ___target;
	SerializableMotionSettings_2_t3693954AA2D12D8178DB11DEC7E90BB1E76E77FA* ___settings;
	bool ___relative;
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___startValue;
};
struct PropertyAnimationComponent_4_t3055D968ED52F4F0273E4FF05CD0DCBED67430D8  : public LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A
{
	Image_tBC1D03F63BF71132E9A5E472B8742F172A011E7E* ___target;
	SerializableMotionSettings_2_tBBF740A1ABE1D1FB72291E86D0A18E1D0344615B* ___settings;
	bool ___relative;
	float ___startValue;
};
struct PropertyAnimationComponent_4_tC2EBE75D61B25786A5CEFAAB069F9728AB8ADE38  : public LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A
{
	Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* ___target;
	SerializableMotionSettings_2_t3693954AA2D12D8178DB11DEC7E90BB1E76E77FA* ___settings;
	bool ___relative;
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___startValue;
};
struct PropertyAnimationComponent_4_tFFC6E0AAEFF40362D0995EF33FEC0D86477AAF43  : public LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A
{
	Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* ___target;
	SerializableMotionSettings_2_t1FDFBAD6B5C5F21C24DBC093EFE2E877001AACA8* ___settings;
	bool ___relative;
	int32_t ___startValue;
};
struct PropertyAnimationComponent_4_tD63E338D9E1045DE04B8EA405E7946BE7D672DC4  : public LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A
{
	Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* ___target;
	SerializableMotionSettings_2_tBBF740A1ABE1D1FB72291E86D0A18E1D0344615B* ___settings;
	bool ___relative;
	float ___startValue;
};
struct PropertyAnimationComponent_4_tD1FBBFFD2E7F359EC9AC66E71037C1FEB7CEA0A0  : public LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A
{
	Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* ___target;
	SerializableMotionSettings_2_t68BA1EF7C3F3B237B28B6AFFA427FC7555F2DD55* ___settings;
	bool ___relative;
	Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 ___startValue;
};
struct PropertyAnimationComponent_4_t46F55C810208BA57FDE53B079B56049C478DDB0D  : public LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A
{
	RectTransform_t6C5DA5E41A89E0F488B001E45E58963480E543A5* ___target;
	SerializableMotionSettings_2_t30307AE6A2C15AC189466AF2611AC7B1E0B9CF34* ___settings;
	bool ___relative;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___startValue;
};
struct PropertyAnimationComponent_4_tA1BCD5BEE0F57B312D19B1F3B3DA6DB724382651  : public LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A
{
	Rigidbody_t268697F5A994213ED97393309870968BC1C7393C* ___target;
	SerializableMotionSettings_2_tB03FD30C5F0B8FA9208B7DEDC0DCE8428F3D4BA3* ___settings;
	bool ___relative;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___startValue;
};
struct PropertyAnimationComponent_4_tADAC6F356EF97A0F23B4DA5600668B9179BDCEA9  : public LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A
{
	Rigidbody_t268697F5A994213ED97393309870968BC1C7393C* ___target;
	SerializableMotionSettings_2_t941B49604D2AED174AA3273AF5D5F123B4A524E0* ___settings;
	bool ___relative;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___startValue;
};
struct PropertyAnimationComponent_4_tC0373528562106EA72F0343691996D51CB63E787  : public LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A
{
	Rigidbody_t268697F5A994213ED97393309870968BC1C7393C* ___target;
	SerializableMotionSettings_2_t55B63654E284330688B1B3E6E3118556152DA199* ___settings;
	bool ___relative;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___startValue;
};
struct PropertyAnimationComponent_4_t3B21B4F6274B7FB8DBE9C6BE52DE8DCF4BF3E8F6  : public LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A
{
	Rigidbody2D_tBEBE9523CF4448544085AF46BF7E10AA499F320F* ___target;
	SerializableMotionSettings_2_tBBF740A1ABE1D1FB72291E86D0A18E1D0344615B* ___settings;
	bool ___relative;
	float ___startValue;
};
struct PropertyAnimationComponent_4_t650DF9F64171F99EF36ED6A65686A07593418047  : public LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A
{
	Rigidbody2D_tBEBE9523CF4448544085AF46BF7E10AA499F320F* ___target;
	SerializableMotionSettings_2_t56C9F73DC1787F07D5384F25A751DE5F2545FCF2* ___settings;
	bool ___relative;
	float ___startValue;
};
struct PropertyAnimationComponent_4_t66CBB5247360266707E3991C6E8D4CB67E3725C7  : public LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A
{
	Rigidbody2D_tBEBE9523CF4448544085AF46BF7E10AA499F320F* ___target;
	SerializableMotionSettings_2_t43CB1E09C9A34B62C32B4D459EDD9F4B6B550BB0* ___settings;
	bool ___relative;
	float ___startValue;
};
struct PropertyAnimationComponent_4_t9C615334D5F4C9EB77B51B6CC7FCBBA68A6F1BC1  : public LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A
{
	Rigidbody2D_tBEBE9523CF4448544085AF46BF7E10AA499F320F* ___target;
	SerializableMotionSettings_2_t30307AE6A2C15AC189466AF2611AC7B1E0B9CF34* ___settings;
	bool ___relative;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___startValue;
};
struct PropertyAnimationComponent_4_t5D30CE0EB8040A65C3D65C89085AE5F5228863A1  : public LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A
{
	Rigidbody2D_tBEBE9523CF4448544085AF46BF7E10AA499F320F* ___target;
	SerializableMotionSettings_2_tD4D79FB48DACE0789874BFFB567CA8E370472B4F* ___settings;
	bool ___relative;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___startValue;
};
struct PropertyAnimationComponent_4_t50A59A2A155B4C0FEE2BB24AAB724B14F6B09FCA  : public LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A
{
	Rigidbody2D_tBEBE9523CF4448544085AF46BF7E10AA499F320F* ___target;
	SerializableMotionSettings_2_t30E1FF6A91CF9E004A78F436437C4041445AAE30* ___settings;
	bool ___relative;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___startValue;
};
struct PropertyAnimationComponent_4_t94E70FEB75C540E578E0A08DCF6494D5FB308BDF  : public LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A
{
	Slider_t87EA570E3D6556CABF57456C2F3873FFD86E652F* ___target;
	SerializableMotionSettings_2_tBBF740A1ABE1D1FB72291E86D0A18E1D0344615B* ___settings;
	bool ___relative;
	float ___startValue;
};
struct PropertyAnimationComponent_4_t7C407E55692B175E9B85F6F37E47389D5E016033  : public LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A
{
	SpriteRenderer_t1DD7FE258F072E1FA87D6577BA27225892B8047B* ___target;
	SerializableMotionSettings_2_t3693954AA2D12D8178DB11DEC7E90BB1E76E77FA* ___settings;
	bool ___relative;
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___startValue;
};
struct PropertyAnimationComponent_4_tC9DF0BA8C7379660466E756E8BC51941FBADF773  : public LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A
{
	TMP_Text_tE8D677872D43AD4B2AAF0D6101692A17D0B251A9* ___target;
	SerializableMotionSettings_2_t3693954AA2D12D8178DB11DEC7E90BB1E76E77FA* ___settings;
	bool ___relative;
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___startValue;
};
struct PropertyAnimationComponent_4_t1F5D2F365D7CFCE15EDC8B1893E303565AD26A5D  : public LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A
{
	TMP_Text_tE8D677872D43AD4B2AAF0D6101692A17D0B251A9* ___target;
	SerializableMotionSettings_2_tBBF740A1ABE1D1FB72291E86D0A18E1D0344615B* ___settings;
	bool ___relative;
	float ___startValue;
};
struct PropertyAnimationComponent_4_tB90DAC0CA312E951113B94CAF38513E5387970CC  : public LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A
{
	Text_tD60B2346DAA6666BF0D822FF607F0B220C2B9E62* ___target;
	SerializableMotionSettings_2_t3693954AA2D12D8178DB11DEC7E90BB1E76E77FA* ___settings;
	bool ___relative;
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___startValue;
};
struct PropertyAnimationComponent_4_t9A27A73C9D0C8FCD7BEB04ECBD94195E2C0DCDE6  : public LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A
{
	Text_tD60B2346DAA6666BF0D822FF607F0B220C2B9E62* ___target;
	SerializableMotionSettings_2_t1FDFBAD6B5C5F21C24DBC093EFE2E877001AACA8* ___settings;
	bool ___relative;
	int32_t ___startValue;
};
struct PropertyAnimationComponent_4_t7C8F125421D8471CD9D69B22158C7C229C8BDF9F  : public LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A
{
	Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* ___target;
	SerializableMotionSettings_2_tB03FD30C5F0B8FA9208B7DEDC0DCE8428F3D4BA3* ___settings;
	bool ___relative;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___startValue;
};
struct PropertyAnimationComponent_4_tF0CA74F1986C69F2881B9323B814B78914FF2B3F  : public LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A
{
	Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* ___target;
	SerializableMotionSettings_2_t941B49604D2AED174AA3273AF5D5F123B4A524E0* ___settings;
	bool ___relative;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___startValue;
};
struct PropertyAnimationComponent_4_t0F104589B9C63A113397FAD0148DC1D0285C3570  : public LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A
{
	Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* ___target;
	SerializableMotionSettings_2_t55B63654E284330688B1B3E6E3118556152DA199* ___settings;
	bool ___relative;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___startValue;
};
struct PropertyAnimationComponent_4_t43BC3D24D9998C1D11EC4BDDA0FA469910CEC744  : public LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A
{
	Volume_t7CAAEA22D7F13A50FAE114DE7A6986FEAC837377* ___target;
	SerializableMotionSettings_2_tBBF740A1ABE1D1FB72291E86D0A18E1D0344615B* ___settings;
	bool ___relative;
	float ___startValue;
};
struct Span_1_t44E69912875D62DD12C3D1C8927FE31A9A209821 
{
	ByReference_1_tFF25269D410D247DBCA2AA9CEA0DBE153B705049 ____pointer;
	int32_t ____length;
};
struct Span_1_t3F436092261253E8F2AF9867CA253C3B370766C2 
{
	ByReference_1_t98B79BFB40A2CA0814BC183B09B4339A5EBF8524 ____pointer;
	int32_t ____length;
};
struct TMP_TextProcessingStack_1_tA5C8CED87DD9E73F6359E23B334FFB5B6F813FD4 
{
	FontWeightU5BU5D_t2A406B5BAB0DD0F06E7F1773DB062E4AF98067BA* ___itemStack;
	int32_t ___index;
	int32_t ___m_DefaultItem;
	int32_t ___m_Capacity;
	int32_t ___m_RolloverSize;
	int32_t ___m_Count;
};
struct TMP_TextProcessingStack_1_t57AECDCC936A7FF1D6CF66CA11560B28A675648D 
{
	HighlightStateU5BU5D_tA878A0AF1F4F52882ACD29515AADC277EE135622* ___itemStack;
	int32_t ___index;
	HighlightState_tE4F50287E5E2E91D42AB77DEA281D88D3AD6A28B ___m_DefaultItem;
	int32_t ___m_Capacity;
	int32_t ___m_RolloverSize;
	int32_t ___m_Count;
};
struct TMP_TextProcessingStack_1_t243EA1B5D7FD2295D6533B953F0BBE8F52EFB8A0 
{
	HorizontalAlignmentOptionsU5BU5D_t4D185662282BFB910D8B9A8199E91578E9422658* ___itemStack;
	int32_t ___index;
	int32_t ___m_DefaultItem;
	int32_t ___m_Capacity;
	int32_t ___m_RolloverSize;
	int32_t ___m_Count;
};
struct ValueAnimationComponent_3_t02507D0A207D68A1BC1701CC8EE80C6B5DAD75E0  : public LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A
{
	SerializableMotionSettings_2_t3693954AA2D12D8178DB11DEC7E90BB1E76E77FA* ___settings;
	UnityEvent_1_tF4BE0B078FD22C6D76548861637E94AB782888C9* ___onValueChanged;
};
struct ValueAnimationComponent_3_tB17C7EB4A1D9997DB82C86BAE9692FCE017D2415  : public LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A
{
	SerializableMotionSettings_2_t034045EE86761080D86B40BCC8800E384935F499* ___settings;
	UnityEvent_1_t7EBD40037C3DBB4EEFE941AEFD2E3CA88C7382ED* ___onValueChanged;
};
struct ValueAnimationComponent_3_t595F5BA6422722CDF28C79CF2971881EFCC09CCD  : public LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A
{
	SerializableMotionSettings_2_t1FDFBAD6B5C5F21C24DBC093EFE2E877001AACA8* ___settings;
	UnityEvent_1_t7CC0661D6B113117B4CC68761D93AC8DF5DBD66A* ___onValueChanged;
};
struct ValueAnimationComponent_3_t92CD7588E37E9CCDA6A882EE3FB36A61E34CF7D2  : public LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A
{
	SerializableMotionSettings_2_tB573D3BB787DCCC4166AA217DB2424C3C4A8787E* ___settings;
	UnityEvent_1_t04EB8F75BA20E19772BBB0023A57CC7FBAFED743* ___onValueChanged;
};
struct ValueAnimationComponent_3_tCC2320109B3D50771B2F3D6B0F00DC4EDDAD7351  : public LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A
{
	SerializableMotionSettings_2_tBBF740A1ABE1D1FB72291E86D0A18E1D0344615B* ___settings;
	UnityEvent_1_tDD811EB8F49CEE97BA6DF59344DFE6C6F42553D4* ___onValueChanged;
};
struct ValueAnimationComponent_3_t4DCB9D2381C489A5D17579A5EF8F3B8E5ABA2876  : public LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A
{
	SerializableMotionSettings_2_t30307AE6A2C15AC189466AF2611AC7B1E0B9CF34* ___settings;
	UnityEvent_1_t9A868DD8EBFC0D9D8134D903A170ECBDEE567932* ___onValueChanged;
};
struct ValueAnimationComponent_3_t53C6B62F99FEFCE0BB2D49AF64FA7B03EFC2745D  : public LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A
{
	SerializableMotionSettings_2_tB03FD30C5F0B8FA9208B7DEDC0DCE8428F3D4BA3* ___settings;
	UnityEvent_1_tB42B7E8E9010FF524B45FD0EC7AD37D7D3B006AE* ___onValueChanged;
};
struct ValueAnimationComponent_3_tF4558CF2EFCE6A8F01D0043A88161A0AAD83882E  : public LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A
{
	SerializableMotionSettings_2_t68BA1EF7C3F3B237B28B6AFFA427FC7555F2DD55* ___settings;
	UnityEvent_1_t7FA3641C06EC4F8BD5600438DB4CB16B7042FB59* ___onValueChanged;
};
struct Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3  : public Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C
{
};
struct DelayComponent_t02FB33B26EE45214DF563DDEFA55F59D4CBF582D  : public LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A
{
	float ___delay;
};
struct EventComponent_t25446BA52F33198616853D8AD89403122A0400FF  : public LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A
{
	UnityEvent_tDC2C3548799DBC91D1E3F3DE60083A66F4751977* ___onPlay;
	UnityEvent_tDC2C3548799DBC91D1E3F3DE60083A66F4751977* ___onStop;
};
struct FixedString512Bytes_t0C425B0F2C07FEA1642C32BF8559116DF2BFF50E 
{
	union
	{
		struct
		{
			uint16_t ___utf8LengthInBytes;
			alignas(1) FixedBytes510_t95B284C3FF966246998B23701C3F0F55C6BD7973 ___bytes;
		};
		uint8_t FixedString512Bytes_t0C425B0F2C07FEA1642C32BF8559116DF2BFF50E__padding[512];
	};
};
struct FixedString64Bytes_t0F1B6FFAFD8C15898CD77D91A79AB36AA078E0A5 
{
	union
	{
		struct
		{
			uint16_t ___utf8LengthInBytes;
			alignas(1) FixedBytes62_t25CC23B7A3CF922DF0D1F0BFD5F801864D4FFD2A ___bytes;
		};
		uint8_t FixedString64Bytes_t0F1B6FFAFD8C15898CD77D91A79AB36AA078E0A5__padding[64];
	};
};
struct Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3  : public Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C
{
};
struct MulticastDelegate_t  : public Delegate_t
{
	DelegateU5BU5D_tC5AB7E8F745616680F337909D3A8E6C722CDF771* ___delegates;
};
struct MulticastDelegate_t_marshaled_pinvoke : public Delegate_t_marshaled_pinvoke
{
	Delegate_t_marshaled_pinvoke** ___delegates;
};
struct MulticastDelegate_t_marshaled_com : public Delegate_t_marshaled_com
{
	Delegate_t_marshaled_com** ___delegates;
};
struct Navigation_t4D2E201D65749CF4E104E8AC1232CF1D6F14795C 
{
	int32_t ___m_Mode;
	bool ___m_WrapAround;
	Selectable_t3251808068A17B8E92FB33590A4C2FA66D456712* ___m_SelectOnUp;
	Selectable_t3251808068A17B8E92FB33590A4C2FA66D456712* ___m_SelectOnDown;
	Selectable_t3251808068A17B8E92FB33590A4C2FA66D456712* ___m_SelectOnLeft;
	Selectable_t3251808068A17B8E92FB33590A4C2FA66D456712* ___m_SelectOnRight;
};
struct Navigation_t4D2E201D65749CF4E104E8AC1232CF1D6F14795C_marshaled_pinvoke
{
	int32_t ___m_Mode;
	int32_t ___m_WrapAround;
	Selectable_t3251808068A17B8E92FB33590A4C2FA66D456712* ___m_SelectOnUp;
	Selectable_t3251808068A17B8E92FB33590A4C2FA66D456712* ___m_SelectOnDown;
	Selectable_t3251808068A17B8E92FB33590A4C2FA66D456712* ___m_SelectOnLeft;
	Selectable_t3251808068A17B8E92FB33590A4C2FA66D456712* ___m_SelectOnRight;
};
struct Navigation_t4D2E201D65749CF4E104E8AC1232CF1D6F14795C_marshaled_com
{
	int32_t ___m_Mode;
	int32_t ___m_WrapAround;
	Selectable_t3251808068A17B8E92FB33590A4C2FA66D456712* ___m_SelectOnUp;
	Selectable_t3251808068A17B8E92FB33590A4C2FA66D456712* ___m_SelectOnDown;
	Selectable_t3251808068A17B8E92FB33590A4C2FA66D456712* ___m_SelectOnLeft;
	Selectable_t3251808068A17B8E92FB33590A4C2FA66D456712* ___m_SelectOnRight;
};
struct PlayLitMotionAnimationComponent_t44C212AF544D647258158A4B4979FF728DB597CE  : public LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A
{
	LitMotionAnimation_t92F1AF7CF4859CEDD166DF7FD8091FDB2C34AF4A* ___target;
};
struct StringValueAnimation_t4C21FFF21AA92CA403E695317EFDDC25F6F729A0  : public LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A
{
	SerializableMotionSettings_2_tF48B33D24FDF5B9DD96E1CE305D788D9D67A704E* ___settings;
	UnityEvent_1_tC9859540CF1468306CAB6D758C0A0D95DBCEC257* ___onValueChanged;
};
struct SystemException_tCC48D868298F4C0705279823E34B00F4FBDB7295  : public Exception_t
{
};
struct TMP_LineInfo_tB75C1965B58DB7B3A046C8CA55AD6AB92B6B17B3 
{
	int32_t ___controlCharacterCount;
	int32_t ___characterCount;
	int32_t ___visibleCharacterCount;
	int32_t ___spaceCount;
	int32_t ___wordCount;
	int32_t ___firstCharacterIndex;
	int32_t ___firstVisibleCharacterIndex;
	int32_t ___lastCharacterIndex;
	int32_t ___lastVisibleCharacterIndex;
	float ___length;
	float ___lineHeight;
	float ___ascender;
	float ___baseline;
	float ___descender;
	float ___maxAdvance;
	float ___width;
	float ___marginLeft;
	float ___marginRight;
	int32_t ___alignment;
	Extents_tA2D2F95811D0A18CB7AC3570D2D8F8CD3AF4C4A8 ___lineExtents;
};
struct Type_t  : public MemberInfo_t
{
	RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B ____impl;
};
struct UnsafeAnimationCurve_tBD078B5CE0B9A8AACE46BD298C6FB2F7EBDC9E0C 
{
	UnsafeList_1_t5460E69E2F6E287ACD0577422AF7887E387EE42F ___keys;
	int32_t ___preWrapMode;
	int32_t ___postWrapMode;
};
struct MotionState_t92DCDE8373525507DED2EAF6EA7B9AB1F409C46D 
{
	uint8_t ___Status;
	uint8_t ___PrevStatus;
	bool ___IsPreserved;
	bool ___IsInSequence;
	uint16_t ___CompletedLoops;
	uint16_t ___PrevCompletedLoops;
	double ___Time;
	float ___PlaybackSpeed;
};
struct MotionState_t92DCDE8373525507DED2EAF6EA7B9AB1F409C46D_marshaled_pinvoke
{
	uint8_t ___Status;
	uint8_t ___PrevStatus;
	int32_t ___IsPreserved;
	int32_t ___IsInSequence;
	uint16_t ___CompletedLoops;
	uint16_t ___PrevCompletedLoops;
	double ___Time;
	float ___PlaybackSpeed;
};
struct MotionState_t92DCDE8373525507DED2EAF6EA7B9AB1F409C46D_marshaled_com
{
	uint8_t ___Status;
	uint8_t ___PrevStatus;
	int32_t ___IsPreserved;
	int32_t ___IsInSequence;
	uint16_t ___CompletedLoops;
	uint16_t ___PrevCompletedLoops;
	double ___Time;
	float ___PlaybackSpeed;
};
struct Action_2_t61CA71EC63C565FB5A59A8A8E52B6C7336CD7A45  : public MulticastDelegate_t
{
};
struct Action_2_t02F23795BC96180858055AB64D80A7006668D4D6  : public MulticastDelegate_t
{
};
struct Action_2_t25A8547AFC85E4914143093EDA11AE27C97D9E10  : public MulticastDelegate_t
{
};
struct Action_2_t000191B3D702D8E6114AA07D7039CA5909A6BF5E  : public MulticastDelegate_t
{
};
struct ColorPropertyAnimationComponent_1_tAD1067F24BBAF944E40270FCC4CBBB6A0F82380D  : public PropertyAnimationComponent_4_tBB99184C5152D278C19B1DCFB8D5A5D544ED412F
{
};
struct ColorPropertyAnimationComponent_1_t2DCF6BE5B232AEEC66DE5551BC91B57589438F4C  : public PropertyAnimationComponent_4_tF6F50EDB8A7E8C29222B3AFADBAE693D16DF52B9
{
};
struct ColorPropertyAnimationComponent_1_tE6AEF5F64546DD2C24C3A74C1AE6F63EF46DABD9  : public PropertyAnimationComponent_4_t70828D7BD0C4515F113C031602DE064B14E54D9D
{
};
struct ColorPropertyAnimationComponent_1_t1A004C26C26C52867C5A5F2113F7CE3672961051  : public PropertyAnimationComponent_4_tC2EBE75D61B25786A5CEFAAB069F9728AB8ADE38
{
};
struct ColorPropertyAnimationComponent_1_t2D91760F9E1E05B38E267F816DBC28A81B2F74AC  : public PropertyAnimationComponent_4_t7C407E55692B175E9B85F6F37E47389D5E016033
{
};
struct ColorPropertyAnimationComponent_1_t39834DD27CD732DFE4F633FFD9F2D6A086CCBB06  : public PropertyAnimationComponent_4_tC9DF0BA8C7379660466E756E8BC51941FBADF773
{
};
struct ColorPropertyAnimationComponent_1_tF608D7D00AEB026C1B6A6C045B2055450B730FA3  : public PropertyAnimationComponent_4_tB90DAC0CA312E951113B94CAF38513E5387970CC
{
};
struct FloatPropertyAnimationComponent_1_t766705FAD368B247FAB08A4A977B8B3529AC1933  : public PropertyAnimationComponent_4_tCA45DED6A95CE84A368CA4F8254063DFDEF50430
{
};
struct FloatPropertyAnimationComponent_1_t37A61899F20322CCCFA7C09CD5B7271B76DCEC17  : public PropertyAnimationComponent_4_t2D8062522B1FCB807975978239046A4155D39B20
{
};
struct FloatPropertyAnimationComponent_1_t3EE1259037FE3CF6B6303A336C605A0F318CB5E6  : public PropertyAnimationComponent_4_t2601A4947A2D5A35A2599084810AF6453A550997
{
};
struct FloatPropertyAnimationComponent_1_tBF445C4B23C073DFA04D5E88341C03BB479265B7  : public PropertyAnimationComponent_4_t3055D968ED52F4F0273E4FF05CD0DCBED67430D8
{
};
struct FloatPropertyAnimationComponent_1_t4EDF64705C1358D105E46606D44590DA7EE60C40  : public PropertyAnimationComponent_4_tD63E338D9E1045DE04B8EA405E7946BE7D672DC4
{
};
struct FloatPropertyAnimationComponent_1_t48E23230B8D939712EE3FA3062665415D1B00BAB  : public PropertyAnimationComponent_4_t94E70FEB75C540E578E0A08DCF6494D5FB308BDF
{
};
struct FloatPropertyAnimationComponent_1_t1AB552982C92303A2C6CC5876C59DC138C174E43  : public PropertyAnimationComponent_4_t1F5D2F365D7CFCE15EDC8B1893E303565AD26A5D
{
};
struct FloatPropertyAnimationComponent_1_t230707BA48829ADA4918D773DC2E3DF24F0080C7  : public PropertyAnimationComponent_4_t43BC3D24D9998C1D11EC4BDDA0FA469910CEC744
{
};
struct IntPropertyAnimationComponent_1_tB4B79248A9306E45219CF94F18CB2856CA5E3B68  : public PropertyAnimationComponent_4_tFFC6E0AAEFF40362D0995EF33FEC0D86477AAF43
{
};
struct IntPropertyAnimationComponent_1_t41237611C68CEA9B10C6849A5D5AE94883405EE5  : public PropertyAnimationComponent_4_t9A27A73C9D0C8FCD7BEB04ECBD94195E2C0DCDE6
{
};
struct PropertyAnimationComponent_4_t6EC795E7B3C765371C852B50A1F3A17D7C9E170E  : public LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A
{
	TMP_Text_tE8D677872D43AD4B2AAF0D6101692A17D0B251A9* ___target;
	SerializableMotionSettings_2_tF48B33D24FDF5B9DD96E1CE305D788D9D67A704E* ___settings;
	bool ___relative;
	FixedString512Bytes_t0C425B0F2C07FEA1642C32BF8559116DF2BFF50E ___startValue;
};
struct PropertyAnimationComponent_4_tDEA5D4A766BC92512DB06B4207009265F9BBDDFD  : public LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A
{
	Text_tD60B2346DAA6666BF0D822FF607F0B220C2B9E62* ___target;
	SerializableMotionSettings_2_tF48B33D24FDF5B9DD96E1CE305D788D9D67A704E* ___settings;
	bool ___relative;
	FixedString512Bytes_t0C425B0F2C07FEA1642C32BF8559116DF2BFF50E ___startValue;
};
struct RectPropertyAnimationComponent_1_t4A7ABE05AD402BC5405540B43257C6CAD0704C6D  : public PropertyAnimationComponent_4_t6064C157FE1A4A4F45E1AFB6FE75E039906FF546
{
};
struct Rigidbody2DPositionAnimationBase_2_t69CE9CD2C9AB3634CDE70A4430336030629AE27A  : public PropertyAnimationComponent_4_t9C615334D5F4C9EB77B51B6CC7FCBBA68A6F1BC1
{
	bool ___useMovePosition;
};
struct Rigidbody2DPositionAnimationBase_2_tEC44827A997686FCA2419EFAB9EBE6C21594FA08  : public PropertyAnimationComponent_4_t5D30CE0EB8040A65C3D65C89085AE5F5228863A1
{
	bool ___useMovePosition;
};
struct Rigidbody2DPositionAnimationBase_2_tD610427108FEF8F8BE698CEB4A2CECD0661F7A50  : public PropertyAnimationComponent_4_t50A59A2A155B4C0FEE2BB24AAB724B14F6B09FCA
{
	bool ___useMovePosition;
};
struct Rigidbody2DRotationAnimationBase_2_t86890366317CD729D92AE6912188756B638532DB  : public PropertyAnimationComponent_4_t3B21B4F6274B7FB8DBE9C6BE52DE8DCF4BF3E8F6
{
	bool ___useMoveRotation;
};
struct Rigidbody2DRotationAnimationBase_2_tE0B9D89B886A179723638F20CECDFA9455B1FFCE  : public PropertyAnimationComponent_4_t650DF9F64171F99EF36ED6A65686A07593418047
{
	bool ___useMoveRotation;
};
struct Rigidbody2DRotationAnimationBase_2_tFE21E96D35A59F82D112D56BB1CD4DACE31FF142  : public PropertyAnimationComponent_4_t66CBB5247360266707E3991C6E8D4CB67E3725C7
{
	bool ___useMoveRotation;
};
struct RigidbodyPositionAnimationBase_2_t3F8DF2590F9F24CFEC040865A4F75F1112C82612  : public PropertyAnimationComponent_4_tA1BCD5BEE0F57B312D19B1F3B3DA6DB724382651
{
	bool ___useMovePosition;
};
struct RigidbodyPositionAnimationBase_2_t4B7F6634781E49079650FC961B633396250CCB64  : public PropertyAnimationComponent_4_tADAC6F356EF97A0F23B4DA5600668B9179BDCEA9
{
	bool ___useMovePosition;
};
struct RigidbodyPositionAnimationBase_2_tFF37C9F46D719B654E722FB0A986E443FEA95121  : public PropertyAnimationComponent_4_tC0373528562106EA72F0343691996D51CB63E787
{
	bool ___useMovePosition;
};
struct RigidbodyRotationAnimationBase_2_t66E15E7EA84710E8BE780C9D318AC50C679161CF  : public PropertyAnimationComponent_4_tA1BCD5BEE0F57B312D19B1F3B3DA6DB724382651
{
	bool ___useMoveRotation;
};
struct RigidbodyRotationAnimationBase_2_t4ED992FE889ACCD94A95BB34F67343C3D80FD125  : public PropertyAnimationComponent_4_tADAC6F356EF97A0F23B4DA5600668B9179BDCEA9
{
	bool ___useMoveRotation;
};
struct RigidbodyRotationAnimationBase_2_t1817E2F258DFDA6844D7130B71D79AF68737E26E  : public PropertyAnimationComponent_4_tC0373528562106EA72F0343691996D51CB63E787
{
	bool ___useMoveRotation;
};
struct TransformPositionAnimationBase_2_t12DE280ED40D363B87FDE5057F2A16BAE67FF802  : public PropertyAnimationComponent_4_t7C8F125421D8471CD9D69B22158C7C229C8BDF9F
{
	bool ___useWorldSpace;
};
struct TransformPositionAnimationBase_2_tE046D85AA8257B076AEA1EC06523CD68298DC058  : public PropertyAnimationComponent_4_tF0CA74F1986C69F2881B9323B814B78914FF2B3F
{
	bool ___useWorldSpace;
};
struct TransformPositionAnimationBase_2_t4B424C50A5AC08FC89469AD266ABFD58DE673D3C  : public PropertyAnimationComponent_4_t0F104589B9C63A113397FAD0148DC1D0285C3570
{
	bool ___useWorldSpace;
};
struct TransformRotationAnimationBase_2_tFFAE1B23BA584F4A5C424117F0D678258C5D31F2  : public PropertyAnimationComponent_4_t7C8F125421D8471CD9D69B22158C7C229C8BDF9F
{
	bool ___useWorldSpace;
};
struct TransformRotationAnimationBase_2_t844162742B1631A9477AD8582685D20A7F4A8563  : public PropertyAnimationComponent_4_tF0CA74F1986C69F2881B9323B814B78914FF2B3F
{
	bool ___useWorldSpace;
};
struct TransformRotationAnimationBase_2_t8558AFAC045C23114B034681003F2C2F07D02A8E  : public PropertyAnimationComponent_4_t0F104589B9C63A113397FAD0148DC1D0285C3570
{
	bool ___useWorldSpace;
};
struct TransformScaleAnimationBase_2_tBE02F8DFD3B63F89AD312977DEA9C0F63D75C8CA  : public PropertyAnimationComponent_4_t7C8F125421D8471CD9D69B22158C7C229C8BDF9F
{
};
struct TransformScaleAnimationBase_2_t17D49FB722A1A6F77DC3B5D3EB244F546109A4B0  : public PropertyAnimationComponent_4_tF0CA74F1986C69F2881B9323B814B78914FF2B3F
{
};
struct TransformScaleAnimationBase_2_t35F2BE396D00623EE3AFF251C9245768B42F60CB  : public PropertyAnimationComponent_4_t0F104589B9C63A113397FAD0148DC1D0285C3570
{
};
struct Vector2PropertyAnimationComponent_1_t8A177D739E23FF44639E13B99726B0A6C932D365  : public PropertyAnimationComponent_4_t46F55C810208BA57FDE53B079B56049C478DDB0D
{
};
struct Vector4PropertyAnimationComponent_1_tD5781699C642AFAE6FA17B1A9EC214F1A3DBDB75  : public PropertyAnimationComponent_4_tD1FBBFFD2E7F359EC9AC66E71037C1FEB7CEA0A0
{
};
struct Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07  : public MulticastDelegate_t
{
};
struct ArgumentException_tAD90411542A20A9C72D5CDA3A84181D8B947A263  : public SystemException_tCC48D868298F4C0705279823E34B00F4FBDB7295
{
	String_t* ____paramName;
};
struct Behaviour_t01970CFBBA658497AE30F311C447DB0440BAB7FA  : public Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3
{
};
struct ColorValueAnimation_tF6326EB4342E17097D0A55A411436898AA192498  : public ValueAnimationComponent_3_t02507D0A207D68A1BC1701CC8EE80C6B5DAD75E0
{
};
struct DoubleValueAnimation_t7880671F5296AFC7C98CC1B199266E8FD5018FBF  : public ValueAnimationComponent_3_tB17C7EB4A1D9997DB82C86BAE9692FCE017D2415
{
};
struct FloatValueAnimation_t1F8A888C348C3256D7562E59BCCE868B248CC70E  : public ValueAnimationComponent_3_tCC2320109B3D50771B2F3D6B0F00DC4EDDAD7351
{
};
struct IntValueAnimation_t45CDBEB887CCC11D6E63FD4949100D30B9C74900  : public ValueAnimationComponent_3_t595F5BA6422722CDF28C79CF2971881EFCC09CCD
{
};
struct InvalidOperationException_t5DDE4D49B7405FAAB1E4576F4715A42A3FAD4BAB  : public SystemException_tCC48D868298F4C0705279823E34B00F4FBDB7295
{
};
struct LongValueAnimation_t93592BE53D79469CDD3CAF211EA24407BC68FEBD  : public ValueAnimationComponent_3_t92CD7588E37E9CCDA6A882EE3FB36A61E34CF7D2
{
};
struct Renderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF  : public Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3
{
};
struct StringOptions_tDDA84E855CD29F2BCD9BA102EA173B219CFCADA7 
{
	uint8_t ___ScrambleMode;
	bool ___RichTextEnabled;
	FixedString64Bytes_t0F1B6FFAFD8C15898CD77D91A79AB36AA078E0A5 ___CustomScrambleChars;
	uint32_t ___RandomSeed;
};
struct StringOptions_tDDA84E855CD29F2BCD9BA102EA173B219CFCADA7_marshaled_pinvoke
{
	uint8_t ___ScrambleMode;
	int32_t ___RichTextEnabled;
	FixedString64Bytes_t0F1B6FFAFD8C15898CD77D91A79AB36AA078E0A5 ___CustomScrambleChars;
	uint32_t ___RandomSeed;
};
struct StringOptions_tDDA84E855CD29F2BCD9BA102EA173B219CFCADA7_marshaled_com
{
	uint8_t ___ScrambleMode;
	int32_t ___RichTextEnabled;
	FixedString64Bytes_t0F1B6FFAFD8C15898CD77D91A79AB36AA078E0A5 ___CustomScrambleChars;
	uint32_t ___RandomSeed;
};
struct Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1  : public Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3
{
};
struct Vector2ValueAnimation_tD1E052DC2FF67290D4853707C8D826CEEB001ABC  : public ValueAnimationComponent_3_t4DCB9D2381C489A5D17579A5EF8F3B8E5ABA2876
{
};
struct Vector3ValueAnimation_t9E4D1D97B97516C598B84661502882CFC3E870EF  : public ValueAnimationComponent_3_t53C6B62F99FEFCE0BB2D49AF64FA7B03EFC2745D
{
};
struct Vector4ValueAnimation_t76776B5052E42EBBC0A23B83622EAA759229A76A  : public ValueAnimationComponent_3_tF4558CF2EFCE6A8F01D0043A88161A0AAD83882E
{
};
struct WordWrapState_t80F67D8CAA9B1A0A3D5266521E23A9F3100EDD0A 
{
	int32_t ___previous_WordBreak;
	int32_t ___total_CharacterCount;
	int32_t ___visible_CharacterCount;
	int32_t ___visible_SpriteCount;
	int32_t ___visible_LinkCount;
	int32_t ___firstCharacterIndex;
	int32_t ___firstVisibleCharacterIndex;
	int32_t ___lastCharacterIndex;
	int32_t ___lastVisibleCharIndex;
	int32_t ___lineNumber;
	float ___maxCapHeight;
	float ___maxAscender;
	float ___maxDescender;
	float ___startOfLineAscender;
	float ___maxLineAscender;
	float ___maxLineDescender;
	float ___pageAscender;
	int32_t ___horizontalAlignment;
	float ___marginLeft;
	float ___marginRight;
	float ___xAdvance;
	float ___preferredWidth;
	float ___preferredHeight;
	float ___previousLineScale;
	int32_t ___wordCount;
	int32_t ___fontStyle;
	int32_t ___italicAngle;
	float ___fontScaleMultiplier;
	float ___currentFontSize;
	float ___baselineOffset;
	float ___lineOffset;
	bool ___isDrivenLineSpacing;
	float ___glyphHorizontalAdvanceAdjustment;
	float ___cSpace;
	float ___mSpace;
	TMP_TextInfo_t09A8E906329422C3F0C059876801DD695B8D524D* ___textInfo;
	TMP_LineInfo_tB75C1965B58DB7B3A046C8CA55AD6AB92B6B17B3 ___lineInfo;
	Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B ___vertexColor;
	Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B ___underlineColor;
	Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B ___strikethroughColor;
	Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B ___highlightColor;
	TMP_FontStyleStack_t52885F172FADBC21346C835B5302167BDA8020DC ___basicStyleStack;
	TMP_TextProcessingStack_1_tFBA719426D68CE1F2B5849D97AF5E5D65846290C ___italicAngleStack;
	TMP_TextProcessingStack_1_tF2CD5BE59E5EB22EA9E3EE3043A004EA918C4BB3 ___colorStack;
	TMP_TextProcessingStack_1_tF2CD5BE59E5EB22EA9E3EE3043A004EA918C4BB3 ___underlineColorStack;
	TMP_TextProcessingStack_1_tF2CD5BE59E5EB22EA9E3EE3043A004EA918C4BB3 ___strikethroughColorStack;
	TMP_TextProcessingStack_1_tF2CD5BE59E5EB22EA9E3EE3043A004EA918C4BB3 ___highlightColorStack;
	TMP_TextProcessingStack_1_t57AECDCC936A7FF1D6CF66CA11560B28A675648D ___highlightStateStack;
	TMP_TextProcessingStack_1_tC8FAEB17246D3B171EFD11165A5761AE39B40D0C ___colorGradientStack;
	TMP_TextProcessingStack_1_t138EC06BE7F101AA0A3C8D2DC951E55AACE085E9 ___sizeStack;
	TMP_TextProcessingStack_1_t138EC06BE7F101AA0A3C8D2DC951E55AACE085E9 ___indentStack;
	TMP_TextProcessingStack_1_tA5C8CED87DD9E73F6359E23B334FFB5B6F813FD4 ___fontWeightStack;
	TMP_TextProcessingStack_1_tFBA719426D68CE1F2B5849D97AF5E5D65846290C ___styleStack;
	TMP_TextProcessingStack_1_t138EC06BE7F101AA0A3C8D2DC951E55AACE085E9 ___baselineStack;
	TMP_TextProcessingStack_1_tFBA719426D68CE1F2B5849D97AF5E5D65846290C ___actionStack;
	TMP_TextProcessingStack_1_tB03E08F69415B281A5A81138F09E49EE58402DF9 ___materialReferenceStack;
	TMP_TextProcessingStack_1_t243EA1B5D7FD2295D6533B953F0BBE8F52EFB8A0 ___lineJustificationStack;
	int32_t ___spriteAnimationID;
	TMP_FontAsset_t923BF2F78D7C5AC36376E168A1193B7CB4855160* ___currentFontAsset;
	TMP_SpriteAsset_t81F779E6F705CE190DC0D1F93A954CB8B1774B39* ___currentSpriteAsset;
	Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* ___currentMaterial;
	int32_t ___currentMaterialIndex;
	Extents_tA2D2F95811D0A18CB7AC3570D2D8F8CD3AF4C4A8 ___meshExtents;
	bool ___tagNoParsing;
	bool ___isNonBreakingSpace;
};
struct WordWrapState_t80F67D8CAA9B1A0A3D5266521E23A9F3100EDD0A_marshaled_pinvoke
{
	int32_t ___previous_WordBreak;
	int32_t ___total_CharacterCount;
	int32_t ___visible_CharacterCount;
	int32_t ___visible_SpriteCount;
	int32_t ___visible_LinkCount;
	int32_t ___firstCharacterIndex;
	int32_t ___firstVisibleCharacterIndex;
	int32_t ___lastCharacterIndex;
	int32_t ___lastVisibleCharIndex;
	int32_t ___lineNumber;
	float ___maxCapHeight;
	float ___maxAscender;
	float ___maxDescender;
	float ___startOfLineAscender;
	float ___maxLineAscender;
	float ___maxLineDescender;
	float ___pageAscender;
	int32_t ___horizontalAlignment;
	float ___marginLeft;
	float ___marginRight;
	float ___xAdvance;
	float ___preferredWidth;
	float ___preferredHeight;
	float ___previousLineScale;
	int32_t ___wordCount;
	int32_t ___fontStyle;
	int32_t ___italicAngle;
	float ___fontScaleMultiplier;
	float ___currentFontSize;
	float ___baselineOffset;
	float ___lineOffset;
	int32_t ___isDrivenLineSpacing;
	float ___glyphHorizontalAdvanceAdjustment;
	float ___cSpace;
	float ___mSpace;
	TMP_TextInfo_t09A8E906329422C3F0C059876801DD695B8D524D* ___textInfo;
	TMP_LineInfo_tB75C1965B58DB7B3A046C8CA55AD6AB92B6B17B3 ___lineInfo;
	Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B ___vertexColor;
	Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B ___underlineColor;
	Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B ___strikethroughColor;
	Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B ___highlightColor;
	TMP_FontStyleStack_t52885F172FADBC21346C835B5302167BDA8020DC ___basicStyleStack;
	TMP_TextProcessingStack_1_tFBA719426D68CE1F2B5849D97AF5E5D65846290C ___italicAngleStack;
	TMP_TextProcessingStack_1_tF2CD5BE59E5EB22EA9E3EE3043A004EA918C4BB3 ___colorStack;
	TMP_TextProcessingStack_1_tF2CD5BE59E5EB22EA9E3EE3043A004EA918C4BB3 ___underlineColorStack;
	TMP_TextProcessingStack_1_tF2CD5BE59E5EB22EA9E3EE3043A004EA918C4BB3 ___strikethroughColorStack;
	TMP_TextProcessingStack_1_tF2CD5BE59E5EB22EA9E3EE3043A004EA918C4BB3 ___highlightColorStack;
	TMP_TextProcessingStack_1_t57AECDCC936A7FF1D6CF66CA11560B28A675648D ___highlightStateStack;
	TMP_TextProcessingStack_1_tC8FAEB17246D3B171EFD11165A5761AE39B40D0C ___colorGradientStack;
	TMP_TextProcessingStack_1_t138EC06BE7F101AA0A3C8D2DC951E55AACE085E9 ___sizeStack;
	TMP_TextProcessingStack_1_t138EC06BE7F101AA0A3C8D2DC951E55AACE085E9 ___indentStack;
	TMP_TextProcessingStack_1_tA5C8CED87DD9E73F6359E23B334FFB5B6F813FD4 ___fontWeightStack;
	TMP_TextProcessingStack_1_tFBA719426D68CE1F2B5849D97AF5E5D65846290C ___styleStack;
	TMP_TextProcessingStack_1_t138EC06BE7F101AA0A3C8D2DC951E55AACE085E9 ___baselineStack;
	TMP_TextProcessingStack_1_tFBA719426D68CE1F2B5849D97AF5E5D65846290C ___actionStack;
	TMP_TextProcessingStack_1_tB03E08F69415B281A5A81138F09E49EE58402DF9 ___materialReferenceStack;
	TMP_TextProcessingStack_1_t243EA1B5D7FD2295D6533B953F0BBE8F52EFB8A0 ___lineJustificationStack;
	int32_t ___spriteAnimationID;
	TMP_FontAsset_t923BF2F78D7C5AC36376E168A1193B7CB4855160* ___currentFontAsset;
	TMP_SpriteAsset_t81F779E6F705CE190DC0D1F93A954CB8B1774B39* ___currentSpriteAsset;
	Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* ___currentMaterial;
	int32_t ___currentMaterialIndex;
	Extents_tA2D2F95811D0A18CB7AC3570D2D8F8CD3AF4C4A8 ___meshExtents;
	int32_t ___tagNoParsing;
	int32_t ___isNonBreakingSpace;
};
struct WordWrapState_t80F67D8CAA9B1A0A3D5266521E23A9F3100EDD0A_marshaled_com
{
	int32_t ___previous_WordBreak;
	int32_t ___total_CharacterCount;
	int32_t ___visible_CharacterCount;
	int32_t ___visible_SpriteCount;
	int32_t ___visible_LinkCount;
	int32_t ___firstCharacterIndex;
	int32_t ___firstVisibleCharacterIndex;
	int32_t ___lastCharacterIndex;
	int32_t ___lastVisibleCharIndex;
	int32_t ___lineNumber;
	float ___maxCapHeight;
	float ___maxAscender;
	float ___maxDescender;
	float ___startOfLineAscender;
	float ___maxLineAscender;
	float ___maxLineDescender;
	float ___pageAscender;
	int32_t ___horizontalAlignment;
	float ___marginLeft;
	float ___marginRight;
	float ___xAdvance;
	float ___preferredWidth;
	float ___preferredHeight;
	float ___previousLineScale;
	int32_t ___wordCount;
	int32_t ___fontStyle;
	int32_t ___italicAngle;
	float ___fontScaleMultiplier;
	float ___currentFontSize;
	float ___baselineOffset;
	float ___lineOffset;
	int32_t ___isDrivenLineSpacing;
	float ___glyphHorizontalAdvanceAdjustment;
	float ___cSpace;
	float ___mSpace;
	TMP_TextInfo_t09A8E906329422C3F0C059876801DD695B8D524D* ___textInfo;
	TMP_LineInfo_tB75C1965B58DB7B3A046C8CA55AD6AB92B6B17B3 ___lineInfo;
	Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B ___vertexColor;
	Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B ___underlineColor;
	Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B ___strikethroughColor;
	Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B ___highlightColor;
	TMP_FontStyleStack_t52885F172FADBC21346C835B5302167BDA8020DC ___basicStyleStack;
	TMP_TextProcessingStack_1_tFBA719426D68CE1F2B5849D97AF5E5D65846290C ___italicAngleStack;
	TMP_TextProcessingStack_1_tF2CD5BE59E5EB22EA9E3EE3043A004EA918C4BB3 ___colorStack;
	TMP_TextProcessingStack_1_tF2CD5BE59E5EB22EA9E3EE3043A004EA918C4BB3 ___underlineColorStack;
	TMP_TextProcessingStack_1_tF2CD5BE59E5EB22EA9E3EE3043A004EA918C4BB3 ___strikethroughColorStack;
	TMP_TextProcessingStack_1_tF2CD5BE59E5EB22EA9E3EE3043A004EA918C4BB3 ___highlightColorStack;
	TMP_TextProcessingStack_1_t57AECDCC936A7FF1D6CF66CA11560B28A675648D ___highlightStateStack;
	TMP_TextProcessingStack_1_tC8FAEB17246D3B171EFD11165A5761AE39B40D0C ___colorGradientStack;
	TMP_TextProcessingStack_1_t138EC06BE7F101AA0A3C8D2DC951E55AACE085E9 ___sizeStack;
	TMP_TextProcessingStack_1_t138EC06BE7F101AA0A3C8D2DC951E55AACE085E9 ___indentStack;
	TMP_TextProcessingStack_1_tA5C8CED87DD9E73F6359E23B334FFB5B6F813FD4 ___fontWeightStack;
	TMP_TextProcessingStack_1_tFBA719426D68CE1F2B5849D97AF5E5D65846290C ___styleStack;
	TMP_TextProcessingStack_1_t138EC06BE7F101AA0A3C8D2DC951E55AACE085E9 ___baselineStack;
	TMP_TextProcessingStack_1_tFBA719426D68CE1F2B5849D97AF5E5D65846290C ___actionStack;
	TMP_TextProcessingStack_1_tB03E08F69415B281A5A81138F09E49EE58402DF9 ___materialReferenceStack;
	TMP_TextProcessingStack_1_t243EA1B5D7FD2295D6533B953F0BBE8F52EFB8A0 ___lineJustificationStack;
	int32_t ___spriteAnimationID;
	TMP_FontAsset_t923BF2F78D7C5AC36376E168A1193B7CB4855160* ___currentFontAsset;
	TMP_SpriteAsset_t81F779E6F705CE190DC0D1F93A954CB8B1774B39* ___currentSpriteAsset;
	Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* ___currentMaterial;
	int32_t ___currentMaterialIndex;
	Extents_tA2D2F95811D0A18CB7AC3570D2D8F8CD3AF4C4A8 ___meshExtents;
	int32_t ___tagNoParsing;
	int32_t ___isNonBreakingSpace;
};
struct MotionParameters_t10035140520E621AB2F4681BE05799BE3F754DDC 
{
	float ___Duration;
	int32_t ___Ease;
	UnsafeAnimationCurve_tBD078B5CE0B9A8AACE46BD298C6FB2F7EBDC9E0C ___AnimationCurve;
	uint8_t ___TimeKind;
	float ___Delay;
	int32_t ___Loops;
	uint8_t ___DelayType;
	uint8_t ___LoopType;
};
struct FixedString512BytesPropertyAnimationComponent_1_t478DB1A1E260CF40B695F4D3603F5E706B027BDF  : public PropertyAnimationComponent_4_t6EC795E7B3C765371C852B50A1F3A17D7C9E170E
{
};
struct FixedString512BytesPropertyAnimationComponent_1_t12AAC49370780B9B738527218781305E8DD49E8E  : public PropertyAnimationComponent_4_tDEA5D4A766BC92512DB06B4207009265F9BBDDFD
{
};
struct MotionBuilderBuffer_2_t04E3EEA09283FAF1D9197F4BE4C39D9944C98311  : public RuntimeObject
{
	uint16_t ___Version;
	MotionBuilderBuffer_2_t04E3EEA09283FAF1D9197F4BE4C39D9944C98311* ___NextNode;
	FixedString512Bytes_t0C425B0F2C07FEA1642C32BF8559116DF2BFF50E ___StartValue;
	FixedString512Bytes_t0C425B0F2C07FEA1642C32BF8559116DF2BFF50E ___EndValue;
	StringOptions_tDDA84E855CD29F2BCD9BA102EA173B219CFCADA7 ___Options;
	float ___Duration;
	int32_t ___Ease;
	uint8_t ___TimeKind;
	float ___Delay;
	int32_t ___Loops;
	uint8_t ___DelayType;
	uint8_t ___LoopType;
	bool ___CancelOnError;
	bool ___SkipValuesDuringDelay;
	bool ___ImmediateBind;
	RuntimeObject* ___State0;
	RuntimeObject* ___State1;
	RuntimeObject* ___State2;
	uint8_t ___StateCount;
	RuntimeObject* ___UpdateAction;
	Action_1_tD69A6DC9FBE94131E52F5A73B2A9D4AB51EEC404* ___OnLoopCompleteAction;
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___OnCompleteAction;
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___OnCancelAction;
	AnimationCurve_tCBFFAAD05CEBB35EF8D8631BD99914BE1A6BB354* ___AnimationCurve;
	RuntimeObject* ___Scheduler;
};
struct MotionSettings_2_t366DDAE7CADD1434DF2C190978E1C0C0B14A2316  : public RuntimeObject
{
	FixedString512Bytes_t0C425B0F2C07FEA1642C32BF8559116DF2BFF50E ___startValue;
	FixedString512Bytes_t0C425B0F2C07FEA1642C32BF8559116DF2BFF50E ___endValue;
	float ___duration;
	StringOptions_tDDA84E855CD29F2BCD9BA102EA173B219CFCADA7 ___options;
	int32_t ___ease;
	AnimationCurve_tCBFFAAD05CEBB35EF8D8631BD99914BE1A6BB354* ___customEaseCurve;
	float ___delay;
	uint8_t ___delayType;
	int32_t ___loops;
	uint8_t ___loopType;
	bool ___cancelOnError;
	bool ___skipValuesDuringDelay;
	bool ___immediateBind;
	RuntimeObject* ___scheduler;
};
struct TMP_TextProcessingStack_1_t2DDA00FFC64AF6E3AFD475AB2086D16C34787E0F 
{
	WordWrapStateU5BU5D_t473D59C9DBCC949CE72EF1EB471CBA152A6CEAC9* ___itemStack;
	int32_t ___index;
	WordWrapState_t80F67D8CAA9B1A0A3D5266521E23A9F3100EDD0A ___m_DefaultItem;
	int32_t ___m_Capacity;
	int32_t ___m_RolloverSize;
	int32_t ___m_Count;
};
struct AudioBehaviour_t2DC0BEF7B020C952F3D2DA5AAAC88501C7EEB941  : public Behaviour_t01970CFBBA658497AE30F311C447DB0440BAB7FA
{
};
struct AudioSourcePitchAnimation_t62AB22027FB5C3AF778317F9B6E0CC0A1093D6F6  : public FloatPropertyAnimationComponent_1_t766705FAD368B247FAB08A4A977B8B3529AC1933
{
};
struct AudioSourceVolumeAnimation_tD2D1289A6EBA9DE03DBB4B2E11EADC73F7B4C1BF  : public FloatPropertyAnimationComponent_1_t766705FAD368B247FAB08A4A977B8B3529AC1933
{
};
struct Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184  : public Behaviour_t01970CFBBA658497AE30F311C447DB0440BAB7FA
{
};
struct CameraAspectAnimation_t7DB104395B9F6412E22D3ED76E390ECD41CF173A  : public FloatPropertyAnimationComponent_1_t37A61899F20322CCCFA7C09CD5B7271B76DCEC17
{
};
struct CameraBackgroundColorAnimation_tC9D3E651F449E23A3A476C80A1B13AFD549904C1  : public ColorPropertyAnimationComponent_1_tAD1067F24BBAF944E40270FCC4CBBB6A0F82380D
{
};
struct CameraFarClipPlaneAnimation_tBFB0D31C2A77E1DDBD510EF20D65A3B0094416CE  : public FloatPropertyAnimationComponent_1_t37A61899F20322CCCFA7C09CD5B7271B76DCEC17
{
};
struct CameraFieldOfViewAnimation_t8B4975929A9FC5EB1292E0D061E461C5C500457F  : public FloatPropertyAnimationComponent_1_t37A61899F20322CCCFA7C09CD5B7271B76DCEC17
{
};
struct CameraNearClipPlaneAnimation_t31C2AEFB468ED028ABF534C762CB31DF85DA903F  : public FloatPropertyAnimationComponent_1_t37A61899F20322CCCFA7C09CD5B7271B76DCEC17
{
};
struct CameraOrthographicSizeAnimation_tF07CAB3E4E205B2FEC72C19488BAB814B8165969  : public FloatPropertyAnimationComponent_1_t37A61899F20322CCCFA7C09CD5B7271B76DCEC17
{
};
struct CameraPixelRectAnimation_tFB2DC1B4364CE768560D1DB65D91C3148520EFEB  : public RectPropertyAnimationComponent_1_t4A7ABE05AD402BC5405540B43257C6CAD0704C6D
{
};
struct CameraRectAnimation_t27350DC7D603BA5AA49A5EEFC6FA9F8CDF070EF1  : public RectPropertyAnimationComponent_1_t4A7ABE05AD402BC5405540B43257C6CAD0704C6D
{
};
struct CanvasGroup_t048C1461B14628CFAEBE6E7353093ADB04EBC094  : public Behaviour_t01970CFBBA658497AE30F311C447DB0440BAB7FA
{
};
struct CanvasGroupAlphaAnimation_t117A030C75C58FCE864557262A0B9D6B6669D650  : public FloatPropertyAnimationComponent_1_t3EE1259037FE3CF6B6303A336C605A0F318CB5E6
{
};
struct GraphicColorAnimation_t42939D1590768EA413C629493D8BB748515A58DE  : public ColorPropertyAnimationComponent_1_t2DCF6BE5B232AEEC66DE5551BC91B57589438F4C
{
};
struct ImageColorAlphaAnimation_t4BB2FC2C18CA060FFAD5E1D245318DD67D61E7B2  : public FloatPropertyAnimationComponent_1_tBF445C4B23C073DFA04D5E88341C03BB479265B7
{
};
struct ImageColorAnimation_tFFB6FA38A3ECA911EA2FCFF5F09B09870F809C7F  : public ColorPropertyAnimationComponent_1_tE6AEF5F64546DD2C24C3A74C1AE6F63EF46DABD9
{
};
struct ImageFillAmountAnimation_t05DF0A6E067043EE01BB0A2879930F5D76047CBB  : public FloatPropertyAnimationComponent_1_tBF445C4B23C073DFA04D5E88341C03BB479265B7
{
};
struct MaterialColorAnimation_tB8E52253F7D8EBFB2B345D4524125FF9BE2233BE  : public ColorPropertyAnimationComponent_1_t1A004C26C26C52867C5A5F2113F7CE3672961051
{
	String_t* ___propertyName;
};
struct MaterialFloatAnimation_t33F07CC2F42829138320058153639C8993A2E7F4  : public FloatPropertyAnimationComponent_1_t4EDF64705C1358D105E46606D44590DA7EE60C40
{
	String_t* ___propertyName;
};
struct MaterialIntAnimation_t743758EB02F1C6889456973EEBF255FC2EEC0F55  : public IntPropertyAnimationComponent_1_tB4B79248A9306E45219CF94F18CB2856CA5E3B68
{
	String_t* ___propertyName;
};
struct MaterialVectorAnimation_t16760AE18B1441C66C97461AD42F886ECF16DA59  : public Vector4PropertyAnimationComponent_1_tD5781699C642AFAE6FA17B1A9EC214F1A3DBDB75
{
	String_t* ___propertyName;
};
struct MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71  : public Behaviour_t01970CFBBA658497AE30F311C447DB0440BAB7FA
{
	CancellationTokenSource_tAAE1E0033BCFC233801F8CB4CED5C852B350CB7B* ___m_CancellationTokenSource;
};
struct MotionData_tA820A6BD10DEA3326A5D51B503754F003CE7D399 
{
	MotionState_t92DCDE8373525507DED2EAF6EA7B9AB1F409C46D ___State;
	MotionParameters_t10035140520E621AB2F4681BE05799BE3F754DDC ___Parameters;
};
struct MotionData_tA820A6BD10DEA3326A5D51B503754F003CE7D399_marshaled_pinvoke
{
	MotionState_t92DCDE8373525507DED2EAF6EA7B9AB1F409C46D_marshaled_pinvoke ___State;
	MotionParameters_t10035140520E621AB2F4681BE05799BE3F754DDC ___Parameters;
};
struct MotionData_tA820A6BD10DEA3326A5D51B503754F003CE7D399_marshaled_com
{
	MotionState_t92DCDE8373525507DED2EAF6EA7B9AB1F409C46D_marshaled_com ___State;
	MotionParameters_t10035140520E621AB2F4681BE05799BE3F754DDC ___Parameters;
};
struct RectTransform_t6C5DA5E41A89E0F488B001E45E58963480E543A5  : public Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1
{
};
struct RectTransformPivotAnimation_t08B1166052D57CC0DBE2F80C97A226462107161E  : public Vector2PropertyAnimationComponent_1_t8A177D739E23FF44639E13B99726B0A6C932D365
{
};
struct RectTransformSizeDeltaAnimation_t0ADD7484D0932C20AD535D02E43A6D2A231D8AC3  : public Vector2PropertyAnimationComponent_1_t8A177D739E23FF44639E13B99726B0A6C932D365
{
};
struct Rigidbody2DPositionAnimation_t6384BF0D1B3C33236307B689CC87FEE9B1B1EC20  : public Rigidbody2DPositionAnimationBase_2_t69CE9CD2C9AB3634CDE70A4430336030629AE27A
{
};
struct Rigidbody2DPositionPunchAnimation_t4CE478F8B49D2CE9AECA0EF49DE21F695BC8AE2E  : public Rigidbody2DPositionAnimationBase_2_tEC44827A997686FCA2419EFAB9EBE6C21594FA08
{
};
struct Rigidbody2DPositionShakeAnimation_t83C832CD3C8323CF36DE5786F713CC66E190F2B5  : public Rigidbody2DPositionAnimationBase_2_tD610427108FEF8F8BE698CEB4A2CECD0661F7A50
{
};
struct Rigidbody2DRotationAnimation_t1D9950D3CD8D64136141526DA47B1DD6544CAA9E  : public Rigidbody2DRotationAnimationBase_2_t86890366317CD729D92AE6912188756B638532DB
{
};
struct Rigidbody2DRotationPunchAnimation_tF9792F42F38E6B7DBDF861BCA13DC95A15E25FF0  : public Rigidbody2DRotationAnimationBase_2_tE0B9D89B886A179723638F20CECDFA9455B1FFCE
{
};
struct Rigidbody2DRotationShakeAnimation_tFDF23B7A5FB3DDD00CB5060A5DCFC336B0965B8B  : public Rigidbody2DRotationAnimationBase_2_tFE21E96D35A59F82D112D56BB1CD4DACE31FF142
{
};
struct RigidbodyPositionAnimation_tD5E8DC790768D7450851E943B0375C358EDE3153  : public RigidbodyPositionAnimationBase_2_t3F8DF2590F9F24CFEC040865A4F75F1112C82612
{
};
struct RigidbodyPositionPunchAnimation_t1E1EF5E7F5D33CF09BBA49998CEEBBEE50409E64  : public RigidbodyPositionAnimationBase_2_t4B7F6634781E49079650FC961B633396250CCB64
{
};
struct RigidbodyPositionShakeAnimation_t835F38965062CA69323D55FD17352596B5D9E83F  : public RigidbodyPositionAnimationBase_2_tFF37C9F46D719B654E722FB0A986E443FEA95121
{
};
struct RigidbodyRotationAnimation_t78187C7A9D397C810CD2674EEA4C026C29A6383D  : public RigidbodyRotationAnimationBase_2_t66E15E7EA84710E8BE780C9D318AC50C679161CF
{
};
struct RigidbodyRotationPunchAnimation_tD4DA1E4686D1038ED68A9AA90A906FCD96A6F8FE  : public RigidbodyRotationAnimationBase_2_t4ED992FE889ACCD94A95BB34F67343C3D80FD125
{
};
struct RigidbodyRotationShakeAnimation_t84A7DABACAA48B32D9F74051B32E5371DD1E5861  : public RigidbodyRotationAnimationBase_2_t1817E2F258DFDA6844D7130B71D79AF68737E26E
{
};
struct SliderValueAnimation_t3DCA44DE2E0898CB1C626393BB657089FB9DAA8D  : public FloatPropertyAnimationComponent_1_t48E23230B8D939712EE3FA3062665415D1B00BAB
{
};
struct SpriteRenderer_t1DD7FE258F072E1FA87D6577BA27225892B8047B  : public Renderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF
{
	UnityEvent_1_t8ABE5544759145B8D7A09F1C54FFCB6907EDD56E* ___m_SpriteChangeEvent;
};
struct SpriteRendererColorAnimation_tB90E5AB6A31262E5049CFDA53172D0D3EEB8F766  : public ColorPropertyAnimationComponent_1_t2D91760F9E1E05B38E267F816DBC28A81B2F74AC
{
};
struct TMPTextCharacterSpacingAnimation_t26A3F5C492B9F1B71D70B24EACEC606FA72228BD  : public FloatPropertyAnimationComponent_1_t1AB552982C92303A2C6CC5876C59DC138C174E43
{
};
struct TMPTextColorAlphaAnimation_t25988B988761C4836D8EEC13DB51674914D70B98  : public FloatPropertyAnimationComponent_1_t1AB552982C92303A2C6CC5876C59DC138C174E43
{
};
struct TMPTextColorAnimation_t47399F6153BA6F7A67AD03F194FF931600B1DAC4  : public ColorPropertyAnimationComponent_1_t39834DD27CD732DFE4F633FFD9F2D6A086CCBB06
{
};
struct TMPTextFontSizeAnimation_t3B7B223B80DA067FF5B20970560EED95A74E0A06  : public FloatPropertyAnimationComponent_1_t1AB552982C92303A2C6CC5876C59DC138C174E43
{
};
struct TMPTextLineSpacingAnimation_t1BDC9F5B32C3685301B009A9BFF9CB76407BCDA3  : public FloatPropertyAnimationComponent_1_t1AB552982C92303A2C6CC5876C59DC138C174E43
{
};
struct TMPTextParagraphSpacingAnimation_t6FB4A8093F859DD882F9616613B76258C059AF93  : public FloatPropertyAnimationComponent_1_t1AB552982C92303A2C6CC5876C59DC138C174E43
{
};
struct TMPTextWordSpacingAnimation_tF2EA00511F45C3E0D5CBDD069E5E08A242CC59DE  : public FloatPropertyAnimationComponent_1_t1AB552982C92303A2C6CC5876C59DC138C174E43
{
};
struct TextColorAnimation_t6574309620A762556ADE0DAB9A913B1B76E0EF28  : public ColorPropertyAnimationComponent_1_tF608D7D00AEB026C1B6A6C045B2055450B730FA3
{
};
struct TextFontSizeAnimation_t39852601D9D17EA06325D177ABFCBC4BEEE8C066  : public IntPropertyAnimationComponent_1_t41237611C68CEA9B10C6849A5D5AE94883405EE5
{
};
struct TransformPositionAnimation_tA672FD35BC9E5A38D23FC09076A16FDBCD8AD162  : public TransformPositionAnimationBase_2_t12DE280ED40D363B87FDE5057F2A16BAE67FF802
{
};
struct TransformPositionPunchAnimation_t111BCFAE3984E761BF110D887515F103ACA6BEC7  : public TransformPositionAnimationBase_2_tE046D85AA8257B076AEA1EC06523CD68298DC058
{
};
struct TransformPositionShakeAnimation_t9BC6FA5232E8683B7CE5365D2953ADB875C4402A  : public TransformPositionAnimationBase_2_t4B424C50A5AC08FC89469AD266ABFD58DE673D3C
{
};
struct TransformRotationAnimation_t181A786C31B7C8AC2C83152CCC24C0C82EED1785  : public TransformRotationAnimationBase_2_tFFAE1B23BA584F4A5C424117F0D678258C5D31F2
{
};
struct TransformRotationPunchAnimation_tDD2CD304B79268D5D844374DF14C4322733E3A01  : public TransformRotationAnimationBase_2_t844162742B1631A9477AD8582685D20A7F4A8563
{
};
struct TransformRotationShakeAnimation_t1817882F0893684DF23BECBADBFD7E1458A69E1F  : public TransformRotationAnimationBase_2_t8558AFAC045C23114B034681003F2C2F07D02A8E
{
};
struct TransformScaleAnimation_tEEEC832BD42C59D1A08B30347D67DF8C4839E102  : public TransformScaleAnimationBase_2_tBE02F8DFD3B63F89AD312977DEA9C0F63D75C8CA
{
};
struct TransformScalePunchAnimation_t8C68CC300945C3F4CB4BCDA907D785A600080DDA  : public TransformScaleAnimationBase_2_t17D49FB722A1A6F77DC3B5D3EB244F546109A4B0
{
};
struct TransformScaleShakeAnimation_tCEDC563ACEF258A7C21F2FCBF03F28648A065DA3  : public TransformScaleAnimationBase_2_t35F2BE396D00623EE3AFF251C9245768B42F60CB
{
};
struct VolumeWeightAnimation_t748E9F2603B2964DAF687456ECE284BB9FD23CF7  : public FloatPropertyAnimationComponent_1_t230707BA48829ADA4918D773DC2E3DF24F0080C7
{
};
struct SerializableMotionSettings_2_tF48B33D24FDF5B9DD96E1CE305D788D9D67A704E  : public MotionSettings_2_t366DDAE7CADD1434DF2C190978E1C0C0B14A2316
{
	uint8_t ___schedulerType;
};
struct AudioSource_t871AC2272F896738252F04EE949AEF5B241D3299  : public AudioBehaviour_t2DC0BEF7B020C952F3D2DA5AAAC88501C7EEB941
{
};
struct LitMotionAnimation_t92F1AF7CF4859CEDD166DF7FD8091FDB2C34AF4A  : public MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71
{
	bool ___playOnAwake;
	int32_t ___animationMode;
	LitMotionAnimationComponentU5BU5D_tA8718977F5DABB54F0422858CC77746CF7E85897* ___components;
	Queue_1_t7CF7AD96EFA79CC0D603DFB407D7945D8F635C5B* ___queue;
	FastListCore_1_tC5AF8169955F580A98B5AF92CC49BA523978EC21 ___playingComponents;
};
struct TMPTextAnimation_t2E906E9232E91180CEC49B9D9C8EF37EE8908AF2  : public FixedString512BytesPropertyAnimationComponent_1_t478DB1A1E260CF40B695F4D3603F5E706B027BDF
{
};
struct TextAnimation_t9777A34576E8E54B799AA1238A3B2D52E559A263  : public FixedString512BytesPropertyAnimationComponent_1_t12AAC49370780B9B738527218781305E8DD49E8E
{
};
struct UIBehaviour_tB9D4295827BD2EEDEF0749200C6CA7090C742A9D  : public MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71
{
};
struct Volume_t7CAAEA22D7F13A50FAE114DE7A6986FEAC837377  : public MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71
{
	bool ___m_IsGlobal;
	float ___priority;
	float ___blendDistance;
	float ___weight;
	VolumeProfile_t9B5F2005F575A710F38A124EF81A6228CCACACE1* ___sharedProfile;
	List_1_t58F89DEDCD7DABB0CFB009AAD9C0CFE061592252* ___m_Colliders;
	int32_t ___m_PreviousLayer;
	float ___m_PreviousPriority;
	VolumeProfile_t9B5F2005F575A710F38A124EF81A6228CCACACE1* ___m_InternalProfile;
};
struct Graphic_tCBFCA4585A19E2B75465AECFEAC43F4016BF7931  : public UIBehaviour_tB9D4295827BD2EEDEF0749200C6CA7090C742A9D
{
	Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* ___m_Material;
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___m_Color;
	bool ___m_SkipLayoutUpdate;
	bool ___m_SkipMaterialUpdate;
	bool ___m_RaycastTarget;
	bool ___m_RaycastTargetCache;
	Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 ___m_RaycastPadding;
	RectTransform_t6C5DA5E41A89E0F488B001E45E58963480E543A5* ___m_RectTransform;
	CanvasRenderer_tAB9A55A976C4E3B2B37D0CE5616E5685A8B43860* ___m_CanvasRenderer;
	Canvas_t2DB4CEFDFF732884866C83F11ABF75F5AE8FFB26* ___m_Canvas;
	bool ___m_VertsDirty;
	bool ___m_MaterialDirty;
	UnityAction_t11A1F3B953B365C072A5DCC32677EE1796A962A7* ___m_OnDirtyLayoutCallback;
	UnityAction_t11A1F3B953B365C072A5DCC32677EE1796A962A7* ___m_OnDirtyVertsCallback;
	UnityAction_t11A1F3B953B365C072A5DCC32677EE1796A962A7* ___m_OnDirtyMaterialCallback;
	Mesh_t6D9C539763A09BC2B12AEAEF36F6DFFC98AE63D4* ___m_CachedMesh;
	Vector2U5BU5D_tFEBBC94BCC6C9C88277BA04047D2B3FDB6ED7FDA* ___m_CachedUvs;
	TweenRunner_1_t5BB0582F926E75E2FE795492679A6CF55A4B4BC4* ___m_ColorTweenRunner;
	bool ___U3CuseLegacyMeshGenerationU3Ek__BackingField;
};
struct Selectable_t3251808068A17B8E92FB33590A4C2FA66D456712  : public UIBehaviour_tB9D4295827BD2EEDEF0749200C6CA7090C742A9D
{
	bool ___m_EnableCalled;
	Navigation_t4D2E201D65749CF4E104E8AC1232CF1D6F14795C ___m_Navigation;
	int32_t ___m_Transition;
	ColorBlock_tDD7C62E7AFE442652FC98F8D058CE8AE6BFD7C11 ___m_Colors;
	SpriteState_tC8199570BE6337FB5C49347C97892B4222E5AACD ___m_SpriteState;
	AnimationTriggers_tA0DC06F89C5280C6DD972F6F4C8A56D7F4F79074* ___m_AnimationTriggers;
	bool ___m_Interactable;
	Graphic_tCBFCA4585A19E2B75465AECFEAC43F4016BF7931* ___m_TargetGraphic;
	bool ___m_GroupsAllowInteraction;
	int32_t ___m_CurrentIndex;
	bool ___U3CisPointerInsideU3Ek__BackingField;
	bool ___U3CisPointerDownU3Ek__BackingField;
	bool ___U3ChasSelectionU3Ek__BackingField;
	List_1_t2CDCA768E7F493F5EDEBC75AEB200FD621354E35* ___m_CanvasGroupCache;
};
struct MaskableGraphic_tFC5B6BE351C90DE53744DF2A70940242774B361E  : public Graphic_tCBFCA4585A19E2B75465AECFEAC43F4016BF7931
{
	bool ___m_ShouldRecalculateStencil;
	Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* ___m_MaskMaterial;
	RectMask2D_tACF92BE999C791A665BD1ADEABF5BCEB82846670* ___m_ParentMask;
	bool ___m_Maskable;
	bool ___m_IsMaskingGraphic;
	bool ___m_IncludeForMasking;
	CullStateChangedEvent_t6073CD0D951EC1256BF74B8F9107D68FC89B99B8* ___m_OnCullStateChanged;
	bool ___m_ShouldRecalculate;
	int32_t ___m_StencilValue;
	Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* ___m_Corners;
};
struct Slider_t87EA570E3D6556CABF57456C2F3873FFD86E652F  : public Selectable_t3251808068A17B8E92FB33590A4C2FA66D456712
{
	RectTransform_t6C5DA5E41A89E0F488B001E45E58963480E543A5* ___m_FillRect;
	RectTransform_t6C5DA5E41A89E0F488B001E45E58963480E543A5* ___m_HandleRect;
	int32_t ___m_Direction;
	float ___m_MinValue;
	float ___m_MaxValue;
	bool ___m_WholeNumbers;
	float ___m_Value;
	SliderEvent_t92A82EF6C62E15AF92B640FE2D960E877E8C6555* ___m_OnValueChanged;
	Image_tBC1D03F63BF71132E9A5E472B8742F172A011E7E* ___m_FillImage;
	Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* ___m_FillTransform;
	RectTransform_t6C5DA5E41A89E0F488B001E45E58963480E543A5* ___m_FillContainerRect;
	Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* ___m_HandleTransform;
	RectTransform_t6C5DA5E41A89E0F488B001E45E58963480E543A5* ___m_HandleContainerRect;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___m_Offset;
	DrivenRectTransformTracker_tFB0706C933E3C68E4F377C204FCEEF091F1EE0B1 ___m_Tracker;
	bool ___m_DelayedUpdateVisuals;
};
struct Image_tBC1D03F63BF71132E9A5E472B8742F172A011E7E  : public MaskableGraphic_tFC5B6BE351C90DE53744DF2A70940242774B361E
{
	Sprite_tAFF74BC83CD68037494CB0B4F28CBDF8971CAB99* ___m_Sprite;
	Sprite_tAFF74BC83CD68037494CB0B4F28CBDF8971CAB99* ___m_OverrideSprite;
	int32_t ___m_Type;
	bool ___m_PreserveAspect;
	bool ___m_FillCenter;
	int32_t ___m_FillMethod;
	float ___m_FillAmount;
	bool ___m_FillClockwise;
	int32_t ___m_FillOrigin;
	float ___m_AlphaHitTestMinimumThreshold;
	bool ___m_Tracked;
	bool ___m_UseSpriteMesh;
	float ___m_PixelsPerUnitMultiplier;
	float ___m_CachedReferencePixelsPerUnit;
};
struct TMP_Text_tE8D677872D43AD4B2AAF0D6101692A17D0B251A9  : public MaskableGraphic_tFC5B6BE351C90DE53744DF2A70940242774B361E
{
	String_t* ___m_text;
	bool ___m_IsTextBackingStringDirty;
	RuntimeObject* ___m_TextPreprocessor;
	bool ___m_isRightToLeft;
	TMP_FontAsset_t923BF2F78D7C5AC36376E168A1193B7CB4855160* ___m_fontAsset;
	TMP_FontAsset_t923BF2F78D7C5AC36376E168A1193B7CB4855160* ___m_currentFontAsset;
	bool ___m_isSDFShader;
	Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* ___m_sharedMaterial;
	Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* ___m_currentMaterial;
	int32_t ___m_currentMaterialIndex;
	MaterialU5BU5D_t2B1D11C42DB07A4400C0535F92DBB87A2E346D3D* ___m_fontSharedMaterials;
	Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* ___m_fontMaterial;
	MaterialU5BU5D_t2B1D11C42DB07A4400C0535F92DBB87A2E346D3D* ___m_fontMaterials;
	bool ___m_isMaterialDirty;
	Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B ___m_fontColor32;
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___m_fontColor;
	Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B ___m_underlineColor;
	Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B ___m_strikethroughColor;
	bool ___m_enableVertexGradient;
	int32_t ___m_colorMode;
	VertexGradient_t2C057B53C0EA6E987C2B7BAB0305E686DA1C9A8F ___m_fontColorGradient;
	TMP_ColorGradient_t17B51752B4E9499A1FF7D875DCEC1D15A0F4AEBB* ___m_fontColorGradientPreset;
	TMP_SpriteAsset_t81F779E6F705CE190DC0D1F93A954CB8B1774B39* ___m_spriteAsset;
	bool ___m_tintAllSprites;
	bool ___m_tintSprite;
	Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B ___m_spriteColor;
	TMP_StyleSheet_t70C71699F5CB2D855C361DBB78A44C901236C859* ___m_StyleSheet;
	TMP_Style_tA9E5B1B35EBFE24EF980CEA03251B638282E120C* ___m_TextStyle;
	int32_t ___m_TextStyleHashCode;
	bool ___m_overrideHtmlColors;
	Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B ___m_faceColor;
	Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B ___m_outlineColor;
	float ___m_outlineWidth;
	float ___m_fontSize;
	float ___m_currentFontSize;
	float ___m_fontSizeBase;
	TMP_TextProcessingStack_1_t138EC06BE7F101AA0A3C8D2DC951E55AACE085E9 ___m_sizeStack;
	int32_t ___m_fontWeight;
	int32_t ___m_FontWeightInternal;
	TMP_TextProcessingStack_1_tA5C8CED87DD9E73F6359E23B334FFB5B6F813FD4 ___m_FontWeightStack;
	bool ___m_enableAutoSizing;
	float ___m_maxFontSize;
	float ___m_minFontSize;
	int32_t ___m_AutoSizeIterationCount;
	int32_t ___m_AutoSizeMaxIterationCount;
	bool ___m_IsAutoSizePointSizeSet;
	float ___m_fontSizeMin;
	float ___m_fontSizeMax;
	int32_t ___m_fontStyle;
	int32_t ___m_FontStyleInternal;
	TMP_FontStyleStack_t52885F172FADBC21346C835B5302167BDA8020DC ___m_fontStyleStack;
	bool ___m_isUsingBold;
	int32_t ___m_HorizontalAlignment;
	int32_t ___m_VerticalAlignment;
	int32_t ___m_textAlignment;
	int32_t ___m_lineJustification;
	TMP_TextProcessingStack_1_t243EA1B5D7FD2295D6533B953F0BBE8F52EFB8A0 ___m_lineJustificationStack;
	Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* ___m_textContainerLocalCorners;
	float ___m_characterSpacing;
	float ___m_cSpacing;
	float ___m_monoSpacing;
	float ___m_wordSpacing;
	float ___m_lineSpacing;
	float ___m_lineSpacingDelta;
	float ___m_lineHeight;
	bool ___m_IsDrivenLineSpacing;
	float ___m_lineSpacingMax;
	float ___m_paragraphSpacing;
	float ___m_charWidthMaxAdj;
	float ___m_charWidthAdjDelta;
	bool ___m_enableWordWrapping;
	bool ___m_isCharacterWrappingEnabled;
	bool ___m_isNonBreakingSpace;
	bool ___m_isIgnoringAlignment;
	float ___m_wordWrappingRatios;
	int32_t ___m_overflowMode;
	int32_t ___m_firstOverflowCharacterIndex;
	TMP_Text_tE8D677872D43AD4B2AAF0D6101692A17D0B251A9* ___m_linkedTextComponent;
	TMP_Text_tE8D677872D43AD4B2AAF0D6101692A17D0B251A9* ___parentLinkedComponent;
	bool ___m_isTextTruncated;
	bool ___m_enableKerning;
	float ___m_GlyphHorizontalAdvanceAdjustment;
	bool ___m_enableExtraPadding;
	bool ___checkPaddingRequired;
	bool ___m_isRichText;
	bool ___m_parseCtrlCharacters;
	bool ___m_isOverlay;
	bool ___m_isOrthographic;
	bool ___m_isCullingEnabled;
	bool ___m_isMaskingEnabled;
	bool ___isMaskUpdateRequired;
	bool ___m_ignoreCulling;
	int32_t ___m_horizontalMapping;
	int32_t ___m_verticalMapping;
	float ___m_uvLineOffset;
	int32_t ___m_renderMode;
	int32_t ___m_geometrySortingOrder;
	bool ___m_IsTextObjectScaleStatic;
	bool ___m_VertexBufferAutoSizeReduction;
	int32_t ___m_firstVisibleCharacter;
	int32_t ___m_maxVisibleCharacters;
	int32_t ___m_maxVisibleWords;
	int32_t ___m_maxVisibleLines;
	bool ___m_useMaxVisibleDescender;
	int32_t ___m_pageToDisplay;
	bool ___m_isNewPage;
	Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 ___m_margin;
	float ___m_marginLeft;
	float ___m_marginRight;
	float ___m_marginWidth;
	float ___m_marginHeight;
	float ___m_width;
	TMP_TextInfo_t09A8E906329422C3F0C059876801DD695B8D524D* ___m_textInfo;
	bool ___m_havePropertiesChanged;
	bool ___m_isUsingLegacyAnimationComponent;
	Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* ___m_transform;
	RectTransform_t6C5DA5E41A89E0F488B001E45E58963480E543A5* ___m_rectTransform;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___m_PreviousRectTransformSize;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___m_PreviousPivotPosition;
	bool ___U3CautoSizeTextContainerU3Ek__BackingField;
	bool ___m_autoSizeTextContainer;
	Mesh_t6D9C539763A09BC2B12AEAEF36F6DFFC98AE63D4* ___m_mesh;
	bool ___m_isVolumetricText;
	Action_1_tB93AB717F9D419A1BEC832FF76E74EAA32184CC1* ___OnPreRenderText;
	TMP_SpriteAnimator_t2E0F016A61CA343E3222FF51E7CF0E53F9F256E4* ___m_spriteAnimator;
	float ___m_flexibleHeight;
	float ___m_flexibleWidth;
	float ___m_minWidth;
	float ___m_minHeight;
	float ___m_maxWidth;
	float ___m_maxHeight;
	LayoutElement_tB1F24CC11AF4AA87015C8D8EE06D22349C5BF40A* ___m_LayoutElement;
	float ___m_preferredWidth;
	float ___m_renderedWidth;
	bool ___m_isPreferredWidthDirty;
	float ___m_preferredHeight;
	float ___m_renderedHeight;
	bool ___m_isPreferredHeightDirty;
	bool ___m_isCalculatingPreferredValues;
	int32_t ___m_layoutPriority;
	bool ___m_isLayoutDirty;
	bool ___m_isAwake;
	bool ___m_isWaitingOnResourceLoad;
	int32_t ___m_inputSource;
	float ___m_fontScaleMultiplier;
	float ___tag_LineIndent;
	float ___tag_Indent;
	TMP_TextProcessingStack_1_t138EC06BE7F101AA0A3C8D2DC951E55AACE085E9 ___m_indentStack;
	bool ___tag_NoParsing;
	bool ___m_isParsingText;
	Matrix4x4_tDB70CF134A14BA38190C59AA700BCE10E2AED3E6 ___m_FXMatrix;
	bool ___m_isFXMatrixSet;
	UnicodeCharU5BU5D_t67F27D09F8EB28D2C42DFF16FE60054F157012F5* ___m_TextProcessingArray;
	int32_t ___m_InternalTextProcessingArraySize;
	TMP_CharacterInfoU5BU5D_t297D56FCF66DAA99D8FEA7C30F9F3926902C5B99* ___m_internalCharacterInfo;
	int32_t ___m_totalCharacterCount;
	int32_t ___m_characterCount;
	int32_t ___m_firstCharacterOfLine;
	int32_t ___m_firstVisibleCharacterOfLine;
	int32_t ___m_lastCharacterOfLine;
	int32_t ___m_lastVisibleCharacterOfLine;
	int32_t ___m_lineNumber;
	int32_t ___m_lineVisibleCharacterCount;
	int32_t ___m_pageNumber;
	float ___m_PageAscender;
	float ___m_maxTextAscender;
	float ___m_maxCapHeight;
	float ___m_ElementAscender;
	float ___m_ElementDescender;
	float ___m_maxLineAscender;
	float ___m_maxLineDescender;
	float ___m_startOfLineAscender;
	float ___m_startOfLineDescender;
	float ___m_lineOffset;
	Extents_tA2D2F95811D0A18CB7AC3570D2D8F8CD3AF4C4A8 ___m_meshExtents;
	Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B ___m_htmlColor;
	TMP_TextProcessingStack_1_tF2CD5BE59E5EB22EA9E3EE3043A004EA918C4BB3 ___m_colorStack;
	TMP_TextProcessingStack_1_tF2CD5BE59E5EB22EA9E3EE3043A004EA918C4BB3 ___m_underlineColorStack;
	TMP_TextProcessingStack_1_tF2CD5BE59E5EB22EA9E3EE3043A004EA918C4BB3 ___m_strikethroughColorStack;
	TMP_TextProcessingStack_1_t57AECDCC936A7FF1D6CF66CA11560B28A675648D ___m_HighlightStateStack;
	TMP_ColorGradient_t17B51752B4E9499A1FF7D875DCEC1D15A0F4AEBB* ___m_colorGradientPreset;
	TMP_TextProcessingStack_1_tC8FAEB17246D3B171EFD11165A5761AE39B40D0C ___m_colorGradientStack;
	bool ___m_colorGradientPresetIsTinted;
	float ___m_tabSpacing;
	float ___m_spacing;
	TMP_TextProcessingStack_1U5BU5D_t08293E0BB072311BB96170F351D1083BCA97B9B2* ___m_TextStyleStacks;
	int32_t ___m_TextStyleStackDepth;
	TMP_TextProcessingStack_1_tFBA719426D68CE1F2B5849D97AF5E5D65846290C ___m_ItalicAngleStack;
	int32_t ___m_ItalicAngle;
	TMP_TextProcessingStack_1_tFBA719426D68CE1F2B5849D97AF5E5D65846290C ___m_actionStack;
	float ___m_padding;
	float ___m_baselineOffset;
	TMP_TextProcessingStack_1_t138EC06BE7F101AA0A3C8D2DC951E55AACE085E9 ___m_baselineOffsetStack;
	float ___m_xAdvance;
	int32_t ___m_textElementType;
	TMP_TextElement_t262A55214F712D4274485ABE5676E5254B84D0A5* ___m_cached_TextElement;
	SpecialCharacter_t6C1DBE8C490706D1620899BAB7F0B8091AD26777 ___m_Ellipsis;
	SpecialCharacter_t6C1DBE8C490706D1620899BAB7F0B8091AD26777 ___m_Underline;
	TMP_SpriteAsset_t81F779E6F705CE190DC0D1F93A954CB8B1774B39* ___m_defaultSpriteAsset;
	TMP_SpriteAsset_t81F779E6F705CE190DC0D1F93A954CB8B1774B39* ___m_currentSpriteAsset;
	int32_t ___m_spriteCount;
	int32_t ___m_spriteIndex;
	int32_t ___m_spriteAnimationID;
	bool ___m_ignoreActiveState;
	TextBackingContainer_t33D1CE628E7B26C45EDAC1D87BEF2DD22A5C6361 ___m_TextBackingArray;
	DecimalU5BU5D_t93BA0C88FA80728F73B792EE1A5199D0C060B615* ___k_Power;
};
struct Text_tD60B2346DAA6666BF0D822FF607F0B220C2B9E62  : public MaskableGraphic_tFC5B6BE351C90DE53744DF2A70940242774B361E
{
	FontData_tB8E562846C6CB59C43260F69AE346B9BF3157224* ___m_FontData;
	String_t* ___m_Text;
	TextGenerator_t85D00417640A53953556C01F9D4E7DDE1ABD8FEC* ___m_TextCache;
	TextGenerator_t85D00417640A53953556C01F9D4E7DDE1ABD8FEC* ___m_TextCacheForLayout;
	bool ___m_DisableFontTextureRebuiltCallback;
	UIVertexU5BU5D_tBC532486B45D071A520751A90E819C77BA4E3D2F* ___m_TempVerts;
};
struct U3CPrivateImplementationDetailsU3E_t6ECB8E8EED910A1EDD673CA4D1B8D1200B474B0B_StaticFields
{
	__StaticArrayInitTypeSizeU3D1405_t68DB5F2E62461C67E9A44D9BCF56CBC3FB1C121B ___350A400EB8CF293100456CE6B2B54169AC1689C33C50319718AAC01B4391F6B3;
	__StaticArrayInitTypeSizeU3D5393_t3A32A024DBF4CF4577A1D9D09291901427880195 ___4ACE9CA5EB9773B32830743CC01A3AFEDC23ADF9337FCE1CE0248C872EB42017;
};
struct MotionDebugger_t65073AE52251C2BB7A2D83C38CFE9FB805538D40_StaticFields
{
	bool ___Enabled;
	bool ___EnableStackTrace;
	List_1_tA25D79C6E8BB82D03B97AB0F2F2EA71EE31D2AF3* ___trackings;
};
struct MotionManager_t55CCB0A78FD15BD0E274607AC5A3F35C3115763F_StaticFields
{
	FastListCore_1_t7622FA94EE4948D93B3B798B45AAE6DFAAC94519 ___list;
	int32_t ___U3CMotionTypeCountU3Ek__BackingField;
};
struct MotionScheduler_t98622FCF54DC19E35E9CB686D1D56DC64EE07AE5_StaticFields
{
	RuntimeObject* ___U3CDefaultSchedulerU3Ek__BackingField;
	RuntimeObject* ___Initialization;
	RuntimeObject* ___InitializationIgnoreTimeScale;
	RuntimeObject* ___InitializationRealtime;
	RuntimeObject* ___EarlyUpdate;
	RuntimeObject* ___EarlyUpdateIgnoreTimeScale;
	RuntimeObject* ___EarlyUpdateRealtime;
	RuntimeObject* ___FixedUpdate;
	RuntimeObject* ___PreUpdate;
	RuntimeObject* ___PreUpdateIgnoreTimeScale;
	RuntimeObject* ___PreUpdateRealtime;
	RuntimeObject* ___Update;
	RuntimeObject* ___UpdateIgnoreTimeScale;
	RuntimeObject* ___UpdateRealtime;
	RuntimeObject* ___PreLateUpdate;
	RuntimeObject* ___PreLateUpdateIgnoreTimeScale;
	RuntimeObject* ___PreLateUpdateRealtime;
	RuntimeObject* ___PostLateUpdate;
	RuntimeObject* ___PostLateUpdateIgnoreTimeScale;
	RuntimeObject* ___PostLateUpdateRealtime;
	RuntimeObject* ___TimeUpdate;
	RuntimeObject* ___TimeUpdateIgnoreTimeScale;
	RuntimeObject* ___TimeUpdateRealtime;
};
struct String_t_StaticFields
{
	String_t* ___Empty;
};
struct U3CU3Ec_tDAFA5C4E997B098FAFADB5DBAD4676795BD4F04C_StaticFields
{
	U3CU3Ec_tDAFA5C4E997B098FAFADB5DBAD4676795BD4F04C* ___U3CU3E9;
	Action_2_t02F23795BC96180858055AB64D80A7006668D4D6* ___U3CU3E9__2_0;
};
struct FastListCore_1_t7622FA94EE4948D93B3B798B45AAE6DFAAC94519_StaticFields
{
	FastListCore_1_t7622FA94EE4948D93B3B798B45AAE6DFAAC94519 ___Empty;
};
struct FastListCore_1_tC5AF8169955F580A98B5AF92CC49BA523978EC21_StaticFields
{
	FastListCore_1_tC5AF8169955F580A98B5AF92CC49BA523978EC21 ___Empty;
};
struct FastListCore_1_tD253A2B097165863483F30B720AE3EDC9C7FB508_StaticFields
{
	FastListCore_1_tD253A2B097165863483F30B720AE3EDC9C7FB508 ___Empty;
};
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_StaticFields
{
	String_t* ___TrueString;
	String_t* ___FalseString;
};
struct IntPtr_t_StaticFields
{
	intptr_t ___Zero;
};
struct MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375_StaticFields
{
	MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 ___None;
};
struct Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7_StaticFields
{
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___zeroVector;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___oneVector;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___upVector;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___downVector;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___leftVector;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___rightVector;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___positiveInfinityVector;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___negativeInfinityVector;
};
struct Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3_StaticFields
{
	Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 ___zeroVector;
	Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 ___oneVector;
	Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 ___positiveInfinityVector;
	Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 ___negativeInfinityVector;
};
struct Exception_t_StaticFields
{
	RuntimeObject* ___s_EDILock;
};
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_StaticFields
{
	int32_t ___OffsetOfInstanceIDInCPlusPlusObject;
};
struct MotionBuilderBuffer_2_t190528172F72DD395324243FA145EE61169E32DD_StaticFields
{
	MotionBuilderBuffer_2_t190528172F72DD395324243FA145EE61169E32DD* ___PoolRoot;
};
struct Type_t_StaticFields
{
	Binder_t91BFCE95A7057FADF4D8A1A342AFE52872246235* ___s_defaultBinder;
	Il2CppChar ___Delimiter;
	TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* ___EmptyTypes;
	RuntimeObject* ___Missing;
	MemberFilter_tF644F1AE82F611B677CE1964D5A3277DDA21D553* ___FilterAttribute;
	MemberFilter_tF644F1AE82F611B677CE1964D5A3277DDA21D553* ___FilterName;
	MemberFilter_tF644F1AE82F611B677CE1964D5A3277DDA21D553* ___FilterNameIgnoreCase;
};
struct MotionBuilderBuffer_2_t04E3EEA09283FAF1D9197F4BE4C39D9944C98311_StaticFields
{
	MotionBuilderBuffer_2_t04E3EEA09283FAF1D9197F4BE4C39D9944C98311* ___PoolRoot;
};
struct Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184_StaticFields
{
	CameraCallback_t844E527BFE37BC0495E7F67993E43C07642DA9DD* ___onPreCull;
	CameraCallback_t844E527BFE37BC0495E7F67993E43C07642DA9DD* ___onPreRender;
	CameraCallback_t844E527BFE37BC0495E7F67993E43C07642DA9DD* ___onPostRender;
};
struct RectTransform_t6C5DA5E41A89E0F488B001E45E58963480E543A5_StaticFields
{
	ReapplyDrivenProperties_t3482EA130A01FF7EE2EEFE37F66A5215D08CFE24* ___reapplyDrivenProperties;
};
struct Graphic_tCBFCA4585A19E2B75465AECFEAC43F4016BF7931_StaticFields
{
	Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* ___s_DefaultUI;
	Texture2D_tE6505BC111DD8A424A9DBE8E05D7D09E11FFFCF4* ___s_WhiteTexture;
	Mesh_t6D9C539763A09BC2B12AEAEF36F6DFFC98AE63D4* ___s_Mesh;
	VertexHelper_tB905FCB02AE67CBEE5F265FE37A5938FC5D136FE* ___s_VertexHelper;
};
struct Image_tBC1D03F63BF71132E9A5E472B8742F172A011E7E_StaticFields
{
	Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* ___s_ETC1DefaultUI;
	Vector2U5BU5D_tFEBBC94BCC6C9C88277BA04047D2B3FDB6ED7FDA* ___s_VertScratch;
	Vector2U5BU5D_tFEBBC94BCC6C9C88277BA04047D2B3FDB6ED7FDA* ___s_UVScratch;
	Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* ___s_Xy;
	Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* ___s_Uv;
	List_1_tE6BB71ABF15905EFA2BE92C38A2716547AEADB19* ___m_TrackedTexturelessImages;
	bool ___s_Initialized;
};
struct TMP_Text_tE8D677872D43AD4B2AAF0D6101692A17D0B251A9_StaticFields
{
	MaterialReferenceU5BU5D_t7491D335AB3E3E13CE9C0F5E931F396F6A02E1F2* ___m_materialReferences;
	Dictionary_2_tABE19B9C5C52F1DE14F0D3287B2696E7D7419180* ___m_materialReferenceIndexLookup;
	TMP_TextProcessingStack_1_tB03E08F69415B281A5A81138F09E49EE58402DF9 ___m_materialReferenceStack;
	Color32_t73C5004937BF5BB8AD55323D51AAA40A898EF48B ___s_colorWhite;
	Func_3_tC721DF8CDD07ED66A4833A19A2ED2302608C906C* ___OnFontAssetRequest;
	Func_3_t6F6D9932638EA1A5A45303C6626C818C25D164E5* ___OnSpriteAssetRequest;
	CharU5BU5D_t799905CF001DD5F13F7DBB310181FC4D8B7D0AAB* ___m_htmlTag;
	RichTextTagAttributeU5BU5D_t5816316EFD8F59DBC30B9F88E15828C564E47B6D* ___m_xmlAttribute;
	SingleU5BU5D_t89DEFE97BCEDB5857010E79ECE0F52CF6E93B87C* ___m_attributeParameterValues;
	WordWrapState_t80F67D8CAA9B1A0A3D5266521E23A9F3100EDD0A ___m_SavedWordWrapState;
	WordWrapState_t80F67D8CAA9B1A0A3D5266521E23A9F3100EDD0A ___m_SavedLineState;
	WordWrapState_t80F67D8CAA9B1A0A3D5266521E23A9F3100EDD0A ___m_SavedEllipsisState;
	WordWrapState_t80F67D8CAA9B1A0A3D5266521E23A9F3100EDD0A ___m_SavedLastValidState;
	WordWrapState_t80F67D8CAA9B1A0A3D5266521E23A9F3100EDD0A ___m_SavedSoftLineBreakState;
	TMP_TextProcessingStack_1_t2DDA00FFC64AF6E3AFD475AB2086D16C34787E0F ___m_EllipsisInsertionCandidateStack;
	ProfilerMarker_tA256E18DA86EDBC5528CE066FC91C96EE86501AD ___k_ParseTextMarker;
	ProfilerMarker_tA256E18DA86EDBC5528CE066FC91C96EE86501AD ___k_InsertNewLineMarker;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___k_LargePositiveVector2;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___k_LargeNegativeVector2;
	float ___k_LargePositiveFloat;
	float ___k_LargeNegativeFloat;
	int32_t ___k_LargePositiveInt;
	int32_t ___k_LargeNegativeInt;
};
struct Text_tD60B2346DAA6666BF0D822FF607F0B220C2B9E62_StaticFields
{
	Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* ___s_DefaultText;
};
#ifdef __clang__
#pragma clang diagnostic pop
#endif
struct ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031  : public RuntimeArray
{
	ALIGN_FIELD (8) uint8_t m_Items[1];

	inline uint8_t GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline uint8_t* GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, uint8_t value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
	}
	inline uint8_t GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline uint8_t* GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, uint8_t value)
	{
		m_Items[index] = value;
	}
};
struct LitMotionAnimationComponentU5BU5D_tA8718977F5DABB54F0422858CC77746CF7E85897  : public RuntimeArray
{
	ALIGN_FIELD (8) LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A* m_Items[1];

	inline LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A* GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A** GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A* value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
	inline LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A* GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A** GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A* value)
	{
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
};
struct ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918  : public RuntimeArray
{
	ALIGN_FIELD (8) RuntimeObject* m_Items[1];

	inline RuntimeObject* GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline RuntimeObject** GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, RuntimeObject* value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
	inline RuntimeObject* GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline RuntimeObject** GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, RuntimeObject* value)
	{
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
};


IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Vector2PropertyAnimationComponent_1__ctor_m674BDB539EAA5121E07705010B7D7EA0BEDFBFB8_gshared (Vector2PropertyAnimationComponent_1_t98697C175F4F833C0E6C686B39A0405631280CEA* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Queue_1_TryDequeue_m13AAD6552BBFDBE843C324A37375B35618569981_gshared (Queue_1_tE9EF546915795972C3BFD68FBB8FA859D3BAF3B5* __this, RuntimeObject** ___0_result, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void FastListCore_1_Add_mD6343E7F9F9D7AFEF75F492FBDC69F7F184CABDA_gshared_inline (FastListCore_1_tD253A2B097165863483F30B720AE3EDC9C7FB508* __this, RuntimeObject* ___0_element, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Span_1_t3F436092261253E8F2AF9867CA253C3B370766C2 FastListCore_1_AsSpan_mC8464DA5C76668324D0AE57C05BDEF1EF0770843_gshared (FastListCore_1_tD253A2B097165863483F30B720AE3EDC9C7FB508* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t Span_1_get_Length_m0B5336E05EEAAE122DF68A3A82C2D4359A2BE33D_gshared_inline (Span_1_t3F436092261253E8F2AF9867CA253C3B370766C2* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void FastListCore_1_Clear_m98F6A1FC4B1C2A6BFD6CED3667DE26CC29C702E9_gshared_inline (FastListCore_1_tD253A2B097165863483F30B720AE3EDC9C7FB508* __this, bool ___0_removeArray, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Queue_1_Enqueue_m5CB8CF3906F1289F92036F0973EC5BE3450402EF_gshared (Queue_1_tE9EF546915795972C3BFD68FBB8FA859D3BAF3B5* __this, RuntimeObject* ___0_item, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void MemoryExtensions_Reverse_TisRuntimeObject_m3448D0FC3A26C4E470AAA0048F324EAC1568D384_gshared (Span_1_t3F436092261253E8F2AF9867CA253C3B370766C2 ___0_span, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Queue_1_Clear_m70861E24CF43ECFF3BC5C2AD4EE55963D54D8711_gshared (Queue_1_tE9EF546915795972C3BFD68FBB8FA859D3BAF3B5* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t Queue_1_get_Count_m1768ADA9855B7CDA14C9C42E098A287F1A39C3A2_gshared_inline (Queue_1_tE9EF546915795972C3BFD68FBB8FA859D3BAF3B5* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Queue_1__ctor_m6E2A5A8173E0CC524496D5155C737DF8FD10D0EB_gshared (Queue_1_tE9EF546915795972C3BFD68FBB8FA859D3BAF3B5* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FloatPropertyAnimationComponent_1__ctor_m520E36837C0A979A35BBF254FF9230E33F064C3B_gshared (FloatPropertyAnimationComponent_1_tFE951CD9C72E8FD2C77BE0F3B0997FF9E3D23856* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RectPropertyAnimationComponent_1__ctor_m38F767107814FE0F5024A381A3B3F0764211B63E_gshared (RectPropertyAnimationComponent_1_t250A38A245D16CE5E221D2A0697F92DCB6592D73* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ColorPropertyAnimationComponent_1__ctor_mD8EC402F74F3B957C0730AAB4865D8A5E4C7218B_gshared (ColorPropertyAnimationComponent_1_tF4E4FDC0F5884AB0CB16F991028753C583BB3E44* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 MotionBuilder_3_RunWithoutBinding_mAEC7D58DAFA362934E3EB208CF5CE215C5D31B52_gshared_inline (MotionBuilder_3_tA23CEA9CCADDC2C36C8D976574E70A1D2A8909DC* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Action_2__ctor_mAD9BE38609AAEB7611F1A3736C24C3DFB0003861_gshared (Action_2_t25A8547AFC85E4914143093EDA11AE27C97D9E10* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 MotionBuilder_3_Bind_TisRuntimeObject_mC70C7DF3D4674E1D86107939A4C9ECA9AF590DED_gshared_inline (MotionBuilder_3_tA23CEA9CCADDC2C36C8D976574E70A1D2A8909DC* __this, RuntimeObject* ___0_state, Action_2_t25A8547AFC85E4914143093EDA11AE27C97D9E10* ___1_action, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void IntPropertyAnimationComponent_1__ctor_mAC8A51E19F4A331BAB0FDBAA55548C824AA21210_gshared (IntPropertyAnimationComponent_1_tD1459341AC334ADE02035F3FFB921C4DDB7859CA* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Vector4PropertyAnimationComponent_1__ctor_m225523B006F920019C823B8E5CDC66328D7FD2B0_gshared (Vector4PropertyAnimationComponent_1_tBE0B4EE415ABAEDE12538C71AAB2A8017528ACA0* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Rigidbody2DPositionAnimationBase_2__ctor_m91DBE4A1F747FD0DE57E36D5FF7A6C61AF65B093_gshared (Rigidbody2DPositionAnimationBase_2_t69CE9CD2C9AB3634CDE70A4430336030629AE27A* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Rigidbody2DPositionAnimationBase_2__ctor_m03A6CA89E10DDD75104A6AFAD90BAA664F55B30D_gshared (Rigidbody2DPositionAnimationBase_2_tEC44827A997686FCA2419EFAB9EBE6C21594FA08* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Rigidbody2DPositionAnimationBase_2__ctor_mC7824A99A66657C2E4F29844CE1206368D04DFA7_gshared (Rigidbody2DPositionAnimationBase_2_tD610427108FEF8F8BE698CEB4A2CECD0661F7A50* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Rigidbody2DRotationAnimationBase_2__ctor_mEEF666BD97FF8470CDFDC86B28CC270896E97D92_gshared (Rigidbody2DRotationAnimationBase_2_t86890366317CD729D92AE6912188756B638532DB* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Rigidbody2DRotationAnimationBase_2__ctor_m1EC94B27FCD01301E526332F2D4F024B8AF31C19_gshared (Rigidbody2DRotationAnimationBase_2_tE0B9D89B886A179723638F20CECDFA9455B1FFCE* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Rigidbody2DRotationAnimationBase_2__ctor_mB0C521DFA77F00B33FA186775B0FBE7C6722E364_gshared (Rigidbody2DRotationAnimationBase_2_tFE21E96D35A59F82D112D56BB1CD4DACE31FF142* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RigidbodyPositionAnimationBase_2__ctor_mE32805C6840A3AE1B3D1E29B526B8B6207579FE7_gshared (RigidbodyPositionAnimationBase_2_t3F8DF2590F9F24CFEC040865A4F75F1112C82612* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RigidbodyPositionAnimationBase_2__ctor_m5C9917A6D03F7F4B4EE34A03603193C670CFCFCA_gshared (RigidbodyPositionAnimationBase_2_t4B7F6634781E49079650FC961B633396250CCB64* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RigidbodyPositionAnimationBase_2__ctor_m52D79A66585233D8FF78F254A11F0CD5814D06DC_gshared (RigidbodyPositionAnimationBase_2_tFF37C9F46D719B654E722FB0A986E443FEA95121* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RigidbodyRotationAnimationBase_2__ctor_m9E52DDCFD6232030E2B638C3FA5DF2C4222D89B6_gshared (RigidbodyRotationAnimationBase_2_t66E15E7EA84710E8BE780C9D318AC50C679161CF* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RigidbodyRotationAnimationBase_2__ctor_m3D1D00C56767E1ECEF1B20F1722F3E83163930A7_gshared (RigidbodyRotationAnimationBase_2_t4ED992FE889ACCD94A95BB34F67343C3D80FD125* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RigidbodyRotationAnimationBase_2__ctor_m0FCEADC70BB2100B197645B518E21182B2D32928_gshared (RigidbodyRotationAnimationBase_2_t1817E2F258DFDA6844D7130B71D79AF68737E26E* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FixedString512BytesPropertyAnimationComponent_1__ctor_m6B7A63E988FD64FB5D6E5DFAE84BF7B68F225087_gshared (FixedString512BytesPropertyAnimationComponent_1_t478A8D50B267C479B29E1ACB7AB82274236EB836* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TransformPositionAnimationBase_2__ctor_mEEF0C512CF0CE9AA65F5F0984ADC7F2929384839_gshared (TransformPositionAnimationBase_2_t12DE280ED40D363B87FDE5057F2A16BAE67FF802* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TransformPositionAnimationBase_2__ctor_m8BDC2ADB7E9BD450BE42D78257850F220AABFF3C_gshared (TransformPositionAnimationBase_2_tE046D85AA8257B076AEA1EC06523CD68298DC058* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TransformPositionAnimationBase_2__ctor_mFB3FBCF99341AF765414DAA4F6EA24237DEB4D97_gshared (TransformPositionAnimationBase_2_t4B424C50A5AC08FC89469AD266ABFD58DE673D3C* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TransformRotationAnimationBase_2__ctor_m386B72ED5CBE026112F0016776A5DEBFE6650100_gshared (TransformRotationAnimationBase_2_tFFAE1B23BA584F4A5C424117F0D678258C5D31F2* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TransformRotationAnimationBase_2__ctor_mF21C092DF3DF33EE4D2AFD09A711D5620318C4D7_gshared (TransformRotationAnimationBase_2_t844162742B1631A9477AD8582685D20A7F4A8563* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TransformRotationAnimationBase_2__ctor_m7703FEF1A7410371F3F55026E386C1440E06B4F7_gshared (TransformRotationAnimationBase_2_t8558AFAC045C23114B034681003F2C2F07D02A8E* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TransformScaleAnimationBase_2__ctor_m1A5814121EBBC7C79C79CA062A524047F5EBB54C_gshared (TransformScaleAnimationBase_2_tBE02F8DFD3B63F89AD312977DEA9C0F63D75C8CA* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TransformScaleAnimationBase_2__ctor_mCCF29084DD333FEFAAEB2BDE353FB40A457694FD_gshared (TransformScaleAnimationBase_2_t17D49FB722A1A6F77DC3B5D3EB244F546109A4B0* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TransformScaleAnimationBase_2__ctor_m942D49424390A604708E7F41879DA29A8A6C3DE5_gshared (TransformScaleAnimationBase_2_t35F2BE396D00623EE3AFF251C9245768B42F60CB* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ValueAnimationComponent_3__ctor_m8ECB8F98AC1F6B0EA2F8DD2A20500E9FDDCBBB8E_gshared (ValueAnimationComponent_3_tCC2320109B3D50771B2F3D6B0F00DC4EDDAD7351* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ValueAnimationComponent_3__ctor_m7DC5DA0DF4325BCB95A2C1AC970E867DB8C6FB49_gshared (ValueAnimationComponent_3_tB17C7EB4A1D9997DB82C86BAE9692FCE017D2415* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ValueAnimationComponent_3__ctor_mFF142C6DE5E394B6F27CACEE067BD1C94C364B6E_gshared (ValueAnimationComponent_3_t595F5BA6422722CDF28C79CF2971881EFCC09CCD* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ValueAnimationComponent_3__ctor_mBD571DA23EAF4EDA73118C65A12D269BCA79D6B8_gshared (ValueAnimationComponent_3_t92CD7588E37E9CCDA6A882EE3FB36A61E34CF7D2* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ValueAnimationComponent_3__ctor_mB50DBC5A781213FD78CA4B6F0335327192242531_gshared (ValueAnimationComponent_3_t4DCB9D2381C489A5D17579A5EF8F3B8E5ABA2876* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ValueAnimationComponent_3__ctor_mF978B0E2507972B653807AB76DB5B6194310DB65_gshared (ValueAnimationComponent_3_t53C6B62F99FEFCE0BB2D49AF64FA7B03EFC2745D* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ValueAnimationComponent_3__ctor_mB06D7CD5E6F5AD7B33589B8DD7AFF78B507F224D_gshared (ValueAnimationComponent_3_tF4558CF2EFCE6A8F01D0043A88161A0AAD83882E* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ValueAnimationComponent_3__ctor_mBE855EEE224D3A0F08B0CE2CD6BCC3ACC38EACC0_gshared (ValueAnimationComponent_3_t02507D0A207D68A1BC1701CC8EE80C6B5DAD75E0* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR MotionBuilder_3_t99F4E0CCAE5DBB73925A375FDD89E2C368786FDA LMotion_Create_TisFixedString512Bytes_t0C425B0F2C07FEA1642C32BF8559116DF2BFF50E_TisStringOptions_tDDA84E855CD29F2BCD9BA102EA173B219CFCADA7_TisFixedString512BytesMotionAdapter_tE0926B0F0FCE1F39802A3A7B8E7F6C7E191C023C_mB9F06C5F7EBD9AF7B4CC7B1FBDE30DF686CC5D02_gshared (MotionSettings_2_t366DDAE7CADD1434DF2C190978E1C0C0B14A2316* ___0_settings, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Action_2__ctor_m1407F85E4A01BFB72BA3C12BD56C795485C7A4A9_gshared (Action_2_t61CA71EC63C565FB5A59A8A8E52B6C7336CD7A45* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 MotionBuilder_3_Bind_TisRuntimeObject_m3F7EBF2695B40B1A6AB1AF74C368B6EB3570BFB5_gshared_inline (MotionBuilder_3_t99F4E0CCAE5DBB73925A375FDD89E2C368786FDA* __this, RuntimeObject* ___0_state, Action_2_t61CA71EC63C565FB5A59A8A8E52B6C7336CD7A45* ___1_action, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* FixedStringMethods_ConvertToString_TisFixedString512Bytes_t0C425B0F2C07FEA1642C32BF8559116DF2BFF50E_mF5D257B46C5CC1CBFBC8A26BEA9F213A217A05EF_gshared (FixedString512Bytes_t0C425B0F2C07FEA1642C32BF8559116DF2BFF50E* ___0_fs, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void UnityEvent_1_Invoke_m6CDC8B0639CE8935E2E13D10B2C8E500968130B6_gshared (UnityEvent_1_t3CE03B42D5873C0C0E0692BEE72E1E6D5399F205* __this, RuntimeObject* ___0_arg0, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR RuntimeObject* FastListCore_1_get_Item_mFE5C63DC39367FCFC1EF140A1A219F98E0AAB31E_gshared_inline (FastListCore_1_tD253A2B097165863483F30B720AE3EDC9C7FB508* __this, int32_t ___0_index, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Array_Resize_TisRuntimeObject_mE8D92C287251BAF8256D85E5829F749359EC334E_gshared (ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918** ___0_array, int32_t ___1_newSize, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Span_1_t3F436092261253E8F2AF9867CA253C3B370766C2 MemoryExtensions_AsSpan_TisRuntimeObject_mDE52AF2E134C93F32A3E74B8B364196EE5D1F61B_gshared_inline (ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* ___0_array, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Span_1_Clear_mE8440147924A10C96926501B80472DFE2576383A_gshared_inline (Span_1_t3F436092261253E8F2AF9867CA253C3B370766C2* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void MotionBuilder_3_CheckBuffer_mC87B907D318DACF4CD7BFF327C156515E81102D5_gshared_inline (MotionBuilder_3_tA23CEA9CCADDC2C36C8D976574E70A1D2A8909DC* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 MotionBuilder_3_ScheduleMotion_m78777BD29641DDE6EA56ABB48A8979179D108A34_gshared_inline (MotionBuilder_3_tA23CEA9CCADDC2C36C8D976574E70A1D2A8909DC* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void MotionBuilder_3_SetCallbackData_TisRuntimeObject_m952DBBC1DAD5EECFC3151EB1EA39234B83381300_gshared_inline (MotionBuilder_3_tA23CEA9CCADDC2C36C8D976574E70A1D2A8909DC* __this, RuntimeObject* ___0_state, Action_2_t25A8547AFC85E4914143093EDA11AE27C97D9E10* ___1_action, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void MotionBuilder_3_CheckBuffer_mEED900CB970E8457CC3CF27F03C9ACFDC23F2F53_gshared_inline (MotionBuilder_3_t99F4E0CCAE5DBB73925A375FDD89E2C368786FDA* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void MotionBuilder_3_SetCallbackData_TisRuntimeObject_mC0C6324795BD3E6EE99C27545D0F5C2AFA7F1F52_gshared_inline (MotionBuilder_3_t99F4E0CCAE5DBB73925A375FDD89E2C368786FDA* __this, RuntimeObject* ___0_state, Action_2_t61CA71EC63C565FB5A59A8A8E52B6C7336CD7A45* ___1_action, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 MotionBuilder_3_ScheduleMotion_m0B2762B42FC511CA6867A9E0C817D00348FC804C_gshared_inline (MotionBuilder_3_t99F4E0CCAE5DBB73925A375FDD89E2C368786FDA* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Span_1__ctor_m8DB171982E2F26182BA531AC18EEAA27D52763EE_gshared_inline (Span_1_t3F436092261253E8F2AF9867CA253C3B370766C2* __this, ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* ___0_array, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 MotionDispatcher_Schedule_TisSingle_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_TisNoOptions_t7F036E20F8ED9230BFEF6EDF7C39EDD013A70C13_TisFloatMotionAdapter_t921836088726C215C673FCA1C985DEB91DD59E6D_m49F38264AA02A961F3AEE8437657F027A184BFFD_gshared (MotionBuilder_3_tA23CEA9CCADDC2C36C8D976574E70A1D2A8909DC* ___0_builder, int32_t ___1_playerLoopTiming, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void MotionBuilder_3_Dispose_mE427E5C7D3C1A34E54DAA0378E406537AE6C724E_gshared (MotionBuilder_3_tA23CEA9CCADDC2C36C8D976574E70A1D2A8909DC* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 MotionDispatcher_Schedule_TisFixedString512Bytes_t0C425B0F2C07FEA1642C32BF8559116DF2BFF50E_TisStringOptions_tDDA84E855CD29F2BCD9BA102EA173B219CFCADA7_TisFixedString512BytesMotionAdapter_tE0926B0F0FCE1F39802A3A7B8E7F6C7E191C023C_m165A3B75B57E53AA4FD0739235EA54F03274AA04_gshared (MotionBuilder_3_t99F4E0CCAE5DBB73925A375FDD89E2C368786FDA* ___0_builder, int32_t ___1_playerLoopTiming, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void MotionBuilder_3_Dispose_mCD91B91C78D1CF6B5D970F8FB78F07AEE2618C5A_gshared (MotionBuilder_3_t99F4E0CCAE5DBB73925A375FDD89E2C368786FDA* __this, const RuntimeMethod* method) ;

IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Attribute__ctor_m79ED1BF1EE36D1E417BA89A0D9F91F8AAD8D19E2 (Attribute_tFDA8EFEFB0711976D22474794576DAF28F7440AA* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 RectTransform_get_sizeDelta_m822A8493F2035677384F1540A2E9E5ACE63010BB (RectTransform_t6C5DA5E41A89E0F488B001E45E58963480E543A5* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RectTransform_set_sizeDelta_mC9A980EA6036E6725EF24CEDF3EE80A9B2B50EE5 (RectTransform_t6C5DA5E41A89E0F488B001E45E58963480E543A5* __this, Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___0_value, const RuntimeMethod* method) ;
inline void Vector2PropertyAnimationComponent_1__ctor_m56572DEAEB45BBC1F444175A1DC00589DC0CE40A (Vector2PropertyAnimationComponent_1_t8A177D739E23FF44639E13B99726B0A6C932D365* __this, const RuntimeMethod* method)
{
	((  void (*) (Vector2PropertyAnimationComponent_1_t8A177D739E23FF44639E13B99726B0A6C932D365*, const RuntimeMethod*))Vector2PropertyAnimationComponent_1__ctor_m674BDB539EAA5121E07705010B7D7EA0BEDFBFB8_gshared)(__this, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 RectTransform_get_pivot_mA8334AF05AA7FF09A173A2430F2BB9E85E5CBFFF (RectTransform_t6C5DA5E41A89E0F488B001E45E58963480E543A5* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RectTransform_set_pivot_m79D0177D383D432A93C2615F1932B739B1C6E146 (RectTransform_t6C5DA5E41A89E0F488B001E45E58963480E543A5* __this, Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RuntimeHelpers_InitializeArray_m751372AA3F24FBF6DA9B9D687CBFA2DE436CAB9B (RuntimeArray* ___0_array, RuntimeFieldHandle_t6E4C45B6D2EA12FC99185805A7E77527899B25C5 ___1_fldHandle, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2 (RuntimeObject* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void LitMotionAnimation_Play_m597E40471DA063B32C89155C4A7531A18E180D44 (LitMotionAnimation_t92F1AF7CF4859CEDD166DF7FD8091FDB2C34AF4A* __this, const RuntimeMethod* method) ;
inline bool Queue_1_TryDequeue_m1451317685A7F792584DE24EE0583389B6039EAF (Queue_1_t7CF7AD96EFA79CC0D603DFB407D7945D8F635C5B* __this, LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A** ___0_result, const RuntimeMethod* method)
{
	return ((  bool (*) (Queue_1_t7CF7AD96EFA79CC0D603DFB407D7945D8F635C5B*, LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A**, const RuntimeMethod*))Queue_1_TryDequeue_m13AAD6552BBFDBE843C324A37375B35618569981_gshared)(__this, ___0_result, method);
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool MotionHandleExtensions_IsActive_m33A1DAE84888047637F589CFF54E458F2CC0EED2_inline (MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 ___0_handle, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 MotionHandleExtensions_Preserve_m6B6F0C6EB75391E011DF1CBA1BA86DE2EC6AFA0D_inline (MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 ___0_handle, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR ManagedMotionData_tE1DE04DA7EDE8DF722FC643EAF489B5222F396CB* MotionManager_GetManagedDataRef_m56749CE994D083F22E7FF3458B71A9B04C3FFDAF_inline (MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 ___0_handle, bool ___1_checkIsInSequence, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Action__ctor_mBDC7B0B4A3F583B64C2896F01BDED360772F67DC (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Delegate_t* Delegate_Combine_m1F725AEF318BE6F0426863490691A6F4606E7D00 (Delegate_t* ___0_a, Delegate_t* ___1_b, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void LitMotionAnimationComponent_set_TrackedHandle_mC6DE281FF2BE11BB93377BCB3B311226AA8CAE31_inline (LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A* __this, MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 ___0_value, const RuntimeMethod* method) ;
inline void FastListCore_1_Add_mD59F1028468A63C7DA7F6097093EFDDADD2BF0A7_inline (FastListCore_1_tC5AF8169955F580A98B5AF92CC49BA523978EC21* __this, LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A* ___0_element, const RuntimeMethod* method)
{
	((  void (*) (FastListCore_1_tC5AF8169955F580A98B5AF92CC49BA523978EC21*, LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A*, const RuntimeMethod*))FastListCore_1_Add_mD6343E7F9F9D7AFEF75F492FBDC69F7F184CABDA_gshared_inline)(__this, ___0_element, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void LitMotionAnimation_MoveNextMotion_m758C60CEC79EB60134B2B283113E9F5D7D40B518 (LitMotionAnimation_t92F1AF7CF4859CEDD166DF7FD8091FDB2C34AF4A* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Debug_LogException_mAB3F4DC7297ED8FBB49DAA718B70E59A6B0171B0 (Exception_t* ___0_exception, const RuntimeMethod* method) ;
inline Span_1_t44E69912875D62DD12C3D1C8927FE31A9A209821 FastListCore_1_AsSpan_mD0CAF2920CC176F2EFB6A3DE58FE4A2F0F30CCFF (FastListCore_1_tC5AF8169955F580A98B5AF92CC49BA523978EC21* __this, const RuntimeMethod* method)
{
	return ((  Span_1_t44E69912875D62DD12C3D1C8927FE31A9A209821 (*) (FastListCore_1_tC5AF8169955F580A98B5AF92CC49BA523978EC21*, const RuntimeMethod*))FastListCore_1_AsSpan_mC8464DA5C76668324D0AE57C05BDEF1EF0770843_gshared)(__this, method);
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 LitMotionAnimationComponent_get_TrackedHandle_m5B903D2A947941090BE2F79870D73368DD0A0606_inline (LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void MotionHandle_set_PlaybackSpeed_m2CE0693B92A83C716EA4BF498A5E9C57AC71833E (MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375* __this, float ___0_value, const RuntimeMethod* method) ;
inline int32_t Span_1_get_Length_mE2128B4E1A4BFB4D166BDCD48BC921B8217B1B2D_inline (Span_1_t44E69912875D62DD12C3D1C8927FE31A9A209821* __this, const RuntimeMethod* method)
{
	return ((  int32_t (*) (Span_1_t44E69912875D62DD12C3D1C8927FE31A9A209821*, const RuntimeMethod*))Span_1_get_Length_m0B5336E05EEAAE122DF68A3A82C2D4359A2BE33D_gshared_inline)(__this, method);
}
inline void FastListCore_1_Clear_m811C076F75B1B9EEC15258970957561A31093597_inline (FastListCore_1_tC5AF8169955F580A98B5AF92CC49BA523978EC21* __this, bool ___0_removeArray, const RuntimeMethod* method)
{
	((  void (*) (FastListCore_1_tC5AF8169955F580A98B5AF92CC49BA523978EC21*, bool, const RuntimeMethod*))FastListCore_1_Clear_m98F6A1FC4B1C2A6BFD6CED3667DE26CC29C702E9_gshared_inline)(__this, ___0_removeArray, method);
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool LitMotionAnimationComponent_get_Enabled_m910F7930C43DDE7E7951EA1EC5767B3DE7EFF718_inline (LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A* __this, const RuntimeMethod* method) ;
inline void Queue_1_Enqueue_mD2C036FE4D6589DF5C96C83519C1B516764E325E (Queue_1_t7CF7AD96EFA79CC0D603DFB407D7945D8F635C5B* __this, LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A* ___0_item, const RuntimeMethod* method)
{
	((  void (*) (Queue_1_t7CF7AD96EFA79CC0D603DFB407D7945D8F635C5B*, LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A*, const RuntimeMethod*))Queue_1_Enqueue_m5CB8CF3906F1289F92036F0973EC5BE3450402EF_gshared)(__this, ___0_item, method);
}
inline void MemoryExtensions_Reverse_TisLitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A_m200CEA2272D4D2FB9B215FA2775C0CCE87B27A21 (Span_1_t44E69912875D62DD12C3D1C8927FE31A9A209821 ___0_span, const RuntimeMethod* method)
{
	((  void (*) (Span_1_t44E69912875D62DD12C3D1C8927FE31A9A209821, const RuntimeMethod*))MemoryExtensions_Reverse_TisRuntimeObject_m3448D0FC3A26C4E470AAA0048F324EAC1568D384_gshared)(___0_span, method);
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool MotionHandleExtensions_TryCancel_m5407A0C8EEB83CEEA742485A104889A7E14CD090_inline (MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 ___0_handle, const RuntimeMethod* method) ;
inline void Queue_1_Clear_m54078367C450AADBA046E46201CC85502B1547B3 (Queue_1_t7CF7AD96EFA79CC0D603DFB407D7945D8F635C5B* __this, const RuntimeMethod* method)
{
	((  void (*) (Queue_1_t7CF7AD96EFA79CC0D603DFB407D7945D8F635C5B*, const RuntimeMethod*))Queue_1_Clear_m70861E24CF43ECFF3BC5C2AD4EE55963D54D8711_gshared)(__this, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void LitMotionAnimation_Stop_m05B7E202159A8AE86BBF69671025C1EB439B13BE (LitMotionAnimation_t92F1AF7CF4859CEDD166DF7FD8091FDB2C34AF4A* __this, const RuntimeMethod* method) ;
inline int32_t Queue_1_get_Count_mE417CA84BEE80A2B2292D64CD09543348F6BE33D_inline (Queue_1_t7CF7AD96EFA79CC0D603DFB407D7945D8F635C5B* __this, const RuntimeMethod* method)
{
	return ((  int32_t (*) (Queue_1_t7CF7AD96EFA79CC0D603DFB407D7945D8F635C5B*, const RuntimeMethod*))Queue_1_get_Count_m1768ADA9855B7CDA14C9C42E098A287F1A39C3A2_gshared_inline)(__this, method);
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool MotionHandleExtensions_IsPlaying_m6CB941850BF2A95AB88CA4B5BD4FD3FB78F43DCF_inline (MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 ___0_handle, const RuntimeMethod* method) ;
inline void Queue_1__ctor_m290A360C903C36952EBBF4E8CC6D99A2D2722C6F (Queue_1_t7CF7AD96EFA79CC0D603DFB407D7945D8F635C5B* __this, const RuntimeMethod* method)
{
	((  void (*) (Queue_1_t7CF7AD96EFA79CC0D603DFB407D7945D8F635C5B*, const RuntimeMethod*))Queue_1__ctor_m6E2A5A8173E0CC524496D5155C737DF8FD10D0EB_gshared)(__this, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void MonoBehaviour__ctor_m592DB0105CA0BC97AA1C5F4AD27B12D68A3B7C1E (MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float AudioSource_get_volume_m9CCF33BC636562EA282FDE07463B547D70134EE3 (AudioSource_t871AC2272F896738252F04EE949AEF5B241D3299* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AudioSource_set_volume_mD902BBDBBDE0E3C148609BF3C05096148E90F2C0 (AudioSource_t871AC2272F896738252F04EE949AEF5B241D3299* __this, float ___0_value, const RuntimeMethod* method) ;
inline void FloatPropertyAnimationComponent_1__ctor_m077CB2E8666EFD5B50DB2BC64E516558A088FB46 (FloatPropertyAnimationComponent_1_t766705FAD368B247FAB08A4A977B8B3529AC1933* __this, const RuntimeMethod* method)
{
	((  void (*) (FloatPropertyAnimationComponent_1_t766705FAD368B247FAB08A4A977B8B3529AC1933*, const RuntimeMethod*))FloatPropertyAnimationComponent_1__ctor_m520E36837C0A979A35BBF254FF9230E33F064C3B_gshared)(__this, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float AudioSource_get_pitch_mB1B0B8A52400B5C798BF1E644FE1C2FFA20A9863 (AudioSource_t871AC2272F896738252F04EE949AEF5B241D3299* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AudioSource_set_pitch_mD14631FC99BF38AAFB356D9C45546BC16CF9E811 (AudioSource_t871AC2272F896738252F04EE949AEF5B241D3299* __this, float ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float Camera_get_aspect_m48BF8820EA2D55BE0D154BC5546819FB65BE257D (Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Camera_set_aspect_m537F21B48FDD5C060DFF9D87F34F4FB2B0F9BEB6 (Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* __this, float ___0_value, const RuntimeMethod* method) ;
inline void FloatPropertyAnimationComponent_1__ctor_m054E330B04F7CC58DB5069B067B6DE6AC4BEA62D (FloatPropertyAnimationComponent_1_t37A61899F20322CCCFA7C09CD5B7271B76DCEC17* __this, const RuntimeMethod* method)
{
	((  void (*) (FloatPropertyAnimationComponent_1_t37A61899F20322CCCFA7C09CD5B7271B76DCEC17*, const RuntimeMethod*))FloatPropertyAnimationComponent_1__ctor_m520E36837C0A979A35BBF254FF9230E33F064C3B_gshared)(__this, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float Camera_get_nearClipPlane_m5E8FAF84326E3192CB036BD29DCCDAF6A9861013 (Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Camera_set_nearClipPlane_m78482B5E4E0CE4C195D9CE0332AA75B2D9CCDDF6 (Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* __this, float ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float Camera_get_farClipPlane_m1D7128B85B5DB866F75FBE8CEBA48335716B67BD (Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Camera_set_farClipPlane_m84EF39B09573168734613481FD979BFF31C60139 (Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* __this, float ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float Camera_get_fieldOfView_m9A93F17BBF89F496AE231C21817AFD1C1E833FBB (Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Camera_set_fieldOfView_m5AA9EED4D1603A1DEDBF883D9C42814B2BDEB777 (Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* __this, float ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float Camera_get_orthographicSize_m7950C5627086253E02992A43ADFE59039DB473F8 (Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Camera_set_orthographicSize_m76DD021032ACB3DDBD052B75EC66DCE3A7295A5C (Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* __this, float ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D Camera_get_rect_m848C23B32814D1351E43F0A0110DB8ECA19C6772 (Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Camera_set_rect_mA81158BC169AF8674DE240AE9460FC5A0EADBB19 (Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* __this, Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D ___0_value, const RuntimeMethod* method) ;
inline void RectPropertyAnimationComponent_1__ctor_m5368181322126D16A737C4EA2ACBB2FC81D4DCED (RectPropertyAnimationComponent_1_t4A7ABE05AD402BC5405540B43257C6CAD0704C6D* __this, const RuntimeMethod* method)
{
	((  void (*) (RectPropertyAnimationComponent_1_t4A7ABE05AD402BC5405540B43257C6CAD0704C6D*, const RuntimeMethod*))RectPropertyAnimationComponent_1__ctor_m38F767107814FE0F5024A381A3B3F0764211B63E_gshared)(__this, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D Camera_get_pixelRect_m5F40F5C324EB252261F66962411EE08CC4BE39E7 (Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Camera_set_pixelRect_m4A9504577204D4E72B39BFB637ED808B778568A5 (Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* __this, Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Color_tD001788D726C3A7F1379BEED0260B9591F440C1F Camera_get_backgroundColor_m1577A81D1E6A91D7934CECB8A284AA2D4704D96F (Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Camera_set_backgroundColor_m036FD8C316A93A0B168ACC89AFF16D396B872138 (Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* __this, Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___0_value, const RuntimeMethod* method) ;
inline void ColorPropertyAnimationComponent_1__ctor_mD6C4FA4DA1DEE210AAE15DA766F9B509CBDA935D (ColorPropertyAnimationComponent_1_tAD1067F24BBAF944E40270FCC4CBBB6A0F82380D* __this, const RuntimeMethod* method)
{
	((  void (*) (ColorPropertyAnimationComponent_1_tAD1067F24BBAF944E40270FCC4CBBB6A0F82380D*, const RuntimeMethod*))ColorPropertyAnimationComponent_1__ctor_mD8EC402F74F3B957C0730AAB4865D8A5E4C7218B_gshared)(__this, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR MotionBuilder_3_tA23CEA9CCADDC2C36C8D976574E70A1D2A8909DC LMotion_Create_m884E0C4AC225B788D89F2B25D83F7EB2E7FE1E98 (float ___0_from, float ___1_to, float ___2_duration, const RuntimeMethod* method) ;
inline MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 MotionBuilder_3_RunWithoutBinding_mAEC7D58DAFA362934E3EB208CF5CE215C5D31B52_inline (MotionBuilder_3_tA23CEA9CCADDC2C36C8D976574E70A1D2A8909DC* __this, const RuntimeMethod* method)
{
	return ((  MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 (*) (MotionBuilder_3_tA23CEA9CCADDC2C36C8D976574E70A1D2A8909DC*, const RuntimeMethod*))MotionBuilder_3_RunWithoutBinding_mAEC7D58DAFA362934E3EB208CF5CE215C5D31B52_gshared_inline)(__this, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void LitMotionAnimationComponent__ctor_mD62326B8DB8585E40CFAA315FC8F3A94B8FA356B (LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void UnityEvent_Invoke_mFBF80D59B03C30C5FE6A06F897D954ACADE061D2 (UnityEvent_tDC2C3548799DBC91D1E3F3DE60083A66F4751977* __this, const RuntimeMethod* method) ;
inline void Action_2__ctor_mC5DAE4709C46376BA513C0B0C36447130D5AC00F (Action_2_t000191B3D702D8E6114AA07D7039CA5909A6BF5E* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method)
{
	((  void (*) (Action_2_t000191B3D702D8E6114AA07D7039CA5909A6BF5E*, RuntimeObject*, intptr_t, const RuntimeMethod*))Action_2__ctor_mAD9BE38609AAEB7611F1A3736C24C3DFB0003861_gshared)(__this, ___0_object, ___1_method, method);
}
inline MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 MotionBuilder_3_Bind_TisPlayLitMotionAnimationComponent_t44C212AF544D647258158A4B4979FF728DB597CE_m973634D4E62BFC53D209D9BEACC6B6BBF728B9E7_inline (MotionBuilder_3_tA23CEA9CCADDC2C36C8D976574E70A1D2A8909DC* __this, PlayLitMotionAnimationComponent_t44C212AF544D647258158A4B4979FF728DB597CE* ___0_state, Action_2_t000191B3D702D8E6114AA07D7039CA5909A6BF5E* ___1_action, const RuntimeMethod* method)
{
	return ((  MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 (*) (MotionBuilder_3_tA23CEA9CCADDC2C36C8D976574E70A1D2A8909DC*, PlayLitMotionAnimationComponent_t44C212AF544D647258158A4B4979FF728DB597CE*, Action_2_t000191B3D702D8E6114AA07D7039CA5909A6BF5E*, const RuntimeMethod*))MotionBuilder_3_Bind_TisRuntimeObject_mC70C7DF3D4674E1D86107939A4C9ECA9AF590DED_gshared_inline)(__this, ___0_state, ___1_action, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void LitMotionAnimation_Pause_mB7C40D6C139E1AC10D0B6E293F950D32BA20CD10 (LitMotionAnimation_t92F1AF7CF4859CEDD166DF7FD8091FDB2C34AF4A* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Object_op_Equality_mB6120F782D83091EF56A198FCEBCF066DB4A9605 (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C* ___0_x, Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C* ___1_y, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool MotionHandleExtensions_TryComplete_m6E5C4F018592F928CBA2FD1626DBE6D25A2472E1_inline (MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 ___0_handle, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool LitMotionAnimation_get_IsPlaying_mBC613A1EF1342DD779D8E3F5EFF1A910863E5F05 (LitMotionAnimation_t92F1AF7CF4859CEDD166DF7FD8091FDB2C34AF4A* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float Material_GetFloat_m2A77F10E6AA13EA3FA56166EFEA897115A14FA5A (Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* __this, String_t* ___0_name, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Material_SetFloat_m879CF81D740BAE6F23C9822400679F4D16365836 (Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* __this, String_t* ___0_name, float ___1_value, const RuntimeMethod* method) ;
inline void FloatPropertyAnimationComponent_1__ctor_mFE8113C499820E874DA39EE35599E7CD5F90335A (FloatPropertyAnimationComponent_1_t4EDF64705C1358D105E46606D44590DA7EE60C40* __this, const RuntimeMethod* method)
{
	((  void (*) (FloatPropertyAnimationComponent_1_t4EDF64705C1358D105E46606D44590DA7EE60C40*, const RuntimeMethod*))FloatPropertyAnimationComponent_1__ctor_m520E36837C0A979A35BBF254FF9230E33F064C3B_gshared)(__this, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Material_GetInteger_m333B6E8E15DEBFCE7DAC24DF0AE0C7E1AFAEEBA6 (Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* __this, String_t* ___0_name, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Material_SetInteger_m042389110B2242425663A4484DB1D3F553D526B7 (Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* __this, String_t* ___0_name, int32_t ___1_value, const RuntimeMethod* method) ;
inline void IntPropertyAnimationComponent_1__ctor_m40A64E8F3FDE81C56C1CC916ED517491E25D985A (IntPropertyAnimationComponent_1_tB4B79248A9306E45219CF94F18CB2856CA5E3B68* __this, const RuntimeMethod* method)
{
	((  void (*) (IntPropertyAnimationComponent_1_tB4B79248A9306E45219CF94F18CB2856CA5E3B68*, const RuntimeMethod*))IntPropertyAnimationComponent_1__ctor_mAC8A51E19F4A331BAB0FDBAA55548C824AA21210_gshared)(__this, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 Material_GetVector_mA2011D4DA2CC58003AE90DBF0802CF5EE31B014D (Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* __this, String_t* ___0_name, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Material_SetVector_m69444B8040D955821F241113446CC8713C9E12D1 (Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* __this, String_t* ___0_name, Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 ___1_value, const RuntimeMethod* method) ;
inline void Vector4PropertyAnimationComponent_1__ctor_m6A26AF4901445E2E2EEB09953E15E407EA5F87CD (Vector4PropertyAnimationComponent_1_tD5781699C642AFAE6FA17B1A9EC214F1A3DBDB75* __this, const RuntimeMethod* method)
{
	((  void (*) (Vector4PropertyAnimationComponent_1_tD5781699C642AFAE6FA17B1A9EC214F1A3DBDB75*, const RuntimeMethod*))Vector4PropertyAnimationComponent_1__ctor_m225523B006F920019C823B8E5CDC66328D7FD2B0_gshared)(__this, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Color_tD001788D726C3A7F1379BEED0260B9591F440C1F Material_GetColor_mAC702C70081A597DD2AA2F4627B1A1C65DDF6609 (Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* __this, String_t* ___0_name, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Material_SetColor_mFAB32FAA44461E46FD707B34184EC080CBB3539F (Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* __this, String_t* ___0_name, Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___1_value, const RuntimeMethod* method) ;
inline void ColorPropertyAnimationComponent_1__ctor_mA25D66433EC9D23253C25CF32C37A352ADEB804A (ColorPropertyAnimationComponent_1_t1A004C26C26C52867C5A5F2113F7CE3672961051* __this, const RuntimeMethod* method)
{
	((  void (*) (ColorPropertyAnimationComponent_1_t1A004C26C26C52867C5A5F2113F7CE3672961051*, const RuntimeMethod*))ColorPropertyAnimationComponent_1__ctor_mD8EC402F74F3B957C0730AAB4865D8A5E4C7218B_gshared)(__this, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Color_tD001788D726C3A7F1379BEED0260B9591F440C1F SpriteRenderer_get_color_mF19DA1B83ABD9A825127D4FBED9A111FE52F1F52 (SpriteRenderer_t1DD7FE258F072E1FA87D6577BA27225892B8047B* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SpriteRenderer_set_color_mB0EEC2845A0347E296C01C831F967731D2804546 (SpriteRenderer_t1DD7FE258F072E1FA87D6577BA27225892B8047B* __this, Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___0_value, const RuntimeMethod* method) ;
inline void ColorPropertyAnimationComponent_1__ctor_mEF8FD8C1434534785B3672D5C24EF181830F452A (ColorPropertyAnimationComponent_1_t2D91760F9E1E05B38E267F816DBC28A81B2F74AC* __this, const RuntimeMethod* method)
{
	((  void (*) (ColorPropertyAnimationComponent_1_t2D91760F9E1E05B38E267F816DBC28A81B2F74AC*, const RuntimeMethod*))ColorPropertyAnimationComponent_1__ctor_mD8EC402F74F3B957C0730AAB4865D8A5E4C7218B_gshared)(__this, method);
}
inline void FloatPropertyAnimationComponent_1__ctor_m100EED231E7D0285C9CE776FC962DB8920AC5A3D (FloatPropertyAnimationComponent_1_t230707BA48829ADA4918D773DC2E3DF24F0080C7* __this, const RuntimeMethod* method)
{
	((  void (*) (FloatPropertyAnimationComponent_1_t230707BA48829ADA4918D773DC2E3DF24F0080C7*, const RuntimeMethod*))FloatPropertyAnimationComponent_1__ctor_m520E36837C0A979A35BBF254FF9230E33F064C3B_gshared)(__this, method);
}
inline void Rigidbody2DPositionAnimationBase_2__ctor_m91DBE4A1F747FD0DE57E36D5FF7A6C61AF65B093 (Rigidbody2DPositionAnimationBase_2_t69CE9CD2C9AB3634CDE70A4430336030629AE27A* __this, const RuntimeMethod* method)
{
	((  void (*) (Rigidbody2DPositionAnimationBase_2_t69CE9CD2C9AB3634CDE70A4430336030629AE27A*, const RuntimeMethod*))Rigidbody2DPositionAnimationBase_2__ctor_m91DBE4A1F747FD0DE57E36D5FF7A6C61AF65B093_gshared)(__this, method);
}
inline void Rigidbody2DPositionAnimationBase_2__ctor_m03A6CA89E10DDD75104A6AFAD90BAA664F55B30D (Rigidbody2DPositionAnimationBase_2_tEC44827A997686FCA2419EFAB9EBE6C21594FA08* __this, const RuntimeMethod* method)
{
	((  void (*) (Rigidbody2DPositionAnimationBase_2_tEC44827A997686FCA2419EFAB9EBE6C21594FA08*, const RuntimeMethod*))Rigidbody2DPositionAnimationBase_2__ctor_m03A6CA89E10DDD75104A6AFAD90BAA664F55B30D_gshared)(__this, method);
}
inline void Rigidbody2DPositionAnimationBase_2__ctor_mC7824A99A66657C2E4F29844CE1206368D04DFA7 (Rigidbody2DPositionAnimationBase_2_tD610427108FEF8F8BE698CEB4A2CECD0661F7A50* __this, const RuntimeMethod* method)
{
	((  void (*) (Rigidbody2DPositionAnimationBase_2_tD610427108FEF8F8BE698CEB4A2CECD0661F7A50*, const RuntimeMethod*))Rigidbody2DPositionAnimationBase_2__ctor_mC7824A99A66657C2E4F29844CE1206368D04DFA7_gshared)(__this, method);
}
inline void Rigidbody2DRotationAnimationBase_2__ctor_mEEF666BD97FF8470CDFDC86B28CC270896E97D92 (Rigidbody2DRotationAnimationBase_2_t86890366317CD729D92AE6912188756B638532DB* __this, const RuntimeMethod* method)
{
	((  void (*) (Rigidbody2DRotationAnimationBase_2_t86890366317CD729D92AE6912188756B638532DB*, const RuntimeMethod*))Rigidbody2DRotationAnimationBase_2__ctor_mEEF666BD97FF8470CDFDC86B28CC270896E97D92_gshared)(__this, method);
}
inline void Rigidbody2DRotationAnimationBase_2__ctor_m1EC94B27FCD01301E526332F2D4F024B8AF31C19 (Rigidbody2DRotationAnimationBase_2_tE0B9D89B886A179723638F20CECDFA9455B1FFCE* __this, const RuntimeMethod* method)
{
	((  void (*) (Rigidbody2DRotationAnimationBase_2_tE0B9D89B886A179723638F20CECDFA9455B1FFCE*, const RuntimeMethod*))Rigidbody2DRotationAnimationBase_2__ctor_m1EC94B27FCD01301E526332F2D4F024B8AF31C19_gshared)(__this, method);
}
inline void Rigidbody2DRotationAnimationBase_2__ctor_mB0C521DFA77F00B33FA186775B0FBE7C6722E364 (Rigidbody2DRotationAnimationBase_2_tFE21E96D35A59F82D112D56BB1CD4DACE31FF142* __this, const RuntimeMethod* method)
{
	((  void (*) (Rigidbody2DRotationAnimationBase_2_tFE21E96D35A59F82D112D56BB1CD4DACE31FF142*, const RuntimeMethod*))Rigidbody2DRotationAnimationBase_2__ctor_mB0C521DFA77F00B33FA186775B0FBE7C6722E364_gshared)(__this, method);
}
inline void RigidbodyPositionAnimationBase_2__ctor_mE32805C6840A3AE1B3D1E29B526B8B6207579FE7 (RigidbodyPositionAnimationBase_2_t3F8DF2590F9F24CFEC040865A4F75F1112C82612* __this, const RuntimeMethod* method)
{
	((  void (*) (RigidbodyPositionAnimationBase_2_t3F8DF2590F9F24CFEC040865A4F75F1112C82612*, const RuntimeMethod*))RigidbodyPositionAnimationBase_2__ctor_mE32805C6840A3AE1B3D1E29B526B8B6207579FE7_gshared)(__this, method);
}
inline void RigidbodyPositionAnimationBase_2__ctor_m5C9917A6D03F7F4B4EE34A03603193C670CFCFCA (RigidbodyPositionAnimationBase_2_t4B7F6634781E49079650FC961B633396250CCB64* __this, const RuntimeMethod* method)
{
	((  void (*) (RigidbodyPositionAnimationBase_2_t4B7F6634781E49079650FC961B633396250CCB64*, const RuntimeMethod*))RigidbodyPositionAnimationBase_2__ctor_m5C9917A6D03F7F4B4EE34A03603193C670CFCFCA_gshared)(__this, method);
}
inline void RigidbodyPositionAnimationBase_2__ctor_m52D79A66585233D8FF78F254A11F0CD5814D06DC (RigidbodyPositionAnimationBase_2_tFF37C9F46D719B654E722FB0A986E443FEA95121* __this, const RuntimeMethod* method)
{
	((  void (*) (RigidbodyPositionAnimationBase_2_tFF37C9F46D719B654E722FB0A986E443FEA95121*, const RuntimeMethod*))RigidbodyPositionAnimationBase_2__ctor_m52D79A66585233D8FF78F254A11F0CD5814D06DC_gshared)(__this, method);
}
inline void RigidbodyRotationAnimationBase_2__ctor_m9E52DDCFD6232030E2B638C3FA5DF2C4222D89B6 (RigidbodyRotationAnimationBase_2_t66E15E7EA84710E8BE780C9D318AC50C679161CF* __this, const RuntimeMethod* method)
{
	((  void (*) (RigidbodyRotationAnimationBase_2_t66E15E7EA84710E8BE780C9D318AC50C679161CF*, const RuntimeMethod*))RigidbodyRotationAnimationBase_2__ctor_m9E52DDCFD6232030E2B638C3FA5DF2C4222D89B6_gshared)(__this, method);
}
inline void RigidbodyRotationAnimationBase_2__ctor_m3D1D00C56767E1ECEF1B20F1722F3E83163930A7 (RigidbodyRotationAnimationBase_2_t4ED992FE889ACCD94A95BB34F67343C3D80FD125* __this, const RuntimeMethod* method)
{
	((  void (*) (RigidbodyRotationAnimationBase_2_t4ED992FE889ACCD94A95BB34F67343C3D80FD125*, const RuntimeMethod*))RigidbodyRotationAnimationBase_2__ctor_m3D1D00C56767E1ECEF1B20F1722F3E83163930A7_gshared)(__this, method);
}
inline void RigidbodyRotationAnimationBase_2__ctor_m0FCEADC70BB2100B197645B518E21182B2D32928 (RigidbodyRotationAnimationBase_2_t1817E2F258DFDA6844D7130B71D79AF68737E26E* __this, const RuntimeMethod* method)
{
	((  void (*) (RigidbodyRotationAnimationBase_2_t1817E2F258DFDA6844D7130B71D79AF68737E26E*, const RuntimeMethod*))RigidbodyRotationAnimationBase_2__ctor_m0FCEADC70BB2100B197645B518E21182B2D32928_gshared)(__this, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR FixedString512Bytes_t0C425B0F2C07FEA1642C32BF8559116DF2BFF50E FixedString512Bytes_op_Implicit_mCF055F8B2FB98005951417E9FE994E3D230F58E4 (String_t* ___0_b, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* FixedString512Bytes_ToString_m1A47583FB34608DBEEDC65F9CA6E7B8E7930233F (FixedString512Bytes_t0C425B0F2C07FEA1642C32BF8559116DF2BFF50E* __this, const RuntimeMethod* method) ;
inline void FixedString512BytesPropertyAnimationComponent_1__ctor_mA507E727510CA75D6217FB4C246A0F6F41FC8951 (FixedString512BytesPropertyAnimationComponent_1_t478DB1A1E260CF40B695F4D3603F5E706B027BDF* __this, const RuntimeMethod* method)
{
	((  void (*) (FixedString512BytesPropertyAnimationComponent_1_t478DB1A1E260CF40B695F4D3603F5E706B027BDF*, const RuntimeMethod*))FixedString512BytesPropertyAnimationComponent_1__ctor_m6B7A63E988FD64FB5D6E5DFAE84BF7B68F225087_gshared)(__this, method);
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float TMP_Text_get_characterSpacing_m48A3B73EFBF47B5227D2BB4816FCFF628254C8FB_inline (TMP_Text_tE8D677872D43AD4B2AAF0D6101692A17D0B251A9* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TMP_Text_set_characterSpacing_mDCD34D244A502CA21CEB817E1F4CAC5BC6CCBA63 (TMP_Text_tE8D677872D43AD4B2AAF0D6101692A17D0B251A9* __this, float ___0_value, const RuntimeMethod* method) ;
inline void FloatPropertyAnimationComponent_1__ctor_m76B1B70CB536FF9DAD16FE9E20EE6EEDE9F0DC56 (FloatPropertyAnimationComponent_1_t1AB552982C92303A2C6CC5876C59DC138C174E43* __this, const RuntimeMethod* method)
{
	((  void (*) (FloatPropertyAnimationComponent_1_t1AB552982C92303A2C6CC5876C59DC138C174E43*, const RuntimeMethod*))FloatPropertyAnimationComponent_1__ctor_m520E36837C0A979A35BBF254FF9230E33F064C3B_gshared)(__this, method);
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float TMP_Text_get_wordSpacing_mF3DF1445C78E06195904FCF0293E63654C527D33_inline (TMP_Text_tE8D677872D43AD4B2AAF0D6101692A17D0B251A9* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TMP_Text_set_wordSpacing_m319C51E318DBC91F236F3CC65ED24787903F7E1E (TMP_Text_tE8D677872D43AD4B2AAF0D6101692A17D0B251A9* __this, float ___0_value, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float TMP_Text_get_lineSpacing_m7481D705EAD920B8D143D19A270D44CDABDAA251_inline (TMP_Text_tE8D677872D43AD4B2AAF0D6101692A17D0B251A9* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TMP_Text_set_lineSpacing_m1BA54B315F7472AE0E7B721CA7481016643591A7 (TMP_Text_tE8D677872D43AD4B2AAF0D6101692A17D0B251A9* __this, float ___0_value, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float TMP_Text_get_paragraphSpacing_mCCBC792CAE59958E92EB04B8E636AA2066534713_inline (TMP_Text_tE8D677872D43AD4B2AAF0D6101692A17D0B251A9* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TMP_Text_set_paragraphSpacing_m69921E35B44DE397FE604590913CAFB7DBFBAF30 (TMP_Text_tE8D677872D43AD4B2AAF0D6101692A17D0B251A9* __this, float ___0_value, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float TMP_Text_get_fontSize_m13A8365A56EA2B726EAD826B4A69C8918A528731_inline (TMP_Text_tE8D677872D43AD4B2AAF0D6101692A17D0B251A9* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TMP_Text_set_fontSize_m1C3A3BA2BC88E5E1D89375FD35A0AA91E75D3AAD (TMP_Text_tE8D677872D43AD4B2AAF0D6101692A17D0B251A9* __this, float ___0_value, const RuntimeMethod* method) ;
inline void ColorPropertyAnimationComponent_1__ctor_m8BE4FDFE086E6931EACF9112681328B5A24A1915 (ColorPropertyAnimationComponent_1_t39834DD27CD732DFE4F633FFD9F2D6A086CCBB06* __this, const RuntimeMethod* method)
{
	((  void (*) (ColorPropertyAnimationComponent_1_t39834DD27CD732DFE4F633FFD9F2D6A086CCBB06*, const RuntimeMethod*))ColorPropertyAnimationComponent_1__ctor_mD8EC402F74F3B957C0730AAB4865D8A5E4C7218B_gshared)(__this, method);
}
inline void TransformPositionAnimationBase_2__ctor_mEEF0C512CF0CE9AA65F5F0984ADC7F2929384839 (TransformPositionAnimationBase_2_t12DE280ED40D363B87FDE5057F2A16BAE67FF802* __this, const RuntimeMethod* method)
{
	((  void (*) (TransformPositionAnimationBase_2_t12DE280ED40D363B87FDE5057F2A16BAE67FF802*, const RuntimeMethod*))TransformPositionAnimationBase_2__ctor_mEEF0C512CF0CE9AA65F5F0984ADC7F2929384839_gshared)(__this, method);
}
inline void TransformPositionAnimationBase_2__ctor_m8BDC2ADB7E9BD450BE42D78257850F220AABFF3C (TransformPositionAnimationBase_2_tE046D85AA8257B076AEA1EC06523CD68298DC058* __this, const RuntimeMethod* method)
{
	((  void (*) (TransformPositionAnimationBase_2_tE046D85AA8257B076AEA1EC06523CD68298DC058*, const RuntimeMethod*))TransformPositionAnimationBase_2__ctor_m8BDC2ADB7E9BD450BE42D78257850F220AABFF3C_gshared)(__this, method);
}
inline void TransformPositionAnimationBase_2__ctor_mFB3FBCF99341AF765414DAA4F6EA24237DEB4D97 (TransformPositionAnimationBase_2_t4B424C50A5AC08FC89469AD266ABFD58DE673D3C* __this, const RuntimeMethod* method)
{
	((  void (*) (TransformPositionAnimationBase_2_t4B424C50A5AC08FC89469AD266ABFD58DE673D3C*, const RuntimeMethod*))TransformPositionAnimationBase_2__ctor_mFB3FBCF99341AF765414DAA4F6EA24237DEB4D97_gshared)(__this, method);
}
inline void TransformRotationAnimationBase_2__ctor_m386B72ED5CBE026112F0016776A5DEBFE6650100 (TransformRotationAnimationBase_2_tFFAE1B23BA584F4A5C424117F0D678258C5D31F2* __this, const RuntimeMethod* method)
{
	((  void (*) (TransformRotationAnimationBase_2_tFFAE1B23BA584F4A5C424117F0D678258C5D31F2*, const RuntimeMethod*))TransformRotationAnimationBase_2__ctor_m386B72ED5CBE026112F0016776A5DEBFE6650100_gshared)(__this, method);
}
inline void TransformRotationAnimationBase_2__ctor_mF21C092DF3DF33EE4D2AFD09A711D5620318C4D7 (TransformRotationAnimationBase_2_t844162742B1631A9477AD8582685D20A7F4A8563* __this, const RuntimeMethod* method)
{
	((  void (*) (TransformRotationAnimationBase_2_t844162742B1631A9477AD8582685D20A7F4A8563*, const RuntimeMethod*))TransformRotationAnimationBase_2__ctor_mF21C092DF3DF33EE4D2AFD09A711D5620318C4D7_gshared)(__this, method);
}
inline void TransformRotationAnimationBase_2__ctor_m7703FEF1A7410371F3F55026E386C1440E06B4F7 (TransformRotationAnimationBase_2_t8558AFAC045C23114B034681003F2C2F07D02A8E* __this, const RuntimeMethod* method)
{
	((  void (*) (TransformRotationAnimationBase_2_t8558AFAC045C23114B034681003F2C2F07D02A8E*, const RuntimeMethod*))TransformRotationAnimationBase_2__ctor_m7703FEF1A7410371F3F55026E386C1440E06B4F7_gshared)(__this, method);
}
inline void TransformScaleAnimationBase_2__ctor_m1A5814121EBBC7C79C79CA062A524047F5EBB54C (TransformScaleAnimationBase_2_tBE02F8DFD3B63F89AD312977DEA9C0F63D75C8CA* __this, const RuntimeMethod* method)
{
	((  void (*) (TransformScaleAnimationBase_2_tBE02F8DFD3B63F89AD312977DEA9C0F63D75C8CA*, const RuntimeMethod*))TransformScaleAnimationBase_2__ctor_m1A5814121EBBC7C79C79CA062A524047F5EBB54C_gshared)(__this, method);
}
inline void TransformScaleAnimationBase_2__ctor_mCCF29084DD333FEFAAEB2BDE353FB40A457694FD (TransformScaleAnimationBase_2_t17D49FB722A1A6F77DC3B5D3EB244F546109A4B0* __this, const RuntimeMethod* method)
{
	((  void (*) (TransformScaleAnimationBase_2_t17D49FB722A1A6F77DC3B5D3EB244F546109A4B0*, const RuntimeMethod*))TransformScaleAnimationBase_2__ctor_mCCF29084DD333FEFAAEB2BDE353FB40A457694FD_gshared)(__this, method);
}
inline void TransformScaleAnimationBase_2__ctor_m942D49424390A604708E7F41879DA29A8A6C3DE5 (TransformScaleAnimationBase_2_t35F2BE396D00623EE3AFF251C9245768B42F60CB* __this, const RuntimeMethod* method)
{
	((  void (*) (TransformScaleAnimationBase_2_t35F2BE396D00623EE3AFF251C9245768B42F60CB*, const RuntimeMethod*))TransformScaleAnimationBase_2__ctor_m942D49424390A604708E7F41879DA29A8A6C3DE5_gshared)(__this, method);
}
inline void FixedString512BytesPropertyAnimationComponent_1__ctor_m5816EC6A01ADE6AB5F7F7845913E24E9F895A6D1 (FixedString512BytesPropertyAnimationComponent_1_t12AAC49370780B9B738527218781305E8DD49E8E* __this, const RuntimeMethod* method)
{
	((  void (*) (FixedString512BytesPropertyAnimationComponent_1_t12AAC49370780B9B738527218781305E8DD49E8E*, const RuntimeMethod*))FixedString512BytesPropertyAnimationComponent_1__ctor_m6B7A63E988FD64FB5D6E5DFAE84BF7B68F225087_gshared)(__this, method);
}
inline void ColorPropertyAnimationComponent_1__ctor_m1AF482252E756F15D71CC78DB2C0938B29384FA6 (ColorPropertyAnimationComponent_1_tF608D7D00AEB026C1B6A6C045B2055450B730FA3* __this, const RuntimeMethod* method)
{
	((  void (*) (ColorPropertyAnimationComponent_1_tF608D7D00AEB026C1B6A6C045B2055450B730FA3*, const RuntimeMethod*))ColorPropertyAnimationComponent_1__ctor_mD8EC402F74F3B957C0730AAB4865D8A5E4C7218B_gshared)(__this, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Text_get_fontSize_m837C0618E78D0FDA972D11DDE3015DC888E93993 (Text_tD60B2346DAA6666BF0D822FF607F0B220C2B9E62* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Text_set_fontSize_m426338B0A2CDA58609028FFD471EF5F2C9F364D4 (Text_tD60B2346DAA6666BF0D822FF607F0B220C2B9E62* __this, int32_t ___0_value, const RuntimeMethod* method) ;
inline void IntPropertyAnimationComponent_1__ctor_mED6C6022A4A66D182CE79D0E04160F1CF7D70360 (IntPropertyAnimationComponent_1_t41237611C68CEA9B10C6849A5D5AE94883405EE5* __this, const RuntimeMethod* method)
{
	((  void (*) (IntPropertyAnimationComponent_1_t41237611C68CEA9B10C6849A5D5AE94883405EE5*, const RuntimeMethod*))IntPropertyAnimationComponent_1__ctor_mAC8A51E19F4A331BAB0FDBAA55548C824AA21210_gshared)(__this, method);
}
inline void ColorPropertyAnimationComponent_1__ctor_m774CD3721E9CA656495B1AC9880CC105C67395F7 (ColorPropertyAnimationComponent_1_t2DCF6BE5B232AEEC66DE5551BC91B57589438F4C* __this, const RuntimeMethod* method)
{
	((  void (*) (ColorPropertyAnimationComponent_1_t2DCF6BE5B232AEEC66DE5551BC91B57589438F4C*, const RuntimeMethod*))ColorPropertyAnimationComponent_1__ctor_mD8EC402F74F3B957C0730AAB4865D8A5E4C7218B_gshared)(__this, method);
}
inline void ColorPropertyAnimationComponent_1__ctor_mCDCEA5280E525AC77C62F9975B1CBAC04B12DD45 (ColorPropertyAnimationComponent_1_tE6AEF5F64546DD2C24C3A74C1AE6F63EF46DABD9* __this, const RuntimeMethod* method)
{
	((  void (*) (ColorPropertyAnimationComponent_1_tE6AEF5F64546DD2C24C3A74C1AE6F63EF46DABD9*, const RuntimeMethod*))ColorPropertyAnimationComponent_1__ctor_mD8EC402F74F3B957C0730AAB4865D8A5E4C7218B_gshared)(__this, method);
}
inline void FloatPropertyAnimationComponent_1__ctor_mA94A443DF00FEBBCE145422ED585E95DB5DD0550 (FloatPropertyAnimationComponent_1_tBF445C4B23C073DFA04D5E88341C03BB479265B7* __this, const RuntimeMethod* method)
{
	((  void (*) (FloatPropertyAnimationComponent_1_tBF445C4B23C073DFA04D5E88341C03BB479265B7*, const RuntimeMethod*))FloatPropertyAnimationComponent_1__ctor_m520E36837C0A979A35BBF254FF9230E33F064C3B_gshared)(__this, method);
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Image_get_fillAmount_mDEE52490D07124E21E7CB36718A5E3714D8B9788_inline (Image_tBC1D03F63BF71132E9A5E472B8742F172A011E7E* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Image_set_fillAmount_m8A9B55F47F966A3214EAC4ACBFE198776A98FAA7 (Image_tBC1D03F63BF71132E9A5E472B8742F172A011E7E* __this, float ___0_value, const RuntimeMethod* method) ;
inline void FloatPropertyAnimationComponent_1__ctor_mC888083E996E645550BEA6E923A8007C3C733830 (FloatPropertyAnimationComponent_1_t48E23230B8D939712EE3FA3062665415D1B00BAB* __this, const RuntimeMethod* method)
{
	((  void (*) (FloatPropertyAnimationComponent_1_t48E23230B8D939712EE3FA3062665415D1B00BAB*, const RuntimeMethod*))FloatPropertyAnimationComponent_1__ctor_m520E36837C0A979A35BBF254FF9230E33F064C3B_gshared)(__this, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float CanvasGroup_get_alpha_mBFEA193D2886B27CC53B31F90F7A1659B67ED6DF (CanvasGroup_t048C1461B14628CFAEBE6E7353093ADB04EBC094* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void CanvasGroup_set_alpha_m5C06839316D948BB4F75ED72C87FA1F1A20C333F (CanvasGroup_t048C1461B14628CFAEBE6E7353093ADB04EBC094* __this, float ___0_value, const RuntimeMethod* method) ;
inline void FloatPropertyAnimationComponent_1__ctor_m1DD2B509363FBC2BC129C88A860277D9028EF5CF (FloatPropertyAnimationComponent_1_t3EE1259037FE3CF6B6303A336C605A0F318CB5E6* __this, const RuntimeMethod* method)
{
	((  void (*) (FloatPropertyAnimationComponent_1_t3EE1259037FE3CF6B6303A336C605A0F318CB5E6*, const RuntimeMethod*))FloatPropertyAnimationComponent_1__ctor_m520E36837C0A979A35BBF254FF9230E33F064C3B_gshared)(__this, method);
}
inline void ValueAnimationComponent_3__ctor_m8ECB8F98AC1F6B0EA2F8DD2A20500E9FDDCBBB8E (ValueAnimationComponent_3_tCC2320109B3D50771B2F3D6B0F00DC4EDDAD7351* __this, const RuntimeMethod* method)
{
	((  void (*) (ValueAnimationComponent_3_tCC2320109B3D50771B2F3D6B0F00DC4EDDAD7351*, const RuntimeMethod*))ValueAnimationComponent_3__ctor_m8ECB8F98AC1F6B0EA2F8DD2A20500E9FDDCBBB8E_gshared)(__this, method);
}
inline void ValueAnimationComponent_3__ctor_m7DC5DA0DF4325BCB95A2C1AC970E867DB8C6FB49 (ValueAnimationComponent_3_tB17C7EB4A1D9997DB82C86BAE9692FCE017D2415* __this, const RuntimeMethod* method)
{
	((  void (*) (ValueAnimationComponent_3_tB17C7EB4A1D9997DB82C86BAE9692FCE017D2415*, const RuntimeMethod*))ValueAnimationComponent_3__ctor_m7DC5DA0DF4325BCB95A2C1AC970E867DB8C6FB49_gshared)(__this, method);
}
inline void ValueAnimationComponent_3__ctor_mFF142C6DE5E394B6F27CACEE067BD1C94C364B6E (ValueAnimationComponent_3_t595F5BA6422722CDF28C79CF2971881EFCC09CCD* __this, const RuntimeMethod* method)
{
	((  void (*) (ValueAnimationComponent_3_t595F5BA6422722CDF28C79CF2971881EFCC09CCD*, const RuntimeMethod*))ValueAnimationComponent_3__ctor_mFF142C6DE5E394B6F27CACEE067BD1C94C364B6E_gshared)(__this, method);
}
inline void ValueAnimationComponent_3__ctor_mBD571DA23EAF4EDA73118C65A12D269BCA79D6B8 (ValueAnimationComponent_3_t92CD7588E37E9CCDA6A882EE3FB36A61E34CF7D2* __this, const RuntimeMethod* method)
{
	((  void (*) (ValueAnimationComponent_3_t92CD7588E37E9CCDA6A882EE3FB36A61E34CF7D2*, const RuntimeMethod*))ValueAnimationComponent_3__ctor_mBD571DA23EAF4EDA73118C65A12D269BCA79D6B8_gshared)(__this, method);
}
inline void ValueAnimationComponent_3__ctor_mB50DBC5A781213FD78CA4B6F0335327192242531 (ValueAnimationComponent_3_t4DCB9D2381C489A5D17579A5EF8F3B8E5ABA2876* __this, const RuntimeMethod* method)
{
	((  void (*) (ValueAnimationComponent_3_t4DCB9D2381C489A5D17579A5EF8F3B8E5ABA2876*, const RuntimeMethod*))ValueAnimationComponent_3__ctor_mB50DBC5A781213FD78CA4B6F0335327192242531_gshared)(__this, method);
}
inline void ValueAnimationComponent_3__ctor_mF978B0E2507972B653807AB76DB5B6194310DB65 (ValueAnimationComponent_3_t53C6B62F99FEFCE0BB2D49AF64FA7B03EFC2745D* __this, const RuntimeMethod* method)
{
	((  void (*) (ValueAnimationComponent_3_t53C6B62F99FEFCE0BB2D49AF64FA7B03EFC2745D*, const RuntimeMethod*))ValueAnimationComponent_3__ctor_mF978B0E2507972B653807AB76DB5B6194310DB65_gshared)(__this, method);
}
inline void ValueAnimationComponent_3__ctor_mB06D7CD5E6F5AD7B33589B8DD7AFF78B507F224D (ValueAnimationComponent_3_tF4558CF2EFCE6A8F01D0043A88161A0AAD83882E* __this, const RuntimeMethod* method)
{
	((  void (*) (ValueAnimationComponent_3_tF4558CF2EFCE6A8F01D0043A88161A0AAD83882E*, const RuntimeMethod*))ValueAnimationComponent_3__ctor_mB06D7CD5E6F5AD7B33589B8DD7AFF78B507F224D_gshared)(__this, method);
}
inline void ValueAnimationComponent_3__ctor_mBE855EEE224D3A0F08B0CE2CD6BCC3ACC38EACC0 (ValueAnimationComponent_3_t02507D0A207D68A1BC1701CC8EE80C6B5DAD75E0* __this, const RuntimeMethod* method)
{
	((  void (*) (ValueAnimationComponent_3_t02507D0A207D68A1BC1701CC8EE80C6B5DAD75E0*, const RuntimeMethod*))ValueAnimationComponent_3__ctor_mBE855EEE224D3A0F08B0CE2CD6BCC3ACC38EACC0_gshared)(__this, method);
}
inline MotionBuilder_3_t99F4E0CCAE5DBB73925A375FDD89E2C368786FDA LMotion_Create_TisFixedString512Bytes_t0C425B0F2C07FEA1642C32BF8559116DF2BFF50E_TisStringOptions_tDDA84E855CD29F2BCD9BA102EA173B219CFCADA7_TisFixedString512BytesMotionAdapter_tE0926B0F0FCE1F39802A3A7B8E7F6C7E191C023C_mB9F06C5F7EBD9AF7B4CC7B1FBDE30DF686CC5D02 (MotionSettings_2_t366DDAE7CADD1434DF2C190978E1C0C0B14A2316* ___0_settings, const RuntimeMethod* method)
{
	return ((  MotionBuilder_3_t99F4E0CCAE5DBB73925A375FDD89E2C368786FDA (*) (MotionSettings_2_t366DDAE7CADD1434DF2C190978E1C0C0B14A2316*, const RuntimeMethod*))LMotion_Create_TisFixedString512Bytes_t0C425B0F2C07FEA1642C32BF8559116DF2BFF50E_TisStringOptions_tDDA84E855CD29F2BCD9BA102EA173B219CFCADA7_TisFixedString512BytesMotionAdapter_tE0926B0F0FCE1F39802A3A7B8E7F6C7E191C023C_mB9F06C5F7EBD9AF7B4CC7B1FBDE30DF686CC5D02_gshared)(___0_settings, method);
}
inline void Action_2__ctor_mDD38C5EC0EA5A3490A03B631884AF92642BDB78D (Action_2_t02F23795BC96180858055AB64D80A7006668D4D6* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method)
{
	((  void (*) (Action_2_t02F23795BC96180858055AB64D80A7006668D4D6*, RuntimeObject*, intptr_t, const RuntimeMethod*))Action_2__ctor_m1407F85E4A01BFB72BA3C12BD56C795485C7A4A9_gshared)(__this, ___0_object, ___1_method, method);
}
inline MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 MotionBuilder_3_Bind_TisStringValueAnimation_t4C21FFF21AA92CA403E695317EFDDC25F6F729A0_mCE150DB267F3DE64EA2D4761334CC080CFDC6BDE_inline (MotionBuilder_3_t99F4E0CCAE5DBB73925A375FDD89E2C368786FDA* __this, StringValueAnimation_t4C21FFF21AA92CA403E695317EFDDC25F6F729A0* ___0_state, Action_2_t02F23795BC96180858055AB64D80A7006668D4D6* ___1_action, const RuntimeMethod* method)
{
	return ((  MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 (*) (MotionBuilder_3_t99F4E0CCAE5DBB73925A375FDD89E2C368786FDA*, StringValueAnimation_t4C21FFF21AA92CA403E695317EFDDC25F6F729A0*, Action_2_t02F23795BC96180858055AB64D80A7006668D4D6*, const RuntimeMethod*))MotionBuilder_3_Bind_TisRuntimeObject_m3F7EBF2695B40B1A6AB1AF74C368B6EB3570BFB5_gshared_inline)(__this, ___0_state, ___1_action, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__ctor_m6282724279A29E01D74080870544C26EA3CF5E66 (U3CU3Ec_tDAFA5C4E997B098FAFADB5DBAD4676795BD4F04C* __this, const RuntimeMethod* method) ;
inline String_t* FixedStringMethods_ConvertToString_TisFixedString512Bytes_t0C425B0F2C07FEA1642C32BF8559116DF2BFF50E_mF5D257B46C5CC1CBFBC8A26BEA9F213A217A05EF (FixedString512Bytes_t0C425B0F2C07FEA1642C32BF8559116DF2BFF50E* ___0_fs, const RuntimeMethod* method)
{
	return ((  String_t* (*) (FixedString512Bytes_t0C425B0F2C07FEA1642C32BF8559116DF2BFF50E*, const RuntimeMethod*))FixedStringMethods_ConvertToString_TisFixedString512Bytes_t0C425B0F2C07FEA1642C32BF8559116DF2BFF50E_mF5D257B46C5CC1CBFBC8A26BEA9F213A217A05EF_gshared)(___0_fs, method);
}
inline void UnityEvent_1_Invoke_mA633B48B5D287DA856FB954AC3E4012487E63C15 (UnityEvent_1_tC9859540CF1468306CAB6D758C0A0D95DBCEC257* __this, String_t* ___0_arg0, const RuntimeMethod* method)
{
	((  void (*) (UnityEvent_1_tC9859540CF1468306CAB6D758C0A0D95DBCEC257*, String_t*, const RuntimeMethod*))UnityEvent_1_Invoke_m6CDC8B0639CE8935E2E13D10B2C8E500968130B6_gshared)(__this, ___0_arg0, method);
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool MotionManager_IsActive_mF4852406A0DD2636E235C9D61F213B0F33063258_inline (MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 ___0_handle, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR MotionData_tA820A6BD10DEA3326A5D51B503754F003CE7D399* MotionManager_GetDataRef_m51519EC669AF4A72192ECD1B44279959134E8CBE_inline (MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 ___0_handle, bool ___1_checkIsInSequence, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void MotionManager_CheckTypeId_mEFEB7AD899D19579BD664224BD414C6A366792CB_inline (MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375* ___0_handle, const RuntimeMethod* method) ;
inline RuntimeObject* FastListCore_1_get_Item_m9E02DE7FC9F92F09CFAF6958B134367BB973A9F3_inline (FastListCore_1_t7622FA94EE4948D93B3B798B45AAE6DFAAC94519* __this, int32_t ___0_index, const RuntimeMethod* method)
{
	return ((  RuntimeObject* (*) (FastListCore_1_t7622FA94EE4948D93B3B798B45AAE6DFAAC94519*, int32_t, const RuntimeMethod*))FastListCore_1_get_Item_mFE5C63DC39367FCFC1EF140A1A219F98E0AAB31E_gshared_inline)(__this, ___0_index, method);
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool MotionManager_TryCancel_m3F8F15BDC21AEBB3EEDB58CC70B604A8C9EE972A_inline (MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 ___0_handle, bool ___1_checkIsInSequence, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool MotionManager_IsPlaying_m73338D2E14A34995AB74EB371311EB23611F819C_inline (MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 ___0_handle, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool MotionManager_TryComplete_m7BECEFD3EB4321B741D8ABDB3B999A2B065BD611_inline (MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 ___0_handle, bool ___1_checkIsInSequence, const RuntimeMethod* method) ;
inline void Array_Resize_TisRuntimeObject_mE8D92C287251BAF8256D85E5829F749359EC334E (ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918** ___0_array, int32_t ___1_newSize, const RuntimeMethod* method)
{
	((  void (*) (ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918**, int32_t, const RuntimeMethod*))Array_Resize_TisRuntimeObject_mE8D92C287251BAF8256D85E5829F749359EC334E_gshared)(___0_array, ___1_newSize, method);
}
inline Span_1_t3F436092261253E8F2AF9867CA253C3B370766C2 MemoryExtensions_AsSpan_TisRuntimeObject_mDE52AF2E134C93F32A3E74B8B364196EE5D1F61B_inline (ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* ___0_array, const RuntimeMethod* method)
{
	return ((  Span_1_t3F436092261253E8F2AF9867CA253C3B370766C2 (*) (ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918*, const RuntimeMethod*))MemoryExtensions_AsSpan_TisRuntimeObject_mDE52AF2E134C93F32A3E74B8B364196EE5D1F61B_gshared_inline)(___0_array, method);
}
inline void Span_1_Clear_mE8440147924A10C96926501B80472DFE2576383A_inline (Span_1_t3F436092261253E8F2AF9867CA253C3B370766C2* __this, const RuntimeMethod* method)
{
	((  void (*) (Span_1_t3F436092261253E8F2AF9867CA253C3B370766C2*, const RuntimeMethod*))Span_1_Clear_mE8440147924A10C96926501B80472DFE2576383A_gshared_inline)(__this, method);
}
inline void MotionBuilder_3_CheckBuffer_mC87B907D318DACF4CD7BFF327C156515E81102D5_inline (MotionBuilder_3_tA23CEA9CCADDC2C36C8D976574E70A1D2A8909DC* __this, const RuntimeMethod* method)
{
	((  void (*) (MotionBuilder_3_tA23CEA9CCADDC2C36C8D976574E70A1D2A8909DC*, const RuntimeMethod*))MotionBuilder_3_CheckBuffer_mC87B907D318DACF4CD7BFF327C156515E81102D5_gshared_inline)(__this, method);
}
inline MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 MotionBuilder_3_ScheduleMotion_m78777BD29641DDE6EA56ABB48A8979179D108A34_inline (MotionBuilder_3_tA23CEA9CCADDC2C36C8D976574E70A1D2A8909DC* __this, const RuntimeMethod* method)
{
	return ((  MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 (*) (MotionBuilder_3_tA23CEA9CCADDC2C36C8D976574E70A1D2A8909DC*, const RuntimeMethod*))MotionBuilder_3_ScheduleMotion_m78777BD29641DDE6EA56ABB48A8979179D108A34_gshared_inline)(__this, method);
}
inline void MotionBuilder_3_SetCallbackData_TisRuntimeObject_m952DBBC1DAD5EECFC3151EB1EA39234B83381300_inline (MotionBuilder_3_tA23CEA9CCADDC2C36C8D976574E70A1D2A8909DC* __this, RuntimeObject* ___0_state, Action_2_t25A8547AFC85E4914143093EDA11AE27C97D9E10* ___1_action, const RuntimeMethod* method)
{
	((  void (*) (MotionBuilder_3_tA23CEA9CCADDC2C36C8D976574E70A1D2A8909DC*, RuntimeObject*, Action_2_t25A8547AFC85E4914143093EDA11AE27C97D9E10*, const RuntimeMethod*))MotionBuilder_3_SetCallbackData_TisRuntimeObject_m952DBBC1DAD5EECFC3151EB1EA39234B83381300_gshared_inline)(__this, ___0_state, ___1_action, method);
}
inline void MotionBuilder_3_CheckBuffer_mEED900CB970E8457CC3CF27F03C9ACFDC23F2F53_inline (MotionBuilder_3_t99F4E0CCAE5DBB73925A375FDD89E2C368786FDA* __this, const RuntimeMethod* method)
{
	((  void (*) (MotionBuilder_3_t99F4E0CCAE5DBB73925A375FDD89E2C368786FDA*, const RuntimeMethod*))MotionBuilder_3_CheckBuffer_mEED900CB970E8457CC3CF27F03C9ACFDC23F2F53_gshared_inline)(__this, method);
}
inline void MotionBuilder_3_SetCallbackData_TisRuntimeObject_mC0C6324795BD3E6EE99C27545D0F5C2AFA7F1F52_inline (MotionBuilder_3_t99F4E0CCAE5DBB73925A375FDD89E2C368786FDA* __this, RuntimeObject* ___0_state, Action_2_t61CA71EC63C565FB5A59A8A8E52B6C7336CD7A45* ___1_action, const RuntimeMethod* method)
{
	((  void (*) (MotionBuilder_3_t99F4E0CCAE5DBB73925A375FDD89E2C368786FDA*, RuntimeObject*, Action_2_t61CA71EC63C565FB5A59A8A8E52B6C7336CD7A45*, const RuntimeMethod*))MotionBuilder_3_SetCallbackData_TisRuntimeObject_mC0C6324795BD3E6EE99C27545D0F5C2AFA7F1F52_gshared_inline)(__this, ___0_state, ___1_action, method);
}
inline MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 MotionBuilder_3_ScheduleMotion_m0B2762B42FC511CA6867A9E0C817D00348FC804C_inline (MotionBuilder_3_t99F4E0CCAE5DBB73925A375FDD89E2C368786FDA* __this, const RuntimeMethod* method)
{
	return ((  MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 (*) (MotionBuilder_3_t99F4E0CCAE5DBB73925A375FDD89E2C368786FDA*, const RuntimeMethod*))MotionBuilder_3_ScheduleMotion_m0B2762B42FC511CA6867A9E0C817D00348FC804C_gshared_inline)(__this, method);
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t MotionManager_get_MotionTypeCount_m747275F1C694CA07B477CA9E940FCFE9C5051A5F_inline (const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ArgumentException__ctor_m026938A67AF9D36BB7ED27F80425D7194B514465 (ArgumentException_tAD90411542A20A9C72D5CDA3A84181D8B947A263* __this, String_t* ___0_message, const RuntimeMethod* method) ;
inline void Span_1__ctor_m8DB171982E2F26182BA531AC18EEAA27D52763EE_inline (Span_1_t3F436092261253E8F2AF9867CA253C3B370766C2* __this, ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* ___0_array, const RuntimeMethod* method)
{
	((  void (*) (Span_1_t3F436092261253E8F2AF9867CA253C3B370766C2*, ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918*, const RuntimeMethod*))Span_1__ctor_m8DB171982E2F26182BA531AC18EEAA27D52763EE_gshared_inline)(__this, ___0_array, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t IntPtr_get_Size_m1FAAA59DA73D7E32BB1AB55DD92A90AFE3251DBE (const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SpanHelpers_ClearWithReferences_m9641D8B6DC3AE81B4B0734BBA0E477EF131CD430 (intptr_t* ___0_ip, uint64_t ___1_pointerSizeLength, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void InvalidOperationException__ctor_mE4CB6F4712AB6D99A2358FBAE2E052B3EE976162 (InvalidOperationException_t5DDE4D49B7405FAAB1E4576F4715A42A3FAD4BAB* __this, String_t* ___0_message, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR RuntimeObject* MotionScheduler_get_DefaultScheduler_m1815603ECA3208DFD19FE6160C8FF824784E9FAB_inline (const RuntimeMethod* method) ;
inline MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 MotionDispatcher_Schedule_TisSingle_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_TisNoOptions_t7F036E20F8ED9230BFEF6EDF7C39EDD013A70C13_TisFloatMotionAdapter_t921836088726C215C673FCA1C985DEB91DD59E6D_m49F38264AA02A961F3AEE8437657F027A184BFFD (MotionBuilder_3_tA23CEA9CCADDC2C36C8D976574E70A1D2A8909DC* ___0_builder, int32_t ___1_playerLoopTiming, const RuntimeMethod* method)
{
	return ((  MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 (*) (MotionBuilder_3_tA23CEA9CCADDC2C36C8D976574E70A1D2A8909DC*, int32_t, const RuntimeMethod*))MotionDispatcher_Schedule_TisSingle_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_TisNoOptions_t7F036E20F8ED9230BFEF6EDF7C39EDD013A70C13_TisFloatMotionAdapter_t921836088726C215C673FCA1C985DEB91DD59E6D_m49F38264AA02A961F3AEE8437657F027A184BFFD_gshared)(___0_builder, ___1_playerLoopTiming, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void MotionDebugger_AddTracking_m5DAB6174F7F49BCC3F5C64BC81A53CCF88C5B475 (MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 ___0_motionHandle, RuntimeObject* ___1_scheduler, int32_t ___2_skipFrames, const RuntimeMethod* method) ;
inline void MotionBuilder_3_Dispose_mE427E5C7D3C1A34E54DAA0378E406537AE6C724E (MotionBuilder_3_tA23CEA9CCADDC2C36C8D976574E70A1D2A8909DC* __this, const RuntimeMethod* method)
{
	((  void (*) (MotionBuilder_3_tA23CEA9CCADDC2C36C8D976574E70A1D2A8909DC*, const RuntimeMethod*))MotionBuilder_3_Dispose_mE427E5C7D3C1A34E54DAA0378E406537AE6C724E_gshared)(__this, method);
}
inline MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 MotionDispatcher_Schedule_TisFixedString512Bytes_t0C425B0F2C07FEA1642C32BF8559116DF2BFF50E_TisStringOptions_tDDA84E855CD29F2BCD9BA102EA173B219CFCADA7_TisFixedString512BytesMotionAdapter_tE0926B0F0FCE1F39802A3A7B8E7F6C7E191C023C_m165A3B75B57E53AA4FD0739235EA54F03274AA04 (MotionBuilder_3_t99F4E0CCAE5DBB73925A375FDD89E2C368786FDA* ___0_builder, int32_t ___1_playerLoopTiming, const RuntimeMethod* method)
{
	return ((  MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 (*) (MotionBuilder_3_t99F4E0CCAE5DBB73925A375FDD89E2C368786FDA*, int32_t, const RuntimeMethod*))MotionDispatcher_Schedule_TisFixedString512Bytes_t0C425B0F2C07FEA1642C32BF8559116DF2BFF50E_TisStringOptions_tDDA84E855CD29F2BCD9BA102EA173B219CFCADA7_TisFixedString512BytesMotionAdapter_tE0926B0F0FCE1F39802A3A7B8E7F6C7E191C023C_m165A3B75B57E53AA4FD0739235EA54F03274AA04_gshared)(___0_builder, ___1_playerLoopTiming, method);
}
inline void MotionBuilder_3_Dispose_mCD91B91C78D1CF6B5D970F8FB78F07AEE2618C5A (MotionBuilder_3_t99F4E0CCAE5DBB73925A375FDD89E2C368786FDA* __this, const RuntimeMethod* method)
{
	((  void (*) (MotionBuilder_3_t99F4E0CCAE5DBB73925A375FDD89E2C368786FDA*, const RuntimeMethod*))MotionBuilder_3_Dispose_mCD91B91C78D1CF6B5D970F8FB78F07AEE2618C5A_gshared)(__this, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Type_t* Object_GetType_mE10A8FC1E57F3DF29972CCBC026C2DC3942263B3 (RuntimeObject* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Type_t* Type_GetTypeFromHandle_m6062B81682F79A4D6DF2640692EE6D9987858C57 (RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B ___0_handle, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Type_op_Inequality_m83209C7BB3C05DFBEA3B6199B0BEFE8037301172 (Type_t* ___0_left, Type_t* ___1_right, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ThrowHelper_ThrowArrayTypeMismatchException_m781AD7A903FEA43FAE3137977E6BC5F9BAEBC590 (const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint8_t* Array_GetRawSzArrayData_m2F8F5B2A381AEF971F12866D9C0A6C4FBA59F6BB_inline (RuntimeArray* __this, const RuntimeMethod* method) ;
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void EmbeddedAttribute__ctor_m43CCC2B1DEE103B98BFF31EE6090FA59DA8A1103 (EmbeddedAttribute_t4AF2D38B103036A7E5864EFB730772BD58CB94DE* __this, const RuntimeMethod* method) 
{
	{
		Attribute__ctor_m79ED1BF1EE36D1E417BA89A0D9F91F8AAD8D19E2(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void IsUnmanagedAttribute__ctor_mF5002E8D38A31E57A4DC7F5DA00D31B5D88D934F (IsUnmanagedAttribute_t3A3B7234A45164EED2468C5D59391259F9D6999B* __this, const RuntimeMethod* method) 
{
	{
		Attribute__ctor_m79ED1BF1EE36D1E417BA89A0D9F91F8AAD8D19E2(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 RectTransformSizeDeltaAnimation_GetValue_m41F50265689F1582BFF9D95FAED03C2AA04697A3 (RectTransformSizeDeltaAnimation_t0ADD7484D0932C20AD535D02E43A6D2A231D8AC3* __this, RectTransform_t6C5DA5E41A89E0F488B001E45E58963480E543A5* ___0_target, const RuntimeMethod* method) 
{
	{
		RectTransform_t6C5DA5E41A89E0F488B001E45E58963480E543A5* L_0 = ___0_target;
		NullCheck(L_0);
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_1;
		L_1 = RectTransform_get_sizeDelta_m822A8493F2035677384F1540A2E9E5ACE63010BB(L_0, NULL);
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RectTransformSizeDeltaAnimation_SetValue_m3DB3F02FE77B17EADB4E4CD08A5160835A5F5FA0 (RectTransformSizeDeltaAnimation_t0ADD7484D0932C20AD535D02E43A6D2A231D8AC3* __this, RectTransform_t6C5DA5E41A89E0F488B001E45E58963480E543A5* ___0_target, Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7* ___1_value, const RuntimeMethod* method) 
{
	{
		RectTransform_t6C5DA5E41A89E0F488B001E45E58963480E543A5* L_0 = ___0_target;
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7* L_1 = ___1_value;
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_2 = (*(Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7*)L_1);
		NullCheck(L_0);
		RectTransform_set_sizeDelta_mC9A980EA6036E6725EF24CEDF3EE80A9B2B50EE5(L_0, L_2, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RectTransformSizeDeltaAnimation__ctor_m4ABAB5D30DAA4A1E0A894ED5ACF641B30C789CD7 (RectTransformSizeDeltaAnimation_t0ADD7484D0932C20AD535D02E43A6D2A231D8AC3* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Vector2PropertyAnimationComponent_1__ctor_m56572DEAEB45BBC1F444175A1DC00589DC0CE40A_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Vector2PropertyAnimationComponent_1__ctor_m56572DEAEB45BBC1F444175A1DC00589DC0CE40A(__this, Vector2PropertyAnimationComponent_1__ctor_m56572DEAEB45BBC1F444175A1DC00589DC0CE40A_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 RectTransformPivotAnimation_GetValue_mDDC6AE296276918D77A551D1272DABAAF76C5B34 (RectTransformPivotAnimation_t08B1166052D57CC0DBE2F80C97A226462107161E* __this, RectTransform_t6C5DA5E41A89E0F488B001E45E58963480E543A5* ___0_target, const RuntimeMethod* method) 
{
	{
		RectTransform_t6C5DA5E41A89E0F488B001E45E58963480E543A5* L_0 = ___0_target;
		NullCheck(L_0);
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_1;
		L_1 = RectTransform_get_pivot_mA8334AF05AA7FF09A173A2430F2BB9E85E5CBFFF(L_0, NULL);
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RectTransformPivotAnimation_SetValue_m3A205C846693426B426DAC9993AF041489214904 (RectTransformPivotAnimation_t08B1166052D57CC0DBE2F80C97A226462107161E* __this, RectTransform_t6C5DA5E41A89E0F488B001E45E58963480E543A5* ___0_target, Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7* ___1_value, const RuntimeMethod* method) 
{
	{
		RectTransform_t6C5DA5E41A89E0F488B001E45E58963480E543A5* L_0 = ___0_target;
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7* L_1 = ___1_value;
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_2 = (*(Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7*)L_1);
		NullCheck(L_0);
		RectTransform_set_pivot_m79D0177D383D432A93C2615F1932B739B1C6E146(L_0, L_2, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RectTransformPivotAnimation__ctor_m8E2E83E3B8ABAFD7CE97CB5C70E22B858E9BCF98 (RectTransformPivotAnimation_t08B1166052D57CC0DBE2F80C97A226462107161E* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Vector2PropertyAnimationComponent_1__ctor_m56572DEAEB45BBC1F444175A1DC00589DC0CE40A_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Vector2PropertyAnimationComponent_1__ctor_m56572DEAEB45BBC1F444175A1DC00589DC0CE40A(__this, Vector2PropertyAnimationComponent_1__ctor_m56572DEAEB45BBC1F444175A1DC00589DC0CE40A_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR MonoScriptData_tE93C20220981B07F2835D890E72BD7FED954612A UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_m80B6A513F4ADF9E40FB55BDD20F5E30998A90941 (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CPrivateImplementationDetailsU3E_t6ECB8E8EED910A1EDD673CA4D1B8D1200B474B0B____350A400EB8CF293100456CE6B2B54169AC1689C33C50319718AAC01B4391F6B3_FieldInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CPrivateImplementationDetailsU3E_t6ECB8E8EED910A1EDD673CA4D1B8D1200B474B0B____4ACE9CA5EB9773B32830743CC01A3AFEDC23ADF9337FCE1CE0248C872EB42017_FieldInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	MonoScriptData_tE93C20220981B07F2835D890E72BD7FED954612A V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		il2cpp_codegen_initobj((&V_0), sizeof(MonoScriptData_tE93C20220981B07F2835D890E72BD7FED954612A));
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_0 = (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)SZArrayNew(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031_il2cpp_TypeInfo_var, (uint32_t)((int32_t)1405));
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_1 = L_0;
		RuntimeFieldHandle_t6E4C45B6D2EA12FC99185805A7E77527899B25C5 L_2 = { reinterpret_cast<intptr_t> (U3CPrivateImplementationDetailsU3E_t6ECB8E8EED910A1EDD673CA4D1B8D1200B474B0B____350A400EB8CF293100456CE6B2B54169AC1689C33C50319718AAC01B4391F6B3_FieldInfo_var) };
		RuntimeHelpers_InitializeArray_m751372AA3F24FBF6DA9B9D687CBFA2DE436CAB9B((RuntimeArray*)L_1, L_2, NULL);
		(&V_0)->___FilePathsData = L_1;
		Il2CppCodeGenWriteBarrier((void**)(&(&V_0)->___FilePathsData), (void*)L_1);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_3 = (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)SZArrayNew(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031_il2cpp_TypeInfo_var, (uint32_t)((int32_t)5393));
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_4 = L_3;
		RuntimeFieldHandle_t6E4C45B6D2EA12FC99185805A7E77527899B25C5 L_5 = { reinterpret_cast<intptr_t> (U3CPrivateImplementationDetailsU3E_t6ECB8E8EED910A1EDD673CA4D1B8D1200B474B0B____4ACE9CA5EB9773B32830743CC01A3AFEDC23ADF9337FCE1CE0248C872EB42017_FieldInfo_var) };
		RuntimeHelpers_InitializeArray_m751372AA3F24FBF6DA9B9D687CBFA2DE436CAB9B((RuntimeArray*)L_4, L_5, NULL);
		(&V_0)->___TypesData = L_4;
		Il2CppCodeGenWriteBarrier((void**)(&(&V_0)->___TypesData), (void*)L_4);
		(&V_0)->___TotalFiles = ((int32_t)15);
		(&V_0)->___TotalTypes = ((int32_t)90);
		(&V_0)->___IsEditorOnly = (bool)0;
		MonoScriptData_tE93C20220981B07F2835D890E72BD7FED954612A L_6 = V_0;
		return L_6;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_m145372F693CCD1311B8D6739DDF5283BA0E58C87 (UnitySourceGeneratedAssemblyMonoScriptTypes_v1_tEEC2B2FFC6DD357B5E6F61C238E835856299C93E* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C void MonoScriptData_tE93C20220981B07F2835D890E72BD7FED954612A_marshal_pinvoke(const MonoScriptData_tE93C20220981B07F2835D890E72BD7FED954612A& unmarshaled, MonoScriptData_tE93C20220981B07F2835D890E72BD7FED954612A_marshaled_pinvoke& marshaled)
{
	marshaled.___FilePathsData = il2cpp_codegen_com_marshal_safe_array(IL2CPP_VT_I1, unmarshaled.___FilePathsData);
	marshaled.___TypesData = il2cpp_codegen_com_marshal_safe_array(IL2CPP_VT_I1, unmarshaled.___TypesData);
	marshaled.___TotalTypes = unmarshaled.___TotalTypes;
	marshaled.___TotalFiles = unmarshaled.___TotalFiles;
	marshaled.___IsEditorOnly = static_cast<int32_t>(unmarshaled.___IsEditorOnly);
}
IL2CPP_EXTERN_C void MonoScriptData_tE93C20220981B07F2835D890E72BD7FED954612A_marshal_pinvoke_back(const MonoScriptData_tE93C20220981B07F2835D890E72BD7FED954612A_marshaled_pinvoke& marshaled, MonoScriptData_tE93C20220981B07F2835D890E72BD7FED954612A& unmarshaled)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	unmarshaled.___FilePathsData = (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I1, Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, marshaled.___FilePathsData);
	Il2CppCodeGenWriteBarrier((void**)(&unmarshaled.___FilePathsData), (void*)(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I1, Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, marshaled.___FilePathsData));
	unmarshaled.___TypesData = (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I1, Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, marshaled.___TypesData);
	Il2CppCodeGenWriteBarrier((void**)(&unmarshaled.___TypesData), (void*)(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I1, Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, marshaled.___TypesData));
	int32_t unmarshaledTotalTypes_temp_2 = 0;
	unmarshaledTotalTypes_temp_2 = marshaled.___TotalTypes;
	unmarshaled.___TotalTypes = unmarshaledTotalTypes_temp_2;
	int32_t unmarshaledTotalFiles_temp_3 = 0;
	unmarshaledTotalFiles_temp_3 = marshaled.___TotalFiles;
	unmarshaled.___TotalFiles = unmarshaledTotalFiles_temp_3;
	bool unmarshaledIsEditorOnly_temp_4 = false;
	unmarshaledIsEditorOnly_temp_4 = static_cast<bool>(marshaled.___IsEditorOnly);
	unmarshaled.___IsEditorOnly = unmarshaledIsEditorOnly_temp_4;
}
IL2CPP_EXTERN_C void MonoScriptData_tE93C20220981B07F2835D890E72BD7FED954612A_marshal_pinvoke_cleanup(MonoScriptData_tE93C20220981B07F2835D890E72BD7FED954612A_marshaled_pinvoke& marshaled)
{
	il2cpp_codegen_com_destroy_safe_array(marshaled.___FilePathsData);
	marshaled.___FilePathsData = NULL;
	il2cpp_codegen_com_destroy_safe_array(marshaled.___TypesData);
	marshaled.___TypesData = NULL;
}
IL2CPP_EXTERN_C void MonoScriptData_tE93C20220981B07F2835D890E72BD7FED954612A_marshal_com(const MonoScriptData_tE93C20220981B07F2835D890E72BD7FED954612A& unmarshaled, MonoScriptData_tE93C20220981B07F2835D890E72BD7FED954612A_marshaled_com& marshaled)
{
	marshaled.___FilePathsData = il2cpp_codegen_com_marshal_safe_array(IL2CPP_VT_I1, unmarshaled.___FilePathsData);
	marshaled.___TypesData = il2cpp_codegen_com_marshal_safe_array(IL2CPP_VT_I1, unmarshaled.___TypesData);
	marshaled.___TotalTypes = unmarshaled.___TotalTypes;
	marshaled.___TotalFiles = unmarshaled.___TotalFiles;
	marshaled.___IsEditorOnly = static_cast<int32_t>(unmarshaled.___IsEditorOnly);
}
IL2CPP_EXTERN_C void MonoScriptData_tE93C20220981B07F2835D890E72BD7FED954612A_marshal_com_back(const MonoScriptData_tE93C20220981B07F2835D890E72BD7FED954612A_marshaled_com& marshaled, MonoScriptData_tE93C20220981B07F2835D890E72BD7FED954612A& unmarshaled)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	unmarshaled.___FilePathsData = (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I1, Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, marshaled.___FilePathsData);
	Il2CppCodeGenWriteBarrier((void**)(&unmarshaled.___FilePathsData), (void*)(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I1, Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, marshaled.___FilePathsData));
	unmarshaled.___TypesData = (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I1, Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, marshaled.___TypesData);
	Il2CppCodeGenWriteBarrier((void**)(&unmarshaled.___TypesData), (void*)(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I1, Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, marshaled.___TypesData));
	int32_t unmarshaledTotalTypes_temp_2 = 0;
	unmarshaledTotalTypes_temp_2 = marshaled.___TotalTypes;
	unmarshaled.___TotalTypes = unmarshaledTotalTypes_temp_2;
	int32_t unmarshaledTotalFiles_temp_3 = 0;
	unmarshaledTotalFiles_temp_3 = marshaled.___TotalFiles;
	unmarshaled.___TotalFiles = unmarshaledTotalFiles_temp_3;
	bool unmarshaledIsEditorOnly_temp_4 = false;
	unmarshaledIsEditorOnly_temp_4 = static_cast<bool>(marshaled.___IsEditorOnly);
	unmarshaled.___IsEditorOnly = unmarshaledIsEditorOnly_temp_4;
}
IL2CPP_EXTERN_C void MonoScriptData_tE93C20220981B07F2835D890E72BD7FED954612A_marshal_com_cleanup(MonoScriptData_tE93C20220981B07F2835D890E72BD7FED954612A_marshaled_com& marshaled)
{
	il2cpp_codegen_com_destroy_safe_array(marshaled.___FilePathsData);
	marshaled.___FilePathsData = NULL;
	il2cpp_codegen_com_destroy_safe_array(marshaled.___TypesData);
	marshaled.___TypesData = NULL;
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* LitMotionAnimation_get_Components_mE4468AC0ACFD680534475A97C65B73001D5DC47D (LitMotionAnimation_t92F1AF7CF4859CEDD166DF7FD8091FDB2C34AF4A* __this, const RuntimeMethod* method) 
{
	{
		LitMotionAnimationComponentU5BU5D_tA8718977F5DABB54F0422858CC77746CF7E85897* L_0 = __this->___components;
		return (RuntimeObject*)L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void LitMotionAnimation_Start_m08CD9F50D29ACAB3BC814B024B247BFC4993EB07 (LitMotionAnimation_t92F1AF7CF4859CEDD166DF7FD8091FDB2C34AF4A* __this, const RuntimeMethod* method) 
{
	{
		bool L_0 = __this->___playOnAwake;
		if (!L_0)
		{
			goto IL_000e;
		}
	}
	{
		LitMotionAnimation_Play_m597E40471DA063B32C89155C4A7531A18E180D44(__this, NULL);
	}

IL_000e:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void LitMotionAnimation_MoveNextMotion_m758C60CEC79EB60134B2B283113E9F5D7D40B518 (LitMotionAnimation_t92F1AF7CF4859CEDD166DF7FD8091FDB2C34AF4A* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&FastListCore_1_Add_mD59F1028468A63C7DA7F6097093EFDDADD2BF0A7_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&FastListCore_1_tC5AF8169955F580A98B5AF92CC49BA523978EC21_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&LitMotionAnimation_MoveNextMotion_m758C60CEC79EB60134B2B283113E9F5D7D40B518_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Queue_1_TryDequeue_m1451317685A7F792584DE24EE0583389B6039EAF_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A* V_0 = NULL;
	MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 V_1;
	memset((&V_1), 0, sizeof(V_1));
	il2cpp::utils::ExceptionSupportStack<RuntimeObject*, 1> __active_exceptions;
	bool G_B3_0 = false;
	bool G_B2_0 = false;
	{
		Queue_1_t7CF7AD96EFA79CC0D603DFB407D7945D8F635C5B* L_0 = __this->___queue;
		NullCheck(L_0);
		bool L_1;
		L_1 = Queue_1_TryDequeue_m1451317685A7F792584DE24EE0583389B6039EAF(L_0, (&V_0), Queue_1_TryDequeue_m1451317685A7F792584DE24EE0583389B6039EAF_RuntimeMethod_var);
		if (!L_1)
		{
			goto IL_006f;
		}
	}
	try
	{
		{
			LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A* L_2 = V_0;
			NullCheck(L_2);
			MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 L_3;
			L_3 = VirtualFuncInvoker0< MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 >::Invoke(4, L_2);
			V_1 = L_3;
			MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 L_4 = V_1;
			bool L_5;
			L_5 = MotionHandleExtensions_IsActive_m33A1DAE84888047637F589CFF54E458F2CC0EED2_inline(L_4, NULL);
			bool L_6 = L_5;
			if (!L_6)
			{
				G_B3_0 = L_6;
				goto IL_004b_1;
			}
			G_B2_0 = L_6;
		}
		{
			MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 L_7 = V_1;
			MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 L_8;
			L_8 = MotionHandleExtensions_Preserve_m6B6F0C6EB75391E011DF1CBA1BA86DE2EC6AFA0D_inline(L_7, NULL);
			MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 L_9 = V_1;
			ManagedMotionData_tE1DE04DA7EDE8DF722FC643EAF489B5222F396CB* L_10;
			L_10 = MotionManager_GetManagedDataRef_m56749CE994D083F22E7FF3458B71A9B04C3FFDAF_inline(L_9, (bool)0, NULL);
			Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07** L_11 = (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07**)(&L_10->___OnCompleteAction);
			Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07** L_12 = L_11;
			Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_13 = *((Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07**)L_12);
			Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_14 = (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)il2cpp_codegen_object_new(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
			Action__ctor_mBDC7B0B4A3F583B64C2896F01BDED360772F67DC(L_14, __this, (intptr_t)((void*)LitMotionAnimation_MoveNextMotion_m758C60CEC79EB60134B2B283113E9F5D7D40B518_RuntimeMethod_var), NULL);
			Delegate_t* L_15;
			L_15 = Delegate_Combine_m1F725AEF318BE6F0426863490691A6F4606E7D00(L_13, L_14, NULL);
			*((RuntimeObject**)L_12) = (RuntimeObject*)((Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)CastclassSealed((RuntimeObject*)L_15, Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var));
			Il2CppCodeGenWriteBarrier((void**)(RuntimeObject**)L_12, (void*)(RuntimeObject*)((Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)CastclassSealed((RuntimeObject*)L_15, Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var)));
			G_B3_0 = G_B2_0;
		}

IL_004b_1:
		{
			LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A* L_16 = V_0;
			MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 L_17 = V_1;
			NullCheck(L_16);
			LitMotionAnimationComponent_set_TrackedHandle_mC6DE281FF2BE11BB93377BCB3B311226AA8CAE31_inline(L_16, L_17, NULL);
			FastListCore_1_tC5AF8169955F580A98B5AF92CC49BA523978EC21* L_18 = (FastListCore_1_tC5AF8169955F580A98B5AF92CC49BA523978EC21*)(&__this->___playingComponents);
			LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A* L_19 = V_0;
			il2cpp_codegen_runtime_class_init_inline(FastListCore_1_tC5AF8169955F580A98B5AF92CC49BA523978EC21_il2cpp_TypeInfo_var);
			FastListCore_1_Add_mD59F1028468A63C7DA7F6097093EFDDADD2BF0A7_inline(L_18, L_19, FastListCore_1_Add_mD59F1028468A63C7DA7F6097093EFDDADD2BF0A7_RuntimeMethod_var);
			if (G_B3_0)
			{
				goto IL_0066_1;
			}
		}
		{
			LitMotionAnimation_MoveNextMotion_m758C60CEC79EB60134B2B283113E9F5D7D40B518(__this, NULL);
		}

IL_0066_1:
		{
			goto IL_006f;
		}
	}
	catch(Il2CppExceptionWrapper& e)
	{
		if(il2cpp_codegen_class_is_assignable_from (((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&Exception_t_il2cpp_TypeInfo_var)), il2cpp_codegen_object_class(e.ex)))
		{
			IL2CPP_PUSH_ACTIVE_EXCEPTION(e.ex);
			goto CATCH_0068;
		}
		throw e;
	}

CATCH_0068:
	{
		Exception_t* L_20 = ((Exception_t*)IL2CPP_GET_ACTIVE_EXCEPTION(Exception_t*));;
		il2cpp_codegen_runtime_class_init_inline(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&Debug_t8394C7EEAECA3689C2C9B9DE9C7166D73596276F_il2cpp_TypeInfo_var)));
		Debug_LogException_mAB3F4DC7297ED8FBB49DAA718B70E59A6B0171B0(L_20, NULL);
		IL2CPP_POP_ACTIVE_EXCEPTION(Exception_t*);
		goto IL_006f;
	}

IL_006f:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void LitMotionAnimation_Play_m597E40471DA063B32C89155C4A7531A18E180D44 (LitMotionAnimation_t92F1AF7CF4859CEDD166DF7FD8091FDB2C34AF4A* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&FastListCore_1_Add_mD59F1028468A63C7DA7F6097093EFDDADD2BF0A7_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&FastListCore_1_AsSpan_mD0CAF2920CC176F2EFB6A3DE58FE4A2F0F30CCFF_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&FastListCore_1_Clear_m811C076F75B1B9EEC15258970957561A31093597_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&FastListCore_1_tC5AF8169955F580A98B5AF92CC49BA523978EC21_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Queue_1_Enqueue_mD2C036FE4D6589DF5C96C83519C1B516764E325E_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Span_1_get_Length_mE2128B4E1A4BFB4D166BDCD48BC921B8217B1B2D_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	bool V_0 = false;
	Span_1_t44E69912875D62DD12C3D1C8927FE31A9A209821 V_1;
	memset((&V_1), 0, sizeof(V_1));
	int32_t V_2 = 0;
	LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A* V_3 = NULL;
	MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 V_4;
	memset((&V_4), 0, sizeof(V_4));
	int32_t V_5 = 0;
	LitMotionAnimationComponentU5BU5D_tA8718977F5DABB54F0422858CC77746CF7E85897* V_6 = NULL;
	LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A* V_7 = NULL;
	LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A* V_8 = NULL;
	MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 V_9;
	memset((&V_9), 0, sizeof(V_9));
	il2cpp::utils::ExceptionSupportStack<RuntimeObject*, 1> __active_exceptions;
	{
		V_0 = (bool)0;
		FastListCore_1_tC5AF8169955F580A98B5AF92CC49BA523978EC21* L_0 = (FastListCore_1_tC5AF8169955F580A98B5AF92CC49BA523978EC21*)(&__this->___playingComponents);
		il2cpp_codegen_runtime_class_init_inline(FastListCore_1_tC5AF8169955F580A98B5AF92CC49BA523978EC21_il2cpp_TypeInfo_var);
		Span_1_t44E69912875D62DD12C3D1C8927FE31A9A209821 L_1;
		L_1 = FastListCore_1_AsSpan_mD0CAF2920CC176F2EFB6A3DE58FE4A2F0F30CCFF(L_0, FastListCore_1_AsSpan_mD0CAF2920CC176F2EFB6A3DE58FE4A2F0F30CCFF_RuntimeMethod_var);
		V_1 = L_1;
		V_2 = 0;
		goto IL_0045;
	}

IL_0012:
	{
		int32_t L_2 = V_2;
		LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A** L_3;
		L_3 = il2cpp_span_get_item((LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A**)((Il2CppByReference*)&(((&V_1))->____pointer))->value, (L_2), ((&V_1))->____length);
		LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A* L_4 = *((LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A**)L_3);
		V_3 = L_4;
		LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A* L_5 = V_3;
		NullCheck(L_5);
		MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 L_6;
		L_6 = LitMotionAnimationComponent_get_TrackedHandle_m5B903D2A947941090BE2F79870D73368DD0A0606_inline(L_5, NULL);
		V_4 = L_6;
		MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 L_7 = V_4;
		bool L_8;
		L_8 = MotionHandleExtensions_IsActive_m33A1DAE84888047637F589CFF54E458F2CC0EED2_inline(L_7, NULL);
		if (!L_8)
		{
			goto IL_0041;
		}
	}
	{
		il2cpp_codegen_runtime_class_init_inline(MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375_il2cpp_TypeInfo_var);
		MotionHandle_set_PlaybackSpeed_m2CE0693B92A83C716EA4BF498A5E9C57AC71833E((&V_4), (1.0f), NULL);
		V_0 = (bool)1;
		LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A* L_9 = V_3;
		NullCheck(L_9);
		VirtualActionInvoker0::Invoke(5, L_9);
	}

IL_0041:
	{
		int32_t L_10 = V_2;
		V_2 = ((int32_t)il2cpp_codegen_add(L_10, 1));
	}

IL_0045:
	{
		int32_t L_11 = V_2;
		int32_t L_12;
		L_12 = Span_1_get_Length_mE2128B4E1A4BFB4D166BDCD48BC921B8217B1B2D_inline((&V_1), Span_1_get_Length_mE2128B4E1A4BFB4D166BDCD48BC921B8217B1B2D_RuntimeMethod_var);
		if ((((int32_t)L_11) < ((int32_t)L_12)))
		{
			goto IL_0012;
		}
	}
	{
		bool L_13 = V_0;
		if (!L_13)
		{
			goto IL_0053;
		}
	}
	{
		return;
	}

IL_0053:
	{
		FastListCore_1_tC5AF8169955F580A98B5AF92CC49BA523978EC21* L_14 = (FastListCore_1_tC5AF8169955F580A98B5AF92CC49BA523978EC21*)(&__this->___playingComponents);
		il2cpp_codegen_runtime_class_init_inline(FastListCore_1_tC5AF8169955F580A98B5AF92CC49BA523978EC21_il2cpp_TypeInfo_var);
		FastListCore_1_Clear_m811C076F75B1B9EEC15258970957561A31093597_inline(L_14, (bool)0, FastListCore_1_Clear_m811C076F75B1B9EEC15258970957561A31093597_RuntimeMethod_var);
		int32_t L_15 = __this->___animationMode;
		V_5 = L_15;
		int32_t L_16 = V_5;
		if (!L_16)
		{
			goto IL_00b1;
		}
	}
	{
		int32_t L_17 = V_5;
		if ((!(((uint32_t)L_17) == ((uint32_t)1))))
		{
			goto IL_0115;
		}
	}
	{
		LitMotionAnimationComponentU5BU5D_tA8718977F5DABB54F0422858CC77746CF7E85897* L_18 = __this->___components;
		V_6 = L_18;
		V_2 = 0;
		goto IL_00a3;
	}

IL_007f:
	{
		LitMotionAnimationComponentU5BU5D_tA8718977F5DABB54F0422858CC77746CF7E85897* L_19 = V_6;
		int32_t L_20 = V_2;
		NullCheck(L_19);
		int32_t L_21 = L_20;
		LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A* L_22 = (L_19)->GetAt(static_cast<il2cpp_array_size_t>(L_21));
		V_7 = L_22;
		LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A* L_23 = V_7;
		if (!L_23)
		{
			goto IL_009f;
		}
	}
	{
		LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A* L_24 = V_7;
		NullCheck(L_24);
		bool L_25;
		L_25 = LitMotionAnimationComponent_get_Enabled_m910F7930C43DDE7E7951EA1EC5767B3DE7EFF718_inline(L_24, NULL);
		if (!L_25)
		{
			goto IL_009f;
		}
	}
	{
		Queue_1_t7CF7AD96EFA79CC0D603DFB407D7945D8F635C5B* L_26 = __this->___queue;
		LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A* L_27 = V_7;
		NullCheck(L_26);
		Queue_1_Enqueue_mD2C036FE4D6589DF5C96C83519C1B516764E325E(L_26, L_27, Queue_1_Enqueue_mD2C036FE4D6589DF5C96C83519C1B516764E325E_RuntimeMethod_var);
	}

IL_009f:
	{
		int32_t L_28 = V_2;
		V_2 = ((int32_t)il2cpp_codegen_add(L_28, 1));
	}

IL_00a3:
	{
		int32_t L_29 = V_2;
		LitMotionAnimationComponentU5BU5D_tA8718977F5DABB54F0422858CC77746CF7E85897* L_30 = V_6;
		NullCheck(L_30);
		if ((((int32_t)L_29) < ((int32_t)((int32_t)(((RuntimeArray*)L_30)->max_length)))))
		{
			goto IL_007f;
		}
	}
	{
		LitMotionAnimation_MoveNextMotion_m758C60CEC79EB60134B2B283113E9F5D7D40B518(__this, NULL);
		return;
	}

IL_00b1:
	{
		LitMotionAnimationComponentU5BU5D_tA8718977F5DABB54F0422858CC77746CF7E85897* L_31 = __this->___components;
		V_6 = L_31;
		V_2 = 0;
		goto IL_010e;
	}

IL_00bd:
	{
		LitMotionAnimationComponentU5BU5D_tA8718977F5DABB54F0422858CC77746CF7E85897* L_32 = V_6;
		int32_t L_33 = V_2;
		NullCheck(L_32);
		int32_t L_34 = L_33;
		LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A* L_35 = (L_32)->GetAt(static_cast<il2cpp_array_size_t>(L_34));
		V_8 = L_35;
		LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A* L_36 = V_8;
		if (!L_36)
		{
			goto IL_010a;
		}
	}
	{
		LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A* L_37 = V_8;
		NullCheck(L_37);
		bool L_38;
		L_38 = LitMotionAnimationComponent_get_Enabled_m910F7930C43DDE7E7951EA1EC5767B3DE7EFF718_inline(L_37, NULL);
		if (!L_38)
		{
			goto IL_010a;
		}
	}
	{
	}
	try
	{
		{
			LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A* L_39 = V_8;
			NullCheck(L_39);
			MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 L_40;
			L_40 = VirtualFuncInvoker0< MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 >::Invoke(4, L_39);
			V_9 = L_40;
			LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A* L_41 = V_8;
			MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 L_42 = V_9;
			NullCheck(L_41);
			LitMotionAnimationComponent_set_TrackedHandle_mC6DE281FF2BE11BB93377BCB3B311226AA8CAE31_inline(L_41, L_42, NULL);
			MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 L_43 = V_9;
			bool L_44;
			L_44 = MotionHandleExtensions_IsActive_m33A1DAE84888047637F589CFF54E458F2CC0EED2_inline(L_43, NULL);
			if (!L_44)
			{
				goto IL_00f4_1;
			}
		}
		{
			MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 L_45 = V_9;
			MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 L_46;
			L_46 = MotionHandleExtensions_Preserve_m6B6F0C6EB75391E011DF1CBA1BA86DE2EC6AFA0D_inline(L_45, NULL);
		}

IL_00f4_1:
		{
			FastListCore_1_tC5AF8169955F580A98B5AF92CC49BA523978EC21* L_47 = (FastListCore_1_tC5AF8169955F580A98B5AF92CC49BA523978EC21*)(&__this->___playingComponents);
			LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A* L_48 = V_8;
			il2cpp_codegen_runtime_class_init_inline(FastListCore_1_tC5AF8169955F580A98B5AF92CC49BA523978EC21_il2cpp_TypeInfo_var);
			FastListCore_1_Add_mD59F1028468A63C7DA7F6097093EFDDADD2BF0A7_inline(L_47, L_48, FastListCore_1_Add_mD59F1028468A63C7DA7F6097093EFDDADD2BF0A7_RuntimeMethod_var);
			goto IL_010a;
		}
	}
	catch(Il2CppExceptionWrapper& e)
	{
		if(il2cpp_codegen_class_is_assignable_from (((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&Exception_t_il2cpp_TypeInfo_var)), il2cpp_codegen_object_class(e.ex)))
		{
			IL2CPP_PUSH_ACTIVE_EXCEPTION(e.ex);
			goto CATCH_0103;
		}
		throw e;
	}

CATCH_0103:
	{
		Exception_t* L_49 = ((Exception_t*)IL2CPP_GET_ACTIVE_EXCEPTION(Exception_t*));;
		il2cpp_codegen_runtime_class_init_inline(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&Debug_t8394C7EEAECA3689C2C9B9DE9C7166D73596276F_il2cpp_TypeInfo_var)));
		Debug_LogException_mAB3F4DC7297ED8FBB49DAA718B70E59A6B0171B0(L_49, NULL);
		IL2CPP_POP_ACTIVE_EXCEPTION(Exception_t*);
		goto IL_010a;
	}

IL_010a:
	{
		int32_t L_50 = V_2;
		V_2 = ((int32_t)il2cpp_codegen_add(L_50, 1));
	}

IL_010e:
	{
		int32_t L_51 = V_2;
		LitMotionAnimationComponentU5BU5D_tA8718977F5DABB54F0422858CC77746CF7E85897* L_52 = V_6;
		NullCheck(L_52);
		if ((((int32_t)L_51) < ((int32_t)((int32_t)(((RuntimeArray*)L_52)->max_length)))))
		{
			goto IL_00bd;
		}
	}

IL_0115:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void LitMotionAnimation_Pause_mB7C40D6C139E1AC10D0B6E293F950D32BA20CD10 (LitMotionAnimation_t92F1AF7CF4859CEDD166DF7FD8091FDB2C34AF4A* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&FastListCore_1_AsSpan_mD0CAF2920CC176F2EFB6A3DE58FE4A2F0F30CCFF_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&FastListCore_1_tC5AF8169955F580A98B5AF92CC49BA523978EC21_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Span_1_get_Length_mE2128B4E1A4BFB4D166BDCD48BC921B8217B1B2D_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	Span_1_t44E69912875D62DD12C3D1C8927FE31A9A209821 V_0;
	memset((&V_0), 0, sizeof(V_0));
	int32_t V_1 = 0;
	LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A* V_2 = NULL;
	MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 V_3;
	memset((&V_3), 0, sizeof(V_3));
	{
		FastListCore_1_tC5AF8169955F580A98B5AF92CC49BA523978EC21* L_0 = (FastListCore_1_tC5AF8169955F580A98B5AF92CC49BA523978EC21*)(&__this->___playingComponents);
		il2cpp_codegen_runtime_class_init_inline(FastListCore_1_tC5AF8169955F580A98B5AF92CC49BA523978EC21_il2cpp_TypeInfo_var);
		Span_1_t44E69912875D62DD12C3D1C8927FE31A9A209821 L_1;
		L_1 = FastListCore_1_AsSpan_mD0CAF2920CC176F2EFB6A3DE58FE4A2F0F30CCFF(L_0, FastListCore_1_AsSpan_mD0CAF2920CC176F2EFB6A3DE58FE4A2F0F30CCFF_RuntimeMethod_var);
		V_0 = L_1;
		V_1 = 0;
		goto IL_003f;
	}

IL_0010:
	{
		int32_t L_2 = V_1;
		LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A** L_3;
		L_3 = il2cpp_span_get_item((LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A**)((Il2CppByReference*)&(((&V_0))->____pointer))->value, (L_2), ((&V_0))->____length);
		LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A* L_4 = *((LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A**)L_3);
		V_2 = L_4;
		LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A* L_5 = V_2;
		NullCheck(L_5);
		MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 L_6;
		L_6 = LitMotionAnimationComponent_get_TrackedHandle_m5B903D2A947941090BE2F79870D73368DD0A0606_inline(L_5, NULL);
		V_3 = L_6;
		MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 L_7 = V_3;
		bool L_8;
		L_8 = MotionHandleExtensions_IsActive_m33A1DAE84888047637F589CFF54E458F2CC0EED2_inline(L_7, NULL);
		if (!L_8)
		{
			goto IL_003b;
		}
	}
	{
		il2cpp_codegen_runtime_class_init_inline(MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375_il2cpp_TypeInfo_var);
		MotionHandle_set_PlaybackSpeed_m2CE0693B92A83C716EA4BF498A5E9C57AC71833E((&V_3), (0.0f), NULL);
		LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A* L_9 = V_2;
		NullCheck(L_9);
		VirtualActionInvoker0::Invoke(6, L_9);
	}

IL_003b:
	{
		int32_t L_10 = V_1;
		V_1 = ((int32_t)il2cpp_codegen_add(L_10, 1));
	}

IL_003f:
	{
		int32_t L_11 = V_1;
		int32_t L_12;
		L_12 = Span_1_get_Length_mE2128B4E1A4BFB4D166BDCD48BC921B8217B1B2D_inline((&V_0), Span_1_get_Length_mE2128B4E1A4BFB4D166BDCD48BC921B8217B1B2D_RuntimeMethod_var);
		if ((((int32_t)L_11) < ((int32_t)L_12)))
		{
			goto IL_0010;
		}
	}
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void LitMotionAnimation_Stop_m05B7E202159A8AE86BBF69671025C1EB439B13BE (LitMotionAnimation_t92F1AF7CF4859CEDD166DF7FD8091FDB2C34AF4A* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&FastListCore_1_AsSpan_mD0CAF2920CC176F2EFB6A3DE58FE4A2F0F30CCFF_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&FastListCore_1_Clear_m811C076F75B1B9EEC15258970957561A31093597_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&FastListCore_1_tC5AF8169955F580A98B5AF92CC49BA523978EC21_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MemoryExtensions_Reverse_TisLitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A_m200CEA2272D4D2FB9B215FA2775C0CCE87B27A21_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Queue_1_Clear_m54078367C450AADBA046E46201CC85502B1547B3_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Span_1_get_Length_mE2128B4E1A4BFB4D166BDCD48BC921B8217B1B2D_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	Span_1_t44E69912875D62DD12C3D1C8927FE31A9A209821 V_0;
	memset((&V_0), 0, sizeof(V_0));
	int32_t V_1 = 0;
	MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 V_2;
	memset((&V_2), 0, sizeof(V_2));
	{
		FastListCore_1_tC5AF8169955F580A98B5AF92CC49BA523978EC21* L_0 = (FastListCore_1_tC5AF8169955F580A98B5AF92CC49BA523978EC21*)(&__this->___playingComponents);
		il2cpp_codegen_runtime_class_init_inline(FastListCore_1_tC5AF8169955F580A98B5AF92CC49BA523978EC21_il2cpp_TypeInfo_var);
		Span_1_t44E69912875D62DD12C3D1C8927FE31A9A209821 L_1;
		L_1 = FastListCore_1_AsSpan_mD0CAF2920CC176F2EFB6A3DE58FE4A2F0F30CCFF(L_0, FastListCore_1_AsSpan_mD0CAF2920CC176F2EFB6A3DE58FE4A2F0F30CCFF_RuntimeMethod_var);
		Span_1_t44E69912875D62DD12C3D1C8927FE31A9A209821 L_2 = L_1;
		MemoryExtensions_Reverse_TisLitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A_m200CEA2272D4D2FB9B215FA2775C0CCE87B27A21(L_2, MemoryExtensions_Reverse_TisLitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A_m200CEA2272D4D2FB9B215FA2775C0CCE87B27A21_RuntimeMethod_var);
		V_0 = L_2;
		V_1 = 0;
		goto IL_003d;
	}

IL_0016:
	{
		int32_t L_3 = V_1;
		LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A** L_4;
		L_4 = il2cpp_span_get_item((LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A**)((Il2CppByReference*)&(((&V_0))->____pointer))->value, (L_3), ((&V_0))->____length);
		LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A* L_5 = *((LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A**)L_4);
		LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A* L_6 = L_5;
		NullCheck(L_6);
		MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 L_7;
		L_7 = LitMotionAnimationComponent_get_TrackedHandle_m5B903D2A947941090BE2F79870D73368DD0A0606_inline(L_6, NULL);
		V_2 = L_7;
		MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 L_8 = V_2;
		bool L_9;
		L_9 = MotionHandleExtensions_TryCancel_m5407A0C8EEB83CEEA742485A104889A7E14CD090_inline(L_8, NULL);
		LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A* L_10 = L_6;
		NullCheck(L_10);
		VirtualActionInvoker0::Invoke(7, L_10);
		MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 L_11 = V_2;
		NullCheck(L_10);
		LitMotionAnimationComponent_set_TrackedHandle_mC6DE281FF2BE11BB93377BCB3B311226AA8CAE31_inline(L_10, L_11, NULL);
		int32_t L_12 = V_1;
		V_1 = ((int32_t)il2cpp_codegen_add(L_12, 1));
	}

IL_003d:
	{
		int32_t L_13 = V_1;
		int32_t L_14;
		L_14 = Span_1_get_Length_mE2128B4E1A4BFB4D166BDCD48BC921B8217B1B2D_inline((&V_0), Span_1_get_Length_mE2128B4E1A4BFB4D166BDCD48BC921B8217B1B2D_RuntimeMethod_var);
		if ((((int32_t)L_13) < ((int32_t)L_14)))
		{
			goto IL_0016;
		}
	}
	{
		FastListCore_1_tC5AF8169955F580A98B5AF92CC49BA523978EC21* L_15 = (FastListCore_1_tC5AF8169955F580A98B5AF92CC49BA523978EC21*)(&__this->___playingComponents);
		il2cpp_codegen_runtime_class_init_inline(FastListCore_1_tC5AF8169955F580A98B5AF92CC49BA523978EC21_il2cpp_TypeInfo_var);
		FastListCore_1_Clear_m811C076F75B1B9EEC15258970957561A31093597_inline(L_15, (bool)0, FastListCore_1_Clear_m811C076F75B1B9EEC15258970957561A31093597_RuntimeMethod_var);
		Queue_1_t7CF7AD96EFA79CC0D603DFB407D7945D8F635C5B* L_16 = __this->___queue;
		NullCheck(L_16);
		Queue_1_Clear_m54078367C450AADBA046E46201CC85502B1547B3(L_16, Queue_1_Clear_m54078367C450AADBA046E46201CC85502B1547B3_RuntimeMethod_var);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void LitMotionAnimation_Restart_mD00836C97E7C7D5CEDD7C4FD55C170BC689C63FE (LitMotionAnimation_t92F1AF7CF4859CEDD166DF7FD8091FDB2C34AF4A* __this, const RuntimeMethod* method) 
{
	{
		LitMotionAnimation_Stop_m05B7E202159A8AE86BBF69671025C1EB439B13BE(__this, NULL);
		LitMotionAnimation_Play_m597E40471DA063B32C89155C4A7531A18E180D44(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool LitMotionAnimation_get_IsActive_m0B1FBE994BFE23FB181A193CCA45FBA31456F581 (LitMotionAnimation_t92F1AF7CF4859CEDD166DF7FD8091FDB2C34AF4A* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&FastListCore_1_AsSpan_mD0CAF2920CC176F2EFB6A3DE58FE4A2F0F30CCFF_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&FastListCore_1_tC5AF8169955F580A98B5AF92CC49BA523978EC21_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Queue_1_get_Count_mE417CA84BEE80A2B2292D64CD09543348F6BE33D_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Span_1_get_Length_mE2128B4E1A4BFB4D166BDCD48BC921B8217B1B2D_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	Span_1_t44E69912875D62DD12C3D1C8927FE31A9A209821 V_0;
	memset((&V_0), 0, sizeof(V_0));
	int32_t V_1 = 0;
	{
		Queue_1_t7CF7AD96EFA79CC0D603DFB407D7945D8F635C5B* L_0 = __this->___queue;
		NullCheck(L_0);
		int32_t L_1;
		L_1 = Queue_1_get_Count_mE417CA84BEE80A2B2292D64CD09543348F6BE33D_inline(L_0, Queue_1_get_Count_mE417CA84BEE80A2B2292D64CD09543348F6BE33D_RuntimeMethod_var);
		if ((((int32_t)L_1) <= ((int32_t)0)))
		{
			goto IL_0010;
		}
	}
	{
		return (bool)1;
	}

IL_0010:
	{
		FastListCore_1_tC5AF8169955F580A98B5AF92CC49BA523978EC21* L_2 = (FastListCore_1_tC5AF8169955F580A98B5AF92CC49BA523978EC21*)(&__this->___playingComponents);
		il2cpp_codegen_runtime_class_init_inline(FastListCore_1_tC5AF8169955F580A98B5AF92CC49BA523978EC21_il2cpp_TypeInfo_var);
		Span_1_t44E69912875D62DD12C3D1C8927FE31A9A209821 L_3;
		L_3 = FastListCore_1_AsSpan_mD0CAF2920CC176F2EFB6A3DE58FE4A2F0F30CCFF(L_2, FastListCore_1_AsSpan_mD0CAF2920CC176F2EFB6A3DE58FE4A2F0F30CCFF_RuntimeMethod_var);
		V_0 = L_3;
		V_1 = 0;
		goto IL_003b;
	}

IL_0020:
	{
		int32_t L_4 = V_1;
		LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A** L_5;
		L_5 = il2cpp_span_get_item((LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A**)((Il2CppByReference*)&(((&V_0))->____pointer))->value, (L_4), ((&V_0))->____length);
		LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A* L_6 = *((LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A**)L_5);
		NullCheck(L_6);
		MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 L_7;
		L_7 = LitMotionAnimationComponent_get_TrackedHandle_m5B903D2A947941090BE2F79870D73368DD0A0606_inline(L_6, NULL);
		bool L_8;
		L_8 = MotionHandleExtensions_IsActive_m33A1DAE84888047637F589CFF54E458F2CC0EED2_inline(L_7, NULL);
		if (!L_8)
		{
			goto IL_0037;
		}
	}
	{
		return (bool)1;
	}

IL_0037:
	{
		int32_t L_9 = V_1;
		V_1 = ((int32_t)il2cpp_codegen_add(L_9, 1));
	}

IL_003b:
	{
		int32_t L_10 = V_1;
		int32_t L_11;
		L_11 = Span_1_get_Length_mE2128B4E1A4BFB4D166BDCD48BC921B8217B1B2D_inline((&V_0), Span_1_get_Length_mE2128B4E1A4BFB4D166BDCD48BC921B8217B1B2D_RuntimeMethod_var);
		if ((((int32_t)L_10) < ((int32_t)L_11)))
		{
			goto IL_0020;
		}
	}
	{
		return (bool)0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool LitMotionAnimation_get_IsPlaying_mBC613A1EF1342DD779D8E3F5EFF1A910863E5F05 (LitMotionAnimation_t92F1AF7CF4859CEDD166DF7FD8091FDB2C34AF4A* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&FastListCore_1_AsSpan_mD0CAF2920CC176F2EFB6A3DE58FE4A2F0F30CCFF_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&FastListCore_1_tC5AF8169955F580A98B5AF92CC49BA523978EC21_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Queue_1_get_Count_mE417CA84BEE80A2B2292D64CD09543348F6BE33D_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Span_1_get_Length_mE2128B4E1A4BFB4D166BDCD48BC921B8217B1B2D_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	Span_1_t44E69912875D62DD12C3D1C8927FE31A9A209821 V_0;
	memset((&V_0), 0, sizeof(V_0));
	int32_t V_1 = 0;
	{
		Queue_1_t7CF7AD96EFA79CC0D603DFB407D7945D8F635C5B* L_0 = __this->___queue;
		NullCheck(L_0);
		int32_t L_1;
		L_1 = Queue_1_get_Count_mE417CA84BEE80A2B2292D64CD09543348F6BE33D_inline(L_0, Queue_1_get_Count_mE417CA84BEE80A2B2292D64CD09543348F6BE33D_RuntimeMethod_var);
		if ((((int32_t)L_1) <= ((int32_t)0)))
		{
			goto IL_0010;
		}
	}
	{
		return (bool)1;
	}

IL_0010:
	{
		FastListCore_1_tC5AF8169955F580A98B5AF92CC49BA523978EC21* L_2 = (FastListCore_1_tC5AF8169955F580A98B5AF92CC49BA523978EC21*)(&__this->___playingComponents);
		il2cpp_codegen_runtime_class_init_inline(FastListCore_1_tC5AF8169955F580A98B5AF92CC49BA523978EC21_il2cpp_TypeInfo_var);
		Span_1_t44E69912875D62DD12C3D1C8927FE31A9A209821 L_3;
		L_3 = FastListCore_1_AsSpan_mD0CAF2920CC176F2EFB6A3DE58FE4A2F0F30CCFF(L_2, FastListCore_1_AsSpan_mD0CAF2920CC176F2EFB6A3DE58FE4A2F0F30CCFF_RuntimeMethod_var);
		V_0 = L_3;
		V_1 = 0;
		goto IL_003b;
	}

IL_0020:
	{
		int32_t L_4 = V_1;
		LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A** L_5;
		L_5 = il2cpp_span_get_item((LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A**)((Il2CppByReference*)&(((&V_0))->____pointer))->value, (L_4), ((&V_0))->____length);
		LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A* L_6 = *((LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A**)L_5);
		NullCheck(L_6);
		MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 L_7;
		L_7 = LitMotionAnimationComponent_get_TrackedHandle_m5B903D2A947941090BE2F79870D73368DD0A0606_inline(L_6, NULL);
		bool L_8;
		L_8 = MotionHandleExtensions_IsPlaying_m6CB941850BF2A95AB88CA4B5BD4FD3FB78F43DCF_inline(L_7, NULL);
		if (!L_8)
		{
			goto IL_0037;
		}
	}
	{
		return (bool)1;
	}

IL_0037:
	{
		int32_t L_9 = V_1;
		V_1 = ((int32_t)il2cpp_codegen_add(L_9, 1));
	}

IL_003b:
	{
		int32_t L_10 = V_1;
		int32_t L_11;
		L_11 = Span_1_get_Length_mE2128B4E1A4BFB4D166BDCD48BC921B8217B1B2D_inline((&V_0), Span_1_get_Length_mE2128B4E1A4BFB4D166BDCD48BC921B8217B1B2D_RuntimeMethod_var);
		if ((((int32_t)L_10) < ((int32_t)L_11)))
		{
			goto IL_0020;
		}
	}
	{
		return (bool)0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void LitMotionAnimation_OnDestroy_m47A84B980BB95E548742B6852B39274F51808CB9 (LitMotionAnimation_t92F1AF7CF4859CEDD166DF7FD8091FDB2C34AF4A* __this, const RuntimeMethod* method) 
{
	{
		LitMotionAnimation_Stop_m05B7E202159A8AE86BBF69671025C1EB439B13BE(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void LitMotionAnimation__ctor_m17EB0D2D850BEB3718A200621A8543741C1C478B (LitMotionAnimation_t92F1AF7CF4859CEDD166DF7FD8091FDB2C34AF4A* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Queue_1__ctor_m290A360C903C36952EBBF4E8CC6D99A2D2722C6F_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Queue_1_t7CF7AD96EFA79CC0D603DFB407D7945D8F635C5B_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		__this->___playOnAwake = (bool)1;
		Queue_1_t7CF7AD96EFA79CC0D603DFB407D7945D8F635C5B* L_0 = (Queue_1_t7CF7AD96EFA79CC0D603DFB407D7945D8F635C5B*)il2cpp_codegen_object_new(Queue_1_t7CF7AD96EFA79CC0D603DFB407D7945D8F635C5B_il2cpp_TypeInfo_var);
		Queue_1__ctor_m290A360C903C36952EBBF4E8CC6D99A2D2722C6F(L_0, Queue_1__ctor_m290A360C903C36952EBBF4E8CC6D99A2D2722C6F_RuntimeMethod_var);
		__this->___queue = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___queue), (void*)L_0);
		MonoBehaviour__ctor_m592DB0105CA0BC97AA1C5F4AD27B12D68A3B7C1E(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void LitMotionAnimationComponent__ctor_mD62326B8DB8585E40CFAA315FC8F3A94B8FA356B (LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A* __this, const RuntimeMethod* method) 
{
	{
		__this->___enabled = (bool)1;
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool LitMotionAnimationComponent_get_Enabled_m910F7930C43DDE7E7951EA1EC5767B3DE7EFF718 (LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A* __this, const RuntimeMethod* method) 
{
	{
		bool L_0 = __this->___enabled;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* LitMotionAnimationComponent_get_DisplayName_mC4D963924B10F1CDD9F78D4E52464C89198912EC (LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A* __this, const RuntimeMethod* method) 
{
	{
		String_t* L_0 = __this->___displayName;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void LitMotionAnimationComponent_OnResume_m58544AC3643B1915D7CCC9C3FA958F076FC42A37 (LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A* __this, const RuntimeMethod* method) 
{
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void LitMotionAnimationComponent_OnPause_m4816CF7B8FEC93F345909CC3B5FF2F84EAA5B67E (LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A* __this, const RuntimeMethod* method) 
{
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void LitMotionAnimationComponent_OnStop_mBF3F5B3295E59AB4210C89B341B6E0AD349CA8AC (LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A* __this, const RuntimeMethod* method) 
{
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 LitMotionAnimationComponent_get_TrackedHandle_m5B903D2A947941090BE2F79870D73368DD0A0606 (LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A* __this, const RuntimeMethod* method) 
{
	{
		MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 L_0 = __this->___U3CTrackedHandleU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void LitMotionAnimationComponent_set_TrackedHandle_mC6DE281FF2BE11BB93377BCB3B311226AA8CAE31 (LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A* __this, MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 ___0_value, const RuntimeMethod* method) 
{
	{
		MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 L_0 = ___0_value;
		__this->___U3CTrackedHandleU3Ek__BackingField = L_0;
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void LitMotionAnimationComponentMenuAttribute__ctor_mFB980AD8F8EE812DBD0C7A75C376AD53DFB31462 (LitMotionAnimationComponentMenuAttribute_t6C29773ED47514A8074EC34A851EFF85D572180A* __this, String_t* ___0_menuName, const RuntimeMethod* method) 
{
	{
		Attribute__ctor_m79ED1BF1EE36D1E417BA89A0D9F91F8AAD8D19E2(__this, NULL);
		String_t* L_0 = ___0_menuName;
		__this->___U3CMenuNameU3Ek__BackingField = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___U3CMenuNameU3Ek__BackingField), (void*)L_0);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* LitMotionAnimationComponentMenuAttribute_get_MenuName_mEC11A3D4C87AB226A6F15C3469B96BE0FEB4FFF3 (LitMotionAnimationComponentMenuAttribute_t6C29773ED47514A8074EC34A851EFF85D572180A* __this, const RuntimeMethod* method) 
{
	{
		String_t* L_0 = __this->___U3CMenuNameU3Ek__BackingField;
		return L_0;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float AudioSourceVolumeAnimation_GetValue_m8BF329DE9619DCC0E1C23A731F8D2B849C2D6471 (AudioSourceVolumeAnimation_tD2D1289A6EBA9DE03DBB4B2E11EADC73F7B4C1BF* __this, AudioSource_t871AC2272F896738252F04EE949AEF5B241D3299* ___0_target, const RuntimeMethod* method) 
{
	{
		AudioSource_t871AC2272F896738252F04EE949AEF5B241D3299* L_0 = ___0_target;
		NullCheck(L_0);
		float L_1;
		L_1 = AudioSource_get_volume_m9CCF33BC636562EA282FDE07463B547D70134EE3(L_0, NULL);
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AudioSourceVolumeAnimation_SetValue_m6C23E9337AD89C1237F2CF65360333206595A496 (AudioSourceVolumeAnimation_tD2D1289A6EBA9DE03DBB4B2E11EADC73F7B4C1BF* __this, AudioSource_t871AC2272F896738252F04EE949AEF5B241D3299* ___0_target, float* ___1_value, const RuntimeMethod* method) 
{
	{
		AudioSource_t871AC2272F896738252F04EE949AEF5B241D3299* L_0 = ___0_target;
		float* L_1 = ___1_value;
		float L_2 = *((float*)L_1);
		NullCheck(L_0);
		AudioSource_set_volume_mD902BBDBBDE0E3C148609BF3C05096148E90F2C0(L_0, L_2, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AudioSourceVolumeAnimation__ctor_m25000CB1A95A753C109E667EF5FDE5F9239780F2 (AudioSourceVolumeAnimation_tD2D1289A6EBA9DE03DBB4B2E11EADC73F7B4C1BF* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&FloatPropertyAnimationComponent_1__ctor_m077CB2E8666EFD5B50DB2BC64E516558A088FB46_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		FloatPropertyAnimationComponent_1__ctor_m077CB2E8666EFD5B50DB2BC64E516558A088FB46(__this, FloatPropertyAnimationComponent_1__ctor_m077CB2E8666EFD5B50DB2BC64E516558A088FB46_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float AudioSourcePitchAnimation_GetValue_m678A92D5A613E2F9DF330A43C7412202DB68B8C4 (AudioSourcePitchAnimation_t62AB22027FB5C3AF778317F9B6E0CC0A1093D6F6* __this, AudioSource_t871AC2272F896738252F04EE949AEF5B241D3299* ___0_target, const RuntimeMethod* method) 
{
	{
		AudioSource_t871AC2272F896738252F04EE949AEF5B241D3299* L_0 = ___0_target;
		NullCheck(L_0);
		float L_1;
		L_1 = AudioSource_get_pitch_mB1B0B8A52400B5C798BF1E644FE1C2FFA20A9863(L_0, NULL);
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AudioSourcePitchAnimation_SetValue_m319255C814C45450FA7F626DED27FDB8568E96F4 (AudioSourcePitchAnimation_t62AB22027FB5C3AF778317F9B6E0CC0A1093D6F6* __this, AudioSource_t871AC2272F896738252F04EE949AEF5B241D3299* ___0_target, float* ___1_value, const RuntimeMethod* method) 
{
	{
		AudioSource_t871AC2272F896738252F04EE949AEF5B241D3299* L_0 = ___0_target;
		float* L_1 = ___1_value;
		float L_2 = *((float*)L_1);
		NullCheck(L_0);
		AudioSource_set_pitch_mD14631FC99BF38AAFB356D9C45546BC16CF9E811(L_0, L_2, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AudioSourcePitchAnimation__ctor_mD45A1C54E1C33173DBDBAC0BFD420042D5E73379 (AudioSourcePitchAnimation_t62AB22027FB5C3AF778317F9B6E0CC0A1093D6F6* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&FloatPropertyAnimationComponent_1__ctor_m077CB2E8666EFD5B50DB2BC64E516558A088FB46_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		FloatPropertyAnimationComponent_1__ctor_m077CB2E8666EFD5B50DB2BC64E516558A088FB46(__this, FloatPropertyAnimationComponent_1__ctor_m077CB2E8666EFD5B50DB2BC64E516558A088FB46_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float CameraAspectAnimation_GetValue_mB2F65796DBD88DBB0495E1BB99C3DCFC6C979BB5 (CameraAspectAnimation_t7DB104395B9F6412E22D3ED76E390ECD41CF173A* __this, Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* ___0_target, const RuntimeMethod* method) 
{
	{
		Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* L_0 = ___0_target;
		NullCheck(L_0);
		float L_1;
		L_1 = Camera_get_aspect_m48BF8820EA2D55BE0D154BC5546819FB65BE257D(L_0, NULL);
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void CameraAspectAnimation_SetValue_mAF5484690CE01F2FA34B5922698A98779CCABF99 (CameraAspectAnimation_t7DB104395B9F6412E22D3ED76E390ECD41CF173A* __this, Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* ___0_target, float* ___1_value, const RuntimeMethod* method) 
{
	{
		Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* L_0 = ___0_target;
		float* L_1 = ___1_value;
		float L_2 = *((float*)L_1);
		NullCheck(L_0);
		Camera_set_aspect_m537F21B48FDD5C060DFF9D87F34F4FB2B0F9BEB6(L_0, L_2, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void CameraAspectAnimation__ctor_mCF081386A5F8DBD9E6DA9677E8F737EFDC5B93E4 (CameraAspectAnimation_t7DB104395B9F6412E22D3ED76E390ECD41CF173A* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&FloatPropertyAnimationComponent_1__ctor_m054E330B04F7CC58DB5069B067B6DE6AC4BEA62D_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		FloatPropertyAnimationComponent_1__ctor_m054E330B04F7CC58DB5069B067B6DE6AC4BEA62D(__this, FloatPropertyAnimationComponent_1__ctor_m054E330B04F7CC58DB5069B067B6DE6AC4BEA62D_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float CameraNearClipPlaneAnimation_GetValue_mFCF2557AB6EB89787D43288DC74DD6F92DD96D62 (CameraNearClipPlaneAnimation_t31C2AEFB468ED028ABF534C762CB31DF85DA903F* __this, Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* ___0_target, const RuntimeMethod* method) 
{
	{
		Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* L_0 = ___0_target;
		NullCheck(L_0);
		float L_1;
		L_1 = Camera_get_nearClipPlane_m5E8FAF84326E3192CB036BD29DCCDAF6A9861013(L_0, NULL);
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void CameraNearClipPlaneAnimation_SetValue_mCC13EE87F12433A9B6D097B59A1D1BB6E6B1CC8B (CameraNearClipPlaneAnimation_t31C2AEFB468ED028ABF534C762CB31DF85DA903F* __this, Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* ___0_target, float* ___1_value, const RuntimeMethod* method) 
{
	{
		Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* L_0 = ___0_target;
		float* L_1 = ___1_value;
		float L_2 = *((float*)L_1);
		NullCheck(L_0);
		Camera_set_nearClipPlane_m78482B5E4E0CE4C195D9CE0332AA75B2D9CCDDF6(L_0, L_2, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void CameraNearClipPlaneAnimation__ctor_mD09AD373B26E3D40BB6B2D418AED3B0535444F40 (CameraNearClipPlaneAnimation_t31C2AEFB468ED028ABF534C762CB31DF85DA903F* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&FloatPropertyAnimationComponent_1__ctor_m054E330B04F7CC58DB5069B067B6DE6AC4BEA62D_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		FloatPropertyAnimationComponent_1__ctor_m054E330B04F7CC58DB5069B067B6DE6AC4BEA62D(__this, FloatPropertyAnimationComponent_1__ctor_m054E330B04F7CC58DB5069B067B6DE6AC4BEA62D_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float CameraFarClipPlaneAnimation_GetValue_m89401C1C8CC4CEC71CBC1583DF386285FF484A92 (CameraFarClipPlaneAnimation_tBFB0D31C2A77E1DDBD510EF20D65A3B0094416CE* __this, Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* ___0_target, const RuntimeMethod* method) 
{
	{
		Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* L_0 = ___0_target;
		NullCheck(L_0);
		float L_1;
		L_1 = Camera_get_farClipPlane_m1D7128B85B5DB866F75FBE8CEBA48335716B67BD(L_0, NULL);
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void CameraFarClipPlaneAnimation_SetValue_m1F2FD5F4C72B64C1A2B6E2ADC212D9EEF5A5B9A9 (CameraFarClipPlaneAnimation_tBFB0D31C2A77E1DDBD510EF20D65A3B0094416CE* __this, Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* ___0_target, float* ___1_value, const RuntimeMethod* method) 
{
	{
		Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* L_0 = ___0_target;
		float* L_1 = ___1_value;
		float L_2 = *((float*)L_1);
		NullCheck(L_0);
		Camera_set_farClipPlane_m84EF39B09573168734613481FD979BFF31C60139(L_0, L_2, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void CameraFarClipPlaneAnimation__ctor_m053D594578414E468D4EAD4B1D808E1D4D182CAF (CameraFarClipPlaneAnimation_tBFB0D31C2A77E1DDBD510EF20D65A3B0094416CE* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&FloatPropertyAnimationComponent_1__ctor_m054E330B04F7CC58DB5069B067B6DE6AC4BEA62D_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		FloatPropertyAnimationComponent_1__ctor_m054E330B04F7CC58DB5069B067B6DE6AC4BEA62D(__this, FloatPropertyAnimationComponent_1__ctor_m054E330B04F7CC58DB5069B067B6DE6AC4BEA62D_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float CameraFieldOfViewAnimation_GetValue_m6A0CBFCC6AA1CBC5386C161D45CD5B8032952B13 (CameraFieldOfViewAnimation_t8B4975929A9FC5EB1292E0D061E461C5C500457F* __this, Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* ___0_target, const RuntimeMethod* method) 
{
	{
		Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* L_0 = ___0_target;
		NullCheck(L_0);
		float L_1;
		L_1 = Camera_get_fieldOfView_m9A93F17BBF89F496AE231C21817AFD1C1E833FBB(L_0, NULL);
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void CameraFieldOfViewAnimation_SetValue_mC1BEB8E9F3329270644F17281CA2117CB9D04A8D (CameraFieldOfViewAnimation_t8B4975929A9FC5EB1292E0D061E461C5C500457F* __this, Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* ___0_target, float* ___1_value, const RuntimeMethod* method) 
{
	{
		Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* L_0 = ___0_target;
		float* L_1 = ___1_value;
		float L_2 = *((float*)L_1);
		NullCheck(L_0);
		Camera_set_fieldOfView_m5AA9EED4D1603A1DEDBF883D9C42814B2BDEB777(L_0, L_2, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void CameraFieldOfViewAnimation__ctor_m99A290058C2B174F6996C95F2C79FE2AB8A47B2B (CameraFieldOfViewAnimation_t8B4975929A9FC5EB1292E0D061E461C5C500457F* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&FloatPropertyAnimationComponent_1__ctor_m054E330B04F7CC58DB5069B067B6DE6AC4BEA62D_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		FloatPropertyAnimationComponent_1__ctor_m054E330B04F7CC58DB5069B067B6DE6AC4BEA62D(__this, FloatPropertyAnimationComponent_1__ctor_m054E330B04F7CC58DB5069B067B6DE6AC4BEA62D_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float CameraOrthographicSizeAnimation_GetValue_mDE03A8F9B59FA5FEDF19665EAA82BE6D57D7928D (CameraOrthographicSizeAnimation_tF07CAB3E4E205B2FEC72C19488BAB814B8165969* __this, Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* ___0_target, const RuntimeMethod* method) 
{
	{
		Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* L_0 = ___0_target;
		NullCheck(L_0);
		float L_1;
		L_1 = Camera_get_orthographicSize_m7950C5627086253E02992A43ADFE59039DB473F8(L_0, NULL);
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void CameraOrthographicSizeAnimation_SetValue_m739E7F8FB599195BF88FE7ADCDA117987EC3403F (CameraOrthographicSizeAnimation_tF07CAB3E4E205B2FEC72C19488BAB814B8165969* __this, Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* ___0_target, float* ___1_value, const RuntimeMethod* method) 
{
	{
		Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* L_0 = ___0_target;
		float* L_1 = ___1_value;
		float L_2 = *((float*)L_1);
		NullCheck(L_0);
		Camera_set_orthographicSize_m76DD021032ACB3DDBD052B75EC66DCE3A7295A5C(L_0, L_2, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void CameraOrthographicSizeAnimation__ctor_m37AB2469AF8399CD0E00EC6D2EA175465C03DB07 (CameraOrthographicSizeAnimation_tF07CAB3E4E205B2FEC72C19488BAB814B8165969* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&FloatPropertyAnimationComponent_1__ctor_m054E330B04F7CC58DB5069B067B6DE6AC4BEA62D_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		FloatPropertyAnimationComponent_1__ctor_m054E330B04F7CC58DB5069B067B6DE6AC4BEA62D(__this, FloatPropertyAnimationComponent_1__ctor_m054E330B04F7CC58DB5069B067B6DE6AC4BEA62D_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D CameraRectAnimation_GetValue_mA7F60A3EA3B78869B56569046D0420722974174A (CameraRectAnimation_t27350DC7D603BA5AA49A5EEFC6FA9F8CDF070EF1* __this, Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* ___0_target, const RuntimeMethod* method) 
{
	{
		Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* L_0 = ___0_target;
		NullCheck(L_0);
		Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D L_1;
		L_1 = Camera_get_rect_m848C23B32814D1351E43F0A0110DB8ECA19C6772(L_0, NULL);
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void CameraRectAnimation_SetValue_m1B84BFE29522923A31937F3A7B5696E9672723BE (CameraRectAnimation_t27350DC7D603BA5AA49A5EEFC6FA9F8CDF070EF1* __this, Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* ___0_target, Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D* ___1_value, const RuntimeMethod* method) 
{
	{
		Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* L_0 = ___0_target;
		Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D* L_1 = ___1_value;
		Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D L_2 = (*(Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D*)L_1);
		NullCheck(L_0);
		Camera_set_rect_mA81158BC169AF8674DE240AE9460FC5A0EADBB19(L_0, L_2, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void CameraRectAnimation__ctor_m3395A4EEE94E4472024B10C7B14572658857AC8A (CameraRectAnimation_t27350DC7D603BA5AA49A5EEFC6FA9F8CDF070EF1* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&RectPropertyAnimationComponent_1__ctor_m5368181322126D16A737C4EA2ACBB2FC81D4DCED_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		RectPropertyAnimationComponent_1__ctor_m5368181322126D16A737C4EA2ACBB2FC81D4DCED(__this, RectPropertyAnimationComponent_1__ctor_m5368181322126D16A737C4EA2ACBB2FC81D4DCED_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D CameraPixelRectAnimation_GetValue_mBD00F2EFAF43C862C13BBACE0916CAC52DBA4B47 (CameraPixelRectAnimation_tFB2DC1B4364CE768560D1DB65D91C3148520EFEB* __this, Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* ___0_target, const RuntimeMethod* method) 
{
	{
		Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* L_0 = ___0_target;
		NullCheck(L_0);
		Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D L_1;
		L_1 = Camera_get_pixelRect_m5F40F5C324EB252261F66962411EE08CC4BE39E7(L_0, NULL);
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void CameraPixelRectAnimation_SetValue_m0B9FD0055D0815BF8F5116B57C447729FA347802 (CameraPixelRectAnimation_tFB2DC1B4364CE768560D1DB65D91C3148520EFEB* __this, Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* ___0_target, Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D* ___1_value, const RuntimeMethod* method) 
{
	{
		Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* L_0 = ___0_target;
		Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D* L_1 = ___1_value;
		Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D L_2 = (*(Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D*)L_1);
		NullCheck(L_0);
		Camera_set_pixelRect_m4A9504577204D4E72B39BFB637ED808B778568A5(L_0, L_2, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void CameraPixelRectAnimation__ctor_m6D28F37BF6F2751A9CB88831907C24155FBE8453 (CameraPixelRectAnimation_tFB2DC1B4364CE768560D1DB65D91C3148520EFEB* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&RectPropertyAnimationComponent_1__ctor_m5368181322126D16A737C4EA2ACBB2FC81D4DCED_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		RectPropertyAnimationComponent_1__ctor_m5368181322126D16A737C4EA2ACBB2FC81D4DCED(__this, RectPropertyAnimationComponent_1__ctor_m5368181322126D16A737C4EA2ACBB2FC81D4DCED_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Color_tD001788D726C3A7F1379BEED0260B9591F440C1F CameraBackgroundColorAnimation_GetValue_m31BE05B04683C476A5CA5050F09294848C8E12C1 (CameraBackgroundColorAnimation_tC9D3E651F449E23A3A476C80A1B13AFD549904C1* __this, Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* ___0_target, const RuntimeMethod* method) 
{
	{
		Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* L_0 = ___0_target;
		NullCheck(L_0);
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_1;
		L_1 = Camera_get_backgroundColor_m1577A81D1E6A91D7934CECB8A284AA2D4704D96F(L_0, NULL);
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void CameraBackgroundColorAnimation_SetValue_m411AC4DCB8D4ED18AE96ACFAEDD24E799E732289 (CameraBackgroundColorAnimation_tC9D3E651F449E23A3A476C80A1B13AFD549904C1* __this, Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* ___0_target, Color_tD001788D726C3A7F1379BEED0260B9591F440C1F* ___1_value, const RuntimeMethod* method) 
{
	{
		Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* L_0 = ___0_target;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F* L_1 = ___1_value;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_2 = (*(Color_tD001788D726C3A7F1379BEED0260B9591F440C1F*)L_1);
		NullCheck(L_0);
		Camera_set_backgroundColor_m036FD8C316A93A0B168ACC89AFF16D396B872138(L_0, L_2, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void CameraBackgroundColorAnimation__ctor_mA749FF07FCC9FC17152E9A3443E995347F13F0AF (CameraBackgroundColorAnimation_tC9D3E651F449E23A3A476C80A1B13AFD549904C1* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ColorPropertyAnimationComponent_1__ctor_mD6C4FA4DA1DEE210AAE15DA766F9B509CBDA935D_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		ColorPropertyAnimationComponent_1__ctor_mD6C4FA4DA1DEE210AAE15DA766F9B509CBDA935D(__this, ColorPropertyAnimationComponent_1__ctor_mD6C4FA4DA1DEE210AAE15DA766F9B509CBDA935D_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 DelayComponent_Play_m3F91995ABA2874838D9D918ABC5415DD9E521BAB (DelayComponent_t02FB33B26EE45214DF563DDEFA55F59D4CBF582D* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MotionBuilder_3_RunWithoutBinding_mAEC7D58DAFA362934E3EB208CF5CE215C5D31B52_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	MotionBuilder_3_tA23CEA9CCADDC2C36C8D976574E70A1D2A8909DC V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		float L_0 = __this->___delay;
		MotionBuilder_3_tA23CEA9CCADDC2C36C8D976574E70A1D2A8909DC L_1;
		L_1 = LMotion_Create_m884E0C4AC225B788D89F2B25D83F7EB2E7FE1E98((0.0f), (1.0f), L_0, NULL);
		V_0 = L_1;
		MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 L_2;
		L_2 = MotionBuilder_3_RunWithoutBinding_mAEC7D58DAFA362934E3EB208CF5CE215C5D31B52_inline((&V_0), MotionBuilder_3_RunWithoutBinding_mAEC7D58DAFA362934E3EB208CF5CE215C5D31B52_RuntimeMethod_var);
		return L_2;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DelayComponent_OnStop_mE97AC1EF7713C0166CC6E5491643C200FA8E36B1 (DelayComponent_t02FB33B26EE45214DF563DDEFA55F59D4CBF582D* __this, const RuntimeMethod* method) 
{
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DelayComponent__ctor_mB00D0CC724B87AF745CCC79983A445B77F3DC977 (DelayComponent_t02FB33B26EE45214DF563DDEFA55F59D4CBF582D* __this, const RuntimeMethod* method) 
{
	{
		LitMotionAnimationComponent__ctor_mD62326B8DB8585E40CFAA315FC8F3A94B8FA356B(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 EventComponent_Play_mD9F8DC42830A7BA0A418342DD035B67D27492B06 (EventComponent_t25446BA52F33198616853D8AD89403122A0400FF* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MotionBuilder_3_RunWithoutBinding_mAEC7D58DAFA362934E3EB208CF5CE215C5D31B52_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	MotionBuilder_3_tA23CEA9CCADDC2C36C8D976574E70A1D2A8909DC V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		UnityEvent_tDC2C3548799DBC91D1E3F3DE60083A66F4751977* L_0 = __this->___onPlay;
		NullCheck(L_0);
		UnityEvent_Invoke_mFBF80D59B03C30C5FE6A06F897D954ACADE061D2(L_0, NULL);
		MotionBuilder_3_tA23CEA9CCADDC2C36C8D976574E70A1D2A8909DC L_1;
		L_1 = LMotion_Create_m884E0C4AC225B788D89F2B25D83F7EB2E7FE1E98((0.0f), (1.0f), (0.0f), NULL);
		V_0 = L_1;
		MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 L_2;
		L_2 = MotionBuilder_3_RunWithoutBinding_mAEC7D58DAFA362934E3EB208CF5CE215C5D31B52_inline((&V_0), MotionBuilder_3_RunWithoutBinding_mAEC7D58DAFA362934E3EB208CF5CE215C5D31B52_RuntimeMethod_var);
		return L_2;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void EventComponent_OnStop_m4F401B9C7C96D2DDEBC8E34661147F920E6DFFE2 (EventComponent_t25446BA52F33198616853D8AD89403122A0400FF* __this, const RuntimeMethod* method) 
{
	{
		UnityEvent_tDC2C3548799DBC91D1E3F3DE60083A66F4751977* L_0 = __this->___onStop;
		NullCheck(L_0);
		UnityEvent_Invoke_mFBF80D59B03C30C5FE6A06F897D954ACADE061D2(L_0, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void EventComponent__ctor_m17720E8105FC1A33080C19C16BEA0E1EAC3BE080 (EventComponent_t25446BA52F33198616853D8AD89403122A0400FF* __this, const RuntimeMethod* method) 
{
	{
		LitMotionAnimationComponent__ctor_mD62326B8DB8585E40CFAA315FC8F3A94B8FA356B(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 PlayLitMotionAnimationComponent_Play_m7F8186399E3C5A58C964A05F673BE3EAA447E199 (PlayLitMotionAnimationComponent_t44C212AF544D647258158A4B4979FF728DB597CE* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_2_t000191B3D702D8E6114AA07D7039CA5909A6BF5E_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MotionBuilder_3_Bind_TisPlayLitMotionAnimationComponent_t44C212AF544D647258158A4B4979FF728DB597CE_m973634D4E62BFC53D209D9BEACC6B6BBF728B9E7_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PlayLitMotionAnimationComponent_U3CPlayU3Eb__1_0_m8B3A9D036CC26B921B03359877171C5B8F39CF4E_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	MotionBuilder_3_tA23CEA9CCADDC2C36C8D976574E70A1D2A8909DC V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		LitMotionAnimation_t92F1AF7CF4859CEDD166DF7FD8091FDB2C34AF4A* L_0 = __this->___target;
		NullCheck(L_0);
		LitMotionAnimation_Play_m597E40471DA063B32C89155C4A7531A18E180D44(L_0, NULL);
		MotionBuilder_3_tA23CEA9CCADDC2C36C8D976574E70A1D2A8909DC L_1;
		L_1 = LMotion_Create_m884E0C4AC225B788D89F2B25D83F7EB2E7FE1E98((0.0f), (1.0f), ((std::numeric_limits<float>::max)()), NULL);
		V_0 = L_1;
		Action_2_t000191B3D702D8E6114AA07D7039CA5909A6BF5E* L_2 = (Action_2_t000191B3D702D8E6114AA07D7039CA5909A6BF5E*)il2cpp_codegen_object_new(Action_2_t000191B3D702D8E6114AA07D7039CA5909A6BF5E_il2cpp_TypeInfo_var);
		Action_2__ctor_mC5DAE4709C46376BA513C0B0C36447130D5AC00F(L_2, __this, (intptr_t)((void*)PlayLitMotionAnimationComponent_U3CPlayU3Eb__1_0_m8B3A9D036CC26B921B03359877171C5B8F39CF4E_RuntimeMethod_var), NULL);
		MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 L_3;
		L_3 = MotionBuilder_3_Bind_TisPlayLitMotionAnimationComponent_t44C212AF544D647258158A4B4979FF728DB597CE_m973634D4E62BFC53D209D9BEACC6B6BBF728B9E7_inline((&V_0), __this, L_2, MotionBuilder_3_Bind_TisPlayLitMotionAnimationComponent_t44C212AF544D647258158A4B4979FF728DB597CE_m973634D4E62BFC53D209D9BEACC6B6BBF728B9E7_RuntimeMethod_var);
		return L_3;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlayLitMotionAnimationComponent_OnResume_m700E111782AC225D624070F53C12F87F75E6EF3F (PlayLitMotionAnimationComponent_t44C212AF544D647258158A4B4979FF728DB597CE* __this, const RuntimeMethod* method) 
{
	{
		LitMotionAnimation_t92F1AF7CF4859CEDD166DF7FD8091FDB2C34AF4A* L_0 = __this->___target;
		NullCheck(L_0);
		LitMotionAnimation_Play_m597E40471DA063B32C89155C4A7531A18E180D44(L_0, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlayLitMotionAnimationComponent_OnPause_m0E3614B876F3B5D8FA3B5798DDA995194A3FFCCF (PlayLitMotionAnimationComponent_t44C212AF544D647258158A4B4979FF728DB597CE* __this, const RuntimeMethod* method) 
{
	{
		LitMotionAnimation_t92F1AF7CF4859CEDD166DF7FD8091FDB2C34AF4A* L_0 = __this->___target;
		NullCheck(L_0);
		LitMotionAnimation_Pause_mB7C40D6C139E1AC10D0B6E293F950D32BA20CD10(L_0, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlayLitMotionAnimationComponent_OnStop_m6ADBA0EA4AA6EE4562F45A2FEA91333ED852D9A4 (PlayLitMotionAnimationComponent_t44C212AF544D647258158A4B4979FF728DB597CE* __this, const RuntimeMethod* method) 
{
	{
		LitMotionAnimation_t92F1AF7CF4859CEDD166DF7FD8091FDB2C34AF4A* L_0 = __this->___target;
		NullCheck(L_0);
		LitMotionAnimation_Stop_m05B7E202159A8AE86BBF69671025C1EB439B13BE(L_0, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlayLitMotionAnimationComponent__ctor_mAA68F59ACD285F2E25D516A081503E70C16957F7 (PlayLitMotionAnimationComponent_t44C212AF544D647258158A4B4979FF728DB597CE* __this, const RuntimeMethod* method) 
{
	{
		LitMotionAnimationComponent__ctor_mD62326B8DB8585E40CFAA315FC8F3A94B8FA356B(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void PlayLitMotionAnimationComponent_U3CPlayU3Eb__1_0_m8B3A9D036CC26B921B03359877171C5B8F39CF4E (PlayLitMotionAnimationComponent_t44C212AF544D647258158A4B4979FF728DB597CE* __this, float ___0_x, PlayLitMotionAnimationComponent_t44C212AF544D647258158A4B4979FF728DB597CE* ___1_state, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		LitMotionAnimation_t92F1AF7CF4859CEDD166DF7FD8091FDB2C34AF4A* L_0 = __this->___target;
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_1;
		L_1 = Object_op_Equality_mB6120F782D83091EF56A198FCEBCF066DB4A9605(L_0, (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*)NULL, NULL);
		if (!L_1)
		{
			goto IL_001a;
		}
	}
	{
		MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 L_2;
		L_2 = LitMotionAnimationComponent_get_TrackedHandle_m5B903D2A947941090BE2F79870D73368DD0A0606_inline(__this, NULL);
		bool L_3;
		L_3 = MotionHandleExtensions_TryComplete_m6E5C4F018592F928CBA2FD1626DBE6D25A2472E1_inline(L_2, NULL);
	}

IL_001a:
	{
		LitMotionAnimation_t92F1AF7CF4859CEDD166DF7FD8091FDB2C34AF4A* L_4 = __this->___target;
		NullCheck(L_4);
		bool L_5;
		L_5 = LitMotionAnimation_get_IsPlaying_mBC613A1EF1342DD779D8E3F5EFF1A910863E5F05(L_4, NULL);
		if (L_5)
		{
			goto IL_0033;
		}
	}
	{
		MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 L_6;
		L_6 = LitMotionAnimationComponent_get_TrackedHandle_m5B903D2A947941090BE2F79870D73368DD0A0606_inline(__this, NULL);
		bool L_7;
		L_7 = MotionHandleExtensions_TryComplete_m6E5C4F018592F928CBA2FD1626DBE6D25A2472E1_inline(L_6, NULL);
	}

IL_0033:
	{
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float MaterialFloatAnimation_GetValue_mC9281C689D2D7C7D706153F3A885E3FA2C7FD469 (MaterialFloatAnimation_t33F07CC2F42829138320058153639C8993A2E7F4* __this, Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* ___0_target, const RuntimeMethod* method) 
{
	{
		Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* L_0 = ___0_target;
		String_t* L_1 = __this->___propertyName;
		NullCheck(L_0);
		float L_2;
		L_2 = Material_GetFloat_m2A77F10E6AA13EA3FA56166EFEA897115A14FA5A(L_0, L_1, NULL);
		return L_2;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void MaterialFloatAnimation_SetValue_mF451A3A973CA19534A8DBA5996E964B1F04863E3 (MaterialFloatAnimation_t33F07CC2F42829138320058153639C8993A2E7F4* __this, Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* ___0_target, float* ___1_value, const RuntimeMethod* method) 
{
	{
		Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* L_0 = ___0_target;
		String_t* L_1 = __this->___propertyName;
		float* L_2 = ___1_value;
		float L_3 = *((float*)L_2);
		NullCheck(L_0);
		Material_SetFloat_m879CF81D740BAE6F23C9822400679F4D16365836(L_0, L_1, L_3, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void MaterialFloatAnimation__ctor_m1AE831576A3226E8C5EEBFC1271CCE58C50261F4 (MaterialFloatAnimation_t33F07CC2F42829138320058153639C8993A2E7F4* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&FloatPropertyAnimationComponent_1__ctor_mFE8113C499820E874DA39EE35599E7CD5F90335A_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralDA39A3EE5E6B4B0D3255BFEF95601890AFD80709);
		s_Il2CppMethodInitialized = true;
	}
	{
		__this->___propertyName = _stringLiteralDA39A3EE5E6B4B0D3255BFEF95601890AFD80709;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___propertyName), (void*)_stringLiteralDA39A3EE5E6B4B0D3255BFEF95601890AFD80709);
		FloatPropertyAnimationComponent_1__ctor_mFE8113C499820E874DA39EE35599E7CD5F90335A(__this, FloatPropertyAnimationComponent_1__ctor_mFE8113C499820E874DA39EE35599E7CD5F90335A_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t MaterialIntAnimation_GetValue_m7C5901F45C8D9980DB6D44F1A3728501B9AB630C (MaterialIntAnimation_t743758EB02F1C6889456973EEBF255FC2EEC0F55* __this, Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* ___0_target, const RuntimeMethod* method) 
{
	{
		Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* L_0 = ___0_target;
		String_t* L_1 = __this->___propertyName;
		NullCheck(L_0);
		int32_t L_2;
		L_2 = Material_GetInteger_m333B6E8E15DEBFCE7DAC24DF0AE0C7E1AFAEEBA6(L_0, L_1, NULL);
		return L_2;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void MaterialIntAnimation_SetValue_m6E13273971C4761F22B6BDB773851363EFE7272A (MaterialIntAnimation_t743758EB02F1C6889456973EEBF255FC2EEC0F55* __this, Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* ___0_target, int32_t* ___1_value, const RuntimeMethod* method) 
{
	{
		Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* L_0 = ___0_target;
		String_t* L_1 = __this->___propertyName;
		int32_t* L_2 = ___1_value;
		int32_t L_3 = *((int32_t*)L_2);
		NullCheck(L_0);
		Material_SetInteger_m042389110B2242425663A4484DB1D3F553D526B7(L_0, L_1, L_3, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void MaterialIntAnimation__ctor_m01E1DB0656117DCCA165CE82D93917092B94572A (MaterialIntAnimation_t743758EB02F1C6889456973EEBF255FC2EEC0F55* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IntPropertyAnimationComponent_1__ctor_m40A64E8F3FDE81C56C1CC916ED517491E25D985A_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralDA39A3EE5E6B4B0D3255BFEF95601890AFD80709);
		s_Il2CppMethodInitialized = true;
	}
	{
		__this->___propertyName = _stringLiteralDA39A3EE5E6B4B0D3255BFEF95601890AFD80709;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___propertyName), (void*)_stringLiteralDA39A3EE5E6B4B0D3255BFEF95601890AFD80709);
		IntPropertyAnimationComponent_1__ctor_m40A64E8F3FDE81C56C1CC916ED517491E25D985A(__this, IntPropertyAnimationComponent_1__ctor_m40A64E8F3FDE81C56C1CC916ED517491E25D985A_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 MaterialVectorAnimation_GetValue_m5A1BC717030106C681FF2F6332ABCB30C54FCE3D (MaterialVectorAnimation_t16760AE18B1441C66C97461AD42F886ECF16DA59* __this, Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* ___0_target, const RuntimeMethod* method) 
{
	{
		Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* L_0 = ___0_target;
		String_t* L_1 = __this->___propertyName;
		NullCheck(L_0);
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_2;
		L_2 = Material_GetVector_mA2011D4DA2CC58003AE90DBF0802CF5EE31B014D(L_0, L_1, NULL);
		return L_2;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void MaterialVectorAnimation_SetValue_m16E33B65AE12CFE8C3439C130284E5073CB55DCD (MaterialVectorAnimation_t16760AE18B1441C66C97461AD42F886ECF16DA59* __this, Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* ___0_target, Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3* ___1_value, const RuntimeMethod* method) 
{
	{
		Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* L_0 = ___0_target;
		String_t* L_1 = __this->___propertyName;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3* L_2 = ___1_value;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_3 = (*(Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3*)L_2);
		NullCheck(L_0);
		Material_SetVector_m69444B8040D955821F241113446CC8713C9E12D1(L_0, L_1, L_3, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void MaterialVectorAnimation__ctor_m969F0AF882C11E94A3B47C5E1A2099CE12CFA547 (MaterialVectorAnimation_t16760AE18B1441C66C97461AD42F886ECF16DA59* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Vector4PropertyAnimationComponent_1__ctor_m6A26AF4901445E2E2EEB09953E15E407EA5F87CD_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralDA39A3EE5E6B4B0D3255BFEF95601890AFD80709);
		s_Il2CppMethodInitialized = true;
	}
	{
		__this->___propertyName = _stringLiteralDA39A3EE5E6B4B0D3255BFEF95601890AFD80709;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___propertyName), (void*)_stringLiteralDA39A3EE5E6B4B0D3255BFEF95601890AFD80709);
		Vector4PropertyAnimationComponent_1__ctor_m6A26AF4901445E2E2EEB09953E15E407EA5F87CD(__this, Vector4PropertyAnimationComponent_1__ctor_m6A26AF4901445E2E2EEB09953E15E407EA5F87CD_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Color_tD001788D726C3A7F1379BEED0260B9591F440C1F MaterialColorAnimation_GetValue_mF7E2B9141566DFF9B4CA9B80BCC019985E1EF285 (MaterialColorAnimation_tB8E52253F7D8EBFB2B345D4524125FF9BE2233BE* __this, Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* ___0_target, const RuntimeMethod* method) 
{
	{
		Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* L_0 = ___0_target;
		String_t* L_1 = __this->___propertyName;
		NullCheck(L_0);
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_2;
		L_2 = Material_GetColor_mAC702C70081A597DD2AA2F4627B1A1C65DDF6609(L_0, L_1, NULL);
		return L_2;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void MaterialColorAnimation_SetValue_m2A399FEDA21CBAC9EA4679C4ED815B89D09F3125 (MaterialColorAnimation_tB8E52253F7D8EBFB2B345D4524125FF9BE2233BE* __this, Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* ___0_target, Color_tD001788D726C3A7F1379BEED0260B9591F440C1F* ___1_value, const RuntimeMethod* method) 
{
	{
		Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* L_0 = ___0_target;
		String_t* L_1 = __this->___propertyName;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F* L_2 = ___1_value;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_3 = (*(Color_tD001788D726C3A7F1379BEED0260B9591F440C1F*)L_2);
		NullCheck(L_0);
		Material_SetColor_mFAB32FAA44461E46FD707B34184EC080CBB3539F(L_0, L_1, L_3, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void MaterialColorAnimation__ctor_mC93EA57970134869E98AEF99D233CFD13238EE37 (MaterialColorAnimation_tB8E52253F7D8EBFB2B345D4524125FF9BE2233BE* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ColorPropertyAnimationComponent_1__ctor_mA25D66433EC9D23253C25CF32C37A352ADEB804A_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral47A3FAF17D89549FD0F0ECA7370B81F7C80DFCDE);
		s_Il2CppMethodInitialized = true;
	}
	{
		__this->___propertyName = _stringLiteral47A3FAF17D89549FD0F0ECA7370B81F7C80DFCDE;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___propertyName), (void*)_stringLiteral47A3FAF17D89549FD0F0ECA7370B81F7C80DFCDE);
		ColorPropertyAnimationComponent_1__ctor_mA25D66433EC9D23253C25CF32C37A352ADEB804A(__this, ColorPropertyAnimationComponent_1__ctor_mA25D66433EC9D23253C25CF32C37A352ADEB804A_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Color_tD001788D726C3A7F1379BEED0260B9591F440C1F SpriteRendererColorAnimation_GetValue_mFCE79EDAD5702719670F9A63EEE03C3605B6D90C (SpriteRendererColorAnimation_tB90E5AB6A31262E5049CFDA53172D0D3EEB8F766* __this, SpriteRenderer_t1DD7FE258F072E1FA87D6577BA27225892B8047B* ___0_target, const RuntimeMethod* method) 
{
	{
		SpriteRenderer_t1DD7FE258F072E1FA87D6577BA27225892B8047B* L_0 = ___0_target;
		NullCheck(L_0);
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_1;
		L_1 = SpriteRenderer_get_color_mF19DA1B83ABD9A825127D4FBED9A111FE52F1F52(L_0, NULL);
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SpriteRendererColorAnimation_SetValue_m855981BECBAF9D6601927212C0BC64287530FADE (SpriteRendererColorAnimation_tB90E5AB6A31262E5049CFDA53172D0D3EEB8F766* __this, SpriteRenderer_t1DD7FE258F072E1FA87D6577BA27225892B8047B* ___0_target, Color_tD001788D726C3A7F1379BEED0260B9591F440C1F* ___1_value, const RuntimeMethod* method) 
{
	{
		SpriteRenderer_t1DD7FE258F072E1FA87D6577BA27225892B8047B* L_0 = ___0_target;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F* L_1 = ___1_value;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_2 = (*(Color_tD001788D726C3A7F1379BEED0260B9591F440C1F*)L_1);
		NullCheck(L_0);
		SpriteRenderer_set_color_mB0EEC2845A0347E296C01C831F967731D2804546(L_0, L_2, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SpriteRendererColorAnimation__ctor_m6CE828A5751B7AB0A00DE6FA07844059C9FA3B19 (SpriteRendererColorAnimation_tB90E5AB6A31262E5049CFDA53172D0D3EEB8F766* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ColorPropertyAnimationComponent_1__ctor_mEF8FD8C1434534785B3672D5C24EF181830F452A_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		ColorPropertyAnimationComponent_1__ctor_mEF8FD8C1434534785B3672D5C24EF181830F452A(__this, ColorPropertyAnimationComponent_1__ctor_mEF8FD8C1434534785B3672D5C24EF181830F452A_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float VolumeWeightAnimation_GetValue_mE05D0E9F954AC84043852EA736BF1C143D9344D3 (VolumeWeightAnimation_t748E9F2603B2964DAF687456ECE284BB9FD23CF7* __this, Volume_t7CAAEA22D7F13A50FAE114DE7A6986FEAC837377* ___0_target, const RuntimeMethod* method) 
{
	{
		Volume_t7CAAEA22D7F13A50FAE114DE7A6986FEAC837377* L_0 = ___0_target;
		NullCheck(L_0);
		float L_1 = L_0->___weight;
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VolumeWeightAnimation_SetValue_mF6C4D9429A04BD51E156C42663EEB8C6BFC55019 (VolumeWeightAnimation_t748E9F2603B2964DAF687456ECE284BB9FD23CF7* __this, Volume_t7CAAEA22D7F13A50FAE114DE7A6986FEAC837377* ___0_target, float* ___1_value, const RuntimeMethod* method) 
{
	{
		Volume_t7CAAEA22D7F13A50FAE114DE7A6986FEAC837377* L_0 = ___0_target;
		float* L_1 = ___1_value;
		float L_2 = *((float*)L_1);
		NullCheck(L_0);
		L_0->___weight = L_2;
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void VolumeWeightAnimation__ctor_mBDE0BF73D81E3F87A295CB6B8FDF51A0D9C0CAB9 (VolumeWeightAnimation_t748E9F2603B2964DAF687456ECE284BB9FD23CF7* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&FloatPropertyAnimationComponent_1__ctor_m100EED231E7D0285C9CE776FC962DB8920AC5A3D_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		FloatPropertyAnimationComponent_1__ctor_m100EED231E7D0285C9CE776FC962DB8920AC5A3D(__this, FloatPropertyAnimationComponent_1__ctor_m100EED231E7D0285C9CE776FC962DB8920AC5A3D_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Rigidbody2DPositionAnimation__ctor_m09D70B6A6BE5891C63B89E9544F054507AEADAC0 (Rigidbody2DPositionAnimation_t6384BF0D1B3C33236307B689CC87FEE9B1B1EC20* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Rigidbody2DPositionAnimationBase_2__ctor_m91DBE4A1F747FD0DE57E36D5FF7A6C61AF65B093_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Rigidbody2DPositionAnimationBase_2__ctor_m91DBE4A1F747FD0DE57E36D5FF7A6C61AF65B093(__this, Rigidbody2DPositionAnimationBase_2__ctor_m91DBE4A1F747FD0DE57E36D5FF7A6C61AF65B093_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Rigidbody2DPositionPunchAnimation__ctor_mEB018B7047644AACA9C76896312F4F08B21C99DA (Rigidbody2DPositionPunchAnimation_t4CE478F8B49D2CE9AECA0EF49DE21F695BC8AE2E* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Rigidbody2DPositionAnimationBase_2__ctor_m03A6CA89E10DDD75104A6AFAD90BAA664F55B30D_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Rigidbody2DPositionAnimationBase_2__ctor_m03A6CA89E10DDD75104A6AFAD90BAA664F55B30D(__this, Rigidbody2DPositionAnimationBase_2__ctor_m03A6CA89E10DDD75104A6AFAD90BAA664F55B30D_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Rigidbody2DPositionShakeAnimation__ctor_m165BB68F36998824D0F29111A66A460529795F38 (Rigidbody2DPositionShakeAnimation_t83C832CD3C8323CF36DE5786F713CC66E190F2B5* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Rigidbody2DPositionAnimationBase_2__ctor_mC7824A99A66657C2E4F29844CE1206368D04DFA7_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Rigidbody2DPositionAnimationBase_2__ctor_mC7824A99A66657C2E4F29844CE1206368D04DFA7(__this, Rigidbody2DPositionAnimationBase_2__ctor_mC7824A99A66657C2E4F29844CE1206368D04DFA7_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Rigidbody2DRotationAnimation__ctor_m473389D364DEA5868735B2DD3D6AC832631A13FC (Rigidbody2DRotationAnimation_t1D9950D3CD8D64136141526DA47B1DD6544CAA9E* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Rigidbody2DRotationAnimationBase_2__ctor_mEEF666BD97FF8470CDFDC86B28CC270896E97D92_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Rigidbody2DRotationAnimationBase_2__ctor_mEEF666BD97FF8470CDFDC86B28CC270896E97D92(__this, Rigidbody2DRotationAnimationBase_2__ctor_mEEF666BD97FF8470CDFDC86B28CC270896E97D92_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Rigidbody2DRotationPunchAnimation__ctor_m405B03D393E2A81609644454D3684A88B4B66F57 (Rigidbody2DRotationPunchAnimation_tF9792F42F38E6B7DBDF861BCA13DC95A15E25FF0* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Rigidbody2DRotationAnimationBase_2__ctor_m1EC94B27FCD01301E526332F2D4F024B8AF31C19_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Rigidbody2DRotationAnimationBase_2__ctor_m1EC94B27FCD01301E526332F2D4F024B8AF31C19(__this, Rigidbody2DRotationAnimationBase_2__ctor_m1EC94B27FCD01301E526332F2D4F024B8AF31C19_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Rigidbody2DRotationShakeAnimation__ctor_mD53EA361A92A6AB113DE2E2FE0FC615E4EA96D43 (Rigidbody2DRotationShakeAnimation_tFDF23B7A5FB3DDD00CB5060A5DCFC336B0965B8B* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Rigidbody2DRotationAnimationBase_2__ctor_mB0C521DFA77F00B33FA186775B0FBE7C6722E364_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Rigidbody2DRotationAnimationBase_2__ctor_mB0C521DFA77F00B33FA186775B0FBE7C6722E364(__this, Rigidbody2DRotationAnimationBase_2__ctor_mB0C521DFA77F00B33FA186775B0FBE7C6722E364_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RigidbodyPositionAnimation__ctor_m1882F91F2244565437466E2B533C194619CA1E6D (RigidbodyPositionAnimation_tD5E8DC790768D7450851E943B0375C358EDE3153* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&RigidbodyPositionAnimationBase_2__ctor_mE32805C6840A3AE1B3D1E29B526B8B6207579FE7_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		RigidbodyPositionAnimationBase_2__ctor_mE32805C6840A3AE1B3D1E29B526B8B6207579FE7(__this, RigidbodyPositionAnimationBase_2__ctor_mE32805C6840A3AE1B3D1E29B526B8B6207579FE7_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RigidbodyPositionPunchAnimation__ctor_m46F5FDC977E89382B0D4D6EAE5109AE28CCE73AA (RigidbodyPositionPunchAnimation_t1E1EF5E7F5D33CF09BBA49998CEEBBEE50409E64* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&RigidbodyPositionAnimationBase_2__ctor_m5C9917A6D03F7F4B4EE34A03603193C670CFCFCA_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		RigidbodyPositionAnimationBase_2__ctor_m5C9917A6D03F7F4B4EE34A03603193C670CFCFCA(__this, RigidbodyPositionAnimationBase_2__ctor_m5C9917A6D03F7F4B4EE34A03603193C670CFCFCA_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RigidbodyPositionShakeAnimation__ctor_m1DE452B65A8A8A93D3A7C847DEB570047E6FAE17 (RigidbodyPositionShakeAnimation_t835F38965062CA69323D55FD17352596B5D9E83F* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&RigidbodyPositionAnimationBase_2__ctor_m52D79A66585233D8FF78F254A11F0CD5814D06DC_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		RigidbodyPositionAnimationBase_2__ctor_m52D79A66585233D8FF78F254A11F0CD5814D06DC(__this, RigidbodyPositionAnimationBase_2__ctor_m52D79A66585233D8FF78F254A11F0CD5814D06DC_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RigidbodyRotationAnimation__ctor_mA94BF194AD0899B93733595768AC4B6CCCAF2AEE (RigidbodyRotationAnimation_t78187C7A9D397C810CD2674EEA4C026C29A6383D* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&RigidbodyRotationAnimationBase_2__ctor_m9E52DDCFD6232030E2B638C3FA5DF2C4222D89B6_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		RigidbodyRotationAnimationBase_2__ctor_m9E52DDCFD6232030E2B638C3FA5DF2C4222D89B6(__this, RigidbodyRotationAnimationBase_2__ctor_m9E52DDCFD6232030E2B638C3FA5DF2C4222D89B6_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RigidbodyRotationPunchAnimation__ctor_m5A9ED12A880FEA940F27A2B250D24D85B1122F76 (RigidbodyRotationPunchAnimation_tD4DA1E4686D1038ED68A9AA90A906FCD96A6F8FE* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&RigidbodyRotationAnimationBase_2__ctor_m3D1D00C56767E1ECEF1B20F1722F3E83163930A7_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		RigidbodyRotationAnimationBase_2__ctor_m3D1D00C56767E1ECEF1B20F1722F3E83163930A7(__this, RigidbodyRotationAnimationBase_2__ctor_m3D1D00C56767E1ECEF1B20F1722F3E83163930A7_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RigidbodyRotationShakeAnimation__ctor_m73DF76DD05E3E869D070CF02631E97B98D32D78B (RigidbodyRotationShakeAnimation_t84A7DABACAA48B32D9F74051B32E5371DD1E5861* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&RigidbodyRotationAnimationBase_2__ctor_m0FCEADC70BB2100B197645B518E21182B2D32928_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		RigidbodyRotationAnimationBase_2__ctor_m0FCEADC70BB2100B197645B518E21182B2D32928(__this, RigidbodyRotationAnimationBase_2__ctor_m0FCEADC70BB2100B197645B518E21182B2D32928_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR FixedString512Bytes_t0C425B0F2C07FEA1642C32BF8559116DF2BFF50E TMPTextAnimation_GetValue_m1A9253407828CD9A271E728CE5B8742BB88FD226 (TMPTextAnimation_t2E906E9232E91180CEC49B9D9C8EF37EE8908AF2* __this, TMP_Text_tE8D677872D43AD4B2AAF0D6101692A17D0B251A9* ___0_target, const RuntimeMethod* method) 
{
	{
		TMP_Text_tE8D677872D43AD4B2AAF0D6101692A17D0B251A9* L_0 = ___0_target;
		NullCheck(L_0);
		String_t* L_1;
		L_1 = VirtualFuncInvoker0< String_t* >::Invoke(65, L_0);
		FixedString512Bytes_t0C425B0F2C07FEA1642C32BF8559116DF2BFF50E L_2;
		L_2 = FixedString512Bytes_op_Implicit_mCF055F8B2FB98005951417E9FE994E3D230F58E4(L_1, NULL);
		return L_2;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TMPTextAnimation_SetValue_m6EB1FAAD752E90EDDD48539959D5FEF6AF38295C (TMPTextAnimation_t2E906E9232E91180CEC49B9D9C8EF37EE8908AF2* __this, TMP_Text_tE8D677872D43AD4B2AAF0D6101692A17D0B251A9* ___0_target, FixedString512Bytes_t0C425B0F2C07FEA1642C32BF8559116DF2BFF50E* ___1_value, const RuntimeMethod* method) 
{
	FixedString512Bytes_t0C425B0F2C07FEA1642C32BF8559116DF2BFF50E V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		TMP_Text_tE8D677872D43AD4B2AAF0D6101692A17D0B251A9* L_0 = ___0_target;
		FixedString512Bytes_t0C425B0F2C07FEA1642C32BF8559116DF2BFF50E* L_1 = ___1_value;
		FixedString512Bytes_t0C425B0F2C07FEA1642C32BF8559116DF2BFF50E L_2 = (*(FixedString512Bytes_t0C425B0F2C07FEA1642C32BF8559116DF2BFF50E*)L_1);
		V_0 = L_2;
		String_t* L_3;
		L_3 = FixedString512Bytes_ToString_m1A47583FB34608DBEEDC65F9CA6E7B8E7930233F((&V_0), NULL);
		NullCheck(L_0);
		VirtualActionInvoker1< String_t* >::Invoke(66, L_0, L_3);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TMPTextAnimation__ctor_m083C2B177D26F479A531C951A201DA679D1E93D9 (TMPTextAnimation_t2E906E9232E91180CEC49B9D9C8EF37EE8908AF2* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&FixedString512BytesPropertyAnimationComponent_1__ctor_mA507E727510CA75D6217FB4C246A0F6F41FC8951_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		FixedString512BytesPropertyAnimationComponent_1__ctor_mA507E727510CA75D6217FB4C246A0F6F41FC8951(__this, FixedString512BytesPropertyAnimationComponent_1__ctor_mA507E727510CA75D6217FB4C246A0F6F41FC8951_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float TMPTextCharacterSpacingAnimation_GetValue_mC757EF527CE4D02D69BCFCECD6054C55BAB76718 (TMPTextCharacterSpacingAnimation_t26A3F5C492B9F1B71D70B24EACEC606FA72228BD* __this, TMP_Text_tE8D677872D43AD4B2AAF0D6101692A17D0B251A9* ___0_target, const RuntimeMethod* method) 
{
	{
		TMP_Text_tE8D677872D43AD4B2AAF0D6101692A17D0B251A9* L_0 = ___0_target;
		NullCheck(L_0);
		float L_1;
		L_1 = TMP_Text_get_characterSpacing_m48A3B73EFBF47B5227D2BB4816FCFF628254C8FB_inline(L_0, NULL);
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TMPTextCharacterSpacingAnimation_SetValue_mCDA746D1BB0C80C50CA7103763ED14BCA4A8C6DE (TMPTextCharacterSpacingAnimation_t26A3F5C492B9F1B71D70B24EACEC606FA72228BD* __this, TMP_Text_tE8D677872D43AD4B2AAF0D6101692A17D0B251A9* ___0_target, float* ___1_value, const RuntimeMethod* method) 
{
	{
		TMP_Text_tE8D677872D43AD4B2AAF0D6101692A17D0B251A9* L_0 = ___0_target;
		float* L_1 = ___1_value;
		float L_2 = *((float*)L_1);
		NullCheck(L_0);
		TMP_Text_set_characterSpacing_mDCD34D244A502CA21CEB817E1F4CAC5BC6CCBA63(L_0, L_2, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TMPTextCharacterSpacingAnimation__ctor_m694CA6949B6B3E5D7067E5202CB5EB5D24AC9DC8 (TMPTextCharacterSpacingAnimation_t26A3F5C492B9F1B71D70B24EACEC606FA72228BD* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&FloatPropertyAnimationComponent_1__ctor_m76B1B70CB536FF9DAD16FE9E20EE6EEDE9F0DC56_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		FloatPropertyAnimationComponent_1__ctor_m76B1B70CB536FF9DAD16FE9E20EE6EEDE9F0DC56(__this, FloatPropertyAnimationComponent_1__ctor_m76B1B70CB536FF9DAD16FE9E20EE6EEDE9F0DC56_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float TMPTextWordSpacingAnimation_GetValue_mCA8CBCC9E379337CAFA6D14E73428567D5F363CE (TMPTextWordSpacingAnimation_tF2EA00511F45C3E0D5CBDD069E5E08A242CC59DE* __this, TMP_Text_tE8D677872D43AD4B2AAF0D6101692A17D0B251A9* ___0_target, const RuntimeMethod* method) 
{
	{
		TMP_Text_tE8D677872D43AD4B2AAF0D6101692A17D0B251A9* L_0 = ___0_target;
		NullCheck(L_0);
		float L_1;
		L_1 = TMP_Text_get_wordSpacing_mF3DF1445C78E06195904FCF0293E63654C527D33_inline(L_0, NULL);
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TMPTextWordSpacingAnimation_SetValue_m4D8AA237A51CC69FB0DEA75C46FCBBCA6C3F1937 (TMPTextWordSpacingAnimation_tF2EA00511F45C3E0D5CBDD069E5E08A242CC59DE* __this, TMP_Text_tE8D677872D43AD4B2AAF0D6101692A17D0B251A9* ___0_target, float* ___1_value, const RuntimeMethod* method) 
{
	{
		TMP_Text_tE8D677872D43AD4B2AAF0D6101692A17D0B251A9* L_0 = ___0_target;
		float* L_1 = ___1_value;
		float L_2 = *((float*)L_1);
		NullCheck(L_0);
		TMP_Text_set_wordSpacing_m319C51E318DBC91F236F3CC65ED24787903F7E1E(L_0, L_2, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TMPTextWordSpacingAnimation__ctor_m65DC5AAC1A7577B22703EF07250A40E497F653B7 (TMPTextWordSpacingAnimation_tF2EA00511F45C3E0D5CBDD069E5E08A242CC59DE* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&FloatPropertyAnimationComponent_1__ctor_m76B1B70CB536FF9DAD16FE9E20EE6EEDE9F0DC56_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		FloatPropertyAnimationComponent_1__ctor_m76B1B70CB536FF9DAD16FE9E20EE6EEDE9F0DC56(__this, FloatPropertyAnimationComponent_1__ctor_m76B1B70CB536FF9DAD16FE9E20EE6EEDE9F0DC56_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float TMPTextLineSpacingAnimation_GetValue_m05233598FE33596D89413A1FD01D00743A38DB7B (TMPTextLineSpacingAnimation_t1BDC9F5B32C3685301B009A9BFF9CB76407BCDA3* __this, TMP_Text_tE8D677872D43AD4B2AAF0D6101692A17D0B251A9* ___0_target, const RuntimeMethod* method) 
{
	{
		TMP_Text_tE8D677872D43AD4B2AAF0D6101692A17D0B251A9* L_0 = ___0_target;
		NullCheck(L_0);
		float L_1;
		L_1 = TMP_Text_get_lineSpacing_m7481D705EAD920B8D143D19A270D44CDABDAA251_inline(L_0, NULL);
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TMPTextLineSpacingAnimation_SetValue_m373729AE91AB2555B60C55B56B1E9F26FF843231 (TMPTextLineSpacingAnimation_t1BDC9F5B32C3685301B009A9BFF9CB76407BCDA3* __this, TMP_Text_tE8D677872D43AD4B2AAF0D6101692A17D0B251A9* ___0_target, float* ___1_value, const RuntimeMethod* method) 
{
	{
		TMP_Text_tE8D677872D43AD4B2AAF0D6101692A17D0B251A9* L_0 = ___0_target;
		float* L_1 = ___1_value;
		float L_2 = *((float*)L_1);
		NullCheck(L_0);
		TMP_Text_set_lineSpacing_m1BA54B315F7472AE0E7B721CA7481016643591A7(L_0, L_2, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TMPTextLineSpacingAnimation__ctor_m9260FDF44FCD3EA447F6D86F076B30A8286AAE3F (TMPTextLineSpacingAnimation_t1BDC9F5B32C3685301B009A9BFF9CB76407BCDA3* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&FloatPropertyAnimationComponent_1__ctor_m76B1B70CB536FF9DAD16FE9E20EE6EEDE9F0DC56_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		FloatPropertyAnimationComponent_1__ctor_m76B1B70CB536FF9DAD16FE9E20EE6EEDE9F0DC56(__this, FloatPropertyAnimationComponent_1__ctor_m76B1B70CB536FF9DAD16FE9E20EE6EEDE9F0DC56_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float TMPTextParagraphSpacingAnimation_GetValue_mD6E1E6C3A03BA44CCF1200A39E373B5809B6E91A (TMPTextParagraphSpacingAnimation_t6FB4A8093F859DD882F9616613B76258C059AF93* __this, TMP_Text_tE8D677872D43AD4B2AAF0D6101692A17D0B251A9* ___0_target, const RuntimeMethod* method) 
{
	{
		TMP_Text_tE8D677872D43AD4B2AAF0D6101692A17D0B251A9* L_0 = ___0_target;
		NullCheck(L_0);
		float L_1;
		L_1 = TMP_Text_get_paragraphSpacing_mCCBC792CAE59958E92EB04B8E636AA2066534713_inline(L_0, NULL);
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TMPTextParagraphSpacingAnimation_SetValue_mBAE2A40EC1BAE81D9998F75D82568878CCAA5ED6 (TMPTextParagraphSpacingAnimation_t6FB4A8093F859DD882F9616613B76258C059AF93* __this, TMP_Text_tE8D677872D43AD4B2AAF0D6101692A17D0B251A9* ___0_target, float* ___1_value, const RuntimeMethod* method) 
{
	{
		TMP_Text_tE8D677872D43AD4B2AAF0D6101692A17D0B251A9* L_0 = ___0_target;
		float* L_1 = ___1_value;
		float L_2 = *((float*)L_1);
		NullCheck(L_0);
		TMP_Text_set_paragraphSpacing_m69921E35B44DE397FE604590913CAFB7DBFBAF30(L_0, L_2, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TMPTextParagraphSpacingAnimation__ctor_mB84E4F61B30568A0B804F701F84511453C494CCB (TMPTextParagraphSpacingAnimation_t6FB4A8093F859DD882F9616613B76258C059AF93* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&FloatPropertyAnimationComponent_1__ctor_m76B1B70CB536FF9DAD16FE9E20EE6EEDE9F0DC56_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		FloatPropertyAnimationComponent_1__ctor_m76B1B70CB536FF9DAD16FE9E20EE6EEDE9F0DC56(__this, FloatPropertyAnimationComponent_1__ctor_m76B1B70CB536FF9DAD16FE9E20EE6EEDE9F0DC56_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float TMPTextFontSizeAnimation_GetValue_m7C458EA58BEB38C0B4315EE9BE5F776BD9DDECF4 (TMPTextFontSizeAnimation_t3B7B223B80DA067FF5B20970560EED95A74E0A06* __this, TMP_Text_tE8D677872D43AD4B2AAF0D6101692A17D0B251A9* ___0_target, const RuntimeMethod* method) 
{
	{
		TMP_Text_tE8D677872D43AD4B2AAF0D6101692A17D0B251A9* L_0 = ___0_target;
		NullCheck(L_0);
		float L_1;
		L_1 = TMP_Text_get_fontSize_m13A8365A56EA2B726EAD826B4A69C8918A528731_inline(L_0, NULL);
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TMPTextFontSizeAnimation_SetValue_m9A341ADAC9A8D5A60F28B9C82AAEDDAE63EEB703 (TMPTextFontSizeAnimation_t3B7B223B80DA067FF5B20970560EED95A74E0A06* __this, TMP_Text_tE8D677872D43AD4B2AAF0D6101692A17D0B251A9* ___0_target, float* ___1_value, const RuntimeMethod* method) 
{
	{
		TMP_Text_tE8D677872D43AD4B2AAF0D6101692A17D0B251A9* L_0 = ___0_target;
		float* L_1 = ___1_value;
		float L_2 = *((float*)L_1);
		NullCheck(L_0);
		TMP_Text_set_fontSize_m1C3A3BA2BC88E5E1D89375FD35A0AA91E75D3AAD(L_0, L_2, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TMPTextFontSizeAnimation__ctor_m2350D9664C3403E9BB28D99D2843C75FE1D610FC (TMPTextFontSizeAnimation_t3B7B223B80DA067FF5B20970560EED95A74E0A06* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&FloatPropertyAnimationComponent_1__ctor_m76B1B70CB536FF9DAD16FE9E20EE6EEDE9F0DC56_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		FloatPropertyAnimationComponent_1__ctor_m76B1B70CB536FF9DAD16FE9E20EE6EEDE9F0DC56(__this, FloatPropertyAnimationComponent_1__ctor_m76B1B70CB536FF9DAD16FE9E20EE6EEDE9F0DC56_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Color_tD001788D726C3A7F1379BEED0260B9591F440C1F TMPTextColorAnimation_GetValue_mF3C30D6348C84401B338B08F900239E852FE3584 (TMPTextColorAnimation_t47399F6153BA6F7A67AD03F194FF931600B1DAC4* __this, TMP_Text_tE8D677872D43AD4B2AAF0D6101692A17D0B251A9* ___0_target, const RuntimeMethod* method) 
{
	{
		TMP_Text_tE8D677872D43AD4B2AAF0D6101692A17D0B251A9* L_0 = ___0_target;
		NullCheck(L_0);
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_1;
		L_1 = VirtualFuncInvoker0< Color_tD001788D726C3A7F1379BEED0260B9591F440C1F >::Invoke(22, L_0);
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TMPTextColorAnimation_SetValue_m3D841E628CDCBD935481E73B4C4029619DC21139 (TMPTextColorAnimation_t47399F6153BA6F7A67AD03F194FF931600B1DAC4* __this, TMP_Text_tE8D677872D43AD4B2AAF0D6101692A17D0B251A9* ___0_target, Color_tD001788D726C3A7F1379BEED0260B9591F440C1F* ___1_value, const RuntimeMethod* method) 
{
	{
		TMP_Text_tE8D677872D43AD4B2AAF0D6101692A17D0B251A9* L_0 = ___0_target;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F* L_1 = ___1_value;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_2 = (*(Color_tD001788D726C3A7F1379BEED0260B9591F440C1F*)L_1);
		NullCheck(L_0);
		VirtualActionInvoker1< Color_tD001788D726C3A7F1379BEED0260B9591F440C1F >::Invoke(23, L_0, L_2);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TMPTextColorAnimation__ctor_m14D4994498C2AA1A521FFC8C0C2664412709D72C (TMPTextColorAnimation_t47399F6153BA6F7A67AD03F194FF931600B1DAC4* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ColorPropertyAnimationComponent_1__ctor_m8BE4FDFE086E6931EACF9112681328B5A24A1915_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		ColorPropertyAnimationComponent_1__ctor_m8BE4FDFE086E6931EACF9112681328B5A24A1915(__this, ColorPropertyAnimationComponent_1__ctor_m8BE4FDFE086E6931EACF9112681328B5A24A1915_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float TMPTextColorAlphaAnimation_GetValue_mD62B97CD9047FAA1366060EE9AA64CBFBECC011E (TMPTextColorAlphaAnimation_t25988B988761C4836D8EEC13DB51674914D70B98* __this, TMP_Text_tE8D677872D43AD4B2AAF0D6101692A17D0B251A9* ___0_target, const RuntimeMethod* method) 
{
	{
		TMP_Text_tE8D677872D43AD4B2AAF0D6101692A17D0B251A9* L_0 = ___0_target;
		NullCheck(L_0);
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_1;
		L_1 = VirtualFuncInvoker0< Color_tD001788D726C3A7F1379BEED0260B9591F440C1F >::Invoke(22, L_0);
		float L_2 = L_1.___a;
		return L_2;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TMPTextColorAlphaAnimation_SetValue_mE3A411F9E6E47F00D334E7958D8671C20B2D486F (TMPTextColorAlphaAnimation_t25988B988761C4836D8EEC13DB51674914D70B98* __this, TMP_Text_tE8D677872D43AD4B2AAF0D6101692A17D0B251A9* ___0_target, float* ___1_value, const RuntimeMethod* method) 
{
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		TMP_Text_tE8D677872D43AD4B2AAF0D6101692A17D0B251A9* L_0 = ___0_target;
		NullCheck(L_0);
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_1;
		L_1 = VirtualFuncInvoker0< Color_tD001788D726C3A7F1379BEED0260B9591F440C1F >::Invoke(22, L_0);
		V_0 = L_1;
		float* L_2 = ___1_value;
		float L_3 = *((float*)L_2);
		(&V_0)->___a = L_3;
		TMP_Text_tE8D677872D43AD4B2AAF0D6101692A17D0B251A9* L_4 = ___0_target;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_5 = V_0;
		NullCheck(L_4);
		VirtualActionInvoker1< Color_tD001788D726C3A7F1379BEED0260B9591F440C1F >::Invoke(23, L_4, L_5);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TMPTextColorAlphaAnimation__ctor_m910A471AE89548D7C868C040B74895C7007035A2 (TMPTextColorAlphaAnimation_t25988B988761C4836D8EEC13DB51674914D70B98* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&FloatPropertyAnimationComponent_1__ctor_m76B1B70CB536FF9DAD16FE9E20EE6EEDE9F0DC56_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		FloatPropertyAnimationComponent_1__ctor_m76B1B70CB536FF9DAD16FE9E20EE6EEDE9F0DC56(__this, FloatPropertyAnimationComponent_1__ctor_m76B1B70CB536FF9DAD16FE9E20EE6EEDE9F0DC56_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TransformPositionAnimation__ctor_mDAE0A5F3015E2316E2FD359B01AB10B030074CE6 (TransformPositionAnimation_tA672FD35BC9E5A38D23FC09076A16FDBCD8AD162* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TransformPositionAnimationBase_2__ctor_mEEF0C512CF0CE9AA65F5F0984ADC7F2929384839_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		TransformPositionAnimationBase_2__ctor_mEEF0C512CF0CE9AA65F5F0984ADC7F2929384839(__this, TransformPositionAnimationBase_2__ctor_mEEF0C512CF0CE9AA65F5F0984ADC7F2929384839_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TransformPositionPunchAnimation__ctor_mD17E2FDB3BFC1B16A74E041E363565B821000411 (TransformPositionPunchAnimation_t111BCFAE3984E761BF110D887515F103ACA6BEC7* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TransformPositionAnimationBase_2__ctor_m8BDC2ADB7E9BD450BE42D78257850F220AABFF3C_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		TransformPositionAnimationBase_2__ctor_m8BDC2ADB7E9BD450BE42D78257850F220AABFF3C(__this, TransformPositionAnimationBase_2__ctor_m8BDC2ADB7E9BD450BE42D78257850F220AABFF3C_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TransformPositionShakeAnimation__ctor_m260221C7FCF468E50E6029CE02117E50DAB7BA36 (TransformPositionShakeAnimation_t9BC6FA5232E8683B7CE5365D2953ADB875C4402A* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TransformPositionAnimationBase_2__ctor_mFB3FBCF99341AF765414DAA4F6EA24237DEB4D97_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		TransformPositionAnimationBase_2__ctor_mFB3FBCF99341AF765414DAA4F6EA24237DEB4D97(__this, TransformPositionAnimationBase_2__ctor_mFB3FBCF99341AF765414DAA4F6EA24237DEB4D97_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TransformRotationAnimation__ctor_mC409D029250FC3AC1D7DB420A274A8B85B736592 (TransformRotationAnimation_t181A786C31B7C8AC2C83152CCC24C0C82EED1785* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TransformRotationAnimationBase_2__ctor_m386B72ED5CBE026112F0016776A5DEBFE6650100_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		TransformRotationAnimationBase_2__ctor_m386B72ED5CBE026112F0016776A5DEBFE6650100(__this, TransformRotationAnimationBase_2__ctor_m386B72ED5CBE026112F0016776A5DEBFE6650100_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TransformRotationPunchAnimation__ctor_m75EF145CEA3F9D335EF7B81A4537FF6266E9676A (TransformRotationPunchAnimation_tDD2CD304B79268D5D844374DF14C4322733E3A01* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TransformRotationAnimationBase_2__ctor_mF21C092DF3DF33EE4D2AFD09A711D5620318C4D7_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		TransformRotationAnimationBase_2__ctor_mF21C092DF3DF33EE4D2AFD09A711D5620318C4D7(__this, TransformRotationAnimationBase_2__ctor_mF21C092DF3DF33EE4D2AFD09A711D5620318C4D7_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TransformRotationShakeAnimation__ctor_m1B7A4DE7414129E250D6B664CEB82CDCDAF852F0 (TransformRotationShakeAnimation_t1817882F0893684DF23BECBADBFD7E1458A69E1F* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TransformRotationAnimationBase_2__ctor_m7703FEF1A7410371F3F55026E386C1440E06B4F7_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		TransformRotationAnimationBase_2__ctor_m7703FEF1A7410371F3F55026E386C1440E06B4F7(__this, TransformRotationAnimationBase_2__ctor_m7703FEF1A7410371F3F55026E386C1440E06B4F7_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TransformScaleAnimation__ctor_m334568E452D0775E3C56D11DA2DD189A705711DB (TransformScaleAnimation_tEEEC832BD42C59D1A08B30347D67DF8C4839E102* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TransformScaleAnimationBase_2__ctor_m1A5814121EBBC7C79C79CA062A524047F5EBB54C_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		TransformScaleAnimationBase_2__ctor_m1A5814121EBBC7C79C79CA062A524047F5EBB54C(__this, TransformScaleAnimationBase_2__ctor_m1A5814121EBBC7C79C79CA062A524047F5EBB54C_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TransformScalePunchAnimation__ctor_m8FDA121800FCBF9743D192CAF49EE5ACCE52E809 (TransformScalePunchAnimation_t8C68CC300945C3F4CB4BCDA907D785A600080DDA* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TransformScaleAnimationBase_2__ctor_mCCF29084DD333FEFAAEB2BDE353FB40A457694FD_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		TransformScaleAnimationBase_2__ctor_mCCF29084DD333FEFAAEB2BDE353FB40A457694FD(__this, TransformScaleAnimationBase_2__ctor_mCCF29084DD333FEFAAEB2BDE353FB40A457694FD_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TransformScaleShakeAnimation__ctor_m743D01D1659D867B3C13B8E01B72D261D3BF73FD (TransformScaleShakeAnimation_tCEDC563ACEF258A7C21F2FCBF03F28648A065DA3* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TransformScaleAnimationBase_2__ctor_m942D49424390A604708E7F41879DA29A8A6C3DE5_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		TransformScaleAnimationBase_2__ctor_m942D49424390A604708E7F41879DA29A8A6C3DE5(__this, TransformScaleAnimationBase_2__ctor_m942D49424390A604708E7F41879DA29A8A6C3DE5_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR FixedString512Bytes_t0C425B0F2C07FEA1642C32BF8559116DF2BFF50E TextAnimation_GetValue_m8600BFFA502918A0F2142FFF08B71905EB58CA6F (TextAnimation_t9777A34576E8E54B799AA1238A3B2D52E559A263* __this, Text_tD60B2346DAA6666BF0D822FF607F0B220C2B9E62* ___0_target, const RuntimeMethod* method) 
{
	{
		Text_tD60B2346DAA6666BF0D822FF607F0B220C2B9E62* L_0 = ___0_target;
		NullCheck(L_0);
		String_t* L_1;
		L_1 = VirtualFuncInvoker0< String_t* >::Invoke(74, L_0);
		FixedString512Bytes_t0C425B0F2C07FEA1642C32BF8559116DF2BFF50E L_2;
		L_2 = FixedString512Bytes_op_Implicit_mCF055F8B2FB98005951417E9FE994E3D230F58E4(L_1, NULL);
		return L_2;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TextAnimation_SetValue_m33F9E42F4BF1C6F4BD4E29A5109A26FAE6F8F8BC (TextAnimation_t9777A34576E8E54B799AA1238A3B2D52E559A263* __this, Text_tD60B2346DAA6666BF0D822FF607F0B220C2B9E62* ___0_target, FixedString512Bytes_t0C425B0F2C07FEA1642C32BF8559116DF2BFF50E* ___1_value, const RuntimeMethod* method) 
{
	FixedString512Bytes_t0C425B0F2C07FEA1642C32BF8559116DF2BFF50E V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		Text_tD60B2346DAA6666BF0D822FF607F0B220C2B9E62* L_0 = ___0_target;
		FixedString512Bytes_t0C425B0F2C07FEA1642C32BF8559116DF2BFF50E* L_1 = ___1_value;
		FixedString512Bytes_t0C425B0F2C07FEA1642C32BF8559116DF2BFF50E L_2 = (*(FixedString512Bytes_t0C425B0F2C07FEA1642C32BF8559116DF2BFF50E*)L_1);
		V_0 = L_2;
		String_t* L_3;
		L_3 = FixedString512Bytes_ToString_m1A47583FB34608DBEEDC65F9CA6E7B8E7930233F((&V_0), NULL);
		NullCheck(L_0);
		VirtualActionInvoker1< String_t* >::Invoke(75, L_0, L_3);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TextAnimation__ctor_m587A6D6554ACC86C6C3444FFBF7F9EBC51BB36B0 (TextAnimation_t9777A34576E8E54B799AA1238A3B2D52E559A263* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&FixedString512BytesPropertyAnimationComponent_1__ctor_m5816EC6A01ADE6AB5F7F7845913E24E9F895A6D1_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		FixedString512BytesPropertyAnimationComponent_1__ctor_m5816EC6A01ADE6AB5F7F7845913E24E9F895A6D1(__this, FixedString512BytesPropertyAnimationComponent_1__ctor_m5816EC6A01ADE6AB5F7F7845913E24E9F895A6D1_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Color_tD001788D726C3A7F1379BEED0260B9591F440C1F TextColorAnimation_GetValue_mADB6804970D3F3630CB2578C15C352574ACA22FD (TextColorAnimation_t6574309620A762556ADE0DAB9A913B1B76E0EF28* __this, Text_tD60B2346DAA6666BF0D822FF607F0B220C2B9E62* ___0_target, const RuntimeMethod* method) 
{
	{
		Text_tD60B2346DAA6666BF0D822FF607F0B220C2B9E62* L_0 = ___0_target;
		NullCheck(L_0);
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_1;
		L_1 = VirtualFuncInvoker0< Color_tD001788D726C3A7F1379BEED0260B9591F440C1F >::Invoke(22, L_0);
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TextColorAnimation_SetValue_m913E227B8E497A3782658FFAD5AD0308A126A7A3 (TextColorAnimation_t6574309620A762556ADE0DAB9A913B1B76E0EF28* __this, Text_tD60B2346DAA6666BF0D822FF607F0B220C2B9E62* ___0_target, Color_tD001788D726C3A7F1379BEED0260B9591F440C1F* ___1_value, const RuntimeMethod* method) 
{
	{
		Text_tD60B2346DAA6666BF0D822FF607F0B220C2B9E62* L_0 = ___0_target;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F* L_1 = ___1_value;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_2 = (*(Color_tD001788D726C3A7F1379BEED0260B9591F440C1F*)L_1);
		NullCheck(L_0);
		VirtualActionInvoker1< Color_tD001788D726C3A7F1379BEED0260B9591F440C1F >::Invoke(23, L_0, L_2);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TextColorAnimation__ctor_m048855B24ECEC5A6AFBC71D9995A88CF8E279EA1 (TextColorAnimation_t6574309620A762556ADE0DAB9A913B1B76E0EF28* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ColorPropertyAnimationComponent_1__ctor_m1AF482252E756F15D71CC78DB2C0938B29384FA6_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		ColorPropertyAnimationComponent_1__ctor_m1AF482252E756F15D71CC78DB2C0938B29384FA6(__this, ColorPropertyAnimationComponent_1__ctor_m1AF482252E756F15D71CC78DB2C0938B29384FA6_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t TextFontSizeAnimation_GetValue_m1AF051D4E265FC2C0610785E2D302B1863612DF1 (TextFontSizeAnimation_t39852601D9D17EA06325D177ABFCBC4BEEE8C066* __this, Text_tD60B2346DAA6666BF0D822FF607F0B220C2B9E62* ___0_target, const RuntimeMethod* method) 
{
	{
		Text_tD60B2346DAA6666BF0D822FF607F0B220C2B9E62* L_0 = ___0_target;
		NullCheck(L_0);
		int32_t L_1;
		L_1 = Text_get_fontSize_m837C0618E78D0FDA972D11DDE3015DC888E93993(L_0, NULL);
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TextFontSizeAnimation_SetValue_m99196F3D82934735A6D5FCB1451C845FABE1703C (TextFontSizeAnimation_t39852601D9D17EA06325D177ABFCBC4BEEE8C066* __this, Text_tD60B2346DAA6666BF0D822FF607F0B220C2B9E62* ___0_target, int32_t* ___1_value, const RuntimeMethod* method) 
{
	{
		Text_tD60B2346DAA6666BF0D822FF607F0B220C2B9E62* L_0 = ___0_target;
		int32_t* L_1 = ___1_value;
		int32_t L_2 = *((int32_t*)L_1);
		NullCheck(L_0);
		Text_set_fontSize_m426338B0A2CDA58609028FFD471EF5F2C9F364D4(L_0, L_2, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TextFontSizeAnimation__ctor_m87C9A28C8A8853CC198F3111A5E314DE665695A1 (TextFontSizeAnimation_t39852601D9D17EA06325D177ABFCBC4BEEE8C066* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IntPropertyAnimationComponent_1__ctor_mED6C6022A4A66D182CE79D0E04160F1CF7D70360_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		IntPropertyAnimationComponent_1__ctor_mED6C6022A4A66D182CE79D0E04160F1CF7D70360(__this, IntPropertyAnimationComponent_1__ctor_mED6C6022A4A66D182CE79D0E04160F1CF7D70360_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Color_tD001788D726C3A7F1379BEED0260B9591F440C1F GraphicColorAnimation_GetValue_mE8C9561CD10F966B50ACE0DEAB3F775874782E24 (GraphicColorAnimation_t42939D1590768EA413C629493D8BB748515A58DE* __this, Graphic_tCBFCA4585A19E2B75465AECFEAC43F4016BF7931* ___0_target, const RuntimeMethod* method) 
{
	{
		Graphic_tCBFCA4585A19E2B75465AECFEAC43F4016BF7931* L_0 = ___0_target;
		NullCheck(L_0);
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_1;
		L_1 = VirtualFuncInvoker0< Color_tD001788D726C3A7F1379BEED0260B9591F440C1F >::Invoke(22, L_0);
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void GraphicColorAnimation_SetValue_m3CB23168572E2E90EFAAF4D66D547E3861FAAA35 (GraphicColorAnimation_t42939D1590768EA413C629493D8BB748515A58DE* __this, Graphic_tCBFCA4585A19E2B75465AECFEAC43F4016BF7931* ___0_target, Color_tD001788D726C3A7F1379BEED0260B9591F440C1F* ___1_value, const RuntimeMethod* method) 
{
	{
		Graphic_tCBFCA4585A19E2B75465AECFEAC43F4016BF7931* L_0 = ___0_target;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F* L_1 = ___1_value;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_2 = (*(Color_tD001788D726C3A7F1379BEED0260B9591F440C1F*)L_1);
		NullCheck(L_0);
		VirtualActionInvoker1< Color_tD001788D726C3A7F1379BEED0260B9591F440C1F >::Invoke(23, L_0, L_2);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void GraphicColorAnimation__ctor_m288E56671326902591010E3AB28533431B0CF290 (GraphicColorAnimation_t42939D1590768EA413C629493D8BB748515A58DE* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ColorPropertyAnimationComponent_1__ctor_m774CD3721E9CA656495B1AC9880CC105C67395F7_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		ColorPropertyAnimationComponent_1__ctor_m774CD3721E9CA656495B1AC9880CC105C67395F7(__this, ColorPropertyAnimationComponent_1__ctor_m774CD3721E9CA656495B1AC9880CC105C67395F7_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ImageColorAnimation_GetValue_m6E1EDD657548A690F465E7A49C393DCAEE6153CB (ImageColorAnimation_tFFB6FA38A3ECA911EA2FCFF5F09B09870F809C7F* __this, Image_tBC1D03F63BF71132E9A5E472B8742F172A011E7E* ___0_target, const RuntimeMethod* method) 
{
	{
		Image_tBC1D03F63BF71132E9A5E472B8742F172A011E7E* L_0 = ___0_target;
		NullCheck(L_0);
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_1;
		L_1 = VirtualFuncInvoker0< Color_tD001788D726C3A7F1379BEED0260B9591F440C1F >::Invoke(22, L_0);
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ImageColorAnimation_SetValue_mA09FDF3568497950BD81024995A823457D80EBD5 (ImageColorAnimation_tFFB6FA38A3ECA911EA2FCFF5F09B09870F809C7F* __this, Image_tBC1D03F63BF71132E9A5E472B8742F172A011E7E* ___0_target, Color_tD001788D726C3A7F1379BEED0260B9591F440C1F* ___1_value, const RuntimeMethod* method) 
{
	{
		Image_tBC1D03F63BF71132E9A5E472B8742F172A011E7E* L_0 = ___0_target;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F* L_1 = ___1_value;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_2 = (*(Color_tD001788D726C3A7F1379BEED0260B9591F440C1F*)L_1);
		NullCheck(L_0);
		VirtualActionInvoker1< Color_tD001788D726C3A7F1379BEED0260B9591F440C1F >::Invoke(23, L_0, L_2);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ImageColorAnimation__ctor_m88E171095847625BF018289C724B34286FF30262 (ImageColorAnimation_tFFB6FA38A3ECA911EA2FCFF5F09B09870F809C7F* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ColorPropertyAnimationComponent_1__ctor_mCDCEA5280E525AC77C62F9975B1CBAC04B12DD45_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		ColorPropertyAnimationComponent_1__ctor_mCDCEA5280E525AC77C62F9975B1CBAC04B12DD45(__this, ColorPropertyAnimationComponent_1__ctor_mCDCEA5280E525AC77C62F9975B1CBAC04B12DD45_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float ImageColorAlphaAnimation_GetValue_mE6A08B0E3B4ECF52D2ECC04505A5F74C04DD48E4 (ImageColorAlphaAnimation_t4BB2FC2C18CA060FFAD5E1D245318DD67D61E7B2* __this, Image_tBC1D03F63BF71132E9A5E472B8742F172A011E7E* ___0_target, const RuntimeMethod* method) 
{
	{
		Image_tBC1D03F63BF71132E9A5E472B8742F172A011E7E* L_0 = ___0_target;
		NullCheck(L_0);
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_1;
		L_1 = VirtualFuncInvoker0< Color_tD001788D726C3A7F1379BEED0260B9591F440C1F >::Invoke(22, L_0);
		float L_2 = L_1.___a;
		return L_2;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ImageColorAlphaAnimation_SetValue_mFC57F8AAAC85DA0D172896A780151E8D5939AE57 (ImageColorAlphaAnimation_t4BB2FC2C18CA060FFAD5E1D245318DD67D61E7B2* __this, Image_tBC1D03F63BF71132E9A5E472B8742F172A011E7E* ___0_target, float* ___1_value, const RuntimeMethod* method) 
{
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		Image_tBC1D03F63BF71132E9A5E472B8742F172A011E7E* L_0 = ___0_target;
		NullCheck(L_0);
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_1;
		L_1 = VirtualFuncInvoker0< Color_tD001788D726C3A7F1379BEED0260B9591F440C1F >::Invoke(22, L_0);
		V_0 = L_1;
		float* L_2 = ___1_value;
		float L_3 = *((float*)L_2);
		(&V_0)->___a = L_3;
		Image_tBC1D03F63BF71132E9A5E472B8742F172A011E7E* L_4 = ___0_target;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_5 = V_0;
		NullCheck(L_4);
		VirtualActionInvoker1< Color_tD001788D726C3A7F1379BEED0260B9591F440C1F >::Invoke(23, L_4, L_5);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ImageColorAlphaAnimation__ctor_mAFB1ACD9F6207441409EBF45682D8DEF60106C1D (ImageColorAlphaAnimation_t4BB2FC2C18CA060FFAD5E1D245318DD67D61E7B2* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&FloatPropertyAnimationComponent_1__ctor_mA94A443DF00FEBBCE145422ED585E95DB5DD0550_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		FloatPropertyAnimationComponent_1__ctor_mA94A443DF00FEBBCE145422ED585E95DB5DD0550(__this, FloatPropertyAnimationComponent_1__ctor_mA94A443DF00FEBBCE145422ED585E95DB5DD0550_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float ImageFillAmountAnimation_GetValue_mB62C07E93DBABFA12485068607449D1CED67D655 (ImageFillAmountAnimation_t05DF0A6E067043EE01BB0A2879930F5D76047CBB* __this, Image_tBC1D03F63BF71132E9A5E472B8742F172A011E7E* ___0_target, const RuntimeMethod* method) 
{
	{
		Image_tBC1D03F63BF71132E9A5E472B8742F172A011E7E* L_0 = ___0_target;
		NullCheck(L_0);
		float L_1;
		L_1 = Image_get_fillAmount_mDEE52490D07124E21E7CB36718A5E3714D8B9788_inline(L_0, NULL);
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ImageFillAmountAnimation_SetValue_m8E63709BBA578130019B438D4CE88869839F8992 (ImageFillAmountAnimation_t05DF0A6E067043EE01BB0A2879930F5D76047CBB* __this, Image_tBC1D03F63BF71132E9A5E472B8742F172A011E7E* ___0_target, float* ___1_value, const RuntimeMethod* method) 
{
	{
		Image_tBC1D03F63BF71132E9A5E472B8742F172A011E7E* L_0 = ___0_target;
		float* L_1 = ___1_value;
		float L_2 = *((float*)L_1);
		NullCheck(L_0);
		Image_set_fillAmount_m8A9B55F47F966A3214EAC4ACBFE198776A98FAA7(L_0, L_2, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ImageFillAmountAnimation__ctor_m5DCCED55FA424D55D759E67C935CDBFF6725419E (ImageFillAmountAnimation_t05DF0A6E067043EE01BB0A2879930F5D76047CBB* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&FloatPropertyAnimationComponent_1__ctor_mA94A443DF00FEBBCE145422ED585E95DB5DD0550_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		FloatPropertyAnimationComponent_1__ctor_mA94A443DF00FEBBCE145422ED585E95DB5DD0550(__this, FloatPropertyAnimationComponent_1__ctor_mA94A443DF00FEBBCE145422ED585E95DB5DD0550_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float SliderValueAnimation_GetValue_m9BB623607B6D753DEF65B9370DEF7C804AFF3DAA (SliderValueAnimation_t3DCA44DE2E0898CB1C626393BB657089FB9DAA8D* __this, Slider_t87EA570E3D6556CABF57456C2F3873FFD86E652F* ___0_target, const RuntimeMethod* method) 
{
	{
		Slider_t87EA570E3D6556CABF57456C2F3873FFD86E652F* L_0 = ___0_target;
		NullCheck(L_0);
		float L_1;
		L_1 = VirtualFuncInvoker0< float >::Invoke(46, L_0);
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SliderValueAnimation_SetValue_mE6887006C98BEC23920124938DC3EF838CDCEBED (SliderValueAnimation_t3DCA44DE2E0898CB1C626393BB657089FB9DAA8D* __this, Slider_t87EA570E3D6556CABF57456C2F3873FFD86E652F* ___0_target, float* ___1_value, const RuntimeMethod* method) 
{
	{
		Slider_t87EA570E3D6556CABF57456C2F3873FFD86E652F* L_0 = ___0_target;
		float* L_1 = ___1_value;
		float L_2 = *((float*)L_1);
		NullCheck(L_0);
		VirtualActionInvoker1< float >::Invoke(47, L_0, L_2);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SliderValueAnimation__ctor_m26F9E778C4ED855C2654EB0D20CF7B63D50F9A2A (SliderValueAnimation_t3DCA44DE2E0898CB1C626393BB657089FB9DAA8D* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&FloatPropertyAnimationComponent_1__ctor_mC888083E996E645550BEA6E923A8007C3C733830_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		FloatPropertyAnimationComponent_1__ctor_mC888083E996E645550BEA6E923A8007C3C733830(__this, FloatPropertyAnimationComponent_1__ctor_mC888083E996E645550BEA6E923A8007C3C733830_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float CanvasGroupAlphaAnimation_GetValue_m804B59FFDBA9BDD2EB2A29DE43406564F42BCD9E (CanvasGroupAlphaAnimation_t117A030C75C58FCE864557262A0B9D6B6669D650* __this, CanvasGroup_t048C1461B14628CFAEBE6E7353093ADB04EBC094* ___0_target, const RuntimeMethod* method) 
{
	{
		CanvasGroup_t048C1461B14628CFAEBE6E7353093ADB04EBC094* L_0 = ___0_target;
		NullCheck(L_0);
		float L_1;
		L_1 = CanvasGroup_get_alpha_mBFEA193D2886B27CC53B31F90F7A1659B67ED6DF(L_0, NULL);
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void CanvasGroupAlphaAnimation_SetValue_m97E2BA49705950C6DDB62791E0E06EFF171C8512 (CanvasGroupAlphaAnimation_t117A030C75C58FCE864557262A0B9D6B6669D650* __this, CanvasGroup_t048C1461B14628CFAEBE6E7353093ADB04EBC094* ___0_target, float* ___1_value, const RuntimeMethod* method) 
{
	{
		CanvasGroup_t048C1461B14628CFAEBE6E7353093ADB04EBC094* L_0 = ___0_target;
		float* L_1 = ___1_value;
		float L_2 = *((float*)L_1);
		NullCheck(L_0);
		CanvasGroup_set_alpha_m5C06839316D948BB4F75ED72C87FA1F1A20C333F(L_0, L_2, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void CanvasGroupAlphaAnimation__ctor_mDF253ED1F8ABF5CA03225ACA4C490C7AAE89A041 (CanvasGroupAlphaAnimation_t117A030C75C58FCE864557262A0B9D6B6669D650* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&FloatPropertyAnimationComponent_1__ctor_m1DD2B509363FBC2BC129C88A860277D9028EF5CF_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		FloatPropertyAnimationComponent_1__ctor_m1DD2B509363FBC2BC129C88A860277D9028EF5CF(__this, FloatPropertyAnimationComponent_1__ctor_m1DD2B509363FBC2BC129C88A860277D9028EF5CF_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FloatValueAnimation__ctor_mE153CF3E7794FDAB805AA6BD6320991BA80068B8 (FloatValueAnimation_t1F8A888C348C3256D7562E59BCCE868B248CC70E* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ValueAnimationComponent_3__ctor_m8ECB8F98AC1F6B0EA2F8DD2A20500E9FDDCBBB8E_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		ValueAnimationComponent_3__ctor_m8ECB8F98AC1F6B0EA2F8DD2A20500E9FDDCBBB8E(__this, ValueAnimationComponent_3__ctor_m8ECB8F98AC1F6B0EA2F8DD2A20500E9FDDCBBB8E_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void DoubleValueAnimation__ctor_m056B5D3F68A271AA480DD0EA0A60F7780CF2C0D6 (DoubleValueAnimation_t7880671F5296AFC7C98CC1B199266E8FD5018FBF* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ValueAnimationComponent_3__ctor_m7DC5DA0DF4325BCB95A2C1AC970E867DB8C6FB49_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		ValueAnimationComponent_3__ctor_m7DC5DA0DF4325BCB95A2C1AC970E867DB8C6FB49(__this, ValueAnimationComponent_3__ctor_m7DC5DA0DF4325BCB95A2C1AC970E867DB8C6FB49_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void IntValueAnimation__ctor_m352EA06516720DA4BE2AB2C883094B776D1A0237 (IntValueAnimation_t45CDBEB887CCC11D6E63FD4949100D30B9C74900* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ValueAnimationComponent_3__ctor_mFF142C6DE5E394B6F27CACEE067BD1C94C364B6E_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		ValueAnimationComponent_3__ctor_mFF142C6DE5E394B6F27CACEE067BD1C94C364B6E(__this, ValueAnimationComponent_3__ctor_mFF142C6DE5E394B6F27CACEE067BD1C94C364B6E_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void LongValueAnimation__ctor_m153F089490214E4DB4C01540D6B4304E004B9135 (LongValueAnimation_t93592BE53D79469CDD3CAF211EA24407BC68FEBD* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ValueAnimationComponent_3__ctor_mBD571DA23EAF4EDA73118C65A12D269BCA79D6B8_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		ValueAnimationComponent_3__ctor_mBD571DA23EAF4EDA73118C65A12D269BCA79D6B8(__this, ValueAnimationComponent_3__ctor_mBD571DA23EAF4EDA73118C65A12D269BCA79D6B8_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Vector2ValueAnimation__ctor_m136E4D95ABF8CB2785A23E77BFC4EC8F900252DA (Vector2ValueAnimation_tD1E052DC2FF67290D4853707C8D826CEEB001ABC* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ValueAnimationComponent_3__ctor_mB50DBC5A781213FD78CA4B6F0335327192242531_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		ValueAnimationComponent_3__ctor_mB50DBC5A781213FD78CA4B6F0335327192242531(__this, ValueAnimationComponent_3__ctor_mB50DBC5A781213FD78CA4B6F0335327192242531_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Vector3ValueAnimation__ctor_m301EC277CBC1EE37B80A0290AFF38347C3CEAFAD (Vector3ValueAnimation_t9E4D1D97B97516C598B84661502882CFC3E870EF* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ValueAnimationComponent_3__ctor_mF978B0E2507972B653807AB76DB5B6194310DB65_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		ValueAnimationComponent_3__ctor_mF978B0E2507972B653807AB76DB5B6194310DB65(__this, ValueAnimationComponent_3__ctor_mF978B0E2507972B653807AB76DB5B6194310DB65_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Vector4ValueAnimation__ctor_mBE48AD9477B7A05A511D3B0B15F4EE9DCA82DA49 (Vector4ValueAnimation_t76776B5052E42EBBC0A23B83622EAA759229A76A* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ValueAnimationComponent_3__ctor_mB06D7CD5E6F5AD7B33589B8DD7AFF78B507F224D_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		ValueAnimationComponent_3__ctor_mB06D7CD5E6F5AD7B33589B8DD7AFF78B507F224D(__this, ValueAnimationComponent_3__ctor_mB06D7CD5E6F5AD7B33589B8DD7AFF78B507F224D_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ColorValueAnimation__ctor_m4C8AE07F352EC0DC93EB4D29D162B812A3E95937 (ColorValueAnimation_tF6326EB4342E17097D0A55A411436898AA192498* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ValueAnimationComponent_3__ctor_mBE855EEE224D3A0F08B0CE2CD6BCC3ACC38EACC0_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		ValueAnimationComponent_3__ctor_mBE855EEE224D3A0F08B0CE2CD6BCC3ACC38EACC0(__this, ValueAnimationComponent_3__ctor_mBE855EEE224D3A0F08B0CE2CD6BCC3ACC38EACC0_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 StringValueAnimation_Play_m2341ABE671694F0E753497A0FF8E7A16EB73C68E (StringValueAnimation_t4C21FFF21AA92CA403E695317EFDDC25F6F729A0* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_2_t02F23795BC96180858055AB64D80A7006668D4D6_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&LMotion_Create_TisFixedString512Bytes_t0C425B0F2C07FEA1642C32BF8559116DF2BFF50E_TisStringOptions_tDDA84E855CD29F2BCD9BA102EA173B219CFCADA7_TisFixedString512BytesMotionAdapter_tE0926B0F0FCE1F39802A3A7B8E7F6C7E191C023C_mB9F06C5F7EBD9AF7B4CC7B1FBDE30DF686CC5D02_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MotionBuilder_3_Bind_TisStringValueAnimation_t4C21FFF21AA92CA403E695317EFDDC25F6F729A0_mCE150DB267F3DE64EA2D4761334CC080CFDC6BDE_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec_U3CPlayU3Eb__2_0_mF360A40509FD2F30750DCF886F357F69F1629B7B_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec_tDAFA5C4E997B098FAFADB5DBAD4676795BD4F04C_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	MotionBuilder_3_t99F4E0CCAE5DBB73925A375FDD89E2C368786FDA V_0;
	memset((&V_0), 0, sizeof(V_0));
	Action_2_t02F23795BC96180858055AB64D80A7006668D4D6* G_B2_0 = NULL;
	StringValueAnimation_t4C21FFF21AA92CA403E695317EFDDC25F6F729A0* G_B2_1 = NULL;
	MotionBuilder_3_t99F4E0CCAE5DBB73925A375FDD89E2C368786FDA* G_B2_2 = NULL;
	Action_2_t02F23795BC96180858055AB64D80A7006668D4D6* G_B1_0 = NULL;
	StringValueAnimation_t4C21FFF21AA92CA403E695317EFDDC25F6F729A0* G_B1_1 = NULL;
	MotionBuilder_3_t99F4E0CCAE5DBB73925A375FDD89E2C368786FDA* G_B1_2 = NULL;
	{
		SerializableMotionSettings_2_tF48B33D24FDF5B9DD96E1CE305D788D9D67A704E* L_0 = __this->___settings;
		MotionBuilder_3_t99F4E0CCAE5DBB73925A375FDD89E2C368786FDA L_1;
		L_1 = LMotion_Create_TisFixedString512Bytes_t0C425B0F2C07FEA1642C32BF8559116DF2BFF50E_TisStringOptions_tDDA84E855CD29F2BCD9BA102EA173B219CFCADA7_TisFixedString512BytesMotionAdapter_tE0926B0F0FCE1F39802A3A7B8E7F6C7E191C023C_mB9F06C5F7EBD9AF7B4CC7B1FBDE30DF686CC5D02(L_0, LMotion_Create_TisFixedString512Bytes_t0C425B0F2C07FEA1642C32BF8559116DF2BFF50E_TisStringOptions_tDDA84E855CD29F2BCD9BA102EA173B219CFCADA7_TisFixedString512BytesMotionAdapter_tE0926B0F0FCE1F39802A3A7B8E7F6C7E191C023C_mB9F06C5F7EBD9AF7B4CC7B1FBDE30DF686CC5D02_RuntimeMethod_var);
		V_0 = L_1;
		il2cpp_codegen_runtime_class_init_inline(U3CU3Ec_tDAFA5C4E997B098FAFADB5DBAD4676795BD4F04C_il2cpp_TypeInfo_var);
		Action_2_t02F23795BC96180858055AB64D80A7006668D4D6* L_2 = ((U3CU3Ec_tDAFA5C4E997B098FAFADB5DBAD4676795BD4F04C_StaticFields*)il2cpp_codegen_static_fields_for(U3CU3Ec_tDAFA5C4E997B098FAFADB5DBAD4676795BD4F04C_il2cpp_TypeInfo_var))->___U3CU3E9__2_0;
		Action_2_t02F23795BC96180858055AB64D80A7006668D4D6* L_3 = L_2;
		if (L_3)
		{
			G_B2_0 = L_3;
			G_B2_1 = __this;
			G_B2_2 = (&V_0);
			goto IL_002e;
		}
		G_B1_0 = L_3;
		G_B1_1 = __this;
		G_B1_2 = (&V_0);
	}
	{
		il2cpp_codegen_runtime_class_init_inline(U3CU3Ec_tDAFA5C4E997B098FAFADB5DBAD4676795BD4F04C_il2cpp_TypeInfo_var);
		U3CU3Ec_tDAFA5C4E997B098FAFADB5DBAD4676795BD4F04C* L_4 = ((U3CU3Ec_tDAFA5C4E997B098FAFADB5DBAD4676795BD4F04C_StaticFields*)il2cpp_codegen_static_fields_for(U3CU3Ec_tDAFA5C4E997B098FAFADB5DBAD4676795BD4F04C_il2cpp_TypeInfo_var))->___U3CU3E9;
		Action_2_t02F23795BC96180858055AB64D80A7006668D4D6* L_5 = (Action_2_t02F23795BC96180858055AB64D80A7006668D4D6*)il2cpp_codegen_object_new(Action_2_t02F23795BC96180858055AB64D80A7006668D4D6_il2cpp_TypeInfo_var);
		Action_2__ctor_mDD38C5EC0EA5A3490A03B631884AF92642BDB78D(L_5, L_4, (intptr_t)((void*)U3CU3Ec_U3CPlayU3Eb__2_0_mF360A40509FD2F30750DCF886F357F69F1629B7B_RuntimeMethod_var), NULL);
		Action_2_t02F23795BC96180858055AB64D80A7006668D4D6* L_6 = L_5;
		((U3CU3Ec_tDAFA5C4E997B098FAFADB5DBAD4676795BD4F04C_StaticFields*)il2cpp_codegen_static_fields_for(U3CU3Ec_tDAFA5C4E997B098FAFADB5DBAD4676795BD4F04C_il2cpp_TypeInfo_var))->___U3CU3E9__2_0 = L_6;
		Il2CppCodeGenWriteBarrier((void**)(&((U3CU3Ec_tDAFA5C4E997B098FAFADB5DBAD4676795BD4F04C_StaticFields*)il2cpp_codegen_static_fields_for(U3CU3Ec_tDAFA5C4E997B098FAFADB5DBAD4676795BD4F04C_il2cpp_TypeInfo_var))->___U3CU3E9__2_0), (void*)L_6);
		G_B2_0 = L_6;
		G_B2_1 = G_B1_1;
		G_B2_2 = G_B1_2;
	}

IL_002e:
	{
		MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 L_7;
		L_7 = MotionBuilder_3_Bind_TisStringValueAnimation_t4C21FFF21AA92CA403E695317EFDDC25F6F729A0_mCE150DB267F3DE64EA2D4761334CC080CFDC6BDE_inline(G_B2_2, G_B2_1, G_B2_0, MotionBuilder_3_Bind_TisStringValueAnimation_t4C21FFF21AA92CA403E695317EFDDC25F6F729A0_mCE150DB267F3DE64EA2D4761334CC080CFDC6BDE_RuntimeMethod_var);
		return L_7;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void StringValueAnimation_OnStop_mF02A154A26B0A52CA9BBD83A6C5C4238E664A914 (StringValueAnimation_t4C21FFF21AA92CA403E695317EFDDC25F6F729A0* __this, const RuntimeMethod* method) 
{
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void StringValueAnimation__ctor_m9D852268322B756973640E0954A6FAF6A95A3417 (StringValueAnimation_t4C21FFF21AA92CA403E695317EFDDC25F6F729A0* __this, const RuntimeMethod* method) 
{
	{
		LitMotionAnimationComponent__ctor_mD62326B8DB8585E40CFAA315FC8F3A94B8FA356B(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__cctor_m1B8082DD774DBB205D93FCE9C36A71817E2FF5B8 (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec_tDAFA5C4E997B098FAFADB5DBAD4676795BD4F04C_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		U3CU3Ec_tDAFA5C4E997B098FAFADB5DBAD4676795BD4F04C* L_0 = (U3CU3Ec_tDAFA5C4E997B098FAFADB5DBAD4676795BD4F04C*)il2cpp_codegen_object_new(U3CU3Ec_tDAFA5C4E997B098FAFADB5DBAD4676795BD4F04C_il2cpp_TypeInfo_var);
		U3CU3Ec__ctor_m6282724279A29E01D74080870544C26EA3CF5E66(L_0, NULL);
		((U3CU3Ec_tDAFA5C4E997B098FAFADB5DBAD4676795BD4F04C_StaticFields*)il2cpp_codegen_static_fields_for(U3CU3Ec_tDAFA5C4E997B098FAFADB5DBAD4676795BD4F04C_il2cpp_TypeInfo_var))->___U3CU3E9 = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&((U3CU3Ec_tDAFA5C4E997B098FAFADB5DBAD4676795BD4F04C_StaticFields*)il2cpp_codegen_static_fields_for(U3CU3Ec_tDAFA5C4E997B098FAFADB5DBAD4676795BD4F04C_il2cpp_TypeInfo_var))->___U3CU3E9), (void*)L_0);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__ctor_m6282724279A29E01D74080870544C26EA3CF5E66 (U3CU3Ec_tDAFA5C4E997B098FAFADB5DBAD4676795BD4F04C* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec_U3CPlayU3Eb__2_0_mF360A40509FD2F30750DCF886F357F69F1629B7B (U3CU3Ec_tDAFA5C4E997B098FAFADB5DBAD4676795BD4F04C* __this, FixedString512Bytes_t0C425B0F2C07FEA1642C32BF8559116DF2BFF50E ___0_x, StringValueAnimation_t4C21FFF21AA92CA403E695317EFDDC25F6F729A0* ___1_state, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&FixedStringMethods_ConvertToString_TisFixedString512Bytes_t0C425B0F2C07FEA1642C32BF8559116DF2BFF50E_mF5D257B46C5CC1CBFBC8A26BEA9F213A217A05EF_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&UnityEvent_1_Invoke_mA633B48B5D287DA856FB954AC3E4012487E63C15_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		StringValueAnimation_t4C21FFF21AA92CA403E695317EFDDC25F6F729A0* L_0 = ___1_state;
		NullCheck(L_0);
		UnityEvent_1_tC9859540CF1468306CAB6D758C0A0D95DBCEC257* L_1 = L_0->___onValueChanged;
		String_t* L_2;
		L_2 = FixedStringMethods_ConvertToString_TisFixedString512Bytes_t0C425B0F2C07FEA1642C32BF8559116DF2BFF50E_mF5D257B46C5CC1CBFBC8A26BEA9F213A217A05EF((&___0_x), FixedStringMethods_ConvertToString_TisFixedString512Bytes_t0C425B0F2C07FEA1642C32BF8559116DF2BFF50E_mF5D257B46C5CC1CBFBC8A26BEA9F213A217A05EF_RuntimeMethod_var);
		NullCheck(L_1);
		UnityEvent_1_Invoke_mA633B48B5D287DA856FB954AC3E4012487E63C15(L_1, L_2, UnityEvent_1_Invoke_mA633B48B5D287DA856FB954AC3E4012487E63C15_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool MotionHandleExtensions_IsActive_m33A1DAE84888047637F589CFF54E458F2CC0EED2_inline (MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 ___0_handle, const RuntimeMethod* method) 
{
	{
		MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 L_0 = ___0_handle;
		bool L_1;
		L_1 = MotionManager_IsActive_mF4852406A0DD2636E235C9D61F213B0F33063258_inline(L_0, NULL);
		return L_1;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 MotionHandleExtensions_Preserve_m6B6F0C6EB75391E011DF1CBA1BA86DE2EC6AFA0D_inline (MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 ___0_handle, const RuntimeMethod* method) 
{
	{
		MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 L_0 = ___0_handle;
		MotionData_tA820A6BD10DEA3326A5D51B503754F003CE7D399* L_1;
		L_1 = MotionManager_GetDataRef_m51519EC669AF4A72192ECD1B44279959134E8CBE_inline(L_0, (bool)1, NULL);
		MotionState_t92DCDE8373525507DED2EAF6EA7B9AB1F409C46D* L_2 = (MotionState_t92DCDE8373525507DED2EAF6EA7B9AB1F409C46D*)(&L_1->___State);
		L_2->___IsPreserved = (bool)1;
		MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 L_3 = ___0_handle;
		return L_3;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR ManagedMotionData_tE1DE04DA7EDE8DF722FC643EAF489B5222F396CB* MotionManager_GetManagedDataRef_m56749CE994D083F22E7FF3458B71A9B04C3FFDAF_inline (MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 ___0_handle, bool ___1_checkIsInSequence, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&FastListCore_1_get_Item_m9E02DE7FC9F92F09CFAF6958B134367BB973A9F3_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&FastListCore_1_t7622FA94EE4948D93B3B798B45AAE6DFAAC94519_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IMotionStorage_tC498F012248E7231BB90DCBEDAC47F5E1A57AB7A_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MotionManager_t55CCB0A78FD15BD0E274607AC5A3F35C3115763F_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		MotionManager_CheckTypeId_mEFEB7AD899D19579BD664224BD414C6A366792CB_inline((&___0_handle), NULL);
		MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 L_0 = ___0_handle;
		int32_t L_1 = L_0.___StorageId;
		il2cpp_codegen_runtime_class_init_inline(FastListCore_1_t7622FA94EE4948D93B3B798B45AAE6DFAAC94519_il2cpp_TypeInfo_var);
		RuntimeObject* L_2;
		L_2 = FastListCore_1_get_Item_m9E02DE7FC9F92F09CFAF6958B134367BB973A9F3_inline((&((MotionManager_t55CCB0A78FD15BD0E274607AC5A3F35C3115763F_StaticFields*)il2cpp_codegen_static_fields_for(MotionManager_t55CCB0A78FD15BD0E274607AC5A3F35C3115763F_il2cpp_TypeInfo_var))->___list), L_1, FastListCore_1_get_Item_m9E02DE7FC9F92F09CFAF6958B134367BB973A9F3_RuntimeMethod_var);
		MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 L_3 = ___0_handle;
		bool L_4 = ___1_checkIsInSequence;
		NullCheck(L_2);
		ManagedMotionData_tE1DE04DA7EDE8DF722FC643EAF489B5222F396CB* L_5;
		L_5 = InterfaceFuncInvoker2< ManagedMotionData_tE1DE04DA7EDE8DF722FC643EAF489B5222F396CB*, MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375, bool >::Invoke(8, IMotionStorage_tC498F012248E7231BB90DCBEDAC47F5E1A57AB7A_il2cpp_TypeInfo_var, L_2, L_3, L_4);
		return L_5;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void LitMotionAnimationComponent_set_TrackedHandle_mC6DE281FF2BE11BB93377BCB3B311226AA8CAE31_inline (LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A* __this, MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 ___0_value, const RuntimeMethod* method) 
{
	{
		MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 L_0 = ___0_value;
		__this->___U3CTrackedHandleU3Ek__BackingField = L_0;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 LitMotionAnimationComponent_get_TrackedHandle_m5B903D2A947941090BE2F79870D73368DD0A0606_inline (LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A* __this, const RuntimeMethod* method) 
{
	{
		MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 L_0 = __this->___U3CTrackedHandleU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool LitMotionAnimationComponent_get_Enabled_m910F7930C43DDE7E7951EA1EC5767B3DE7EFF718_inline (LitMotionAnimationComponent_t8C064ED173DF9604FBFF36715268E38B8A22E06A* __this, const RuntimeMethod* method) 
{
	{
		bool L_0 = __this->___enabled;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool MotionHandleExtensions_TryCancel_m5407A0C8EEB83CEEA742485A104889A7E14CD090_inline (MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 ___0_handle, const RuntimeMethod* method) 
{
	{
		MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 L_0 = ___0_handle;
		bool L_1;
		L_1 = MotionManager_TryCancel_m3F8F15BDC21AEBB3EEDB58CC70B604A8C9EE972A_inline(L_0, (bool)1, NULL);
		return L_1;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool MotionHandleExtensions_IsPlaying_m6CB941850BF2A95AB88CA4B5BD4FD3FB78F43DCF_inline (MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 ___0_handle, const RuntimeMethod* method) 
{
	{
		MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 L_0 = ___0_handle;
		bool L_1;
		L_1 = MotionManager_IsPlaying_m73338D2E14A34995AB74EB371311EB23611F819C_inline(L_0, NULL);
		return L_1;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool MotionHandleExtensions_TryComplete_m6E5C4F018592F928CBA2FD1626DBE6D25A2472E1_inline (MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 ___0_handle, const RuntimeMethod* method) 
{
	{
		MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 L_0 = ___0_handle;
		bool L_1;
		L_1 = MotionManager_TryComplete_m7BECEFD3EB4321B741D8ABDB3B999A2B065BD611_inline(L_0, (bool)1, NULL);
		return L_1;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float TMP_Text_get_characterSpacing_m48A3B73EFBF47B5227D2BB4816FCFF628254C8FB_inline (TMP_Text_tE8D677872D43AD4B2AAF0D6101692A17D0B251A9* __this, const RuntimeMethod* method) 
{
	{
		float L_0 = __this->___m_characterSpacing;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float TMP_Text_get_wordSpacing_mF3DF1445C78E06195904FCF0293E63654C527D33_inline (TMP_Text_tE8D677872D43AD4B2AAF0D6101692A17D0B251A9* __this, const RuntimeMethod* method) 
{
	{
		float L_0 = __this->___m_wordSpacing;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float TMP_Text_get_lineSpacing_m7481D705EAD920B8D143D19A270D44CDABDAA251_inline (TMP_Text_tE8D677872D43AD4B2AAF0D6101692A17D0B251A9* __this, const RuntimeMethod* method) 
{
	{
		float L_0 = __this->___m_lineSpacing;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float TMP_Text_get_paragraphSpacing_mCCBC792CAE59958E92EB04B8E636AA2066534713_inline (TMP_Text_tE8D677872D43AD4B2AAF0D6101692A17D0B251A9* __this, const RuntimeMethod* method) 
{
	{
		float L_0 = __this->___m_paragraphSpacing;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float TMP_Text_get_fontSize_m13A8365A56EA2B726EAD826B4A69C8918A528731_inline (TMP_Text_tE8D677872D43AD4B2AAF0D6101692A17D0B251A9* __this, const RuntimeMethod* method) 
{
	{
		float L_0 = __this->___m_fontSize;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Image_get_fillAmount_mDEE52490D07124E21E7CB36718A5E3714D8B9788_inline (Image_tBC1D03F63BF71132E9A5E472B8742F172A011E7E* __this, const RuntimeMethod* method) 
{
	{
		float L_0 = __this->___m_FillAmount;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void FastListCore_1_Add_mD6343E7F9F9D7AFEF75F492FBDC69F7F184CABDA_gshared_inline (FastListCore_1_tD253A2B097165863483F30B720AE3EDC9C7FB508* __this, RuntimeObject* ___0_element, const RuntimeMethod* method) 
{
	{
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_0 = __this->___array;
		if (L_0)
		{
			goto IL_0016;
		}
	}
	{
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_1 = (ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918*)(ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918*)SZArrayNew(il2cpp_rgctx_data(InitializedTypeInfo(method->klass)->rgctx_data, 2), (uint32_t)8);
		__this->___array = L_1;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___array), (void*)L_1);
		goto IL_0039;
	}

IL_0016:
	{
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_2 = __this->___array;
		NullCheck(L_2);
		int32_t L_3 = __this->___tailIndex;
		if ((!(((uint32_t)((int32_t)(((RuntimeArray*)L_2)->max_length))) == ((uint32_t)L_3))))
		{
			goto IL_0039;
		}
	}
	{
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918** L_4 = (ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918**)(&__this->___array);
		int32_t L_5 = __this->___tailIndex;
		Array_Resize_TisRuntimeObject_mE8D92C287251BAF8256D85E5829F749359EC334E(L_4, ((int32_t)il2cpp_codegen_multiply(L_5, 2)), il2cpp_rgctx_method(InitializedTypeInfo(method->klass)->rgctx_data, 3));
	}

IL_0039:
	{
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_6 = __this->___array;
		int32_t L_7 = __this->___tailIndex;
		RuntimeObject* L_8 = ___0_element;
		NullCheck(L_6);
		(L_6)->SetAt(static_cast<il2cpp_array_size_t>(L_7), (RuntimeObject*)L_8);
		int32_t L_9 = __this->___tailIndex;
		__this->___tailIndex = ((int32_t)il2cpp_codegen_add(L_9, 1));
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t Span_1_get_Length_m0B5336E05EEAAE122DF68A3A82C2D4359A2BE33D_gshared_inline (Span_1_t3F436092261253E8F2AF9867CA253C3B370766C2* __this, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = __this->____length;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void FastListCore_1_Clear_m98F6A1FC4B1C2A6BFD6CED3667DE26CC29C702E9_gshared_inline (FastListCore_1_tD253A2B097165863483F30B720AE3EDC9C7FB508* __this, bool ___0_removeArray, const RuntimeMethod* method) 
{
	Span_1_t3F436092261253E8F2AF9867CA253C3B370766C2 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_0 = __this->___array;
		if (L_0)
		{
			goto IL_0009;
		}
	}
	{
		return;
	}

IL_0009:
	{
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_1 = __this->___array;
		Span_1_t3F436092261253E8F2AF9867CA253C3B370766C2 L_2;
		L_2 = MemoryExtensions_AsSpan_TisRuntimeObject_mDE52AF2E134C93F32A3E74B8B364196EE5D1F61B_inline(L_1, il2cpp_rgctx_method(InitializedTypeInfo(method->klass)->rgctx_data, 9));
		V_0 = L_2;
		Span_1_Clear_mE8440147924A10C96926501B80472DFE2576383A_inline((&V_0), il2cpp_rgctx_method(InitializedTypeInfo(method->klass)->rgctx_data, 11));
		__this->___tailIndex = 0;
		bool L_3 = ___0_removeArray;
		if (!L_3)
		{
			goto IL_002d;
		}
	}
	{
		__this->___array = (ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918*)NULL;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___array), (void*)(ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918*)NULL);
	}

IL_002d:
	{
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t Queue_1_get_Count_m1768ADA9855B7CDA14C9C42E098A287F1A39C3A2_gshared_inline (Queue_1_tE9EF546915795972C3BFD68FBB8FA859D3BAF3B5* __this, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = __this->____size;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 MotionBuilder_3_RunWithoutBinding_mAEC7D58DAFA362934E3EB208CF5CE215C5D31B52_gshared_inline (MotionBuilder_3_tA23CEA9CCADDC2C36C8D976574E70A1D2A8909DC* __this, const RuntimeMethod* method) 
{
	{
		MotionBuilder_3_CheckBuffer_mC87B907D318DACF4CD7BFF327C156515E81102D5_inline(__this, il2cpp_rgctx_method(InitializedTypeInfo(method->klass)->rgctx_data, 4));
		MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 L_0;
		L_0 = MotionBuilder_3_ScheduleMotion_m78777BD29641DDE6EA56ABB48A8979179D108A34_inline(__this, il2cpp_rgctx_method(InitializedTypeInfo(method->klass)->rgctx_data, 6));
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 MotionBuilder_3_Bind_TisRuntimeObject_mC70C7DF3D4674E1D86107939A4C9ECA9AF590DED_gshared_inline (MotionBuilder_3_tA23CEA9CCADDC2C36C8D976574E70A1D2A8909DC* __this, RuntimeObject* ___0_state, Action_2_t25A8547AFC85E4914143093EDA11AE27C97D9E10* ___1_action, const RuntimeMethod* method) 
{
	il2cpp_rgctx_method_init(method);
	{
		MotionBuilder_3_CheckBuffer_mC87B907D318DACF4CD7BFF327C156515E81102D5_inline(__this, il2cpp_rgctx_method(InitializedTypeInfo(method->klass)->rgctx_data, 4));
		RuntimeObject* L_0 = ___0_state;
		Action_2_t25A8547AFC85E4914143093EDA11AE27C97D9E10* L_1 = ___1_action;
		MotionBuilder_3_SetCallbackData_TisRuntimeObject_m952DBBC1DAD5EECFC3151EB1EA39234B83381300_inline(__this, L_0, L_1, il2cpp_rgctx_method(method->rgctx_data, 2));
		MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 L_2;
		L_2 = MotionBuilder_3_ScheduleMotion_m78777BD29641DDE6EA56ABB48A8979179D108A34_inline(__this, il2cpp_rgctx_method(InitializedTypeInfo(method->klass)->rgctx_data, 6));
		return L_2;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 MotionBuilder_3_Bind_TisRuntimeObject_m3F7EBF2695B40B1A6AB1AF74C368B6EB3570BFB5_gshared_inline (MotionBuilder_3_t99F4E0CCAE5DBB73925A375FDD89E2C368786FDA* __this, RuntimeObject* ___0_state, Action_2_t61CA71EC63C565FB5A59A8A8E52B6C7336CD7A45* ___1_action, const RuntimeMethod* method) 
{
	il2cpp_rgctx_method_init(method);
	{
		MotionBuilder_3_CheckBuffer_mEED900CB970E8457CC3CF27F03C9ACFDC23F2F53_inline(__this, il2cpp_rgctx_method(InitializedTypeInfo(method->klass)->rgctx_data, 4));
		RuntimeObject* L_0 = ___0_state;
		Action_2_t61CA71EC63C565FB5A59A8A8E52B6C7336CD7A45* L_1 = ___1_action;
		MotionBuilder_3_SetCallbackData_TisRuntimeObject_mC0C6324795BD3E6EE99C27545D0F5C2AFA7F1F52_inline(__this, L_0, L_1, il2cpp_rgctx_method(method->rgctx_data, 2));
		MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 L_2;
		L_2 = MotionBuilder_3_ScheduleMotion_m0B2762B42FC511CA6867A9E0C817D00348FC804C_inline(__this, il2cpp_rgctx_method(InitializedTypeInfo(method->klass)->rgctx_data, 6));
		return L_2;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool MotionManager_IsActive_mF4852406A0DD2636E235C9D61F213B0F33063258_inline (MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 ___0_handle, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&FastListCore_1_get_Item_m9E02DE7FC9F92F09CFAF6958B134367BB973A9F3_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&FastListCore_1_t7622FA94EE4948D93B3B798B45AAE6DFAAC94519_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IMotionStorage_tC498F012248E7231BB90DCBEDAC47F5E1A57AB7A_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MotionManager_t55CCB0A78FD15BD0E274607AC5A3F35C3115763F_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 L_0 = ___0_handle;
		int32_t L_1 = L_0.___StorageId;
		if ((((int32_t)L_1) < ((int32_t)0)))
		{
			goto IL_0016;
		}
	}
	{
		MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 L_2 = ___0_handle;
		int32_t L_3 = L_2.___StorageId;
		int32_t L_4;
		L_4 = MotionManager_get_MotionTypeCount_m747275F1C694CA07B477CA9E940FCFE9C5051A5F_inline(NULL);
		if ((((int32_t)L_3) < ((int32_t)L_4)))
		{
			goto IL_0018;
		}
	}

IL_0016:
	{
		return (bool)0;
	}

IL_0018:
	{
		MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 L_5 = ___0_handle;
		int32_t L_6 = L_5.___StorageId;
		il2cpp_codegen_runtime_class_init_inline(FastListCore_1_t7622FA94EE4948D93B3B798B45AAE6DFAAC94519_il2cpp_TypeInfo_var);
		RuntimeObject* L_7;
		L_7 = FastListCore_1_get_Item_m9E02DE7FC9F92F09CFAF6958B134367BB973A9F3_inline((&((MotionManager_t55CCB0A78FD15BD0E274607AC5A3F35C3115763F_StaticFields*)il2cpp_codegen_static_fields_for(MotionManager_t55CCB0A78FD15BD0E274607AC5A3F35C3115763F_il2cpp_TypeInfo_var))->___list), L_6, FastListCore_1_get_Item_m9E02DE7FC9F92F09CFAF6958B134367BB973A9F3_RuntimeMethod_var);
		MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 L_8 = ___0_handle;
		NullCheck(L_7);
		bool L_9;
		L_9 = InterfaceFuncInvoker1< bool, MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 >::Invoke(0, IMotionStorage_tC498F012248E7231BB90DCBEDAC47F5E1A57AB7A_il2cpp_TypeInfo_var, L_7, L_8);
		return L_9;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR MotionData_tA820A6BD10DEA3326A5D51B503754F003CE7D399* MotionManager_GetDataRef_m51519EC669AF4A72192ECD1B44279959134E8CBE_inline (MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 ___0_handle, bool ___1_checkIsInSequence, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&FastListCore_1_get_Item_m9E02DE7FC9F92F09CFAF6958B134367BB973A9F3_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&FastListCore_1_t7622FA94EE4948D93B3B798B45AAE6DFAAC94519_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IMotionStorage_tC498F012248E7231BB90DCBEDAC47F5E1A57AB7A_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MotionManager_t55CCB0A78FD15BD0E274607AC5A3F35C3115763F_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		MotionManager_CheckTypeId_mEFEB7AD899D19579BD664224BD414C6A366792CB_inline((&___0_handle), NULL);
		MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 L_0 = ___0_handle;
		int32_t L_1 = L_0.___StorageId;
		il2cpp_codegen_runtime_class_init_inline(FastListCore_1_t7622FA94EE4948D93B3B798B45AAE6DFAAC94519_il2cpp_TypeInfo_var);
		RuntimeObject* L_2;
		L_2 = FastListCore_1_get_Item_m9E02DE7FC9F92F09CFAF6958B134367BB973A9F3_inline((&((MotionManager_t55CCB0A78FD15BD0E274607AC5A3F35C3115763F_StaticFields*)il2cpp_codegen_static_fields_for(MotionManager_t55CCB0A78FD15BD0E274607AC5A3F35C3115763F_il2cpp_TypeInfo_var))->___list), L_1, FastListCore_1_get_Item_m9E02DE7FC9F92F09CFAF6958B134367BB973A9F3_RuntimeMethod_var);
		MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 L_3 = ___0_handle;
		bool L_4 = ___1_checkIsInSequence;
		NullCheck(L_2);
		MotionData_tA820A6BD10DEA3326A5D51B503754F003CE7D399* L_5;
		L_5 = InterfaceFuncInvoker2< MotionData_tA820A6BD10DEA3326A5D51B503754F003CE7D399*, MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375, bool >::Invoke(7, IMotionStorage_tC498F012248E7231BB90DCBEDAC47F5E1A57AB7A_il2cpp_TypeInfo_var, L_2, L_3, L_4);
		return L_5;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void MotionManager_CheckTypeId_mEFEB7AD899D19579BD664224BD414C6A366792CB_inline (MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375* ___0_handle, const RuntimeMethod* method) 
{
	{
		MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375* L_0 = ___0_handle;
		int32_t L_1 = L_0->___StorageId;
		if ((((int32_t)L_1) < ((int32_t)0)))
		{
			goto IL_0016;
		}
	}
	{
		MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375* L_2 = ___0_handle;
		int32_t L_3 = L_2->___StorageId;
		int32_t L_4;
		L_4 = MotionManager_get_MotionTypeCount_m747275F1C694CA07B477CA9E940FCFE9C5051A5F_inline(NULL);
		if ((((int32_t)L_3) < ((int32_t)L_4)))
		{
			goto IL_0021;
		}
	}

IL_0016:
	{
		ArgumentException_tAD90411542A20A9C72D5CDA3A84181D8B947A263* L_5 = (ArgumentException_tAD90411542A20A9C72D5CDA3A84181D8B947A263*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ArgumentException_tAD90411542A20A9C72D5CDA3A84181D8B947A263_il2cpp_TypeInfo_var)));
		ArgumentException__ctor_m026938A67AF9D36BB7ED27F80425D7194B514465(L_5, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteral79E0E0F15F51BD91F0DA337090C4FABDB4CD6644)), NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_5, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&MotionManager_CheckTypeId_mEFEB7AD899D19579BD664224BD414C6A366792CB_RuntimeMethod_var)));
	}

IL_0021:
	{
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool MotionManager_TryCancel_m3F8F15BDC21AEBB3EEDB58CC70B604A8C9EE972A_inline (MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 ___0_handle, bool ___1_checkIsInSequence, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&FastListCore_1_get_Item_m9E02DE7FC9F92F09CFAF6958B134367BB973A9F3_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&FastListCore_1_t7622FA94EE4948D93B3B798B45AAE6DFAAC94519_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IMotionStorage_tC498F012248E7231BB90DCBEDAC47F5E1A57AB7A_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MotionManager_t55CCB0A78FD15BD0E274607AC5A3F35C3115763F_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 L_0 = ___0_handle;
		int32_t L_1 = L_0.___StorageId;
		if ((((int32_t)L_1) < ((int32_t)0)))
		{
			goto IL_0016;
		}
	}
	{
		MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 L_2 = ___0_handle;
		int32_t L_3 = L_2.___StorageId;
		int32_t L_4;
		L_4 = MotionManager_get_MotionTypeCount_m747275F1C694CA07B477CA9E940FCFE9C5051A5F_inline(NULL);
		if ((((int32_t)L_3) < ((int32_t)L_4)))
		{
			goto IL_0018;
		}
	}

IL_0016:
	{
		return (bool)0;
	}

IL_0018:
	{
		MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 L_5 = ___0_handle;
		int32_t L_6 = L_5.___StorageId;
		il2cpp_codegen_runtime_class_init_inline(FastListCore_1_t7622FA94EE4948D93B3B798B45AAE6DFAAC94519_il2cpp_TypeInfo_var);
		RuntimeObject* L_7;
		L_7 = FastListCore_1_get_Item_m9E02DE7FC9F92F09CFAF6958B134367BB973A9F3_inline((&((MotionManager_t55CCB0A78FD15BD0E274607AC5A3F35C3115763F_StaticFields*)il2cpp_codegen_static_fields_for(MotionManager_t55CCB0A78FD15BD0E274607AC5A3F35C3115763F_il2cpp_TypeInfo_var))->___list), L_6, FastListCore_1_get_Item_m9E02DE7FC9F92F09CFAF6958B134367BB973A9F3_RuntimeMethod_var);
		MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 L_8 = ___0_handle;
		bool L_9 = ___1_checkIsInSequence;
		NullCheck(L_7);
		bool L_10;
		L_10 = InterfaceFuncInvoker2< bool, MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375, bool >::Invoke(2, IMotionStorage_tC498F012248E7231BB90DCBEDAC47F5E1A57AB7A_il2cpp_TypeInfo_var, L_7, L_8, L_9);
		return L_10;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool MotionManager_IsPlaying_m73338D2E14A34995AB74EB371311EB23611F819C_inline (MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 ___0_handle, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&FastListCore_1_get_Item_m9E02DE7FC9F92F09CFAF6958B134367BB973A9F3_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&FastListCore_1_t7622FA94EE4948D93B3B798B45AAE6DFAAC94519_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IMotionStorage_tC498F012248E7231BB90DCBEDAC47F5E1A57AB7A_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MotionManager_t55CCB0A78FD15BD0E274607AC5A3F35C3115763F_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 L_0 = ___0_handle;
		int32_t L_1 = L_0.___StorageId;
		if ((((int32_t)L_1) < ((int32_t)0)))
		{
			goto IL_0016;
		}
	}
	{
		MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 L_2 = ___0_handle;
		int32_t L_3 = L_2.___StorageId;
		int32_t L_4;
		L_4 = MotionManager_get_MotionTypeCount_m747275F1C694CA07B477CA9E940FCFE9C5051A5F_inline(NULL);
		if ((((int32_t)L_3) < ((int32_t)L_4)))
		{
			goto IL_0018;
		}
	}

IL_0016:
	{
		return (bool)0;
	}

IL_0018:
	{
		MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 L_5 = ___0_handle;
		int32_t L_6 = L_5.___StorageId;
		il2cpp_codegen_runtime_class_init_inline(FastListCore_1_t7622FA94EE4948D93B3B798B45AAE6DFAAC94519_il2cpp_TypeInfo_var);
		RuntimeObject* L_7;
		L_7 = FastListCore_1_get_Item_m9E02DE7FC9F92F09CFAF6958B134367BB973A9F3_inline((&((MotionManager_t55CCB0A78FD15BD0E274607AC5A3F35C3115763F_StaticFields*)il2cpp_codegen_static_fields_for(MotionManager_t55CCB0A78FD15BD0E274607AC5A3F35C3115763F_il2cpp_TypeInfo_var))->___list), L_6, FastListCore_1_get_Item_m9E02DE7FC9F92F09CFAF6958B134367BB973A9F3_RuntimeMethod_var);
		MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 L_8 = ___0_handle;
		NullCheck(L_7);
		bool L_9;
		L_9 = InterfaceFuncInvoker1< bool, MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 >::Invoke(1, IMotionStorage_tC498F012248E7231BB90DCBEDAC47F5E1A57AB7A_il2cpp_TypeInfo_var, L_7, L_8);
		return L_9;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool MotionManager_TryComplete_m7BECEFD3EB4321B741D8ABDB3B999A2B065BD611_inline (MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 ___0_handle, bool ___1_checkIsInSequence, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&FastListCore_1_get_Item_m9E02DE7FC9F92F09CFAF6958B134367BB973A9F3_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&FastListCore_1_t7622FA94EE4948D93B3B798B45AAE6DFAAC94519_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IMotionStorage_tC498F012248E7231BB90DCBEDAC47F5E1A57AB7A_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MotionManager_t55CCB0A78FD15BD0E274607AC5A3F35C3115763F_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 L_0 = ___0_handle;
		int32_t L_1 = L_0.___StorageId;
		if ((((int32_t)L_1) < ((int32_t)0)))
		{
			goto IL_0016;
		}
	}
	{
		MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 L_2 = ___0_handle;
		int32_t L_3 = L_2.___StorageId;
		int32_t L_4;
		L_4 = MotionManager_get_MotionTypeCount_m747275F1C694CA07B477CA9E940FCFE9C5051A5F_inline(NULL);
		if ((((int32_t)L_3) < ((int32_t)L_4)))
		{
			goto IL_0018;
		}
	}

IL_0016:
	{
		return (bool)0;
	}

IL_0018:
	{
		MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 L_5 = ___0_handle;
		int32_t L_6 = L_5.___StorageId;
		il2cpp_codegen_runtime_class_init_inline(FastListCore_1_t7622FA94EE4948D93B3B798B45AAE6DFAAC94519_il2cpp_TypeInfo_var);
		RuntimeObject* L_7;
		L_7 = FastListCore_1_get_Item_m9E02DE7FC9F92F09CFAF6958B134367BB973A9F3_inline((&((MotionManager_t55CCB0A78FD15BD0E274607AC5A3F35C3115763F_StaticFields*)il2cpp_codegen_static_fields_for(MotionManager_t55CCB0A78FD15BD0E274607AC5A3F35C3115763F_il2cpp_TypeInfo_var))->___list), L_6, FastListCore_1_get_Item_m9E02DE7FC9F92F09CFAF6958B134367BB973A9F3_RuntimeMethod_var);
		MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 L_8 = ___0_handle;
		bool L_9 = ___1_checkIsInSequence;
		NullCheck(L_7);
		bool L_10;
		L_10 = InterfaceFuncInvoker2< bool, MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375, bool >::Invoke(3, IMotionStorage_tC498F012248E7231BB90DCBEDAC47F5E1A57AB7A_il2cpp_TypeInfo_var, L_7, L_8, L_9);
		return L_10;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR RuntimeObject* FastListCore_1_get_Item_mFE5C63DC39367FCFC1EF140A1A219F98E0AAB31E_gshared_inline (FastListCore_1_tD253A2B097165863483F30B720AE3EDC9C7FB508* __this, int32_t ___0_index, const RuntimeMethod* method) 
{
	{
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_0 = __this->___array;
		int32_t L_1 = ___0_index;
		NullCheck(L_0);
		int32_t L_2 = L_1;
		RuntimeObject* L_3 = (L_0)->GetAt(static_cast<il2cpp_array_size_t>(L_2));
		return L_3;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Span_1_t3F436092261253E8F2AF9867CA253C3B370766C2 MemoryExtensions_AsSpan_TisRuntimeObject_mDE52AF2E134C93F32A3E74B8B364196EE5D1F61B_gshared_inline (ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* ___0_array, const RuntimeMethod* method) 
{
	il2cpp_rgctx_method_init(method);
	{
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_0 = ___0_array;
		Span_1_t3F436092261253E8F2AF9867CA253C3B370766C2 L_1;
		memset((&L_1), 0, sizeof(L_1));
		Span_1__ctor_m8DB171982E2F26182BA531AC18EEAA27D52763EE_inline((&L_1), L_0, il2cpp_rgctx_method(method->rgctx_data, 2));
		return L_1;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Span_1_Clear_mE8440147924A10C96926501B80472DFE2576383A_gshared_inline (Span_1_t3F436092261253E8F2AF9867CA253C3B370766C2* __this, const RuntimeMethod* method) 
{
	ByReference_1_t98B79BFB40A2CA0814BC183B09B4339A5EBF8524 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
	}
	{
		ByReference_1_t98B79BFB40A2CA0814BC183B09B4339A5EBF8524 L_0 = __this->____pointer;
		V_0 = L_0;
		RuntimeObject** L_1;
		L_1 = IL2CPP_BY_REFERENCE_GET_VALUE(RuntimeObject*, (Il2CppByReference*)(&V_0));
		intptr_t* L_2;
		L_2 = il2cpp_unsafe_as_ref<intptr_t>(L_1);
		int32_t L_3 = __this->____length;
		int32_t L_4;
		L_4 = il2cpp_unsafe_sizeof<RuntimeObject*>();
		int32_t L_5;
		L_5 = IntPtr_get_Size_m1FAAA59DA73D7E32BB1AB55DD92A90AFE3251DBE(NULL);
		SpanHelpers_ClearWithReferences_m9641D8B6DC3AE81B4B0734BBA0E477EF131CD430(L_2, (uint64_t)((int64_t)il2cpp_codegen_multiply(((int64_t)L_3), ((int64_t)((int32_t)(L_4/L_5))))), NULL);
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void MotionBuilder_3_CheckBuffer_mC87B907D318DACF4CD7BFF327C156515E81102D5_gshared_inline (MotionBuilder_3_tA23CEA9CCADDC2C36C8D976574E70A1D2A8909DC* __this, const RuntimeMethod* method) 
{
	{
		MotionBuilderBuffer_2_t190528172F72DD395324243FA145EE61169E32DD* L_0 = __this->___buffer;
		if (!L_0)
		{
			goto IL_001b;
		}
	}
	{
		MotionBuilderBuffer_2_t190528172F72DD395324243FA145EE61169E32DD* L_1 = __this->___buffer;
		NullCheck(L_1);
		uint16_t L_2 = L_1->___Version;
		uint16_t L_3 = __this->___version;
		if ((((int32_t)L_2) == ((int32_t)L_3)))
		{
			goto IL_0026;
		}
	}

IL_001b:
	{
		InvalidOperationException_t5DDE4D49B7405FAAB1E4576F4715A42A3FAD4BAB* L_4 = (InvalidOperationException_t5DDE4D49B7405FAAB1E4576F4715A42A3FAD4BAB*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&InvalidOperationException_t5DDE4D49B7405FAAB1E4576F4715A42A3FAD4BAB_il2cpp_TypeInfo_var)));
		InvalidOperationException__ctor_mE4CB6F4712AB6D99A2358FBAE2E052B3EE976162(L_4, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteral0F11661CCB4BF59FC5EC30D63E4D71323F857EFE)), NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_4, method);
	}

IL_0026:
	{
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 MotionBuilder_3_ScheduleMotion_m78777BD29641DDE6EA56ABB48A8979179D108A34_gshared_inline (MotionBuilder_3_tA23CEA9CCADDC2C36C8D976574E70A1D2A8909DC* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MotionDebugger_t65073AE52251C2BB7A2D83C38CFE9FB805538D40_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MotionDispatcher_t1390D0649134C8A06154D3B03FD2C726E353ED08_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MotionScheduler_t98622FCF54DC19E35E9CB686D1D56DC64EE07AE5_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		MotionBuilderBuffer_2_t190528172F72DD395324243FA145EE61169E32DD* L_0 = __this->___buffer;
		NullCheck(L_0);
		RuntimeObject* L_1 = L_0->___Scheduler;
		if (L_1)
		{
			goto IL_0031;
		}
	}
	{
		il2cpp_codegen_runtime_class_init_inline(MotionScheduler_t98622FCF54DC19E35E9CB686D1D56DC64EE07AE5_il2cpp_TypeInfo_var);
		RuntimeObject* L_2;
		L_2 = MotionScheduler_get_DefaultScheduler_m1815603ECA3208DFD19FE6160C8FF824784E9FAB_inline(NULL);
		RuntimeObject* L_3 = ((MotionScheduler_t98622FCF54DC19E35E9CB686D1D56DC64EE07AE5_StaticFields*)il2cpp_codegen_static_fields_for(MotionScheduler_t98622FCF54DC19E35E9CB686D1D56DC64EE07AE5_il2cpp_TypeInfo_var))->___Update;
		if ((!(((RuntimeObject*)(RuntimeObject*)L_2) == ((RuntimeObject*)(RuntimeObject*)L_3))))
		{
			goto IL_0023;
		}
	}
	{
		il2cpp_codegen_runtime_class_init_inline(MotionDispatcher_t1390D0649134C8A06154D3B03FD2C726E353ED08_il2cpp_TypeInfo_var);
		MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 L_4;
		L_4 = MotionDispatcher_Schedule_TisSingle_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C_TisNoOptions_t7F036E20F8ED9230BFEF6EDF7C39EDD013A70C13_TisFloatMotionAdapter_t921836088726C215C673FCA1C985DEB91DD59E6D_m49F38264AA02A961F3AEE8437657F027A184BFFD(__this, (int32_t)4, il2cpp_rgctx_method(InitializedTypeInfo(method->klass)->rgctx_data, 26));
		V_0 = L_4;
		goto IL_0043;
	}

IL_0023:
	{
		il2cpp_codegen_runtime_class_init_inline(MotionScheduler_t98622FCF54DC19E35E9CB686D1D56DC64EE07AE5_il2cpp_TypeInfo_var);
		RuntimeObject* L_5;
		L_5 = MotionScheduler_get_DefaultScheduler_m1815603ECA3208DFD19FE6160C8FF824784E9FAB_inline(NULL);
		NullCheck(L_5);
		MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 L_6;
		L_6 = GenericInterfaceFuncInvoker1< MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375, MotionBuilder_3_tA23CEA9CCADDC2C36C8D976574E70A1D2A8909DC* >::Invoke(il2cpp_rgctx_method(InitializedTypeInfo(method->klass)->rgctx_data, 28), L_5, __this);
		V_0 = L_6;
		goto IL_0043;
	}

IL_0031:
	{
		MotionBuilderBuffer_2_t190528172F72DD395324243FA145EE61169E32DD* L_7 = __this->___buffer;
		NullCheck(L_7);
		RuntimeObject* L_8 = L_7->___Scheduler;
		NullCheck(L_8);
		MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 L_9;
		L_9 = GenericInterfaceFuncInvoker1< MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375, MotionBuilder_3_tA23CEA9CCADDC2C36C8D976574E70A1D2A8909DC* >::Invoke(il2cpp_rgctx_method(InitializedTypeInfo(method->klass)->rgctx_data, 28), L_8, __this);
		V_0 = L_9;
	}

IL_0043:
	{
		il2cpp_codegen_runtime_class_init_inline(MotionDebugger_t65073AE52251C2BB7A2D83C38CFE9FB805538D40_il2cpp_TypeInfo_var);
		bool L_10 = ((MotionDebugger_t65073AE52251C2BB7A2D83C38CFE9FB805538D40_StaticFields*)il2cpp_codegen_static_fields_for(MotionDebugger_t65073AE52251C2BB7A2D83C38CFE9FB805538D40_il2cpp_TypeInfo_var))->___Enabled;
		if (!L_10)
		{
			goto IL_005c;
		}
	}
	{
		MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 L_11 = V_0;
		MotionBuilderBuffer_2_t190528172F72DD395324243FA145EE61169E32DD* L_12 = __this->___buffer;
		NullCheck(L_12);
		RuntimeObject* L_13 = L_12->___Scheduler;
		il2cpp_codegen_runtime_class_init_inline(MotionDebugger_t65073AE52251C2BB7A2D83C38CFE9FB805538D40_il2cpp_TypeInfo_var);
		MotionDebugger_AddTracking_m5DAB6174F7F49BCC3F5C64BC81A53CCF88C5B475(L_11, L_13, 3, NULL);
	}

IL_005c:
	{
		MotionBuilder_3_Dispose_mE427E5C7D3C1A34E54DAA0378E406537AE6C724E(__this, il2cpp_rgctx_method(InitializedTypeInfo(method->klass)->rgctx_data, 29));
		MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 L_14 = V_0;
		return L_14;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void MotionBuilder_3_SetCallbackData_TisRuntimeObject_m952DBBC1DAD5EECFC3151EB1EA39234B83381300_gshared_inline (MotionBuilder_3_tA23CEA9CCADDC2C36C8D976574E70A1D2A8909DC* __this, RuntimeObject* ___0_state, Action_2_t25A8547AFC85E4914143093EDA11AE27C97D9E10* ___1_action, const RuntimeMethod* method) 
{
	{
		MotionBuilderBuffer_2_t190528172F72DD395324243FA145EE61169E32DD* L_0 = __this->___buffer;
		NullCheck(L_0);
		L_0->___StateCount = (uint8_t)1;
		MotionBuilderBuffer_2_t190528172F72DD395324243FA145EE61169E32DD* L_1 = __this->___buffer;
		RuntimeObject* L_2 = ___0_state;
		NullCheck(L_1);
		L_1->___State0 = L_2;
		Il2CppCodeGenWriteBarrier((void**)(&L_1->___State0), (void*)L_2);
		MotionBuilderBuffer_2_t190528172F72DD395324243FA145EE61169E32DD* L_3 = __this->___buffer;
		Action_2_t25A8547AFC85E4914143093EDA11AE27C97D9E10* L_4 = ___1_action;
		NullCheck(L_3);
		L_3->___UpdateAction = (RuntimeObject*)L_4;
		Il2CppCodeGenWriteBarrier((void**)(&L_3->___UpdateAction), (void*)(RuntimeObject*)L_4);
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void MotionBuilder_3_CheckBuffer_mEED900CB970E8457CC3CF27F03C9ACFDC23F2F53_gshared_inline (MotionBuilder_3_t99F4E0CCAE5DBB73925A375FDD89E2C368786FDA* __this, const RuntimeMethod* method) 
{
	{
		MotionBuilderBuffer_2_t04E3EEA09283FAF1D9197F4BE4C39D9944C98311* L_0 = __this->___buffer;
		if (!L_0)
		{
			goto IL_001b;
		}
	}
	{
		MotionBuilderBuffer_2_t04E3EEA09283FAF1D9197F4BE4C39D9944C98311* L_1 = __this->___buffer;
		NullCheck(L_1);
		uint16_t L_2 = L_1->___Version;
		uint16_t L_3 = __this->___version;
		if ((((int32_t)L_2) == ((int32_t)L_3)))
		{
			goto IL_0026;
		}
	}

IL_001b:
	{
		InvalidOperationException_t5DDE4D49B7405FAAB1E4576F4715A42A3FAD4BAB* L_4 = (InvalidOperationException_t5DDE4D49B7405FAAB1E4576F4715A42A3FAD4BAB*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&InvalidOperationException_t5DDE4D49B7405FAAB1E4576F4715A42A3FAD4BAB_il2cpp_TypeInfo_var)));
		InvalidOperationException__ctor_mE4CB6F4712AB6D99A2358FBAE2E052B3EE976162(L_4, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteral0F11661CCB4BF59FC5EC30D63E4D71323F857EFE)), NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_4, method);
	}

IL_0026:
	{
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void MotionBuilder_3_SetCallbackData_TisRuntimeObject_mC0C6324795BD3E6EE99C27545D0F5C2AFA7F1F52_gshared_inline (MotionBuilder_3_t99F4E0CCAE5DBB73925A375FDD89E2C368786FDA* __this, RuntimeObject* ___0_state, Action_2_t61CA71EC63C565FB5A59A8A8E52B6C7336CD7A45* ___1_action, const RuntimeMethod* method) 
{
	{
		MotionBuilderBuffer_2_t04E3EEA09283FAF1D9197F4BE4C39D9944C98311* L_0 = __this->___buffer;
		NullCheck(L_0);
		L_0->___StateCount = (uint8_t)1;
		MotionBuilderBuffer_2_t04E3EEA09283FAF1D9197F4BE4C39D9944C98311* L_1 = __this->___buffer;
		RuntimeObject* L_2 = ___0_state;
		NullCheck(L_1);
		L_1->___State0 = L_2;
		Il2CppCodeGenWriteBarrier((void**)(&L_1->___State0), (void*)L_2);
		MotionBuilderBuffer_2_t04E3EEA09283FAF1D9197F4BE4C39D9944C98311* L_3 = __this->___buffer;
		Action_2_t61CA71EC63C565FB5A59A8A8E52B6C7336CD7A45* L_4 = ___1_action;
		NullCheck(L_3);
		L_3->___UpdateAction = (RuntimeObject*)L_4;
		Il2CppCodeGenWriteBarrier((void**)(&L_3->___UpdateAction), (void*)(RuntimeObject*)L_4);
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 MotionBuilder_3_ScheduleMotion_m0B2762B42FC511CA6867A9E0C817D00348FC804C_gshared_inline (MotionBuilder_3_t99F4E0CCAE5DBB73925A375FDD89E2C368786FDA* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MotionDebugger_t65073AE52251C2BB7A2D83C38CFE9FB805538D40_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MotionDispatcher_t1390D0649134C8A06154D3B03FD2C726E353ED08_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MotionScheduler_t98622FCF54DC19E35E9CB686D1D56DC64EE07AE5_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		MotionBuilderBuffer_2_t04E3EEA09283FAF1D9197F4BE4C39D9944C98311* L_0 = __this->___buffer;
		NullCheck(L_0);
		RuntimeObject* L_1 = L_0->___Scheduler;
		if (L_1)
		{
			goto IL_0031;
		}
	}
	{
		il2cpp_codegen_runtime_class_init_inline(MotionScheduler_t98622FCF54DC19E35E9CB686D1D56DC64EE07AE5_il2cpp_TypeInfo_var);
		RuntimeObject* L_2;
		L_2 = MotionScheduler_get_DefaultScheduler_m1815603ECA3208DFD19FE6160C8FF824784E9FAB_inline(NULL);
		RuntimeObject* L_3 = ((MotionScheduler_t98622FCF54DC19E35E9CB686D1D56DC64EE07AE5_StaticFields*)il2cpp_codegen_static_fields_for(MotionScheduler_t98622FCF54DC19E35E9CB686D1D56DC64EE07AE5_il2cpp_TypeInfo_var))->___Update;
		if ((!(((RuntimeObject*)(RuntimeObject*)L_2) == ((RuntimeObject*)(RuntimeObject*)L_3))))
		{
			goto IL_0023;
		}
	}
	{
		il2cpp_codegen_runtime_class_init_inline(MotionDispatcher_t1390D0649134C8A06154D3B03FD2C726E353ED08_il2cpp_TypeInfo_var);
		MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 L_4;
		L_4 = MotionDispatcher_Schedule_TisFixedString512Bytes_t0C425B0F2C07FEA1642C32BF8559116DF2BFF50E_TisStringOptions_tDDA84E855CD29F2BCD9BA102EA173B219CFCADA7_TisFixedString512BytesMotionAdapter_tE0926B0F0FCE1F39802A3A7B8E7F6C7E191C023C_m165A3B75B57E53AA4FD0739235EA54F03274AA04(__this, (int32_t)4, il2cpp_rgctx_method(InitializedTypeInfo(method->klass)->rgctx_data, 26));
		V_0 = L_4;
		goto IL_0043;
	}

IL_0023:
	{
		il2cpp_codegen_runtime_class_init_inline(MotionScheduler_t98622FCF54DC19E35E9CB686D1D56DC64EE07AE5_il2cpp_TypeInfo_var);
		RuntimeObject* L_5;
		L_5 = MotionScheduler_get_DefaultScheduler_m1815603ECA3208DFD19FE6160C8FF824784E9FAB_inline(NULL);
		NullCheck(L_5);
		MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 L_6;
		L_6 = GenericInterfaceFuncInvoker1< MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375, MotionBuilder_3_t99F4E0CCAE5DBB73925A375FDD89E2C368786FDA* >::Invoke(il2cpp_rgctx_method(InitializedTypeInfo(method->klass)->rgctx_data, 28), L_5, __this);
		V_0 = L_6;
		goto IL_0043;
	}

IL_0031:
	{
		MotionBuilderBuffer_2_t04E3EEA09283FAF1D9197F4BE4C39D9944C98311* L_7 = __this->___buffer;
		NullCheck(L_7);
		RuntimeObject* L_8 = L_7->___Scheduler;
		NullCheck(L_8);
		MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 L_9;
		L_9 = GenericInterfaceFuncInvoker1< MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375, MotionBuilder_3_t99F4E0CCAE5DBB73925A375FDD89E2C368786FDA* >::Invoke(il2cpp_rgctx_method(InitializedTypeInfo(method->klass)->rgctx_data, 28), L_8, __this);
		V_0 = L_9;
	}

IL_0043:
	{
		il2cpp_codegen_runtime_class_init_inline(MotionDebugger_t65073AE52251C2BB7A2D83C38CFE9FB805538D40_il2cpp_TypeInfo_var);
		bool L_10 = ((MotionDebugger_t65073AE52251C2BB7A2D83C38CFE9FB805538D40_StaticFields*)il2cpp_codegen_static_fields_for(MotionDebugger_t65073AE52251C2BB7A2D83C38CFE9FB805538D40_il2cpp_TypeInfo_var))->___Enabled;
		if (!L_10)
		{
			goto IL_005c;
		}
	}
	{
		MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 L_11 = V_0;
		MotionBuilderBuffer_2_t04E3EEA09283FAF1D9197F4BE4C39D9944C98311* L_12 = __this->___buffer;
		NullCheck(L_12);
		RuntimeObject* L_13 = L_12->___Scheduler;
		il2cpp_codegen_runtime_class_init_inline(MotionDebugger_t65073AE52251C2BB7A2D83C38CFE9FB805538D40_il2cpp_TypeInfo_var);
		MotionDebugger_AddTracking_m5DAB6174F7F49BCC3F5C64BC81A53CCF88C5B475(L_11, L_13, 3, NULL);
	}

IL_005c:
	{
		MotionBuilder_3_Dispose_mCD91B91C78D1CF6B5D970F8FB78F07AEE2618C5A(__this, il2cpp_rgctx_method(InitializedTypeInfo(method->klass)->rgctx_data, 29));
		MotionHandle_tF5525498887D8DE6166542E3ADEE38A547B84375 L_14 = V_0;
		return L_14;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t MotionManager_get_MotionTypeCount_m747275F1C694CA07B477CA9E940FCFE9C5051A5F_inline (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MotionManager_t55CCB0A78FD15BD0E274607AC5A3F35C3115763F_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		int32_t L_0 = ((MotionManager_t55CCB0A78FD15BD0E274607AC5A3F35C3115763F_StaticFields*)il2cpp_codegen_static_fields_for(MotionManager_t55CCB0A78FD15BD0E274607AC5A3F35C3115763F_il2cpp_TypeInfo_var))->___U3CMotionTypeCountU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR RuntimeObject* MotionScheduler_get_DefaultScheduler_m1815603ECA3208DFD19FE6160C8FF824784E9FAB_inline (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MotionScheduler_t98622FCF54DC19E35E9CB686D1D56DC64EE07AE5_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		il2cpp_codegen_runtime_class_init_inline(MotionScheduler_t98622FCF54DC19E35E9CB686D1D56DC64EE07AE5_il2cpp_TypeInfo_var);
		RuntimeObject* L_0 = ((MotionScheduler_t98622FCF54DC19E35E9CB686D1D56DC64EE07AE5_StaticFields*)il2cpp_codegen_static_fields_for(MotionScheduler_t98622FCF54DC19E35E9CB686D1D56DC64EE07AE5_il2cpp_TypeInfo_var))->___U3CDefaultSchedulerU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Span_1__ctor_m8DB171982E2F26182BA531AC18EEAA27D52763EE_gshared_inline (Span_1_t3F436092261253E8F2AF9867CA253C3B370766C2* __this, ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* ___0_array, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Type_t_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	RuntimeObject* V_0 = NULL;
	{
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_0 = ___0_array;
		if (L_0)
		{
			goto IL_000b;
		}
	}
	{
		il2cpp_codegen_initobj(__this, sizeof(Span_1_t3F436092261253E8F2AF9867CA253C3B370766C2));
		return;
	}

IL_000b:
	{
		il2cpp_codegen_initobj((&V_0), sizeof(RuntimeObject*));
		RuntimeObject* L_1 = V_0;
		if (L_1)
		{
			goto IL_0037;
		}
	}
	{
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_2 = ___0_array;
		NullCheck((RuntimeObject*)L_2);
		Type_t* L_3;
		L_3 = Object_GetType_mE10A8FC1E57F3DF29972CCBC026C2DC3942263B3((RuntimeObject*)L_2, NULL);
		RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B L_4 = { reinterpret_cast<intptr_t> (il2cpp_rgctx_type(InitializedTypeInfo(method->klass)->rgctx_data, 3)) };
		il2cpp_codegen_runtime_class_init_inline(Type_t_il2cpp_TypeInfo_var);
		Type_t* L_5;
		L_5 = Type_GetTypeFromHandle_m6062B81682F79A4D6DF2640692EE6D9987858C57(L_4, NULL);
		bool L_6;
		L_6 = Type_op_Inequality_m83209C7BB3C05DFBEA3B6199B0BEFE8037301172(L_3, L_5, NULL);
		if (!L_6)
		{
			goto IL_0037;
		}
	}
	{
		ThrowHelper_ThrowArrayTypeMismatchException_m781AD7A903FEA43FAE3137977E6BC5F9BAEBC590(NULL);
	}

IL_0037:
	{
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_7 = ___0_array;
		NullCheck((RuntimeArray*)L_7);
		uint8_t* L_8;
		L_8 = Array_GetRawSzArrayData_m2F8F5B2A381AEF971F12866D9C0A6C4FBA59F6BB_inline((RuntimeArray*)L_7, NULL);
		RuntimeObject** L_9;
		L_9 = il2cpp_unsafe_as_ref<RuntimeObject*>(L_8);
		ByReference_1_t98B79BFB40A2CA0814BC183B09B4339A5EBF8524 L_10;
		memset((&L_10), 0, sizeof(L_10));
		il2cpp_codegen_by_reference_constructor((Il2CppByReference*)(&L_10), L_9);
		__this->____pointer = L_10;
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_11 = ___0_array;
		NullCheck(L_11);
		__this->____length = ((int32_t)(((RuntimeArray*)L_11)->max_length));
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint8_t* Array_GetRawSzArrayData_m2F8F5B2A381AEF971F12866D9C0A6C4FBA59F6BB_inline (RuntimeArray* __this, const RuntimeMethod* method) 
{
	{
		RawData_t37CAF2D3F74B7723974ED7CEEE9B297D8FA64ED0* L_0;
		L_0 = il2cpp_unsafe_as<RawData_t37CAF2D3F74B7723974ED7CEEE9B297D8FA64ED0*>(__this);
		NullCheck(L_0);
		uint8_t* L_1 = (uint8_t*)(&L_0->___Data);
		return L_1;
	}
}

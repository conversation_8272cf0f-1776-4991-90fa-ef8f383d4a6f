using System.Collections.Generic;
using System.Text;
using UnityEngine;
using UnityEngine.UI;

public struct BitmapCharacterInfo
{
    public BitmapGlyph glyph;
    public Vector2 position;
    public Color32 color;
    public float scale;
    public bool isVisible;

    public BitmapCharacterInfo(BitmapGlyph glyph, Vector2 position, Color32 color, float scale = 1f)
    {
        this.glyph = glyph;
        this.position = position;
        this.color = color;
        this.scale = scale;
        this.isVisible = true;
    }
}

public struct BitmapTextInfo
{
    public List<BitmapCharacterInfo> characters;
    public Vector2 textSize;
    public int lineCount;
    public float actualFontSize;

    public BitmapTextInfo(int capacity = 0)
    {
        characters = new List<BitmapCharacterInfo>(capacity);
        textSize = Vector2.zero;
        lineCount = 0;
        actualFontSize = 0f;
    }
}

public static class BitmapTextGenerator
{
    private static readonly StringBuilder s_StringBuilder = new StringBuilder();
    private static readonly List<string> s_Lines = new List<string>();
    private static readonly List<float> s_LineWidths = new List<float>();

    /// <summary>
    /// Generate bitmap text layout information
    /// </summary>
    public static BitmapTextInfo GenerateText(string text, BitmapFont font, BitmapTextSettings settings, Color32 color)
    {
        var textInfo = new BitmapTextInfo();

        if (string.IsNullOrEmpty(text) || font == null || !settings.IsValid())
        {
            return textInfo;
        }

        // Calculate font scale
        float fontScale = settings.GetFontScale(font.lineHeight);
        textInfo.actualFontSize = settings.fontSize;

        // Process text and handle rich text if enabled
        string processedText = settings.richText ? ProcessRichText(text) : text;

        // Split text into lines based on overflow settings
        SplitTextIntoLines(processedText, font, settings, fontScale);

        // Generate character positions
        GenerateCharacterPositions(font, settings, fontScale, color, ref textInfo);

        // Apply auto-sizing if enabled
        if (settings.autoSize && textInfo.textSize.x > settings.maxSize.x || textInfo.textSize.y > settings.maxSize.y)
        {
            ApplyAutoSizing(processedText, font, settings, color, ref textInfo);
        }

        return textInfo;
    }

    /// <summary>
    /// Process rich text tags (simplified implementation)
    /// </summary>
    private static string ProcessRichText(string text)
    {
        // For now, just remove basic rich text tags
        // TODO: Implement full rich text support with color, size, etc.
        s_StringBuilder.Clear();
        bool inTag = false;

        for (int i = 0; i < text.Length; i++)
        {
            char c = text[i];
            if (c == '<')
            {
                inTag = true;
            }
            else if (c == '>')
            {
                inTag = false;
            }
            else if (!inTag)
            {
                s_StringBuilder.Append(c);
            }
        }

        return s_StringBuilder.ToString();
    }

    /// <summary>
    /// Split text into lines based on overflow settings
    /// </summary>
    private static void SplitTextIntoLines(string text, BitmapFont font, BitmapTextSettings settings, float fontScale)
    {
        s_Lines.Clear();
        s_LineWidths.Clear();

        if (settings.overflow == BitmapTextOverflow.Overflow)
        {
            // No wrapping, treat as single line
            s_Lines.Add(text);
            s_LineWidths.Add(CalculateLineWidth(text, font, settings, fontScale));
            return;
        }

        float maxWidth = settings.maxSize.x;
        if (maxWidth <= 0)
        {
            s_Lines.Add(text);
            s_LineWidths.Add(CalculateLineWidth(text, font, settings, fontScale));
            return;
        }

        // Split by existing line breaks first
        string[] paragraphs = text.Split('\n');

        foreach (string paragraph in paragraphs)
        {
            if (settings.overflow == BitmapTextOverflow.Wrap)
            {
                WrapLine(paragraph, font, settings, fontScale, maxWidth);
            }
            else // Truncate
            {
                string truncated = TruncateLine(paragraph, font, settings, fontScale, maxWidth);
                s_Lines.Add(truncated);
                s_LineWidths.Add(CalculateLineWidth(truncated, font, settings, fontScale));
            }
        }
    }

    /// <summary>
    /// Wrap a line of text to fit within the specified width
    /// </summary>
    private static void WrapLine(string line, BitmapFont font, BitmapTextSettings settings, float fontScale, float maxWidth)
    {
        if (string.IsNullOrEmpty(line))
        {
            s_Lines.Add("");
            s_LineWidths.Add(0f);
            return;
        }

        string[] words = line.Split(' ');
        s_StringBuilder.Clear();
        float currentWidth = 0f;

        for (int i = 0; i < words.Length; i++)
        {
            string word = words[i];
            float wordWidth = CalculateLineWidth(word, font, settings, fontScale);
            float spaceWidth = (i > 0) ? font.spaceWidth * fontScale + settings.wordSpacing : 0f;

            if (currentWidth + spaceWidth + wordWidth <= maxWidth || s_StringBuilder.Length == 0)
            {
                if (s_StringBuilder.Length > 0)
                {
                    s_StringBuilder.Append(' ');
                    currentWidth += spaceWidth;
                }
                s_StringBuilder.Append(word);
                currentWidth += wordWidth;
            }
            else
            {
                // Start new line
                string currentLine = s_StringBuilder.ToString();
                s_Lines.Add(currentLine);
                s_LineWidths.Add(currentWidth);

                s_StringBuilder.Clear();
                s_StringBuilder.Append(word);
                currentWidth = wordWidth;
            }
        }

        // Add the last line
        if (s_StringBuilder.Length > 0)
        {
            s_Lines.Add(s_StringBuilder.ToString());
            s_LineWidths.Add(currentWidth);
        }
    }

    /// <summary>
    /// Truncate a line to fit within the specified width
    /// </summary>
    private static string TruncateLine(string line, BitmapFont font, BitmapTextSettings settings, float fontScale, float maxWidth)
    {
        if (string.IsNullOrEmpty(line))
            return line;

        float currentWidth = 0f;
        s_StringBuilder.Clear();

        for (int i = 0; i < line.Length; i++)
        {
            char c = line[i];
            BitmapGlyph glyph = font.GetGlyph(c);

            float charWidth = 0f;
            if (glyph != null && glyph.IsValid())
            {
                charWidth = glyph.xAdvance * fontScale;
            }
            else if (c == ' ')
            {
                charWidth = font.spaceWidth * fontScale;
            }

            if (i > 0)
                charWidth += settings.characterSpacing;

            if (currentWidth + charWidth > maxWidth)
                break;

            s_StringBuilder.Append(c);
            currentWidth += charWidth;
        }

        return s_StringBuilder.ToString();
    }

    /// <summary>
    /// Calculate the width of a line of text
    /// </summary>
    private static float CalculateLineWidth(string line, BitmapFont font, BitmapTextSettings settings, float fontScale)
    {
        if (string.IsNullOrEmpty(line))
            return 0f;

        float width = 0f;

        for (int i = 0; i < line.Length; i++)
        {
            char c = line[i];
            BitmapGlyph glyph = font.GetGlyph(c);

            if (glyph != null && glyph.IsValid())
            {
                width += glyph.xAdvance * fontScale;
            }
            else if (c == ' ')
            {
                width += font.spaceWidth * fontScale;
            }

            if (i > 0)
                width += settings.characterSpacing;
        }

        return width;
    }

    /// <summary>
    /// Generate character positions for all lines
    /// </summary>
    private static void GenerateCharacterPositions(BitmapFont font, BitmapTextSettings settings, float fontScale, Color32 color, ref BitmapTextInfo textInfo)
    {
        textInfo.lineCount = s_Lines.Count;
        if (textInfo.lineCount == 0)
            return;

        float lineHeight = settings.GetLineHeight(font.lineHeight * fontScale);
        float totalHeight = textInfo.lineCount * lineHeight;
        float maxWidth = 0f;

        // Calculate max width
        for (int i = 0; i < s_LineWidths.Count; i++)
        {
            if (s_LineWidths[i] > maxWidth)
                maxWidth = s_LineWidths[i];
        }

        textInfo.textSize = new Vector2(maxWidth, totalHeight);

        // Calculate starting Y position based on vertical alignment
        float startY = 0f;
        switch (settings.verticalAlignment)
        {
            case BitmapTextVerticalAlignment.Top:
                startY = 0f;
                break;
            case BitmapTextVerticalAlignment.Middle:
                startY = -totalHeight * 0.5f;
                break;
            case BitmapTextVerticalAlignment.Bottom:
                startY = -totalHeight;
                break;
        }

        // Generate character positions for each line
        for (int lineIndex = 0; lineIndex < s_Lines.Count; lineIndex++)
        {
            string line = s_Lines[lineIndex];
            float lineWidth = s_LineWidths[lineIndex];
            float y = startY + lineIndex * lineHeight;

            // Calculate starting X position based on alignment
            float startX = 0f;
            switch (settings.alignment)
            {
                case BitmapTextAlignment.Left:
                    startX = 0f;
                    break;
                case BitmapTextAlignment.Center:
                    startX = -lineWidth * 0.5f;
                    break;
                case BitmapTextAlignment.Right:
                    startX = -lineWidth;
                    break;
            }

            GenerateLineCharacters(line, font, settings, fontScale, color, startX, y, ref textInfo);
        }
    }

    /// <summary>
    /// Generate character positions for a single line
    /// </summary>
    private static void GenerateLineCharacters(string line, BitmapFont font, BitmapTextSettings settings, float fontScale, Color32 color, float startX, float y, ref BitmapTextInfo textInfo)
    {
        float x = startX;

        for (int i = 0; i < line.Length; i++)
        {
            char c = line[i];

            if (c == ' ')
            {
                x += font.spaceWidth * fontScale + settings.wordSpacing;
                continue;
            }

            BitmapGlyph glyph = font.GetGlyph(c);
            if (glyph != null && glyph.IsValid())
            {
                Vector2 position = new Vector2(
                    x + glyph.xOffset * fontScale,
                    y + glyph.yOffset * fontScale
                );

                var charInfo = new BitmapCharacterInfo(glyph, position, color, fontScale);
                textInfo.characters.Add(charInfo);

                x += glyph.xAdvance * fontScale;
            }

            if (i > 0)
                x += settings.characterSpacing;
        }
    }

    /// <summary>
    /// Apply auto-sizing to fit text within bounds
    /// </summary>
    private static void ApplyAutoSizing(string text, BitmapFont font, BitmapTextSettings settings, Color32 color, ref BitmapTextInfo textInfo)
    {
        float minSize = settings.minFontSize;
        float maxSize = settings.maxFontSize;
        float targetSize = settings.fontSize;

        // Binary search for optimal font size
        for (int iteration = 0; iteration < 10; iteration++) // Limit iterations
        {
            var testSettings = settings.Clone();
            testSettings.fontSize = targetSize;

            var testInfo = GenerateText(text, font, testSettings, color);

            bool fitsWidth = testInfo.textSize.x <= settings.maxSize.x || settings.maxSize.x <= 0;
            bool fitsHeight = testInfo.textSize.y <= settings.maxSize.y || settings.maxSize.y <= 0;

            if (fitsWidth && fitsHeight)
            {
                // Text fits, try larger size
                minSize = targetSize;
                if (maxSize - minSize < 0.5f)
                {
                    textInfo = testInfo;
                    break;
                }
                targetSize = (targetSize + maxSize) * 0.5f;
            }
            else
            {
                // Text doesn't fit, try smaller size
                maxSize = targetSize;
                if (maxSize - minSize < 0.5f)
                {
                    testSettings.fontSize = minSize;
                    textInfo = GenerateText(text, font, testSettings, color);
                    break;
                }
                targetSize = (minSize + targetSize) * 0.5f;
            }
        }
    }

    /// <summary>
    /// Generate mesh data from bitmap text info
    /// </summary>
    public static void PopulateMesh(VertexHelper vh, BitmapTextInfo textInfo, Vector2 pivot = default)
    {
        vh.Clear();

        if (textInfo.characters == null || textInfo.characters.Count == 0)
            return;

        // Apply pivot offset
        Vector2 pivotOffset = new Vector2(
            -textInfo.textSize.x * pivot.x,
            -textInfo.textSize.y * pivot.y
        );

        foreach (var charInfo in textInfo.characters)
        {
            if (!charInfo.isVisible || charInfo.glyph == null || !charInfo.glyph.IsValid())
                continue;

            AddCharacterToMesh(vh, charInfo, pivotOffset);
        }
    }

    /// <summary>
    /// Add a single character to the mesh
    /// </summary>
    private static void AddCharacterToMesh(VertexHelper vh, BitmapCharacterInfo charInfo, Vector2 offset)
    {
        var glyph = charInfo.glyph;
        var pos = charInfo.position + offset;
        var scale = charInfo.scale;

        // Calculate quad vertices
        float left = pos.x;
        float right = pos.x + glyph.width * scale;
        float bottom = pos.y;
        float top = pos.y + glyph.height * scale;

        // UV coordinates
        var uv = glyph.uvRect;

        // Add vertices
        int vertexIndex = vh.currentVertCount;

        vh.AddVert(new Vector3(left, bottom, 0), charInfo.color, new Vector2(uv.xMin, uv.yMin));
        vh.AddVert(new Vector3(left, top, 0), charInfo.color, new Vector2(uv.xMin, uv.yMax));
        vh.AddVert(new Vector3(right, top, 0), charInfo.color, new Vector2(uv.xMax, uv.yMax));
        vh.AddVert(new Vector3(right, bottom, 0), charInfo.color, new Vector2(uv.xMax, uv.yMin));

        // Add triangles
        vh.AddTriangle(vertexIndex, vertexIndex + 1, vertexIndex + 2);
        vh.AddTriangle(vertexIndex + 2, vertexIndex + 3, vertexIndex);
    }

    /// <summary>
    /// Calculate the preferred size for the given text
    /// </summary>
    public static Vector2 GetPreferredSize(string text, BitmapFont font, BitmapTextSettings settings)
    {
        if (string.IsNullOrEmpty(text) || font == null)
            return Vector2.zero;

        var tempSettings = settings.Clone();
        tempSettings.overflow = BitmapTextOverflow.Overflow; // Don't wrap for preferred size
        tempSettings.maxSize = new Vector2(float.MaxValue, float.MaxValue);

        var textInfo = GenerateText(text, font, tempSettings, Color.white);
        return textInfo.textSize;
    }
}

﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void BigInteger__ctor_mFEDEDA4A6AFC2BA534FEEF4EB3431B9C5AC3F9D9 (void);
extern void BigInteger__ctor_mA87DC2182BB2F6AA0209BD98E81C30BCE5B8BBAA (void);
extern void BigInteger__ctor_m25FA3F3C832BC621CD91ABFAC3535A2F4EE7B371 (void);
extern void BigInteger__ctor_mF6B2AC2AD4056AB6903913C56A92F565EB08BAFD (void);
extern void BigInteger__ctor_mB5E6337A77FC9888762DD29AAF54D2BBDD828D03 (void);
extern void BigInteger__ctor_m14A0AC47ABF1AF6DE7B653CED685D7AD11B54BE6 (void);
extern void BigInteger_get_MinusOne_m3FF0F44F8C3D6DFB66C00B61017E42E555585465 (void);
extern void BigInteger_GetHashCode_m2CB01C462C09EB23616010EB24E0A87A4D35D783 (void);
extern void BigInteger_Equals_mC711A33C4466DC2C053785E0A8A34B49A197EA1C (void);
extern void BigInteger_Equals_m36D3C46B170C61FBA8D78C5D4D52B6627DCB2314 (void);
extern void BigInteger_CompareTo_m1B9ADF53CC7255B4DE59A270AA1EA854E25E7296 (void);
extern void BigInteger_CompareTo_mFF2E7BDB82C698B3A3BFD93FD723629F9606B33E (void);
extern void BigInteger_TryWriteBytes_m4B86F875CA1C01790FE20D1A689DCA41D22326C0 (void);
extern void BigInteger_TryWriteOrCountBytes_mB22B053CE50BA54DF99F85EEAE05CA7ABDEF6441 (void);
extern void BigInteger_TryGetBytes_m015641ED36F51F9CA2D92665048EB5E0ED3C6DCD (void);
extern void BigInteger_ToString_m6AFB0DE9CD953DA8B015C31B3CC1FEF86D98A306 (void);
extern void BigInteger_ToString_mF67077A813661D27640565FC41346D65A155B3F6 (void);
extern void BigInteger_Add_m7544497BCCB4A4612A2CFC9F272FFA6E22F740D8 (void);
extern void BigInteger_Subtract_mA96B4B988F08F5DCADABE0B27BA23D970EBFF88D (void);
extern void BigInteger_op_Implicit_m9B2DA118DD0522DFA6983787CC9C09DACDB5469B (void);
extern void BigInteger_op_Implicit_m0E4A1C7B1F24EB10AE57608F2EBA5B127006D850 (void);
extern void BigInteger_op_Implicit_mB409693E4A4DD397B321CE56E748831FD0B67A06 (void);
extern void BigInteger_op_Explicit_m381F80B7F65BFEC7F2597DCDB24622F7F32E6848 (void);
extern void BigInteger_op_LeftShift_m9C9DD1A22775C31A01650FEB59A91D4A58B719B3 (void);
extern void BigInteger_op_RightShift_mE0645B6555F95B4838A8F471584DCA026848F7AB (void);
extern void BigInteger_op_Addition_m895A3ED96D07E1A6E4AD97483EDC256107A31B12 (void);
extern void BigInteger_GetPartsForBitManipulation_m679473F863B7A311A59B4B58F26F1D30ADFEEADF (void);
extern void BigInteger_GetDiffLength_m86F9E98613660CB092EA24BC931C98B60E802902 (void);
extern void BigInteger__cctor_mEE4D16FA07B1AD31F26502B9C54DB72DB4D1C626 (void);
extern void BigIntegerCalculator_Add_m6B363515A5150645D0CAD0DDF4B04348B95C29C3 (void);
extern void BigIntegerCalculator_Add_mE49BAD7CDF073CF2D2A2DEABCF11AEE5CC239298 (void);
extern void BigIntegerCalculator_Add_m3CB52FDB28E32D96E1400C839ED8E7F51726A814 (void);
extern void BigIntegerCalculator_Subtract_mB336D562EA742D2092AFD220202FC15D9A2791A7 (void);
extern void BigIntegerCalculator_Subtract_m8BFB5D1D8B089DA2BA5AC0EB0A45D98A6F6AC681 (void);
extern void BigIntegerCalculator_Subtract_mAD5DABE3E13103E8ADCBDA013BC874F17839AC45 (void);
extern void BigIntegerCalculator_Compare_mD61B527BE1032F19B97519458C7AEAD60CBB8774 (void);
extern void BigIntegerCalculator__cctor_mB0C09E685F08931052AF8D95CEDCCFD3D914D03B (void);
extern void BigNumber_ParseFormatSpecifier_m642DCFB18345FAC78777645E487EE8279BA17073 (void);
extern void BigNumber_FormatBigIntegerToHex_m5BD805D186861A2FDE1CB6D7C8BF730B78B251BE (void);
extern void BigNumber_FormatBigInteger_m1C6793BBD747BBC3E729A18FDAF8A5C814C80DC4 (void);
extern void BigNumber_FormatBigInteger_m626A9168DC2BBB348E31E47313E83D88263747AA (void);
extern void NumericsHelpers_DangerousMakeTwosComplement_m4B6EED2DF0E8C7B5171CDC7BBE5A7A3D68C79D4B (void);
extern void NumericsHelpers_MakeUlong_mAB879C53817E4E9BE9C649F09EEE0CB11DE53514 (void);
extern void NumericsHelpers_Abs_m82DD45E6FEBF5DAB9533B31E5BC62DC5EF37568A (void);
extern void NumericsHelpers_CombineHash_m16177FC379833624A7C14834FA38ADE527A53CCE (void);
extern void NumericsHelpers_CombineHash_m676E72BC5EC287D4C9602A8D876D24E2CBFDC776 (void);
extern void FormatProvider_FormatBigInteger_m1FBB2719E6A285F3F9EE6A4EE4B95AB6C50C0BAB (void);
extern void Number_Int32ToDecChars_mA94E1FFBC0C831A23C6A974FC11018B9E6F9ED4E (void);
extern void Number_ParseFormatSpecifier_mC2A7C10F8899ED9BA94E9D9EFE6FDDCADE68618A (void);
extern void Number_NumberToString_mB02B6AFBEEF66C19BB094F00189CC8E15A16AD18 (void);
extern void Number_FormatCurrency_m097DB55A0D1FC114CC86AF8F08F56A8AFEDC93DD (void);
extern void Number_wcslen_mCD526D9E32ECC29B992889CBDBC18EFF2F3F7CC4 (void);
extern void Number_FormatFixed_m0AE79A769FD61DE736216A34F723B8D7D793C1BB (void);
extern void Number_FormatNumber_m24CDBE74E5644DDE85C931202384C04F91951EA6 (void);
extern void Number_FormatScientific_m2F27814915B4A407DE4F3692B2EECE8AD267C358 (void);
extern void Number_FormatExponent_m2194D98B7488C2DE4AB59E0AFECDCF4D258412F1 (void);
extern void Number_FormatGeneral_m68D4F0A31B064E3FDF311EFF410D774C0D3BAF0A (void);
extern void Number_FormatPercent_m765FF9BE8896DA80FDBC469B9EB40732C521B85D (void);
extern void Number_RoundNumber_m4CF4E60F6BB2595CAF7D220275299620A954CAA0 (void);
extern void Number_FindSection_mC2D1C69F848ACAB296ADB63DD0D87CF39C446849 (void);
extern void Number_NumberToStringFormat_mA407C99BE332392E17203E2A9BDC5544DDF89090 (void);
extern void Number__cctor_m2D3E19E23CA70D2ABBB814BA5694680EE8AFE430 (void);
extern void NumberBuffer_get_digits_mB2D6183F180088A57C57ED5FBA7BDD4CDF4A369A (void);
extern void ValueStringBuilder__ctor_m0660F060D846CA37202B1BEEE35D26DAC2B6AFF6 (void);
extern void ValueStringBuilder_get_Length_m5D0F5925DA1601B18CF1ADC62D8750F955DC3F6B (void);
extern void ValueStringBuilder_ToString_mAB4C26796468880783F57E543C5102DE83C10BCE (void);
extern void ValueStringBuilder_TryCopyTo_m1ADDDEC065D0CCAB6A61D871D7272522B95F801E (void);
extern void ValueStringBuilder_Insert_m658B685FEAD8D7A9935D2720FAAAB05382942E2C (void);
extern void ValueStringBuilder_Append_mBB79BFE6EAB412D689B7D6675A6E0BC3F6FCDFCC (void);
extern void ValueStringBuilder_Append_m4F9C03D9B78FD7AE877AAC57178D2F84AD2956CF (void);
extern void ValueStringBuilder_AppendSlow_mF1E32E44AE0CD50A28EE3E945C8CCE40FB184526 (void);
extern void ValueStringBuilder_Append_m4E46E62A9444CE58033DDB6EC5D9AE7CF02B48B0 (void);
extern void ValueStringBuilder_Append_m58580EDC69E4BCFEFFE0A266FE36684AC660BBD6 (void);
extern void ValueStringBuilder_AppendSpan_m0D80091AA43B5BD4944DCD4D8729310FEAF11382 (void);
extern void ValueStringBuilder_GrowAndAppend_mDB5F96AAA8A9CAD064B96D8A182D84C76BF24F46 (void);
extern void ValueStringBuilder_Grow_m8107401166703C9CB129685FA6F78F26615FC6A9 (void);
extern void ValueStringBuilder_Dispose_m3BC81A03C95916FF7171ADB0CF6F16E2366A1392 (void);
static Il2CppMethodPointer s_methodPointers[77] = 
{
	BigInteger__ctor_mFEDEDA4A6AFC2BA534FEEF4EB3431B9C5AC3F9D9,
	BigInteger__ctor_mA87DC2182BB2F6AA0209BD98E81C30BCE5B8BBAA,
	BigInteger__ctor_m25FA3F3C832BC621CD91ABFAC3535A2F4EE7B371,
	BigInteger__ctor_mF6B2AC2AD4056AB6903913C56A92F565EB08BAFD,
	BigInteger__ctor_mB5E6337A77FC9888762DD29AAF54D2BBDD828D03,
	BigInteger__ctor_m14A0AC47ABF1AF6DE7B653CED685D7AD11B54BE6,
	BigInteger_get_MinusOne_m3FF0F44F8C3D6DFB66C00B61017E42E555585465,
	BigInteger_GetHashCode_m2CB01C462C09EB23616010EB24E0A87A4D35D783,
	BigInteger_Equals_mC711A33C4466DC2C053785E0A8A34B49A197EA1C,
	BigInteger_Equals_m36D3C46B170C61FBA8D78C5D4D52B6627DCB2314,
	BigInteger_CompareTo_m1B9ADF53CC7255B4DE59A270AA1EA854E25E7296,
	BigInteger_CompareTo_mFF2E7BDB82C698B3A3BFD93FD723629F9606B33E,
	BigInteger_TryWriteBytes_m4B86F875CA1C01790FE20D1A689DCA41D22326C0,
	BigInteger_TryWriteOrCountBytes_mB22B053CE50BA54DF99F85EEAE05CA7ABDEF6441,
	BigInteger_TryGetBytes_m015641ED36F51F9CA2D92665048EB5E0ED3C6DCD,
	BigInteger_ToString_m6AFB0DE9CD953DA8B015C31B3CC1FEF86D98A306,
	BigInteger_ToString_mF67077A813661D27640565FC41346D65A155B3F6,
	BigInteger_Add_m7544497BCCB4A4612A2CFC9F272FFA6E22F740D8,
	BigInteger_Subtract_mA96B4B988F08F5DCADABE0B27BA23D970EBFF88D,
	BigInteger_op_Implicit_m9B2DA118DD0522DFA6983787CC9C09DACDB5469B,
	BigInteger_op_Implicit_m0E4A1C7B1F24EB10AE57608F2EBA5B127006D850,
	BigInteger_op_Implicit_mB409693E4A4DD397B321CE56E748831FD0B67A06,
	BigInteger_op_Explicit_m381F80B7F65BFEC7F2597DCDB24622F7F32E6848,
	BigInteger_op_LeftShift_m9C9DD1A22775C31A01650FEB59A91D4A58B719B3,
	BigInteger_op_RightShift_mE0645B6555F95B4838A8F471584DCA026848F7AB,
	BigInteger_op_Addition_m895A3ED96D07E1A6E4AD97483EDC256107A31B12,
	BigInteger_GetPartsForBitManipulation_m679473F863B7A311A59B4B58F26F1D30ADFEEADF,
	BigInteger_GetDiffLength_m86F9E98613660CB092EA24BC931C98B60E802902,
	BigInteger__cctor_mEE4D16FA07B1AD31F26502B9C54DB72DB4D1C626,
	BigIntegerCalculator_Add_m6B363515A5150645D0CAD0DDF4B04348B95C29C3,
	BigIntegerCalculator_Add_mE49BAD7CDF073CF2D2A2DEABCF11AEE5CC239298,
	BigIntegerCalculator_Add_m3CB52FDB28E32D96E1400C839ED8E7F51726A814,
	BigIntegerCalculator_Subtract_mB336D562EA742D2092AFD220202FC15D9A2791A7,
	BigIntegerCalculator_Subtract_m8BFB5D1D8B089DA2BA5AC0EB0A45D98A6F6AC681,
	BigIntegerCalculator_Subtract_mAD5DABE3E13103E8ADCBDA013BC874F17839AC45,
	BigIntegerCalculator_Compare_mD61B527BE1032F19B97519458C7AEAD60CBB8774,
	BigIntegerCalculator__cctor_mB0C09E685F08931052AF8D95CEDCCFD3D914D03B,
	BigNumber_ParseFormatSpecifier_m642DCFB18345FAC78777645E487EE8279BA17073,
	BigNumber_FormatBigIntegerToHex_m5BD805D186861A2FDE1CB6D7C8BF730B78B251BE,
	BigNumber_FormatBigInteger_m1C6793BBD747BBC3E729A18FDAF8A5C814C80DC4,
	BigNumber_FormatBigInteger_m626A9168DC2BBB348E31E47313E83D88263747AA,
	NumericsHelpers_DangerousMakeTwosComplement_m4B6EED2DF0E8C7B5171CDC7BBE5A7A3D68C79D4B,
	NumericsHelpers_MakeUlong_mAB879C53817E4E9BE9C649F09EEE0CB11DE53514,
	NumericsHelpers_Abs_m82DD45E6FEBF5DAB9533B31E5BC62DC5EF37568A,
	NumericsHelpers_CombineHash_m16177FC379833624A7C14834FA38ADE527A53CCE,
	NumericsHelpers_CombineHash_m676E72BC5EC287D4C9602A8D876D24E2CBFDC776,
	FormatProvider_FormatBigInteger_m1FBB2719E6A285F3F9EE6A4EE4B95AB6C50C0BAB,
	Number_Int32ToDecChars_mA94E1FFBC0C831A23C6A974FC11018B9E6F9ED4E,
	Number_ParseFormatSpecifier_mC2A7C10F8899ED9BA94E9D9EFE6FDDCADE68618A,
	Number_NumberToString_mB02B6AFBEEF66C19BB094F00189CC8E15A16AD18,
	Number_FormatCurrency_m097DB55A0D1FC114CC86AF8F08F56A8AFEDC93DD,
	Number_wcslen_mCD526D9E32ECC29B992889CBDBC18EFF2F3F7CC4,
	Number_FormatFixed_m0AE79A769FD61DE736216A34F723B8D7D793C1BB,
	Number_FormatNumber_m24CDBE74E5644DDE85C931202384C04F91951EA6,
	Number_FormatScientific_m2F27814915B4A407DE4F3692B2EECE8AD267C358,
	Number_FormatExponent_m2194D98B7488C2DE4AB59E0AFECDCF4D258412F1,
	Number_FormatGeneral_m68D4F0A31B064E3FDF311EFF410D774C0D3BAF0A,
	Number_FormatPercent_m765FF9BE8896DA80FDBC469B9EB40732C521B85D,
	Number_RoundNumber_m4CF4E60F6BB2595CAF7D220275299620A954CAA0,
	Number_FindSection_mC2D1C69F848ACAB296ADB63DD0D87CF39C446849,
	Number_NumberToStringFormat_mA407C99BE332392E17203E2A9BDC5544DDF89090,
	Number__cctor_m2D3E19E23CA70D2ABBB814BA5694680EE8AFE430,
	NumberBuffer_get_digits_mB2D6183F180088A57C57ED5FBA7BDD4CDF4A369A,
	ValueStringBuilder__ctor_m0660F060D846CA37202B1BEEE35D26DAC2B6AFF6,
	ValueStringBuilder_get_Length_m5D0F5925DA1601B18CF1ADC62D8750F955DC3F6B,
	ValueStringBuilder_ToString_mAB4C26796468880783F57E543C5102DE83C10BCE,
	ValueStringBuilder_TryCopyTo_m1ADDDEC065D0CCAB6A61D871D7272522B95F801E,
	ValueStringBuilder_Insert_m658B685FEAD8D7A9935D2720FAAAB05382942E2C,
	ValueStringBuilder_Append_mBB79BFE6EAB412D689B7D6675A6E0BC3F6FCDFCC,
	ValueStringBuilder_Append_m4F9C03D9B78FD7AE877AAC57178D2F84AD2956CF,
	ValueStringBuilder_AppendSlow_mF1E32E44AE0CD50A28EE3E945C8CCE40FB184526,
	ValueStringBuilder_Append_m4E46E62A9444CE58033DDB6EC5D9AE7CF02B48B0,
	ValueStringBuilder_Append_m58580EDC69E4BCFEFFE0A266FE36684AC660BBD6,
	ValueStringBuilder_AppendSpan_m0D80091AA43B5BD4944DCD4D8729310FEAF11382,
	ValueStringBuilder_GrowAndAppend_mDB5F96AAA8A9CAD064B96D8A182D84C76BF24F46,
	ValueStringBuilder_Grow_m8107401166703C9CB129685FA6F78F26615FC6A9,
	ValueStringBuilder_Dispose_m3BC81A03C95916FF7171ADB0CF6F16E2366A1392,
};
extern void BigInteger__ctor_mFEDEDA4A6AFC2BA534FEEF4EB3431B9C5AC3F9D9_AdjustorThunk (void);
extern void BigInteger__ctor_mA87DC2182BB2F6AA0209BD98E81C30BCE5B8BBAA_AdjustorThunk (void);
extern void BigInteger__ctor_m25FA3F3C832BC621CD91ABFAC3535A2F4EE7B371_AdjustorThunk (void);
extern void BigInteger__ctor_mF6B2AC2AD4056AB6903913C56A92F565EB08BAFD_AdjustorThunk (void);
extern void BigInteger__ctor_mB5E6337A77FC9888762DD29AAF54D2BBDD828D03_AdjustorThunk (void);
extern void BigInteger__ctor_m14A0AC47ABF1AF6DE7B653CED685D7AD11B54BE6_AdjustorThunk (void);
extern void BigInteger_GetHashCode_m2CB01C462C09EB23616010EB24E0A87A4D35D783_AdjustorThunk (void);
extern void BigInteger_Equals_mC711A33C4466DC2C053785E0A8A34B49A197EA1C_AdjustorThunk (void);
extern void BigInteger_Equals_m36D3C46B170C61FBA8D78C5D4D52B6627DCB2314_AdjustorThunk (void);
extern void BigInteger_CompareTo_m1B9ADF53CC7255B4DE59A270AA1EA854E25E7296_AdjustorThunk (void);
extern void BigInteger_CompareTo_mFF2E7BDB82C698B3A3BFD93FD723629F9606B33E_AdjustorThunk (void);
extern void BigInteger_TryWriteBytes_m4B86F875CA1C01790FE20D1A689DCA41D22326C0_AdjustorThunk (void);
extern void BigInteger_TryWriteOrCountBytes_mB22B053CE50BA54DF99F85EEAE05CA7ABDEF6441_AdjustorThunk (void);
extern void BigInteger_TryGetBytes_m015641ED36F51F9CA2D92665048EB5E0ED3C6DCD_AdjustorThunk (void);
extern void BigInteger_ToString_m6AFB0DE9CD953DA8B015C31B3CC1FEF86D98A306_AdjustorThunk (void);
extern void BigInteger_ToString_mF67077A813661D27640565FC41346D65A155B3F6_AdjustorThunk (void);
extern void NumberBuffer_get_digits_mB2D6183F180088A57C57ED5FBA7BDD4CDF4A369A_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[17] = 
{
	{ 0x06000001, BigInteger__ctor_mFEDEDA4A6AFC2BA534FEEF4EB3431B9C5AC3F9D9_AdjustorThunk },
	{ 0x06000002, BigInteger__ctor_mA87DC2182BB2F6AA0209BD98E81C30BCE5B8BBAA_AdjustorThunk },
	{ 0x06000003, BigInteger__ctor_m25FA3F3C832BC621CD91ABFAC3535A2F4EE7B371_AdjustorThunk },
	{ 0x06000004, BigInteger__ctor_mF6B2AC2AD4056AB6903913C56A92F565EB08BAFD_AdjustorThunk },
	{ 0x06000005, BigInteger__ctor_mB5E6337A77FC9888762DD29AAF54D2BBDD828D03_AdjustorThunk },
	{ 0x06000006, BigInteger__ctor_m14A0AC47ABF1AF6DE7B653CED685D7AD11B54BE6_AdjustorThunk },
	{ 0x06000008, BigInteger_GetHashCode_m2CB01C462C09EB23616010EB24E0A87A4D35D783_AdjustorThunk },
	{ 0x06000009, BigInteger_Equals_mC711A33C4466DC2C053785E0A8A34B49A197EA1C_AdjustorThunk },
	{ 0x0600000A, BigInteger_Equals_m36D3C46B170C61FBA8D78C5D4D52B6627DCB2314_AdjustorThunk },
	{ 0x0600000B, BigInteger_CompareTo_m1B9ADF53CC7255B4DE59A270AA1EA854E25E7296_AdjustorThunk },
	{ 0x0600000C, BigInteger_CompareTo_mFF2E7BDB82C698B3A3BFD93FD723629F9606B33E_AdjustorThunk },
	{ 0x0600000D, BigInteger_TryWriteBytes_m4B86F875CA1C01790FE20D1A689DCA41D22326C0_AdjustorThunk },
	{ 0x0600000E, BigInteger_TryWriteOrCountBytes_mB22B053CE50BA54DF99F85EEAE05CA7ABDEF6441_AdjustorThunk },
	{ 0x0600000F, BigInteger_TryGetBytes_m015641ED36F51F9CA2D92665048EB5E0ED3C6DCD_AdjustorThunk },
	{ 0x06000010, BigInteger_ToString_m6AFB0DE9CD953DA8B015C31B3CC1FEF86D98A306_AdjustorThunk },
	{ 0x06000011, BigInteger_ToString_mF67077A813661D27640565FC41346D65A155B3F6_AdjustorThunk },
	{ 0x0600003F, NumberBuffer_get_digits_mB2D6183F180088A57C57ED5FBA7BDD4CDF4A369A_AdjustorThunk },
};
static const int32_t s_InvokerIndices[77] = 
{
	10629,
	10630,
	10682,
	2511,
	5309,
	5666,
	21221,
	12996,
	7736,
	7563,
	8713,
	8845,
	962,
	962,
	752,
	13052,
	4489,
	14868,
	14868,
	19860,
	19861,
	19862,
	20196,
	17122,
	17122,
	17121,
	15977,
	16233,
	21355,
	18015,
	18006,
	13984,
	18015,
	18006,
	13984,
	17842,
	21355,
	18209,
	13666,
	16391,
	13665,
	20847,
	18245,
	20741,
	18237,
	17822,
	13692,
	15547,
	18209,
	13983,
	14666,
	20193,
	13690,
	14666,
	13979,
	13987,
	13797,
	14666,
	18506,
	17806,
	15539,
	21355,
	12792,
	10302,
	12996,
	13052,
	3303,
	2668,
	10890,
	10682,
	10682,
	5807,
	4705,
	6701,
	10890,
	10629,
	13298,
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_System_Numerics_CodeGenModule;
const Il2CppCodeGenModule g_System_Numerics_CodeGenModule = 
{
	"System.Numerics.dll",
	77,
	s_methodPointers,
	17,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};

using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

[CustomEditor(typeof(BitmapFont))]
public class BitmapFontEditor : Editor
{
    private BitmapFont m_Target;
    private SerializedProperty m_FontNameProp;
    private SerializedProperty m_LineHeightProp;
    private SerializedProperty m_BaseLineProp;
    private SerializedProperty m_SpaceWidthProp;
    private SerializedProperty m_AtlasProp;
    private SerializedProperty m_MaterialProp;
    private SerializedProperty m_GlyphsProp;
    
    private Vector2 m_ScrollPosition;
    private bool m_ShowGlyphs = true;
    private string m_SearchFilter = "";
    
    private void OnEnable()
    {
        m_Target = target as BitmapFont;
        
        m_FontNameProp = serializedObject.FindProperty("m_FontName");
        m_LineHeightProp = serializedObject.FindProperty("m_LineHeight");
        m_BaseLineProp = serializedObject.FindProperty("m_BaseLine");
        m_SpaceWidthProp = serializedObject.FindProperty("m_SpaceWidth");
        m_AtlasProp = serializedObject.FindProperty("m_Atlas");
        m_MaterialProp = serializedObject.FindProperty("m_Material");
        m_GlyphsProp = serializedObject.FindProperty("m_Glyphs");
    }
    
    public override void OnInspectorGUI()
    {
        serializedObject.Update();
        
        EditorGUILayout.LabelField("Bitmap Font", EditorStyles.boldLabel);
        EditorGUILayout.Space();
        
        // Font Settings
        EditorGUILayout.LabelField("Font Settings", EditorStyles.boldLabel);
        EditorGUILayout.PropertyField(m_FontNameProp, new GUIContent("Font Name"));
        EditorGUILayout.PropertyField(m_LineHeightProp, new GUIContent("Line Height"));
        EditorGUILayout.PropertyField(m_BaseLineProp, new GUIContent("Base Line"));
        EditorGUILayout.PropertyField(m_SpaceWidthProp, new GUIContent("Space Width"));
        
        EditorGUILayout.Space();
        
        // Atlas and Material
        EditorGUILayout.LabelField("Atlas", EditorStyles.boldLabel);
        EditorGUILayout.PropertyField(m_AtlasProp, new GUIContent("Atlas Texture"));
        EditorGUILayout.PropertyField(m_MaterialProp, new GUIContent("Material"));
        
        EditorGUILayout.Space();
        
        // Glyphs section
        EditorGUILayout.BeginHorizontal();
        m_ShowGlyphs = EditorGUILayout.Foldout(m_ShowGlyphs, $"Glyphs ({m_GlyphsProp.arraySize})", true);
        
        if (GUILayout.Button("Add Glyph", GUILayout.Width(80)))
        {
            AddNewGlyph();
        }
        
        if (GUILayout.Button("Clear All", GUILayout.Width(80)))
        {
            if (EditorUtility.DisplayDialog("Clear All Glyphs", "Are you sure you want to remove all glyphs?", "Yes", "No"))
            {
                m_GlyphsProp.ClearArray();
            }
        }
        EditorGUILayout.EndHorizontal();
        
        if (m_ShowGlyphs)
        {
            EditorGUILayout.Space();
            
            // Search filter
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField("Search:", GUILayout.Width(50));
            m_SearchFilter = EditorGUILayout.TextField(m_SearchFilter);
            if (GUILayout.Button("Clear", GUILayout.Width(50)))
            {
                m_SearchFilter = "";
            }
            EditorGUILayout.EndHorizontal();
            
            EditorGUILayout.Space();
            
            // Glyphs list
            m_ScrollPosition = EditorGUILayout.BeginScrollView(m_ScrollPosition, GUILayout.Height(300));
            
            for (int i = 0; i < m_GlyphsProp.arraySize; i++)
            {
                var glyphProp = m_GlyphsProp.GetArrayElementAtIndex(i);
                if (glyphProp.objectReferenceValue == null)
                {
                    m_GlyphsProp.DeleteArrayElementAtIndex(i);
                    i--;
                    continue;
                }
                
                var glyph = glyphProp.objectReferenceValue as BitmapGlyph;
                if (glyph == null) continue;
                
                // Apply search filter
                if (!string.IsNullOrEmpty(m_SearchFilter))
                {
                    bool matches = glyph.glyphName.ToLower().Contains(m_SearchFilter.ToLower()) ||
                                   glyph.character.ToString().ToLower().Contains(m_SearchFilter.ToLower());
                    if (!matches) continue;
                }
                
                DrawGlyphItem(glyph, i);
            }
            
            EditorGUILayout.EndScrollView();
        }
        
        EditorGUILayout.Space();
        
        // Utility buttons
        EditorGUILayout.LabelField("Utilities", EditorStyles.boldLabel);
        EditorGUILayout.BeginHorizontal();
        
        if (GUILayout.Button("Generate from Sprites"))
        {
            GenerateFromSprites();
        }
        
        if (GUILayout.Button("Auto-Generate ASCII"))
        {
            GenerateASCIIGlyphs();
        }
        
        if (GUILayout.Button("Validate Glyphs"))
        {
            ValidateGlyphs();
        }
        
        EditorGUILayout.EndHorizontal();
        
        if (serializedObject.ApplyModifiedProperties())
        {
            m_Target.InvalidateLookupCache();
        }
    }
    
    private void DrawGlyphItem(BitmapGlyph glyph, int index)
    {
        EditorGUILayout.BeginVertical(EditorStyles.helpBox);
        
        EditorGUILayout.BeginHorizontal();
        
        // Glyph preview
        if (glyph.sprite != null)
        {
            var texture = glyph.sprite.texture;
            var rect = glyph.sprite.rect;
            var uv = new Rect(rect.x / texture.width, rect.y / texture.height, 
                             rect.width / texture.width, rect.height / texture.height);
            
            GUILayout.Label("", GUILayout.Width(32), GUILayout.Height(32));
            var lastRect = GUILayoutUtility.GetLastRect();
            GUI.DrawTextureWithTexCoords(lastRect, texture, uv);
        }
        else
        {
            GUILayout.Label("No Sprite", GUILayout.Width(32), GUILayout.Height(32));
        }
        
        // Glyph info
        EditorGUILayout.BeginVertical();
        EditorGUILayout.LabelField($"Name: {glyph.glyphName}");
        EditorGUILayout.LabelField($"Char: '{glyph.character}' ({(int)glyph.character})");
        EditorGUILayout.LabelField($"Size: {glyph.width}x{glyph.height}");
        EditorGUILayout.EndVertical();
        
        // Actions
        EditorGUILayout.BeginVertical(GUILayout.Width(80));
        if (GUILayout.Button("Edit"))
        {
            Selection.activeObject = glyph;
        }
        if (GUILayout.Button("Remove"))
        {
            m_GlyphsProp.DeleteArrayElementAtIndex(index);
        }
        EditorGUILayout.EndVertical();
        
        EditorGUILayout.EndHorizontal();
        EditorGUILayout.EndVertical();
    }
    
    private void AddNewGlyph()
    {
        var newGlyph = CreateInstance<BitmapGlyph>();
        newGlyph.name = "New Glyph";
        
        AssetDatabase.AddObjectToAsset(newGlyph, m_Target);
        
        m_GlyphsProp.arraySize++;
        var newElement = m_GlyphsProp.GetArrayElementAtIndex(m_GlyphsProp.arraySize - 1);
        newElement.objectReferenceValue = newGlyph;
        
        AssetDatabase.SaveAssets();
        AssetDatabase.Refresh();
    }
    
    private void GenerateFromSprites()
    {
        var sprites = Selection.GetFiltered<Sprite>(SelectionMode.Assets);
        if (sprites.Length == 0)
        {
            EditorUtility.DisplayDialog("No Sprites Selected", "Please select sprites in the Project window first.", "OK");
            return;
        }
        
        foreach (var sprite in sprites)
        {
            var glyph = CreateInstance<BitmapGlyph>();
            glyph.name = sprite.name;
            glyph.glyphName = sprite.name;
            glyph.sprite = sprite;
            
            // Try to parse character from sprite name
            if (sprite.name.Length == 1)
            {
                glyph.character = sprite.name[0];
            }
            
            AssetDatabase.AddObjectToAsset(glyph, m_Target);
            m_Target.SetGlyph(glyph);
        }
        
        AssetDatabase.SaveAssets();
        AssetDatabase.Refresh();
        EditorUtility.SetDirty(m_Target);
    }
    
    private void GenerateASCIIGlyphs()
    {
        // Generate basic ASCII characters (32-126)
        for (int i = 32; i <= 126; i++)
        {
            char c = (char)i;
            if (!m_Target.HasGlyph(c))
            {
                var glyph = CreateInstance<BitmapGlyph>();
                glyph.name = $"Char_{i}";
                glyph.glyphName = c.ToString();
                glyph.character = c;
                
                AssetDatabase.AddObjectToAsset(glyph, m_Target);
                m_Target.SetGlyph(glyph);
            }
        }
        
        AssetDatabase.SaveAssets();
        AssetDatabase.Refresh();
        EditorUtility.SetDirty(m_Target);
    }
    
    private void ValidateGlyphs()
    {
        int validCount = 0;
        int invalidCount = 0;
        
        foreach (var glyph in m_Target.glyphs)
        {
            if (glyph != null && glyph.IsValid())
                validCount++;
            else
                invalidCount++;
        }
        
        EditorUtility.DisplayDialog("Glyph Validation", 
            $"Valid glyphs: {validCount}\nInvalid glyphs: {invalidCount}", "OK");
    }
}

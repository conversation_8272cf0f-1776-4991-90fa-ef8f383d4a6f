﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void ParticleSystem_Emit_m27ED2FE38DEE11C9C98F7D30392BB5B39B6A16ED (void);
extern void ParticleSystem_Emit_m329091E7F55C972CE85A100CA624192AD8442688 (void);
extern void ParticleSystem_set_playbackSpeed_m2326EB08C96C18E22194986674D1CBB3358EDFF4 (void);
extern void ParticleSystem_Simulate_m514E1FCC76A6882650BD237E1D14B43ECAA97643 (void);
extern void ParticleSystem_Simulate_mE81EFF12AC1E2C08F3AE86DA7CF0D5CA4EA8F91F (void);
extern void ParticleSystem_Play_m4A59E0A2C7CA49EF75287A067305ABB314A16E62 (void);
extern void ParticleSystem_Play_mD943E601BFE16CB9BB5D1F5E6AED5C36F5F11EF5 (void);
extern void ParticleSystem_Stop_mB5761CB85083F593FFEC3D27931CACF1855A6326 (void);
extern void ParticleSystem_Stop_m6CA855033D5CE2D3AF7927B6709BC65DBCD632DF (void);
extern void ParticleSystem_Stop_m2D8D4967496EF0F5BFEF679C49A9E65A9646C423 (void);
extern void ParticleSystem_Clear_m26F18A1A58006417A0F66688E3124CFD1E880E11 (void);
extern void ParticleSystem_Clear_mE026AF9610248EB560530CD292FEED0F7571F732 (void);
extern void ParticleSystem_Emit_m3E9E6359087607E84E0D4D35844D80D9452AD72D (void);
extern void ParticleSystem_Emit_Internal_m54D6D9A78E8634846C9DB6445C0E0A0885E8A20E (void);
extern void ParticleSystem_Emit_m5AD1A3F02A19B61E3B0CC738FD498B52D19B65AA (void);
extern void ParticleSystem_EmitOld_Internal_mD22E235F6AB32455147A7DAF814AC8B4949C89D3 (void);
extern void ParticleSystem_get_main_mD86DFCD96150E2CE760CD2F37052BB3BCA33C189 (void);
extern void ParticleSystem_get_shape_mD7F072CC18587858138AA7B3A882995493AA7C80 (void);
extern void ParticleSystem_get_velocityOverLifetime_m84189E42E4B97EF44B66BC1FBB9FA0F15F05B535 (void);
extern void ParticleSystem_get_colorOverLifetime_mD8C72661EFE9BB063126752E744544EE2FF5814C (void);
extern void ParticleSystem_get_lights_m45FB86E05C48DCB25ED3481B9FE0966DF29EDD6C (void);
extern void ParticleSystem_get_trails_mDDDD23F6C3540ECD10E42BB0A4F9ECBADC294C66 (void);
extern void ParticleSystem__ctor_mABC4A409D6EC077A89AD3AEF259CE48D32EC47EF (void);
extern void ParticleSystem_Emit_Injected_mC5638D07E58C6EDB8C0CD3717A66A61CBA30C3A6 (void);
extern void MainModule__ctor_m5F7D2DD815C741DE3FA18A0C1BB2F2776612EF7A (void);
extern void MainModule_get_duration_mDCB80E6BD3B6B2DFB92E41FBCCA154D7E7150EB1 (void);
extern void MainModule_set_duration_m3B328F1E542EEE7BAE101B309512E0E3B4638B77 (void);
extern void MainModule_get_loop_m4F75CD377C0555635995F7C1F8D580517473EDF8 (void);
extern void MainModule_set_loop_mB3FC83A6DA8D248D15C53388D6B9B7711ADD89BE (void);
extern void MainModule_get_prewarm_m2B4B37F0B20CA2FDAB53315FC47E8FDCF3BBCD72 (void);
extern void MainModule_set_prewarm_m99964C46DE65B6797E9FD6A5CAF19E0B150D99F6 (void);
extern void MainModule_get_startDelay_m548FABFC02F1542B2B84EB3EBD9148C7CEB5DB6D (void);
extern void MainModule_set_startDelay_mFE9F9E7F79978F670904789F7F55B96F42434F69 (void);
extern void MainModule_get_startLifetime_m09FB4B0E4B44A5B76E2DB11F177F770682BEA654 (void);
extern void MainModule_set_startLifetime_mDFC2433853CB12C0C93C8BF16FEDB4EA2E0B3514 (void);
extern void MainModule_get_startLifetimeMultiplier_m635DF5E608211FCF706FBAD039AE0C140BCEDB7C (void);
extern void MainModule_get_startSpeed_m2F9A0CF5D05AA3522424EAD30CF226F0CDF8382B (void);
extern void MainModule_set_startSpeed_mE6041E4BA54F01117AF6B81B1E53C5C9C669AC75 (void);
extern void MainModule_get_startSize3D_mA7F91E70AE687D0561D563C38C582DE9299F3FE9 (void);
extern void MainModule_get_startSize_m7D92E17A7D36FB18A9D3ADA54D2D1DEDE89601FC (void);
extern void MainModule_set_startSize_m44C3A39EAF1AE9A526A126D93160128223BE827B (void);
extern void MainModule_get_startSizeX_mBDB87DD40CDA54AD0045EF86FB43763D192E6B6A (void);
extern void MainModule_set_startSizeX_m5F3F00611409B616D7B5B05EF3A6D7C35448C445 (void);
extern void MainModule_get_startSizeY_m9344B56673317BFEAF3A15C024BA57F034546199 (void);
extern void MainModule_set_startSizeY_mAA04D165042FFAEC3F17672ED84F066A8295C0FD (void);
extern void MainModule_get_startSizeZ_m16145FFC5083D901D99270682AA250CB429076A6 (void);
extern void MainModule_set_startSizeZ_mA1D0B1E2E9505DFDB42622E575FD91134ABE9A50 (void);
extern void MainModule_get_startColor_m24E66E583EB51341A885ABAE84114CBB37018781 (void);
extern void MainModule_set_startColor_m504911284CAC53A7C1B97B5D01BBDD2FA67D0E7A (void);
extern void MainModule_set_simulationSpeed_mFB44E06BF3F0D423636A6F37642CCC5722EBE0C6 (void);
extern void MainModule_get_duration_Injected_m9F3A5598AEAB180D2CA14AC34E8FDAE306DAE4BA (void);
extern void MainModule_set_duration_Injected_m704DD7FC97FA887A55B3FA4CC4ACF1156302F04A (void);
extern void MainModule_get_loop_Injected_mB2E11A7A29523C771FC0A9D2A4C061B25716E3F9 (void);
extern void MainModule_set_loop_Injected_mF673B0C6B2194F3C5FD0DCAB438350685E5CE815 (void);
extern void MainModule_get_prewarm_Injected_m7B17EFC0A2D51E04E264EE33B4E5B93F45718C12 (void);
extern void MainModule_set_prewarm_Injected_m46B87F73F4CFE5E2B6CD5DE12D49844CF228182D (void);
extern void MainModule_get_startDelay_Injected_m88F899E7E079C2DAA5BB36C716F0564A332AB71B (void);
extern void MainModule_set_startDelay_Injected_m1C4EE4D5526B23E404D764BDBF1C4447C16700D2 (void);
extern void MainModule_get_startLifetime_Injected_m36726268D85E0AEB1093360557DA04A4AB28ECAF (void);
extern void MainModule_set_startLifetime_Injected_m6EB92974DED01D362E15FC6708FEDEDF71BE850E (void);
extern void MainModule_get_startLifetimeMultiplier_Injected_m6B2A665AD38BD4060365A81678783E85B1AA7E08 (void);
extern void MainModule_get_startSpeed_Injected_m6CF0BA547DB427231676C501D0114A2005BDA8B9 (void);
extern void MainModule_set_startSpeed_Injected_m88DF340149F7C459065FCC37E3A1BCF12003354B (void);
extern void MainModule_get_startSize3D_Injected_m0F82FCEBC94206FDDA6889C7629507DED9540932 (void);
extern void MainModule_get_startSize_Injected_m25E9C5984FDD871FC424034F345BA8815FAB2A44 (void);
extern void MainModule_set_startSize_Injected_m1D975526AB75F6536C653FB3BBE6D57631CF9F79 (void);
extern void MainModule_get_startSizeX_Injected_mF2D47B2F03051413F8BAC92061CD4A1366B50E74 (void);
extern void MainModule_set_startSizeX_Injected_m88A66621CBA524C650A3E36693F7E13E98E5BB95 (void);
extern void MainModule_get_startSizeY_Injected_m2C5A9BF829EE13B3DFF0F323136096F9C7C69D11 (void);
extern void MainModule_set_startSizeY_Injected_m3A325F16F15C24AC478F593A34AD468A00CE2D36 (void);
extern void MainModule_get_startSizeZ_Injected_m7DE62BB9222064E975067A62DCB511961F79E372 (void);
extern void MainModule_set_startSizeZ_Injected_mAE393DF24DC90949D0A42B5252FBE9234B794343 (void);
extern void MainModule_get_startColor_Injected_m29B9E581238C0CEB45140E5B9E959486017F4CBE (void);
extern void MainModule_set_startColor_Injected_mF946A6488A011017014FD84C86FAC959F2909812 (void);
extern void MainModule_set_simulationSpeed_Injected_mA6676B56E0672D0CEC70F092F6070CDD1B19C265 (void);
extern void ShapeModule__ctor_m951B06AF6BDA194E8111B2B72C104562F41CB0AD (void);
extern void ShapeModule_get_enabled_m95D1E7C55963FC8A6BFEC95B7B22C2E1218DCC4F (void);
extern void ShapeModule_get_radius_m41AE7721BB6CDD1E9E91509DD2D7B60F0D302E90 (void);
extern void ShapeModule_set_radius_m37F79E13EB60FA39EAE36B49DAC4AC880416E89C (void);
extern void ShapeModule_get_enabled_Injected_m58FF6E62F85A41F0CAF8831939E513C2958AEDA1 (void);
extern void ShapeModule_get_radius_Injected_mD642B1633E69D260766DDF44AB5299D34C163AFF (void);
extern void ShapeModule_set_radius_Injected_m5C12B0E7933E1044FED367AFC72712AD04974B82 (void);
extern void Particle_set_lifetime_m29360AF093721364BF46996EE1D400256DB95911 (void);
extern void Particle_set_position_mE9103000DB4EA6CE09D25650F5A2915731F7A63E (void);
extern void Particle_set_velocity_mF4C1DE326CCABE480F44D3DF3873241E85A6303B (void);
extern void Particle_set_remainingLifetime_m3E58D8B3599B0BA6790D43022C3DF16E6896D018 (void);
extern void Particle_set_startLifetime_mCD0B16F2B1F2E2AEED84C4FCD85D5AD96F853A77 (void);
extern void Particle_set_startColor_mC3031F4238B0C003DFA5BF9AB4B3141B7CF71538 (void);
extern void Particle_set_randomSeed_mCC3C02CCBF9C0EA80E2CE01EC47AD30F31D5F6C2 (void);
extern void Particle_set_startSize_mBBEBF7365A4E68FF2044E2ECEACC562376EA4A1F (void);
extern void Particle_set_rotation3D_mC0B19BFEBA780F95C763DE14C80B29764E519D62 (void);
extern void Particle_set_angularVelocity3D_m56AE22FE7AFB178DD206EA2A7E0DA64B360D7EA8 (void);
extern void MinMaxCurve_get_mode_m3501926642278B695E0CDFE2E24B641A560B9526 (void);
extern void MinMaxCurve_set_mode_m6870C4CA0FE7AF1029F45040B827C9F0A6A80A86 (void);
extern void MinMaxCurve_get_curveMultiplier_m3C3EF4A373A3B2E51A4D27B88F7D1ADEA5B35936 (void);
extern void MinMaxCurve_set_curveMultiplier_m9FCF7F21AECE399ACD9FEB9ADEEBD93FD12DDABC (void);
extern void MinMaxCurve_get_curveMax_m455705B0A048A51461E94DACD1503EA1DEAB68B8 (void);
extern void MinMaxCurve_set_curveMax_m39B9E8AE4E62E874DA99AA7108C2C47C6CA333DF (void);
extern void MinMaxCurve_get_curveMin_m2F6041CAA8760D21D39A6204973B411D7109CF00 (void);
extern void MinMaxCurve_set_curveMin_m17ADE22B882B75CBE87836C85C477A05122D9E75 (void);
extern void MinMaxCurve_get_constantMax_mBC9884017B40BDF911C768F521950370A6B41463 (void);
extern void MinMaxCurve_set_constantMax_mDB7B011E5B1EA76429B2A1FFDE6C06E82D290DC1 (void);
extern void MinMaxCurve_get_constantMin_mB39AF05209B993DC31AB4247D9BE8D4F80E5D710 (void);
extern void MinMaxCurve_set_constantMin_mBF7CF2A9F167AD06C327EE6EA37BA427E1CE548C (void);
extern void MinMaxCurve_get_constant_m4F2B7693C00CC9FAEDE1DAD32FEEE893414FBE91 (void);
extern void MinMaxCurve_set_constant_mB6E4F22F67D3ED641FEBB387351F17451BB6A897 (void);
extern void MinMaxCurve_get_curve_mE735424B0F4A9F55699BD82254F159D4226F9661 (void);
extern void MinMaxCurve_set_curve_mC745A6526FBF122F96DA597997DFAB9D7D2529BF (void);
extern void MinMaxGradient__ctor_m1C456463FD9B1BD7A72A31C95A851A0ECF249858 (void);
extern void MinMaxGradient_get_mode_mC4B1A93FD93B41544AED855AB1FC520EC642AA98 (void);
extern void MinMaxGradient_set_mode_mCD15C79CD57AD7D400D7B171E10BC5E24F64295E (void);
extern void MinMaxGradient_get_gradientMax_m94B2E99380FFCC82F0387C4B1E685DDD5B9C7947 (void);
extern void MinMaxGradient_set_gradientMax_mC432C0AEA15FE74D6E6E6E2058BE039361D57ABB (void);
extern void MinMaxGradient_get_gradientMin_m906D46F5A5F72C83AF7651A910A97B616342DE4E (void);
extern void MinMaxGradient_set_gradientMin_m4152642243D268E1A95A7FC0B58E8F179A933609 (void);
extern void MinMaxGradient_get_colorMax_mB9E445D4D3E2788B10EA785B0BA8A76015FEDD1C (void);
extern void MinMaxGradient_set_colorMax_m28A85822E89FC80D1F5B49225DEC4ABCF86CAE33 (void);
extern void MinMaxGradient_get_colorMin_mD8698D56F1E88B7EE97638A3DC48AFC3FD2A6664 (void);
extern void MinMaxGradient_set_colorMin_m7D3D987AB86FE01E7BC4AD856AAEE703F18EF1EF (void);
extern void MinMaxGradient_get_color_m534E35D538D549F006E9F90E453D41B92FBAC3BF (void);
extern void MinMaxGradient_set_color_m0890DD6E249DE8552C04AFC4F959DD37EA05033B (void);
extern void MinMaxGradient_get_gradient_m012030F7AEAE915DCC71C80674564E3FFD02FF9D (void);
extern void MinMaxGradient_set_gradient_m6AAD335DD1A2BD5C091B608C604825C32CFB825D (void);
extern void MinMaxGradient_op_Implicit_m8568E0D8DD06940C6A710801B3FC41BB5307298B (void);
extern void VelocityOverLifetimeModule__ctor_m64A15E09916657384A99217570EB78F31A09E10A (void);
extern void VelocityOverLifetimeModule_get_enabled_m60395DDF6F516804EBFCD7AAE5AD0D741B279E4E (void);
extern void VelocityOverLifetimeModule_get_x_mB9014044B32BB551EBB06A9DF167DCA4036AA002 (void);
extern void VelocityOverLifetimeModule_set_x_m667368604CF8D09F133E552E21CBD3DC344281A0 (void);
extern void VelocityOverLifetimeModule_get_y_m2B00A63C14F173791F1CDD2ACB527F64A72D16BF (void);
extern void VelocityOverLifetimeModule_set_y_m4B08D16D7F274567CC18790702C8F3C53A443089 (void);
extern void VelocityOverLifetimeModule_get_z_mBEF172621B262D8E14742E3940DEC14757870B0C (void);
extern void VelocityOverLifetimeModule_set_z_m8B5A03C2D6E06CD7C6177BD321E9121E96D1D889 (void);
extern void VelocityOverLifetimeModule_get_enabled_Injected_m892B000A889163BFE44C5CD0BA411301CB3C9D2F (void);
extern void VelocityOverLifetimeModule_get_x_Injected_mB25014D3EB25B87EAA24E7A560BC225191458EB6 (void);
extern void VelocityOverLifetimeModule_set_x_Injected_m34CEB5AF1FD3B9EC2570E9A9050EBB2AB62EE0CE (void);
extern void VelocityOverLifetimeModule_get_y_Injected_m131EFAF627BC2547CF942C08DBDDBCB846E59EF7 (void);
extern void VelocityOverLifetimeModule_set_y_Injected_m6B46621FF40E2D7AA7FB2044BFBB3EA7D05E8360 (void);
extern void VelocityOverLifetimeModule_get_z_Injected_m8B37906252280916D3C5E21C156A7165FF9C2E14 (void);
extern void VelocityOverLifetimeModule_set_z_Injected_mC7111B47EC074D073099C6777DCA67C18E8C9E41 (void);
extern void ColorOverLifetimeModule__ctor_m3870A84520D71E559FF26F8592B98865763E42F3 (void);
extern void ColorOverLifetimeModule_get_enabled_mAB86D22805596728C643ABDF4887A246F1652B5B (void);
extern void ColorOverLifetimeModule_get_color_m320131C4BBF3B84729CE1D2F7DD7FB986746BDB1 (void);
extern void ColorOverLifetimeModule_set_color_m054950B589DA58FFD5BDBC10A70304BF89E3C86B (void);
extern void ColorOverLifetimeModule_get_enabled_Injected_m0C4A986E19B4B3293A09912B59A708B766D94DA8 (void);
extern void ColorOverLifetimeModule_get_color_Injected_mAA8F60C0A4B1612ED9BF8FF0F3A986F9A501C02D (void);
extern void ColorOverLifetimeModule_set_color_Injected_mC9CD2A5A5155CEB2DEFAE335517A371F4E9E08C0 (void);
extern void LightsModule__ctor_mB04B2490B71B56B4CCD7F5DFD983DAB98696FB4E (void);
extern void LightsModule_get_enabled_m944379FB6DC57FE6F4E11714BBCE194C51EA7DA9 (void);
extern void LightsModule_set_enabled_mCA61408EE4B83423BB92B032567BA63005379DCE (void);
extern void LightsModule_get_enabled_Injected_mB6E6F142EAC560A8EED900F6992280DE2689A9D9 (void);
extern void LightsModule_set_enabled_Injected_m382E8227B5C6FF79C08F00DE8DE091C571303D77 (void);
extern void TrailModule__ctor_m9FB482ADCAA6B390BC1D6A145D51594A8E13FE4C (void);
extern void TrailModule_get_enabled_m78262C91CD53A91AFC1472EA65B64EA0D064EE0C (void);
extern void TrailModule_set_enabled_m905BF8B82FF8F6FCCC430A05BD7DF0AE66A86C36 (void);
extern void TrailModule_get_enabled_Injected_mEBBB04187226C17D9FBFF6DAE30244B08A65BC0C (void);
extern void TrailModule_set_enabled_Injected_mAE458C40CB038CE8F438EBABFA456EF7A0CC7FA8 (void);
extern void ParticleSystemRenderer_GetMeshes_m3CA9AA8947C7F0468F6C0B7F1344D747EA43D440 (void);
static Il2CppMethodPointer s_methodPointers[157] = 
{
	ParticleSystem_Emit_m27ED2FE38DEE11C9C98F7D30392BB5B39B6A16ED,
	ParticleSystem_Emit_m329091E7F55C972CE85A100CA624192AD8442688,
	ParticleSystem_set_playbackSpeed_m2326EB08C96C18E22194986674D1CBB3358EDFF4,
	ParticleSystem_Simulate_m514E1FCC76A6882650BD237E1D14B43ECAA97643,
	ParticleSystem_Simulate_mE81EFF12AC1E2C08F3AE86DA7CF0D5CA4EA8F91F,
	ParticleSystem_Play_m4A59E0A2C7CA49EF75287A067305ABB314A16E62,
	ParticleSystem_Play_mD943E601BFE16CB9BB5D1F5E6AED5C36F5F11EF5,
	ParticleSystem_Stop_mB5761CB85083F593FFEC3D27931CACF1855A6326,
	ParticleSystem_Stop_m6CA855033D5CE2D3AF7927B6709BC65DBCD632DF,
	ParticleSystem_Stop_m2D8D4967496EF0F5BFEF679C49A9E65A9646C423,
	ParticleSystem_Clear_m26F18A1A58006417A0F66688E3124CFD1E880E11,
	ParticleSystem_Clear_mE026AF9610248EB560530CD292FEED0F7571F732,
	ParticleSystem_Emit_m3E9E6359087607E84E0D4D35844D80D9452AD72D,
	ParticleSystem_Emit_Internal_m54D6D9A78E8634846C9DB6445C0E0A0885E8A20E,
	ParticleSystem_Emit_m5AD1A3F02A19B61E3B0CC738FD498B52D19B65AA,
	ParticleSystem_EmitOld_Internal_mD22E235F6AB32455147A7DAF814AC8B4949C89D3,
	ParticleSystem_get_main_mD86DFCD96150E2CE760CD2F37052BB3BCA33C189,
	ParticleSystem_get_shape_mD7F072CC18587858138AA7B3A882995493AA7C80,
	ParticleSystem_get_velocityOverLifetime_m84189E42E4B97EF44B66BC1FBB9FA0F15F05B535,
	ParticleSystem_get_colorOverLifetime_mD8C72661EFE9BB063126752E744544EE2FF5814C,
	ParticleSystem_get_lights_m45FB86E05C48DCB25ED3481B9FE0966DF29EDD6C,
	ParticleSystem_get_trails_mDDDD23F6C3540ECD10E42BB0A4F9ECBADC294C66,
	ParticleSystem__ctor_mABC4A409D6EC077A89AD3AEF259CE48D32EC47EF,
	ParticleSystem_Emit_Injected_mC5638D07E58C6EDB8C0CD3717A66A61CBA30C3A6,
	MainModule__ctor_m5F7D2DD815C741DE3FA18A0C1BB2F2776612EF7A,
	MainModule_get_duration_mDCB80E6BD3B6B2DFB92E41FBCCA154D7E7150EB1,
	MainModule_set_duration_m3B328F1E542EEE7BAE101B309512E0E3B4638B77,
	MainModule_get_loop_m4F75CD377C0555635995F7C1F8D580517473EDF8,
	MainModule_set_loop_mB3FC83A6DA8D248D15C53388D6B9B7711ADD89BE,
	MainModule_get_prewarm_m2B4B37F0B20CA2FDAB53315FC47E8FDCF3BBCD72,
	MainModule_set_prewarm_m99964C46DE65B6797E9FD6A5CAF19E0B150D99F6,
	MainModule_get_startDelay_m548FABFC02F1542B2B84EB3EBD9148C7CEB5DB6D,
	MainModule_set_startDelay_mFE9F9E7F79978F670904789F7F55B96F42434F69,
	MainModule_get_startLifetime_m09FB4B0E4B44A5B76E2DB11F177F770682BEA654,
	MainModule_set_startLifetime_mDFC2433853CB12C0C93C8BF16FEDB4EA2E0B3514,
	MainModule_get_startLifetimeMultiplier_m635DF5E608211FCF706FBAD039AE0C140BCEDB7C,
	MainModule_get_startSpeed_m2F9A0CF5D05AA3522424EAD30CF226F0CDF8382B,
	MainModule_set_startSpeed_mE6041E4BA54F01117AF6B81B1E53C5C9C669AC75,
	MainModule_get_startSize3D_mA7F91E70AE687D0561D563C38C582DE9299F3FE9,
	MainModule_get_startSize_m7D92E17A7D36FB18A9D3ADA54D2D1DEDE89601FC,
	MainModule_set_startSize_m44C3A39EAF1AE9A526A126D93160128223BE827B,
	MainModule_get_startSizeX_mBDB87DD40CDA54AD0045EF86FB43763D192E6B6A,
	MainModule_set_startSizeX_m5F3F00611409B616D7B5B05EF3A6D7C35448C445,
	MainModule_get_startSizeY_m9344B56673317BFEAF3A15C024BA57F034546199,
	MainModule_set_startSizeY_mAA04D165042FFAEC3F17672ED84F066A8295C0FD,
	MainModule_get_startSizeZ_m16145FFC5083D901D99270682AA250CB429076A6,
	MainModule_set_startSizeZ_mA1D0B1E2E9505DFDB42622E575FD91134ABE9A50,
	MainModule_get_startColor_m24E66E583EB51341A885ABAE84114CBB37018781,
	MainModule_set_startColor_m504911284CAC53A7C1B97B5D01BBDD2FA67D0E7A,
	MainModule_set_simulationSpeed_mFB44E06BF3F0D423636A6F37642CCC5722EBE0C6,
	MainModule_get_duration_Injected_m9F3A5598AEAB180D2CA14AC34E8FDAE306DAE4BA,
	MainModule_set_duration_Injected_m704DD7FC97FA887A55B3FA4CC4ACF1156302F04A,
	MainModule_get_loop_Injected_mB2E11A7A29523C771FC0A9D2A4C061B25716E3F9,
	MainModule_set_loop_Injected_mF673B0C6B2194F3C5FD0DCAB438350685E5CE815,
	MainModule_get_prewarm_Injected_m7B17EFC0A2D51E04E264EE33B4E5B93F45718C12,
	MainModule_set_prewarm_Injected_m46B87F73F4CFE5E2B6CD5DE12D49844CF228182D,
	MainModule_get_startDelay_Injected_m88F899E7E079C2DAA5BB36C716F0564A332AB71B,
	MainModule_set_startDelay_Injected_m1C4EE4D5526B23E404D764BDBF1C4447C16700D2,
	MainModule_get_startLifetime_Injected_m36726268D85E0AEB1093360557DA04A4AB28ECAF,
	MainModule_set_startLifetime_Injected_m6EB92974DED01D362E15FC6708FEDEDF71BE850E,
	MainModule_get_startLifetimeMultiplier_Injected_m6B2A665AD38BD4060365A81678783E85B1AA7E08,
	MainModule_get_startSpeed_Injected_m6CF0BA547DB427231676C501D0114A2005BDA8B9,
	MainModule_set_startSpeed_Injected_m88DF340149F7C459065FCC37E3A1BCF12003354B,
	MainModule_get_startSize3D_Injected_m0F82FCEBC94206FDDA6889C7629507DED9540932,
	MainModule_get_startSize_Injected_m25E9C5984FDD871FC424034F345BA8815FAB2A44,
	MainModule_set_startSize_Injected_m1D975526AB75F6536C653FB3BBE6D57631CF9F79,
	MainModule_get_startSizeX_Injected_mF2D47B2F03051413F8BAC92061CD4A1366B50E74,
	MainModule_set_startSizeX_Injected_m88A66621CBA524C650A3E36693F7E13E98E5BB95,
	MainModule_get_startSizeY_Injected_m2C5A9BF829EE13B3DFF0F323136096F9C7C69D11,
	MainModule_set_startSizeY_Injected_m3A325F16F15C24AC478F593A34AD468A00CE2D36,
	MainModule_get_startSizeZ_Injected_m7DE62BB9222064E975067A62DCB511961F79E372,
	MainModule_set_startSizeZ_Injected_mAE393DF24DC90949D0A42B5252FBE9234B794343,
	MainModule_get_startColor_Injected_m29B9E581238C0CEB45140E5B9E959486017F4CBE,
	MainModule_set_startColor_Injected_mF946A6488A011017014FD84C86FAC959F2909812,
	MainModule_set_simulationSpeed_Injected_mA6676B56E0672D0CEC70F092F6070CDD1B19C265,
	ShapeModule__ctor_m951B06AF6BDA194E8111B2B72C104562F41CB0AD,
	ShapeModule_get_enabled_m95D1E7C55963FC8A6BFEC95B7B22C2E1218DCC4F,
	ShapeModule_get_radius_m41AE7721BB6CDD1E9E91509DD2D7B60F0D302E90,
	ShapeModule_set_radius_m37F79E13EB60FA39EAE36B49DAC4AC880416E89C,
	ShapeModule_get_enabled_Injected_m58FF6E62F85A41F0CAF8831939E513C2958AEDA1,
	ShapeModule_get_radius_Injected_mD642B1633E69D260766DDF44AB5299D34C163AFF,
	ShapeModule_set_radius_Injected_m5C12B0E7933E1044FED367AFC72712AD04974B82,
	Particle_set_lifetime_m29360AF093721364BF46996EE1D400256DB95911,
	Particle_set_position_mE9103000DB4EA6CE09D25650F5A2915731F7A63E,
	Particle_set_velocity_mF4C1DE326CCABE480F44D3DF3873241E85A6303B,
	Particle_set_remainingLifetime_m3E58D8B3599B0BA6790D43022C3DF16E6896D018,
	Particle_set_startLifetime_mCD0B16F2B1F2E2AEED84C4FCD85D5AD96F853A77,
	Particle_set_startColor_mC3031F4238B0C003DFA5BF9AB4B3141B7CF71538,
	Particle_set_randomSeed_mCC3C02CCBF9C0EA80E2CE01EC47AD30F31D5F6C2,
	Particle_set_startSize_mBBEBF7365A4E68FF2044E2ECEACC562376EA4A1F,
	Particle_set_rotation3D_mC0B19BFEBA780F95C763DE14C80B29764E519D62,
	Particle_set_angularVelocity3D_m56AE22FE7AFB178DD206EA2A7E0DA64B360D7EA8,
	MinMaxCurve_get_mode_m3501926642278B695E0CDFE2E24B641A560B9526,
	MinMaxCurve_set_mode_m6870C4CA0FE7AF1029F45040B827C9F0A6A80A86,
	MinMaxCurve_get_curveMultiplier_m3C3EF4A373A3B2E51A4D27B88F7D1ADEA5B35936,
	MinMaxCurve_set_curveMultiplier_m9FCF7F21AECE399ACD9FEB9ADEEBD93FD12DDABC,
	MinMaxCurve_get_curveMax_m455705B0A048A51461E94DACD1503EA1DEAB68B8,
	MinMaxCurve_set_curveMax_m39B9E8AE4E62E874DA99AA7108C2C47C6CA333DF,
	MinMaxCurve_get_curveMin_m2F6041CAA8760D21D39A6204973B411D7109CF00,
	MinMaxCurve_set_curveMin_m17ADE22B882B75CBE87836C85C477A05122D9E75,
	MinMaxCurve_get_constantMax_mBC9884017B40BDF911C768F521950370A6B41463,
	MinMaxCurve_set_constantMax_mDB7B011E5B1EA76429B2A1FFDE6C06E82D290DC1,
	MinMaxCurve_get_constantMin_mB39AF05209B993DC31AB4247D9BE8D4F80E5D710,
	MinMaxCurve_set_constantMin_mBF7CF2A9F167AD06C327EE6EA37BA427E1CE548C,
	MinMaxCurve_get_constant_m4F2B7693C00CC9FAEDE1DAD32FEEE893414FBE91,
	MinMaxCurve_set_constant_mB6E4F22F67D3ED641FEBB387351F17451BB6A897,
	MinMaxCurve_get_curve_mE735424B0F4A9F55699BD82254F159D4226F9661,
	MinMaxCurve_set_curve_mC745A6526FBF122F96DA597997DFAB9D7D2529BF,
	MinMaxGradient__ctor_m1C456463FD9B1BD7A72A31C95A851A0ECF249858,
	MinMaxGradient_get_mode_mC4B1A93FD93B41544AED855AB1FC520EC642AA98,
	MinMaxGradient_set_mode_mCD15C79CD57AD7D400D7B171E10BC5E24F64295E,
	MinMaxGradient_get_gradientMax_m94B2E99380FFCC82F0387C4B1E685DDD5B9C7947,
	MinMaxGradient_set_gradientMax_mC432C0AEA15FE74D6E6E6E2058BE039361D57ABB,
	MinMaxGradient_get_gradientMin_m906D46F5A5F72C83AF7651A910A97B616342DE4E,
	MinMaxGradient_set_gradientMin_m4152642243D268E1A95A7FC0B58E8F179A933609,
	MinMaxGradient_get_colorMax_mB9E445D4D3E2788B10EA785B0BA8A76015FEDD1C,
	MinMaxGradient_set_colorMax_m28A85822E89FC80D1F5B49225DEC4ABCF86CAE33,
	MinMaxGradient_get_colorMin_mD8698D56F1E88B7EE97638A3DC48AFC3FD2A6664,
	MinMaxGradient_set_colorMin_m7D3D987AB86FE01E7BC4AD856AAEE703F18EF1EF,
	MinMaxGradient_get_color_m534E35D538D549F006E9F90E453D41B92FBAC3BF,
	MinMaxGradient_set_color_m0890DD6E249DE8552C04AFC4F959DD37EA05033B,
	MinMaxGradient_get_gradient_m012030F7AEAE915DCC71C80674564E3FFD02FF9D,
	MinMaxGradient_set_gradient_m6AAD335DD1A2BD5C091B608C604825C32CFB825D,
	MinMaxGradient_op_Implicit_m8568E0D8DD06940C6A710801B3FC41BB5307298B,
	VelocityOverLifetimeModule__ctor_m64A15E09916657384A99217570EB78F31A09E10A,
	VelocityOverLifetimeModule_get_enabled_m60395DDF6F516804EBFCD7AAE5AD0D741B279E4E,
	VelocityOverLifetimeModule_get_x_mB9014044B32BB551EBB06A9DF167DCA4036AA002,
	VelocityOverLifetimeModule_set_x_m667368604CF8D09F133E552E21CBD3DC344281A0,
	VelocityOverLifetimeModule_get_y_m2B00A63C14F173791F1CDD2ACB527F64A72D16BF,
	VelocityOverLifetimeModule_set_y_m4B08D16D7F274567CC18790702C8F3C53A443089,
	VelocityOverLifetimeModule_get_z_mBEF172621B262D8E14742E3940DEC14757870B0C,
	VelocityOverLifetimeModule_set_z_m8B5A03C2D6E06CD7C6177BD321E9121E96D1D889,
	VelocityOverLifetimeModule_get_enabled_Injected_m892B000A889163BFE44C5CD0BA411301CB3C9D2F,
	VelocityOverLifetimeModule_get_x_Injected_mB25014D3EB25B87EAA24E7A560BC225191458EB6,
	VelocityOverLifetimeModule_set_x_Injected_m34CEB5AF1FD3B9EC2570E9A9050EBB2AB62EE0CE,
	VelocityOverLifetimeModule_get_y_Injected_m131EFAF627BC2547CF942C08DBDDBCB846E59EF7,
	VelocityOverLifetimeModule_set_y_Injected_m6B46621FF40E2D7AA7FB2044BFBB3EA7D05E8360,
	VelocityOverLifetimeModule_get_z_Injected_m8B37906252280916D3C5E21C156A7165FF9C2E14,
	VelocityOverLifetimeModule_set_z_Injected_mC7111B47EC074D073099C6777DCA67C18E8C9E41,
	ColorOverLifetimeModule__ctor_m3870A84520D71E559FF26F8592B98865763E42F3,
	ColorOverLifetimeModule_get_enabled_mAB86D22805596728C643ABDF4887A246F1652B5B,
	ColorOverLifetimeModule_get_color_m320131C4BBF3B84729CE1D2F7DD7FB986746BDB1,
	ColorOverLifetimeModule_set_color_m054950B589DA58FFD5BDBC10A70304BF89E3C86B,
	ColorOverLifetimeModule_get_enabled_Injected_m0C4A986E19B4B3293A09912B59A708B766D94DA8,
	ColorOverLifetimeModule_get_color_Injected_mAA8F60C0A4B1612ED9BF8FF0F3A986F9A501C02D,
	ColorOverLifetimeModule_set_color_Injected_mC9CD2A5A5155CEB2DEFAE335517A371F4E9E08C0,
	LightsModule__ctor_mB04B2490B71B56B4CCD7F5DFD983DAB98696FB4E,
	LightsModule_get_enabled_m944379FB6DC57FE6F4E11714BBCE194C51EA7DA9,
	LightsModule_set_enabled_mCA61408EE4B83423BB92B032567BA63005379DCE,
	LightsModule_get_enabled_Injected_mB6E6F142EAC560A8EED900F6992280DE2689A9D9,
	LightsModule_set_enabled_Injected_m382E8227B5C6FF79C08F00DE8DE091C571303D77,
	TrailModule__ctor_m9FB482ADCAA6B390BC1D6A145D51594A8E13FE4C,
	TrailModule_get_enabled_m78262C91CD53A91AFC1472EA65B64EA0D064EE0C,
	TrailModule_set_enabled_m905BF8B82FF8F6FCCC430A05BD7DF0AE66A86C36,
	TrailModule_get_enabled_Injected_mEBBB04187226C17D9FBFF6DAE30244B08A65BC0C,
	TrailModule_set_enabled_Injected_mAE458C40CB038CE8F438EBABFA456EF7A0CC7FA8,
	ParticleSystemRenderer_GetMeshes_m3CA9AA8947C7F0468F6C0B7F1344D747EA43D440,
};
extern void MainModule__ctor_m5F7D2DD815C741DE3FA18A0C1BB2F2776612EF7A_AdjustorThunk (void);
extern void MainModule_get_duration_mDCB80E6BD3B6B2DFB92E41FBCCA154D7E7150EB1_AdjustorThunk (void);
extern void MainModule_set_duration_m3B328F1E542EEE7BAE101B309512E0E3B4638B77_AdjustorThunk (void);
extern void MainModule_get_loop_m4F75CD377C0555635995F7C1F8D580517473EDF8_AdjustorThunk (void);
extern void MainModule_set_loop_mB3FC83A6DA8D248D15C53388D6B9B7711ADD89BE_AdjustorThunk (void);
extern void MainModule_get_prewarm_m2B4B37F0B20CA2FDAB53315FC47E8FDCF3BBCD72_AdjustorThunk (void);
extern void MainModule_set_prewarm_m99964C46DE65B6797E9FD6A5CAF19E0B150D99F6_AdjustorThunk (void);
extern void MainModule_get_startDelay_m548FABFC02F1542B2B84EB3EBD9148C7CEB5DB6D_AdjustorThunk (void);
extern void MainModule_set_startDelay_mFE9F9E7F79978F670904789F7F55B96F42434F69_AdjustorThunk (void);
extern void MainModule_get_startLifetime_m09FB4B0E4B44A5B76E2DB11F177F770682BEA654_AdjustorThunk (void);
extern void MainModule_set_startLifetime_mDFC2433853CB12C0C93C8BF16FEDB4EA2E0B3514_AdjustorThunk (void);
extern void MainModule_get_startLifetimeMultiplier_m635DF5E608211FCF706FBAD039AE0C140BCEDB7C_AdjustorThunk (void);
extern void MainModule_get_startSpeed_m2F9A0CF5D05AA3522424EAD30CF226F0CDF8382B_AdjustorThunk (void);
extern void MainModule_set_startSpeed_mE6041E4BA54F01117AF6B81B1E53C5C9C669AC75_AdjustorThunk (void);
extern void MainModule_get_startSize3D_mA7F91E70AE687D0561D563C38C582DE9299F3FE9_AdjustorThunk (void);
extern void MainModule_get_startSize_m7D92E17A7D36FB18A9D3ADA54D2D1DEDE89601FC_AdjustorThunk (void);
extern void MainModule_set_startSize_m44C3A39EAF1AE9A526A126D93160128223BE827B_AdjustorThunk (void);
extern void MainModule_get_startSizeX_mBDB87DD40CDA54AD0045EF86FB43763D192E6B6A_AdjustorThunk (void);
extern void MainModule_set_startSizeX_m5F3F00611409B616D7B5B05EF3A6D7C35448C445_AdjustorThunk (void);
extern void MainModule_get_startSizeY_m9344B56673317BFEAF3A15C024BA57F034546199_AdjustorThunk (void);
extern void MainModule_set_startSizeY_mAA04D165042FFAEC3F17672ED84F066A8295C0FD_AdjustorThunk (void);
extern void MainModule_get_startSizeZ_m16145FFC5083D901D99270682AA250CB429076A6_AdjustorThunk (void);
extern void MainModule_set_startSizeZ_mA1D0B1E2E9505DFDB42622E575FD91134ABE9A50_AdjustorThunk (void);
extern void MainModule_get_startColor_m24E66E583EB51341A885ABAE84114CBB37018781_AdjustorThunk (void);
extern void MainModule_set_startColor_m504911284CAC53A7C1B97B5D01BBDD2FA67D0E7A_AdjustorThunk (void);
extern void MainModule_set_simulationSpeed_mFB44E06BF3F0D423636A6F37642CCC5722EBE0C6_AdjustorThunk (void);
extern void ShapeModule__ctor_m951B06AF6BDA194E8111B2B72C104562F41CB0AD_AdjustorThunk (void);
extern void ShapeModule_get_enabled_m95D1E7C55963FC8A6BFEC95B7B22C2E1218DCC4F_AdjustorThunk (void);
extern void ShapeModule_get_radius_m41AE7721BB6CDD1E9E91509DD2D7B60F0D302E90_AdjustorThunk (void);
extern void ShapeModule_set_radius_m37F79E13EB60FA39EAE36B49DAC4AC880416E89C_AdjustorThunk (void);
extern void Particle_set_lifetime_m29360AF093721364BF46996EE1D400256DB95911_AdjustorThunk (void);
extern void Particle_set_position_mE9103000DB4EA6CE09D25650F5A2915731F7A63E_AdjustorThunk (void);
extern void Particle_set_velocity_mF4C1DE326CCABE480F44D3DF3873241E85A6303B_AdjustorThunk (void);
extern void Particle_set_remainingLifetime_m3E58D8B3599B0BA6790D43022C3DF16E6896D018_AdjustorThunk (void);
extern void Particle_set_startLifetime_mCD0B16F2B1F2E2AEED84C4FCD85D5AD96F853A77_AdjustorThunk (void);
extern void Particle_set_startColor_mC3031F4238B0C003DFA5BF9AB4B3141B7CF71538_AdjustorThunk (void);
extern void Particle_set_randomSeed_mCC3C02CCBF9C0EA80E2CE01EC47AD30F31D5F6C2_AdjustorThunk (void);
extern void Particle_set_startSize_mBBEBF7365A4E68FF2044E2ECEACC562376EA4A1F_AdjustorThunk (void);
extern void Particle_set_rotation3D_mC0B19BFEBA780F95C763DE14C80B29764E519D62_AdjustorThunk (void);
extern void Particle_set_angularVelocity3D_m56AE22FE7AFB178DD206EA2A7E0DA64B360D7EA8_AdjustorThunk (void);
extern void MinMaxCurve_get_mode_m3501926642278B695E0CDFE2E24B641A560B9526_AdjustorThunk (void);
extern void MinMaxCurve_set_mode_m6870C4CA0FE7AF1029F45040B827C9F0A6A80A86_AdjustorThunk (void);
extern void MinMaxCurve_get_curveMultiplier_m3C3EF4A373A3B2E51A4D27B88F7D1ADEA5B35936_AdjustorThunk (void);
extern void MinMaxCurve_set_curveMultiplier_m9FCF7F21AECE399ACD9FEB9ADEEBD93FD12DDABC_AdjustorThunk (void);
extern void MinMaxCurve_get_curveMax_m455705B0A048A51461E94DACD1503EA1DEAB68B8_AdjustorThunk (void);
extern void MinMaxCurve_set_curveMax_m39B9E8AE4E62E874DA99AA7108C2C47C6CA333DF_AdjustorThunk (void);
extern void MinMaxCurve_get_curveMin_m2F6041CAA8760D21D39A6204973B411D7109CF00_AdjustorThunk (void);
extern void MinMaxCurve_set_curveMin_m17ADE22B882B75CBE87836C85C477A05122D9E75_AdjustorThunk (void);
extern void MinMaxCurve_get_constantMax_mBC9884017B40BDF911C768F521950370A6B41463_AdjustorThunk (void);
extern void MinMaxCurve_set_constantMax_mDB7B011E5B1EA76429B2A1FFDE6C06E82D290DC1_AdjustorThunk (void);
extern void MinMaxCurve_get_constantMin_mB39AF05209B993DC31AB4247D9BE8D4F80E5D710_AdjustorThunk (void);
extern void MinMaxCurve_set_constantMin_mBF7CF2A9F167AD06C327EE6EA37BA427E1CE548C_AdjustorThunk (void);
extern void MinMaxCurve_get_constant_m4F2B7693C00CC9FAEDE1DAD32FEEE893414FBE91_AdjustorThunk (void);
extern void MinMaxCurve_set_constant_mB6E4F22F67D3ED641FEBB387351F17451BB6A897_AdjustorThunk (void);
extern void MinMaxCurve_get_curve_mE735424B0F4A9F55699BD82254F159D4226F9661_AdjustorThunk (void);
extern void MinMaxCurve_set_curve_mC745A6526FBF122F96DA597997DFAB9D7D2529BF_AdjustorThunk (void);
extern void MinMaxGradient__ctor_m1C456463FD9B1BD7A72A31C95A851A0ECF249858_AdjustorThunk (void);
extern void MinMaxGradient_get_mode_mC4B1A93FD93B41544AED855AB1FC520EC642AA98_AdjustorThunk (void);
extern void MinMaxGradient_set_mode_mCD15C79CD57AD7D400D7B171E10BC5E24F64295E_AdjustorThunk (void);
extern void MinMaxGradient_get_gradientMax_m94B2E99380FFCC82F0387C4B1E685DDD5B9C7947_AdjustorThunk (void);
extern void MinMaxGradient_set_gradientMax_mC432C0AEA15FE74D6E6E6E2058BE039361D57ABB_AdjustorThunk (void);
extern void MinMaxGradient_get_gradientMin_m906D46F5A5F72C83AF7651A910A97B616342DE4E_AdjustorThunk (void);
extern void MinMaxGradient_set_gradientMin_m4152642243D268E1A95A7FC0B58E8F179A933609_AdjustorThunk (void);
extern void MinMaxGradient_get_colorMax_mB9E445D4D3E2788B10EA785B0BA8A76015FEDD1C_AdjustorThunk (void);
extern void MinMaxGradient_set_colorMax_m28A85822E89FC80D1F5B49225DEC4ABCF86CAE33_AdjustorThunk (void);
extern void MinMaxGradient_get_colorMin_mD8698D56F1E88B7EE97638A3DC48AFC3FD2A6664_AdjustorThunk (void);
extern void MinMaxGradient_set_colorMin_m7D3D987AB86FE01E7BC4AD856AAEE703F18EF1EF_AdjustorThunk (void);
extern void MinMaxGradient_get_color_m534E35D538D549F006E9F90E453D41B92FBAC3BF_AdjustorThunk (void);
extern void MinMaxGradient_set_color_m0890DD6E249DE8552C04AFC4F959DD37EA05033B_AdjustorThunk (void);
extern void MinMaxGradient_get_gradient_m012030F7AEAE915DCC71C80674564E3FFD02FF9D_AdjustorThunk (void);
extern void MinMaxGradient_set_gradient_m6AAD335DD1A2BD5C091B608C604825C32CFB825D_AdjustorThunk (void);
extern void VelocityOverLifetimeModule__ctor_m64A15E09916657384A99217570EB78F31A09E10A_AdjustorThunk (void);
extern void VelocityOverLifetimeModule_get_enabled_m60395DDF6F516804EBFCD7AAE5AD0D741B279E4E_AdjustorThunk (void);
extern void VelocityOverLifetimeModule_get_x_mB9014044B32BB551EBB06A9DF167DCA4036AA002_AdjustorThunk (void);
extern void VelocityOverLifetimeModule_set_x_m667368604CF8D09F133E552E21CBD3DC344281A0_AdjustorThunk (void);
extern void VelocityOverLifetimeModule_get_y_m2B00A63C14F173791F1CDD2ACB527F64A72D16BF_AdjustorThunk (void);
extern void VelocityOverLifetimeModule_set_y_m4B08D16D7F274567CC18790702C8F3C53A443089_AdjustorThunk (void);
extern void VelocityOverLifetimeModule_get_z_mBEF172621B262D8E14742E3940DEC14757870B0C_AdjustorThunk (void);
extern void VelocityOverLifetimeModule_set_z_m8B5A03C2D6E06CD7C6177BD321E9121E96D1D889_AdjustorThunk (void);
extern void ColorOverLifetimeModule__ctor_m3870A84520D71E559FF26F8592B98865763E42F3_AdjustorThunk (void);
extern void ColorOverLifetimeModule_get_enabled_mAB86D22805596728C643ABDF4887A246F1652B5B_AdjustorThunk (void);
extern void ColorOverLifetimeModule_get_color_m320131C4BBF3B84729CE1D2F7DD7FB986746BDB1_AdjustorThunk (void);
extern void ColorOverLifetimeModule_set_color_m054950B589DA58FFD5BDBC10A70304BF89E3C86B_AdjustorThunk (void);
extern void LightsModule__ctor_mB04B2490B71B56B4CCD7F5DFD983DAB98696FB4E_AdjustorThunk (void);
extern void LightsModule_get_enabled_m944379FB6DC57FE6F4E11714BBCE194C51EA7DA9_AdjustorThunk (void);
extern void LightsModule_set_enabled_mCA61408EE4B83423BB92B032567BA63005379DCE_AdjustorThunk (void);
extern void TrailModule__ctor_m9FB482ADCAA6B390BC1D6A145D51594A8E13FE4C_AdjustorThunk (void);
extern void TrailModule_get_enabled_m78262C91CD53A91AFC1472EA65B64EA0D064EE0C_AdjustorThunk (void);
extern void TrailModule_set_enabled_m905BF8B82FF8F6FCCC430A05BD7DF0AE66A86C36_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[89] = 
{
	{ 0x06000019, MainModule__ctor_m5F7D2DD815C741DE3FA18A0C1BB2F2776612EF7A_AdjustorThunk },
	{ 0x0600001A, MainModule_get_duration_mDCB80E6BD3B6B2DFB92E41FBCCA154D7E7150EB1_AdjustorThunk },
	{ 0x0600001B, MainModule_set_duration_m3B328F1E542EEE7BAE101B309512E0E3B4638B77_AdjustorThunk },
	{ 0x0600001C, MainModule_get_loop_m4F75CD377C0555635995F7C1F8D580517473EDF8_AdjustorThunk },
	{ 0x0600001D, MainModule_set_loop_mB3FC83A6DA8D248D15C53388D6B9B7711ADD89BE_AdjustorThunk },
	{ 0x0600001E, MainModule_get_prewarm_m2B4B37F0B20CA2FDAB53315FC47E8FDCF3BBCD72_AdjustorThunk },
	{ 0x0600001F, MainModule_set_prewarm_m99964C46DE65B6797E9FD6A5CAF19E0B150D99F6_AdjustorThunk },
	{ 0x06000020, MainModule_get_startDelay_m548FABFC02F1542B2B84EB3EBD9148C7CEB5DB6D_AdjustorThunk },
	{ 0x06000021, MainModule_set_startDelay_mFE9F9E7F79978F670904789F7F55B96F42434F69_AdjustorThunk },
	{ 0x06000022, MainModule_get_startLifetime_m09FB4B0E4B44A5B76E2DB11F177F770682BEA654_AdjustorThunk },
	{ 0x06000023, MainModule_set_startLifetime_mDFC2433853CB12C0C93C8BF16FEDB4EA2E0B3514_AdjustorThunk },
	{ 0x06000024, MainModule_get_startLifetimeMultiplier_m635DF5E608211FCF706FBAD039AE0C140BCEDB7C_AdjustorThunk },
	{ 0x06000025, MainModule_get_startSpeed_m2F9A0CF5D05AA3522424EAD30CF226F0CDF8382B_AdjustorThunk },
	{ 0x06000026, MainModule_set_startSpeed_mE6041E4BA54F01117AF6B81B1E53C5C9C669AC75_AdjustorThunk },
	{ 0x06000027, MainModule_get_startSize3D_mA7F91E70AE687D0561D563C38C582DE9299F3FE9_AdjustorThunk },
	{ 0x06000028, MainModule_get_startSize_m7D92E17A7D36FB18A9D3ADA54D2D1DEDE89601FC_AdjustorThunk },
	{ 0x06000029, MainModule_set_startSize_m44C3A39EAF1AE9A526A126D93160128223BE827B_AdjustorThunk },
	{ 0x0600002A, MainModule_get_startSizeX_mBDB87DD40CDA54AD0045EF86FB43763D192E6B6A_AdjustorThunk },
	{ 0x0600002B, MainModule_set_startSizeX_m5F3F00611409B616D7B5B05EF3A6D7C35448C445_AdjustorThunk },
	{ 0x0600002C, MainModule_get_startSizeY_m9344B56673317BFEAF3A15C024BA57F034546199_AdjustorThunk },
	{ 0x0600002D, MainModule_set_startSizeY_mAA04D165042FFAEC3F17672ED84F066A8295C0FD_AdjustorThunk },
	{ 0x0600002E, MainModule_get_startSizeZ_m16145FFC5083D901D99270682AA250CB429076A6_AdjustorThunk },
	{ 0x0600002F, MainModule_set_startSizeZ_mA1D0B1E2E9505DFDB42622E575FD91134ABE9A50_AdjustorThunk },
	{ 0x06000030, MainModule_get_startColor_m24E66E583EB51341A885ABAE84114CBB37018781_AdjustorThunk },
	{ 0x06000031, MainModule_set_startColor_m504911284CAC53A7C1B97B5D01BBDD2FA67D0E7A_AdjustorThunk },
	{ 0x06000032, MainModule_set_simulationSpeed_mFB44E06BF3F0D423636A6F37642CCC5722EBE0C6_AdjustorThunk },
	{ 0x0600004C, ShapeModule__ctor_m951B06AF6BDA194E8111B2B72C104562F41CB0AD_AdjustorThunk },
	{ 0x0600004D, ShapeModule_get_enabled_m95D1E7C55963FC8A6BFEC95B7B22C2E1218DCC4F_AdjustorThunk },
	{ 0x0600004E, ShapeModule_get_radius_m41AE7721BB6CDD1E9E91509DD2D7B60F0D302E90_AdjustorThunk },
	{ 0x0600004F, ShapeModule_set_radius_m37F79E13EB60FA39EAE36B49DAC4AC880416E89C_AdjustorThunk },
	{ 0x06000053, Particle_set_lifetime_m29360AF093721364BF46996EE1D400256DB95911_AdjustorThunk },
	{ 0x06000054, Particle_set_position_mE9103000DB4EA6CE09D25650F5A2915731F7A63E_AdjustorThunk },
	{ 0x06000055, Particle_set_velocity_mF4C1DE326CCABE480F44D3DF3873241E85A6303B_AdjustorThunk },
	{ 0x06000056, Particle_set_remainingLifetime_m3E58D8B3599B0BA6790D43022C3DF16E6896D018_AdjustorThunk },
	{ 0x06000057, Particle_set_startLifetime_mCD0B16F2B1F2E2AEED84C4FCD85D5AD96F853A77_AdjustorThunk },
	{ 0x06000058, Particle_set_startColor_mC3031F4238B0C003DFA5BF9AB4B3141B7CF71538_AdjustorThunk },
	{ 0x06000059, Particle_set_randomSeed_mCC3C02CCBF9C0EA80E2CE01EC47AD30F31D5F6C2_AdjustorThunk },
	{ 0x0600005A, Particle_set_startSize_mBBEBF7365A4E68FF2044E2ECEACC562376EA4A1F_AdjustorThunk },
	{ 0x0600005B, Particle_set_rotation3D_mC0B19BFEBA780F95C763DE14C80B29764E519D62_AdjustorThunk },
	{ 0x0600005C, Particle_set_angularVelocity3D_m56AE22FE7AFB178DD206EA2A7E0DA64B360D7EA8_AdjustorThunk },
	{ 0x0600005D, MinMaxCurve_get_mode_m3501926642278B695E0CDFE2E24B641A560B9526_AdjustorThunk },
	{ 0x0600005E, MinMaxCurve_set_mode_m6870C4CA0FE7AF1029F45040B827C9F0A6A80A86_AdjustorThunk },
	{ 0x0600005F, MinMaxCurve_get_curveMultiplier_m3C3EF4A373A3B2E51A4D27B88F7D1ADEA5B35936_AdjustorThunk },
	{ 0x06000060, MinMaxCurve_set_curveMultiplier_m9FCF7F21AECE399ACD9FEB9ADEEBD93FD12DDABC_AdjustorThunk },
	{ 0x06000061, MinMaxCurve_get_curveMax_m455705B0A048A51461E94DACD1503EA1DEAB68B8_AdjustorThunk },
	{ 0x06000062, MinMaxCurve_set_curveMax_m39B9E8AE4E62E874DA99AA7108C2C47C6CA333DF_AdjustorThunk },
	{ 0x06000063, MinMaxCurve_get_curveMin_m2F6041CAA8760D21D39A6204973B411D7109CF00_AdjustorThunk },
	{ 0x06000064, MinMaxCurve_set_curveMin_m17ADE22B882B75CBE87836C85C477A05122D9E75_AdjustorThunk },
	{ 0x06000065, MinMaxCurve_get_constantMax_mBC9884017B40BDF911C768F521950370A6B41463_AdjustorThunk },
	{ 0x06000066, MinMaxCurve_set_constantMax_mDB7B011E5B1EA76429B2A1FFDE6C06E82D290DC1_AdjustorThunk },
	{ 0x06000067, MinMaxCurve_get_constantMin_mB39AF05209B993DC31AB4247D9BE8D4F80E5D710_AdjustorThunk },
	{ 0x06000068, MinMaxCurve_set_constantMin_mBF7CF2A9F167AD06C327EE6EA37BA427E1CE548C_AdjustorThunk },
	{ 0x06000069, MinMaxCurve_get_constant_m4F2B7693C00CC9FAEDE1DAD32FEEE893414FBE91_AdjustorThunk },
	{ 0x0600006A, MinMaxCurve_set_constant_mB6E4F22F67D3ED641FEBB387351F17451BB6A897_AdjustorThunk },
	{ 0x0600006B, MinMaxCurve_get_curve_mE735424B0F4A9F55699BD82254F159D4226F9661_AdjustorThunk },
	{ 0x0600006C, MinMaxCurve_set_curve_mC745A6526FBF122F96DA597997DFAB9D7D2529BF_AdjustorThunk },
	{ 0x0600006D, MinMaxGradient__ctor_m1C456463FD9B1BD7A72A31C95A851A0ECF249858_AdjustorThunk },
	{ 0x0600006E, MinMaxGradient_get_mode_mC4B1A93FD93B41544AED855AB1FC520EC642AA98_AdjustorThunk },
	{ 0x0600006F, MinMaxGradient_set_mode_mCD15C79CD57AD7D400D7B171E10BC5E24F64295E_AdjustorThunk },
	{ 0x06000070, MinMaxGradient_get_gradientMax_m94B2E99380FFCC82F0387C4B1E685DDD5B9C7947_AdjustorThunk },
	{ 0x06000071, MinMaxGradient_set_gradientMax_mC432C0AEA15FE74D6E6E6E2058BE039361D57ABB_AdjustorThunk },
	{ 0x06000072, MinMaxGradient_get_gradientMin_m906D46F5A5F72C83AF7651A910A97B616342DE4E_AdjustorThunk },
	{ 0x06000073, MinMaxGradient_set_gradientMin_m4152642243D268E1A95A7FC0B58E8F179A933609_AdjustorThunk },
	{ 0x06000074, MinMaxGradient_get_colorMax_mB9E445D4D3E2788B10EA785B0BA8A76015FEDD1C_AdjustorThunk },
	{ 0x06000075, MinMaxGradient_set_colorMax_m28A85822E89FC80D1F5B49225DEC4ABCF86CAE33_AdjustorThunk },
	{ 0x06000076, MinMaxGradient_get_colorMin_mD8698D56F1E88B7EE97638A3DC48AFC3FD2A6664_AdjustorThunk },
	{ 0x06000077, MinMaxGradient_set_colorMin_m7D3D987AB86FE01E7BC4AD856AAEE703F18EF1EF_AdjustorThunk },
	{ 0x06000078, MinMaxGradient_get_color_m534E35D538D549F006E9F90E453D41B92FBAC3BF_AdjustorThunk },
	{ 0x06000079, MinMaxGradient_set_color_m0890DD6E249DE8552C04AFC4F959DD37EA05033B_AdjustorThunk },
	{ 0x0600007A, MinMaxGradient_get_gradient_m012030F7AEAE915DCC71C80674564E3FFD02FF9D_AdjustorThunk },
	{ 0x0600007B, MinMaxGradient_set_gradient_m6AAD335DD1A2BD5C091B608C604825C32CFB825D_AdjustorThunk },
	{ 0x0600007D, VelocityOverLifetimeModule__ctor_m64A15E09916657384A99217570EB78F31A09E10A_AdjustorThunk },
	{ 0x0600007E, VelocityOverLifetimeModule_get_enabled_m60395DDF6F516804EBFCD7AAE5AD0D741B279E4E_AdjustorThunk },
	{ 0x0600007F, VelocityOverLifetimeModule_get_x_mB9014044B32BB551EBB06A9DF167DCA4036AA002_AdjustorThunk },
	{ 0x06000080, VelocityOverLifetimeModule_set_x_m667368604CF8D09F133E552E21CBD3DC344281A0_AdjustorThunk },
	{ 0x06000081, VelocityOverLifetimeModule_get_y_m2B00A63C14F173791F1CDD2ACB527F64A72D16BF_AdjustorThunk },
	{ 0x06000082, VelocityOverLifetimeModule_set_y_m4B08D16D7F274567CC18790702C8F3C53A443089_AdjustorThunk },
	{ 0x06000083, VelocityOverLifetimeModule_get_z_mBEF172621B262D8E14742E3940DEC14757870B0C_AdjustorThunk },
	{ 0x06000084, VelocityOverLifetimeModule_set_z_m8B5A03C2D6E06CD7C6177BD321E9121E96D1D889_AdjustorThunk },
	{ 0x0600008C, ColorOverLifetimeModule__ctor_m3870A84520D71E559FF26F8592B98865763E42F3_AdjustorThunk },
	{ 0x0600008D, ColorOverLifetimeModule_get_enabled_mAB86D22805596728C643ABDF4887A246F1652B5B_AdjustorThunk },
	{ 0x0600008E, ColorOverLifetimeModule_get_color_m320131C4BBF3B84729CE1D2F7DD7FB986746BDB1_AdjustorThunk },
	{ 0x0600008F, ColorOverLifetimeModule_set_color_m054950B589DA58FFD5BDBC10A70304BF89E3C86B_AdjustorThunk },
	{ 0x06000093, LightsModule__ctor_mB04B2490B71B56B4CCD7F5DFD983DAB98696FB4E_AdjustorThunk },
	{ 0x06000094, LightsModule_get_enabled_m944379FB6DC57FE6F4E11714BBCE194C51EA7DA9_AdjustorThunk },
	{ 0x06000095, LightsModule_set_enabled_mCA61408EE4B83423BB92B032567BA63005379DCE_AdjustorThunk },
	{ 0x06000098, TrailModule__ctor_m9FB482ADCAA6B390BC1D6A145D51594A8E13FE4C_AdjustorThunk },
	{ 0x06000099, TrailModule_get_enabled_m78262C91CD53A91AFC1472EA65B64EA0D064EE0C_AdjustorThunk },
	{ 0x0600009A, TrailModule_set_enabled_m905BF8B82FF8F6FCCC430A05BD7DF0AE66A86C36_AdjustorThunk },
};
static const int32_t s_InvokerIndices[157] = 
{
	945,
	11018,
	10823,
	1976,
	2856,
	10442,
	13298,
	4724,
	10442,
	13298,
	10442,
	13298,
	10629,
	10629,
	5877,
	10415,
	13399,
	13402,
	13404,
	13397,
	13398,
	13403,
	13298,
	4705,
	10682,
	13195,
	10823,
	12815,
	10442,
	12815,
	10442,
	13400,
	11016,
	13400,
	11016,
	13195,
	13400,
	11016,
	12815,
	13400,
	11016,
	13400,
	11016,
	13400,
	11016,
	13400,
	11016,
	13401,
	11017,
	10823,
	20653,
	18511,
	19877,
	18503,
	19877,
	18503,
	18502,
	18502,
	18502,
	18502,
	20653,
	18502,
	18502,
	19877,
	18502,
	18502,
	18502,
	18502,
	18502,
	18502,
	18502,
	18502,
	18502,
	18502,
	18511,
	10682,
	12815,
	13195,
	10823,
	19877,
	20653,
	18511,
	10823,
	10912,
	10912,
	10823,
	10823,
	10458,
	10891,
	10823,
	10912,
	10912,
	12996,
	10629,
	13195,
	10823,
	13052,
	10682,
	13052,
	10682,
	13195,
	10823,
	13195,
	10823,
	13195,
	10823,
	13052,
	10682,
	10682,
	12996,
	10629,
	13052,
	10682,
	13052,
	10682,
	12829,
	10457,
	12829,
	10457,
	12829,
	10457,
	13052,
	10682,
	20926,
	10682,
	12815,
	13400,
	11016,
	13400,
	11016,
	13400,
	11016,
	19877,
	18502,
	18502,
	18502,
	18502,
	18502,
	18502,
	10682,
	12815,
	13401,
	11017,
	19877,
	18502,
	18502,
	10682,
	12815,
	10442,
	19877,
	18503,
	10682,
	12815,
	10442,
	19877,
	18503,
	8845,
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_ParticleSystemModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_ParticleSystemModule_CodeGenModule = 
{
	"UnityEngine.ParticleSystemModule.dll",
	157,
	s_methodPointers,
	89,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};

using UnityEngine;

public enum BitmapTextAlignment
{
    Left,
    Center,
    Right
}

public enum BitmapTextVerticalAlignment
{
    Top,
    Middle,
    Bottom
}

public enum BitmapTextOverflow
{
    Wrap,
    Truncate,
    Overflow
}

[System.Serializable]
public class BitmapTextSettings
{
    [Header("Layout")]
    [SerializeField] private BitmapTextAlignment m_Alignment = BitmapTextAlignment.Left;
    [SerializeField] private BitmapTextVerticalAlignment m_VerticalAlignment = BitmapTextVerticalAlignment.Top;
    [SerializeField] private BitmapTextOverflow m_Overflow = BitmapTextOverflow.Wrap;
    
    [Header("Spacing")]
    [SerializeField] private float m_LineSpacing = 1.0f;
    [SerializeField] private float m_CharacterSpacing = 0f;
    [SerializeField] private float m_WordSpacing = 0f;
    
    [Header("Size")]
    [SerializeField] private float m_FontSize = 32f;
    [SerializeField] private Vector2 m_MaxSize = new Vector2(200f, 100f);
    [SerializeField] private bool m_AutoSize = false;
    [SerializeField] private float m_MinFontSize = 8f;
    [SerializeField] private float m_MaxFontSize = 72f;
    
    [Header("Rich Text")]
    [SerializeField] private bool m_RichText = true;
    [SerializeField] private bool m_ParseEscapeCharacters = true;
    
    public BitmapTextAlignment alignment 
    { 
        get => m_Alignment; 
        set => m_Alignment = value; 
    }
    
    public BitmapTextVerticalAlignment verticalAlignment 
    { 
        get => m_VerticalAlignment; 
        set => m_VerticalAlignment = value; 
    }
    
    public BitmapTextOverflow overflow 
    { 
        get => m_Overflow; 
        set => m_Overflow = value; 
    }
    
    public float lineSpacing 
    { 
        get => m_LineSpacing; 
        set => m_LineSpacing = Mathf.Max(0f, value); 
    }
    
    public float characterSpacing 
    { 
        get => m_CharacterSpacing; 
        set => m_CharacterSpacing = value; 
    }
    
    public float wordSpacing 
    { 
        get => m_WordSpacing; 
        set => m_WordSpacing = value; 
    }
    
    public float fontSize 
    { 
        get => m_FontSize; 
        set => m_FontSize = Mathf.Max(0.1f, value); 
    }
    
    public Vector2 maxSize 
    { 
        get => m_MaxSize; 
        set => m_MaxSize = new Vector2(Mathf.Max(0f, value.x), Mathf.Max(0f, value.y)); 
    }
    
    public bool autoSize 
    { 
        get => m_AutoSize; 
        set => m_AutoSize = value; 
    }
    
    public float minFontSize 
    { 
        get => m_MinFontSize; 
        set => m_MinFontSize = Mathf.Max(0.1f, value); 
    }
    
    public float maxFontSize 
    { 
        get => m_MaxFontSize; 
        set => m_MaxFontSize = Mathf.Max(m_MinFontSize, value); 
    }
    
    public bool richText 
    { 
        get => m_RichText; 
        set => m_RichText = value; 
    }
    
    public bool parseEscapeCharacters 
    { 
        get => m_ParseEscapeCharacters; 
        set => m_ParseEscapeCharacters = value; 
    }
    
    /// <summary>
    /// Default constructor with sensible defaults
    /// </summary>
    public BitmapTextSettings()
    {
        m_Alignment = BitmapTextAlignment.Left;
        m_VerticalAlignment = BitmapTextVerticalAlignment.Top;
        m_Overflow = BitmapTextOverflow.Wrap;
        m_LineSpacing = 1.0f;
        m_CharacterSpacing = 0f;
        m_WordSpacing = 0f;
        m_FontSize = 32f;
        m_MaxSize = new Vector2(200f, 100f);
        m_AutoSize = false;
        m_MinFontSize = 8f;
        m_MaxFontSize = 72f;
        m_RichText = true;
        m_ParseEscapeCharacters = true;
    }
    
    /// <summary>
    /// Copy constructor
    /// </summary>
    public BitmapTextSettings(BitmapTextSettings other)
    {
        if (other != null)
        {
            m_Alignment = other.m_Alignment;
            m_VerticalAlignment = other.m_VerticalAlignment;
            m_Overflow = other.m_Overflow;
            m_LineSpacing = other.m_LineSpacing;
            m_CharacterSpacing = other.m_CharacterSpacing;
            m_WordSpacing = other.m_WordSpacing;
            m_FontSize = other.m_FontSize;
            m_MaxSize = other.m_MaxSize;
            m_AutoSize = other.m_AutoSize;
            m_MinFontSize = other.m_MinFontSize;
            m_MaxFontSize = other.m_MaxFontSize;
            m_RichText = other.m_RichText;
            m_ParseEscapeCharacters = other.m_ParseEscapeCharacters;
        }
    }
    
    /// <summary>
    /// Create a copy of these settings
    /// </summary>
    public BitmapTextSettings Clone()
    {
        return new BitmapTextSettings(this);
    }
    
    /// <summary>
    /// Get the effective font scale based on font size and base font size
    /// </summary>
    public float GetFontScale(float baseFontSize)
    {
        if (baseFontSize <= 0) return 1f;
        return m_FontSize / baseFontSize;
    }
    
    /// <summary>
    /// Get the effective line height with spacing applied
    /// </summary>
    public float GetLineHeight(float baseLineHeight)
    {
        return baseLineHeight * m_LineSpacing;
    }
    
    /// <summary>
    /// Check if the settings are valid
    /// </summary>
    public bool IsValid()
    {
        return m_FontSize > 0 && m_LineSpacing > 0 && 
               m_MaxSize.x >= 0 && m_MaxSize.y >= 0 &&
               m_MinFontSize > 0 && m_MaxFontSize >= m_MinFontSize;
    }
}

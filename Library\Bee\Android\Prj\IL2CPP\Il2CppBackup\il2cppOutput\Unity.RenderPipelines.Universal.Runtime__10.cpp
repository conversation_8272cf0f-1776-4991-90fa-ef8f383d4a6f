﻿#include "pch-cpp.hpp"

#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include <limits>



struct CharU5BU5D_t799905CF001DD5F13F7DBB310181FC4D8B7D0AAB;
struct IntPtrU5BU5D_tFD177F8C806A6921AD7150264CCC62FA00CAD832;
struct StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF;
struct TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB;
struct Binder_t91BFCE95A7057FADF4D8A1A342AFE52872246235;
struct Exception_t;
struct IDictionary_t6D03155AF1FA9083817AA5B6AD7DEEACC26AB220;
struct MemberFilter_tF644F1AE82F611B677CE1964D5A3277DDA21D553;
struct SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6;
struct String_t;
struct Type_t;
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915;

IL2CPP_EXTERN_C RuntimeClass* Exception_t_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Math_tEB65DE7CA8B083C412C969C92981C030865486CE_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* ModuleHandle_tF45CBC10412074A0AAEF63CBDAA5F92A4F7A9978_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* PlanarGraph_t53936E128A9BC16439B53E0E845693E21A31B761_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Refinery_t7AB9DFA0E0468A03A75D525BE59E9B17FFC270F9_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Smoothen_t66451B46E8AA634F6F80536137F061EC45767822_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Type_t_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* float4_t89D9A294E7A79BD81BFBDD18654508532958555E_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C const RuntimeMethod* IJobExtensions_EarlyJobInit_TisDrawCallJob_t3EA2ABC822AD5DF50675A5B437DAB927DB95215D_m8175192716D38C9C006D2C98D5B36A7CC75E33AD_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* IJobForExtensions_EarlyJobInit_TisLightMinMaxZJob_tB4FE0854445DAADF46E5511EAAF54EA1E4B611C4_m6D407EF2FD72EE5C26BA1C843C9932ABCB9C16DA_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* IJobForExtensions_EarlyJobInit_TisReflectionProbeMinMaxZJob_tB55272F39D5B8B189F5DF7212CDA3FFF1EC0C71C_mE2595D2099394DF551AC793D9874D90167904DF2_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* IJobForExtensions_EarlyJobInit_TisTileRangeExpansionJob_t8342AD91DCB87CA5DBDB463981EE24D47408C876_m7F3455814736950B257AF91E4AE26136F7D2D588_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* IJobForExtensions_EarlyJobInit_TisTilingJob_t4506E6F62C95A90210A474DE43C83AF5EB8D3352_m4C05E9D5B6CBBFDCFF690B0C60C83CC47D299593_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* IJobForExtensions_EarlyJobInit_TisZBinningJob_t9BC217C31924E66E667568C1B51EA2F44FA0A08E_mA5B015BCBE9BD211FDEFDAE5347DCBE7E91D1970_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* IJobParallelForTransformExtensions_EarlyJobInit_TisUpdateTransformsJob_t7CF957169E8C6560084F48A51BC15A447F3002C7_mA2F79749E27788E6B6B0DA93086B08A0C553480E_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ModuleHandle_Copy_TisInt32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_m08EAF9EE4BD7B3611121EEBB1CA1AC40D9C29874_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ModuleHandle_Copy_Tisfloat2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA_m07543F135AF3627179553F55ED1804459CFCE11F_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* ModuleHandle_Copy_Tisint2_tF4AC25F87943DC0B2BB3456B0B919B3B42A9432A_mBFF6B1AB4AB8B44E22265CE0FC194BEAF466399C_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* NativeArray_1_Dispose_m05C674E687B921C37722A6A1FF938FD56574642E_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* NativeArray_1_Dispose_m3135DCFBA5DDC3D2CAA20FB2666F3A996856F2F2_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* NativeArray_1_Dispose_mAD3B69E4B23316C46AF8C35D7E1E81206323F16F_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* NativeArray_1__ctor_m3CB679B1B77F99FC5CF890F75C914E22555A1F13_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* NativeArray_1__ctor_mAF9D0A865FBFFE6364C3073A253711B4C109C67A_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* NativeArray_1__ctor_mB7BB23924A114599D399A5EC6C00B2B6407CF66D_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeType* DrawCallJob_t3EA2ABC822AD5DF50675A5B437DAB927DB95215D_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* LightMinMaxZJob_tB4FE0854445DAADF46E5511EAAF54EA1E4B611C4_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* ReflectionProbeMinMaxZJob_tB55272F39D5B8B189F5DF7212CDA3FFF1EC0C71C_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* TileRangeExpansionJob_t8342AD91DCB87CA5DBDB463981EE24D47408C876_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* TilingJob_t4506E6F62C95A90210A474DE43C83AF5EB8D3352_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* UpdateTransformsJob_t7CF957169E8C6560084F48A51BC15A447F3002C7_0_0_0_var;
IL2CPP_EXTERN_C const RuntimeType* ZBinningJob_t9BC217C31924E66E667568C1B51EA2F44FA0A08E_0_0_0_var;
struct Exception_t_marshaled_com;
struct Exception_t_marshaled_pinvoke;


IL2CPP_EXTERN_C_BEGIN
IL2CPP_EXTERN_C_END

#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
struct U3CPrivateImplementationDetailsU3E_t16CE31F4DEE6BA0AEFEB3FA0105D58630695B339  : public RuntimeObject
{
};
struct MemberInfo_t  : public RuntimeObject
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F  : public RuntimeObject
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_pinvoke
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_com
{
};
struct __JobReflectionRegistrationOutput__2787129498_t171E463AB60FA3A88A04886E577354241377B314  : public RuntimeObject
{
};
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22 
{
	bool ___m_value;
};
struct DelaEdgeCompare_tB840B82782097F179823018C8C3F86D79167714B 
{
	union
	{
		struct
		{
		};
		uint8_t DelaEdgeCompare_tB840B82782097F179823018C8C3F86D79167714B__padding[1];
	};
};
struct Double_tE150EF3D1D43DEE85D533810AB4C742307EEDE5F 
{
	double ___m_value;
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2  : public ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F
{
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2_marshaled_pinvoke
{
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2_marshaled_com
{
};
struct Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C 
{
	int32_t ___m_value;
};
struct Int64_t092CFB123BE63C28ACDAF65C68F21A526050DBA3 
{
	int64_t ___m_value;
};
struct IntPtr_t 
{
	void* ___m_value;
};
struct ModuleHandle_tF45CBC10412074A0AAEF63CBDAA5F92A4F7A9978 
{
	union
	{
		struct
		{
		};
		uint8_t ModuleHandle_tF45CBC10412074A0AAEF63CBDAA5F92A4F7A9978__padding[1];
	};
};
struct Single_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C 
{
	float ___m_value;
};
struct TessCellCompare_tC18A10A741079B0B69C186B257E59D25768908BE 
{
	union
	{
		struct
		{
		};
		uint8_t TessCellCompare_tC18A10A741079B0B69C186B257E59D25768908BE__padding[1];
	};
};
struct TessEdgeCompare_t41229EB788BF2503A67B087C78F734EADDE71434 
{
	union
	{
		struct
		{
		};
		uint8_t TessEdgeCompare_t41229EB788BF2503A67B087C78F734EADDE71434__padding[1];
	};
};
struct TessEventCompare_t286A68412E43ED2680CC0D5FF7189FADAB1FBC4F 
{
	union
	{
		struct
		{
		};
		uint8_t TessEventCompare_t286A68412E43ED2680CC0D5FF7189FADAB1FBC4F__padding[1];
	};
};
struct TessJunctionCompare_tBB128A7D2EB479A4CCFE1AB7ECD6A2F284762249 
{
	union
	{
		struct
		{
		};
		uint8_t TessJunctionCompare_tBB128A7D2EB479A4CCFE1AB7ECD6A2F284762249__padding[1];
	};
};
struct UInt32_t1833D51FFA667B18A5AA4B8D34DE284F8495D29B 
{
	uint32_t ___m_value;
};
struct UInt64_t8F12534CC8FC4B5860F2A2CD1EE79D322E7A41AF 
{
	uint64_t ___m_value;
};
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915 
{
	union
	{
		struct
		{
		};
		uint8_t Void_t4861ACF8F4594C3437BB48B6E56783494B843915__padding[1];
	};
};
struct double2_t0A9854C934D0BBE9DD41F2B318B64F830D7253FA 
{
	double ___x;
	double ___y;
};
struct float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA 
{
	float ___x;
	float ___y;
};
struct float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E 
{
	float ___x;
	float ___y;
	float ___z;
};
struct float4_t89D9A294E7A79BD81BFBDD18654508532958555E 
{
	float ___x;
	float ___y;
	float ___z;
	float ___w;
};
struct int2_tF4AC25F87943DC0B2BB3456B0B919B3B42A9432A 
{
	int32_t ___x;
	int32_t ___y;
};
struct int3_t1D01D28AA6D32890A228297EBADD9BB1A960E2BF 
{
	int32_t ___x;
	int32_t ___y;
	int32_t ___z;
};
struct int4_tBA77D4945786DE82C3A487B33955EA1004996052 
{
	int32_t ___x;
	int32_t ___y;
	int32_t ___z;
	int32_t ___w;
};
#pragma pack(push, tp, 1)
struct __StaticArrayInitTypeSizeU3D12_t5F40C9EEDE242DFE47A8DCE218ED3DF3E88B4EC0 
{
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D12_t5F40C9EEDE242DFE47A8DCE218ED3DF3E88B4EC0__padding[12];
	};
};
#pragma pack(pop, tp)
#pragma pack(push, tp, 1)
struct __StaticArrayInitTypeSizeU3D16_tB86B9BFC4ADBF4E2DF11F39AF43639693C65DF05 
{
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D16_tB86B9BFC4ADBF4E2DF11F39AF43639693C65DF05__padding[16];
	};
};
#pragma pack(pop, tp)
#pragma pack(push, tp, 1)
struct __StaticArrayInitTypeSizeU3D18462_t4544C81ECC68FF0593F377DFFDA8C3DDFF8B4E4B 
{
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D18462_t4544C81ECC68FF0593F377DFFDA8C3DDFF8B4E4B__padding[18462];
	};
};
#pragma pack(pop, tp)
#pragma pack(push, tp, 1)
struct __StaticArrayInitTypeSizeU3D23756_t1AC4FA2C9ED9812F724F4088DEDE4872BD0D5B98 
{
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D23756_t1AC4FA2C9ED9812F724F4088DEDE4872BD0D5B98__padding[23756];
	};
};
#pragma pack(pop, tp)
#pragma pack(push, tp, 1)
struct __StaticArrayInitTypeSizeU3D24_tB605E983EFADFA4C2759D8C48AB45B0B3A7BCC51 
{
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D24_tB605E983EFADFA4C2759D8C48AB45B0B3A7BCC51__padding[24];
	};
};
#pragma pack(pop, tp)
#pragma pack(push, tp, 1)
struct __StaticArrayInitTypeSizeU3D960_t86900CB1F8550ABFAD884FDD8E17F7B7AA90ED0D 
{
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D960_t86900CB1F8550ABFAD884FDD8E17F7B7AA90ED0D__padding[960];
	};
};
#pragma pack(pop, tp)
struct U3CxvasortU3Ee__FixedBuffer_t79DE72BBEB4757EE616BB011D62D7EAD6D3519E0 
{
	union
	{
		struct
		{
			double ___FixedElementField;
		};
		uint8_t U3CxvasortU3Ee__FixedBuffer_t79DE72BBEB4757EE616BB011D62D7EAD6D3519E0__padding[32];
	};
};
struct U3CxvbsortU3Ee__FixedBuffer_tB79D6FDE211F7AC2E9EADF73CAF439599E5FCC29 
{
	union
	{
		struct
		{
			double ___FixedElementField;
		};
		uint8_t U3CxvbsortU3Ee__FixedBuffer_tB79D6FDE211F7AC2E9EADF73CAF439599E5FCC29__padding[32];
	};
};
struct IntFloatUnion_t549256A9DD754252DD18383D9CE7EA55EBBD6D96 
{
	union
	{
		#pragma pack(push, tp, 1)
		struct
		{
			int32_t ___intValue;
		};
		#pragma pack(pop, tp)
		struct
		{
			int32_t ___intValue_forAlignmentOnly;
		};
		#pragma pack(push, tp, 1)
		struct
		{
			float ___floatValue;
		};
		#pragma pack(pop, tp)
		struct
		{
			float ___floatValue_forAlignmentOnly;
		};
	};
};
struct LongDoubleUnion_tD71C400B6C4CD1A7F13CE8125AC6BBC7A22791CA 
{
	union
	{
		#pragma pack(push, tp, 1)
		struct
		{
			int64_t ___longValue;
		};
		#pragma pack(pop, tp)
		struct
		{
			int64_t ___longValue_forAlignmentOnly;
		};
		#pragma pack(push, tp, 1)
		struct
		{
			double ___doubleValue;
		};
		#pragma pack(pop, tp)
		struct
		{
			double ___doubleValue_forAlignmentOnly;
		};
	};
};
struct Allocator_t996642592271AAD9EE688F142741D512C07B5824 
{
	int32_t ___value__;
};
struct Exception_t  : public RuntimeObject
{
	String_t* ____className;
	String_t* ____message;
	RuntimeObject* ____data;
	Exception_t* ____innerException;
	String_t* ____helpURL;
	RuntimeObject* ____stackTrace;
	String_t* ____stackTraceString;
	String_t* ____remoteStackTraceString;
	int32_t ____remoteStackIndex;
	RuntimeObject* ____dynamicMethods;
	int32_t ____HResult;
	String_t* ____source;
	SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6* ____safeSerializationManager;
	StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF* ___captured_traces;
	IntPtrU5BU5D_tFD177F8C806A6921AD7150264CCC62FA00CAD832* ___native_trace_ips;
	int32_t ___caught_in_unmanaged;
};
struct Exception_t_marshaled_pinvoke
{
	char* ____className;
	char* ____message;
	RuntimeObject* ____data;
	Exception_t_marshaled_pinvoke* ____innerException;
	char* ____helpURL;
	Il2CppIUnknown* ____stackTrace;
	char* ____stackTraceString;
	char* ____remoteStackTraceString;
	int32_t ____remoteStackIndex;
	Il2CppIUnknown* ____dynamicMethods;
	int32_t ____HResult;
	char* ____source;
	SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6* ____safeSerializationManager;
	StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF* ___captured_traces;
	Il2CppSafeArray* ___native_trace_ips;
	int32_t ___caught_in_unmanaged;
};
struct Exception_t_marshaled_com
{
	Il2CppChar* ____className;
	Il2CppChar* ____message;
	RuntimeObject* ____data;
	Exception_t_marshaled_com* ____innerException;
	Il2CppChar* ____helpURL;
	Il2CppIUnknown* ____stackTrace;
	Il2CppChar* ____stackTraceString;
	Il2CppChar* ____remoteStackTraceString;
	int32_t ____remoteStackIndex;
	Il2CppIUnknown* ____dynamicMethods;
	int32_t ____HResult;
	Il2CppChar* ____source;
	SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6* ____safeSerializationManager;
	StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF* ___captured_traces;
	Il2CppSafeArray* ___native_trace_ips;
	int32_t ___caught_in_unmanaged;
};
struct NativeArrayOptions_t3E979EEF4B4840228A7692A97DA07553C6465F1D 
{
	int32_t ___value__;
};
struct RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B 
{
	intptr_t ___value;
};
struct UCircle_t**************************************** 
{
	float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA ___center;
	float ___radius;
};
struct UEvent_t6B46070B7BC816FDF53E67775BD4EA5508B807C2 
{
	float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA ___a;
	float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA ___b;
	int32_t ___idx;
	int32_t ___type;
};
struct NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C 
{
	void* ___m_Buffer;
	int32_t ___m_Length;
	int32_t ___m_AllocatorLabel;
};
struct NativeArray_1_t949918E734BDA97DE9BC71119C34E3CC11B1F8D5 
{
	void* ___m_Buffer;
	int32_t ___m_Length;
	int32_t ___m_AllocatorLabel;
};
struct NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E 
{
	void* ___m_Buffer;
	int32_t ___m_Length;
	int32_t ___m_AllocatorLabel;
};
struct NativeArray_1_tE86585F07CF10FCD01AA2652A104B149336F7EC2 
{
	void* ___m_Buffer;
	int32_t ___m_Length;
	int32_t ___m_AllocatorLabel;
};
struct NativeArray_1_tBCDB44165F65D6BEE48CAD34C04286D158C1A200 
{
	void* ___m_Buffer;
	int32_t ___m_Length;
	int32_t ___m_AllocatorLabel;
};
struct Type_t  : public MemberInfo_t
{
	RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B ____impl;
};
struct UTriangle_tCD210F61D627BAB81A1CFFEC7076C3FBB9A6D7CD 
{
	float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA ___va;
	float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA ___vb;
	float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA ___vc;
	UCircle_t**************************************** ___c;
	float ___area;
	int3_t1D01D28AA6D32890A228297EBADD9BB1A960E2BF ___indices;
};
struct TessLink_tB36FA873ED8F645C11850647C5EAECC581B60D38 
{
	NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C ___roots;
	NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C ___ranks;
};
struct U3CPrivateImplementationDetailsU3E_t16CE31F4DEE6BA0AEFEB3FA0105D58630695B339_StaticFields
{
	__StaticArrayInitTypeSizeU3D16_tB86B9BFC4ADBF4E2DF11F39AF43639693C65DF05 ___08243D32F28C35701F6EA57F52AE707302C8528E8D358F13C6E6915543D265C6;
	__StaticArrayInitTypeSizeU3D24_tB605E983EFADFA4C2759D8C48AB45B0B3A7BCC51 ___18689A54C1FF754BE58500B2ED77A6C75B025BE96F6D01FEF89C42DA1C953F34;
	__StaticArrayInitTypeSizeU3D12_t5F40C9EEDE242DFE47A8DCE218ED3DF3E88B4EC0 ___4636993D3E1DA4E9D6B8F87B79E8F7C6D018580D52661950EABC3845C5897A4D;
	__StaticArrayInitTypeSizeU3D960_t86900CB1F8550ABFAD884FDD8E17F7B7AA90ED0D ___6322123493378558D4F9DD025993C168685B194246485704DD5B391FDCD77A64;
	__StaticArrayInitTypeSizeU3D23756_t1AC4FA2C9ED9812F724F4088DEDE4872BD0D5B98 ___6D38AC27B07D199F160756B50922F8CB747BCDA1786618BFE0589FD1A36D2064;
	__StaticArrayInitTypeSizeU3D16_tB86B9BFC4ADBF4E2DF11F39AF43639693C65DF05 ___888955380992D62883B27CC51FDC7E5C290C441026048F403C5618F305AD2BF1;
	__StaticArrayInitTypeSizeU3D12_t5F40C9EEDE242DFE47A8DCE218ED3DF3E88B4EC0 ___8E2129A5F232A49B45FCB149981C3507166B7EE6265A5B90A1C9B0B87B2C0A80;
	__StaticArrayInitTypeSizeU3D18462_t4544C81ECC68FF0593F377DFFDA8C3DDFF8B4E4B ___91489F78AD24ECD21CD375334DF4E55370243C8179AF2EC75F7277C1BBC6BAF5;
	__StaticArrayInitTypeSizeU3D12_t5F40C9EEDE242DFE47A8DCE218ED3DF3E88B4EC0 ___9D3A6E7E88415D8C1A0F3887B6384A9A8E4F44A036C5A24796C319751ACACCAD;
	__StaticArrayInitTypeSizeU3D12_t5F40C9EEDE242DFE47A8DCE218ED3DF3E88B4EC0 ___B6599D21CE74F24FA42D57991D6B0D0C5770322C90AF734EEB36A37F74090137;
	__StaticArrayInitTypeSizeU3D16_tB86B9BFC4ADBF4E2DF11F39AF43639693C65DF05 ___BAED642339816AFFB3FE8719792D0E4CE82F12DB72B7373D244EAA65445800FE;
	__StaticArrayInitTypeSizeU3D16_tB86B9BFC4ADBF4E2DF11F39AF43639693C65DF05 ___C94719FC63BFC7010A8361E8B4D4746BAB3C8AD593769F86532655EE58EBB101;
	__StaticArrayInitTypeSizeU3D960_t86900CB1F8550ABFAD884FDD8E17F7B7AA90ED0D ___E2EF5640DF412939A64301FFA3F66A62A34FA6E45A26E62F6994E5390B380D01;
};
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_StaticFields
{
	String_t* ___TrueString;
	String_t* ___FalseString;
};
struct ModuleHandle_tF45CBC10412074A0AAEF63CBDAA5F92A4F7A9978_StaticFields
{
	int32_t ___kMaxArea;
	int32_t ___kMaxEdgeCount;
	int32_t ___kMaxIndexCount;
	int32_t ___kMaxVertexCount;
	int32_t ___kMaxTriangleCount;
	int32_t ___kMaxRefineIterations;
	int32_t ___kMaxSmoothenIterations;
	float ___kIncrementAreaFactor;
};
struct double2_t0A9854C934D0BBE9DD41F2B318B64F830D7253FA_StaticFields
{
	double2_t0A9854C934D0BBE9DD41F2B318B64F830D7253FA ___zero;
};
struct float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA_StaticFields
{
	float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA ___zero;
};
struct float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E_StaticFields
{
	float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___zero;
};
struct float4_t89D9A294E7A79BD81BFBDD18654508532958555E_StaticFields
{
	float4_t89D9A294E7A79BD81BFBDD18654508532958555E ___zero;
};
struct int2_tF4AC25F87943DC0B2BB3456B0B919B3B42A9432A_StaticFields
{
	int2_tF4AC25F87943DC0B2BB3456B0B919B3B42A9432A ___zero;
};
struct Exception_t_StaticFields
{
	RuntimeObject* ___s_EDILock;
};
struct Type_t_StaticFields
{
	Binder_t91BFCE95A7057FADF4D8A1A342AFE52872246235* ___s_defaultBinder;
	Il2CppChar ___Delimiter;
	TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* ___EmptyTypes;
	RuntimeObject* ___Missing;
	MemberFilter_tF644F1AE82F611B677CE1964D5A3277DDA21D553* ___FilterAttribute;
	MemberFilter_tF644F1AE82F611B677CE1964D5A3277DDA21D553* ___FilterName;
	MemberFilter_tF644F1AE82F611B677CE1964D5A3277DDA21D553* ___FilterNameIgnoreCase;
};
#ifdef __clang__
#pragma clang diagnostic pop
#endif


IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void NativeArray_1__ctor_mB7BB23924A114599D399A5EC6C00B2B6407CF66D_gshared (NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C* __this, int32_t ___0_length, int32_t ___1_allocator, int32_t ___2_options, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void NativeArray_1_Dispose_m05C674E687B921C37722A6A1FF938FD56574642E_gshared (NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ModuleHandle_Copy_Tisint2_tF4AC25F87943DC0B2BB3456B0B919B3B42A9432A_mBFF6B1AB4AB8B44E22265CE0FC194BEAF466399C_gshared (NativeArray_1_tE86585F07CF10FCD01AA2652A104B149336F7EC2 ___0_src, NativeArray_1_tE86585F07CF10FCD01AA2652A104B149336F7EC2 ___1_dst, int32_t ___2_length, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ModuleHandle_Copy_Tisfloat2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA_m07543F135AF3627179553F55ED1804459CFCE11F_gshared (NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E ___0_src, NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E ___1_dst, int32_t ___2_length, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ModuleHandle_Copy_TisInt32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_m08EAF9EE4BD7B3611121EEBB1CA1AC40D9C29874_gshared (NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C ___0_src, NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C ___1_dst, int32_t ___2_length, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void NativeArray_1__ctor_m3CB679B1B77F99FC5CF890F75C914E22555A1F13_gshared (NativeArray_1_tE86585F07CF10FCD01AA2652A104B149336F7EC2* __this, int32_t ___0_length, int32_t ___1_allocator, int32_t ___2_options, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void NativeArray_1__ctor_mAF9D0A865FBFFE6364C3073A253711B4C109C67A_gshared (NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E* __this, int32_t ___0_length, int32_t ___1_allocator, int32_t ___2_options, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void NativeArray_1_Dispose_mAD3B69E4B23316C46AF8C35D7E1E81206323F16F_gshared (NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void NativeArray_1_Dispose_m3135DCFBA5DDC3D2CAA20FB2666F3A996856F2F2_gshared (NativeArray_1_tE86585F07CF10FCD01AA2652A104B149336F7EC2* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void IJobExtensions_EarlyJobInit_TisDrawCallJob_t3EA2ABC822AD5DF50675A5B437DAB927DB95215D_m8175192716D38C9C006D2C98D5B36A7CC75E33AD_gshared (const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void IJobParallelForTransformExtensions_EarlyJobInit_TisUpdateTransformsJob_t7CF957169E8C6560084F48A51BC15A447F3002C7_mA2F79749E27788E6B6B0DA93086B08A0C553480E_gshared (const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void IJobForExtensions_EarlyJobInit_TisLightMinMaxZJob_tB4FE0854445DAADF46E5511EAAF54EA1E4B611C4_m6D407EF2FD72EE5C26BA1C843C9932ABCB9C16DA_gshared (const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void IJobForExtensions_EarlyJobInit_TisReflectionProbeMinMaxZJob_tB55272F39D5B8B189F5DF7212CDA3FFF1EC0C71C_mE2595D2099394DF551AC793D9874D90167904DF2_gshared (const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void IJobForExtensions_EarlyJobInit_TisTileRangeExpansionJob_t8342AD91DCB87CA5DBDB463981EE24D47408C876_m7F3455814736950B257AF91E4AE26136F7D2D588_gshared (const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void IJobForExtensions_EarlyJobInit_TisTilingJob_t4506E6F62C95A90210A474DE43C83AF5EB8D3352_m4C05E9D5B6CBBFDCFF690B0C60C83CC47D299593_gshared (const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void IJobForExtensions_EarlyJobInit_TisZBinningJob_t9BC217C31924E66E667568C1B51EA2F44FA0A08E_mA5B015BCBE9BD211FDEFDAE5347DCBE7E91D1970_gshared (const RuntimeMethod* method) ;

IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float ModuleHandle_OrientFast_m39D34C2844061E3607200824ADE00A8CA5680634 (float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA ___0_a, float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA ___1_b, float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA ___2_c, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t TessEventCompare_Compare_mFBC498906F6BF3279BB6843DC8C7D12B9D4522C1 (TessEventCompare_t286A68412E43ED2680CC0D5FF7189FADAB1FBC4F* __this, UEvent_t6B46070B7BC816FDF53E67775BD4EA5508B807C2 ___0_a, UEvent_t6B46070B7BC816FDF53E67775BD4EA5508B807C2 ___1_b, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t TessEdgeCompare_Compare_m29540C70B61ECC31030C7D3F52E8F3B534BEC37D (TessEdgeCompare_t41229EB788BF2503A67B087C78F734EADDE71434* __this, int2_tF4AC25F87943DC0B2BB3456B0B919B3B42A9432A ___0_a, int2_tF4AC25F87943DC0B2BB3456B0B919B3B42A9432A ___1_b, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t TessCellCompare_Compare_m1CA8ED338230EF907349884C7B60CCE3559BA57A (TessCellCompare_tC18A10A741079B0B69C186B257E59D25768908BE* __this, int3_t1D01D28AA6D32890A228297EBADD9BB1A960E2BF ___0_a, int3_t1D01D28AA6D32890A228297EBADD9BB1A960E2BF ___1_b, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t TessJunctionCompare_Compare_m212C2AE3DCBD9AB1C8FA5E503811F8E573E6285A (TessJunctionCompare_tBB128A7D2EB479A4CCFE1AB7ECD6A2F284762249* __this, int2_tF4AC25F87943DC0B2BB3456B0B919B3B42A9432A ___0_a, int2_tF4AC25F87943DC0B2BB3456B0B919B3B42A9432A ___1_b, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t DelaEdgeCompare_Compare_m9D63142C6EBC894B7DAD24EBE3A38FDD5678BEF2 (DelaEdgeCompare_tB840B82782097F179823018C8C3F86D79167714B* __this, int4_tBA77D4945786DE82C3A487B33955EA1004996052 ___0_a, int4_tBA77D4945786DE82C3A487B33955EA1004996052 ___1_b, const RuntimeMethod* method) ;
inline void NativeArray_1__ctor_mB7BB23924A114599D399A5EC6C00B2B6407CF66D (NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C* __this, int32_t ___0_length, int32_t ___1_allocator, int32_t ___2_options, const RuntimeMethod* method)
{
	((  void (*) (NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C*, int32_t, int32_t, int32_t, const RuntimeMethod*))NativeArray_1__ctor_mB7BB23924A114599D399A5EC6C00B2B6407CF66D_gshared)(__this, ___0_length, ___1_allocator, ___2_options, method);
}
inline void NativeArray_1_Dispose_m05C674E687B921C37722A6A1FF938FD56574642E (NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C* __this, const RuntimeMethod* method)
{
	((  void (*) (NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C*, const RuntimeMethod*))NativeArray_1_Dispose_m05C674E687B921C37722A6A1FF938FD56574642E_gshared)(__this, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t TessLink_Find_mAD567324D6131379359E9A266D2AC30A31F0226E (TessLink_tB36FA873ED8F645C11850647C5EAECC581B60D38* __this, int32_t ___0_x, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TessLink_Link_m549C4DE253753727A94A94BC9BC7EF6B417DC9E0 (TessLink_tB36FA873ED8F645C11850647C5EAECC581B60D38* __this, int32_t ___0_x, int32_t ___1_y, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float math_abs_m3D9508B36B045BFE7B89C6C69AD34596264E4FE1_inline (float ___0_x, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR double math_abs_mDF669CF3AF2C60713E8E118578461CDA050DAFD0_inline (double ___0_x, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void float2__ctor_m3D598E2C2D173DE852F3AB157502968261383C97_inline (float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA* __this, float ___0_x, float ___1_y, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float math_sqrt_mEF31DE7BD0179009683C5D7B0C58E6571B30CF4A_inline (float ___0_x, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float math_distance_mE5E0FFDD103E710A4CB23360BFCAFD0AF2E1EFA9_inline (float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA ___0_x, float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA ___1_y, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void float3__ctor_mC61002CD0EC13D7C37D846D021A78C028FB80DB9_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* __this, float ___0_x, float ___1_y, float ___2_z, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E float3_op_Subtraction_mB6036E9849D95650D6E73DA0D179CD7B61E696F2_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_lhs, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_rhs, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E math_cross_m4CA2DAE150C6381B0D05E8AA9E48E88CF6157180_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_x, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_y, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float ModuleHandle_Sign_mAD4C03A02763F90C0B6BB07F6A9A11DC00B59294 (float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA ___0_p1, float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA ___1_p2, float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA ___2_p3, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float ModuleHandle_TriangleArea_m40B327CC9176416944F14D245D0F49DD5CB2CEA0 (float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA ___0_va, float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA ___1_vb, float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA ___2_vc, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float math_dot_mF673D3E5B7D267C0A8569B678D05BDCCB667D04D_inline (float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA ___0_x, float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA ___1_y, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR UCircle_t**************************************** ModuleHandle_CircumCircle_m**************************************** (UTriangle_tCD210F61D627BAB81A1CFFEC7076C3FBB9A6D7CD ___0_tri, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float math_max_m4B454A91AE8827997609E74C4C24036BBD3CC496_inline (float ___0_x, float ___1_y, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float math_min_m54FD010BEF505D2BA1F79FC793BEB0723C329C3B_inline (float ___0_x, float ___1_y, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void int3__ctor_mE478318DE4CA648614FEF2C1DD438C0455284BF2_inline (int3_t1D01D28AA6D32890A228297EBADD9BB1A960E2BF* __this, int32_t ___0_x, int32_t ___1_y, int32_t ___2_z, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t math_min_m02D43DF516544C279AF660EA4731449C82991849_inline (int32_t ___0_x, int32_t ___1_y, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t math_max_m9083201D37A8ED0157B127B5878D9B7F3A2A40BE_inline (int32_t ___0_x, int32_t ___1_y, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void int4__ctor_m4E8D71A09721E26F7FCCE82EA8AD699062EE6216_inline (int4_tBA77D4945786DE82C3A487B33955EA1004996052* __this, int32_t ___0_x, int32_t ___1_y, int32_t ___2_z, int32_t ___3_w, const RuntimeMethod* method) ;
inline void ModuleHandle_Copy_Tisint2_tF4AC25F87943DC0B2BB3456B0B919B3B42A9432A_mBFF6B1AB4AB8B44E22265CE0FC194BEAF466399C (NativeArray_1_tE86585F07CF10FCD01AA2652A104B149336F7EC2 ___0_src, NativeArray_1_tE86585F07CF10FCD01AA2652A104B149336F7EC2 ___1_dst, int32_t ___2_length, const RuntimeMethod* method)
{
	((  void (*) (NativeArray_1_tE86585F07CF10FCD01AA2652A104B149336F7EC2, NativeArray_1_tE86585F07CF10FCD01AA2652A104B149336F7EC2, int32_t, const RuntimeMethod*))ModuleHandle_Copy_Tisint2_tF4AC25F87943DC0B2BB3456B0B919B3B42A9432A_mBFF6B1AB4AB8B44E22265CE0FC194BEAF466399C_gshared)(___0_src, ___1_dst, ___2_length, method);
}
inline void ModuleHandle_Copy_Tisfloat2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA_m07543F135AF3627179553F55ED1804459CFCE11F (NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E ___0_src, NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E ___1_dst, int32_t ___2_length, const RuntimeMethod* method)
{
	((  void (*) (NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E, NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E, int32_t, const RuntimeMethod*))ModuleHandle_Copy_Tisfloat2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA_m07543F135AF3627179553F55ED1804459CFCE11F_gshared)(___0_src, ___1_dst, ___2_length, method);
}
inline void ModuleHandle_Copy_TisInt32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_m08EAF9EE4BD7B3611121EEBB1CA1AC40D9C29874 (NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C ___0_src, NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C ___1_dst, int32_t ___2_length, const RuntimeMethod* method)
{
	((  void (*) (NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C, NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C, int32_t, const RuntimeMethod*))ModuleHandle_Copy_TisInt32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_m08EAF9EE4BD7B3611121EEBB1CA1AC40D9C29874_gshared)(___0_src, ___1_dst, ___2_length, method);
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA math_min_m68ED612C41E325FA3446050EA04D0AC0CD191558_inline (float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA ___0_x, float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA ___1_y, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA math_max_mFD64D6399932C2D91018BA7895C06FD055E1361B_inline (float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA ___0_x, float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA ___1_y, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA float2_op_Subtraction_m28172675A65BCFFBC8C9023BE815019E668B8380_inline (float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA ___0_lhs, float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA ___1_rhs, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA float2_op_Multiply_m34D03129CE0D7AD665A914DE83CB749585B2455F_inline (float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA ___0_lhs, float ___1_rhs, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void int2__ctor_m452D21510717D0961119C89A72BBB8D84DCD49F4_inline (int2_tF4AC25F87943DC0B2BB3456B0B919B3B42A9432A* __this, int32_t ___0_x, int32_t ___1_y, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ModuleHandle_Reorder_mC37CEAA08AE5E9B432106F95C633908AE0ABD600 (int32_t ___0_startVertexCount, int32_t ___1_index, NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C* ___2_indices, int32_t* ___3_indexCount, NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E* ___4_vertices, int32_t* ___5_vertexCount, const RuntimeMethod* method) ;
inline void NativeArray_1__ctor_m3CB679B1B77F99FC5CF890F75C914E22555A1F13 (NativeArray_1_tE86585F07CF10FCD01AA2652A104B149336F7EC2* __this, int32_t ___0_length, int32_t ___1_allocator, int32_t ___2_options, const RuntimeMethod* method)
{
	((  void (*) (NativeArray_1_tE86585F07CF10FCD01AA2652A104B149336F7EC2*, int32_t, int32_t, int32_t, const RuntimeMethod*))NativeArray_1__ctor_m3CB679B1B77F99FC5CF890F75C914E22555A1F13_gshared)(__this, ___0_length, ___1_allocator, ___2_options, method);
}
inline void NativeArray_1__ctor_mAF9D0A865FBFFE6364C3073A253711B4C109C67A (NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E* __this, int32_t ___0_length, int32_t ___1_allocator, int32_t ___2_options, const RuntimeMethod* method)
{
	((  void (*) (NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E*, int32_t, int32_t, int32_t, const RuntimeMethod*))NativeArray_1__ctor_mAF9D0A865FBFFE6364C3073A253711B4C109C67A_gshared)(__this, ___0_length, ___1_allocator, ___2_options, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ModuleHandle_GraphConditioner_m7C685F797F7096123ABD62F4932FFC0177FDE3CF (NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E ___0_points, NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E* ___1_pgPoints, int32_t* ___2_pgPointCount, NativeArray_1_tE86585F07CF10FCD01AA2652A104B149336F7EC2* ___3_pgEdges, int32_t* ___4_pgEdgeCount, bool ___5_resetTopology, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Tessellator_Tessellate_m84DB7B38E7EC9AB5155F7EEDBC3382CF1092EC5E (int32_t ___0_allocator, NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E ___1_pgPoints, int32_t ___2_pgPointCount, NativeArray_1_tE86585F07CF10FCD01AA2652A104B149336F7EC2 ___3_pgEdges, int32_t ___4_pgEdgeCount, NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E* ___5_outputVertices, int32_t* ___6_vertexCount, NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C* ___7_outputIndices, int32_t* ___8_indexCount, const RuntimeMethod* method) ;
inline void NativeArray_1_Dispose_mAD3B69E4B23316C46AF8C35D7E1E81206323F16F (NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E* __this, const RuntimeMethod* method)
{
	((  void (*) (NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E*, const RuntimeMethod*))NativeArray_1_Dispose_mAD3B69E4B23316C46AF8C35D7E1E81206323F16F_gshared)(__this, method);
}
inline void NativeArray_1_Dispose_m3135DCFBA5DDC3D2CAA20FB2666F3A996856F2F2 (NativeArray_1_tE86585F07CF10FCD01AA2652A104B149336F7EC2* __this, const RuntimeMethod* method)
{
	((  void (*) (NativeArray_1_tE86585F07CF10FCD01AA2652A104B149336F7EC2*, const RuntimeMethod*))NativeArray_1_Dispose_m3135DCFBA5DDC3D2CAA20FB2666F3A996856F2F2_gshared)(__this, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool PlanarGraph_Validate_mD6F5B2173F4C2C298986E926D9C372B88B0ED39D (int32_t ___0_allocator, NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E ___1_inputPoints, int32_t ___2_pointCount, NativeArray_1_tE86585F07CF10FCD01AA2652A104B149336F7EC2 ___3_inputEdges, int32_t ___4_edgeCount, NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E* ___5_outputPoints, int32_t* ___6_outputPointCount, NativeArray_1_tE86585F07CF10FCD01AA2652A104B149336F7EC2* ___7_outputEdges, int32_t* ___8_outputEdgeCount, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ModuleHandle_TransferOutput_mE69BFB90D12C2CA71E368EC00347F5C1DA21BDAD (NativeArray_1_tE86585F07CF10FCD01AA2652A104B149336F7EC2 ___0_srcEdges, int32_t ___1_srcEdgeCount, NativeArray_1_tE86585F07CF10FCD01AA2652A104B149336F7EC2* ___2_dstEdges, int32_t* ___3_dstEdgeCount, NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C ___4_srcIndices, int32_t ___5_srcIndexCount, NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C* ___6_dstIndices, int32_t* ___7_dstIndexCount, NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E ___8_srcVertices, int32_t ___9_srcVertexCount, NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E* ___10_dstVertices, int32_t* ___11_dstVertexCount, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Math_Min_m53C488772A34D53917BCA2A491E79A0A5356ED52 (int32_t ___0_val1, int32_t ___1_val2, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ModuleHandle_CopyGraph_mD198F917465F876C1D09639EED8A2C2600ADF7EB (NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E ___0_srcPoints, int32_t ___1_srcPointCount, NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E* ___2_dstPoints, int32_t* ___3_dstPointCount, NativeArray_1_tE86585F07CF10FCD01AA2652A104B149336F7EC2 ___4_srcEdges, int32_t ___5_srcEdgeCount, NativeArray_1_tE86585F07CF10FCD01AA2652A104B149336F7EC2* ___6_dstEdges, int32_t* ___7_dstEdgeCount, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ModuleHandle_CopyGeometry_m41B14E71387642F5CDDA4F2C8C2C173FA9FF5E3C (NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C ___0_srcIndices, int32_t ___1_srcIndexCount, NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C* ___2_dstIndices, int32_t* ___3_dstIndexCount, NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E ___4_srcVertices, int32_t ___5_srcVertexCount, NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E* ___6_dstVertices, int32_t* ___7_dstVertexCount, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Refinery_Condition_m75ECBF8D82871AEB1D046E61F20DD3700E18D214 (int32_t ___0_allocator, float ___1_factorArea, float ___2_targetArea, NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E* ___3_pgPoints, int32_t* ___4_pgPointCount, NativeArray_1_tE86585F07CF10FCD01AA2652A104B149336F7EC2* ___5_pgEdges, int32_t* ___6_pgEdgeCount, NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E* ___7_vertices, int32_t* ___8_vertexCount, NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C* ___9_indices, int32_t* ___10_indexCount, float* ___11_maxArea, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float math_lerp_m58A82DB48BBA11871FFA81583C700875B3A9BC84_inline (float ___0_x, float ___1_y, float ___2_s, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ModuleHandle_VertexCleanupConditioner_m53303FF76EFACCC24EB5C389780B9533FBD50D5A (int32_t ___0_startVertexCount, NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C* ___1_indices, int32_t* ___2_indexCount, NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E* ___3_vertices, int32_t* ___4_vertexCount, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t math_clamp_m9EABD008C8EAD9D150062ABE724D96FA2121EE1C_inline (int32_t ___0_x, int32_t ___1_a, int32_t ___2_b, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Smoothen_Condition_m831A479BB846A668D896E06A2737129629F3DFC2 (int32_t ___0_allocator, NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E* ___1_pgPoints, int32_t ___2_pgPointCount, NativeArray_1_tE86585F07CF10FCD01AA2652A104B149336F7EC2 ___3_pgEdges, int32_t ___4_pgEdgeCount, NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E* ___5_vertices, int32_t* ___6_vertexCount, NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C* ___7_indices, int32_t* ___8_indexCount, const RuntimeMethod* method) ;
inline void IJobExtensions_EarlyJobInit_TisDrawCallJob_t3EA2ABC822AD5DF50675A5B437DAB927DB95215D_m8175192716D38C9C006D2C98D5B36A7CC75E33AD (const RuntimeMethod* method)
{
	((  void (*) (const RuntimeMethod*))IJobExtensions_EarlyJobInit_TisDrawCallJob_t3EA2ABC822AD5DF50675A5B437DAB927DB95215D_m8175192716D38C9C006D2C98D5B36A7CC75E33AD_gshared)(method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Type_t* Type_GetTypeFromHandle_m6062B81682F79A4D6DF2640692EE6D9987858C57 (RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B ___0_handle, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void EarlyInitHelpers_JobReflectionDataCreationFailed_m385E2249E15484F8BCB1A1EA9BA9AFB7EB425109 (Exception_t* ___0_ex, Type_t* ___1_jobType, const RuntimeMethod* method) ;
inline void IJobParallelForTransformExtensions_EarlyJobInit_TisUpdateTransformsJob_t7CF957169E8C6560084F48A51BC15A447F3002C7_mA2F79749E27788E6B6B0DA93086B08A0C553480E (const RuntimeMethod* method)
{
	((  void (*) (const RuntimeMethod*))IJobParallelForTransformExtensions_EarlyJobInit_TisUpdateTransformsJob_t7CF957169E8C6560084F48A51BC15A447F3002C7_mA2F79749E27788E6B6B0DA93086B08A0C553480E_gshared)(method);
}
inline void IJobForExtensions_EarlyJobInit_TisLightMinMaxZJob_tB4FE0854445DAADF46E5511EAAF54EA1E4B611C4_m6D407EF2FD72EE5C26BA1C843C9932ABCB9C16DA (const RuntimeMethod* method)
{
	((  void (*) (const RuntimeMethod*))IJobForExtensions_EarlyJobInit_TisLightMinMaxZJob_tB4FE0854445DAADF46E5511EAAF54EA1E4B611C4_m6D407EF2FD72EE5C26BA1C843C9932ABCB9C16DA_gshared)(method);
}
inline void IJobForExtensions_EarlyJobInit_TisReflectionProbeMinMaxZJob_tB55272F39D5B8B189F5DF7212CDA3FFF1EC0C71C_mE2595D2099394DF551AC793D9874D90167904DF2 (const RuntimeMethod* method)
{
	((  void (*) (const RuntimeMethod*))IJobForExtensions_EarlyJobInit_TisReflectionProbeMinMaxZJob_tB55272F39D5B8B189F5DF7212CDA3FFF1EC0C71C_mE2595D2099394DF551AC793D9874D90167904DF2_gshared)(method);
}
inline void IJobForExtensions_EarlyJobInit_TisTileRangeExpansionJob_t8342AD91DCB87CA5DBDB463981EE24D47408C876_m7F3455814736950B257AF91E4AE26136F7D2D588 (const RuntimeMethod* method)
{
	((  void (*) (const RuntimeMethod*))IJobForExtensions_EarlyJobInit_TisTileRangeExpansionJob_t8342AD91DCB87CA5DBDB463981EE24D47408C876_m7F3455814736950B257AF91E4AE26136F7D2D588_gshared)(method);
}
inline void IJobForExtensions_EarlyJobInit_TisTilingJob_t4506E6F62C95A90210A474DE43C83AF5EB8D3352_m4C05E9D5B6CBBFDCFF690B0C60C83CC47D299593 (const RuntimeMethod* method)
{
	((  void (*) (const RuntimeMethod*))IJobForExtensions_EarlyJobInit_TisTilingJob_t4506E6F62C95A90210A474DE43C83AF5EB8D3352_m4C05E9D5B6CBBFDCFF690B0C60C83CC47D299593_gshared)(method);
}
inline void IJobForExtensions_EarlyJobInit_TisZBinningJob_t9BC217C31924E66E667568C1B51EA2F44FA0A08E_mA5B015BCBE9BD211FDEFDAE5347DCBE7E91D1970 (const RuntimeMethod* method)
{
	((  void (*) (const RuntimeMethod*))IJobForExtensions_EarlyJobInit_TisZBinningJob_t9BC217C31924E66E667568C1B51EA2F44FA0A08E_mA5B015BCBE9BD211FDEFDAE5347DCBE7E91D1970_gshared)(method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void __JobReflectionRegistrationOutput__2787129498_CreateJobReflectionData_mCA7D08807C4C5650B11C4359590829BF1377336B (const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint32_t math_asuint_m503D1ABF19E4BA615FD8AE1BF1A2E103BBED6139_inline (float ___0_x, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float math_asfloat_m20D259DAAB46464B59BD8BF5678F9D59800F70A9_inline (uint32_t ___0_x, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint64_t math_asulong_m2CF160E23B5FF618A85C3C29B2FB1C000E40290F_inline (double ___0_x, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR double math_asdouble_m3E7BC790C743E67EA45476AECD6D2D9A9E62E4F2_inline (uint64_t ___0_x, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float math_length_m3DB47D254C8544FBB740A892B4AE2143E8F45634_inline (float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA ___0_x, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E float3_get_yzx_mDF6DE39B69C5DE384F74C0D1EC91AA0388E23535_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E float3_op_Multiply_m05E57074FBD5FAB0E72940C9CC019C41915280D7_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_lhs, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_rhs, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool Single_IsNaN_mFE637F6ECA9F7697CE8EFF56427858F4C5EDF75D_inline (float ___0_f, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t math_asint_mBDED7FE966CA65F6A8ACEAEF8FD779B1B8998288_inline (float ___0_x, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float math_asfloat_m9FA56DE5C61FCEF3DCD0675252D40DFD9C9B712F_inline (int32_t ___0_x, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int64_t math_aslong_mCD3846AC0EFB4901B00A20D0960C80C8CBE66366_inline (double ___0_x, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR double math_asdouble_m4C4CC1B9299FE33530ED375768D67B00676C31C8_inline (int64_t ___0_x, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t BitConverter_SingleToInt32Bits_mC760C7CFC89725E3CF68DC45BE3A9A42A7E7DA73_inline (float ___0_value, const RuntimeMethod* method) ;
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t TessEventCompare_Compare_mFBC498906F6BF3279BB6843DC8C7D12B9D4522C1 (TessEventCompare_t286A68412E43ED2680CC0D5FF7189FADAB1FBC4F* __this, UEvent_t6B46070B7BC816FDF53E67775BD4EA5508B807C2 ___0_a, UEvent_t6B46070B7BC816FDF53E67775BD4EA5508B807C2 ___1_b, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ModuleHandle_tF45CBC10412074A0AAEF63CBDAA5F92A4F7A9978_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	float V_0 = 0.0f;
	int32_t V_1 = 0;
	float V_2 = 0.0f;
	{
		UEvent_t6B46070B7BC816FDF53E67775BD4EA5508B807C2 L_0 = ___0_a;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_1 = L_0.___a;
		float L_2 = L_1.___x;
		UEvent_t6B46070B7BC816FDF53E67775BD4EA5508B807C2 L_3 = ___1_b;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_4 = L_3.___a;
		float L_5 = L_4.___x;
		V_0 = ((float)il2cpp_codegen_subtract(L_2, L_5));
		float L_6 = V_0;
		if ((((float)(0.0f)) == ((float)L_6)))
		{
			goto IL_002c;
		}
	}
	{
		float L_7 = V_0;
		if ((((float)L_7) > ((float)(0.0f))))
		{
			goto IL_002a;
		}
	}
	{
		return (-1);
	}

IL_002a:
	{
		return 1;
	}

IL_002c:
	{
		UEvent_t6B46070B7BC816FDF53E67775BD4EA5508B807C2 L_8 = ___0_a;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_9 = L_8.___a;
		float L_10 = L_9.___y;
		UEvent_t6B46070B7BC816FDF53E67775BD4EA5508B807C2 L_11 = ___1_b;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_12 = L_11.___a;
		float L_13 = L_12.___y;
		V_0 = ((float)il2cpp_codegen_subtract(L_10, L_13));
		float L_14 = V_0;
		if ((((float)(0.0f)) == ((float)L_14)))
		{
			goto IL_0058;
		}
	}
	{
		float L_15 = V_0;
		if ((((float)L_15) > ((float)(0.0f))))
		{
			goto IL_0056;
		}
	}
	{
		return (-1);
	}

IL_0056:
	{
		return 1;
	}

IL_0058:
	{
		UEvent_t6B46070B7BC816FDF53E67775BD4EA5508B807C2 L_16 = ___0_a;
		int32_t L_17 = L_16.___type;
		UEvent_t6B46070B7BC816FDF53E67775BD4EA5508B807C2 L_18 = ___1_b;
		int32_t L_19 = L_18.___type;
		V_1 = ((int32_t)il2cpp_codegen_subtract(L_17, L_19));
		int32_t L_20 = V_1;
		if (!L_20)
		{
			goto IL_006b;
		}
	}
	{
		int32_t L_21 = V_1;
		return L_21;
	}

IL_006b:
	{
		UEvent_t6B46070B7BC816FDF53E67775BD4EA5508B807C2 L_22 = ___0_a;
		int32_t L_23 = L_22.___type;
		if (!L_23)
		{
			goto IL_009f;
		}
	}
	{
		UEvent_t6B46070B7BC816FDF53E67775BD4EA5508B807C2 L_24 = ___0_a;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_25 = L_24.___a;
		UEvent_t6B46070B7BC816FDF53E67775BD4EA5508B807C2 L_26 = ___0_a;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_27 = L_26.___b;
		UEvent_t6B46070B7BC816FDF53E67775BD4EA5508B807C2 L_28 = ___1_b;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_29 = L_28.___b;
		il2cpp_codegen_runtime_class_init_inline(ModuleHandle_tF45CBC10412074A0AAEF63CBDAA5F92A4F7A9978_il2cpp_TypeInfo_var);
		float L_30;
		L_30 = ModuleHandle_OrientFast_m39D34C2844061E3607200824ADE00A8CA5680634(L_25, L_27, L_29, NULL);
		V_2 = L_30;
		float L_31 = V_2;
		if ((((float)(0.0f)) == ((float)L_31)))
		{
			goto IL_009f;
		}
	}
	{
		float L_32 = V_2;
		if ((((float)L_32) > ((float)(0.0f))))
		{
			goto IL_009d;
		}
	}
	{
		return (-1);
	}

IL_009d:
	{
		return 1;
	}

IL_009f:
	{
		UEvent_t6B46070B7BC816FDF53E67775BD4EA5508B807C2 L_33 = ___0_a;
		int32_t L_34 = L_33.___idx;
		UEvent_t6B46070B7BC816FDF53E67775BD4EA5508B807C2 L_35 = ___1_b;
		int32_t L_36 = L_35.___idx;
		return ((int32_t)il2cpp_codegen_subtract(L_34, L_36));
	}
}
IL2CPP_EXTERN_C  int32_t TessEventCompare_Compare_mFBC498906F6BF3279BB6843DC8C7D12B9D4522C1_AdjustorThunk (RuntimeObject* __this, UEvent_t6B46070B7BC816FDF53E67775BD4EA5508B807C2 ___0_a, UEvent_t6B46070B7BC816FDF53E67775BD4EA5508B807C2 ___1_b, const RuntimeMethod* method)
{
	TessEventCompare_t286A68412E43ED2680CC0D5FF7189FADAB1FBC4F* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<TessEventCompare_t286A68412E43ED2680CC0D5FF7189FADAB1FBC4F*>(__this + _offset);
	int32_t _returnValue;
	_returnValue = TessEventCompare_Compare_mFBC498906F6BF3279BB6843DC8C7D12B9D4522C1(_thisAdjusted, ___0_a, ___1_b, method);
	return _returnValue;
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t TessEdgeCompare_Compare_m29540C70B61ECC31030C7D3F52E8F3B534BEC37D (TessEdgeCompare_t41229EB788BF2503A67B087C78F734EADDE71434* __this, int2_tF4AC25F87943DC0B2BB3456B0B919B3B42A9432A ___0_a, int2_tF4AC25F87943DC0B2BB3456B0B919B3B42A9432A ___1_b, const RuntimeMethod* method) 
{
	int32_t V_0 = 0;
	{
		int2_tF4AC25F87943DC0B2BB3456B0B919B3B42A9432A L_0 = ___0_a;
		int32_t L_1 = L_0.___x;
		int2_tF4AC25F87943DC0B2BB3456B0B919B3B42A9432A L_2 = ___1_b;
		int32_t L_3 = L_2.___x;
		V_0 = ((int32_t)il2cpp_codegen_subtract(L_1, L_3));
		int32_t L_4 = V_0;
		if (!L_4)
		{
			goto IL_0013;
		}
	}
	{
		int32_t L_5 = V_0;
		return L_5;
	}

IL_0013:
	{
		int2_tF4AC25F87943DC0B2BB3456B0B919B3B42A9432A L_6 = ___0_a;
		int32_t L_7 = L_6.___y;
		int2_tF4AC25F87943DC0B2BB3456B0B919B3B42A9432A L_8 = ___1_b;
		int32_t L_9 = L_8.___y;
		V_0 = ((int32_t)il2cpp_codegen_subtract(L_7, L_9));
		int32_t L_10 = V_0;
		return L_10;
	}
}
IL2CPP_EXTERN_C  int32_t TessEdgeCompare_Compare_m29540C70B61ECC31030C7D3F52E8F3B534BEC37D_AdjustorThunk (RuntimeObject* __this, int2_tF4AC25F87943DC0B2BB3456B0B919B3B42A9432A ___0_a, int2_tF4AC25F87943DC0B2BB3456B0B919B3B42A9432A ___1_b, const RuntimeMethod* method)
{
	TessEdgeCompare_t41229EB788BF2503A67B087C78F734EADDE71434* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<TessEdgeCompare_t41229EB788BF2503A67B087C78F734EADDE71434*>(__this + _offset);
	int32_t _returnValue;
	_returnValue = TessEdgeCompare_Compare_m29540C70B61ECC31030C7D3F52E8F3B534BEC37D(_thisAdjusted, ___0_a, ___1_b, method);
	return _returnValue;
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t TessCellCompare_Compare_m1CA8ED338230EF907349884C7B60CCE3559BA57A (TessCellCompare_tC18A10A741079B0B69C186B257E59D25768908BE* __this, int3_t1D01D28AA6D32890A228297EBADD9BB1A960E2BF ___0_a, int3_t1D01D28AA6D32890A228297EBADD9BB1A960E2BF ___1_b, const RuntimeMethod* method) 
{
	int32_t V_0 = 0;
	{
		int3_t1D01D28AA6D32890A228297EBADD9BB1A960E2BF L_0 = ___0_a;
		int32_t L_1 = L_0.___x;
		int3_t1D01D28AA6D32890A228297EBADD9BB1A960E2BF L_2 = ___1_b;
		int32_t L_3 = L_2.___x;
		V_0 = ((int32_t)il2cpp_codegen_subtract(L_1, L_3));
		int32_t L_4 = V_0;
		if (!L_4)
		{
			goto IL_0013;
		}
	}
	{
		int32_t L_5 = V_0;
		return L_5;
	}

IL_0013:
	{
		int3_t1D01D28AA6D32890A228297EBADD9BB1A960E2BF L_6 = ___0_a;
		int32_t L_7 = L_6.___y;
		int3_t1D01D28AA6D32890A228297EBADD9BB1A960E2BF L_8 = ___1_b;
		int32_t L_9 = L_8.___y;
		V_0 = ((int32_t)il2cpp_codegen_subtract(L_7, L_9));
		int32_t L_10 = V_0;
		if (!L_10)
		{
			goto IL_0026;
		}
	}
	{
		int32_t L_11 = V_0;
		return L_11;
	}

IL_0026:
	{
		int3_t1D01D28AA6D32890A228297EBADD9BB1A960E2BF L_12 = ___0_a;
		int32_t L_13 = L_12.___z;
		int3_t1D01D28AA6D32890A228297EBADD9BB1A960E2BF L_14 = ___1_b;
		int32_t L_15 = L_14.___z;
		V_0 = ((int32_t)il2cpp_codegen_subtract(L_13, L_15));
		int32_t L_16 = V_0;
		return L_16;
	}
}
IL2CPP_EXTERN_C  int32_t TessCellCompare_Compare_m1CA8ED338230EF907349884C7B60CCE3559BA57A_AdjustorThunk (RuntimeObject* __this, int3_t1D01D28AA6D32890A228297EBADD9BB1A960E2BF ___0_a, int3_t1D01D28AA6D32890A228297EBADD9BB1A960E2BF ___1_b, const RuntimeMethod* method)
{
	TessCellCompare_tC18A10A741079B0B69C186B257E59D25768908BE* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<TessCellCompare_tC18A10A741079B0B69C186B257E59D25768908BE*>(__this + _offset);
	int32_t _returnValue;
	_returnValue = TessCellCompare_Compare_m1CA8ED338230EF907349884C7B60CCE3559BA57A(_thisAdjusted, ___0_a, ___1_b, method);
	return _returnValue;
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t TessJunctionCompare_Compare_m212C2AE3DCBD9AB1C8FA5E503811F8E573E6285A (TessJunctionCompare_tBB128A7D2EB479A4CCFE1AB7ECD6A2F284762249* __this, int2_tF4AC25F87943DC0B2BB3456B0B919B3B42A9432A ___0_a, int2_tF4AC25F87943DC0B2BB3456B0B919B3B42A9432A ___1_b, const RuntimeMethod* method) 
{
	int32_t V_0 = 0;
	{
		int2_tF4AC25F87943DC0B2BB3456B0B919B3B42A9432A L_0 = ___0_a;
		int32_t L_1 = L_0.___x;
		int2_tF4AC25F87943DC0B2BB3456B0B919B3B42A9432A L_2 = ___1_b;
		int32_t L_3 = L_2.___x;
		V_0 = ((int32_t)il2cpp_codegen_subtract(L_1, L_3));
		int32_t L_4 = V_0;
		if (!L_4)
		{
			goto IL_0013;
		}
	}
	{
		int32_t L_5 = V_0;
		return L_5;
	}

IL_0013:
	{
		int2_tF4AC25F87943DC0B2BB3456B0B919B3B42A9432A L_6 = ___0_a;
		int32_t L_7 = L_6.___y;
		int2_tF4AC25F87943DC0B2BB3456B0B919B3B42A9432A L_8 = ___1_b;
		int32_t L_9 = L_8.___y;
		V_0 = ((int32_t)il2cpp_codegen_subtract(L_7, L_9));
		int32_t L_10 = V_0;
		return L_10;
	}
}
IL2CPP_EXTERN_C  int32_t TessJunctionCompare_Compare_m212C2AE3DCBD9AB1C8FA5E503811F8E573E6285A_AdjustorThunk (RuntimeObject* __this, int2_tF4AC25F87943DC0B2BB3456B0B919B3B42A9432A ___0_a, int2_tF4AC25F87943DC0B2BB3456B0B919B3B42A9432A ___1_b, const RuntimeMethod* method)
{
	TessJunctionCompare_tBB128A7D2EB479A4CCFE1AB7ECD6A2F284762249* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<TessJunctionCompare_tBB128A7D2EB479A4CCFE1AB7ECD6A2F284762249*>(__this + _offset);
	int32_t _returnValue;
	_returnValue = TessJunctionCompare_Compare_m212C2AE3DCBD9AB1C8FA5E503811F8E573E6285A(_thisAdjusted, ___0_a, ___1_b, method);
	return _returnValue;
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t DelaEdgeCompare_Compare_m9D63142C6EBC894B7DAD24EBE3A38FDD5678BEF2 (DelaEdgeCompare_tB840B82782097F179823018C8C3F86D79167714B* __this, int4_tBA77D4945786DE82C3A487B33955EA1004996052 ___0_a, int4_tBA77D4945786DE82C3A487B33955EA1004996052 ___1_b, const RuntimeMethod* method) 
{
	int32_t V_0 = 0;
	{
		int4_tBA77D4945786DE82C3A487B33955EA1004996052 L_0 = ___0_a;
		int32_t L_1 = L_0.___x;
		int4_tBA77D4945786DE82C3A487B33955EA1004996052 L_2 = ___1_b;
		int32_t L_3 = L_2.___x;
		V_0 = ((int32_t)il2cpp_codegen_subtract(L_1, L_3));
		int32_t L_4 = V_0;
		if (!L_4)
		{
			goto IL_0013;
		}
	}
	{
		int32_t L_5 = V_0;
		return L_5;
	}

IL_0013:
	{
		int4_tBA77D4945786DE82C3A487B33955EA1004996052 L_6 = ___0_a;
		int32_t L_7 = L_6.___y;
		int4_tBA77D4945786DE82C3A487B33955EA1004996052 L_8 = ___1_b;
		int32_t L_9 = L_8.___y;
		V_0 = ((int32_t)il2cpp_codegen_subtract(L_7, L_9));
		int32_t L_10 = V_0;
		if (!L_10)
		{
			goto IL_0026;
		}
	}
	{
		int32_t L_11 = V_0;
		return L_11;
	}

IL_0026:
	{
		int4_tBA77D4945786DE82C3A487B33955EA1004996052 L_12 = ___0_a;
		int32_t L_13 = L_12.___z;
		int4_tBA77D4945786DE82C3A487B33955EA1004996052 L_14 = ___1_b;
		int32_t L_15 = L_14.___z;
		V_0 = ((int32_t)il2cpp_codegen_subtract(L_13, L_15));
		int32_t L_16 = V_0;
		if (!L_16)
		{
			goto IL_0039;
		}
	}
	{
		int32_t L_17 = V_0;
		return L_17;
	}

IL_0039:
	{
		int4_tBA77D4945786DE82C3A487B33955EA1004996052 L_18 = ___0_a;
		int32_t L_19 = L_18.___w;
		int4_tBA77D4945786DE82C3A487B33955EA1004996052 L_20 = ___1_b;
		int32_t L_21 = L_20.___w;
		V_0 = ((int32_t)il2cpp_codegen_subtract(L_19, L_21));
		int32_t L_22 = V_0;
		return L_22;
	}
}
IL2CPP_EXTERN_C  int32_t DelaEdgeCompare_Compare_m9D63142C6EBC894B7DAD24EBE3A38FDD5678BEF2_AdjustorThunk (RuntimeObject* __this, int4_tBA77D4945786DE82C3A487B33955EA1004996052 ___0_a, int4_tBA77D4945786DE82C3A487B33955EA1004996052 ___1_b, const RuntimeMethod* method)
{
	DelaEdgeCompare_tB840B82782097F179823018C8C3F86D79167714B* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<DelaEdgeCompare_tB840B82782097F179823018C8C3F86D79167714B*>(__this + _offset);
	int32_t _returnValue;
	_returnValue = DelaEdgeCompare_Compare_m9D63142C6EBC894B7DAD24EBE3A38FDD5678BEF2(_thisAdjusted, ___0_a, ___1_b, method);
	return _returnValue;
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR TessLink_tB36FA873ED8F645C11850647C5EAECC581B60D38 TessLink_CreateLink_m1E7D2D1DD89E196BF1CBBDC76B508021F81B2FB8 (int32_t ___0_count, int32_t ___1_allocator, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&NativeArray_1__ctor_mB7BB23924A114599D399A5EC6C00B2B6407CF66D_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	TessLink_tB36FA873ED8F645C11850647C5EAECC581B60D38 V_0;
	memset((&V_0), 0, sizeof(V_0));
	int32_t V_1 = 0;
	{
		il2cpp_codegen_initobj((&V_0), sizeof(TessLink_tB36FA873ED8F645C11850647C5EAECC581B60D38));
		int32_t L_0 = ___0_count;
		int32_t L_1 = ___1_allocator;
		NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C L_2;
		memset((&L_2), 0, sizeof(L_2));
		NativeArray_1__ctor_mB7BB23924A114599D399A5EC6C00B2B6407CF66D((&L_2), L_0, L_1, 1, NativeArray_1__ctor_mB7BB23924A114599D399A5EC6C00B2B6407CF66D_RuntimeMethod_var);
		(&V_0)->___roots = L_2;
		int32_t L_3 = ___0_count;
		int32_t L_4 = ___1_allocator;
		NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C L_5;
		memset((&L_5), 0, sizeof(L_5));
		NativeArray_1__ctor_mB7BB23924A114599D399A5EC6C00B2B6407CF66D((&L_5), L_3, L_4, 1, NativeArray_1__ctor_mB7BB23924A114599D399A5EC6C00B2B6407CF66D_RuntimeMethod_var);
		(&V_0)->___ranks = L_5;
		V_1 = 0;
		goto IL_004a;
	}

IL_002a:
	{
		NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C* L_6 = (NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C*)(&(&V_0)->___roots);
		int32_t L_7 = V_1;
		int32_t L_8 = V_1;
		IL2CPP_NATIVEARRAY_SET_ITEM(int32_t, (L_6)->___m_Buffer, L_7, (L_8));
		NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C* L_9 = (NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C*)(&(&V_0)->___ranks);
		int32_t L_10 = V_1;
		IL2CPP_NATIVEARRAY_SET_ITEM(int32_t, (L_9)->___m_Buffer, L_10, (0));
		int32_t L_11 = V_1;
		V_1 = ((int32_t)il2cpp_codegen_add(L_11, 1));
	}

IL_004a:
	{
		int32_t L_12 = V_1;
		int32_t L_13 = ___0_count;
		if ((((int32_t)L_12) < ((int32_t)L_13)))
		{
			goto IL_002a;
		}
	}
	{
		TessLink_tB36FA873ED8F645C11850647C5EAECC581B60D38 L_14 = V_0;
		return L_14;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TessLink_DestroyLink_m96AD3C95F393DABD8862EC3CE5F8EEEB905B1CAD (TessLink_tB36FA873ED8F645C11850647C5EAECC581B60D38 ___0_link, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&NativeArray_1_Dispose_m05C674E687B921C37722A6A1FF938FD56574642E_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C* L_0 = (NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C*)(&(&___0_link)->___ranks);
		NativeArray_1_Dispose_m05C674E687B921C37722A6A1FF938FD56574642E(L_0, NativeArray_1_Dispose_m05C674E687B921C37722A6A1FF938FD56574642E_RuntimeMethod_var);
		NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C* L_1 = (NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C*)(&(&___0_link)->___roots);
		NativeArray_1_Dispose_m05C674E687B921C37722A6A1FF938FD56574642E(L_1, NativeArray_1_Dispose_m05C674E687B921C37722A6A1FF938FD56574642E_RuntimeMethod_var);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t TessLink_Find_mAD567324D6131379359E9A266D2AC30A31F0226E (TessLink_tB36FA873ED8F645C11850647C5EAECC581B60D38* __this, int32_t ___0_x, const RuntimeMethod* method) 
{
	int32_t V_0 = 0;
	{
		int32_t L_0 = ___0_x;
		V_0 = L_0;
		goto IL_0012;
	}

IL_0004:
	{
		NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C* L_1 = (NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C*)(&__this->___roots);
		int32_t L_2 = ___0_x;
		int32_t L_3;
		L_3 = IL2CPP_NATIVEARRAY_GET_ITEM(int32_t, (L_1)->___m_Buffer, L_2);
		___0_x = L_3;
	}

IL_0012:
	{
		NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C* L_4 = (NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C*)(&__this->___roots);
		int32_t L_5 = ___0_x;
		int32_t L_6;
		L_6 = IL2CPP_NATIVEARRAY_GET_ITEM(int32_t, (L_4)->___m_Buffer, L_5);
		int32_t L_7 = ___0_x;
		if ((!(((uint32_t)L_6) == ((uint32_t)L_7))))
		{
			goto IL_0004;
		}
	}
	{
		goto IL_003d;
	}

IL_0023:
	{
		NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C* L_8 = (NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C*)(&__this->___roots);
		int32_t L_9 = V_0;
		int32_t L_10;
		L_10 = IL2CPP_NATIVEARRAY_GET_ITEM(int32_t, (L_8)->___m_Buffer, L_9);
		NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C* L_11 = (NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C*)(&__this->___roots);
		int32_t L_12 = V_0;
		int32_t L_13 = ___0_x;
		IL2CPP_NATIVEARRAY_SET_ITEM(int32_t, (L_11)->___m_Buffer, L_12, (L_13));
		V_0 = L_10;
	}

IL_003d:
	{
		NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C* L_14 = (NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C*)(&__this->___roots);
		int32_t L_15 = V_0;
		int32_t L_16;
		L_16 = IL2CPP_NATIVEARRAY_GET_ITEM(int32_t, (L_14)->___m_Buffer, L_15);
		int32_t L_17 = ___0_x;
		if ((!(((uint32_t)L_16) == ((uint32_t)L_17))))
		{
			goto IL_0023;
		}
	}
	{
		int32_t L_18 = ___0_x;
		return L_18;
	}
}
IL2CPP_EXTERN_C  int32_t TessLink_Find_mAD567324D6131379359E9A266D2AC30A31F0226E_AdjustorThunk (RuntimeObject* __this, int32_t ___0_x, const RuntimeMethod* method)
{
	TessLink_tB36FA873ED8F645C11850647C5EAECC581B60D38* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<TessLink_tB36FA873ED8F645C11850647C5EAECC581B60D38*>(__this + _offset);
	int32_t _returnValue;
	_returnValue = TessLink_Find_mAD567324D6131379359E9A266D2AC30A31F0226E(_thisAdjusted, ___0_x, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TessLink_Link_m549C4DE253753727A94A94BC9BC7EF6B417DC9E0 (TessLink_tB36FA873ED8F645C11850647C5EAECC581B60D38* __this, int32_t ___0_x, int32_t ___1_y, const RuntimeMethod* method) 
{
	int32_t V_0 = 0;
	int32_t V_1 = 0;
	int32_t V_2 = 0;
	int32_t V_3 = 0;
	int32_t V_4 = 0;
	int32_t V_5 = 0;
	{
		int32_t L_0 = ___0_x;
		int32_t L_1;
		L_1 = TessLink_Find_mAD567324D6131379359E9A266D2AC30A31F0226E(__this, L_0, NULL);
		V_0 = L_1;
		int32_t L_2 = ___1_y;
		int32_t L_3;
		L_3 = TessLink_Find_mAD567324D6131379359E9A266D2AC30A31F0226E(__this, L_2, NULL);
		V_1 = L_3;
		int32_t L_4 = V_0;
		int32_t L_5 = V_1;
		if ((!(((uint32_t)L_4) == ((uint32_t)L_5))))
		{
			goto IL_0015;
		}
	}
	{
		return;
	}

IL_0015:
	{
		NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C* L_6 = (NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C*)(&__this->___ranks);
		int32_t L_7 = V_0;
		int32_t L_8;
		L_8 = IL2CPP_NATIVEARRAY_GET_ITEM(int32_t, (L_6)->___m_Buffer, L_7);
		V_2 = L_8;
		NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C* L_9 = (NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C*)(&__this->___ranks);
		int32_t L_10 = V_1;
		int32_t L_11;
		L_11 = IL2CPP_NATIVEARRAY_GET_ITEM(int32_t, (L_9)->___m_Buffer, L_10);
		V_3 = L_11;
		int32_t L_12 = V_2;
		int32_t L_13 = V_3;
		if ((((int32_t)L_12) >= ((int32_t)L_13)))
		{
			goto IL_0041;
		}
	}
	{
		NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C* L_14 = (NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C*)(&__this->___roots);
		int32_t L_15 = V_0;
		int32_t L_16 = V_1;
		IL2CPP_NATIVEARRAY_SET_ITEM(int32_t, (L_14)->___m_Buffer, L_15, (L_16));
		return;
	}

IL_0041:
	{
		int32_t L_17 = V_3;
		int32_t L_18 = V_2;
		if ((((int32_t)L_17) >= ((int32_t)L_18)))
		{
			goto IL_0053;
		}
	}
	{
		NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C* L_19 = (NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C*)(&__this->___roots);
		int32_t L_20 = V_1;
		int32_t L_21 = V_0;
		IL2CPP_NATIVEARRAY_SET_ITEM(int32_t, (L_19)->___m_Buffer, L_20, (L_21));
		return;
	}

IL_0053:
	{
		NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C* L_22 = (NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C*)(&__this->___roots);
		int32_t L_23 = V_1;
		int32_t L_24 = V_0;
		IL2CPP_NATIVEARRAY_SET_ITEM(int32_t, (L_22)->___m_Buffer, L_23, (L_24));
		NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C* L_25 = (NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C*)(&__this->___ranks);
		int32_t L_26 = V_0;
		V_4 = L_26;
		NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C* L_27 = L_25;
		int32_t L_28 = V_4;
		int32_t L_29;
		L_29 = IL2CPP_NATIVEARRAY_GET_ITEM(int32_t, (L_27)->___m_Buffer, L_28);
		V_5 = ((int32_t)il2cpp_codegen_add(L_29, 1));
		int32_t L_30 = V_4;
		int32_t L_31 = V_5;
		IL2CPP_NATIVEARRAY_SET_ITEM(int32_t, (L_27)->___m_Buffer, L_30, (L_31));
		return;
	}
}
IL2CPP_EXTERN_C  void TessLink_Link_m549C4DE253753727A94A94BC9BC7EF6B417DC9E0_AdjustorThunk (RuntimeObject* __this, int32_t ___0_x, int32_t ___1_y, const RuntimeMethod* method)
{
	TessLink_tB36FA873ED8F645C11850647C5EAECC581B60D38* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<TessLink_tB36FA873ED8F645C11850647C5EAECC581B60D38*>(__this + _offset);
	TessLink_Link_m549C4DE253753727A94A94BC9BC7EF6B417DC9E0(_thisAdjusted, ___0_x, ___1_y, method);
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float ModuleHandle_OrientFast_m39D34C2844061E3607200824ADE00A8CA5680634 (float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA ___0_a, float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA ___1_b, float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA ___2_c, const RuntimeMethod* method) 
{
	float V_0 = 0.0f;
	float V_1 = 0.0f;
	{
		V_0 = (1.11022302E-16f);
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_0 = ___1_b;
		float L_1 = L_0.___y;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_2 = ___0_a;
		float L_3 = L_2.___y;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_4 = ___2_c;
		float L_5 = L_4.___x;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_6 = ___1_b;
		float L_7 = L_6.___x;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_8 = ___1_b;
		float L_9 = L_8.___x;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_10 = ___0_a;
		float L_11 = L_10.___x;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_12 = ___2_c;
		float L_13 = L_12.___y;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_14 = ___1_b;
		float L_15 = L_14.___y;
		V_1 = ((float)il2cpp_codegen_subtract(((float)il2cpp_codegen_multiply(((float)il2cpp_codegen_subtract(L_1, L_3)), ((float)il2cpp_codegen_subtract(L_5, L_7)))), ((float)il2cpp_codegen_multiply(((float)il2cpp_codegen_subtract(L_9, L_11)), ((float)il2cpp_codegen_subtract(L_13, L_15))))));
		float L_16 = V_1;
		float L_17;
		L_17 = math_abs_m3D9508B36B045BFE7B89C6C69AD34596264E4FE1_inline(L_16, NULL);
		float L_18 = V_0;
		if ((!(((float)L_17) < ((float)L_18))))
		{
			goto IL_004d;
		}
	}
	{
		return (0.0f);
	}

IL_004d:
	{
		float L_19 = V_1;
		return L_19;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR double ModuleHandle_OrientFastDouble_mABC4A67B5FBB79701003AF2B4367E67DA4E021AA (double2_t0A9854C934D0BBE9DD41F2B318B64F830D7253FA ___0_a, double2_t0A9854C934D0BBE9DD41F2B318B64F830D7253FA ___1_b, double2_t0A9854C934D0BBE9DD41F2B318B64F830D7253FA ___2_c, const RuntimeMethod* method) 
{
	double V_0 = 0.0;
	double V_1 = 0.0;
	{
		V_0 = (1.1102230246251565E-16);
		double2_t0A9854C934D0BBE9DD41F2B318B64F830D7253FA L_0 = ___1_b;
		double L_1 = L_0.___y;
		double2_t0A9854C934D0BBE9DD41F2B318B64F830D7253FA L_2 = ___0_a;
		double L_3 = L_2.___y;
		double2_t0A9854C934D0BBE9DD41F2B318B64F830D7253FA L_4 = ___2_c;
		double L_5 = L_4.___x;
		double2_t0A9854C934D0BBE9DD41F2B318B64F830D7253FA L_6 = ___1_b;
		double L_7 = L_6.___x;
		double2_t0A9854C934D0BBE9DD41F2B318B64F830D7253FA L_8 = ___1_b;
		double L_9 = L_8.___x;
		double2_t0A9854C934D0BBE9DD41F2B318B64F830D7253FA L_10 = ___0_a;
		double L_11 = L_10.___x;
		double2_t0A9854C934D0BBE9DD41F2B318B64F830D7253FA L_12 = ___2_c;
		double L_13 = L_12.___y;
		double2_t0A9854C934D0BBE9DD41F2B318B64F830D7253FA L_14 = ___1_b;
		double L_15 = L_14.___y;
		V_1 = ((double)il2cpp_codegen_subtract(((double)il2cpp_codegen_multiply(((double)il2cpp_codegen_subtract(L_1, L_3)), ((double)il2cpp_codegen_subtract(L_5, L_7)))), ((double)il2cpp_codegen_multiply(((double)il2cpp_codegen_subtract(L_9, L_11)), ((double)il2cpp_codegen_subtract(L_13, L_15))))));
		double L_16 = V_1;
		double L_17;
		L_17 = math_abs_mDF669CF3AF2C60713E8E118578461CDA050DAFD0_inline(L_16, NULL);
		double L_18 = V_0;
		if ((!(((double)L_17) < ((double)L_18))))
		{
			goto IL_0055;
		}
	}
	{
		return (0.0);
	}

IL_0055:
	{
		double L_19 = V_1;
		return L_19;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR UCircle_t**************************************** ModuleHandle_CircumCircle_m**************************************** (UTriangle_tCD210F61D627BAB81A1CFFEC7076C3FBB9A6D7CD ___0_tri, const RuntimeMethod* method) 
{
	float V_0 = 0.0f;
	float V_1 = 0.0f;
	float V_2 = 0.0f;
	float V_3 = 0.0f;
	float V_4 = 0.0f;
	float V_5 = 0.0f;
	float V_6 = 0.0f;
	float V_7 = 0.0f;
	float V_8 = 0.0f;
	float V_9 = 0.0f;
	float V_10 = 0.0f;
	UCircle_t**************************************** V_11;
	memset((&V_11), 0, sizeof(V_11));
	{
		UTriangle_tCD210F61D627BAB81A1CFFEC7076C3FBB9A6D7CD L_0 = ___0_tri;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_1 = L_0.___va;
		float L_2 = L_1.___x;
		UTriangle_tCD210F61D627BAB81A1CFFEC7076C3FBB9A6D7CD L_3 = ___0_tri;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_4 = L_3.___va;
		float L_5 = L_4.___x;
		V_0 = ((float)il2cpp_codegen_multiply(L_2, L_5));
		UTriangle_tCD210F61D627BAB81A1CFFEC7076C3FBB9A6D7CD L_6 = ___0_tri;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_7 = L_6.___vb;
		float L_8 = L_7.___x;
		UTriangle_tCD210F61D627BAB81A1CFFEC7076C3FBB9A6D7CD L_9 = ___0_tri;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_10 = L_9.___vb;
		float L_11 = L_10.___x;
		V_1 = ((float)il2cpp_codegen_multiply(L_8, L_11));
		UTriangle_tCD210F61D627BAB81A1CFFEC7076C3FBB9A6D7CD L_12 = ___0_tri;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_13 = L_12.___vc;
		float L_14 = L_13.___x;
		UTriangle_tCD210F61D627BAB81A1CFFEC7076C3FBB9A6D7CD L_15 = ___0_tri;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_16 = L_15.___vc;
		float L_17 = L_16.___x;
		V_2 = ((float)il2cpp_codegen_multiply(L_14, L_17));
		UTriangle_tCD210F61D627BAB81A1CFFEC7076C3FBB9A6D7CD L_18 = ___0_tri;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_19 = L_18.___va;
		float L_20 = L_19.___y;
		UTriangle_tCD210F61D627BAB81A1CFFEC7076C3FBB9A6D7CD L_21 = ___0_tri;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_22 = L_21.___va;
		float L_23 = L_22.___y;
		V_3 = ((float)il2cpp_codegen_multiply(L_20, L_23));
		UTriangle_tCD210F61D627BAB81A1CFFEC7076C3FBB9A6D7CD L_24 = ___0_tri;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_25 = L_24.___vb;
		float L_26 = L_25.___y;
		UTriangle_tCD210F61D627BAB81A1CFFEC7076C3FBB9A6D7CD L_27 = ___0_tri;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_28 = L_27.___vb;
		float L_29 = L_28.___y;
		V_4 = ((float)il2cpp_codegen_multiply(L_26, L_29));
		UTriangle_tCD210F61D627BAB81A1CFFEC7076C3FBB9A6D7CD L_30 = ___0_tri;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_31 = L_30.___vc;
		float L_32 = L_31.___y;
		UTriangle_tCD210F61D627BAB81A1CFFEC7076C3FBB9A6D7CD L_33 = ___0_tri;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_34 = L_33.___vc;
		float L_35 = L_34.___y;
		V_5 = ((float)il2cpp_codegen_multiply(L_32, L_35));
		UTriangle_tCD210F61D627BAB81A1CFFEC7076C3FBB9A6D7CD L_36 = ___0_tri;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_37 = L_36.___vb;
		float L_38 = L_37.___x;
		UTriangle_tCD210F61D627BAB81A1CFFEC7076C3FBB9A6D7CD L_39 = ___0_tri;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_40 = L_39.___va;
		float L_41 = L_40.___x;
		UTriangle_tCD210F61D627BAB81A1CFFEC7076C3FBB9A6D7CD L_42 = ___0_tri;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_43 = L_42.___vc;
		float L_44 = L_43.___y;
		UTriangle_tCD210F61D627BAB81A1CFFEC7076C3FBB9A6D7CD L_45 = ___0_tri;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_46 = L_45.___va;
		float L_47 = L_46.___y;
		UTriangle_tCD210F61D627BAB81A1CFFEC7076C3FBB9A6D7CD L_48 = ___0_tri;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_49 = L_48.___vb;
		float L_50 = L_49.___y;
		UTriangle_tCD210F61D627BAB81A1CFFEC7076C3FBB9A6D7CD L_51 = ___0_tri;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_52 = L_51.___va;
		float L_53 = L_52.___y;
		UTriangle_tCD210F61D627BAB81A1CFFEC7076C3FBB9A6D7CD L_54 = ___0_tri;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_55 = L_54.___vc;
		float L_56 = L_55.___x;
		UTriangle_tCD210F61D627BAB81A1CFFEC7076C3FBB9A6D7CD L_57 = ___0_tri;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_58 = L_57.___va;
		float L_59 = L_58.___x;
		V_6 = ((float)il2cpp_codegen_multiply((2.0f), ((float)il2cpp_codegen_subtract(((float)il2cpp_codegen_multiply(((float)il2cpp_codegen_subtract(L_38, L_41)), ((float)il2cpp_codegen_subtract(L_44, L_47)))), ((float)il2cpp_codegen_multiply(((float)il2cpp_codegen_subtract(L_50, L_53)), ((float)il2cpp_codegen_subtract(L_56, L_59))))))));
		UTriangle_tCD210F61D627BAB81A1CFFEC7076C3FBB9A6D7CD L_60 = ___0_tri;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_61 = L_60.___vc;
		float L_62 = L_61.___y;
		UTriangle_tCD210F61D627BAB81A1CFFEC7076C3FBB9A6D7CD L_63 = ___0_tri;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_64 = L_63.___va;
		float L_65 = L_64.___y;
		float L_66 = V_1;
		float L_67 = V_0;
		float L_68 = V_4;
		float L_69 = V_3;
		UTriangle_tCD210F61D627BAB81A1CFFEC7076C3FBB9A6D7CD L_70 = ___0_tri;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_71 = L_70.___va;
		float L_72 = L_71.___y;
		UTriangle_tCD210F61D627BAB81A1CFFEC7076C3FBB9A6D7CD L_73 = ___0_tri;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_74 = L_73.___vb;
		float L_75 = L_74.___y;
		float L_76 = V_2;
		float L_77 = V_0;
		float L_78 = V_5;
		float L_79 = V_3;
		float L_80 = V_6;
		V_7 = ((float)(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(((float)il2cpp_codegen_subtract(L_62, L_65)), ((float)il2cpp_codegen_subtract(((float)il2cpp_codegen_add(((float)il2cpp_codegen_subtract(L_66, L_67)), L_68)), L_69)))), ((float)il2cpp_codegen_multiply(((float)il2cpp_codegen_subtract(L_72, L_75)), ((float)il2cpp_codegen_subtract(((float)il2cpp_codegen_add(((float)il2cpp_codegen_subtract(L_76, L_77)), L_78)), L_79))))))/L_80));
		UTriangle_tCD210F61D627BAB81A1CFFEC7076C3FBB9A6D7CD L_81 = ___0_tri;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_82 = L_81.___va;
		float L_83 = L_82.___x;
		UTriangle_tCD210F61D627BAB81A1CFFEC7076C3FBB9A6D7CD L_84 = ___0_tri;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_85 = L_84.___vc;
		float L_86 = L_85.___x;
		float L_87 = V_1;
		float L_88 = V_0;
		float L_89 = V_4;
		float L_90 = V_3;
		UTriangle_tCD210F61D627BAB81A1CFFEC7076C3FBB9A6D7CD L_91 = ___0_tri;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_92 = L_91.___vb;
		float L_93 = L_92.___x;
		UTriangle_tCD210F61D627BAB81A1CFFEC7076C3FBB9A6D7CD L_94 = ___0_tri;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_95 = L_94.___va;
		float L_96 = L_95.___x;
		float L_97 = V_2;
		float L_98 = V_0;
		float L_99 = V_5;
		float L_100 = V_3;
		float L_101 = V_6;
		V_8 = ((float)(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(((float)il2cpp_codegen_subtract(L_83, L_86)), ((float)il2cpp_codegen_subtract(((float)il2cpp_codegen_add(((float)il2cpp_codegen_subtract(L_87, L_88)), L_89)), L_90)))), ((float)il2cpp_codegen_multiply(((float)il2cpp_codegen_subtract(L_93, L_96)), ((float)il2cpp_codegen_subtract(((float)il2cpp_codegen_add(((float)il2cpp_codegen_subtract(L_97, L_98)), L_99)), L_100))))))/L_101));
		UTriangle_tCD210F61D627BAB81A1CFFEC7076C3FBB9A6D7CD L_102 = ___0_tri;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_103 = L_102.___va;
		float L_104 = L_103.___x;
		float L_105 = V_7;
		V_9 = ((float)il2cpp_codegen_subtract(L_104, L_105));
		UTriangle_tCD210F61D627BAB81A1CFFEC7076C3FBB9A6D7CD L_106 = ___0_tri;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_107 = L_106.___va;
		float L_108 = L_107.___y;
		float L_109 = V_8;
		V_10 = ((float)il2cpp_codegen_subtract(L_108, L_109));
		il2cpp_codegen_initobj((&V_11), sizeof(UCircle_t****************************************));
		float L_110 = V_7;
		float L_111 = V_8;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_112;
		memset((&L_112), 0, sizeof(L_112));
		float2__ctor_m3D598E2C2D173DE852F3AB157502968261383C97_inline((&L_112), L_110, L_111, NULL);
		(&V_11)->___center = L_112;
		float L_113 = V_9;
		float L_114 = V_9;
		float L_115 = V_10;
		float L_116 = V_10;
		float L_117;
		L_117 = math_sqrt_mEF31DE7BD0179009683C5D7B0C58E6571B30CF4A_inline(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_113, L_114)), ((float)il2cpp_codegen_multiply(L_115, L_116)))), NULL);
		(&V_11)->___radius = L_117;
		UCircle_t**************************************** L_118 = V_11;
		return L_118;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool ModuleHandle_IsInsideCircle_m**************************************** (UCircle_t**************************************** ___0_c, float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA ___1_v, const RuntimeMethod* method) 
{
	{
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_0 = ___1_v;
		UCircle_t**************************************** L_1 = ___0_c;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_2 = L_1.___center;
		float L_3;
		L_3 = math_distance_mE5E0FFDD103E710A4CB23360BFCAFD0AF2E1EFA9_inline(L_0, L_2, NULL);
		UCircle_t**************************************** L_4 = ___0_c;
		float L_5 = L_4.___radius;
		return (bool)((((float)L_3) < ((float)L_5))? 1 : 0);
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float ModuleHandle_TriangleArea_m40B327CC9176416944F14D245D0F49DD5CB2CEA0 (float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA ___0_va, float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA ___1_vb, float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA ___2_vc, const RuntimeMethod* method) 
{
	float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E V_0;
	memset((&V_0), 0, sizeof(V_0));
	float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E V_1;
	memset((&V_1), 0, sizeof(V_1));
	float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E V_2;
	memset((&V_2), 0, sizeof(V_2));
	{
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_0 = ___0_va;
		float L_1 = L_0.___x;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_2 = ___0_va;
		float L_3 = L_2.___y;
		float3__ctor_mC61002CD0EC13D7C37D846D021A78C028FB80DB9_inline((&V_0), L_1, L_3, (0.0f), NULL);
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_4 = ___1_vb;
		float L_5 = L_4.___x;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_6 = ___1_vb;
		float L_7 = L_6.___y;
		float3__ctor_mC61002CD0EC13D7C37D846D021A78C028FB80DB9_inline((&V_1), L_5, L_7, (0.0f), NULL);
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_8 = ___2_vc;
		float L_9 = L_8.___x;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_10 = ___2_vc;
		float L_11 = L_10.___y;
		float3__ctor_mC61002CD0EC13D7C37D846D021A78C028FB80DB9_inline((&V_2), L_9, L_11, (0.0f), NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_12 = V_0;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_13 = V_1;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_14;
		L_14 = float3_op_Subtraction_mB6036E9849D95650D6E73DA0D179CD7B61E696F2_inline(L_12, L_13, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_15 = V_0;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_16 = V_2;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_17;
		L_17 = float3_op_Subtraction_mB6036E9849D95650D6E73DA0D179CD7B61E696F2_inline(L_15, L_16, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_18;
		L_18 = math_cross_m4CA2DAE150C6381B0D05E8AA9E48E88CF6157180_inline(L_14, L_17, NULL);
		float L_19 = L_18.___z;
		float L_20;
		L_20 = math_abs_m3D9508B36B045BFE7B89C6C69AD34596264E4FE1_inline(L_19, NULL);
		return ((float)il2cpp_codegen_multiply(L_20, (0.5f)));
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float ModuleHandle_Sign_mAD4C03A02763F90C0B6BB07F6A9A11DC00B59294 (float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA ___0_p1, float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA ___1_p2, float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA ___2_p3, const RuntimeMethod* method) 
{
	{
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_0 = ___0_p1;
		float L_1 = L_0.___x;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_2 = ___2_p3;
		float L_3 = L_2.___x;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_4 = ___1_p2;
		float L_5 = L_4.___y;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_6 = ___2_p3;
		float L_7 = L_6.___y;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_8 = ___1_p2;
		float L_9 = L_8.___x;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_10 = ___2_p3;
		float L_11 = L_10.___x;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_12 = ___0_p1;
		float L_13 = L_12.___y;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_14 = ___2_p3;
		float L_15 = L_14.___y;
		return ((float)il2cpp_codegen_subtract(((float)il2cpp_codegen_multiply(((float)il2cpp_codegen_subtract(L_1, L_3)), ((float)il2cpp_codegen_subtract(L_5, L_7)))), ((float)il2cpp_codegen_multiply(((float)il2cpp_codegen_subtract(L_9, L_11)), ((float)il2cpp_codegen_subtract(L_13, L_15))))));
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool ModuleHandle_IsInsideTriangle_m014222929EBBBFF8B3FCF265D1AB1302CE8DD9B5 (float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA ___0_pt, float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA ___1_v1, float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA ___2_v2, float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA ___3_v3, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ModuleHandle_tF45CBC10412074A0AAEF63CBDAA5F92A4F7A9978_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	float V_0 = 0.0f;
	float V_1 = 0.0f;
	bool V_2 = false;
	bool V_3 = false;
	float G_B3_0 = 0.0f;
	float G_B1_0 = 0.0f;
	float G_B2_0 = 0.0f;
	int32_t G_B4_0 = 0;
	float G_B4_1 = 0.0f;
	int32_t G_B8_0 = 0;
	{
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_0 = ___0_pt;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_1 = ___1_v1;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_2 = ___2_v2;
		il2cpp_codegen_runtime_class_init_inline(ModuleHandle_tF45CBC10412074A0AAEF63CBDAA5F92A4F7A9978_il2cpp_TypeInfo_var);
		float L_3;
		L_3 = ModuleHandle_Sign_mAD4C03A02763F90C0B6BB07F6A9A11DC00B59294(L_0, L_1, L_2, NULL);
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_4 = ___0_pt;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_5 = ___2_v2;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_6 = ___3_v3;
		float L_7;
		L_7 = ModuleHandle_Sign_mAD4C03A02763F90C0B6BB07F6A9A11DC00B59294(L_4, L_5, L_6, NULL);
		V_0 = L_7;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_8 = ___0_pt;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_9 = ___3_v3;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_10 = ___1_v1;
		float L_11;
		L_11 = ModuleHandle_Sign_mAD4C03A02763F90C0B6BB07F6A9A11DC00B59294(L_8, L_9, L_10, NULL);
		V_1 = L_11;
		float L_12 = L_3;
		if ((((float)L_12) < ((float)(0.0f))))
		{
			G_B3_0 = L_12;
			goto IL_0034;
		}
		G_B1_0 = L_12;
	}
	{
		float L_13 = V_0;
		if ((((float)L_13) < ((float)(0.0f))))
		{
			G_B3_0 = G_B1_0;
			goto IL_0034;
		}
		G_B2_0 = G_B1_0;
	}
	{
		float L_14 = V_1;
		G_B4_0 = ((((float)L_14) < ((float)(0.0f)))? 1 : 0);
		G_B4_1 = G_B2_0;
		goto IL_0035;
	}

IL_0034:
	{
		G_B4_0 = 1;
		G_B4_1 = G_B3_0;
	}

IL_0035:
	{
		V_2 = (bool)G_B4_0;
		if ((((float)G_B4_1) > ((float)(0.0f))))
		{
			goto IL_004f;
		}
	}
	{
		float L_15 = V_0;
		if ((((float)L_15) > ((float)(0.0f))))
		{
			goto IL_004f;
		}
	}
	{
		float L_16 = V_1;
		G_B8_0 = ((((float)L_16) > ((float)(0.0f)))? 1 : 0);
		goto IL_0050;
	}

IL_004f:
	{
		G_B8_0 = 1;
	}

IL_0050:
	{
		V_3 = (bool)G_B8_0;
		bool L_17 = V_2;
		bool L_18 = V_3;
		return (bool)((((int32_t)((int32_t)((int32_t)L_17&(int32_t)L_18))) == ((int32_t)0))? 1 : 0);
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool ModuleHandle_IsInsideTriangleApproximate_mF5C0D5E914AA32D2FE47DFD536A53DC7E3D741A0 (float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA ___0_pt, float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA ___1_v1, float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA ___2_v2, float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA ___3_v3, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ModuleHandle_tF45CBC10412074A0AAEF63CBDAA5F92A4F7A9978_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	float V_0 = 0.0f;
	float V_1 = 0.0f;
	float V_2 = 0.0f;
	float V_3 = 0.0f;
	{
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_0 = ___1_v1;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_1 = ___2_v2;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_2 = ___3_v3;
		il2cpp_codegen_runtime_class_init_inline(ModuleHandle_tF45CBC10412074A0AAEF63CBDAA5F92A4F7A9978_il2cpp_TypeInfo_var);
		float L_3;
		L_3 = ModuleHandle_TriangleArea_m40B327CC9176416944F14D245D0F49DD5CB2CEA0(L_0, L_1, L_2, NULL);
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_4 = ___0_pt;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_5 = ___1_v1;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_6 = ___2_v2;
		float L_7;
		L_7 = ModuleHandle_TriangleArea_m40B327CC9176416944F14D245D0F49DD5CB2CEA0(L_4, L_5, L_6, NULL);
		V_0 = L_7;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_8 = ___0_pt;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_9 = ___2_v2;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_10 = ___3_v3;
		float L_11;
		L_11 = ModuleHandle_TriangleArea_m40B327CC9176416944F14D245D0F49DD5CB2CEA0(L_8, L_9, L_10, NULL);
		V_1 = L_11;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_12 = ___0_pt;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_13 = ___3_v3;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_14 = ___1_v1;
		float L_15;
		L_15 = ModuleHandle_TriangleArea_m40B327CC9176416944F14D245D0F49DD5CB2CEA0(L_12, L_13, L_14, NULL);
		V_2 = L_15;
		V_3 = (1.11022302E-16f);
		float L_16 = V_0;
		float L_17 = V_1;
		float L_18 = V_2;
		float L_19;
		L_19 = fabsf(((float)il2cpp_codegen_subtract(L_3, ((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(L_16, L_17)), L_18)))));
		float L_20 = V_3;
		return (bool)((((float)L_19) < ((float)L_20))? 1 : 0);
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool ModuleHandle_IsInsideCircle_m**************************************** (float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA ___0_a, float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA ___1_b, float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA ___2_c, float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA ___3_p, const RuntimeMethod* method) 
{
	float V_0 = 0.0f;
	float V_1 = 0.0f;
	float V_2 = 0.0f;
	float V_3 = 0.0f;
	float V_4 = 0.0f;
	float V_5 = 0.0f;
	float V_6 = 0.0f;
	float V_7 = 0.0f;
	float V_8 = 0.0f;
	float V_9 = 0.0f;
	float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA V_10;
	memset((&V_10), 0, sizeof(V_10));
	float V_11 = 0.0f;
	{
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_0 = ___0_a;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_1 = ___0_a;
		float L_2;
		L_2 = math_dot_mF673D3E5B7D267C0A8569B678D05BDCCB667D04D_inline(L_0, L_1, NULL);
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_3 = ___1_b;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_4 = ___1_b;
		float L_5;
		L_5 = math_dot_mF673D3E5B7D267C0A8569B678D05BDCCB667D04D_inline(L_3, L_4, NULL);
		V_0 = L_5;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_6 = ___2_c;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_7 = ___2_c;
		float L_8;
		L_8 = math_dot_mF673D3E5B7D267C0A8569B678D05BDCCB667D04D_inline(L_6, L_7, NULL);
		V_1 = L_8;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_9 = ___0_a;
		float L_10 = L_9.___x;
		V_2 = L_10;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_11 = ___0_a;
		float L_12 = L_11.___y;
		V_3 = L_12;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_13 = ___1_b;
		float L_14 = L_13.___x;
		V_4 = L_14;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_15 = ___1_b;
		float L_16 = L_15.___y;
		V_5 = L_16;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_17 = ___2_c;
		float L_18 = L_17.___x;
		V_6 = L_18;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_19 = ___2_c;
		float L_20 = L_19.___y;
		V_7 = L_20;
		float L_21 = L_2;
		float L_22 = V_7;
		float L_23 = V_5;
		float L_24 = V_0;
		float L_25 = V_3;
		float L_26 = V_7;
		float L_27 = V_1;
		float L_28 = V_5;
		float L_29 = V_3;
		float L_30 = V_2;
		float L_31 = V_7;
		float L_32 = V_5;
		float L_33 = V_4;
		float L_34 = V_3;
		float L_35 = V_7;
		float L_36 = V_6;
		float L_37 = V_5;
		float L_38 = V_3;
		V_8 = ((float)(((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_21, ((float)il2cpp_codegen_subtract(L_22, L_23)))), ((float)il2cpp_codegen_multiply(L_24, ((float)il2cpp_codegen_subtract(L_25, L_26)))))), ((float)il2cpp_codegen_multiply(L_27, ((float)il2cpp_codegen_subtract(L_28, L_29))))))/((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_30, ((float)il2cpp_codegen_subtract(L_31, L_32)))), ((float)il2cpp_codegen_multiply(L_33, ((float)il2cpp_codegen_subtract(L_34, L_35)))))), ((float)il2cpp_codegen_multiply(L_36, ((float)il2cpp_codegen_subtract(L_37, L_38))))))));
		float L_39 = V_6;
		float L_40 = V_4;
		float L_41 = V_0;
		float L_42 = V_2;
		float L_43 = V_6;
		float L_44 = V_1;
		float L_45 = V_4;
		float L_46 = V_2;
		float L_47 = V_3;
		float L_48 = V_6;
		float L_49 = V_4;
		float L_50 = V_5;
		float L_51 = V_2;
		float L_52 = V_6;
		float L_53 = V_7;
		float L_54 = V_4;
		float L_55 = V_2;
		V_9 = ((float)(((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_21, ((float)il2cpp_codegen_subtract(L_39, L_40)))), ((float)il2cpp_codegen_multiply(L_41, ((float)il2cpp_codegen_subtract(L_42, L_43)))))), ((float)il2cpp_codegen_multiply(L_44, ((float)il2cpp_codegen_subtract(L_45, L_46))))))/((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_47, ((float)il2cpp_codegen_subtract(L_48, L_49)))), ((float)il2cpp_codegen_multiply(L_50, ((float)il2cpp_codegen_subtract(L_51, L_52)))))), ((float)il2cpp_codegen_multiply(L_53, ((float)il2cpp_codegen_subtract(L_54, L_55))))))));
		il2cpp_codegen_initobj((&V_10), sizeof(float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA));
		float L_56 = V_8;
		(&V_10)->___x = ((float)(L_56/(2.0f)));
		float L_57 = V_9;
		(&V_10)->___y = ((float)(L_57/(2.0f)));
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_58 = ___0_a;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_59 = V_10;
		float L_60;
		L_60 = math_distance_mE5E0FFDD103E710A4CB23360BFCAFD0AF2E1EFA9_inline(L_58, L_59, NULL);
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_61 = ___3_p;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_62 = V_10;
		float L_63;
		L_63 = math_distance_mE5E0FFDD103E710A4CB23360BFCAFD0AF2E1EFA9_inline(L_61, L_62, NULL);
		V_11 = L_63;
		float L_64 = V_11;
		return (bool)((((float)((float)il2cpp_codegen_subtract(L_60, L_64))) > ((float)(9.99999975E-06f)))? 1 : 0);
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ModuleHandle_BuildTriangles_m48C3ED6D6EEFF398B32541E7685F1332A0040BA6 (NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E ___0_vertices, int32_t ___1_vertexCount, NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C ___2_indices, int32_t ___3_indexCount, NativeArray_1_t949918E734BDA97DE9BC71119C34E3CC11B1F8D5* ___4_triangles, int32_t* ___5_triangleCount, float* ___6_maxArea, float* ___7_avgArea, float* ___8_minArea, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ModuleHandle_tF45CBC10412074A0AAEF63CBDAA5F92A4F7A9978_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	int32_t V_0 = 0;
	UTriangle_tCD210F61D627BAB81A1CFFEC7076C3FBB9A6D7CD V_1;
	memset((&V_1), 0, sizeof(V_1));
	int32_t V_2 = 0;
	int32_t V_3 = 0;
	int32_t V_4 = 0;
	int32_t V_5 = 0;
	{
		V_0 = 0;
		goto IL_00d1;
	}

IL_0007:
	{
		il2cpp_codegen_initobj((&V_1), sizeof(UTriangle_tCD210F61D627BAB81A1CFFEC7076C3FBB9A6D7CD));
		int32_t L_0 = V_0;
		int32_t L_1;
		L_1 = IL2CPP_NATIVEARRAY_GET_ITEM(int32_t, ((&___2_indices))->___m_Buffer, L_0);
		V_2 = L_1;
		int32_t L_2 = V_0;
		int32_t L_3;
		L_3 = IL2CPP_NATIVEARRAY_GET_ITEM(int32_t, ((&___2_indices))->___m_Buffer, ((int32_t)il2cpp_codegen_add(L_2, 1)));
		V_3 = L_3;
		int32_t L_4 = V_0;
		int32_t L_5;
		L_5 = IL2CPP_NATIVEARRAY_GET_ITEM(int32_t, ((&___2_indices))->___m_Buffer, ((int32_t)il2cpp_codegen_add(L_4, 2)));
		V_4 = L_5;
		int32_t L_6 = V_2;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_7;
		L_7 = IL2CPP_NATIVEARRAY_GET_ITEM(float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA, ((&___0_vertices))->___m_Buffer, L_6);
		(&V_1)->___va = L_7;
		int32_t L_8 = V_3;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_9;
		L_9 = IL2CPP_NATIVEARRAY_GET_ITEM(float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA, ((&___0_vertices))->___m_Buffer, L_8);
		(&V_1)->___vb = L_9;
		int32_t L_10 = V_4;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_11;
		L_11 = IL2CPP_NATIVEARRAY_GET_ITEM(float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA, ((&___0_vertices))->___m_Buffer, L_10);
		(&V_1)->___vc = L_11;
		UTriangle_tCD210F61D627BAB81A1CFFEC7076C3FBB9A6D7CD L_12 = V_1;
		il2cpp_codegen_runtime_class_init_inline(ModuleHandle_tF45CBC10412074A0AAEF63CBDAA5F92A4F7A9978_il2cpp_TypeInfo_var);
		UCircle_t**************************************** L_13;
		L_13 = ModuleHandle_CircumCircle_m****************************************(L_12, NULL);
		(&V_1)->___c = L_13;
		UTriangle_tCD210F61D627BAB81A1CFFEC7076C3FBB9A6D7CD L_14 = V_1;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_15 = L_14.___va;
		UTriangle_tCD210F61D627BAB81A1CFFEC7076C3FBB9A6D7CD L_16 = V_1;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_17 = L_16.___vb;
		UTriangle_tCD210F61D627BAB81A1CFFEC7076C3FBB9A6D7CD L_18 = V_1;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_19 = L_18.___vc;
		float L_20;
		L_20 = ModuleHandle_TriangleArea_m40B327CC9176416944F14D245D0F49DD5CB2CEA0(L_15, L_17, L_19, NULL);
		(&V_1)->___area = L_20;
		float* L_21 = ___6_maxArea;
		UTriangle_tCD210F61D627BAB81A1CFFEC7076C3FBB9A6D7CD L_22 = V_1;
		float L_23 = L_22.___area;
		float* L_24 = ___6_maxArea;
		float L_25 = *((float*)L_24);
		float L_26;
		L_26 = math_max_m4B454A91AE8827997609E74C4C24036BBD3CC496_inline(L_23, L_25, NULL);
		*((float*)L_21) = (float)L_26;
		float* L_27 = ___8_minArea;
		UTriangle_tCD210F61D627BAB81A1CFFEC7076C3FBB9A6D7CD L_28 = V_1;
		float L_29 = L_28.___area;
		float* L_30 = ___8_minArea;
		float L_31 = *((float*)L_30);
		float L_32;
		L_32 = math_min_m54FD010BEF505D2BA1F79FC793BEB0723C329C3B_inline(L_29, L_31, NULL);
		*((float*)L_27) = (float)L_32;
		float* L_33 = ___7_avgArea;
		float* L_34 = ___7_avgArea;
		float L_35 = *((float*)L_34);
		UTriangle_tCD210F61D627BAB81A1CFFEC7076C3FBB9A6D7CD L_36 = V_1;
		float L_37 = L_36.___area;
		*((float*)L_33) = (float)((float)il2cpp_codegen_add(L_35, L_37));
		NativeArray_1_t949918E734BDA97DE9BC71119C34E3CC11B1F8D5* L_38 = ___4_triangles;
		int32_t* L_39 = ___5_triangleCount;
		int32_t* L_40 = ___5_triangleCount;
		int32_t L_41 = *((int32_t*)L_40);
		V_5 = L_41;
		int32_t L_42 = V_5;
		*((int32_t*)L_39) = (int32_t)((int32_t)il2cpp_codegen_add(L_42, 1));
		int32_t L_43 = V_5;
		UTriangle_tCD210F61D627BAB81A1CFFEC7076C3FBB9A6D7CD L_44 = V_1;
		IL2CPP_NATIVEARRAY_SET_ITEM(UTriangle_tCD210F61D627BAB81A1CFFEC7076C3FBB9A6D7CD, (L_38)->___m_Buffer, L_43, (L_44));
		int32_t L_45 = V_0;
		V_0 = ((int32_t)il2cpp_codegen_add(L_45, 3));
	}

IL_00d1:
	{
		int32_t L_46 = V_0;
		int32_t L_47 = ___3_indexCount;
		if ((((int32_t)L_46) < ((int32_t)L_47)))
		{
			goto IL_0007;
		}
	}
	{
		float* L_48 = ___7_avgArea;
		float* L_49 = ___7_avgArea;
		float L_50 = *((float*)L_49);
		int32_t* L_51 = ___5_triangleCount;
		int32_t L_52 = *((int32_t*)L_51);
		*((float*)L_48) = (float)((float)(L_50/((float)L_52)));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ModuleHandle_BuildTriangles_m2C767A1D0CBE89D726305D210EB2416DE1DA98CD (NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E ___0_vertices, int32_t ___1_vertexCount, NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C ___2_indices, int32_t ___3_indexCount, NativeArray_1_t949918E734BDA97DE9BC71119C34E3CC11B1F8D5* ___4_triangles, int32_t* ___5_triangleCount, float* ___6_maxArea, float* ___7_avgArea, float* ___8_minArea, float* ___9_maxEdge, float* ___10_avgEdge, float* ___11_minEdge, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ModuleHandle_tF45CBC10412074A0AAEF63CBDAA5F92A4F7A9978_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	int32_t V_0 = 0;
	UTriangle_tCD210F61D627BAB81A1CFFEC7076C3FBB9A6D7CD V_1;
	memset((&V_1), 0, sizeof(V_1));
	int32_t V_2 = 0;
	int32_t V_3 = 0;
	int32_t V_4 = 0;
	float V_5 = 0.0f;
	float V_6 = 0.0f;
	float V_7 = 0.0f;
	int32_t V_8 = 0;
	{
		V_0 = 0;
		goto IL_0173;
	}

IL_0007:
	{
		il2cpp_codegen_initobj((&V_1), sizeof(UTriangle_tCD210F61D627BAB81A1CFFEC7076C3FBB9A6D7CD));
		int32_t L_0 = V_0;
		int32_t L_1;
		L_1 = IL2CPP_NATIVEARRAY_GET_ITEM(int32_t, ((&___2_indices))->___m_Buffer, L_0);
		V_2 = L_1;
		int32_t L_2 = V_0;
		int32_t L_3;
		L_3 = IL2CPP_NATIVEARRAY_GET_ITEM(int32_t, ((&___2_indices))->___m_Buffer, ((int32_t)il2cpp_codegen_add(L_2, 1)));
		V_3 = L_3;
		int32_t L_4 = V_0;
		int32_t L_5;
		L_5 = IL2CPP_NATIVEARRAY_GET_ITEM(int32_t, ((&___2_indices))->___m_Buffer, ((int32_t)il2cpp_codegen_add(L_4, 2)));
		V_4 = L_5;
		int32_t L_6 = V_2;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_7;
		L_7 = IL2CPP_NATIVEARRAY_GET_ITEM(float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA, ((&___0_vertices))->___m_Buffer, L_6);
		(&V_1)->___va = L_7;
		int32_t L_8 = V_3;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_9;
		L_9 = IL2CPP_NATIVEARRAY_GET_ITEM(float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA, ((&___0_vertices))->___m_Buffer, L_8);
		(&V_1)->___vb = L_9;
		int32_t L_10 = V_4;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_11;
		L_11 = IL2CPP_NATIVEARRAY_GET_ITEM(float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA, ((&___0_vertices))->___m_Buffer, L_10);
		(&V_1)->___vc = L_11;
		UTriangle_tCD210F61D627BAB81A1CFFEC7076C3FBB9A6D7CD L_12 = V_1;
		il2cpp_codegen_runtime_class_init_inline(ModuleHandle_tF45CBC10412074A0AAEF63CBDAA5F92A4F7A9978_il2cpp_TypeInfo_var);
		UCircle_t**************************************** L_13;
		L_13 = ModuleHandle_CircumCircle_m****************************************(L_12, NULL);
		(&V_1)->___c = L_13;
		UTriangle_tCD210F61D627BAB81A1CFFEC7076C3FBB9A6D7CD L_14 = V_1;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_15 = L_14.___va;
		UTriangle_tCD210F61D627BAB81A1CFFEC7076C3FBB9A6D7CD L_16 = V_1;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_17 = L_16.___vb;
		UTriangle_tCD210F61D627BAB81A1CFFEC7076C3FBB9A6D7CD L_18 = V_1;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_19 = L_18.___vc;
		float L_20;
		L_20 = ModuleHandle_TriangleArea_m40B327CC9176416944F14D245D0F49DD5CB2CEA0(L_15, L_17, L_19, NULL);
		(&V_1)->___area = L_20;
		float* L_21 = ___6_maxArea;
		UTriangle_tCD210F61D627BAB81A1CFFEC7076C3FBB9A6D7CD L_22 = V_1;
		float L_23 = L_22.___area;
		float* L_24 = ___6_maxArea;
		float L_25 = *((float*)L_24);
		float L_26;
		L_26 = math_max_m4B454A91AE8827997609E74C4C24036BBD3CC496_inline(L_23, L_25, NULL);
		*((float*)L_21) = (float)L_26;
		float* L_27 = ___8_minArea;
		UTriangle_tCD210F61D627BAB81A1CFFEC7076C3FBB9A6D7CD L_28 = V_1;
		float L_29 = L_28.___area;
		float* L_30 = ___8_minArea;
		float L_31 = *((float*)L_30);
		float L_32;
		L_32 = math_min_m54FD010BEF505D2BA1F79FC793BEB0723C329C3B_inline(L_29, L_31, NULL);
		*((float*)L_27) = (float)L_32;
		float* L_33 = ___7_avgArea;
		float* L_34 = ___7_avgArea;
		float L_35 = *((float*)L_34);
		UTriangle_tCD210F61D627BAB81A1CFFEC7076C3FBB9A6D7CD L_36 = V_1;
		float L_37 = L_36.___area;
		*((float*)L_33) = (float)((float)il2cpp_codegen_add(L_35, L_37));
		UTriangle_tCD210F61D627BAB81A1CFFEC7076C3FBB9A6D7CD L_38 = V_1;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_39 = L_38.___va;
		UTriangle_tCD210F61D627BAB81A1CFFEC7076C3FBB9A6D7CD L_40 = V_1;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_41 = L_40.___vb;
		float L_42;
		L_42 = math_distance_mE5E0FFDD103E710A4CB23360BFCAFD0AF2E1EFA9_inline(L_39, L_41, NULL);
		V_5 = L_42;
		UTriangle_tCD210F61D627BAB81A1CFFEC7076C3FBB9A6D7CD L_43 = V_1;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_44 = L_43.___vb;
		UTriangle_tCD210F61D627BAB81A1CFFEC7076C3FBB9A6D7CD L_45 = V_1;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_46 = L_45.___vc;
		float L_47;
		L_47 = math_distance_mE5E0FFDD103E710A4CB23360BFCAFD0AF2E1EFA9_inline(L_44, L_46, NULL);
		V_6 = L_47;
		UTriangle_tCD210F61D627BAB81A1CFFEC7076C3FBB9A6D7CD L_48 = V_1;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_49 = L_48.___vc;
		UTriangle_tCD210F61D627BAB81A1CFFEC7076C3FBB9A6D7CD L_50 = V_1;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_51 = L_50.___va;
		float L_52;
		L_52 = math_distance_mE5E0FFDD103E710A4CB23360BFCAFD0AF2E1EFA9_inline(L_49, L_51, NULL);
		V_7 = L_52;
		float* L_53 = ___9_maxEdge;
		float L_54 = V_5;
		float* L_55 = ___9_maxEdge;
		float L_56 = *((float*)L_55);
		float L_57;
		L_57 = math_max_m4B454A91AE8827997609E74C4C24036BBD3CC496_inline(L_54, L_56, NULL);
		*((float*)L_53) = (float)L_57;
		float* L_58 = ___9_maxEdge;
		float L_59 = V_6;
		float* L_60 = ___9_maxEdge;
		float L_61 = *((float*)L_60);
		float L_62;
		L_62 = math_max_m4B454A91AE8827997609E74C4C24036BBD3CC496_inline(L_59, L_61, NULL);
		*((float*)L_58) = (float)L_62;
		float* L_63 = ___9_maxEdge;
		float L_64 = V_7;
		float* L_65 = ___9_maxEdge;
		float L_66 = *((float*)L_65);
		float L_67;
		L_67 = math_max_m4B454A91AE8827997609E74C4C24036BBD3CC496_inline(L_64, L_66, NULL);
		*((float*)L_63) = (float)L_67;
		float* L_68 = ___11_minEdge;
		float L_69 = V_5;
		float* L_70 = ___11_minEdge;
		float L_71 = *((float*)L_70);
		float L_72;
		L_72 = math_min_m54FD010BEF505D2BA1F79FC793BEB0723C329C3B_inline(L_69, L_71, NULL);
		*((float*)L_68) = (float)L_72;
		float* L_73 = ___11_minEdge;
		float L_74 = V_6;
		float* L_75 = ___11_minEdge;
		float L_76 = *((float*)L_75);
		float L_77;
		L_77 = math_min_m54FD010BEF505D2BA1F79FC793BEB0723C329C3B_inline(L_74, L_76, NULL);
		*((float*)L_73) = (float)L_77;
		float* L_78 = ___11_minEdge;
		float L_79 = V_7;
		float* L_80 = ___11_minEdge;
		float L_81 = *((float*)L_80);
		float L_82;
		L_82 = math_min_m54FD010BEF505D2BA1F79FC793BEB0723C329C3B_inline(L_79, L_81, NULL);
		*((float*)L_78) = (float)L_82;
		float* L_83 = ___10_avgEdge;
		float* L_84 = ___10_avgEdge;
		float L_85 = *((float*)L_84);
		float L_86 = V_5;
		*((float*)L_83) = (float)((float)il2cpp_codegen_add(L_85, L_86));
		float* L_87 = ___10_avgEdge;
		float* L_88 = ___10_avgEdge;
		float L_89 = *((float*)L_88);
		float L_90 = V_6;
		*((float*)L_87) = (float)((float)il2cpp_codegen_add(L_89, L_90));
		float* L_91 = ___10_avgEdge;
		float* L_92 = ___10_avgEdge;
		float L_93 = *((float*)L_92);
		float L_94 = V_7;
		*((float*)L_91) = (float)((float)il2cpp_codegen_add(L_93, L_94));
		NativeArray_1_t949918E734BDA97DE9BC71119C34E3CC11B1F8D5* L_95 = ___4_triangles;
		int32_t* L_96 = ___5_triangleCount;
		int32_t* L_97 = ___5_triangleCount;
		int32_t L_98 = *((int32_t*)L_97);
		V_8 = L_98;
		int32_t L_99 = V_8;
		*((int32_t*)L_96) = (int32_t)((int32_t)il2cpp_codegen_add(L_99, 1));
		int32_t L_100 = V_8;
		UTriangle_tCD210F61D627BAB81A1CFFEC7076C3FBB9A6D7CD L_101 = V_1;
		IL2CPP_NATIVEARRAY_SET_ITEM(UTriangle_tCD210F61D627BAB81A1CFFEC7076C3FBB9A6D7CD, (L_95)->___m_Buffer, L_100, (L_101));
		int32_t L_102 = V_0;
		V_0 = ((int32_t)il2cpp_codegen_add(L_102, 3));
	}

IL_0173:
	{
		int32_t L_103 = V_0;
		int32_t L_104 = ___3_indexCount;
		if ((((int32_t)L_103) < ((int32_t)L_104)))
		{
			goto IL_0007;
		}
	}
	{
		float* L_105 = ___7_avgArea;
		float* L_106 = ___7_avgArea;
		float L_107 = *((float*)L_106);
		int32_t* L_108 = ___5_triangleCount;
		int32_t L_109 = *((int32_t*)L_108);
		*((float*)L_105) = (float)((float)(L_107/((float)L_109)));
		float* L_110 = ___10_avgEdge;
		float* L_111 = ___10_avgEdge;
		float L_112 = *((float*)L_111);
		int32_t L_113 = ___3_indexCount;
		*((float*)L_110) = (float)((float)(L_112/((float)L_113)));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ModuleHandle_BuildTrianglesAndEdges_m902E978EB29E4C37FCA0B80D66E30B677CADDD31 (NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E ___0_vertices, int32_t ___1_vertexCount, NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C ___2_indices, int32_t ___3_indexCount, NativeArray_1_t949918E734BDA97DE9BC71119C34E3CC11B1F8D5* ___4_triangles, int32_t* ___5_triangleCount, NativeArray_1_tBCDB44165F65D6BEE48CAD34C04286D158C1A200* ___6_delaEdges, int32_t* ___7_delaEdgeCount, float* ___8_maxArea, float* ___9_avgArea, float* ___10_minArea, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ModuleHandle_tF45CBC10412074A0AAEF63CBDAA5F92A4F7A9978_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	int32_t V_0 = 0;
	UTriangle_tCD210F61D627BAB81A1CFFEC7076C3FBB9A6D7CD V_1;
	memset((&V_1), 0, sizeof(V_1));
	int32_t V_2 = 0;
	int32_t V_3 = 0;
	int32_t V_4 = 0;
	int32_t V_5 = 0;
	{
		V_0 = 0;
		goto IL_0169;
	}

IL_0007:
	{
		il2cpp_codegen_initobj((&V_1), sizeof(UTriangle_tCD210F61D627BAB81A1CFFEC7076C3FBB9A6D7CD));
		int32_t L_0 = V_0;
		int32_t L_1;
		L_1 = IL2CPP_NATIVEARRAY_GET_ITEM(int32_t, ((&___2_indices))->___m_Buffer, L_0);
		V_2 = L_1;
		int32_t L_2 = V_0;
		int32_t L_3;
		L_3 = IL2CPP_NATIVEARRAY_GET_ITEM(int32_t, ((&___2_indices))->___m_Buffer, ((int32_t)il2cpp_codegen_add(L_2, 1)));
		V_3 = L_3;
		int32_t L_4 = V_0;
		int32_t L_5;
		L_5 = IL2CPP_NATIVEARRAY_GET_ITEM(int32_t, ((&___2_indices))->___m_Buffer, ((int32_t)il2cpp_codegen_add(L_4, 2)));
		V_4 = L_5;
		int32_t L_6 = V_2;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_7;
		L_7 = IL2CPP_NATIVEARRAY_GET_ITEM(float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA, ((&___0_vertices))->___m_Buffer, L_6);
		(&V_1)->___va = L_7;
		int32_t L_8 = V_3;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_9;
		L_9 = IL2CPP_NATIVEARRAY_GET_ITEM(float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA, ((&___0_vertices))->___m_Buffer, L_8);
		(&V_1)->___vb = L_9;
		int32_t L_10 = V_4;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_11;
		L_11 = IL2CPP_NATIVEARRAY_GET_ITEM(float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA, ((&___0_vertices))->___m_Buffer, L_10);
		(&V_1)->___vc = L_11;
		UTriangle_tCD210F61D627BAB81A1CFFEC7076C3FBB9A6D7CD L_12 = V_1;
		il2cpp_codegen_runtime_class_init_inline(ModuleHandle_tF45CBC10412074A0AAEF63CBDAA5F92A4F7A9978_il2cpp_TypeInfo_var);
		UCircle_t**************************************** L_13;
		L_13 = ModuleHandle_CircumCircle_m****************************************(L_12, NULL);
		(&V_1)->___c = L_13;
		UTriangle_tCD210F61D627BAB81A1CFFEC7076C3FBB9A6D7CD L_14 = V_1;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_15 = L_14.___va;
		UTriangle_tCD210F61D627BAB81A1CFFEC7076C3FBB9A6D7CD L_16 = V_1;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_17 = L_16.___vb;
		UTriangle_tCD210F61D627BAB81A1CFFEC7076C3FBB9A6D7CD L_18 = V_1;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_19 = L_18.___vc;
		float L_20;
		L_20 = ModuleHandle_TriangleArea_m40B327CC9176416944F14D245D0F49DD5CB2CEA0(L_15, L_17, L_19, NULL);
		(&V_1)->___area = L_20;
		float* L_21 = ___8_maxArea;
		UTriangle_tCD210F61D627BAB81A1CFFEC7076C3FBB9A6D7CD L_22 = V_1;
		float L_23 = L_22.___area;
		float* L_24 = ___8_maxArea;
		float L_25 = *((float*)L_24);
		float L_26;
		L_26 = math_max_m4B454A91AE8827997609E74C4C24036BBD3CC496_inline(L_23, L_25, NULL);
		*((float*)L_21) = (float)L_26;
		float* L_27 = ___10_minArea;
		UTriangle_tCD210F61D627BAB81A1CFFEC7076C3FBB9A6D7CD L_28 = V_1;
		float L_29 = L_28.___area;
		float* L_30 = ___10_minArea;
		float L_31 = *((float*)L_30);
		float L_32;
		L_32 = math_min_m54FD010BEF505D2BA1F79FC793BEB0723C329C3B_inline(L_29, L_31, NULL);
		*((float*)L_27) = (float)L_32;
		float* L_33 = ___9_avgArea;
		float* L_34 = ___9_avgArea;
		float L_35 = *((float*)L_34);
		UTriangle_tCD210F61D627BAB81A1CFFEC7076C3FBB9A6D7CD L_36 = V_1;
		float L_37 = L_36.___area;
		*((float*)L_33) = (float)((float)il2cpp_codegen_add(L_35, L_37));
		int32_t L_38 = V_2;
		int32_t L_39 = V_3;
		int32_t L_40 = V_4;
		int3_t1D01D28AA6D32890A228297EBADD9BB1A960E2BF L_41;
		memset((&L_41), 0, sizeof(L_41));
		int3__ctor_mE478318DE4CA648614FEF2C1DD438C0455284BF2_inline((&L_41), L_38, L_39, L_40, NULL);
		(&V_1)->___indices = L_41;
		NativeArray_1_tBCDB44165F65D6BEE48CAD34C04286D158C1A200* L_42 = ___6_delaEdges;
		int32_t* L_43 = ___7_delaEdgeCount;
		int32_t* L_44 = ___7_delaEdgeCount;
		int32_t L_45 = *((int32_t*)L_44);
		V_5 = L_45;
		int32_t L_46 = V_5;
		*((int32_t*)L_43) = (int32_t)((int32_t)il2cpp_codegen_add(L_46, 1));
		int32_t L_47 = V_5;
		int32_t L_48 = V_2;
		int32_t L_49 = V_3;
		int32_t L_50;
		L_50 = math_min_m02D43DF516544C279AF660EA4731449C82991849_inline(L_48, L_49, NULL);
		int32_t L_51 = V_2;
		int32_t L_52 = V_3;
		int32_t L_53;
		L_53 = math_max_m9083201D37A8ED0157B127B5878D9B7F3A2A40BE_inline(L_51, L_52, NULL);
		int32_t* L_54 = ___5_triangleCount;
		int32_t L_55 = *((int32_t*)L_54);
		int4_tBA77D4945786DE82C3A487B33955EA1004996052 L_56;
		memset((&L_56), 0, sizeof(L_56));
		int4__ctor_m4E8D71A09721E26F7FCCE82EA8AD699062EE6216_inline((&L_56), L_50, L_53, L_55, (-1), NULL);
		IL2CPP_NATIVEARRAY_SET_ITEM(int4_tBA77D4945786DE82C3A487B33955EA1004996052, (L_42)->___m_Buffer, L_47, (L_56));
		NativeArray_1_tBCDB44165F65D6BEE48CAD34C04286D158C1A200* L_57 = ___6_delaEdges;
		int32_t* L_58 = ___7_delaEdgeCount;
		int32_t* L_59 = ___7_delaEdgeCount;
		int32_t L_60 = *((int32_t*)L_59);
		V_5 = L_60;
		int32_t L_61 = V_5;
		*((int32_t*)L_58) = (int32_t)((int32_t)il2cpp_codegen_add(L_61, 1));
		int32_t L_62 = V_5;
		int32_t L_63 = V_3;
		int32_t L_64 = V_4;
		int32_t L_65;
		L_65 = math_min_m02D43DF516544C279AF660EA4731449C82991849_inline(L_63, L_64, NULL);
		int32_t L_66 = V_3;
		int32_t L_67 = V_4;
		int32_t L_68;
		L_68 = math_max_m9083201D37A8ED0157B127B5878D9B7F3A2A40BE_inline(L_66, L_67, NULL);
		int32_t* L_69 = ___5_triangleCount;
		int32_t L_70 = *((int32_t*)L_69);
		int4_tBA77D4945786DE82C3A487B33955EA1004996052 L_71;
		memset((&L_71), 0, sizeof(L_71));
		int4__ctor_m4E8D71A09721E26F7FCCE82EA8AD699062EE6216_inline((&L_71), L_65, L_68, L_70, (-1), NULL);
		IL2CPP_NATIVEARRAY_SET_ITEM(int4_tBA77D4945786DE82C3A487B33955EA1004996052, (L_57)->___m_Buffer, L_62, (L_71));
		NativeArray_1_tBCDB44165F65D6BEE48CAD34C04286D158C1A200* L_72 = ___6_delaEdges;
		int32_t* L_73 = ___7_delaEdgeCount;
		int32_t* L_74 = ___7_delaEdgeCount;
		int32_t L_75 = *((int32_t*)L_74);
		V_5 = L_75;
		int32_t L_76 = V_5;
		*((int32_t*)L_73) = (int32_t)((int32_t)il2cpp_codegen_add(L_76, 1));
		int32_t L_77 = V_5;
		int32_t L_78 = V_4;
		int32_t L_79 = V_2;
		int32_t L_80;
		L_80 = math_min_m02D43DF516544C279AF660EA4731449C82991849_inline(L_78, L_79, NULL);
		int32_t L_81 = V_4;
		int32_t L_82 = V_2;
		int32_t L_83;
		L_83 = math_max_m9083201D37A8ED0157B127B5878D9B7F3A2A40BE_inline(L_81, L_82, NULL);
		int32_t* L_84 = ___5_triangleCount;
		int32_t L_85 = *((int32_t*)L_84);
		int4_tBA77D4945786DE82C3A487B33955EA1004996052 L_86;
		memset((&L_86), 0, sizeof(L_86));
		int4__ctor_m4E8D71A09721E26F7FCCE82EA8AD699062EE6216_inline((&L_86), L_80, L_83, L_85, (-1), NULL);
		IL2CPP_NATIVEARRAY_SET_ITEM(int4_tBA77D4945786DE82C3A487B33955EA1004996052, (L_72)->___m_Buffer, L_77, (L_86));
		NativeArray_1_t949918E734BDA97DE9BC71119C34E3CC11B1F8D5* L_87 = ___4_triangles;
		int32_t* L_88 = ___5_triangleCount;
		int32_t* L_89 = ___5_triangleCount;
		int32_t L_90 = *((int32_t*)L_89);
		V_5 = L_90;
		int32_t L_91 = V_5;
		*((int32_t*)L_88) = (int32_t)((int32_t)il2cpp_codegen_add(L_91, 1));
		int32_t L_92 = V_5;
		UTriangle_tCD210F61D627BAB81A1CFFEC7076C3FBB9A6D7CD L_93 = V_1;
		IL2CPP_NATIVEARRAY_SET_ITEM(UTriangle_tCD210F61D627BAB81A1CFFEC7076C3FBB9A6D7CD, (L_87)->___m_Buffer, L_92, (L_93));
		int32_t L_94 = V_0;
		V_0 = ((int32_t)il2cpp_codegen_add(L_94, 3));
	}

IL_0169:
	{
		int32_t L_95 = V_0;
		int32_t L_96 = ___3_indexCount;
		if ((((int32_t)L_95) < ((int32_t)L_96)))
		{
			goto IL_0007;
		}
	}
	{
		float* L_97 = ___9_avgArea;
		float* L_98 = ___9_avgArea;
		float L_99 = *((float*)L_98);
		int32_t* L_100 = ___5_triangleCount;
		int32_t L_101 = *((int32_t*)L_100);
		*((float*)L_97) = (float)((float)(L_99/((float)L_101)));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ModuleHandle_CopyGraph_mD198F917465F876C1D09639EED8A2C2600ADF7EB (NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E ___0_srcPoints, int32_t ___1_srcPointCount, NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E* ___2_dstPoints, int32_t* ___3_dstPointCount, NativeArray_1_tE86585F07CF10FCD01AA2652A104B149336F7EC2 ___4_srcEdges, int32_t ___5_srcEdgeCount, NativeArray_1_tE86585F07CF10FCD01AA2652A104B149336F7EC2* ___6_dstEdges, int32_t* ___7_dstEdgeCount, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ModuleHandle_Copy_Tisfloat2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA_m07543F135AF3627179553F55ED1804459CFCE11F_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ModuleHandle_Copy_Tisint2_tF4AC25F87943DC0B2BB3456B0B919B3B42A9432A_mBFF6B1AB4AB8B44E22265CE0FC194BEAF466399C_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ModuleHandle_tF45CBC10412074A0AAEF63CBDAA5F92A4F7A9978_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		int32_t* L_0 = ___7_dstEdgeCount;
		int32_t L_1 = ___5_srcEdgeCount;
		*((int32_t*)L_0) = (int32_t)L_1;
		int32_t* L_2 = ___3_dstPointCount;
		int32_t L_3 = ___1_srcPointCount;
		*((int32_t*)L_2) = (int32_t)L_3;
		NativeArray_1_tE86585F07CF10FCD01AA2652A104B149336F7EC2 L_4 = ___4_srcEdges;
		NativeArray_1_tE86585F07CF10FCD01AA2652A104B149336F7EC2* L_5 = ___6_dstEdges;
		NativeArray_1_tE86585F07CF10FCD01AA2652A104B149336F7EC2 L_6 = (*(NativeArray_1_tE86585F07CF10FCD01AA2652A104B149336F7EC2*)L_5);
		int32_t L_7 = ___5_srcEdgeCount;
		il2cpp_codegen_runtime_class_init_inline(ModuleHandle_tF45CBC10412074A0AAEF63CBDAA5F92A4F7A9978_il2cpp_TypeInfo_var);
		ModuleHandle_Copy_Tisint2_tF4AC25F87943DC0B2BB3456B0B919B3B42A9432A_mBFF6B1AB4AB8B44E22265CE0FC194BEAF466399C(L_4, L_6, L_7, ModuleHandle_Copy_Tisint2_tF4AC25F87943DC0B2BB3456B0B919B3B42A9432A_mBFF6B1AB4AB8B44E22265CE0FC194BEAF466399C_RuntimeMethod_var);
		NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E L_8 = ___0_srcPoints;
		NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E* L_9 = ___2_dstPoints;
		NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E L_10 = (*(NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E*)L_9);
		int32_t L_11 = ___1_srcPointCount;
		ModuleHandle_Copy_Tisfloat2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA_m07543F135AF3627179553F55ED1804459CFCE11F(L_8, L_10, L_11, ModuleHandle_Copy_Tisfloat2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA_m07543F135AF3627179553F55ED1804459CFCE11F_RuntimeMethod_var);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ModuleHandle_CopyGeometry_m41B14E71387642F5CDDA4F2C8C2C173FA9FF5E3C (NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C ___0_srcIndices, int32_t ___1_srcIndexCount, NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C* ___2_dstIndices, int32_t* ___3_dstIndexCount, NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E ___4_srcVertices, int32_t ___5_srcVertexCount, NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E* ___6_dstVertices, int32_t* ___7_dstVertexCount, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ModuleHandle_Copy_TisInt32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_m08EAF9EE4BD7B3611121EEBB1CA1AC40D9C29874_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ModuleHandle_Copy_Tisfloat2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA_m07543F135AF3627179553F55ED1804459CFCE11F_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ModuleHandle_tF45CBC10412074A0AAEF63CBDAA5F92A4F7A9978_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		int32_t* L_0 = ___3_dstIndexCount;
		int32_t L_1 = ___1_srcIndexCount;
		*((int32_t*)L_0) = (int32_t)L_1;
		int32_t* L_2 = ___7_dstVertexCount;
		int32_t L_3 = ___5_srcVertexCount;
		*((int32_t*)L_2) = (int32_t)L_3;
		NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C L_4 = ___0_srcIndices;
		NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C* L_5 = ___2_dstIndices;
		NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C L_6 = (*(NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C*)L_5);
		int32_t L_7 = ___1_srcIndexCount;
		il2cpp_codegen_runtime_class_init_inline(ModuleHandle_tF45CBC10412074A0AAEF63CBDAA5F92A4F7A9978_il2cpp_TypeInfo_var);
		ModuleHandle_Copy_TisInt32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_m08EAF9EE4BD7B3611121EEBB1CA1AC40D9C29874(L_4, L_6, L_7, ModuleHandle_Copy_TisInt32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_m08EAF9EE4BD7B3611121EEBB1CA1AC40D9C29874_RuntimeMethod_var);
		NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E L_8 = ___4_srcVertices;
		NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E* L_9 = ___6_dstVertices;
		NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E L_10 = (*(NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E*)L_9);
		int32_t L_11 = ___5_srcVertexCount;
		ModuleHandle_Copy_Tisfloat2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA_m07543F135AF3627179553F55ED1804459CFCE11F(L_8, L_10, L_11, ModuleHandle_Copy_Tisfloat2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA_m07543F135AF3627179553F55ED1804459CFCE11F_RuntimeMethod_var);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ModuleHandle_TransferOutput_mE69BFB90D12C2CA71E368EC00347F5C1DA21BDAD (NativeArray_1_tE86585F07CF10FCD01AA2652A104B149336F7EC2 ___0_srcEdges, int32_t ___1_srcEdgeCount, NativeArray_1_tE86585F07CF10FCD01AA2652A104B149336F7EC2* ___2_dstEdges, int32_t* ___3_dstEdgeCount, NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C ___4_srcIndices, int32_t ___5_srcIndexCount, NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C* ___6_dstIndices, int32_t* ___7_dstIndexCount, NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E ___8_srcVertices, int32_t ___9_srcVertexCount, NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E* ___10_dstVertices, int32_t* ___11_dstVertexCount, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ModuleHandle_Copy_TisInt32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_m08EAF9EE4BD7B3611121EEBB1CA1AC40D9C29874_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ModuleHandle_Copy_Tisfloat2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA_m07543F135AF3627179553F55ED1804459CFCE11F_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ModuleHandle_Copy_Tisint2_tF4AC25F87943DC0B2BB3456B0B919B3B42A9432A_mBFF6B1AB4AB8B44E22265CE0FC194BEAF466399C_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ModuleHandle_tF45CBC10412074A0AAEF63CBDAA5F92A4F7A9978_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		int32_t* L_0 = ___3_dstEdgeCount;
		int32_t L_1 = ___1_srcEdgeCount;
		*((int32_t*)L_0) = (int32_t)L_1;
		int32_t* L_2 = ___7_dstIndexCount;
		int32_t L_3 = ___5_srcIndexCount;
		*((int32_t*)L_2) = (int32_t)L_3;
		int32_t* L_4 = ___11_dstVertexCount;
		int32_t L_5 = ___9_srcVertexCount;
		*((int32_t*)L_4) = (int32_t)L_5;
		NativeArray_1_tE86585F07CF10FCD01AA2652A104B149336F7EC2 L_6 = ___0_srcEdges;
		NativeArray_1_tE86585F07CF10FCD01AA2652A104B149336F7EC2* L_7 = ___2_dstEdges;
		NativeArray_1_tE86585F07CF10FCD01AA2652A104B149336F7EC2 L_8 = (*(NativeArray_1_tE86585F07CF10FCD01AA2652A104B149336F7EC2*)L_7);
		int32_t L_9 = ___1_srcEdgeCount;
		il2cpp_codegen_runtime_class_init_inline(ModuleHandle_tF45CBC10412074A0AAEF63CBDAA5F92A4F7A9978_il2cpp_TypeInfo_var);
		ModuleHandle_Copy_Tisint2_tF4AC25F87943DC0B2BB3456B0B919B3B42A9432A_mBFF6B1AB4AB8B44E22265CE0FC194BEAF466399C(L_6, L_8, L_9, ModuleHandle_Copy_Tisint2_tF4AC25F87943DC0B2BB3456B0B919B3B42A9432A_mBFF6B1AB4AB8B44E22265CE0FC194BEAF466399C_RuntimeMethod_var);
		NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C L_10 = ___4_srcIndices;
		NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C* L_11 = ___6_dstIndices;
		NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C L_12 = (*(NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C*)L_11);
		int32_t L_13 = ___5_srcIndexCount;
		ModuleHandle_Copy_TisInt32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_m08EAF9EE4BD7B3611121EEBB1CA1AC40D9C29874(L_10, L_12, L_13, ModuleHandle_Copy_TisInt32_t680FF22E76F6EFAD4375103CBBFFA0421349384C_m08EAF9EE4BD7B3611121EEBB1CA1AC40D9C29874_RuntimeMethod_var);
		NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E L_14 = ___8_srcVertices;
		NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E* L_15 = ___10_dstVertices;
		NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E L_16 = (*(NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E*)L_15);
		int32_t L_17 = ___9_srcVertexCount;
		ModuleHandle_Copy_Tisfloat2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA_m07543F135AF3627179553F55ED1804459CFCE11F(L_14, L_16, L_17, ModuleHandle_Copy_Tisfloat2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA_m07543F135AF3627179553F55ED1804459CFCE11F_RuntimeMethod_var);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ModuleHandle_GraphConditioner_m7C685F797F7096123ABD62F4932FFC0177FDE3CF (NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E ___0_points, NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E* ___1_pgPoints, int32_t* ___2_pgPointCount, NativeArray_1_tE86585F07CF10FCD01AA2652A104B149336F7EC2* ___3_pgEdges, int32_t* ___4_pgEdgeCount, bool ___5_resetTopology, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA V_0;
	memset((&V_0), 0, sizeof(V_0));
	float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA V_1;
	memset((&V_1), 0, sizeof(V_1));
	float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA V_2;
	memset((&V_2), 0, sizeof(V_2));
	float V_3 = 0.0f;
	int32_t V_4 = 0;
	int32_t V_5 = 0;
	int32_t V_6 = 0;
	int32_t* G_B5_0 = NULL;
	int32_t* G_B4_0 = NULL;
	int32_t G_B6_0 = 0;
	int32_t* G_B6_1 = NULL;
	{
		float2__ctor_m3D598E2C2D173DE852F3AB157502968261383C97_inline((&V_0), (std::numeric_limits<float>::infinity()), (std::numeric_limits<float>::infinity()), NULL);
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_0 = ((float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA_StaticFields*)il2cpp_codegen_static_fields_for(float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA_il2cpp_TypeInfo_var))->___zero;
		V_1 = L_0;
		V_5 = 0;
		goto IL_0042;
	}

IL_001c:
	{
		int32_t L_1 = V_5;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_2;
		L_2 = IL2CPP_NATIVEARRAY_GET_ITEM(float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA, ((&___0_points))->___m_Buffer, L_1);
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_3 = V_0;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_4;
		L_4 = math_min_m68ED612C41E325FA3446050EA04D0AC0CD191558_inline(L_2, L_3, NULL);
		V_0 = L_4;
		int32_t L_5 = V_5;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_6;
		L_6 = IL2CPP_NATIVEARRAY_GET_ITEM(float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA, ((&___0_points))->___m_Buffer, L_5);
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_7 = V_1;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_8;
		L_8 = math_max_mFD64D6399932C2D91018BA7895C06FD055E1361B_inline(L_6, L_7, NULL);
		V_1 = L_8;
		int32_t L_9 = V_5;
		V_5 = ((int32_t)il2cpp_codegen_add(L_9, 1));
	}

IL_0042:
	{
		int32_t L_10 = V_5;
		int32_t L_11;
		L_11 = IL2CPP_NATIVEARRAY_GET_LENGTH(((&___0_points))->___m_Length);
		if ((((int32_t)L_10) < ((int32_t)L_11)))
		{
			goto IL_001c;
		}
	}
	{
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_12 = V_1;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_13 = V_0;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_14;
		L_14 = float2_op_Subtraction_m28172675A65BCFFBC8C9023BE815019E668B8380_inline(L_12, L_13, NULL);
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_15;
		L_15 = float2_op_Multiply_m34D03129CE0D7AD665A914DE83CB749585B2455F_inline(L_14, (0.5f), NULL);
		V_2 = L_15;
		V_3 = (9.99999975E-05f);
		int32_t* L_16 = ___2_pgPointCount;
		bool L_17 = ___5_resetTopology;
		if (L_17)
		{
			G_B5_0 = L_16;
			goto IL_006e;
		}
		G_B4_0 = L_16;
	}
	{
		int32_t* L_18 = ___2_pgPointCount;
		int32_t L_19 = *((int32_t*)L_18);
		G_B6_0 = L_19;
		G_B6_1 = G_B4_0;
		goto IL_006f;
	}

IL_006e:
	{
		G_B6_0 = 0;
		G_B6_1 = G_B5_0;
	}

IL_006f:
	{
		*((int32_t*)G_B6_1) = (int32_t)G_B6_0;
		int32_t* L_20 = ___2_pgPointCount;
		int32_t L_21 = *((int32_t*)L_20);
		V_4 = L_21;
		NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E* L_22 = ___1_pgPoints;
		int32_t* L_23 = ___2_pgPointCount;
		int32_t* L_24 = ___2_pgPointCount;
		int32_t L_25 = *((int32_t*)L_24);
		V_6 = L_25;
		int32_t L_26 = V_6;
		*((int32_t*)L_23) = (int32_t)((int32_t)il2cpp_codegen_add(L_26, 1));
		int32_t L_27 = V_6;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_28 = V_0;
		float L_29 = L_28.___x;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_30 = V_0;
		float L_31 = L_30.___y;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_32;
		memset((&L_32), 0, sizeof(L_32));
		float2__ctor_m3D598E2C2D173DE852F3AB157502968261383C97_inline((&L_32), L_29, L_31, NULL);
		IL2CPP_NATIVEARRAY_SET_ITEM(float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA, (L_22)->___m_Buffer, L_27, (L_32));
		NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E* L_33 = ___1_pgPoints;
		int32_t* L_34 = ___2_pgPointCount;
		int32_t* L_35 = ___2_pgPointCount;
		int32_t L_36 = *((int32_t*)L_35);
		V_6 = L_36;
		int32_t L_37 = V_6;
		*((int32_t*)L_34) = (int32_t)((int32_t)il2cpp_codegen_add(L_37, 1));
		int32_t L_38 = V_6;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_39 = V_0;
		float L_40 = L_39.___x;
		float L_41 = V_3;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_42 = V_0;
		float L_43 = L_42.___y;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_44 = V_2;
		float L_45 = L_44.___y;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_46;
		memset((&L_46), 0, sizeof(L_46));
		float2__ctor_m3D598E2C2D173DE852F3AB157502968261383C97_inline((&L_46), ((float)il2cpp_codegen_subtract(L_40, L_41)), ((float)il2cpp_codegen_add(L_43, L_45)), NULL);
		IL2CPP_NATIVEARRAY_SET_ITEM(float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA, (L_33)->___m_Buffer, L_38, (L_46));
		NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E* L_47 = ___1_pgPoints;
		int32_t* L_48 = ___2_pgPointCount;
		int32_t* L_49 = ___2_pgPointCount;
		int32_t L_50 = *((int32_t*)L_49);
		V_6 = L_50;
		int32_t L_51 = V_6;
		*((int32_t*)L_48) = (int32_t)((int32_t)il2cpp_codegen_add(L_51, 1));
		int32_t L_52 = V_6;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_53 = V_0;
		float L_54 = L_53.___x;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_55 = V_1;
		float L_56 = L_55.___y;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_57;
		memset((&L_57), 0, sizeof(L_57));
		float2__ctor_m3D598E2C2D173DE852F3AB157502968261383C97_inline((&L_57), L_54, L_56, NULL);
		IL2CPP_NATIVEARRAY_SET_ITEM(float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA, (L_47)->___m_Buffer, L_52, (L_57));
		NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E* L_58 = ___1_pgPoints;
		int32_t* L_59 = ___2_pgPointCount;
		int32_t* L_60 = ___2_pgPointCount;
		int32_t L_61 = *((int32_t*)L_60);
		V_6 = L_61;
		int32_t L_62 = V_6;
		*((int32_t*)L_59) = (int32_t)((int32_t)il2cpp_codegen_add(L_62, 1));
		int32_t L_63 = V_6;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_64 = V_0;
		float L_65 = L_64.___x;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_66 = V_2;
		float L_67 = L_66.___x;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_68 = V_1;
		float L_69 = L_68.___y;
		float L_70 = V_3;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_71;
		memset((&L_71), 0, sizeof(L_71));
		float2__ctor_m3D598E2C2D173DE852F3AB157502968261383C97_inline((&L_71), ((float)il2cpp_codegen_add(L_65, L_67)), ((float)il2cpp_codegen_add(L_69, L_70)), NULL);
		IL2CPP_NATIVEARRAY_SET_ITEM(float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA, (L_58)->___m_Buffer, L_63, (L_71));
		NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E* L_72 = ___1_pgPoints;
		int32_t* L_73 = ___2_pgPointCount;
		int32_t* L_74 = ___2_pgPointCount;
		int32_t L_75 = *((int32_t*)L_74);
		V_6 = L_75;
		int32_t L_76 = V_6;
		*((int32_t*)L_73) = (int32_t)((int32_t)il2cpp_codegen_add(L_76, 1));
		int32_t L_77 = V_6;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_78 = V_1;
		float L_79 = L_78.___x;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_80 = V_1;
		float L_81 = L_80.___y;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_82;
		memset((&L_82), 0, sizeof(L_82));
		float2__ctor_m3D598E2C2D173DE852F3AB157502968261383C97_inline((&L_82), L_79, L_81, NULL);
		IL2CPP_NATIVEARRAY_SET_ITEM(float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA, (L_72)->___m_Buffer, L_77, (L_82));
		NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E* L_83 = ___1_pgPoints;
		int32_t* L_84 = ___2_pgPointCount;
		int32_t* L_85 = ___2_pgPointCount;
		int32_t L_86 = *((int32_t*)L_85);
		V_6 = L_86;
		int32_t L_87 = V_6;
		*((int32_t*)L_84) = (int32_t)((int32_t)il2cpp_codegen_add(L_87, 1));
		int32_t L_88 = V_6;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_89 = V_1;
		float L_90 = L_89.___x;
		float L_91 = V_3;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_92 = V_0;
		float L_93 = L_92.___y;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_94 = V_2;
		float L_95 = L_94.___y;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_96;
		memset((&L_96), 0, sizeof(L_96));
		float2__ctor_m3D598E2C2D173DE852F3AB157502968261383C97_inline((&L_96), ((float)il2cpp_codegen_add(L_90, L_91)), ((float)il2cpp_codegen_add(L_93, L_95)), NULL);
		IL2CPP_NATIVEARRAY_SET_ITEM(float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA, (L_83)->___m_Buffer, L_88, (L_96));
		NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E* L_97 = ___1_pgPoints;
		int32_t* L_98 = ___2_pgPointCount;
		int32_t* L_99 = ___2_pgPointCount;
		int32_t L_100 = *((int32_t*)L_99);
		V_6 = L_100;
		int32_t L_101 = V_6;
		*((int32_t*)L_98) = (int32_t)((int32_t)il2cpp_codegen_add(L_101, 1));
		int32_t L_102 = V_6;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_103 = V_1;
		float L_104 = L_103.___x;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_105 = V_0;
		float L_106 = L_105.___y;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_107;
		memset((&L_107), 0, sizeof(L_107));
		float2__ctor_m3D598E2C2D173DE852F3AB157502968261383C97_inline((&L_107), L_104, L_106, NULL);
		IL2CPP_NATIVEARRAY_SET_ITEM(float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA, (L_97)->___m_Buffer, L_102, (L_107));
		NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E* L_108 = ___1_pgPoints;
		int32_t* L_109 = ___2_pgPointCount;
		int32_t* L_110 = ___2_pgPointCount;
		int32_t L_111 = *((int32_t*)L_110);
		V_6 = L_111;
		int32_t L_112 = V_6;
		*((int32_t*)L_109) = (int32_t)((int32_t)il2cpp_codegen_add(L_112, 1));
		int32_t L_113 = V_6;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_114 = V_0;
		float L_115 = L_114.___x;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_116 = V_2;
		float L_117 = L_116.___x;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_118 = V_0;
		float L_119 = L_118.___y;
		float L_120 = V_3;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_121;
		memset((&L_121), 0, sizeof(L_121));
		float2__ctor_m3D598E2C2D173DE852F3AB157502968261383C97_inline((&L_121), ((float)il2cpp_codegen_add(L_115, L_117)), ((float)il2cpp_codegen_subtract(L_119, L_120)), NULL);
		IL2CPP_NATIVEARRAY_SET_ITEM(float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA, (L_108)->___m_Buffer, L_113, (L_121));
		int32_t* L_122 = ___4_pgEdgeCount;
		*((int32_t*)L_122) = (int32_t)8;
		NativeArray_1_tE86585F07CF10FCD01AA2652A104B149336F7EC2* L_123 = ___3_pgEdges;
		int32_t L_124 = V_4;
		int32_t L_125 = V_4;
		int2_tF4AC25F87943DC0B2BB3456B0B919B3B42A9432A L_126;
		memset((&L_126), 0, sizeof(L_126));
		int2__ctor_m452D21510717D0961119C89A72BBB8D84DCD49F4_inline((&L_126), L_124, ((int32_t)il2cpp_codegen_add(L_125, 1)), NULL);
		IL2CPP_NATIVEARRAY_SET_ITEM(int2_tF4AC25F87943DC0B2BB3456B0B919B3B42A9432A, (L_123)->___m_Buffer, 0, (L_126));
		NativeArray_1_tE86585F07CF10FCD01AA2652A104B149336F7EC2* L_127 = ___3_pgEdges;
		int32_t L_128 = V_4;
		int32_t L_129 = V_4;
		int2_tF4AC25F87943DC0B2BB3456B0B919B3B42A9432A L_130;
		memset((&L_130), 0, sizeof(L_130));
		int2__ctor_m452D21510717D0961119C89A72BBB8D84DCD49F4_inline((&L_130), ((int32_t)il2cpp_codegen_add(L_128, 1)), ((int32_t)il2cpp_codegen_add(L_129, 2)), NULL);
		IL2CPP_NATIVEARRAY_SET_ITEM(int2_tF4AC25F87943DC0B2BB3456B0B919B3B42A9432A, (L_127)->___m_Buffer, 1, (L_130));
		NativeArray_1_tE86585F07CF10FCD01AA2652A104B149336F7EC2* L_131 = ___3_pgEdges;
		int32_t L_132 = V_4;
		int32_t L_133 = V_4;
		int2_tF4AC25F87943DC0B2BB3456B0B919B3B42A9432A L_134;
		memset((&L_134), 0, sizeof(L_134));
		int2__ctor_m452D21510717D0961119C89A72BBB8D84DCD49F4_inline((&L_134), ((int32_t)il2cpp_codegen_add(L_132, 2)), ((int32_t)il2cpp_codegen_add(L_133, 3)), NULL);
		IL2CPP_NATIVEARRAY_SET_ITEM(int2_tF4AC25F87943DC0B2BB3456B0B919B3B42A9432A, (L_131)->___m_Buffer, 2, (L_134));
		NativeArray_1_tE86585F07CF10FCD01AA2652A104B149336F7EC2* L_135 = ___3_pgEdges;
		int32_t L_136 = V_4;
		int32_t L_137 = V_4;
		int2_tF4AC25F87943DC0B2BB3456B0B919B3B42A9432A L_138;
		memset((&L_138), 0, sizeof(L_138));
		int2__ctor_m452D21510717D0961119C89A72BBB8D84DCD49F4_inline((&L_138), ((int32_t)il2cpp_codegen_add(L_136, 3)), ((int32_t)il2cpp_codegen_add(L_137, 4)), NULL);
		IL2CPP_NATIVEARRAY_SET_ITEM(int2_tF4AC25F87943DC0B2BB3456B0B919B3B42A9432A, (L_135)->___m_Buffer, 3, (L_138));
		NativeArray_1_tE86585F07CF10FCD01AA2652A104B149336F7EC2* L_139 = ___3_pgEdges;
		int32_t L_140 = V_4;
		int32_t L_141 = V_4;
		int2_tF4AC25F87943DC0B2BB3456B0B919B3B42A9432A L_142;
		memset((&L_142), 0, sizeof(L_142));
		int2__ctor_m452D21510717D0961119C89A72BBB8D84DCD49F4_inline((&L_142), ((int32_t)il2cpp_codegen_add(L_140, 4)), ((int32_t)il2cpp_codegen_add(L_141, 5)), NULL);
		IL2CPP_NATIVEARRAY_SET_ITEM(int2_tF4AC25F87943DC0B2BB3456B0B919B3B42A9432A, (L_139)->___m_Buffer, 4, (L_142));
		NativeArray_1_tE86585F07CF10FCD01AA2652A104B149336F7EC2* L_143 = ___3_pgEdges;
		int32_t L_144 = V_4;
		int32_t L_145 = V_4;
		int2_tF4AC25F87943DC0B2BB3456B0B919B3B42A9432A L_146;
		memset((&L_146), 0, sizeof(L_146));
		int2__ctor_m452D21510717D0961119C89A72BBB8D84DCD49F4_inline((&L_146), ((int32_t)il2cpp_codegen_add(L_144, 5)), ((int32_t)il2cpp_codegen_add(L_145, 6)), NULL);
		IL2CPP_NATIVEARRAY_SET_ITEM(int2_tF4AC25F87943DC0B2BB3456B0B919B3B42A9432A, (L_143)->___m_Buffer, 5, (L_146));
		NativeArray_1_tE86585F07CF10FCD01AA2652A104B149336F7EC2* L_147 = ___3_pgEdges;
		int32_t L_148 = V_4;
		int32_t L_149 = V_4;
		int2_tF4AC25F87943DC0B2BB3456B0B919B3B42A9432A L_150;
		memset((&L_150), 0, sizeof(L_150));
		int2__ctor_m452D21510717D0961119C89A72BBB8D84DCD49F4_inline((&L_150), ((int32_t)il2cpp_codegen_add(L_148, 6)), ((int32_t)il2cpp_codegen_add(L_149, 7)), NULL);
		IL2CPP_NATIVEARRAY_SET_ITEM(int2_tF4AC25F87943DC0B2BB3456B0B919B3B42A9432A, (L_147)->___m_Buffer, 6, (L_150));
		NativeArray_1_tE86585F07CF10FCD01AA2652A104B149336F7EC2* L_151 = ___3_pgEdges;
		int32_t L_152 = V_4;
		int32_t L_153 = V_4;
		int2_tF4AC25F87943DC0B2BB3456B0B919B3B42A9432A L_154;
		memset((&L_154), 0, sizeof(L_154));
		int2__ctor_m452D21510717D0961119C89A72BBB8D84DCD49F4_inline((&L_154), ((int32_t)il2cpp_codegen_add(L_152, 7)), L_153, NULL);
		IL2CPP_NATIVEARRAY_SET_ITEM(int2_tF4AC25F87943DC0B2BB3456B0B919B3B42A9432A, (L_151)->___m_Buffer, 7, (L_154));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ModuleHandle_Reorder_mC37CEAA08AE5E9B432106F95C633908AE0ABD600 (int32_t ___0_startVertexCount, int32_t ___1_index, NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C* ___2_indices, int32_t* ___3_indexCount, NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E* ___4_vertices, int32_t* ___5_vertexCount, const RuntimeMethod* method) 
{
	bool V_0 = false;
	int32_t V_1 = 0;
	int32_t V_2 = 0;
	{
		V_0 = (bool)0;
		V_1 = 0;
		goto IL_0018;
	}

IL_0006:
	{
		NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C* L_0 = ___2_indices;
		int32_t L_1 = V_1;
		int32_t L_2;
		L_2 = IL2CPP_NATIVEARRAY_GET_ITEM(int32_t, (L_0)->___m_Buffer, L_1);
		int32_t L_3 = ___1_index;
		if ((!(((uint32_t)L_2) == ((uint32_t)L_3))))
		{
			goto IL_0014;
		}
	}
	{
		V_0 = (bool)1;
		goto IL_001d;
	}

IL_0014:
	{
		int32_t L_4 = V_1;
		V_1 = ((int32_t)il2cpp_codegen_add(L_4, 1));
	}

IL_0018:
	{
		int32_t L_5 = V_1;
		int32_t* L_6 = ___3_indexCount;
		int32_t L_7 = *((int32_t*)L_6);
		if ((((int32_t)L_5) < ((int32_t)L_7)))
		{
			goto IL_0006;
		}
	}

IL_001d:
	{
		bool L_8 = V_0;
		if (L_8)
		{
			goto IL_005b;
		}
	}
	{
		int32_t* L_9 = ___5_vertexCount;
		int32_t* L_10 = ___5_vertexCount;
		int32_t L_11 = *((int32_t*)L_10);
		*((int32_t*)L_9) = (int32_t)((int32_t)il2cpp_codegen_subtract(L_11, 1));
		NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E* L_12 = ___4_vertices;
		int32_t L_13 = ___1_index;
		NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E* L_14 = ___4_vertices;
		int32_t* L_15 = ___5_vertexCount;
		int32_t L_16 = *((int32_t*)L_15);
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_17;
		L_17 = IL2CPP_NATIVEARRAY_GET_ITEM(float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA, (L_14)->___m_Buffer, L_16);
		IL2CPP_NATIVEARRAY_SET_ITEM(float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA, (L_12)->___m_Buffer, L_13, (L_17));
		V_2 = 0;
		goto IL_0056;
	}

IL_003e:
	{
		NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C* L_18 = ___2_indices;
		int32_t L_19 = V_2;
		int32_t L_20;
		L_20 = IL2CPP_NATIVEARRAY_GET_ITEM(int32_t, (L_18)->___m_Buffer, L_19);
		int32_t* L_21 = ___5_vertexCount;
		int32_t L_22 = *((int32_t*)L_21);
		if ((!(((uint32_t)L_20) == ((uint32_t)L_22))))
		{
			goto IL_0052;
		}
	}
	{
		NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C* L_23 = ___2_indices;
		int32_t L_24 = V_2;
		int32_t L_25 = ___1_index;
		IL2CPP_NATIVEARRAY_SET_ITEM(int32_t, (L_23)->___m_Buffer, L_24, (L_25));
	}

IL_0052:
	{
		int32_t L_26 = V_2;
		V_2 = ((int32_t)il2cpp_codegen_add(L_26, 1));
	}

IL_0056:
	{
		int32_t L_27 = V_2;
		int32_t* L_28 = ___3_indexCount;
		int32_t L_29 = *((int32_t*)L_28);
		if ((((int32_t)L_27) < ((int32_t)L_29)))
		{
			goto IL_003e;
		}
	}

IL_005b:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ModuleHandle_VertexCleanupConditioner_m53303FF76EFACCC24EB5C389780B9533FBD50D5A (int32_t ___0_startVertexCount, NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C* ___1_indices, int32_t* ___2_indexCount, NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E* ___3_vertices, int32_t* ___4_vertexCount, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ModuleHandle_tF45CBC10412074A0AAEF63CBDAA5F92A4F7A9978_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	int32_t V_0 = 0;
	{
		int32_t L_0 = ___0_startVertexCount;
		V_0 = L_0;
		goto IL_0014;
	}

IL_0004:
	{
		int32_t L_1 = ___0_startVertexCount;
		int32_t L_2 = V_0;
		NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C* L_3 = ___1_indices;
		int32_t* L_4 = ___2_indexCount;
		NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E* L_5 = ___3_vertices;
		int32_t* L_6 = ___4_vertexCount;
		il2cpp_codegen_runtime_class_init_inline(ModuleHandle_tF45CBC10412074A0AAEF63CBDAA5F92A4F7A9978_il2cpp_TypeInfo_var);
		ModuleHandle_Reorder_mC37CEAA08AE5E9B432106F95C633908AE0ABD600(L_1, L_2, L_3, L_4, L_5, L_6, NULL);
		int32_t L_7 = V_0;
		V_0 = ((int32_t)il2cpp_codegen_add(L_7, 1));
	}

IL_0014:
	{
		int32_t L_8 = V_0;
		int32_t* L_9 = ___4_vertexCount;
		int32_t L_10 = *((int32_t*)L_9);
		if ((((int32_t)L_8) < ((int32_t)L_10)))
		{
			goto IL_0004;
		}
	}
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float4_t89D9A294E7A79BD81BFBDD18654508532958555E ModuleHandle_ConvexQuad_m11A1141C87F3A85C30DD2C1891CE0A418B9C33F0 (int32_t ___0_allocator, NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E ___1_points, NativeArray_1_tE86585F07CF10FCD01AA2652A104B149336F7EC2 ___2_edges, NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E* ___3_outVertices, int32_t* ___4_outVertexCount, NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C* ___5_outIndices, int32_t* ___6_outIndexCount, NativeArray_1_tE86585F07CF10FCD01AA2652A104B149336F7EC2* ___7_outEdges, int32_t* ___8_outEdgeCount, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ModuleHandle_tF45CBC10412074A0AAEF63CBDAA5F92A4F7A9978_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&NativeArray_1_Dispose_m3135DCFBA5DDC3D2CAA20FB2666F3A996856F2F2_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&NativeArray_1_Dispose_mAD3B69E4B23316C46AF8C35D7E1E81206323F16F_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&NativeArray_1__ctor_m3CB679B1B77F99FC5CF890F75C914E22555A1F13_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&NativeArray_1__ctor_mAF9D0A865FBFFE6364C3073A253711B4C109C67A_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&float4_t89D9A294E7A79BD81BFBDD18654508532958555E_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	float4_t89D9A294E7A79BD81BFBDD18654508532958555E V_0;
	memset((&V_0), 0, sizeof(V_0));
	int32_t V_1 = 0;
	int32_t V_2 = 0;
	NativeArray_1_tE86585F07CF10FCD01AA2652A104B149336F7EC2 V_3;
	memset((&V_3), 0, sizeof(V_3));
	NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E V_4;
	memset((&V_4), 0, sizeof(V_4));
	{
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_0 = ((float4_t89D9A294E7A79BD81BFBDD18654508532958555E_StaticFields*)il2cpp_codegen_static_fields_for(float4_t89D9A294E7A79BD81BFBDD18654508532958555E_il2cpp_TypeInfo_var))->___zero;
		V_0 = L_0;
		int32_t* L_1 = ___8_outEdgeCount;
		*((int32_t*)L_1) = (int32_t)0;
		int32_t* L_2 = ___6_outIndexCount;
		*((int32_t*)L_2) = (int32_t)0;
		int32_t* L_3 = ___4_outVertexCount;
		*((int32_t*)L_3) = (int32_t)0;
		int32_t L_4;
		L_4 = IL2CPP_NATIVEARRAY_GET_LENGTH(((&___1_points))->___m_Length);
		if ((((int32_t)L_4) < ((int32_t)3)))
		{
			goto IL_002a;
		}
	}
	{
		int32_t L_5;
		L_5 = IL2CPP_NATIVEARRAY_GET_LENGTH(((&___1_points))->___m_Length);
		il2cpp_codegen_runtime_class_init_inline(ModuleHandle_tF45CBC10412074A0AAEF63CBDAA5F92A4F7A9978_il2cpp_TypeInfo_var);
		int32_t L_6 = ((ModuleHandle_tF45CBC10412074A0AAEF63CBDAA5F92A4F7A9978_StaticFields*)il2cpp_codegen_static_fields_for(ModuleHandle_tF45CBC10412074A0AAEF63CBDAA5F92A4F7A9978_il2cpp_TypeInfo_var))->___kMaxVertexCount;
		if ((((int32_t)L_5) < ((int32_t)L_6)))
		{
			goto IL_002c;
		}
	}

IL_002a:
	{
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_7 = V_0;
		return L_7;
	}

IL_002c:
	{
		V_1 = 0;
		V_2 = 0;
		il2cpp_codegen_runtime_class_init_inline(ModuleHandle_tF45CBC10412074A0AAEF63CBDAA5F92A4F7A9978_il2cpp_TypeInfo_var);
		int32_t L_8 = ((ModuleHandle_tF45CBC10412074A0AAEF63CBDAA5F92A4F7A9978_StaticFields*)il2cpp_codegen_static_fields_for(ModuleHandle_tF45CBC10412074A0AAEF63CBDAA5F92A4F7A9978_il2cpp_TypeInfo_var))->___kMaxEdgeCount;
		int32_t L_9 = ___0_allocator;
		NativeArray_1__ctor_m3CB679B1B77F99FC5CF890F75C914E22555A1F13((&V_3), L_8, L_9, 1, NativeArray_1__ctor_m3CB679B1B77F99FC5CF890F75C914E22555A1F13_RuntimeMethod_var);
		int32_t L_10 = ((ModuleHandle_tF45CBC10412074A0AAEF63CBDAA5F92A4F7A9978_StaticFields*)il2cpp_codegen_static_fields_for(ModuleHandle_tF45CBC10412074A0AAEF63CBDAA5F92A4F7A9978_il2cpp_TypeInfo_var))->___kMaxVertexCount;
		int32_t L_11 = ___0_allocator;
		NativeArray_1__ctor_mAF9D0A865FBFFE6364C3073A253711B4C109C67A((&V_4), L_10, L_11, 1, NativeArray_1__ctor_mAF9D0A865FBFFE6364C3073A253711B4C109C67A_RuntimeMethod_var);
		NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E L_12 = ___1_points;
		ModuleHandle_GraphConditioner_m7C685F797F7096123ABD62F4932FFC0177FDE3CF(L_12, (&V_4), (&V_2), (&V_3), (&V_1), (bool)1, NULL);
		int32_t L_13 = ___0_allocator;
		NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E L_14 = V_4;
		int32_t L_15 = V_2;
		NativeArray_1_tE86585F07CF10FCD01AA2652A104B149336F7EC2 L_16 = V_3;
		int32_t L_17 = V_1;
		NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E* L_18 = ___3_outVertices;
		int32_t* L_19 = ___4_outVertexCount;
		NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C* L_20 = ___5_outIndices;
		int32_t* L_21 = ___6_outIndexCount;
		bool L_22;
		L_22 = Tessellator_Tessellate_m84DB7B38E7EC9AB5155F7EEDBC3382CF1092EC5E(L_13, L_14, L_15, L_16, L_17, L_18, L_19, L_20, L_21, NULL);
		NativeArray_1_Dispose_mAD3B69E4B23316C46AF8C35D7E1E81206323F16F((&V_4), NativeArray_1_Dispose_mAD3B69E4B23316C46AF8C35D7E1E81206323F16F_RuntimeMethod_var);
		NativeArray_1_Dispose_m3135DCFBA5DDC3D2CAA20FB2666F3A996856F2F2((&V_3), NativeArray_1_Dispose_m3135DCFBA5DDC3D2CAA20FB2666F3A996856F2F2_RuntimeMethod_var);
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_23 = V_0;
		return L_23;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float4_t89D9A294E7A79BD81BFBDD18654508532958555E ModuleHandle_Tessellate_mA1E9CDB4B4F5A347C6A1E3BFDB106B2BC3301EE0 (int32_t ___0_allocator, NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E ___1_points, NativeArray_1_tE86585F07CF10FCD01AA2652A104B149336F7EC2 ___2_edges, NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E* ___3_outVertices, int32_t* ___4_outVertexCount, NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C* ___5_outIndices, int32_t* ___6_outIndexCount, NativeArray_1_tE86585F07CF10FCD01AA2652A104B149336F7EC2* ___7_outEdges, int32_t* ___8_outEdgeCount, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ModuleHandle_Copy_Tisfloat2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA_m07543F135AF3627179553F55ED1804459CFCE11F_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ModuleHandle_Copy_Tisint2_tF4AC25F87943DC0B2BB3456B0B919B3B42A9432A_mBFF6B1AB4AB8B44E22265CE0FC194BEAF466399C_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ModuleHandle_tF45CBC10412074A0AAEF63CBDAA5F92A4F7A9978_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&NativeArray_1_Dispose_m05C674E687B921C37722A6A1FF938FD56574642E_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&NativeArray_1_Dispose_m3135DCFBA5DDC3D2CAA20FB2666F3A996856F2F2_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&NativeArray_1_Dispose_mAD3B69E4B23316C46AF8C35D7E1E81206323F16F_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&NativeArray_1__ctor_m3CB679B1B77F99FC5CF890F75C914E22555A1F13_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&NativeArray_1__ctor_mAF9D0A865FBFFE6364C3073A253711B4C109C67A_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&NativeArray_1__ctor_mB7BB23924A114599D399A5EC6C00B2B6407CF66D_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&PlanarGraph_t53936E128A9BC16439B53E0E845693E21A31B761_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&float4_t89D9A294E7A79BD81BFBDD18654508532958555E_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	float4_t89D9A294E7A79BD81BFBDD18654508532958555E V_0;
	memset((&V_0), 0, sizeof(V_0));
	bool V_1 = false;
	bool V_2 = false;
	int32_t V_3 = 0;
	int32_t V_4 = 0;
	NativeArray_1_tE86585F07CF10FCD01AA2652A104B149336F7EC2 V_5;
	memset((&V_5), 0, sizeof(V_5));
	NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E V_6;
	memset((&V_6), 0, sizeof(V_6));
	NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C V_7;
	memset((&V_7), 0, sizeof(V_7));
	NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E V_8;
	memset((&V_8), 0, sizeof(V_8));
	int32_t V_9 = 0;
	int32_t V_10 = 0;
	{
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_0 = ((float4_t89D9A294E7A79BD81BFBDD18654508532958555E_StaticFields*)il2cpp_codegen_static_fields_for(float4_t89D9A294E7A79BD81BFBDD18654508532958555E_il2cpp_TypeInfo_var))->___zero;
		V_0 = L_0;
		int32_t* L_1 = ___8_outEdgeCount;
		*((int32_t*)L_1) = (int32_t)0;
		int32_t* L_2 = ___6_outIndexCount;
		*((int32_t*)L_2) = (int32_t)0;
		int32_t* L_3 = ___4_outVertexCount;
		*((int32_t*)L_3) = (int32_t)0;
		int32_t L_4;
		L_4 = IL2CPP_NATIVEARRAY_GET_LENGTH(((&___1_points))->___m_Length);
		if ((((int32_t)L_4) < ((int32_t)3)))
		{
			goto IL_002a;
		}
	}
	{
		int32_t L_5;
		L_5 = IL2CPP_NATIVEARRAY_GET_LENGTH(((&___1_points))->___m_Length);
		il2cpp_codegen_runtime_class_init_inline(ModuleHandle_tF45CBC10412074A0AAEF63CBDAA5F92A4F7A9978_il2cpp_TypeInfo_var);
		int32_t L_6 = ((ModuleHandle_tF45CBC10412074A0AAEF63CBDAA5F92A4F7A9978_StaticFields*)il2cpp_codegen_static_fields_for(ModuleHandle_tF45CBC10412074A0AAEF63CBDAA5F92A4F7A9978_il2cpp_TypeInfo_var))->___kMaxVertexCount;
		if ((((int32_t)L_5) < ((int32_t)L_6)))
		{
			goto IL_002c;
		}
	}

IL_002a:
	{
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_7 = V_0;
		return L_7;
	}

IL_002c:
	{
		V_1 = (bool)0;
		V_2 = (bool)0;
		V_3 = 0;
		V_4 = 0;
		int32_t L_8;
		L_8 = IL2CPP_NATIVEARRAY_GET_LENGTH(((&___2_edges))->___m_Length);
		int32_t L_9 = ___0_allocator;
		NativeArray_1__ctor_m3CB679B1B77F99FC5CF890F75C914E22555A1F13((&V_5), ((int32_t)il2cpp_codegen_multiply(L_8, 8)), L_9, 1, NativeArray_1__ctor_m3CB679B1B77F99FC5CF890F75C914E22555A1F13_RuntimeMethod_var);
		int32_t L_10;
		L_10 = IL2CPP_NATIVEARRAY_GET_LENGTH(((&___1_points))->___m_Length);
		int32_t L_11 = ___0_allocator;
		NativeArray_1__ctor_mAF9D0A865FBFFE6364C3073A253711B4C109C67A((&V_6), ((int32_t)il2cpp_codegen_multiply(L_10, 4)), L_11, 1, NativeArray_1__ctor_mAF9D0A865FBFFE6364C3073A253711B4C109C67A_RuntimeMethod_var);
		int32_t L_12;
		L_12 = IL2CPP_NATIVEARRAY_GET_LENGTH(((&___2_edges))->___m_Length);
		if (!L_12)
		{
			goto IL_0081;
		}
	}
	{
		int32_t L_13 = ___0_allocator;
		NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E L_14 = ___1_points;
		int32_t L_15;
		L_15 = IL2CPP_NATIVEARRAY_GET_LENGTH(((&___1_points))->___m_Length);
		NativeArray_1_tE86585F07CF10FCD01AA2652A104B149336F7EC2 L_16 = ___2_edges;
		int32_t L_17;
		L_17 = IL2CPP_NATIVEARRAY_GET_LENGTH(((&___2_edges))->___m_Length);
		il2cpp_codegen_runtime_class_init_inline(PlanarGraph_t53936E128A9BC16439B53E0E845693E21A31B761_il2cpp_TypeInfo_var);
		bool L_18;
		L_18 = PlanarGraph_Validate_mD6F5B2173F4C2C298986E926D9C372B88B0ED39D(L_13, L_14, L_15, L_16, L_17, (&V_6), (&V_4), (&V_5), (&V_3), NULL);
		V_1 = L_18;
	}

IL_0081:
	{
		bool L_19 = V_1;
		if (L_19)
		{
			goto IL_00bf;
		}
	}
	{
		int32_t* L_20 = ___8_outEdgeCount;
		int32_t L_21;
		L_21 = IL2CPP_NATIVEARRAY_GET_LENGTH(((&___2_edges))->___m_Length);
		*((int32_t*)L_20) = (int32_t)L_21;
		int32_t* L_22 = ___4_outVertexCount;
		int32_t L_23;
		L_23 = IL2CPP_NATIVEARRAY_GET_LENGTH(((&___1_points))->___m_Length);
		*((int32_t*)L_22) = (int32_t)L_23;
		NativeArray_1_tE86585F07CF10FCD01AA2652A104B149336F7EC2 L_24 = ___2_edges;
		NativeArray_1_tE86585F07CF10FCD01AA2652A104B149336F7EC2* L_25 = ___7_outEdges;
		NativeArray_1_tE86585F07CF10FCD01AA2652A104B149336F7EC2 L_26 = (*(NativeArray_1_tE86585F07CF10FCD01AA2652A104B149336F7EC2*)L_25);
		int32_t L_27;
		L_27 = IL2CPP_NATIVEARRAY_GET_LENGTH(((&___2_edges))->___m_Length);
		il2cpp_codegen_runtime_class_init_inline(ModuleHandle_tF45CBC10412074A0AAEF63CBDAA5F92A4F7A9978_il2cpp_TypeInfo_var);
		ModuleHandle_Copy_Tisint2_tF4AC25F87943DC0B2BB3456B0B919B3B42A9432A_mBFF6B1AB4AB8B44E22265CE0FC194BEAF466399C(L_24, L_26, L_27, ModuleHandle_Copy_Tisint2_tF4AC25F87943DC0B2BB3456B0B919B3B42A9432A_mBFF6B1AB4AB8B44E22265CE0FC194BEAF466399C_RuntimeMethod_var);
		NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E L_28 = ___1_points;
		NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E* L_29 = ___3_outVertices;
		NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E L_30 = (*(NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E*)L_29);
		int32_t L_31;
		L_31 = IL2CPP_NATIVEARRAY_GET_LENGTH(((&___1_points))->___m_Length);
		ModuleHandle_Copy_Tisfloat2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA_m07543F135AF3627179553F55ED1804459CFCE11F(L_28, L_30, L_31, ModuleHandle_Copy_Tisfloat2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA_m07543F135AF3627179553F55ED1804459CFCE11F_RuntimeMethod_var);
	}

IL_00bf:
	{
		int32_t L_32 = V_4;
		if ((((int32_t)L_32) <= ((int32_t)2)))
		{
			goto IL_0131;
		}
	}
	{
		int32_t L_33 = V_3;
		if ((((int32_t)L_33) <= ((int32_t)2)))
		{
			goto IL_0131;
		}
	}
	{
		int32_t L_34 = V_4;
		int32_t L_35 = ___0_allocator;
		NativeArray_1__ctor_mB7BB23924A114599D399A5EC6C00B2B6407CF66D((&V_7), ((int32_t)il2cpp_codegen_multiply(L_34, 8)), L_35, 1, NativeArray_1__ctor_mB7BB23924A114599D399A5EC6C00B2B6407CF66D_RuntimeMethod_var);
		int32_t L_36 = V_4;
		int32_t L_37 = ___0_allocator;
		NativeArray_1__ctor_mAF9D0A865FBFFE6364C3073A253711B4C109C67A((&V_8), ((int32_t)il2cpp_codegen_multiply(L_36, 4)), L_37, 1, NativeArray_1__ctor_mAF9D0A865FBFFE6364C3073A253711B4C109C67A_RuntimeMethod_var);
		V_9 = 0;
		V_10 = 0;
		int32_t L_38 = ___0_allocator;
		NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E L_39 = V_6;
		int32_t L_40 = V_4;
		NativeArray_1_tE86585F07CF10FCD01AA2652A104B149336F7EC2 L_41 = V_5;
		int32_t L_42 = V_3;
		bool L_43;
		L_43 = Tessellator_Tessellate_m84DB7B38E7EC9AB5155F7EEDBC3382CF1092EC5E(L_38, L_39, L_40, L_41, L_42, (&V_8), (&V_10), (&V_7), (&V_9), NULL);
		V_1 = L_43;
		bool L_44 = V_1;
		if (!L_44)
		{
			goto IL_0123;
		}
	}
	{
		NativeArray_1_tE86585F07CF10FCD01AA2652A104B149336F7EC2 L_45 = V_5;
		int32_t L_46 = V_3;
		NativeArray_1_tE86585F07CF10FCD01AA2652A104B149336F7EC2* L_47 = ___7_outEdges;
		int32_t* L_48 = ___8_outEdgeCount;
		NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C L_49 = V_7;
		int32_t L_50 = V_9;
		NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C* L_51 = ___5_outIndices;
		int32_t* L_52 = ___6_outIndexCount;
		NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E L_53 = V_8;
		int32_t L_54 = V_10;
		NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E* L_55 = ___3_outVertices;
		int32_t* L_56 = ___4_outVertexCount;
		il2cpp_codegen_runtime_class_init_inline(ModuleHandle_tF45CBC10412074A0AAEF63CBDAA5F92A4F7A9978_il2cpp_TypeInfo_var);
		ModuleHandle_TransferOutput_mE69BFB90D12C2CA71E368EC00347F5C1DA21BDAD(L_45, L_46, L_47, L_48, L_49, L_50, L_51, L_52, L_53, L_54, L_55, L_56, NULL);
		bool L_57 = V_2;
		if (!L_57)
		{
			goto IL_0123;
		}
	}
	{
		int32_t* L_58 = ___8_outEdgeCount;
		*((int32_t*)L_58) = (int32_t)0;
	}

IL_0123:
	{
		NativeArray_1_Dispose_mAD3B69E4B23316C46AF8C35D7E1E81206323F16F((&V_8), NativeArray_1_Dispose_mAD3B69E4B23316C46AF8C35D7E1E81206323F16F_RuntimeMethod_var);
		NativeArray_1_Dispose_m05C674E687B921C37722A6A1FF938FD56574642E((&V_7), NativeArray_1_Dispose_m05C674E687B921C37722A6A1FF938FD56574642E_RuntimeMethod_var);
	}

IL_0131:
	{
		NativeArray_1_Dispose_mAD3B69E4B23316C46AF8C35D7E1E81206323F16F((&V_6), NativeArray_1_Dispose_mAD3B69E4B23316C46AF8C35D7E1E81206323F16F_RuntimeMethod_var);
		NativeArray_1_Dispose_m3135DCFBA5DDC3D2CAA20FB2666F3A996856F2F2((&V_5), NativeArray_1_Dispose_m3135DCFBA5DDC3D2CAA20FB2666F3A996856F2F2_RuntimeMethod_var);
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_59 = V_0;
		return L_59;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float4_t89D9A294E7A79BD81BFBDD18654508532958555E ModuleHandle_Subdivide_m29840DF7AE9A6487B3B997FF917951EE25A41408 (int32_t ___0_allocator, NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E ___1_points, NativeArray_1_tE86585F07CF10FCD01AA2652A104B149336F7EC2 ___2_edges, NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E* ___3_outVertices, int32_t* ___4_outVertexCount, NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C* ___5_outIndices, int32_t* ___6_outIndexCount, NativeArray_1_tE86585F07CF10FCD01AA2652A104B149336F7EC2* ___7_outEdges, int32_t* ___8_outEdgeCount, float ___9_areaFactor, float ___10_targetArea, int32_t ___11_refineIterations, int32_t ___12_smoothenIterations, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Math_tEB65DE7CA8B083C412C969C92981C030865486CE_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ModuleHandle_tF45CBC10412074A0AAEF63CBDAA5F92A4F7A9978_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&NativeArray_1_Dispose_m05C674E687B921C37722A6A1FF938FD56574642E_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&NativeArray_1_Dispose_m3135DCFBA5DDC3D2CAA20FB2666F3A996856F2F2_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&NativeArray_1_Dispose_mAD3B69E4B23316C46AF8C35D7E1E81206323F16F_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&NativeArray_1__ctor_m3CB679B1B77F99FC5CF890F75C914E22555A1F13_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&NativeArray_1__ctor_mAF9D0A865FBFFE6364C3073A253711B4C109C67A_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&NativeArray_1__ctor_mB7BB23924A114599D399A5EC6C00B2B6407CF66D_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Refinery_t7AB9DFA0E0468A03A75D525BE59E9B17FFC270F9_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Smoothen_t66451B46E8AA634F6F80536137F061EC45767822_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&float4_t89D9A294E7A79BD81BFBDD18654508532958555E_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	float4_t89D9A294E7A79BD81BFBDD18654508532958555E V_0;
	memset((&V_0), 0, sizeof(V_0));
	int32_t V_1 = 0;
	int32_t V_2 = 0;
	NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C V_3;
	memset((&V_3), 0, sizeof(V_3));
	NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E V_4;
	memset((&V_4), 0, sizeof(V_4));
	bool V_5 = false;
	bool V_6 = false;
	bool V_7 = false;
	float V_8 = 0.0f;
	float V_9 = 0.0f;
	int32_t V_10 = 0;
	int32_t V_11 = 0;
	int32_t V_12 = 0;
	int32_t V_13 = 0;
	NativeArray_1_tE86585F07CF10FCD01AA2652A104B149336F7EC2 V_14;
	memset((&V_14), 0, sizeof(V_14));
	NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E V_15;
	memset((&V_15), 0, sizeof(V_15));
	NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C V_16;
	memset((&V_16), 0, sizeof(V_16));
	NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E V_17;
	memset((&V_17), 0, sizeof(V_17));
	int32_t G_B7_0 = 0;
	{
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_0 = ((float4_t89D9A294E7A79BD81BFBDD18654508532958555E_StaticFields*)il2cpp_codegen_static_fields_for(float4_t89D9A294E7A79BD81BFBDD18654508532958555E_il2cpp_TypeInfo_var))->___zero;
		V_0 = L_0;
		int32_t* L_1 = ___8_outEdgeCount;
		*((int32_t*)L_1) = (int32_t)0;
		int32_t* L_2 = ___6_outIndexCount;
		*((int32_t*)L_2) = (int32_t)0;
		int32_t* L_3 = ___4_outVertexCount;
		*((int32_t*)L_3) = (int32_t)0;
		int32_t L_4;
		L_4 = IL2CPP_NATIVEARRAY_GET_LENGTH(((&___1_points))->___m_Length);
		if ((((int32_t)L_4) < ((int32_t)3)))
		{
			goto IL_0033;
		}
	}
	{
		int32_t L_5;
		L_5 = IL2CPP_NATIVEARRAY_GET_LENGTH(((&___1_points))->___m_Length);
		il2cpp_codegen_runtime_class_init_inline(ModuleHandle_tF45CBC10412074A0AAEF63CBDAA5F92A4F7A9978_il2cpp_TypeInfo_var);
		int32_t L_6 = ((ModuleHandle_tF45CBC10412074A0AAEF63CBDAA5F92A4F7A9978_StaticFields*)il2cpp_codegen_static_fields_for(ModuleHandle_tF45CBC10412074A0AAEF63CBDAA5F92A4F7A9978_il2cpp_TypeInfo_var))->___kMaxVertexCount;
		if ((((int32_t)L_5) >= ((int32_t)L_6)))
		{
			goto IL_0033;
		}
	}
	{
		int32_t L_7;
		L_7 = IL2CPP_NATIVEARRAY_GET_LENGTH(((&___2_edges))->___m_Length);
		if (L_7)
		{
			goto IL_0035;
		}
	}

IL_0033:
	{
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_8 = V_0;
		return L_8;
	}

IL_0035:
	{
		V_1 = 0;
		V_2 = 0;
		il2cpp_codegen_runtime_class_init_inline(ModuleHandle_tF45CBC10412074A0AAEF63CBDAA5F92A4F7A9978_il2cpp_TypeInfo_var);
		int32_t L_9 = ((ModuleHandle_tF45CBC10412074A0AAEF63CBDAA5F92A4F7A9978_StaticFields*)il2cpp_codegen_static_fields_for(ModuleHandle_tF45CBC10412074A0AAEF63CBDAA5F92A4F7A9978_il2cpp_TypeInfo_var))->___kMaxIndexCount;
		int32_t L_10 = ___0_allocator;
		NativeArray_1__ctor_mB7BB23924A114599D399A5EC6C00B2B6407CF66D((&V_3), L_9, L_10, 1, NativeArray_1__ctor_mB7BB23924A114599D399A5EC6C00B2B6407CF66D_RuntimeMethod_var);
		int32_t L_11 = ((ModuleHandle_tF45CBC10412074A0AAEF63CBDAA5F92A4F7A9978_StaticFields*)il2cpp_codegen_static_fields_for(ModuleHandle_tF45CBC10412074A0AAEF63CBDAA5F92A4F7A9978_il2cpp_TypeInfo_var))->___kMaxVertexCount;
		int32_t L_12 = ___0_allocator;
		NativeArray_1__ctor_mAF9D0A865FBFFE6364C3073A253711B4C109C67A((&V_4), L_11, L_12, 1, NativeArray_1__ctor_mAF9D0A865FBFFE6364C3073A253711B4C109C67A_RuntimeMethod_var);
		int32_t L_13 = ___0_allocator;
		NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E L_14 = ___1_points;
		int32_t L_15;
		L_15 = IL2CPP_NATIVEARRAY_GET_LENGTH(((&___1_points))->___m_Length);
		NativeArray_1_tE86585F07CF10FCD01AA2652A104B149336F7EC2 L_16 = ___2_edges;
		int32_t L_17;
		L_17 = IL2CPP_NATIVEARRAY_GET_LENGTH(((&___2_edges))->___m_Length);
		bool L_18;
		L_18 = Tessellator_Tessellate_m84DB7B38E7EC9AB5155F7EEDBC3382CF1092EC5E(L_13, L_14, L_15, L_16, L_17, (&V_4), (&V_2), (&V_3), (&V_1), NULL);
		V_5 = L_18;
		V_6 = (bool)0;
		float L_19 = ___10_targetArea;
		if ((!(((float)L_19) == ((float)(0.0f)))))
		{
			goto IL_008f;
		}
	}
	{
		float L_20 = ___9_areaFactor;
		G_B7_0 = ((((int32_t)((((float)L_20) == ((float)(0.0f)))? 1 : 0)) == ((int32_t)0))? 1 : 0);
		goto IL_0090;
	}

IL_008f:
	{
		G_B7_0 = 1;
	}

IL_0090:
	{
		V_7 = (bool)G_B7_0;
		bool L_21 = V_5;
		bool L_22 = V_7;
		if (!((int32_t)((int32_t)L_21&(int32_t)L_22)))
		{
			goto IL_0365;
		}
	}
	{
		V_8 = (0.0f);
		V_9 = (0.0f);
		V_10 = 0;
		V_11 = 0;
		V_12 = 0;
		V_13 = 0;
		il2cpp_codegen_runtime_class_init_inline(ModuleHandle_tF45CBC10412074A0AAEF63CBDAA5F92A4F7A9978_il2cpp_TypeInfo_var);
		int32_t L_23 = ((ModuleHandle_tF45CBC10412074A0AAEF63CBDAA5F92A4F7A9978_StaticFields*)il2cpp_codegen_static_fields_for(ModuleHandle_tF45CBC10412074A0AAEF63CBDAA5F92A4F7A9978_il2cpp_TypeInfo_var))->___kMaxEdgeCount;
		int32_t L_24 = ___0_allocator;
		NativeArray_1__ctor_m3CB679B1B77F99FC5CF890F75C914E22555A1F13((&V_14), L_23, L_24, 1, NativeArray_1__ctor_m3CB679B1B77F99FC5CF890F75C914E22555A1F13_RuntimeMethod_var);
		int32_t L_25 = ((ModuleHandle_tF45CBC10412074A0AAEF63CBDAA5F92A4F7A9978_StaticFields*)il2cpp_codegen_static_fields_for(ModuleHandle_tF45CBC10412074A0AAEF63CBDAA5F92A4F7A9978_il2cpp_TypeInfo_var))->___kMaxVertexCount;
		int32_t L_26 = ___0_allocator;
		NativeArray_1__ctor_mAF9D0A865FBFFE6364C3073A253711B4C109C67A((&V_15), L_25, L_26, 1, NativeArray_1__ctor_mAF9D0A865FBFFE6364C3073A253711B4C109C67A_RuntimeMethod_var);
		int32_t L_27 = ((ModuleHandle_tF45CBC10412074A0AAEF63CBDAA5F92A4F7A9978_StaticFields*)il2cpp_codegen_static_fields_for(ModuleHandle_tF45CBC10412074A0AAEF63CBDAA5F92A4F7A9978_il2cpp_TypeInfo_var))->___kMaxIndexCount;
		int32_t L_28 = ___0_allocator;
		NativeArray_1__ctor_mB7BB23924A114599D399A5EC6C00B2B6407CF66D((&V_16), L_27, L_28, 1, NativeArray_1__ctor_mB7BB23924A114599D399A5EC6C00B2B6407CF66D_RuntimeMethod_var);
		int32_t L_29 = ((ModuleHandle_tF45CBC10412074A0AAEF63CBDAA5F92A4F7A9978_StaticFields*)il2cpp_codegen_static_fields_for(ModuleHandle_tF45CBC10412074A0AAEF63CBDAA5F92A4F7A9978_il2cpp_TypeInfo_var))->___kMaxVertexCount;
		int32_t L_30 = ___0_allocator;
		NativeArray_1__ctor_mAF9D0A865FBFFE6364C3073A253711B4C109C67A((&V_17), L_29, L_30, 1, NativeArray_1__ctor_mAF9D0A865FBFFE6364C3073A253711B4C109C67A_RuntimeMethod_var);
		(&V_0)->___x = (0.0f);
		int32_t L_31 = ___11_refineIterations;
		int32_t L_32 = ((ModuleHandle_tF45CBC10412074A0AAEF63CBDAA5F92A4F7A9978_StaticFields*)il2cpp_codegen_static_fields_for(ModuleHandle_tF45CBC10412074A0AAEF63CBDAA5F92A4F7A9978_il2cpp_TypeInfo_var))->___kMaxRefineIterations;
		il2cpp_codegen_runtime_class_init_inline(Math_tEB65DE7CA8B083C412C969C92981C030865486CE_il2cpp_TypeInfo_var);
		int32_t L_33;
		L_33 = Math_Min_m53C488772A34D53917BCA2A491E79A0A5356ED52(L_31, L_32, NULL);
		___11_refineIterations = L_33;
		float L_34 = ___10_targetArea;
		if ((((float)L_34) == ((float)(0.0f))))
		{
			goto IL_01ce;
		}
	}
	{
		float L_35 = ___10_targetArea;
		V_9 = ((float)(L_35/(10.0f)));
		goto IL_01b4;
	}

IL_0123:
	{
		NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E L_36 = ___1_points;
		int32_t L_37;
		L_37 = IL2CPP_NATIVEARRAY_GET_LENGTH(((&___1_points))->___m_Length);
		NativeArray_1_tE86585F07CF10FCD01AA2652A104B149336F7EC2 L_38 = ___2_edges;
		int32_t L_39;
		L_39 = IL2CPP_NATIVEARRAY_GET_LENGTH(((&___2_edges))->___m_Length);
		il2cpp_codegen_runtime_class_init_inline(ModuleHandle_tF45CBC10412074A0AAEF63CBDAA5F92A4F7A9978_il2cpp_TypeInfo_var);
		ModuleHandle_CopyGraph_mD198F917465F876C1D09639EED8A2C2600ADF7EB(L_36, L_37, (&V_15), (&V_11), L_38, L_39, (&V_14), (&V_10), NULL);
		NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C L_40 = V_3;
		int32_t L_41 = V_1;
		NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E L_42 = V_4;
		int32_t L_43 = V_2;
		ModuleHandle_CopyGeometry_m41B14E71387642F5CDDA4F2C8C2C173FA9FF5E3C(L_40, L_41, (&V_16), (&V_12), L_42, L_43, (&V_17), (&V_13), NULL);
		int32_t L_44 = ___0_allocator;
		float L_45 = ___9_areaFactor;
		float L_46 = ___10_targetArea;
		il2cpp_codegen_runtime_class_init_inline(Refinery_t7AB9DFA0E0468A03A75D525BE59E9B17FFC270F9_il2cpp_TypeInfo_var);
		bool L_47;
		L_47 = Refinery_Condition_m75ECBF8D82871AEB1D046E61F20DD3700E18D214(L_44, L_45, L_46, (&V_15), (&V_11), (&V_14), (&V_10), (&V_17), (&V_13), (&V_16), (&V_12), (&V_8), NULL);
		V_6 = L_47;
		bool L_48 = V_6;
		if (!L_48)
		{
			goto IL_01a4;
		}
	}
	{
		int32_t L_49 = V_12;
		int32_t L_50 = V_11;
		if ((((int32_t)L_49) <= ((int32_t)L_50)))
		{
			goto IL_01a4;
		}
	}
	{
		float L_51 = ___9_areaFactor;
		(&V_0)->___x = L_51;
		NativeArray_1_tE86585F07CF10FCD01AA2652A104B149336F7EC2 L_52 = V_14;
		int32_t L_53 = V_10;
		NativeArray_1_tE86585F07CF10FCD01AA2652A104B149336F7EC2* L_54 = ___7_outEdges;
		int32_t* L_55 = ___8_outEdgeCount;
		NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C L_56 = V_16;
		int32_t L_57 = V_12;
		NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C* L_58 = ___5_outIndices;
		int32_t* L_59 = ___6_outIndexCount;
		NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E L_60 = V_17;
		int32_t L_61 = V_13;
		NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E* L_62 = ___3_outVertices;
		int32_t* L_63 = ___4_outVertexCount;
		il2cpp_codegen_runtime_class_init_inline(ModuleHandle_tF45CBC10412074A0AAEF63CBDAA5F92A4F7A9978_il2cpp_TypeInfo_var);
		ModuleHandle_TransferOutput_mE69BFB90D12C2CA71E368EC00347F5C1DA21BDAD(L_52, L_53, L_54, L_55, L_56, L_57, L_58, L_59, L_60, L_61, L_62, L_63, NULL);
		goto IL_02a7;
	}

IL_01a4:
	{
		V_6 = (bool)0;
		float L_64 = ___10_targetArea;
		float L_65 = V_9;
		___10_targetArea = ((float)il2cpp_codegen_add(L_64, L_65));
		int32_t L_66 = ___11_refineIterations;
		___11_refineIterations = ((int32_t)il2cpp_codegen_subtract(L_66, 1));
	}

IL_01b4:
	{
		float L_67 = ___10_targetArea;
		il2cpp_codegen_runtime_class_init_inline(ModuleHandle_tF45CBC10412074A0AAEF63CBDAA5F92A4F7A9978_il2cpp_TypeInfo_var);
		int32_t L_68 = ((ModuleHandle_tF45CBC10412074A0AAEF63CBDAA5F92A4F7A9978_StaticFields*)il2cpp_codegen_static_fields_for(ModuleHandle_tF45CBC10412074A0AAEF63CBDAA5F92A4F7A9978_il2cpp_TypeInfo_var))->___kMaxArea;
		if ((!(((float)L_67) < ((float)((float)L_68)))))
		{
			goto IL_02a7;
		}
	}
	{
		int32_t L_69 = ___11_refineIterations;
		if ((((int32_t)L_69) > ((int32_t)0)))
		{
			goto IL_0123;
		}
	}
	{
		goto IL_02a7;
	}

IL_01ce:
	{
		float L_70 = ___9_areaFactor;
		if ((((float)L_70) == ((float)(0.0f))))
		{
			goto IL_02a7;
		}
	}
	{
		float L_71 = ___9_areaFactor;
		float L_72;
		L_72 = math_lerp_m58A82DB48BBA11871FFA81583C700875B3A9BC84_inline((0.100000001f), (0.540000021f), ((float)(((float)il2cpp_codegen_subtract(L_71, (0.0500000007f)))/(0.449999988f))), NULL);
		___9_areaFactor = L_72;
		float L_73 = ___9_areaFactor;
		V_9 = ((float)(L_73/(10.0f)));
		goto IL_0296;
	}

IL_0208:
	{
		NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E L_74 = ___1_points;
		int32_t L_75;
		L_75 = IL2CPP_NATIVEARRAY_GET_LENGTH(((&___1_points))->___m_Length);
		NativeArray_1_tE86585F07CF10FCD01AA2652A104B149336F7EC2 L_76 = ___2_edges;
		int32_t L_77;
		L_77 = IL2CPP_NATIVEARRAY_GET_LENGTH(((&___2_edges))->___m_Length);
		il2cpp_codegen_runtime_class_init_inline(ModuleHandle_tF45CBC10412074A0AAEF63CBDAA5F92A4F7A9978_il2cpp_TypeInfo_var);
		ModuleHandle_CopyGraph_mD198F917465F876C1D09639EED8A2C2600ADF7EB(L_74, L_75, (&V_15), (&V_11), L_76, L_77, (&V_14), (&V_10), NULL);
		NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C L_78 = V_3;
		int32_t L_79 = V_1;
		NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E L_80 = V_4;
		int32_t L_81 = V_2;
		ModuleHandle_CopyGeometry_m41B14E71387642F5CDDA4F2C8C2C173FA9FF5E3C(L_78, L_79, (&V_16), (&V_12), L_80, L_81, (&V_17), (&V_13), NULL);
		int32_t L_82 = ___0_allocator;
		float L_83 = ___9_areaFactor;
		float L_84 = ___10_targetArea;
		il2cpp_codegen_runtime_class_init_inline(Refinery_t7AB9DFA0E0468A03A75D525BE59E9B17FFC270F9_il2cpp_TypeInfo_var);
		bool L_85;
		L_85 = Refinery_Condition_m75ECBF8D82871AEB1D046E61F20DD3700E18D214(L_82, L_83, L_84, (&V_15), (&V_11), (&V_14), (&V_10), (&V_17), (&V_13), (&V_16), (&V_12), (&V_8), NULL);
		V_6 = L_85;
		bool L_86 = V_6;
		if (!L_86)
		{
			goto IL_0286;
		}
	}
	{
		int32_t L_87 = V_12;
		int32_t L_88 = V_11;
		if ((((int32_t)L_87) <= ((int32_t)L_88)))
		{
			goto IL_0286;
		}
	}
	{
		float L_89 = ___9_areaFactor;
		(&V_0)->___x = L_89;
		NativeArray_1_tE86585F07CF10FCD01AA2652A104B149336F7EC2 L_90 = V_14;
		int32_t L_91 = V_10;
		NativeArray_1_tE86585F07CF10FCD01AA2652A104B149336F7EC2* L_92 = ___7_outEdges;
		int32_t* L_93 = ___8_outEdgeCount;
		NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C L_94 = V_16;
		int32_t L_95 = V_12;
		NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C* L_96 = ___5_outIndices;
		int32_t* L_97 = ___6_outIndexCount;
		NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E L_98 = V_17;
		int32_t L_99 = V_13;
		NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E* L_100 = ___3_outVertices;
		int32_t* L_101 = ___4_outVertexCount;
		il2cpp_codegen_runtime_class_init_inline(ModuleHandle_tF45CBC10412074A0AAEF63CBDAA5F92A4F7A9978_il2cpp_TypeInfo_var);
		ModuleHandle_TransferOutput_mE69BFB90D12C2CA71E368EC00347F5C1DA21BDAD(L_90, L_91, L_92, L_93, L_94, L_95, L_96, L_97, L_98, L_99, L_100, L_101, NULL);
		goto IL_02a7;
	}

IL_0286:
	{
		V_6 = (bool)0;
		float L_102 = ___9_areaFactor;
		float L_103 = V_9;
		___9_areaFactor = ((float)il2cpp_codegen_add(L_102, L_103));
		int32_t L_104 = ___11_refineIterations;
		___11_refineIterations = ((int32_t)il2cpp_codegen_subtract(L_104, 1));
	}

IL_0296:
	{
		float L_105 = ___9_areaFactor;
		if ((!(((float)L_105) < ((float)(0.800000012f)))))
		{
			goto IL_02a7;
		}
	}
	{
		int32_t L_106 = ___11_refineIterations;
		if ((((int32_t)L_106) > ((int32_t)0)))
		{
			goto IL_0208;
		}
	}

IL_02a7:
	{
		bool L_107 = V_6;
		if (!L_107)
		{
			goto IL_0349;
		}
	}
	{
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_108 = V_0;
		float L_109 = L_108.___x;
		if ((((float)L_109) == ((float)(0.0f))))
		{
			goto IL_02c9;
		}
	}
	{
		int32_t L_110 = V_2;
		il2cpp_codegen_runtime_class_init_inline(ModuleHandle_tF45CBC10412074A0AAEF63CBDAA5F92A4F7A9978_il2cpp_TypeInfo_var);
		ModuleHandle_VertexCleanupConditioner_m53303FF76EFACCC24EB5C389780B9533FBD50D5A(L_110, (&V_16), (&V_12), (&V_17), (&V_13), NULL);
	}

IL_02c9:
	{
		(&V_0)->___y = (0.0f);
		int32_t L_111 = ___12_smoothenIterations;
		il2cpp_codegen_runtime_class_init_inline(ModuleHandle_tF45CBC10412074A0AAEF63CBDAA5F92A4F7A9978_il2cpp_TypeInfo_var);
		int32_t L_112 = ((ModuleHandle_tF45CBC10412074A0AAEF63CBDAA5F92A4F7A9978_StaticFields*)il2cpp_codegen_static_fields_for(ModuleHandle_tF45CBC10412074A0AAEF63CBDAA5F92A4F7A9978_il2cpp_TypeInfo_var))->___kMaxSmoothenIterations;
		int32_t L_113;
		L_113 = math_clamp_m9EABD008C8EAD9D150062ABE724D96FA2121EE1C_inline(L_111, 0, L_112, NULL);
		___12_smoothenIterations = L_113;
		goto IL_032a;
	}

IL_02e6:
	{
		int32_t L_114 = ___0_allocator;
		int32_t L_115 = V_11;
		NativeArray_1_tE86585F07CF10FCD01AA2652A104B149336F7EC2 L_116 = V_14;
		int32_t L_117 = V_10;
		il2cpp_codegen_runtime_class_init_inline(Smoothen_t66451B46E8AA634F6F80536137F061EC45767822_il2cpp_TypeInfo_var);
		bool L_118;
		L_118 = Smoothen_Condition_m831A479BB846A668D896E06A2737129629F3DFC2(L_114, (&V_15), L_115, L_116, L_117, (&V_17), (&V_13), (&V_16), (&V_12), NULL);
		if (!L_118)
		{
			goto IL_032f;
		}
	}
	{
		int32_t L_119 = ___12_smoothenIterations;
		(&V_0)->___y = ((float)L_119);
		NativeArray_1_tE86585F07CF10FCD01AA2652A104B149336F7EC2 L_120 = V_14;
		int32_t L_121 = V_10;
		NativeArray_1_tE86585F07CF10FCD01AA2652A104B149336F7EC2* L_122 = ___7_outEdges;
		int32_t* L_123 = ___8_outEdgeCount;
		NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C L_124 = V_16;
		int32_t L_125 = V_12;
		NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C* L_126 = ___5_outIndices;
		int32_t* L_127 = ___6_outIndexCount;
		NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E L_128 = V_17;
		int32_t L_129 = V_13;
		NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E* L_130 = ___3_outVertices;
		int32_t* L_131 = ___4_outVertexCount;
		il2cpp_codegen_runtime_class_init_inline(ModuleHandle_tF45CBC10412074A0AAEF63CBDAA5F92A4F7A9978_il2cpp_TypeInfo_var);
		ModuleHandle_TransferOutput_mE69BFB90D12C2CA71E368EC00347F5C1DA21BDAD(L_120, L_121, L_122, L_123, L_124, L_125, L_126, L_127, L_128, L_129, L_130, L_131, NULL);
		int32_t L_132 = ___12_smoothenIterations;
		___12_smoothenIterations = ((int32_t)il2cpp_codegen_subtract(L_132, 1));
	}

IL_032a:
	{
		int32_t L_133 = ___12_smoothenIterations;
		if ((((int32_t)L_133) > ((int32_t)0)))
		{
			goto IL_02e6;
		}
	}

IL_032f:
	{
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_134 = V_0;
		float L_135 = L_134.___y;
		if ((((float)L_135) == ((float)(0.0f))))
		{
			goto IL_0349;
		}
	}
	{
		int32_t L_136 = V_2;
		NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C* L_137 = ___5_outIndices;
		int32_t* L_138 = ___6_outIndexCount;
		NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E* L_139 = ___3_outVertices;
		int32_t* L_140 = ___4_outVertexCount;
		il2cpp_codegen_runtime_class_init_inline(ModuleHandle_tF45CBC10412074A0AAEF63CBDAA5F92A4F7A9978_il2cpp_TypeInfo_var);
		ModuleHandle_VertexCleanupConditioner_m53303FF76EFACCC24EB5C389780B9533FBD50D5A(L_136, L_137, L_138, L_139, L_140, NULL);
	}

IL_0349:
	{
		NativeArray_1_Dispose_mAD3B69E4B23316C46AF8C35D7E1E81206323F16F((&V_17), NativeArray_1_Dispose_mAD3B69E4B23316C46AF8C35D7E1E81206323F16F_RuntimeMethod_var);
		NativeArray_1_Dispose_m05C674E687B921C37722A6A1FF938FD56574642E((&V_16), NativeArray_1_Dispose_m05C674E687B921C37722A6A1FF938FD56574642E_RuntimeMethod_var);
		NativeArray_1_Dispose_mAD3B69E4B23316C46AF8C35D7E1E81206323F16F((&V_15), NativeArray_1_Dispose_mAD3B69E4B23316C46AF8C35D7E1E81206323F16F_RuntimeMethod_var);
		NativeArray_1_Dispose_m3135DCFBA5DDC3D2CAA20FB2666F3A996856F2F2((&V_14), NativeArray_1_Dispose_m3135DCFBA5DDC3D2CAA20FB2666F3A996856F2F2_RuntimeMethod_var);
	}

IL_0365:
	{
		bool L_141 = V_5;
		if (!L_141)
		{
			goto IL_038a;
		}
	}
	{
		bool L_142 = V_6;
		if (L_142)
		{
			goto IL_038a;
		}
	}
	{
		NativeArray_1_tE86585F07CF10FCD01AA2652A104B149336F7EC2 L_143 = ___2_edges;
		int32_t L_144;
		L_144 = IL2CPP_NATIVEARRAY_GET_LENGTH(((&___2_edges))->___m_Length);
		NativeArray_1_tE86585F07CF10FCD01AA2652A104B149336F7EC2* L_145 = ___7_outEdges;
		int32_t* L_146 = ___8_outEdgeCount;
		NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C L_147 = V_3;
		int32_t L_148 = V_1;
		NativeArray_1_tA833EB7E3E1C9AF82C37976AD964B8D4BAC38B2C* L_149 = ___5_outIndices;
		int32_t* L_150 = ___6_outIndexCount;
		NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E L_151 = V_4;
		int32_t L_152 = V_2;
		NativeArray_1_t46D43179C2B71BAB34958401E08B5C5DA4488E9E* L_153 = ___3_outVertices;
		int32_t* L_154 = ___4_outVertexCount;
		il2cpp_codegen_runtime_class_init_inline(ModuleHandle_tF45CBC10412074A0AAEF63CBDAA5F92A4F7A9978_il2cpp_TypeInfo_var);
		ModuleHandle_TransferOutput_mE69BFB90D12C2CA71E368EC00347F5C1DA21BDAD(L_143, L_144, L_145, L_146, L_147, L_148, L_149, L_150, L_151, L_152, L_153, L_154, NULL);
	}

IL_038a:
	{
		NativeArray_1_Dispose_mAD3B69E4B23316C46AF8C35D7E1E81206323F16F((&V_4), NativeArray_1_Dispose_mAD3B69E4B23316C46AF8C35D7E1E81206323F16F_RuntimeMethod_var);
		NativeArray_1_Dispose_m05C674E687B921C37722A6A1FF938FD56574642E((&V_3), NativeArray_1_Dispose_m05C674E687B921C37722A6A1FF938FD56574642E_RuntimeMethod_var);
		float4_t89D9A294E7A79BD81BFBDD18654508532958555E L_155 = V_0;
		return L_155;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ModuleHandle__cctor_m31191B19D351568AAFC535285B715AA49285D4E4 (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ModuleHandle_tF45CBC10412074A0AAEF63CBDAA5F92A4F7A9978_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		((ModuleHandle_tF45CBC10412074A0AAEF63CBDAA5F92A4F7A9978_StaticFields*)il2cpp_codegen_static_fields_for(ModuleHandle_tF45CBC10412074A0AAEF63CBDAA5F92A4F7A9978_il2cpp_TypeInfo_var))->___kMaxArea = ((int32_t)65536);
		((ModuleHandle_tF45CBC10412074A0AAEF63CBDAA5F92A4F7A9978_StaticFields*)il2cpp_codegen_static_fields_for(ModuleHandle_tF45CBC10412074A0AAEF63CBDAA5F92A4F7A9978_il2cpp_TypeInfo_var))->___kMaxEdgeCount = ((int32_t)65536);
		((ModuleHandle_tF45CBC10412074A0AAEF63CBDAA5F92A4F7A9978_StaticFields*)il2cpp_codegen_static_fields_for(ModuleHandle_tF45CBC10412074A0AAEF63CBDAA5F92A4F7A9978_il2cpp_TypeInfo_var))->___kMaxIndexCount = ((int32_t)65536);
		((ModuleHandle_tF45CBC10412074A0AAEF63CBDAA5F92A4F7A9978_StaticFields*)il2cpp_codegen_static_fields_for(ModuleHandle_tF45CBC10412074A0AAEF63CBDAA5F92A4F7A9978_il2cpp_TypeInfo_var))->___kMaxVertexCount = ((int32_t)65536);
		int32_t L_0 = ((ModuleHandle_tF45CBC10412074A0AAEF63CBDAA5F92A4F7A9978_StaticFields*)il2cpp_codegen_static_fields_for(ModuleHandle_tF45CBC10412074A0AAEF63CBDAA5F92A4F7A9978_il2cpp_TypeInfo_var))->___kMaxIndexCount;
		((ModuleHandle_tF45CBC10412074A0AAEF63CBDAA5F92A4F7A9978_StaticFields*)il2cpp_codegen_static_fields_for(ModuleHandle_tF45CBC10412074A0AAEF63CBDAA5F92A4F7A9978_il2cpp_TypeInfo_var))->___kMaxTriangleCount = ((int32_t)(L_0/3));
		((ModuleHandle_tF45CBC10412074A0AAEF63CBDAA5F92A4F7A9978_StaticFields*)il2cpp_codegen_static_fields_for(ModuleHandle_tF45CBC10412074A0AAEF63CBDAA5F92A4F7A9978_il2cpp_TypeInfo_var))->___kMaxRefineIterations = ((int32_t)48);
		((ModuleHandle_tF45CBC10412074A0AAEF63CBDAA5F92A4F7A9978_StaticFields*)il2cpp_codegen_static_fields_for(ModuleHandle_tF45CBC10412074A0AAEF63CBDAA5F92A4F7A9978_il2cpp_TypeInfo_var))->___kMaxSmoothenIterations = ((int32_t)256);
		((ModuleHandle_tF45CBC10412074A0AAEF63CBDAA5F92A4F7A9978_StaticFields*)il2cpp_codegen_static_fields_for(ModuleHandle_tF45CBC10412074A0AAEF63CBDAA5F92A4F7A9978_il2cpp_TypeInfo_var))->___kIncrementAreaFactor = (1.20000005f);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void __JobReflectionRegistrationOutput__2787129498_CreateJobReflectionData_mCA7D08807C4C5650B11C4359590829BF1377336B (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IJobExtensions_EarlyJobInit_TisDrawCallJob_t3EA2ABC822AD5DF50675A5B437DAB927DB95215D_m8175192716D38C9C006D2C98D5B36A7CC75E33AD_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IJobForExtensions_EarlyJobInit_TisLightMinMaxZJob_tB4FE0854445DAADF46E5511EAAF54EA1E4B611C4_m6D407EF2FD72EE5C26BA1C843C9932ABCB9C16DA_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IJobForExtensions_EarlyJobInit_TisReflectionProbeMinMaxZJob_tB55272F39D5B8B189F5DF7212CDA3FFF1EC0C71C_mE2595D2099394DF551AC793D9874D90167904DF2_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IJobForExtensions_EarlyJobInit_TisTileRangeExpansionJob_t8342AD91DCB87CA5DBDB463981EE24D47408C876_m7F3455814736950B257AF91E4AE26136F7D2D588_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IJobForExtensions_EarlyJobInit_TisTilingJob_t4506E6F62C95A90210A474DE43C83AF5EB8D3352_m4C05E9D5B6CBBFDCFF690B0C60C83CC47D299593_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IJobForExtensions_EarlyJobInit_TisZBinningJob_t9BC217C31924E66E667568C1B51EA2F44FA0A08E_mA5B015BCBE9BD211FDEFDAE5347DCBE7E91D1970_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IJobParallelForTransformExtensions_EarlyJobInit_TisUpdateTransformsJob_t7CF957169E8C6560084F48A51BC15A447F3002C7_mA2F79749E27788E6B6B0DA93086B08A0C553480E_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	il2cpp::utils::ExceptionSupportStack<RuntimeObject*, 1> __active_exceptions;
	try
	{
		IJobExtensions_EarlyJobInit_TisDrawCallJob_t3EA2ABC822AD5DF50675A5B437DAB927DB95215D_m8175192716D38C9C006D2C98D5B36A7CC75E33AD(IJobExtensions_EarlyJobInit_TisDrawCallJob_t3EA2ABC822AD5DF50675A5B437DAB927DB95215D_m8175192716D38C9C006D2C98D5B36A7CC75E33AD_RuntimeMethod_var);
		goto IL_001f;
	}
	catch(Il2CppExceptionWrapper& e)
	{
		if(il2cpp_codegen_class_is_assignable_from (((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&Exception_t_il2cpp_TypeInfo_var)), il2cpp_codegen_object_class(e.ex)))
		{
			IL2CPP_PUSH_ACTIVE_EXCEPTION(e.ex);
			goto CATCH_000a;
		}
		throw e;
	}

CATCH_000a:
	{
		Exception_t* L_0 = ((Exception_t*)IL2CPP_GET_ACTIVE_EXCEPTION(Exception_t*));;
		RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B L_1 = { reinterpret_cast<intptr_t> (((RuntimeType*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&DrawCallJob_t3EA2ABC822AD5DF50675A5B437DAB927DB95215D_0_0_0_var))) };
		il2cpp_codegen_runtime_class_init_inline(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&Type_t_il2cpp_TypeInfo_var)));
		Type_t* L_2;
		L_2 = Type_GetTypeFromHandle_m6062B81682F79A4D6DF2640692EE6D9987858C57(L_1, NULL);
		EarlyInitHelpers_JobReflectionDataCreationFailed_m385E2249E15484F8BCB1A1EA9BA9AFB7EB425109(L_0, L_2, NULL);
		IL2CPP_POP_ACTIVE_EXCEPTION(Exception_t*);
		goto IL_001f;
	}

IL_001f:
	{
	}
	try
	{
		IJobParallelForTransformExtensions_EarlyJobInit_TisUpdateTransformsJob_t7CF957169E8C6560084F48A51BC15A447F3002C7_mA2F79749E27788E6B6B0DA93086B08A0C553480E(IJobParallelForTransformExtensions_EarlyJobInit_TisUpdateTransformsJob_t7CF957169E8C6560084F48A51BC15A447F3002C7_mA2F79749E27788E6B6B0DA93086B08A0C553480E_RuntimeMethod_var);
		goto IL_003f;
	}
	catch(Il2CppExceptionWrapper& e)
	{
		if(il2cpp_codegen_class_is_assignable_from (((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&Exception_t_il2cpp_TypeInfo_var)), il2cpp_codegen_object_class(e.ex)))
		{
			IL2CPP_PUSH_ACTIVE_EXCEPTION(e.ex);
			goto CATCH_002a;
		}
		throw e;
	}

CATCH_002a:
	{
		Exception_t* L_3 = ((Exception_t*)IL2CPP_GET_ACTIVE_EXCEPTION(Exception_t*));;
		RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B L_4 = { reinterpret_cast<intptr_t> (((RuntimeType*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&UpdateTransformsJob_t7CF957169E8C6560084F48A51BC15A447F3002C7_0_0_0_var))) };
		il2cpp_codegen_runtime_class_init_inline(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&Type_t_il2cpp_TypeInfo_var)));
		Type_t* L_5;
		L_5 = Type_GetTypeFromHandle_m6062B81682F79A4D6DF2640692EE6D9987858C57(L_4, NULL);
		EarlyInitHelpers_JobReflectionDataCreationFailed_m385E2249E15484F8BCB1A1EA9BA9AFB7EB425109(L_3, L_5, NULL);
		IL2CPP_POP_ACTIVE_EXCEPTION(Exception_t*);
		goto IL_003f;
	}

IL_003f:
	{
	}
	try
	{
		IJobForExtensions_EarlyJobInit_TisLightMinMaxZJob_tB4FE0854445DAADF46E5511EAAF54EA1E4B611C4_m6D407EF2FD72EE5C26BA1C843C9932ABCB9C16DA(IJobForExtensions_EarlyJobInit_TisLightMinMaxZJob_tB4FE0854445DAADF46E5511EAAF54EA1E4B611C4_m6D407EF2FD72EE5C26BA1C843C9932ABCB9C16DA_RuntimeMethod_var);
		goto IL_005f;
	}
	catch(Il2CppExceptionWrapper& e)
	{
		if(il2cpp_codegen_class_is_assignable_from (((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&Exception_t_il2cpp_TypeInfo_var)), il2cpp_codegen_object_class(e.ex)))
		{
			IL2CPP_PUSH_ACTIVE_EXCEPTION(e.ex);
			goto CATCH_004a;
		}
		throw e;
	}

CATCH_004a:
	{
		Exception_t* L_6 = ((Exception_t*)IL2CPP_GET_ACTIVE_EXCEPTION(Exception_t*));;
		RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B L_7 = { reinterpret_cast<intptr_t> (((RuntimeType*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&LightMinMaxZJob_tB4FE0854445DAADF46E5511EAAF54EA1E4B611C4_0_0_0_var))) };
		il2cpp_codegen_runtime_class_init_inline(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&Type_t_il2cpp_TypeInfo_var)));
		Type_t* L_8;
		L_8 = Type_GetTypeFromHandle_m6062B81682F79A4D6DF2640692EE6D9987858C57(L_7, NULL);
		EarlyInitHelpers_JobReflectionDataCreationFailed_m385E2249E15484F8BCB1A1EA9BA9AFB7EB425109(L_6, L_8, NULL);
		IL2CPP_POP_ACTIVE_EXCEPTION(Exception_t*);
		goto IL_005f;
	}

IL_005f:
	{
	}
	try
	{
		IJobForExtensions_EarlyJobInit_TisReflectionProbeMinMaxZJob_tB55272F39D5B8B189F5DF7212CDA3FFF1EC0C71C_mE2595D2099394DF551AC793D9874D90167904DF2(IJobForExtensions_EarlyJobInit_TisReflectionProbeMinMaxZJob_tB55272F39D5B8B189F5DF7212CDA3FFF1EC0C71C_mE2595D2099394DF551AC793D9874D90167904DF2_RuntimeMethod_var);
		goto IL_007f;
	}
	catch(Il2CppExceptionWrapper& e)
	{
		if(il2cpp_codegen_class_is_assignable_from (((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&Exception_t_il2cpp_TypeInfo_var)), il2cpp_codegen_object_class(e.ex)))
		{
			IL2CPP_PUSH_ACTIVE_EXCEPTION(e.ex);
			goto CATCH_006a;
		}
		throw e;
	}

CATCH_006a:
	{
		Exception_t* L_9 = ((Exception_t*)IL2CPP_GET_ACTIVE_EXCEPTION(Exception_t*));;
		RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B L_10 = { reinterpret_cast<intptr_t> (((RuntimeType*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ReflectionProbeMinMaxZJob_tB55272F39D5B8B189F5DF7212CDA3FFF1EC0C71C_0_0_0_var))) };
		il2cpp_codegen_runtime_class_init_inline(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&Type_t_il2cpp_TypeInfo_var)));
		Type_t* L_11;
		L_11 = Type_GetTypeFromHandle_m6062B81682F79A4D6DF2640692EE6D9987858C57(L_10, NULL);
		EarlyInitHelpers_JobReflectionDataCreationFailed_m385E2249E15484F8BCB1A1EA9BA9AFB7EB425109(L_9, L_11, NULL);
		IL2CPP_POP_ACTIVE_EXCEPTION(Exception_t*);
		goto IL_007f;
	}

IL_007f:
	{
	}
	try
	{
		IJobForExtensions_EarlyJobInit_TisTileRangeExpansionJob_t8342AD91DCB87CA5DBDB463981EE24D47408C876_m7F3455814736950B257AF91E4AE26136F7D2D588(IJobForExtensions_EarlyJobInit_TisTileRangeExpansionJob_t8342AD91DCB87CA5DBDB463981EE24D47408C876_m7F3455814736950B257AF91E4AE26136F7D2D588_RuntimeMethod_var);
		goto IL_009f;
	}
	catch(Il2CppExceptionWrapper& e)
	{
		if(il2cpp_codegen_class_is_assignable_from (((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&Exception_t_il2cpp_TypeInfo_var)), il2cpp_codegen_object_class(e.ex)))
		{
			IL2CPP_PUSH_ACTIVE_EXCEPTION(e.ex);
			goto CATCH_008a;
		}
		throw e;
	}

CATCH_008a:
	{
		Exception_t* L_12 = ((Exception_t*)IL2CPP_GET_ACTIVE_EXCEPTION(Exception_t*));;
		RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B L_13 = { reinterpret_cast<intptr_t> (((RuntimeType*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&TileRangeExpansionJob_t8342AD91DCB87CA5DBDB463981EE24D47408C876_0_0_0_var))) };
		il2cpp_codegen_runtime_class_init_inline(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&Type_t_il2cpp_TypeInfo_var)));
		Type_t* L_14;
		L_14 = Type_GetTypeFromHandle_m6062B81682F79A4D6DF2640692EE6D9987858C57(L_13, NULL);
		EarlyInitHelpers_JobReflectionDataCreationFailed_m385E2249E15484F8BCB1A1EA9BA9AFB7EB425109(L_12, L_14, NULL);
		IL2CPP_POP_ACTIVE_EXCEPTION(Exception_t*);
		goto IL_009f;
	}

IL_009f:
	{
	}
	try
	{
		IJobForExtensions_EarlyJobInit_TisTilingJob_t4506E6F62C95A90210A474DE43C83AF5EB8D3352_m4C05E9D5B6CBBFDCFF690B0C60C83CC47D299593(IJobForExtensions_EarlyJobInit_TisTilingJob_t4506E6F62C95A90210A474DE43C83AF5EB8D3352_m4C05E9D5B6CBBFDCFF690B0C60C83CC47D299593_RuntimeMethod_var);
		goto IL_00bf;
	}
	catch(Il2CppExceptionWrapper& e)
	{
		if(il2cpp_codegen_class_is_assignable_from (((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&Exception_t_il2cpp_TypeInfo_var)), il2cpp_codegen_object_class(e.ex)))
		{
			IL2CPP_PUSH_ACTIVE_EXCEPTION(e.ex);
			goto CATCH_00aa;
		}
		throw e;
	}

CATCH_00aa:
	{
		Exception_t* L_15 = ((Exception_t*)IL2CPP_GET_ACTIVE_EXCEPTION(Exception_t*));;
		RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B L_16 = { reinterpret_cast<intptr_t> (((RuntimeType*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&TilingJob_t4506E6F62C95A90210A474DE43C83AF5EB8D3352_0_0_0_var))) };
		il2cpp_codegen_runtime_class_init_inline(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&Type_t_il2cpp_TypeInfo_var)));
		Type_t* L_17;
		L_17 = Type_GetTypeFromHandle_m6062B81682F79A4D6DF2640692EE6D9987858C57(L_16, NULL);
		EarlyInitHelpers_JobReflectionDataCreationFailed_m385E2249E15484F8BCB1A1EA9BA9AFB7EB425109(L_15, L_17, NULL);
		IL2CPP_POP_ACTIVE_EXCEPTION(Exception_t*);
		goto IL_00bf;
	}

IL_00bf:
	{
	}
	try
	{
		IJobForExtensions_EarlyJobInit_TisZBinningJob_t9BC217C31924E66E667568C1B51EA2F44FA0A08E_mA5B015BCBE9BD211FDEFDAE5347DCBE7E91D1970(IJobForExtensions_EarlyJobInit_TisZBinningJob_t9BC217C31924E66E667568C1B51EA2F44FA0A08E_mA5B015BCBE9BD211FDEFDAE5347DCBE7E91D1970_RuntimeMethod_var);
		goto IL_00df;
	}
	catch(Il2CppExceptionWrapper& e)
	{
		if(il2cpp_codegen_class_is_assignable_from (((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&Exception_t_il2cpp_TypeInfo_var)), il2cpp_codegen_object_class(e.ex)))
		{
			IL2CPP_PUSH_ACTIVE_EXCEPTION(e.ex);
			goto CATCH_00ca;
		}
		throw e;
	}

CATCH_00ca:
	{
		Exception_t* L_18 = ((Exception_t*)IL2CPP_GET_ACTIVE_EXCEPTION(Exception_t*));;
		RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B L_19 = { reinterpret_cast<intptr_t> (((RuntimeType*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ZBinningJob_t9BC217C31924E66E667568C1B51EA2F44FA0A08E_0_0_0_var))) };
		il2cpp_codegen_runtime_class_init_inline(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&Type_t_il2cpp_TypeInfo_var)));
		Type_t* L_20;
		L_20 = Type_GetTypeFromHandle_m6062B81682F79A4D6DF2640692EE6D9987858C57(L_19, NULL);
		EarlyInitHelpers_JobReflectionDataCreationFailed_m385E2249E15484F8BCB1A1EA9BA9AFB7EB425109(L_18, L_20, NULL);
		IL2CPP_POP_ACTIVE_EXCEPTION(Exception_t*);
		goto IL_00df;
	}

IL_00df:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void __JobReflectionRegistrationOutput__2787129498_EarlyInit_m34EA1BD5E7D9D9C817771577E28D0AF488499053 (const RuntimeMethod* method) 
{
	{
		__JobReflectionRegistrationOutput__2787129498_CreateJobReflectionData_mCA7D08807C4C5650B11C4359590829BF1377336B(NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float math_abs_m3D9508B36B045BFE7B89C6C69AD34596264E4FE1_inline (float ___0_x, const RuntimeMethod* method) 
{
	{
		float L_0 = ___0_x;
		uint32_t L_1;
		L_1 = math_asuint_m503D1ABF19E4BA615FD8AE1BF1A2E103BBED6139_inline(L_0, NULL);
		float L_2;
		L_2 = math_asfloat_m20D259DAAB46464B59BD8BF5678F9D59800F70A9_inline(((int32_t)((int32_t)L_1&((int32_t)2147483647LL))), NULL);
		return L_2;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR double math_abs_mDF669CF3AF2C60713E8E118578461CDA050DAFD0_inline (double ___0_x, const RuntimeMethod* method) 
{
	{
		double L_0 = ___0_x;
		uint64_t L_1;
		L_1 = math_asulong_m2CF160E23B5FF618A85C3C29B2FB1C000E40290F_inline(L_0, NULL);
		double L_2;
		L_2 = math_asdouble_m3E7BC790C743E67EA45476AECD6D2D9A9E62E4F2_inline(((int64_t)((int64_t)L_1&((int64_t)(std::numeric_limits<int64_t>::max)()))), NULL);
		return L_2;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void float2__ctor_m3D598E2C2D173DE852F3AB157502968261383C97_inline (float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA* __this, float ___0_x, float ___1_y, const RuntimeMethod* method) 
{
	{
		float L_0 = ___0_x;
		__this->___x = L_0;
		float L_1 = ___1_y;
		__this->___y = L_1;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float math_sqrt_mEF31DE7BD0179009683C5D7B0C58E6571B30CF4A_inline (float ___0_x, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Math_tEB65DE7CA8B083C412C969C92981C030865486CE_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		float L_0 = ___0_x;
		il2cpp_codegen_runtime_class_init_inline(Math_tEB65DE7CA8B083C412C969C92981C030865486CE_il2cpp_TypeInfo_var);
		double L_1;
		L_1 = sqrt(((double)((float)L_0)));
		return ((float)L_1);
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float math_distance_mE5E0FFDD103E710A4CB23360BFCAFD0AF2E1EFA9_inline (float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA ___0_x, float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA ___1_y, const RuntimeMethod* method) 
{
	{
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_0 = ___1_y;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_1 = ___0_x;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_2;
		L_2 = float2_op_Subtraction_m28172675A65BCFFBC8C9023BE815019E668B8380_inline(L_0, L_1, NULL);
		float L_3;
		L_3 = math_length_m3DB47D254C8544FBB740A892B4AE2143E8F45634_inline(L_2, NULL);
		return L_3;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void float3__ctor_mC61002CD0EC13D7C37D846D021A78C028FB80DB9_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* __this, float ___0_x, float ___1_y, float ___2_z, const RuntimeMethod* method) 
{
	{
		float L_0 = ___0_x;
		__this->___x = L_0;
		float L_1 = ___1_y;
		__this->___y = L_1;
		float L_2 = ___2_z;
		__this->___z = L_2;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E float3_op_Subtraction_mB6036E9849D95650D6E73DA0D179CD7B61E696F2_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_lhs, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_rhs, const RuntimeMethod* method) 
{
	{
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = ___0_lhs;
		float L_1 = L_0.___x;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_2 = ___1_rhs;
		float L_3 = L_2.___x;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_4 = ___0_lhs;
		float L_5 = L_4.___y;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_6 = ___1_rhs;
		float L_7 = L_6.___y;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_8 = ___0_lhs;
		float L_9 = L_8.___z;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_10 = ___1_rhs;
		float L_11 = L_10.___z;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_12;
		memset((&L_12), 0, sizeof(L_12));
		float3__ctor_mC61002CD0EC13D7C37D846D021A78C028FB80DB9_inline((&L_12), ((float)il2cpp_codegen_subtract(L_1, L_3)), ((float)il2cpp_codegen_subtract(L_5, L_7)), ((float)il2cpp_codegen_subtract(L_9, L_11)), NULL);
		return L_12;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E math_cross_m4CA2DAE150C6381B0D05E8AA9E48E88CF6157180_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_x, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_y, const RuntimeMethod* method) 
{
	float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = ___0_x;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_1;
		L_1 = float3_get_yzx_mDF6DE39B69C5DE384F74C0D1EC91AA0388E23535_inline((&___1_y), NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_2;
		L_2 = float3_op_Multiply_m05E57074FBD5FAB0E72940C9CC019C41915280D7_inline(L_0, L_1, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_3;
		L_3 = float3_get_yzx_mDF6DE39B69C5DE384F74C0D1EC91AA0388E23535_inline((&___0_x), NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_4 = ___1_y;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_5;
		L_5 = float3_op_Multiply_m05E57074FBD5FAB0E72940C9CC019C41915280D7_inline(L_3, L_4, NULL);
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_6;
		L_6 = float3_op_Subtraction_mB6036E9849D95650D6E73DA0D179CD7B61E696F2_inline(L_2, L_5, NULL);
		V_0 = L_6;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_7;
		L_7 = float3_get_yzx_mDF6DE39B69C5DE384F74C0D1EC91AA0388E23535_inline((&V_0), NULL);
		return L_7;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float math_dot_mF673D3E5B7D267C0A8569B678D05BDCCB667D04D_inline (float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA ___0_x, float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA ___1_y, const RuntimeMethod* method) 
{
	{
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_0 = ___0_x;
		float L_1 = L_0.___x;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_2 = ___1_y;
		float L_3 = L_2.___x;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_4 = ___0_x;
		float L_5 = L_4.___y;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_6 = ___1_y;
		float L_7 = L_6.___y;
		return ((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_1, L_3)), ((float)il2cpp_codegen_multiply(L_5, L_7))));
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float math_max_m4B454A91AE8827997609E74C4C24036BBD3CC496_inline (float ___0_x, float ___1_y, const RuntimeMethod* method) 
{
	{
		float L_0 = ___1_y;
		bool L_1;
		L_1 = Single_IsNaN_mFE637F6ECA9F7697CE8EFF56427858F4C5EDF75D_inline(L_0, NULL);
		if (L_1)
		{
			goto IL_000e;
		}
	}
	{
		float L_2 = ___0_x;
		float L_3 = ___1_y;
		if ((((float)L_2) > ((float)L_3)))
		{
			goto IL_000e;
		}
	}
	{
		float L_4 = ___1_y;
		return L_4;
	}

IL_000e:
	{
		float L_5 = ___0_x;
		return L_5;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float math_min_m54FD010BEF505D2BA1F79FC793BEB0723C329C3B_inline (float ___0_x, float ___1_y, const RuntimeMethod* method) 
{
	{
		float L_0 = ___1_y;
		bool L_1;
		L_1 = Single_IsNaN_mFE637F6ECA9F7697CE8EFF56427858F4C5EDF75D_inline(L_0, NULL);
		if (L_1)
		{
			goto IL_000e;
		}
	}
	{
		float L_2 = ___0_x;
		float L_3 = ___1_y;
		if ((((float)L_2) < ((float)L_3)))
		{
			goto IL_000e;
		}
	}
	{
		float L_4 = ___1_y;
		return L_4;
	}

IL_000e:
	{
		float L_5 = ___0_x;
		return L_5;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void int3__ctor_mE478318DE4CA648614FEF2C1DD438C0455284BF2_inline (int3_t1D01D28AA6D32890A228297EBADD9BB1A960E2BF* __this, int32_t ___0_x, int32_t ___1_y, int32_t ___2_z, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = ___0_x;
		__this->___x = L_0;
		int32_t L_1 = ___1_y;
		__this->___y = L_1;
		int32_t L_2 = ___2_z;
		__this->___z = L_2;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t math_min_m02D43DF516544C279AF660EA4731449C82991849_inline (int32_t ___0_x, int32_t ___1_y, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = ___0_x;
		int32_t L_1 = ___1_y;
		if ((((int32_t)L_0) < ((int32_t)L_1)))
		{
			goto IL_0006;
		}
	}
	{
		int32_t L_2 = ___1_y;
		return L_2;
	}

IL_0006:
	{
		int32_t L_3 = ___0_x;
		return L_3;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t math_max_m9083201D37A8ED0157B127B5878D9B7F3A2A40BE_inline (int32_t ___0_x, int32_t ___1_y, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = ___0_x;
		int32_t L_1 = ___1_y;
		if ((((int32_t)L_0) > ((int32_t)L_1)))
		{
			goto IL_0006;
		}
	}
	{
		int32_t L_2 = ___1_y;
		return L_2;
	}

IL_0006:
	{
		int32_t L_3 = ___0_x;
		return L_3;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void int4__ctor_m4E8D71A09721E26F7FCCE82EA8AD699062EE6216_inline (int4_tBA77D4945786DE82C3A487B33955EA1004996052* __this, int32_t ___0_x, int32_t ___1_y, int32_t ___2_z, int32_t ___3_w, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = ___0_x;
		__this->___x = L_0;
		int32_t L_1 = ___1_y;
		__this->___y = L_1;
		int32_t L_2 = ___2_z;
		__this->___z = L_2;
		int32_t L_3 = ___3_w;
		__this->___w = L_3;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA math_min_m68ED612C41E325FA3446050EA04D0AC0CD191558_inline (float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA ___0_x, float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA ___1_y, const RuntimeMethod* method) 
{
	{
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_0 = ___0_x;
		float L_1 = L_0.___x;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_2 = ___1_y;
		float L_3 = L_2.___x;
		float L_4;
		L_4 = math_min_m54FD010BEF505D2BA1F79FC793BEB0723C329C3B_inline(L_1, L_3, NULL);
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_5 = ___0_x;
		float L_6 = L_5.___y;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_7 = ___1_y;
		float L_8 = L_7.___y;
		float L_9;
		L_9 = math_min_m54FD010BEF505D2BA1F79FC793BEB0723C329C3B_inline(L_6, L_8, NULL);
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_10;
		memset((&L_10), 0, sizeof(L_10));
		float2__ctor_m3D598E2C2D173DE852F3AB157502968261383C97_inline((&L_10), L_4, L_9, NULL);
		return L_10;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA math_max_mFD64D6399932C2D91018BA7895C06FD055E1361B_inline (float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA ___0_x, float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA ___1_y, const RuntimeMethod* method) 
{
	{
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_0 = ___0_x;
		float L_1 = L_0.___x;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_2 = ___1_y;
		float L_3 = L_2.___x;
		float L_4;
		L_4 = math_max_m4B454A91AE8827997609E74C4C24036BBD3CC496_inline(L_1, L_3, NULL);
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_5 = ___0_x;
		float L_6 = L_5.___y;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_7 = ___1_y;
		float L_8 = L_7.___y;
		float L_9;
		L_9 = math_max_m4B454A91AE8827997609E74C4C24036BBD3CC496_inline(L_6, L_8, NULL);
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_10;
		memset((&L_10), 0, sizeof(L_10));
		float2__ctor_m3D598E2C2D173DE852F3AB157502968261383C97_inline((&L_10), L_4, L_9, NULL);
		return L_10;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA float2_op_Subtraction_m28172675A65BCFFBC8C9023BE815019E668B8380_inline (float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA ___0_lhs, float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA ___1_rhs, const RuntimeMethod* method) 
{
	{
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_0 = ___0_lhs;
		float L_1 = L_0.___x;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_2 = ___1_rhs;
		float L_3 = L_2.___x;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_4 = ___0_lhs;
		float L_5 = L_4.___y;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_6 = ___1_rhs;
		float L_7 = L_6.___y;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_8;
		memset((&L_8), 0, sizeof(L_8));
		float2__ctor_m3D598E2C2D173DE852F3AB157502968261383C97_inline((&L_8), ((float)il2cpp_codegen_subtract(L_1, L_3)), ((float)il2cpp_codegen_subtract(L_5, L_7)), NULL);
		return L_8;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA float2_op_Multiply_m34D03129CE0D7AD665A914DE83CB749585B2455F_inline (float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA ___0_lhs, float ___1_rhs, const RuntimeMethod* method) 
{
	{
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_0 = ___0_lhs;
		float L_1 = L_0.___x;
		float L_2 = ___1_rhs;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_3 = ___0_lhs;
		float L_4 = L_3.___y;
		float L_5 = ___1_rhs;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_6;
		memset((&L_6), 0, sizeof(L_6));
		float2__ctor_m3D598E2C2D173DE852F3AB157502968261383C97_inline((&L_6), ((float)il2cpp_codegen_multiply(L_1, L_2)), ((float)il2cpp_codegen_multiply(L_4, L_5)), NULL);
		return L_6;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void int2__ctor_m452D21510717D0961119C89A72BBB8D84DCD49F4_inline (int2_tF4AC25F87943DC0B2BB3456B0B919B3B42A9432A* __this, int32_t ___0_x, int32_t ___1_y, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = ___0_x;
		__this->___x = L_0;
		int32_t L_1 = ___1_y;
		__this->___y = L_1;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float math_lerp_m58A82DB48BBA11871FFA81583C700875B3A9BC84_inline (float ___0_x, float ___1_y, float ___2_s, const RuntimeMethod* method) 
{
	{
		float L_0 = ___0_x;
		float L_1 = ___2_s;
		float L_2 = ___1_y;
		float L_3 = ___0_x;
		return ((float)il2cpp_codegen_add(L_0, ((float)il2cpp_codegen_multiply(L_1, ((float)il2cpp_codegen_subtract(L_2, L_3))))));
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t math_clamp_m9EABD008C8EAD9D150062ABE724D96FA2121EE1C_inline (int32_t ___0_x, int32_t ___1_a, int32_t ___2_b, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = ___1_a;
		int32_t L_1 = ___2_b;
		int32_t L_2 = ___0_x;
		int32_t L_3;
		L_3 = math_min_m02D43DF516544C279AF660EA4731449C82991849_inline(L_1, L_2, NULL);
		int32_t L_4;
		L_4 = math_max_m9083201D37A8ED0157B127B5878D9B7F3A2A40BE_inline(L_0, L_3, NULL);
		return L_4;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint32_t math_asuint_m503D1ABF19E4BA615FD8AE1BF1A2E103BBED6139_inline (float ___0_x, const RuntimeMethod* method) 
{
	{
		float L_0 = ___0_x;
		int32_t L_1;
		L_1 = math_asint_mBDED7FE966CA65F6A8ACEAEF8FD779B1B8998288_inline(L_0, NULL);
		return L_1;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float math_asfloat_m20D259DAAB46464B59BD8BF5678F9D59800F70A9_inline (uint32_t ___0_x, const RuntimeMethod* method) 
{
	{
		uint32_t L_0 = ___0_x;
		float L_1;
		L_1 = math_asfloat_m9FA56DE5C61FCEF3DCD0675252D40DFD9C9B712F_inline(L_0, NULL);
		return L_1;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR uint64_t math_asulong_m2CF160E23B5FF618A85C3C29B2FB1C000E40290F_inline (double ___0_x, const RuntimeMethod* method) 
{
	{
		double L_0 = ___0_x;
		int64_t L_1;
		L_1 = math_aslong_mCD3846AC0EFB4901B00A20D0960C80C8CBE66366_inline(L_0, NULL);
		return L_1;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR double math_asdouble_m3E7BC790C743E67EA45476AECD6D2D9A9E62E4F2_inline (uint64_t ___0_x, const RuntimeMethod* method) 
{
	{
		uint64_t L_0 = ___0_x;
		double L_1;
		L_1 = math_asdouble_m4C4CC1B9299FE33530ED375768D67B00676C31C8_inline(L_0, NULL);
		return L_1;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float math_length_m3DB47D254C8544FBB740A892B4AE2143E8F45634_inline (float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA ___0_x, const RuntimeMethod* method) 
{
	{
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_0 = ___0_x;
		float2_t24AA5C0F612B0672315EDAFEC9D9E7F1C4A5B0BA L_1 = ___0_x;
		float L_2;
		L_2 = math_dot_mF673D3E5B7D267C0A8569B678D05BDCCB667D04D_inline(L_0, L_1, NULL);
		float L_3;
		L_3 = math_sqrt_mEF31DE7BD0179009683C5D7B0C58E6571B30CF4A_inline(L_2, NULL);
		return L_3;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E float3_get_yzx_mDF6DE39B69C5DE384F74C0D1EC91AA0388E23535_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E* __this, const RuntimeMethod* method) 
{
	{
		float L_0 = __this->___y;
		float L_1 = __this->___z;
		float L_2 = __this->___x;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_3;
		memset((&L_3), 0, sizeof(L_3));
		float3__ctor_mC61002CD0EC13D7C37D846D021A78C028FB80DB9_inline((&L_3), L_0, L_1, L_2, NULL);
		return L_3;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E float3_op_Multiply_m05E57074FBD5FAB0E72940C9CC019C41915280D7_inline (float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___0_lhs, float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E ___1_rhs, const RuntimeMethod* method) 
{
	{
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_0 = ___0_lhs;
		float L_1 = L_0.___x;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_2 = ___1_rhs;
		float L_3 = L_2.___x;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_4 = ___0_lhs;
		float L_5 = L_4.___y;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_6 = ___1_rhs;
		float L_7 = L_6.___y;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_8 = ___0_lhs;
		float L_9 = L_8.___z;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_10 = ___1_rhs;
		float L_11 = L_10.___z;
		float3_t4AB5D88249ADB24F69FFD0793E8ED25E1CC3745E L_12;
		memset((&L_12), 0, sizeof(L_12));
		float3__ctor_mC61002CD0EC13D7C37D846D021A78C028FB80DB9_inline((&L_12), ((float)il2cpp_codegen_multiply(L_1, L_3)), ((float)il2cpp_codegen_multiply(L_5, L_7)), ((float)il2cpp_codegen_multiply(L_9, L_11)), NULL);
		return L_12;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool Single_IsNaN_mFE637F6ECA9F7697CE8EFF56427858F4C5EDF75D_inline (float ___0_f, const RuntimeMethod* method) 
{
	{
		float L_0 = ___0_f;
		int32_t L_1;
		L_1 = BitConverter_SingleToInt32Bits_mC760C7CFC89725E3CF68DC45BE3A9A42A7E7DA73_inline(L_0, NULL);
		return (bool)((((int32_t)((int32_t)(L_1&((int32_t)2147483647LL)))) > ((int32_t)((int32_t)2139095040)))? 1 : 0);
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t math_asint_mBDED7FE966CA65F6A8ACEAEF8FD779B1B8998288_inline (float ___0_x, const RuntimeMethod* method) 
{
	IntFloatUnion_t549256A9DD754252DD18383D9CE7EA55EBBD6D96 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		(&V_0)->___intValue = 0;
		float L_0 = ___0_x;
		(&V_0)->___floatValue = L_0;
		IntFloatUnion_t549256A9DD754252DD18383D9CE7EA55EBBD6D96 L_1 = V_0;
		int32_t L_2 = L_1.___intValue;
		return L_2;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float math_asfloat_m9FA56DE5C61FCEF3DCD0675252D40DFD9C9B712F_inline (int32_t ___0_x, const RuntimeMethod* method) 
{
	IntFloatUnion_t549256A9DD754252DD18383D9CE7EA55EBBD6D96 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		(&V_0)->___floatValue = (0.0f);
		int32_t L_0 = ___0_x;
		(&V_0)->___intValue = L_0;
		IntFloatUnion_t549256A9DD754252DD18383D9CE7EA55EBBD6D96 L_1 = V_0;
		float L_2 = L_1.___floatValue;
		return L_2;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int64_t math_aslong_mCD3846AC0EFB4901B00A20D0960C80C8CBE66366_inline (double ___0_x, const RuntimeMethod* method) 
{
	LongDoubleUnion_tD71C400B6C4CD1A7F13CE8125AC6BBC7A22791CA V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		(&V_0)->___longValue = ((int64_t)0);
		double L_0 = ___0_x;
		(&V_0)->___doubleValue = L_0;
		LongDoubleUnion_tD71C400B6C4CD1A7F13CE8125AC6BBC7A22791CA L_1 = V_0;
		int64_t L_2 = L_1.___longValue;
		return L_2;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR double math_asdouble_m4C4CC1B9299FE33530ED375768D67B00676C31C8_inline (int64_t ___0_x, const RuntimeMethod* method) 
{
	LongDoubleUnion_tD71C400B6C4CD1A7F13CE8125AC6BBC7A22791CA V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		(&V_0)->___doubleValue = (0.0);
		int64_t L_0 = ___0_x;
		(&V_0)->___longValue = L_0;
		LongDoubleUnion_tD71C400B6C4CD1A7F13CE8125AC6BBC7A22791CA L_1 = V_0;
		double L_2 = L_1.___doubleValue;
		return L_2;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t BitConverter_SingleToInt32Bits_mC760C7CFC89725E3CF68DC45BE3A9A42A7E7DA73_inline (float ___0_value, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = *((int32_t*)((uintptr_t)(&___0_value)));
		return L_0;
	}
}

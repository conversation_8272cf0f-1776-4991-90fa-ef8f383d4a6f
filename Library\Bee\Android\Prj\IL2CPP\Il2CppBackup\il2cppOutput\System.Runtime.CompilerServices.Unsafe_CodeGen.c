﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void NonVersionableAttribute__ctor_m0ADAC9DE3EE89069011DA00E4AF296F598A6B91E (void);
static Il2CppMethodPointer s_methodPointers[3] = 
{
	NULL,
	NULL,
	NonVersionableAttribute__ctor_m0ADAC9DE3EE89069011DA00E4AF296F598A6B91E,
};
static const int32_t s_InvokerIndices[3] = 
{
	0,
	0,
	13298,
};
static const Il2CppTokenRangePair s_rgctxIndices[2] = 
{
	{ 0x06000001, { 0, 1 } },
	{ 0x06000002, { 1, 1 } },
};
extern const uint32_t g_rgctx_TU26_t62B21F9D8E7716028ACD2D6835A871D20B7FFD63;
extern const uint32_t g_rgctx_T_t0346E908023B5A0B19D2DE95E5285C02A44E4606;
static const Il2CppRGCTXDefinition s_rgctxValues[2] = 
{
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_t62B21F9D8E7716028ACD2D6835A871D20B7FFD63 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t0346E908023B5A0B19D2DE95E5285C02A44E4606 },
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_System_Runtime_CompilerServices_Unsafe_CodeGenModule;
const Il2CppCodeGenModule g_System_Runtime_CompilerServices_Unsafe_CodeGenModule = 
{
	"System.Runtime.CompilerServices.Unsafe.dll",
	3,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	2,
	s_rgctxIndices,
	2,
	s_rgctxValues,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};

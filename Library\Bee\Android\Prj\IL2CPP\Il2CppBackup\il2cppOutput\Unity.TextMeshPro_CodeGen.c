﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_m5C1A0B0AB85E71B4D5E08CF87ACA9DB2E22FC785 (void);
extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_m1D3940CE59232BC12EC287DAB6D6AA5171997E32 (void);
extern void FastAction_Add_m825D4C18BAB7721BE32B7097493D34F201BF81F3 (void);
extern void FastAction_Remove_mAF00AE428F0F047567E37925BBD4A5750ECD63D4 (void);
extern void FastAction_Call_mBAF2E050A7548718C6596AA230EBBD3945595298 (void);
extern void FastAction__ctor_mF3C5146C45DAE04B656283ED83FAF3F463D38F33 (void);
extern void MaterialReferenceManager_get_instance_m7B24E02E4C8E9EEBAB831C3F5E4DDFF5D46579B6 (void);
extern void MaterialReferenceManager_AddFontAsset_m6792FB2A583AFD91FF776D0D29737E723F38F039 (void);
extern void MaterialReferenceManager_AddFontAssetInternal_m8C0B7B9510BF49EBE2380A3E4D9CD3894616FA82 (void);
extern void MaterialReferenceManager_AddSpriteAsset_mA212A7706AAD427F665DC83BDE0D3DF9BD9DF907 (void);
extern void MaterialReferenceManager_AddSpriteAssetInternal_m237AFE73008A61174496142E1DE0AFE62BC54738 (void);
extern void MaterialReferenceManager_AddSpriteAsset_mD012F5C582F67AECA204F814452BBB3D1FB69E63 (void);
extern void MaterialReferenceManager_AddSpriteAssetInternal_m40F829BF9127F458984FD889E70D02474946D84F (void);
extern void MaterialReferenceManager_AddFontMaterial_mE3C0E0ABEDE58AC212AFD4CFE7938F234C70BBE9 (void);
extern void MaterialReferenceManager_AddFontMaterialInternal_mE7FAF4082935FBB50EA6F11931549BA723BA16D5 (void);
extern void MaterialReferenceManager_AddColorGradientPreset_m3BDD6F313678612D54E151D7DF901F43319CBCB5 (void);
extern void MaterialReferenceManager_AddColorGradientPreset_Internal_mB23BD54CD86FBEDB4100031E98CCB65C88750C0E (void);
extern void MaterialReferenceManager_Contains_m215CCF891A17BC708D981C9DA2BDC9A752BCCACE (void);
extern void MaterialReferenceManager_Contains_m835A31CDCAD096D2C93CC0DC91ED04A5450A5743 (void);
extern void MaterialReferenceManager_TryGetFontAsset_m2A3E5301004C96F262F336D554F64B1217D26231 (void);
extern void MaterialReferenceManager_TryGetFontAssetInternal_m2C38E5D98E644C0B43C350A1212BD1B981E435CC (void);
extern void MaterialReferenceManager_TryGetSpriteAsset_m9B41FCA12C297EAD46D171500B95C037C75A855F (void);
extern void MaterialReferenceManager_TryGetSpriteAssetInternal_mEB43000DBA4E428E3BC83ADE74B525E715211D1B (void);
extern void MaterialReferenceManager_TryGetColorGradientPreset_m874B43FD78065DFFD31E3A477AE686CD445504CE (void);
extern void MaterialReferenceManager_TryGetColorGradientPresetInternal_mC1D435676D1B9576A4F77CC2736D846FF40C592F (void);
extern void MaterialReferenceManager_TryGetMaterial_m24D3BA8401616B78412735D1E9206B77AB4A124E (void);
extern void MaterialReferenceManager_TryGetMaterialInternal_m023208FF7BBB5E5FFA086C1C07F64A2A92413DCB (void);
extern void MaterialReferenceManager__ctor_mDD3425577D03313636DFE375E377F289D0EA2295 (void);
extern void MaterialReference__ctor_m022ED9858AAD1DCEC25CBC4C304797F4539D87E7 (void);
extern void MaterialReference_Contains_m051E7238A73C56AE613307D840F185F56AF84B14 (void);
extern void MaterialReference_AddMaterialReference_mB50C19EBDE894D9F7BF7281A40BE052ABABC69BF (void);
extern void MaterialReference_AddMaterialReference_m10CD58333F42D11909FB7D396C51A4AE6707FE55 (void);
extern void TextContainer_get_hasChanged_m8DF8FF327827736E4729E37C1521F035B413C5F7 (void);
extern void TextContainer_set_hasChanged_mABA40DFD4EEC897F4A56B69CBB7A488D721C12DD (void);
extern void TextContainer_get_pivot_mABB55AD58B05D35BB0EB844B8CA9A837E4C95921 (void);
extern void TextContainer_set_pivot_mA8B07A0D65AB363D46CD27E42320059E8DA52EE5 (void);
extern void TextContainer_get_anchorPosition_m78B62F2ECB901D8B611131A213DEEF972790D1F7 (void);
extern void TextContainer_set_anchorPosition_mA915529616A0B4679FAAA6183FC194597E03EA50 (void);
extern void TextContainer_get_rect_m94D2400B7559ED8BD076F74B7AEB1CFF5ADDB6B5 (void);
extern void TextContainer_set_rect_m1D085EDA4A56F1B94CE17F373787634403015B77 (void);
extern void TextContainer_get_size_m0B22BF2C6CF43C2424C2864B87707D00E29B259B (void);
extern void TextContainer_set_size_m06C432F3136FCD30566702A5198587E4BE555A5C (void);
extern void TextContainer_get_width_mF4A385D6C76E375D1838E3586DFB0C7868508C1B (void);
extern void TextContainer_set_width_m6CCE1D33F804A90F452CF4E6F5E72E14FB040BCE (void);
extern void TextContainer_get_height_mF3302A1C468D314D85FE24DD4D09754232027C20 (void);
extern void TextContainer_set_height_mF4164F3ED8244480E64B7837B310B30388EEE6CD (void);
extern void TextContainer_get_isDefaultWidth_mC93E766339878C17E406E69A43EC9E0D6A7982C6 (void);
extern void TextContainer_get_isDefaultHeight_m3F66ACBFC5980F5CF466775067B1D38E55F208C6 (void);
extern void TextContainer_get_isAutoFitting_mDFC6D0A44C286CAD6DC53B5A7B6F6E38E1339382 (void);
extern void TextContainer_set_isAutoFitting_m3A9144590137287255F159FC93A1EAAD96B8141F (void);
extern void TextContainer_get_corners_mA9378A1DDF7C54BA2934885C9A21C71F8F778754 (void);
extern void TextContainer_get_worldCorners_m608A9D2C48FABA9361C0030238101F8121E98B12 (void);
extern void TextContainer_get_margins_mF4A4340460B6A5A271534AB50CA17026236E071A (void);
extern void TextContainer_set_margins_mC6A8F2814430CA0AAFF7985A27B4870AF736379B (void);
extern void TextContainer_get_rectTransform_mA715878DE6F7CB581CA2D82DA13FBC1A0BA14B5D (void);
extern void TextContainer_get_textMeshPro_m7FE78F97AF1550A7667BB6ED9BCF20DCCC3166A1 (void);
extern void TextContainer_Awake_m450AB495666750B5B2B9C20639C44C894F179B95 (void);
extern void TextContainer_OnEnable_mCB79CBA590B19D04274F74819286BD9A6E73EAA7 (void);
extern void TextContainer_OnDisable_m7A822FF8139DF70D91292638C7C737C647F5DEEB (void);
extern void TextContainer_OnContainerChanged_mCC9F83EC6FF3262388F26E17DA1C900ECE6156C0 (void);
extern void TextContainer_OnRectTransformDimensionsChange_mA9D29C6BAE685CE4C19365B0371E180365FAFAD4 (void);
extern void TextContainer_SetRect_mC61B8DA58B65D1D7EBE48F78E7AB80CD1FE34B87 (void);
extern void TextContainer_UpdateCorners_m3223B7904DD32545705F9B24FA52E13F8F198026 (void);
extern void TextContainer_GetPivot_m2AF9923059C0EDA822C235ABE88D11A86A9DC16D (void);
extern void TextContainer_GetAnchorPosition_mEB7F12AEDB37EC48501719D685FFC55B6998E2D8 (void);
extern void TextContainer__ctor_m31365F9C21AADAA5C2407613762F313663B62735 (void);
extern void TextContainer__cctor_m3776E1588AB9236443B6EC72F3A963AAF16B6DEE (void);
extern void TextMeshPro_get_sortingLayerID_m246B079B5004616673509A12006E1D46CE8F7DB5 (void);
extern void TextMeshPro_set_sortingLayerID_m4AFD807EC0960BE45C271C7F6B813884E27F98AC (void);
extern void TextMeshPro_get_sortingOrder_m8E5894C4390F4FDC0C7374C7EABAF96D37F0E089 (void);
extern void TextMeshPro_set_sortingOrder_mF4E75E33617F04E98CB5C529E0FB1E97D7473715 (void);
extern void TextMeshPro_get_autoSizeTextContainer_m92B636FDA452908D3752AA3DAACA7B5BDD81E73C (void);
extern void TextMeshPro_set_autoSizeTextContainer_m02CA26428A5BC9A4F5053C389560320DADECCA53 (void);
extern void TextMeshPro_get_textContainer_m9ADA73A2523843F13F731E47A0CE7E0EFB3BF771 (void);
extern void TextMeshPro_get_transform_m750148EC362B176A0E80D6F4ABAC1062E5281E11 (void);
extern void TextMeshPro_get_renderer_m2E657DD550DAB1C896B1D8955AE08F84FB9FE78E (void);
extern void TextMeshPro_get_mesh_m848BFE512069747B47E7FEF2EDFB6F6BF56A6F87 (void);
extern void TextMeshPro_get_meshFilter_m6A15037A8B675F9FB89F91A454DE6F49FE34A865 (void);
extern void TextMeshPro_get_maskType_mF1F98A216FDBE60BACABA283E32D4F9D4D884E04 (void);
extern void TextMeshPro_set_maskType_m218BA91F0BDEEAB2495D8A24BE494C29040F8AD5 (void);
extern void TextMeshPro_SetMask_m6F20ECB1EE60267C66C29C2BE2A4E8297023E10B (void);
extern void TextMeshPro_SetMask_mCD7144AD14B1E578DAC7062F1C4AFE3F91F097DB (void);
extern void TextMeshPro_SetVerticesDirty_mFF321A7A3250A1983437B651C6B08CA6C238E295 (void);
extern void TextMeshPro_SetLayoutDirty_mBB2707E3FB406BA91AE3CCF34300E4BD0EF13935 (void);
extern void TextMeshPro_SetMaterialDirty_m94E7469E10AE410A7E13BDAA52B635B886D2BF14 (void);
extern void TextMeshPro_SetAllDirty_mCA57F3D9977D6691FEAE9B42EC93188D665F9C45 (void);
extern void TextMeshPro_Rebuild_mF51AAF8F7475EDDD27EF62BE70C71E250C810CAC (void);
extern void TextMeshPro_UpdateMaterial_m5D5307EC06A3B37A7638B8474A89ACA8F59ED8AD (void);
extern void TextMeshPro_UpdateMeshPadding_mD4CCF7EA6D5E6EC61BC530EEA4C8A722DF21887C (void);
extern void TextMeshPro_ForceMeshUpdate_mA8196630B76CD7E10CD0CECD0E4ED263006EDF51 (void);
extern void TextMeshPro_GetTextInfo_m48DFA3AF6030E06CDCF41002C129A945DC1DC594 (void);
extern void TextMeshPro_ClearMesh_mD868C52676C08B2044509DB25E53500797C3E1EB (void);
extern void TextMeshPro_add_OnPreRenderText_m095FA48218C2A8F8ACB5673DF77EADCFFB2B8F75 (void);
extern void TextMeshPro_remove_OnPreRenderText_m832796A813128E6641DB46677DA21BFCF05D2E55 (void);
extern void TextMeshPro_UpdateGeometry_m07DBFC391FC1B0D663AF1D42BCEA4FE0FDAE96E9 (void);
extern void TextMeshPro_UpdateVertexData_mA83AC36F27C0D3A7977BE1E108F3A0763A32414C (void);
extern void TextMeshPro_UpdateVertexData_mA9E7A74B6EB347CFD3D351C1AC5406A0F29ACEC7 (void);
extern void TextMeshPro_UpdateFontAsset_mE940EA22C479BF14F099BDC5F9E89EEFEC59260F (void);
extern void TextMeshPro_CalculateLayoutInputHorizontal_m831878E1B35FA9625FC0E4D4FA94AB1D8417E7F0 (void);
extern void TextMeshPro_CalculateLayoutInputVertical_m9272D3BADA4804CF676D43F60B3758ED1EDDF510 (void);
extern void TextMeshPro_Awake_mB6744F75E7577F7A8EC8DC7277C2D98B855875B9 (void);
extern void TextMeshPro_OnEnable_m3E687CB93295674CBAD2CF98C10D048451181690 (void);
extern void TextMeshPro_OnDisable_m97F173D468229C917EC3A1B1D1A562D60C17C80E (void);
extern void TextMeshPro_OnDestroy_m85DA8AE90BB8E3A852CA4E4B6BF6676BC8F4617C (void);
extern void TextMeshPro_LoadFontAsset_m9FC6839F4EDA45629776E483BD4F121C5108F9DD (void);
extern void TextMeshPro_UpdateEnvMapMatrix_m85ECB9C9F9BE379982CE64FC814A36B338060E1F (void);
extern void TextMeshPro_SetMask_m88A4C3AB9A50C4A54E20030E6C13899A01CF8443 (void);
extern void TextMeshPro_SetMaskCoordinates_m892E763E92E5E5DED0EC610A16A4FCCDD447F40D (void);
extern void TextMeshPro_SetMaskCoordinates_m7CB4750ABC78278B40DE77342ABD2D02D9F931BC (void);
extern void TextMeshPro_EnableMasking_mAA4AD6CD94E45D033817486638BD4F0ED6B1E056 (void);
extern void TextMeshPro_DisableMasking_m51B9768DECE33C8E84FE0BEC40AC1A872C3A93FE (void);
extern void TextMeshPro_UpdateMask_m0DC0BD7B23520E3980306B01C17D21C2E53FD476 (void);
extern void TextMeshPro_GetMaterial_m7EE039E42CD673DCCF3C7EA8C334842FAB2A358F (void);
extern void TextMeshPro_GetMaterials_m106D2B8EA87AFCEDA5889C3ED9D2173E4521DEAC (void);
extern void TextMeshPro_SetSharedMaterial_m73380C05E2FF59D1A5C1563541F1340988D2AEC8 (void);
extern void TextMeshPro_GetSharedMaterials_m9F4B35F7947C0564074B9B3CB36EC70CEAF6FB52 (void);
extern void TextMeshPro_SetSharedMaterials_m0FB019F8C55D0A5D5812AD500FC74182F862DCAB (void);
extern void TextMeshPro_SetOutlineThickness_mEF32508CB6771185D243E2F04BABA73B9B45226B (void);
extern void TextMeshPro_SetFaceColor_m19E6DB372523664B314BBF8A3B0FA7BBE7C52BF7 (void);
extern void TextMeshPro_SetOutlineColor_mB71099D9A168FB2956E9983E324DC76ECC5E7C5D (void);
extern void TextMeshPro_CreateMaterialInstance_m60E579A5964E977F66A0E7C56BB7FA86D1780094 (void);
extern void TextMeshPro_SetShaderDepth_mBA26AEBB2FD4040C02A10E74A5CB8CA5697CA781 (void);
extern void TextMeshPro_SetCulling_mA51D5BAD4DEA2A00C8AC7388A3277161CBCF1C20 (void);
extern void TextMeshPro_SetPerspectiveCorrection_m6AF6ECDB7FCC9E45F4AC1AF6B0F76EEF1FEDF827 (void);
extern void TextMeshPro_SetArraySizes_mA31A609B7368CFB3D7A42F67DE6F3E450F11D0EC (void);
extern void TextMeshPro_ComputeMarginSize_mF595014F36FD313F4552F91FCCEB32B6CF38275D (void);
extern void TextMeshPro_OnDidApplyAnimationProperties_m3951CDAD2B420D110042E6F2E4C8F9DB641AC652 (void);
extern void TextMeshPro_OnTransformParentChanged_m13B32E506A0BD3041D6024988C029386EF65C82E (void);
extern void TextMeshPro_OnRectTransformDimensionsChange_m54E70A1F06C19D4A8CEF6EB8A3A130ACD45F7EB2 (void);
extern void TextMeshPro_InternalUpdate_mACA7A0E963E587E3678586D0AF79A14EF7D65A20 (void);
extern void TextMeshPro_OnPreRenderObject_mEDEAC1CC6D8C7AFEA948F713910E910F8FE301C1 (void);
extern void TextMeshPro_GenerateTextMesh_m92A3D91D20D8BB4E46C2E9E305EE4207B1B2A1DD (void);
extern void TextMeshPro_GetTextContainerLocalCorners_m28DCECBF011AEEE457382B1ACF989366B7E51844 (void);
extern void TextMeshPro_SetMeshFilters_m8F9CE41507555B248CBB176E3E19B905AF497BAB (void);
extern void TextMeshPro_SetActiveSubMeshes_mD13EDA99249DB1756900267DFD6A3D1D72FBADAA (void);
extern void TextMeshPro_SetActiveSubTextObjectRenderers_m84A606F6A82904D9EB5F92DAB05E1D7AF725CB6A (void);
extern void TextMeshPro_DestroySubMeshObjects_mD556C6CAB0FD455BC8BE7390BEEAF6C39B0873DE (void);
extern void TextMeshPro_UpdateSubMeshSortingLayerID_m63C46E43CFC2E1FEE48D6B48170DF3267E4BDF11 (void);
extern void TextMeshPro_UpdateSubMeshSortingOrder_mDD41BBD341BA493B30022CC8930025BEB85070EC (void);
extern void TextMeshPro_GetCompoundBounds_m260ABC99A7A361F587BA40E99CE77B0F4402DFA9 (void);
extern void TextMeshPro_UpdateSDFScale_m83845259E62DB13544143A11EF2089E03BE0D96C (void);
extern void TextMeshPro__ctor_mF12CEBF1FB86BE922890B59AD288B3F3BFC4AB0A (void);
extern void TextMeshPro__cctor_mD1C3870C44CE1F6FAD0CB5BAE861619017F60C34 (void);
extern void TextMeshProUGUI_get_materialForRendering_mCB8AB4D6211E94B4A910E547A435BEDC8B5AA483 (void);
extern void TextMeshProUGUI_get_autoSizeTextContainer_mFB1F26E61C6D873940BE188D1E8B8C35324237C2 (void);
extern void TextMeshProUGUI_set_autoSizeTextContainer_m2E04DBDF79EF2A70A84DC27204410F642A8F7E57 (void);
extern void TextMeshProUGUI_get_mesh_m9D7580B03E695D83EE9A7DB4B63CE0C570B4F404 (void);
extern void TextMeshProUGUI_get_canvasRenderer_m74291CFD17F47B70F21F49C93058D2756A521887 (void);
extern void TextMeshProUGUI_CalculateLayoutInputHorizontal_m96BF3C6C61F9B47F1C984A85921266E188151198 (void);
extern void TextMeshProUGUI_CalculateLayoutInputVertical_m478DA445BEBEFD266F26B53B172A415F11EA0D4E (void);
extern void TextMeshProUGUI_SetVerticesDirty_m08925B150A0C61A9E62D6312094BEE4F2A085B35 (void);
extern void TextMeshProUGUI_SetLayoutDirty_m7B98DD726DC1F539A829660218629BDCBC5D6EFE (void);
extern void TextMeshProUGUI_SetMaterialDirty_m34D20F8ED0CEF3D4F335BA53CDFBA22D3638E24F (void);
extern void TextMeshProUGUI_SetAllDirty_mBCABE23045A171B70D7C8AA1E9D0E197B24A9921 (void);
extern void TextMeshProUGUI_DelayedGraphicRebuild_m4FD11E5C4B9F5E1DFE4D361C78CDC849F7D53F8E (void);
extern void TextMeshProUGUI_DelayedMaterialRebuild_m3D156CF49F99C2239E74AEC027473359A715397E (void);
extern void TextMeshProUGUI_Rebuild_m81A0BB63EF4D157FB9DF14184DF198DAEC24A50D (void);
extern void TextMeshProUGUI_UpdateSubObjectPivot_mF5A9ADE5A239AFC6C34BFC02FDE622DE14EAAF41 (void);
extern void TextMeshProUGUI_GetModifiedMaterial_mF485112E8CD8B5466BC6F3F5C9EBB195B9DED181 (void);
extern void TextMeshProUGUI_UpdateMaterial_m9342D18203EC31AF43B1F2FD179409825C9E246A (void);
extern void TextMeshProUGUI_get_maskOffset_m8B38AEB61B68ED587044250886FB3C3E6327711E (void);
extern void TextMeshProUGUI_set_maskOffset_m4556A74663F0603718E533AD6469D5D2062D5814 (void);
extern void TextMeshProUGUI_RecalculateClipping_m6D9CD29782F2306FA411BE914F45C6E02D2438BA (void);
extern void TextMeshProUGUI_Cull_m28722BE62117D8666B334C1D98B0FDA31C801297 (void);
extern void TextMeshProUGUI_UpdateCulling_mBBCAFB8FF0E605AA916782D723F605C7C5790E0F (void);
extern void TextMeshProUGUI_UpdateMeshPadding_mAD9B5590BB1C07BD9F26F9ACF31E3AD7D0375D52 (void);
extern void TextMeshProUGUI_InternalCrossFadeColor_mCAC46A6435D3D73F8138A4DF4125AF63D6197E93 (void);
extern void TextMeshProUGUI_InternalCrossFadeAlpha_m8F1403C1E5A7F3CC52DF054A11189D8E14353C63 (void);
extern void TextMeshProUGUI_ForceMeshUpdate_mB5BCD2205BC620DC7AC4F00D7F21104D65FB90DE (void);
extern void TextMeshProUGUI_GetTextInfo_m0DF7D8487C0203268A4C62A67E1C4B7589ACC17E (void);
extern void TextMeshProUGUI_ClearMesh_mE803C85017AAA2E8F6C47661A7F59078C83D7D73 (void);
extern void TextMeshProUGUI_add_OnPreRenderText_m1F3D640166C47D5E8AC83392022A7A76F252882D (void);
extern void TextMeshProUGUI_remove_OnPreRenderText_m5580AE95B7FCDF054DCB9F30D545CD60B5F40049 (void);
extern void TextMeshProUGUI_UpdateGeometry_m7877CE2E695637CFD480AF6D3A144A96F4B8DEAD (void);
extern void TextMeshProUGUI_UpdateVertexData_mAFE22C7EF036C51A5DABECD97AA3294963B17B8A (void);
extern void TextMeshProUGUI_UpdateVertexData_mDCD835F9897D4A1D1C3299F8610E10AAF9693621 (void);
extern void TextMeshProUGUI_UpdateFontAsset_m5A9C9C239F67AD02E0EE52BDDBBCA6B747AD5B3B (void);
extern void TextMeshProUGUI_Awake_mE225DF2C3511625374A16201302F4A8AD2A72B26 (void);
extern void TextMeshProUGUI_OnEnable_m192C5020191F6DE0CDFD8D91BB1518496E841E98 (void);
extern void TextMeshProUGUI_OnDisable_m55A5E45BD3538FECB646D245F23223C33C9C6CEE (void);
extern void TextMeshProUGUI_OnDestroy_mF6F5098912944CBAEE8F9162CBC8513B6FCB4DCB (void);
extern void TextMeshProUGUI_LoadFontAsset_mD79574232A722B157F79A7D1430115829107B0D9 (void);
extern void TextMeshProUGUI_GetCanvas_mA62DCDC49EF734C76E2DCC33373AE38485267B07 (void);
extern void TextMeshProUGUI_UpdateEnvMapMatrix_mBB9F3DBD385A9CF38674F27B1265FEF0214A3BDB (void);
extern void TextMeshProUGUI_EnableMasking_m825DABEA53C5743CE0B4D896476BCB8B4D23CCA5 (void);
extern void TextMeshProUGUI_DisableMasking_m00E17B3141C80A2A15A8CF42FB9919563FBC9317 (void);
extern void TextMeshProUGUI_UpdateMask_m35565EC9D9C802C94BE4694B5B340B2B4577EF44 (void);
extern void TextMeshProUGUI_GetMaterial_m2A3E0BA6B812DEF61F0FB90E9FC0D6C4BDCEBA0A (void);
extern void TextMeshProUGUI_GetMaterials_m4C0D6622FD2BD30E4CE5F868F1CFDB64C1C1403D (void);
extern void TextMeshProUGUI_SetSharedMaterial_m9F64130FA72B17A58824E3A40980C92013F4E0EB (void);
extern void TextMeshProUGUI_GetSharedMaterials_mFC9F284B9D0F7588F7FBDCB8C98B221DCF8660F6 (void);
extern void TextMeshProUGUI_SetSharedMaterials_m0E33EE522DBE0E2BA8464C455A9663327F9E34ED (void);
extern void TextMeshProUGUI_SetOutlineThickness_m1819530A5C28F978C80D6BA614595D900099D897 (void);
extern void TextMeshProUGUI_SetFaceColor_m6A655C7FF042096CC71D943BCAFB98A42036EE1B (void);
extern void TextMeshProUGUI_SetOutlineColor_mFCA7A6D7F2BA52DB12239F9051D2BDF40F92ADA8 (void);
extern void TextMeshProUGUI_SetShaderDepth_m7A017149B97BAD6B9618750C0976729BD4740A70 (void);
extern void TextMeshProUGUI_SetCulling_m62AF94911CBF89A8ADDA8A21CEE391ED756360EF (void);
extern void TextMeshProUGUI_SetPerspectiveCorrection_mF547B8584EC083BAE1D3EFBD22CC5D9D78EDC48F (void);
extern void TextMeshProUGUI_SetMeshArrays_mBB3809698124A9B2B65A4F57954CEFA790568C2A (void);
extern void TextMeshProUGUI_SetArraySizes_mAE0F66585903F33A1056D130213B5680D0951D42 (void);
extern void TextMeshProUGUI_ComputeMarginSize_m0647B2989C0A1098FFCCB9EA66810DAAE6C2E0D5 (void);
extern void TextMeshProUGUI_OnDidApplyAnimationProperties_m8ADE42C6DD519A08609CB526A983561DBACB7266 (void);
extern void TextMeshProUGUI_OnCanvasHierarchyChanged_mDF06442735F795DA1A2CEBF42542EF352A0AC2D4 (void);
extern void TextMeshProUGUI_OnTransformParentChanged_m7B078BE5B1E69D3CA642D32F15204E7A7D2D7825 (void);
extern void TextMeshProUGUI_OnRectTransformDimensionsChange_mE7B272E5EF1538872749F01C82CD28D4352F37FC (void);
extern void TextMeshProUGUI_InternalUpdate_mEA820FAC0E9C659AC8198ABD63AC8515FC2EEC75 (void);
extern void TextMeshProUGUI_OnPreRenderCanvas_mD208EA1BBA9D3A10B255322BABED2056A83BA2FC (void);
extern void TextMeshProUGUI_GenerateTextMesh_m542C2DC0CA489C10BB3371B0114CD9ACED4E97D3 (void);
extern void TextMeshProUGUI_GetTextContainerLocalCorners_m2D6D185BC85A20431398BEC3EC258EBBD7D7C6AE (void);
extern void TextMeshProUGUI_SetActiveSubMeshes_m85AACB4166A13ACDC7E0F944BD37E1C771A90716 (void);
extern void TextMeshProUGUI_DestroySubMeshObjects_mBE2D8860515389A8AC3FDC37D184B03E38186F23 (void);
extern void TextMeshProUGUI_GetCompoundBounds_m74366E456D195D8D2FEBB1E85D0FE5DCE4105DF0 (void);
extern void TextMeshProUGUI_GetCanvasSpaceClippingRect_m1E7125B754E5B3935318B425847952E705DD5B6F (void);
extern void TextMeshProUGUI_UpdateSDFScale_m357367AFC5B9A2EBD11FA3C4D7FC99C5208044AE (void);
extern void TextMeshProUGUI__ctor_m3733FC98975BAD7C96BA932B8BE6A63602BA83B3 (void);
extern void TextMeshProUGUI__cctor_mBE116F1939402BD5AFB61FF61EFD35EDE6F5A9B9 (void);
extern void U3CDelayedGraphicRebuildU3Ed__18__ctor_mCAB88DA6910047EF7A9FA188F4CF1F1EA42A858B (void);
extern void U3CDelayedGraphicRebuildU3Ed__18_System_IDisposable_Dispose_m4BEB7CF8BAD78596B1C1FC7359A791492F8EA33F (void);
extern void U3CDelayedGraphicRebuildU3Ed__18_MoveNext_m0B62B0E98E2F3A3DA9A316759B3D76181ED512AF (void);
extern void U3CDelayedGraphicRebuildU3Ed__18_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m7A1891BD230605BA6BA156CAE8857BD6413AFEAA (void);
extern void U3CDelayedGraphicRebuildU3Ed__18_System_Collections_IEnumerator_Reset_m251AEA2015F485A8BA72E4B5ED787AE5224DC4DE (void);
extern void U3CDelayedGraphicRebuildU3Ed__18_System_Collections_IEnumerator_get_Current_m87FE04FA504B5B9B9606E2447830CBD06986DFF8 (void);
extern void U3CDelayedMaterialRebuildU3Ed__19__ctor_m6351B5737E3B2ADA60C28688744DE4FA2541606E (void);
extern void U3CDelayedMaterialRebuildU3Ed__19_System_IDisposable_Dispose_m2EF3A18F3B761153309EDC6D2AE1C3457515C1CF (void);
extern void U3CDelayedMaterialRebuildU3Ed__19_MoveNext_m9F6CEA5597DFFDA1AF6344B784640145736FFD68 (void);
extern void U3CDelayedMaterialRebuildU3Ed__19_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mBCB103321A5F41C5119C356DF074BBEE78D2A19E (void);
extern void U3CDelayedMaterialRebuildU3Ed__19_System_Collections_IEnumerator_Reset_m601E654D1F099331AA189E94AB46DE1A332A2559 (void);
extern void U3CDelayedMaterialRebuildU3Ed__19_System_Collections_IEnumerator_get_Current_m81AC91995AC85EEE6EC8E360A3D6D1BA49EF9714 (void);
extern void TMPro_EventManager_ON_MATERIAL_PROPERTY_CHANGED_mA3B9310945A5CBD70197154F55F91495E24255D2 (void);
extern void TMPro_EventManager_ON_FONT_PROPERTY_CHANGED_mDD93FA81065725C1F7AED35667D48F13FC2D9DC9 (void);
extern void TMPro_EventManager_ON_SPRITE_ASSET_PROPERTY_CHANGED_m9A90D8500F7EF60AE7D3FF22985EF09B3DF02A98 (void);
extern void TMPro_EventManager_ON_TEXTMESHPRO_PROPERTY_CHANGED_m9A254BD37DED765A10C03417DE5DA8AC63455CAD (void);
extern void TMPro_EventManager_ON_DRAG_AND_DROP_MATERIAL_CHANGED_mC43C0BD630FB674BDF3363C50F46E5779B7BF6D4 (void);
extern void TMPro_EventManager_ON_TEXT_STYLE_PROPERTY_CHANGED_m48AF453FABAF93571CF18FFFFA43B1AB21192F91 (void);
extern void TMPro_EventManager_ON_COLOR_GRADIENT_PROPERTY_CHANGED_m533FFD466019FC48A0F170854D2CB26F2284B340 (void);
extern void TMPro_EventManager_ON_TEXT_CHANGED_mC933ED67F225E90E2F7B5F0D176D62D3A560BFAE (void);
extern void TMPro_EventManager_ON_TMP_SETTINGS_CHANGED_m6BF9B1490D1602D1F5837EA29954272B92539050 (void);
extern void TMPro_EventManager_ON_RESOURCES_LOADED_m168B6EEE1214E3D17D1B450F871456BEACC127B2 (void);
extern void TMPro_EventManager_ON_TEXTMESHPRO_UGUI_PROPERTY_CHANGED_m6AC5515A817163122EF62E17CEA63E8D881F8B9B (void);
extern void TMPro_EventManager_ON_COMPUTE_DT_EVENT_m38F4534680AED9689989F220DE438B81BFA13714 (void);
extern void TMPro_EventManager__cctor_mAA44D45F96295F60D3407910459559C917ED1596 (void);
extern void Compute_DT_EventArgs__ctor_mFB724195485EB6D002C77F0B2262A5F94835A28D (void);
extern void Compute_DT_EventArgs__ctor_m9834D9D98042FC8AC11B9B6A834BFEDB3ED2BFFF (void);
extern void TMPro_ExtensionMethods_ToIntArray_mAFB2830378327380013F520489E8B20F5A93C0AC (void);
extern void TMPro_ExtensionMethods_ArrayToString_mCCFBF0B40F958253FC75EE46917D436BAE1D757C (void);
extern void TMPro_ExtensionMethods_IntToString_m20E167DC69EAF4F15BA6E5C95AA9151C0712FCF8 (void);
extern void TMPro_ExtensionMethods_UintToString_mCE4BA004D9FC332F4A359EC4BE2E97C81FC3A8A8 (void);
extern void TMPro_ExtensionMethods_IntToString_m7D01EB4E4B554DB3B29BD1701E972EFBD0A9424A (void);
extern void TMPro_ExtensionMethods_Compare_m1838CE0635EC60A2288FA34D81634A7F808DE370 (void);
extern void TMPro_ExtensionMethods_CompareRGB_m7CC49D7549E748CEC20F6EC84A1094084150F33A (void);
extern void TMPro_ExtensionMethods_Compare_mA56784B99E3F3FDC0C2C32BFDF99F77E309230F6 (void);
extern void TMPro_ExtensionMethods_CompareRGB_m3EEC2B9EB0B59134AB15C55568C54A78A06C954E (void);
extern void TMPro_ExtensionMethods_Multiply_m110996D0A26FD6BB8231C5BFA1913F01AFDB8BAB (void);
extern void TMPro_ExtensionMethods_Tint_mF3595B5B4DF616CE03A79DE5BB4EB55531FB56D5 (void);
extern void TMPro_ExtensionMethods_Tint_m6B681898DA88005A3CA7B1450849F44AD6991159 (void);
extern void TMPro_ExtensionMethods_MinAlpha_mBDF86191325DE876306DFADE5EB6A27A5DB5F1CE (void);
extern void TMPro_ExtensionMethods_Compare_mC71D1D722A3BEB91D0F47B71514A634AF72D6880 (void);
extern void TMPro_ExtensionMethods_Compare_m1BA0218FAED74AC8BB89E9F58D6CFF5D4BE1FCB0 (void);
extern void TMP_Math_Approximately_m58958A2D9DB66040360C17A460E0CA35F705EA2F (void);
extern void TMP_Math_Mod_m0BCF68D3977E3B9CABEB8845C8C82746BA4DABF1 (void);
extern void TMP_Math__cctor_mF2F11F0FFA33F4CE827C505FE2D48832F4044090 (void);
extern void VertexGradient__ctor_m9B59D99E8B67833BD6CC50F4704614744D271C3A (void);
extern void VertexGradient__ctor_m8FFAAFD98D0DC4F7C6D41410EF574A6600DCA40B (void);
extern void TMP_LinkInfo_SetLinkID_m9E9A1B09A536609EC636A3F6D14498F70C6C487A (void);
extern void TMP_LinkInfo_GetLinkText_m954EE8FF39D62BA8113773A696095EAE85CD5E3F (void);
extern void TMP_LinkInfo_GetLinkID_mCC9D9E783D606660A4D15E0E746E1E27AD9C2425 (void);
extern void TMP_WordInfo_GetWord_m7F72AB87E8AB0FA75616FD5409A8F5C031294D2C (void);
extern void Extents__ctor_m2C44BA0B2EDAAB80829698A019D2BBF8DDFF630B (void);
extern void Extents_ToString_m947E31139C9C66B1F1942EF74B2FDE347A778F99 (void);
extern void Extents__cctor_mF148593F1B6B500BFC7C4D3823E2B7A81455379E (void);
extern void Mesh_Extents__ctor_m37E0BBEE5EED57082B82AC6162F7785B231907CB (void);
extern void Mesh_Extents_ToString_m733F0275FC20FD2F4EE5B6E9ABBD6F3CD8D5AE23 (void);
extern void TMP_Asset_get_instanceID_mD7D5D79979B77457C3A376955C316AC289BB3D1D (void);
extern void TMP_Asset__ctor_m12FF90A96AD41AEDF9AD37175E7070FAC070D8E9 (void);
extern void TMP_Character__ctor_m16CBCDD3EB5695396E4C95A0876F2C4DD4500673 (void);
extern void TMP_Character__ctor_m79F49FBBC8657569BA621EFBF64A000BB1B56ED3 (void);
extern void TMP_Character__ctor_mAF0A895127F27795D515FF97C84185A01EFDAB0D (void);
extern void TMP_Character__ctor_m7019F2E56EE1A654151F581F2293FBB2410E4775 (void);
extern void TMP_Vertex_get_zero_m8350B6247D50962DFA3B8396E97D4F854A4C5D3D (void);
extern void TMP_Vertex__cctor_m60A31A768302204B848462AD7AE51680AAB0DA61 (void);
extern void TMP_Offset_get_left_mCDC93F42B720817E1119AA5360962F038A39E044 (void);
extern void TMP_Offset_set_left_m6FF3BCE823654D55CC03B9202094E129A3891958 (void);
extern void TMP_Offset_get_right_m268492C5D14D1239A429A152ED04DD8790EC98C4 (void);
extern void TMP_Offset_set_right_m19952C4778E73F559E8229B5D1438766E4FF62F2 (void);
extern void TMP_Offset_get_top_m5BAE1A688A264A63524AD4C456CE88CB2086105E (void);
extern void TMP_Offset_set_top_m5346213516D5B378349B70D61C4EE6BB25603DCC (void);
extern void TMP_Offset_get_bottom_m71E985879E87F76BE28A0FB0485F279866279845 (void);
extern void TMP_Offset_set_bottom_m4FF1AE55CF113FD06678B22A8ED029F17A9019A8 (void);
extern void TMP_Offset_get_horizontal_m3BE3663354670CEA3945FD6EC7C6FD1A3F4E81F7 (void);
extern void TMP_Offset_set_horizontal_m38C3B111DD01790C98E91423FCEF6BE826826891 (void);
extern void TMP_Offset_get_vertical_mB6681568C4F9B09DCE4CBFFDC33F733DE7EFE149 (void);
extern void TMP_Offset_set_vertical_m16CC389B6E1291EA006498F0E739A05A5CD16ABE (void);
extern void TMP_Offset_get_zero_m8D8E8D2E46EAB0DFFED647AC5EEB41A5B2AA2339 (void);
extern void TMP_Offset__ctor_mE88A176987DB6F468CA09553D74E86E1B48AA81C (void);
extern void TMP_Offset__ctor_m5C1836C5751505F6A9E674C8CD7A6419F4BFDCA0 (void);
extern void TMP_Offset_op_Equality_m3D2451105415FA35C1E9CA5DE8C4C246094E1CA5 (void);
extern void TMP_Offset_op_Inequality_m6B6DDC71D8DA2341BB06316615A3A3C66A4BE31E (void);
extern void TMP_Offset_op_Multiply_mC618A5520464FC68B05E5B08985D3FA94204DF75 (void);
extern void TMP_Offset_GetHashCode_mD43DEA54E08FF70C12AAB7FD40AC4310B81C1421 (void);
extern void TMP_Offset_Equals_m7D8B386EF75BA3B1F931F1F70AAB10FEFA6B17F4 (void);
extern void TMP_Offset_Equals_m1670D25215AB3B155D89F019D27286D294A9ECF0 (void);
extern void TMP_Offset__cctor_mADC96160EF508F9E23B40CCF1228A01606C83F90 (void);
extern void HighlightState__ctor_m25791146FF94DD76C2FAAAF47C1735C01D9F47B2 (void);
extern void HighlightState_op_Equality_mA8B294C1DDCDE0322A0834A3A0B742FCCB92A1E7 (void);
extern void HighlightState_op_Inequality_m98311E1C788EC5DB2E62731BA43E0AE8D73333F8 (void);
extern void HighlightState_GetHashCode_m2BE4FEEDFCB6713FA9C10C2D3B93E938E011C985 (void);
extern void HighlightState_Equals_m0317881F19561A64B9016A27C306FDB77460D165 (void);
extern void HighlightState_Equals_mFC0B5D3A36F1CB24FFDC21F6C238184D43324825 (void);
extern void TMP_ColorGradient__ctor_m16EACE29FFBC9D027D21703EE927AEE4C370EF8A (void);
extern void TMP_ColorGradient__ctor_m60AB4FD600B132618C4DED1BDE0864E7C8CBAC14 (void);
extern void TMP_ColorGradient__ctor_mD73E13202AFB41FB9171358320B28DEF6F1162A4 (void);
extern void TMP_ColorGradient__cctor_m84A7D02BB31A1A1FE3303B7C61B277AA066D934A (void);
extern void TMP_Compatibility_ConvertTextAlignmentEnumValues_mE840105F8940EB3B458F11758D4FBB8E1C8EF775 (void);
extern void ColorTween_get_startColor_mC3CD44E2CCEF5BB78ED52759D570B1B6855CBBCC (void);
extern void ColorTween_set_startColor_m01FB2C14DD0139433F9EBCF39A286AE064B8A2FB (void);
extern void ColorTween_get_targetColor_m26BAA4AAA09E6FD3E79F35C51170C32889919446 (void);
extern void ColorTween_set_targetColor_m24EB21B05BDCC21A4178DEB116962BE18B361B27 (void);
extern void ColorTween_get_tweenMode_m9194D120345334B358FA8487E98C75FDD8F8355B (void);
extern void ColorTween_set_tweenMode_mB2A52A753B322F14EEF3A1C17B0CC51EB5210035 (void);
extern void ColorTween_get_duration_m2C26D45A626E334E9EECD575CCA45B55945736D3 (void);
extern void ColorTween_set_duration_m0C781971A9EE23189EA02A2835731918680957F0 (void);
extern void ColorTween_get_ignoreTimeScale_m008715D2A64C9FC6419C9D71A7030F8710ABA6AE (void);
extern void ColorTween_set_ignoreTimeScale_mDC7F5B7E7EDF149304E8139C64712462DAB8D40D (void);
extern void ColorTween_TweenValue_m43ED566CB5FA4818535832C2ECFFCFEAAE7FFE8E (void);
extern void ColorTween_AddOnChangedCallback_mF9FE28A71E2818B2C25D79B9272E838034B5A4E4 (void);
extern void ColorTween_GetIgnoreTimescale_m502CC5CF9B974F7F564316687E61A16E8CF038F7 (void);
extern void ColorTween_GetDuration_mC929D8E88C9C230CE6A0EAE684B84CA08CFAA615 (void);
extern void ColorTween_ValidTarget_mFA6997930396ACBF53FCDFBDC457FC3C63AE90D7 (void);
extern void ColorTweenCallback__ctor_m8723485C32B5BEAF1EBC7F4A7078FB90BDCF1AB4 (void);
extern void FloatTween_get_startValue_mE12446AD7FA5B7816124ADFF1FF7959A3B6ACF1B (void);
extern void FloatTween_set_startValue_m1E04EA68FFEE3AA1553B194D0F82C32815E2C718 (void);
extern void FloatTween_get_targetValue_m2FA9DBB4C75BFD36E2A9DE57522CAA25D9F44FF2 (void);
extern void FloatTween_set_targetValue_mE110CBA03582B01B96766AE53F2DDD3C2D4DE131 (void);
extern void FloatTween_get_duration_mE71CD40934ED69FDD7CDA8D5438E9897E6E9FE7A (void);
extern void FloatTween_set_duration_mF59D55C5F70E037AF88A6191D282D404C532D613 (void);
extern void FloatTween_get_ignoreTimeScale_m8FE31080B4800A6CFB89918E0803BB1BE21FDA4B (void);
extern void FloatTween_set_ignoreTimeScale_m53B7945E5B54998B9BC28E7508E94D3A8205C10A (void);
extern void FloatTween_TweenValue_m022D385B013439E2FB8020F6A6BD329CECA81E89 (void);
extern void FloatTween_AddOnChangedCallback_m2DCB737D223D6345503A9DA057D7935F9C3A5AD6 (void);
extern void FloatTween_GetIgnoreTimescale_mB13FC7DCC241FCF2C9EC10D8AF8A9B6103F9B420 (void);
extern void FloatTween_GetDuration_m4F4E336D66A32D1F051E0EF2B1513F05EC3BF349 (void);
extern void FloatTween_ValidTarget_m4591FB5DBEE8762554B97A09B232893EE754D7DF (void);
extern void FloatTweenCallback__ctor_m93E614F36DA9484D40A5286A7EB4220220A25F00 (void);
extern void TMP_DefaultControls_CreateUIElementRoot_m67AF400D15F9252F70D1DDCCE1B6E017FCCF3608 (void);
extern void TMP_DefaultControls_CreateUIObject_m60CC06A3210ABF4961B73EE916210E9A5BEE3278 (void);
extern void TMP_DefaultControls_SetDefaultTextValues_mDC3353B8B8D0ED33A70A1B2AC9B7602DA9DCC67F (void);
extern void TMP_DefaultControls_SetDefaultColorTransitionValues_m72AC254CDF013CD5831DC9FF9A49A17E9857A228 (void);
extern void TMP_DefaultControls_SetParentAndAlign_m3DC6F261496415BFD5A95D0EA5EE8D7B320AE157 (void);
extern void TMP_DefaultControls_SetLayerRecursively_m34C986A57E7C7B3C75C44E54174BB5276F7D640B (void);
extern void TMP_DefaultControls_CreateScrollbar_mAB1F6E4EFD132C2BF186727978CF87844E0751AA (void);
extern void TMP_DefaultControls_CreateButton_m19500388D51128AC9E8EA08A6822D183E65AE43B (void);
extern void TMP_DefaultControls_CreateText_mD4727FBA88AD13862711F4C1E6507514A7C2BB56 (void);
extern void TMP_DefaultControls_CreateInputField_m76F50E353AD266E85972F4BE3DC871F9AE796B8B (void);
extern void TMP_DefaultControls_CreateDropdown_mC088CFDA6188DE6D4E215A5299276018B0AC7642 (void);
extern void TMP_DefaultControls__cctor_m6E1710C0401A290E8DE7C2E36389E34E875853A3 (void);
extern void TMP_Dropdown_get_template_m6C77CA07D48383A133E8D7567E1CF909876BAE30 (void);
extern void TMP_Dropdown_set_template_mCA07B030A71D2E4D4C179B4E4E72568656A340BF (void);
extern void TMP_Dropdown_get_captionText_mBC7B6DBEA095496F29EDA88E92F95E650B1BBC46 (void);
extern void TMP_Dropdown_set_captionText_m7050B2703E0A8C652571271C860004F0014BD464 (void);
extern void TMP_Dropdown_get_captionImage_mD946C03912041B698DD64BE58890CB30CD727308 (void);
extern void TMP_Dropdown_set_captionImage_m1AE6C5CF0660B3BF8C4A24DFF58E088C7CC67C19 (void);
extern void TMP_Dropdown_get_placeholder_m425453F7D24F908584DCC5BEB8CCB16D1E4A4B20 (void);
extern void TMP_Dropdown_set_placeholder_mA6502D4B0CDB70E1BF14B43FB468CF048929035B (void);
extern void TMP_Dropdown_get_itemText_m0AAE4A304C3AC8132209E844C5303411DB3C1AFB (void);
extern void TMP_Dropdown_set_itemText_mCC983B4A78E8C594E5C78BE1F8F8DBB067E3CA1B (void);
extern void TMP_Dropdown_get_itemImage_mE0C2F6ED6A8338733F2A260939530BAB06DB576E (void);
extern void TMP_Dropdown_set_itemImage_m41479EC2993BB2900024406EC39FDACC83DB808D (void);
extern void TMP_Dropdown_get_options_mA543A0EFE4D1953E73C6F60ECA8CE177182571C5 (void);
extern void TMP_Dropdown_set_options_m4CD66EEEFDF53BAD9862AA2768F6048B2CA5B2AD (void);
extern void TMP_Dropdown_get_onValueChanged_mC5A65068BFDC48C26FAC4B6FB2C0983633168BFF (void);
extern void TMP_Dropdown_set_onValueChanged_m79F7EFB914F2FDD0747F0DF2338E100996B49ECE (void);
extern void TMP_Dropdown_get_alphaFadeSpeed_m18A605C6E2AA4BA276BB7D8200718977530988DD (void);
extern void TMP_Dropdown_set_alphaFadeSpeed_m61345D8CD279D16406067AEA81DC4CFE6DFDB073 (void);
extern void TMP_Dropdown_get_value_m5916A6D1897431E8ED789FEC24551A311D1B5C70 (void);
extern void TMP_Dropdown_set_value_m8362A866D571975FECFD1FE47D3C4D83559795BF (void);
extern void TMP_Dropdown_SetValueWithoutNotify_m619A7D38D1EBCCA60EB1F5891FAD9FD0FB4839FD (void);
extern void TMP_Dropdown_SetValue_m1E528A87C2B11DD84D0141D8C6D175EEFE26F202 (void);
extern void TMP_Dropdown_get_IsExpanded_mFCB1622385A1BBB05908C0C94D5751801B5512FC (void);
extern void TMP_Dropdown__ctor_m5405D55FF150C4ED50322BA7B80E11E30096819D (void);
extern void TMP_Dropdown_Awake_m056E49917C61BD2AE6779CB0AD5A8983D17AB8FB (void);
extern void TMP_Dropdown_Start_m734BEF53026D3806A8E321B1E5DE53C340D28B16 (void);
extern void TMP_Dropdown_OnDisable_m10823C942F18754D85E6BB4C86A267D2D4B0F8B3 (void);
extern void TMP_Dropdown_RefreshShownValue_mF63797E989A9075B6E4CF767B2C4B55FBD23DFF8 (void);
extern void TMP_Dropdown_AddOptions_m0552A59BC909364951E1661FABADBC076715E4DD (void);
extern void TMP_Dropdown_AddOptions_m6EB4E175FE6B5555BDC2C31EFBCC3B115DE85FF5 (void);
extern void TMP_Dropdown_AddOptions_m788BACF88AF7E1342CEE571AAC04EF86D72DDEDE (void);
extern void TMP_Dropdown_ClearOptions_m9888C23BC3033268E5C2D8613C0854661E7BDEB5 (void);
extern void TMP_Dropdown_SetupTemplate_m3016B8C83713F451E88F3CBF008856BAB835F0EA (void);
extern void TMP_Dropdown_OnPointerClick_m008E7A23F525AF69BE769CE82695A8D07EE6EB39 (void);
extern void TMP_Dropdown_OnSubmit_mC64400DED5E54304BF98C84C4D4FB71B089CA49C (void);
extern void TMP_Dropdown_OnCancel_m67E30490F0C9D41D7BDCF42DF1621DABADC14B32 (void);
extern void TMP_Dropdown_Show_m8A64194BE150E0774C66E69479A1699DBB6AA900 (void);
extern void TMP_Dropdown_CreateBlocker_m4048F0A0133E9DE795874056A9BBD8F58B6E947B (void);
extern void TMP_Dropdown_DestroyBlocker_m2C9D955C6958C84AD323AC9E6193A07AE8212EA6 (void);
extern void TMP_Dropdown_CreateDropdownList_m3C853DEAA3D9F33653126E195B57D9F423064116 (void);
extern void TMP_Dropdown_DestroyDropdownList_mABF98A8706914CAFF13B3F6EB359F4F67E1E3FD9 (void);
extern void TMP_Dropdown_CreateItem_m6DBD654E30D5A7ABD668DE8651E554966D0A7191 (void);
extern void TMP_Dropdown_DestroyItem_mC0A868E6C60C5E1406E57B388FAFC1307C381A40 (void);
extern void TMP_Dropdown_AddItem_m1B768BFBFFE0E340BAD0141E2C3F90FFAE889EFA (void);
extern void TMP_Dropdown_AlphaFadeList_mC7278768821D6B8FD28FC712E8DE85CA334E2104 (void);
extern void TMP_Dropdown_AlphaFadeList_m663AC38ACFC469DBDFFF025314B34E310D70FBB8 (void);
extern void TMP_Dropdown_SetAlpha_m2AF7CD832E68BA385496F4F70D291219B3668ED7 (void);
extern void TMP_Dropdown_Hide_m7FFE4A08B7370707BCE35A3EDD74373A1926E618 (void);
extern void TMP_Dropdown_DelayedDestroyDropdownList_m746DFE51A0D66199C34FA32CF81761FDF1BE5F9C (void);
extern void TMP_Dropdown_ImmediateDestroyDropdownList_mFEE6CE2C3264546861C82E60CE4C61BA42559B98 (void);
extern void TMP_Dropdown_OnSelectItem_m9F1323DE41E23EFF4381C914BF1FCAA57F73C7AC (void);
extern void TMP_Dropdown__cctor_mF1ADB07E43839AA40B5E3492D0130638325D6BC2 (void);
extern void DropdownItem_get_text_mBE92632907F1772753BC60A7FE1E5358077E6FD4 (void);
extern void DropdownItem_set_text_m7C30C7E6DEE86A8896B99A872C5934D6F0B234F2 (void);
extern void DropdownItem_get_image_m1C89F635266FD742D3542FA99DB1A0D224CFD270 (void);
extern void DropdownItem_set_image_m133BCC7D7320DC10EBBF5193FAE0E60C3F9A15C2 (void);
extern void DropdownItem_get_rectTransform_mFE9F9A7B8259B150E399A40F9BE3555DC3273926 (void);
extern void DropdownItem_set_rectTransform_m0C6BAB5E2EB69C438BF2F8D4DDE3E0DE136651AD (void);
extern void DropdownItem_get_toggle_mCC7B7993BF2AB865B2B6B82472391D5B1AB873D3 (void);
extern void DropdownItem_set_toggle_mBD6E9CFB6C3757CB013CB683A8BB6DAFF34A94FA (void);
extern void DropdownItem_OnPointerEnter_m6020D4A90F1136573E7173875329573C07219B3D (void);
extern void DropdownItem_OnCancel_mD75155CB0FBA2A6FF217AC54CD0651061E830B5E (void);
extern void DropdownItem__ctor_m3A599BCBC6EEC74EA7CFE8D6AED4080D041BBE65 (void);
extern void OptionData_get_text_m2E822D5D50B597BFBA7AB3485EF15B526A726A1A (void);
extern void OptionData_set_text_mFF1E8A215A399CB9EA9DDFB0C6F3B9F068837226 (void);
extern void OptionData_get_image_m5C866E5C2E025EABF5591C7F88FB46E426EF20BA (void);
extern void OptionData_set_image_m17048079C3FCD19A6D190AAEBA198DBB74D93C88 (void);
extern void OptionData__ctor_m5B8E6B683070AB406FA738E689E2FD4055697FB6 (void);
extern void OptionData__ctor_mC08B019055F1AFA7CFC262EF24A64F6D8E7C84E6 (void);
extern void OptionData__ctor_mFB73CF17AD7974CEBAA97EB2C90B2E37FB624065 (void);
extern void OptionData__ctor_m19DDCFEF4911D4F1E6D07F5CE9924AF555CB4EC5 (void);
extern void OptionDataList_get_options_mBC2E351252449F2B611857D18B396CEDF2289781 (void);
extern void OptionDataList_set_options_m826C957D08679C2620276456BCD535D148640174 (void);
extern void OptionDataList__ctor_m698166937E8E51F44FC2CCE7B3EF7BDA3932645B (void);
extern void DropdownEvent__ctor_m65812C2BBC12188466CE844E9EC223B89E0946C1 (void);
extern void U3CU3Ec__DisplayClass69_0__ctor_mD126C84DF1978CB7709CC2B7374265951604D581 (void);
extern void U3CU3Ec__DisplayClass69_0_U3CShowU3Eb__0_mE6A18CF6156B45C4052F1CBF0707DAB544ABA722 (void);
extern void U3CDelayedDestroyDropdownListU3Ed__81__ctor_mFA83B08D54630018817A4BF76653AFDD3B1F8F69 (void);
extern void U3CDelayedDestroyDropdownListU3Ed__81_System_IDisposable_Dispose_mA124316B9DB4950F9756BDB797488D41F72383CD (void);
extern void U3CDelayedDestroyDropdownListU3Ed__81_MoveNext_mA1EBFA3FA05EF54A866C4C59793A1B9A2D1C9E70 (void);
extern void U3CDelayedDestroyDropdownListU3Ed__81_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m39BF9EDA0A38F97C5D75B53E1EF3954A3F58DE1F (void);
extern void U3CDelayedDestroyDropdownListU3Ed__81_System_Collections_IEnumerator_Reset_mB0690A94BB4E305E068156253A5D0F9044C7A101 (void);
extern void U3CDelayedDestroyDropdownListU3Ed__81_System_Collections_IEnumerator_get_Current_mFFDC09A7ADBA08CB2BE7D2DE3F83C54F5418F8F0 (void);
extern void TMP_FontAsset_get_version_mD6CCB4A7D6F1FD5C46C65A0711426299D50B2AFE (void);
extern void TMP_FontAsset_set_version_m7E9B93F4FDCE9EF03EDE8EA1245C522AFC61DE02 (void);
extern void TMP_FontAsset_get_sourceFontFile_mD9B0F4FCBBCDCAAE5A370399DDE46C768B736722 (void);
extern void TMP_FontAsset_set_sourceFontFile_mD3E145F204C872196FAE7DF1B97724C43BE24377 (void);
extern void TMP_FontAsset_get_atlasPopulationMode_m31A707178FB4F1722BA7D090A8E169CE2FAEB19F (void);
extern void TMP_FontAsset_set_atlasPopulationMode_mE002B4CEA0FB2CA8A52C77820B054165881E4FA4 (void);
extern void TMP_FontAsset_get_faceInfo_m1EB979B4CA53AA9EC5B09C445E28C24A477CBA6F (void);
extern void TMP_FontAsset_set_faceInfo_m711C454A1E8FE2B5DF455B6673257187B4C65B39 (void);
extern void TMP_FontAsset_get_glyphTable_mF205AA67646F5B0D2642CCBD2C1386F9DDDB8BCA (void);
extern void TMP_FontAsset_set_glyphTable_m4604AAE8FFF44D5129D1BA4DB8354EB850550528 (void);
extern void TMP_FontAsset_get_glyphLookupTable_m8793591000281F1CA28F3B9BDB8ACD3316BA64BB (void);
extern void TMP_FontAsset_get_characterTable_m8447F604F2D0F3615DA9AC1EBF5C289E112EDDF6 (void);
extern void TMP_FontAsset_set_characterTable_m00ADF21D5EC39D8DC306332253F7123C82AED02B (void);
extern void TMP_FontAsset_get_characterLookupTable_mEFAADDFAA6233DFEC3A0D8C163588B3C678451E9 (void);
extern void TMP_FontAsset_get_atlasTexture_mC44D519047CD3E54BD92AB8FE6773034F990AC7D (void);
extern void TMP_FontAsset_get_atlasTextures_m80D4DF83161F39AC7D06B0B097038B1E02AFE307 (void);
extern void TMP_FontAsset_set_atlasTextures_m5FED84FD7829C6B72E5937EC8DB9F844E654E937 (void);
extern void TMP_FontAsset_get_atlasTextureCount_m45F8A9CA48B566FF57230414BC298846FB0DD407 (void);
extern void TMP_FontAsset_get_isMultiAtlasTexturesEnabled_m0F308BCE9267BCAB86BBF7CEF0CBAE302BF5E8C0 (void);
extern void TMP_FontAsset_set_isMultiAtlasTexturesEnabled_m1EBFF9BBDE2D2A4E0B754FB1CCC45B76B0F1FA10 (void);
extern void TMP_FontAsset_get_clearDynamicDataOnBuild_m0D55A98AE41E211EE960F57F0A835A6FBB627B13 (void);
extern void TMP_FontAsset_set_clearDynamicDataOnBuild_m74DA616F7435271D377E7092D3B7BB2A95EB7EAD (void);
extern void TMP_FontAsset_get_usedGlyphRects_m6E5D5A9E86583AE37DA1ABD7F184A7F359C9627E (void);
extern void TMP_FontAsset_set_usedGlyphRects_m75EC42D8C57FFCA998DC859C5B6D91D03F7C1615 (void);
extern void TMP_FontAsset_get_freeGlyphRects_m093618A6748A2CB3C23904B03E5AD85B4B04521D (void);
extern void TMP_FontAsset_set_freeGlyphRects_mC976815EFF6313591B1B2A4CFFF9C77249570DC1 (void);
extern void TMP_FontAsset_get_fontInfo_m88825262BEC22C48EB4854A09CD31908024777FD (void);
extern void TMP_FontAsset_get_atlasWidth_m45CB71477140814BBFF666E9179D0F9BFFA03EFC (void);
extern void TMP_FontAsset_set_atlasWidth_mBF3960711180E34BB43393B4AA9951DC82B3535E (void);
extern void TMP_FontAsset_get_atlasHeight_m95F59523E66882079E1D2A4157DE5FF52C4892AC (void);
extern void TMP_FontAsset_set_atlasHeight_m1272E53248672683B4210DC23137D7F270CFE81C (void);
extern void TMP_FontAsset_get_atlasPadding_m556957263DC5F92E8EAA8460635860E96ACBD616 (void);
extern void TMP_FontAsset_set_atlasPadding_m3113EAFFC2BFEE6B95D5371D0325A8284CFBDA4B (void);
extern void TMP_FontAsset_get_atlasRenderMode_mF139904718DC44F83E657D2FB175A52B45B4FFAC (void);
extern void TMP_FontAsset_set_atlasRenderMode_m82C0939AEF473FB4A319457D4B6B7E67F36059BE (void);
extern void TMP_FontAsset_get_fontFeatureTable_mF00EEAEDD0448BE4667CB959CCE79ED45D2300AE (void);
extern void TMP_FontAsset_set_fontFeatureTable_m1FFFA492C09D14639BA2FA94B334758AC5E1B009 (void);
extern void TMP_FontAsset_get_fallbackFontAssetTable_mE0C2D8D8A55C5E2FAAB13CE0A5591C82F1AAF15A (void);
extern void TMP_FontAsset_set_fallbackFontAssetTable_mEFEEF51CD10815BE4CED7C0D356041401FB8BC1E (void);
extern void TMP_FontAsset_get_creationSettings_mC0B2DC9BAFE3BECB8F00A82C586B45FD0B2C9F6F (void);
extern void TMP_FontAsset_set_creationSettings_m81E03C271762A9134DEA4A3A8EA2EA47078DB000 (void);
extern void TMP_FontAsset_get_fontWeightTable_mC27EC0A27F82292FB24E3AB7B87421AEFD0869DD (void);
extern void TMP_FontAsset_set_fontWeightTable_m9E22B6BBDB11B70D6696C79F6E1B85F4D3183B5E (void);
extern void TMP_FontAsset_CreateFontAsset_m735DB93A8954566CA674FD0E6C95B4D20E039465 (void);
extern void TMP_FontAsset_CreateFontAsset_mC0DE04380E79B7A0A33E5F84DBD7B8595A27C639 (void);
extern void TMP_FontAsset_Awake_m2B709B96B357084D0D82D4DFC69AABB73061FB21 (void);
extern void TMP_FontAsset_ReadFontAssetDefinition_mC268F8946D0D6B28BABB3BF28FDF64FABDA2DF93 (void);
extern void TMP_FontAsset_InitializeDictionaryLookupTables_mEB2EEBAF42E2F540C08EE513E9C03D814E6213C3 (void);
extern void TMP_FontAsset_InitializeGlyphLookupDictionary_mFAED66009A159849EC515F922317CA629B3DF1F3 (void);
extern void TMP_FontAsset_InitializeCharacterLookupDictionary_m598F2AE1FBDE1D58002C4C89371B8FEF1AE61C7D (void);
extern void TMP_FontAsset_InitializeGlyphPaidAdjustmentRecordsLookupDictionary_m0A997B751301AB9C3D3A495D02462B116CEE08B5 (void);
extern void TMP_FontAsset_AddSynthesizedCharactersAndFaceMetrics_m4B60AD7CEE8FD7A1DA1EDCAFC6A55E4762F174BB (void);
extern void TMP_FontAsset_AddSynthesizedCharacter_m1BCF99FA52D4BEC7EF4C599ABA57270A444F9903 (void);
extern void TMP_FontAsset_AddCharacterToLookupCache_mA1A087CCE0961AB0FDE7032A5592BECC31FDE76E (void);
extern void TMP_FontAsset_SortCharacterTable_mDC720A14A27EA7CBAFCBD87B376379A177A20E8E (void);
extern void TMP_FontAsset_SortGlyphTable_m9A2A9FC31D2388CC762B28BC821AB380CCFCDBCE (void);
extern void TMP_FontAsset_SortFontFeatureTable_mA987AB015B7897DB1503B295D26565CFAEAB010F (void);
extern void TMP_FontAsset_SortAllTables_mB36ED96BA086B55246208EF70C141199131FAA5C (void);
extern void TMP_FontAsset_HasCharacter_mA90CDB3FAED3495F38016D37AE6EB47AFF5F9B3F (void);
extern void TMP_FontAsset_HasCharacter_mBCA92C1927170D017B189B711EAFF3B94D09E5C2 (void);
extern void TMP_FontAsset_HasCharacter_Internal_m3BEAA9D7D0F4794B1C70CC846AFFCEA2664977E6 (void);
extern void TMP_FontAsset_HasCharacters_m4FE851AD87F33A07C2ACF1E6EB3EBD53CE79D3BC (void);
extern void TMP_FontAsset_HasCharacters_mBFCB2CF8940F3C6B932EB5FDC7DAA08EFCB57190 (void);
extern void TMP_FontAsset_HasCharacters_mD2F9B73092E10B96039C1167BE7C5071B9954AD8 (void);
extern void TMP_FontAsset_GetCharacters_mF4A9E3F51C7F907DED5B55E780EA12CD6A118E81 (void);
extern void TMP_FontAsset_GetCharactersArray_m5162A0B4367DB2DE3DD7149214D493696CB503C9 (void);
extern void TMP_FontAsset_GetGlyphIndex_m22E9BF763EC7252C7FCD1D89B97AE424FAF7C957 (void);
extern void TMP_FontAsset_RegisterFontAssetForFontFeatureUpdate_mCA002DC71DE402BEE2C442826C3ED618FB852076 (void);
extern void TMP_FontAsset_UpdateFontFeaturesForFontAssetsInQueue_m6815C83C4C471BA54A26D97A94AC8B8D0FA112F2 (void);
extern void TMP_FontAsset_RegisterFontAssetForAtlasTextureUpdate_mB2E8F0BE304B5933C6861714DEE68FF97B9856EA (void);
extern void TMP_FontAsset_UpdateAtlasTexturesForFontAssetsInQueue_mDBF71CCDD755921536D87A8821968D03F6D8CCE2 (void);
extern void TMP_FontAsset_TryAddCharacters_mC2E29947A0696751B70B643959C787825BEE0A99 (void);
extern void TMP_FontAsset_TryAddCharacters_m007642E36561CCCBB6412F12387011A113E7CB58 (void);
extern void TMP_FontAsset_TryAddCharacters_m790E9AC68D5E0B177490733D1BC69CA42B04CDB1 (void);
extern void TMP_FontAsset_TryAddCharacters_m899ED242A4630FA7A60DD9EDC140C5E6508A8517 (void);
extern void TMP_FontAsset_TryAddCharacterInternal_m95DD37F41C18EE7692B44DCD984CD12C2350C122 (void);
extern void TMP_FontAsset_TryGetCharacter_and_QueueRenderToTexture_m7C957763D146CEBEE8CE749B147DBDAD9D7D16E9 (void);
extern void TMP_FontAsset_TryAddGlyphsToAtlasTextures_m7498D7F7102E6B35C41CE28CE0A60AAE9228DC47 (void);
extern void TMP_FontAsset_TryAddGlyphsToNewAtlasTexture_mD885238E48514F6063212EA6964ECC32F4F1D4FB (void);
extern void TMP_FontAsset_SetupNewAtlasTexture_m01889BA60E325103E4BB5A8B2D6AA8EB0CDA9B92 (void);
extern void TMP_FontAsset_UpdateAtlasTexture_m748FA0BD4B0A8C835518565D119585ED7B9A209D (void);
extern void TMP_FontAsset_UpdateGlyphAdjustmentRecords_m8530223EDCBEB89B965D763C65AC194F144C2C23 (void);
extern void TMP_FontAsset_UpdateGlyphAdjustmentRecords_mAD7016C52AE74A9F7482D6CCCE7F7319E78229FF (void);
extern void TMP_FontAsset_UpdateGlyphAdjustmentRecords_m04C430C2BBEBD156F4125657D24D2B8370DB991F (void);
extern void TMP_FontAsset_UpdateGlyphAdjustmentRecords_mC1E677EF9FB0B1E4400B4790468E542B5360B7CD (void);
extern void TMP_FontAsset_ClearFontAssetData_mB81DC841D38B3AC504B53D0920D86E6E4E56596D (void);
extern void TMP_FontAsset_ClearFontAssetDataInternal_m060146345FFE228F634C73F623C99666639773E2 (void);
extern void TMP_FontAsset_UpdateFontAssetData_m093EFEDF92C4667C5A92C6180B30A2F532C5C88C (void);
extern void TMP_FontAsset_ClearFontAssetTables_mC1F9BA41514D50F1E5A9CDAD3A845D90ADB98D56 (void);
extern void TMP_FontAsset_ClearAtlasTextures_m9B1D7B0F65146FF93ECDE10DFC286D8A38D5C007 (void);
extern void TMP_FontAsset_UpgradeFontAsset_m97EF749DD42303D0AF9EF5C120832CA697F9DAC1 (void);
extern void TMP_FontAsset_UpgradeGlyphAdjustmentTableToFontFeatureTable_m3C51FF9BA35FBA397791A4EFAAFB4B413553F492 (void);
extern void TMP_FontAsset__ctor_m20A531FC2F5114F9D1B98B38E06ACE46AF3403EC (void);
extern void TMP_FontAsset__cctor_m6DF47BFD17DC2C50356B01C6F41FDC6532648EF0 (void);
extern void U3CU3Ec__cctor_m22BBC842831DF1DFBE74F04A06CDD7808133B90E (void);
extern void U3CU3Ec__ctor_mFF3DD36E78B4295FBBC0AEA2BC2B5D1C50E3FDC4 (void);
extern void U3CU3Ec_U3CSortCharacterTableU3Eb__124_0_m6AEE1B8840F16BB5E083850B57EAF42151BC2781 (void);
extern void U3CU3Ec_U3CSortGlyphTableU3Eb__125_0_m19A43542E1087AC697E12E01BCD19D210B3C7E51 (void);
extern void FaceInfo_Legacy__ctor_mCDDCBA8EA56302A85281073F59AF8CF669DB5260 (void);
extern void TMP_Glyph_Clone_m9A59A9A4503F2C7B553A786CF79A022956EF7091 (void);
extern void TMP_Glyph__ctor_mEAE3F4DA2D1BDC556A578BAAA15BD354183AE2A8 (void);
extern void FontAssetCreationSettings__ctor_m9A07F1B7C85235E9BDA86E7505E0A5AE0B78E5BA (void);
extern void KerningPairKey__ctor_m76933735460799247D37F13189B62469E35C767B (void);
extern void GlyphValueRecord_Legacy__ctor_m6E3D36058693888F61D14BA825F5F29EA4FC7033 (void);
extern void GlyphValueRecord_Legacy_op_Addition_m4AE9E2E46D9322962969207ABC806BF6CE792F88 (void);
extern void KerningPair_get_firstGlyph_m8B473F310BB1D0E83BE4DB2E9C395C97E578BDCD (void);
extern void KerningPair_set_firstGlyph_m558F1AB56DF0BC72921E60524E906B3308EF6254 (void);
extern void KerningPair_get_firstGlyphAdjustments_mDD893850E04A182C37A2360992AE0F352E585600 (void);
extern void KerningPair_get_secondGlyph_m7F0717E0FE69CCE0ECFFB39680839D2734C095F0 (void);
extern void KerningPair_set_secondGlyph_m73FF4FD9F0409E3B2FC7DBC542C47DEC6E6979B5 (void);
extern void KerningPair_get_secondGlyphAdjustments_m04BE3DDED12C198E109C8E176E8BD8A4D49B0358 (void);
extern void KerningPair_get_ignoreSpacingAdjustments_mAF52BE99F08941D553BB53FBD6FA02391A1CE07C (void);
extern void KerningPair__ctor_mE4BD600F9F79E3590C13CE3F1C7BC89693420416 (void);
extern void KerningPair__ctor_m7AB7CD68A07A7BD8B1CB2A41C84EA8115F3ED974 (void);
extern void KerningPair__ctor_m97D222190FBC36A6C9843C7CB8F4E5F0CF963A11 (void);
extern void KerningPair_ConvertLegacyKerningData_mEA902FF8F87D8EEB50C054172D20CA9795ED1D11 (void);
extern void KerningPair__cctor_mECCE71DE39C14E6EDC210466B15D4C6CADEC6CBD (void);
extern void KerningTable__ctor_m5D6DF57B05146E104A2756917A31C27D0CC7A108 (void);
extern void KerningTable_AddKerningPair_mBAF75C93E61FAEE9998A2EE648119E723B8BE020 (void);
extern void KerningTable_AddKerningPair_m4392E91557C580125D59C4018E880D056476CE89 (void);
extern void KerningTable_AddGlyphPairAdjustmentRecord_m3542CA49AAE214B069445C85916D73C339EAF7E2 (void);
extern void KerningTable_RemoveKerningPair_m5FCDFF2DC4CAADDD361D70158DEA5E0A43010469 (void);
extern void KerningTable_RemoveKerningPair_mEE2E6198028618C0A5055AEE1A2D1DBB7F108DAD (void);
extern void KerningTable_SortKerningPairs_m59E143CF7D8EFC9E72ABA558A6680A6B3B86161B (void);
extern void U3CU3Ec__cctor_mE35FA8FEA10BD859A3ECA9CFE2661AF2EFAB4F41 (void);
extern void U3CU3Ec__ctor_mD36FC6FECCA7288F1C50E1C1C33357CB9CBD141A (void);
extern void U3CU3Ec_U3CSortKerningPairsU3Eb__7_0_m20BBABBDA17C6FD8E15138433B9D7FF73FC93183 (void);
extern void U3CU3Ec_U3CSortKerningPairsU3Eb__7_1_m6B19D6F1591A26B21706695288AE73FB9B26C470 (void);
extern void U3CU3Ec__DisplayClass3_0__ctor_m5B27602FA83CA988234ED481E13726F7ACFDEDFD (void);
extern void U3CU3Ec__DisplayClass3_0_U3CAddKerningPairU3Eb__0_mE8621E8C8419041EA962313967C9B22DADB7EC59 (void);
extern void U3CU3Ec__DisplayClass4_0__ctor_m7835A094259518636DCEBD5D5F5AC88B48799B4D (void);
extern void U3CU3Ec__DisplayClass4_0_U3CAddGlyphPairAdjustmentRecordU3Eb__0_m0457EDA34868349FD7DF84F23C8EC201BBA2FE4A (void);
extern void U3CU3Ec__DisplayClass5_0__ctor_m3718024C98F2CB240337703C272C56F9D60E0D86 (void);
extern void U3CU3Ec__DisplayClass5_0_U3CRemoveKerningPairU3Eb__0_mDD3433EA90C0CAE26949E21FBB418FF2DA10E509 (void);
extern void TMP_FontUtilities_SearchForCharacter_mAE34577D79CB6FFB29D2060F412D269E1F153D30 (void);
extern void TMP_FontUtilities_SearchForCharacter_mAACB85FD391B93D395CC934A3975EC9F8C654D9C (void);
extern void TMP_FontUtilities_SearchForCharacterInternal_m5D2FAB64754939BE5D183DF2832305D4C0335923 (void);
extern void TMP_FontUtilities_SearchForCharacterInternal_mBF347940D14E9D9585AC18E3037C0A4F0C1A8233 (void);
extern void TMP_FontAssetUtilities__cctor_m4C4A8256EFF82F5C0B86B8E1A95B55DD69D049AA (void);
extern void TMP_FontAssetUtilities_get_instance_mF9DD70CC93B066641699EEE945D7E10BF9829179 (void);
extern void TMP_FontAssetUtilities_GetCharacterFromFontAsset_m26EEEB3C26157C92CF623A246D6E92085E06CA26 (void);
extern void TMP_FontAssetUtilities_GetCharacterFromFontAsset_Internal_m0275490A50962C94DBC85C431D4FB8D3117C2716 (void);
extern void TMP_FontAssetUtilities_GetCharacterFromFontAssets_mF773865B6F097CDA5625615EA2CFC39DFB7A12D0 (void);
extern void TMP_FontAssetUtilities_GetSpriteCharacterFromSpriteAsset_m740B16719D09EF1F68B66DBE3D15265686D4DBB8 (void);
extern void TMP_FontAssetUtilities_GetSpriteCharacterFromSpriteAsset_Internal_mF5BFC00DAC02457C1C8F372373029476D300D3E5 (void);
extern void TMP_FontAssetUtilities__ctor_m8F25AE77B581CFF45180EF5FABBB4688608FDA02 (void);
extern void TMP_GlyphValueRecord_get_xPlacement_m3BB0AE22AA4B44163AD2BFB438E60E227523D5E7 (void);
extern void TMP_GlyphValueRecord_set_xPlacement_m12D97CDB7F44213ACBB3C015B5E88147147850A2 (void);
extern void TMP_GlyphValueRecord_get_yPlacement_m4FC0DDE3029083A45158537122D3BC3391DF2143 (void);
extern void TMP_GlyphValueRecord_set_yPlacement_m21EE385F1B674F9A575FFE6583A7E9035CFA2C24 (void);
extern void TMP_GlyphValueRecord_get_xAdvance_mA01138133A0841ADC49C3D0718B2268D9819CE4B (void);
extern void TMP_GlyphValueRecord_set_xAdvance_m862DABDFC3FF1C78E6A4C655A6C5631B905370E9 (void);
extern void TMP_GlyphValueRecord_get_yAdvance_m6F2282B9DF89F62B52A07D36327CC39720225BA3 (void);
extern void TMP_GlyphValueRecord_set_yAdvance_m5369AC719C39D3B9B79F5FEDC85C109754A4D60E (void);
extern void TMP_GlyphValueRecord__ctor_m030CD9864F16A5FB58D41ECD6CF66EC883B078BA (void);
extern void TMP_GlyphValueRecord__ctor_m5F96BB76417057AB3AC83120DA921295DBCA9952 (void);
extern void TMP_GlyphValueRecord__ctor_mFE317398DD11D070520A083E7C0758D7FD862F11 (void);
extern void TMP_GlyphValueRecord_op_Addition_m27CD190E35E404FAF3DC7283A76FC20650E55A73 (void);
extern void TMP_GlyphAdjustmentRecord_get_glyphIndex_m5DE8A84366AD7DC8B32D99B47D2BFE291F3C4F34 (void);
extern void TMP_GlyphAdjustmentRecord_set_glyphIndex_m3045246D7E256A1DEC17ADE2887BCEB013DF2DBB (void);
extern void TMP_GlyphAdjustmentRecord_get_glyphValueRecord_m1368E9CA86E6E76E04901506445319BAEFD6AA56 (void);
extern void TMP_GlyphAdjustmentRecord_set_glyphValueRecord_m47A43D4E95C3A89DC17588C3BE7F093517B4EBE9 (void);
extern void TMP_GlyphAdjustmentRecord__ctor_m41FDDFADD92DB1A8446228B1108E3E5C985CAAE0 (void);
extern void TMP_GlyphAdjustmentRecord__ctor_mB6BB797DD594B413042DD5D4FB8D691430FC8F51 (void);
extern void TMP_GlyphPairAdjustmentRecord_get_firstAdjustmentRecord_m4782831AE89EF77464166E4EB47C251B8483A458 (void);
extern void TMP_GlyphPairAdjustmentRecord_set_firstAdjustmentRecord_m795F115F13680DDAA3F4BCED9902C3CE3C8A497F (void);
extern void TMP_GlyphPairAdjustmentRecord_get_secondAdjustmentRecord_mF238079D6ADF0E2D6BE59D48758E13C2ED2F2B32 (void);
extern void TMP_GlyphPairAdjustmentRecord_set_secondAdjustmentRecord_mAE3695EF425238B8F692F1808BF9055E63AEF98A (void);
extern void TMP_GlyphPairAdjustmentRecord_get_featureLookupFlags_mAAFBDA6BE590EC3C085CA1537384CB1D97390691 (void);
extern void TMP_GlyphPairAdjustmentRecord_set_featureLookupFlags_m20C444D8AAE7A18E0B767B385272AE28C21007AB (void);
extern void TMP_GlyphPairAdjustmentRecord__ctor_m0BCCF9AF25F0A727D02FD778ACA2C7AD38F981CC (void);
extern void TMP_GlyphPairAdjustmentRecord__ctor_m33C61225BE06EEB15E3AD599451078F503BA4A60 (void);
extern void GlyphPairKey__ctor_m59DDEB66E800AABAEF624BCCF1CE091F27F124A2 (void);
extern void GlyphPairKey__ctor_mB1A0951B06F19D942015727B646A530A9EB68577 (void);
extern void TMP_FontFeatureTable_get_glyphPairAdjustmentRecords_m00772830EC8C026F17A21CBC39D26FC4D0A49FB2 (void);
extern void TMP_FontFeatureTable_set_glyphPairAdjustmentRecords_mCA20A72ABB8E829EE3C258B305143166EF220D62 (void);
extern void TMP_FontFeatureTable__ctor_m6F156B35A4B68F5616CFD3236C64F1E790D69039 (void);
extern void TMP_FontFeatureTable_SortGlyphPairAdjustmentRecords_m8BF5A029B84FF32BFCF4B32AD3D32F463DD050BD (void);
extern void U3CU3Ec__cctor_m3905215994C71FD63777B32B5154BC23204BDB84 (void);
extern void U3CU3Ec__ctor_mE70BB44A038503EE1979AD30BA141C6792A5160A (void);
extern void U3CU3Ec_U3CSortGlyphPairAdjustmentRecordsU3Eb__6_0_m6E8D8B97DDFB03B6EF69ADF2E85D412411D50075 (void);
extern void U3CU3Ec_U3CSortGlyphPairAdjustmentRecordsU3Eb__6_1_m12FAD67963FC77E6B359185D50EE168FCB5F7095 (void);
extern void TMP_InputField_get_inputSystem_mB89A77F46D53CCD05D9F57E03F4586B90265A55A (void);
extern void TMP_InputField_get_compositionString_m4332AACD655CF044F84411B3BCE32BF2034AC4CC (void);
extern void TMP_InputField_get_compositionLength_m444E57B7F68C9AECE1CDEBF4732FAD66EBA4937F (void);
extern void TMP_InputField__ctor_m6C5321A190D58235E29A17D7EE17D249D210A07B (void);
extern void TMP_InputField_get_mesh_m200F4FCC0738B54A3DFED98FF430660DB52E3E00 (void);
extern void TMP_InputField_get_shouldHideMobileInput_mB40438A3E4172E95CE11F03FD2484E954CDB1F1B (void);
extern void TMP_InputField_set_shouldHideMobileInput_mB1D2ADC209DE64154BAD42C2D25BFDA27081BB9B (void);
extern void TMP_InputField_get_shouldHideSoftKeyboard_m08F3F6AB9DC9A89029B6A41E26F42B7222535790 (void);
extern void TMP_InputField_set_shouldHideSoftKeyboard_m0414B5C0C3B07F0371671963D699EEA303D8A13E (void);
extern void TMP_InputField_isKeyboardUsingEvents_mE8A5552B89353CF45DBC4206F55ED41EB7C0F273 (void);
extern void TMP_InputField_get_text_mA4ACBF52435893D9DFD822A492454301740B3C6A (void);
extern void TMP_InputField_set_text_m684E9CDA2D9E82D1C497B5E03DBE79C00584FF62 (void);
extern void TMP_InputField_SetTextWithoutNotify_mE5ED91EB1759228F56E15A0E4BF47A7F8E28AB23 (void);
extern void TMP_InputField_SetText_m8D34D265867AA18228AA10E118A2DFE630911BFE (void);
extern void TMP_InputField_get_isFocused_m7FD1AA3B92404C30596FF6EE5F644757A2F060DE (void);
extern void TMP_InputField_get_caretBlinkRate_mACAF2093330BB9CB0B8C5F7D76EAA4EB0AE4DD18 (void);
extern void TMP_InputField_set_caretBlinkRate_m4D4B8F3C2169EE3FA7B27BECBD1563BFAD7B41F7 (void);
extern void TMP_InputField_get_caretWidth_mA95E0A88F505D9618791AEDE9D649CA70F7E3B65 (void);
extern void TMP_InputField_set_caretWidth_m291DBA8BEF0BD40BB4FAEE2AC71F9CDD114FAA9A (void);
extern void TMP_InputField_get_textViewport_m51E9CFB11A78199484D2BC2750F19DB7D2A26763 (void);
extern void TMP_InputField_set_textViewport_m3CB40F8DD0636EFBA496F1E76D41EE9C9570CB17 (void);
extern void TMP_InputField_get_textComponent_m85C4BC3F4C18206B3B942F03DB0B953B028EE1CE (void);
extern void TMP_InputField_set_textComponent_mCECC9B18AE37E999E5B38431D89C17B9BE384E07 (void);
extern void TMP_InputField_get_placeholder_m6C5FDEB031E2900A1792B928E4864B21B144AB3C (void);
extern void TMP_InputField_set_placeholder_m597012397FF55E6DE7E5E63972A3BE03EEAC480A (void);
extern void TMP_InputField_get_verticalScrollbar_mCB3FAFA0D86926FCD1A6620009BF3AEB274F17DD (void);
extern void TMP_InputField_set_verticalScrollbar_m8863C8FDC647B006DC8DAD8C3EDCF6353E08F945 (void);
extern void TMP_InputField_get_scrollSensitivity_m219F37C4A7DF784B9522EE565AE70EB813E799A8 (void);
extern void TMP_InputField_set_scrollSensitivity_m67129EC21A5560B781F61CB5C2282F977EB9AE12 (void);
extern void TMP_InputField_get_caretColor_m9733E1CB5CAD3CCFA9C32343D12F0095BA6DC76F (void);
extern void TMP_InputField_set_caretColor_mAF2AF8646B44D6AAA885F2A664DB88431E22177C (void);
extern void TMP_InputField_get_customCaretColor_m566EC393CFD6206101A2E0BE5AA2BB9D7233CF19 (void);
extern void TMP_InputField_set_customCaretColor_m9A33CA9154050A1C09881207B9C7B832B6C44B6B (void);
extern void TMP_InputField_get_selectionColor_m99D5B9FBC11FEAA170C113FB238120A73429F5BB (void);
extern void TMP_InputField_set_selectionColor_m9B30F4DC90BBD21ECDA6B5888F2F8E4B2EC7686D (void);
extern void TMP_InputField_get_onEndEdit_m0CE9718C71A834CC279430E20DC7FF4F42114FD3 (void);
extern void TMP_InputField_set_onEndEdit_mE34D6037D2C9FCAE1C9AF253D34D72541D306F4A (void);
extern void TMP_InputField_get_onSubmit_mAA494FA0B3CFFB2916B399BD5D87C2E1AA637B90 (void);
extern void TMP_InputField_set_onSubmit_m0FD1B91CB6BDD3864C74BFDBC458DF0C3B2EA193 (void);
extern void TMP_InputField_get_onSelect_m6762226148A4B3265EE5FD70ED894BBE8DE86AF0 (void);
extern void TMP_InputField_set_onSelect_m0D471B94B9358B9AD840B3F5E2A756C1D5DACD1F (void);
extern void TMP_InputField_get_onDeselect_mC9429495032728AEE8FCB818D61EDFB5DC7F9B0A (void);
extern void TMP_InputField_set_onDeselect_m13E94D8DA5530F8E6B438D98E1C8B801E3702006 (void);
extern void TMP_InputField_get_onTextSelection_mEBA14AF8E2BAF100DE885B78385F510A8E978A33 (void);
extern void TMP_InputField_set_onTextSelection_m52044BF605C6084CC95CB9D492652BA29D4E5699 (void);
extern void TMP_InputField_get_onEndTextSelection_mB01ED58A536B3DCC323A974C27C50337EAC7CAD5 (void);
extern void TMP_InputField_set_onEndTextSelection_mAF020E9DF7C78B4A16653D2F5F77C8B78B474C67 (void);
extern void TMP_InputField_get_onValueChanged_m407B5F5BFD1F4B04032F6B90B06F5072F5993407 (void);
extern void TMP_InputField_set_onValueChanged_m2C1B41AC850107D098E1D8BC481D23ED5310952E (void);
extern void TMP_InputField_get_onTouchScreenKeyboardStatusChanged_mF14075CDC1B4C99F300FCAD70350CDF144CB4818 (void);
extern void TMP_InputField_set_onTouchScreenKeyboardStatusChanged_m7AC290C056FF92BFA6558AEEE89E10BAC48A92CA (void);
extern void TMP_InputField_get_onValidateInput_mF293BE6DE7AAA1F8E37E20B73418A639A8963A7D (void);
extern void TMP_InputField_set_onValidateInput_mDA2BDCF7BFA9F24D48BA27027B9BCD366164C972 (void);
extern void TMP_InputField_get_characterLimit_m59833E0A22BACBDF3EDA6A70A30B87272FBAA409 (void);
extern void TMP_InputField_set_characterLimit_m64ADC294FC147C1E0806B5C175B9EA626059D4DC (void);
extern void TMP_InputField_get_pointSize_m2F9C02B8B2E8501799E118F3FC1675DB1555EEB3 (void);
extern void TMP_InputField_set_pointSize_m5001D4D1325CE0737CAA65D86ACAB2D88DAA87C3 (void);
extern void TMP_InputField_get_fontAsset_m9EAAF4737728BB51C8D5D7A1AC46E77DD970F176 (void);
extern void TMP_InputField_set_fontAsset_mB102F697B83B5115F2E4B30A076FE67D30BCA201 (void);
extern void TMP_InputField_get_onFocusSelectAll_m6A1A06461D6B01EE2E68624B9D7E5E3C7D092CDC (void);
extern void TMP_InputField_set_onFocusSelectAll_mDC9C36C7201E90054B97AE94251577ABB103FD75 (void);
extern void TMP_InputField_get_resetOnDeActivation_m6BB1C27CCFB72767235B459ED4F3A81965273771 (void);
extern void TMP_InputField_set_resetOnDeActivation_mCB035C9EADE4A6896C42DDCEC996D00D4A7F6CB2 (void);
extern void TMP_InputField_get_restoreOriginalTextOnEscape_m138E8AAD613E1A3693B8B9E6469B6450F86D367D (void);
extern void TMP_InputField_set_restoreOriginalTextOnEscape_mA8F67F61689BBD34C3B4811DAD7380253EA8069C (void);
extern void TMP_InputField_get_isRichTextEditingAllowed_mBF19A7F1ECC8F9C2F06D1D7636F45882E671CCB3 (void);
extern void TMP_InputField_set_isRichTextEditingAllowed_m3BEB725A42ACC0CD7990E36B4B707AB892EA8B21 (void);
extern void TMP_InputField_get_contentType_m32EEDFC275E9CB6C759A4F117EBAA40336B9030D (void);
extern void TMP_InputField_set_contentType_mB9BCF78B6868FBB3CDE671DDF008E3716D3ADC91 (void);
extern void TMP_InputField_get_lineType_mE221F133A310EB6C93DA24E1F5900E948771D64C (void);
extern void TMP_InputField_set_lineType_m0B3A3770A8229ABCDF139F80765FC6C3A659FD21 (void);
extern void TMP_InputField_get_lineLimit_m771801BE2D9D7F788EDA1F90336929FC54193D9F (void);
extern void TMP_InputField_set_lineLimit_mD645AAD616399138A50AA905E8A8CD4B5B177B62 (void);
extern void TMP_InputField_get_inputType_m93A6CC8FF76412F46471D91952323CE4C63B7D34 (void);
extern void TMP_InputField_set_inputType_mF1647C27280C29CE45DB74457ABF43B46A19366C (void);
extern void TMP_InputField_get_keyboardType_m8B616A743B2FAB03C6263F1582171BB390F94F8B (void);
extern void TMP_InputField_set_keyboardType_m97210FB5D41B6AAE5352D2BD6C1D45AF8174FC95 (void);
extern void TMP_InputField_get_characterValidation_m57E36C62FC9E23DB17F440BA4325A314EF0B0679 (void);
extern void TMP_InputField_set_characterValidation_mE2D042600CF00A3F5D8EFF09271C0FCDCE324D4C (void);
extern void TMP_InputField_get_inputValidator_mF47AEABCFD04F4F9FE7F7C64331A8B01B7976CF7 (void);
extern void TMP_InputField_set_inputValidator_m619FB8CCDB4B2BA3FE13ADF567137061647E9AA2 (void);
extern void TMP_InputField_get_readOnly_m551BFA0AB64EBD12F49C0993305274BC8176E0A5 (void);
extern void TMP_InputField_set_readOnly_m05A0789FE9583F510DF5299A963BA0C32EC03C8A (void);
extern void TMP_InputField_get_richText_mFDFECA8E9F49F27A5FCCB4D147C283581BE66155 (void);
extern void TMP_InputField_set_richText_m1AE9CD128CFF3316C1C602717337241666AA1FA4 (void);
extern void TMP_InputField_get_multiLine_m3000150A39B90BCFFAFD41E0F49F479323F045B7 (void);
extern void TMP_InputField_get_asteriskChar_m3D3F22537749D339A3DB36BE6C56015D0B06A38E (void);
extern void TMP_InputField_set_asteriskChar_m5A6D4BEB046DC3E1397972AEF86F00F5FEDB4CD0 (void);
extern void TMP_InputField_get_wasCanceled_mEF43E80CFB8EE3DCE8599D64213D3F977D9794FF (void);
extern void TMP_InputField_ClampStringPos_mCEF1B5B73F19C6FFA1A9411FCA485B7F81C73D05 (void);
extern void TMP_InputField_ClampCaretPos_m24F8EDB52862BA470A2CD5FD3D2A62AA86A00FC1 (void);
extern void TMP_InputField_get_caretPositionInternal_m21C9BFCD70C944B374E5C916C7E7E67B75B831EA (void);
extern void TMP_InputField_set_caretPositionInternal_mEC3488328558F5257115078785242BE6C59BA1BF (void);
extern void TMP_InputField_get_stringPositionInternal_mBDA10D8ED51D01C973FB6CFDD1096DD29CA5D214 (void);
extern void TMP_InputField_set_stringPositionInternal_m0C190ABB9829A8F93268F669655D6AF29E25E265 (void);
extern void TMP_InputField_get_caretSelectPositionInternal_m977002CC2C821A3B4FA5FB3F1BC15C7DD0BA35A4 (void);
extern void TMP_InputField_set_caretSelectPositionInternal_m2AA6FD295A4E6D7236ABFE88B4CF49EDDA566191 (void);
extern void TMP_InputField_get_stringSelectPositionInternal_m8FE3D7533D67501DFDC1EA83B3FD72F8C1E0A79D (void);
extern void TMP_InputField_set_stringSelectPositionInternal_mCBA385B30788D514E2306703B370F6350E1B9997 (void);
extern void TMP_InputField_get_hasSelection_mA2CF23CC43AD3EE9F66C67A5995407EBB2F59565 (void);
extern void TMP_InputField_get_caretPosition_m1F103634776349DFA375AC8C64F1D2535A693A15 (void);
extern void TMP_InputField_set_caretPosition_mD5B0AFA01D9947B7EFC98CD4C4BF927518513FF4 (void);
extern void TMP_InputField_get_selectionAnchorPosition_mAAD925C368B16EFE972C11F551A1D9DCB93B0B93 (void);
extern void TMP_InputField_set_selectionAnchorPosition_mB6E72D94EFD7C55EAFA8F8AAC30D255935438B06 (void);
extern void TMP_InputField_get_selectionFocusPosition_m64C9DB19CDB18E29B7CB02DCC84B5F05ACDB473E (void);
extern void TMP_InputField_set_selectionFocusPosition_m862731C1A303D3778E292AB427BC1BEF4407050D (void);
extern void TMP_InputField_get_stringPosition_m5C9E52B4A7304183ED4F690AD6239D57B142A7B6 (void);
extern void TMP_InputField_set_stringPosition_mB6538BDB302FECF09EAD5BA986FB11BBE6A49E8A (void);
extern void TMP_InputField_get_selectionStringAnchorPosition_m321370B1A913B9B619DE5C5A5E5FA8D251C0B8F2 (void);
extern void TMP_InputField_set_selectionStringAnchorPosition_m60E8DEBD9389373AD410E7E868D3C36CCA202B8E (void);
extern void TMP_InputField_get_selectionStringFocusPosition_mA044AFF5699E8B61BF3CBE271522AC8CA7088B0F (void);
extern void TMP_InputField_set_selectionStringFocusPosition_mB23FDE5288C4F033028320FE4DBDEB096AAB3917 (void);
extern void TMP_InputField_OnEnable_m3A78BC51F18EDA6775A85DB81F8F401B17D04475 (void);
extern void TMP_InputField_OnDisable_m2E967647BDF216075B8F3EB3C1559B6AAA2D3C95 (void);
extern void TMP_InputField_ON_TEXT_CHANGED_mEA6A2C8BD4AF9D1C0CF5A6EC9608F2149256B553 (void);
extern void TMP_InputField_CaretBlink_m280BE5F4289F6C4ABA767D15C147E39DA6B54AD5 (void);
extern void TMP_InputField_SetCaretVisible_m1D8A496EA7137B9CCEFD1785B1D5BFA3A3325194 (void);
extern void TMP_InputField_SetCaretActive_mC9858C9E1FE6D8800219C49C52A9FACC1ED5EEC1 (void);
extern void TMP_InputField_OnFocus_mBDC52EE4DF24C43E6C4C272B09FDAE6F7CB5970F (void);
extern void TMP_InputField_SelectAll_mC66107E00F20D1E401A04108D2A9136559AD23F7 (void);
extern void TMP_InputField_MoveTextEnd_mC781F7D531E0B22F73DF2C6E5F882243DD133E6A (void);
extern void TMP_InputField_MoveTextStart_m8D0AA8989DE9DB5D0B540343640BFAAA1C0CEC9E (void);
extern void TMP_InputField_MoveToEndOfLine_m42BC00BF1E1A82E956220244E72E577C667297D8 (void);
extern void TMP_InputField_MoveToStartOfLine_mC73D470B797643AC87FCFCC778D43DD945B18252 (void);
extern void TMP_InputField_get_clipboard_m53271C5A442FE382959DEF76525B14616E58BFAE (void);
extern void TMP_InputField_set_clipboard_m2A21EC4F18EF6AB80DD0D03887BB115E3AB5D0BB (void);
extern void TMP_InputField_InPlaceEditing_m2FEE000FC40DAF7CAE8B2DA4FF5D364E30873FC1 (void);
extern void TMP_InputField_UpdateStringPositionFromKeyboard_mED93ADC3A6B158262ECFB07CD6B21EC39B99022E (void);
extern void TMP_InputField_LateUpdate_m9D1496DFE349C330C4FD29BDE290209E22E66DC4 (void);
extern void TMP_InputField_MayDrag_mB0F0807D39BED3B80A5EF8F757E8956D5869ED1F (void);
extern void TMP_InputField_OnBeginDrag_m0F9B51A6CEBD12DAB6DFFF1CA1F15AD1D3495940 (void);
extern void TMP_InputField_OnDrag_mBA444852C709D10301A31FAD4DA6AD33479C05E4 (void);
extern void TMP_InputField_MouseDragOutsideRect_m19E6E5421BFCC996DC30FD6FCD07EF890931FB44 (void);
extern void TMP_InputField_OnEndDrag_m0CB1102EBBDC28E1AFA38FD0D50469F08492D4AC (void);
extern void TMP_InputField_OnPointerDown_mA194D68CFB19DF910D8EE1B63DF5FF4D795C6C8D (void);
extern void TMP_InputField_KeyPressed_m1C528E6E9E2FB05DFA8CA61F880DEE24C2C304F6 (void);
extern void TMP_InputField_IsValidChar_m12ACC6064ABA5E72C3CF133AFC578300A67EEFC1 (void);
extern void TMP_InputField_ProcessEvent_mED1F52CCCF82A49EF61E080D8A55B63EB8878124 (void);
extern void TMP_InputField_OnUpdateSelected_m04CB39F3A7156D62E73D7A04E52F4356DD40FCA3 (void);
extern void TMP_InputField_OnScroll_m87663801D19AE16C594D8C106CD2A5532CE1B22E (void);
extern void TMP_InputField_GetScrollPositionRelativeToViewport_mE320B683741E2E7403D1F2ADCD65F44B68FA4158 (void);
extern void TMP_InputField_GetSelectedString_m4BF128EBC96DAB95E95CD9F292A1EB99AD1238C6 (void);
extern void TMP_InputField_FindNextWordBegin_m1207B66382CCC488015CD5EB2E017C20E20A6AF2 (void);
extern void TMP_InputField_MoveRight_m8831525A4FF9E75CA86BD5E4BAC9351EF640D497 (void);
extern void TMP_InputField_FindPrevWordBegin_mD8DA9061047B673CDB67C2F762A14C1514CFEC17 (void);
extern void TMP_InputField_MoveLeft_m787CBD78E57FDD7DC28A10CA1624EA4118157898 (void);
extern void TMP_InputField_LineUpCharacterPosition_m6FAA53F203CF66F87F62F985E830CB769A169F16 (void);
extern void TMP_InputField_LineDownCharacterPosition_m0A95990F452ECFB15A5BF8C12D8E92592CF3B2CD (void);
extern void TMP_InputField_PageUpCharacterPosition_m68C124FCEE737E9CB486D5218A2B5804D407BD0A (void);
extern void TMP_InputField_PageDownCharacterPosition_mD00879F9AD1E5315C8896D8CB421FAB93045F818 (void);
extern void TMP_InputField_MoveDown_mB8F65AD03355C867F63BAB0021C93B75F534CCBE (void);
extern void TMP_InputField_MoveDown_m96FE2822D035DFBE82474737DEE8DED622AAD868 (void);
extern void TMP_InputField_MoveUp_m0A8E579FDBE203C29D7AF1B4225C9A64498DE5A9 (void);
extern void TMP_InputField_MoveUp_m79291882C851A7AEC3945EB8479D31984941F8DB (void);
extern void TMP_InputField_MovePageUp_mA945CEDD104AAC4B417B1AC6D95FC75798ED3040 (void);
extern void TMP_InputField_MovePageUp_m1B01B4C15C5D556CED7B34E7F55149E1DA35ECF1 (void);
extern void TMP_InputField_MovePageDown_mE32EFCBEB2A1D230D3C6C8B27357C454F4AD5EC2 (void);
extern void TMP_InputField_MovePageDown_m80AEFB5ACD656505A347F13FAEFB55EA62F0EC86 (void);
extern void TMP_InputField_Delete_mD817C69CFF25B762DF673A1FD31DAF0E2F761784 (void);
extern void TMP_InputField_DeleteKey_m3EE34B2EE784E0F8833BCEA668B590D8C838BDCC (void);
extern void TMP_InputField_Backspace_m1962DCE85EA39B861EF3E9E147A63C8CFE58A917 (void);
extern void TMP_InputField_Append_m4595DE62B0D6CD1E1EACC127F8B84563351190C8 (void);
extern void TMP_InputField_Append_m90791E607DDDAD68C715529BF47B24726ED86582 (void);
extern void TMP_InputField_Insert_mD8773951E82B4743AF137BE4EDA14915EC704907 (void);
extern void TMP_InputField_UpdateTouchKeyboardFromEditChanges_m05E63AC0F9D593BB8584E97AC236646C05E22B12 (void);
extern void TMP_InputField_SendOnValueChangedAndUpdateLabel_m9A56A0E7406E3E3362400445749CE33C20C7BC64 (void);
extern void TMP_InputField_SendOnValueChanged_m9138A30966454771476FF25A71ED03DDAF6EC0C7 (void);
extern void TMP_InputField_SendOnEndEdit_mBE399B126786848BC400A04B165A6C9BD6757CD1 (void);
extern void TMP_InputField_SendOnSubmit_m3993BECBCAB4632CD5C564C0BC38486FC2320D14 (void);
extern void TMP_InputField_SendOnFocus_m306B75E91484337E9A090AB1A45D971133ACF7C8 (void);
extern void TMP_InputField_SendOnFocusLost_m0BC85C3C362617A4E81F9E9360207EFC0D2882FF (void);
extern void TMP_InputField_SendOnTextSelection_m301880AB4201417DFE7FEB6CC22A323DF0935ADC (void);
extern void TMP_InputField_SendOnEndTextSelection_m5142CBC7340FC8E2B0457DDD1F257C1A19DE01D0 (void);
extern void TMP_InputField_SendTouchScreenKeyboardStatusChanged_mF0F1E86DFF3023EA6167004879DAE86E1D2C3AEB (void);
extern void TMP_InputField_UpdateLabel_mC40048ECFCF13981FE38993C7251024EC2477ED2 (void);
extern void TMP_InputField_UpdateScrollbar_m61D071BE0C6F2D5C8FD3F75AF4B6A256685429C9 (void);
extern void TMP_InputField_OnScrollbarValueChange_mD38291A7EBF4EDA6C308DF090261355519C10E03 (void);
extern void TMP_InputField_UpdateMaskRegions_mD22E32D41A5E6EDAC8A7547194CA34A4DE918343 (void);
extern void TMP_InputField_AdjustTextPositionRelativeToViewport_m7EC3FED9FB3F4F5450E60552FE36F0D79E48592C (void);
extern void TMP_InputField_GetCaretPositionFromStringIndex_m24E11A6B461D41DAD8CA4DC96F0AB263175DE283 (void);
extern void TMP_InputField_GetMinCaretPositionFromStringIndex_mF22329EB6607A83C8791B9DE0A1FB4B8B53575AC (void);
extern void TMP_InputField_GetMaxCaretPositionFromStringIndex_m5A2C033C4018D10695C8E3CA0A53EA7E5F6F5B01 (void);
extern void TMP_InputField_GetStringIndexFromCaretPosition_mE8952E126639234C711E6DA723C272AA6C22FB59 (void);
extern void TMP_InputField_ForceLabelUpdate_m06F01A5D3EF44553E23404EC82D65B405A842C11 (void);
extern void TMP_InputField_MarkGeometryAsDirty_m3FD825DDE67FAA8CFBF12EE92C65463823402138 (void);
extern void TMP_InputField_Rebuild_mA714C05AB0AAF3BDB56E2534622E978943AC2317 (void);
extern void TMP_InputField_LayoutComplete_m3C78365E6DFF603628C61A7321EEE5AA1FBCDA67 (void);
extern void TMP_InputField_GraphicUpdateComplete_m5FE6F033C3688FD16CE1D52A0CABE075699C568E (void);
extern void TMP_InputField_UpdateGeometry_m44637D3DF51E919CE2F702A61B49A2F4FEFCEAFB (void);
extern void TMP_InputField_AssignPositioningIfNeeded_m3CA56BB25376EF783C8E1218AA2643FACEB924E1 (void);
extern void TMP_InputField_OnFillVBO_m4F9AEEB359EABEA669C3E3587ECF4B1262067E6A (void);
extern void TMP_InputField_GenerateCaret_m6020296CC782C426A13349E6B8885C029DBEBB72 (void);
extern void TMP_InputField_CreateCursorVerts_mAD2D8B4DD0331646AA852C4BFF7595CC76D27836 (void);
extern void TMP_InputField_GenerateHightlight_m6B8F6ECF6369850A6B87D68E75A639021F463B8F (void);
extern void TMP_InputField_AdjustRectTransformRelativeToViewport_m58C2AAE39A4A6EE2309BAACBDFBAA22A885CF479 (void);
extern void TMP_InputField_Validate_m76212763DA49DFD7C152C65F8AF6CC056EE69979 (void);
extern void TMP_InputField_ActivateInputField_m9471012A606F201DF838539F5400D072A827914F (void);
extern void TMP_InputField_ActivateInputFieldInternal_m95B34ECC08F02FF048EFC2272CE07648657627BC (void);
extern void TMP_InputField_OnSelect_m586B40BE0FAFFDA515B1AF7A391094F076B2036F (void);
extern void TMP_InputField_OnPointerClick_m2A6F2110D5AD4EF8C3FBA29166BC76921C469C55 (void);
extern void TMP_InputField_OnControlClick_m5E418EA29EFE5180655F904E5727AE8210B6EC21 (void);
extern void TMP_InputField_ReleaseSelection_mC70F802822362452CFDD9FE095F5147E6BB5020F (void);
extern void TMP_InputField_DeactivateInputField_m1C829676E9DC0D3E5DAE85D1869D26FBF748184D (void);
extern void TMP_InputField_OnDeselect_m19AA85C6A6FAB27850293318B1D92908B82F99AF (void);
extern void TMP_InputField_OnSubmit_m52BE7037E939A81A0EF41FCB4DA55D77C5970039 (void);
extern void TMP_InputField_EnforceContentType_m4D5F42FD6E04B3B3B541E978C9C603B7696E7AB2 (void);
extern void TMP_InputField_SetTextComponentWrapMode_m9CF72ADC54A79451A8B35A85FFF2232F1D6A79ED (void);
extern void TMP_InputField_SetTextComponentRichTextMode_mAA7F99B2DFD5DD46007BF0247B37A8CA008F1947 (void);
extern void TMP_InputField_SetToCustomIfContentTypeIsNot_mB9AC8BE6A15C7DC926F5064C49A0F2199CC6B14D (void);
extern void TMP_InputField_SetToCustom_m798A8846432794AA8687F6020551B512357D2CF0 (void);
extern void TMP_InputField_SetToCustom_mB668A3AB0C1900F2B2618DB9C1288C805DD21D58 (void);
extern void TMP_InputField_DoStateTransition_mB9F4AAD269179A5EBE7A31DDC64D8832C403F260 (void);
extern void TMP_InputField_CalculateLayoutInputHorizontal_m46CEB3041DFCF55FF496A01B186965E0846BDAA0 (void);
extern void TMP_InputField_CalculateLayoutInputVertical_m89664390EDA3B835EF3540E85A65978247884577 (void);
extern void TMP_InputField_get_minWidth_m2A2D1042C5D78373A2AD8BBF514157D83C3A706A (void);
extern void TMP_InputField_get_preferredWidth_m7B67921BC7BD2A2FDD53C93FC9AB63B04A00C753 (void);
extern void TMP_InputField_get_flexibleWidth_m8E903250C848B81530D7A757513C98FD7DB4E3CB (void);
extern void TMP_InputField_get_minHeight_m5B9B113BDB4FA8562DE935A377CA0734F7ADE7B1 (void);
extern void TMP_InputField_get_preferredHeight_mF8468DD1FB5F87870379277710E1D5C3DDCFFC1D (void);
extern void TMP_InputField_get_flexibleHeight_m3293370FBA374E6FCDBC1E7BF9EF975C1D00DEC3 (void);
extern void TMP_InputField_get_layoutPriority_m29F413DB25AC2A615265C1C2820D89AC60816DF2 (void);
extern void TMP_InputField_SetGlobalPointSize_m3FFB4ADB49E9D601673A53AEA78839B964D32A81 (void);
extern void TMP_InputField_SetGlobalFontAsset_m4AD28DDE68A928EA340F360105C99A1EBC789201 (void);
extern void TMP_InputField__cctor_mEFAAE367E78CED0A26A678556C750E8045A5AC37 (void);
extern void TMP_InputField_UnityEngine_UI_ICanvasElement_get_transform_m54CD226342494A37D5AF311CC975A634588AE1AF (void);
extern void OnValidateInput__ctor_m734DB6ABACB01CDC715C54E93A47B817C0E7FB68 (void);
extern void OnValidateInput_Invoke_m1CDDA220BF2691F42200A098F57AE10FDE383E49 (void);
extern void OnValidateInput_BeginInvoke_m43FBD60B6478C13E662CE8C9BB98085409286F6F (void);
extern void OnValidateInput_EndInvoke_m71D53E4976D82E4B04234B4EC8877D54E3C84954 (void);
extern void SubmitEvent__ctor_m7D30737EA13979AD78F6D7C46563FD43A32301C8 (void);
extern void OnChangeEvent__ctor_mA7F876316D0F5198E90ECA7304C6542D63758698 (void);
extern void SelectionEvent__ctor_m9EF6D8DB48A30C615541A297E3739B078BA2F8AD (void);
extern void TextSelectionEvent__ctor_mB76781548533BA217F1FDD39550335889994027E (void);
extern void TouchScreenKeyboardEvent__ctor_mA7D12057CDF3115B9B47CFFE817A2D901B90EB37 (void);
extern void U3CCaretBlinkU3Ed__276__ctor_m04194456FB3C1DBD716CFA59EDDE760D986AAE94 (void);
extern void U3CCaretBlinkU3Ed__276_System_IDisposable_Dispose_m62F3C667730FA038C1323EAB48AEF59FEFFCD1A4 (void);
extern void U3CCaretBlinkU3Ed__276_MoveNext_mDD251CD28C8D1BDCAA212FB35443F07ECABB6E87 (void);
extern void U3CCaretBlinkU3Ed__276_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mDD9FEFE4B5114891E051AA778A47A32E4530798E (void);
extern void U3CCaretBlinkU3Ed__276_System_Collections_IEnumerator_Reset_mD05FB2B8858676366A0ED98063C5AEECC60AEB28 (void);
extern void U3CCaretBlinkU3Ed__276_System_Collections_IEnumerator_get_Current_m06538DD0CE0EA13A3E001E9E7B390F96B2E9B724 (void);
extern void U3CMouseDragOutsideRectU3Ed__294__ctor_mC97D27357520CBB200DD3254DEF53DF620ACB6B6 (void);
extern void U3CMouseDragOutsideRectU3Ed__294_System_IDisposable_Dispose_mDDECF9B91F7E66A9166A3FC4863656BC94C4A28D (void);
extern void U3CMouseDragOutsideRectU3Ed__294_MoveNext_mE91DEA64A594ABE2D110805114C864C501529C0E (void);
extern void U3CMouseDragOutsideRectU3Ed__294_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m36806333FD5DF40BAC0946CEBC2DE197E7E3090C (void);
extern void U3CMouseDragOutsideRectU3Ed__294_System_Collections_IEnumerator_Reset_mA500CB2991458842FB24C83B54624A9B91446E8C (void);
extern void U3CMouseDragOutsideRectU3Ed__294_System_Collections_IEnumerator_get_Current_mCD6E5AE2B1016D22C30F38001C6DFB243EAF10AE (void);
extern void SetPropertyUtility_SetColor_mBDA27D2F874BAC08FEDDAED677ECF596B3743547 (void);
extern void TMP_InputValidator__ctor_mD15E0AFA50E8CA10B2849A66A5B96D50B7EA66F3 (void);
extern void TMP_MaterialManager__cctor_m9A67C84C9A17C88DA0C043F8B6C6604A0C609BC6 (void);
extern void TMP_MaterialManager_OnPreRender_m568227EA396CF03FD388C00EBDD713D05A3558F3 (void);
extern void TMP_MaterialManager_GetStencilMaterial_mDC5A5B34DC6E0AE05CE94A3D92B823A6EAF96F96 (void);
extern void TMP_MaterialManager_ReleaseStencilMaterial_mECF794E6299D84E46FBC0BC6F23155A8751FCD41 (void);
extern void TMP_MaterialManager_GetBaseMaterial_m462BBD276522D865CA64749658495FE727A878A0 (void);
extern void TMP_MaterialManager_SetStencil_m885536A1C4F790102E5AF2D3518F39601062F870 (void);
extern void TMP_MaterialManager_AddMaskingMaterial_m23B894F9E1FA5F9FDBA6E99A2B3FD574BEC2F5C5 (void);
extern void TMP_MaterialManager_RemoveStencilMaterial_m3B79911A308BB53F51AD8BCAB8D240ED60E6E71A (void);
extern void TMP_MaterialManager_ReleaseBaseMaterial_mA38A80719DC32DFC0C37E5DEE17FF622C873CDA2 (void);
extern void TMP_MaterialManager_ClearMaterials_mE4F43E6AE2BC8FA8F1B369F8B38B5DB3F78FE37E (void);
extern void TMP_MaterialManager_GetStencilID_mE85832BD9E38E435296801AD6CDA9A2EBD68C035 (void);
extern void TMP_MaterialManager_GetMaterialForRendering_mDC4BDF626B827DE3A6B13933DA0F376D7E77ABD4 (void);
extern void TMP_MaterialManager_FindRootSortOverrideCanvas_m2E4C1A734D8568458C70A1A40F279412E5F76844 (void);
extern void TMP_MaterialManager_GetFallbackMaterial_m95FD4E0D6101A850C43D5917D46D9ED7DAD9CBB3 (void);
extern void TMP_MaterialManager_GetFallbackMaterial_m2DE6B5385E5AF9AF9CBBBE1D7D6F1497ED6498D0 (void);
extern void TMP_MaterialManager_AddFallbackMaterialReference_m945320584E67E1D285BF1D99C63FD19799E94993 (void);
extern void TMP_MaterialManager_RemoveFallbackMaterialReference_m57C507E25D288372CADC9C2C10AA78876B033B92 (void);
extern void TMP_MaterialManager_CleanupFallbackMaterials_m94C5B0D70AE592181468BEB3046F51FEDE61698B (void);
extern void TMP_MaterialManager_ReleaseFallbackMaterial_m270B368718E0FBEC8300F0C518603B6E917E6668 (void);
extern void TMP_MaterialManager_CopyMaterialPresetProperties_m091CA38EAB2582F1792E94216E05706A13DD3965 (void);
extern void FallbackMaterial__ctor_m5AA6484722CD55AD1E40B459CAB79DD8990A713F (void);
extern void MaskingMaterial__ctor_mA1BA8800085879CFA3DE2A0DED61A4AA92C62B2C (void);
extern void U3CU3Ec__DisplayClass11_0__ctor_m28B98E0B4AE129848CDFF36F6F34E9D2D9141268 (void);
extern void U3CU3Ec__DisplayClass11_0_U3CAddMaskingMaterialU3Eb__0_m4F0BC91E6CEE544BBB91FC63751A03DB13F1037D (void);
extern void U3CU3Ec__DisplayClass12_0__ctor_mAB99B1A35D85999D6D5626476FDFBC24C2267F92 (void);
extern void U3CU3Ec__DisplayClass12_0_U3CRemoveStencilMaterialU3Eb__0_m287C61417605FA86EFA7C1727977B50AD14C85E6 (void);
extern void U3CU3Ec__DisplayClass13_0__ctor_m75854EC300C2C88140C6100C42620E466E0A149A (void);
extern void U3CU3Ec__DisplayClass13_0_U3CReleaseBaseMaterialU3Eb__0_m890054ECE6EF0D16429C8BE76649990EAC0CBD58 (void);
extern void U3CU3Ec__DisplayClass9_0__ctor_m848336CD827EA7C72F02A2F5197CC154956B3D84 (void);
extern void U3CU3Ec__DisplayClass9_0_U3CGetBaseMaterialU3Eb__0_m98229F401F0560DF925A73A963C8371504C1A4B0 (void);
extern void TMP_MeshInfo__ctor_m453B9FC30A2CB8AB2A5C868AC4229B7903F033E6 (void);
extern void TMP_MeshInfo__ctor_m95D69F6D719C924C0AF92DCBB1F642D39469CBB5 (void);
extern void TMP_MeshInfo_ResizeMeshInfo_m13DF794141EBDD4446391BAF6FD469EEFE3DD6D1 (void);
extern void TMP_MeshInfo_ResizeMeshInfo_m247290DC2AD29A232C6473904748ADD11779D543 (void);
extern void TMP_MeshInfo_Clear_m002C7A793C6BBFF39C878B909F0162E6EB5C12F8 (void);
extern void TMP_MeshInfo_Clear_m28C815908490A64459F38D5EC110C6823B813888 (void);
extern void TMP_MeshInfo_ClearUnusedVertices_mF5DC41BB72A19486A4079208D13472DD0BDE2CD9 (void);
extern void TMP_MeshInfo_ClearUnusedVertices_m1BDC394210705FC5219A44B3D110BF50F3027B55 (void);
extern void TMP_MeshInfo_ClearUnusedVertices_mB4475A7E8ED25FBCD1D1E91924D9DF3D60AE7A1A (void);
extern void TMP_MeshInfo_SortGeometry_m28C6E9A947C7352F16910BAE2F744087720DBECA (void);
extern void TMP_MeshInfo_SortGeometry_m74ED0FE2065414A659EE9A9C809E1B0B4A8A7734 (void);
extern void TMP_MeshInfo_SwapVertexData_mBB35F36F8E7E6CF1429B26417140570EE94FE718 (void);
extern void TMP_MeshInfo__cctor_mEAB7D06415A81CD66D7478DD7C2818D3589155D2 (void);
extern void TMP_ResourceManager__cctor_m709C83B3CFE2015C6FFA1006351A778F20CC1197 (void);
extern void TMP_ResourceManager_GetTextSettings_mD481945B6E5473F74D39745DC9B593E3B8E4DCDC (void);
extern void TMP_ResourceManager_AddFontAsset_m9C159C7A2E95B8EAF2FA8D2FDF4B7B136C099801 (void);
extern void TMP_ResourceManager_TryGetFontAsset_m96F72D836B1EC6C95FA3B715012F8870F1564268 (void);
extern void TMP_ResourceManager_RebuildFontAssetCache_mD7B939B5E993C10BD5746CA6C654A1CED645D925 (void);
extern void TMP_ResourceManager__ctor_m41A56B8623D6458A03C6A8C3D7470C2F7BB7A1AE (void);
extern void TMP_ScrollbarEventHandler_OnPointerClick_m34AF6B0146F0BC5B3C09C32EED107B4463E7F8DE (void);
extern void TMP_ScrollbarEventHandler_OnSelect_mDF45AA8D470D08691E9F4D615B7DE3BE9AC7135D (void);
extern void TMP_ScrollbarEventHandler_OnDeselect_mF833BEBAB98A0B437BFC9BCB5EE3747434A082F4 (void);
extern void TMP_ScrollbarEventHandler__ctor_m58CED24AFA6F683381D1730590691DDDD5239555 (void);
extern void TMP_SelectionCaret_Cull_m2DC72A9C1EA02ECCB716CD88EFE102299E9006F1 (void);
extern void TMP_SelectionCaret_UpdateGeometry_mF6C6F61B4CD8E34D7D9777EF897639DBFB18755E (void);
extern void TMP_SelectionCaret__ctor_m68388B98DDFDBA26F60C2AF944794D3A43BE8070 (void);
extern void TMP_Settings_get_version_mD4931533CD1B724A2147506EBB7533609220CFE8 (void);
extern void TMP_Settings_get_enableWordWrapping_m6768537460F6CD13F5A581282353B2B98EE22A1D (void);
extern void TMP_Settings_get_enableKerning_mC1031F78F03B64FE3082EFFF3736C0D428A29E22 (void);
extern void TMP_Settings_get_enableExtraPadding_mDB4FE26B3547EA2BF5FFC8CE354680B4EC02CB42 (void);
extern void TMP_Settings_get_enableTintAllSprites_mD2803D776AE9A89D55E521D82C2DD0AB8135A120 (void);
extern void TMP_Settings_get_enableParseEscapeCharacters_mE6CB6DE4E034CA3CA08D0035A16923CC7EB847D2 (void);
extern void TMP_Settings_get_enableRaycastTarget_mC7F0756A3563CCF4788AEA19355C221963BF2260 (void);
extern void TMP_Settings_get_getFontFeaturesAtRuntime_m75190CE90D69720EBDE06438C4B72072D1FD7BBE (void);
extern void TMP_Settings_get_missingGlyphCharacter_mA9AB8619A2A7275DAF4788B0868B4933F9A451A2 (void);
extern void TMP_Settings_set_missingGlyphCharacter_m11E37FBC7A2FE60F7BBE86E545E51AE74A512779 (void);
extern void TMP_Settings_get_warningsDisabled_m2590555E7D849D05AF4B63DEA82407812DB37B22 (void);
extern void TMP_Settings_get_defaultFontAsset_m08D5F2C60E2E313EFAE26C16934F08A499DDFC64 (void);
extern void TMP_Settings_get_defaultFontAssetPath_m839245F25AC624824660B9A7C2A8B0D7F5FFCC99 (void);
extern void TMP_Settings_get_defaultFontSize_m0DD0FFB0811B5EA0DAF7C44BB1F3BA2B8F0C6F1C (void);
extern void TMP_Settings_get_defaultTextAutoSizingMinRatio_m7DAE2F65CA41AF99FEF2AF1B0AF9F2AA0F3992B7 (void);
extern void TMP_Settings_get_defaultTextAutoSizingMaxRatio_m58977C845522D0083F422883C8158BBED78086AE (void);
extern void TMP_Settings_get_defaultTextMeshProTextContainerSize_m466E747B45873AD1DF7E06157B97E731B5AEE5DB (void);
extern void TMP_Settings_get_defaultTextMeshProUITextContainerSize_m0D4A8F331AA212AADCB5BA044E5C79B811ED70DF (void);
extern void TMP_Settings_get_autoSizeTextContainer_m975EB0FF2086BA79F214C099AF1839D4FA2F0DF3 (void);
extern void TMP_Settings_get_isTextObjectScaleStatic_m2F89F247DDA607F93B26EB5B9A698C5C2A975D18 (void);
extern void TMP_Settings_set_isTextObjectScaleStatic_mF18745726FE671226582BD5BC19C6DBE9199DD70 (void);
extern void TMP_Settings_get_fallbackFontAssets_mD671B9D809736E7DC84543568C25BEF9C0B7269D (void);
extern void TMP_Settings_get_matchMaterialPreset_m3C4B2C06C35CF61FCDB127236F522B4454734627 (void);
extern void TMP_Settings_get_defaultSpriteAsset_m1A6D796CB68107284294DAB40442F2CFFA26A672 (void);
extern void TMP_Settings_get_defaultSpriteAssetPath_m0697504D0CD5728F61DE0E1DA9379B8E8CF62E11 (void);
extern void TMP_Settings_get_enableEmojiSupport_mC5DAE356F0396330F8B266F83F44E36BCB3B6AC7 (void);
extern void TMP_Settings_set_enableEmojiSupport_m6BE82A8651B2CAC18F2E29B74431B6221C013126 (void);
extern void TMP_Settings_get_missingCharacterSpriteUnicode_mD82A3253E2CD0C9D467FBD152E4F0FE5E2CBFE2D (void);
extern void TMP_Settings_set_missingCharacterSpriteUnicode_mC77F2F7E2F328440D7BAE8D410EA299434336B39 (void);
extern void TMP_Settings_get_defaultColorGradientPresetsPath_mBB00B879E09F5B4ABC9D92E1CDA90D1C11236798 (void);
extern void TMP_Settings_get_defaultStyleSheet_m348327B30DA1E60CAFBD929D9724E4FECAD23AE4 (void);
extern void TMP_Settings_get_styleSheetsResourcePath_mD9B018B6AA0A84B293970BB92AB5247063CA8262 (void);
extern void TMP_Settings_get_leadingCharacters_m68937B28B95ED59288E22A2F26275AF6F5CF3C7D (void);
extern void TMP_Settings_get_followingCharacters_m2E92204242696D31D43203E388BA5AB178907237 (void);
extern void TMP_Settings_get_linebreakingRules_m9128A20C31E5CBB0D06E0A1537E40617640FCBB2 (void);
extern void TMP_Settings_get_useModernHangulLineBreakingRules_m20EF8E9FBDF86C21A8E30F3B5B2DF997ABB3A060 (void);
extern void TMP_Settings_set_useModernHangulLineBreakingRules_m2BBA6F13171F67AE513A9684BA253C90B626386D (void);
extern void TMP_Settings_get_instance_mFFEE513A89138F5FACD8CE35BF241C2D1F4A9BF4 (void);
extern void TMP_Settings_LoadDefaultSettings_mAD730F80FED7CBB4D15D94ED7A3F0703234C01CB (void);
extern void TMP_Settings_GetSettings_mD7694E5469539C92793D8C1C6C940C875EB0F74A (void);
extern void TMP_Settings_GetFontAsset_m2B9CCF67F14FF0294D8F09A00FA04240640B59E5 (void);
extern void TMP_Settings_GetSpriteAsset_m1E0A427691CBE9DE384E5FEA5FEFE2994178B7E2 (void);
extern void TMP_Settings_GetStyleSheet_m77182866141F5C1699DBC2E25F69FAB3CC347BD0 (void);
extern void TMP_Settings_LoadLinebreakingRules_m77145E921D2176F814DCB968247B938A1379C6B7 (void);
extern void TMP_Settings_GetCharacters_mE221906A29576DD47B3AE3CA25905D65BFA13E1B (void);
extern void TMP_Settings__ctor_m8D99E9A01FB47EDF64A744B6B1AD5B424CB9F38F (void);
extern void LineBreakingTable__ctor_m20DC4ED032712E7234F19604082B5B41DEF713EB (void);
extern void ShaderUtilities_get_ShaderRef_MobileSDF_mD39AD31910FCE56B1B682C626D4D3F69B811D3F4 (void);
extern void ShaderUtilities_get_ShaderRef_MobileBitmap_m7D670C6D6FBB3D84F1CA22E3F6AD4BC4C6AD4929 (void);
extern void ShaderUtilities__cctor_m9360E8869783EBD2DD6CA7308C37A03D7434BCCA (void);
extern void ShaderUtilities_GetShaderPropertyIDs_m3EE2D3D2A31C57AE418FCC0782D0CC9D2FBD0A65 (void);
extern void ShaderUtilities_UpdateShaderRatios_m212CC45DE044E3004EAE6360885C9C02DDC3DEE0 (void);
extern void ShaderUtilities_GetFontExtent_mC52BFBDA68568A8EF78C1AC8D98041C206CC8B4A (void);
extern void ShaderUtilities_IsMaskingEnabled_mC2C8788713E32E1ECB8D2ED17F5FE3335F4FA723 (void);
extern void ShaderUtilities_GetPadding_mACB25967DE353794970CEC89362214C3F65341FA (void);
extern void ShaderUtilities_GetPadding_m163157F37E9267CC1A48349E589B8ECF91B73110 (void);
extern void TMP_Sprite__ctor_mEAF426A39C3129E4D1997ED2D1591F3ADE1A25A2 (void);
extern void TMP_SpriteAnimator_Awake_m6A8FFA0C1EF9E744486051B028DE20B122FADF66 (void);
extern void TMP_SpriteAnimator_OnEnable_mBAA3D31A82A9CDEFC373D10DF860384E31D38BA9 (void);
extern void TMP_SpriteAnimator_OnDisable_mF9A39A9D836AF81C70ED6D301275B814AF3ABBAD (void);
extern void TMP_SpriteAnimator_StopAllAnimations_m0531CA658CF1A4E5A18BC73234FE5CC8318F64F1 (void);
extern void TMP_SpriteAnimator_DoSpriteAnimation_m02F535CA423940D067CABC1F1FE45745409510FC (void);
extern void TMP_SpriteAnimator_DoSpriteAnimationInternal_mCF00A0F5F136AAF118AE0178104FE885E7DE8EF0 (void);
extern void TMP_SpriteAnimator__ctor_mCFCE75C7C04926B5DE46F9FF2C5C9A3904F7FE78 (void);
extern void U3CDoSpriteAnimationInternalU3Ed__7__ctor_m8BBDA4F604B39E235BB82F6E3F20B0FD693688A8 (void);
extern void U3CDoSpriteAnimationInternalU3Ed__7_System_IDisposable_Dispose_m20F949D3F288FCD413EFBF1AD9B7E508334C5DEE (void);
extern void U3CDoSpriteAnimationInternalU3Ed__7_MoveNext_m8FFB7B97D3E8C8A2F5C5239E74A6B93111180A92 (void);
extern void U3CDoSpriteAnimationInternalU3Ed__7_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m226B3A70CA139557EB0F887B08A867394A12AE05 (void);
extern void U3CDoSpriteAnimationInternalU3Ed__7_System_Collections_IEnumerator_Reset_m0D4ED9831F002A26C52E1A969C7AFB0F7A6125B3 (void);
extern void U3CDoSpriteAnimationInternalU3Ed__7_System_Collections_IEnumerator_get_Current_m6C9904888FA484F24441F723715CD60757A8BD21 (void);
extern void TMP_SpriteAsset_get_version_m908EBE63C74A10EF17C07C046D2E76FA54C1ADB8 (void);
extern void TMP_SpriteAsset_set_version_m55AC1FB92B62B73170D86867DDD97FFB5B184751 (void);
extern void TMP_SpriteAsset_get_faceInfo_m1530AA39D6792A0EEE0EAD23159893F418A7E3EB (void);
extern void TMP_SpriteAsset_set_faceInfo_mDF753986EF1CB074813927B23968C70AE69DA1DC (void);
extern void TMP_SpriteAsset_get_spriteCharacterTable_m2F591ADE7DC8DE042B8A32AF84AC169C19CB9D2A (void);
extern void TMP_SpriteAsset_set_spriteCharacterTable_m129E6E18F2DED131EBEBE4C792DFC71DE35968FD (void);
extern void TMP_SpriteAsset_get_spriteCharacterLookupTable_mA1128B86D4510139DB3712D8886F64C3B2A8D30F (void);
extern void TMP_SpriteAsset_set_spriteCharacterLookupTable_mA5C2661AF36B12516A888D463B69F2DAA6AFBD6D (void);
extern void TMP_SpriteAsset_get_spriteGlyphTable_mF26169916988D252767D8801D1E8A2C2D10744A9 (void);
extern void TMP_SpriteAsset_set_spriteGlyphTable_m7207A256A0E4A37F083B1A870C88967C9249E46D (void);
extern void TMP_SpriteAsset_Awake_mAD9AA42B857F3D524A18BCC7512F09D02810EA8B (void);
extern void TMP_SpriteAsset_GetDefaultSpriteMaterial_m71B824EDA9676B6A30EDE264BEE9E54C6D621AAC (void);
extern void TMP_SpriteAsset_UpdateLookupTables_mEC56B333C873E25ED75D6DD85E1628ED8C631545 (void);
extern void TMP_SpriteAsset_GetSpriteIndexFromHashcode_mE1E4D499A7FAD58AB955E1CA8344D640D82219C2 (void);
extern void TMP_SpriteAsset_GetSpriteIndexFromUnicode_m20CA8E503DE1FD6FE80E9418EF9A426DFEDD9014 (void);
extern void TMP_SpriteAsset_GetSpriteIndexFromName_m0CFC33E1F9D56B23BB7DD2DDD1C0886D41A9BE6B (void);
extern void TMP_SpriteAsset_SearchForSpriteByUnicode_m411F1164B0288CA8D94432974177BADDC2A54EAB (void);
extern void TMP_SpriteAsset_SearchForSpriteByUnicodeInternal_m47A6A4AA14924152B0E3E6866A3146ED57E9538A (void);
extern void TMP_SpriteAsset_SearchForSpriteByUnicodeInternal_m79729A0D68B2C4F99DE643D5A5BAFAEDF23281C2 (void);
extern void TMP_SpriteAsset_SearchForSpriteByHashCode_m95F9A3A7C67245EF2C5E16F51F7CD627D005427D (void);
extern void TMP_SpriteAsset_SearchForSpriteByHashCodeInternal_m748846D7F42C95D37EE5DB594EC49AF048AB245B (void);
extern void TMP_SpriteAsset_SearchForSpriteByHashCodeInternal_m232218D0EFC8E51BFA7FB14517E489C5941DDE32 (void);
extern void TMP_SpriteAsset_SortGlyphTable_m0B638BC195978816F72A5D32E1FD2608EB388B68 (void);
extern void TMP_SpriteAsset_SortCharacterTable_mAAE212E44DECC76673001EB17D3BBCBCF1A3CCA1 (void);
extern void TMP_SpriteAsset_SortGlyphAndCharacterTables_mEFA1D54654552CA29AD602DB21D6320A396C9E4B (void);
extern void TMP_SpriteAsset_UpgradeSpriteAsset_mE4C0306402DA32DC3C4BCC8FD11F6C8D35FF2E54 (void);
extern void TMP_SpriteAsset__ctor_mE12AAD30F24A6007B20DAE40E64FFDB78BEE8E12 (void);
extern void U3CU3Ec__cctor_mD89C817B002CF26D3417F56AFE6EBF9DE3AF3197 (void);
extern void U3CU3Ec__ctor_m8DEE5249803AAEB1971F104609B40E1327C4B13E (void);
extern void U3CU3Ec_U3CSortGlyphTableU3Eb__40_0_m51F7FC32BA4529C5284CC0DC3572FAA3257AD8A3 (void);
extern void U3CU3Ec_U3CSortCharacterTableU3Eb__41_0_mECA45EB0A1A8B501039633E87B1A71903857445E (void);
extern void TMP_SpriteCharacter_get_name_m207A7AF57DA74FCC9409AEA2E8581FF4009512A4 (void);
extern void TMP_SpriteCharacter_set_name_m5893C4B6DF938F2E6BB37C578C3B7AB8501F079A (void);
extern void TMP_SpriteCharacter_get_hashCode_mD0A6D291E2DEC9D29C0E6715C4497765E0AB384F (void);
extern void TMP_SpriteCharacter__ctor_mC81C5F64670E2A27460B808E9685102BD9CFDACD (void);
extern void TMP_SpriteCharacter__ctor_m8F33DB3BDCC21B35CAD05BAC8D9DCCADF861DDB2 (void);
extern void TMP_SpriteCharacter__ctor_mE00D2BBE2D3FF0E47A84EF5D0B8C57AB1153FEB8 (void);
extern void TMP_SpriteCharacter__ctor_m12DB9C781228C5D5DDF21E578BE48BDBDE0CD4C6 (void);
extern void TMP_SpriteGlyph__ctor_mE15D3E35E9F68B201CD34569F3A19B22D980D5DE (void);
extern void TMP_SpriteGlyph__ctor_mDFAB2320924E4687FED7E3BA2E1F551ED05B9D36 (void);
extern void TMP_SpriteGlyph__ctor_m8FFB4374AE9F72ABC4E3B7A50FEF593CA2736348 (void);
extern void TMP_Style_get_NormalStyle_mB8B470F18522380C52B6E76D4B287F3D21009CC0 (void);
extern void TMP_Style_get_name_mBA0F1FE80A39D071DC286A2BE674203BE59926E8 (void);
extern void TMP_Style_set_name_m2FCB28B0836C6BE1D8F460538D450295EF6CB80F (void);
extern void TMP_Style_get_hashCode_m19EC41583BBC799AC118324ED1A0405E26990E85 (void);
extern void TMP_Style_set_hashCode_m2EC34153FFE0E3D2CD13138A29A87D13F21D4147 (void);
extern void TMP_Style_get_styleOpeningDefinition_m24394DAB1ADA5D1F7D1A386CED1C51D46BD50B8B (void);
extern void TMP_Style_get_styleClosingDefinition_mA23115F2648B0A6B4AABE9E4043A4A272509209A (void);
extern void TMP_Style_get_styleOpeningTagArray_mB7640D4E0C5A8EF7E1C46AFEFC98909A642ACCC7 (void);
extern void TMP_Style_get_styleClosingTagArray_m286697AF575989E08FA185934FCCA3CD54565A8B (void);
extern void TMP_Style__ctor_mBC114846B015F0C6F9DEF28EF765BED9947538F1 (void);
extern void TMP_Style_RefreshStyle_m90C4C9D26FDE915FE8C6F307E0A4AE2F09BB9C25 (void);
extern void TMP_StyleSheet_get_styles_mD3FB628CE8162DD6F8532FC5B8AF64409E0A9DB7 (void);
extern void TMP_StyleSheet_Reset_mCA48D63055490174046D802C414CD6A5E7291E63 (void);
extern void TMP_StyleSheet_GetStyle_m1A066C8EB0E74AE5D84DEC570BFE301D45FAE078 (void);
extern void TMP_StyleSheet_GetStyle_m14703829269D37F3E69B1DCDA0C508A1DFC4F9A1 (void);
extern void TMP_StyleSheet_RefreshStyles_m5F93989FB986DE16268D2F70D2F9855612547458 (void);
extern void TMP_StyleSheet_LoadStyleDictionaryInternal_m54F7544F778ACD234CE8DC6FEEB3F33E6FD28B69 (void);
extern void TMP_StyleSheet__ctor_mD3DFB99F53DB503018B1613AB6EE21E75512754C (void);
extern void TMP_SubMesh_get_fontAsset_mE8BD0B068366708271FE9EEA521C6A66B0D2D70A (void);
extern void TMP_SubMesh_set_fontAsset_m72B98C846C0BED1F95B642359D9B682E6B99FD5A (void);
extern void TMP_SubMesh_get_spriteAsset_mA42C14F49819531B0C7F9A516FDF98CB64B7E8F8 (void);
extern void TMP_SubMesh_set_spriteAsset_m8090A6E45EB4780476223BF53115ECF3B5297F9B (void);
extern void TMP_SubMesh_get_material_mC2E739573C72E85402DEEDC8BA589146E7738A2D (void);
extern void TMP_SubMesh_set_material_mAD78A696DADACDF54AAB0347F520B7F848E0E517 (void);
extern void TMP_SubMesh_get_sharedMaterial_mDBA65AAA3DF5B047D8A05CF00CBDCC0B22E18957 (void);
extern void TMP_SubMesh_set_sharedMaterial_m39D3800DFDB361235F85066E08FEE26CAD12461B (void);
extern void TMP_SubMesh_get_fallbackMaterial_m56ADAE065A5B9822474BA92763B325D752C6410B (void);
extern void TMP_SubMesh_set_fallbackMaterial_m834BFAF4851FD7EC116808334791B57D3EA4BF13 (void);
extern void TMP_SubMesh_get_fallbackSourceMaterial_mC434387C192AA72EA046F1B87CFF73547C6C1020 (void);
extern void TMP_SubMesh_set_fallbackSourceMaterial_m42EC6CD630C1E531012C8FE7C042D17E3D4B67BD (void);
extern void TMP_SubMesh_get_isDefaultMaterial_m9674DD519EB470FEA52B2BA0D88C03342B93037B (void);
extern void TMP_SubMesh_set_isDefaultMaterial_m519BA7D8650EF98CB6113FC8AAA48BA76EB1C584 (void);
extern void TMP_SubMesh_get_padding_mF12E331397602A9A39ECB674B02412668752F766 (void);
extern void TMP_SubMesh_set_padding_m3004519034FED4E8DAB9A37118B7F624E55E5D85 (void);
extern void TMP_SubMesh_get_renderer_m57EDD2B2B7742D389E019F7D81BFCD7BDA468013 (void);
extern void TMP_SubMesh_get_meshFilter_m84185B727B379F28F2955070CBF99AA14339F34E (void);
extern void TMP_SubMesh_get_mesh_m9AF8E94AA6D6A9B47B76EE0B88A75BCECE8F43EB (void);
extern void TMP_SubMesh_set_mesh_mCE8299D19097FA2472DCEFA3AA07F5AE7D3600DA (void);
extern void TMP_SubMesh_get_textComponent_m0432A85ED37E13DB37CE87B0A09C7C9B5C1369D6 (void);
extern void TMP_SubMesh_AddSubTextObject_m5365D77A55D42718310ED170B0BB1C2DB04DCBBE (void);
extern void TMP_SubMesh_OnEnable_mB044C518B33D5CB3C040D552994581FA754DE233 (void);
extern void TMP_SubMesh_OnDisable_m2A63ACC5E996C6AC1D2A3358972B6592F0B6856C (void);
extern void TMP_SubMesh_OnDestroy_m50A083A81A84781BF0700B1A63B8AAB83C0EBFAD (void);
extern void TMP_SubMesh_DestroySelf_mB9BF2C94B673B284DB58D13EAD1E6798129B7B55 (void);
extern void TMP_SubMesh_GetMaterial_m7FA3D54A057606FA90DC3841AAD76C3877BBDA54 (void);
extern void TMP_SubMesh_CreateMaterialInstance_mCBD7450E65428732A15ADD20F0A5BE7EA1DBF2BA (void);
extern void TMP_SubMesh_GetSharedMaterial_m7C37BF890F16727019DF5A7EA36FABE4E5D21F42 (void);
extern void TMP_SubMesh_SetSharedMaterial_m894423F785E34D24902F385582889CF9170CEA4F (void);
extern void TMP_SubMesh_GetPaddingForMaterial_mE7297313C36D02A7879790C4EEA21551B52B9544 (void);
extern void TMP_SubMesh_UpdateMeshPadding_mC15404FE24CC51BCA2D8BC8B7A15934FF71ACAAF (void);
extern void TMP_SubMesh_SetVerticesDirty_m55CA9BE0F62ED78693A82CD3A583FA24F1C734B1 (void);
extern void TMP_SubMesh_SetMaterialDirty_mF4015AA542DC6AF1A7E554CF66A42AB0939D826C (void);
extern void TMP_SubMesh_UpdateMaterial_mF2AA7298784A74354917AE11C33C06DF5EE48FD3 (void);
extern void TMP_SubMesh__ctor_m94A6C004CCE46FD9B6DDFBBD8436B386594FABEF (void);
extern void TMP_SubMeshUI_get_fontAsset_mFA21AA0E69C872A2E9AD4F2F7A4E2E61B343275F (void);
extern void TMP_SubMeshUI_set_fontAsset_m76AFB364ECE0D6CBD609CA2FC1BD28BBC09437A6 (void);
extern void TMP_SubMeshUI_get_spriteAsset_m657B36AC1C3BFA60B17013CB401750F80719F7E4 (void);
extern void TMP_SubMeshUI_set_spriteAsset_m535AEEBE4A7548A93E6A252DF62C5BCC3578F05C (void);
extern void TMP_SubMeshUI_get_mainTexture_m812ABD578CE01020099166A3F9F63E31E635E4F1 (void);
extern void TMP_SubMeshUI_get_material_mCEDB25BF8F4D1ADBDDE5E0D6A9D6BE34352B59C0 (void);
extern void TMP_SubMeshUI_set_material_m42EDB47D729254FE9BA1A521AD957ED2D9CED532 (void);
extern void TMP_SubMeshUI_get_sharedMaterial_m9F6E8D48BE941352C6395CE6B25D1A026F9B1A50 (void);
extern void TMP_SubMeshUI_set_sharedMaterial_m76325941FAD77DA10D3BD3B85506D0473CD6DB2B (void);
extern void TMP_SubMeshUI_get_fallbackMaterial_mAF8B16164650A91CA244445F5717BCEA12B75CAE (void);
extern void TMP_SubMeshUI_set_fallbackMaterial_mDABCF9FA80529D8A6452EBD3C9B52E1D4A8F6A08 (void);
extern void TMP_SubMeshUI_get_fallbackSourceMaterial_m0CCD5224BD22B4AF5B8D7994040F7925FA047787 (void);
extern void TMP_SubMeshUI_set_fallbackSourceMaterial_m6176ADCD1C46E99F4FC95189D58B406397E54C0E (void);
extern void TMP_SubMeshUI_get_materialForRendering_m49CDCE464B0837AF4EAC89AF71B7CB8602BE1A27 (void);
extern void TMP_SubMeshUI_get_isDefaultMaterial_mF713B637150AA5A39FB25D9C296A0D2011A7F1E5 (void);
extern void TMP_SubMeshUI_set_isDefaultMaterial_m1CA334C661C393A92BB29993C559F43FE899E525 (void);
extern void TMP_SubMeshUI_get_padding_mFE0F475014CBD79033493C185323B095356C4D98 (void);
extern void TMP_SubMeshUI_set_padding_m8EF3F2C730BADF9C71D789E2B964A0FF0FBC44CD (void);
extern void TMP_SubMeshUI_get_mesh_m18BAE0DB357DC5D7993D07BD826429AF727548E2 (void);
extern void TMP_SubMeshUI_set_mesh_m253BA01B0CF8F664D4C8910C746C56C863A76191 (void);
extern void TMP_SubMeshUI_get_textComponent_m899050C714DCF7C38409E40ACED46128426E5981 (void);
extern void TMP_SubMeshUI_AddSubTextObject_mDABF53418F7955156FFC98AAB400EF9BB3EC99F4 (void);
extern void TMP_SubMeshUI_OnEnable_m5FC1C2F3A131CDD8AEBE462F6E02F98C8EFD91A2 (void);
extern void TMP_SubMeshUI_OnDisable_m7E93F77D46B86974F82E651F1C5ABEC4965E7A19 (void);
extern void TMP_SubMeshUI_OnDestroy_m9B06AF411C751749285D664C97E4534F8DB46421 (void);
extern void TMP_SubMeshUI_OnTransformParentChanged_m1C0D38B644942ABCCE807FD0EDA40069FCD4F758 (void);
extern void TMP_SubMeshUI_GetModifiedMaterial_mE55896B318E1B14EA2E05E8B4C9B7395F889637A (void);
extern void TMP_SubMeshUI_GetPaddingForMaterial_m59C406EAAF3622C5C66AC02B57EE54017E6F80C9 (void);
extern void TMP_SubMeshUI_GetPaddingForMaterial_m5600CCCC50A30C965D5522C7CDC62559B1AACD3E (void);
extern void TMP_SubMeshUI_UpdateMeshPadding_mFE485B3241997E25482483616D1B5482EA8BBC81 (void);
extern void TMP_SubMeshUI_SetAllDirty_m17BC0FAF84604A8419F055074E538D3B92D8DFEC (void);
extern void TMP_SubMeshUI_SetVerticesDirty_m6BC1FB6642A719D0B542920D87C47B91BCAE8F3D (void);
extern void TMP_SubMeshUI_SetLayoutDirty_mF9E12FA430FDF4CAB2142C256069206F66F4BE39 (void);
extern void TMP_SubMeshUI_SetMaterialDirty_m427E2E5CA2522811C510ADFB88183F5C7168C41E (void);
extern void TMP_SubMeshUI_SetPivotDirty_m7CB8262E46A59A4309FB63BBDC85305DB66AC08C (void);
extern void TMP_SubMeshUI_GetRootCanvasTransform_m044D69EEDD595930E39EE9B58180440A1C318699 (void);
extern void TMP_SubMeshUI_Cull_mC2938541DF75ECBE0A20743633BB59E0E2FB2C8D (void);
extern void TMP_SubMeshUI_UpdateGeometry_m8A12469615865F793E84FD08A01CA20C82344504 (void);
extern void TMP_SubMeshUI_Rebuild_m157FB1223ADFBB21D2C66599D9130FF09687009A (void);
extern void TMP_SubMeshUI_RefreshMaterial_mD91D017F05BFC8667A26179D17565E3411A0FE75 (void);
extern void TMP_SubMeshUI_UpdateMaterial_m4147C455FDAE0B050969761CEA78CC665D2B162B (void);
extern void TMP_SubMeshUI_RecalculateClipping_mAF6020BB8D612D61DD64C6B3A66E21B1ED27E629 (void);
extern void TMP_SubMeshUI_GetMaterial_mFE6F9315B7C5FCD8DC6F5B885D0DE5F6E860FD22 (void);
extern void TMP_SubMeshUI_GetMaterial_m42B838E7CFD90166E7AB6288140E0DDC42C5BFBD (void);
extern void TMP_SubMeshUI_CreateMaterialInstance_mC6A3BF4276D9FDB1120EDE06B688F57BD50012B2 (void);
extern void TMP_SubMeshUI_GetSharedMaterial_m3D24E4226259E175D6BCB0D846D5D6D6BC2740D0 (void);
extern void TMP_SubMeshUI_SetSharedMaterial_m3E8AB169F4C47E062E3996E25F2F9D015FDAAA0C (void);
extern void TMP_SubMeshUI__ctor_m9AA49928094650F82BE200A086839EA4DABF3D25 (void);
extern void TMP_Text_get_text_mF8371DA9FE7C67218422F6A5B5F4BAB1219EB22F (void);
extern void TMP_Text_set_text_m7802824EFC54A60A4FEF444FD34301663CF974EA (void);
extern void TMP_Text_get_textPreprocessor_m342C8D483950A64497716F34BCCA853A2D5D430C (void);
extern void TMP_Text_set_textPreprocessor_mF26E0EFC2718F08112B9C4065EFB6C7D4322D56F (void);
extern void TMP_Text_get_isRightToLeftText_m91867E4BBD159ACF669FF0103FB15194E5A35910 (void);
extern void TMP_Text_set_isRightToLeftText_m92473AB03681DE06DCE0845AE43B23F13FEF5D25 (void);
extern void TMP_Text_get_font_m1F5E907B9181A54212FBD8123242583C1CA4BE2A (void);
extern void TMP_Text_set_font_mC55E4A8C1C09595031384B35F2C2FB2FC3479E83 (void);
extern void TMP_Text_get_fontSharedMaterial_mF1F4B4A3379A9928CF2CD51835381B31C0976C82 (void);
extern void TMP_Text_set_fontSharedMaterial_m4C3E1FAD0780FF04D2998177B794C773EE3B0DD7 (void);
extern void TMP_Text_get_fontSharedMaterials_m09C5F786FE99C75C954C548AFDED330C4785C4D3 (void);
extern void TMP_Text_set_fontSharedMaterials_mE82D24FE08F46E5E59438F51938A6B99D74EE376 (void);
extern void TMP_Text_get_fontMaterial_m4EBEC9AF78B5B66C983A98F78948E753EE4DDFC6 (void);
extern void TMP_Text_set_fontMaterial_m091675AB7E417CD77F8C69B3AEE5B78BBCF59922 (void);
extern void TMP_Text_get_fontMaterials_m354B3F7CF4AB2B7E38C2610D8403D14744286A55 (void);
extern void TMP_Text_set_fontMaterials_m0DC39367F86944E57BE16634A45225ACA97F461B (void);
extern void TMP_Text_get_color_m4A843DBD73462B4EE0F823039AE9F8499102D9B5 (void);
extern void TMP_Text_set_color_m776196F566F4F8CD25263BB40CA2D3AE5F2D444B (void);
extern void TMP_Text_get_alpha_mF6093A9BEAC44060DA2CC7A61097DB99A25E7DAE (void);
extern void TMP_Text_set_alpha_mD01D24A2E320F30E26BD42AEE8137F9C4F4EBB57 (void);
extern void TMP_Text_get_enableVertexGradient_mB5CFDE007B14BB0425CEACA8FE33C8B2B29769A5 (void);
extern void TMP_Text_set_enableVertexGradient_m21A55C744B7BF817B6AA349FCB8C2AC54E8CCACA (void);
extern void TMP_Text_get_colorGradient_m29541E9BEF4511BEEB2B4951E5BF07DA01AC9105 (void);
extern void TMP_Text_set_colorGradient_m372D6EEDBE955EC7F33895F57E760802937808C8 (void);
extern void TMP_Text_get_colorGradientPreset_mEA5E8B98E88641BE9437222F33DDCCB1B05566B7 (void);
extern void TMP_Text_set_colorGradientPreset_m21DD271B3D1ADF6E81ED68922809F158612A7B46 (void);
extern void TMP_Text_get_spriteAsset_m2D4DEEA11BF5B9DEBA1859A401A15C455529D07A (void);
extern void TMP_Text_set_spriteAsset_mAA6F8F2CD83E208C185A30367CF7E308B5A1F750 (void);
extern void TMP_Text_get_tintAllSprites_mFDB02B03D3513B536D47260FC9B5CCC8BB471C83 (void);
extern void TMP_Text_set_tintAllSprites_mFFCB8F9B1E8C23016C460BC26024DAEC7CD49D65 (void);
extern void TMP_Text_get_styleSheet_m72E52DC4A12109C1D0C46F2CF89F4A0D439913DC (void);
extern void TMP_Text_set_styleSheet_mBADF3BE1110DBC043A75F42AD0C5FB8C245BC1BF (void);
extern void TMP_Text_get_textStyle_m18773DC7DEFAA035C8D86475294AD3C0DDB52603 (void);
extern void TMP_Text_set_textStyle_mBD9F0E7332606863C32DC78E1BD163E7858D9425 (void);
extern void TMP_Text_get_overrideColorTags_mACA2CBC4B1D3033B30322B2366E1AA97AFB81E41 (void);
extern void TMP_Text_set_overrideColorTags_m9F9D83AA86AA7A310EA41F66A029F11100519CED (void);
extern void TMP_Text_get_faceColor_mC6A763106D17F58C97965AFD5EE47646C813B4B8 (void);
extern void TMP_Text_set_faceColor_m5E9FCC324958ABD25823193117B9BA5304043E51 (void);
extern void TMP_Text_get_outlineColor_mA443B0C207A8B6A5E2546A31F46A3106FB0573EF (void);
extern void TMP_Text_set_outlineColor_mBEFF42BF9AB15BC7C1DA78489CB4F32A2270F7F0 (void);
extern void TMP_Text_get_outlineWidth_mC94A3AD32458544743E07AE0A495A86214823C29 (void);
extern void TMP_Text_set_outlineWidth_m33ADF665CB2D3DBD9FB3F70DE62979FD63ADD592 (void);
extern void TMP_Text_get_fontSize_m13A8365A56EA2B726EAD826B4A69C8918A528731 (void);
extern void TMP_Text_set_fontSize_m1C3A3BA2BC88E5E1D89375FD35A0AA91E75D3AAD (void);
extern void TMP_Text_get_fontWeight_m9A7A4ED9ECA3A192B28E24E94D40D5B545D6118E (void);
extern void TMP_Text_set_fontWeight_m4F7016B98AAA89004CFBEBBBE1C4E35B94EF0EE2 (void);
extern void TMP_Text_get_pixelsPerUnit_mBCEF0125AEB4F14A5BA5D179C3523FD382E45796 (void);
extern void TMP_Text_get_enableAutoSizing_m0A101957A4E1D156437E454DF813ACE3714F0FE7 (void);
extern void TMP_Text_set_enableAutoSizing_mDD34BC7AA735EEBEB916FF5C9791B1502F65FBCA (void);
extern void TMP_Text_get_fontSizeMin_m5F97E2EFFE86CB4BFFFC31E167E1E577134EF05D (void);
extern void TMP_Text_set_fontSizeMin_mEAF970BB9CA053DF953AF83E638EA0F1D885358F (void);
extern void TMP_Text_get_fontSizeMax_m8FAB0C39D22B722F6AA6CF15E6C0636715D64BD4 (void);
extern void TMP_Text_set_fontSizeMax_mC84B7090F5CE69BA63556A71FD63ABD67C911750 (void);
extern void TMP_Text_get_fontStyle_mC34CC5EBEDD43CE93BA911CCC4D33F9697838586 (void);
extern void TMP_Text_set_fontStyle_m61931944B2E922D50087312D80F8685A2F29EBF8 (void);
extern void TMP_Text_get_isUsingBold_mA0F9BE071B0F9DB995BC04D1CD409CA5C5AF6CF0 (void);
extern void TMP_Text_get_horizontalAlignment_mB33E135CD810BE68FA3E29D57D360575DE18C4CA (void);
extern void TMP_Text_set_horizontalAlignment_m5621041CDB60BAD5BAB18AE01701ADA2FD2231B2 (void);
extern void TMP_Text_get_verticalAlignment_m83109ED3E925A505F5E9E9142B07829A56CCB54A (void);
extern void TMP_Text_set_verticalAlignment_mA79C8E375EEC0B960D517D2D8ED217564ABBFB82 (void);
extern void TMP_Text_get_alignment_m52C559D8E496889812623C56CD8EA056FD92D565 (void);
extern void TMP_Text_set_alignment_mE5216A28797987CC19927ED3CB8DFAC438C6B95A (void);
extern void TMP_Text_get_characterSpacing_m48A3B73EFBF47B5227D2BB4816FCFF628254C8FB (void);
extern void TMP_Text_set_characterSpacing_mDCD34D244A502CA21CEB817E1F4CAC5BC6CCBA63 (void);
extern void TMP_Text_get_wordSpacing_mF3DF1445C78E06195904FCF0293E63654C527D33 (void);
extern void TMP_Text_set_wordSpacing_m319C51E318DBC91F236F3CC65ED24787903F7E1E (void);
extern void TMP_Text_get_lineSpacing_m7481D705EAD920B8D143D19A270D44CDABDAA251 (void);
extern void TMP_Text_set_lineSpacing_m1BA54B315F7472AE0E7B721CA7481016643591A7 (void);
extern void TMP_Text_get_lineSpacingAdjustment_m3858BA838BBFBA60A0A1DDCB195075C6620CF637 (void);
extern void TMP_Text_set_lineSpacingAdjustment_mAC9A57D852EBAD8DD53ED2F1DE316C0DA52659FB (void);
extern void TMP_Text_get_paragraphSpacing_mCCBC792CAE59958E92EB04B8E636AA2066534713 (void);
extern void TMP_Text_set_paragraphSpacing_m69921E35B44DE397FE604590913CAFB7DBFBAF30 (void);
extern void TMP_Text_get_characterWidthAdjustment_mE879BF9A6273376AEE54BE88745ABE7944DBF26A (void);
extern void TMP_Text_set_characterWidthAdjustment_m11B7CC28C0A7FFC6434DB671C635691B529071BE (void);
extern void TMP_Text_get_enableWordWrapping_mF228EF12091EF9FB53E44B6B0278B610E350E551 (void);
extern void TMP_Text_set_enableWordWrapping_mFAEE849315B4723F9C86C127B1A59EF50BE1C12F (void);
extern void TMP_Text_get_wordWrappingRatios_m3316BC010D7B02829CE0B86868B01419C81ED072 (void);
extern void TMP_Text_set_wordWrappingRatios_m83A82AE875C4CD836D5802A1C051AF07CA2A0D85 (void);
extern void TMP_Text_get_overflowMode_m494E5C01E450AF8F4F344856D289D0FDEB8DDCB4 (void);
extern void TMP_Text_set_overflowMode_mB8911BA07CEE0AC1E4E108B5EB79B230F90E96A1 (void);
extern void TMP_Text_get_isTextOverflowing_mF29482F663A6195FF48628DF3B6F5ACAEF8538D0 (void);
extern void TMP_Text_get_firstOverflowCharacterIndex_mB9AEEBC749FBDEA2E73023CBA83FA2BE72D08480 (void);
extern void TMP_Text_get_linkedTextComponent_m84DA92BFD208833ED4C1EC4C4F537F5D594EF4F0 (void);
extern void TMP_Text_set_linkedTextComponent_m08B4CBAD470F918E2D2E19CE96B2443F38B76D93 (void);
extern void TMP_Text_get_isTextTruncated_mCB152B5BD9B3FFB994F6B89E2ED89A3602A750F3 (void);
extern void TMP_Text_get_enableKerning_mA8CA8FB9322358B72F0F7C49954AE3C0E618DDDD (void);
extern void TMP_Text_set_enableKerning_m681685E06B8789F5F2B7043EBEA561AAE48E82BD (void);
extern void TMP_Text_get_extraPadding_m84294178A4E3BFD708FC746DB98CB0A64FBC35AA (void);
extern void TMP_Text_set_extraPadding_m26595B78EDE43EFBCCBF7D5E23932ADCB983EF32 (void);
extern void TMP_Text_get_richText_m630DE7C1ABC507556E716428264A793423ACAB27 (void);
extern void TMP_Text_set_richText_mAB3D04F620E13F02117B34BBA2EF7BD30AAE6F0F (void);
extern void TMP_Text_get_parseCtrlCharacters_mB10A3CBD2DEFB7BB15BC6330951DCDAB814D2584 (void);
extern void TMP_Text_set_parseCtrlCharacters_mE733B4A0271EEFA977C39E7F86DDDF73C52D1976 (void);
extern void TMP_Text_get_isOverlay_m1A9199A9C2FBB09BEAA0B0B2E3D41CDF8A3B708B (void);
extern void TMP_Text_set_isOverlay_m0DA2AC113AE402CA25097641AD38D0822C6D5561 (void);
extern void TMP_Text_get_isOrthographic_mBC78A70B2233363411D9D918346DFE19DF3CF72B (void);
extern void TMP_Text_set_isOrthographic_mF58B9C6B492D4FD1BA0AB339E4B91F0A1F644C18 (void);
extern void TMP_Text_get_enableCulling_m233860FA65153E4C5C3FE3E78B835D4230FC45B0 (void);
extern void TMP_Text_set_enableCulling_m3CDE2F50BF96E110427D2C1B3505436D87576102 (void);
extern void TMP_Text_get_ignoreVisibility_m479580B3550B3652B3E4E889B8E62902633C7477 (void);
extern void TMP_Text_set_ignoreVisibility_mB06EE9EA50439B339824FDF4B52CAF423AC1209D (void);
extern void TMP_Text_get_horizontalMapping_mDD4C7F3FF8D4619EA539A964636EC841FCFE7873 (void);
extern void TMP_Text_set_horizontalMapping_m26A114EFF3D3143214F753521B4DCB2971C19C84 (void);
extern void TMP_Text_get_verticalMapping_mCD5A83DF6CAA818E89F483F11B6748538D7E9C35 (void);
extern void TMP_Text_set_verticalMapping_mBF1DBAC92E4E6BE48F39275FAFF5F8106FABD317 (void);
extern void TMP_Text_get_mappingUvLineOffset_m296EF64BABC2BA1A47BD7309B10027E51BB37394 (void);
extern void TMP_Text_set_mappingUvLineOffset_m963D80134C47160C7896A7C86FFF3C4B3CF51E73 (void);
extern void TMP_Text_get_renderMode_mE67A34CDA63B22321E3C511078F9CC42B19EEC8C (void);
extern void TMP_Text_set_renderMode_m091533DEE7FD20A61249DC52C786ED4FFE5A5C2A (void);
extern void TMP_Text_get_geometrySortingOrder_m7A757613E064B108D3598B3953AB846E3B63B756 (void);
extern void TMP_Text_set_geometrySortingOrder_mFE993584D0FDB12A43F0F1907BD1FFAF240E0D95 (void);
extern void TMP_Text_get_isTextObjectScaleStatic_mBAC6CC2ACE413148E868A14281629B9C72851940 (void);
extern void TMP_Text_set_isTextObjectScaleStatic_m8436FC38400ABE08F513770AF9C8CC6743DBE092 (void);
extern void TMP_Text_get_vertexBufferAutoSizeReduction_m304AA345FEF2D0D542E2B1F2CB9AB51464BFDB91 (void);
extern void TMP_Text_set_vertexBufferAutoSizeReduction_m188984707109669597440E6F250B124D6FB66270 (void);
extern void TMP_Text_get_firstVisibleCharacter_mD2CEE9A9803C530DA337B22BD994B9CEBE15AE63 (void);
extern void TMP_Text_set_firstVisibleCharacter_m343804C8FF610EB13CCB14E8D54C889BC356AD53 (void);
extern void TMP_Text_get_maxVisibleCharacters_mF695995258B5013340B8C026B2A0FA643D5FD302 (void);
extern void TMP_Text_set_maxVisibleCharacters_mEDD8DCB11D204F3FC10BFAC49BF6E8E09548358A (void);
extern void TMP_Text_get_maxVisibleWords_mD9E44CE8FBCB6F7182716E61EB435B61048155B9 (void);
extern void TMP_Text_set_maxVisibleWords_mE2EDC75AA5E4795233F753643202868E4D3226B9 (void);
extern void TMP_Text_get_maxVisibleLines_m9E8FB188E50DCF321793C7E75B7F90E2142AC52B (void);
extern void TMP_Text_set_maxVisibleLines_m55D236A0DA8C5A10C793663674FA3A44F61DF861 (void);
extern void TMP_Text_get_useMaxVisibleDescender_m3A85730B4F5723C8B7884B89FB70EE0A6888165B (void);
extern void TMP_Text_set_useMaxVisibleDescender_mBFE9133E5EEF987942919D4FE369CB03A0EBC559 (void);
extern void TMP_Text_get_pageToDisplay_mAA3CCC7BD6CA9430558F3409E05B6E754D82C730 (void);
extern void TMP_Text_set_pageToDisplay_mBD985B613FCEC04266FDA43E916B19DD505D7469 (void);
extern void TMP_Text_get_margin_mB8102487C6CFA509555D3A892C899E0A1E86CBCE (void);
extern void TMP_Text_set_margin_mE431DCEED182B2979246E04233F943E8D3B82D5D (void);
extern void TMP_Text_get_textInfo_mA24C606B8EA51436E4AA3B9D6DCDFA7A8995E10E (void);
extern void TMP_Text_get_havePropertiesChanged_m42ECC7D1CA0DF6E59ACF761EB20635E81FCB8EFF (void);
extern void TMP_Text_set_havePropertiesChanged_mA38D7BC9E260BF29450738B827F2220A05662B31 (void);
extern void TMP_Text_get_isUsingLegacyAnimationComponent_mC52DDE08FAB3DA14C5BDDAF7533A8465B30CCE7A (void);
extern void TMP_Text_set_isUsingLegacyAnimationComponent_mC3A3CB0EBBE9A4AF0106EDC9EDB7DC1D0AD62170 (void);
extern void TMP_Text_get_transform_m6BD41E08BFCFCE722DFCE4627626AD60CA99CCA8 (void);
extern void TMP_Text_get_rectTransform_m22DC10116809BEB2C66047A55337A588ED023EBF (void);
extern void TMP_Text_get_autoSizeTextContainer_mF7DEF97EAB3EEE86558E5A173264DA46068F7E13 (void);
extern void TMP_Text_set_autoSizeTextContainer_m47F5010FC3B3496C58017BC5B21E51FF8BD0D448 (void);
extern void TMP_Text_get_mesh_m7B90E1F477480ADB825851B54F898CC39B6DF376 (void);
extern void TMP_Text_get_isVolumetricText_m176FAF1E14C8054B274E7972EA02D84D3EB4E074 (void);
extern void TMP_Text_set_isVolumetricText_mE827C3B8F33DB163A48F2A314A66D02274372B9B (void);
extern void TMP_Text_get_bounds_mAEE407DE6CA2E1D1180868C03A3F0A3B6E455189 (void);
extern void TMP_Text_get_textBounds_m0D3E180B72130830D1C16BC7E5097AF2958E2740 (void);
extern void TMP_Text_add_OnFontAssetRequest_m5CF2F09BB8B2E7E1F11488B48FDF3CEF23CEEA84 (void);
extern void TMP_Text_remove_OnFontAssetRequest_m6B616134E9114F5ADC8034A7B2E38D41488A8BF9 (void);
extern void TMP_Text_add_OnSpriteAssetRequest_m676ECA34B7C6E92AFF2A20AFC1A9AE2DE60CEA2F (void);
extern void TMP_Text_remove_OnSpriteAssetRequest_mDA9E1F66F082FC479A3EF7D8E530317B38563870 (void);
extern void TMP_Text_add_OnPreRenderText_m52F3DEA8A022AFA077BB776BB59734B1C9D5D9CA (void);
extern void TMP_Text_remove_OnPreRenderText_mB46FBE276D13CB41194906F9FF92EDE25D7641BA (void);
extern void TMP_Text_get_spriteAnimator_m3DB8B24C845D9BE3C1E117F39DE45F202D7F9321 (void);
extern void TMP_Text_get_flexibleHeight_m810BADBB953332F1112BEDA609F0D2D899E75347 (void);
extern void TMP_Text_get_flexibleWidth_mAE1FB54D0F3EB910F566B87871BB7CCE5B3250D7 (void);
extern void TMP_Text_get_minWidth_m6FDD2AE333AC038F0ADB47FE30AF428A44160B03 (void);
extern void TMP_Text_get_minHeight_m54FCFDDB577882C173B9677008A2B97E92533AC7 (void);
extern void TMP_Text_get_maxWidth_mA2913A569850C5B0186FFC02EBD9B17D7E4123D9 (void);
extern void TMP_Text_get_maxHeight_m5673CE516B95A7268D1DD29CB14F26EB443688C2 (void);
extern void TMP_Text_get_layoutElement_m6D5276FEE925F3E8CA6DD4C554F8BE1A88A5E6E6 (void);
extern void TMP_Text_get_preferredWidth_mE30D1F5B8573BD0A558054D004A53DE868BD208A (void);
extern void TMP_Text_get_preferredHeight_m4F28E8FB388AFF1DC052F5F982DB2F959598B004 (void);
extern void TMP_Text_get_renderedWidth_m61F93CE4B988DBCF6332EE731223AF0F72471146 (void);
extern void TMP_Text_get_renderedHeight_mD905DB93B2634BB5EE481C1F71D2CAFCEF5C738D (void);
extern void TMP_Text_get_layoutPriority_m6D8DF0CCD8515FFCFA3B74F7946B32072B8EC596 (void);
extern void TMP_Text_LoadFontAsset_m3E175C3A91E04695300603D04F10E6432C1D870C (void);
extern void TMP_Text_SetSharedMaterial_m2BC9A6E29786D4221CA8086F199B54691DAF0569 (void);
extern void TMP_Text_GetMaterial_mF58308E4AA9C3F7448FF976710B9206C066C5406 (void);
extern void TMP_Text_SetFontBaseMaterial_m6E38354D0E49FAE5EBD408A22F92236C1D68E33F (void);
extern void TMP_Text_GetSharedMaterials_m5C748AC07C4282734F6D4C553769BFE3B63F21B5 (void);
extern void TMP_Text_SetSharedMaterials_m3D152FA115539A0362D44135EE48BCAAFB56F2D6 (void);
extern void TMP_Text_GetMaterials_mA3F8E1546BE9C5D84DC349A8B1739DB1D16F0679 (void);
extern void TMP_Text_CreateMaterialInstance_m201B4389FB351E5316ACA573F4593EA5F44D1D0A (void);
extern void TMP_Text_SetVertexColorGradient_m35E9AB171BCC614A2989143F329C96BD3E914151 (void);
extern void TMP_Text_SetTextSortingOrder_m5E42564CFECE090388DE121858E94CC8903F4402 (void);
extern void TMP_Text_SetTextSortingOrder_m17CA540342EAA44144E32829D672161E6C6F425B (void);
extern void TMP_Text_SetFaceColor_m865370BB950DE1BE4111341536AE062C046E5FDC (void);
extern void TMP_Text_SetOutlineColor_m22F952AFBAE8CE4564B02F573BEB9FDC30705555 (void);
extern void TMP_Text_SetOutlineThickness_m2CBC33AAA504B07B48DFE771986230C772FE605C (void);
extern void TMP_Text_SetShaderDepth_mB508746026A248495C693EC1039E3A562D8A704E (void);
extern void TMP_Text_SetCulling_mEC62FDEFC0E222313165637A26D700C29DAE389D (void);
extern void TMP_Text_UpdateCulling_mFB9FD3AF46C9222182056C808198BEDB8810C82F (void);
extern void TMP_Text_GetPaddingForMaterial_m381ACEBE9696389001F7853D821FECC4E83A2111 (void);
extern void TMP_Text_GetPaddingForMaterial_m5FB68F03D16813FCFC20F70ACC50DBAFEB420196 (void);
extern void TMP_Text_GetTextContainerLocalCorners_m588C57396E94A4BD6B1311542E985E6587665845 (void);
extern void TMP_Text_ForceMeshUpdate_mFEB0D607572734B168FCD4954BB2F32F9CE0AE7E (void);
extern void TMP_Text_UpdateGeometry_m2FA2F775454629B5ED0CF4B8E089D48B8B1A31DA (void);
extern void TMP_Text_UpdateVertexData_m2E77B6DA477425BFDA2661C6BD71E65E42CA3A98 (void);
extern void TMP_Text_UpdateVertexData_m79089A6FF3818129609C9ACF34D79232FA4C5493 (void);
extern void TMP_Text_SetVertices_mB1F51FB2B5247428AB1A302488BAFDCED686C0C1 (void);
extern void TMP_Text_UpdateMeshPadding_m1B9F1E66E3B3E3C305567E412328865A083CD430 (void);
extern void TMP_Text_CrossFadeColor_mAB054E0720A156FC584B2D71878F6C24160FC07C (void);
extern void TMP_Text_CrossFadeAlpha_mF4C9347458127DBC88C015AF4872486B7AB2E86E (void);
extern void TMP_Text_InternalCrossFadeColor_m217E640043CBDE6D81B948B138D5C9AB9B33CF71 (void);
extern void TMP_Text_InternalCrossFadeAlpha_m2E502349E3F0991FFA5D6D19FC6E14E3E9F89B53 (void);
extern void TMP_Text_ParseInputText_m3B4CF13CC0BF8E8A2B3980BD191A3B2FA421E36C (void);
extern void TMP_Text_PopulateTextBackingArray_mFD376BD29DBC5157116653E031FA2BB8AD85CB8B (void);
extern void TMP_Text_PopulateTextBackingArray_mDAFAFBA1D6EF883BBA870BEC34F4AFC52A8D4799 (void);
extern void TMP_Text_PopulateTextBackingArray_m2DD1214AFFFF0214596222BCC5B759D0F8D48557 (void);
extern void TMP_Text_PopulateTextBackingArray_mF50056377989BB902E9ECB7B8607BD5CAE2B9EC8 (void);
extern void TMP_Text_PopulateTextProcessingArray_m2D1F8D3CAE8F1F29242547BCCC91D1226FA9A6F0 (void);
extern void TMP_Text_SetTextInternal_mE5AAC38C055046B9EE3228640DAFA627C5BDF924 (void);
extern void TMP_Text_SetText_m848189C290727009A95A00E432B66DFB2F2C3454 (void);
extern void TMP_Text_SetText_mC6973FFC60DB6A96B0C4253CD2FD9D0789ECC533 (void);
extern void TMP_Text_SetText_m033947AEEEBDA12707E4B0535B4CCD7EB28B5F31 (void);
extern void TMP_Text_SetText_m91C93245F1F0BD149D7E81A870B1E156EBB50DD7 (void);
extern void TMP_Text_SetText_mA55E85AB5C2C2ECC55F91825828DD3CCF2173E80 (void);
extern void TMP_Text_SetText_m8F8C230992A14AC54379698221FA40B5AD0250E3 (void);
extern void TMP_Text_SetText_m888964CBEFDBE9D7788D25D8EA11D832B52CC739 (void);
extern void TMP_Text_SetText_m8AF09C554904D1C1B0004879BA3A9F1C585CB41B (void);
extern void TMP_Text_SetText_m5093EBC3B7161E3775B6A6EA2F3E7C4FAA55814B (void);
extern void TMP_Text_SetText_m229965F9267D1A1D825FF32828DDC9528A40F015 (void);
extern void TMP_Text_SetText_mEFBC8BA593BB9B7A6F58BE8A1EF74F83E7B4CFF1 (void);
extern void TMP_Text_SetText_m060E57CFB07010482FBDD53A653F0A61A4CDDE74 (void);
extern void TMP_Text_SetText_mCF423F9A56990664E9711E71AEFB464987179AFF (void);
extern void TMP_Text_SetCharArray_mCCBCFF7608CA622F9A7E15E027662DB8561583B5 (void);
extern void TMP_Text_SetCharArray_mA6EC91F806E7B7B4BAF34317531083DEC6AAFD70 (void);
extern void TMP_Text_GetStyle_m556317F676C8A404F2BEEB1EA28AA188229D5886 (void);
extern void TMP_Text_ReplaceOpeningStyleTag_m140CE17F312BBDE9A6F429F6976A6EAF22FBF7F7 (void);
extern void TMP_Text_ReplaceOpeningStyleTag_mFE4861A4A73DA7879121B8CFCEB051320E7C2B3A (void);
extern void TMP_Text_ReplaceClosingStyleTag_m8F0A4C880ED8811B94472B9A122FEE3DF1CEA06C (void);
extern void TMP_Text_ReplaceClosingStyleTag_m930CFBC820CF701CCF4A92E8CC798640FD9E0009 (void);
extern void TMP_Text_InsertOpeningStyleTag_m7194E079B8619F42CF27B3AB2A9B0A9FE2AB14BC (void);
extern void TMP_Text_InsertClosingStyleTag_m6AA7BC638D9F53B831DB2702256CFBFC25EA19AA (void);
extern void TMP_Text_GetMarkupTagHashCode_mB8A6C6A1ED3D704ADBEA0E90FCEF722AB826CD7A (void);
extern void TMP_Text_GetMarkupTagHashCode_mF2C6D3C0D954B1B17F584758FFACAAFA270B37BA (void);
extern void TMP_Text_GetStyleHashCode_m834CA7ED28BF6377F7A42C654FAA748EB0D514D6 (void);
extern void TMP_Text_GetStyleHashCode_mB54D3FEFFCA8A40441A169AD140C1531A788C92F (void);
extern void TMP_Text_AddFloatToInternalTextBackingArray_m91003C38D80CE33F40B45FB30E6B90F2EC2B78AB (void);
extern void TMP_Text_AddIntegerToInternalTextBackingArray_m0C9B986C866F3CD9D1424E44F57B281EDAB7DE92 (void);
extern void TMP_Text_InternalTextBackingArrayToString_m7E70067C4FF555AFF7D95718141ADA0794EF37B5 (void);
extern void TMP_Text_SetArraySizes_mAD14AE87D71586E0D4BEAFC6C89347FE02E33FE2 (void);
extern void TMP_Text_GetPreferredValues_mE55DE48997CA56E867C94ABF8873D1CA413ADAA8 (void);
extern void TMP_Text_GetPreferredValues_m1F06F3D203FD8F13D0335F697E839E5DAA61DD14 (void);
extern void TMP_Text_GetPreferredValues_m398215E34C2F85F6073BB4FFAD99E077319B2726 (void);
extern void TMP_Text_GetPreferredValues_m3FAA12BB95111827B71EBDE6B3B3F59EE4EA0C2C (void);
extern void TMP_Text_GetPreferredWidth_m0478A5C6B1B1C3A4A64C5BF89401B2A33A192F5C (void);
extern void TMP_Text_GetPreferredWidth_m51F52DCBCDF0AA45D5F6F1031D15560948E08C16 (void);
extern void TMP_Text_GetPreferredHeight_mD8B87C32069B477E010E30D33CB616854CE708B4 (void);
extern void TMP_Text_GetPreferredHeight_m6DD3E52AA402B1D6DC3D18F8760E0B89436F97CF (void);
extern void TMP_Text_GetRenderedValues_m758F7ECA29F67E1E7E782336B2CAD7B04EEB9222 (void);
extern void TMP_Text_GetRenderedValues_m08075C102D6F4332871ECF6D818664B6170B1374 (void);
extern void TMP_Text_GetRenderedWidth_mCCCE790E25FD4C17B55DBE153663D8024B458EDF (void);
extern void TMP_Text_GetRenderedWidth_m73C7A4A74971381580735209DD14A2CCCC9E3281 (void);
extern void TMP_Text_GetRenderedHeight_m7BEF1FB09209779C3D70185491FBC6E90A71214C (void);
extern void TMP_Text_GetRenderedHeight_m64D7F5014A10FFF692DED07E7619674F30D3B099 (void);
extern void TMP_Text_CalculatePreferredValues_mFC2117C2481613AF4CD0FE52E9C7162D4EB31C2A (void);
extern void TMP_Text_GetCompoundBounds_mF60F723948DF048E702AAB62F9408FAD30A1DBF2 (void);
extern void TMP_Text_GetCanvasSpaceClippingRect_m7C7869D4D77FBFFD707A3846A29792EB48B5D64F (void);
extern void TMP_Text_GetTextBounds_m9B8ADDB3EE48C956CF9D61DA303B21D5EA32081A (void);
extern void TMP_Text_GetTextBounds_m26FEA0CD67904DA57ABE718926102EEFCD374BF1 (void);
extern void TMP_Text_AdjustLineOffset_m52F6B152C307D094A146CA506C23704DD425218D (void);
extern void TMP_Text_ResizeLineExtents_mD9792BED7C93557CF2A93C604497729729CCBC66 (void);
extern void TMP_Text_GetTextInfo_m229923ABD01B6275D27C7BE608D316A1C4F623E7 (void);
extern void TMP_Text_ComputeMarginSize_mB8DA02298390E7D183460D39B765158D5B4C4C0B (void);
extern void TMP_Text_InsertNewLine_m2FB79A0D3C653AF608C8C6C9B56BC78AD696CE85 (void);
extern void TMP_Text_SaveWordWrappingState_m89FFAEE3796170C90F8EDBA696E4A14884A56650 (void);
extern void TMP_Text_RestoreWordWrappingState_mB126C83C447A92E11F6AC19C2BBBD923C74D8FCA (void);
extern void TMP_Text_SaveGlyphVertexInfo_mFFB0B3A7B1DBA2EE3F4116DB0AD2D7BA2A7BADBE (void);
extern void TMP_Text_SaveSpriteVertexInfo_mB11F4EA9C81BF4C58707941D616151EE6CD2BAC3 (void);
extern void TMP_Text_FillCharacterVertexBuffers_m4C17C2D2386E31401B012982171D0AB7E239B4EE (void);
extern void TMP_Text_FillCharacterVertexBuffers_mA8074BF6121C6716C641EB322E501BCFCE3CFB25 (void);
extern void TMP_Text_FillSpriteVertexBuffers_m7B3035DA24821F84AE49946ABEF06D0A2A87143B (void);
extern void TMP_Text_DrawUnderlineMesh_m9A89FEC9730C4C234A06A090CEDD2338C351E3F3 (void);
extern void TMP_Text_DrawTextHighlight_m328E45B989DA4EC8754CC437EACC79D8D0A7F327 (void);
extern void TMP_Text_LoadDefaultSettings_m529A22FF5A03DA761B775E3EABAF5EC6D122404A (void);
extern void TMP_Text_GetSpecialCharacters_mE903DAAA333AFF79BE23404C0E530BF2F974F86E (void);
extern void TMP_Text_GetEllipsisSpecialCharacter_mAB1E3B988E1169235AEC26DC0EC29B993FDF4735 (void);
extern void TMP_Text_GetUnderlineSpecialCharacter_m52EA407A41AABE20FE8888C6E94BB70EF0E82CE1 (void);
extern void TMP_Text_ReplaceTagWithCharacter_m27550FAAA0F89BCBF7E6ABF7E52888B04C92AFBF (void);
extern void TMP_Text_GetFontAssetForWeight_m8CAC4978C3092AE62D5354BE0D579E1985F84323 (void);
extern void TMP_Text_GetTextElement_mA9AC208C5F6080ADB94B84638ABFCB28124E889C (void);
extern void TMP_Text_SetActiveSubMeshes_mE3867037AB040A083339828CEA98FFC7D81758FE (void);
extern void TMP_Text_DestroySubMeshObjects_m7FFA3E35D4B393CC01847424F2F5C77416C1F8B3 (void);
extern void TMP_Text_ClearMesh_m3A40E9A07ABE32A911001625A4DE8F80353ECF8F (void);
extern void TMP_Text_ClearMesh_m5E212AB7BAA3D3F6A84AF20D0D4C1AE72985F329 (void);
extern void TMP_Text_GetParsedText_m0C3CD267431DA477842729B36C6C80D7296D4C65 (void);
extern void TMP_Text_IsSelfOrLinkedAncestor_m81351987CC1F547B1E7A0EDE1109F5EF596A8F76 (void);
extern void TMP_Text_ReleaseLinkedTextComponent_mBFBB0BB0702503E5492FE5CDC94164363A139696 (void);
extern void TMP_Text_PackUV_m6B919A58FF6988F660ACE59AA97910B31D577905 (void);
extern void TMP_Text_PackUV_m66B8E7066DC310AC67BA1FE63494D1A3BA726A00 (void);
extern void TMP_Text_InternalUpdate_mD5C4F3ADB7909023ADCED1033A6EE0D15AAC1781 (void);
extern void TMP_Text_HexToInt_m608FA8E451B2D296D60F096CB890714F72C5596B (void);
extern void TMP_Text_GetUTF16_m6A920DAFDD9869F0847B5C3F5B646EBFF4364B38 (void);
extern void TMP_Text_GetUTF16_m5DCD9865CEC393DE526550744D2F17448FFFB031 (void);
extern void TMP_Text_GetUTF16_m75142BDA9CD0E09E00079D51807092CDA41AB293 (void);
extern void TMP_Text_GetUTF16_m1A6DF3361330C4A1930A8CED3EE9AB1A661FBB69 (void);
extern void TMP_Text_GetUTF16_m6B311F8F9A6775761D65E56B3A14D4300694018C (void);
extern void TMP_Text_GetUTF32_m0AEBD15BD012872CA6305D7BA0C481FDA82AAC25 (void);
extern void TMP_Text_GetUTF32_m5417B3BA725A8B5C3EAD1AB1C8704DCAA7D8112E (void);
extern void TMP_Text_GetUTF32_m36AC6F004482AD41D7A6E02C3661FB84CA49C939 (void);
extern void TMP_Text_GetUTF32_mC701D13B98BB4F3EDA7BA77D2FEC84B957DF055D (void);
extern void TMP_Text_GetUTF32_m8969A7CF25219B3D95051380B0BF81E36515FA8B (void);
extern void TMP_Text_HexCharsToColor_mFF3D804C9D8FA7A297DE7D2FDD8ACAF56F3AE41F (void);
extern void TMP_Text_HexCharsToColor_mAB24870B76767E96CBCE96AF48D78744FBAEA2E7 (void);
extern void TMP_Text_GetAttributeParameters_mA3AE2EA072B750B11D4FA5FB08F3026062B3CB5E (void);
extern void TMP_Text_ConvertToFloat_m8C77647DEB5B96F427BA09AFC56A902F3C812D09 (void);
extern void TMP_Text_ConvertToFloat_m3A00B254D2DEC8796A64339BF2370E2FF0A76869 (void);
extern void TMP_Text_ValidateHtmlTag_mCA56FCCE3DC46EF51927B96CD7F91B1097A0EEBA (void);
extern void TMP_Text__ctor_m9E1AC8762428FEF98646584351299FFF499B823C (void);
extern void TMP_Text__cctor_m08F26D45B9462DC23A4AB77265441FC49818D0CD (void);
extern void CharacterSubstitution__ctor_m5727A2342B980E68CA8CA895437F82280B5E4378 (void);
extern void SpecialCharacter__ctor_m6EA478027143EA28D3A52D1E020B95B9286824FF (void);
extern void TextBackingContainer_get_Capacity_m314198D61452DF6CAB895C2BF8D1C0829C579F9C (void);
extern void TextBackingContainer_get_Count_mA4E440D40E9EECB361CE4697B11F9B017B19E0C1 (void);
extern void TextBackingContainer_set_Count_m3833989ADDB6C436DFB7A8979080FF5F2A411F19 (void);
extern void TextBackingContainer_get_Item_mA0E8BB3275942C3B08087D7E27914F436370C276 (void);
extern void TextBackingContainer_set_Item_mF263D268B2D3185D818FD470F86FC8C53DD42381 (void);
extern void TextBackingContainer__ctor_m28ABE283E7734CCAFCB78E5C71E817D495C1699D (void);
extern void TextBackingContainer_Resize_m669CEE085664D77F581761A5888EEF20E095F752 (void);
extern void U3CU3Ec__cctor_m3BE2B41C3099327A36FCB39A0697BACA0961A764 (void);
extern void U3CU3Ec__ctor_mB6F7B455BB3E880F27BB5E26F8D49389333F84E1 (void);
extern void U3CU3Ec_U3C_ctorU3Eb__622_0_m4ADE4CF5BF5DB0476C27555136DB926EB976EEFE (void);
extern void TMP_TextElement_get_elementType_m932830311118A83F12EA542C3B7C03C7B1437EB3 (void);
extern void TMP_TextElement_get_unicode_mF963B03CCA673335FB682EBDD1CFF86F0DB8539F (void);
extern void TMP_TextElement_set_unicode_m5DDC85416E46FEB989F4924ED4E1C8BABDE09AA0 (void);
extern void TMP_TextElement_get_textAsset_m3FFA01E6D0068D1F8F578CBF2590A752683A61EA (void);
extern void TMP_TextElement_set_textAsset_m046A7EF50875FC30233B8CB06D4A5138FB63C4E1 (void);
extern void TMP_TextElement_get_glyph_mB86D5107DDF4ADB051309056E876FEAE843E3D07 (void);
extern void TMP_TextElement_set_glyph_m29945C7CDA0F0F2429D3000A9376B4B5177A23BD (void);
extern void TMP_TextElement_get_glyphIndex_m149D0BB0350CFC7D3C46CA011669295DC8CF8E9E (void);
extern void TMP_TextElement_set_glyphIndex_mD2D21A9AD7EF332ABE56C52031E03CB5570C2FD3 (void);
extern void TMP_TextElement_get_scale_m23102716AD6E67BB03C2893983B105E8B425FE14 (void);
extern void TMP_TextElement_set_scale_mB753D739067A2DF395673D5C6B01E30B74B35362 (void);
extern void TMP_TextElement__ctor_m17ECA25C496E92124412C4B48665D75EE848AF83 (void);
extern void TMP_TextElement_Legacy__ctor_m662C0DC5276E1A91D27923DA266C23D825949787 (void);
extern void TMP_TextInfo__ctor_m3676CC349997AD7A21E043DB9938502FAAB87578 (void);
extern void TMP_TextInfo__ctor_m8E0E818004F0B2A107DC7CB7DA5753DC67496263 (void);
extern void TMP_TextInfo__ctor_m3F285FBF2EA3C9CF7B75EA3C1AB8CB66D37B4B4C (void);
extern void TMP_TextInfo_Clear_m288FFE54C6744C369E9B2BA903A634F461721D70 (void);
extern void TMP_TextInfo_ClearAllData_m8DCD1E84BDCA57F35235847D1DC8F3758BF265AB (void);
extern void TMP_TextInfo_ClearMeshInfo_m0FBBA8965BED7D2907087B746F87B28A1956962A (void);
extern void TMP_TextInfo_ClearAllMeshInfo_mC1F838D304E7F57366F2BB6D671D9E855D48AFFE (void);
extern void TMP_TextInfo_ResetVertexLayout_mDD6C8111384A819DDD015F66567A69C97C4F74E2 (void);
extern void TMP_TextInfo_ClearUnusedVertices_m46C02F1D4EB0183A973859CEDE6EE284B1F9EB56 (void);
extern void TMP_TextInfo_ClearLineInfo_m055901C815B31D3996CA828A79D73DAE576A1037 (void);
extern void TMP_TextInfo_ClearPageInfo_mD479D3067FC68407924FF28A468D68EA8B0680AE (void);
extern void TMP_TextInfo_CopyMeshInfoVertexData_mF66E2F8821470E68D95FEB53D456CFA86241C0CA (void);
extern void TMP_TextInfo__cctor_m5F410975A60AACB9C9286C9CD9FF445AF2E3D6AB (void);
extern void TMP_TextParsingUtilities__cctor_mEE5F7444F4B17038658A67A8C46BCE39169D141F (void);
extern void TMP_TextParsingUtilities_get_instance_m497D9C60451722EEE056E3F1971F8CD0E127BF02 (void);
extern void TMP_TextParsingUtilities_GetHashCode_m96340EA80E8D65E5FC9CA5AC20A3B119E64B4228 (void);
extern void TMP_TextParsingUtilities_GetHashCodeCaseSensitive_mD52F61679E036846C1303C58ED08E184E3E3EA45 (void);
extern void TMP_TextParsingUtilities_ToLowerASCIIFast_m2185A2449367B135B442E8DD08DE1272A1888104 (void);
extern void TMP_TextParsingUtilities_ToUpperASCIIFast_mB1C34D8B2251FE6792CFD9DEC9344201E459B545 (void);
extern void TMP_TextParsingUtilities_ToUpperASCIIFast_m7264A276BBBFB6F0BF767DA0F71C0554453E0723 (void);
extern void TMP_TextParsingUtilities_ToLowerASCIIFast_m781E773F4B4389AA8720355E57DD582BA06190F5 (void);
extern void TMP_TextParsingUtilities_IsHighSurrogate_mBFD44ED10F92D3A76EB771CFEA7729B3464AD179 (void);
extern void TMP_TextParsingUtilities_IsLowSurrogate_m19E3F3FEDCBEFE09BEE4A761B90A55C0830F34AE (void);
extern void TMP_TextParsingUtilities_ConvertToUTF32_m867CF53D1EEA890D5BF53B3D328398D60895E04B (void);
extern void TMP_TextParsingUtilities__ctor_m5B95C165745456957248A8D1EDFAD9B495F47323 (void);
extern void TMP_FontStyleStack_Clear_m49B787473D053AE4AB61D0A89BE3022B6D39B15D (void);
extern void TMP_FontStyleStack_Add_m86B65684B67DF2CA334037A30E9876C0F02D454A (void);
extern void TMP_FontStyleStack_Remove_mF44A8D00AA01FCBED6B6FD0A43A8D77990D2A26E (void);
extern void CaretInfo__ctor_m32D2780AAB3322C5EB68677CE3A73BF6B43E51B8 (void);
extern void TMP_TextUtilities_GetCursorIndexFromPosition_m2FE033144E5BE7DA05F0682DCD9DD9C54231198A (void);
extern void TMP_TextUtilities_GetCursorIndexFromPosition_mC95254F6E9C80CC65D7B89AB3281FB090DACAF43 (void);
extern void TMP_TextUtilities_FindNearestLine_mE50181F2B093AB40EA565DFD65586EBF8D916B73 (void);
extern void TMP_TextUtilities_FindNearestCharacterOnLine_m6EBAB5183A14C5D2E4471386C305979394A85C66 (void);
extern void TMP_TextUtilities_IsIntersectingRectTransform_m6AAA4D9F7266A54E014CA2A6BC5A37FB32CE7CC5 (void);
extern void TMP_TextUtilities_FindIntersectingCharacter_m89C20D1FB440DECC3C06670B457A707B6DB36453 (void);
extern void TMP_TextUtilities_FindNearestCharacter_mC99A2AEAAC3B5C5C4D878B13BE167BC42E554030 (void);
extern void TMP_TextUtilities_FindIntersectingWord_m93E6DBCA2781A67271E7D0F8906CA0488CA08EB2 (void);
extern void TMP_TextUtilities_FindNearestWord_m3F68C93DA3141F35817DFE093F3F68168C40A358 (void);
extern void TMP_TextUtilities_FindIntersectingLine_mDC468F4E9D28F89D313DEC468155185CB4E803E8 (void);
extern void TMP_TextUtilities_FindIntersectingLink_mC752442B8650D5146505B5C18C7D92B681D276E9 (void);
extern void TMP_TextUtilities_FindNearestLink_mC8D07DB867843C49B4A384A5AD980814464E0664 (void);
extern void TMP_TextUtilities_PointIntersectRectangle_m02A9C5ABEC703E15DB81913479B115CB52D0E848 (void);
extern void TMP_TextUtilities_ScreenPointToWorldPointInRectangle_m221201A3D27B7351AD7C5E8329FCFDB3B3377318 (void);
extern void TMP_TextUtilities_IntersectLinePlane_mEC8C8890EFCC3A296825F4E754E45CC9E6CE5AF9 (void);
extern void TMP_TextUtilities_DistanceToLine_mF92AF55AD4AFCCB06C8664E6FCE9BECBC1C8F347 (void);
extern void TMP_TextUtilities_ToLowerFast_mC2674EF9199EBBE4FEC3A112908E99DAD62C0971 (void);
extern void TMP_TextUtilities_ToUpperFast_mD4058FCC040A29181AF91BEDB9040F73C91061FA (void);
extern void TMP_TextUtilities_ToUpperASCIIFast_m0EFD2CE711167DCD6FAB7EEF3DFB371101A79ACB (void);
extern void TMP_TextUtilities_GetHashCode_mD7C0E83EA385E892E6A80B3CABB69505F2E122AE (void);
extern void TMP_TextUtilities_GetSimpleHashCode_m5BBE01E9DB50DBE54DE8834A3FC077C5C4329F7B (void);
extern void TMP_TextUtilities_GetSimpleHashCodeLowercase_m671C54756F544E0F2E814C1331FA06E3FC0F3C90 (void);
extern void TMP_TextUtilities_HexToInt_m3FB0402E5313B00B59CBB7F11B5FAF73499A8E6B (void);
extern void TMP_TextUtilities_StringHexToInt_mFD6F7A40E99D45CCE70F379EF70EA0321E7A1C99 (void);
extern void TMP_TextUtilities__cctor_m4D6B0C6DC30191A0209F04C0F7AD8A93F3CC250C (void);
extern void LineSegment__ctor_mD12FAF67166FBF4154B4C71793A87AC3EB9EEF0B (void);
extern void TMP_UpdateManager_get_instance_m1650984C00D47E778930C9063DFDA10409C87D4E (void);
extern void TMP_UpdateManager__ctor_m69A0A84DD4CD9C719AC6241795E327E198F3B8D5 (void);
extern void TMP_UpdateManager_RegisterTextObjectForUpdate_m18247DEF67E359156574B001461A8995D6CD027D (void);
extern void TMP_UpdateManager_InternalRegisterTextObjectForUpdate_m3BE2C4BF2F7380096474A113CEA612A72B5E5BF7 (void);
extern void TMP_UpdateManager_RegisterTextElementForLayoutRebuild_m6AE3A0CF4112A8963AB4C0EFA7B7ACC4505C158E (void);
extern void TMP_UpdateManager_InternalRegisterTextElementForLayoutRebuild_m99DD6449E8F765D5F10D2C272EB26673D29BAE97 (void);
extern void TMP_UpdateManager_RegisterTextElementForGraphicRebuild_m483FB163F9D2AF1712185A874B980724B19BFFD5 (void);
extern void TMP_UpdateManager_InternalRegisterTextElementForGraphicRebuild_m2BB7D188B607FAE033CCE3B65F6D5DBF13562524 (void);
extern void TMP_UpdateManager_RegisterTextElementForCullingUpdate_m20855E80BBE3AB418B5350D58782FC57A1E65841 (void);
extern void TMP_UpdateManager_InternalRegisterTextElementForCullingUpdate_m41E41B6A7F27C62897A7369DA43163AEADCC908F (void);
extern void TMP_UpdateManager_OnCameraPreCull_m265431745A965ECAA26603D4B5519043E7D99D98 (void);
extern void TMP_UpdateManager_DoRebuilds_m14F711CC2FA9DA7B2B8964059CB6CA4B776F6BE8 (void);
extern void TMP_UpdateManager_UnRegisterTextObjectForUpdate_mEFBA4B82356AAFD89692D3A3DA55B760977A8D40 (void);
extern void TMP_UpdateManager_UnRegisterTextElementForRebuild_m024BF55859F2F2FB7ABF6994059D74FF4F9B3548 (void);
extern void TMP_UpdateManager_InternalUnRegisterTextElementForGraphicRebuild_m9E12886C94B95F5EF2F4DE0F32C8CD9C4A597198 (void);
extern void TMP_UpdateManager_InternalUnRegisterTextElementForLayoutRebuild_mA216D19431D7C825E187253D6D68B5EDB3B8FCEF (void);
extern void TMP_UpdateManager_InternalUnRegisterTextObjectForUpdate_m2856DE05E46E68058986301E3DBEE17C8153B2E8 (void);
extern void TMP_UpdateManager__cctor_mEF08DEAFD5C6000A5E8AF447E12B4D82D91DD634 (void);
extern void TMP_UpdateRegistry_get_instance_m6DDAF8DA224196A7AC60D0F1FF65752D6C03548A (void);
extern void TMP_UpdateRegistry__ctor_mAD466DAAF6A8867F7D24D9B00AD6D5E113D2649E (void);
extern void TMP_UpdateRegistry_RegisterCanvasElementForLayoutRebuild_mA9F9146A5AC0DBAB51A11A85026673AB0362BD11 (void);
extern void TMP_UpdateRegistry_InternalRegisterCanvasElementForLayoutRebuild_mD0B965C049EBEB2E71B2BC8D2C7F16ECD47F0E53 (void);
extern void TMP_UpdateRegistry_RegisterCanvasElementForGraphicRebuild_mB7AAD68EA73E63195616E956AB75DB961228DCD2 (void);
extern void TMP_UpdateRegistry_InternalRegisterCanvasElementForGraphicRebuild_m3D71D7AB720191114A3CAD60899ED945D8614F3A (void);
extern void TMP_UpdateRegistry_PerformUpdateForCanvasRendererObjects_m0C118FBD3B2ADDD2FE9E40136F45D38D5C8975B9 (void);
extern void TMP_UpdateRegistry_PerformUpdateForMeshRendererObjects_mA5D3656421524B3258494141D9A73D3A5EA49D4E (void);
extern void TMP_UpdateRegistry_UnRegisterCanvasElementForRebuild_m7ADDF5C250DDEBBD803615D0B62A0B2A25BF08C5 (void);
extern void TMP_UpdateRegistry_InternalUnRegisterCanvasElementForLayoutRebuild_mA9A8EC29FC4587BAB47D3A48725299C9F3251BEC (void);
extern void TMP_UpdateRegistry_InternalUnRegisterCanvasElementForGraphicRebuild_mA2012BE5D69C00E5865F9C4428F9D85A39B4331A (void);
extern void TexturePacker_JsonArray__ctor_mA7347C642D7784A142849ED55983E779C997D853 (void);
extern void SpriteFrame_ToString_m74A323FCED2C3503F98BEB090A2EF8FE20B53E0C (void);
extern void SpriteSize_ToString_mED85E2303923FBF7A05A012E064705856A4CC2DB (void);
extern void SpriteDataObject__ctor_m89C520B855B17B46E4B43024C4941688A276CBE1 (void);
static Il2CppMethodPointer s_methodPointers[1620] = 
{
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_m5C1A0B0AB85E71B4D5E08CF87ACA9DB2E22FC785,
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_m1D3940CE59232BC12EC287DAB6D6AA5171997E32,
	FastAction_Add_m825D4C18BAB7721BE32B7097493D34F201BF81F3,
	FastAction_Remove_mAF00AE428F0F047567E37925BBD4A5750ECD63D4,
	FastAction_Call_mBAF2E050A7548718C6596AA230EBBD3945595298,
	FastAction__ctor_mF3C5146C45DAE04B656283ED83FAF3F463D38F33,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	MaterialReferenceManager_get_instance_m7B24E02E4C8E9EEBAB831C3F5E4DDFF5D46579B6,
	MaterialReferenceManager_AddFontAsset_m6792FB2A583AFD91FF776D0D29737E723F38F039,
	MaterialReferenceManager_AddFontAssetInternal_m8C0B7B9510BF49EBE2380A3E4D9CD3894616FA82,
	MaterialReferenceManager_AddSpriteAsset_mA212A7706AAD427F665DC83BDE0D3DF9BD9DF907,
	MaterialReferenceManager_AddSpriteAssetInternal_m237AFE73008A61174496142E1DE0AFE62BC54738,
	MaterialReferenceManager_AddSpriteAsset_mD012F5C582F67AECA204F814452BBB3D1FB69E63,
	MaterialReferenceManager_AddSpriteAssetInternal_m40F829BF9127F458984FD889E70D02474946D84F,
	MaterialReferenceManager_AddFontMaterial_mE3C0E0ABEDE58AC212AFD4CFE7938F234C70BBE9,
	MaterialReferenceManager_AddFontMaterialInternal_mE7FAF4082935FBB50EA6F11931549BA723BA16D5,
	MaterialReferenceManager_AddColorGradientPreset_m3BDD6F313678612D54E151D7DF901F43319CBCB5,
	MaterialReferenceManager_AddColorGradientPreset_Internal_mB23BD54CD86FBEDB4100031E98CCB65C88750C0E,
	MaterialReferenceManager_Contains_m215CCF891A17BC708D981C9DA2BDC9A752BCCACE,
	MaterialReferenceManager_Contains_m835A31CDCAD096D2C93CC0DC91ED04A5450A5743,
	MaterialReferenceManager_TryGetFontAsset_m2A3E5301004C96F262F336D554F64B1217D26231,
	MaterialReferenceManager_TryGetFontAssetInternal_m2C38E5D98E644C0B43C350A1212BD1B981E435CC,
	MaterialReferenceManager_TryGetSpriteAsset_m9B41FCA12C297EAD46D171500B95C037C75A855F,
	MaterialReferenceManager_TryGetSpriteAssetInternal_mEB43000DBA4E428E3BC83ADE74B525E715211D1B,
	MaterialReferenceManager_TryGetColorGradientPreset_m874B43FD78065DFFD31E3A477AE686CD445504CE,
	MaterialReferenceManager_TryGetColorGradientPresetInternal_mC1D435676D1B9576A4F77CC2736D846FF40C592F,
	MaterialReferenceManager_TryGetMaterial_m24D3BA8401616B78412735D1E9206B77AB4A124E,
	MaterialReferenceManager_TryGetMaterialInternal_m023208FF7BBB5E5FFA086C1C07F64A2A92413DCB,
	MaterialReferenceManager__ctor_mDD3425577D03313636DFE375E377F289D0EA2295,
	MaterialReference__ctor_m022ED9858AAD1DCEC25CBC4C304797F4539D87E7,
	MaterialReference_Contains_m051E7238A73C56AE613307D840F185F56AF84B14,
	MaterialReference_AddMaterialReference_mB50C19EBDE894D9F7BF7281A40BE052ABABC69BF,
	MaterialReference_AddMaterialReference_m10CD58333F42D11909FB7D396C51A4AE6707FE55,
	TextContainer_get_hasChanged_m8DF8FF327827736E4729E37C1521F035B413C5F7,
	TextContainer_set_hasChanged_mABA40DFD4EEC897F4A56B69CBB7A488D721C12DD,
	TextContainer_get_pivot_mABB55AD58B05D35BB0EB844B8CA9A837E4C95921,
	TextContainer_set_pivot_mA8B07A0D65AB363D46CD27E42320059E8DA52EE5,
	TextContainer_get_anchorPosition_m78B62F2ECB901D8B611131A213DEEF972790D1F7,
	TextContainer_set_anchorPosition_mA915529616A0B4679FAAA6183FC194597E03EA50,
	TextContainer_get_rect_m94D2400B7559ED8BD076F74B7AEB1CFF5ADDB6B5,
	TextContainer_set_rect_m1D085EDA4A56F1B94CE17F373787634403015B77,
	TextContainer_get_size_m0B22BF2C6CF43C2424C2864B87707D00E29B259B,
	TextContainer_set_size_m06C432F3136FCD30566702A5198587E4BE555A5C,
	TextContainer_get_width_mF4A385D6C76E375D1838E3586DFB0C7868508C1B,
	TextContainer_set_width_m6CCE1D33F804A90F452CF4E6F5E72E14FB040BCE,
	TextContainer_get_height_mF3302A1C468D314D85FE24DD4D09754232027C20,
	TextContainer_set_height_mF4164F3ED8244480E64B7837B310B30388EEE6CD,
	TextContainer_get_isDefaultWidth_mC93E766339878C17E406E69A43EC9E0D6A7982C6,
	TextContainer_get_isDefaultHeight_m3F66ACBFC5980F5CF466775067B1D38E55F208C6,
	TextContainer_get_isAutoFitting_mDFC6D0A44C286CAD6DC53B5A7B6F6E38E1339382,
	TextContainer_set_isAutoFitting_m3A9144590137287255F159FC93A1EAAD96B8141F,
	TextContainer_get_corners_mA9378A1DDF7C54BA2934885C9A21C71F8F778754,
	TextContainer_get_worldCorners_m608A9D2C48FABA9361C0030238101F8121E98B12,
	TextContainer_get_margins_mF4A4340460B6A5A271534AB50CA17026236E071A,
	TextContainer_set_margins_mC6A8F2814430CA0AAFF7985A27B4870AF736379B,
	TextContainer_get_rectTransform_mA715878DE6F7CB581CA2D82DA13FBC1A0BA14B5D,
	TextContainer_get_textMeshPro_m7FE78F97AF1550A7667BB6ED9BCF20DCCC3166A1,
	TextContainer_Awake_m450AB495666750B5B2B9C20639C44C894F179B95,
	TextContainer_OnEnable_mCB79CBA590B19D04274F74819286BD9A6E73EAA7,
	TextContainer_OnDisable_m7A822FF8139DF70D91292638C7C737C647F5DEEB,
	TextContainer_OnContainerChanged_mCC9F83EC6FF3262388F26E17DA1C900ECE6156C0,
	TextContainer_OnRectTransformDimensionsChange_mA9D29C6BAE685CE4C19365B0371E180365FAFAD4,
	TextContainer_SetRect_mC61B8DA58B65D1D7EBE48F78E7AB80CD1FE34B87,
	TextContainer_UpdateCorners_m3223B7904DD32545705F9B24FA52E13F8F198026,
	TextContainer_GetPivot_m2AF9923059C0EDA822C235ABE88D11A86A9DC16D,
	TextContainer_GetAnchorPosition_mEB7F12AEDB37EC48501719D685FFC55B6998E2D8,
	TextContainer__ctor_m31365F9C21AADAA5C2407613762F313663B62735,
	TextContainer__cctor_m3776E1588AB9236443B6EC72F3A963AAF16B6DEE,
	TextMeshPro_get_sortingLayerID_m246B079B5004616673509A12006E1D46CE8F7DB5,
	TextMeshPro_set_sortingLayerID_m4AFD807EC0960BE45C271C7F6B813884E27F98AC,
	TextMeshPro_get_sortingOrder_m8E5894C4390F4FDC0C7374C7EABAF96D37F0E089,
	TextMeshPro_set_sortingOrder_mF4E75E33617F04E98CB5C529E0FB1E97D7473715,
	TextMeshPro_get_autoSizeTextContainer_m92B636FDA452908D3752AA3DAACA7B5BDD81E73C,
	TextMeshPro_set_autoSizeTextContainer_m02CA26428A5BC9A4F5053C389560320DADECCA53,
	TextMeshPro_get_textContainer_m9ADA73A2523843F13F731E47A0CE7E0EFB3BF771,
	TextMeshPro_get_transform_m750148EC362B176A0E80D6F4ABAC1062E5281E11,
	TextMeshPro_get_renderer_m2E657DD550DAB1C896B1D8955AE08F84FB9FE78E,
	TextMeshPro_get_mesh_m848BFE512069747B47E7FEF2EDFB6F6BF56A6F87,
	TextMeshPro_get_meshFilter_m6A15037A8B675F9FB89F91A454DE6F49FE34A865,
	TextMeshPro_get_maskType_mF1F98A216FDBE60BACABA283E32D4F9D4D884E04,
	TextMeshPro_set_maskType_m218BA91F0BDEEAB2495D8A24BE494C29040F8AD5,
	TextMeshPro_SetMask_m6F20ECB1EE60267C66C29C2BE2A4E8297023E10B,
	TextMeshPro_SetMask_mCD7144AD14B1E578DAC7062F1C4AFE3F91F097DB,
	TextMeshPro_SetVerticesDirty_mFF321A7A3250A1983437B651C6B08CA6C238E295,
	TextMeshPro_SetLayoutDirty_mBB2707E3FB406BA91AE3CCF34300E4BD0EF13935,
	TextMeshPro_SetMaterialDirty_m94E7469E10AE410A7E13BDAA52B635B886D2BF14,
	TextMeshPro_SetAllDirty_mCA57F3D9977D6691FEAE9B42EC93188D665F9C45,
	TextMeshPro_Rebuild_mF51AAF8F7475EDDD27EF62BE70C71E250C810CAC,
	TextMeshPro_UpdateMaterial_m5D5307EC06A3B37A7638B8474A89ACA8F59ED8AD,
	TextMeshPro_UpdateMeshPadding_mD4CCF7EA6D5E6EC61BC530EEA4C8A722DF21887C,
	TextMeshPro_ForceMeshUpdate_mA8196630B76CD7E10CD0CECD0E4ED263006EDF51,
	TextMeshPro_GetTextInfo_m48DFA3AF6030E06CDCF41002C129A945DC1DC594,
	TextMeshPro_ClearMesh_mD868C52676C08B2044509DB25E53500797C3E1EB,
	TextMeshPro_add_OnPreRenderText_m095FA48218C2A8F8ACB5673DF77EADCFFB2B8F75,
	TextMeshPro_remove_OnPreRenderText_m832796A813128E6641DB46677DA21BFCF05D2E55,
	TextMeshPro_UpdateGeometry_m07DBFC391FC1B0D663AF1D42BCEA4FE0FDAE96E9,
	TextMeshPro_UpdateVertexData_mA83AC36F27C0D3A7977BE1E108F3A0763A32414C,
	TextMeshPro_UpdateVertexData_mA9E7A74B6EB347CFD3D351C1AC5406A0F29ACEC7,
	TextMeshPro_UpdateFontAsset_mE940EA22C479BF14F099BDC5F9E89EEFEC59260F,
	TextMeshPro_CalculateLayoutInputHorizontal_m831878E1B35FA9625FC0E4D4FA94AB1D8417E7F0,
	TextMeshPro_CalculateLayoutInputVertical_m9272D3BADA4804CF676D43F60B3758ED1EDDF510,
	TextMeshPro_Awake_mB6744F75E7577F7A8EC8DC7277C2D98B855875B9,
	TextMeshPro_OnEnable_m3E687CB93295674CBAD2CF98C10D048451181690,
	TextMeshPro_OnDisable_m97F173D468229C917EC3A1B1D1A562D60C17C80E,
	TextMeshPro_OnDestroy_m85DA8AE90BB8E3A852CA4E4B6BF6676BC8F4617C,
	TextMeshPro_LoadFontAsset_m9FC6839F4EDA45629776E483BD4F121C5108F9DD,
	TextMeshPro_UpdateEnvMapMatrix_m85ECB9C9F9BE379982CE64FC814A36B338060E1F,
	TextMeshPro_SetMask_m88A4C3AB9A50C4A54E20030E6C13899A01CF8443,
	TextMeshPro_SetMaskCoordinates_m892E763E92E5E5DED0EC610A16A4FCCDD447F40D,
	TextMeshPro_SetMaskCoordinates_m7CB4750ABC78278B40DE77342ABD2D02D9F931BC,
	TextMeshPro_EnableMasking_mAA4AD6CD94E45D033817486638BD4F0ED6B1E056,
	TextMeshPro_DisableMasking_m51B9768DECE33C8E84FE0BEC40AC1A872C3A93FE,
	TextMeshPro_UpdateMask_m0DC0BD7B23520E3980306B01C17D21C2E53FD476,
	TextMeshPro_GetMaterial_m7EE039E42CD673DCCF3C7EA8C334842FAB2A358F,
	TextMeshPro_GetMaterials_m106D2B8EA87AFCEDA5889C3ED9D2173E4521DEAC,
	TextMeshPro_SetSharedMaterial_m73380C05E2FF59D1A5C1563541F1340988D2AEC8,
	TextMeshPro_GetSharedMaterials_m9F4B35F7947C0564074B9B3CB36EC70CEAF6FB52,
	TextMeshPro_SetSharedMaterials_m0FB019F8C55D0A5D5812AD500FC74182F862DCAB,
	TextMeshPro_SetOutlineThickness_mEF32508CB6771185D243E2F04BABA73B9B45226B,
	TextMeshPro_SetFaceColor_m19E6DB372523664B314BBF8A3B0FA7BBE7C52BF7,
	TextMeshPro_SetOutlineColor_mB71099D9A168FB2956E9983E324DC76ECC5E7C5D,
	TextMeshPro_CreateMaterialInstance_m60E579A5964E977F66A0E7C56BB7FA86D1780094,
	TextMeshPro_SetShaderDepth_mBA26AEBB2FD4040C02A10E74A5CB8CA5697CA781,
	TextMeshPro_SetCulling_mA51D5BAD4DEA2A00C8AC7388A3277161CBCF1C20,
	TextMeshPro_SetPerspectiveCorrection_m6AF6ECDB7FCC9E45F4AC1AF6B0F76EEF1FEDF827,
	TextMeshPro_SetArraySizes_mA31A609B7368CFB3D7A42F67DE6F3E450F11D0EC,
	TextMeshPro_ComputeMarginSize_mF595014F36FD313F4552F91FCCEB32B6CF38275D,
	TextMeshPro_OnDidApplyAnimationProperties_m3951CDAD2B420D110042E6F2E4C8F9DB641AC652,
	TextMeshPro_OnTransformParentChanged_m13B32E506A0BD3041D6024988C029386EF65C82E,
	TextMeshPro_OnRectTransformDimensionsChange_m54E70A1F06C19D4A8CEF6EB8A3A130ACD45F7EB2,
	TextMeshPro_InternalUpdate_mACA7A0E963E587E3678586D0AF79A14EF7D65A20,
	TextMeshPro_OnPreRenderObject_mEDEAC1CC6D8C7AFEA948F713910E910F8FE301C1,
	TextMeshPro_GenerateTextMesh_m92A3D91D20D8BB4E46C2E9E305EE4207B1B2A1DD,
	TextMeshPro_GetTextContainerLocalCorners_m28DCECBF011AEEE457382B1ACF989366B7E51844,
	TextMeshPro_SetMeshFilters_m8F9CE41507555B248CBB176E3E19B905AF497BAB,
	TextMeshPro_SetActiveSubMeshes_mD13EDA99249DB1756900267DFD6A3D1D72FBADAA,
	TextMeshPro_SetActiveSubTextObjectRenderers_m84A606F6A82904D9EB5F92DAB05E1D7AF725CB6A,
	TextMeshPro_DestroySubMeshObjects_mD556C6CAB0FD455BC8BE7390BEEAF6C39B0873DE,
	TextMeshPro_UpdateSubMeshSortingLayerID_m63C46E43CFC2E1FEE48D6B48170DF3267E4BDF11,
	TextMeshPro_UpdateSubMeshSortingOrder_mDD41BBD341BA493B30022CC8930025BEB85070EC,
	TextMeshPro_GetCompoundBounds_m260ABC99A7A361F587BA40E99CE77B0F4402DFA9,
	TextMeshPro_UpdateSDFScale_m83845259E62DB13544143A11EF2089E03BE0D96C,
	TextMeshPro__ctor_mF12CEBF1FB86BE922890B59AD288B3F3BFC4AB0A,
	TextMeshPro__cctor_mD1C3870C44CE1F6FAD0CB5BAE861619017F60C34,
	TextMeshProUGUI_get_materialForRendering_mCB8AB4D6211E94B4A910E547A435BEDC8B5AA483,
	TextMeshProUGUI_get_autoSizeTextContainer_mFB1F26E61C6D873940BE188D1E8B8C35324237C2,
	TextMeshProUGUI_set_autoSizeTextContainer_m2E04DBDF79EF2A70A84DC27204410F642A8F7E57,
	TextMeshProUGUI_get_mesh_m9D7580B03E695D83EE9A7DB4B63CE0C570B4F404,
	TextMeshProUGUI_get_canvasRenderer_m74291CFD17F47B70F21F49C93058D2756A521887,
	TextMeshProUGUI_CalculateLayoutInputHorizontal_m96BF3C6C61F9B47F1C984A85921266E188151198,
	TextMeshProUGUI_CalculateLayoutInputVertical_m478DA445BEBEFD266F26B53B172A415F11EA0D4E,
	TextMeshProUGUI_SetVerticesDirty_m08925B150A0C61A9E62D6312094BEE4F2A085B35,
	TextMeshProUGUI_SetLayoutDirty_m7B98DD726DC1F539A829660218629BDCBC5D6EFE,
	TextMeshProUGUI_SetMaterialDirty_m34D20F8ED0CEF3D4F335BA53CDFBA22D3638E24F,
	TextMeshProUGUI_SetAllDirty_mBCABE23045A171B70D7C8AA1E9D0E197B24A9921,
	TextMeshProUGUI_DelayedGraphicRebuild_m4FD11E5C4B9F5E1DFE4D361C78CDC849F7D53F8E,
	TextMeshProUGUI_DelayedMaterialRebuild_m3D156CF49F99C2239E74AEC027473359A715397E,
	TextMeshProUGUI_Rebuild_m81A0BB63EF4D157FB9DF14184DF198DAEC24A50D,
	TextMeshProUGUI_UpdateSubObjectPivot_mF5A9ADE5A239AFC6C34BFC02FDE622DE14EAAF41,
	TextMeshProUGUI_GetModifiedMaterial_mF485112E8CD8B5466BC6F3F5C9EBB195B9DED181,
	TextMeshProUGUI_UpdateMaterial_m9342D18203EC31AF43B1F2FD179409825C9E246A,
	TextMeshProUGUI_get_maskOffset_m8B38AEB61B68ED587044250886FB3C3E6327711E,
	TextMeshProUGUI_set_maskOffset_m4556A74663F0603718E533AD6469D5D2062D5814,
	TextMeshProUGUI_RecalculateClipping_m6D9CD29782F2306FA411BE914F45C6E02D2438BA,
	TextMeshProUGUI_Cull_m28722BE62117D8666B334C1D98B0FDA31C801297,
	TextMeshProUGUI_UpdateCulling_mBBCAFB8FF0E605AA916782D723F605C7C5790E0F,
	TextMeshProUGUI_UpdateMeshPadding_mAD9B5590BB1C07BD9F26F9ACF31E3AD7D0375D52,
	TextMeshProUGUI_InternalCrossFadeColor_mCAC46A6435D3D73F8138A4DF4125AF63D6197E93,
	TextMeshProUGUI_InternalCrossFadeAlpha_m8F1403C1E5A7F3CC52DF054A11189D8E14353C63,
	TextMeshProUGUI_ForceMeshUpdate_mB5BCD2205BC620DC7AC4F00D7F21104D65FB90DE,
	TextMeshProUGUI_GetTextInfo_m0DF7D8487C0203268A4C62A67E1C4B7589ACC17E,
	TextMeshProUGUI_ClearMesh_mE803C85017AAA2E8F6C47661A7F59078C83D7D73,
	TextMeshProUGUI_add_OnPreRenderText_m1F3D640166C47D5E8AC83392022A7A76F252882D,
	TextMeshProUGUI_remove_OnPreRenderText_m5580AE95B7FCDF054DCB9F30D545CD60B5F40049,
	TextMeshProUGUI_UpdateGeometry_m7877CE2E695637CFD480AF6D3A144A96F4B8DEAD,
	TextMeshProUGUI_UpdateVertexData_mAFE22C7EF036C51A5DABECD97AA3294963B17B8A,
	TextMeshProUGUI_UpdateVertexData_mDCD835F9897D4A1D1C3299F8610E10AAF9693621,
	TextMeshProUGUI_UpdateFontAsset_m5A9C9C239F67AD02E0EE52BDDBBCA6B747AD5B3B,
	TextMeshProUGUI_Awake_mE225DF2C3511625374A16201302F4A8AD2A72B26,
	TextMeshProUGUI_OnEnable_m192C5020191F6DE0CDFD8D91BB1518496E841E98,
	TextMeshProUGUI_OnDisable_m55A5E45BD3538FECB646D245F23223C33C9C6CEE,
	TextMeshProUGUI_OnDestroy_mF6F5098912944CBAEE8F9162CBC8513B6FCB4DCB,
	TextMeshProUGUI_LoadFontAsset_mD79574232A722B157F79A7D1430115829107B0D9,
	TextMeshProUGUI_GetCanvas_mA62DCDC49EF734C76E2DCC33373AE38485267B07,
	TextMeshProUGUI_UpdateEnvMapMatrix_mBB9F3DBD385A9CF38674F27B1265FEF0214A3BDB,
	TextMeshProUGUI_EnableMasking_m825DABEA53C5743CE0B4D896476BCB8B4D23CCA5,
	TextMeshProUGUI_DisableMasking_m00E17B3141C80A2A15A8CF42FB9919563FBC9317,
	TextMeshProUGUI_UpdateMask_m35565EC9D9C802C94BE4694B5B340B2B4577EF44,
	TextMeshProUGUI_GetMaterial_m2A3E0BA6B812DEF61F0FB90E9FC0D6C4BDCEBA0A,
	TextMeshProUGUI_GetMaterials_m4C0D6622FD2BD30E4CE5F868F1CFDB64C1C1403D,
	TextMeshProUGUI_SetSharedMaterial_m9F64130FA72B17A58824E3A40980C92013F4E0EB,
	TextMeshProUGUI_GetSharedMaterials_mFC9F284B9D0F7588F7FBDCB8C98B221DCF8660F6,
	TextMeshProUGUI_SetSharedMaterials_m0E33EE522DBE0E2BA8464C455A9663327F9E34ED,
	TextMeshProUGUI_SetOutlineThickness_m1819530A5C28F978C80D6BA614595D900099D897,
	TextMeshProUGUI_SetFaceColor_m6A655C7FF042096CC71D943BCAFB98A42036EE1B,
	TextMeshProUGUI_SetOutlineColor_mFCA7A6D7F2BA52DB12239F9051D2BDF40F92ADA8,
	TextMeshProUGUI_SetShaderDepth_m7A017149B97BAD6B9618750C0976729BD4740A70,
	TextMeshProUGUI_SetCulling_m62AF94911CBF89A8ADDA8A21CEE391ED756360EF,
	TextMeshProUGUI_SetPerspectiveCorrection_mF547B8584EC083BAE1D3EFBD22CC5D9D78EDC48F,
	TextMeshProUGUI_SetMeshArrays_mBB3809698124A9B2B65A4F57954CEFA790568C2A,
	TextMeshProUGUI_SetArraySizes_mAE0F66585903F33A1056D130213B5680D0951D42,
	TextMeshProUGUI_ComputeMarginSize_m0647B2989C0A1098FFCCB9EA66810DAAE6C2E0D5,
	TextMeshProUGUI_OnDidApplyAnimationProperties_m8ADE42C6DD519A08609CB526A983561DBACB7266,
	TextMeshProUGUI_OnCanvasHierarchyChanged_mDF06442735F795DA1A2CEBF42542EF352A0AC2D4,
	TextMeshProUGUI_OnTransformParentChanged_m7B078BE5B1E69D3CA642D32F15204E7A7D2D7825,
	TextMeshProUGUI_OnRectTransformDimensionsChange_mE7B272E5EF1538872749F01C82CD28D4352F37FC,
	TextMeshProUGUI_InternalUpdate_mEA820FAC0E9C659AC8198ABD63AC8515FC2EEC75,
	TextMeshProUGUI_OnPreRenderCanvas_mD208EA1BBA9D3A10B255322BABED2056A83BA2FC,
	TextMeshProUGUI_GenerateTextMesh_m542C2DC0CA489C10BB3371B0114CD9ACED4E97D3,
	TextMeshProUGUI_GetTextContainerLocalCorners_m2D6D185BC85A20431398BEC3EC258EBBD7D7C6AE,
	TextMeshProUGUI_SetActiveSubMeshes_m85AACB4166A13ACDC7E0F944BD37E1C771A90716,
	TextMeshProUGUI_DestroySubMeshObjects_mBE2D8860515389A8AC3FDC37D184B03E38186F23,
	TextMeshProUGUI_GetCompoundBounds_m74366E456D195D8D2FEBB1E85D0FE5DCE4105DF0,
	TextMeshProUGUI_GetCanvasSpaceClippingRect_m1E7125B754E5B3935318B425847952E705DD5B6F,
	TextMeshProUGUI_UpdateSDFScale_m357367AFC5B9A2EBD11FA3C4D7FC99C5208044AE,
	TextMeshProUGUI__ctor_m3733FC98975BAD7C96BA932B8BE6A63602BA83B3,
	TextMeshProUGUI__cctor_mBE116F1939402BD5AFB61FF61EFD35EDE6F5A9B9,
	U3CDelayedGraphicRebuildU3Ed__18__ctor_mCAB88DA6910047EF7A9FA188F4CF1F1EA42A858B,
	U3CDelayedGraphicRebuildU3Ed__18_System_IDisposable_Dispose_m4BEB7CF8BAD78596B1C1FC7359A791492F8EA33F,
	U3CDelayedGraphicRebuildU3Ed__18_MoveNext_m0B62B0E98E2F3A3DA9A316759B3D76181ED512AF,
	U3CDelayedGraphicRebuildU3Ed__18_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m7A1891BD230605BA6BA156CAE8857BD6413AFEAA,
	U3CDelayedGraphicRebuildU3Ed__18_System_Collections_IEnumerator_Reset_m251AEA2015F485A8BA72E4B5ED787AE5224DC4DE,
	U3CDelayedGraphicRebuildU3Ed__18_System_Collections_IEnumerator_get_Current_m87FE04FA504B5B9B9606E2447830CBD06986DFF8,
	U3CDelayedMaterialRebuildU3Ed__19__ctor_m6351B5737E3B2ADA60C28688744DE4FA2541606E,
	U3CDelayedMaterialRebuildU3Ed__19_System_IDisposable_Dispose_m2EF3A18F3B761153309EDC6D2AE1C3457515C1CF,
	U3CDelayedMaterialRebuildU3Ed__19_MoveNext_m9F6CEA5597DFFDA1AF6344B784640145736FFD68,
	U3CDelayedMaterialRebuildU3Ed__19_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mBCB103321A5F41C5119C356DF074BBEE78D2A19E,
	U3CDelayedMaterialRebuildU3Ed__19_System_Collections_IEnumerator_Reset_m601E654D1F099331AA189E94AB46DE1A332A2559,
	U3CDelayedMaterialRebuildU3Ed__19_System_Collections_IEnumerator_get_Current_m81AC91995AC85EEE6EC8E360A3D6D1BA49EF9714,
	TMPro_EventManager_ON_MATERIAL_PROPERTY_CHANGED_mA3B9310945A5CBD70197154F55F91495E24255D2,
	TMPro_EventManager_ON_FONT_PROPERTY_CHANGED_mDD93FA81065725C1F7AED35667D48F13FC2D9DC9,
	TMPro_EventManager_ON_SPRITE_ASSET_PROPERTY_CHANGED_m9A90D8500F7EF60AE7D3FF22985EF09B3DF02A98,
	TMPro_EventManager_ON_TEXTMESHPRO_PROPERTY_CHANGED_m9A254BD37DED765A10C03417DE5DA8AC63455CAD,
	TMPro_EventManager_ON_DRAG_AND_DROP_MATERIAL_CHANGED_mC43C0BD630FB674BDF3363C50F46E5779B7BF6D4,
	TMPro_EventManager_ON_TEXT_STYLE_PROPERTY_CHANGED_m48AF453FABAF93571CF18FFFFA43B1AB21192F91,
	TMPro_EventManager_ON_COLOR_GRADIENT_PROPERTY_CHANGED_m533FFD466019FC48A0F170854D2CB26F2284B340,
	TMPro_EventManager_ON_TEXT_CHANGED_mC933ED67F225E90E2F7B5F0D176D62D3A560BFAE,
	TMPro_EventManager_ON_TMP_SETTINGS_CHANGED_m6BF9B1490D1602D1F5837EA29954272B92539050,
	TMPro_EventManager_ON_RESOURCES_LOADED_m168B6EEE1214E3D17D1B450F871456BEACC127B2,
	TMPro_EventManager_ON_TEXTMESHPRO_UGUI_PROPERTY_CHANGED_m6AC5515A817163122EF62E17CEA63E8D881F8B9B,
	TMPro_EventManager_ON_COMPUTE_DT_EVENT_m38F4534680AED9689989F220DE438B81BFA13714,
	TMPro_EventManager__cctor_mAA44D45F96295F60D3407910459559C917ED1596,
	Compute_DT_EventArgs__ctor_mFB724195485EB6D002C77F0B2262A5F94835A28D,
	Compute_DT_EventArgs__ctor_m9834D9D98042FC8AC11B9B6A834BFEDB3ED2BFFF,
	TMPro_ExtensionMethods_ToIntArray_mAFB2830378327380013F520489E8B20F5A93C0AC,
	TMPro_ExtensionMethods_ArrayToString_mCCFBF0B40F958253FC75EE46917D436BAE1D757C,
	TMPro_ExtensionMethods_IntToString_m20E167DC69EAF4F15BA6E5C95AA9151C0712FCF8,
	TMPro_ExtensionMethods_UintToString_mCE4BA004D9FC332F4A359EC4BE2E97C81FC3A8A8,
	TMPro_ExtensionMethods_IntToString_m7D01EB4E4B554DB3B29BD1701E972EFBD0A9424A,
	NULL,
	TMPro_ExtensionMethods_Compare_m1838CE0635EC60A2288FA34D81634A7F808DE370,
	TMPro_ExtensionMethods_CompareRGB_m7CC49D7549E748CEC20F6EC84A1094084150F33A,
	TMPro_ExtensionMethods_Compare_mA56784B99E3F3FDC0C2C32BFDF99F77E309230F6,
	TMPro_ExtensionMethods_CompareRGB_m3EEC2B9EB0B59134AB15C55568C54A78A06C954E,
	TMPro_ExtensionMethods_Multiply_m110996D0A26FD6BB8231C5BFA1913F01AFDB8BAB,
	TMPro_ExtensionMethods_Tint_mF3595B5B4DF616CE03A79DE5BB4EB55531FB56D5,
	TMPro_ExtensionMethods_Tint_m6B681898DA88005A3CA7B1450849F44AD6991159,
	TMPro_ExtensionMethods_MinAlpha_mBDF86191325DE876306DFADE5EB6A27A5DB5F1CE,
	TMPro_ExtensionMethods_Compare_mC71D1D722A3BEB91D0F47B71514A634AF72D6880,
	TMPro_ExtensionMethods_Compare_m1BA0218FAED74AC8BB89E9F58D6CFF5D4BE1FCB0,
	TMP_Math_Approximately_m58958A2D9DB66040360C17A460E0CA35F705EA2F,
	TMP_Math_Mod_m0BCF68D3977E3B9CABEB8845C8C82746BA4DABF1,
	TMP_Math__cctor_mF2F11F0FFA33F4CE827C505FE2D48832F4044090,
	VertexGradient__ctor_m9B59D99E8B67833BD6CC50F4704614744D271C3A,
	VertexGradient__ctor_m8FFAAFD98D0DC4F7C6D41410EF574A6600DCA40B,
	TMP_LinkInfo_SetLinkID_m9E9A1B09A536609EC636A3F6D14498F70C6C487A,
	TMP_LinkInfo_GetLinkText_m954EE8FF39D62BA8113773A696095EAE85CD5E3F,
	TMP_LinkInfo_GetLinkID_mCC9D9E783D606660A4D15E0E746E1E27AD9C2425,
	TMP_WordInfo_GetWord_m7F72AB87E8AB0FA75616FD5409A8F5C031294D2C,
	Extents__ctor_m2C44BA0B2EDAAB80829698A019D2BBF8DDFF630B,
	Extents_ToString_m947E31139C9C66B1F1942EF74B2FDE347A778F99,
	Extents__cctor_mF148593F1B6B500BFC7C4D3823E2B7A81455379E,
	Mesh_Extents__ctor_m37E0BBEE5EED57082B82AC6162F7785B231907CB,
	Mesh_Extents_ToString_m733F0275FC20FD2F4EE5B6E9ABBD6F3CD8D5AE23,
	TMP_Asset_get_instanceID_mD7D5D79979B77457C3A376955C316AC289BB3D1D,
	TMP_Asset__ctor_m12FF90A96AD41AEDF9AD37175E7070FAC070D8E9,
	TMP_Character__ctor_m16CBCDD3EB5695396E4C95A0876F2C4DD4500673,
	TMP_Character__ctor_m79F49FBBC8657569BA621EFBF64A000BB1B56ED3,
	TMP_Character__ctor_mAF0A895127F27795D515FF97C84185A01EFDAB0D,
	TMP_Character__ctor_m7019F2E56EE1A654151F581F2293FBB2410E4775,
	TMP_Vertex_get_zero_m8350B6247D50962DFA3B8396E97D4F854A4C5D3D,
	TMP_Vertex__cctor_m60A31A768302204B848462AD7AE51680AAB0DA61,
	TMP_Offset_get_left_mCDC93F42B720817E1119AA5360962F038A39E044,
	TMP_Offset_set_left_m6FF3BCE823654D55CC03B9202094E129A3891958,
	TMP_Offset_get_right_m268492C5D14D1239A429A152ED04DD8790EC98C4,
	TMP_Offset_set_right_m19952C4778E73F559E8229B5D1438766E4FF62F2,
	TMP_Offset_get_top_m5BAE1A688A264A63524AD4C456CE88CB2086105E,
	TMP_Offset_set_top_m5346213516D5B378349B70D61C4EE6BB25603DCC,
	TMP_Offset_get_bottom_m71E985879E87F76BE28A0FB0485F279866279845,
	TMP_Offset_set_bottom_m4FF1AE55CF113FD06678B22A8ED029F17A9019A8,
	TMP_Offset_get_horizontal_m3BE3663354670CEA3945FD6EC7C6FD1A3F4E81F7,
	TMP_Offset_set_horizontal_m38C3B111DD01790C98E91423FCEF6BE826826891,
	TMP_Offset_get_vertical_mB6681568C4F9B09DCE4CBFFDC33F733DE7EFE149,
	TMP_Offset_set_vertical_m16CC389B6E1291EA006498F0E739A05A5CD16ABE,
	TMP_Offset_get_zero_m8D8E8D2E46EAB0DFFED647AC5EEB41A5B2AA2339,
	TMP_Offset__ctor_mE88A176987DB6F468CA09553D74E86E1B48AA81C,
	TMP_Offset__ctor_m5C1836C5751505F6A9E674C8CD7A6419F4BFDCA0,
	TMP_Offset_op_Equality_m3D2451105415FA35C1E9CA5DE8C4C246094E1CA5,
	TMP_Offset_op_Inequality_m6B6DDC71D8DA2341BB06316615A3A3C66A4BE31E,
	TMP_Offset_op_Multiply_mC618A5520464FC68B05E5B08985D3FA94204DF75,
	TMP_Offset_GetHashCode_mD43DEA54E08FF70C12AAB7FD40AC4310B81C1421,
	TMP_Offset_Equals_m7D8B386EF75BA3B1F931F1F70AAB10FEFA6B17F4,
	TMP_Offset_Equals_m1670D25215AB3B155D89F019D27286D294A9ECF0,
	TMP_Offset__cctor_mADC96160EF508F9E23B40CCF1228A01606C83F90,
	HighlightState__ctor_m25791146FF94DD76C2FAAAF47C1735C01D9F47B2,
	HighlightState_op_Equality_mA8B294C1DDCDE0322A0834A3A0B742FCCB92A1E7,
	HighlightState_op_Inequality_m98311E1C788EC5DB2E62731BA43E0AE8D73333F8,
	HighlightState_GetHashCode_m2BE4FEEDFCB6713FA9C10C2D3B93E938E011C985,
	HighlightState_Equals_m0317881F19561A64B9016A27C306FDB77460D165,
	HighlightState_Equals_mFC0B5D3A36F1CB24FFDC21F6C238184D43324825,
	TMP_ColorGradient__ctor_m16EACE29FFBC9D027D21703EE927AEE4C370EF8A,
	TMP_ColorGradient__ctor_m60AB4FD600B132618C4DED1BDE0864E7C8CBAC14,
	TMP_ColorGradient__ctor_mD73E13202AFB41FB9171358320B28DEF6F1162A4,
	TMP_ColorGradient__cctor_m84A7D02BB31A1A1FE3303B7C61B277AA066D934A,
	TMP_Compatibility_ConvertTextAlignmentEnumValues_mE840105F8940EB3B458F11758D4FBB8E1C8EF775,
	NULL,
	NULL,
	NULL,
	NULL,
	ColorTween_get_startColor_mC3CD44E2CCEF5BB78ED52759D570B1B6855CBBCC,
	ColorTween_set_startColor_m01FB2C14DD0139433F9EBCF39A286AE064B8A2FB,
	ColorTween_get_targetColor_m26BAA4AAA09E6FD3E79F35C51170C32889919446,
	ColorTween_set_targetColor_m24EB21B05BDCC21A4178DEB116962BE18B361B27,
	ColorTween_get_tweenMode_m9194D120345334B358FA8487E98C75FDD8F8355B,
	ColorTween_set_tweenMode_mB2A52A753B322F14EEF3A1C17B0CC51EB5210035,
	ColorTween_get_duration_m2C26D45A626E334E9EECD575CCA45B55945736D3,
	ColorTween_set_duration_m0C781971A9EE23189EA02A2835731918680957F0,
	ColorTween_get_ignoreTimeScale_m008715D2A64C9FC6419C9D71A7030F8710ABA6AE,
	ColorTween_set_ignoreTimeScale_mDC7F5B7E7EDF149304E8139C64712462DAB8D40D,
	ColorTween_TweenValue_m43ED566CB5FA4818535832C2ECFFCFEAAE7FFE8E,
	ColorTween_AddOnChangedCallback_mF9FE28A71E2818B2C25D79B9272E838034B5A4E4,
	ColorTween_GetIgnoreTimescale_m502CC5CF9B974F7F564316687E61A16E8CF038F7,
	ColorTween_GetDuration_mC929D8E88C9C230CE6A0EAE684B84CA08CFAA615,
	ColorTween_ValidTarget_mFA6997930396ACBF53FCDFBDC457FC3C63AE90D7,
	ColorTweenCallback__ctor_m8723485C32B5BEAF1EBC7F4A7078FB90BDCF1AB4,
	FloatTween_get_startValue_mE12446AD7FA5B7816124ADFF1FF7959A3B6ACF1B,
	FloatTween_set_startValue_m1E04EA68FFEE3AA1553B194D0F82C32815E2C718,
	FloatTween_get_targetValue_m2FA9DBB4C75BFD36E2A9DE57522CAA25D9F44FF2,
	FloatTween_set_targetValue_mE110CBA03582B01B96766AE53F2DDD3C2D4DE131,
	FloatTween_get_duration_mE71CD40934ED69FDD7CDA8D5438E9897E6E9FE7A,
	FloatTween_set_duration_mF59D55C5F70E037AF88A6191D282D404C532D613,
	FloatTween_get_ignoreTimeScale_m8FE31080B4800A6CFB89918E0803BB1BE21FDA4B,
	FloatTween_set_ignoreTimeScale_m53B7945E5B54998B9BC28E7508E94D3A8205C10A,
	FloatTween_TweenValue_m022D385B013439E2FB8020F6A6BD329CECA81E89,
	FloatTween_AddOnChangedCallback_m2DCB737D223D6345503A9DA057D7935F9C3A5AD6,
	FloatTween_GetIgnoreTimescale_mB13FC7DCC241FCF2C9EC10D8AF8A9B6103F9B420,
	FloatTween_GetDuration_m4F4E336D66A32D1F051E0EF2B1513F05EC3BF349,
	FloatTween_ValidTarget_m4591FB5DBEE8762554B97A09B232893EE754D7DF,
	FloatTweenCallback__ctor_m93E614F36DA9484D40A5286A7EB4220220A25F00,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	TMP_DefaultControls_CreateUIElementRoot_m67AF400D15F9252F70D1DDCCE1B6E017FCCF3608,
	TMP_DefaultControls_CreateUIObject_m60CC06A3210ABF4961B73EE916210E9A5BEE3278,
	TMP_DefaultControls_SetDefaultTextValues_mDC3353B8B8D0ED33A70A1B2AC9B7602DA9DCC67F,
	TMP_DefaultControls_SetDefaultColorTransitionValues_m72AC254CDF013CD5831DC9FF9A49A17E9857A228,
	TMP_DefaultControls_SetParentAndAlign_m3DC6F261496415BFD5A95D0EA5EE8D7B320AE157,
	TMP_DefaultControls_SetLayerRecursively_m34C986A57E7C7B3C75C44E54174BB5276F7D640B,
	TMP_DefaultControls_CreateScrollbar_mAB1F6E4EFD132C2BF186727978CF87844E0751AA,
	TMP_DefaultControls_CreateButton_m19500388D51128AC9E8EA08A6822D183E65AE43B,
	TMP_DefaultControls_CreateText_mD4727FBA88AD13862711F4C1E6507514A7C2BB56,
	TMP_DefaultControls_CreateInputField_m76F50E353AD266E85972F4BE3DC871F9AE796B8B,
	TMP_DefaultControls_CreateDropdown_mC088CFDA6188DE6D4E215A5299276018B0AC7642,
	TMP_DefaultControls__cctor_m6E1710C0401A290E8DE7C2E36389E34E875853A3,
	TMP_Dropdown_get_template_m6C77CA07D48383A133E8D7567E1CF909876BAE30,
	TMP_Dropdown_set_template_mCA07B030A71D2E4D4C179B4E4E72568656A340BF,
	TMP_Dropdown_get_captionText_mBC7B6DBEA095496F29EDA88E92F95E650B1BBC46,
	TMP_Dropdown_set_captionText_m7050B2703E0A8C652571271C860004F0014BD464,
	TMP_Dropdown_get_captionImage_mD946C03912041B698DD64BE58890CB30CD727308,
	TMP_Dropdown_set_captionImage_m1AE6C5CF0660B3BF8C4A24DFF58E088C7CC67C19,
	TMP_Dropdown_get_placeholder_m425453F7D24F908584DCC5BEB8CCB16D1E4A4B20,
	TMP_Dropdown_set_placeholder_mA6502D4B0CDB70E1BF14B43FB468CF048929035B,
	TMP_Dropdown_get_itemText_m0AAE4A304C3AC8132209E844C5303411DB3C1AFB,
	TMP_Dropdown_set_itemText_mCC983B4A78E8C594E5C78BE1F8F8DBB067E3CA1B,
	TMP_Dropdown_get_itemImage_mE0C2F6ED6A8338733F2A260939530BAB06DB576E,
	TMP_Dropdown_set_itemImage_m41479EC2993BB2900024406EC39FDACC83DB808D,
	TMP_Dropdown_get_options_mA543A0EFE4D1953E73C6F60ECA8CE177182571C5,
	TMP_Dropdown_set_options_m4CD66EEEFDF53BAD9862AA2768F6048B2CA5B2AD,
	TMP_Dropdown_get_onValueChanged_mC5A65068BFDC48C26FAC4B6FB2C0983633168BFF,
	TMP_Dropdown_set_onValueChanged_m79F7EFB914F2FDD0747F0DF2338E100996B49ECE,
	TMP_Dropdown_get_alphaFadeSpeed_m18A605C6E2AA4BA276BB7D8200718977530988DD,
	TMP_Dropdown_set_alphaFadeSpeed_m61345D8CD279D16406067AEA81DC4CFE6DFDB073,
	TMP_Dropdown_get_value_m5916A6D1897431E8ED789FEC24551A311D1B5C70,
	TMP_Dropdown_set_value_m8362A866D571975FECFD1FE47D3C4D83559795BF,
	TMP_Dropdown_SetValueWithoutNotify_m619A7D38D1EBCCA60EB1F5891FAD9FD0FB4839FD,
	TMP_Dropdown_SetValue_m1E528A87C2B11DD84D0141D8C6D175EEFE26F202,
	TMP_Dropdown_get_IsExpanded_mFCB1622385A1BBB05908C0C94D5751801B5512FC,
	TMP_Dropdown__ctor_m5405D55FF150C4ED50322BA7B80E11E30096819D,
	TMP_Dropdown_Awake_m056E49917C61BD2AE6779CB0AD5A8983D17AB8FB,
	TMP_Dropdown_Start_m734BEF53026D3806A8E321B1E5DE53C340D28B16,
	TMP_Dropdown_OnDisable_m10823C942F18754D85E6BB4C86A267D2D4B0F8B3,
	TMP_Dropdown_RefreshShownValue_mF63797E989A9075B6E4CF767B2C4B55FBD23DFF8,
	TMP_Dropdown_AddOptions_m0552A59BC909364951E1661FABADBC076715E4DD,
	TMP_Dropdown_AddOptions_m6EB4E175FE6B5555BDC2C31EFBCC3B115DE85FF5,
	TMP_Dropdown_AddOptions_m788BACF88AF7E1342CEE571AAC04EF86D72DDEDE,
	TMP_Dropdown_ClearOptions_m9888C23BC3033268E5C2D8613C0854661E7BDEB5,
	TMP_Dropdown_SetupTemplate_m3016B8C83713F451E88F3CBF008856BAB835F0EA,
	NULL,
	TMP_Dropdown_OnPointerClick_m008E7A23F525AF69BE769CE82695A8D07EE6EB39,
	TMP_Dropdown_OnSubmit_mC64400DED5E54304BF98C84C4D4FB71B089CA49C,
	TMP_Dropdown_OnCancel_m67E30490F0C9D41D7BDCF42DF1621DABADC14B32,
	TMP_Dropdown_Show_m8A64194BE150E0774C66E69479A1699DBB6AA900,
	TMP_Dropdown_CreateBlocker_m4048F0A0133E9DE795874056A9BBD8F58B6E947B,
	TMP_Dropdown_DestroyBlocker_m2C9D955C6958C84AD323AC9E6193A07AE8212EA6,
	TMP_Dropdown_CreateDropdownList_m3C853DEAA3D9F33653126E195B57D9F423064116,
	TMP_Dropdown_DestroyDropdownList_mABF98A8706914CAFF13B3F6EB359F4F67E1E3FD9,
	TMP_Dropdown_CreateItem_m6DBD654E30D5A7ABD668DE8651E554966D0A7191,
	TMP_Dropdown_DestroyItem_mC0A868E6C60C5E1406E57B388FAFC1307C381A40,
	TMP_Dropdown_AddItem_m1B768BFBFFE0E340BAD0141E2C3F90FFAE889EFA,
	TMP_Dropdown_AlphaFadeList_mC7278768821D6B8FD28FC712E8DE85CA334E2104,
	TMP_Dropdown_AlphaFadeList_m663AC38ACFC469DBDFFF025314B34E310D70FBB8,
	TMP_Dropdown_SetAlpha_m2AF7CD832E68BA385496F4F70D291219B3668ED7,
	TMP_Dropdown_Hide_m7FFE4A08B7370707BCE35A3EDD74373A1926E618,
	TMP_Dropdown_DelayedDestroyDropdownList_m746DFE51A0D66199C34FA32CF81761FDF1BE5F9C,
	TMP_Dropdown_ImmediateDestroyDropdownList_mFEE6CE2C3264546861C82E60CE4C61BA42559B98,
	TMP_Dropdown_OnSelectItem_m9F1323DE41E23EFF4381C914BF1FCAA57F73C7AC,
	TMP_Dropdown__cctor_mF1ADB07E43839AA40B5E3492D0130638325D6BC2,
	DropdownItem_get_text_mBE92632907F1772753BC60A7FE1E5358077E6FD4,
	DropdownItem_set_text_m7C30C7E6DEE86A8896B99A872C5934D6F0B234F2,
	DropdownItem_get_image_m1C89F635266FD742D3542FA99DB1A0D224CFD270,
	DropdownItem_set_image_m133BCC7D7320DC10EBBF5193FAE0E60C3F9A15C2,
	DropdownItem_get_rectTransform_mFE9F9A7B8259B150E399A40F9BE3555DC3273926,
	DropdownItem_set_rectTransform_m0C6BAB5E2EB69C438BF2F8D4DDE3E0DE136651AD,
	DropdownItem_get_toggle_mCC7B7993BF2AB865B2B6B82472391D5B1AB873D3,
	DropdownItem_set_toggle_mBD6E9CFB6C3757CB013CB683A8BB6DAFF34A94FA,
	DropdownItem_OnPointerEnter_m6020D4A90F1136573E7173875329573C07219B3D,
	DropdownItem_OnCancel_mD75155CB0FBA2A6FF217AC54CD0651061E830B5E,
	DropdownItem__ctor_m3A599BCBC6EEC74EA7CFE8D6AED4080D041BBE65,
	OptionData_get_text_m2E822D5D50B597BFBA7AB3485EF15B526A726A1A,
	OptionData_set_text_mFF1E8A215A399CB9EA9DDFB0C6F3B9F068837226,
	OptionData_get_image_m5C866E5C2E025EABF5591C7F88FB46E426EF20BA,
	OptionData_set_image_m17048079C3FCD19A6D190AAEBA198DBB74D93C88,
	OptionData__ctor_m5B8E6B683070AB406FA738E689E2FD4055697FB6,
	OptionData__ctor_mC08B019055F1AFA7CFC262EF24A64F6D8E7C84E6,
	OptionData__ctor_mFB73CF17AD7974CEBAA97EB2C90B2E37FB624065,
	OptionData__ctor_m19DDCFEF4911D4F1E6D07F5CE9924AF555CB4EC5,
	OptionDataList_get_options_mBC2E351252449F2B611857D18B396CEDF2289781,
	OptionDataList_set_options_m826C957D08679C2620276456BCD535D148640174,
	OptionDataList__ctor_m698166937E8E51F44FC2CCE7B3EF7BDA3932645B,
	DropdownEvent__ctor_m65812C2BBC12188466CE844E9EC223B89E0946C1,
	U3CU3Ec__DisplayClass69_0__ctor_mD126C84DF1978CB7709CC2B7374265951604D581,
	U3CU3Ec__DisplayClass69_0_U3CShowU3Eb__0_mE6A18CF6156B45C4052F1CBF0707DAB544ABA722,
	U3CDelayedDestroyDropdownListU3Ed__81__ctor_mFA83B08D54630018817A4BF76653AFDD3B1F8F69,
	U3CDelayedDestroyDropdownListU3Ed__81_System_IDisposable_Dispose_mA124316B9DB4950F9756BDB797488D41F72383CD,
	U3CDelayedDestroyDropdownListU3Ed__81_MoveNext_mA1EBFA3FA05EF54A866C4C59793A1B9A2D1C9E70,
	U3CDelayedDestroyDropdownListU3Ed__81_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m39BF9EDA0A38F97C5D75B53E1EF3954A3F58DE1F,
	U3CDelayedDestroyDropdownListU3Ed__81_System_Collections_IEnumerator_Reset_mB0690A94BB4E305E068156253A5D0F9044C7A101,
	U3CDelayedDestroyDropdownListU3Ed__81_System_Collections_IEnumerator_get_Current_mFFDC09A7ADBA08CB2BE7D2DE3F83C54F5418F8F0,
	TMP_FontAsset_get_version_mD6CCB4A7D6F1FD5C46C65A0711426299D50B2AFE,
	TMP_FontAsset_set_version_m7E9B93F4FDCE9EF03EDE8EA1245C522AFC61DE02,
	TMP_FontAsset_get_sourceFontFile_mD9B0F4FCBBCDCAAE5A370399DDE46C768B736722,
	TMP_FontAsset_set_sourceFontFile_mD3E145F204C872196FAE7DF1B97724C43BE24377,
	TMP_FontAsset_get_atlasPopulationMode_m31A707178FB4F1722BA7D090A8E169CE2FAEB19F,
	TMP_FontAsset_set_atlasPopulationMode_mE002B4CEA0FB2CA8A52C77820B054165881E4FA4,
	TMP_FontAsset_get_faceInfo_m1EB979B4CA53AA9EC5B09C445E28C24A477CBA6F,
	TMP_FontAsset_set_faceInfo_m711C454A1E8FE2B5DF455B6673257187B4C65B39,
	TMP_FontAsset_get_glyphTable_mF205AA67646F5B0D2642CCBD2C1386F9DDDB8BCA,
	TMP_FontAsset_set_glyphTable_m4604AAE8FFF44D5129D1BA4DB8354EB850550528,
	TMP_FontAsset_get_glyphLookupTable_m8793591000281F1CA28F3B9BDB8ACD3316BA64BB,
	TMP_FontAsset_get_characterTable_m8447F604F2D0F3615DA9AC1EBF5C289E112EDDF6,
	TMP_FontAsset_set_characterTable_m00ADF21D5EC39D8DC306332253F7123C82AED02B,
	TMP_FontAsset_get_characterLookupTable_mEFAADDFAA6233DFEC3A0D8C163588B3C678451E9,
	TMP_FontAsset_get_atlasTexture_mC44D519047CD3E54BD92AB8FE6773034F990AC7D,
	TMP_FontAsset_get_atlasTextures_m80D4DF83161F39AC7D06B0B097038B1E02AFE307,
	TMP_FontAsset_set_atlasTextures_m5FED84FD7829C6B72E5937EC8DB9F844E654E937,
	TMP_FontAsset_get_atlasTextureCount_m45F8A9CA48B566FF57230414BC298846FB0DD407,
	TMP_FontAsset_get_isMultiAtlasTexturesEnabled_m0F308BCE9267BCAB86BBF7CEF0CBAE302BF5E8C0,
	TMP_FontAsset_set_isMultiAtlasTexturesEnabled_m1EBFF9BBDE2D2A4E0B754FB1CCC45B76B0F1FA10,
	TMP_FontAsset_get_clearDynamicDataOnBuild_m0D55A98AE41E211EE960F57F0A835A6FBB627B13,
	TMP_FontAsset_set_clearDynamicDataOnBuild_m74DA616F7435271D377E7092D3B7BB2A95EB7EAD,
	TMP_FontAsset_get_usedGlyphRects_m6E5D5A9E86583AE37DA1ABD7F184A7F359C9627E,
	TMP_FontAsset_set_usedGlyphRects_m75EC42D8C57FFCA998DC859C5B6D91D03F7C1615,
	TMP_FontAsset_get_freeGlyphRects_m093618A6748A2CB3C23904B03E5AD85B4B04521D,
	TMP_FontAsset_set_freeGlyphRects_mC976815EFF6313591B1B2A4CFFF9C77249570DC1,
	TMP_FontAsset_get_fontInfo_m88825262BEC22C48EB4854A09CD31908024777FD,
	TMP_FontAsset_get_atlasWidth_m45CB71477140814BBFF666E9179D0F9BFFA03EFC,
	TMP_FontAsset_set_atlasWidth_mBF3960711180E34BB43393B4AA9951DC82B3535E,
	TMP_FontAsset_get_atlasHeight_m95F59523E66882079E1D2A4157DE5FF52C4892AC,
	TMP_FontAsset_set_atlasHeight_m1272E53248672683B4210DC23137D7F270CFE81C,
	TMP_FontAsset_get_atlasPadding_m556957263DC5F92E8EAA8460635860E96ACBD616,
	TMP_FontAsset_set_atlasPadding_m3113EAFFC2BFEE6B95D5371D0325A8284CFBDA4B,
	TMP_FontAsset_get_atlasRenderMode_mF139904718DC44F83E657D2FB175A52B45B4FFAC,
	TMP_FontAsset_set_atlasRenderMode_m82C0939AEF473FB4A319457D4B6B7E67F36059BE,
	TMP_FontAsset_get_fontFeatureTable_mF00EEAEDD0448BE4667CB959CCE79ED45D2300AE,
	TMP_FontAsset_set_fontFeatureTable_m1FFFA492C09D14639BA2FA94B334758AC5E1B009,
	TMP_FontAsset_get_fallbackFontAssetTable_mE0C2D8D8A55C5E2FAAB13CE0A5591C82F1AAF15A,
	TMP_FontAsset_set_fallbackFontAssetTable_mEFEEF51CD10815BE4CED7C0D356041401FB8BC1E,
	TMP_FontAsset_get_creationSettings_mC0B2DC9BAFE3BECB8F00A82C586B45FD0B2C9F6F,
	TMP_FontAsset_set_creationSettings_m81E03C271762A9134DEA4A3A8EA2EA47078DB000,
	TMP_FontAsset_get_fontWeightTable_mC27EC0A27F82292FB24E3AB7B87421AEFD0869DD,
	TMP_FontAsset_set_fontWeightTable_m9E22B6BBDB11B70D6696C79F6E1B85F4D3183B5E,
	TMP_FontAsset_CreateFontAsset_m735DB93A8954566CA674FD0E6C95B4D20E039465,
	TMP_FontAsset_CreateFontAsset_mC0DE04380E79B7A0A33E5F84DBD7B8595A27C639,
	TMP_FontAsset_Awake_m2B709B96B357084D0D82D4DFC69AABB73061FB21,
	TMP_FontAsset_ReadFontAssetDefinition_mC268F8946D0D6B28BABB3BF28FDF64FABDA2DF93,
	TMP_FontAsset_InitializeDictionaryLookupTables_mEB2EEBAF42E2F540C08EE513E9C03D814E6213C3,
	TMP_FontAsset_InitializeGlyphLookupDictionary_mFAED66009A159849EC515F922317CA629B3DF1F3,
	TMP_FontAsset_InitializeCharacterLookupDictionary_m598F2AE1FBDE1D58002C4C89371B8FEF1AE61C7D,
	TMP_FontAsset_InitializeGlyphPaidAdjustmentRecordsLookupDictionary_m0A997B751301AB9C3D3A495D02462B116CEE08B5,
	TMP_FontAsset_AddSynthesizedCharactersAndFaceMetrics_m4B60AD7CEE8FD7A1DA1EDCAFC6A55E4762F174BB,
	TMP_FontAsset_AddSynthesizedCharacter_m1BCF99FA52D4BEC7EF4C599ABA57270A444F9903,
	TMP_FontAsset_AddCharacterToLookupCache_mA1A087CCE0961AB0FDE7032A5592BECC31FDE76E,
	TMP_FontAsset_SortCharacterTable_mDC720A14A27EA7CBAFCBD87B376379A177A20E8E,
	TMP_FontAsset_SortGlyphTable_m9A2A9FC31D2388CC762B28BC821AB380CCFCDBCE,
	TMP_FontAsset_SortFontFeatureTable_mA987AB015B7897DB1503B295D26565CFAEAB010F,
	TMP_FontAsset_SortAllTables_mB36ED96BA086B55246208EF70C141199131FAA5C,
	TMP_FontAsset_HasCharacter_mA90CDB3FAED3495F38016D37AE6EB47AFF5F9B3F,
	TMP_FontAsset_HasCharacter_mBCA92C1927170D017B189B711EAFF3B94D09E5C2,
	TMP_FontAsset_HasCharacter_Internal_m3BEAA9D7D0F4794B1C70CC846AFFCEA2664977E6,
	TMP_FontAsset_HasCharacters_m4FE851AD87F33A07C2ACF1E6EB3EBD53CE79D3BC,
	TMP_FontAsset_HasCharacters_mBFCB2CF8940F3C6B932EB5FDC7DAA08EFCB57190,
	TMP_FontAsset_HasCharacters_mD2F9B73092E10B96039C1167BE7C5071B9954AD8,
	TMP_FontAsset_GetCharacters_mF4A9E3F51C7F907DED5B55E780EA12CD6A118E81,
	TMP_FontAsset_GetCharactersArray_m5162A0B4367DB2DE3DD7149214D493696CB503C9,
	TMP_FontAsset_GetGlyphIndex_m22E9BF763EC7252C7FCD1D89B97AE424FAF7C957,
	TMP_FontAsset_RegisterFontAssetForFontFeatureUpdate_mCA002DC71DE402BEE2C442826C3ED618FB852076,
	TMP_FontAsset_UpdateFontFeaturesForFontAssetsInQueue_m6815C83C4C471BA54A26D97A94AC8B8D0FA112F2,
	TMP_FontAsset_RegisterFontAssetForAtlasTextureUpdate_mB2E8F0BE304B5933C6861714DEE68FF97B9856EA,
	TMP_FontAsset_UpdateAtlasTexturesForFontAssetsInQueue_mDBF71CCDD755921536D87A8821968D03F6D8CCE2,
	TMP_FontAsset_TryAddCharacters_mC2E29947A0696751B70B643959C787825BEE0A99,
	TMP_FontAsset_TryAddCharacters_m007642E36561CCCBB6412F12387011A113E7CB58,
	TMP_FontAsset_TryAddCharacters_m790E9AC68D5E0B177490733D1BC69CA42B04CDB1,
	TMP_FontAsset_TryAddCharacters_m899ED242A4630FA7A60DD9EDC140C5E6508A8517,
	TMP_FontAsset_TryAddCharacterInternal_m95DD37F41C18EE7692B44DCD984CD12C2350C122,
	TMP_FontAsset_TryGetCharacter_and_QueueRenderToTexture_m7C957763D146CEBEE8CE749B147DBDAD9D7D16E9,
	TMP_FontAsset_TryAddGlyphsToAtlasTextures_m7498D7F7102E6B35C41CE28CE0A60AAE9228DC47,
	TMP_FontAsset_TryAddGlyphsToNewAtlasTexture_mD885238E48514F6063212EA6964ECC32F4F1D4FB,
	TMP_FontAsset_SetupNewAtlasTexture_m01889BA60E325103E4BB5A8B2D6AA8EB0CDA9B92,
	TMP_FontAsset_UpdateAtlasTexture_m748FA0BD4B0A8C835518565D119585ED7B9A209D,
	TMP_FontAsset_UpdateGlyphAdjustmentRecords_m8530223EDCBEB89B965D763C65AC194F144C2C23,
	TMP_FontAsset_UpdateGlyphAdjustmentRecords_mAD7016C52AE74A9F7482D6CCCE7F7319E78229FF,
	TMP_FontAsset_UpdateGlyphAdjustmentRecords_m04C430C2BBEBD156F4125657D24D2B8370DB991F,
	TMP_FontAsset_UpdateGlyphAdjustmentRecords_mC1E677EF9FB0B1E4400B4790468E542B5360B7CD,
	NULL,
	TMP_FontAsset_ClearFontAssetData_mB81DC841D38B3AC504B53D0920D86E6E4E56596D,
	TMP_FontAsset_ClearFontAssetDataInternal_m060146345FFE228F634C73F623C99666639773E2,
	TMP_FontAsset_UpdateFontAssetData_m093EFEDF92C4667C5A92C6180B30A2F532C5C88C,
	TMP_FontAsset_ClearFontAssetTables_mC1F9BA41514D50F1E5A9CDAD3A845D90ADB98D56,
	TMP_FontAsset_ClearAtlasTextures_m9B1D7B0F65146FF93ECDE10DFC286D8A38D5C007,
	TMP_FontAsset_UpgradeFontAsset_m97EF749DD42303D0AF9EF5C120832CA697F9DAC1,
	TMP_FontAsset_UpgradeGlyphAdjustmentTableToFontFeatureTable_m3C51FF9BA35FBA397791A4EFAAFB4B413553F492,
	TMP_FontAsset__ctor_m20A531FC2F5114F9D1B98B38E06ACE46AF3403EC,
	TMP_FontAsset__cctor_m6DF47BFD17DC2C50356B01C6F41FDC6532648EF0,
	U3CU3Ec__cctor_m22BBC842831DF1DFBE74F04A06CDD7808133B90E,
	U3CU3Ec__ctor_mFF3DD36E78B4295FBBC0AEA2BC2B5D1C50E3FDC4,
	U3CU3Ec_U3CSortCharacterTableU3Eb__124_0_m6AEE1B8840F16BB5E083850B57EAF42151BC2781,
	U3CU3Ec_U3CSortGlyphTableU3Eb__125_0_m19A43542E1087AC697E12E01BCD19D210B3C7E51,
	FaceInfo_Legacy__ctor_mCDDCBA8EA56302A85281073F59AF8CF669DB5260,
	TMP_Glyph_Clone_m9A59A9A4503F2C7B553A786CF79A022956EF7091,
	TMP_Glyph__ctor_mEAE3F4DA2D1BDC556A578BAAA15BD354183AE2A8,
	FontAssetCreationSettings__ctor_m9A07F1B7C85235E9BDA86E7505E0A5AE0B78E5BA,
	KerningPairKey__ctor_m76933735460799247D37F13189B62469E35C767B,
	GlyphValueRecord_Legacy__ctor_m6E3D36058693888F61D14BA825F5F29EA4FC7033,
	GlyphValueRecord_Legacy_op_Addition_m4AE9E2E46D9322962969207ABC806BF6CE792F88,
	KerningPair_get_firstGlyph_m8B473F310BB1D0E83BE4DB2E9C395C97E578BDCD,
	KerningPair_set_firstGlyph_m558F1AB56DF0BC72921E60524E906B3308EF6254,
	KerningPair_get_firstGlyphAdjustments_mDD893850E04A182C37A2360992AE0F352E585600,
	KerningPair_get_secondGlyph_m7F0717E0FE69CCE0ECFFB39680839D2734C095F0,
	KerningPair_set_secondGlyph_m73FF4FD9F0409E3B2FC7DBC542C47DEC6E6979B5,
	KerningPair_get_secondGlyphAdjustments_m04BE3DDED12C198E109C8E176E8BD8A4D49B0358,
	KerningPair_get_ignoreSpacingAdjustments_mAF52BE99F08941D553BB53FBD6FA02391A1CE07C,
	KerningPair__ctor_mE4BD600F9F79E3590C13CE3F1C7BC89693420416,
	KerningPair__ctor_m7AB7CD68A07A7BD8B1CB2A41C84EA8115F3ED974,
	KerningPair__ctor_m97D222190FBC36A6C9843C7CB8F4E5F0CF963A11,
	KerningPair_ConvertLegacyKerningData_mEA902FF8F87D8EEB50C054172D20CA9795ED1D11,
	KerningPair__cctor_mECCE71DE39C14E6EDC210466B15D4C6CADEC6CBD,
	KerningTable__ctor_m5D6DF57B05146E104A2756917A31C27D0CC7A108,
	KerningTable_AddKerningPair_mBAF75C93E61FAEE9998A2EE648119E723B8BE020,
	KerningTable_AddKerningPair_m4392E91557C580125D59C4018E880D056476CE89,
	KerningTable_AddGlyphPairAdjustmentRecord_m3542CA49AAE214B069445C85916D73C339EAF7E2,
	KerningTable_RemoveKerningPair_m5FCDFF2DC4CAADDD361D70158DEA5E0A43010469,
	KerningTable_RemoveKerningPair_mEE2E6198028618C0A5055AEE1A2D1DBB7F108DAD,
	KerningTable_SortKerningPairs_m59E143CF7D8EFC9E72ABA558A6680A6B3B86161B,
	U3CU3Ec__cctor_mE35FA8FEA10BD859A3ECA9CFE2661AF2EFAB4F41,
	U3CU3Ec__ctor_mD36FC6FECCA7288F1C50E1C1C33357CB9CBD141A,
	U3CU3Ec_U3CSortKerningPairsU3Eb__7_0_m20BBABBDA17C6FD8E15138433B9D7FF73FC93183,
	U3CU3Ec_U3CSortKerningPairsU3Eb__7_1_m6B19D6F1591A26B21706695288AE73FB9B26C470,
	U3CU3Ec__DisplayClass3_0__ctor_m5B27602FA83CA988234ED481E13726F7ACFDEDFD,
	U3CU3Ec__DisplayClass3_0_U3CAddKerningPairU3Eb__0_mE8621E8C8419041EA962313967C9B22DADB7EC59,
	U3CU3Ec__DisplayClass4_0__ctor_m7835A094259518636DCEBD5D5F5AC88B48799B4D,
	U3CU3Ec__DisplayClass4_0_U3CAddGlyphPairAdjustmentRecordU3Eb__0_m0457EDA34868349FD7DF84F23C8EC201BBA2FE4A,
	U3CU3Ec__DisplayClass5_0__ctor_m3718024C98F2CB240337703C272C56F9D60E0D86,
	U3CU3Ec__DisplayClass5_0_U3CRemoveKerningPairU3Eb__0_mDD3433EA90C0CAE26949E21FBB418FF2DA10E509,
	TMP_FontUtilities_SearchForCharacter_mAE34577D79CB6FFB29D2060F412D269E1F153D30,
	TMP_FontUtilities_SearchForCharacter_mAACB85FD391B93D395CC934A3975EC9F8C654D9C,
	TMP_FontUtilities_SearchForCharacterInternal_m5D2FAB64754939BE5D183DF2832305D4C0335923,
	TMP_FontUtilities_SearchForCharacterInternal_mBF347940D14E9D9585AC18E3037C0A4F0C1A8233,
	TMP_FontAssetUtilities__cctor_m4C4A8256EFF82F5C0B86B8E1A95B55DD69D049AA,
	TMP_FontAssetUtilities_get_instance_mF9DD70CC93B066641699EEE945D7E10BF9829179,
	TMP_FontAssetUtilities_GetCharacterFromFontAsset_m26EEEB3C26157C92CF623A246D6E92085E06CA26,
	TMP_FontAssetUtilities_GetCharacterFromFontAsset_Internal_m0275490A50962C94DBC85C431D4FB8D3117C2716,
	TMP_FontAssetUtilities_GetCharacterFromFontAssets_mF773865B6F097CDA5625615EA2CFC39DFB7A12D0,
	TMP_FontAssetUtilities_GetSpriteCharacterFromSpriteAsset_m740B16719D09EF1F68B66DBE3D15265686D4DBB8,
	TMP_FontAssetUtilities_GetSpriteCharacterFromSpriteAsset_Internal_mF5BFC00DAC02457C1C8F372373029476D300D3E5,
	TMP_FontAssetUtilities__ctor_m8F25AE77B581CFF45180EF5FABBB4688608FDA02,
	TMP_GlyphValueRecord_get_xPlacement_m3BB0AE22AA4B44163AD2BFB438E60E227523D5E7,
	TMP_GlyphValueRecord_set_xPlacement_m12D97CDB7F44213ACBB3C015B5E88147147850A2,
	TMP_GlyphValueRecord_get_yPlacement_m4FC0DDE3029083A45158537122D3BC3391DF2143,
	TMP_GlyphValueRecord_set_yPlacement_m21EE385F1B674F9A575FFE6583A7E9035CFA2C24,
	TMP_GlyphValueRecord_get_xAdvance_mA01138133A0841ADC49C3D0718B2268D9819CE4B,
	TMP_GlyphValueRecord_set_xAdvance_m862DABDFC3FF1C78E6A4C655A6C5631B905370E9,
	TMP_GlyphValueRecord_get_yAdvance_m6F2282B9DF89F62B52A07D36327CC39720225BA3,
	TMP_GlyphValueRecord_set_yAdvance_m5369AC719C39D3B9B79F5FEDC85C109754A4D60E,
	TMP_GlyphValueRecord__ctor_m030CD9864F16A5FB58D41ECD6CF66EC883B078BA,
	TMP_GlyphValueRecord__ctor_m5F96BB76417057AB3AC83120DA921295DBCA9952,
	TMP_GlyphValueRecord__ctor_mFE317398DD11D070520A083E7C0758D7FD862F11,
	TMP_GlyphValueRecord_op_Addition_m27CD190E35E404FAF3DC7283A76FC20650E55A73,
	TMP_GlyphAdjustmentRecord_get_glyphIndex_m5DE8A84366AD7DC8B32D99B47D2BFE291F3C4F34,
	TMP_GlyphAdjustmentRecord_set_glyphIndex_m3045246D7E256A1DEC17ADE2887BCEB013DF2DBB,
	TMP_GlyphAdjustmentRecord_get_glyphValueRecord_m1368E9CA86E6E76E04901506445319BAEFD6AA56,
	TMP_GlyphAdjustmentRecord_set_glyphValueRecord_m47A43D4E95C3A89DC17588C3BE7F093517B4EBE9,
	TMP_GlyphAdjustmentRecord__ctor_m41FDDFADD92DB1A8446228B1108E3E5C985CAAE0,
	TMP_GlyphAdjustmentRecord__ctor_mB6BB797DD594B413042DD5D4FB8D691430FC8F51,
	TMP_GlyphPairAdjustmentRecord_get_firstAdjustmentRecord_m4782831AE89EF77464166E4EB47C251B8483A458,
	TMP_GlyphPairAdjustmentRecord_set_firstAdjustmentRecord_m795F115F13680DDAA3F4BCED9902C3CE3C8A497F,
	TMP_GlyphPairAdjustmentRecord_get_secondAdjustmentRecord_mF238079D6ADF0E2D6BE59D48758E13C2ED2F2B32,
	TMP_GlyphPairAdjustmentRecord_set_secondAdjustmentRecord_mAE3695EF425238B8F692F1808BF9055E63AEF98A,
	TMP_GlyphPairAdjustmentRecord_get_featureLookupFlags_mAAFBDA6BE590EC3C085CA1537384CB1D97390691,
	TMP_GlyphPairAdjustmentRecord_set_featureLookupFlags_m20C444D8AAE7A18E0B767B385272AE28C21007AB,
	TMP_GlyphPairAdjustmentRecord__ctor_m0BCCF9AF25F0A727D02FD778ACA2C7AD38F981CC,
	TMP_GlyphPairAdjustmentRecord__ctor_m33C61225BE06EEB15E3AD599451078F503BA4A60,
	GlyphPairKey__ctor_m59DDEB66E800AABAEF624BCCF1CE091F27F124A2,
	GlyphPairKey__ctor_mB1A0951B06F19D942015727B646A530A9EB68577,
	TMP_FontFeatureTable_get_glyphPairAdjustmentRecords_m00772830EC8C026F17A21CBC39D26FC4D0A49FB2,
	TMP_FontFeatureTable_set_glyphPairAdjustmentRecords_mCA20A72ABB8E829EE3C258B305143166EF220D62,
	TMP_FontFeatureTable__ctor_m6F156B35A4B68F5616CFD3236C64F1E790D69039,
	TMP_FontFeatureTable_SortGlyphPairAdjustmentRecords_m8BF5A029B84FF32BFCF4B32AD3D32F463DD050BD,
	U3CU3Ec__cctor_m3905215994C71FD63777B32B5154BC23204BDB84,
	U3CU3Ec__ctor_mE70BB44A038503EE1979AD30BA141C6792A5160A,
	U3CU3Ec_U3CSortGlyphPairAdjustmentRecordsU3Eb__6_0_m6E8D8B97DDFB03B6EF69ADF2E85D412411D50075,
	U3CU3Ec_U3CSortGlyphPairAdjustmentRecordsU3Eb__6_1_m12FAD67963FC77E6B359185D50EE168FCB5F7095,
	TMP_InputField_get_inputSystem_mB89A77F46D53CCD05D9F57E03F4586B90265A55A,
	TMP_InputField_get_compositionString_m4332AACD655CF044F84411B3BCE32BF2034AC4CC,
	TMP_InputField_get_compositionLength_m444E57B7F68C9AECE1CDEBF4732FAD66EBA4937F,
	TMP_InputField__ctor_m6C5321A190D58235E29A17D7EE17D249D210A07B,
	TMP_InputField_get_mesh_m200F4FCC0738B54A3DFED98FF430660DB52E3E00,
	TMP_InputField_get_shouldHideMobileInput_mB40438A3E4172E95CE11F03FD2484E954CDB1F1B,
	TMP_InputField_set_shouldHideMobileInput_mB1D2ADC209DE64154BAD42C2D25BFDA27081BB9B,
	TMP_InputField_get_shouldHideSoftKeyboard_m08F3F6AB9DC9A89029B6A41E26F42B7222535790,
	TMP_InputField_set_shouldHideSoftKeyboard_m0414B5C0C3B07F0371671963D699EEA303D8A13E,
	TMP_InputField_isKeyboardUsingEvents_mE8A5552B89353CF45DBC4206F55ED41EB7C0F273,
	TMP_InputField_get_text_mA4ACBF52435893D9DFD822A492454301740B3C6A,
	TMP_InputField_set_text_m684E9CDA2D9E82D1C497B5E03DBE79C00584FF62,
	TMP_InputField_SetTextWithoutNotify_mE5ED91EB1759228F56E15A0E4BF47A7F8E28AB23,
	TMP_InputField_SetText_m8D34D265867AA18228AA10E118A2DFE630911BFE,
	TMP_InputField_get_isFocused_m7FD1AA3B92404C30596FF6EE5F644757A2F060DE,
	TMP_InputField_get_caretBlinkRate_mACAF2093330BB9CB0B8C5F7D76EAA4EB0AE4DD18,
	TMP_InputField_set_caretBlinkRate_m4D4B8F3C2169EE3FA7B27BECBD1563BFAD7B41F7,
	TMP_InputField_get_caretWidth_mA95E0A88F505D9618791AEDE9D649CA70F7E3B65,
	TMP_InputField_set_caretWidth_m291DBA8BEF0BD40BB4FAEE2AC71F9CDD114FAA9A,
	TMP_InputField_get_textViewport_m51E9CFB11A78199484D2BC2750F19DB7D2A26763,
	TMP_InputField_set_textViewport_m3CB40F8DD0636EFBA496F1E76D41EE9C9570CB17,
	TMP_InputField_get_textComponent_m85C4BC3F4C18206B3B942F03DB0B953B028EE1CE,
	TMP_InputField_set_textComponent_mCECC9B18AE37E999E5B38431D89C17B9BE384E07,
	TMP_InputField_get_placeholder_m6C5FDEB031E2900A1792B928E4864B21B144AB3C,
	TMP_InputField_set_placeholder_m597012397FF55E6DE7E5E63972A3BE03EEAC480A,
	TMP_InputField_get_verticalScrollbar_mCB3FAFA0D86926FCD1A6620009BF3AEB274F17DD,
	TMP_InputField_set_verticalScrollbar_m8863C8FDC647B006DC8DAD8C3EDCF6353E08F945,
	TMP_InputField_get_scrollSensitivity_m219F37C4A7DF784B9522EE565AE70EB813E799A8,
	TMP_InputField_set_scrollSensitivity_m67129EC21A5560B781F61CB5C2282F977EB9AE12,
	TMP_InputField_get_caretColor_m9733E1CB5CAD3CCFA9C32343D12F0095BA6DC76F,
	TMP_InputField_set_caretColor_mAF2AF8646B44D6AAA885F2A664DB88431E22177C,
	TMP_InputField_get_customCaretColor_m566EC393CFD6206101A2E0BE5AA2BB9D7233CF19,
	TMP_InputField_set_customCaretColor_m9A33CA9154050A1C09881207B9C7B832B6C44B6B,
	TMP_InputField_get_selectionColor_m99D5B9FBC11FEAA170C113FB238120A73429F5BB,
	TMP_InputField_set_selectionColor_m9B30F4DC90BBD21ECDA6B5888F2F8E4B2EC7686D,
	TMP_InputField_get_onEndEdit_m0CE9718C71A834CC279430E20DC7FF4F42114FD3,
	TMP_InputField_set_onEndEdit_mE34D6037D2C9FCAE1C9AF253D34D72541D306F4A,
	TMP_InputField_get_onSubmit_mAA494FA0B3CFFB2916B399BD5D87C2E1AA637B90,
	TMP_InputField_set_onSubmit_m0FD1B91CB6BDD3864C74BFDBC458DF0C3B2EA193,
	TMP_InputField_get_onSelect_m6762226148A4B3265EE5FD70ED894BBE8DE86AF0,
	TMP_InputField_set_onSelect_m0D471B94B9358B9AD840B3F5E2A756C1D5DACD1F,
	TMP_InputField_get_onDeselect_mC9429495032728AEE8FCB818D61EDFB5DC7F9B0A,
	TMP_InputField_set_onDeselect_m13E94D8DA5530F8E6B438D98E1C8B801E3702006,
	TMP_InputField_get_onTextSelection_mEBA14AF8E2BAF100DE885B78385F510A8E978A33,
	TMP_InputField_set_onTextSelection_m52044BF605C6084CC95CB9D492652BA29D4E5699,
	TMP_InputField_get_onEndTextSelection_mB01ED58A536B3DCC323A974C27C50337EAC7CAD5,
	TMP_InputField_set_onEndTextSelection_mAF020E9DF7C78B4A16653D2F5F77C8B78B474C67,
	TMP_InputField_get_onValueChanged_m407B5F5BFD1F4B04032F6B90B06F5072F5993407,
	TMP_InputField_set_onValueChanged_m2C1B41AC850107D098E1D8BC481D23ED5310952E,
	TMP_InputField_get_onTouchScreenKeyboardStatusChanged_mF14075CDC1B4C99F300FCAD70350CDF144CB4818,
	TMP_InputField_set_onTouchScreenKeyboardStatusChanged_m7AC290C056FF92BFA6558AEEE89E10BAC48A92CA,
	TMP_InputField_get_onValidateInput_mF293BE6DE7AAA1F8E37E20B73418A639A8963A7D,
	TMP_InputField_set_onValidateInput_mDA2BDCF7BFA9F24D48BA27027B9BCD366164C972,
	TMP_InputField_get_characterLimit_m59833E0A22BACBDF3EDA6A70A30B87272FBAA409,
	TMP_InputField_set_characterLimit_m64ADC294FC147C1E0806B5C175B9EA626059D4DC,
	TMP_InputField_get_pointSize_m2F9C02B8B2E8501799E118F3FC1675DB1555EEB3,
	TMP_InputField_set_pointSize_m5001D4D1325CE0737CAA65D86ACAB2D88DAA87C3,
	TMP_InputField_get_fontAsset_m9EAAF4737728BB51C8D5D7A1AC46E77DD970F176,
	TMP_InputField_set_fontAsset_mB102F697B83B5115F2E4B30A076FE67D30BCA201,
	TMP_InputField_get_onFocusSelectAll_m6A1A06461D6B01EE2E68624B9D7E5E3C7D092CDC,
	TMP_InputField_set_onFocusSelectAll_mDC9C36C7201E90054B97AE94251577ABB103FD75,
	TMP_InputField_get_resetOnDeActivation_m6BB1C27CCFB72767235B459ED4F3A81965273771,
	TMP_InputField_set_resetOnDeActivation_mCB035C9EADE4A6896C42DDCEC996D00D4A7F6CB2,
	TMP_InputField_get_restoreOriginalTextOnEscape_m138E8AAD613E1A3693B8B9E6469B6450F86D367D,
	TMP_InputField_set_restoreOriginalTextOnEscape_mA8F67F61689BBD34C3B4811DAD7380253EA8069C,
	TMP_InputField_get_isRichTextEditingAllowed_mBF19A7F1ECC8F9C2F06D1D7636F45882E671CCB3,
	TMP_InputField_set_isRichTextEditingAllowed_m3BEB725A42ACC0CD7990E36B4B707AB892EA8B21,
	TMP_InputField_get_contentType_m32EEDFC275E9CB6C759A4F117EBAA40336B9030D,
	TMP_InputField_set_contentType_mB9BCF78B6868FBB3CDE671DDF008E3716D3ADC91,
	TMP_InputField_get_lineType_mE221F133A310EB6C93DA24E1F5900E948771D64C,
	TMP_InputField_set_lineType_m0B3A3770A8229ABCDF139F80765FC6C3A659FD21,
	TMP_InputField_get_lineLimit_m771801BE2D9D7F788EDA1F90336929FC54193D9F,
	TMP_InputField_set_lineLimit_mD645AAD616399138A50AA905E8A8CD4B5B177B62,
	TMP_InputField_get_inputType_m93A6CC8FF76412F46471D91952323CE4C63B7D34,
	TMP_InputField_set_inputType_mF1647C27280C29CE45DB74457ABF43B46A19366C,
	TMP_InputField_get_keyboardType_m8B616A743B2FAB03C6263F1582171BB390F94F8B,
	TMP_InputField_set_keyboardType_m97210FB5D41B6AAE5352D2BD6C1D45AF8174FC95,
	TMP_InputField_get_characterValidation_m57E36C62FC9E23DB17F440BA4325A314EF0B0679,
	TMP_InputField_set_characterValidation_mE2D042600CF00A3F5D8EFF09271C0FCDCE324D4C,
	TMP_InputField_get_inputValidator_mF47AEABCFD04F4F9FE7F7C64331A8B01B7976CF7,
	TMP_InputField_set_inputValidator_m619FB8CCDB4B2BA3FE13ADF567137061647E9AA2,
	TMP_InputField_get_readOnly_m551BFA0AB64EBD12F49C0993305274BC8176E0A5,
	TMP_InputField_set_readOnly_m05A0789FE9583F510DF5299A963BA0C32EC03C8A,
	TMP_InputField_get_richText_mFDFECA8E9F49F27A5FCCB4D147C283581BE66155,
	TMP_InputField_set_richText_m1AE9CD128CFF3316C1C602717337241666AA1FA4,
	TMP_InputField_get_multiLine_m3000150A39B90BCFFAFD41E0F49F479323F045B7,
	TMP_InputField_get_asteriskChar_m3D3F22537749D339A3DB36BE6C56015D0B06A38E,
	TMP_InputField_set_asteriskChar_m5A6D4BEB046DC3E1397972AEF86F00F5FEDB4CD0,
	TMP_InputField_get_wasCanceled_mEF43E80CFB8EE3DCE8599D64213D3F977D9794FF,
	TMP_InputField_ClampStringPos_mCEF1B5B73F19C6FFA1A9411FCA485B7F81C73D05,
	TMP_InputField_ClampCaretPos_m24F8EDB52862BA470A2CD5FD3D2A62AA86A00FC1,
	TMP_InputField_get_caretPositionInternal_m21C9BFCD70C944B374E5C916C7E7E67B75B831EA,
	TMP_InputField_set_caretPositionInternal_mEC3488328558F5257115078785242BE6C59BA1BF,
	TMP_InputField_get_stringPositionInternal_mBDA10D8ED51D01C973FB6CFDD1096DD29CA5D214,
	TMP_InputField_set_stringPositionInternal_m0C190ABB9829A8F93268F669655D6AF29E25E265,
	TMP_InputField_get_caretSelectPositionInternal_m977002CC2C821A3B4FA5FB3F1BC15C7DD0BA35A4,
	TMP_InputField_set_caretSelectPositionInternal_m2AA6FD295A4E6D7236ABFE88B4CF49EDDA566191,
	TMP_InputField_get_stringSelectPositionInternal_m8FE3D7533D67501DFDC1EA83B3FD72F8C1E0A79D,
	TMP_InputField_set_stringSelectPositionInternal_mCBA385B30788D514E2306703B370F6350E1B9997,
	TMP_InputField_get_hasSelection_mA2CF23CC43AD3EE9F66C67A5995407EBB2F59565,
	TMP_InputField_get_caretPosition_m1F103634776349DFA375AC8C64F1D2535A693A15,
	TMP_InputField_set_caretPosition_mD5B0AFA01D9947B7EFC98CD4C4BF927518513FF4,
	TMP_InputField_get_selectionAnchorPosition_mAAD925C368B16EFE972C11F551A1D9DCB93B0B93,
	TMP_InputField_set_selectionAnchorPosition_mB6E72D94EFD7C55EAFA8F8AAC30D255935438B06,
	TMP_InputField_get_selectionFocusPosition_m64C9DB19CDB18E29B7CB02DCC84B5F05ACDB473E,
	TMP_InputField_set_selectionFocusPosition_m862731C1A303D3778E292AB427BC1BEF4407050D,
	TMP_InputField_get_stringPosition_m5C9E52B4A7304183ED4F690AD6239D57B142A7B6,
	TMP_InputField_set_stringPosition_mB6538BDB302FECF09EAD5BA986FB11BBE6A49E8A,
	TMP_InputField_get_selectionStringAnchorPosition_m321370B1A913B9B619DE5C5A5E5FA8D251C0B8F2,
	TMP_InputField_set_selectionStringAnchorPosition_m60E8DEBD9389373AD410E7E868D3C36CCA202B8E,
	TMP_InputField_get_selectionStringFocusPosition_mA044AFF5699E8B61BF3CBE271522AC8CA7088B0F,
	TMP_InputField_set_selectionStringFocusPosition_mB23FDE5288C4F033028320FE4DBDEB096AAB3917,
	TMP_InputField_OnEnable_m3A78BC51F18EDA6775A85DB81F8F401B17D04475,
	TMP_InputField_OnDisable_m2E967647BDF216075B8F3EB3C1559B6AAA2D3C95,
	TMP_InputField_ON_TEXT_CHANGED_mEA6A2C8BD4AF9D1C0CF5A6EC9608F2149256B553,
	TMP_InputField_CaretBlink_m280BE5F4289F6C4ABA767D15C147E39DA6B54AD5,
	TMP_InputField_SetCaretVisible_m1D8A496EA7137B9CCEFD1785B1D5BFA3A3325194,
	TMP_InputField_SetCaretActive_mC9858C9E1FE6D8800219C49C52A9FACC1ED5EEC1,
	TMP_InputField_OnFocus_mBDC52EE4DF24C43E6C4C272B09FDAE6F7CB5970F,
	TMP_InputField_SelectAll_mC66107E00F20D1E401A04108D2A9136559AD23F7,
	TMP_InputField_MoveTextEnd_mC781F7D531E0B22F73DF2C6E5F882243DD133E6A,
	TMP_InputField_MoveTextStart_m8D0AA8989DE9DB5D0B540343640BFAAA1C0CEC9E,
	TMP_InputField_MoveToEndOfLine_m42BC00BF1E1A82E956220244E72E577C667297D8,
	TMP_InputField_MoveToStartOfLine_mC73D470B797643AC87FCFCC778D43DD945B18252,
	TMP_InputField_get_clipboard_m53271C5A442FE382959DEF76525B14616E58BFAE,
	TMP_InputField_set_clipboard_m2A21EC4F18EF6AB80DD0D03887BB115E3AB5D0BB,
	TMP_InputField_InPlaceEditing_m2FEE000FC40DAF7CAE8B2DA4FF5D364E30873FC1,
	TMP_InputField_UpdateStringPositionFromKeyboard_mED93ADC3A6B158262ECFB07CD6B21EC39B99022E,
	TMP_InputField_LateUpdate_m9D1496DFE349C330C4FD29BDE290209E22E66DC4,
	TMP_InputField_MayDrag_mB0F0807D39BED3B80A5EF8F757E8956D5869ED1F,
	TMP_InputField_OnBeginDrag_m0F9B51A6CEBD12DAB6DFFF1CA1F15AD1D3495940,
	TMP_InputField_OnDrag_mBA444852C709D10301A31FAD4DA6AD33479C05E4,
	TMP_InputField_MouseDragOutsideRect_m19E6E5421BFCC996DC30FD6FCD07EF890931FB44,
	TMP_InputField_OnEndDrag_m0CB1102EBBDC28E1AFA38FD0D50469F08492D4AC,
	TMP_InputField_OnPointerDown_mA194D68CFB19DF910D8EE1B63DF5FF4D795C6C8D,
	TMP_InputField_KeyPressed_m1C528E6E9E2FB05DFA8CA61F880DEE24C2C304F6,
	TMP_InputField_IsValidChar_m12ACC6064ABA5E72C3CF133AFC578300A67EEFC1,
	TMP_InputField_ProcessEvent_mED1F52CCCF82A49EF61E080D8A55B63EB8878124,
	TMP_InputField_OnUpdateSelected_m04CB39F3A7156D62E73D7A04E52F4356DD40FCA3,
	TMP_InputField_OnScroll_m87663801D19AE16C594D8C106CD2A5532CE1B22E,
	TMP_InputField_GetScrollPositionRelativeToViewport_mE320B683741E2E7403D1F2ADCD65F44B68FA4158,
	TMP_InputField_GetSelectedString_m4BF128EBC96DAB95E95CD9F292A1EB99AD1238C6,
	TMP_InputField_FindNextWordBegin_m1207B66382CCC488015CD5EB2E017C20E20A6AF2,
	TMP_InputField_MoveRight_m8831525A4FF9E75CA86BD5E4BAC9351EF640D497,
	TMP_InputField_FindPrevWordBegin_mD8DA9061047B673CDB67C2F762A14C1514CFEC17,
	TMP_InputField_MoveLeft_m787CBD78E57FDD7DC28A10CA1624EA4118157898,
	TMP_InputField_LineUpCharacterPosition_m6FAA53F203CF66F87F62F985E830CB769A169F16,
	TMP_InputField_LineDownCharacterPosition_m0A95990F452ECFB15A5BF8C12D8E92592CF3B2CD,
	TMP_InputField_PageUpCharacterPosition_m68C124FCEE737E9CB486D5218A2B5804D407BD0A,
	TMP_InputField_PageDownCharacterPosition_mD00879F9AD1E5315C8896D8CB421FAB93045F818,
	TMP_InputField_MoveDown_mB8F65AD03355C867F63BAB0021C93B75F534CCBE,
	TMP_InputField_MoveDown_m96FE2822D035DFBE82474737DEE8DED622AAD868,
	TMP_InputField_MoveUp_m0A8E579FDBE203C29D7AF1B4225C9A64498DE5A9,
	TMP_InputField_MoveUp_m79291882C851A7AEC3945EB8479D31984941F8DB,
	TMP_InputField_MovePageUp_mA945CEDD104AAC4B417B1AC6D95FC75798ED3040,
	TMP_InputField_MovePageUp_m1B01B4C15C5D556CED7B34E7F55149E1DA35ECF1,
	TMP_InputField_MovePageDown_mE32EFCBEB2A1D230D3C6C8B27357C454F4AD5EC2,
	TMP_InputField_MovePageDown_m80AEFB5ACD656505A347F13FAEFB55EA62F0EC86,
	TMP_InputField_Delete_mD817C69CFF25B762DF673A1FD31DAF0E2F761784,
	TMP_InputField_DeleteKey_m3EE34B2EE784E0F8833BCEA668B590D8C838BDCC,
	TMP_InputField_Backspace_m1962DCE85EA39B861EF3E9E147A63C8CFE58A917,
	TMP_InputField_Append_m4595DE62B0D6CD1E1EACC127F8B84563351190C8,
	TMP_InputField_Append_m90791E607DDDAD68C715529BF47B24726ED86582,
	TMP_InputField_Insert_mD8773951E82B4743AF137BE4EDA14915EC704907,
	TMP_InputField_UpdateTouchKeyboardFromEditChanges_m05E63AC0F9D593BB8584E97AC236646C05E22B12,
	TMP_InputField_SendOnValueChangedAndUpdateLabel_m9A56A0E7406E3E3362400445749CE33C20C7BC64,
	TMP_InputField_SendOnValueChanged_m9138A30966454771476FF25A71ED03DDAF6EC0C7,
	TMP_InputField_SendOnEndEdit_mBE399B126786848BC400A04B165A6C9BD6757CD1,
	TMP_InputField_SendOnSubmit_m3993BECBCAB4632CD5C564C0BC38486FC2320D14,
	TMP_InputField_SendOnFocus_m306B75E91484337E9A090AB1A45D971133ACF7C8,
	TMP_InputField_SendOnFocusLost_m0BC85C3C362617A4E81F9E9360207EFC0D2882FF,
	TMP_InputField_SendOnTextSelection_m301880AB4201417DFE7FEB6CC22A323DF0935ADC,
	TMP_InputField_SendOnEndTextSelection_m5142CBC7340FC8E2B0457DDD1F257C1A19DE01D0,
	TMP_InputField_SendTouchScreenKeyboardStatusChanged_mF0F1E86DFF3023EA6167004879DAE86E1D2C3AEB,
	TMP_InputField_UpdateLabel_mC40048ECFCF13981FE38993C7251024EC2477ED2,
	TMP_InputField_UpdateScrollbar_m61D071BE0C6F2D5C8FD3F75AF4B6A256685429C9,
	TMP_InputField_OnScrollbarValueChange_mD38291A7EBF4EDA6C308DF090261355519C10E03,
	TMP_InputField_UpdateMaskRegions_mD22E32D41A5E6EDAC8A7547194CA34A4DE918343,
	TMP_InputField_AdjustTextPositionRelativeToViewport_m7EC3FED9FB3F4F5450E60552FE36F0D79E48592C,
	TMP_InputField_GetCaretPositionFromStringIndex_m24E11A6B461D41DAD8CA4DC96F0AB263175DE283,
	TMP_InputField_GetMinCaretPositionFromStringIndex_mF22329EB6607A83C8791B9DE0A1FB4B8B53575AC,
	TMP_InputField_GetMaxCaretPositionFromStringIndex_m5A2C033C4018D10695C8E3CA0A53EA7E5F6F5B01,
	TMP_InputField_GetStringIndexFromCaretPosition_mE8952E126639234C711E6DA723C272AA6C22FB59,
	TMP_InputField_ForceLabelUpdate_m06F01A5D3EF44553E23404EC82D65B405A842C11,
	TMP_InputField_MarkGeometryAsDirty_m3FD825DDE67FAA8CFBF12EE92C65463823402138,
	TMP_InputField_Rebuild_mA714C05AB0AAF3BDB56E2534622E978943AC2317,
	TMP_InputField_LayoutComplete_m3C78365E6DFF603628C61A7321EEE5AA1FBCDA67,
	TMP_InputField_GraphicUpdateComplete_m5FE6F033C3688FD16CE1D52A0CABE075699C568E,
	TMP_InputField_UpdateGeometry_m44637D3DF51E919CE2F702A61B49A2F4FEFCEAFB,
	TMP_InputField_AssignPositioningIfNeeded_m3CA56BB25376EF783C8E1218AA2643FACEB924E1,
	TMP_InputField_OnFillVBO_m4F9AEEB359EABEA669C3E3587ECF4B1262067E6A,
	TMP_InputField_GenerateCaret_m6020296CC782C426A13349E6B8885C029DBEBB72,
	TMP_InputField_CreateCursorVerts_mAD2D8B4DD0331646AA852C4BFF7595CC76D27836,
	TMP_InputField_GenerateHightlight_m6B8F6ECF6369850A6B87D68E75A639021F463B8F,
	TMP_InputField_AdjustRectTransformRelativeToViewport_m58C2AAE39A4A6EE2309BAACBDFBAA22A885CF479,
	TMP_InputField_Validate_m76212763DA49DFD7C152C65F8AF6CC056EE69979,
	TMP_InputField_ActivateInputField_m9471012A606F201DF838539F5400D072A827914F,
	TMP_InputField_ActivateInputFieldInternal_m95B34ECC08F02FF048EFC2272CE07648657627BC,
	TMP_InputField_OnSelect_m586B40BE0FAFFDA515B1AF7A391094F076B2036F,
	TMP_InputField_OnPointerClick_m2A6F2110D5AD4EF8C3FBA29166BC76921C469C55,
	TMP_InputField_OnControlClick_m5E418EA29EFE5180655F904E5727AE8210B6EC21,
	TMP_InputField_ReleaseSelection_mC70F802822362452CFDD9FE095F5147E6BB5020F,
	TMP_InputField_DeactivateInputField_m1C829676E9DC0D3E5DAE85D1869D26FBF748184D,
	TMP_InputField_OnDeselect_m19AA85C6A6FAB27850293318B1D92908B82F99AF,
	TMP_InputField_OnSubmit_m52BE7037E939A81A0EF41FCB4DA55D77C5970039,
	TMP_InputField_EnforceContentType_m4D5F42FD6E04B3B3B541E978C9C603B7696E7AB2,
	TMP_InputField_SetTextComponentWrapMode_m9CF72ADC54A79451A8B35A85FFF2232F1D6A79ED,
	TMP_InputField_SetTextComponentRichTextMode_mAA7F99B2DFD5DD46007BF0247B37A8CA008F1947,
	TMP_InputField_SetToCustomIfContentTypeIsNot_mB9AC8BE6A15C7DC926F5064C49A0F2199CC6B14D,
	TMP_InputField_SetToCustom_m798A8846432794AA8687F6020551B512357D2CF0,
	TMP_InputField_SetToCustom_mB668A3AB0C1900F2B2618DB9C1288C805DD21D58,
	TMP_InputField_DoStateTransition_mB9F4AAD269179A5EBE7A31DDC64D8832C403F260,
	TMP_InputField_CalculateLayoutInputHorizontal_m46CEB3041DFCF55FF496A01B186965E0846BDAA0,
	TMP_InputField_CalculateLayoutInputVertical_m89664390EDA3B835EF3540E85A65978247884577,
	TMP_InputField_get_minWidth_m2A2D1042C5D78373A2AD8BBF514157D83C3A706A,
	TMP_InputField_get_preferredWidth_m7B67921BC7BD2A2FDD53C93FC9AB63B04A00C753,
	TMP_InputField_get_flexibleWidth_m8E903250C848B81530D7A757513C98FD7DB4E3CB,
	TMP_InputField_get_minHeight_m5B9B113BDB4FA8562DE935A377CA0734F7ADE7B1,
	TMP_InputField_get_preferredHeight_mF8468DD1FB5F87870379277710E1D5C3DDCFFC1D,
	TMP_InputField_get_flexibleHeight_m3293370FBA374E6FCDBC1E7BF9EF975C1D00DEC3,
	TMP_InputField_get_layoutPriority_m29F413DB25AC2A615265C1C2820D89AC60816DF2,
	TMP_InputField_SetGlobalPointSize_m3FFB4ADB49E9D601673A53AEA78839B964D32A81,
	TMP_InputField_SetGlobalFontAsset_m4AD28DDE68A928EA340F360105C99A1EBC789201,
	TMP_InputField__cctor_mEFAAE367E78CED0A26A678556C750E8045A5AC37,
	TMP_InputField_UnityEngine_UI_ICanvasElement_get_transform_m54CD226342494A37D5AF311CC975A634588AE1AF,
	OnValidateInput__ctor_m734DB6ABACB01CDC715C54E93A47B817C0E7FB68,
	OnValidateInput_Invoke_m1CDDA220BF2691F42200A098F57AE10FDE383E49,
	OnValidateInput_BeginInvoke_m43FBD60B6478C13E662CE8C9BB98085409286F6F,
	OnValidateInput_EndInvoke_m71D53E4976D82E4B04234B4EC8877D54E3C84954,
	SubmitEvent__ctor_m7D30737EA13979AD78F6D7C46563FD43A32301C8,
	OnChangeEvent__ctor_mA7F876316D0F5198E90ECA7304C6542D63758698,
	SelectionEvent__ctor_m9EF6D8DB48A30C615541A297E3739B078BA2F8AD,
	TextSelectionEvent__ctor_mB76781548533BA217F1FDD39550335889994027E,
	TouchScreenKeyboardEvent__ctor_mA7D12057CDF3115B9B47CFFE817A2D901B90EB37,
	U3CCaretBlinkU3Ed__276__ctor_m04194456FB3C1DBD716CFA59EDDE760D986AAE94,
	U3CCaretBlinkU3Ed__276_System_IDisposable_Dispose_m62F3C667730FA038C1323EAB48AEF59FEFFCD1A4,
	U3CCaretBlinkU3Ed__276_MoveNext_mDD251CD28C8D1BDCAA212FB35443F07ECABB6E87,
	U3CCaretBlinkU3Ed__276_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mDD9FEFE4B5114891E051AA778A47A32E4530798E,
	U3CCaretBlinkU3Ed__276_System_Collections_IEnumerator_Reset_mD05FB2B8858676366A0ED98063C5AEECC60AEB28,
	U3CCaretBlinkU3Ed__276_System_Collections_IEnumerator_get_Current_m06538DD0CE0EA13A3E001E9E7B390F96B2E9B724,
	U3CMouseDragOutsideRectU3Ed__294__ctor_mC97D27357520CBB200DD3254DEF53DF620ACB6B6,
	U3CMouseDragOutsideRectU3Ed__294_System_IDisposable_Dispose_mDDECF9B91F7E66A9166A3FC4863656BC94C4A28D,
	U3CMouseDragOutsideRectU3Ed__294_MoveNext_mE91DEA64A594ABE2D110805114C864C501529C0E,
	U3CMouseDragOutsideRectU3Ed__294_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m36806333FD5DF40BAC0946CEBC2DE197E7E3090C,
	U3CMouseDragOutsideRectU3Ed__294_System_Collections_IEnumerator_Reset_mA500CB2991458842FB24C83B54624A9B91446E8C,
	U3CMouseDragOutsideRectU3Ed__294_System_Collections_IEnumerator_get_Current_mCD6E5AE2B1016D22C30F38001C6DFB243EAF10AE,
	SetPropertyUtility_SetColor_mBDA27D2F874BAC08FEDDAED677ECF596B3743547,
	NULL,
	NULL,
	NULL,
	NULL,
	TMP_InputValidator__ctor_mD15E0AFA50E8CA10B2849A66A5B96D50B7EA66F3,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	TMP_MaterialManager__cctor_m9A67C84C9A17C88DA0C043F8B6C6604A0C609BC6,
	TMP_MaterialManager_OnPreRender_m568227EA396CF03FD388C00EBDD713D05A3558F3,
	TMP_MaterialManager_GetStencilMaterial_mDC5A5B34DC6E0AE05CE94A3D92B823A6EAF96F96,
	TMP_MaterialManager_ReleaseStencilMaterial_mECF794E6299D84E46FBC0BC6F23155A8751FCD41,
	TMP_MaterialManager_GetBaseMaterial_m462BBD276522D865CA64749658495FE727A878A0,
	TMP_MaterialManager_SetStencil_m885536A1C4F790102E5AF2D3518F39601062F870,
	TMP_MaterialManager_AddMaskingMaterial_m23B894F9E1FA5F9FDBA6E99A2B3FD574BEC2F5C5,
	TMP_MaterialManager_RemoveStencilMaterial_m3B79911A308BB53F51AD8BCAB8D240ED60E6E71A,
	TMP_MaterialManager_ReleaseBaseMaterial_mA38A80719DC32DFC0C37E5DEE17FF622C873CDA2,
	TMP_MaterialManager_ClearMaterials_mE4F43E6AE2BC8FA8F1B369F8B38B5DB3F78FE37E,
	TMP_MaterialManager_GetStencilID_mE85832BD9E38E435296801AD6CDA9A2EBD68C035,
	TMP_MaterialManager_GetMaterialForRendering_mDC4BDF626B827DE3A6B13933DA0F376D7E77ABD4,
	TMP_MaterialManager_FindRootSortOverrideCanvas_m2E4C1A734D8568458C70A1A40F279412E5F76844,
	TMP_MaterialManager_GetFallbackMaterial_m95FD4E0D6101A850C43D5917D46D9ED7DAD9CBB3,
	TMP_MaterialManager_GetFallbackMaterial_m2DE6B5385E5AF9AF9CBBBE1D7D6F1497ED6498D0,
	TMP_MaterialManager_AddFallbackMaterialReference_m945320584E67E1D285BF1D99C63FD19799E94993,
	TMP_MaterialManager_RemoveFallbackMaterialReference_m57C507E25D288372CADC9C2C10AA78876B033B92,
	TMP_MaterialManager_CleanupFallbackMaterials_m94C5B0D70AE592181468BEB3046F51FEDE61698B,
	TMP_MaterialManager_ReleaseFallbackMaterial_m270B368718E0FBEC8300F0C518603B6E917E6668,
	TMP_MaterialManager_CopyMaterialPresetProperties_m091CA38EAB2582F1792E94216E05706A13DD3965,
	FallbackMaterial__ctor_m5AA6484722CD55AD1E40B459CAB79DD8990A713F,
	MaskingMaterial__ctor_mA1BA8800085879CFA3DE2A0DED61A4AA92C62B2C,
	U3CU3Ec__DisplayClass11_0__ctor_m28B98E0B4AE129848CDFF36F6F34E9D2D9141268,
	U3CU3Ec__DisplayClass11_0_U3CAddMaskingMaterialU3Eb__0_m4F0BC91E6CEE544BBB91FC63751A03DB13F1037D,
	U3CU3Ec__DisplayClass12_0__ctor_mAB99B1A35D85999D6D5626476FDFBC24C2267F92,
	U3CU3Ec__DisplayClass12_0_U3CRemoveStencilMaterialU3Eb__0_m287C61417605FA86EFA7C1727977B50AD14C85E6,
	U3CU3Ec__DisplayClass13_0__ctor_m75854EC300C2C88140C6100C42620E466E0A149A,
	U3CU3Ec__DisplayClass13_0_U3CReleaseBaseMaterialU3Eb__0_m890054ECE6EF0D16429C8BE76649990EAC0CBD58,
	U3CU3Ec__DisplayClass9_0__ctor_m848336CD827EA7C72F02A2F5197CC154956B3D84,
	U3CU3Ec__DisplayClass9_0_U3CGetBaseMaterialU3Eb__0_m98229F401F0560DF925A73A963C8371504C1A4B0,
	TMP_MeshInfo__ctor_m453B9FC30A2CB8AB2A5C868AC4229B7903F033E6,
	TMP_MeshInfo__ctor_m95D69F6D719C924C0AF92DCBB1F642D39469CBB5,
	TMP_MeshInfo_ResizeMeshInfo_m13DF794141EBDD4446391BAF6FD469EEFE3DD6D1,
	TMP_MeshInfo_ResizeMeshInfo_m247290DC2AD29A232C6473904748ADD11779D543,
	TMP_MeshInfo_Clear_m002C7A793C6BBFF39C878B909F0162E6EB5C12F8,
	TMP_MeshInfo_Clear_m28C815908490A64459F38D5EC110C6823B813888,
	TMP_MeshInfo_ClearUnusedVertices_mF5DC41BB72A19486A4079208D13472DD0BDE2CD9,
	TMP_MeshInfo_ClearUnusedVertices_m1BDC394210705FC5219A44B3D110BF50F3027B55,
	TMP_MeshInfo_ClearUnusedVertices_mB4475A7E8ED25FBCD1D1E91924D9DF3D60AE7A1A,
	TMP_MeshInfo_SortGeometry_m28C6E9A947C7352F16910BAE2F744087720DBECA,
	TMP_MeshInfo_SortGeometry_m74ED0FE2065414A659EE9A9C809E1B0B4A8A7734,
	TMP_MeshInfo_SwapVertexData_mBB35F36F8E7E6CF1429B26417140570EE94FE718,
	TMP_MeshInfo__cctor_mEAB7D06415A81CD66D7478DD7C2818D3589155D2,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	TMP_ResourceManager__cctor_m709C83B3CFE2015C6FFA1006351A778F20CC1197,
	TMP_ResourceManager_GetTextSettings_mD481945B6E5473F74D39745DC9B593E3B8E4DCDC,
	TMP_ResourceManager_AddFontAsset_m9C159C7A2E95B8EAF2FA8D2FDF4B7B136C099801,
	TMP_ResourceManager_TryGetFontAsset_m96F72D836B1EC6C95FA3B715012F8870F1564268,
	TMP_ResourceManager_RebuildFontAssetCache_mD7B939B5E993C10BD5746CA6C654A1CED645D925,
	TMP_ResourceManager__ctor_m41A56B8623D6458A03C6A8C3D7470C2F7BB7A1AE,
	TMP_ScrollbarEventHandler_OnPointerClick_m34AF6B0146F0BC5B3C09C32EED107B4463E7F8DE,
	TMP_ScrollbarEventHandler_OnSelect_mDF45AA8D470D08691E9F4D615B7DE3BE9AC7135D,
	TMP_ScrollbarEventHandler_OnDeselect_mF833BEBAB98A0B437BFC9BCB5EE3747434A082F4,
	TMP_ScrollbarEventHandler__ctor_m58CED24AFA6F683381D1730590691DDDD5239555,
	TMP_SelectionCaret_Cull_m2DC72A9C1EA02ECCB716CD88EFE102299E9006F1,
	TMP_SelectionCaret_UpdateGeometry_mF6C6F61B4CD8E34D7D9777EF897639DBFB18755E,
	TMP_SelectionCaret__ctor_m68388B98DDFDBA26F60C2AF944794D3A43BE8070,
	TMP_Settings_get_version_mD4931533CD1B724A2147506EBB7533609220CFE8,
	TMP_Settings_get_enableWordWrapping_m6768537460F6CD13F5A581282353B2B98EE22A1D,
	TMP_Settings_get_enableKerning_mC1031F78F03B64FE3082EFFF3736C0D428A29E22,
	TMP_Settings_get_enableExtraPadding_mDB4FE26B3547EA2BF5FFC8CE354680B4EC02CB42,
	TMP_Settings_get_enableTintAllSprites_mD2803D776AE9A89D55E521D82C2DD0AB8135A120,
	TMP_Settings_get_enableParseEscapeCharacters_mE6CB6DE4E034CA3CA08D0035A16923CC7EB847D2,
	TMP_Settings_get_enableRaycastTarget_mC7F0756A3563CCF4788AEA19355C221963BF2260,
	TMP_Settings_get_getFontFeaturesAtRuntime_m75190CE90D69720EBDE06438C4B72072D1FD7BBE,
	TMP_Settings_get_missingGlyphCharacter_mA9AB8619A2A7275DAF4788B0868B4933F9A451A2,
	TMP_Settings_set_missingGlyphCharacter_m11E37FBC7A2FE60F7BBE86E545E51AE74A512779,
	TMP_Settings_get_warningsDisabled_m2590555E7D849D05AF4B63DEA82407812DB37B22,
	TMP_Settings_get_defaultFontAsset_m08D5F2C60E2E313EFAE26C16934F08A499DDFC64,
	TMP_Settings_get_defaultFontAssetPath_m839245F25AC624824660B9A7C2A8B0D7F5FFCC99,
	TMP_Settings_get_defaultFontSize_m0DD0FFB0811B5EA0DAF7C44BB1F3BA2B8F0C6F1C,
	TMP_Settings_get_defaultTextAutoSizingMinRatio_m7DAE2F65CA41AF99FEF2AF1B0AF9F2AA0F3992B7,
	TMP_Settings_get_defaultTextAutoSizingMaxRatio_m58977C845522D0083F422883C8158BBED78086AE,
	TMP_Settings_get_defaultTextMeshProTextContainerSize_m466E747B45873AD1DF7E06157B97E731B5AEE5DB,
	TMP_Settings_get_defaultTextMeshProUITextContainerSize_m0D4A8F331AA212AADCB5BA044E5C79B811ED70DF,
	TMP_Settings_get_autoSizeTextContainer_m975EB0FF2086BA79F214C099AF1839D4FA2F0DF3,
	TMP_Settings_get_isTextObjectScaleStatic_m2F89F247DDA607F93B26EB5B9A698C5C2A975D18,
	TMP_Settings_set_isTextObjectScaleStatic_mF18745726FE671226582BD5BC19C6DBE9199DD70,
	TMP_Settings_get_fallbackFontAssets_mD671B9D809736E7DC84543568C25BEF9C0B7269D,
	TMP_Settings_get_matchMaterialPreset_m3C4B2C06C35CF61FCDB127236F522B4454734627,
	TMP_Settings_get_defaultSpriteAsset_m1A6D796CB68107284294DAB40442F2CFFA26A672,
	TMP_Settings_get_defaultSpriteAssetPath_m0697504D0CD5728F61DE0E1DA9379B8E8CF62E11,
	TMP_Settings_get_enableEmojiSupport_mC5DAE356F0396330F8B266F83F44E36BCB3B6AC7,
	TMP_Settings_set_enableEmojiSupport_m6BE82A8651B2CAC18F2E29B74431B6221C013126,
	TMP_Settings_get_missingCharacterSpriteUnicode_mD82A3253E2CD0C9D467FBD152E4F0FE5E2CBFE2D,
	TMP_Settings_set_missingCharacterSpriteUnicode_mC77F2F7E2F328440D7BAE8D410EA299434336B39,
	TMP_Settings_get_defaultColorGradientPresetsPath_mBB00B879E09F5B4ABC9D92E1CDA90D1C11236798,
	TMP_Settings_get_defaultStyleSheet_m348327B30DA1E60CAFBD929D9724E4FECAD23AE4,
	TMP_Settings_get_styleSheetsResourcePath_mD9B018B6AA0A84B293970BB92AB5247063CA8262,
	TMP_Settings_get_leadingCharacters_m68937B28B95ED59288E22A2F26275AF6F5CF3C7D,
	TMP_Settings_get_followingCharacters_m2E92204242696D31D43203E388BA5AB178907237,
	TMP_Settings_get_linebreakingRules_m9128A20C31E5CBB0D06E0A1537E40617640FCBB2,
	TMP_Settings_get_useModernHangulLineBreakingRules_m20EF8E9FBDF86C21A8E30F3B5B2DF997ABB3A060,
	TMP_Settings_set_useModernHangulLineBreakingRules_m2BBA6F13171F67AE513A9684BA253C90B626386D,
	TMP_Settings_get_instance_mFFEE513A89138F5FACD8CE35BF241C2D1F4A9BF4,
	TMP_Settings_LoadDefaultSettings_mAD730F80FED7CBB4D15D94ED7A3F0703234C01CB,
	TMP_Settings_GetSettings_mD7694E5469539C92793D8C1C6C940C875EB0F74A,
	TMP_Settings_GetFontAsset_m2B9CCF67F14FF0294D8F09A00FA04240640B59E5,
	TMP_Settings_GetSpriteAsset_m1E0A427691CBE9DE384E5FEA5FEFE2994178B7E2,
	TMP_Settings_GetStyleSheet_m77182866141F5C1699DBC2E25F69FAB3CC347BD0,
	TMP_Settings_LoadLinebreakingRules_m77145E921D2176F814DCB968247B938A1379C6B7,
	TMP_Settings_GetCharacters_mE221906A29576DD47B3AE3CA25905D65BFA13E1B,
	TMP_Settings__ctor_m8D99E9A01FB47EDF64A744B6B1AD5B424CB9F38F,
	LineBreakingTable__ctor_m20DC4ED032712E7234F19604082B5B41DEF713EB,
	ShaderUtilities_get_ShaderRef_MobileSDF_mD39AD31910FCE56B1B682C626D4D3F69B811D3F4,
	ShaderUtilities_get_ShaderRef_MobileBitmap_m7D670C6D6FBB3D84F1CA22E3F6AD4BC4C6AD4929,
	ShaderUtilities__cctor_m9360E8869783EBD2DD6CA7308C37A03D7434BCCA,
	ShaderUtilities_GetShaderPropertyIDs_m3EE2D3D2A31C57AE418FCC0782D0CC9D2FBD0A65,
	ShaderUtilities_UpdateShaderRatios_m212CC45DE044E3004EAE6360885C9C02DDC3DEE0,
	ShaderUtilities_GetFontExtent_mC52BFBDA68568A8EF78C1AC8D98041C206CC8B4A,
	ShaderUtilities_IsMaskingEnabled_mC2C8788713E32E1ECB8D2ED17F5FE3335F4FA723,
	ShaderUtilities_GetPadding_mACB25967DE353794970CEC89362214C3F65341FA,
	ShaderUtilities_GetPadding_m163157F37E9267CC1A48349E589B8ECF91B73110,
	TMP_Sprite__ctor_mEAF426A39C3129E4D1997ED2D1591F3ADE1A25A2,
	TMP_SpriteAnimator_Awake_m6A8FFA0C1EF9E744486051B028DE20B122FADF66,
	TMP_SpriteAnimator_OnEnable_mBAA3D31A82A9CDEFC373D10DF860384E31D38BA9,
	TMP_SpriteAnimator_OnDisable_mF9A39A9D836AF81C70ED6D301275B814AF3ABBAD,
	TMP_SpriteAnimator_StopAllAnimations_m0531CA658CF1A4E5A18BC73234FE5CC8318F64F1,
	TMP_SpriteAnimator_DoSpriteAnimation_m02F535CA423940D067CABC1F1FE45745409510FC,
	TMP_SpriteAnimator_DoSpriteAnimationInternal_mCF00A0F5F136AAF118AE0178104FE885E7DE8EF0,
	TMP_SpriteAnimator__ctor_mCFCE75C7C04926B5DE46F9FF2C5C9A3904F7FE78,
	U3CDoSpriteAnimationInternalU3Ed__7__ctor_m8BBDA4F604B39E235BB82F6E3F20B0FD693688A8,
	U3CDoSpriteAnimationInternalU3Ed__7_System_IDisposable_Dispose_m20F949D3F288FCD413EFBF1AD9B7E508334C5DEE,
	U3CDoSpriteAnimationInternalU3Ed__7_MoveNext_m8FFB7B97D3E8C8A2F5C5239E74A6B93111180A92,
	U3CDoSpriteAnimationInternalU3Ed__7_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m226B3A70CA139557EB0F887B08A867394A12AE05,
	U3CDoSpriteAnimationInternalU3Ed__7_System_Collections_IEnumerator_Reset_m0D4ED9831F002A26C52E1A969C7AFB0F7A6125B3,
	U3CDoSpriteAnimationInternalU3Ed__7_System_Collections_IEnumerator_get_Current_m6C9904888FA484F24441F723715CD60757A8BD21,
	TMP_SpriteAsset_get_version_m908EBE63C74A10EF17C07C046D2E76FA54C1ADB8,
	TMP_SpriteAsset_set_version_m55AC1FB92B62B73170D86867DDD97FFB5B184751,
	TMP_SpriteAsset_get_faceInfo_m1530AA39D6792A0EEE0EAD23159893F418A7E3EB,
	TMP_SpriteAsset_set_faceInfo_mDF753986EF1CB074813927B23968C70AE69DA1DC,
	TMP_SpriteAsset_get_spriteCharacterTable_m2F591ADE7DC8DE042B8A32AF84AC169C19CB9D2A,
	TMP_SpriteAsset_set_spriteCharacterTable_m129E6E18F2DED131EBEBE4C792DFC71DE35968FD,
	TMP_SpriteAsset_get_spriteCharacterLookupTable_mA1128B86D4510139DB3712D8886F64C3B2A8D30F,
	TMP_SpriteAsset_set_spriteCharacterLookupTable_mA5C2661AF36B12516A888D463B69F2DAA6AFBD6D,
	TMP_SpriteAsset_get_spriteGlyphTable_mF26169916988D252767D8801D1E8A2C2D10744A9,
	TMP_SpriteAsset_set_spriteGlyphTable_m7207A256A0E4A37F083B1A870C88967C9249E46D,
	TMP_SpriteAsset_Awake_mAD9AA42B857F3D524A18BCC7512F09D02810EA8B,
	TMP_SpriteAsset_GetDefaultSpriteMaterial_m71B824EDA9676B6A30EDE264BEE9E54C6D621AAC,
	TMP_SpriteAsset_UpdateLookupTables_mEC56B333C873E25ED75D6DD85E1628ED8C631545,
	TMP_SpriteAsset_GetSpriteIndexFromHashcode_mE1E4D499A7FAD58AB955E1CA8344D640D82219C2,
	TMP_SpriteAsset_GetSpriteIndexFromUnicode_m20CA8E503DE1FD6FE80E9418EF9A426DFEDD9014,
	TMP_SpriteAsset_GetSpriteIndexFromName_m0CFC33E1F9D56B23BB7DD2DDD1C0886D41A9BE6B,
	TMP_SpriteAsset_SearchForSpriteByUnicode_m411F1164B0288CA8D94432974177BADDC2A54EAB,
	TMP_SpriteAsset_SearchForSpriteByUnicodeInternal_m47A6A4AA14924152B0E3E6866A3146ED57E9538A,
	TMP_SpriteAsset_SearchForSpriteByUnicodeInternal_m79729A0D68B2C4F99DE643D5A5BAFAEDF23281C2,
	TMP_SpriteAsset_SearchForSpriteByHashCode_m95F9A3A7C67245EF2C5E16F51F7CD627D005427D,
	TMP_SpriteAsset_SearchForSpriteByHashCodeInternal_m748846D7F42C95D37EE5DB594EC49AF048AB245B,
	TMP_SpriteAsset_SearchForSpriteByHashCodeInternal_m232218D0EFC8E51BFA7FB14517E489C5941DDE32,
	TMP_SpriteAsset_SortGlyphTable_m0B638BC195978816F72A5D32E1FD2608EB388B68,
	TMP_SpriteAsset_SortCharacterTable_mAAE212E44DECC76673001EB17D3BBCBCF1A3CCA1,
	TMP_SpriteAsset_SortGlyphAndCharacterTables_mEFA1D54654552CA29AD602DB21D6320A396C9E4B,
	TMP_SpriteAsset_UpgradeSpriteAsset_mE4C0306402DA32DC3C4BCC8FD11F6C8D35FF2E54,
	TMP_SpriteAsset__ctor_mE12AAD30F24A6007B20DAE40E64FFDB78BEE8E12,
	U3CU3Ec__cctor_mD89C817B002CF26D3417F56AFE6EBF9DE3AF3197,
	U3CU3Ec__ctor_m8DEE5249803AAEB1971F104609B40E1327C4B13E,
	U3CU3Ec_U3CSortGlyphTableU3Eb__40_0_m51F7FC32BA4529C5284CC0DC3572FAA3257AD8A3,
	U3CU3Ec_U3CSortCharacterTableU3Eb__41_0_mECA45EB0A1A8B501039633E87B1A71903857445E,
	TMP_SpriteCharacter_get_name_m207A7AF57DA74FCC9409AEA2E8581FF4009512A4,
	TMP_SpriteCharacter_set_name_m5893C4B6DF938F2E6BB37C578C3B7AB8501F079A,
	TMP_SpriteCharacter_get_hashCode_mD0A6D291E2DEC9D29C0E6715C4497765E0AB384F,
	TMP_SpriteCharacter__ctor_mC81C5F64670E2A27460B808E9685102BD9CFDACD,
	TMP_SpriteCharacter__ctor_m8F33DB3BDCC21B35CAD05BAC8D9DCCADF861DDB2,
	TMP_SpriteCharacter__ctor_mE00D2BBE2D3FF0E47A84EF5D0B8C57AB1153FEB8,
	TMP_SpriteCharacter__ctor_m12DB9C781228C5D5DDF21E578BE48BDBDE0CD4C6,
	TMP_SpriteGlyph__ctor_mE15D3E35E9F68B201CD34569F3A19B22D980D5DE,
	TMP_SpriteGlyph__ctor_mDFAB2320924E4687FED7E3BA2E1F551ED05B9D36,
	TMP_SpriteGlyph__ctor_m8FFB4374AE9F72ABC4E3B7A50FEF593CA2736348,
	TMP_Style_get_NormalStyle_mB8B470F18522380C52B6E76D4B287F3D21009CC0,
	TMP_Style_get_name_mBA0F1FE80A39D071DC286A2BE674203BE59926E8,
	TMP_Style_set_name_m2FCB28B0836C6BE1D8F460538D450295EF6CB80F,
	TMP_Style_get_hashCode_m19EC41583BBC799AC118324ED1A0405E26990E85,
	TMP_Style_set_hashCode_m2EC34153FFE0E3D2CD13138A29A87D13F21D4147,
	TMP_Style_get_styleOpeningDefinition_m24394DAB1ADA5D1F7D1A386CED1C51D46BD50B8B,
	TMP_Style_get_styleClosingDefinition_mA23115F2648B0A6B4AABE9E4043A4A272509209A,
	TMP_Style_get_styleOpeningTagArray_mB7640D4E0C5A8EF7E1C46AFEFC98909A642ACCC7,
	TMP_Style_get_styleClosingTagArray_m286697AF575989E08FA185934FCCA3CD54565A8B,
	TMP_Style__ctor_mBC114846B015F0C6F9DEF28EF765BED9947538F1,
	TMP_Style_RefreshStyle_m90C4C9D26FDE915FE8C6F307E0A4AE2F09BB9C25,
	TMP_StyleSheet_get_styles_mD3FB628CE8162DD6F8532FC5B8AF64409E0A9DB7,
	TMP_StyleSheet_Reset_mCA48D63055490174046D802C414CD6A5E7291E63,
	TMP_StyleSheet_GetStyle_m1A066C8EB0E74AE5D84DEC570BFE301D45FAE078,
	TMP_StyleSheet_GetStyle_m14703829269D37F3E69B1DCDA0C508A1DFC4F9A1,
	TMP_StyleSheet_RefreshStyles_m5F93989FB986DE16268D2F70D2F9855612547458,
	TMP_StyleSheet_LoadStyleDictionaryInternal_m54F7544F778ACD234CE8DC6FEEB3F33E6FD28B69,
	TMP_StyleSheet__ctor_mD3DFB99F53DB503018B1613AB6EE21E75512754C,
	TMP_SubMesh_get_fontAsset_mE8BD0B068366708271FE9EEA521C6A66B0D2D70A,
	TMP_SubMesh_set_fontAsset_m72B98C846C0BED1F95B642359D9B682E6B99FD5A,
	TMP_SubMesh_get_spriteAsset_mA42C14F49819531B0C7F9A516FDF98CB64B7E8F8,
	TMP_SubMesh_set_spriteAsset_m8090A6E45EB4780476223BF53115ECF3B5297F9B,
	TMP_SubMesh_get_material_mC2E739573C72E85402DEEDC8BA589146E7738A2D,
	TMP_SubMesh_set_material_mAD78A696DADACDF54AAB0347F520B7F848E0E517,
	TMP_SubMesh_get_sharedMaterial_mDBA65AAA3DF5B047D8A05CF00CBDCC0B22E18957,
	TMP_SubMesh_set_sharedMaterial_m39D3800DFDB361235F85066E08FEE26CAD12461B,
	TMP_SubMesh_get_fallbackMaterial_m56ADAE065A5B9822474BA92763B325D752C6410B,
	TMP_SubMesh_set_fallbackMaterial_m834BFAF4851FD7EC116808334791B57D3EA4BF13,
	TMP_SubMesh_get_fallbackSourceMaterial_mC434387C192AA72EA046F1B87CFF73547C6C1020,
	TMP_SubMesh_set_fallbackSourceMaterial_m42EC6CD630C1E531012C8FE7C042D17E3D4B67BD,
	TMP_SubMesh_get_isDefaultMaterial_m9674DD519EB470FEA52B2BA0D88C03342B93037B,
	TMP_SubMesh_set_isDefaultMaterial_m519BA7D8650EF98CB6113FC8AAA48BA76EB1C584,
	TMP_SubMesh_get_padding_mF12E331397602A9A39ECB674B02412668752F766,
	TMP_SubMesh_set_padding_m3004519034FED4E8DAB9A37118B7F624E55E5D85,
	TMP_SubMesh_get_renderer_m57EDD2B2B7742D389E019F7D81BFCD7BDA468013,
	TMP_SubMesh_get_meshFilter_m84185B727B379F28F2955070CBF99AA14339F34E,
	TMP_SubMesh_get_mesh_m9AF8E94AA6D6A9B47B76EE0B88A75BCECE8F43EB,
	TMP_SubMesh_set_mesh_mCE8299D19097FA2472DCEFA3AA07F5AE7D3600DA,
	TMP_SubMesh_get_textComponent_m0432A85ED37E13DB37CE87B0A09C7C9B5C1369D6,
	TMP_SubMesh_AddSubTextObject_m5365D77A55D42718310ED170B0BB1C2DB04DCBBE,
	TMP_SubMesh_OnEnable_mB044C518B33D5CB3C040D552994581FA754DE233,
	TMP_SubMesh_OnDisable_m2A63ACC5E996C6AC1D2A3358972B6592F0B6856C,
	TMP_SubMesh_OnDestroy_m50A083A81A84781BF0700B1A63B8AAB83C0EBFAD,
	TMP_SubMesh_DestroySelf_mB9BF2C94B673B284DB58D13EAD1E6798129B7B55,
	TMP_SubMesh_GetMaterial_m7FA3D54A057606FA90DC3841AAD76C3877BBDA54,
	TMP_SubMesh_CreateMaterialInstance_mCBD7450E65428732A15ADD20F0A5BE7EA1DBF2BA,
	TMP_SubMesh_GetSharedMaterial_m7C37BF890F16727019DF5A7EA36FABE4E5D21F42,
	TMP_SubMesh_SetSharedMaterial_m894423F785E34D24902F385582889CF9170CEA4F,
	TMP_SubMesh_GetPaddingForMaterial_mE7297313C36D02A7879790C4EEA21551B52B9544,
	TMP_SubMesh_UpdateMeshPadding_mC15404FE24CC51BCA2D8BC8B7A15934FF71ACAAF,
	TMP_SubMesh_SetVerticesDirty_m55CA9BE0F62ED78693A82CD3A583FA24F1C734B1,
	TMP_SubMesh_SetMaterialDirty_mF4015AA542DC6AF1A7E554CF66A42AB0939D826C,
	TMP_SubMesh_UpdateMaterial_mF2AA7298784A74354917AE11C33C06DF5EE48FD3,
	TMP_SubMesh__ctor_m94A6C004CCE46FD9B6DDFBBD8436B386594FABEF,
	TMP_SubMeshUI_get_fontAsset_mFA21AA0E69C872A2E9AD4F2F7A4E2E61B343275F,
	TMP_SubMeshUI_set_fontAsset_m76AFB364ECE0D6CBD609CA2FC1BD28BBC09437A6,
	TMP_SubMeshUI_get_spriteAsset_m657B36AC1C3BFA60B17013CB401750F80719F7E4,
	TMP_SubMeshUI_set_spriteAsset_m535AEEBE4A7548A93E6A252DF62C5BCC3578F05C,
	TMP_SubMeshUI_get_mainTexture_m812ABD578CE01020099166A3F9F63E31E635E4F1,
	TMP_SubMeshUI_get_material_mCEDB25BF8F4D1ADBDDE5E0D6A9D6BE34352B59C0,
	TMP_SubMeshUI_set_material_m42EDB47D729254FE9BA1A521AD957ED2D9CED532,
	TMP_SubMeshUI_get_sharedMaterial_m9F6E8D48BE941352C6395CE6B25D1A026F9B1A50,
	TMP_SubMeshUI_set_sharedMaterial_m76325941FAD77DA10D3BD3B85506D0473CD6DB2B,
	TMP_SubMeshUI_get_fallbackMaterial_mAF8B16164650A91CA244445F5717BCEA12B75CAE,
	TMP_SubMeshUI_set_fallbackMaterial_mDABCF9FA80529D8A6452EBD3C9B52E1D4A8F6A08,
	TMP_SubMeshUI_get_fallbackSourceMaterial_m0CCD5224BD22B4AF5B8D7994040F7925FA047787,
	TMP_SubMeshUI_set_fallbackSourceMaterial_m6176ADCD1C46E99F4FC95189D58B406397E54C0E,
	TMP_SubMeshUI_get_materialForRendering_m49CDCE464B0837AF4EAC89AF71B7CB8602BE1A27,
	TMP_SubMeshUI_get_isDefaultMaterial_mF713B637150AA5A39FB25D9C296A0D2011A7F1E5,
	TMP_SubMeshUI_set_isDefaultMaterial_m1CA334C661C393A92BB29993C559F43FE899E525,
	TMP_SubMeshUI_get_padding_mFE0F475014CBD79033493C185323B095356C4D98,
	TMP_SubMeshUI_set_padding_m8EF3F2C730BADF9C71D789E2B964A0FF0FBC44CD,
	TMP_SubMeshUI_get_mesh_m18BAE0DB357DC5D7993D07BD826429AF727548E2,
	TMP_SubMeshUI_set_mesh_m253BA01B0CF8F664D4C8910C746C56C863A76191,
	TMP_SubMeshUI_get_textComponent_m899050C714DCF7C38409E40ACED46128426E5981,
	TMP_SubMeshUI_AddSubTextObject_mDABF53418F7955156FFC98AAB400EF9BB3EC99F4,
	TMP_SubMeshUI_OnEnable_m5FC1C2F3A131CDD8AEBE462F6E02F98C8EFD91A2,
	TMP_SubMeshUI_OnDisable_m7E93F77D46B86974F82E651F1C5ABEC4965E7A19,
	TMP_SubMeshUI_OnDestroy_m9B06AF411C751749285D664C97E4534F8DB46421,
	TMP_SubMeshUI_OnTransformParentChanged_m1C0D38B644942ABCCE807FD0EDA40069FCD4F758,
	TMP_SubMeshUI_GetModifiedMaterial_mE55896B318E1B14EA2E05E8B4C9B7395F889637A,
	TMP_SubMeshUI_GetPaddingForMaterial_m59C406EAAF3622C5C66AC02B57EE54017E6F80C9,
	TMP_SubMeshUI_GetPaddingForMaterial_m5600CCCC50A30C965D5522C7CDC62559B1AACD3E,
	TMP_SubMeshUI_UpdateMeshPadding_mFE485B3241997E25482483616D1B5482EA8BBC81,
	TMP_SubMeshUI_SetAllDirty_m17BC0FAF84604A8419F055074E538D3B92D8DFEC,
	TMP_SubMeshUI_SetVerticesDirty_m6BC1FB6642A719D0B542920D87C47B91BCAE8F3D,
	TMP_SubMeshUI_SetLayoutDirty_mF9E12FA430FDF4CAB2142C256069206F66F4BE39,
	TMP_SubMeshUI_SetMaterialDirty_m427E2E5CA2522811C510ADFB88183F5C7168C41E,
	TMP_SubMeshUI_SetPivotDirty_m7CB8262E46A59A4309FB63BBDC85305DB66AC08C,
	TMP_SubMeshUI_GetRootCanvasTransform_m044D69EEDD595930E39EE9B58180440A1C318699,
	TMP_SubMeshUI_Cull_mC2938541DF75ECBE0A20743633BB59E0E2FB2C8D,
	TMP_SubMeshUI_UpdateGeometry_m8A12469615865F793E84FD08A01CA20C82344504,
	TMP_SubMeshUI_Rebuild_m157FB1223ADFBB21D2C66599D9130FF09687009A,
	TMP_SubMeshUI_RefreshMaterial_mD91D017F05BFC8667A26179D17565E3411A0FE75,
	TMP_SubMeshUI_UpdateMaterial_m4147C455FDAE0B050969761CEA78CC665D2B162B,
	TMP_SubMeshUI_RecalculateClipping_mAF6020BB8D612D61DD64C6B3A66E21B1ED27E629,
	TMP_SubMeshUI_GetMaterial_mFE6F9315B7C5FCD8DC6F5B885D0DE5F6E860FD22,
	TMP_SubMeshUI_GetMaterial_m42B838E7CFD90166E7AB6288140E0DDC42C5BFBD,
	TMP_SubMeshUI_CreateMaterialInstance_mC6A3BF4276D9FDB1120EDE06B688F57BD50012B2,
	TMP_SubMeshUI_GetSharedMaterial_m3D24E4226259E175D6BCB0D846D5D6D6BC2740D0,
	TMP_SubMeshUI_SetSharedMaterial_m3E8AB169F4C47E062E3996E25F2F9D015FDAAA0C,
	TMP_SubMeshUI__ctor_m9AA49928094650F82BE200A086839EA4DABF3D25,
	NULL,
	NULL,
	NULL,
	TMP_Text_get_text_mF8371DA9FE7C67218422F6A5B5F4BAB1219EB22F,
	TMP_Text_set_text_m7802824EFC54A60A4FEF444FD34301663CF974EA,
	TMP_Text_get_textPreprocessor_m342C8D483950A64497716F34BCCA853A2D5D430C,
	TMP_Text_set_textPreprocessor_mF26E0EFC2718F08112B9C4065EFB6C7D4322D56F,
	TMP_Text_get_isRightToLeftText_m91867E4BBD159ACF669FF0103FB15194E5A35910,
	TMP_Text_set_isRightToLeftText_m92473AB03681DE06DCE0845AE43B23F13FEF5D25,
	TMP_Text_get_font_m1F5E907B9181A54212FBD8123242583C1CA4BE2A,
	TMP_Text_set_font_mC55E4A8C1C09595031384B35F2C2FB2FC3479E83,
	TMP_Text_get_fontSharedMaterial_mF1F4B4A3379A9928CF2CD51835381B31C0976C82,
	TMP_Text_set_fontSharedMaterial_m4C3E1FAD0780FF04D2998177B794C773EE3B0DD7,
	TMP_Text_get_fontSharedMaterials_m09C5F786FE99C75C954C548AFDED330C4785C4D3,
	TMP_Text_set_fontSharedMaterials_mE82D24FE08F46E5E59438F51938A6B99D74EE376,
	TMP_Text_get_fontMaterial_m4EBEC9AF78B5B66C983A98F78948E753EE4DDFC6,
	TMP_Text_set_fontMaterial_m091675AB7E417CD77F8C69B3AEE5B78BBCF59922,
	TMP_Text_get_fontMaterials_m354B3F7CF4AB2B7E38C2610D8403D14744286A55,
	TMP_Text_set_fontMaterials_m0DC39367F86944E57BE16634A45225ACA97F461B,
	TMP_Text_get_color_m4A843DBD73462B4EE0F823039AE9F8499102D9B5,
	TMP_Text_set_color_m776196F566F4F8CD25263BB40CA2D3AE5F2D444B,
	TMP_Text_get_alpha_mF6093A9BEAC44060DA2CC7A61097DB99A25E7DAE,
	TMP_Text_set_alpha_mD01D24A2E320F30E26BD42AEE8137F9C4F4EBB57,
	TMP_Text_get_enableVertexGradient_mB5CFDE007B14BB0425CEACA8FE33C8B2B29769A5,
	TMP_Text_set_enableVertexGradient_m21A55C744B7BF817B6AA349FCB8C2AC54E8CCACA,
	TMP_Text_get_colorGradient_m29541E9BEF4511BEEB2B4951E5BF07DA01AC9105,
	TMP_Text_set_colorGradient_m372D6EEDBE955EC7F33895F57E760802937808C8,
	TMP_Text_get_colorGradientPreset_mEA5E8B98E88641BE9437222F33DDCCB1B05566B7,
	TMP_Text_set_colorGradientPreset_m21DD271B3D1ADF6E81ED68922809F158612A7B46,
	TMP_Text_get_spriteAsset_m2D4DEEA11BF5B9DEBA1859A401A15C455529D07A,
	TMP_Text_set_spriteAsset_mAA6F8F2CD83E208C185A30367CF7E308B5A1F750,
	TMP_Text_get_tintAllSprites_mFDB02B03D3513B536D47260FC9B5CCC8BB471C83,
	TMP_Text_set_tintAllSprites_mFFCB8F9B1E8C23016C460BC26024DAEC7CD49D65,
	TMP_Text_get_styleSheet_m72E52DC4A12109C1D0C46F2CF89F4A0D439913DC,
	TMP_Text_set_styleSheet_mBADF3BE1110DBC043A75F42AD0C5FB8C245BC1BF,
	TMP_Text_get_textStyle_m18773DC7DEFAA035C8D86475294AD3C0DDB52603,
	TMP_Text_set_textStyle_mBD9F0E7332606863C32DC78E1BD163E7858D9425,
	TMP_Text_get_overrideColorTags_mACA2CBC4B1D3033B30322B2366E1AA97AFB81E41,
	TMP_Text_set_overrideColorTags_m9F9D83AA86AA7A310EA41F66A029F11100519CED,
	TMP_Text_get_faceColor_mC6A763106D17F58C97965AFD5EE47646C813B4B8,
	TMP_Text_set_faceColor_m5E9FCC324958ABD25823193117B9BA5304043E51,
	TMP_Text_get_outlineColor_mA443B0C207A8B6A5E2546A31F46A3106FB0573EF,
	TMP_Text_set_outlineColor_mBEFF42BF9AB15BC7C1DA78489CB4F32A2270F7F0,
	TMP_Text_get_outlineWidth_mC94A3AD32458544743E07AE0A495A86214823C29,
	TMP_Text_set_outlineWidth_m33ADF665CB2D3DBD9FB3F70DE62979FD63ADD592,
	TMP_Text_get_fontSize_m13A8365A56EA2B726EAD826B4A69C8918A528731,
	TMP_Text_set_fontSize_m1C3A3BA2BC88E5E1D89375FD35A0AA91E75D3AAD,
	TMP_Text_get_fontWeight_m9A7A4ED9ECA3A192B28E24E94D40D5B545D6118E,
	TMP_Text_set_fontWeight_m4F7016B98AAA89004CFBEBBBE1C4E35B94EF0EE2,
	TMP_Text_get_pixelsPerUnit_mBCEF0125AEB4F14A5BA5D179C3523FD382E45796,
	TMP_Text_get_enableAutoSizing_m0A101957A4E1D156437E454DF813ACE3714F0FE7,
	TMP_Text_set_enableAutoSizing_mDD34BC7AA735EEBEB916FF5C9791B1502F65FBCA,
	TMP_Text_get_fontSizeMin_m5F97E2EFFE86CB4BFFFC31E167E1E577134EF05D,
	TMP_Text_set_fontSizeMin_mEAF970BB9CA053DF953AF83E638EA0F1D885358F,
	TMP_Text_get_fontSizeMax_m8FAB0C39D22B722F6AA6CF15E6C0636715D64BD4,
	TMP_Text_set_fontSizeMax_mC84B7090F5CE69BA63556A71FD63ABD67C911750,
	TMP_Text_get_fontStyle_mC34CC5EBEDD43CE93BA911CCC4D33F9697838586,
	TMP_Text_set_fontStyle_m61931944B2E922D50087312D80F8685A2F29EBF8,
	TMP_Text_get_isUsingBold_mA0F9BE071B0F9DB995BC04D1CD409CA5C5AF6CF0,
	TMP_Text_get_horizontalAlignment_mB33E135CD810BE68FA3E29D57D360575DE18C4CA,
	TMP_Text_set_horizontalAlignment_m5621041CDB60BAD5BAB18AE01701ADA2FD2231B2,
	TMP_Text_get_verticalAlignment_m83109ED3E925A505F5E9E9142B07829A56CCB54A,
	TMP_Text_set_verticalAlignment_mA79C8E375EEC0B960D517D2D8ED217564ABBFB82,
	TMP_Text_get_alignment_m52C559D8E496889812623C56CD8EA056FD92D565,
	TMP_Text_set_alignment_mE5216A28797987CC19927ED3CB8DFAC438C6B95A,
	TMP_Text_get_characterSpacing_m48A3B73EFBF47B5227D2BB4816FCFF628254C8FB,
	TMP_Text_set_characterSpacing_mDCD34D244A502CA21CEB817E1F4CAC5BC6CCBA63,
	TMP_Text_get_wordSpacing_mF3DF1445C78E06195904FCF0293E63654C527D33,
	TMP_Text_set_wordSpacing_m319C51E318DBC91F236F3CC65ED24787903F7E1E,
	TMP_Text_get_lineSpacing_m7481D705EAD920B8D143D19A270D44CDABDAA251,
	TMP_Text_set_lineSpacing_m1BA54B315F7472AE0E7B721CA7481016643591A7,
	TMP_Text_get_lineSpacingAdjustment_m3858BA838BBFBA60A0A1DDCB195075C6620CF637,
	TMP_Text_set_lineSpacingAdjustment_mAC9A57D852EBAD8DD53ED2F1DE316C0DA52659FB,
	TMP_Text_get_paragraphSpacing_mCCBC792CAE59958E92EB04B8E636AA2066534713,
	TMP_Text_set_paragraphSpacing_m69921E35B44DE397FE604590913CAFB7DBFBAF30,
	TMP_Text_get_characterWidthAdjustment_mE879BF9A6273376AEE54BE88745ABE7944DBF26A,
	TMP_Text_set_characterWidthAdjustment_m11B7CC28C0A7FFC6434DB671C635691B529071BE,
	TMP_Text_get_enableWordWrapping_mF228EF12091EF9FB53E44B6B0278B610E350E551,
	TMP_Text_set_enableWordWrapping_mFAEE849315B4723F9C86C127B1A59EF50BE1C12F,
	TMP_Text_get_wordWrappingRatios_m3316BC010D7B02829CE0B86868B01419C81ED072,
	TMP_Text_set_wordWrappingRatios_m83A82AE875C4CD836D5802A1C051AF07CA2A0D85,
	TMP_Text_get_overflowMode_m494E5C01E450AF8F4F344856D289D0FDEB8DDCB4,
	TMP_Text_set_overflowMode_mB8911BA07CEE0AC1E4E108B5EB79B230F90E96A1,
	TMP_Text_get_isTextOverflowing_mF29482F663A6195FF48628DF3B6F5ACAEF8538D0,
	TMP_Text_get_firstOverflowCharacterIndex_mB9AEEBC749FBDEA2E73023CBA83FA2BE72D08480,
	TMP_Text_get_linkedTextComponent_m84DA92BFD208833ED4C1EC4C4F537F5D594EF4F0,
	TMP_Text_set_linkedTextComponent_m08B4CBAD470F918E2D2E19CE96B2443F38B76D93,
	TMP_Text_get_isTextTruncated_mCB152B5BD9B3FFB994F6B89E2ED89A3602A750F3,
	TMP_Text_get_enableKerning_mA8CA8FB9322358B72F0F7C49954AE3C0E618DDDD,
	TMP_Text_set_enableKerning_m681685E06B8789F5F2B7043EBEA561AAE48E82BD,
	TMP_Text_get_extraPadding_m84294178A4E3BFD708FC746DB98CB0A64FBC35AA,
	TMP_Text_set_extraPadding_m26595B78EDE43EFBCCBF7D5E23932ADCB983EF32,
	TMP_Text_get_richText_m630DE7C1ABC507556E716428264A793423ACAB27,
	TMP_Text_set_richText_mAB3D04F620E13F02117B34BBA2EF7BD30AAE6F0F,
	TMP_Text_get_parseCtrlCharacters_mB10A3CBD2DEFB7BB15BC6330951DCDAB814D2584,
	TMP_Text_set_parseCtrlCharacters_mE733B4A0271EEFA977C39E7F86DDDF73C52D1976,
	TMP_Text_get_isOverlay_m1A9199A9C2FBB09BEAA0B0B2E3D41CDF8A3B708B,
	TMP_Text_set_isOverlay_m0DA2AC113AE402CA25097641AD38D0822C6D5561,
	TMP_Text_get_isOrthographic_mBC78A70B2233363411D9D918346DFE19DF3CF72B,
	TMP_Text_set_isOrthographic_mF58B9C6B492D4FD1BA0AB339E4B91F0A1F644C18,
	TMP_Text_get_enableCulling_m233860FA65153E4C5C3FE3E78B835D4230FC45B0,
	TMP_Text_set_enableCulling_m3CDE2F50BF96E110427D2C1B3505436D87576102,
	TMP_Text_get_ignoreVisibility_m479580B3550B3652B3E4E889B8E62902633C7477,
	TMP_Text_set_ignoreVisibility_mB06EE9EA50439B339824FDF4B52CAF423AC1209D,
	TMP_Text_get_horizontalMapping_mDD4C7F3FF8D4619EA539A964636EC841FCFE7873,
	TMP_Text_set_horizontalMapping_m26A114EFF3D3143214F753521B4DCB2971C19C84,
	TMP_Text_get_verticalMapping_mCD5A83DF6CAA818E89F483F11B6748538D7E9C35,
	TMP_Text_set_verticalMapping_mBF1DBAC92E4E6BE48F39275FAFF5F8106FABD317,
	TMP_Text_get_mappingUvLineOffset_m296EF64BABC2BA1A47BD7309B10027E51BB37394,
	TMP_Text_set_mappingUvLineOffset_m963D80134C47160C7896A7C86FFF3C4B3CF51E73,
	TMP_Text_get_renderMode_mE67A34CDA63B22321E3C511078F9CC42B19EEC8C,
	TMP_Text_set_renderMode_m091533DEE7FD20A61249DC52C786ED4FFE5A5C2A,
	TMP_Text_get_geometrySortingOrder_m7A757613E064B108D3598B3953AB846E3B63B756,
	TMP_Text_set_geometrySortingOrder_mFE993584D0FDB12A43F0F1907BD1FFAF240E0D95,
	TMP_Text_get_isTextObjectScaleStatic_mBAC6CC2ACE413148E868A14281629B9C72851940,
	TMP_Text_set_isTextObjectScaleStatic_m8436FC38400ABE08F513770AF9C8CC6743DBE092,
	TMP_Text_get_vertexBufferAutoSizeReduction_m304AA345FEF2D0D542E2B1F2CB9AB51464BFDB91,
	TMP_Text_set_vertexBufferAutoSizeReduction_m188984707109669597440E6F250B124D6FB66270,
	TMP_Text_get_firstVisibleCharacter_mD2CEE9A9803C530DA337B22BD994B9CEBE15AE63,
	TMP_Text_set_firstVisibleCharacter_m343804C8FF610EB13CCB14E8D54C889BC356AD53,
	TMP_Text_get_maxVisibleCharacters_mF695995258B5013340B8C026B2A0FA643D5FD302,
	TMP_Text_set_maxVisibleCharacters_mEDD8DCB11D204F3FC10BFAC49BF6E8E09548358A,
	TMP_Text_get_maxVisibleWords_mD9E44CE8FBCB6F7182716E61EB435B61048155B9,
	TMP_Text_set_maxVisibleWords_mE2EDC75AA5E4795233F753643202868E4D3226B9,
	TMP_Text_get_maxVisibleLines_m9E8FB188E50DCF321793C7E75B7F90E2142AC52B,
	TMP_Text_set_maxVisibleLines_m55D236A0DA8C5A10C793663674FA3A44F61DF861,
	TMP_Text_get_useMaxVisibleDescender_m3A85730B4F5723C8B7884B89FB70EE0A6888165B,
	TMP_Text_set_useMaxVisibleDescender_mBFE9133E5EEF987942919D4FE369CB03A0EBC559,
	TMP_Text_get_pageToDisplay_mAA3CCC7BD6CA9430558F3409E05B6E754D82C730,
	TMP_Text_set_pageToDisplay_mBD985B613FCEC04266FDA43E916B19DD505D7469,
	TMP_Text_get_margin_mB8102487C6CFA509555D3A892C899E0A1E86CBCE,
	TMP_Text_set_margin_mE431DCEED182B2979246E04233F943E8D3B82D5D,
	TMP_Text_get_textInfo_mA24C606B8EA51436E4AA3B9D6DCDFA7A8995E10E,
	TMP_Text_get_havePropertiesChanged_m42ECC7D1CA0DF6E59ACF761EB20635E81FCB8EFF,
	TMP_Text_set_havePropertiesChanged_mA38D7BC9E260BF29450738B827F2220A05662B31,
	TMP_Text_get_isUsingLegacyAnimationComponent_mC52DDE08FAB3DA14C5BDDAF7533A8465B30CCE7A,
	TMP_Text_set_isUsingLegacyAnimationComponent_mC3A3CB0EBBE9A4AF0106EDC9EDB7DC1D0AD62170,
	TMP_Text_get_transform_m6BD41E08BFCFCE722DFCE4627626AD60CA99CCA8,
	TMP_Text_get_rectTransform_m22DC10116809BEB2C66047A55337A588ED023EBF,
	TMP_Text_get_autoSizeTextContainer_mF7DEF97EAB3EEE86558E5A173264DA46068F7E13,
	TMP_Text_set_autoSizeTextContainer_m47F5010FC3B3496C58017BC5B21E51FF8BD0D448,
	TMP_Text_get_mesh_m7B90E1F477480ADB825851B54F898CC39B6DF376,
	TMP_Text_get_isVolumetricText_m176FAF1E14C8054B274E7972EA02D84D3EB4E074,
	TMP_Text_set_isVolumetricText_mE827C3B8F33DB163A48F2A314A66D02274372B9B,
	TMP_Text_get_bounds_mAEE407DE6CA2E1D1180868C03A3F0A3B6E455189,
	TMP_Text_get_textBounds_m0D3E180B72130830D1C16BC7E5097AF2958E2740,
	TMP_Text_add_OnFontAssetRequest_m5CF2F09BB8B2E7E1F11488B48FDF3CEF23CEEA84,
	TMP_Text_remove_OnFontAssetRequest_m6B616134E9114F5ADC8034A7B2E38D41488A8BF9,
	TMP_Text_add_OnSpriteAssetRequest_m676ECA34B7C6E92AFF2A20AFC1A9AE2DE60CEA2F,
	TMP_Text_remove_OnSpriteAssetRequest_mDA9E1F66F082FC479A3EF7D8E530317B38563870,
	TMP_Text_add_OnPreRenderText_m52F3DEA8A022AFA077BB776BB59734B1C9D5D9CA,
	TMP_Text_remove_OnPreRenderText_mB46FBE276D13CB41194906F9FF92EDE25D7641BA,
	TMP_Text_get_spriteAnimator_m3DB8B24C845D9BE3C1E117F39DE45F202D7F9321,
	TMP_Text_get_flexibleHeight_m810BADBB953332F1112BEDA609F0D2D899E75347,
	TMP_Text_get_flexibleWidth_mAE1FB54D0F3EB910F566B87871BB7CCE5B3250D7,
	TMP_Text_get_minWidth_m6FDD2AE333AC038F0ADB47FE30AF428A44160B03,
	TMP_Text_get_minHeight_m54FCFDDB577882C173B9677008A2B97E92533AC7,
	TMP_Text_get_maxWidth_mA2913A569850C5B0186FFC02EBD9B17D7E4123D9,
	TMP_Text_get_maxHeight_m5673CE516B95A7268D1DD29CB14F26EB443688C2,
	TMP_Text_get_layoutElement_m6D5276FEE925F3E8CA6DD4C554F8BE1A88A5E6E6,
	TMP_Text_get_preferredWidth_mE30D1F5B8573BD0A558054D004A53DE868BD208A,
	TMP_Text_get_preferredHeight_m4F28E8FB388AFF1DC052F5F982DB2F959598B004,
	TMP_Text_get_renderedWidth_m61F93CE4B988DBCF6332EE731223AF0F72471146,
	TMP_Text_get_renderedHeight_mD905DB93B2634BB5EE481C1F71D2CAFCEF5C738D,
	TMP_Text_get_layoutPriority_m6D8DF0CCD8515FFCFA3B74F7946B32072B8EC596,
	TMP_Text_LoadFontAsset_m3E175C3A91E04695300603D04F10E6432C1D870C,
	TMP_Text_SetSharedMaterial_m2BC9A6E29786D4221CA8086F199B54691DAF0569,
	TMP_Text_GetMaterial_mF58308E4AA9C3F7448FF976710B9206C066C5406,
	TMP_Text_SetFontBaseMaterial_m6E38354D0E49FAE5EBD408A22F92236C1D68E33F,
	TMP_Text_GetSharedMaterials_m5C748AC07C4282734F6D4C553769BFE3B63F21B5,
	TMP_Text_SetSharedMaterials_m3D152FA115539A0362D44135EE48BCAAFB56F2D6,
	TMP_Text_GetMaterials_mA3F8E1546BE9C5D84DC349A8B1739DB1D16F0679,
	TMP_Text_CreateMaterialInstance_m201B4389FB351E5316ACA573F4593EA5F44D1D0A,
	TMP_Text_SetVertexColorGradient_m35E9AB171BCC614A2989143F329C96BD3E914151,
	TMP_Text_SetTextSortingOrder_m5E42564CFECE090388DE121858E94CC8903F4402,
	TMP_Text_SetTextSortingOrder_m17CA540342EAA44144E32829D672161E6C6F425B,
	TMP_Text_SetFaceColor_m865370BB950DE1BE4111341536AE062C046E5FDC,
	TMP_Text_SetOutlineColor_m22F952AFBAE8CE4564B02F573BEB9FDC30705555,
	TMP_Text_SetOutlineThickness_m2CBC33AAA504B07B48DFE771986230C772FE605C,
	TMP_Text_SetShaderDepth_mB508746026A248495C693EC1039E3A562D8A704E,
	TMP_Text_SetCulling_mEC62FDEFC0E222313165637A26D700C29DAE389D,
	TMP_Text_UpdateCulling_mFB9FD3AF46C9222182056C808198BEDB8810C82F,
	TMP_Text_GetPaddingForMaterial_m381ACEBE9696389001F7853D821FECC4E83A2111,
	TMP_Text_GetPaddingForMaterial_m5FB68F03D16813FCFC20F70ACC50DBAFEB420196,
	TMP_Text_GetTextContainerLocalCorners_m588C57396E94A4BD6B1311542E985E6587665845,
	TMP_Text_ForceMeshUpdate_mFEB0D607572734B168FCD4954BB2F32F9CE0AE7E,
	TMP_Text_UpdateGeometry_m2FA2F775454629B5ED0CF4B8E089D48B8B1A31DA,
	TMP_Text_UpdateVertexData_m2E77B6DA477425BFDA2661C6BD71E65E42CA3A98,
	TMP_Text_UpdateVertexData_m79089A6FF3818129609C9ACF34D79232FA4C5493,
	TMP_Text_SetVertices_mB1F51FB2B5247428AB1A302488BAFDCED686C0C1,
	TMP_Text_UpdateMeshPadding_m1B9F1E66E3B3E3C305567E412328865A083CD430,
	TMP_Text_CrossFadeColor_mAB054E0720A156FC584B2D71878F6C24160FC07C,
	TMP_Text_CrossFadeAlpha_mF4C9347458127DBC88C015AF4872486B7AB2E86E,
	TMP_Text_InternalCrossFadeColor_m217E640043CBDE6D81B948B138D5C9AB9B33CF71,
	TMP_Text_InternalCrossFadeAlpha_m2E502349E3F0991FFA5D6D19FC6E14E3E9F89B53,
	TMP_Text_ParseInputText_m3B4CF13CC0BF8E8A2B3980BD191A3B2FA421E36C,
	TMP_Text_PopulateTextBackingArray_mFD376BD29DBC5157116653E031FA2BB8AD85CB8B,
	TMP_Text_PopulateTextBackingArray_mDAFAFBA1D6EF883BBA870BEC34F4AFC52A8D4799,
	TMP_Text_PopulateTextBackingArray_m2DD1214AFFFF0214596222BCC5B759D0F8D48557,
	TMP_Text_PopulateTextBackingArray_mF50056377989BB902E9ECB7B8607BD5CAE2B9EC8,
	TMP_Text_PopulateTextProcessingArray_m2D1F8D3CAE8F1F29242547BCCC91D1226FA9A6F0,
	TMP_Text_SetTextInternal_mE5AAC38C055046B9EE3228640DAFA627C5BDF924,
	TMP_Text_SetText_m848189C290727009A95A00E432B66DFB2F2C3454,
	TMP_Text_SetText_mC6973FFC60DB6A96B0C4253CD2FD9D0789ECC533,
	TMP_Text_SetText_m033947AEEEBDA12707E4B0535B4CCD7EB28B5F31,
	TMP_Text_SetText_m91C93245F1F0BD149D7E81A870B1E156EBB50DD7,
	TMP_Text_SetText_mA55E85AB5C2C2ECC55F91825828DD3CCF2173E80,
	TMP_Text_SetText_m8F8C230992A14AC54379698221FA40B5AD0250E3,
	TMP_Text_SetText_m888964CBEFDBE9D7788D25D8EA11D832B52CC739,
	TMP_Text_SetText_m8AF09C554904D1C1B0004879BA3A9F1C585CB41B,
	TMP_Text_SetText_m5093EBC3B7161E3775B6A6EA2F3E7C4FAA55814B,
	TMP_Text_SetText_m229965F9267D1A1D825FF32828DDC9528A40F015,
	TMP_Text_SetText_mEFBC8BA593BB9B7A6F58BE8A1EF74F83E7B4CFF1,
	TMP_Text_SetText_m060E57CFB07010482FBDD53A653F0A61A4CDDE74,
	TMP_Text_SetText_mCF423F9A56990664E9711E71AEFB464987179AFF,
	TMP_Text_SetCharArray_mCCBCFF7608CA622F9A7E15E027662DB8561583B5,
	TMP_Text_SetCharArray_mA6EC91F806E7B7B4BAF34317531083DEC6AAFD70,
	TMP_Text_GetStyle_m556317F676C8A404F2BEEB1EA28AA188229D5886,
	TMP_Text_ReplaceOpeningStyleTag_m140CE17F312BBDE9A6F429F6976A6EAF22FBF7F7,
	TMP_Text_ReplaceOpeningStyleTag_mFE4861A4A73DA7879121B8CFCEB051320E7C2B3A,
	TMP_Text_ReplaceClosingStyleTag_m8F0A4C880ED8811B94472B9A122FEE3DF1CEA06C,
	TMP_Text_ReplaceClosingStyleTag_m930CFBC820CF701CCF4A92E8CC798640FD9E0009,
	TMP_Text_InsertOpeningStyleTag_m7194E079B8619F42CF27B3AB2A9B0A9FE2AB14BC,
	TMP_Text_InsertClosingStyleTag_m6AA7BC638D9F53B831DB2702256CFBFC25EA19AA,
	TMP_Text_GetMarkupTagHashCode_mB8A6C6A1ED3D704ADBEA0E90FCEF722AB826CD7A,
	TMP_Text_GetMarkupTagHashCode_mF2C6D3C0D954B1B17F584758FFACAAFA270B37BA,
	TMP_Text_GetStyleHashCode_m834CA7ED28BF6377F7A42C654FAA748EB0D514D6,
	TMP_Text_GetStyleHashCode_mB54D3FEFFCA8A40441A169AD140C1531A788C92F,
	NULL,
	NULL,
	TMP_Text_AddFloatToInternalTextBackingArray_m91003C38D80CE33F40B45FB30E6B90F2EC2B78AB,
	TMP_Text_AddIntegerToInternalTextBackingArray_m0C9B986C866F3CD9D1424E44F57B281EDAB7DE92,
	TMP_Text_InternalTextBackingArrayToString_m7E70067C4FF555AFF7D95718141ADA0794EF37B5,
	TMP_Text_SetArraySizes_mAD14AE87D71586E0D4BEAFC6C89347FE02E33FE2,
	TMP_Text_GetPreferredValues_mE55DE48997CA56E867C94ABF8873D1CA413ADAA8,
	TMP_Text_GetPreferredValues_m1F06F3D203FD8F13D0335F697E839E5DAA61DD14,
	TMP_Text_GetPreferredValues_m398215E34C2F85F6073BB4FFAD99E077319B2726,
	TMP_Text_GetPreferredValues_m3FAA12BB95111827B71EBDE6B3B3F59EE4EA0C2C,
	TMP_Text_GetPreferredWidth_m0478A5C6B1B1C3A4A64C5BF89401B2A33A192F5C,
	TMP_Text_GetPreferredWidth_m51F52DCBCDF0AA45D5F6F1031D15560948E08C16,
	TMP_Text_GetPreferredHeight_mD8B87C32069B477E010E30D33CB616854CE708B4,
	TMP_Text_GetPreferredHeight_m6DD3E52AA402B1D6DC3D18F8760E0B89436F97CF,
	TMP_Text_GetRenderedValues_m758F7ECA29F67E1E7E782336B2CAD7B04EEB9222,
	TMP_Text_GetRenderedValues_m08075C102D6F4332871ECF6D818664B6170B1374,
	TMP_Text_GetRenderedWidth_mCCCE790E25FD4C17B55DBE153663D8024B458EDF,
	TMP_Text_GetRenderedWidth_m73C7A4A74971381580735209DD14A2CCCC9E3281,
	TMP_Text_GetRenderedHeight_m7BEF1FB09209779C3D70185491FBC6E90A71214C,
	TMP_Text_GetRenderedHeight_m64D7F5014A10FFF692DED07E7619674F30D3B099,
	TMP_Text_CalculatePreferredValues_mFC2117C2481613AF4CD0FE52E9C7162D4EB31C2A,
	TMP_Text_GetCompoundBounds_mF60F723948DF048E702AAB62F9408FAD30A1DBF2,
	TMP_Text_GetCanvasSpaceClippingRect_m7C7869D4D77FBFFD707A3846A29792EB48B5D64F,
	TMP_Text_GetTextBounds_m9B8ADDB3EE48C956CF9D61DA303B21D5EA32081A,
	TMP_Text_GetTextBounds_m26FEA0CD67904DA57ABE718926102EEFCD374BF1,
	TMP_Text_AdjustLineOffset_m52F6B152C307D094A146CA506C23704DD425218D,
	TMP_Text_ResizeLineExtents_mD9792BED7C93557CF2A93C604497729729CCBC66,
	TMP_Text_GetTextInfo_m229923ABD01B6275D27C7BE608D316A1C4F623E7,
	TMP_Text_ComputeMarginSize_mB8DA02298390E7D183460D39B765158D5B4C4C0B,
	TMP_Text_InsertNewLine_m2FB79A0D3C653AF608C8C6C9B56BC78AD696CE85,
	TMP_Text_SaveWordWrappingState_m89FFAEE3796170C90F8EDBA696E4A14884A56650,
	TMP_Text_RestoreWordWrappingState_mB126C83C447A92E11F6AC19C2BBBD923C74D8FCA,
	TMP_Text_SaveGlyphVertexInfo_mFFB0B3A7B1DBA2EE3F4116DB0AD2D7BA2A7BADBE,
	TMP_Text_SaveSpriteVertexInfo_mB11F4EA9C81BF4C58707941D616151EE6CD2BAC3,
	TMP_Text_FillCharacterVertexBuffers_m4C17C2D2386E31401B012982171D0AB7E239B4EE,
	TMP_Text_FillCharacterVertexBuffers_mA8074BF6121C6716C641EB322E501BCFCE3CFB25,
	TMP_Text_FillSpriteVertexBuffers_m7B3035DA24821F84AE49946ABEF06D0A2A87143B,
	TMP_Text_DrawUnderlineMesh_m9A89FEC9730C4C234A06A090CEDD2338C351E3F3,
	TMP_Text_DrawTextHighlight_m328E45B989DA4EC8754CC437EACC79D8D0A7F327,
	TMP_Text_LoadDefaultSettings_m529A22FF5A03DA761B775E3EABAF5EC6D122404A,
	TMP_Text_GetSpecialCharacters_mE903DAAA333AFF79BE23404C0E530BF2F974F86E,
	TMP_Text_GetEllipsisSpecialCharacter_mAB1E3B988E1169235AEC26DC0EC29B993FDF4735,
	TMP_Text_GetUnderlineSpecialCharacter_m52EA407A41AABE20FE8888C6E94BB70EF0E82CE1,
	TMP_Text_ReplaceTagWithCharacter_m27550FAAA0F89BCBF7E6ABF7E52888B04C92AFBF,
	TMP_Text_GetFontAssetForWeight_m8CAC4978C3092AE62D5354BE0D579E1985F84323,
	TMP_Text_GetTextElement_mA9AC208C5F6080ADB94B84638ABFCB28124E889C,
	TMP_Text_SetActiveSubMeshes_mE3867037AB040A083339828CEA98FFC7D81758FE,
	TMP_Text_DestroySubMeshObjects_m7FFA3E35D4B393CC01847424F2F5C77416C1F8B3,
	TMP_Text_ClearMesh_m3A40E9A07ABE32A911001625A4DE8F80353ECF8F,
	TMP_Text_ClearMesh_m5E212AB7BAA3D3F6A84AF20D0D4C1AE72985F329,
	TMP_Text_GetParsedText_m0C3CD267431DA477842729B36C6C80D7296D4C65,
	TMP_Text_IsSelfOrLinkedAncestor_m81351987CC1F547B1E7A0EDE1109F5EF596A8F76,
	TMP_Text_ReleaseLinkedTextComponent_mBFBB0BB0702503E5492FE5CDC94164363A139696,
	TMP_Text_PackUV_m6B919A58FF6988F660ACE59AA97910B31D577905,
	TMP_Text_PackUV_m66B8E7066DC310AC67BA1FE63494D1A3BA726A00,
	TMP_Text_InternalUpdate_mD5C4F3ADB7909023ADCED1033A6EE0D15AAC1781,
	TMP_Text_HexToInt_m608FA8E451B2D296D60F096CB890714F72C5596B,
	TMP_Text_GetUTF16_m6A920DAFDD9869F0847B5C3F5B646EBFF4364B38,
	TMP_Text_GetUTF16_m5DCD9865CEC393DE526550744D2F17448FFFB031,
	TMP_Text_GetUTF16_m75142BDA9CD0E09E00079D51807092CDA41AB293,
	TMP_Text_GetUTF16_m1A6DF3361330C4A1930A8CED3EE9AB1A661FBB69,
	TMP_Text_GetUTF16_m6B311F8F9A6775761D65E56B3A14D4300694018C,
	TMP_Text_GetUTF32_m0AEBD15BD012872CA6305D7BA0C481FDA82AAC25,
	TMP_Text_GetUTF32_m5417B3BA725A8B5C3EAD1AB1C8704DCAA7D8112E,
	TMP_Text_GetUTF32_m36AC6F004482AD41D7A6E02C3661FB84CA49C939,
	TMP_Text_GetUTF32_mC701D13B98BB4F3EDA7BA77D2FEC84B957DF055D,
	TMP_Text_GetUTF32_m8969A7CF25219B3D95051380B0BF81E36515FA8B,
	TMP_Text_HexCharsToColor_mFF3D804C9D8FA7A297DE7D2FDD8ACAF56F3AE41F,
	TMP_Text_HexCharsToColor_mAB24870B76767E96CBCE96AF48D78744FBAEA2E7,
	TMP_Text_GetAttributeParameters_mA3AE2EA072B750B11D4FA5FB08F3026062B3CB5E,
	TMP_Text_ConvertToFloat_m8C77647DEB5B96F427BA09AFC56A902F3C812D09,
	TMP_Text_ConvertToFloat_m3A00B254D2DEC8796A64339BF2370E2FF0A76869,
	TMP_Text_ValidateHtmlTag_mCA56FCCE3DC46EF51927B96CD7F91B1097A0EEBA,
	TMP_Text__ctor_m9E1AC8762428FEF98646584351299FFF499B823C,
	TMP_Text__cctor_m08F26D45B9462DC23A4AB77265441FC49818D0CD,
	CharacterSubstitution__ctor_m5727A2342B980E68CA8CA895437F82280B5E4378,
	SpecialCharacter__ctor_m6EA478027143EA28D3A52D1E020B95B9286824FF,
	TextBackingContainer_get_Capacity_m314198D61452DF6CAB895C2BF8D1C0829C579F9C,
	TextBackingContainer_get_Count_mA4E440D40E9EECB361CE4697B11F9B017B19E0C1,
	TextBackingContainer_set_Count_m3833989ADDB6C436DFB7A8979080FF5F2A411F19,
	TextBackingContainer_get_Item_mA0E8BB3275942C3B08087D7E27914F436370C276,
	TextBackingContainer_set_Item_mF263D268B2D3185D818FD470F86FC8C53DD42381,
	TextBackingContainer__ctor_m28ABE283E7734CCAFCB78E5C71E817D495C1699D,
	TextBackingContainer_Resize_m669CEE085664D77F581761A5888EEF20E095F752,
	U3CU3Ec__cctor_m3BE2B41C3099327A36FCB39A0697BACA0961A764,
	U3CU3Ec__ctor_mB6F7B455BB3E880F27BB5E26F8D49389333F84E1,
	U3CU3Ec_U3C_ctorU3Eb__622_0_m4ADE4CF5BF5DB0476C27555136DB926EB976EEFE,
	TMP_TextElement_get_elementType_m932830311118A83F12EA542C3B7C03C7B1437EB3,
	TMP_TextElement_get_unicode_mF963B03CCA673335FB682EBDD1CFF86F0DB8539F,
	TMP_TextElement_set_unicode_m5DDC85416E46FEB989F4924ED4E1C8BABDE09AA0,
	TMP_TextElement_get_textAsset_m3FFA01E6D0068D1F8F578CBF2590A752683A61EA,
	TMP_TextElement_set_textAsset_m046A7EF50875FC30233B8CB06D4A5138FB63C4E1,
	TMP_TextElement_get_glyph_mB86D5107DDF4ADB051309056E876FEAE843E3D07,
	TMP_TextElement_set_glyph_m29945C7CDA0F0F2429D3000A9376B4B5177A23BD,
	TMP_TextElement_get_glyphIndex_m149D0BB0350CFC7D3C46CA011669295DC8CF8E9E,
	TMP_TextElement_set_glyphIndex_mD2D21A9AD7EF332ABE56C52031E03CB5570C2FD3,
	TMP_TextElement_get_scale_m23102716AD6E67BB03C2893983B105E8B425FE14,
	TMP_TextElement_set_scale_mB753D739067A2DF395673D5C6B01E30B74B35362,
	TMP_TextElement__ctor_m17ECA25C496E92124412C4B48665D75EE848AF83,
	TMP_TextElement_Legacy__ctor_m662C0DC5276E1A91D27923DA266C23D825949787,
	TMP_TextInfo__ctor_m3676CC349997AD7A21E043DB9938502FAAB87578,
	TMP_TextInfo__ctor_m8E0E818004F0B2A107DC7CB7DA5753DC67496263,
	TMP_TextInfo__ctor_m3F285FBF2EA3C9CF7B75EA3C1AB8CB66D37B4B4C,
	TMP_TextInfo_Clear_m288FFE54C6744C369E9B2BA903A634F461721D70,
	TMP_TextInfo_ClearAllData_m8DCD1E84BDCA57F35235847D1DC8F3758BF265AB,
	TMP_TextInfo_ClearMeshInfo_m0FBBA8965BED7D2907087B746F87B28A1956962A,
	TMP_TextInfo_ClearAllMeshInfo_mC1F838D304E7F57366F2BB6D671D9E855D48AFFE,
	TMP_TextInfo_ResetVertexLayout_mDD6C8111384A819DDD015F66567A69C97C4F74E2,
	TMP_TextInfo_ClearUnusedVertices_m46C02F1D4EB0183A973859CEDE6EE284B1F9EB56,
	TMP_TextInfo_ClearLineInfo_m055901C815B31D3996CA828A79D73DAE576A1037,
	TMP_TextInfo_ClearPageInfo_mD479D3067FC68407924FF28A468D68EA8B0680AE,
	TMP_TextInfo_CopyMeshInfoVertexData_mF66E2F8821470E68D95FEB53D456CFA86241C0CA,
	NULL,
	NULL,
	TMP_TextInfo__cctor_m5F410975A60AACB9C9286C9CD9FF445AF2E3D6AB,
	TMP_TextParsingUtilities__cctor_mEE5F7444F4B17038658A67A8C46BCE39169D141F,
	TMP_TextParsingUtilities_get_instance_m497D9C60451722EEE056E3F1971F8CD0E127BF02,
	TMP_TextParsingUtilities_GetHashCode_m96340EA80E8D65E5FC9CA5AC20A3B119E64B4228,
	TMP_TextParsingUtilities_GetHashCodeCaseSensitive_mD52F61679E036846C1303C58ED08E184E3E3EA45,
	TMP_TextParsingUtilities_ToLowerASCIIFast_m2185A2449367B135B442E8DD08DE1272A1888104,
	TMP_TextParsingUtilities_ToUpperASCIIFast_mB1C34D8B2251FE6792CFD9DEC9344201E459B545,
	TMP_TextParsingUtilities_ToUpperASCIIFast_m7264A276BBBFB6F0BF767DA0F71C0554453E0723,
	TMP_TextParsingUtilities_ToLowerASCIIFast_m781E773F4B4389AA8720355E57DD582BA06190F5,
	TMP_TextParsingUtilities_IsHighSurrogate_mBFD44ED10F92D3A76EB771CFEA7729B3464AD179,
	TMP_TextParsingUtilities_IsLowSurrogate_m19E3F3FEDCBEFE09BEE4A761B90A55C0830F34AE,
	TMP_TextParsingUtilities_ConvertToUTF32_m867CF53D1EEA890D5BF53B3D328398D60895E04B,
	TMP_TextParsingUtilities__ctor_m5B95C165745456957248A8D1EDFAD9B495F47323,
	TMP_FontStyleStack_Clear_m49B787473D053AE4AB61D0A89BE3022B6D39B15D,
	TMP_FontStyleStack_Add_m86B65684B67DF2CA334037A30E9876C0F02D454A,
	TMP_FontStyleStack_Remove_mF44A8D00AA01FCBED6B6FD0A43A8D77990D2A26E,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	CaretInfo__ctor_m32D2780AAB3322C5EB68677CE3A73BF6B43E51B8,
	TMP_TextUtilities_GetCursorIndexFromPosition_m2FE033144E5BE7DA05F0682DCD9DD9C54231198A,
	TMP_TextUtilities_GetCursorIndexFromPosition_mC95254F6E9C80CC65D7B89AB3281FB090DACAF43,
	TMP_TextUtilities_FindNearestLine_mE50181F2B093AB40EA565DFD65586EBF8D916B73,
	TMP_TextUtilities_FindNearestCharacterOnLine_m6EBAB5183A14C5D2E4471386C305979394A85C66,
	TMP_TextUtilities_IsIntersectingRectTransform_m6AAA4D9F7266A54E014CA2A6BC5A37FB32CE7CC5,
	TMP_TextUtilities_FindIntersectingCharacter_m89C20D1FB440DECC3C06670B457A707B6DB36453,
	TMP_TextUtilities_FindNearestCharacter_mC99A2AEAAC3B5C5C4D878B13BE167BC42E554030,
	TMP_TextUtilities_FindIntersectingWord_m93E6DBCA2781A67271E7D0F8906CA0488CA08EB2,
	TMP_TextUtilities_FindNearestWord_m3F68C93DA3141F35817DFE093F3F68168C40A358,
	TMP_TextUtilities_FindIntersectingLine_mDC468F4E9D28F89D313DEC468155185CB4E803E8,
	TMP_TextUtilities_FindIntersectingLink_mC752442B8650D5146505B5C18C7D92B681D276E9,
	TMP_TextUtilities_FindNearestLink_mC8D07DB867843C49B4A384A5AD980814464E0664,
	TMP_TextUtilities_PointIntersectRectangle_m02A9C5ABEC703E15DB81913479B115CB52D0E848,
	TMP_TextUtilities_ScreenPointToWorldPointInRectangle_m221201A3D27B7351AD7C5E8329FCFDB3B3377318,
	TMP_TextUtilities_IntersectLinePlane_mEC8C8890EFCC3A296825F4E754E45CC9E6CE5AF9,
	TMP_TextUtilities_DistanceToLine_mF92AF55AD4AFCCB06C8664E6FCE9BECBC1C8F347,
	TMP_TextUtilities_ToLowerFast_mC2674EF9199EBBE4FEC3A112908E99DAD62C0971,
	TMP_TextUtilities_ToUpperFast_mD4058FCC040A29181AF91BEDB9040F73C91061FA,
	TMP_TextUtilities_ToUpperASCIIFast_m0EFD2CE711167DCD6FAB7EEF3DFB371101A79ACB,
	TMP_TextUtilities_GetHashCode_mD7C0E83EA385E892E6A80B3CABB69505F2E122AE,
	TMP_TextUtilities_GetSimpleHashCode_m5BBE01E9DB50DBE54DE8834A3FC077C5C4329F7B,
	TMP_TextUtilities_GetSimpleHashCodeLowercase_m671C54756F544E0F2E814C1331FA06E3FC0F3C90,
	TMP_TextUtilities_HexToInt_m3FB0402E5313B00B59CBB7F11B5FAF73499A8E6B,
	TMP_TextUtilities_StringHexToInt_mFD6F7A40E99D45CCE70F379EF70EA0321E7A1C99,
	TMP_TextUtilities__cctor_m4D6B0C6DC30191A0209F04C0F7AD8A93F3CC250C,
	LineSegment__ctor_mD12FAF67166FBF4154B4C71793A87AC3EB9EEF0B,
	TMP_UpdateManager_get_instance_m1650984C00D47E778930C9063DFDA10409C87D4E,
	TMP_UpdateManager__ctor_m69A0A84DD4CD9C719AC6241795E327E198F3B8D5,
	TMP_UpdateManager_RegisterTextObjectForUpdate_m18247DEF67E359156574B001461A8995D6CD027D,
	TMP_UpdateManager_InternalRegisterTextObjectForUpdate_m3BE2C4BF2F7380096474A113CEA612A72B5E5BF7,
	TMP_UpdateManager_RegisterTextElementForLayoutRebuild_m6AE3A0CF4112A8963AB4C0EFA7B7ACC4505C158E,
	TMP_UpdateManager_InternalRegisterTextElementForLayoutRebuild_m99DD6449E8F765D5F10D2C272EB26673D29BAE97,
	TMP_UpdateManager_RegisterTextElementForGraphicRebuild_m483FB163F9D2AF1712185A874B980724B19BFFD5,
	TMP_UpdateManager_InternalRegisterTextElementForGraphicRebuild_m2BB7D188B607FAE033CCE3B65F6D5DBF13562524,
	TMP_UpdateManager_RegisterTextElementForCullingUpdate_m20855E80BBE3AB418B5350D58782FC57A1E65841,
	TMP_UpdateManager_InternalRegisterTextElementForCullingUpdate_m41E41B6A7F27C62897A7369DA43163AEADCC908F,
	TMP_UpdateManager_OnCameraPreCull_m265431745A965ECAA26603D4B5519043E7D99D98,
	TMP_UpdateManager_DoRebuilds_m14F711CC2FA9DA7B2B8964059CB6CA4B776F6BE8,
	TMP_UpdateManager_UnRegisterTextObjectForUpdate_mEFBA4B82356AAFD89692D3A3DA55B760977A8D40,
	TMP_UpdateManager_UnRegisterTextElementForRebuild_m024BF55859F2F2FB7ABF6994059D74FF4F9B3548,
	TMP_UpdateManager_InternalUnRegisterTextElementForGraphicRebuild_m9E12886C94B95F5EF2F4DE0F32C8CD9C4A597198,
	TMP_UpdateManager_InternalUnRegisterTextElementForLayoutRebuild_mA216D19431D7C825E187253D6D68B5EDB3B8FCEF,
	TMP_UpdateManager_InternalUnRegisterTextObjectForUpdate_m2856DE05E46E68058986301E3DBEE17C8153B2E8,
	TMP_UpdateManager__cctor_mEF08DEAFD5C6000A5E8AF447E12B4D82D91DD634,
	TMP_UpdateRegistry_get_instance_m6DDAF8DA224196A7AC60D0F1FF65752D6C03548A,
	TMP_UpdateRegistry__ctor_mAD466DAAF6A8867F7D24D9B00AD6D5E113D2649E,
	TMP_UpdateRegistry_RegisterCanvasElementForLayoutRebuild_mA9F9146A5AC0DBAB51A11A85026673AB0362BD11,
	TMP_UpdateRegistry_InternalRegisterCanvasElementForLayoutRebuild_mD0B965C049EBEB2E71B2BC8D2C7F16ECD47F0E53,
	TMP_UpdateRegistry_RegisterCanvasElementForGraphicRebuild_mB7AAD68EA73E63195616E956AB75DB961228DCD2,
	TMP_UpdateRegistry_InternalRegisterCanvasElementForGraphicRebuild_m3D71D7AB720191114A3CAD60899ED945D8614F3A,
	TMP_UpdateRegistry_PerformUpdateForCanvasRendererObjects_m0C118FBD3B2ADDD2FE9E40136F45D38D5C8975B9,
	TMP_UpdateRegistry_PerformUpdateForMeshRendererObjects_mA5D3656421524B3258494141D9A73D3A5EA49D4E,
	TMP_UpdateRegistry_UnRegisterCanvasElementForRebuild_m7ADDF5C250DDEBBD803615D0B62A0B2A25BF08C5,
	TMP_UpdateRegistry_InternalUnRegisterCanvasElementForLayoutRebuild_mA9A8EC29FC4587BAB47D3A48725299C9F3251BEC,
	TMP_UpdateRegistry_InternalUnRegisterCanvasElementForGraphicRebuild_mA2012BE5D69C00E5865F9C4428F9D85A39B4331A,
	TexturePacker_JsonArray__ctor_mA7347C642D7784A142849ED55983E779C997D853,
	SpriteFrame_ToString_m74A323FCED2C3503F98BEB090A2EF8FE20B53E0C,
	SpriteSize_ToString_mED85E2303923FBF7A05A012E064705856A4CC2DB,
	SpriteDataObject__ctor_m89C520B855B17B46E4B43024C4941688A276CBE1,
};
extern void MaterialReference__ctor_m022ED9858AAD1DCEC25CBC4C304797F4539D87E7_AdjustorThunk (void);
extern void VertexGradient__ctor_m9B59D99E8B67833BD6CC50F4704614744D271C3A_AdjustorThunk (void);
extern void VertexGradient__ctor_m8FFAAFD98D0DC4F7C6D41410EF574A6600DCA40B_AdjustorThunk (void);
extern void TMP_LinkInfo_SetLinkID_m9E9A1B09A536609EC636A3F6D14498F70C6C487A_AdjustorThunk (void);
extern void TMP_LinkInfo_GetLinkText_m954EE8FF39D62BA8113773A696095EAE85CD5E3F_AdjustorThunk (void);
extern void TMP_LinkInfo_GetLinkID_mCC9D9E783D606660A4D15E0E746E1E27AD9C2425_AdjustorThunk (void);
extern void TMP_WordInfo_GetWord_m7F72AB87E8AB0FA75616FD5409A8F5C031294D2C_AdjustorThunk (void);
extern void Extents__ctor_m2C44BA0B2EDAAB80829698A019D2BBF8DDFF630B_AdjustorThunk (void);
extern void Extents_ToString_m947E31139C9C66B1F1942EF74B2FDE347A778F99_AdjustorThunk (void);
extern void Mesh_Extents__ctor_m37E0BBEE5EED57082B82AC6162F7785B231907CB_AdjustorThunk (void);
extern void Mesh_Extents_ToString_m733F0275FC20FD2F4EE5B6E9ABBD6F3CD8D5AE23_AdjustorThunk (void);
extern void TMP_Offset_get_left_mCDC93F42B720817E1119AA5360962F038A39E044_AdjustorThunk (void);
extern void TMP_Offset_set_left_m6FF3BCE823654D55CC03B9202094E129A3891958_AdjustorThunk (void);
extern void TMP_Offset_get_right_m268492C5D14D1239A429A152ED04DD8790EC98C4_AdjustorThunk (void);
extern void TMP_Offset_set_right_m19952C4778E73F559E8229B5D1438766E4FF62F2_AdjustorThunk (void);
extern void TMP_Offset_get_top_m5BAE1A688A264A63524AD4C456CE88CB2086105E_AdjustorThunk (void);
extern void TMP_Offset_set_top_m5346213516D5B378349B70D61C4EE6BB25603DCC_AdjustorThunk (void);
extern void TMP_Offset_get_bottom_m71E985879E87F76BE28A0FB0485F279866279845_AdjustorThunk (void);
extern void TMP_Offset_set_bottom_m4FF1AE55CF113FD06678B22A8ED029F17A9019A8_AdjustorThunk (void);
extern void TMP_Offset_get_horizontal_m3BE3663354670CEA3945FD6EC7C6FD1A3F4E81F7_AdjustorThunk (void);
extern void TMP_Offset_set_horizontal_m38C3B111DD01790C98E91423FCEF6BE826826891_AdjustorThunk (void);
extern void TMP_Offset_get_vertical_mB6681568C4F9B09DCE4CBFFDC33F733DE7EFE149_AdjustorThunk (void);
extern void TMP_Offset_set_vertical_m16CC389B6E1291EA006498F0E739A05A5CD16ABE_AdjustorThunk (void);
extern void TMP_Offset__ctor_mE88A176987DB6F468CA09553D74E86E1B48AA81C_AdjustorThunk (void);
extern void TMP_Offset__ctor_m5C1836C5751505F6A9E674C8CD7A6419F4BFDCA0_AdjustorThunk (void);
extern void TMP_Offset_GetHashCode_mD43DEA54E08FF70C12AAB7FD40AC4310B81C1421_AdjustorThunk (void);
extern void TMP_Offset_Equals_m7D8B386EF75BA3B1F931F1F70AAB10FEFA6B17F4_AdjustorThunk (void);
extern void TMP_Offset_Equals_m1670D25215AB3B155D89F019D27286D294A9ECF0_AdjustorThunk (void);
extern void HighlightState__ctor_m25791146FF94DD76C2FAAAF47C1735C01D9F47B2_AdjustorThunk (void);
extern void HighlightState_GetHashCode_m2BE4FEEDFCB6713FA9C10C2D3B93E938E011C985_AdjustorThunk (void);
extern void HighlightState_Equals_m0317881F19561A64B9016A27C306FDB77460D165_AdjustorThunk (void);
extern void HighlightState_Equals_mFC0B5D3A36F1CB24FFDC21F6C238184D43324825_AdjustorThunk (void);
extern void ColorTween_get_startColor_mC3CD44E2CCEF5BB78ED52759D570B1B6855CBBCC_AdjustorThunk (void);
extern void ColorTween_set_startColor_m01FB2C14DD0139433F9EBCF39A286AE064B8A2FB_AdjustorThunk (void);
extern void ColorTween_get_targetColor_m26BAA4AAA09E6FD3E79F35C51170C32889919446_AdjustorThunk (void);
extern void ColorTween_set_targetColor_m24EB21B05BDCC21A4178DEB116962BE18B361B27_AdjustorThunk (void);
extern void ColorTween_get_tweenMode_m9194D120345334B358FA8487E98C75FDD8F8355B_AdjustorThunk (void);
extern void ColorTween_set_tweenMode_mB2A52A753B322F14EEF3A1C17B0CC51EB5210035_AdjustorThunk (void);
extern void ColorTween_get_duration_m2C26D45A626E334E9EECD575CCA45B55945736D3_AdjustorThunk (void);
extern void ColorTween_set_duration_m0C781971A9EE23189EA02A2835731918680957F0_AdjustorThunk (void);
extern void ColorTween_get_ignoreTimeScale_m008715D2A64C9FC6419C9D71A7030F8710ABA6AE_AdjustorThunk (void);
extern void ColorTween_set_ignoreTimeScale_mDC7F5B7E7EDF149304E8139C64712462DAB8D40D_AdjustorThunk (void);
extern void ColorTween_TweenValue_m43ED566CB5FA4818535832C2ECFFCFEAAE7FFE8E_AdjustorThunk (void);
extern void ColorTween_AddOnChangedCallback_mF9FE28A71E2818B2C25D79B9272E838034B5A4E4_AdjustorThunk (void);
extern void ColorTween_GetIgnoreTimescale_m502CC5CF9B974F7F564316687E61A16E8CF038F7_AdjustorThunk (void);
extern void ColorTween_GetDuration_mC929D8E88C9C230CE6A0EAE684B84CA08CFAA615_AdjustorThunk (void);
extern void ColorTween_ValidTarget_mFA6997930396ACBF53FCDFBDC457FC3C63AE90D7_AdjustorThunk (void);
extern void FloatTween_get_startValue_mE12446AD7FA5B7816124ADFF1FF7959A3B6ACF1B_AdjustorThunk (void);
extern void FloatTween_set_startValue_m1E04EA68FFEE3AA1553B194D0F82C32815E2C718_AdjustorThunk (void);
extern void FloatTween_get_targetValue_m2FA9DBB4C75BFD36E2A9DE57522CAA25D9F44FF2_AdjustorThunk (void);
extern void FloatTween_set_targetValue_mE110CBA03582B01B96766AE53F2DDD3C2D4DE131_AdjustorThunk (void);
extern void FloatTween_get_duration_mE71CD40934ED69FDD7CDA8D5438E9897E6E9FE7A_AdjustorThunk (void);
extern void FloatTween_set_duration_mF59D55C5F70E037AF88A6191D282D404C532D613_AdjustorThunk (void);
extern void FloatTween_get_ignoreTimeScale_m8FE31080B4800A6CFB89918E0803BB1BE21FDA4B_AdjustorThunk (void);
extern void FloatTween_set_ignoreTimeScale_m53B7945E5B54998B9BC28E7508E94D3A8205C10A_AdjustorThunk (void);
extern void FloatTween_TweenValue_m022D385B013439E2FB8020F6A6BD329CECA81E89_AdjustorThunk (void);
extern void FloatTween_AddOnChangedCallback_m2DCB737D223D6345503A9DA057D7935F9C3A5AD6_AdjustorThunk (void);
extern void FloatTween_GetIgnoreTimescale_mB13FC7DCC241FCF2C9EC10D8AF8A9B6103F9B420_AdjustorThunk (void);
extern void FloatTween_GetDuration_m4F4E336D66A32D1F051E0EF2B1513F05EC3BF349_AdjustorThunk (void);
extern void FloatTween_ValidTarget_m4591FB5DBEE8762554B97A09B232893EE754D7DF_AdjustorThunk (void);
extern void FontAssetCreationSettings__ctor_m9A07F1B7C85235E9BDA86E7505E0A5AE0B78E5BA_AdjustorThunk (void);
extern void KerningPairKey__ctor_m76933735460799247D37F13189B62469E35C767B_AdjustorThunk (void);
extern void GlyphValueRecord_Legacy__ctor_m6E3D36058693888F61D14BA825F5F29EA4FC7033_AdjustorThunk (void);
extern void TMP_GlyphValueRecord_get_xPlacement_m3BB0AE22AA4B44163AD2BFB438E60E227523D5E7_AdjustorThunk (void);
extern void TMP_GlyphValueRecord_set_xPlacement_m12D97CDB7F44213ACBB3C015B5E88147147850A2_AdjustorThunk (void);
extern void TMP_GlyphValueRecord_get_yPlacement_m4FC0DDE3029083A45158537122D3BC3391DF2143_AdjustorThunk (void);
extern void TMP_GlyphValueRecord_set_yPlacement_m21EE385F1B674F9A575FFE6583A7E9035CFA2C24_AdjustorThunk (void);
extern void TMP_GlyphValueRecord_get_xAdvance_mA01138133A0841ADC49C3D0718B2268D9819CE4B_AdjustorThunk (void);
extern void TMP_GlyphValueRecord_set_xAdvance_m862DABDFC3FF1C78E6A4C655A6C5631B905370E9_AdjustorThunk (void);
extern void TMP_GlyphValueRecord_get_yAdvance_m6F2282B9DF89F62B52A07D36327CC39720225BA3_AdjustorThunk (void);
extern void TMP_GlyphValueRecord_set_yAdvance_m5369AC719C39D3B9B79F5FEDC85C109754A4D60E_AdjustorThunk (void);
extern void TMP_GlyphValueRecord__ctor_m030CD9864F16A5FB58D41ECD6CF66EC883B078BA_AdjustorThunk (void);
extern void TMP_GlyphValueRecord__ctor_m5F96BB76417057AB3AC83120DA921295DBCA9952_AdjustorThunk (void);
extern void TMP_GlyphValueRecord__ctor_mFE317398DD11D070520A083E7C0758D7FD862F11_AdjustorThunk (void);
extern void TMP_GlyphAdjustmentRecord_get_glyphIndex_m5DE8A84366AD7DC8B32D99B47D2BFE291F3C4F34_AdjustorThunk (void);
extern void TMP_GlyphAdjustmentRecord_set_glyphIndex_m3045246D7E256A1DEC17ADE2887BCEB013DF2DBB_AdjustorThunk (void);
extern void TMP_GlyphAdjustmentRecord_get_glyphValueRecord_m1368E9CA86E6E76E04901506445319BAEFD6AA56_AdjustorThunk (void);
extern void TMP_GlyphAdjustmentRecord_set_glyphValueRecord_m47A43D4E95C3A89DC17588C3BE7F093517B4EBE9_AdjustorThunk (void);
extern void TMP_GlyphAdjustmentRecord__ctor_m41FDDFADD92DB1A8446228B1108E3E5C985CAAE0_AdjustorThunk (void);
extern void TMP_GlyphAdjustmentRecord__ctor_mB6BB797DD594B413042DD5D4FB8D691430FC8F51_AdjustorThunk (void);
extern void GlyphPairKey__ctor_m59DDEB66E800AABAEF624BCCF1CE091F27F124A2_AdjustorThunk (void);
extern void GlyphPairKey__ctor_mB1A0951B06F19D942015727B646A530A9EB68577_AdjustorThunk (void);
extern void TMP_MeshInfo__ctor_m453B9FC30A2CB8AB2A5C868AC4229B7903F033E6_AdjustorThunk (void);
extern void TMP_MeshInfo__ctor_m95D69F6D719C924C0AF92DCBB1F642D39469CBB5_AdjustorThunk (void);
extern void TMP_MeshInfo_ResizeMeshInfo_m13DF794141EBDD4446391BAF6FD469EEFE3DD6D1_AdjustorThunk (void);
extern void TMP_MeshInfo_ResizeMeshInfo_m247290DC2AD29A232C6473904748ADD11779D543_AdjustorThunk (void);
extern void TMP_MeshInfo_Clear_m002C7A793C6BBFF39C878B909F0162E6EB5C12F8_AdjustorThunk (void);
extern void TMP_MeshInfo_Clear_m28C815908490A64459F38D5EC110C6823B813888_AdjustorThunk (void);
extern void TMP_MeshInfo_ClearUnusedVertices_mF5DC41BB72A19486A4079208D13472DD0BDE2CD9_AdjustorThunk (void);
extern void TMP_MeshInfo_ClearUnusedVertices_m1BDC394210705FC5219A44B3D110BF50F3027B55_AdjustorThunk (void);
extern void TMP_MeshInfo_ClearUnusedVertices_mB4475A7E8ED25FBCD1D1E91924D9DF3D60AE7A1A_AdjustorThunk (void);
extern void TMP_MeshInfo_SortGeometry_m28C6E9A947C7352F16910BAE2F744087720DBECA_AdjustorThunk (void);
extern void TMP_MeshInfo_SortGeometry_m74ED0FE2065414A659EE9A9C809E1B0B4A8A7734_AdjustorThunk (void);
extern void TMP_MeshInfo_SwapVertexData_mBB35F36F8E7E6CF1429B26417140570EE94FE718_AdjustorThunk (void);
extern void CharacterSubstitution__ctor_m5727A2342B980E68CA8CA895437F82280B5E4378_AdjustorThunk (void);
extern void SpecialCharacter__ctor_m6EA478027143EA28D3A52D1E020B95B9286824FF_AdjustorThunk (void);
extern void TextBackingContainer_get_Capacity_m314198D61452DF6CAB895C2BF8D1C0829C579F9C_AdjustorThunk (void);
extern void TextBackingContainer_get_Count_mA4E440D40E9EECB361CE4697B11F9B017B19E0C1_AdjustorThunk (void);
extern void TextBackingContainer_set_Count_m3833989ADDB6C436DFB7A8979080FF5F2A411F19_AdjustorThunk (void);
extern void TextBackingContainer_get_Item_mA0E8BB3275942C3B08087D7E27914F436370C276_AdjustorThunk (void);
extern void TextBackingContainer_set_Item_mF263D268B2D3185D818FD470F86FC8C53DD42381_AdjustorThunk (void);
extern void TextBackingContainer__ctor_m28ABE283E7734CCAFCB78E5C71E817D495C1699D_AdjustorThunk (void);
extern void TextBackingContainer_Resize_m669CEE085664D77F581761A5888EEF20E095F752_AdjustorThunk (void);
extern void TMP_FontStyleStack_Clear_m49B787473D053AE4AB61D0A89BE3022B6D39B15D_AdjustorThunk (void);
extern void TMP_FontStyleStack_Add_m86B65684B67DF2CA334037A30E9876C0F02D454A_AdjustorThunk (void);
extern void TMP_FontStyleStack_Remove_mF44A8D00AA01FCBED6B6FD0A43A8D77990D2A26E_AdjustorThunk (void);
extern void CaretInfo__ctor_m32D2780AAB3322C5EB68677CE3A73BF6B43E51B8_AdjustorThunk (void);
extern void LineSegment__ctor_mD12FAF67166FBF4154B4C71793A87AC3EB9EEF0B_AdjustorThunk (void);
extern void SpriteFrame_ToString_m74A323FCED2C3503F98BEB090A2EF8FE20B53E0C_AdjustorThunk (void);
extern void SpriteSize_ToString_mED85E2303923FBF7A05A012E064705856A4CC2DB_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[110] = 
{
	{ 0x0600002A, MaterialReference__ctor_m022ED9858AAD1DCEC25CBC4C304797F4539D87E7_AdjustorThunk },
	{ 0x06000114, VertexGradient__ctor_m9B59D99E8B67833BD6CC50F4704614744D271C3A_AdjustorThunk },
	{ 0x06000115, VertexGradient__ctor_m8FFAAFD98D0DC4F7C6D41410EF574A6600DCA40B_AdjustorThunk },
	{ 0x06000116, TMP_LinkInfo_SetLinkID_m9E9A1B09A536609EC636A3F6D14498F70C6C487A_AdjustorThunk },
	{ 0x06000117, TMP_LinkInfo_GetLinkText_m954EE8FF39D62BA8113773A696095EAE85CD5E3F_AdjustorThunk },
	{ 0x06000118, TMP_LinkInfo_GetLinkID_mCC9D9E783D606660A4D15E0E746E1E27AD9C2425_AdjustorThunk },
	{ 0x06000119, TMP_WordInfo_GetWord_m7F72AB87E8AB0FA75616FD5409A8F5C031294D2C_AdjustorThunk },
	{ 0x0600011A, Extents__ctor_m2C44BA0B2EDAAB80829698A019D2BBF8DDFF630B_AdjustorThunk },
	{ 0x0600011B, Extents_ToString_m947E31139C9C66B1F1942EF74B2FDE347A778F99_AdjustorThunk },
	{ 0x0600011D, Mesh_Extents__ctor_m37E0BBEE5EED57082B82AC6162F7785B231907CB_AdjustorThunk },
	{ 0x0600011E, Mesh_Extents_ToString_m733F0275FC20FD2F4EE5B6E9ABBD6F3CD8D5AE23_AdjustorThunk },
	{ 0x06000127, TMP_Offset_get_left_mCDC93F42B720817E1119AA5360962F038A39E044_AdjustorThunk },
	{ 0x06000128, TMP_Offset_set_left_m6FF3BCE823654D55CC03B9202094E129A3891958_AdjustorThunk },
	{ 0x06000129, TMP_Offset_get_right_m268492C5D14D1239A429A152ED04DD8790EC98C4_AdjustorThunk },
	{ 0x0600012A, TMP_Offset_set_right_m19952C4778E73F559E8229B5D1438766E4FF62F2_AdjustorThunk },
	{ 0x0600012B, TMP_Offset_get_top_m5BAE1A688A264A63524AD4C456CE88CB2086105E_AdjustorThunk },
	{ 0x0600012C, TMP_Offset_set_top_m5346213516D5B378349B70D61C4EE6BB25603DCC_AdjustorThunk },
	{ 0x0600012D, TMP_Offset_get_bottom_m71E985879E87F76BE28A0FB0485F279866279845_AdjustorThunk },
	{ 0x0600012E, TMP_Offset_set_bottom_m4FF1AE55CF113FD06678B22A8ED029F17A9019A8_AdjustorThunk },
	{ 0x0600012F, TMP_Offset_get_horizontal_m3BE3663354670CEA3945FD6EC7C6FD1A3F4E81F7_AdjustorThunk },
	{ 0x06000130, TMP_Offset_set_horizontal_m38C3B111DD01790C98E91423FCEF6BE826826891_AdjustorThunk },
	{ 0x06000131, TMP_Offset_get_vertical_mB6681568C4F9B09DCE4CBFFDC33F733DE7EFE149_AdjustorThunk },
	{ 0x06000132, TMP_Offset_set_vertical_m16CC389B6E1291EA006498F0E739A05A5CD16ABE_AdjustorThunk },
	{ 0x06000134, TMP_Offset__ctor_mE88A176987DB6F468CA09553D74E86E1B48AA81C_AdjustorThunk },
	{ 0x06000135, TMP_Offset__ctor_m5C1836C5751505F6A9E674C8CD7A6419F4BFDCA0_AdjustorThunk },
	{ 0x06000139, TMP_Offset_GetHashCode_mD43DEA54E08FF70C12AAB7FD40AC4310B81C1421_AdjustorThunk },
	{ 0x0600013A, TMP_Offset_Equals_m7D8B386EF75BA3B1F931F1F70AAB10FEFA6B17F4_AdjustorThunk },
	{ 0x0600013B, TMP_Offset_Equals_m1670D25215AB3B155D89F019D27286D294A9ECF0_AdjustorThunk },
	{ 0x0600013D, HighlightState__ctor_m25791146FF94DD76C2FAAAF47C1735C01D9F47B2_AdjustorThunk },
	{ 0x06000140, HighlightState_GetHashCode_m2BE4FEEDFCB6713FA9C10C2D3B93E938E011C985_AdjustorThunk },
	{ 0x06000141, HighlightState_Equals_m0317881F19561A64B9016A27C306FDB77460D165_AdjustorThunk },
	{ 0x06000142, HighlightState_Equals_mFC0B5D3A36F1CB24FFDC21F6C238184D43324825_AdjustorThunk },
	{ 0x0600014C, ColorTween_get_startColor_mC3CD44E2CCEF5BB78ED52759D570B1B6855CBBCC_AdjustorThunk },
	{ 0x0600014D, ColorTween_set_startColor_m01FB2C14DD0139433F9EBCF39A286AE064B8A2FB_AdjustorThunk },
	{ 0x0600014E, ColorTween_get_targetColor_m26BAA4AAA09E6FD3E79F35C51170C32889919446_AdjustorThunk },
	{ 0x0600014F, ColorTween_set_targetColor_m24EB21B05BDCC21A4178DEB116962BE18B361B27_AdjustorThunk },
	{ 0x06000150, ColorTween_get_tweenMode_m9194D120345334B358FA8487E98C75FDD8F8355B_AdjustorThunk },
	{ 0x06000151, ColorTween_set_tweenMode_mB2A52A753B322F14EEF3A1C17B0CC51EB5210035_AdjustorThunk },
	{ 0x06000152, ColorTween_get_duration_m2C26D45A626E334E9EECD575CCA45B55945736D3_AdjustorThunk },
	{ 0x06000153, ColorTween_set_duration_m0C781971A9EE23189EA02A2835731918680957F0_AdjustorThunk },
	{ 0x06000154, ColorTween_get_ignoreTimeScale_m008715D2A64C9FC6419C9D71A7030F8710ABA6AE_AdjustorThunk },
	{ 0x06000155, ColorTween_set_ignoreTimeScale_mDC7F5B7E7EDF149304E8139C64712462DAB8D40D_AdjustorThunk },
	{ 0x06000156, ColorTween_TweenValue_m43ED566CB5FA4818535832C2ECFFCFEAAE7FFE8E_AdjustorThunk },
	{ 0x06000157, ColorTween_AddOnChangedCallback_mF9FE28A71E2818B2C25D79B9272E838034B5A4E4_AdjustorThunk },
	{ 0x06000158, ColorTween_GetIgnoreTimescale_m502CC5CF9B974F7F564316687E61A16E8CF038F7_AdjustorThunk },
	{ 0x06000159, ColorTween_GetDuration_mC929D8E88C9C230CE6A0EAE684B84CA08CFAA615_AdjustorThunk },
	{ 0x0600015A, ColorTween_ValidTarget_mFA6997930396ACBF53FCDFBDC457FC3C63AE90D7_AdjustorThunk },
	{ 0x0600015C, FloatTween_get_startValue_mE12446AD7FA5B7816124ADFF1FF7959A3B6ACF1B_AdjustorThunk },
	{ 0x0600015D, FloatTween_set_startValue_m1E04EA68FFEE3AA1553B194D0F82C32815E2C718_AdjustorThunk },
	{ 0x0600015E, FloatTween_get_targetValue_m2FA9DBB4C75BFD36E2A9DE57522CAA25D9F44FF2_AdjustorThunk },
	{ 0x0600015F, FloatTween_set_targetValue_mE110CBA03582B01B96766AE53F2DDD3C2D4DE131_AdjustorThunk },
	{ 0x06000160, FloatTween_get_duration_mE71CD40934ED69FDD7CDA8D5438E9897E6E9FE7A_AdjustorThunk },
	{ 0x06000161, FloatTween_set_duration_mF59D55C5F70E037AF88A6191D282D404C532D613_AdjustorThunk },
	{ 0x06000162, FloatTween_get_ignoreTimeScale_m8FE31080B4800A6CFB89918E0803BB1BE21FDA4B_AdjustorThunk },
	{ 0x06000163, FloatTween_set_ignoreTimeScale_m53B7945E5B54998B9BC28E7508E94D3A8205C10A_AdjustorThunk },
	{ 0x06000164, FloatTween_TweenValue_m022D385B013439E2FB8020F6A6BD329CECA81E89_AdjustorThunk },
	{ 0x06000165, FloatTween_AddOnChangedCallback_m2DCB737D223D6345503A9DA057D7935F9C3A5AD6_AdjustorThunk },
	{ 0x06000166, FloatTween_GetIgnoreTimescale_mB13FC7DCC241FCF2C9EC10D8AF8A9B6103F9B420_AdjustorThunk },
	{ 0x06000167, FloatTween_GetDuration_m4F4E336D66A32D1F051E0EF2B1513F05EC3BF349_AdjustorThunk },
	{ 0x06000168, FloatTween_ValidTarget_m4591FB5DBEE8762554B97A09B232893EE754D7DF_AdjustorThunk },
	{ 0x0600023B, FontAssetCreationSettings__ctor_m9A07F1B7C85235E9BDA86E7505E0A5AE0B78E5BA_AdjustorThunk },
	{ 0x0600023C, KerningPairKey__ctor_m76933735460799247D37F13189B62469E35C767B_AdjustorThunk },
	{ 0x0600023D, GlyphValueRecord_Legacy__ctor_m6E3D36058693888F61D14BA825F5F29EA4FC7033_AdjustorThunk },
	{ 0x06000268, TMP_GlyphValueRecord_get_xPlacement_m3BB0AE22AA4B44163AD2BFB438E60E227523D5E7_AdjustorThunk },
	{ 0x06000269, TMP_GlyphValueRecord_set_xPlacement_m12D97CDB7F44213ACBB3C015B5E88147147850A2_AdjustorThunk },
	{ 0x0600026A, TMP_GlyphValueRecord_get_yPlacement_m4FC0DDE3029083A45158537122D3BC3391DF2143_AdjustorThunk },
	{ 0x0600026B, TMP_GlyphValueRecord_set_yPlacement_m21EE385F1B674F9A575FFE6583A7E9035CFA2C24_AdjustorThunk },
	{ 0x0600026C, TMP_GlyphValueRecord_get_xAdvance_mA01138133A0841ADC49C3D0718B2268D9819CE4B_AdjustorThunk },
	{ 0x0600026D, TMP_GlyphValueRecord_set_xAdvance_m862DABDFC3FF1C78E6A4C655A6C5631B905370E9_AdjustorThunk },
	{ 0x0600026E, TMP_GlyphValueRecord_get_yAdvance_m6F2282B9DF89F62B52A07D36327CC39720225BA3_AdjustorThunk },
	{ 0x0600026F, TMP_GlyphValueRecord_set_yAdvance_m5369AC719C39D3B9B79F5FEDC85C109754A4D60E_AdjustorThunk },
	{ 0x06000270, TMP_GlyphValueRecord__ctor_m030CD9864F16A5FB58D41ECD6CF66EC883B078BA_AdjustorThunk },
	{ 0x06000271, TMP_GlyphValueRecord__ctor_m5F96BB76417057AB3AC83120DA921295DBCA9952_AdjustorThunk },
	{ 0x06000272, TMP_GlyphValueRecord__ctor_mFE317398DD11D070520A083E7C0758D7FD862F11_AdjustorThunk },
	{ 0x06000274, TMP_GlyphAdjustmentRecord_get_glyphIndex_m5DE8A84366AD7DC8B32D99B47D2BFE291F3C4F34_AdjustorThunk },
	{ 0x06000275, TMP_GlyphAdjustmentRecord_set_glyphIndex_m3045246D7E256A1DEC17ADE2887BCEB013DF2DBB_AdjustorThunk },
	{ 0x06000276, TMP_GlyphAdjustmentRecord_get_glyphValueRecord_m1368E9CA86E6E76E04901506445319BAEFD6AA56_AdjustorThunk },
	{ 0x06000277, TMP_GlyphAdjustmentRecord_set_glyphValueRecord_m47A43D4E95C3A89DC17588C3BE7F093517B4EBE9_AdjustorThunk },
	{ 0x06000278, TMP_GlyphAdjustmentRecord__ctor_m41FDDFADD92DB1A8446228B1108E3E5C985CAAE0_AdjustorThunk },
	{ 0x06000279, TMP_GlyphAdjustmentRecord__ctor_mB6BB797DD594B413042DD5D4FB8D691430FC8F51_AdjustorThunk },
	{ 0x06000282, GlyphPairKey__ctor_m59DDEB66E800AABAEF624BCCF1CE091F27F124A2_AdjustorThunk },
	{ 0x06000283, GlyphPairKey__ctor_mB1A0951B06F19D942015727B646A530A9EB68577_AdjustorThunk },
	{ 0x060003AC, TMP_MeshInfo__ctor_m453B9FC30A2CB8AB2A5C868AC4229B7903F033E6_AdjustorThunk },
	{ 0x060003AD, TMP_MeshInfo__ctor_m95D69F6D719C924C0AF92DCBB1F642D39469CBB5_AdjustorThunk },
	{ 0x060003AE, TMP_MeshInfo_ResizeMeshInfo_m13DF794141EBDD4446391BAF6FD469EEFE3DD6D1_AdjustorThunk },
	{ 0x060003AF, TMP_MeshInfo_ResizeMeshInfo_m247290DC2AD29A232C6473904748ADD11779D543_AdjustorThunk },
	{ 0x060003B0, TMP_MeshInfo_Clear_m002C7A793C6BBFF39C878B909F0162E6EB5C12F8_AdjustorThunk },
	{ 0x060003B1, TMP_MeshInfo_Clear_m28C815908490A64459F38D5EC110C6823B813888_AdjustorThunk },
	{ 0x060003B2, TMP_MeshInfo_ClearUnusedVertices_mF5DC41BB72A19486A4079208D13472DD0BDE2CD9_AdjustorThunk },
	{ 0x060003B3, TMP_MeshInfo_ClearUnusedVertices_m1BDC394210705FC5219A44B3D110BF50F3027B55_AdjustorThunk },
	{ 0x060003B4, TMP_MeshInfo_ClearUnusedVertices_mB4475A7E8ED25FBCD1D1E91924D9DF3D60AE7A1A_AdjustorThunk },
	{ 0x060003B5, TMP_MeshInfo_SortGeometry_m28C6E9A947C7352F16910BAE2F744087720DBECA_AdjustorThunk },
	{ 0x060003B6, TMP_MeshInfo_SortGeometry_m74ED0FE2065414A659EE9A9C809E1B0B4A8A7734_AdjustorThunk },
	{ 0x060003B7, TMP_MeshInfo_SwapVertexData_mBB35F36F8E7E6CF1429B26417140570EE94FE718_AdjustorThunk },
	{ 0x060005D1, CharacterSubstitution__ctor_m5727A2342B980E68CA8CA895437F82280B5E4378_AdjustorThunk },
	{ 0x060005D2, SpecialCharacter__ctor_m6EA478027143EA28D3A52D1E020B95B9286824FF_AdjustorThunk },
	{ 0x060005D3, TextBackingContainer_get_Capacity_m314198D61452DF6CAB895C2BF8D1C0829C579F9C_AdjustorThunk },
	{ 0x060005D4, TextBackingContainer_get_Count_mA4E440D40E9EECB361CE4697B11F9B017B19E0C1_AdjustorThunk },
	{ 0x060005D5, TextBackingContainer_set_Count_m3833989ADDB6C436DFB7A8979080FF5F2A411F19_AdjustorThunk },
	{ 0x060005D6, TextBackingContainer_get_Item_mA0E8BB3275942C3B08087D7E27914F436370C276_AdjustorThunk },
	{ 0x060005D7, TextBackingContainer_set_Item_mF263D268B2D3185D818FD470F86FC8C53DD42381_AdjustorThunk },
	{ 0x060005D8, TextBackingContainer__ctor_m28ABE283E7734CCAFCB78E5C71E817D495C1699D_AdjustorThunk },
	{ 0x060005D9, TextBackingContainer_Resize_m669CEE085664D77F581761A5888EEF20E095F752_AdjustorThunk },
	{ 0x06000605, TMP_FontStyleStack_Clear_m49B787473D053AE4AB61D0A89BE3022B6D39B15D_AdjustorThunk },
	{ 0x06000606, TMP_FontStyleStack_Add_m86B65684B67DF2CA334037A30E9876C0F02D454A_AdjustorThunk },
	{ 0x06000607, TMP_FontStyleStack_Remove_mF44A8D00AA01FCBED6B6FD0A43A8D77990D2A26E_AdjustorThunk },
	{ 0x06000619, CaretInfo__ctor_m32D2780AAB3322C5EB68677CE3A73BF6B43E51B8_AdjustorThunk },
	{ 0x06000633, LineSegment__ctor_mD12FAF67166FBF4154B4C71793A87AC3EB9EEF0B_AdjustorThunk },
	{ 0x06000652, SpriteFrame_ToString_m74A323FCED2C3503F98BEB090A2EF8FE20B53E0C_AdjustorThunk },
	{ 0x06000653, SpriteSize_ToString_mED85E2303923FBF7A05A012E064705856A4CC2DB_AdjustorThunk },
};
static const int32_t s_InvokerIndices[1620] = 
{
	21400,
	13298,
	10682,
	10682,
	13298,
	13298,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	21274,
	20847,
	10682,
	20847,
	10682,
	18531,
	5309,
	18531,
	5309,
	18531,
	5309,
	7736,
	7736,
	17444,
	3428,
	17444,
	3428,
	17444,
	3428,
	17444,
	3428,
	13298,
	850,
	17478,
	15179,
	15179,
	12815,
	10442,
	13275,
	10907,
	12996,
	10629,
	13083,
	10714,
	13275,
	10907,
	13195,
	10823,
	13195,
	10823,
	12815,
	12815,
	12815,
	10442,
	13052,
	13052,
	13285,
	10917,
	13052,
	13052,
	13298,
	13298,
	13298,
	13298,
	13298,
	10907,
	13298,
	9583,
	8964,
	13298,
	21355,
	12996,
	10629,
	12996,
	10629,
	12815,
	10442,
	13052,
	13052,
	13052,
	13052,
	13052,
	12996,
	10629,
	5438,
	1826,
	13298,
	13298,
	13298,
	13298,
	10629,
	13298,
	13298,
	4722,
	9272,
	10442,
	10682,
	10682,
	5681,
	10629,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	10629,
	10917,
	2917,
	13298,
	13298,
	13298,
	9272,
	9272,
	10682,
	13052,
	10682,
	10823,
	10458,
	10458,
	13298,
	13298,
	13298,
	13298,
	8845,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13052,
	10442,
	10442,
	10442,
	13298,
	10629,
	10629,
	12812,
	10823,
	13298,
	21355,
	13052,
	12815,
	10442,
	13052,
	13052,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13052,
	13052,
	10629,
	13298,
	9272,
	13298,
	13285,
	10917,
	13298,
	5746,
	13298,
	13298,
	1776,
	2861,
	4722,
	9272,
	13298,
	10682,
	10682,
	5681,
	10629,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13052,
	13298,
	13298,
	13298,
	13298,
	9272,
	9272,
	10682,
	13052,
	10682,
	10823,
	10458,
	10458,
	13298,
	13298,
	13298,
	10629,
	8845,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13052,
	10442,
	13298,
	12812,
	13083,
	10823,
	13298,
	21355,
	10629,
	13298,
	12815,
	13052,
	13298,
	13052,
	10629,
	13298,
	12815,
	13052,
	13298,
	13052,
	18522,
	18522,
	18522,
	18522,
	16904,
	20832,
	20847,
	20847,
	21355,
	21355,
	18522,
	18814,
	21355,
	5375,
	5309,
	20515,
	20515,
	20515,
	20515,
	16432,
	0,
	17399,
	17399,
	17398,
	17398,
	17595,
	17595,
	17596,
	17590,
	16060,
	16046,
	17516,
	17822,
	21355,
	10457,
	1774,
	2735,
	13052,
	13052,
	13052,
	5838,
	13052,
	21355,
	5838,
	13052,
	12996,
	13298,
	13298,
	5818,
	2874,
	5821,
	21339,
	21355,
	13195,
	10823,
	13195,
	10823,
	13195,
	10823,
	13195,
	10823,
	13195,
	10823,
	13195,
	10823,
	21338,
	1983,
	5784,
	17533,
	17533,
	18192,
	12996,
	7736,
	7860,
	21355,
	4736,
	17439,
	17439,
	12996,
	7736,
	7674,
	13298,
	10457,
	1774,
	21355,
	20207,
	0,
	0,
	0,
	0,
	12829,
	10457,
	12829,
	10457,
	12996,
	10629,
	13195,
	10823,
	12815,
	10442,
	10823,
	10682,
	12815,
	13195,
	12815,
	13298,
	13195,
	10823,
	13195,
	10823,
	13195,
	10823,
	12815,
	10442,
	10823,
	10682,
	12815,
	13195,
	12815,
	13298,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	18017,
	18006,
	20847,
	20847,
	18814,
	18807,
	20539,
	20539,
	20539,
	20539,
	20539,
	21355,
	13052,
	10682,
	13052,
	10682,
	13052,
	10682,
	13052,
	10682,
	13052,
	10682,
	13052,
	10682,
	13052,
	10682,
	13052,
	10682,
	13195,
	10823,
	12996,
	10629,
	10629,
	5184,
	12815,
	13298,
	13298,
	13298,
	13298,
	13298,
	10682,
	10682,
	10682,
	13298,
	13298,
	0,
	10682,
	10682,
	10682,
	13298,
	9272,
	10682,
	9272,
	10682,
	9272,
	10682,
	1644,
	5784,
	2865,
	10823,
	13298,
	9276,
	13298,
	10682,
	21355,
	13052,
	10682,
	13052,
	10682,
	13052,
	10682,
	13052,
	10682,
	10682,
	10682,
	13298,
	13052,
	10682,
	13052,
	10682,
	13298,
	10682,
	10682,
	5688,
	13052,
	10682,
	13298,
	13298,
	13298,
	10442,
	10629,
	13298,
	12815,
	13052,
	13298,
	13052,
	13052,
	10682,
	13052,
	10682,
	12996,
	10629,
	12898,
	10530,
	13052,
	10682,
	13052,
	13052,
	10682,
	13052,
	13052,
	13052,
	10682,
	12996,
	12815,
	10442,
	12815,
	10442,
	13052,
	10682,
	13052,
	10682,
	13052,
	12996,
	10629,
	12996,
	10629,
	12996,
	10629,
	12996,
	10629,
	13052,
	10682,
	13052,
	10682,
	12918,
	10551,
	13052,
	10682,
	20515,
	13672,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	2872,
	5818,
	13298,
	13298,
	13298,
	13298,
	7685,
	2225,
	2230,
	3489,
	997,
	7736,
	20515,
	20515,
	9566,
	20847,
	21355,
	20847,
	21355,
	3491,
	2158,
	3491,
	2158,
	3622,
	3622,
	13298,
	12815,
	13298,
	13298,
	13298,
	10682,
	10682,
	5688,
	0,
	10442,
	13298,
	13298,
	13298,
	10442,
	13298,
	13298,
	13298,
	21355,
	21355,
	13298,
	9565,
	9565,
	13298,
	20515,
	13298,
	44,
	5821,
	10604,
	17778,
	13261,
	10891,
	12971,
	13261,
	10891,
	12971,
	12815,
	13298,
	2876,
	1990,
	13298,
	21355,
	13298,
	13298,
	2336,
	1614,
	5266,
	10629,
	13298,
	21355,
	13298,
	9565,
	9565,
	13298,
	7736,
	13298,
	7736,
	13298,
	7736,
	16446,
	16446,
	16446,
	16446,
	21355,
	21274,
	13955,
	13955,
	13784,
	16463,
	16463,
	13298,
	13195,
	10823,
	13195,
	10823,
	13195,
	10823,
	13195,
	10823,
	1983,
	10605,
	10604,
	18191,
	13261,
	10891,
	13229,
	10858,
	5820,
	10599,
	13228,
	10857,
	13228,
	10857,
	12996,
	10629,
	5792,
	10602,
	5821,
	10682,
	13052,
	10682,
	13298,
	13298,
	21355,
	13298,
	9565,
	9565,
	13052,
	13052,
	12996,
	13298,
	13052,
	12815,
	10442,
	12815,
	10442,
	12815,
	13052,
	10682,
	10682,
	5666,
	12815,
	13195,
	10823,
	12996,
	10629,
	13052,
	10682,
	13052,
	10682,
	13052,
	10682,
	13052,
	10682,
	13195,
	10823,
	12829,
	10457,
	12815,
	10442,
	12829,
	10457,
	13052,
	10682,
	13052,
	10682,
	13052,
	10682,
	13052,
	10682,
	13052,
	10682,
	13052,
	10682,
	13052,
	10682,
	13052,
	10682,
	13052,
	10682,
	12996,
	10629,
	13195,
	10823,
	13052,
	10682,
	12815,
	10442,
	12815,
	10442,
	12815,
	10442,
	12815,
	10442,
	12996,
	10629,
	12996,
	10629,
	12996,
	10629,
	12996,
	10629,
	12996,
	10629,
	12996,
	10629,
	13052,
	10682,
	12815,
	10442,
	12815,
	10442,
	12815,
	13260,
	10890,
	12815,
	10415,
	10415,
	12996,
	10629,
	12996,
	10629,
	12996,
	10629,
	12996,
	10629,
	12815,
	12996,
	10629,
	12996,
	10629,
	12996,
	10629,
	12996,
	10629,
	12996,
	10629,
	12996,
	10629,
	13298,
	13298,
	10682,
	13052,
	13298,
	13298,
	13298,
	13298,
	10442,
	10442,
	4722,
	4722,
	21274,
	20847,
	12815,
	13298,
	13298,
	7736,
	10682,
	10682,
	9272,
	10682,
	10682,
	8845,
	7891,
	10682,
	10682,
	10682,
	13195,
	13052,
	12996,
	4722,
	12996,
	4722,
	4111,
	4111,
	4111,
	4111,
	10442,
	4722,
	10442,
	4722,
	10442,
	4722,
	10442,
	4722,
	13298,
	13298,
	13298,
	10682,
	10890,
	10890,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	10823,
	13298,
	10823,
	8801,
	8801,
	8801,
	8801,
	13298,
	13298,
	10629,
	13298,
	13298,
	13298,
	13298,
	10682,
	5715,
	13298,
	5715,
	2888,
	2467,
	13298,
	13298,
	10682,
	10682,
	13298,
	13298,
	10442,
	10682,
	10682,
	13298,
	13298,
	13298,
	10682,
	13298,
	10629,
	5184,
	13298,
	13298,
	13195,
	13195,
	13195,
	13195,
	13195,
	13195,
	12996,
	10823,
	10682,
	21355,
	13052,
	5684,
	2467,
	767,
	9555,
	13298,
	13298,
	13298,
	13298,
	13298,
	10629,
	13298,
	12815,
	13052,
	13298,
	13052,
	10629,
	13298,
	12815,
	13052,
	13298,
	13052,
	17367,
	0,
	0,
	0,
	0,
	13298,
	0,
	0,
	0,
	0,
	0,
	0,
	21355,
	21355,
	18000,
	20847,
	20515,
	18000,
	16902,
	20847,
	20847,
	21355,
	20211,
	18006,
	20515,
	16439,
	18006,
	20847,
	20847,
	21355,
	20847,
	18814,
	13298,
	13298,
	13298,
	7736,
	13298,
	7736,
	13298,
	7736,
	13298,
	7736,
	5681,
	2732,
	10629,
	5184,
	13298,
	10442,
	13298,
	10629,
	5184,
	10629,
	10682,
	5266,
	21355,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	21355,
	21274,
	20847,
	17444,
	20840,
	13298,
	10682,
	10682,
	10682,
	13298,
	5746,
	13298,
	13298,
	21274,
	21225,
	21225,
	21225,
	21225,
	21225,
	21225,
	21225,
	21263,
	20840,
	21225,
	21274,
	21274,
	21334,
	21334,
	21334,
	21350,
	21350,
	21225,
	21225,
	20832,
	21274,
	21225,
	21274,
	21274,
	21225,
	20832,
	21347,
	20862,
	21274,
	21274,
	21274,
	21274,
	21274,
	21274,
	21225,
	20832,
	21274,
	21274,
	21274,
	21274,
	21274,
	21274,
	21355,
	20515,
	13298,
	13298,
	21274,
	21274,
	21355,
	21355,
	20847,
	20818,
	19891,
	16501,
	16501,
	13298,
	13298,
	13298,
	13298,
	13298,
	845,
	756,
	13298,
	10629,
	13298,
	12815,
	13052,
	13298,
	13052,
	13052,
	10682,
	12898,
	10530,
	13052,
	10682,
	13052,
	10682,
	13052,
	10682,
	13298,
	13052,
	13298,
	8801,
	8958,
	8845,
	15477,
	15477,
	15477,
	15449,
	15449,
	15449,
	13298,
	13298,
	13298,
	13298,
	13298,
	21355,
	13298,
	9565,
	9565,
	13052,
	10682,
	12996,
	13298,
	5818,
	2874,
	5821,
	13298,
	942,
	465,
	21274,
	13052,
	10682,
	12996,
	10629,
	13052,
	13052,
	13052,
	13052,
	2778,
	13298,
	13052,
	13298,
	9267,
	9272,
	13298,
	13298,
	13298,
	13052,
	10682,
	13052,
	10682,
	13052,
	10682,
	13052,
	10682,
	13052,
	10682,
	13052,
	10682,
	12815,
	10442,
	13195,
	10823,
	13052,
	13052,
	13052,
	10682,
	13052,
	18005,
	13298,
	13298,
	13298,
	13298,
	9272,
	9272,
	13052,
	10682,
	13195,
	4722,
	13298,
	13298,
	13298,
	13298,
	13052,
	10682,
	13052,
	10682,
	13052,
	13052,
	10682,
	13052,
	10682,
	13052,
	10682,
	13052,
	10682,
	13052,
	12815,
	10442,
	13195,
	10823,
	13052,
	10682,
	13052,
	18005,
	13298,
	13298,
	13298,
	13298,
	9272,
	13195,
	9457,
	4722,
	13298,
	13298,
	13298,
	13298,
	13298,
	13052,
	5746,
	13298,
	10629,
	13298,
	13298,
	13298,
	13052,
	9272,
	9272,
	13052,
	10682,
	13298,
	0,
	0,
	0,
	13052,
	10682,
	13052,
	10682,
	12815,
	10442,
	13052,
	10682,
	13052,
	10682,
	13052,
	10682,
	13052,
	10682,
	13052,
	10682,
	12829,
	10457,
	13195,
	10823,
	12815,
	10442,
	13292,
	10923,
	13052,
	10682,
	13052,
	10682,
	12815,
	10442,
	13052,
	10682,
	13052,
	10682,
	12815,
	10442,
	12830,
	10458,
	12830,
	10458,
	13195,
	10823,
	13195,
	10823,
	12996,
	10629,
	13195,
	12815,
	10442,
	13195,
	10823,
	13195,
	10823,
	12996,
	10629,
	12815,
	12996,
	10629,
	12996,
	10629,
	12996,
	10629,
	13195,
	10823,
	13195,
	10823,
	13195,
	10823,
	13195,
	10823,
	13195,
	10823,
	13195,
	10823,
	12815,
	10442,
	13195,
	10823,
	12996,
	10629,
	12815,
	12996,
	13052,
	10682,
	12815,
	12815,
	10442,
	12815,
	10442,
	12815,
	10442,
	12815,
	10442,
	12815,
	10442,
	12815,
	10442,
	12815,
	10442,
	12815,
	10442,
	12996,
	10629,
	12996,
	10629,
	13195,
	10823,
	12996,
	10629,
	12996,
	10629,
	12815,
	10442,
	12815,
	10442,
	12996,
	10629,
	12996,
	10629,
	12996,
	10629,
	12996,
	10629,
	12815,
	10442,
	12996,
	10629,
	13285,
	10917,
	13052,
	12815,
	10442,
	12815,
	10442,
	13052,
	13052,
	12815,
	10442,
	13052,
	12815,
	10442,
	12812,
	12812,
	20847,
	20847,
	20847,
	20847,
	10682,
	10682,
	13052,
	13195,
	13195,
	13195,
	13195,
	13195,
	13195,
	13052,
	13195,
	13195,
	13195,
	13195,
	12996,
	13298,
	10682,
	9272,
	10682,
	13052,
	10682,
	9272,
	9272,
	10682,
	10629,
	10682,
	10458,
	10458,
	10823,
	13298,
	13298,
	13298,
	13195,
	9457,
	13052,
	4722,
	5681,
	10629,
	13298,
	10682,
	13298,
	1776,
	2861,
	1776,
	2861,
	13298,
	10682,
	2735,
	2735,
	2735,
	13298,
	10682,
	5666,
	5703,
	2806,
	1953,
	916,
	453,
	276,
	167,
	76,
	10682,
	2735,
	10682,
	2735,
	10682,
	2735,
	9267,
	486,
	486,
	1735,
	1735,
	1002,
	4702,
	4159,
	4362,
	2295,
	2295,
	0,
	0,
	1977,
	2565,
	13052,
	8845,
	13275,
	4664,
	9584,
	2475,
	13195,
	9464,
	13195,
	9464,
	13275,
	9581,
	13195,
	9454,
	13195,
	9454,
	1713,
	12812,
	13083,
	12812,
	6842,
	2645,
	10629,
	9272,
	13298,
	28,
	2525,
	8701,
	2862,
	10458,
	5266,
	2637,
	5266,
	172,
	2005,
	13298,
	10682,
	10682,
	10682,
	1896,
	9267,
	782,
	10442,
	13298,
	13298,
	10442,
	13052,
	7736,
	10682,
	2476,
	4614,
	13298,
	8957,
	4159,
	4159,
	4159,
	4159,
	4362,
	4159,
	4159,
	4159,
	4159,
	4362,
	3799,
	2267,
	1417,
	2452,
	1695,
	2173,
	13298,
	21355,
	5423,
	5681,
	12996,
	12996,
	10629,
	9562,
	5423,
	10629,
	10629,
	21355,
	13298,
	10682,
	12815,
	13261,
	10891,
	13052,
	10682,
	13052,
	10682,
	13261,
	10891,
	13195,
	10823,
	13298,
	13298,
	13298,
	10629,
	10682,
	13298,
	13298,
	10442,
	13298,
	10442,
	10682,
	13298,
	13298,
	13052,
	0,
	0,
	21355,
	21355,
	21274,
	20211,
	20211,
	20729,
	20729,
	20748,
	20748,
	19905,
	19905,
	18237,
	13298,
	13298,
	7685,
	7685,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	5266,
	16252,
	15248,
	16252,
	14394,
	16041,
	15249,
	15249,
	16252,
	16252,
	16252,
	16252,
	16252,
	14119,
	14929,
	14951,
	16510,
	20729,
	20729,
	20748,
	20211,
	20211,
	20744,
	20220,
	20211,
	21355,
	5845,
	21274,
	13298,
	20847,
	10682,
	20847,
	10682,
	20847,
	10682,
	20847,
	10682,
	13298,
	13298,
	20847,
	20847,
	10682,
	10682,
	10682,
	21355,
	21274,
	13298,
	20847,
	7736,
	20847,
	7736,
	13298,
	13298,
	20847,
	10682,
	10682,
	13298,
	13052,
	13052,
	13298,
};
static const Il2CppTokenRangePair s_rgctxIndices[19] = 
{
	{ 0x02000005, { 0, 19 } },
	{ 0x02000006, { 19, 20 } },
	{ 0x02000007, { 39, 21 } },
	{ 0x02000032, { 64, 8 } },
	{ 0x02000033, { 72, 6 } },
	{ 0x02000066, { 98, 12 } },
	{ 0x02000067, { 110, 5 } },
	{ 0x02000071, { 115, 14 } },
	{ 0x020000A0, { 137, 9 } },
	{ 0x06000106, { 60, 4 } },
	{ 0x060001A2, { 78, 3 } },
	{ 0x0600022A, { 81, 7 } },
	{ 0x06000383, { 88, 4 } },
	{ 0x06000384, { 92, 3 } },
	{ 0x06000385, { 95, 3 } },
	{ 0x06000586, { 129, 2 } },
	{ 0x06000587, { 131, 2 } },
	{ 0x060005F6, { 133, 2 } },
	{ 0x060005F7, { 135, 2 } },
};
extern const uint32_t g_rgctx_FastAction_1_t59CD00DD1E707A4BEC2596881929F2FA537CCAA1;
extern const uint32_t g_rgctx_Dictionary_2_t4EE4307FCD1BA3B2B480FC41AAEDE2A077BD704F;
extern const uint32_t g_rgctx_Action_1_t319B30C0C250095179CB0186D8274BD28256964E;
extern const uint32_t g_rgctx_Dictionary_2_ContainsKey_mB98AAFBA532EF90E4C6EF2378FFFC63EFFD282D9;
extern const uint32_t g_rgctx_LinkedList_1_tD3C0084872187FB26CDCBAB1C08E1C89332356F5;
extern const uint32_t g_rgctx_LinkedList_1_AddLast_mCD2D801E606774CA0EFB7038C0BE5E4F58EEF923;
extern const uint32_t g_rgctx_LinkedListNode_1_t9E16F0F8E4795EAF61E53880448662A9A0D1C358;
extern const uint32_t g_rgctx_Dictionary_2_set_Item_m7E55B45DFE0C25D3AE51EF1D155C127EA5143443;
extern const uint32_t g_rgctx_Dictionary_2_TryGetValue_m20ADC96EC8AD035F9492F65D222BE4F6471CA1FB;
extern const uint32_t g_rgctx_LinkedListNode_1U26_t5A9ACFE9BE0C63F878712E372109BBFE99052909;
extern const uint32_t g_rgctx_Dictionary_2_Remove_mB023D9E41771B0944C46F42E5C9DBC1BB235ABE7;
extern const uint32_t g_rgctx_LinkedList_1_Remove_m11013776D73C2A5CC49D33FB3AADF3FFFE1BE350;
extern const uint32_t g_rgctx_LinkedList_1_get_First_mDCB5EFC35BE263A2B84AB8C91028B145EB358C42;
extern const uint32_t g_rgctx_LinkedListNode_1_get_Value_mD907671EBEB39CFF127F77770E8C0402AA46E8AF;
extern const uint32_t g_rgctx_A_tCBA71CA534EC635C3DE5E0D0E9111CA7A46BB5AF;
extern const uint32_t g_rgctx_Action_1_Invoke_m33D1FAAF394F6D64944E08E3861083D515DA4F9D;
extern const uint32_t g_rgctx_LinkedListNode_1_get_Next_mCDAF8AD94C1EC12E16E064A2CAAB5F8793213BCA;
extern const uint32_t g_rgctx_LinkedList_1__ctor_m03A9F18A6E8D64352FEB3B2408C03855B0693A0B;
extern const uint32_t g_rgctx_Dictionary_2__ctor_m4611C017EF33B9BED0FEE0187EFC029B80C87E93;
extern const uint32_t g_rgctx_FastAction_2_t5E59E4184AAF0029262DEFABA7AA1ED66CBBB738;
extern const uint32_t g_rgctx_Dictionary_2_t6A29222450E394E5E78C6F662CF6A00442634F2B;
extern const uint32_t g_rgctx_Action_2_tF7AA36EB258601432BAA09A7EC0C3B9433680F32;
extern const uint32_t g_rgctx_Dictionary_2_ContainsKey_m5211AC8C69E23A637C61ECEB61581CA6C67DB30A;
extern const uint32_t g_rgctx_LinkedList_1_tE2965BDEA37AB803F97A5EE1A63A06A1B15D420B;
extern const uint32_t g_rgctx_LinkedList_1_AddLast_mD9C31455BD9174A5F3666359E95FF40E7CA7FD04;
extern const uint32_t g_rgctx_LinkedListNode_1_tE2481539BBC4D2CCF23C06DDB4237ED2871F5BCE;
extern const uint32_t g_rgctx_Dictionary_2_set_Item_mCFAD04902D52254697D3EC7ACAFE3AACC344984B;
extern const uint32_t g_rgctx_Dictionary_2_TryGetValue_mEA7B3AD162432AEFC6427BB43C54BEDD3F7023DB;
extern const uint32_t g_rgctx_LinkedListNode_1U26_tD07A894813235C77800AB075A0588A416805630E;
extern const uint32_t g_rgctx_Dictionary_2_Remove_mB3983603B0C49F9C899EF9075C1D3126A8B665CE;
extern const uint32_t g_rgctx_LinkedList_1_Remove_m5838916B61DBC0CB676DDDBF78AF59D2DD66EFBC;
extern const uint32_t g_rgctx_LinkedList_1_get_First_m622A5ADA953CF53B514121DBB0272BB907465830;
extern const uint32_t g_rgctx_LinkedListNode_1_get_Value_mDCF4495862BA5700619602FE2B667428FBBCBB67;
extern const uint32_t g_rgctx_A_tDAC99C1B4A6DED9649BDB03FF3C83BE2D94EB3A8;
extern const uint32_t g_rgctx_B_tA8766572D9783FCD91A87C1DDE23C5AFBAEB5373;
extern const uint32_t g_rgctx_Action_2_Invoke_m969D599195458C3D42C44CAE648E93DFBB840700;
extern const uint32_t g_rgctx_LinkedListNode_1_get_Next_mBE08B2606CA3CE0A9C431B30408F8B7DFC6F98DA;
extern const uint32_t g_rgctx_LinkedList_1__ctor_mD7D348E01C3417944450E7EC71873B8DF4F333BA;
extern const uint32_t g_rgctx_Dictionary_2__ctor_m9FB379D651E11015D5084B7B241FF22C916605AD;
extern const uint32_t g_rgctx_FastAction_3_t812590E3B8571C16E4967C8C92EF8C3893B97264;
extern const uint32_t g_rgctx_Dictionary_2_t0E0EF2BD480BBE4ADF02DE87BCFA7952D6194AEC;
extern const uint32_t g_rgctx_Action_3_tE5FA610F8E6BCB0CD7AE5A99F72941C62B71B2E0;
extern const uint32_t g_rgctx_Dictionary_2_ContainsKey_mC0830A5AC6EBDC63438E2ECBA960B168161A1E64;
extern const uint32_t g_rgctx_LinkedList_1_tD5EB0A2EC43659017ECD044ED25CDC8416A8E576;
extern const uint32_t g_rgctx_LinkedList_1_AddLast_m1E7F7BF8823EB77DAF9B508AA73DE40A2CB8D580;
extern const uint32_t g_rgctx_LinkedListNode_1_t562CBF84923A8339119513BE8764C77E17342358;
extern const uint32_t g_rgctx_Dictionary_2_set_Item_mF7AB370F40372FD2A8EC604B294E0E7B76728BB5;
extern const uint32_t g_rgctx_Dictionary_2_TryGetValue_m73F6CA6096DE6C75CE432F5EAE5585D7FD68D5F3;
extern const uint32_t g_rgctx_LinkedListNode_1U26_t326DB4C57D172810C3437D05C5930DEF006D393C;
extern const uint32_t g_rgctx_Dictionary_2_Remove_m032C3115B5E563981DF48C86D7179F7748EDE1DA;
extern const uint32_t g_rgctx_LinkedList_1_Remove_m51CBEB7E17A80339EF51857850A2FE8D26979390;
extern const uint32_t g_rgctx_LinkedList_1_get_First_mDC826306A3817126B30BF139CA45A609D950504E;
extern const uint32_t g_rgctx_LinkedListNode_1_get_Value_mD23B10DF1D9F9C92807E12DF00AA4BEF699FD183;
extern const uint32_t g_rgctx_A_t84EB77FA45BB3109DC30042750B63B2AC268A57D;
extern const uint32_t g_rgctx_B_t3133555212839B844DCC1604C752030DA7DD9FBB;
extern const uint32_t g_rgctx_C_t67D1BDBE4D71622C2530C48E54ECD01518C7BC1A;
extern const uint32_t g_rgctx_Action_3_Invoke_m54EB748BE3A859AE46997A34B9A76F853043BFE6;
extern const uint32_t g_rgctx_LinkedListNode_1_get_Next_m1F44123CF6F382E48A90522E6C61A44A7033AAC3;
extern const uint32_t g_rgctx_LinkedList_1__ctor_mABC63C63E89EDCC06EEF053E5C6C6A412439201D;
extern const uint32_t g_rgctx_Dictionary_2__ctor_m6CB1E516765BBA29D4EFAB193D86E99C98DCBEF4;
extern const uint32_t g_rgctx_T_t222618A9FABD9A1661B4EB367807BFD74727243F;
extern const uint32_t g_rgctx_List_1_tE673A19CD3E16FCE42BA1E13F8CA3051B0C66A46;
extern const uint32_t g_rgctx_List_1_get_Item_mBF7C93E2B711944BE25C1F4C551CF844292C3AE1;
extern const uint32_t g_rgctx_List_1_get_Count_mE50B1A1B3CCF9E310054DA4BBECEF5DF04BCDCCB;
extern const uint32_t g_rgctx_U3CStartU3Ed__2_tC463ECB31906D102EE6C5B019CAE102F837DC6C1;
extern const uint32_t g_rgctx_U3CStartU3Ed__2__ctor_m8AA498864DB541733DE649A8AB88FAD14D97C6EF;
extern const uint32_t g_rgctx_T_tF89D3C06555F6DCA35317A31A8C999F9341C1008;
extern const uint32_t g_rgctx_TweenRunner_1_t0F39FE120F257C2F368F5167860C56C0924DF79F;
extern const uint32_t g_rgctx_TweenRunner_1_StopTween_m4629C6650DA1109844EF11D623DFDF735BBE8381;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_tF89D3C06555F6DCA35317A31A8C999F9341C1008_ITweenValue_TweenValue_m46989B9AE4EDC4C4CDE0D87C65EA39B15408F552;
extern const uint32_t g_rgctx_TweenRunner_1_Start_mDC8166D2572C1E97009D1FFC149F24C796079E84;
extern const uint32_t g_rgctx_TweenRunner_1_t0F39FE120F257C2F368F5167860C56C0924DF79F;
extern const uint32_t g_rgctx_U3CStartU3Ed__2_t39235C59BB785ECD61B965445B2B9B5812133BAE;
extern const uint32_t g_rgctx_T_t54069A3F07DB8743A6CDBD1A84066E0D88DABCC4;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_t54069A3F07DB8743A6CDBD1A84066E0D88DABCC4_ITweenValue_ValidTarget_mE3AFBC4BBAD2D9F58F9BA79DAD0B2972F66A44A9;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_t54069A3F07DB8743A6CDBD1A84066E0D88DABCC4_ITweenValue_get_ignoreTimeScale_m7C770C3C5E0C6AE2C1E8FD30D612CCA675B8C7F0;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_t54069A3F07DB8743A6CDBD1A84066E0D88DABCC4_ITweenValue_get_duration_m28B0CBE0A1944B381A04C00B784C6123A36C37F6;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_t54069A3F07DB8743A6CDBD1A84066E0D88DABCC4_ITweenValue_TweenValue_m46989B9AE4EDC4C4CDE0D87C65EA39B15408F552;
extern const uint32_t g_rgctx_GameObject_GetComponent_TisT_t43A93FAB0FCF7F464E09DBE78ACE2469A3A01F9C_m8DBDD1ADBB488C6F0F3DD53253BA3CC5A6056B8C;
extern const uint32_t g_rgctx_T_t43A93FAB0FCF7F464E09DBE78ACE2469A3A01F9C;
extern const uint32_t g_rgctx_GameObject_AddComponent_TisT_t43A93FAB0FCF7F464E09DBE78ACE2469A3A01F9C_m285B4C83ED8C703F06CDAF38B14E463763A24330;
extern const uint32_t g_rgctx_List_1_tB247115334146A257ABD4D8A3D79BAEA603546DE;
extern const uint32_t g_rgctx_List_1_get_Count_m97DF00C7C6C0143410EEF396399A1D6A908BC346;
extern const uint32_t g_rgctx_TU5BU5DU26_t29A57B98108A8887166DCBCD2F597A778EC873CE;
extern const uint32_t g_rgctx_TU5BU5D_tB5543F2D5F1EC280218C8C8E7ADA41263E62F812;
extern const uint32_t g_rgctx_Array_Resize_TisT_t9D971C63AFD846E94332C10528EC85114CD902BF_m512355F04803CC8751D69132E22A9FC658999987;
extern const uint32_t g_rgctx_List_1_get_Item_m41E61CAFD24899199C0557CE631A4DFDD2D57A9B;
extern const uint32_t g_rgctx_T_t9D971C63AFD846E94332C10528EC85114CD902BF;
extern const uint32_t g_rgctx_TU26_t1F1A3A411AAEC8F8ABBFE1B91463DEE23CDFE3C7;
extern const uint32_t g_rgctx_T_t28AEE5CED085043F4021279BD15E4A4595D95B16;
extern const uint32_t g_rgctx_IEquatable_1_t9CD34342211B482AE008F9132FC8EFC4A3E9B0B2;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_t28AEE5CED085043F4021279BD15E4A4595D95B16_IEquatable_1_Equals_mBC582F1EA4EADEFD04C0B6A6F49CC0BCE5129D16;
extern const uint32_t g_rgctx_TU26_tAF3E0352195A1130CFBCE8BCD145174708EEE46A;
extern const uint32_t g_rgctx_T_t4558918B360B6D33767D3C706E5D23EACD2BE42E;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_t4558918B360B6D33767D3C706E5D23EACD2BE42E_Object_Equals_m07105C4585D3FE204F2A80D58523D001DC43F63B;
extern const uint32_t g_rgctx_TU26_t092A3539BA2B71215C78F9A42F9BF357551D381C;
extern const uint32_t g_rgctx_T_tC65DEBF874C735FFC1D533843AE9B8201EEBCF08;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_tC65DEBF874C735FFC1D533843AE9B8201EEBCF08_Object_Equals_m07105C4585D3FE204F2A80D58523D001DC43F63B;
extern const uint32_t g_rgctx_TMP_ListPool_1_t196C19EA606371960DEE3DBA14C6931363886000;
extern const uint32_t g_rgctx_TMP_ObjectPool_1_t8A1162899D3A229419EC3BF9001A9353E803D251;
extern const uint32_t g_rgctx_TMP_ListPool_1_t196C19EA606371960DEE3DBA14C6931363886000;
extern const uint32_t g_rgctx_TMP_ObjectPool_1_Get_mBBFF4746AA1057CA9CF5D26A403FE433EAF74F82;
extern const uint32_t g_rgctx_List_1_tDB5554039FD94D6F0C037AF838E570751781A7FE;
extern const uint32_t g_rgctx_TMP_ObjectPool_1_Release_m1BFEF60D7D335DC7957DFA2B62500267D0046054;
extern const uint32_t g_rgctx_U3CU3Ec_tD5287145DB1083FA58D1DE73E6759168650C2F69;
extern const uint32_t g_rgctx_U3CU3Ec_tD5287145DB1083FA58D1DE73E6759168650C2F69;
extern const uint32_t g_rgctx_U3CU3Ec_U3C_cctorU3Eb__3_0_m79AA95AFC19F26FDD9D3908A612B409ABCB78D4B;
extern const uint32_t g_rgctx_UnityAction_1_tCD07B30FDCF4B3342FE374DFC21A60BAF6C9A188;
extern const uint32_t g_rgctx_UnityAction_1__ctor_m709A080DA4D1E6A960F369648A0E9E1E711D680C;
extern const uint32_t g_rgctx_TMP_ObjectPool_1__ctor_mAC48BF6FFC894645E8278FF2A0E2757D0372706F;
extern const uint32_t g_rgctx_U3CU3Ec_t1EFE905FD91B5D31E484ECA3DE6ECC81903F86CA;
extern const uint32_t g_rgctx_U3CU3Ec__ctor_m6581346FF03C23CC25187B84427A34896185647F;
extern const uint32_t g_rgctx_U3CU3Ec_t1EFE905FD91B5D31E484ECA3DE6ECC81903F86CA;
extern const uint32_t g_rgctx_List_1_t551E0227199E54FC2269D739FD5831C7CACBD00D;
extern const uint32_t g_rgctx_List_1_Clear_m3E6FA896DF8B70FF22086C8DB9E282E4D1FC71B0;
extern const uint32_t g_rgctx_TMP_ObjectPool_1_tC8823715D39C2A8956194CBED32E89614DF6D6A1;
extern const uint32_t g_rgctx_TMP_ObjectPool_1_get_countAll_m82B2AB6D4283D3559574BA451C223C47CE884F1C;
extern const uint32_t g_rgctx_TMP_ObjectPool_1_get_countInactive_mB9E058F83EAF2056F60373FD4604D6FC2F9FDA09;
extern const uint32_t g_rgctx_Stack_1_tBB18BE9AAB150218001606CC5163F90787B0B6FB;
extern const uint32_t g_rgctx_Stack_1_get_Count_mDB9B62940BA3CBBB6A65815689C036E8B37BD6A7;
extern const uint32_t g_rgctx_Stack_1__ctor_mE640592B3F050E7D91F65DBF22A3C640E5F5E7DE;
extern const uint32_t g_rgctx_UnityAction_1_tE6AC6EB5FAC186761A9E8AD94FE570756E1941B0;
extern const uint32_t g_rgctx_Activator_CreateInstance_TisT_tCEF5C270075F4A7393B4776EB8CF788092CE92B8_mB0CC5A03DCD5CC47C124420819696AB0E4E5F305;
extern const uint32_t g_rgctx_T_tCEF5C270075F4A7393B4776EB8CF788092CE92B8;
extern const uint32_t g_rgctx_TMP_ObjectPool_1_set_countAll_mCDD60A520286E35F95D7003A1E184E7C86E95C03;
extern const uint32_t g_rgctx_Stack_1_Pop_mF09DF2B2E1B0429ADD3EAF45878B8A52A8059658;
extern const uint32_t g_rgctx_UnityAction_1_Invoke_m5909A5EDD00EE1D8DC182D01E3B454761DD638B5;
extern const uint32_t g_rgctx_Stack_1_Peek_m767628FEA16A6A791A8196C09538E21A1A022403;
extern const uint32_t g_rgctx_Stack_1_Push_mED3FC8406CC8AC8BFAA97A0F77DC38A0B24A772E;
extern const uint32_t g_rgctx_TU5BU5DU26_t5D10A88926A6EA192BBD9822B9F816F7190F071F;
extern const uint32_t g_rgctx_Array_Resize_TisT_t14D3FEDE62BEDF67A86FD7A678B129F9785ED1CB_mCD21AF0A5C6426F8F0CA0F82D4FD0F8AB69F42BD;
extern const uint32_t g_rgctx_TU5BU5DU26_t44606AFF52B6EE6B0FF37A278A85D49CB4FF0A70;
extern const uint32_t g_rgctx_Array_Resize_TisT_t89279E86F7FA201C826BE6B68E22F7D19A0E08D0_m48C1936A2D9A3709970E112FDB496BC8CE3D6C73;
extern const uint32_t g_rgctx_TU5BU5DU26_tF0E47A79D509A01236642FE1A9DC7D8ED96E8325;
extern const uint32_t g_rgctx_Array_Resize_TisT_t995A7B903D087765765951A378DFF0760721B4E4_m74F109C4D9FA8E2AFCEAF11B0E13ABF6E9BDE9AC;
extern const uint32_t g_rgctx_TU5BU5DU26_t8ACD7F0C4AD7F8AB0219B7D0987DDEFD42E336DE;
extern const uint32_t g_rgctx_Array_Resize_TisT_tB032BBB2FF6CEB4AA4167607EEF45028E42AD008_mF3CDF2EF59D55CBAE95F0EA8F8928A7DCE4E322A;
extern const uint32_t g_rgctx_TU5BU5D_tEB79EEC73DB04B187E4E0F0B7A64A71ECD34A982;
extern const uint32_t g_rgctx_TMP_TextProcessingStack_1_t2856C6AF99EF8DCF36E872D1D71C80F023B67D81;
extern const uint32_t g_rgctx_T_tEFDB8B1520E48ED22D3DA45F0204F72A29C2E246;
extern const uint32_t g_rgctx_TU5BU5D_tEB79EEC73DB04B187E4E0F0B7A64A71ECD34A982;
extern const uint32_t g_rgctx_TMP_TextProcessingStack_1U5BU5D_tC15EF571940D34E6DBBC9F5DA8AABB6DB5D121D7;
extern const uint32_t g_rgctx_TMP_TextProcessingStack_1_SetDefault_mFF9116F731F3C1FE35E62013579CDBA5BB6C0C98;
extern const uint32_t g_rgctx_TMP_TextProcessingStack_1_t2856C6AF99EF8DCF36E872D1D71C80F023B67D81;
extern const uint32_t g_rgctx_Array_Resize_TisT_tEFDB8B1520E48ED22D3DA45F0204F72A29C2E246_mEA41A99CCB32776E22126F5733581D853D451A44;
extern const uint32_t g_rgctx_TU5BU5DU26_t53C989AC1D370BEC70A9C229363496DD58B735F4;
static const Il2CppRGCTXDefinition s_rgctxValues[146] = 
{
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FastAction_1_t59CD00DD1E707A4BEC2596881929F2FA537CCAA1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Dictionary_2_t4EE4307FCD1BA3B2B480FC41AAEDE2A077BD704F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_1_t319B30C0C250095179CB0186D8274BD28256964E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Dictionary_2_ContainsKey_mB98AAFBA532EF90E4C6EF2378FFFC63EFFD282D9 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_LinkedList_1_tD3C0084872187FB26CDCBAB1C08E1C89332356F5 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_LinkedList_1_AddLast_mCD2D801E606774CA0EFB7038C0BE5E4F58EEF923 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_LinkedListNode_1_t9E16F0F8E4795EAF61E53880448662A9A0D1C358 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Dictionary_2_set_Item_m7E55B45DFE0C25D3AE51EF1D155C127EA5143443 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Dictionary_2_TryGetValue_m20ADC96EC8AD035F9492F65D222BE4F6471CA1FB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_LinkedListNode_1U26_t5A9ACFE9BE0C63F878712E372109BBFE99052909 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Dictionary_2_Remove_mB023D9E41771B0944C46F42E5C9DBC1BB235ABE7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_LinkedList_1_Remove_m11013776D73C2A5CC49D33FB3AADF3FFFE1BE350 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_LinkedList_1_get_First_mDCB5EFC35BE263A2B84AB8C91028B145EB358C42 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_LinkedListNode_1_get_Value_mD907671EBEB39CFF127F77770E8C0402AA46E8AF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_A_tCBA71CA534EC635C3DE5E0D0E9111CA7A46BB5AF },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Action_1_Invoke_m33D1FAAF394F6D64944E08E3861083D515DA4F9D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_LinkedListNode_1_get_Next_mCDAF8AD94C1EC12E16E064A2CAAB5F8793213BCA },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_LinkedList_1__ctor_m03A9F18A6E8D64352FEB3B2408C03855B0693A0B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Dictionary_2__ctor_m4611C017EF33B9BED0FEE0187EFC029B80C87E93 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FastAction_2_t5E59E4184AAF0029262DEFABA7AA1ED66CBBB738 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Dictionary_2_t6A29222450E394E5E78C6F662CF6A00442634F2B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_2_tF7AA36EB258601432BAA09A7EC0C3B9433680F32 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Dictionary_2_ContainsKey_m5211AC8C69E23A637C61ECEB61581CA6C67DB30A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_LinkedList_1_tE2965BDEA37AB803F97A5EE1A63A06A1B15D420B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_LinkedList_1_AddLast_mD9C31455BD9174A5F3666359E95FF40E7CA7FD04 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_LinkedListNode_1_tE2481539BBC4D2CCF23C06DDB4237ED2871F5BCE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Dictionary_2_set_Item_mCFAD04902D52254697D3EC7ACAFE3AACC344984B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Dictionary_2_TryGetValue_mEA7B3AD162432AEFC6427BB43C54BEDD3F7023DB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_LinkedListNode_1U26_tD07A894813235C77800AB075A0588A416805630E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Dictionary_2_Remove_mB3983603B0C49F9C899EF9075C1D3126A8B665CE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_LinkedList_1_Remove_m5838916B61DBC0CB676DDDBF78AF59D2DD66EFBC },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_LinkedList_1_get_First_m622A5ADA953CF53B514121DBB0272BB907465830 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_LinkedListNode_1_get_Value_mDCF4495862BA5700619602FE2B667428FBBCBB67 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_A_tDAC99C1B4A6DED9649BDB03FF3C83BE2D94EB3A8 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_B_tA8766572D9783FCD91A87C1DDE23C5AFBAEB5373 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Action_2_Invoke_m969D599195458C3D42C44CAE648E93DFBB840700 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_LinkedListNode_1_get_Next_mBE08B2606CA3CE0A9C431B30408F8B7DFC6F98DA },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_LinkedList_1__ctor_mD7D348E01C3417944450E7EC71873B8DF4F333BA },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Dictionary_2__ctor_m9FB379D651E11015D5084B7B241FF22C916605AD },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FastAction_3_t812590E3B8571C16E4967C8C92EF8C3893B97264 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Dictionary_2_t0E0EF2BD480BBE4ADF02DE87BCFA7952D6194AEC },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_3_tE5FA610F8E6BCB0CD7AE5A99F72941C62B71B2E0 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Dictionary_2_ContainsKey_mC0830A5AC6EBDC63438E2ECBA960B168161A1E64 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_LinkedList_1_tD5EB0A2EC43659017ECD044ED25CDC8416A8E576 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_LinkedList_1_AddLast_m1E7F7BF8823EB77DAF9B508AA73DE40A2CB8D580 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_LinkedListNode_1_t562CBF84923A8339119513BE8764C77E17342358 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Dictionary_2_set_Item_mF7AB370F40372FD2A8EC604B294E0E7B76728BB5 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Dictionary_2_TryGetValue_m73F6CA6096DE6C75CE432F5EAE5585D7FD68D5F3 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_LinkedListNode_1U26_t326DB4C57D172810C3437D05C5930DEF006D393C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Dictionary_2_Remove_m032C3115B5E563981DF48C86D7179F7748EDE1DA },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_LinkedList_1_Remove_m51CBEB7E17A80339EF51857850A2FE8D26979390 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_LinkedList_1_get_First_mDC826306A3817126B30BF139CA45A609D950504E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_LinkedListNode_1_get_Value_mD23B10DF1D9F9C92807E12DF00AA4BEF699FD183 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_A_t84EB77FA45BB3109DC30042750B63B2AC268A57D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_B_t3133555212839B844DCC1604C752030DA7DD9FBB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_C_t67D1BDBE4D71622C2530C48E54ECD01518C7BC1A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Action_3_Invoke_m54EB748BE3A859AE46997A34B9A76F853043BFE6 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_LinkedListNode_1_get_Next_m1F44123CF6F382E48A90522E6C61A44A7033AAC3 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_LinkedList_1__ctor_mABC63C63E89EDCC06EEF053E5C6C6A412439201D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Dictionary_2__ctor_m6CB1E516765BBA29D4EFAB193D86E99C98DCBEF4 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t222618A9FABD9A1661B4EB367807BFD74727243F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_List_1_tE673A19CD3E16FCE42BA1E13F8CA3051B0C66A46 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_get_Item_mBF7C93E2B711944BE25C1F4C551CF844292C3AE1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_get_Count_mE50B1A1B3CCF9E310054DA4BBECEF5DF04BCDCCB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CStartU3Ed__2_tC463ECB31906D102EE6C5B019CAE102F837DC6C1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CStartU3Ed__2__ctor_m8AA498864DB541733DE649A8AB88FAD14D97C6EF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tF89D3C06555F6DCA35317A31A8C999F9341C1008 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TweenRunner_1_t0F39FE120F257C2F368F5167860C56C0924DF79F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TweenRunner_1_StopTween_m4629C6650DA1109844EF11D623DFDF735BBE8381 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_tF89D3C06555F6DCA35317A31A8C999F9341C1008_ITweenValue_TweenValue_m46989B9AE4EDC4C4CDE0D87C65EA39B15408F552 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TweenRunner_1_Start_mDC8166D2572C1E97009D1FFC149F24C796079E84 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TweenRunner_1_t0F39FE120F257C2F368F5167860C56C0924DF79F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CStartU3Ed__2_t39235C59BB785ECD61B965445B2B9B5812133BAE },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t54069A3F07DB8743A6CDBD1A84066E0D88DABCC4 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_t54069A3F07DB8743A6CDBD1A84066E0D88DABCC4_ITweenValue_ValidTarget_mE3AFBC4BBAD2D9F58F9BA79DAD0B2972F66A44A9 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_t54069A3F07DB8743A6CDBD1A84066E0D88DABCC4_ITweenValue_get_ignoreTimeScale_m7C770C3C5E0C6AE2C1E8FD30D612CCA675B8C7F0 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_t54069A3F07DB8743A6CDBD1A84066E0D88DABCC4_ITweenValue_get_duration_m28B0CBE0A1944B381A04C00B784C6123A36C37F6 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_t54069A3F07DB8743A6CDBD1A84066E0D88DABCC4_ITweenValue_TweenValue_m46989B9AE4EDC4C4CDE0D87C65EA39B15408F552 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_GameObject_GetComponent_TisT_t43A93FAB0FCF7F464E09DBE78ACE2469A3A01F9C_m8DBDD1ADBB488C6F0F3DD53253BA3CC5A6056B8C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t43A93FAB0FCF7F464E09DBE78ACE2469A3A01F9C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_GameObject_AddComponent_TisT_t43A93FAB0FCF7F464E09DBE78ACE2469A3A01F9C_m285B4C83ED8C703F06CDAF38B14E463763A24330 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_List_1_tB247115334146A257ABD4D8A3D79BAEA603546DE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_get_Count_m97DF00C7C6C0143410EEF396399A1D6A908BC346 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5DU26_t29A57B98108A8887166DCBCD2F597A778EC873CE },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_tB5543F2D5F1EC280218C8C8E7ADA41263E62F812 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Array_Resize_TisT_t9D971C63AFD846E94332C10528EC85114CD902BF_m512355F04803CC8751D69132E22A9FC658999987 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_get_Item_m41E61CAFD24899199C0557CE631A4DFDD2D57A9B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t9D971C63AFD846E94332C10528EC85114CD902BF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_t1F1A3A411AAEC8F8ABBFE1B91463DEE23CDFE3C7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t28AEE5CED085043F4021279BD15E4A4595D95B16 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEquatable_1_t9CD34342211B482AE008F9132FC8EFC4A3E9B0B2 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_t28AEE5CED085043F4021279BD15E4A4595D95B16_IEquatable_1_Equals_mBC582F1EA4EADEFD04C0B6A6F49CC0BCE5129D16 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_tAF3E0352195A1130CFBCE8BCD145174708EEE46A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t4558918B360B6D33767D3C706E5D23EACD2BE42E },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_t4558918B360B6D33767D3C706E5D23EACD2BE42E_Object_Equals_m07105C4585D3FE204F2A80D58523D001DC43F63B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_t092A3539BA2B71215C78F9A42F9BF357551D381C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tC65DEBF874C735FFC1D533843AE9B8201EEBCF08 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_tC65DEBF874C735FFC1D533843AE9B8201EEBCF08_Object_Equals_m07105C4585D3FE204F2A80D58523D001DC43F63B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TMP_ListPool_1_t196C19EA606371960DEE3DBA14C6931363886000 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TMP_ObjectPool_1_t8A1162899D3A229419EC3BF9001A9353E803D251 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TMP_ListPool_1_t196C19EA606371960DEE3DBA14C6931363886000 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TMP_ObjectPool_1_Get_mBBFF4746AA1057CA9CF5D26A403FE433EAF74F82 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_List_1_tDB5554039FD94D6F0C037AF838E570751781A7FE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TMP_ObjectPool_1_Release_m1BFEF60D7D335DC7957DFA2B62500267D0046054 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec_tD5287145DB1083FA58D1DE73E6759168650C2F69 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec_tD5287145DB1083FA58D1DE73E6759168650C2F69 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec_U3C_cctorU3Eb__3_0_m79AA95AFC19F26FDD9D3908A612B409ABCB78D4B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UnityAction_1_tCD07B30FDCF4B3342FE374DFC21A60BAF6C9A188 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnityAction_1__ctor_m709A080DA4D1E6A960F369648A0E9E1E711D680C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TMP_ObjectPool_1__ctor_mAC48BF6FFC894645E8278FF2A0E2757D0372706F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec_t1EFE905FD91B5D31E484ECA3DE6ECC81903F86CA },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec__ctor_m6581346FF03C23CC25187B84427A34896185647F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec_t1EFE905FD91B5D31E484ECA3DE6ECC81903F86CA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_List_1_t551E0227199E54FC2269D739FD5831C7CACBD00D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_Clear_m3E6FA896DF8B70FF22086C8DB9E282E4D1FC71B0 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TMP_ObjectPool_1_tC8823715D39C2A8956194CBED32E89614DF6D6A1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TMP_ObjectPool_1_get_countAll_m82B2AB6D4283D3559574BA451C223C47CE884F1C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TMP_ObjectPool_1_get_countInactive_mB9E058F83EAF2056F60373FD4604D6FC2F9FDA09 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Stack_1_tBB18BE9AAB150218001606CC5163F90787B0B6FB },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Stack_1_get_Count_mDB9B62940BA3CBBB6A65815689C036E8B37BD6A7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Stack_1__ctor_mE640592B3F050E7D91F65DBF22A3C640E5F5E7DE },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UnityAction_1_tE6AC6EB5FAC186761A9E8AD94FE570756E1941B0 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Activator_CreateInstance_TisT_tCEF5C270075F4A7393B4776EB8CF788092CE92B8_mB0CC5A03DCD5CC47C124420819696AB0E4E5F305 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tCEF5C270075F4A7393B4776EB8CF788092CE92B8 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TMP_ObjectPool_1_set_countAll_mCDD60A520286E35F95D7003A1E184E7C86E95C03 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Stack_1_Pop_mF09DF2B2E1B0429ADD3EAF45878B8A52A8059658 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnityAction_1_Invoke_m5909A5EDD00EE1D8DC182D01E3B454761DD638B5 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Stack_1_Peek_m767628FEA16A6A791A8196C09538E21A1A022403 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Stack_1_Push_mED3FC8406CC8AC8BFAA97A0F77DC38A0B24A772E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5DU26_t5D10A88926A6EA192BBD9822B9F816F7190F071F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Array_Resize_TisT_t14D3FEDE62BEDF67A86FD7A678B129F9785ED1CB_mCD21AF0A5C6426F8F0CA0F82D4FD0F8AB69F42BD },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5DU26_t44606AFF52B6EE6B0FF37A278A85D49CB4FF0A70 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Array_Resize_TisT_t89279E86F7FA201C826BE6B68E22F7D19A0E08D0_m48C1936A2D9A3709970E112FDB496BC8CE3D6C73 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5DU26_tF0E47A79D509A01236642FE1A9DC7D8ED96E8325 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Array_Resize_TisT_t995A7B903D087765765951A378DFF0760721B4E4_m74F109C4D9FA8E2AFCEAF11B0E13ABF6E9BDE9AC },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5DU26_t8ACD7F0C4AD7F8AB0219B7D0987DDEFD42E336DE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Array_Resize_TisT_tB032BBB2FF6CEB4AA4167607EEF45028E42AD008_mF3CDF2EF59D55CBAE95F0EA8F8928A7DCE4E322A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_tEB79EEC73DB04B187E4E0F0B7A64A71ECD34A982 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TMP_TextProcessingStack_1_t2856C6AF99EF8DCF36E872D1D71C80F023B67D81 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tEFDB8B1520E48ED22D3DA45F0204F72A29C2E246 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_tEB79EEC73DB04B187E4E0F0B7A64A71ECD34A982 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TMP_TextProcessingStack_1U5BU5D_tC15EF571940D34E6DBBC9F5DA8AABB6DB5D121D7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TMP_TextProcessingStack_1_SetDefault_mFF9116F731F3C1FE35E62013579CDBA5BB6C0C98 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TMP_TextProcessingStack_1_t2856C6AF99EF8DCF36E872D1D71C80F023B67D81 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Array_Resize_TisT_tEFDB8B1520E48ED22D3DA45F0204F72A29C2E246_mEA41A99CCB32776E22126F5733581D853D451A44 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5DU26_t53C989AC1D370BEC70A9C229363496DD58B735F4 },
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_Unity_TextMeshPro_CodeGenModule;
const Il2CppCodeGenModule g_Unity_TextMeshPro_CodeGenModule = 
{
	"Unity.TextMeshPro.dll",
	1620,
	s_methodPointers,
	110,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	19,
	s_rgctxIndices,
	146,
	s_rgctxValues,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};

-- Merging decision tree log ---
manifest
ADDED from E:\Workspace\RGameClient\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:2:1-24:12
INJECTED from E:\Workspace\RGameClient\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:2:1-24:12
INJECTED from E:\Workspace\RGameClient\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:2:1-24:12
	package
		INJECTED from E:\Workspace\RGameClient\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml
		ADDED from E:\Workspace\RGameClient\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:2:1-24:12
		INJECTED from E:\Workspace\RGameClient\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from E:\Workspace\RGameClient\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:2:70-116
	xmlns:android
		ADDED from E:\Workspace\RGameClient\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from E:\Workspace\RGameClient\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:3:3-65
	android:name
		ADDED from E:\Workspace\RGameClient\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:3:20-62
uses-feature#0x00030000
ADDED from E:\Workspace\RGameClient\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:4:3-52
	android:glEsVersion
		ADDED from E:\Workspace\RGameClient\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:4:17-49
uses-feature#android.hardware.touchscreen
ADDED from E:\Workspace\RGameClient\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:5:3-88
	android:required
		ADDED from E:\Workspace\RGameClient\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:5:61-85
	android:name
		ADDED from E:\Workspace\RGameClient\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:5:17-60
uses-feature#android.hardware.touchscreen.multitouch
ADDED from E:\Workspace\RGameClient\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:6:3-99
	android:required
		ADDED from E:\Workspace\RGameClient\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:6:72-96
	android:name
		ADDED from E:\Workspace\RGameClient\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:6:17-71
uses-feature#android.hardware.touchscreen.multitouch.distinct
ADDED from E:\Workspace\RGameClient\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:7:3-108
	android:required
		ADDED from E:\Workspace\RGameClient\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:7:81-105
	android:name
		ADDED from E:\Workspace\RGameClient\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:7:17-80
application
ADDED from E:\Workspace\RGameClient\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:8:3-23:17
	android:extractNativeLibs
		ADDED from E:\Workspace\RGameClient\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:8:16-48
meta-data#unity.splash-mode
ADDED from E:\Workspace\RGameClient\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:9:5-69
	android:value
		ADDED from E:\Workspace\RGameClient\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:9:49-66
	android:name
		ADDED from E:\Workspace\RGameClient\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:9:16-48
meta-data#unity.splash-enable
ADDED from E:\Workspace\RGameClient\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:10:5-74
	android:value
		ADDED from E:\Workspace\RGameClient\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:10:51-71
	android:name
		ADDED from E:\Workspace\RGameClient\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:10:16-50
meta-data#unity.launch-fullscreen
ADDED from E:\Workspace\RGameClient\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:11:5-78
	android:value
		ADDED from E:\Workspace\RGameClient\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:11:55-75
	android:name
		ADDED from E:\Workspace\RGameClient\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:11:16-54
meta-data#unity.render-outside-safearea
ADDED from E:\Workspace\RGameClient\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:12:5-84
	android:value
		ADDED from E:\Workspace\RGameClient\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:12:61-81
	android:name
		ADDED from E:\Workspace\RGameClient\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:12:16-60
meta-data#notch.config
ADDED from E:\Workspace\RGameClient\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:13:5-81
	android:value
		ADDED from E:\Workspace\RGameClient\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:13:44-78
	android:name
		ADDED from E:\Workspace\RGameClient\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:13:16-43
meta-data#unity.auto-report-fully-drawn
ADDED from E:\Workspace\RGameClient\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:14:5-84
	android:value
		ADDED from E:\Workspace\RGameClient\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:14:61-81
	android:name
		ADDED from E:\Workspace\RGameClient\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:14:16-60
activity#com.unity3d.player.UnityPlayerActivity
ADDED from E:\Workspace\RGameClient\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:15:5-22:16
	android:screenOrientation
		ADDED from E:\Workspace\RGameClient\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:15:111-147
	android:launchMode
		ADDED from E:\Workspace\RGameClient\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:15:148-179
	android:hardwareAccelerated
		ADDED from E:\Workspace\RGameClient\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:15:397-432
	android:exported
		ADDED from E:\Workspace\RGameClient\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:15:433-456
	android:resizeableActivity
		ADDED from E:\Workspace\RGameClient\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:15:362-396
	android:configChanges
		ADDED from E:\Workspace\RGameClient\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:15:180-361
	android:theme
		ADDED from E:\Workspace\RGameClient\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:15:69-110
	android:name
		ADDED from E:\Workspace\RGameClient\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:15:15-68
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from E:\Workspace\RGameClient\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:16:7-19:23
category#android.intent.category.LAUNCHER
ADDED from E:\Workspace\RGameClient\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:17:9-69
	android:name
		ADDED from E:\Workspace\RGameClient\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:17:19-66
action#android.intent.action.MAIN
ADDED from E:\Workspace\RGameClient\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:18:9-61
	android:name
		ADDED from E:\Workspace\RGameClient\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:18:17-58
meta-data#unityplayer.UnityActivity
ADDED from E:\Workspace\RGameClient\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:20:7-82
	android:value
		ADDED from E:\Workspace\RGameClient\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:20:59-79
	android:name
		ADDED from E:\Workspace\RGameClient\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:20:18-58
meta-data#notch_support
ADDED from E:\Workspace\RGameClient\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:21:7-70
	android:value
		ADDED from E:\Workspace\RGameClient\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:21:47-67
	android:name
		ADDED from E:\Workspace\RGameClient\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml:21:18-46
uses-sdk
INJECTED from E:\Workspace\RGameClient\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from E:\Workspace\RGameClient\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml
INJECTED from E:\Workspace\RGameClient\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml
INJECTED from E:\Workspace\RGameClient\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml
INJECTED from E:\Workspace\RGameClient\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml
	android:targetSdkVersion
		INJECTED from E:\Workspace\RGameClient\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml
		ADDED from E:\Workspace\RGameClient\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml
		INJECTED from E:\Workspace\RGameClient\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from E:\Workspace\RGameClient\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml
		ADDED from E:\Workspace\RGameClient\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml
		INJECTED from E:\Workspace\RGameClient\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\AndroidManifest.xml

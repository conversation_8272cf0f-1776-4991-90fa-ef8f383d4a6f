﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void ByteBufferAllocator_get_Length_mC038E63A664724F0965D2B7B57899F1BDB1DCE2A (void);
extern void ByteBufferAllocator_set_Length_m17C45CA27C651A79001AED91E6AAA13B575F38C9 (void);
extern void ByteBufferAllocator__ctor_mA20B60936A23DFD8E7D134C8C522AB3916B91772 (void);
extern void ByteArrayAllocator__ctor_m64D6FFED315D68A28E03F3C7B0F57248603C0AA8 (void);
extern void ByteArrayAllocator_Reset_m9492B10C7418A95DEA296A15064D133674A0E823 (void);
extern void ByteArrayAllocator_GrowFront_mFD75F8C69430AD2566AA13EC8885AFEAD545633C (void);
extern void ByteArrayAllocator_get_Span_mA4B4F233538BFD59293E6742D1E3A09BD2F0F134 (void);
extern void ByteArrayAllocator_get_ReadOnlySpan_m2FA5A60F56587D45370CB9F32182AD92E54F7AB0 (void);
extern void ByteArrayAllocator_InitBuffer_m3A5840E5A780906C338F6D7A4F8D5612401B698A (void);
extern void ByteBuffer__ctor_mC76B5A5C27523545664570AAD1C04B663C1C503A (void);
extern void ByteBuffer__ctor_m4B87CF31CBB963E34E96F16585FCA444DF949921 (void);
extern void ByteBuffer__ctor_mEE1915E69AC2062777C33B4393DDC957749C9FF5 (void);
extern void ByteBuffer_get_Position_mAEC84EDE8EECB180F1904E4E10DFE6BF923A3F8D (void);
extern void ByteBuffer_set_Position_m0F1BC982C7D846D2F73A08D04F66D255746FCB20 (void);
extern void ByteBuffer_get_Length_mD83481CCE00F09128DBAC206A094C628E635BA8B (void);
extern void ByteBuffer_Reset_mC9313064E9ABBF21238DD1581864FF7621F343FE (void);
extern void ByteBuffer_Reset_m1750E2DCC8E1C8B18CCA921D876B4A70D7035F03 (void);
extern void ByteBuffer_GrowFront_m0DE57B8E020B3948327E205840F49809DA043F30 (void);
extern void ByteBuffer_ToArray_mF1209C898F0DFF95A6C84A6218886D10808CB379 (void);
extern void ByteBuffer_ToSpan_m00B8ED7F4A80E5C7A3BF6F740394DC2B5ED6274E (void);
extern void ByteBuffer_ReverseBytes_m36121CFC834DEE45DB23F98CA3864D14229D6DAC (void);
extern void ByteBuffer_ReverseBytes_mB15365C2362720EE11ACA5BE0B00B26D4CDA3DC0 (void);
extern void ByteBuffer_AssertOffsetAndLength_m7DE914630999CC751A304662915A714328B53AC7 (void);
extern void ByteBuffer_PutSbyte_mC6055AB467538627CB2EEA9050DA94A819EA5148 (void);
extern void ByteBuffer_PutByte_m81927B710F29CBE2EFB0E5F298398AB7D0D26967 (void);
extern void ByteBuffer_PutByte_mA27E6385D3CF1624F3A44D01275E631FC4F87B2E (void);
extern void ByteBuffer_PutStringUTF8_m8A9FB9D67D35A58029657802ACA2C25A64BADAA3 (void);
extern void ByteBuffer_PutShort_m40945E04907D64971273BA0CA8C483FEAE91F451 (void);
extern void ByteBuffer_PutUshort_m3FAF834DEE05A58095F7851DEA351C8AAC6B09C4 (void);
extern void ByteBuffer_PutInt_mF305D6CF0623CB47E43C29A1CC08BFF7D3214C69 (void);
extern void ByteBuffer_PutUint_m4FE24EA7B76E8405CD82B65841609BBBAF6C9F4C (void);
extern void ByteBuffer_PutLong_mD69C4E9BF0355C81EB6B8829BF85161B95B02A2B (void);
extern void ByteBuffer_PutUlong_m0E9B506A058766F3BB5D8BF01B10AFE695CC6C9F (void);
extern void ByteBuffer_PutFloat_m3BD6261E6C39B5B3F51DDF8412D26901024AC600 (void);
extern void ByteBuffer_PutDouble_mFDF587433B6198987FAF1AD26DF320F9BCA41A49 (void);
extern void ByteBuffer_GetSbyte_mC5F6FC81C592D6FDFDF41EE3EA719EEA2234295B (void);
extern void ByteBuffer_Get_m4DACAF12CF9F83591DF6B595512714C893377FDB (void);
extern void ByteBuffer_GetStringUTF8_m54FDA99A7BCE5B71C55BCE816CE73854FFFB0EAF (void);
extern void ByteBuffer_GetShort_m3ED68273C4B0ABA97718B68F439D042C758D084B (void);
extern void ByteBuffer_GetUshort_m99CBB64DF8F7D844B0F6FE18671922DF28D5DBDD (void);
extern void ByteBuffer_GetInt_mD2F94B1EFC4E1B48E6A25E9FE27A36CB691F93C7 (void);
extern void ByteBuffer_GetUint_m7686A15FBE8934D1AE054CDE1D18F43BA59EBE6D (void);
extern void ByteBuffer_GetLong_m0F4A58CC22C3E0FA3B0B2FD93CC45345E9FB1059 (void);
extern void ByteBuffer_GetUlong_m5960AA0201977D5CBB9E711EB7A912E0A986F549 (void);
extern void ByteBuffer_GetFloat_m24BCA222E9470DEFB7AE68B7DF3800B187DE4AD6 (void);
extern void ByteBuffer_GetDouble_m30822FB271051CFEC593734ED7B4E71EBD61C018 (void);
extern void ByteBuffer__cctor_m4200E985A77D02E2BB383C7B87CB5A607CD130D4 (void);
extern void FlatBufferBuilder__ctor_mD9C52676666C70EDB5CB7C7A9E3BA3CD07961796 (void);
extern void FlatBufferBuilder_Clear_mEF930BED718919B45BF5CCDDC7028D3D851FC7A4 (void);
extern void FlatBufferBuilder_get_ForceDefaults_mA0FE8A741C81C5945948228ED35D4F7E3BECC960 (void);
extern void FlatBufferBuilder_get_Offset_mC74D55C982A3866A2171140BFFEF663306288AE9 (void);
extern void FlatBufferBuilder_Pad_m45B94144FC69144F0D1E1D10A7012FEA5393F964 (void);
extern void FlatBufferBuilder_GrowBuffer_m880D7E362F3C37DF5D2832D6554A8AEEE73E5367 (void);
extern void FlatBufferBuilder_Prep_m3DA1C5F1967E6C9DE1F991519E1F87EE95C35AB7 (void);
extern void FlatBufferBuilder_PutBool_m9DFECA7733C9F313CBB77F2EA3D2C16A6CC4D925 (void);
extern void FlatBufferBuilder_PutSbyte_m4B1681B63002727EF1ACEB361FFD075CBC35EA8C (void);
extern void FlatBufferBuilder_PutByte_mEA2C1C8F64D64C81C35C1D51D33423CF608A60F3 (void);
extern void FlatBufferBuilder_PutShort_mE92EC85294499D0DC414F376099F73C267C82D0E (void);
extern void FlatBufferBuilder_PutUshort_mA897A7DC14B66A18334B2948B58F918B1FFFF915 (void);
extern void FlatBufferBuilder_PutInt_m3657C36B7F37D77B13786E341A742332A942C07B (void);
extern void FlatBufferBuilder_PutUint_mE4C61BE169A0CEF7FDD69C461DA07A200B5A2F6A (void);
extern void FlatBufferBuilder_PutLong_m69929C60C71F08604C3E428A3C3C4F047CEF6F1A (void);
extern void FlatBufferBuilder_PutUlong_m38DBCF4668E499C51EB30003652730076F100680 (void);
extern void FlatBufferBuilder_PutFloat_m4BF4065EA873C657DFC67B301F0AE81AF8A6AB5D (void);
extern void FlatBufferBuilder_PutDouble_mD51D07A38BABE4EA8EB9689D8D5536B97851981D (void);
extern void FlatBufferBuilder_AddBool_m478C50A00AA0FFFC9A0551F1B14688D00E654D67 (void);
extern void FlatBufferBuilder_AddSbyte_m8205DD96B17E57ECA93B00E0CF9941423E635C29 (void);
extern void FlatBufferBuilder_AddByte_m09B496156A95861FDF7841640C5C1719723A7036 (void);
extern void FlatBufferBuilder_AddShort_mA240DA72E828B425EEF2128E7933F8E9696F5075 (void);
extern void FlatBufferBuilder_AddUshort_mFBED2384AD4CFA68C46F725B9342FA4F8AB02573 (void);
extern void FlatBufferBuilder_AddInt_mC7ED89CD9BAD7A1F34E445DDFF066513AA2B926E (void);
extern void FlatBufferBuilder_AddUint_m5E52C37A1B3FF5FEF363B603C8E379847EBF0FF9 (void);
extern void FlatBufferBuilder_AddLong_m7941C58219E84656250A86B5B70D5E3731753DB5 (void);
extern void FlatBufferBuilder_AddUlong_mD667451BC4F21D55EB19276E56C538BABCB0C9A3 (void);
extern void FlatBufferBuilder_AddFloat_m631138283BEE1A25D70D62629445315F699D2C4D (void);
extern void FlatBufferBuilder_AddDouble_mF109F698FFEF929DE54342B6A78D58F791DA7E5D (void);
extern void FlatBufferBuilder_AddOffset_m7A64D677E7BE356B284C1DD84A7FD6E8C7DDCA27 (void);
extern void FlatBufferBuilder_StartVector_m6150568905670346B15955DDA02E5A09EB816E8D (void);
extern void FlatBufferBuilder_EndVector_mB83D1FC66B112D7A125563866327564C1DAF0DC7 (void);
extern void FlatBufferBuilder_Nested_m45AE882A96E0B4309916DD3095168360CBFAE397 (void);
extern void FlatBufferBuilder_NotNested_m550AD28D180BF3E1C547BB535382BCE59B9BADB2 (void);
extern void FlatBufferBuilder_StartTable_m9EB86E88900E2C9419CFF54C05518FA2C89121ED (void);
extern void FlatBufferBuilder_Slot_mCF8F59D561C30C152A31A9754AA66D7A4E205B46 (void);
extern void FlatBufferBuilder_AddBool_m5D0F7FD6399AB4D0990CF2596D4FD15400DF61B6 (void);
extern void FlatBufferBuilder_AddSbyte_mD18D3513DBC378A3887544D238D14BE9DC83732F (void);
extern void FlatBufferBuilder_AddByte_mA2FA2D184EF297FE203000FC60EEF2B055D26F0F (void);
extern void FlatBufferBuilder_AddShort_m11792B784B3D896795B2E2FE4769379E915B9089 (void);
extern void FlatBufferBuilder_AddUshort_m0654CFED47923CA687388B503D8D397DC650A142 (void);
extern void FlatBufferBuilder_AddInt_mCBF834EC2450E9210F91EAB87A2B825F09F08BED (void);
extern void FlatBufferBuilder_AddUint_m311E75686339879CD43C2AA8B764B92E198A98AE (void);
extern void FlatBufferBuilder_AddLong_mD6BCBC1C0CEE0C60F862183C5E7B27936BFEC624 (void);
extern void FlatBufferBuilder_AddUlong_m755B6E69F62BC6D085B3C341B1A6044BC9E4265A (void);
extern void FlatBufferBuilder_AddFloat_m47B8DC15B8788B1ADED0CD59E8DEA8C017ADCDEA (void);
extern void FlatBufferBuilder_AddDouble_mD10E6A16BD9702A5F7FDA2228C3633A7B828A06B (void);
extern void FlatBufferBuilder_AddOffset_mF017678579F65CB1B835E7A9451144D0C656086D (void);
extern void FlatBufferBuilder_CreateString_m1A7788EF2664F6241243D3F1EB6973BBCC511F00 (void);
extern void FlatBufferBuilder_AddStruct_mCAD7F655DB90127B030826FF467F8A17678E4DDC (void);
extern void FlatBufferBuilder_EndTable_m176D804DB1A019B3AF4735EBE7FECDD766E4CA73 (void);
extern void FlatBufferBuilder_Finish_m356784F295B0938131FC67FE20FA7D3A2E91D42E (void);
extern void FlatBufferBuilder_Finish_mAE4C42C9B9F79AF0106923368D952B1160A46F6D (void);
extern void FlatBufferBuilder_FinishSizePrefixed_m46F76A58F13D0AFA203806ED216CB8D23B7D8F72 (void);
extern void FlatBufferBuilder_get_DataBuffer_mF063BD391805D53655476AB674FDFCD9880900D7 (void);
extern void FlatBufferConstants_FLATBUFFERS_23_5_26_m157E82FCD8C5BC7B96196D3EF099C0F9845203EC (void);
extern void FlatBuffersWarpObject__ctor_mB3653C6F6B9FE6D19F0373D08F7D25CF2AF69608 (void);
extern void Options__ctor_m443C4957B9712416B855C1E610E23CCEC9756DB7 (void);
extern void Options_get_maxDepth_m437B08A483A59E4600A3A2B6FE3A26E3EA8B2212 (void);
extern void Options_get_maxTables_mE2E55C408DAE7EB5059F6169F471ED14B93F2D40 (void);
extern void Options_get_stringEndCheck_mB9217751DB5C7D10C8EDB209FC945402821A1339 (void);
extern void Options_get_alignmentCheck_mC94E7A871FC44FE7505713CD0E69A9FE2BFD4EEA (void);
extern void VerifyTableAction__ctor_m7AEEAD7ED5C1ABF23BE481BA49D8BA704E421456 (void);
extern void VerifyTableAction_Invoke_mDD4DF94C57F6A57A87F19A8915ACD085F65FF6D4 (void);
extern void VerifyUnionAction__ctor_m8FE595B1CA4BF89B3520BBD1554A4FCF98D8254B (void);
extern void VerifyUnionAction_Invoke_mF8A06007D2423423954038A65C411972E1DF6B3B (void);
extern void Verifier__ctor_mFF28952281B211ADE655D82536791DEFC321B649 (void);
extern void Verifier_get_options_m5D0BEA9EBE4645B980E3066F2E42B042943D87E9 (void);
extern void Verifier_get_depth_m4A917A65B04FAC0E7374D664A28B7BFC089AA74E (void);
extern void Verifier_set_depth_m996F6606AC8B244E1D3AB94C5C32C1934B47A7C7 (void);
extern void Verifier_get_numTables_mD6123A14045007BAE82B1770EB9201594F778973 (void);
extern void Verifier_set_numTables_mFD577C7B868B6ACE51DCAFDBE879183BBE9B08B4 (void);
extern void Verifier_BufferHasIdentifier_m47A5B9E1944079E3B21B67886594DD78A79F9F76 (void);
extern void Verifier_ReadUOffsetT_mFA082679C617C5C8424CDDFE3965438AB7DE85B1 (void);
extern void Verifier_ReadSOffsetT_m5A43060F831A70DD3ECDFA632C079B51B4C15EBE (void);
extern void Verifier_ReadVOffsetT_m3F7EA721035EA1275A5536FA2965DC30FB246B0F (void);
extern void Verifier_GetVRelOffset_mF26D54B2378F87DEA0C42285F40ECB98B05D2F14 (void);
extern void Verifier_GetVOffset_m75F1130DAE55F0EE4BDFE43737E53113BF25FD8F (void);
extern void Verifier_CheckComplexity_m154BA6645F54D28747BCF950D4371C748BC4991D (void);
extern void Verifier_CheckAlignment_m967E1965E15845B66E5454A303C51D46DE3FEDF5 (void);
extern void Verifier_CheckElement_m2CF35C61F826AE256B69A5EEA5719190AF474680 (void);
extern void Verifier_CheckScalar_m9799566886A1AC4D9A2A82F5093B0BA1FC8BF735 (void);
extern void Verifier_CheckVectorOrString_m84A7DB3D433AC14911F20DB712CF3BB178A37FB8 (void);
extern void Verifier_CheckString_m77D796D84E00F7EE4ED3B35A183B80C54C610241 (void);
extern void Verifier_CheckVector_m53F4531381F8067BDFEF05CE7EE4DCC55E7D4DC9 (void);
extern void Verifier_CheckTable_mBD169E08464C583974B9759AE59A7FF29E2F8691 (void);
extern void Verifier_CheckStringFunc_mA69C648CF9A9C67C33D1FAA25111ECE5EB1EE237 (void);
extern void Verifier_CheckVectorOfObjects_mF84993CB74FF40A3AFA1AF020D677A7CBF4A9E6D (void);
extern void Verifier_CheckIndirectOffset_m9EC00EFA29171D9BCB9D6CF2514D6C0C38E1385A (void);
extern void Verifier_CheckBufferFromStart_m30D3F098F0258380253601997A05C0C0E55569F4 (void);
extern void Verifier_GetIndirectOffset_mD4ABA15561C86526A192B328F49DBFBFF7BA7EBB (void);
extern void Verifier_VerifyTableStart_m59CE4D2B5F4D49AEF99EB014DFC079FC6F068434 (void);
extern void Verifier_VerifyTableEnd_m2C2757CE64B2651A96C0E0064503BA7C09E6ADB3 (void);
extern void Verifier_VerifyField_m8A9C01B059B20EAD3C2A4333216302312FCF2163 (void);
extern void Verifier_VerifyString_m7B29C44FC2B8366930C88194E580DCA5CF202175 (void);
extern void Verifier_VerifyVectorOfData_m35B995B5DC3DD8BB45C3DA30223C1CDD6FBFC79F (void);
extern void Verifier_VerifyVectorOfStrings_m2425C566B672088FD5A52C252B32781A3720FBEF (void);
extern void Verifier_VerifyVectorOfTables_mA744774DCAAF3279E0BF4C17F3156EE9CAFDBF25 (void);
extern void Verifier_VerifyTable_m7858D788213DE336FA2AFD350195CE9F2746DAAF (void);
extern void Verifier_VerifyUnion_m28185E914E664B188CB475970C126B9F72668D88 (void);
extern void Verifier_VerifyBuffer_m3DB092B9C098476FF3A74E39671C11A2FF4ABAFE (void);
extern void StringOffset__ctor_m52D945D231E3E3700A8C667AB97AE4D0A10A27BB (void);
extern void VectorOffset__ctor_mACFDC02BEA6B5322F5E78EA74CE311D3D313B736 (void);
extern void Struct_get_bb_pos_m0DF50405A8A0FA6FCFB3FEAFCB5F7E887E3C53DB (void);
extern void Struct_set_bb_pos_mD303E870401EB784D89A1785AEA7441708DABDEB (void);
extern void Struct_get_bb_m023CCFFF470E9BBBFC4B198B873601E6E8ED3B7E (void);
extern void Struct_set_bb_mE86F490DC850F169136E30896C865534FB72248D (void);
extern void Struct__ctor_m2B636C66F813B59582DB736FD6BCC25E84B021E3 (void);
extern void Table_get_bb_pos_m384022E2F07A268997C4AD3BF8344A8CE72E71C6 (void);
extern void Table_set_bb_pos_m7ECC7E46BB126F475011359342D37F4703F9BA14 (void);
extern void Table_get_bb_mA038E6852549861A3E7346E88A0AE51EF06EF928 (void);
extern void Table_set_bb_m01B9D524A5880128E16440DCD5D7992325FBD71C (void);
extern void Table__ctor_m0FAB6920DFC3116EC9552DD7237AB6E84BA5CA31 (void);
extern void Table___offset_mD41A3FDF5139E05F3EA1BB70EED7C48D593979D6 (void);
extern void Table___indirect_mB18E00AFF82B4E55117FE556350A8D99ABD565C1 (void);
extern void Table___indirect_m758EB41423E5A20EC6F796196BE58FE1226C7913 (void);
extern void Table___string_mFB43D8816C093699854F13DC421C3DD6E78BEAF3 (void);
extern void Table___vector_len_m3B0E26D29F11FD1CF988A59732A1B33D1DAE393D (void);
extern void Table___vector_m7F98F7FF9A2160084141781A48E338392BF4E780 (void);
static Il2CppMethodPointer s_methodPointers[190] = 
{
	NULL,
	NULL,
	ByteBufferAllocator_get_Length_mC038E63A664724F0965D2B7B57899F1BDB1DCE2A,
	ByteBufferAllocator_set_Length_m17C45CA27C651A79001AED91E6AAA13B575F38C9,
	NULL,
	NULL,
	ByteBufferAllocator__ctor_mA20B60936A23DFD8E7D134C8C522AB3916B91772,
	ByteArrayAllocator__ctor_m64D6FFED315D68A28E03F3C7B0F57248603C0AA8,
	ByteArrayAllocator_Reset_m9492B10C7418A95DEA296A15064D133674A0E823,
	ByteArrayAllocator_GrowFront_mFD75F8C69430AD2566AA13EC8885AFEAD545633C,
	ByteArrayAllocator_get_Span_mA4B4F233538BFD59293E6742D1E3A09BD2F0F134,
	ByteArrayAllocator_get_ReadOnlySpan_m2FA5A60F56587D45370CB9F32182AD92E54F7AB0,
	ByteArrayAllocator_InitBuffer_m3A5840E5A780906C338F6D7A4F8D5612401B698A,
	ByteBuffer__ctor_mC76B5A5C27523545664570AAD1C04B663C1C503A,
	ByteBuffer__ctor_m4B87CF31CBB963E34E96F16585FCA444DF949921,
	ByteBuffer__ctor_mEE1915E69AC2062777C33B4393DDC957749C9FF5,
	ByteBuffer_get_Position_mAEC84EDE8EECB180F1904E4E10DFE6BF923A3F8D,
	ByteBuffer_set_Position_m0F1BC982C7D846D2F73A08D04F66D255746FCB20,
	ByteBuffer_get_Length_mD83481CCE00F09128DBAC206A094C628E635BA8B,
	ByteBuffer_Reset_mC9313064E9ABBF21238DD1581864FF7621F343FE,
	ByteBuffer_Reset_m1750E2DCC8E1C8B18CCA921D876B4A70D7035F03,
	ByteBuffer_GrowFront_m0DE57B8E020B3948327E205840F49809DA043F30,
	ByteBuffer_ToArray_mF1209C898F0DFF95A6C84A6218886D10808CB379,
	NULL,
	NULL,
	NULL,
	NULL,
	ByteBuffer_ToSpan_m00B8ED7F4A80E5C7A3BF6F740394DC2B5ED6274E,
	ByteBuffer_ReverseBytes_m36121CFC834DEE45DB23F98CA3864D14229D6DAC,
	ByteBuffer_ReverseBytes_mB15365C2362720EE11ACA5BE0B00B26D4CDA3DC0,
	ByteBuffer_AssertOffsetAndLength_m7DE914630999CC751A304662915A714328B53AC7,
	ByteBuffer_PutSbyte_mC6055AB467538627CB2EEA9050DA94A819EA5148,
	ByteBuffer_PutByte_m81927B710F29CBE2EFB0E5F298398AB7D0D26967,
	ByteBuffer_PutByte_mA27E6385D3CF1624F3A44D01275E631FC4F87B2E,
	ByteBuffer_PutStringUTF8_m8A9FB9D67D35A58029657802ACA2C25A64BADAA3,
	ByteBuffer_PutShort_m40945E04907D64971273BA0CA8C483FEAE91F451,
	ByteBuffer_PutUshort_m3FAF834DEE05A58095F7851DEA351C8AAC6B09C4,
	ByteBuffer_PutInt_mF305D6CF0623CB47E43C29A1CC08BFF7D3214C69,
	ByteBuffer_PutUint_m4FE24EA7B76E8405CD82B65841609BBBAF6C9F4C,
	ByteBuffer_PutLong_mD69C4E9BF0355C81EB6B8829BF85161B95B02A2B,
	ByteBuffer_PutUlong_m0E9B506A058766F3BB5D8BF01B10AFE695CC6C9F,
	ByteBuffer_PutFloat_m3BD6261E6C39B5B3F51DDF8412D26901024AC600,
	ByteBuffer_PutDouble_mFDF587433B6198987FAF1AD26DF320F9BCA41A49,
	ByteBuffer_GetSbyte_mC5F6FC81C592D6FDFDF41EE3EA719EEA2234295B,
	ByteBuffer_Get_m4DACAF12CF9F83591DF6B595512714C893377FDB,
	ByteBuffer_GetStringUTF8_m54FDA99A7BCE5B71C55BCE816CE73854FFFB0EAF,
	ByteBuffer_GetShort_m3ED68273C4B0ABA97718B68F439D042C758D084B,
	ByteBuffer_GetUshort_m99CBB64DF8F7D844B0F6FE18671922DF28D5DBDD,
	ByteBuffer_GetInt_mD2F94B1EFC4E1B48E6A25E9FE27A36CB691F93C7,
	ByteBuffer_GetUint_m7686A15FBE8934D1AE054CDE1D18F43BA59EBE6D,
	ByteBuffer_GetLong_m0F4A58CC22C3E0FA3B0B2FD93CC45345E9FB1059,
	ByteBuffer_GetUlong_m5960AA0201977D5CBB9E711EB7A912E0A986F549,
	ByteBuffer_GetFloat_m24BCA222E9470DEFB7AE68B7DF3800B187DE4AD6,
	ByteBuffer_GetDouble_m30822FB271051CFEC593734ED7B4E71EBD61C018,
	NULL,
	NULL,
	ByteBuffer__cctor_m4200E985A77D02E2BB383C7B87CB5A607CD130D4,
	FlatBufferBuilder__ctor_mD9C52676666C70EDB5CB7C7A9E3BA3CD07961796,
	FlatBufferBuilder_Clear_mEF930BED718919B45BF5CCDDC7028D3D851FC7A4,
	FlatBufferBuilder_get_ForceDefaults_mA0FE8A741C81C5945948228ED35D4F7E3BECC960,
	FlatBufferBuilder_get_Offset_mC74D55C982A3866A2171140BFFEF663306288AE9,
	FlatBufferBuilder_Pad_m45B94144FC69144F0D1E1D10A7012FEA5393F964,
	FlatBufferBuilder_GrowBuffer_m880D7E362F3C37DF5D2832D6554A8AEEE73E5367,
	FlatBufferBuilder_Prep_m3DA1C5F1967E6C9DE1F991519E1F87EE95C35AB7,
	FlatBufferBuilder_PutBool_m9DFECA7733C9F313CBB77F2EA3D2C16A6CC4D925,
	FlatBufferBuilder_PutSbyte_m4B1681B63002727EF1ACEB361FFD075CBC35EA8C,
	FlatBufferBuilder_PutByte_mEA2C1C8F64D64C81C35C1D51D33423CF608A60F3,
	FlatBufferBuilder_PutShort_mE92EC85294499D0DC414F376099F73C267C82D0E,
	FlatBufferBuilder_PutUshort_mA897A7DC14B66A18334B2948B58F918B1FFFF915,
	FlatBufferBuilder_PutInt_m3657C36B7F37D77B13786E341A742332A942C07B,
	FlatBufferBuilder_PutUint_mE4C61BE169A0CEF7FDD69C461DA07A200B5A2F6A,
	FlatBufferBuilder_PutLong_m69929C60C71F08604C3E428A3C3C4F047CEF6F1A,
	FlatBufferBuilder_PutUlong_m38DBCF4668E499C51EB30003652730076F100680,
	FlatBufferBuilder_PutFloat_m4BF4065EA873C657DFC67B301F0AE81AF8A6AB5D,
	NULL,
	NULL,
	FlatBufferBuilder_PutDouble_mD51D07A38BABE4EA8EB9689D8D5536B97851981D,
	FlatBufferBuilder_AddBool_m478C50A00AA0FFFC9A0551F1B14688D00E654D67,
	FlatBufferBuilder_AddSbyte_m8205DD96B17E57ECA93B00E0CF9941423E635C29,
	FlatBufferBuilder_AddByte_m09B496156A95861FDF7841640C5C1719723A7036,
	FlatBufferBuilder_AddShort_mA240DA72E828B425EEF2128E7933F8E9696F5075,
	FlatBufferBuilder_AddUshort_mFBED2384AD4CFA68C46F725B9342FA4F8AB02573,
	FlatBufferBuilder_AddInt_mC7ED89CD9BAD7A1F34E445DDFF066513AA2B926E,
	FlatBufferBuilder_AddUint_m5E52C37A1B3FF5FEF363B603C8E379847EBF0FF9,
	FlatBufferBuilder_AddLong_m7941C58219E84656250A86B5B70D5E3731753DB5,
	FlatBufferBuilder_AddUlong_mD667451BC4F21D55EB19276E56C538BABCB0C9A3,
	FlatBufferBuilder_AddFloat_m631138283BEE1A25D70D62629445315F699D2C4D,
	NULL,
	NULL,
	NULL,
	FlatBufferBuilder_AddDouble_mF109F698FFEF929DE54342B6A78D58F791DA7E5D,
	FlatBufferBuilder_AddOffset_m7A64D677E7BE356B284C1DD84A7FD6E8C7DDCA27,
	FlatBufferBuilder_StartVector_m6150568905670346B15955DDA02E5A09EB816E8D,
	FlatBufferBuilder_EndVector_mB83D1FC66B112D7A125563866327564C1DAF0DC7,
	NULL,
	FlatBufferBuilder_Nested_m45AE882A96E0B4309916DD3095168360CBFAE397,
	FlatBufferBuilder_NotNested_m550AD28D180BF3E1C547BB535382BCE59B9BADB2,
	FlatBufferBuilder_StartTable_m9EB86E88900E2C9419CFF54C05518FA2C89121ED,
	FlatBufferBuilder_Slot_mCF8F59D561C30C152A31A9754AA66D7A4E205B46,
	FlatBufferBuilder_AddBool_m5D0F7FD6399AB4D0990CF2596D4FD15400DF61B6,
	FlatBufferBuilder_AddSbyte_mD18D3513DBC378A3887544D238D14BE9DC83732F,
	FlatBufferBuilder_AddByte_mA2FA2D184EF297FE203000FC60EEF2B055D26F0F,
	FlatBufferBuilder_AddShort_m11792B784B3D896795B2E2FE4769379E915B9089,
	FlatBufferBuilder_AddUshort_m0654CFED47923CA687388B503D8D397DC650A142,
	FlatBufferBuilder_AddInt_mCBF834EC2450E9210F91EAB87A2B825F09F08BED,
	FlatBufferBuilder_AddUint_m311E75686339879CD43C2AA8B764B92E198A98AE,
	FlatBufferBuilder_AddLong_mD6BCBC1C0CEE0C60F862183C5E7B27936BFEC624,
	FlatBufferBuilder_AddUlong_m755B6E69F62BC6D085B3C341B1A6044BC9E4265A,
	FlatBufferBuilder_AddFloat_m47B8DC15B8788B1ADED0CD59E8DEA8C017ADCDEA,
	FlatBufferBuilder_AddDouble_mD10E6A16BD9702A5F7FDA2228C3633A7B828A06B,
	FlatBufferBuilder_AddOffset_mF017678579F65CB1B835E7A9451144D0C656086D,
	FlatBufferBuilder_CreateString_m1A7788EF2664F6241243D3F1EB6973BBCC511F00,
	FlatBufferBuilder_AddStruct_mCAD7F655DB90127B030826FF467F8A17678E4DDC,
	FlatBufferBuilder_EndTable_m176D804DB1A019B3AF4735EBE7FECDD766E4CA73,
	FlatBufferBuilder_Finish_m356784F295B0938131FC67FE20FA7D3A2E91D42E,
	FlatBufferBuilder_Finish_mAE4C42C9B9F79AF0106923368D952B1160A46F6D,
	FlatBufferBuilder_FinishSizePrefixed_m46F76A58F13D0AFA203806ED216CB8D23B7D8F72,
	FlatBufferBuilder_get_DataBuffer_mF063BD391805D53655476AB674FDFCD9880900D7,
	FlatBufferConstants_FLATBUFFERS_23_5_26_m157E82FCD8C5BC7B96196D3EF099C0F9845203EC,
	NULL,
	NULL,
	NULL,
	FlatBuffersWarpObject__ctor_mB3653C6F6B9FE6D19F0373D08F7D25CF2AF69608,
	Options__ctor_m443C4957B9712416B855C1E610E23CCEC9756DB7,
	Options_get_maxDepth_m437B08A483A59E4600A3A2B6FE3A26E3EA8B2212,
	Options_get_maxTables_mE2E55C408DAE7EB5059F6169F471ED14B93F2D40,
	Options_get_stringEndCheck_mB9217751DB5C7D10C8EDB209FC945402821A1339,
	Options_get_alignmentCheck_mC94E7A871FC44FE7505713CD0E69A9FE2BFD4EEA,
	VerifyTableAction__ctor_m7AEEAD7ED5C1ABF23BE481BA49D8BA704E421456,
	VerifyTableAction_Invoke_mDD4DF94C57F6A57A87F19A8915ACD085F65FF6D4,
	VerifyUnionAction__ctor_m8FE595B1CA4BF89B3520BBD1554A4FCF98D8254B,
	VerifyUnionAction_Invoke_mF8A06007D2423423954038A65C411972E1DF6B3B,
	Verifier__ctor_mFF28952281B211ADE655D82536791DEFC321B649,
	Verifier_get_options_m5D0BEA9EBE4645B980E3066F2E42B042943D87E9,
	Verifier_get_depth_m4A917A65B04FAC0E7374D664A28B7BFC089AA74E,
	Verifier_set_depth_m996F6606AC8B244E1D3AB94C5C32C1934B47A7C7,
	Verifier_get_numTables_mD6123A14045007BAE82B1770EB9201594F778973,
	Verifier_set_numTables_mFD577C7B868B6ACE51DCAFDBE879183BBE9B08B4,
	Verifier_BufferHasIdentifier_m47A5B9E1944079E3B21B67886594DD78A79F9F76,
	Verifier_ReadUOffsetT_mFA082679C617C5C8424CDDFE3965438AB7DE85B1,
	Verifier_ReadSOffsetT_m5A43060F831A70DD3ECDFA632C079B51B4C15EBE,
	Verifier_ReadVOffsetT_m3F7EA721035EA1275A5536FA2965DC30FB246B0F,
	Verifier_GetVRelOffset_mF26D54B2378F87DEA0C42285F40ECB98B05D2F14,
	Verifier_GetVOffset_m75F1130DAE55F0EE4BDFE43737E53113BF25FD8F,
	Verifier_CheckComplexity_m154BA6645F54D28747BCF950D4371C748BC4991D,
	Verifier_CheckAlignment_m967E1965E15845B66E5454A303C51D46DE3FEDF5,
	Verifier_CheckElement_m2CF35C61F826AE256B69A5EEA5719190AF474680,
	Verifier_CheckScalar_m9799566886A1AC4D9A2A82F5093B0BA1FC8BF735,
	Verifier_CheckVectorOrString_m84A7DB3D433AC14911F20DB712CF3BB178A37FB8,
	Verifier_CheckString_m77D796D84E00F7EE4ED3B35A183B80C54C610241,
	Verifier_CheckVector_m53F4531381F8067BDFEF05CE7EE4DCC55E7D4DC9,
	Verifier_CheckTable_mBD169E08464C583974B9759AE59A7FF29E2F8691,
	Verifier_CheckStringFunc_mA69C648CF9A9C67C33D1FAA25111ECE5EB1EE237,
	Verifier_CheckVectorOfObjects_mF84993CB74FF40A3AFA1AF020D677A7CBF4A9E6D,
	Verifier_CheckIndirectOffset_m9EC00EFA29171D9BCB9D6CF2514D6C0C38E1385A,
	Verifier_CheckBufferFromStart_m30D3F098F0258380253601997A05C0C0E55569F4,
	Verifier_GetIndirectOffset_mD4ABA15561C86526A192B328F49DBFBFF7BA7EBB,
	Verifier_VerifyTableStart_m59CE4D2B5F4D49AEF99EB014DFC079FC6F068434,
	Verifier_VerifyTableEnd_m2C2757CE64B2651A96C0E0064503BA7C09E6ADB3,
	Verifier_VerifyField_m8A9C01B059B20EAD3C2A4333216302312FCF2163,
	Verifier_VerifyString_m7B29C44FC2B8366930C88194E580DCA5CF202175,
	Verifier_VerifyVectorOfData_m35B995B5DC3DD8BB45C3DA30223C1CDD6FBFC79F,
	Verifier_VerifyVectorOfStrings_m2425C566B672088FD5A52C252B32781A3720FBEF,
	Verifier_VerifyVectorOfTables_mA744774DCAAF3279E0BF4C17F3156EE9CAFDBF25,
	Verifier_VerifyTable_m7858D788213DE336FA2AFD350195CE9F2746DAAF,
	Verifier_VerifyUnion_m28185E914E664B188CB475970C126B9F72668D88,
	Verifier_VerifyBuffer_m3DB092B9C098476FF3A74E39671C11A2FF4ABAFE,
	NULL,
	NULL,
	StringOffset__ctor_m52D945D231E3E3700A8C667AB97AE4D0A10A27BB,
	VectorOffset__ctor_mACFDC02BEA6B5322F5E78EA74CE311D3D313B736,
	Struct_get_bb_pos_m0DF50405A8A0FA6FCFB3FEAFCB5F7E887E3C53DB,
	Struct_set_bb_pos_mD303E870401EB784D89A1785AEA7441708DABDEB,
	Struct_get_bb_m023CCFFF470E9BBBFC4B198B873601E6E8ED3B7E,
	Struct_set_bb_mE86F490DC850F169136E30896C865534FB72248D,
	Struct__ctor_m2B636C66F813B59582DB736FD6BCC25E84B021E3,
	Table_get_bb_pos_m384022E2F07A268997C4AD3BF8344A8CE72E71C6,
	Table_set_bb_pos_m7ECC7E46BB126F475011359342D37F4703F9BA14,
	Table_get_bb_mA038E6852549861A3E7346E88A0AE51EF06EF928,
	Table_set_bb_m01B9D524A5880128E16440DCD5D7992325FBD71C,
	Table__ctor_m0FAB6920DFC3116EC9552DD7237AB6E84BA5CA31,
	Table___offset_mD41A3FDF5139E05F3EA1BB70EED7C48D593979D6,
	Table___indirect_mB18E00AFF82B4E55117FE556350A8D99ABD565C1,
	Table___indirect_m758EB41423E5A20EC6F796196BE58FE1226C7913,
	Table___string_mFB43D8816C093699854F13DC421C3DD6E78BEAF3,
	Table___vector_len_m3B0E26D29F11FD1CF988A59732A1B33D1DAE393D,
	Table___vector_m7F98F7FF9A2160084141781A48E338392BF4E780,
	NULL,
	NULL,
	NULL,
};
extern void StringOffset__ctor_m52D945D231E3E3700A8C667AB97AE4D0A10A27BB_AdjustorThunk (void);
extern void VectorOffset__ctor_mACFDC02BEA6B5322F5E78EA74CE311D3D313B736_AdjustorThunk (void);
extern void Struct_get_bb_pos_m0DF50405A8A0FA6FCFB3FEAFCB5F7E887E3C53DB_AdjustorThunk (void);
extern void Struct_set_bb_pos_mD303E870401EB784D89A1785AEA7441708DABDEB_AdjustorThunk (void);
extern void Struct_get_bb_m023CCFFF470E9BBBFC4B198B873601E6E8ED3B7E_AdjustorThunk (void);
extern void Struct_set_bb_mE86F490DC850F169136E30896C865534FB72248D_AdjustorThunk (void);
extern void Struct__ctor_m2B636C66F813B59582DB736FD6BCC25E84B021E3_AdjustorThunk (void);
extern void Table_get_bb_pos_m384022E2F07A268997C4AD3BF8344A8CE72E71C6_AdjustorThunk (void);
extern void Table_set_bb_pos_m7ECC7E46BB126F475011359342D37F4703F9BA14_AdjustorThunk (void);
extern void Table_get_bb_mA038E6852549861A3E7346E88A0AE51EF06EF928_AdjustorThunk (void);
extern void Table_set_bb_m01B9D524A5880128E16440DCD5D7992325FBD71C_AdjustorThunk (void);
extern void Table__ctor_m0FAB6920DFC3116EC9552DD7237AB6E84BA5CA31_AdjustorThunk (void);
extern void Table___offset_mD41A3FDF5139E05F3EA1BB70EED7C48D593979D6_AdjustorThunk (void);
extern void Table___indirect_mB18E00AFF82B4E55117FE556350A8D99ABD565C1_AdjustorThunk (void);
extern void Table___string_mFB43D8816C093699854F13DC421C3DD6E78BEAF3_AdjustorThunk (void);
extern void Table___vector_len_m3B0E26D29F11FD1CF988A59732A1B33D1DAE393D_AdjustorThunk (void);
extern void Table___vector_m7F98F7FF9A2160084141781A48E338392BF4E780_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[17] = 
{
	{ 0x060000AA, StringOffset__ctor_m52D945D231E3E3700A8C667AB97AE4D0A10A27BB_AdjustorThunk },
	{ 0x060000AB, VectorOffset__ctor_mACFDC02BEA6B5322F5E78EA74CE311D3D313B736_AdjustorThunk },
	{ 0x060000AC, Struct_get_bb_pos_m0DF50405A8A0FA6FCFB3FEAFCB5F7E887E3C53DB_AdjustorThunk },
	{ 0x060000AD, Struct_set_bb_pos_mD303E870401EB784D89A1785AEA7441708DABDEB_AdjustorThunk },
	{ 0x060000AE, Struct_get_bb_m023CCFFF470E9BBBFC4B198B873601E6E8ED3B7E_AdjustorThunk },
	{ 0x060000AF, Struct_set_bb_mE86F490DC850F169136E30896C865534FB72248D_AdjustorThunk },
	{ 0x060000B0, Struct__ctor_m2B636C66F813B59582DB736FD6BCC25E84B021E3_AdjustorThunk },
	{ 0x060000B1, Table_get_bb_pos_m384022E2F07A268997C4AD3BF8344A8CE72E71C6_AdjustorThunk },
	{ 0x060000B2, Table_set_bb_pos_m7ECC7E46BB126F475011359342D37F4703F9BA14_AdjustorThunk },
	{ 0x060000B3, Table_get_bb_mA038E6852549861A3E7346E88A0AE51EF06EF928_AdjustorThunk },
	{ 0x060000B4, Table_set_bb_m01B9D524A5880128E16440DCD5D7992325FBD71C_AdjustorThunk },
	{ 0x060000B5, Table__ctor_m0FAB6920DFC3116EC9552DD7237AB6E84BA5CA31_AdjustorThunk },
	{ 0x060000B6, Table___offset_mD41A3FDF5139E05F3EA1BB70EED7C48D593979D6_AdjustorThunk },
	{ 0x060000B7, Table___indirect_mB18E00AFF82B4E55117FE556350A8D99ABD565C1_AdjustorThunk },
	{ 0x060000B9, Table___string_mFB43D8816C093699854F13DC421C3DD6E78BEAF3_AdjustorThunk },
	{ 0x060000BA, Table___vector_len_m3B0E26D29F11FD1CF988A59732A1B33D1DAE393D_AdjustorThunk },
	{ 0x060000BB, Table___vector_m7F98F7FF9A2160084141781A48E338392BF4E780_AdjustorThunk },
};
static const int32_t s_InvokerIndices[190] = 
{
	0,
	0,
	12996,
	10629,
	0,
	0,
	13298,
	10682,
	9945,
	10629,
	12690,
	12645,
	13298,
	10629,
	10682,
	5681,
	12996,
	10629,
	12996,
	13298,
	9945,
	10629,
	4468,
	0,
	0,
	0,
	0,
	3179,
	20748,
	20780,
	5266,
	5364,
	5184,
	2627,
	5309,
	5264,
	5422,
	5266,
	5423,
	5267,
	5424,
	5375,
	5211,
	9438,
	7685,
	4468,
	8270,
	9554,
	8801,
	9562,
	9157,
	9569,
	9456,
	8183,
	0,
	0,
	21355,
	10682,
	13298,
	12815,
	12996,
	10629,
	13298,
	5266,
	10442,
	10807,
	10442,
	10627,
	10890,
	10629,
	10891,
	10630,
	10892,
	10823,
	0,
	0,
	10506,
	10442,
	10807,
	10442,
	10627,
	10890,
	10629,
	10891,
	10630,
	10892,
	10823,
	0,
	0,
	0,
	10506,
	10629,
	2642,
	13288,
	0,
	10629,
	13298,
	10629,
	10629,
	2626,
	2663,
	2626,
	2634,
	2670,
	2642,
	2673,
	2654,
	2674,
	2664,
	2630,
	2642,
	9474,
	2642,
	12996,
	5184,
	10629,
	10629,
	13052,
	21355,
	0,
	0,
	0,
	13298,
	13298,
	12996,
	12996,
	12815,
	12815,
	5684,
	3516,
	5684,
	2166,
	5688,
	13052,
	12996,
	10629,
	12996,
	10629,
	2196,
	4639,
	4159,
	3907,
	3905,
	4642,
	12815,
	3630,
	3630,
	3630,
	5904,
	7892,
	3630,
	3627,
	3516,
	3627,
	7892,
	2196,
	9566,
	7892,
	7892,
	515,
	2233,
	1030,
	2233,
	1029,
	1029,
	514,
	2165,
	0,
	0,
	10629,
	10629,
	12996,
	10629,
	13052,
	10682,
	5309,
	12996,
	10629,
	13052,
	10682,
	5309,
	8801,
	8801,
	17824,
	9267,
	8801,
	8801,
	0,
	0,
	0,
};
static const Il2CppTokenRangePair s_rgctxIndices[16] = 
{
	{ 0x0200000F, { 46, 1 } },
	{ 0x06000018, { 0, 1 } },
	{ 0x06000019, { 1, 1 } },
	{ 0x0600001A, { 2, 4 } },
	{ 0x0600001B, { 6, 6 } },
	{ 0x06000037, { 12, 11 } },
	{ 0x06000038, { 23, 2 } },
	{ 0x0600004B, { 25, 2 } },
	{ 0x0600004C, { 27, 1 } },
	{ 0x06000058, { 28, 4 } },
	{ 0x06000059, { 32, 9 } },
	{ 0x0600005A, { 41, 3 } },
	{ 0x0600005F, { 44, 2 } },
	{ 0x060000BC, { 47, 2 } },
	{ 0x060000BD, { 49, 2 } },
	{ 0x060000BE, { 51, 3 } },
};
extern const uint32_t g_rgctx_T_t0C766D8F65C661DBC00067084B0649222D4F3829;
extern const uint32_t g_rgctx_T_tD449F9FB7DAC2C9111522B5DA3F7F1A359AE5CDD;
extern const uint32_t g_rgctx_ByteBuffer_SizeOf_TisT_t7BC16398A420E7089C1B065032E1EC5EE1B51AC6_m4D342203BB7D41E7C1C798A6632ED6D243A0BF84;
extern const uint32_t g_rgctx_ArraySegment_1_t61B2E8D6A01B4B5BC289A66E8F672460D7F43DC1;
extern const uint32_t g_rgctx_ArraySegment_1_get_Count_m8DA96E58B14BFE2B5B7490F9E076FC8F4CEA4694;
extern const uint32_t g_rgctx_ArraySegment_1_t61B2E8D6A01B4B5BC289A66E8F672460D7F43DC1;
extern const uint32_t g_rgctx_MemoryMarshal_Cast_TisByte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_TisT_tE885F39583E62FECF99987B1A75D3DCAE975C780_mB4719DFE7A8CD210F78CBD8EC214DE96437C7434;
extern const uint32_t g_rgctx_ReadOnlySpan_1_tE9B90EB59DFD85949CDBC9F7F33E33E1BB66BEC2;
extern const uint32_t g_rgctx_ReadOnlySpan_1_Slice_mC1B6D4B79D4EF50721CDBA821B237DF00B02450D;
extern const uint32_t g_rgctx_ReadOnlySpan_1_tE9B90EB59DFD85949CDBC9F7F33E33E1BB66BEC2;
extern const uint32_t g_rgctx_ReadOnlySpan_1_ToArray_m328BC7B3E909F54BF3666B1FA18870D2FF1D4220;
extern const uint32_t g_rgctx_TU5BU5D_t418C62AE295BA71FAD81D81D6975D46C6B3C18E1;
extern const uint32_t g_rgctx_ArraySegment_1_t1E4CCA461405BF7BAE8E4D41F65C92920D93E202;
extern const uint32_t g_rgctx_ArraySegment_1_Equals_m9549E89ED6FCB7AF50C5449A04A394771FB20C0C;
extern const uint32_t g_rgctx_ArraySegment_1_t1E4CCA461405BF7BAE8E4D41F65C92920D93E202;
extern const uint32_t g_rgctx_ArraySegment_1_get_Count_m9C27342FB85E87BBA712DF0A94B74BF7160F8BE9;
extern const uint32_t g_rgctx_ByteBuffer_IsSupportedType_TisT_t1643D2768CE4E20AAF5E621ECC2ED938FEA757B7_m5FF9538727F5B4182E6398FAB673AE33A66D8C3C;
extern const uint32_t g_rgctx_T_t1643D2768CE4E20AAF5E621ECC2ED938FEA757B7;
extern const uint32_t g_rgctx_ByteBuffer_ArraySize_TisT_t1643D2768CE4E20AAF5E621ECC2ED938FEA757B7_m8487E985D2314EC1EF2ACEFCD0DE9B748E5D05B2;
extern const uint32_t g_rgctx_Span_1_op_Implicit_mB493EAFAF84112CC1BF279F61C6309883C63F2E4;
extern const uint32_t g_rgctx_Span_1_t8E8283D299BD6082DC208C1BAE7A8B53D7BE7901;
extern const uint32_t g_rgctx_Span_1_t8E8283D299BD6082DC208C1BAE7A8B53D7BE7901;
extern const uint32_t g_rgctx_MemoryMarshal_Cast_TisT_t1643D2768CE4E20AAF5E621ECC2ED938FEA757B7_TisByte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_m5D87DE8D22E50C63DE4D5DC99F1895A76A96FA03;
extern const uint32_t g_rgctx_ByteBuffer_IsSupportedType_TisT_tD641006E2D35FB2527FF602E3A73288BFF4E0C2D_mE41E7DAFA82490B41ABE699C1FB0D4A509EC6026;
extern const uint32_t g_rgctx_T_tD641006E2D35FB2527FF602E3A73288BFF4E0C2D;
extern const uint32_t g_rgctx_ArraySegment_1_t3DB881A0A95907BC3E92808BD98991A91582FD64;
extern const uint32_t g_rgctx_ByteBuffer_Put_TisT_t6CF8FAB6DBB249E80949B70F41CCF6645AD6384F_m8E700A0A7DF93030C3E8F257783BA4A97E3BB524;
extern const uint32_t g_rgctx_ByteBuffer_Put_TisT_tD34402CDD6A94B9B110076DD58A62F881DA03267_mD79DF05BA73DE184E8C3AC9C3E4743CC8A943106;
extern const uint32_t g_rgctx_TU5BU5D_t99DC56BEE56E243D95A65EB099CEC5328B1E5BD3;
extern const uint32_t g_rgctx_ArraySegment_1_t4C359F88EAA860CFE3AA817016A6EEBEA324BE8C;
extern const uint32_t g_rgctx_ArraySegment_1__ctor_m7A2C5D09D02645D8DFB713523932EFEE5180DB0B;
extern const uint32_t g_rgctx_FlatBufferBuilder_Add_TisT_tBE317802533EB6C156A7957309916DBAB11B92CA_mCAC57FF9A94AD22788D992108DCEF31592B21F66;
extern const uint32_t g_rgctx_ArraySegment_1_t8C13363A18EE1F0C4AA7B6A76410B777A6ACDD4D;
extern const uint32_t g_rgctx_ArraySegment_1_op_Implicit_m38DDAD17504D980ECEA56BA6102B399BC61E3B37;
extern const uint32_t g_rgctx_ArraySegment_1_t8C13363A18EE1F0C4AA7B6A76410B777A6ACDD4D;
extern const uint32_t g_rgctx_TU5BU5D_t48646B1BE0350132C2D5D544DFB37C94403D1990;
extern const uint32_t g_rgctx_ArraySegment_1_op_Equality_mA9BC4D06FA72D9850DE9A16DEF02162E980A5B96;
extern const uint32_t g_rgctx_ArraySegment_1_get_Count_m3C324D41E8000FEB9A146DB09C57FAE1CAAE8D80;
extern const uint32_t g_rgctx_ByteBuffer_IsSupportedType_TisT_t3D34A58EC8E8C586BCB21FC0B2124E2A0D61811D_m85508F21960AD5CC83204EF37744DF9D94F46058;
extern const uint32_t g_rgctx_ByteBuffer_SizeOf_TisT_t3D34A58EC8E8C586BCB21FC0B2124E2A0D61811D_m1F15BEBFD6DC90D80ACE100311F196A4D02C8965;
extern const uint32_t g_rgctx_FlatBufferBuilder_Put_TisT_t3D34A58EC8E8C586BCB21FC0B2124E2A0D61811D_m7FB79240108BBDF99E80E2949648F90E322F525E;
extern const uint32_t g_rgctx_ByteBuffer_IsSupportedType_TisT_t8A1F3AF14DBB49CB04E60895217D2092E5223B25_mF64AE6A88847C6DC906D6A08C28F586DF03C092E;
extern const uint32_t g_rgctx_ByteBuffer_SizeOf_TisT_t8A1F3AF14DBB49CB04E60895217D2092E5223B25_m731F4639EA97372553074CEB6A24A5B0BB2D8468;
extern const uint32_t g_rgctx_FlatBufferBuilder_Put_TisT_t8A1F3AF14DBB49CB04E60895217D2092E5223B25_mDAC3B76C1A1078FB70D642D73F4FEE3F39D20B19;
extern const uint32_t g_rgctx_Offset_1U5BU5D_t0C300D970D96B2E4869BCF2AFD9A2D3C6CA481D7;
extern const uint32_t g_rgctx_Offset_1_t9EE847348F4E6984F459A4475059EB1C73DB5FC9;
extern const uint32_t g_rgctx_Offset_1_t8A5ECAB0F59F788C646A0E603C843D4628E9FA0F;
extern const uint32_t g_rgctx_Span_1_t1D474C6DC4F6A34C0E0D72B8079B3C4C3A29234C;
extern const uint32_t g_rgctx_MemoryMarshal_Cast_TisByte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_TisT_tDACAAC759020E3FF0C75A0DF805AC465CE7CE6A1_m0EA482AFA3FADA8EA1C68F5B6CB9337CE2F23DD7;
extern const uint32_t g_rgctx_ByteBuffer_ToArray_TisT_tB644B70F8E56078B723E2F4674CDB34E2C299CE3_m16AB6F54DDE3036730CCC4387CF7582C8439D5A0;
extern const uint32_t g_rgctx_TU5BU5D_tBC0E047F3CCFA0682C966766EC03870AD5E99239;
extern const uint32_t g_rgctx_Activator_CreateInstance_TisT_t8DC6FBF68E01CD55A6805C071124BA131CFA0A33_m7368C118ABD38DEB5CEB6E8A338A4CA7171793ED;
extern const uint32_t g_rgctx_T_t8DC6FBF68E01CD55A6805C071124BA131CFA0A33;
extern const Il2CppRGCTXConstrainedData g_rgctx_T_t8DC6FBF68E01CD55A6805C071124BA131CFA0A33_IFlatbufferObject___init_m28B7BE09E1258CA10132FDD0552043CBEB1F03AF;
static const Il2CppRGCTXDefinition s_rgctxValues[54] = 
{
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_t0C766D8F65C661DBC00067084B0649222D4F3829 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_tD449F9FB7DAC2C9111522B5DA3F7F1A359AE5CDD },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ByteBuffer_SizeOf_TisT_t7BC16398A420E7089C1B065032E1EC5EE1B51AC6_m4D342203BB7D41E7C1C798A6632ED6D243A0BF84 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ArraySegment_1_t61B2E8D6A01B4B5BC289A66E8F672460D7F43DC1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ArraySegment_1_get_Count_m8DA96E58B14BFE2B5B7490F9E076FC8F4CEA4694 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ArraySegment_1_t61B2E8D6A01B4B5BC289A66E8F672460D7F43DC1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MemoryMarshal_Cast_TisByte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_TisT_tE885F39583E62FECF99987B1A75D3DCAE975C780_mB4719DFE7A8CD210F78CBD8EC214DE96437C7434 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ReadOnlySpan_1_tE9B90EB59DFD85949CDBC9F7F33E33E1BB66BEC2 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ReadOnlySpan_1_Slice_mC1B6D4B79D4EF50721CDBA821B237DF00B02450D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ReadOnlySpan_1_tE9B90EB59DFD85949CDBC9F7F33E33E1BB66BEC2 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ReadOnlySpan_1_ToArray_m328BC7B3E909F54BF3666B1FA18870D2FF1D4220 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_t418C62AE295BA71FAD81D81D6975D46C6B3C18E1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ArraySegment_1_t1E4CCA461405BF7BAE8E4D41F65C92920D93E202 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ArraySegment_1_Equals_m9549E89ED6FCB7AF50C5449A04A394771FB20C0C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ArraySegment_1_t1E4CCA461405BF7BAE8E4D41F65C92920D93E202 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ArraySegment_1_get_Count_m9C27342FB85E87BBA712DF0A94B74BF7160F8BE9 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ByteBuffer_IsSupportedType_TisT_t1643D2768CE4E20AAF5E621ECC2ED938FEA757B7_m5FF9538727F5B4182E6398FAB673AE33A66D8C3C },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_t1643D2768CE4E20AAF5E621ECC2ED938FEA757B7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ByteBuffer_ArraySize_TisT_t1643D2768CE4E20AAF5E621ECC2ED938FEA757B7_m8487E985D2314EC1EF2ACEFCD0DE9B748E5D05B2 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Span_1_op_Implicit_mB493EAFAF84112CC1BF279F61C6309883C63F2E4 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Span_1_t8E8283D299BD6082DC208C1BAE7A8B53D7BE7901 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Span_1_t8E8283D299BD6082DC208C1BAE7A8B53D7BE7901 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MemoryMarshal_Cast_TisT_t1643D2768CE4E20AAF5E621ECC2ED938FEA757B7_TisByte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_m5D87DE8D22E50C63DE4D5DC99F1895A76A96FA03 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ByteBuffer_IsSupportedType_TisT_tD641006E2D35FB2527FF602E3A73288BFF4E0C2D_mE41E7DAFA82490B41ABE699C1FB0D4A509EC6026 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_tD641006E2D35FB2527FF602E3A73288BFF4E0C2D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ArraySegment_1_t3DB881A0A95907BC3E92808BD98991A91582FD64 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ByteBuffer_Put_TisT_t6CF8FAB6DBB249E80949B70F41CCF6645AD6384F_m8E700A0A7DF93030C3E8F257783BA4A97E3BB524 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ByteBuffer_Put_TisT_tD34402CDD6A94B9B110076DD58A62F881DA03267_mD79DF05BA73DE184E8C3AC9C3E4743CC8A943106 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_t99DC56BEE56E243D95A65EB099CEC5328B1E5BD3 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ArraySegment_1_t4C359F88EAA860CFE3AA817016A6EEBEA324BE8C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ArraySegment_1__ctor_m7A2C5D09D02645D8DFB713523932EFEE5180DB0B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FlatBufferBuilder_Add_TisT_tBE317802533EB6C156A7957309916DBAB11B92CA_mCAC57FF9A94AD22788D992108DCEF31592B21F66 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ArraySegment_1_t8C13363A18EE1F0C4AA7B6A76410B777A6ACDD4D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ArraySegment_1_op_Implicit_m38DDAD17504D980ECEA56BA6102B399BC61E3B37 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ArraySegment_1_t8C13363A18EE1F0C4AA7B6A76410B777A6ACDD4D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_t48646B1BE0350132C2D5D544DFB37C94403D1990 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ArraySegment_1_op_Equality_mA9BC4D06FA72D9850DE9A16DEF02162E980A5B96 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ArraySegment_1_get_Count_m3C324D41E8000FEB9A146DB09C57FAE1CAAE8D80 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ByteBuffer_IsSupportedType_TisT_t3D34A58EC8E8C586BCB21FC0B2124E2A0D61811D_m85508F21960AD5CC83204EF37744DF9D94F46058 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ByteBuffer_SizeOf_TisT_t3D34A58EC8E8C586BCB21FC0B2124E2A0D61811D_m1F15BEBFD6DC90D80ACE100311F196A4D02C8965 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FlatBufferBuilder_Put_TisT_t3D34A58EC8E8C586BCB21FC0B2124E2A0D61811D_m7FB79240108BBDF99E80E2949648F90E322F525E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ByteBuffer_IsSupportedType_TisT_t8A1F3AF14DBB49CB04E60895217D2092E5223B25_mF64AE6A88847C6DC906D6A08C28F586DF03C092E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ByteBuffer_SizeOf_TisT_t8A1F3AF14DBB49CB04E60895217D2092E5223B25_m731F4639EA97372553074CEB6A24A5B0BB2D8468 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FlatBufferBuilder_Put_TisT_t8A1F3AF14DBB49CB04E60895217D2092E5223B25_mDAC3B76C1A1078FB70D642D73F4FEE3F39D20B19 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Offset_1U5BU5D_t0C300D970D96B2E4869BCF2AFD9A2D3C6CA481D7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Offset_1_t9EE847348F4E6984F459A4475059EB1C73DB5FC9 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Offset_1_t8A5ECAB0F59F788C646A0E603C843D4628E9FA0F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Span_1_t1D474C6DC4F6A34C0E0D72B8079B3C4C3A29234C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MemoryMarshal_Cast_TisByte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_TisT_tDACAAC759020E3FF0C75A0DF805AC465CE7CE6A1_m0EA482AFA3FADA8EA1C68F5B6CB9337CE2F23DD7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ByteBuffer_ToArray_TisT_tB644B70F8E56078B723E2F4674CDB34E2C299CE3_m16AB6F54DDE3036730CCC4387CF7582C8439D5A0 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_tBC0E047F3CCFA0682C966766EC03870AD5E99239 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Activator_CreateInstance_TisT_t8DC6FBF68E01CD55A6805C071124BA131CFA0A33_m7368C118ABD38DEB5CEB6E8A338A4CA7171793ED },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t8DC6FBF68E01CD55A6805C071124BA131CFA0A33 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T_t8DC6FBF68E01CD55A6805C071124BA131CFA0A33_IFlatbufferObject___init_m28B7BE09E1258CA10132FDD0552043CBEB1F03AF },
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_Google_FlatBuffers_CodeGenModule;
const Il2CppCodeGenModule g_Google_FlatBuffers_CodeGenModule = 
{
	"Google.FlatBuffers.dll",
	190,
	s_methodPointers,
	17,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	16,
	s_rgctxIndices,
	54,
	s_rgctxValues,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};

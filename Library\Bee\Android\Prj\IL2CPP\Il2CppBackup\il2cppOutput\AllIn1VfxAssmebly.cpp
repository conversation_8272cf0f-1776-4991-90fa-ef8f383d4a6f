﻿#include "pch-cpp.hpp"

#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include <limits>


template <typename T1>
struct VirtualActionInvoker1
{
	typedef void (*Action)(void*, T1, const RuntimeMethod*);

	static inline void Invoke (Il2CppMethodSlot slot, RuntimeObject* obj, T1 p1)
	{
		const VirtualInvokeData& invokeData = il2cpp_codegen_get_virtual_invoke_data(slot, obj);
		((Action)invokeData.methodPtr)(obj, p1, invokeData.method);
	}
};
template <typename R>
struct VirtualFuncInvoker0
{
	typedef R (*Func)(void*, const RuntimeMethod*);

	static inline R Invoke (Il2CppMethodSlot slot, RuntimeObject* obj)
	{
		const VirtualInvokeData& invokeData = il2cpp_codegen_get_virtual_invoke_data(slot, obj);
		return ((Func)invokeData.methodPtr)(obj, invokeData.method);
	}
};

struct TweenRunner_1_t5BB0582F926E75E2FE795492679A6CF55A4B4BC4;
struct ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031;
struct CharU5BU5D_t799905CF001DD5F13F7DBB310181FC4D8B7D0AAB;
struct ColorU5BU5D_t612261CF293F6FFC3D80AB52259FF0DC2B2CC389;
struct TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB;
struct Vector2U5BU5D_tFEBBC94BCC6C9C88277BA04047D2B3FDB6ED7FDA;
struct All1VfxRandomTimeSeed_t67A13258B2C46B1B81032BB643724405898B5B57;
struct AllIn1GraphicMaterialDuplicate_tEC6847E85A67C8AFB79A06C3E271091CCD6B3AA0;
struct AllIn1LookAt_tDBAC078F7CE06BC8241E5429C930D8586A64D836;
struct AllIn1ParticleHelperComponent_tDFD2B3E7E4BE678952CD673F473DBEFEA569C0BB;
struct AllIn1ParticleHelperSO_tB1703DA97F077A501D1897ACE6C5AEE88C4A8061;
struct AllIn1VfxBounceAnimation_tCE51755C53049903743EC707AADB9B8B0019D86E;
struct AllIn1VfxComponent_t2587889FCB75E1748AEF4F9EAE574E8C3631BE54;
struct AllIn1VfxFakeLightDirSetter_t2DF709DA0A4395452D0BF247C1D247B3722FCC06;
struct AllIn1VfxScrollShaderProperty_tAC8F7ED4CC8304DBB58E1B23A2D2CE13EA22E07E;
struct AllIn1VfxScrollShaderTexture_t0B1CA0D34AF4808EA9BA40B9CDBA558936EB3103;
struct Binder_t91BFCE95A7057FADF4D8A1A342AFE52872246235;
struct Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184;
struct CancellationTokenSource_tAAE1E0033BCFC233801F8CB4CED5C852B350CB7B;
struct Canvas_t2DB4CEFDFF732884866C83F11ABF75F5AE8FFB26;
struct CanvasRenderer_tAB9A55A976C4E3B2B37D0CE5616E5685A8B43860;
struct Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3;
struct GameObject_t76FEDD663AB33C991A9C9A23129337651094216F;
struct Gradient_tA7FEBE2FDB4929FFF6C997134841046F713DAC1E;
struct Graphic_tCBFCA4585A19E2B75465AECFEAC43F4016BF7931;
struct Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3;
struct MaterialPropertyBlock_t2308669579033A857EFE6E4831909F638B27411D;
struct MemberFilter_tF644F1AE82F611B677CE1964D5A3277DDA21D553;
struct Mesh_t6D9C539763A09BC2B12AEAEF36F6DFFC98AE63D4;
struct MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71;
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C;
struct RectTransform_t6C5DA5E41A89E0F488B001E45E58963480E543A5;
struct Renderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF;
struct ScriptableObject_tB3BFDB921A1B1795B38A5417D3B97A89A140436A;
struct SetAllIn1VfxCustomGlobalTime_t77136D14EF3C885BA7985A17CC75E8BB9D4D9CAE;
struct Shader_tADC867D36B7876EE22427FAA2CE485105F4EE692;
struct String_t;
struct Texture2D_tE6505BC111DD8A424A9DBE8E05D7D09E11FFFCF4;
struct Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1;
struct Type_t;
struct UnityAction_t11A1F3B953B365C072A5DCC32677EE1796A962A7;
struct UnitySourceGeneratedAssemblyMonoScriptTypes_v1_t44342FB853B426918F6CDA6054309AA633E52ABF;
struct VertexHelper_tB905FCB02AE67CBEE5F265FE37A5938FC5D136FE;
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915;
struct CameraCallback_t844E527BFE37BC0495E7F67993E43C07642DA9DD;

IL2CPP_EXTERN_C RuntimeClass* Application_tDB03BE91CDF0ACA614A5E0B67CFB77C44EB19B21_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Debug_t8394C7EEAECA3689C2C9B9DE9C7166D73596276F_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* MaterialPropertyBlock_t2308669579033A857EFE6E4831909F638B27411D_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Math_tEB65DE7CA8B083C412C969C92981C030865486CE_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Shader_tADC867D36B7876EE22427FAA2CE485105F4EE692_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Texture2D_tE6505BC111DD8A424A9DBE8E05D7D09E11FFFCF4_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Type_t_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeField* U3CPrivateImplementationDetailsU3E_tBD303D34CFA3CA2694E763FA795BFE4D049C3B58____6B01616A8E2823C81992C86218274AA987C7176ACDA6F948FD86F07A1729CCB0_FieldInfo_var;
IL2CPP_EXTERN_C RuntimeField* U3CPrivateImplementationDetailsU3E_tBD303D34CFA3CA2694E763FA795BFE4D049C3B58____F46DB0FE475947149A3EFF971B0B57B34FB89FFD33EB98A59AFBB0D14A8B2544_FieldInfo_var;
IL2CPP_EXTERN_C String_t* _stringLiteral0023D6A9F7F3B566DFB2EFA5BE5820D9509D681E;
IL2CPP_EXTERN_C String_t* _stringLiteral0299CC5F40C577F300BB29854CBAAD8B68ABF5A0;
IL2CPP_EXTERN_C String_t* _stringLiteral0401A6C1F7012C721901C937730CA854AED44F14;
IL2CPP_EXTERN_C String_t* _stringLiteral040793655BC228982AF83F2DE9C015C189306364;
IL2CPP_EXTERN_C String_t* _stringLiteral1340C6E5B2B210689A25CF2270555B16E1489106;
IL2CPP_EXTERN_C String_t* _stringLiteral14D479CBF77090A6D30F543484D1D50B87795337;
IL2CPP_EXTERN_C String_t* _stringLiteral158697E57921300501C71DFA8626FCAE1F8FD030;
IL2CPP_EXTERN_C String_t* _stringLiteral1647B084BF73860206F4BB01E3237ED88F61B4BA;
IL2CPP_EXTERN_C String_t* _stringLiteral185035D897E40E37CE218ED2FFA2B3FD8F8F8F22;
IL2CPP_EXTERN_C String_t* _stringLiteral1E1912CAB55AF7DEF1C5B72F955FFFBCB9884AB5;
IL2CPP_EXTERN_C String_t* _stringLiteral1E74A2EC3C4B69C55D0D1B56F81D53F03FC58D57;
IL2CPP_EXTERN_C String_t* _stringLiteral2043A81282FBC38D068F48CE6B02508288E7F859;
IL2CPP_EXTERN_C String_t* _stringLiteral2F3FA2011635BA3ADF04F3A6636CEA5D2D14EF88;
IL2CPP_EXTERN_C String_t* _stringLiteral34AD56288A03AA8D7B7BE17E549C5FB602F9E885;
IL2CPP_EXTERN_C String_t* _stringLiteral3C1DB6BCE7F7EC4956D0CD51C602C4B9D94DE193;
IL2CPP_EXTERN_C String_t* _stringLiteral3F868CB06E969FC20ED35E84ACC75C8E94BE5789;
IL2CPP_EXTERN_C String_t* _stringLiteral40728BBCE4EE91640605FACC63DB3CEC63B83B80;
IL2CPP_EXTERN_C String_t* _stringLiteral4696BEB1B4DD525F1293813D16EC3A02B2B12251;
IL2CPP_EXTERN_C String_t* _stringLiteral46AFF93E738AD334DF787721BD7F7D0089E5D7AC;
IL2CPP_EXTERN_C String_t* _stringLiteral4A68E99ECA06FD65FDFD5FCD7FECC5839F4C0DBC;
IL2CPP_EXTERN_C String_t* _stringLiteral4AA79340AA7669BF821B747B748410DB52DA3261;
IL2CPP_EXTERN_C String_t* _stringLiteral4B8146FB95E4F51B29DA41EB5F6D60F8FD0ECF21;
IL2CPP_EXTERN_C String_t* _stringLiteral50639CAD49418C7B223CC529395C0E2A3892501C;
IL2CPP_EXTERN_C String_t* _stringLiteral51C68DEA8F259A907A0498E34875D1BD0A6CED03;
IL2CPP_EXTERN_C String_t* _stringLiteral5398DC3D4FFCD34741F382F596A262B6FA2922AC;
IL2CPP_EXTERN_C String_t* _stringLiteral561612A9F818B42EF04003F9D6952E8EC5D027ED;
IL2CPP_EXTERN_C String_t* _stringLiteral5904389432FCA9BFEA539A8A22DDC0BD69F94F04;
IL2CPP_EXTERN_C String_t* _stringLiteral5A02191D32DC069B431D3E54FF28CEC7767178DB;
IL2CPP_EXTERN_C String_t* _stringLiteral5CE72404582BDAE77C15BF3F30FEFFD1A81D8F8C;
IL2CPP_EXTERN_C String_t* _stringLiteral5D5BF7644F6756216DBAE69270F57FE11BEAE972;
IL2CPP_EXTERN_C String_t* _stringLiteral5F61F506633DBCEB100F2CA993128F6DC6A9C618;
IL2CPP_EXTERN_C String_t* _stringLiteral638A6BF6390D12422CAC4910C95F16CFBCE6D50B;
IL2CPP_EXTERN_C String_t* _stringLiteral6677C73BF64E77B045EA94D2AA385D7540F0A39D;
IL2CPP_EXTERN_C String_t* _stringLiteral6757D44A85F13AA2863BDC7DCEF5E30BC21278BD;
IL2CPP_EXTERN_C String_t* _stringLiteral68CB89848359D7BCEA0995C8FB01DAA1D5DFDE28;
IL2CPP_EXTERN_C String_t* _stringLiteral6B20C68293E633F1FCCB3BBD64B19DD052F5ED87;
IL2CPP_EXTERN_C String_t* _stringLiteral7281FF2F619273B6F998E3D3DCA0CFAF23CCFAD2;
IL2CPP_EXTERN_C String_t* _stringLiteral72A7CAD40240F38905C2C0E1E50F4449AD82AEAB;
IL2CPP_EXTERN_C String_t* _stringLiteral757FDB668BCAADD3B45A3175E6AC8EBACA3EEB65;
IL2CPP_EXTERN_C String_t* _stringLiteral76264918B150B6FD44125E9CE7F711A3689B9700;
IL2CPP_EXTERN_C String_t* _stringLiteral79BCB0C2B8C16448AD04D20C4925CF363A67BAA9;
IL2CPP_EXTERN_C String_t* _stringLiteral7D2ED17259CF0DC4179D682E4471BF85B5574BBA;
IL2CPP_EXTERN_C String_t* _stringLiteral7DDFF290B24173A5DC1BC9BC22C9322BB36CFC10;
IL2CPP_EXTERN_C String_t* _stringLiteral872DCAB5572E264E9E4EA514D7E835229090D6BC;
IL2CPP_EXTERN_C String_t* _stringLiteral89115C0E93F9302CD0B8CD7BB21C410B6162644D;
IL2CPP_EXTERN_C String_t* _stringLiteral8DED3C670AB3C2E5A20C926F89F96926BE24AC79;
IL2CPP_EXTERN_C String_t* _stringLiteral92274FFFE307A7AA40F70ECBD38BB73705AC9E5B;
IL2CPP_EXTERN_C String_t* _stringLiteral94BD673B8551A4C6D6A807ED9D7A6C37D921072F;
IL2CPP_EXTERN_C String_t* _stringLiteral94F92EDABB0744C4E72E030B935FEC2580C8A614;
IL2CPP_EXTERN_C String_t* _stringLiteral954CC189A0FC8B78E623F527148C0981714376AC;
IL2CPP_EXTERN_C String_t* _stringLiteral975A5F46FC6E6D8BC7943A3A38CEA489C122E4F1;
IL2CPP_EXTERN_C String_t* _stringLiteral99117A43311619936587FBCABCC9B16B687AB302;
IL2CPP_EXTERN_C String_t* _stringLiteral9A5093C3D376CC1E1CC7EEF2F6A221406781623A;
IL2CPP_EXTERN_C String_t* _stringLiteral9ABAD8FF849D104EA8DB7481A66BB4B9FD7143A2;
IL2CPP_EXTERN_C String_t* _stringLiteral9CE902BD3933F71AD381D3042D88DF18342E37C4;
IL2CPP_EXTERN_C String_t* _stringLiteralA0F4CF9D3B8B4AD6A49A888401B14AE51DD52E16;
IL2CPP_EXTERN_C String_t* _stringLiteralA27C6266A902DDCC5C73F82BEBBBDF1C87CCFFFA;
IL2CPP_EXTERN_C String_t* _stringLiteralA66067A208E75497516342A152D58B32B1C89075;
IL2CPP_EXTERN_C String_t* _stringLiteralA7A626DEA2521BA48EA03C7C5828601203370D81;
IL2CPP_EXTERN_C String_t* _stringLiteralA87819C2031146742C1F5350BC509988DACBE9F9;
IL2CPP_EXTERN_C String_t* _stringLiteralAAE3A15202D762AC5E5D99D35460A3E2C88307E1;
IL2CPP_EXTERN_C String_t* _stringLiteralB1D928ABA3C2555CCA12F60991D28C7F5A0E200E;
IL2CPP_EXTERN_C String_t* _stringLiteralB8649C06FE9FBAB8E997CBD8796167F6283CAB2C;
IL2CPP_EXTERN_C String_t* _stringLiteralBCBD8C7003675066255066C8241D1DCB43737145;
IL2CPP_EXTERN_C String_t* _stringLiteralC1321093811095513C44D23E1503BACF248356F0;
IL2CPP_EXTERN_C String_t* _stringLiteralC3A9DE289B76C73BE63D02B5A01D7C45B656AD49;
IL2CPP_EXTERN_C String_t* _stringLiteralC76190ED0C48EB995A11E863941095B1AA26B582;
IL2CPP_EXTERN_C String_t* _stringLiteralC827CF6C30E43507B780232E56A8ECC3A42FD702;
IL2CPP_EXTERN_C String_t* _stringLiteralC879E94E49560F1B236BDF1611C5EC619EA5B93F;
IL2CPP_EXTERN_C String_t* _stringLiteralCBE96480BEB47650A1397787D142CB9736546564;
IL2CPP_EXTERN_C String_t* _stringLiteralCEE91F51A391C3F771D9B2463C388312AA8DA8CF;
IL2CPP_EXTERN_C String_t* _stringLiteralCF6B5D4AFB7B21CFD9A2454BF9D1139B1B749D14;
IL2CPP_EXTERN_C String_t* _stringLiteralD01835DFD9412FEB7AA45A9F2E69029F2B71B936;
IL2CPP_EXTERN_C String_t* _stringLiteralD34B357F606D69B3A243155329F7C26E9ED9B03B;
IL2CPP_EXTERN_C String_t* _stringLiteralD53DF615DBAF7AA486744EFFCF7D2AB271BC7058;
IL2CPP_EXTERN_C String_t* _stringLiteralD678A75C242A16DA78744D87F52BD6BA550F95C4;
IL2CPP_EXTERN_C String_t* _stringLiteralDA39A3EE5E6B4B0D3255BFEF95601890AFD80709;
IL2CPP_EXTERN_C String_t* _stringLiteralDC639E8CFF8B48439F2DC546D026EE8EAB89718B;
IL2CPP_EXTERN_C String_t* _stringLiteralE8B1F4E65A0B35AB6619D979A27DD1766DEB7039;
IL2CPP_EXTERN_C String_t* _stringLiteralEAE96BC7C4AF88268A19A75CCE8F01ABB5A77AB1;
IL2CPP_EXTERN_C String_t* _stringLiteralEFF7EFBB29A0F779F9CF65D30804B3D60468618E;
IL2CPP_EXTERN_C String_t* _stringLiteralF3D5ADFD704DD9FB58F49F6670F4DAA9E634657F;
IL2CPP_EXTERN_C String_t* _stringLiteralF4B62A69FCAFBA03A81C4FD2F7CF77104D7CB48D;
IL2CPP_EXTERN_C String_t* _stringLiteralF5D8EF422ABD0284BA3EEB3BF58FBA9313575F4E;
IL2CPP_EXTERN_C String_t* _stringLiteralFD3081C211F1405167EBF5BDD775516383D38F4F;
IL2CPP_EXTERN_C const RuntimeMethod* Component_GetComponent_TisGraphic_tCBFCA4585A19E2B75465AECFEAC43F4016BF7931_mFE18E20FC92395F90E776DBC4CD214A4F2D97D90_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Component_GetComponent_TisRenderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF_mC91ACC92AD57CA6CA00991DAF1DB3830BCE07AF8_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeType* Shader_tADC867D36B7876EE22427FAA2CE485105F4EE692_0_0_0_var;
struct Gradient_tA7FEBE2FDB4929FFF6C997134841046F713DAC1E_marshaled_com;

struct ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031;
struct ColorU5BU5D_t612261CF293F6FFC3D80AB52259FF0DC2B2CC389;

IL2CPP_EXTERN_C_BEGIN
IL2CPP_EXTERN_C_END

#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
struct U3CModuleU3E_t637A9216F65DF96B664354B0280EEF43B9EC6A62 
{
};
struct U3CPrivateImplementationDetailsU3E_tBD303D34CFA3CA2694E763FA795BFE4D049C3B58  : public RuntimeObject
{
};
struct AllIn1VfxNoiseCreator_t69332FB6CDC3AB1FD4AB5EB8F52C8A541B46ABA7  : public RuntimeObject
{
};
struct MemberInfo_t  : public RuntimeObject
{
};
struct String_t  : public RuntimeObject
{
	int32_t ____stringLength;
	Il2CppChar ____firstChar;
};
struct UnitySourceGeneratedAssemblyMonoScriptTypes_v1_t44342FB853B426918F6CDA6054309AA633E52ABF  : public RuntimeObject
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F  : public RuntimeObject
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_pinvoke
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_com
{
};
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22 
{
	bool ___m_value;
};
struct Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3 
{
	uint8_t ___m_value;
};
struct Color_tD001788D726C3A7F1379BEED0260B9591F440C1F 
{
	float ___r;
	float ___g;
	float ___b;
	float ___a;
};
struct Double_tE150EF3D1D43DEE85D533810AB4C742307EEDE5F 
{
	double ___m_value;
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2  : public ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F
{
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2_marshaled_pinvoke
{
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2_marshaled_com
{
};
struct Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C 
{
	int32_t ___m_value;
};
struct IntPtr_t 
{
	void* ___m_value;
};
struct Single_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C 
{
	float ___m_value;
};
struct Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 
{
	float ___x;
	float ___y;
};
struct Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 
{
	float ___x;
	float ___y;
	float ___z;
};
struct Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 
{
	float ___x;
	float ___y;
	float ___z;
	float ___w;
};
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915 
{
	union
	{
		struct
		{
		};
		uint8_t Void_t4861ACF8F4594C3437BB48B6E56783494B843915__padding[1];
	};
};
#pragma pack(push, tp, 1)
struct __StaticArrayInitTypeSizeU3D561_tCC9CF2DEF97815A0687F7C880D90365D40399F24 
{
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D561_tCC9CF2DEF97815A0687F7C880D90365D40399F24__padding[561];
	};
};
#pragma pack(pop, tp)
#pragma pack(push, tp, 1)
struct __StaticArrayInitTypeSizeU3D817_tFF8FE8429977FFF978BB1C20A646908B58CDA50E 
{
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D817_tFF8FE8429977FFF978BB1C20A646908B58CDA50E__padding[817];
	};
};
#pragma pack(pop, tp)
struct MonoScriptData_t3949E822A7D170EBCC93AA6DAA8C9039CDD4B543 
{
	ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* ___FilePathsData;
	ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* ___TypesData;
	int32_t ___TotalTypes;
	int32_t ___TotalFiles;
	bool ___IsEditorOnly;
};
struct MonoScriptData_t3949E822A7D170EBCC93AA6DAA8C9039CDD4B543_marshaled_pinvoke
{
	Il2CppSafeArray* ___FilePathsData;
	Il2CppSafeArray* ___TypesData;
	int32_t ___TotalTypes;
	int32_t ___TotalFiles;
	int32_t ___IsEditorOnly;
};
struct MonoScriptData_t3949E822A7D170EBCC93AA6DAA8C9039CDD4B543_marshaled_com
{
	Il2CppSafeArray* ___FilePathsData;
	Il2CppSafeArray* ___TypesData;
	int32_t ___TotalTypes;
	int32_t ___TotalFiles;
	int32_t ___IsEditorOnly;
};
struct Gradient_tA7FEBE2FDB4929FFF6C997134841046F713DAC1E  : public RuntimeObject
{
	intptr_t ___m_Ptr;
};
struct Gradient_tA7FEBE2FDB4929FFF6C997134841046F713DAC1E_marshaled_pinvoke
{
	intptr_t ___m_Ptr;
};
struct Gradient_tA7FEBE2FDB4929FFF6C997134841046F713DAC1E_marshaled_com
{
	intptr_t ___m_Ptr;
};
struct HideFlags_tC514182ACEFD3B847988C45D5DB812FF6DB1BF4A 
{
	int32_t ___value__;
};
struct MaterialPropertyBlock_t2308669579033A857EFE6E4831909F638B27411D  : public RuntimeObject
{
	intptr_t ___m_Ptr;
};
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C  : public RuntimeObject
{
	intptr_t ___m_CachedPtr;
};
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_marshaled_pinvoke
{
	intptr_t ___m_CachedPtr;
};
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_marshaled_com
{
	intptr_t ___m_CachedPtr;
};
struct ParticleSystemGradientMode_t5525A27D816AC28297038E5A98856B29095C4C1A 
{
	int32_t ___value__;
};
struct RuntimeFieldHandle_t6E4C45B6D2EA12FC99185805A7E77527899B25C5 
{
	intptr_t ___value;
};
struct RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B 
{
	intptr_t ___value;
};
struct FaceDirection_t305F90A2FD5BDFE7A9D6CC757889C799D2BBB007 
{
	int32_t ___value__;
};
struct EmissionShapes_tEAC441FB1E2020BC219983C9300DBBA93C365E1E 
{
	int32_t ___value__;
};
struct LifetimeSettings_tE54B88A67F50821D7CB651070D754D708B7219D6 
{
	int32_t ___value__;
};
struct AfterSetAction_tAFA2A593273ED1BE72421FC5A1ECD72164784AEA 
{
	int32_t ___value__;
};
struct Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3  : public Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C
{
};
struct GameObject_t76FEDD663AB33C991A9C9A23129337651094216F  : public Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C
{
};
struct Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3  : public Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C
{
};
struct ScriptableObject_tB3BFDB921A1B1795B38A5417D3B97A89A140436A  : public Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C
{
};
struct ScriptableObject_tB3BFDB921A1B1795B38A5417D3B97A89A140436A_marshaled_pinvoke : public Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_marshaled_pinvoke
{
};
struct ScriptableObject_tB3BFDB921A1B1795B38A5417D3B97A89A140436A_marshaled_com : public Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_marshaled_com
{
};
struct Shader_tADC867D36B7876EE22427FAA2CE485105F4EE692  : public Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C
{
};
struct Texture_t791CBB51219779964E0E8A2ED7C1AA5F92A4A700  : public Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C
{
};
struct Type_t  : public MemberInfo_t
{
	RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B ____impl;
};
struct MinMaxGradient_tFF31B8EC2855D0074AB86E8B37BEA6609070AC69 
{
	int32_t ___m_Mode;
	Gradient_tA7FEBE2FDB4929FFF6C997134841046F713DAC1E* ___m_GradientMin;
	Gradient_tA7FEBE2FDB4929FFF6C997134841046F713DAC1E* ___m_GradientMax;
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___m_ColorMin;
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___m_ColorMax;
};
struct MinMaxGradient_tFF31B8EC2855D0074AB86E8B37BEA6609070AC69_marshaled_pinvoke
{
	int32_t ___m_Mode;
	Gradient_tA7FEBE2FDB4929FFF6C997134841046F713DAC1E_marshaled_pinvoke ___m_GradientMin;
	Gradient_tA7FEBE2FDB4929FFF6C997134841046F713DAC1E_marshaled_pinvoke ___m_GradientMax;
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___m_ColorMin;
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___m_ColorMax;
};
struct MinMaxGradient_tFF31B8EC2855D0074AB86E8B37BEA6609070AC69_marshaled_com
{
	int32_t ___m_Mode;
	Gradient_tA7FEBE2FDB4929FFF6C997134841046F713DAC1E_marshaled_com* ___m_GradientMin;
	Gradient_tA7FEBE2FDB4929FFF6C997134841046F713DAC1E_marshaled_com* ___m_GradientMax;
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___m_ColorMin;
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___m_ColorMax;
};
struct AllIn1ParticleHelperSO_tB1703DA97F077A501D1897ACE6C5AEE88C4A8061  : public ScriptableObject_tB3BFDB921A1B1795B38A5417D3B97A89A140436A
{
	bool ___matchDurationToLifetime;
	bool ___randomRotation;
	float ___minLifetime;
	float ___maxLifetime;
	float ___minSpeed;
	float ___maxSpeed;
	float ___minSize;
	float ___maxSize;
	MinMaxGradient_tFF31B8EC2855D0074AB86E8B37BEA6609070AC69 ___startColor;
	bool ___isBurst;
	int32_t ___minNumberOfParticles;
	int32_t ___maxNumberOfParticles;
	int32_t ___currEmissionShape;
	int32_t ___colorLifetime;
	int32_t ___sizeLifetime;
};
struct Behaviour_t01970CFBBA658497AE30F311C447DB0440BAB7FA  : public Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3
{
};
struct Renderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF  : public Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3
{
};
struct Texture2D_tE6505BC111DD8A424A9DBE8E05D7D09E11FFFCF4  : public Texture_t791CBB51219779964E0E8A2ED7C1AA5F92A4A700
{
};
struct Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1  : public Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3
{
};
struct Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184  : public Behaviour_t01970CFBBA658497AE30F311C447DB0440BAB7FA
{
};
struct MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71  : public Behaviour_t01970CFBBA658497AE30F311C447DB0440BAB7FA
{
	CancellationTokenSource_tAAE1E0033BCFC233801F8CB4CED5C852B350CB7B* ___m_CancellationTokenSource;
};
struct All1VfxRandomTimeSeed_t67A13258B2C46B1B81032BB643724405898B5B57  : public MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71
{
	float ___minSeedValue;
	float ___maxSeedValue;
};
struct AllIn1GraphicMaterialDuplicate_tEC6847E85A67C8AFB79A06C3E271091CCD6B3AA0  : public MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71
{
};
struct AllIn1LookAt_tDBAC078F7CE06BC8241E5429C930D8586A64D836  : public MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71
{
	bool ___updateEveryFrame;
	bool ___targetIsMainCamera;
	Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* ___target;
	int32_t ___faceDirection;
	bool ___negateDirection;
};
struct AllIn1ParticleHelperComponent_tDFD2B3E7E4BE678952CD673F473DBEFEA569C0BB  : public MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71
{
	bool ___hierarchyHelpers;
	bool ___generalOptions;
	bool ___shapeOptions;
	bool ___emissionOptions;
	bool ___overLifetimeOptions;
	bool ___colorChangeOption;
	bool ___particleHelperPresets;
	bool ___particleSystemPresets;
	int32_t ___numberOfCopies;
	bool ___applyEverythingOnChange;
	bool ___matchDurationToLifetime;
	bool ___randomRotation;
	float ___minLifetime;
	float ___maxLifetime;
	float ___minSpeed;
	float ___maxSpeed;
	float ___minSize;
	float ___maxSize;
	MinMaxGradient_tFF31B8EC2855D0074AB86E8B37BEA6609070AC69 ___startColor;
	bool ___isBurst;
	int32_t ___minNumberOfParticles;
	int32_t ___maxNumberOfParticles;
	int32_t ___currEmissionShape;
	int32_t ___colorLifetime;
	int32_t ___sizeLifetime;
};
struct AllIn1VfxBounceAnimation_tCE51755C53049903743EC707AADB9B8B0019D86E  : public MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71
{
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___targetOffset;
	float ___speed;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___startPosition;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___animationMovementVector;
};
struct AllIn1VfxComponent_t2587889FCB75E1748AEF4F9EAE574E8C3631BE54  : public MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71
{
	Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* ___currMaterial;
	Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* ___prevMaterial;
	bool ___matAssigned;
	bool ___destroyed;
};
struct AllIn1VfxFakeLightDirSetter_t2DF709DA0A4395452D0BF247C1D247B3722FCC06  : public MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71
{
	bool ___setOnAwake;
	bool ___setOnUpdate;
	Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* ___target;
	int32_t ___lightDirId;
};
struct AllIn1VfxScrollShaderProperty_tAC8F7ED4CC8304DBB58E1B23A2D2CE13EA22E07E  : public MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71
{
	String_t* ___numericPropertyName;
	float ___scrollSpeed;
	bool ___backAndForth;
	float ___maxValue;
	float ___iniValue;
	bool ___goingUp;
	bool ___applyModulo;
	float ___modulo;
	bool ___stopAtValue;
	float ___stopValue;
	Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* ___mat;
	Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* ___originalMat;
	bool ___restoreMaterialOnDisable;
	int32_t ___propertyShaderID;
	float ___currValue;
	bool ___isValid;
};
struct AllIn1VfxScrollShaderTexture_t0B1CA0D34AF4808EA9BA40B9CDBA558936EB3103  : public MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71
{
	String_t* ___texturePropertyName;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___scrollSpeed;
	bool ___textureOffset;
	bool ___backAndForth;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___maxValue;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___iniValue;
	bool ___goingUpX;
	bool ___goingUpY;
	bool ___applyModulo;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___modulo;
	bool ___stopAtValue;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___stopValue;
	Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* ___mat;
	Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* ___originalMat;
	bool ___restoreMaterialOnDisable;
	int32_t ___propertyShaderID;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___currValue;
	bool ___isValid;
};
struct SetAllIn1VfxCustomGlobalTime_t77136D14EF3C885BA7985A17CC75E8BB9D4D9CAE  : public MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71
{
	int32_t ___globalTime;
	Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 ___timeVector;
};
struct UIBehaviour_tB9D4295827BD2EEDEF0749200C6CA7090C742A9D  : public MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71
{
};
struct Graphic_tCBFCA4585A19E2B75465AECFEAC43F4016BF7931  : public UIBehaviour_tB9D4295827BD2EEDEF0749200C6CA7090C742A9D
{
	Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* ___m_Material;
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___m_Color;
	bool ___m_SkipLayoutUpdate;
	bool ___m_SkipMaterialUpdate;
	bool ___m_RaycastTarget;
	bool ___m_RaycastTargetCache;
	Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 ___m_RaycastPadding;
	RectTransform_t6C5DA5E41A89E0F488B001E45E58963480E543A5* ___m_RectTransform;
	CanvasRenderer_tAB9A55A976C4E3B2B37D0CE5616E5685A8B43860* ___m_CanvasRenderer;
	Canvas_t2DB4CEFDFF732884866C83F11ABF75F5AE8FFB26* ___m_Canvas;
	bool ___m_VertsDirty;
	bool ___m_MaterialDirty;
	UnityAction_t11A1F3B953B365C072A5DCC32677EE1796A962A7* ___m_OnDirtyLayoutCallback;
	UnityAction_t11A1F3B953B365C072A5DCC32677EE1796A962A7* ___m_OnDirtyVertsCallback;
	UnityAction_t11A1F3B953B365C072A5DCC32677EE1796A962A7* ___m_OnDirtyMaterialCallback;
	Mesh_t6D9C539763A09BC2B12AEAEF36F6DFFC98AE63D4* ___m_CachedMesh;
	Vector2U5BU5D_tFEBBC94BCC6C9C88277BA04047D2B3FDB6ED7FDA* ___m_CachedUvs;
	TweenRunner_1_t5BB0582F926E75E2FE795492679A6CF55A4B4BC4* ___m_ColorTweenRunner;
	bool ___U3CuseLegacyMeshGenerationU3Ek__BackingField;
};
struct U3CPrivateImplementationDetailsU3E_tBD303D34CFA3CA2694E763FA795BFE4D049C3B58_StaticFields
{
	__StaticArrayInitTypeSizeU3D561_tCC9CF2DEF97815A0687F7C880D90365D40399F24 ___6B01616A8E2823C81992C86218274AA987C7176ACDA6F948FD86F07A1729CCB0;
	__StaticArrayInitTypeSizeU3D817_tFF8FE8429977FFF978BB1C20A646908B58CDA50E ___F46DB0FE475947149A3EFF971B0B57B34FB89FFD33EB98A59AFBB0D14A8B2544;
};
struct String_t_StaticFields
{
	String_t* ___Empty;
};
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_StaticFields
{
	String_t* ___TrueString;
	String_t* ___FalseString;
};
struct Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7_StaticFields
{
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___zeroVector;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___oneVector;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___upVector;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___downVector;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___leftVector;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___rightVector;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___positiveInfinityVector;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___negativeInfinityVector;
};
struct Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_StaticFields
{
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___zeroVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___oneVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___upVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___downVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___leftVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___rightVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___forwardVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___backVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___positiveInfinityVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___negativeInfinityVector;
};
struct Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3_StaticFields
{
	Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 ___zeroVector;
	Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 ___oneVector;
	Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 ___positiveInfinityVector;
	Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 ___negativeInfinityVector;
};
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_StaticFields
{
	int32_t ___OffsetOfInstanceIDInCPlusPlusObject;
};
struct Texture_t791CBB51219779964E0E8A2ED7C1AA5F92A4A700_StaticFields
{
	int32_t ___GenerateAllMips;
};
struct Type_t_StaticFields
{
	Binder_t91BFCE95A7057FADF4D8A1A342AFE52872246235* ___s_defaultBinder;
	Il2CppChar ___Delimiter;
	TypeU5BU5D_t97234E1129B564EB38B8D85CAC2AD8B5B9522FFB* ___EmptyTypes;
	RuntimeObject* ___Missing;
	MemberFilter_tF644F1AE82F611B677CE1964D5A3277DDA21D553* ___FilterAttribute;
	MemberFilter_tF644F1AE82F611B677CE1964D5A3277DDA21D553* ___FilterName;
	MemberFilter_tF644F1AE82F611B677CE1964D5A3277DDA21D553* ___FilterNameIgnoreCase;
};
struct Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184_StaticFields
{
	CameraCallback_t844E527BFE37BC0495E7F67993E43C07642DA9DD* ___onPreCull;
	CameraCallback_t844E527BFE37BC0495E7F67993E43C07642DA9DD* ___onPreRender;
	CameraCallback_t844E527BFE37BC0495E7F67993E43C07642DA9DD* ___onPostRender;
};
struct Graphic_tCBFCA4585A19E2B75465AECFEAC43F4016BF7931_StaticFields
{
	Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* ___s_DefaultUI;
	Texture2D_tE6505BC111DD8A424A9DBE8E05D7D09E11FFFCF4* ___s_WhiteTexture;
	Mesh_t6D9C539763A09BC2B12AEAEF36F6DFFC98AE63D4* ___s_Mesh;
	VertexHelper_tB905FCB02AE67CBEE5F265FE37A5938FC5D136FE* ___s_VertexHelper;
};
#ifdef __clang__
#pragma clang diagnostic pop
#endif
struct ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031  : public RuntimeArray
{
	ALIGN_FIELD (8) uint8_t m_Items[1];

	inline uint8_t GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline uint8_t* GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, uint8_t value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
	}
	inline uint8_t GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline uint8_t* GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, uint8_t value)
	{
		m_Items[index] = value;
	}
};
struct ColorU5BU5D_t612261CF293F6FFC3D80AB52259FF0DC2B2CC389  : public RuntimeArray
{
	ALIGN_FIELD (8) Color_tD001788D726C3A7F1379BEED0260B9591F440C1F m_Items[1];

	inline Color_tD001788D726C3A7F1379BEED0260B9591F440C1F GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline Color_tD001788D726C3A7F1379BEED0260B9591F440C1F* GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, Color_tD001788D726C3A7F1379BEED0260B9591F440C1F value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
	}
	inline Color_tD001788D726C3A7F1379BEED0260B9591F440C1F GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline Color_tD001788D726C3A7F1379BEED0260B9591F440C1F* GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, Color_tD001788D726C3A7F1379BEED0260B9591F440C1F value)
	{
		m_Items[index] = value;
	}
};


IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* Component_GetComponent_TisRuntimeObject_m7181F81CAEC2CF53F5D2BC79B7425C16E1F80D33_gshared (Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3* __this, const RuntimeMethod* method) ;

IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RuntimeHelpers_InitializeArray_m751372AA3F24FBF6DA9B9D687CBFA2DE436CAB9B (RuntimeArray* ___0_array, RuntimeFieldHandle_t6E4C45B6D2EA12FC99185805A7E77527899B25C5 ___1_fldHandle, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2 (RuntimeObject* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void MaterialPropertyBlock__ctor_m14C3432585F7BB65028BCD64A0FD6607A1B490FB (MaterialPropertyBlock_t2308669579033A857EFE6E4831909F638B27411D* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float Random_Range_m5236C99A7D8AE6AC9190592DC66016652A2D2494 (float ___0_minInclusive, float ___1_maxInclusive, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void MaterialPropertyBlock_SetFloat_m49458EDC57C2B431D765FE7414F18918AD619888 (MaterialPropertyBlock_t2308669579033A857EFE6E4831909F638B27411D* __this, String_t* ___0_name, float ___1_value, const RuntimeMethod* method) ;
inline Renderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF* Component_GetComponent_TisRenderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF_mC91ACC92AD57CA6CA00991DAF1DB3830BCE07AF8 (Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3* __this, const RuntimeMethod* method)
{
	return ((  Renderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF* (*) (Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3*, const RuntimeMethod*))Component_GetComponent_TisRuntimeObject_m7181F81CAEC2CF53F5D2BC79B7425C16E1F80D33_gshared)(__this, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Renderer_SetPropertyBlock_mF565698782FE54580B17CC0BFF9B0C4F0D68DF50 (Renderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF* __this, MaterialPropertyBlock_t2308669579033A857EFE6E4831909F638B27411D* ___0_properties, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void MonoBehaviour__ctor_m592DB0105CA0BC97AA1C5F4AD27B12D68A3B7C1E (MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* Camera_get_main_m52C992F18E05355ABB9EEB64A4BF2215E12762DF (const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371 (Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Object_op_Equality_mB6120F782D83091EF56A198FCEBCF066DB4A9605 (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C* ___0_x, Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C* ___1_y, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* Component_get_gameObject_m57AEFBB14DB39EC476F740BA000E170355DE691B (Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* Object_get_name_mAC2F6B897CF1303BA4249B4CB55271AFACBB6392 (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* String_Concat_m8855A6DE10F84DA7F4EC113CADDB59873A25573B (String_t* ___0_str0, String_t* ___1_str1, String_t* ___2_str2, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Debug_LogError_mB00B2B4468EF3CAF041B038D840820FB84C924B2 (RuntimeObject* ___0_message, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Object_Destroy_mE97D0A766419A81296E8D4E5C23D01D3FE91ACBB (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C* ___0_obj, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AllIn1LookAt_LookAtCompute_m729D7D16E1394FB0818C7E6BCB3661828AFB608E (AllIn1LookAt_tDBAC078F7CE06BC8241E5429C930D8586A64D836* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Transform_get_position_m69CD5FA214FDAE7BB701552943674846C220FDE1 (Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_op_Subtraction_mE42023FF80067CB44A1D4A27EB7CF2B24CABB828_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_a, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___1_b, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_get_normalized_m736BBF65D5CDA7A18414370D15B4DFCC1E466F07_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_op_UnaryNegation_m5450829F333BD2A88AF9A592C4EE331661225915_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_a, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Transform_set_forward_mA178B5CF4F0F6133F9AF8ED3A4ECD2C604C60C26 (Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Transform_set_up_m1FBA5A97E5057747AC027AD5897EDE80A554D554 (Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Transform_set_right_mBE8A7189FB1313A8B3E1E10EA538DED15D3E93DA (Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ScriptableObject__ctor_mD037FDB0B487295EA47F79A4DB1BF1846C9087FF (ScriptableObject_tB3BFDB921A1B1795B38A5417D3B97A89A140436A* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float Time_get_time_m3A271BB1B20041144AC5B7863B71AB1F0150374B (const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_op_Multiply_m87BA7C578F96C8E49BB07088DAAC4649F83B0353_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_a, float ___1_d, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_op_Addition_m78C0EC70CB66E8DCAC225743D82B268DAEE92067_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_a, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___1_b, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Transform_set_position_mA1A817124BB41B685043DED2A9BA48CDF37C4156 (Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_value, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_get_up_m128AF3FDC820BF59D5DE86D973E7DE3F20C3AEBA_inline (const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool AllIn1VfxComponent_SetMaterial_mBFA6195608E559482B63D71AB64188AEC8793F89 (AllIn1VfxComponent_t2587889FCB75E1748AEF4F9EAE574E8C3631BE54* __this, int32_t ___0_action, String_t* ___1_shaderName, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool AllIn1VfxComponent_FetchCurrentMaterial_m39F8AE40F3507E0BE6FAADA12ADA81C59718B026 (AllIn1VfxComponent_t2587889FCB75E1748AEF4F9EAE574E8C3631BE54* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Shader_tADC867D36B7876EE22427FAA2CE485105F4EE692* Material_get_shader_m8B0C11AE6F2AD7DE30AF52D3195EB716F7A71983 (Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool String_Contains_m6D77B121FADA7CA5F397C0FABB65DA62DF03B6C3 (String_t* __this, String_t* ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* String_Replace_mABDB7003A1D0AEDCAE9FF85E3DFFFBA752D2A166 (String_t* __this, String_t* ___0_oldValue, String_t* ___1_newValue, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Object_op_Inequality_mD0BE578448EAA61948F25C32F8DD55AB1F778602 (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C* ___0_x, Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C* ___1_y, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* Renderer_get_sharedMaterial_mA2E0CA0A564617FFC3E0E50947C6300082C35F81 (Renderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF* __this, const RuntimeMethod* method) ;
inline Graphic_tCBFCA4585A19E2B75465AECFEAC43F4016BF7931* Component_GetComponent_TisGraphic_tCBFCA4585A19E2B75465AECFEAC43F4016BF7931_mFE18E20FC92395F90E776DBC4CD214A4F2D97D90 (Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3* __this, const RuntimeMethod* method)
{
	return ((  Graphic_tCBFCA4585A19E2B75465AECFEAC43F4016BF7931* (*) (Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3*, const RuntimeMethod*))Component_GetComponent_TisRuntimeObject_m7181F81CAEC2CF53F5D2BC79B7425C16E1F80D33_gshared)(__this, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AllIn1VfxComponent_MissingRenderer_mB023CB700C00CC98DFECD40F146C57164D16C77E (AllIn1VfxComponent_t2587889FCB75E1748AEF4F9EAE574E8C3631BE54* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Type_t* Type_GetTypeFromHandle_m6062B81682F79A4D6DF2640692EE6D9987858C57 (RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B ___0_handle, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C* Resources_Load_m6CD8FBBCCFFF22179FA0E7B1806B888103008D33 (String_t* ___0_path, Type_t* ___1_systemTypeInstance, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Application_get_isPlaying_m25B0ABDFEF54F5370CD3F263A813540843D00F34 (const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Application_get_isEditor_mEAC51E3ACE6DCE438087FB14BD75A3C219D354D0 (const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Material__ctor_mFCC42FB90257F1E8F7516A8640A79C465A39961C (Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* __this, Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* ___0_source, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Material__ctor_m7FDF47105D66D19591BE505A0C42B0F90D88C9BF (Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* __this, Shader_tADC867D36B7876EE22427FAA2CE485105F4EE692* ___0_shader, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Renderer_set_sharedMaterial_m5E842F9A06CFB7B77656EB319881CB4B3E8E4288 (Renderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF* __this, Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Object_set_hideFlags_mACB8BFC903FB3B01BBD427753E791BF28B5E33D4 (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C* __this, int32_t ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AllIn1VfxComponent_DoAfterSetAction_mE585EF484C1E4657FBAAF98041E7A2772829E727 (AllIn1VfxComponent_t2587889FCB75E1748AEF4F9EAE574E8C3631BE54* __this, int32_t ___0_action, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AllIn1VfxComponent_SetSceneDirty_m9E6A673E9394C3A3B775625029362C9D996410EA (AllIn1VfxComponent_t2587889FCB75E1748AEF4F9EAE574E8C3631BE54* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AllIn1VfxComponent_ClearAllKeywords_m59BC37A1506BB4271456D76BF6E53597D2652F6E (AllIn1VfxComponent_t2587889FCB75E1748AEF4F9EAE574E8C3631BE54* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Material_CopyPropertiesFromMaterial_m4148227E6A0B8E66315D8115F656B7F8BEAE915B (Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* __this, Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* ___0_mat, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AllIn1VfxComponent_ResetAllProperties_m50ABAF51C59617C87828194552AA89EC998BAFB2 (AllIn1VfxComponent_t2587889FCB75E1748AEF4F9EAE574E8C3631BE54* __this, String_t* ___0_shaderName, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AllIn1VfxComponent_CleanMaterial_mFAD12AF2EDBE22342D512879ABFF5F12D059BE78 (AllIn1VfxComponent_t2587889FCB75E1748AEF4F9EAE574E8C3631BE54* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AllIn1VfxComponent_MakeNewMaterial_m995B26BFCD13754FCC6E97DE0A42BA17EB4BBCFF (AllIn1VfxComponent_t2587889FCB75E1748AEF4F9EAE574E8C3631BE54* __this, String_t* ___0_shaderName, bool ___1_notifyWhenDone, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AllIn1VfxComponent_SetKeyword_m95EC164255D1EA16944BE4564EFE7730F9864BF2 (AllIn1VfxComponent_t2587889FCB75E1748AEF4F9EAE574E8C3631BE54* __this, String_t* ___0_keyword, bool ___1_state, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AllIn1VfxComponent_FindCurrMaterial_m6005E8C7A35C0F7F353D15BD4026AD790B609BCD (AllIn1VfxComponent_t2587889FCB75E1748AEF4F9EAE574E8C3631BE54* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Material_DisableKeyword_mC123927EBF2F2A19220A4456C8EA19F2BA416E8C (Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* __this, String_t* ___0_keyword, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Material_EnableKeyword_mE8523EF6CF694284DF976D47ADEDE9363A1174AC (Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* __this, String_t* ___0_keyword, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Shader_tADC867D36B7876EE22427FAA2CE485105F4EE692* Shader_Find_m183AA54F78320212DDEC811592F98456898A41C5 (String_t* ___0_name, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* Int32_ToString_m030E01C24E294D6762FB0B6F37CB541581F55CA5 (int32_t* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* String_Concat_m9E3155FB84015C823606188F53B47CB44C444991 (String_t* ___0_str0, String_t* ___1_str1, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool File_Exists_m95E329ABBE3EAD6750FE1989BBA6884457136D4A (String_t* ___0_path, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AllIn1VfxComponent_SaveMaterialWithOtherName_m813A30F3AC436425C72CAE067E4696DEBAEEAE32 (AllIn1VfxComponent_t2587889FCB75E1748AEF4F9EAE574E8C3631BE54* __this, String_t* ___0_path, int32_t ___1_i, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AllIn1VfxComponent_DoSaving_m936CEFF84DEAAA780F01E1774FD78018AB351FE7 (AllIn1VfxComponent_t2587889FCB75E1748AEF4F9EAE574E8C3631BE54* __this, String_t* ___0_fileName, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AllIn1VfxFakeLightDirSetter_SetGlobalFakeLightDir_mC9E6091C093D3A23241CA19E45E032A889CB71B0 (AllIn1VfxFakeLightDirSetter_t2DF709DA0A4395452D0BF247C1D247B3722FCC06* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Shader_PropertyToID_mE98523D50F5656CAE89B30695C458253EB8956CA (String_t* ___0_name, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Transform_get_forward_mFCFACF7165FDAB21E80E384C494DF278386CEE2F (Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 Vector4_op_Implicit_m2ECA73F345A7AD84144133E9E51657204002B12D_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_v, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Shader_SetGlobalVector_mDC5F45B008D44A2C8BF6D450CFE8B58B847C8190 (int32_t ___0_nameID, Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 ___1_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Random_InitState_mE70961834F42FFEEB06CB9C68175354E0C255664 (int32_t ___0_seed, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Color_tD001788D726C3A7F1379BEED0260B9591F440C1F AllIn1VfxNoiseCreator_CalculatePerlinColor_m3675833D2D56F07A38F4B3FAE9493989B733D9F3 (int32_t ___0_x, int32_t ___1_y, float ___2_scale, float ___3_offset, int32_t ___4_width, int32_t ___5_height, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Texture2D_SetPixel_m2CCFC5F729135D59DC4A697C2605A3FC5C8574DB (Texture2D_tE6505BC111DD8A424A9DBE8E05D7D09E11FFFCF4* __this, int32_t ___0_x, int32_t ___1_y, Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___2_color, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Texture2D_Apply_mA014182C9EE0BBF6EEE3B286854F29E50EB972DC (Texture2D_tE6505BC111DD8A424A9DBE8E05D7D09E11FFFCF4* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Texture2D__ctor_m3BA82E87442B7F69E118477069AE11101B9DF796 (Texture2D_tE6505BC111DD8A424A9DBE8E05D7D09E11FFFCF4* __this, int32_t ___0_width, int32_t ___1_height, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR ColorU5BU5D_t612261CF293F6FFC3D80AB52259FF0DC2B2CC389* Texture2D_GetPixels_m77A00D71DF5CDC7DAA0EE66FF2C90A24C7604039 (Texture2D_tE6505BC111DD8A424A9DBE8E05D7D09E11FFFCF4* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Texture2D_SetPixels_mAE0CDFA15FA96F840D7FFADC31405D8AF20D9073 (Texture2D_tE6505BC111DD8A424A9DBE8E05D7D09E11FFFCF4* __this, ColorU5BU5D_t612261CF293F6FFC3D80AB52259FF0DC2B2CC389* ___0_colors, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Color_tD001788D726C3A7F1379BEED0260B9591F440C1F AllIn1VfxNoiseCreator_PerlinBorderless_m49ADBDE375DC489A6473ED11D71EAD34E771AC45 (int32_t ___0_x, int32_t ___1_y, float ___2_scale, float ___3_offset, int32_t ___4_width, int32_t ___5_height, Texture2D_tE6505BC111DD8A424A9DBE8E05D7D09E11FFFCF4* ___6_previousPerlin, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float Mathf_PerlinNoise_mAB0E53C29FE95469CF303364910AD0D8662A9A6A (float ___0_x, float ___1_y, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Color__ctor_m3786F0D6E510D9CFA544523A955870BD2A514C8C_inline (Color_tD001788D726C3A7F1379BEED0260B9591F440C1F* __this, float ___0_r, float ___1_g, float ___2_b, float ___3_a, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Mathf_Max_mF5379E63D2BBAC76D090748695D833934F8AD051_inline (float ___0_a, float ___1_b, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Color_tD001788D726C3A7F1379BEED0260B9591F440C1F Texture2D_GetPixel_m69A17FE5CC220F438C7421DCB50A9E22AAB4A415 (Texture2D_tE6505BC111DD8A424A9DBE8E05D7D09E11FFFCF4* __this, int32_t ___0_x, int32_t ___1_y, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Color_tD001788D726C3A7F1379BEED0260B9591F440C1F Color_Lerp_mE79F87889843ECDC188E4CB5B5E1F1B2256E5EBE_inline (Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___0_a, Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___1_b, float ___2_t, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* Renderer_get_material_m5BA2A00816C4CC66580D4B2E409CF10718C15656 (Renderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AllIn1VfxScrollShaderProperty_DestroyComponentAndLogError_mE541EDF927CBB77AF92844EDB5F13BC99EE07A36 (AllIn1VfxScrollShaderProperty_tAC8F7ED4CC8304DBB58E1B23A2D2CE13EA22E07E* __this, String_t* ___0_logError, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Material_HasProperty_mC09A83B44E368A217F606DD4954FA080CC03EC6C (Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* __this, String_t* ___0_name, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* String_Concat_m093934F71A9B351911EE46311674ED463B180006 (String_t* ___0_str0, String_t* ___1_str1, String_t* ___2_str2, String_t* ___3_str3, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float Material_GetFloat_m52462F4AEDE20758BFB592B11DE83A79D2774932 (Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* __this, int32_t ___0_nameID, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float Time_get_deltaTime_mC3195000401F0FD167DD2F948FD2BC58330D0865 (const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AllIn1VfxScrollShaderProperty_FlipGoingUp_m2E85C6CA803F28BD510C7D2B57F2C4FFF3D5F180 (AllIn1VfxScrollShaderProperty_tAC8F7ED4CC8304DBB58E1B23A2D2CE13EA22E07E* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Material_SetFloat_m3ECFD92072347A8620254F014865984FA68211A8 (Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* __this, int32_t ___0_nameID, float ___1_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AllIn1VfxScrollShaderTexture_DestroyComponentAndLogError_m0FF3DD2B9CEEF6E92BB15E65DAC3E1F5546C3E6E (AllIn1VfxScrollShaderTexture_t0B1CA0D34AF4808EA9BA40B9CDBA558936EB3103* __this, String_t* ___0_logError, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 Material_GetTextureOffset_m4F9E2C96960DFE3C76B9D878F8E654D78185F9CA (Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* __this, String_t* ___0_name, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 Material_GetTextureScale_mF3406F1439C275C25FEE0C4E19108B29AA77E693 (Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* __this, String_t* ___0_name, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 Vector2_op_Multiply_m2D984B613020089BF5165BA4CA10988E2DC771FE_inline (Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___0_a, float ___1_d, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 Vector2_op_Addition_m8136742CE6EE33BA4EB81C5F584678455917D2AE_inline (Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___0_a, Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___1_b, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AllIn1VfxScrollShaderTexture_FlipGoingUp_mAF1B7A83DBB3020A14A98614C591A410344AC793 (AllIn1VfxScrollShaderTexture_t0B1CA0D34AF4808EA9BA40B9CDBA558936EB3103* __this, bool ___0_isXComponent, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Material_SetTextureOffset_mB28E782AE9F9B4CB9D36F209C976F8A0FE7DF747 (Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* __this, int32_t ___0_nameID, Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___1_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Material_SetTextureScale_mBA092A3DCD393695B32801FD05F70A8CC58CB89D (Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* __this, int32_t ___0_nameID, Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___1_value, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 Vector2_get_zero_m32506C40EC2EE7D5D4410BF40D3EE683A3D5F32C_inline (const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 Vector2_get_one_m9097EB8DC23C26118A591AF16702796C3EF51DFB_inline (const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float Time_get_unscaledTime_mAF4040B858903E1325D1C65B8BF1AC61460B2503 (const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Vector3__ctor_m376936E6B999EF1ECBE57D990A386303E2283DE0_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* __this, float ___0_x, float ___1_y, float ___2_z, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_Normalize_mEF8349CC39674236CFC694189AFD36E31F89AC8F_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_value, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Vector4__ctor_m96B2CD8B862B271F513AF0BDC2EABD58E4DBC813_inline (Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3* __this, float ___0_x, float ___1_y, float ___2_z, float ___3_w, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Mathf_Clamp01_mA7E048DBDA832D399A581BE4D6DED9FA44CE0F14_inline (float ___0_value, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Vector2__ctor_m9525B79969AFFE3254B303A40997A56DEEB6F548_inline (Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7* __this, float ___0_x, float ___1_y, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Vector3_Magnitude_m21652D951393A3D7CE92CE40049A0E7F76544D1B_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_vector, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_op_Division_mCC6BB24E372AB96B8380D1678446EF6A8BAE13BB_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_a, float ___1_d, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_get_zero_m0C1249C3F25B1C70EAD3CC8B31259975A457AE39_inline (const RuntimeMethod* method) ;
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR MonoScriptData_t3949E822A7D170EBCC93AA6DAA8C9039CDD4B543 UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_m92A8DADA841D24C84DF32D2A8551A954DA3A7FCE (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CPrivateImplementationDetailsU3E_tBD303D34CFA3CA2694E763FA795BFE4D049C3B58____6B01616A8E2823C81992C86218274AA987C7176ACDA6F948FD86F07A1729CCB0_FieldInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CPrivateImplementationDetailsU3E_tBD303D34CFA3CA2694E763FA795BFE4D049C3B58____F46DB0FE475947149A3EFF971B0B57B34FB89FFD33EB98A59AFBB0D14A8B2544_FieldInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	MonoScriptData_t3949E822A7D170EBCC93AA6DAA8C9039CDD4B543 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		il2cpp_codegen_initobj((&V_0), sizeof(MonoScriptData_t3949E822A7D170EBCC93AA6DAA8C9039CDD4B543));
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_0 = (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)SZArrayNew(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031_il2cpp_TypeInfo_var, (uint32_t)((int32_t)817));
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_1 = L_0;
		RuntimeFieldHandle_t6E4C45B6D2EA12FC99185805A7E77527899B25C5 L_2 = { reinterpret_cast<intptr_t> (U3CPrivateImplementationDetailsU3E_tBD303D34CFA3CA2694E763FA795BFE4D049C3B58____F46DB0FE475947149A3EFF971B0B57B34FB89FFD33EB98A59AFBB0D14A8B2544_FieldInfo_var) };
		RuntimeHelpers_InitializeArray_m751372AA3F24FBF6DA9B9D687CBFA2DE436CAB9B((RuntimeArray*)L_1, L_2, NULL);
		(&V_0)->___FilePathsData = L_1;
		Il2CppCodeGenWriteBarrier((void**)(&(&V_0)->___FilePathsData), (void*)L_1);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_3 = (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)SZArrayNew(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031_il2cpp_TypeInfo_var, (uint32_t)((int32_t)561));
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_4 = L_3;
		RuntimeFieldHandle_t6E4C45B6D2EA12FC99185805A7E77527899B25C5 L_5 = { reinterpret_cast<intptr_t> (U3CPrivateImplementationDetailsU3E_tBD303D34CFA3CA2694E763FA795BFE4D049C3B58____6B01616A8E2823C81992C86218274AA987C7176ACDA6F948FD86F07A1729CCB0_FieldInfo_var) };
		RuntimeHelpers_InitializeArray_m751372AA3F24FBF6DA9B9D687CBFA2DE436CAB9B((RuntimeArray*)L_4, L_5, NULL);
		(&V_0)->___TypesData = L_4;
		Il2CppCodeGenWriteBarrier((void**)(&(&V_0)->___TypesData), (void*)L_4);
		(&V_0)->___TotalFiles = ((int32_t)12);
		(&V_0)->___TotalTypes = ((int32_t)12);
		(&V_0)->___IsEditorOnly = (bool)0;
		MonoScriptData_t3949E822A7D170EBCC93AA6DAA8C9039CDD4B543 L_6 = V_0;
		return L_6;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_m01AD9B8879E7D22FF293831D78BB116128A99F0D (UnitySourceGeneratedAssemblyMonoScriptTypes_v1_t44342FB853B426918F6CDA6054309AA633E52ABF* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C void MonoScriptData_t3949E822A7D170EBCC93AA6DAA8C9039CDD4B543_marshal_pinvoke(const MonoScriptData_t3949E822A7D170EBCC93AA6DAA8C9039CDD4B543& unmarshaled, MonoScriptData_t3949E822A7D170EBCC93AA6DAA8C9039CDD4B543_marshaled_pinvoke& marshaled)
{
	marshaled.___FilePathsData = il2cpp_codegen_com_marshal_safe_array(IL2CPP_VT_I1, unmarshaled.___FilePathsData);
	marshaled.___TypesData = il2cpp_codegen_com_marshal_safe_array(IL2CPP_VT_I1, unmarshaled.___TypesData);
	marshaled.___TotalTypes = unmarshaled.___TotalTypes;
	marshaled.___TotalFiles = unmarshaled.___TotalFiles;
	marshaled.___IsEditorOnly = static_cast<int32_t>(unmarshaled.___IsEditorOnly);
}
IL2CPP_EXTERN_C void MonoScriptData_t3949E822A7D170EBCC93AA6DAA8C9039CDD4B543_marshal_pinvoke_back(const MonoScriptData_t3949E822A7D170EBCC93AA6DAA8C9039CDD4B543_marshaled_pinvoke& marshaled, MonoScriptData_t3949E822A7D170EBCC93AA6DAA8C9039CDD4B543& unmarshaled)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	unmarshaled.___FilePathsData = (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I1, Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, marshaled.___FilePathsData);
	Il2CppCodeGenWriteBarrier((void**)(&unmarshaled.___FilePathsData), (void*)(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I1, Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, marshaled.___FilePathsData));
	unmarshaled.___TypesData = (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I1, Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, marshaled.___TypesData);
	Il2CppCodeGenWriteBarrier((void**)(&unmarshaled.___TypesData), (void*)(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I1, Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, marshaled.___TypesData));
	int32_t unmarshaledTotalTypes_temp_2 = 0;
	unmarshaledTotalTypes_temp_2 = marshaled.___TotalTypes;
	unmarshaled.___TotalTypes = unmarshaledTotalTypes_temp_2;
	int32_t unmarshaledTotalFiles_temp_3 = 0;
	unmarshaledTotalFiles_temp_3 = marshaled.___TotalFiles;
	unmarshaled.___TotalFiles = unmarshaledTotalFiles_temp_3;
	bool unmarshaledIsEditorOnly_temp_4 = false;
	unmarshaledIsEditorOnly_temp_4 = static_cast<bool>(marshaled.___IsEditorOnly);
	unmarshaled.___IsEditorOnly = unmarshaledIsEditorOnly_temp_4;
}
IL2CPP_EXTERN_C void MonoScriptData_t3949E822A7D170EBCC93AA6DAA8C9039CDD4B543_marshal_pinvoke_cleanup(MonoScriptData_t3949E822A7D170EBCC93AA6DAA8C9039CDD4B543_marshaled_pinvoke& marshaled)
{
	il2cpp_codegen_com_destroy_safe_array(marshaled.___FilePathsData);
	marshaled.___FilePathsData = NULL;
	il2cpp_codegen_com_destroy_safe_array(marshaled.___TypesData);
	marshaled.___TypesData = NULL;
}
IL2CPP_EXTERN_C void MonoScriptData_t3949E822A7D170EBCC93AA6DAA8C9039CDD4B543_marshal_com(const MonoScriptData_t3949E822A7D170EBCC93AA6DAA8C9039CDD4B543& unmarshaled, MonoScriptData_t3949E822A7D170EBCC93AA6DAA8C9039CDD4B543_marshaled_com& marshaled)
{
	marshaled.___FilePathsData = il2cpp_codegen_com_marshal_safe_array(IL2CPP_VT_I1, unmarshaled.___FilePathsData);
	marshaled.___TypesData = il2cpp_codegen_com_marshal_safe_array(IL2CPP_VT_I1, unmarshaled.___TypesData);
	marshaled.___TotalTypes = unmarshaled.___TotalTypes;
	marshaled.___TotalFiles = unmarshaled.___TotalFiles;
	marshaled.___IsEditorOnly = static_cast<int32_t>(unmarshaled.___IsEditorOnly);
}
IL2CPP_EXTERN_C void MonoScriptData_t3949E822A7D170EBCC93AA6DAA8C9039CDD4B543_marshal_com_back(const MonoScriptData_t3949E822A7D170EBCC93AA6DAA8C9039CDD4B543_marshaled_com& marshaled, MonoScriptData_t3949E822A7D170EBCC93AA6DAA8C9039CDD4B543& unmarshaled)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	unmarshaled.___FilePathsData = (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I1, Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, marshaled.___FilePathsData);
	Il2CppCodeGenWriteBarrier((void**)(&unmarshaled.___FilePathsData), (void*)(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I1, Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, marshaled.___FilePathsData));
	unmarshaled.___TypesData = (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I1, Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, marshaled.___TypesData);
	Il2CppCodeGenWriteBarrier((void**)(&unmarshaled.___TypesData), (void*)(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I1, Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, marshaled.___TypesData));
	int32_t unmarshaledTotalTypes_temp_2 = 0;
	unmarshaledTotalTypes_temp_2 = marshaled.___TotalTypes;
	unmarshaled.___TotalTypes = unmarshaledTotalTypes_temp_2;
	int32_t unmarshaledTotalFiles_temp_3 = 0;
	unmarshaledTotalFiles_temp_3 = marshaled.___TotalFiles;
	unmarshaled.___TotalFiles = unmarshaledTotalFiles_temp_3;
	bool unmarshaledIsEditorOnly_temp_4 = false;
	unmarshaledIsEditorOnly_temp_4 = static_cast<bool>(marshaled.___IsEditorOnly);
	unmarshaled.___IsEditorOnly = unmarshaledIsEditorOnly_temp_4;
}
IL2CPP_EXTERN_C void MonoScriptData_t3949E822A7D170EBCC93AA6DAA8C9039CDD4B543_marshal_com_cleanup(MonoScriptData_t3949E822A7D170EBCC93AA6DAA8C9039CDD4B543_marshaled_com& marshaled)
{
	il2cpp_codegen_com_destroy_safe_array(marshaled.___FilePathsData);
	marshaled.___FilePathsData = NULL;
	il2cpp_codegen_com_destroy_safe_array(marshaled.___TypesData);
	marshaled.___TypesData = NULL;
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void All1VfxRandomTimeSeed_Start_mDA15A7000478CFFD6A05A4682D8264A4C25EE08E (All1VfxRandomTimeSeed_t67A13258B2C46B1B81032BB643724405898B5B57* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Component_GetComponent_TisRenderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF_mC91ACC92AD57CA6CA00991DAF1DB3830BCE07AF8_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MaterialPropertyBlock_t2308669579033A857EFE6E4831909F638B27411D_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralCF6B5D4AFB7B21CFD9A2454BF9D1139B1B749D14);
		s_Il2CppMethodInitialized = true;
	}
	MaterialPropertyBlock_t2308669579033A857EFE6E4831909F638B27411D* V_0 = NULL;
	{
		MaterialPropertyBlock_t2308669579033A857EFE6E4831909F638B27411D* L_0 = (MaterialPropertyBlock_t2308669579033A857EFE6E4831909F638B27411D*)il2cpp_codegen_object_new(MaterialPropertyBlock_t2308669579033A857EFE6E4831909F638B27411D_il2cpp_TypeInfo_var);
		MaterialPropertyBlock__ctor_m14C3432585F7BB65028BCD64A0FD6607A1B490FB(L_0, NULL);
		V_0 = L_0;
		MaterialPropertyBlock_t2308669579033A857EFE6E4831909F638B27411D* L_1 = V_0;
		float L_2 = __this->___minSeedValue;
		float L_3 = __this->___maxSeedValue;
		float L_4;
		L_4 = Random_Range_m5236C99A7D8AE6AC9190592DC66016652A2D2494(L_2, L_3, NULL);
		NullCheck(L_1);
		MaterialPropertyBlock_SetFloat_m49458EDC57C2B431D765FE7414F18918AD619888(L_1, _stringLiteralCF6B5D4AFB7B21CFD9A2454BF9D1139B1B749D14, L_4, NULL);
		Renderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF* L_5;
		L_5 = Component_GetComponent_TisRenderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF_mC91ACC92AD57CA6CA00991DAF1DB3830BCE07AF8(__this, Component_GetComponent_TisRenderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF_mC91ACC92AD57CA6CA00991DAF1DB3830BCE07AF8_RuntimeMethod_var);
		MaterialPropertyBlock_t2308669579033A857EFE6E4831909F638B27411D* L_6 = V_0;
		NullCheck(L_5);
		Renderer_SetPropertyBlock_mF565698782FE54580B17CC0BFF9B0C4F0D68DF50(L_5, L_6, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void All1VfxRandomTimeSeed__ctor_m7C1941EA9B48C2B65733D95234AFFCD643604CE2 (All1VfxRandomTimeSeed_t67A13258B2C46B1B81032BB643724405898B5B57* __this, const RuntimeMethod* method) 
{
	{
		__this->___maxSeedValue = (100.0f);
		MonoBehaviour__ctor_m592DB0105CA0BC97AA1C5F4AD27B12D68A3B7C1E(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AllIn1LookAt_Start_mCC8D994795CB43E68509E633B1815DB6537658F0 (AllIn1LookAt_tDBAC078F7CE06BC8241E5429C930D8586A64D836* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Debug_t8394C7EEAECA3689C2C9B9DE9C7166D73596276F_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral6B20C68293E633F1FCCB3BBD64B19DD052F5ED87);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral872DCAB5572E264E9E4EA514D7E835229090D6BC);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral9A5093C3D376CC1E1CC7EEF2F6A221406781623A);
		s_Il2CppMethodInitialized = true;
	}
	{
		bool L_0 = __this->___targetIsMainCamera;
		if (!L_0)
		{
			goto IL_0054;
		}
	}
	{
		Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* L_1;
		L_1 = Camera_get_main_m52C992F18E05355ABB9EEB64A4BF2215E12762DF(NULL);
		if (!L_1)
		{
			goto IL_001f;
		}
	}
	{
		Camera_tA92CC927D7439999BC82DBEDC0AA45B470F9E184* L_2;
		L_2 = Camera_get_main_m52C992F18E05355ABB9EEB64A4BF2215E12762DF(NULL);
		NullCheck(L_2);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_3;
		L_3 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(L_2, NULL);
		__this->___target = L_3;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___target), (void*)L_3);
	}

IL_001f:
	{
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_4 = __this->___target;
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_5;
		L_5 = Object_op_Equality_mB6120F782D83091EF56A198FCEBCF066DB4A9605(L_4, (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*)NULL, NULL);
		if (!L_5)
		{
			goto IL_0087;
		}
	}
	{
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_6;
		L_6 = Component_get_gameObject_m57AEFBB14DB39EC476F740BA000E170355DE691B(__this, NULL);
		NullCheck(L_6);
		String_t* L_7;
		L_7 = Object_get_name_mAC2F6B897CF1303BA4249B4CB55271AFACBB6392(L_6, NULL);
		String_t* L_8;
		L_8 = String_Concat_m8855A6DE10F84DA7F4EC113CADDB59873A25573B(_stringLiteral6B20C68293E633F1FCCB3BBD64B19DD052F5ED87, L_7, _stringLiteral9A5093C3D376CC1E1CC7EEF2F6A221406781623A, NULL);
		il2cpp_codegen_runtime_class_init_inline(Debug_t8394C7EEAECA3689C2C9B9DE9C7166D73596276F_il2cpp_TypeInfo_var);
		Debug_LogError_mB00B2B4468EF3CAF041B038D840820FB84C924B2(L_8, NULL);
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		Object_Destroy_mE97D0A766419A81296E8D4E5C23D01D3FE91ACBB(__this, NULL);
		goto IL_0087;
	}

IL_0054:
	{
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_9 = __this->___target;
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_10;
		L_10 = Object_op_Equality_mB6120F782D83091EF56A198FCEBCF066DB4A9605(L_9, (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*)NULL, NULL);
		if (!L_10)
		{
			goto IL_0087;
		}
	}
	{
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_11;
		L_11 = Component_get_gameObject_m57AEFBB14DB39EC476F740BA000E170355DE691B(__this, NULL);
		NullCheck(L_11);
		String_t* L_12;
		L_12 = Object_get_name_mAC2F6B897CF1303BA4249B4CB55271AFACBB6392(L_11, NULL);
		String_t* L_13;
		L_13 = String_Concat_m8855A6DE10F84DA7F4EC113CADDB59873A25573B(_stringLiteral872DCAB5572E264E9E4EA514D7E835229090D6BC, L_12, _stringLiteral9A5093C3D376CC1E1CC7EEF2F6A221406781623A, NULL);
		il2cpp_codegen_runtime_class_init_inline(Debug_t8394C7EEAECA3689C2C9B9DE9C7166D73596276F_il2cpp_TypeInfo_var);
		Debug_LogError_mB00B2B4468EF3CAF041B038D840820FB84C924B2(L_13, NULL);
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		Object_Destroy_mE97D0A766419A81296E8D4E5C23D01D3FE91ACBB(__this, NULL);
	}

IL_0087:
	{
		bool L_14 = __this->___updateEveryFrame;
		if (L_14)
		{
			goto IL_0095;
		}
	}
	{
		AllIn1LookAt_LookAtCompute_m729D7D16E1394FB0818C7E6BCB3661828AFB608E(__this, NULL);
	}

IL_0095:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AllIn1LookAt_Update_m0A08D68C2DEBDBC9ADBFB2DDEDCE62BD0D8676F8 (AllIn1LookAt_tDBAC078F7CE06BC8241E5429C930D8586A64D836* __this, const RuntimeMethod* method) 
{
	{
		bool L_0 = __this->___updateEveryFrame;
		if (!L_0)
		{
			goto IL_000e;
		}
	}
	{
		AllIn1LookAt_LookAtCompute_m729D7D16E1394FB0818C7E6BCB3661828AFB608E(__this, NULL);
	}

IL_000e:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AllIn1LookAt_LookAtCompute_m729D7D16E1394FB0818C7E6BCB3661828AFB608E (AllIn1LookAt_tDBAC078F7CE06BC8241E5429C930D8586A64D836* __this, const RuntimeMethod* method) 
{
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_0;
	memset((&V_0), 0, sizeof(V_0));
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_1;
	memset((&V_1), 0, sizeof(V_1));
	int32_t V_2 = 0;
	{
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_0 = __this->___target;
		NullCheck(L_0);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_1;
		L_1 = Transform_get_position_m69CD5FA214FDAE7BB701552943674846C220FDE1(L_0, NULL);
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_2;
		L_2 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		NullCheck(L_2);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_3;
		L_3 = Transform_get_position_m69CD5FA214FDAE7BB701552943674846C220FDE1(L_2, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_4;
		L_4 = Vector3_op_Subtraction_mE42023FF80067CB44A1D4A27EB7CF2B24CABB828_inline(L_1, L_3, NULL);
		V_1 = L_4;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_5;
		L_5 = Vector3_get_normalized_m736BBF65D5CDA7A18414370D15B4DFCC1E466F07_inline((&V_1), NULL);
		V_0 = L_5;
		bool L_6 = __this->___negateDirection;
		if (!L_6)
		{
			goto IL_0033;
		}
	}
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_7 = V_0;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_8;
		L_8 = Vector3_op_UnaryNegation_m5450829F333BD2A88AF9A592C4EE331661225915_inline(L_7, NULL);
		V_0 = L_8;
	}

IL_0033:
	{
		int32_t L_9 = __this->___faceDirection;
		V_2 = L_9;
		int32_t L_10 = V_2;
		switch (L_10)
		{
			case 0:
			{
				goto IL_004d;
			}
			case 1:
			{
				goto IL_005a;
			}
			case 2:
			{
				goto IL_0067;
			}
		}
	}
	{
		return;
	}

IL_004d:
	{
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_11;
		L_11 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_12 = V_0;
		NullCheck(L_11);
		Transform_set_forward_mA178B5CF4F0F6133F9AF8ED3A4ECD2C604C60C26(L_11, L_12, NULL);
		return;
	}

IL_005a:
	{
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_13;
		L_13 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_14 = V_0;
		NullCheck(L_13);
		Transform_set_up_m1FBA5A97E5057747AC027AD5897EDE80A554D554(L_13, L_14, NULL);
		return;
	}

IL_0067:
	{
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_15;
		L_15 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_16 = V_0;
		NullCheck(L_15);
		Transform_set_right_mBE8A7189FB1313A8B3E1E10EA538DED15D3E93DA(L_15, L_16, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AllIn1LookAt__ctor_mF02E6C137CA1BC8C258B7C14A30B8C66F158369A (AllIn1LookAt_tDBAC078F7CE06BC8241E5429C930D8586A64D836* __this, const RuntimeMethod* method) 
{
	{
		MonoBehaviour__ctor_m592DB0105CA0BC97AA1C5F4AD27B12D68A3B7C1E(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AllIn1ParticleHelperComponent_SetSceneDirty_mA8C50FFDA9C4CCE517E1FF13921E34A61554D0FD (AllIn1ParticleHelperComponent_tDFD2B3E7E4BE678952CD673F473DBEFEA569C0BB* __this, const RuntimeMethod* method) 
{
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AllIn1ParticleHelperComponent__ctor_m68AF57BA2F4F0E580F068414810D768F0F7FC368 (AllIn1ParticleHelperComponent_tDFD2B3E7E4BE678952CD673F473DBEFEA569C0BB* __this, const RuntimeMethod* method) 
{
	{
		__this->___numberOfCopies = 1;
		__this->___applyEverythingOnChange = (bool)1;
		__this->___minLifetime = (5.0f);
		__this->___maxLifetime = (5.0f);
		__this->___minSpeed = (5.0f);
		__this->___maxSpeed = (5.0f);
		__this->___minSize = (1.0f);
		__this->___maxSize = (1.0f);
		__this->___minNumberOfParticles = ((int32_t)10);
		__this->___maxNumberOfParticles = ((int32_t)10);
		__this->___colorLifetime = 2;
		__this->___sizeLifetime = 2;
		MonoBehaviour__ctor_m592DB0105CA0BC97AA1C5F4AD27B12D68A3B7C1E(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AllIn1ParticleHelperSO__ctor_m9AEBFEDE1FCCE704444E3C59C7C7A3D344601DD8 (AllIn1ParticleHelperSO_tB1703DA97F077A501D1897ACE6C5AEE88C4A8061* __this, const RuntimeMethod* method) 
{
	{
		__this->___minLifetime = (5.0f);
		__this->___maxLifetime = (5.0f);
		__this->___minSpeed = (5.0f);
		__this->___maxSpeed = (5.0f);
		__this->___minSize = (1.0f);
		__this->___maxSize = (1.0f);
		__this->___minNumberOfParticles = ((int32_t)10);
		__this->___maxNumberOfParticles = ((int32_t)10);
		__this->___colorLifetime = 1;
		ScriptableObject__ctor_mD037FDB0B487295EA47F79A4DB1BF1846C9087FF(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AllIn1VfxBounceAnimation_Start_m6980DB7335C5A381ED59D3098EF626C0FD242503 (AllIn1VfxBounceAnimation_tCE51755C53049903743EC707AADB9B8B0019D86E* __this, const RuntimeMethod* method) 
{
	{
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_0;
		L_0 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		NullCheck(L_0);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_1;
		L_1 = Transform_get_position_m69CD5FA214FDAE7BB701552943674846C220FDE1(L_0, NULL);
		__this->___startPosition = L_1;
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AllIn1VfxBounceAnimation_Update_mD47D1739EF4F7267CCE4E33B575AC9E498149727 (AllIn1VfxBounceAnimation_tCE51755C53049903743EC707AADB9B8B0019D86E* __this, const RuntimeMethod* method) 
{
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = __this->___targetOffset;
		float L_1;
		L_1 = Time_get_time_m3A271BB1B20041144AC5B7863B71AB1F0150374B(NULL);
		float L_2 = __this->___speed;
		float L_3;
		L_3 = sinf(((float)il2cpp_codegen_multiply(L_1, L_2)));
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_4;
		L_4 = Vector3_op_Multiply_m87BA7C578F96C8E49BB07088DAAC4649F83B0353_inline(L_0, ((float)(((float)il2cpp_codegen_add(L_3, (1.0f)))/(2.0f))), NULL);
		__this->___animationMovementVector = L_4;
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_5;
		L_5 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_6 = __this->___startPosition;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_7 = __this->___animationMovementVector;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_8;
		L_8 = Vector3_op_Addition_m78C0EC70CB66E8DCAC225743D82B268DAEE92067_inline(L_6, L_7, NULL);
		NullCheck(L_5);
		Transform_set_position_mA1A817124BB41B685043DED2A9BA48CDF37C4156(L_5, L_8, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AllIn1VfxBounceAnimation__ctor_m4F526F9FB3184ECC0EAC866948AE87859C2767C7 (AllIn1VfxBounceAnimation_tCE51755C53049903743EC707AADB9B8B0019D86E* __this, const RuntimeMethod* method) 
{
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0;
		L_0 = Vector3_get_up_m128AF3FDC820BF59D5DE86D973E7DE3F20C3AEBA_inline(NULL);
		__this->___targetOffset = L_0;
		__this->___speed = (1.0f);
		MonoBehaviour__ctor_m592DB0105CA0BC97AA1C5F4AD27B12D68A3B7C1E(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AllIn1VfxComponent_MakeNewMaterial_m995B26BFCD13754FCC6E97DE0A42BA17EB4BBCFF (AllIn1VfxComponent_t2587889FCB75E1748AEF4F9EAE574E8C3631BE54* __this, String_t* ___0_shaderName, bool ___1_notifyWhenDone, const RuntimeMethod* method) 
{
	{
		String_t* L_0 = ___0_shaderName;
		bool L_1;
		L_1 = AllIn1VfxComponent_SetMaterial_mBFA6195608E559482B63D71AB64188AEC8793F89(__this, 0, L_0, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool AllIn1VfxComponent_MakeCopy_m7EEE43932ACDB5E9A9F201770C6C45465D0BAFB5 (AllIn1VfxComponent_t2587889FCB75E1748AEF4F9EAE574E8C3631BE54* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral9CE902BD3933F71AD381D3042D88DF18342E37C4);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralDA39A3EE5E6B4B0D3255BFEF95601890AFD80709);
		s_Il2CppMethodInitialized = true;
	}
	String_t* V_0 = NULL;
	{
		Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* L_0 = __this->___currMaterial;
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_1;
		L_1 = Object_op_Equality_mB6120F782D83091EF56A198FCEBCF066DB4A9605(L_0, (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*)NULL, NULL);
		if (!L_1)
		{
			goto IL_0018;
		}
	}
	{
		bool L_2;
		L_2 = AllIn1VfxComponent_FetchCurrentMaterial_m39F8AE40F3507E0BE6FAADA12ADA81C59718B026(__this, NULL);
		if (!L_2)
		{
			goto IL_0018;
		}
	}
	{
		return (bool)0;
	}

IL_0018:
	{
		Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* L_3 = __this->___currMaterial;
		NullCheck(L_3);
		Shader_tADC867D36B7876EE22427FAA2CE485105F4EE692* L_4;
		L_4 = Material_get_shader_m8B0C11AE6F2AD7DE30AF52D3195EB716F7A71983(L_3, NULL);
		NullCheck(L_4);
		String_t* L_5;
		L_5 = Object_get_name_mAC2F6B897CF1303BA4249B4CB55271AFACBB6392(L_4, NULL);
		V_0 = L_5;
		String_t* L_6 = V_0;
		NullCheck(L_6);
		bool L_7;
		L_7 = String_Contains_m6D77B121FADA7CA5F397C0FABB65DA62DF03B6C3(L_6, _stringLiteral9CE902BD3933F71AD381D3042D88DF18342E37C4, NULL);
		if (!L_7)
		{
			goto IL_0047;
		}
	}
	{
		String_t* L_8 = V_0;
		NullCheck(L_8);
		String_t* L_9;
		L_9 = String_Replace_mABDB7003A1D0AEDCAE9FF85E3DFFFBA752D2A166(L_8, _stringLiteral9CE902BD3933F71AD381D3042D88DF18342E37C4, _stringLiteralDA39A3EE5E6B4B0D3255BFEF95601890AFD80709, NULL);
		V_0 = L_9;
	}

IL_0047:
	{
		String_t* L_10 = V_0;
		bool L_11;
		L_11 = AllIn1VfxComponent_SetMaterial_mBFA6195608E559482B63D71AB64188AEC8793F89(__this, 1, L_10, NULL);
		return (bool)1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool AllIn1VfxComponent_FetchCurrentMaterial_m39F8AE40F3507E0BE6FAADA12ADA81C59718B026 (AllIn1VfxComponent_t2587889FCB75E1748AEF4F9EAE574E8C3631BE54* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Component_GetComponent_TisGraphic_tCBFCA4585A19E2B75465AECFEAC43F4016BF7931_mFE18E20FC92395F90E776DBC4CD214A4F2D97D90_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Component_GetComponent_TisRenderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF_mC91ACC92AD57CA6CA00991DAF1DB3830BCE07AF8_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	bool V_0 = false;
	Renderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF* V_1 = NULL;
	Graphic_tCBFCA4585A19E2B75465AECFEAC43F4016BF7931* V_2 = NULL;
	{
		V_0 = (bool)0;
		Renderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF* L_0;
		L_0 = Component_GetComponent_TisRenderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF_mC91ACC92AD57CA6CA00991DAF1DB3830BCE07AF8(__this, Component_GetComponent_TisRenderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF_mC91ACC92AD57CA6CA00991DAF1DB3830BCE07AF8_RuntimeMethod_var);
		V_1 = L_0;
		Renderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF* L_1 = V_1;
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_2;
		L_2 = Object_op_Inequality_mD0BE578448EAA61948F25C32F8DD55AB1F778602(L_1, (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*)NULL, NULL);
		if (!L_2)
		{
			goto IL_0022;
		}
	}
	{
		V_0 = (bool)1;
		Renderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF* L_3 = V_1;
		NullCheck(L_3);
		Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* L_4;
		L_4 = Renderer_get_sharedMaterial_mA2E0CA0A564617FFC3E0E50947C6300082C35F81(L_3, NULL);
		__this->___currMaterial = L_4;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___currMaterial), (void*)L_4);
		goto IL_0040;
	}

IL_0022:
	{
		Graphic_tCBFCA4585A19E2B75465AECFEAC43F4016BF7931* L_5;
		L_5 = Component_GetComponent_TisGraphic_tCBFCA4585A19E2B75465AECFEAC43F4016BF7931_mFE18E20FC92395F90E776DBC4CD214A4F2D97D90(__this, Component_GetComponent_TisGraphic_tCBFCA4585A19E2B75465AECFEAC43F4016BF7931_mFE18E20FC92395F90E776DBC4CD214A4F2D97D90_RuntimeMethod_var);
		V_2 = L_5;
		Graphic_tCBFCA4585A19E2B75465AECFEAC43F4016BF7931* L_6 = V_2;
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_7;
		L_7 = Object_op_Inequality_mD0BE578448EAA61948F25C32F8DD55AB1F778602(L_6, (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*)NULL, NULL);
		if (!L_7)
		{
			goto IL_0040;
		}
	}
	{
		V_0 = (bool)1;
		Graphic_tCBFCA4585A19E2B75465AECFEAC43F4016BF7931* L_8 = V_2;
		NullCheck(L_8);
		Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* L_9;
		L_9 = VirtualFuncInvoker0< Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* >::Invoke(32, L_8);
		__this->___currMaterial = L_9;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___currMaterial), (void*)L_9);
	}

IL_0040:
	{
		bool L_10 = V_0;
		if (L_10)
		{
			goto IL_004b;
		}
	}
	{
		AllIn1VfxComponent_MissingRenderer_mB023CB700C00CC98DFECD40F146C57164D16C77E(__this, NULL);
		return (bool)1;
	}

IL_004b:
	{
		return (bool)0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AllIn1VfxComponent_ResetAllProperties_m50ABAF51C59617C87828194552AA89EC998BAFB2 (AllIn1VfxComponent_t2587889FCB75E1748AEF4F9EAE574E8C3631BE54* __this, String_t* ___0_shaderName, const RuntimeMethod* method) 
{
	{
		String_t* L_0 = ___0_shaderName;
		bool L_1;
		L_1 = AllIn1VfxComponent_SetMaterial_mBFA6195608E559482B63D71AB64188AEC8793F89(__this, 2, L_0, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool AllIn1VfxComponent_SetMaterial_mBFA6195608E559482B63D71AB64188AEC8793F89 (AllIn1VfxComponent_t2587889FCB75E1748AEF4F9EAE574E8C3631BE54* __this, int32_t ___0_action, String_t* ___1_shaderName, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Application_tDB03BE91CDF0ACA614A5E0B67CFB77C44EB19B21_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Component_GetComponent_TisGraphic_tCBFCA4585A19E2B75465AECFEAC43F4016BF7931_mFE18E20FC92395F90E776DBC4CD214A4F2D97D90_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Component_GetComponent_TisRenderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF_mC91ACC92AD57CA6CA00991DAF1DB3830BCE07AF8_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Shader_tADC867D36B7876EE22427FAA2CE485105F4EE692_0_0_0_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Shader_tADC867D36B7876EE22427FAA2CE485105F4EE692_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Type_t_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Shader_tADC867D36B7876EE22427FAA2CE485105F4EE692* V_0 = NULL;
	bool V_1 = false;
	Graphic_tCBFCA4585A19E2B75465AECFEAC43F4016BF7931* V_2 = NULL;
	{
		String_t* L_0 = ___1_shaderName;
		RuntimeTypeHandle_t332A452B8B6179E4469B69525D0FE82A88030F7B L_1 = { reinterpret_cast<intptr_t> (Shader_tADC867D36B7876EE22427FAA2CE485105F4EE692_0_0_0_var) };
		il2cpp_codegen_runtime_class_init_inline(Type_t_il2cpp_TypeInfo_var);
		Type_t* L_2;
		L_2 = Type_GetTypeFromHandle_m6062B81682F79A4D6DF2640692EE6D9987858C57(L_1, NULL);
		Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C* L_3;
		L_3 = Resources_Load_m6CD8FBBCCFFF22179FA0E7B1806B888103008D33(L_0, L_2, NULL);
		V_0 = ((Shader_tADC867D36B7876EE22427FAA2CE485105F4EE692*)IsInstSealed((RuntimeObject*)L_3, Shader_tADC867D36B7876EE22427FAA2CE485105F4EE692_il2cpp_TypeInfo_var));
		il2cpp_codegen_runtime_class_init_inline(Application_tDB03BE91CDF0ACA614A5E0B67CFB77C44EB19B21_il2cpp_TypeInfo_var);
		bool L_4;
		L_4 = Application_get_isPlaying_m25B0ABDFEF54F5370CD3F263A813540843D00F34(NULL);
		if (L_4)
		{
			goto IL_0104;
		}
	}
	{
		il2cpp_codegen_runtime_class_init_inline(Application_tDB03BE91CDF0ACA614A5E0B67CFB77C44EB19B21_il2cpp_TypeInfo_var);
		bool L_5;
		L_5 = Application_get_isEditor_mEAC51E3ACE6DCE438087FB14BD75A3C219D354D0(NULL);
		if (!L_5)
		{
			goto IL_0104;
		}
	}
	{
		Shader_tADC867D36B7876EE22427FAA2CE485105F4EE692* L_6 = V_0;
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_7;
		L_7 = Object_op_Inequality_mD0BE578448EAA61948F25C32F8DD55AB1F778602(L_6, (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*)NULL, NULL);
		if (!L_7)
		{
			goto IL_0104;
		}
	}
	{
		V_1 = (bool)0;
		Renderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF* L_8;
		L_8 = Component_GetComponent_TisRenderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF_mC91ACC92AD57CA6CA00991DAF1DB3830BCE07AF8(__this, Component_GetComponent_TisRenderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF_mC91ACC92AD57CA6CA00991DAF1DB3830BCE07AF8_RuntimeMethod_var);
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_9;
		L_9 = Object_op_Inequality_mD0BE578448EAA61948F25C32F8DD55AB1F778602(L_8, (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*)NULL, NULL);
		if (!L_9)
		{
			goto IL_009c;
		}
	}
	{
		V_1 = (bool)1;
		Renderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF* L_10;
		L_10 = Component_GetComponent_TisRenderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF_mC91ACC92AD57CA6CA00991DAF1DB3830BCE07AF8(__this, Component_GetComponent_TisRenderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF_mC91ACC92AD57CA6CA00991DAF1DB3830BCE07AF8_RuntimeMethod_var);
		NullCheck(L_10);
		Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* L_11;
		L_11 = Renderer_get_sharedMaterial_mA2E0CA0A564617FFC3E0E50947C6300082C35F81(L_10, NULL);
		Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* L_12 = (Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3*)il2cpp_codegen_object_new(Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3_il2cpp_TypeInfo_var);
		Material__ctor_mFCC42FB90257F1E8F7516A8640A79C465A39961C(L_12, L_11, NULL);
		__this->___prevMaterial = L_12;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___prevMaterial), (void*)L_12);
		Shader_tADC867D36B7876EE22427FAA2CE485105F4EE692* L_13 = V_0;
		Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* L_14 = (Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3*)il2cpp_codegen_object_new(Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3_il2cpp_TypeInfo_var);
		Material__ctor_m7FDF47105D66D19591BE505A0C42B0F90D88C9BF(L_14, L_13, NULL);
		__this->___currMaterial = L_14;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___currMaterial), (void*)L_14);
		Renderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF* L_15;
		L_15 = Component_GetComponent_TisRenderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF_mC91ACC92AD57CA6CA00991DAF1DB3830BCE07AF8(__this, Component_GetComponent_TisRenderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF_mC91ACC92AD57CA6CA00991DAF1DB3830BCE07AF8_RuntimeMethod_var);
		Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* L_16 = __this->___currMaterial;
		NullCheck(L_15);
		Renderer_set_sharedMaterial_m5E842F9A06CFB7B77656EB319881CB4B3E8E4288(L_15, L_16, NULL);
		Renderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF* L_17;
		L_17 = Component_GetComponent_TisRenderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF_mC91ACC92AD57CA6CA00991DAF1DB3830BCE07AF8(__this, Component_GetComponent_TisRenderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF_mC91ACC92AD57CA6CA00991DAF1DB3830BCE07AF8_RuntimeMethod_var);
		NullCheck(L_17);
		Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* L_18;
		L_18 = Renderer_get_sharedMaterial_mA2E0CA0A564617FFC3E0E50947C6300082C35F81(L_17, NULL);
		NullCheck(L_18);
		Object_set_hideFlags_mACB8BFC903FB3B01BBD427753E791BF28B5E33D4(L_18, 0, NULL);
		__this->___matAssigned = (bool)1;
		int32_t L_19 = ___0_action;
		AllIn1VfxComponent_DoAfterSetAction_mE585EF484C1E4657FBAAF98041E7A2772829E727(__this, L_19, NULL);
		goto IL_00f1;
	}

IL_009c:
	{
		Graphic_tCBFCA4585A19E2B75465AECFEAC43F4016BF7931* L_20;
		L_20 = Component_GetComponent_TisGraphic_tCBFCA4585A19E2B75465AECFEAC43F4016BF7931_mFE18E20FC92395F90E776DBC4CD214A4F2D97D90(__this, Component_GetComponent_TisGraphic_tCBFCA4585A19E2B75465AECFEAC43F4016BF7931_mFE18E20FC92395F90E776DBC4CD214A4F2D97D90_RuntimeMethod_var);
		V_2 = L_20;
		Graphic_tCBFCA4585A19E2B75465AECFEAC43F4016BF7931* L_21 = V_2;
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_22;
		L_22 = Object_op_Inequality_mD0BE578448EAA61948F25C32F8DD55AB1F778602(L_21, (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*)NULL, NULL);
		if (!L_22)
		{
			goto IL_00f1;
		}
	}
	{
		V_1 = (bool)1;
		Graphic_tCBFCA4585A19E2B75465AECFEAC43F4016BF7931* L_23 = V_2;
		NullCheck(L_23);
		Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* L_24;
		L_24 = VirtualFuncInvoker0< Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* >::Invoke(32, L_23);
		Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* L_25 = (Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3*)il2cpp_codegen_object_new(Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3_il2cpp_TypeInfo_var);
		Material__ctor_mFCC42FB90257F1E8F7516A8640A79C465A39961C(L_25, L_24, NULL);
		__this->___prevMaterial = L_25;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___prevMaterial), (void*)L_25);
		Shader_tADC867D36B7876EE22427FAA2CE485105F4EE692* L_26 = V_0;
		Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* L_27 = (Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3*)il2cpp_codegen_object_new(Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3_il2cpp_TypeInfo_var);
		Material__ctor_m7FDF47105D66D19591BE505A0C42B0F90D88C9BF(L_27, L_26, NULL);
		__this->___currMaterial = L_27;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___currMaterial), (void*)L_27);
		Graphic_tCBFCA4585A19E2B75465AECFEAC43F4016BF7931* L_28 = V_2;
		Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* L_29 = __this->___currMaterial;
		NullCheck(L_28);
		VirtualActionInvoker1< Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* >::Invoke(33, L_28, L_29);
		Graphic_tCBFCA4585A19E2B75465AECFEAC43F4016BF7931* L_30 = V_2;
		NullCheck(L_30);
		Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* L_31;
		L_31 = VirtualFuncInvoker0< Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* >::Invoke(32, L_30);
		NullCheck(L_31);
		Object_set_hideFlags_mACB8BFC903FB3B01BBD427753E791BF28B5E33D4(L_31, 0, NULL);
		__this->___matAssigned = (bool)1;
		int32_t L_32 = ___0_action;
		AllIn1VfxComponent_DoAfterSetAction_mE585EF484C1E4657FBAAF98041E7A2772829E727(__this, L_32, NULL);
	}

IL_00f1:
	{
		bool L_33 = V_1;
		if (L_33)
		{
			goto IL_00fc;
		}
	}
	{
		AllIn1VfxComponent_MissingRenderer_mB023CB700C00CC98DFECD40F146C57164D16C77E(__this, NULL);
		goto IL_0102;
	}

IL_00fc:
	{
		AllIn1VfxComponent_SetSceneDirty_m9E6A673E9394C3A3B775625029362C9D996410EA(__this, NULL);
	}

IL_0102:
	{
		bool L_34 = V_1;
		return L_34;
	}

IL_0104:
	{
		Shader_tADC867D36B7876EE22427FAA2CE485105F4EE692* L_35 = V_0;
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_36;
		L_36 = Object_op_Equality_mB6120F782D83091EF56A198FCEBCF066DB4A9605(L_35, (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*)NULL, NULL);
		return (bool)0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AllIn1VfxComponent_DoAfterSetAction_mE585EF484C1E4657FBAAF98041E7A2772829E727 (AllIn1VfxComponent_t2587889FCB75E1748AEF4F9EAE574E8C3631BE54* __this, int32_t ___0_action, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = ___0_action;
		if (!L_0)
		{
			goto IL_0008;
		}
	}
	{
		int32_t L_1 = ___0_action;
		if ((((int32_t)L_1) == ((int32_t)1)))
		{
			goto IL_000f;
		}
	}
	{
		return;
	}

IL_0008:
	{
		AllIn1VfxComponent_ClearAllKeywords_m59BC37A1506BB4271456D76BF6E53597D2652F6E(__this, NULL);
		return;
	}

IL_000f:
	{
		Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* L_2 = __this->___currMaterial;
		Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* L_3 = __this->___prevMaterial;
		NullCheck(L_2);
		Material_CopyPropertiesFromMaterial_m4148227E6A0B8E66315D8115F656B7F8BEAE915B(L_2, L_3, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool AllIn1VfxComponent_TryCreateNew_mC64450CB86324EEE3F25245B8BC495F20F23F4D7 (AllIn1VfxComponent_t2587889FCB75E1748AEF4F9EAE574E8C3631BE54* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Component_GetComponent_TisGraphic_tCBFCA4585A19E2B75465AECFEAC43F4016BF7931_mFE18E20FC92395F90E776DBC4CD214A4F2D97D90_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Component_GetComponent_TisRenderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF_mC91ACC92AD57CA6CA00991DAF1DB3830BCE07AF8_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralC76190ED0C48EB995A11E863941095B1AA26B582);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralCEE91F51A391C3F771D9B2463C388312AA8DA8CF);
		s_Il2CppMethodInitialized = true;
	}
	bool V_0 = false;
	Renderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF* V_1 = NULL;
	Graphic_tCBFCA4585A19E2B75465AECFEAC43F4016BF7931* V_2 = NULL;
	{
		V_0 = (bool)0;
		Renderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF* L_0;
		L_0 = Component_GetComponent_TisRenderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF_mC91ACC92AD57CA6CA00991DAF1DB3830BCE07AF8(__this, Component_GetComponent_TisRenderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF_mC91ACC92AD57CA6CA00991DAF1DB3830BCE07AF8_RuntimeMethod_var);
		V_1 = L_0;
		Renderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF* L_1 = V_1;
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_2;
		L_2 = Object_op_Inequality_mD0BE578448EAA61948F25C32F8DD55AB1F778602(L_1, (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*)NULL, NULL);
		if (!L_2)
		{
			goto IL_006e;
		}
	}
	{
		V_0 = (bool)1;
		Renderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF* L_3 = V_1;
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_4;
		L_4 = Object_op_Inequality_mD0BE578448EAA61948F25C32F8DD55AB1F778602(L_3, (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*)NULL, NULL);
		if (!L_4)
		{
			goto IL_005a;
		}
	}
	{
		Renderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF* L_5 = V_1;
		NullCheck(L_5);
		Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* L_6;
		L_6 = Renderer_get_sharedMaterial_mA2E0CA0A564617FFC3E0E50947C6300082C35F81(L_5, NULL);
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_7;
		L_7 = Object_op_Inequality_mD0BE578448EAA61948F25C32F8DD55AB1F778602(L_6, (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*)NULL, NULL);
		if (!L_7)
		{
			goto IL_005a;
		}
	}
	{
		Renderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF* L_8 = V_1;
		NullCheck(L_8);
		Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* L_9;
		L_9 = Renderer_get_sharedMaterial_mA2E0CA0A564617FFC3E0E50947C6300082C35F81(L_8, NULL);
		NullCheck(L_9);
		Shader_tADC867D36B7876EE22427FAA2CE485105F4EE692* L_10;
		L_10 = Material_get_shader_m8B0C11AE6F2AD7DE30AF52D3195EB716F7A71983(L_9, NULL);
		NullCheck(L_10);
		String_t* L_11;
		L_11 = Object_get_name_mAC2F6B897CF1303BA4249B4CB55271AFACBB6392(L_10, NULL);
		NullCheck(L_11);
		bool L_12;
		L_12 = String_Contains_m6D77B121FADA7CA5F397C0FABB65DA62DF03B6C3(L_11, _stringLiteralC76190ED0C48EB995A11E863941095B1AA26B582, NULL);
		if (!L_12)
		{
			goto IL_005a;
		}
	}
	{
		AllIn1VfxComponent_ResetAllProperties_m50ABAF51C59617C87828194552AA89EC998BAFB2(__this, _stringLiteralCEE91F51A391C3F771D9B2463C388312AA8DA8CF, NULL);
		AllIn1VfxComponent_ClearAllKeywords_m59BC37A1506BB4271456D76BF6E53597D2652F6E(__this, NULL);
		goto IL_00bb;
	}

IL_005a:
	{
		AllIn1VfxComponent_CleanMaterial_mFAD12AF2EDBE22342D512879ABFF5F12D059BE78(__this, NULL);
		AllIn1VfxComponent_MakeNewMaterial_m995B26BFCD13754FCC6E97DE0A42BA17EB4BBCFF(__this, _stringLiteralCEE91F51A391C3F771D9B2463C388312AA8DA8CF, (bool)0, NULL);
		goto IL_00bb;
	}

IL_006e:
	{
		Graphic_tCBFCA4585A19E2B75465AECFEAC43F4016BF7931* L_13;
		L_13 = Component_GetComponent_TisGraphic_tCBFCA4585A19E2B75465AECFEAC43F4016BF7931_mFE18E20FC92395F90E776DBC4CD214A4F2D97D90(__this, Component_GetComponent_TisGraphic_tCBFCA4585A19E2B75465AECFEAC43F4016BF7931_mFE18E20FC92395F90E776DBC4CD214A4F2D97D90_RuntimeMethod_var);
		V_2 = L_13;
		Graphic_tCBFCA4585A19E2B75465AECFEAC43F4016BF7931* L_14 = V_2;
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_15;
		L_15 = Object_op_Inequality_mD0BE578448EAA61948F25C32F8DD55AB1F778602(L_14, (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*)NULL, NULL);
		if (!L_15)
		{
			goto IL_00bb;
		}
	}
	{
		V_0 = (bool)1;
		Graphic_tCBFCA4585A19E2B75465AECFEAC43F4016BF7931* L_16 = V_2;
		NullCheck(L_16);
		Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* L_17;
		L_17 = VirtualFuncInvoker0< Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* >::Invoke(32, L_16);
		NullCheck(L_17);
		Shader_tADC867D36B7876EE22427FAA2CE485105F4EE692* L_18;
		L_18 = Material_get_shader_m8B0C11AE6F2AD7DE30AF52D3195EB716F7A71983(L_17, NULL);
		NullCheck(L_18);
		String_t* L_19;
		L_19 = Object_get_name_mAC2F6B897CF1303BA4249B4CB55271AFACBB6392(L_18, NULL);
		NullCheck(L_19);
		bool L_20;
		L_20 = String_Contains_m6D77B121FADA7CA5F397C0FABB65DA62DF03B6C3(L_19, _stringLiteralC76190ED0C48EB995A11E863941095B1AA26B582, NULL);
		if (!L_20)
		{
			goto IL_00af;
		}
	}
	{
		AllIn1VfxComponent_ResetAllProperties_m50ABAF51C59617C87828194552AA89EC998BAFB2(__this, _stringLiteralCEE91F51A391C3F771D9B2463C388312AA8DA8CF, NULL);
		AllIn1VfxComponent_ClearAllKeywords_m59BC37A1506BB4271456D76BF6E53597D2652F6E(__this, NULL);
		goto IL_00bb;
	}

IL_00af:
	{
		AllIn1VfxComponent_MakeNewMaterial_m995B26BFCD13754FCC6E97DE0A42BA17EB4BBCFF(__this, _stringLiteralCEE91F51A391C3F771D9B2463C388312AA8DA8CF, (bool)0, NULL);
	}

IL_00bb:
	{
		bool L_21 = V_0;
		if (L_21)
		{
			goto IL_00c4;
		}
	}
	{
		AllIn1VfxComponent_MissingRenderer_mB023CB700C00CC98DFECD40F146C57164D16C77E(__this, NULL);
	}

IL_00c4:
	{
		AllIn1VfxComponent_SetSceneDirty_m9E6A673E9394C3A3B775625029362C9D996410EA(__this, NULL);
		bool L_22 = V_0;
		return L_22;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AllIn1VfxComponent_ClearAllKeywords_m59BC37A1506BB4271456D76BF6E53597D2652F6E (AllIn1VfxComponent_t2587889FCB75E1748AEF4F9EAE574E8C3631BE54* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral0023D6A9F7F3B566DFB2EFA5BE5820D9509D681E);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral0299CC5F40C577F300BB29854CBAAD8B68ABF5A0);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral0401A6C1F7012C721901C937730CA854AED44F14);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral040793655BC228982AF83F2DE9C015C189306364);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral1340C6E5B2B210689A25CF2270555B16E1489106);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral14D479CBF77090A6D30F543484D1D50B87795337);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral158697E57921300501C71DFA8626FCAE1F8FD030);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral185035D897E40E37CE218ED2FFA2B3FD8F8F8F22);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral1E1912CAB55AF7DEF1C5B72F955FFFBCB9884AB5);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral1E74A2EC3C4B69C55D0D1B56F81D53F03FC58D57);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral2F3FA2011635BA3ADF04F3A6636CEA5D2D14EF88);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral34AD56288A03AA8D7B7BE17E549C5FB602F9E885);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral3C1DB6BCE7F7EC4956D0CD51C602C4B9D94DE193);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral3F868CB06E969FC20ED35E84ACC75C8E94BE5789);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral40728BBCE4EE91640605FACC63DB3CEC63B83B80);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral4696BEB1B4DD525F1293813D16EC3A02B2B12251);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral4A68E99ECA06FD65FDFD5FCD7FECC5839F4C0DBC);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral4AA79340AA7669BF821B747B748410DB52DA3261);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral51C68DEA8F259A907A0498E34875D1BD0A6CED03);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral5398DC3D4FFCD34741F382F596A262B6FA2922AC);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral561612A9F818B42EF04003F9D6952E8EC5D027ED);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral5904389432FCA9BFEA539A8A22DDC0BD69F94F04);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral5A02191D32DC069B431D3E54FF28CEC7767178DB);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral5CE72404582BDAE77C15BF3F30FEFFD1A81D8F8C);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral5D5BF7644F6756216DBAE69270F57FE11BEAE972);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral5F61F506633DBCEB100F2CA993128F6DC6A9C618);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral638A6BF6390D12422CAC4910C95F16CFBCE6D50B);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral6677C73BF64E77B045EA94D2AA385D7540F0A39D);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral6757D44A85F13AA2863BDC7DCEF5E30BC21278BD);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral7281FF2F619273B6F998E3D3DCA0CFAF23CCFAD2);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral72A7CAD40240F38905C2C0E1E50F4449AD82AEAB);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral757FDB668BCAADD3B45A3175E6AC8EBACA3EEB65);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral76264918B150B6FD44125E9CE7F711A3689B9700);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral79BCB0C2B8C16448AD04D20C4925CF363A67BAA9);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral7D2ED17259CF0DC4179D682E4471BF85B5574BBA);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral7DDFF290B24173A5DC1BC9BC22C9322BB36CFC10);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral89115C0E93F9302CD0B8CD7BB21C410B6162644D);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral8DED3C670AB3C2E5A20C926F89F96926BE24AC79);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral92274FFFE307A7AA40F70ECBD38BB73705AC9E5B);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral94BD673B8551A4C6D6A807ED9D7A6C37D921072F);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral94F92EDABB0744C4E72E030B935FEC2580C8A614);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral954CC189A0FC8B78E623F527148C0981714376AC);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral99117A43311619936587FBCABCC9B16B687AB302);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral9ABAD8FF849D104EA8DB7481A66BB4B9FD7143A2);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralA27C6266A902DDCC5C73F82BEBBBDF1C87CCFFFA);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralA66067A208E75497516342A152D58B32B1C89075);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralA87819C2031146742C1F5350BC509988DACBE9F9);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralAAE3A15202D762AC5E5D99D35460A3E2C88307E1);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralB1D928ABA3C2555CCA12F60991D28C7F5A0E200E);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralB8649C06FE9FBAB8E997CBD8796167F6283CAB2C);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralBCBD8C7003675066255066C8241D1DCB43737145);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralC1321093811095513C44D23E1503BACF248356F0);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralC3A9DE289B76C73BE63D02B5A01D7C45B656AD49);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralC879E94E49560F1B236BDF1611C5EC619EA5B93F);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralCBE96480BEB47650A1397787D142CB9736546564);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralD01835DFD9412FEB7AA45A9F2E69029F2B71B936);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralD34B357F606D69B3A243155329F7C26E9ED9B03B);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralD678A75C242A16DA78744D87F52BD6BA550F95C4);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralDC639E8CFF8B48439F2DC546D026EE8EAB89718B);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralE8B1F4E65A0B35AB6619D979A27DD1766DEB7039);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralEFF7EFBB29A0F779F9CF65D30804B3D60468618E);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralF4B62A69FCAFBA03A81C4FD2F7CF77104D7CB48D);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralFD3081C211F1405167EBF5BDD775516383D38F4F);
		s_Il2CppMethodInitialized = true;
	}
	{
		AllIn1VfxComponent_SetKeyword_m95EC164255D1EA16944BE4564EFE7730F9864BF2(__this, _stringLiteral34AD56288A03AA8D7B7BE17E549C5FB602F9E885, (bool)0, NULL);
		AllIn1VfxComponent_SetKeyword_m95EC164255D1EA16944BE4564EFE7730F9864BF2(__this, _stringLiteral72A7CAD40240F38905C2C0E1E50F4449AD82AEAB, (bool)0, NULL);
		AllIn1VfxComponent_SetKeyword_m95EC164255D1EA16944BE4564EFE7730F9864BF2(__this, _stringLiteral1340C6E5B2B210689A25CF2270555B16E1489106, (bool)0, NULL);
		AllIn1VfxComponent_SetKeyword_m95EC164255D1EA16944BE4564EFE7730F9864BF2(__this, _stringLiteralC879E94E49560F1B236BDF1611C5EC619EA5B93F, (bool)0, NULL);
		AllIn1VfxComponent_SetKeyword_m95EC164255D1EA16944BE4564EFE7730F9864BF2(__this, _stringLiteral2F3FA2011635BA3ADF04F3A6636CEA5D2D14EF88, (bool)0, NULL);
		AllIn1VfxComponent_SetKeyword_m95EC164255D1EA16944BE4564EFE7730F9864BF2(__this, _stringLiteral185035D897E40E37CE218ED2FFA2B3FD8F8F8F22, (bool)0, NULL);
		AllIn1VfxComponent_SetKeyword_m95EC164255D1EA16944BE4564EFE7730F9864BF2(__this, _stringLiteral5D5BF7644F6756216DBAE69270F57FE11BEAE972, (bool)0, NULL);
		AllIn1VfxComponent_SetKeyword_m95EC164255D1EA16944BE4564EFE7730F9864BF2(__this, _stringLiteral0401A6C1F7012C721901C937730CA854AED44F14, (bool)0, NULL);
		AllIn1VfxComponent_SetKeyword_m95EC164255D1EA16944BE4564EFE7730F9864BF2(__this, _stringLiteral5CE72404582BDAE77C15BF3F30FEFFD1A81D8F8C, (bool)0, NULL);
		AllIn1VfxComponent_SetKeyword_m95EC164255D1EA16944BE4564EFE7730F9864BF2(__this, _stringLiteral94F92EDABB0744C4E72E030B935FEC2580C8A614, (bool)0, NULL);
		AllIn1VfxComponent_SetKeyword_m95EC164255D1EA16944BE4564EFE7730F9864BF2(__this, _stringLiteral76264918B150B6FD44125E9CE7F711A3689B9700, (bool)0, NULL);
		AllIn1VfxComponent_SetKeyword_m95EC164255D1EA16944BE4564EFE7730F9864BF2(__this, _stringLiteral040793655BC228982AF83F2DE9C015C189306364, (bool)0, NULL);
		AllIn1VfxComponent_SetKeyword_m95EC164255D1EA16944BE4564EFE7730F9864BF2(__this, _stringLiteralCBE96480BEB47650A1397787D142CB9736546564, (bool)0, NULL);
		AllIn1VfxComponent_SetKeyword_m95EC164255D1EA16944BE4564EFE7730F9864BF2(__this, _stringLiteral4AA79340AA7669BF821B747B748410DB52DA3261, (bool)0, NULL);
		AllIn1VfxComponent_SetKeyword_m95EC164255D1EA16944BE4564EFE7730F9864BF2(__this, _stringLiteral14D479CBF77090A6D30F543484D1D50B87795337, (bool)0, NULL);
		AllIn1VfxComponent_SetKeyword_m95EC164255D1EA16944BE4564EFE7730F9864BF2(__this, _stringLiteral8DED3C670AB3C2E5A20C926F89F96926BE24AC79, (bool)0, NULL);
		AllIn1VfxComponent_SetKeyword_m95EC164255D1EA16944BE4564EFE7730F9864BF2(__this, _stringLiteral94BD673B8551A4C6D6A807ED9D7A6C37D921072F, (bool)0, NULL);
		AllIn1VfxComponent_SetKeyword_m95EC164255D1EA16944BE4564EFE7730F9864BF2(__this, _stringLiteral7DDFF290B24173A5DC1BC9BC22C9322BB36CFC10, (bool)0, NULL);
		AllIn1VfxComponent_SetKeyword_m95EC164255D1EA16944BE4564EFE7730F9864BF2(__this, _stringLiteralF4B62A69FCAFBA03A81C4FD2F7CF77104D7CB48D, (bool)0, NULL);
		AllIn1VfxComponent_SetKeyword_m95EC164255D1EA16944BE4564EFE7730F9864BF2(__this, _stringLiteral51C68DEA8F259A907A0498E34875D1BD0A6CED03, (bool)0, NULL);
		AllIn1VfxComponent_SetKeyword_m95EC164255D1EA16944BE4564EFE7730F9864BF2(__this, _stringLiteralDC639E8CFF8B48439F2DC546D026EE8EAB89718B, (bool)0, NULL);
		AllIn1VfxComponent_SetKeyword_m95EC164255D1EA16944BE4564EFE7730F9864BF2(__this, _stringLiteralA27C6266A902DDCC5C73F82BEBBBDF1C87CCFFFA, (bool)0, NULL);
		AllIn1VfxComponent_SetKeyword_m95EC164255D1EA16944BE4564EFE7730F9864BF2(__this, _stringLiteralA87819C2031146742C1F5350BC509988DACBE9F9, (bool)0, NULL);
		AllIn1VfxComponent_SetKeyword_m95EC164255D1EA16944BE4564EFE7730F9864BF2(__this, _stringLiteralB8649C06FE9FBAB8E997CBD8796167F6283CAB2C, (bool)0, NULL);
		AllIn1VfxComponent_SetKeyword_m95EC164255D1EA16944BE4564EFE7730F9864BF2(__this, _stringLiteralB1D928ABA3C2555CCA12F60991D28C7F5A0E200E, (bool)0, NULL);
		AllIn1VfxComponent_SetKeyword_m95EC164255D1EA16944BE4564EFE7730F9864BF2(__this, _stringLiteralD01835DFD9412FEB7AA45A9F2E69029F2B71B936, (bool)0, NULL);
		AllIn1VfxComponent_SetKeyword_m95EC164255D1EA16944BE4564EFE7730F9864BF2(__this, _stringLiteral954CC189A0FC8B78E623F527148C0981714376AC, (bool)0, NULL);
		AllIn1VfxComponent_SetKeyword_m95EC164255D1EA16944BE4564EFE7730F9864BF2(__this, _stringLiteral9ABAD8FF849D104EA8DB7481A66BB4B9FD7143A2, (bool)0, NULL);
		AllIn1VfxComponent_SetKeyword_m95EC164255D1EA16944BE4564EFE7730F9864BF2(__this, _stringLiteral89115C0E93F9302CD0B8CD7BB21C410B6162644D, (bool)0, NULL);
		AllIn1VfxComponent_SetKeyword_m95EC164255D1EA16944BE4564EFE7730F9864BF2(__this, _stringLiteralFD3081C211F1405167EBF5BDD775516383D38F4F, (bool)0, NULL);
		AllIn1VfxComponent_SetKeyword_m95EC164255D1EA16944BE4564EFE7730F9864BF2(__this, _stringLiteral158697E57921300501C71DFA8626FCAE1F8FD030, (bool)0, NULL);
		AllIn1VfxComponent_SetKeyword_m95EC164255D1EA16944BE4564EFE7730F9864BF2(__this, _stringLiteral4696BEB1B4DD525F1293813D16EC3A02B2B12251, (bool)0, NULL);
		AllIn1VfxComponent_SetKeyword_m95EC164255D1EA16944BE4564EFE7730F9864BF2(__this, _stringLiteral0023D6A9F7F3B566DFB2EFA5BE5820D9509D681E, (bool)0, NULL);
		AllIn1VfxComponent_SetKeyword_m95EC164255D1EA16944BE4564EFE7730F9864BF2(__this, _stringLiteral99117A43311619936587FBCABCC9B16B687AB302, (bool)0, NULL);
		AllIn1VfxComponent_SetKeyword_m95EC164255D1EA16944BE4564EFE7730F9864BF2(__this, _stringLiteral1E74A2EC3C4B69C55D0D1B56F81D53F03FC58D57, (bool)0, NULL);
		AllIn1VfxComponent_SetKeyword_m95EC164255D1EA16944BE4564EFE7730F9864BF2(__this, _stringLiteral6757D44A85F13AA2863BDC7DCEF5E30BC21278BD, (bool)0, NULL);
		AllIn1VfxComponent_SetKeyword_m95EC164255D1EA16944BE4564EFE7730F9864BF2(__this, _stringLiteral5398DC3D4FFCD34741F382F596A262B6FA2922AC, (bool)0, NULL);
		AllIn1VfxComponent_SetKeyword_m95EC164255D1EA16944BE4564EFE7730F9864BF2(__this, _stringLiteralE8B1F4E65A0B35AB6619D979A27DD1766DEB7039, (bool)0, NULL);
		AllIn1VfxComponent_SetKeyword_m95EC164255D1EA16944BE4564EFE7730F9864BF2(__this, _stringLiteralBCBD8C7003675066255066C8241D1DCB43737145, (bool)0, NULL);
		AllIn1VfxComponent_SetKeyword_m95EC164255D1EA16944BE4564EFE7730F9864BF2(__this, _stringLiteral7281FF2F619273B6F998E3D3DCA0CFAF23CCFAD2, (bool)0, NULL);
		AllIn1VfxComponent_SetKeyword_m95EC164255D1EA16944BE4564EFE7730F9864BF2(__this, _stringLiteral6677C73BF64E77B045EA94D2AA385D7540F0A39D, (bool)0, NULL);
		AllIn1VfxComponent_SetKeyword_m95EC164255D1EA16944BE4564EFE7730F9864BF2(__this, _stringLiteral0299CC5F40C577F300BB29854CBAAD8B68ABF5A0, (bool)0, NULL);
		AllIn1VfxComponent_SetKeyword_m95EC164255D1EA16944BE4564EFE7730F9864BF2(__this, _stringLiteral4A68E99ECA06FD65FDFD5FCD7FECC5839F4C0DBC, (bool)0, NULL);
		AllIn1VfxComponent_SetKeyword_m95EC164255D1EA16944BE4564EFE7730F9864BF2(__this, _stringLiteral757FDB668BCAADD3B45A3175E6AC8EBACA3EEB65, (bool)0, NULL);
		AllIn1VfxComponent_SetKeyword_m95EC164255D1EA16944BE4564EFE7730F9864BF2(__this, _stringLiteral638A6BF6390D12422CAC4910C95F16CFBCE6D50B, (bool)0, NULL);
		AllIn1VfxComponent_SetKeyword_m95EC164255D1EA16944BE4564EFE7730F9864BF2(__this, _stringLiteralA66067A208E75497516342A152D58B32B1C89075, (bool)0, NULL);
		AllIn1VfxComponent_SetKeyword_m95EC164255D1EA16944BE4564EFE7730F9864BF2(__this, _stringLiteral92274FFFE307A7AA40F70ECBD38BB73705AC9E5B, (bool)0, NULL);
		AllIn1VfxComponent_SetKeyword_m95EC164255D1EA16944BE4564EFE7730F9864BF2(__this, _stringLiteral3C1DB6BCE7F7EC4956D0CD51C602C4B9D94DE193, (bool)0, NULL);
		AllIn1VfxComponent_SetKeyword_m95EC164255D1EA16944BE4564EFE7730F9864BF2(__this, _stringLiteral3F868CB06E969FC20ED35E84ACC75C8E94BE5789, (bool)0, NULL);
		AllIn1VfxComponent_SetKeyword_m95EC164255D1EA16944BE4564EFE7730F9864BF2(__this, _stringLiteral7D2ED17259CF0DC4179D682E4471BF85B5574BBA, (bool)0, NULL);
		AllIn1VfxComponent_SetKeyword_m95EC164255D1EA16944BE4564EFE7730F9864BF2(__this, _stringLiteral5A02191D32DC069B431D3E54FF28CEC7767178DB, (bool)0, NULL);
		AllIn1VfxComponent_SetKeyword_m95EC164255D1EA16944BE4564EFE7730F9864BF2(__this, _stringLiteral5F61F506633DBCEB100F2CA993128F6DC6A9C618, (bool)0, NULL);
		AllIn1VfxComponent_SetKeyword_m95EC164255D1EA16944BE4564EFE7730F9864BF2(__this, _stringLiteralD34B357F606D69B3A243155329F7C26E9ED9B03B, (bool)0, NULL);
		AllIn1VfxComponent_SetKeyword_m95EC164255D1EA16944BE4564EFE7730F9864BF2(__this, _stringLiteral5904389432FCA9BFEA539A8A22DDC0BD69F94F04, (bool)0, NULL);
		AllIn1VfxComponent_SetKeyword_m95EC164255D1EA16944BE4564EFE7730F9864BF2(__this, _stringLiteral561612A9F818B42EF04003F9D6952E8EC5D027ED, (bool)0, NULL);
		AllIn1VfxComponent_SetKeyword_m95EC164255D1EA16944BE4564EFE7730F9864BF2(__this, _stringLiteralD678A75C242A16DA78744D87F52BD6BA550F95C4, (bool)0, NULL);
		AllIn1VfxComponent_SetKeyword_m95EC164255D1EA16944BE4564EFE7730F9864BF2(__this, _stringLiteralAAE3A15202D762AC5E5D99D35460A3E2C88307E1, (bool)0, NULL);
		AllIn1VfxComponent_SetKeyword_m95EC164255D1EA16944BE4564EFE7730F9864BF2(__this, _stringLiteral79BCB0C2B8C16448AD04D20C4925CF363A67BAA9, (bool)0, NULL);
		AllIn1VfxComponent_SetKeyword_m95EC164255D1EA16944BE4564EFE7730F9864BF2(__this, _stringLiteral40728BBCE4EE91640605FACC63DB3CEC63B83B80, (bool)0, NULL);
		AllIn1VfxComponent_SetKeyword_m95EC164255D1EA16944BE4564EFE7730F9864BF2(__this, _stringLiteralEFF7EFBB29A0F779F9CF65D30804B3D60468618E, (bool)0, NULL);
		AllIn1VfxComponent_SetKeyword_m95EC164255D1EA16944BE4564EFE7730F9864BF2(__this, _stringLiteralC3A9DE289B76C73BE63D02B5A01D7C45B656AD49, (bool)0, NULL);
		AllIn1VfxComponent_SetKeyword_m95EC164255D1EA16944BE4564EFE7730F9864BF2(__this, _stringLiteral1E1912CAB55AF7DEF1C5B72F955FFFBCB9884AB5, (bool)0, NULL);
		AllIn1VfxComponent_SetKeyword_m95EC164255D1EA16944BE4564EFE7730F9864BF2(__this, _stringLiteralC1321093811095513C44D23E1503BACF248356F0, (bool)0, NULL);
		AllIn1VfxComponent_SetSceneDirty_m9E6A673E9394C3A3B775625029362C9D996410EA(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AllIn1VfxComponent_SetKeyword_m95EC164255D1EA16944BE4564EFE7730F9864BF2 (AllIn1VfxComponent_t2587889FCB75E1748AEF4F9EAE574E8C3631BE54* __this, String_t* ___0_keyword, bool ___1_state, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		bool L_0 = __this->___destroyed;
		if (!L_0)
		{
			goto IL_0009;
		}
	}
	{
		return;
	}

IL_0009:
	{
		Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* L_1 = __this->___currMaterial;
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_2;
		L_2 = Object_op_Equality_mB6120F782D83091EF56A198FCEBCF066DB4A9605(L_1, (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*)NULL, NULL);
		if (!L_2)
		{
			goto IL_0032;
		}
	}
	{
		AllIn1VfxComponent_FindCurrMaterial_m6005E8C7A35C0F7F353D15BD4026AD790B609BCD(__this, NULL);
		Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* L_3 = __this->___currMaterial;
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_4;
		L_4 = Object_op_Equality_mB6120F782D83091EF56A198FCEBCF066DB4A9605(L_3, (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*)NULL, NULL);
		if (!L_4)
		{
			goto IL_0032;
		}
	}
	{
		AllIn1VfxComponent_MissingRenderer_mB023CB700C00CC98DFECD40F146C57164D16C77E(__this, NULL);
		return;
	}

IL_0032:
	{
		bool L_5 = ___1_state;
		if (L_5)
		{
			goto IL_0042;
		}
	}
	{
		Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* L_6 = __this->___currMaterial;
		String_t* L_7 = ___0_keyword;
		NullCheck(L_6);
		Material_DisableKeyword_mC123927EBF2F2A19220A4456C8EA19F2BA416E8C(L_6, L_7, NULL);
		return;
	}

IL_0042:
	{
		Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* L_8 = __this->___currMaterial;
		String_t* L_9 = ___0_keyword;
		NullCheck(L_8);
		Material_EnableKeyword_mE8523EF6CF694284DF976D47ADEDE9363A1174AC(L_8, L_9, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AllIn1VfxComponent_FindCurrMaterial_m6005E8C7A35C0F7F353D15BD4026AD790B609BCD (AllIn1VfxComponent_t2587889FCB75E1748AEF4F9EAE574E8C3631BE54* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Component_GetComponent_TisGraphic_tCBFCA4585A19E2B75465AECFEAC43F4016BF7931_mFE18E20FC92395F90E776DBC4CD214A4F2D97D90_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Component_GetComponent_TisRenderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF_mC91ACC92AD57CA6CA00991DAF1DB3830BCE07AF8_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Graphic_tCBFCA4585A19E2B75465AECFEAC43F4016BF7931* V_0 = NULL;
	{
		Renderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF* L_0;
		L_0 = Component_GetComponent_TisRenderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF_mC91ACC92AD57CA6CA00991DAF1DB3830BCE07AF8(__this, Component_GetComponent_TisRenderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF_mC91ACC92AD57CA6CA00991DAF1DB3830BCE07AF8_RuntimeMethod_var);
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_1;
		L_1 = Object_op_Inequality_mD0BE578448EAA61948F25C32F8DD55AB1F778602(L_0, (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*)NULL, NULL);
		if (!L_1)
		{
			goto IL_0027;
		}
	}
	{
		Renderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF* L_2;
		L_2 = Component_GetComponent_TisRenderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF_mC91ACC92AD57CA6CA00991DAF1DB3830BCE07AF8(__this, Component_GetComponent_TisRenderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF_mC91ACC92AD57CA6CA00991DAF1DB3830BCE07AF8_RuntimeMethod_var);
		NullCheck(L_2);
		Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* L_3;
		L_3 = Renderer_get_sharedMaterial_mA2E0CA0A564617FFC3E0E50947C6300082C35F81(L_2, NULL);
		__this->___currMaterial = L_3;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___currMaterial), (void*)L_3);
		__this->___matAssigned = (bool)1;
		return;
	}

IL_0027:
	{
		Graphic_tCBFCA4585A19E2B75465AECFEAC43F4016BF7931* L_4;
		L_4 = Component_GetComponent_TisGraphic_tCBFCA4585A19E2B75465AECFEAC43F4016BF7931_mFE18E20FC92395F90E776DBC4CD214A4F2D97D90(__this, Component_GetComponent_TisGraphic_tCBFCA4585A19E2B75465AECFEAC43F4016BF7931_mFE18E20FC92395F90E776DBC4CD214A4F2D97D90_RuntimeMethod_var);
		V_0 = L_4;
		Graphic_tCBFCA4585A19E2B75465AECFEAC43F4016BF7931* L_5 = V_0;
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_6;
		L_6 = Object_op_Inequality_mD0BE578448EAA61948F25C32F8DD55AB1F778602(L_5, (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*)NULL, NULL);
		if (!L_6)
		{
			goto IL_004a;
		}
	}
	{
		Graphic_tCBFCA4585A19E2B75465AECFEAC43F4016BF7931* L_7 = V_0;
		NullCheck(L_7);
		Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* L_8;
		L_8 = VirtualFuncInvoker0< Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* >::Invoke(32, L_7);
		__this->___currMaterial = L_8;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___currMaterial), (void*)L_8);
		__this->___matAssigned = (bool)1;
	}

IL_004a:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AllIn1VfxComponent_CleanMaterial_mFAD12AF2EDBE22342D512879ABFF5F12D059BE78 (AllIn1VfxComponent_t2587889FCB75E1748AEF4F9EAE574E8C3631BE54* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Component_GetComponent_TisGraphic_tCBFCA4585A19E2B75465AECFEAC43F4016BF7931_mFE18E20FC92395F90E776DBC4CD214A4F2D97D90_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Component_GetComponent_TisRenderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF_mC91ACC92AD57CA6CA00991DAF1DB3830BCE07AF8_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral975A5F46FC6E6D8BC7943A3A38CEA489C122E4F1);
		s_Il2CppMethodInitialized = true;
	}
	Renderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF* V_0 = NULL;
	Graphic_tCBFCA4585A19E2B75465AECFEAC43F4016BF7931* V_1 = NULL;
	{
		Renderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF* L_0;
		L_0 = Component_GetComponent_TisRenderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF_mC91ACC92AD57CA6CA00991DAF1DB3830BCE07AF8(__this, Component_GetComponent_TisRenderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF_mC91ACC92AD57CA6CA00991DAF1DB3830BCE07AF8_RuntimeMethod_var);
		V_0 = L_0;
		Renderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF* L_1 = V_0;
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_2;
		L_2 = Object_op_Inequality_mD0BE578448EAA61948F25C32F8DD55AB1F778602(L_1, (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*)NULL, NULL);
		if (!L_2)
		{
			goto IL_002e;
		}
	}
	{
		Renderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF* L_3 = V_0;
		Shader_tADC867D36B7876EE22427FAA2CE485105F4EE692* L_4;
		L_4 = Shader_Find_m183AA54F78320212DDEC811592F98456898A41C5(_stringLiteral975A5F46FC6E6D8BC7943A3A38CEA489C122E4F1, NULL);
		Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* L_5 = (Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3*)il2cpp_codegen_object_new(Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3_il2cpp_TypeInfo_var);
		Material__ctor_m7FDF47105D66D19591BE505A0C42B0F90D88C9BF(L_5, L_4, NULL);
		NullCheck(L_3);
		Renderer_set_sharedMaterial_m5E842F9A06CFB7B77656EB319881CB4B3E8E4288(L_3, L_5, NULL);
		__this->___matAssigned = (bool)0;
		goto IL_005a;
	}

IL_002e:
	{
		Graphic_tCBFCA4585A19E2B75465AECFEAC43F4016BF7931* L_6;
		L_6 = Component_GetComponent_TisGraphic_tCBFCA4585A19E2B75465AECFEAC43F4016BF7931_mFE18E20FC92395F90E776DBC4CD214A4F2D97D90(__this, Component_GetComponent_TisGraphic_tCBFCA4585A19E2B75465AECFEAC43F4016BF7931_mFE18E20FC92395F90E776DBC4CD214A4F2D97D90_RuntimeMethod_var);
		V_1 = L_6;
		Graphic_tCBFCA4585A19E2B75465AECFEAC43F4016BF7931* L_7 = V_1;
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_8;
		L_8 = Object_op_Inequality_mD0BE578448EAA61948F25C32F8DD55AB1F778602(L_7, (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*)NULL, NULL);
		if (!L_8)
		{
			goto IL_005a;
		}
	}
	{
		Graphic_tCBFCA4585A19E2B75465AECFEAC43F4016BF7931* L_9 = V_1;
		Shader_tADC867D36B7876EE22427FAA2CE485105F4EE692* L_10;
		L_10 = Shader_Find_m183AA54F78320212DDEC811592F98456898A41C5(_stringLiteral975A5F46FC6E6D8BC7943A3A38CEA489C122E4F1, NULL);
		Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* L_11 = (Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3*)il2cpp_codegen_object_new(Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3_il2cpp_TypeInfo_var);
		Material__ctor_m7FDF47105D66D19591BE505A0C42B0F90D88C9BF(L_11, L_10, NULL);
		NullCheck(L_9);
		VirtualActionInvoker1< Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* >::Invoke(33, L_9, L_11);
		__this->___matAssigned = (bool)0;
	}

IL_005a:
	{
		AllIn1VfxComponent_SetSceneDirty_m9E6A673E9394C3A3B775625029362C9D996410EA(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool AllIn1VfxComponent_SaveMaterial_mEBF5E599BFBC05BCFD6CA3C99253D86427672C44 (AllIn1VfxComponent_t2587889FCB75E1748AEF4F9EAE574E8C3631BE54* __this, const RuntimeMethod* method) 
{
	{
		return (bool)0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AllIn1VfxComponent_SaveMaterialWithOtherName_m813A30F3AC436425C72CAE067E4696DEBAEEAE32 (AllIn1VfxComponent_t2587889FCB75E1748AEF4F9EAE574E8C3631BE54* __this, String_t* ___0_path, int32_t ___1_i, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral50639CAD49418C7B223CC529395C0E2A3892501C);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralA0F4CF9D3B8B4AD6A49A888401B14AE51DD52E16);
		s_Il2CppMethodInitialized = true;
	}
	int32_t V_0 = 0;
	String_t* V_1 = NULL;
	{
		int32_t L_0 = ___1_i;
		V_0 = L_0;
		String_t* L_1 = ___0_path;
		String_t* L_2;
		L_2 = Int32_ToString_m030E01C24E294D6762FB0B6F37CB541581F55CA5((&V_0), NULL);
		String_t* L_3;
		L_3 = String_Concat_m8855A6DE10F84DA7F4EC113CADDB59873A25573B(L_1, _stringLiteral50639CAD49418C7B223CC529395C0E2A3892501C, L_2, NULL);
		String_t* L_4;
		L_4 = String_Concat_m9E3155FB84015C823606188F53B47CB44C444991(L_3, _stringLiteralA0F4CF9D3B8B4AD6A49A888401B14AE51DD52E16, NULL);
		V_1 = L_4;
		String_t* L_5 = V_1;
		bool L_6;
		L_6 = File_Exists_m95E329ABBE3EAD6750FE1989BBA6884457136D4A(L_5, NULL);
		if (!L_6)
		{
			goto IL_0034;
		}
	}
	{
		int32_t L_7 = V_0;
		V_0 = ((int32_t)il2cpp_codegen_add(L_7, 1));
		String_t* L_8 = ___0_path;
		int32_t L_9 = V_0;
		AllIn1VfxComponent_SaveMaterialWithOtherName_m813A30F3AC436425C72CAE067E4696DEBAEEAE32(__this, L_8, L_9, NULL);
		return;
	}

IL_0034:
	{
		String_t* L_10 = V_1;
		AllIn1VfxComponent_DoSaving_m936CEFF84DEAAA780F01E1774FD78018AB351FE7(__this, L_10, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AllIn1VfxComponent_DoSaving_m936CEFF84DEAAA780F01E1774FD78018AB351FE7 (AllIn1VfxComponent_t2587889FCB75E1748AEF4F9EAE574E8C3631BE54* __this, String_t* ___0_fileName, const RuntimeMethod* method) 
{
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AllIn1VfxComponent_SetSceneDirty_m9E6A673E9394C3A3B775625029362C9D996410EA (AllIn1VfxComponent_t2587889FCB75E1748AEF4F9EAE574E8C3631BE54* __this, const RuntimeMethod* method) 
{
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AllIn1VfxComponent_MissingRenderer_mB023CB700C00CC98DFECD40F146C57164D16C77E (AllIn1VfxComponent_t2587889FCB75E1748AEF4F9EAE574E8C3631BE54* __this, const RuntimeMethod* method) 
{
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AllIn1VfxComponent__ctor_m8ACEF8DBF8B3771EEDB5F36F03F1727BE579862F (AllIn1VfxComponent_t2587889FCB75E1748AEF4F9EAE574E8C3631BE54* __this, const RuntimeMethod* method) 
{
	{
		MonoBehaviour__ctor_m592DB0105CA0BC97AA1C5F4AD27B12D68A3B7C1E(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AllIn1VfxFakeLightDirSetter_Awake_mFC968AF1A810D7A7ECC44EC14A230CC25280A6A2 (AllIn1VfxFakeLightDirSetter_t2DF709DA0A4395452D0BF247C1D247B3722FCC06* __this, const RuntimeMethod* method) 
{
	{
		bool L_0 = __this->___setOnAwake;
		if (!L_0)
		{
			goto IL_000e;
		}
	}
	{
		AllIn1VfxFakeLightDirSetter_SetGlobalFakeLightDir_mC9E6091C093D3A23241CA19E45E032A889CB71B0(__this, NULL);
	}

IL_000e:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AllIn1VfxFakeLightDirSetter_Update_m49832CBC47E8981C8683A61E9139330F894E49BE (AllIn1VfxFakeLightDirSetter_t2DF709DA0A4395452D0BF247C1D247B3722FCC06* __this, const RuntimeMethod* method) 
{
	{
		bool L_0 = __this->___setOnUpdate;
		if (!L_0)
		{
			goto IL_000e;
		}
	}
	{
		AllIn1VfxFakeLightDirSetter_SetGlobalFakeLightDir_mC9E6091C093D3A23241CA19E45E032A889CB71B0(__this, NULL);
	}

IL_000e:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AllIn1VfxFakeLightDirSetter_OnValidate_mAEDEC47E4DEB9BE90B5FABA0578040D8C06085A5 (AllIn1VfxFakeLightDirSetter_t2DF709DA0A4395452D0BF247C1D247B3722FCC06* __this, const RuntimeMethod* method) 
{
	{
		AllIn1VfxFakeLightDirSetter_SetGlobalFakeLightDir_mC9E6091C093D3A23241CA19E45E032A889CB71B0(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AllIn1VfxFakeLightDirSetter_SetGlobalFakeLightDir_mC9E6091C093D3A23241CA19E45E032A889CB71B0 (AllIn1VfxFakeLightDirSetter_t2DF709DA0A4395452D0BF247C1D247B3722FCC06* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralC827CF6C30E43507B780232E56A8ECC3A42FD702);
		s_Il2CppMethodInitialized = true;
	}
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		int32_t L_0 = __this->___lightDirId;
		if (L_0)
		{
			goto IL_0018;
		}
	}
	{
		int32_t L_1;
		L_1 = Shader_PropertyToID_mE98523D50F5656CAE89B30695C458253EB8956CA(_stringLiteralC827CF6C30E43507B780232E56A8ECC3A42FD702, NULL);
		__this->___lightDirId = L_1;
	}

IL_0018:
	{
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_2 = __this->___target;
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_3;
		L_3 = Object_op_Equality_mB6120F782D83091EF56A198FCEBCF066DB4A9605(L_2, (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*)NULL, NULL);
		if (!L_3)
		{
			goto IL_0032;
		}
	}
	{
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_4;
		L_4 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		__this->___target = L_4;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___target), (void*)L_4);
	}

IL_0032:
	{
		int32_t L_5 = __this->___lightDirId;
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_6 = __this->___target;
		NullCheck(L_6);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_7;
		L_7 = Transform_get_forward_mFCFACF7165FDAB21E80E384C494DF278386CEE2F(L_6, NULL);
		V_0 = L_7;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_8;
		L_8 = Vector3_get_normalized_m736BBF65D5CDA7A18414370D15B4DFCC1E466F07_inline((&V_0), NULL);
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_9;
		L_9 = Vector4_op_Implicit_m2ECA73F345A7AD84144133E9E51657204002B12D_inline(L_8, NULL);
		Shader_SetGlobalVector_mDC5F45B008D44A2C8BF6D450CFE8B58B847C8190(L_5, L_9, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AllIn1VfxFakeLightDirSetter_SetNewFakeLightDir_mC8F9F66AF38019A5523C57E2ACB9F4D3DC07C2EE (AllIn1VfxFakeLightDirSetter_t2DF709DA0A4395452D0BF247C1D247B3722FCC06* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_newFakeLightDir, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralC827CF6C30E43507B780232E56A8ECC3A42FD702);
		s_Il2CppMethodInitialized = true;
	}
	{
		int32_t L_0 = __this->___lightDirId;
		if (L_0)
		{
			goto IL_0018;
		}
	}
	{
		int32_t L_1;
		L_1 = Shader_PropertyToID_mE98523D50F5656CAE89B30695C458253EB8956CA(_stringLiteralC827CF6C30E43507B780232E56A8ECC3A42FD702, NULL);
		__this->___lightDirId = L_1;
	}

IL_0018:
	{
		int32_t L_2 = __this->___lightDirId;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_3;
		L_3 = Vector3_get_normalized_m736BBF65D5CDA7A18414370D15B4DFCC1E466F07_inline((&___0_newFakeLightDir), NULL);
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_4;
		L_4 = Vector4_op_Implicit_m2ECA73F345A7AD84144133E9E51657204002B12D_inline(L_3, NULL);
		Shader_SetGlobalVector_mDC5F45B008D44A2C8BF6D450CFE8B58B847C8190(L_2, L_4, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AllIn1VfxFakeLightDirSetter_SetNewTarget_m0F2F25862843224A6D62E6A3784691B25599DE8A (AllIn1VfxFakeLightDirSetter_t2DF709DA0A4395452D0BF247C1D247B3722FCC06* __this, Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* ___0_newTarget, const RuntimeMethod* method) 
{
	{
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_0 = ___0_newTarget;
		__this->___target = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___target), (void*)L_0);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AllIn1VfxFakeLightDirSetter_SetOnUpdateBool_mB9B0A836D23E435B99D73D67F247F6722345DD71 (AllIn1VfxFakeLightDirSetter_t2DF709DA0A4395452D0BF247C1D247B3722FCC06* __this, bool ___0_newSetOnUpdateValue, const RuntimeMethod* method) 
{
	{
		bool L_0 = ___0_newSetOnUpdateValue;
		__this->___setOnUpdate = L_0;
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AllIn1VfxFakeLightDirSetter__ctor_m4C5EA3BCE548CFE5D3AC5842C8540136B460DE4C (AllIn1VfxFakeLightDirSetter_t2DF709DA0A4395452D0BF247C1D247B3722FCC06* __this, const RuntimeMethod* method) 
{
	{
		__this->___setOnAwake = (bool)1;
		MonoBehaviour__ctor_m592DB0105CA0BC97AA1C5F4AD27B12D68A3B7C1E(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Texture2D_tE6505BC111DD8A424A9DBE8E05D7D09E11FFFCF4* AllIn1VfxNoiseCreator_PerlinNoise_mF395BD7B1C25E3A3B0A31239B7783F1D3A04B964 (Texture2D_tE6505BC111DD8A424A9DBE8E05D7D09E11FFFCF4* ___0_tex, float ___1_scale, int32_t ___2_randomSeed, bool ___3_tileable, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Texture2D_tE6505BC111DD8A424A9DBE8E05D7D09E11FFFCF4_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	int32_t V_0 = 0;
	int32_t V_1 = 0;
	float V_2 = 0.0f;
	Texture2D_tE6505BC111DD8A424A9DBE8E05D7D09E11FFFCF4* V_3 = NULL;
	int32_t V_4 = 0;
	int32_t V_5 = 0;
	int32_t V_6 = 0;
	int32_t V_7 = 0;
	{
		Texture2D_tE6505BC111DD8A424A9DBE8E05D7D09E11FFFCF4* L_0 = ___0_tex;
		NullCheck(L_0);
		int32_t L_1;
		L_1 = VirtualFuncInvoker0< int32_t >::Invoke(5, L_0);
		V_0 = L_1;
		Texture2D_tE6505BC111DD8A424A9DBE8E05D7D09E11FFFCF4* L_2 = ___0_tex;
		NullCheck(L_2);
		int32_t L_3;
		L_3 = VirtualFuncInvoker0< int32_t >::Invoke(7, L_2);
		V_1 = L_3;
		int32_t L_4 = ___2_randomSeed;
		Random_InitState_mE70961834F42FFEEB06CB9C68175354E0C255664(L_4, NULL);
		float L_5;
		L_5 = Random_Range_m5236C99A7D8AE6AC9190592DC66016652A2D2494((-100.0f), (100.0f), NULL);
		V_2 = L_5;
		V_4 = 0;
		goto IL_0056;
	}

IL_0029:
	{
		V_5 = 0;
		goto IL_004b;
	}

IL_002e:
	{
		Texture2D_tE6505BC111DD8A424A9DBE8E05D7D09E11FFFCF4* L_6 = ___0_tex;
		int32_t L_7 = V_5;
		int32_t L_8 = V_4;
		int32_t L_9 = V_5;
		int32_t L_10 = V_4;
		float L_11 = ___1_scale;
		float L_12 = V_2;
		int32_t L_13 = V_0;
		int32_t L_14 = V_1;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_15;
		L_15 = AllIn1VfxNoiseCreator_CalculatePerlinColor_m3675833D2D56F07A38F4B3FAE9493989B733D9F3(L_9, L_10, L_11, L_12, L_13, L_14, NULL);
		NullCheck(L_6);
		Texture2D_SetPixel_m2CCFC5F729135D59DC4A697C2605A3FC5C8574DB(L_6, L_7, L_8, L_15, NULL);
		int32_t L_16 = V_5;
		V_5 = ((int32_t)il2cpp_codegen_add(L_16, 1));
	}

IL_004b:
	{
		int32_t L_17 = V_5;
		int32_t L_18 = V_0;
		if ((((int32_t)L_17) < ((int32_t)L_18)))
		{
			goto IL_002e;
		}
	}
	{
		int32_t L_19 = V_4;
		V_4 = ((int32_t)il2cpp_codegen_add(L_19, 1));
	}

IL_0056:
	{
		int32_t L_20 = V_4;
		int32_t L_21 = V_1;
		if ((((int32_t)L_20) < ((int32_t)L_21)))
		{
			goto IL_0029;
		}
	}
	{
		Texture2D_tE6505BC111DD8A424A9DBE8E05D7D09E11FFFCF4* L_22 = ___0_tex;
		NullCheck(L_22);
		Texture2D_Apply_mA014182C9EE0BBF6EEE3B286854F29E50EB972DC(L_22, NULL);
		int32_t L_23 = V_1;
		int32_t L_24 = V_0;
		Texture2D_tE6505BC111DD8A424A9DBE8E05D7D09E11FFFCF4* L_25 = (Texture2D_tE6505BC111DD8A424A9DBE8E05D7D09E11FFFCF4*)il2cpp_codegen_object_new(Texture2D_tE6505BC111DD8A424A9DBE8E05D7D09E11FFFCF4_il2cpp_TypeInfo_var);
		Texture2D__ctor_m3BA82E87442B7F69E118477069AE11101B9DF796(L_25, L_23, L_24, NULL);
		V_3 = L_25;
		Texture2D_tE6505BC111DD8A424A9DBE8E05D7D09E11FFFCF4* L_26 = V_3;
		Texture2D_tE6505BC111DD8A424A9DBE8E05D7D09E11FFFCF4* L_27 = ___0_tex;
		NullCheck(L_27);
		ColorU5BU5D_t612261CF293F6FFC3D80AB52259FF0DC2B2CC389* L_28;
		L_28 = Texture2D_GetPixels_m77A00D71DF5CDC7DAA0EE66FF2C90A24C7604039(L_27, NULL);
		NullCheck(L_26);
		Texture2D_SetPixels_mAE0CDFA15FA96F840D7FFADC31405D8AF20D9073(L_26, L_28, NULL);
		bool L_29 = ___3_tileable;
		if (!L_29)
		{
			goto IL_00b0;
		}
	}
	{
		V_6 = 0;
		goto IL_00ab;
	}

IL_007d:
	{
		V_7 = 0;
		goto IL_00a0;
	}

IL_0082:
	{
		Texture2D_tE6505BC111DD8A424A9DBE8E05D7D09E11FFFCF4* L_30 = V_3;
		int32_t L_31 = V_7;
		int32_t L_32 = V_6;
		int32_t L_33 = V_7;
		int32_t L_34 = V_6;
		float L_35 = ___1_scale;
		float L_36 = V_2;
		int32_t L_37 = V_0;
		int32_t L_38 = V_1;
		Texture2D_tE6505BC111DD8A424A9DBE8E05D7D09E11FFFCF4* L_39 = ___0_tex;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_40;
		L_40 = AllIn1VfxNoiseCreator_PerlinBorderless_m49ADBDE375DC489A6473ED11D71EAD34E771AC45(L_33, L_34, L_35, L_36, L_37, L_38, L_39, NULL);
		NullCheck(L_30);
		Texture2D_SetPixel_m2CCFC5F729135D59DC4A697C2605A3FC5C8574DB(L_30, L_31, L_32, L_40, NULL);
		int32_t L_41 = V_7;
		V_7 = ((int32_t)il2cpp_codegen_add(L_41, 1));
	}

IL_00a0:
	{
		int32_t L_42 = V_7;
		int32_t L_43 = V_0;
		if ((((int32_t)L_42) < ((int32_t)L_43)))
		{
			goto IL_0082;
		}
	}
	{
		int32_t L_44 = V_6;
		V_6 = ((int32_t)il2cpp_codegen_add(L_44, 1));
	}

IL_00ab:
	{
		int32_t L_45 = V_6;
		int32_t L_46 = V_1;
		if ((((int32_t)L_45) < ((int32_t)L_46)))
		{
			goto IL_007d;
		}
	}

IL_00b0:
	{
		Texture2D_tE6505BC111DD8A424A9DBE8E05D7D09E11FFFCF4* L_47 = V_3;
		NullCheck(L_47);
		Texture2D_Apply_mA014182C9EE0BBF6EEE3B286854F29E50EB972DC(L_47, NULL);
		Texture2D_tE6505BC111DD8A424A9DBE8E05D7D09E11FFFCF4* L_48 = V_3;
		return L_48;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Color_tD001788D726C3A7F1379BEED0260B9591F440C1F AllIn1VfxNoiseCreator_CalculatePerlinColor_m3675833D2D56F07A38F4B3FAE9493989B733D9F3 (int32_t ___0_x, int32_t ___1_y, float ___2_scale, float ___3_offset, int32_t ___4_width, int32_t ___5_height, const RuntimeMethod* method) 
{
	float V_0 = 0.0f;
	{
		int32_t L_0 = ___0_x;
		float L_1 = ___3_offset;
		int32_t L_2 = ___4_width;
		float L_3 = ___2_scale;
		int32_t L_4 = ___1_y;
		float L_5 = ___3_offset;
		int32_t L_6 = ___5_height;
		float L_7 = ___2_scale;
		V_0 = ((float)il2cpp_codegen_multiply(((float)(((float)il2cpp_codegen_add(((float)L_4), L_5))/((float)L_6))), L_7));
		float L_8 = V_0;
		float L_9;
		L_9 = Mathf_PerlinNoise_mAB0E53C29FE95469CF303364910AD0D8662A9A6A(((float)il2cpp_codegen_multiply(((float)(((float)il2cpp_codegen_add(((float)L_0), L_1))/((float)L_2))), L_3)), L_8, NULL);
		float L_10 = L_9;
		float L_11 = L_10;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_12;
		memset((&L_12), 0, sizeof(L_12));
		Color__ctor_m3786F0D6E510D9CFA544523A955870BD2A514C8C_inline((&L_12), L_10, L_11, L_11, (1.0f), NULL);
		return L_12;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Color_tD001788D726C3A7F1379BEED0260B9591F440C1F AllIn1VfxNoiseCreator_PerlinBorderless_m49ADBDE375DC489A6473ED11D71EAD34E771AC45 (int32_t ___0_x, int32_t ___1_y, float ___2_scale, float ___3_offset, int32_t ___4_width, int32_t ___5_height, Texture2D_tE6505BC111DD8A424A9DBE8E05D7D09E11FFFCF4* ___6_previousPerlin, const RuntimeMethod* method) 
{
	int32_t V_0 = 0;
	int32_t V_1 = 0;
	float V_2 = 0.0f;
	float V_3 = 0.0f;
	float V_4 = 0.0f;
	float V_5 = 0.0f;
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F V_6;
	memset((&V_6), 0, sizeof(V_6));
	float V_7 = 0.0f;
	{
		int32_t L_0 = ___0_x;
		V_0 = L_0;
		int32_t L_1 = ___1_y;
		V_1 = L_1;
		int32_t L_2 = ___0_x;
		int32_t L_3 = ___4_width;
		V_2 = ((float)(((float)L_2)/((float)L_3)));
		int32_t L_4 = ___1_y;
		int32_t L_5 = ___5_height;
		V_3 = ((float)(((float)L_4)/((float)L_5)));
		float L_6 = V_2;
		if ((!(((float)L_6) > ((float)(0.5f)))))
		{
			goto IL_0020;
		}
	}
	{
		int32_t L_7 = ___4_width;
		int32_t L_8 = ___0_x;
		___0_x = ((int32_t)il2cpp_codegen_subtract(L_7, L_8));
	}

IL_0020:
	{
		float L_9 = V_3;
		if ((!(((float)L_9) > ((float)(0.5f)))))
		{
			goto IL_002e;
		}
	}
	{
		int32_t L_10 = ___5_height;
		int32_t L_11 = ___1_y;
		___1_y = ((int32_t)il2cpp_codegen_subtract(L_10, L_11));
	}

IL_002e:
	{
		float L_12 = ___3_offset;
		___3_offset = ((float)il2cpp_codegen_add(L_12, (23.4300003f)));
		int32_t L_13 = ___0_x;
		float L_14 = ___3_offset;
		int32_t L_15 = ___4_width;
		float L_16 = ___2_scale;
		int32_t L_17 = ___1_y;
		float L_18 = ___3_offset;
		int32_t L_19 = ___5_height;
		float L_20 = ___2_scale;
		V_4 = ((float)il2cpp_codegen_multiply(((float)(((float)il2cpp_codegen_add(((float)L_17), L_18))/((float)L_19))), L_20));
		float L_21 = V_4;
		float L_22;
		L_22 = Mathf_PerlinNoise_mAB0E53C29FE95469CF303364910AD0D8662A9A6A(((float)il2cpp_codegen_multiply(((float)(((float)il2cpp_codegen_add(((float)L_13), L_14))/((float)L_15))), L_16)), L_21, NULL);
		V_5 = L_22;
		float L_23 = V_5;
		float L_24 = V_5;
		float L_25 = V_5;
		Color__ctor_m3786F0D6E510D9CFA544523A955870BD2A514C8C_inline((&V_6), L_23, L_24, L_25, (1.0f), NULL);
		float L_26 = V_2;
		float L_27 = V_3;
		float L_28;
		L_28 = Mathf_Max_mF5379E63D2BBAC76D090748695D833934F8AD051_inline(L_26, L_27, NULL);
		V_7 = L_28;
		float L_29 = V_7;
		float L_30 = V_2;
		float L_31 = V_3;
		float L_32;
		L_32 = Mathf_Max_mF5379E63D2BBAC76D090748695D833934F8AD051_inline(((float)il2cpp_codegen_subtract((1.0f), L_30)), ((float)il2cpp_codegen_subtract((1.0f), L_31)), NULL);
		float L_33;
		L_33 = Mathf_Max_mF5379E63D2BBAC76D090748695D833934F8AD051_inline(L_29, L_32, NULL);
		V_7 = L_33;
		float L_34 = V_7;
		float L_35;
		L_35 = powf(L_34, (10.0f));
		V_7 = L_35;
		Texture2D_tE6505BC111DD8A424A9DBE8E05D7D09E11FFFCF4* L_36 = ___6_previousPerlin;
		int32_t L_37 = V_0;
		int32_t L_38 = V_1;
		NullCheck(L_36);
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_39;
		L_39 = Texture2D_GetPixel_m69A17FE5CC220F438C7421DCB50A9E22AAB4A415(L_36, L_37, L_38, NULL);
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_40 = V_6;
		float L_41 = V_7;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_42;
		L_42 = Color_Lerp_mE79F87889843ECDC188E4CB5B5E1F1B2256E5EBE_inline(L_39, L_40, L_41, NULL);
		return L_42;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AllIn1VfxScrollShaderProperty_Start_mD66CC06C259D0358916B2E72EC5E0F495A4C52EB (AllIn1VfxScrollShaderProperty_tAC8F7ED4CC8304DBB58E1B23A2D2CE13EA22E07E* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Component_GetComponent_TisRenderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF_mC91ACC92AD57CA6CA00991DAF1DB3830BCE07AF8_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral2043A81282FBC38D068F48CE6B02508288E7F859);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralA7A626DEA2521BA48EA03C7C5828601203370D81);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralEAE96BC7C4AF88268A19A75CCE8F01ABB5A77AB1);
		s_Il2CppMethodInitialized = true;
	}
	{
		Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* L_0 = __this->___mat;
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_1;
		L_1 = Object_op_Equality_mB6120F782D83091EF56A198FCEBCF066DB4A9605(L_0, (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*)NULL, NULL);
		if (!L_1)
		{
			goto IL_0021;
		}
	}
	{
		Renderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF* L_2;
		L_2 = Component_GetComponent_TisRenderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF_mC91ACC92AD57CA6CA00991DAF1DB3830BCE07AF8(__this, Component_GetComponent_TisRenderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF_mC91ACC92AD57CA6CA00991DAF1DB3830BCE07AF8_RuntimeMethod_var);
		NullCheck(L_2);
		Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* L_3;
		L_3 = Renderer_get_material_m5BA2A00816C4CC66580D4B2E409CF10718C15656(L_2, NULL);
		__this->___mat = L_3;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___mat), (void*)L_3);
		goto IL_0039;
	}

IL_0021:
	{
		Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* L_4 = __this->___mat;
		Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* L_5 = (Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3*)il2cpp_codegen_object_new(Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3_il2cpp_TypeInfo_var);
		Material__ctor_mFCC42FB90257F1E8F7516A8640A79C465A39961C(L_5, L_4, NULL);
		__this->___originalMat = L_5;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___originalMat), (void*)L_5);
		__this->___restoreMaterialOnDisable = (bool)1;
	}

IL_0039:
	{
		Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* L_6 = __this->___mat;
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_7;
		L_7 = Object_op_Equality_mB6120F782D83091EF56A198FCEBCF066DB4A9605(L_6, (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*)NULL, NULL);
		if (!L_7)
		{
			goto IL_0063;
		}
	}
	{
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_8;
		L_8 = Component_get_gameObject_m57AEFBB14DB39EC476F740BA000E170355DE691B(__this, NULL);
		NullCheck(L_8);
		String_t* L_9;
		L_9 = Object_get_name_mAC2F6B897CF1303BA4249B4CB55271AFACBB6392(L_8, NULL);
		String_t* L_10;
		L_10 = String_Concat_m9E3155FB84015C823606188F53B47CB44C444991(L_9, _stringLiteralEAE96BC7C4AF88268A19A75CCE8F01ABB5A77AB1, NULL);
		AllIn1VfxScrollShaderProperty_DestroyComponentAndLogError_mE541EDF927CBB77AF92844EDB5F13BC99EE07A36(__this, L_10, NULL);
		return;
	}

IL_0063:
	{
		Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* L_11 = __this->___mat;
		String_t* L_12 = __this->___numericPropertyName;
		NullCheck(L_11);
		bool L_13;
		L_13 = Material_HasProperty_mC09A83B44E368A217F606DD4954FA080CC03EC6C(L_11, L_12, NULL);
		if (!L_13)
		{
			goto IL_0089;
		}
	}
	{
		String_t* L_14 = __this->___numericPropertyName;
		int32_t L_15;
		L_15 = Shader_PropertyToID_mE98523D50F5656CAE89B30695C458253EB8956CA(L_14, NULL);
		__this->___propertyShaderID = L_15;
		goto IL_00af;
	}

IL_0089:
	{
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_16;
		L_16 = Component_get_gameObject_m57AEFBB14DB39EC476F740BA000E170355DE691B(__this, NULL);
		NullCheck(L_16);
		String_t* L_17;
		L_17 = Object_get_name_mAC2F6B897CF1303BA4249B4CB55271AFACBB6392(L_16, NULL);
		String_t* L_18 = __this->___numericPropertyName;
		String_t* L_19;
		L_19 = String_Concat_m093934F71A9B351911EE46311674ED463B180006(L_17, _stringLiteralA7A626DEA2521BA48EA03C7C5828601203370D81, L_18, _stringLiteral2043A81282FBC38D068F48CE6B02508288E7F859, NULL);
		AllIn1VfxScrollShaderProperty_DestroyComponentAndLogError_mE541EDF927CBB77AF92844EDB5F13BC99EE07A36(__this, L_19, NULL);
	}

IL_00af:
	{
		Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* L_20 = __this->___mat;
		int32_t L_21 = __this->___propertyShaderID;
		NullCheck(L_20);
		float L_22;
		L_22 = Material_GetFloat_m52462F4AEDE20758BFB592B11DE83A79D2774932(L_20, L_21, NULL);
		__this->___currValue = L_22;
		bool L_23 = __this->___backAndForth;
		if (L_23)
		{
			goto IL_00d6;
		}
	}
	{
		bool L_24 = __this->___stopAtValue;
		if (!L_24)
		{
			goto IL_0144;
		}
	}

IL_00d6:
	{
		float L_25 = __this->___currValue;
		__this->___iniValue = L_25;
		float L_26 = __this->___iniValue;
		float L_27 = __this->___maxValue;
		__this->___goingUp = (bool)((((float)L_26) < ((float)L_27))? 1 : 0);
		bool L_28 = __this->___goingUp;
		if (L_28)
		{
			goto IL_011d;
		}
	}
	{
		float L_29 = __this->___scrollSpeed;
		if ((!(((float)L_29) > ((float)(0.0f)))))
		{
			goto IL_011d;
		}
	}
	{
		float L_30 = __this->___scrollSpeed;
		__this->___scrollSpeed = ((float)il2cpp_codegen_multiply(L_30, (-1.0f)));
	}

IL_011d:
	{
		bool L_31 = __this->___goingUp;
		if (!L_31)
		{
			goto IL_0144;
		}
	}
	{
		float L_32 = __this->___scrollSpeed;
		if ((!(((float)L_32) < ((float)(0.0f)))))
		{
			goto IL_0144;
		}
	}
	{
		float L_33 = __this->___scrollSpeed;
		__this->___scrollSpeed = ((float)il2cpp_codegen_multiply(L_33, (-1.0f)));
	}

IL_0144:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AllIn1VfxScrollShaderProperty_Update_mC7E9894774CBE05469B32451E8EA242254567EAF (AllIn1VfxScrollShaderProperty_tAC8F7ED4CC8304DBB58E1B23A2D2CE13EA22E07E* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Debug_t8394C7EEAECA3689C2C9B9DE9C7166D73596276F_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral68CB89848359D7BCEA0995C8FB01DAA1D5DFDE28);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralF5D8EF422ABD0284BA3EEB3BF58FBA9313575F4E);
		s_Il2CppMethodInitialized = true;
	}
	{
		Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* L_0 = __this->___mat;
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_1;
		L_1 = Object_op_Equality_mB6120F782D83091EF56A198FCEBCF066DB4A9605(L_0, (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*)NULL, NULL);
		if (!L_1)
		{
			goto IL_003d;
		}
	}
	{
		bool L_2 = __this->___isValid;
		if (!L_2)
		{
			goto IL_003c;
		}
	}
	{
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_3;
		L_3 = Component_get_gameObject_m57AEFBB14DB39EC476F740BA000E170355DE691B(__this, NULL);
		NullCheck(L_3);
		String_t* L_4;
		L_4 = Object_get_name_mAC2F6B897CF1303BA4249B4CB55271AFACBB6392(L_3, NULL);
		String_t* L_5;
		L_5 = String_Concat_m8855A6DE10F84DA7F4EC113CADDB59873A25573B(_stringLiteralF5D8EF422ABD0284BA3EEB3BF58FBA9313575F4E, L_4, _stringLiteral68CB89848359D7BCEA0995C8FB01DAA1D5DFDE28, NULL);
		il2cpp_codegen_runtime_class_init_inline(Debug_t8394C7EEAECA3689C2C9B9DE9C7166D73596276F_il2cpp_TypeInfo_var);
		Debug_LogError_mB00B2B4468EF3CAF041B038D840820FB84C924B2(L_5, NULL);
		__this->___isValid = (bool)0;
	}

IL_003c:
	{
		return;
	}

IL_003d:
	{
		float L_6 = __this->___currValue;
		float L_7 = __this->___scrollSpeed;
		float L_8;
		L_8 = Time_get_deltaTime_mC3195000401F0FD167DD2F948FD2BC58330D0865(NULL);
		__this->___currValue = ((float)il2cpp_codegen_add(L_6, ((float)il2cpp_codegen_multiply(L_7, L_8))));
		bool L_9 = __this->___backAndForth;
		if (!L_9)
		{
			goto IL_0098;
		}
	}
	{
		bool L_10 = __this->___goingUp;
		if (!L_10)
		{
			goto IL_007c;
		}
	}
	{
		float L_11 = __this->___currValue;
		float L_12 = __this->___maxValue;
		if ((!(((float)L_11) >= ((float)L_12))))
		{
			goto IL_007c;
		}
	}
	{
		AllIn1VfxScrollShaderProperty_FlipGoingUp_m2E85C6CA803F28BD510C7D2B57F2C4FFF3D5F180(__this, NULL);
		goto IL_0098;
	}

IL_007c:
	{
		bool L_13 = __this->___goingUp;
		if (L_13)
		{
			goto IL_0098;
		}
	}
	{
		float L_14 = __this->___currValue;
		float L_15 = __this->___iniValue;
		if ((!(((float)L_14) <= ((float)L_15))))
		{
			goto IL_0098;
		}
	}
	{
		AllIn1VfxScrollShaderProperty_FlipGoingUp_m2E85C6CA803F28BD510C7D2B57F2C4FFF3D5F180(__this, NULL);
	}

IL_0098:
	{
		bool L_16 = __this->___applyModulo;
		if (!L_16)
		{
			goto IL_00b3;
		}
	}
	{
		float L_17 = __this->___currValue;
		float L_18 = __this->___modulo;
		__this->___currValue = (fmodf(L_17, L_18));
	}

IL_00b3:
	{
		bool L_19 = __this->___stopAtValue;
		if (!L_19)
		{
			goto IL_00ff;
		}
	}
	{
		bool L_20 = __this->___goingUp;
		if (!L_20)
		{
			goto IL_00de;
		}
	}
	{
		float L_21 = __this->___currValue;
		float L_22 = __this->___stopValue;
		if ((!(((float)L_21) >= ((float)L_22))))
		{
			goto IL_00de;
		}
	}
	{
		__this->___scrollSpeed = (0.0f);
		goto IL_00ff;
	}

IL_00de:
	{
		bool L_23 = __this->___goingUp;
		if (L_23)
		{
			goto IL_00ff;
		}
	}
	{
		float L_24 = __this->___currValue;
		float L_25 = __this->___stopValue;
		if ((!(((float)L_24) <= ((float)L_25))))
		{
			goto IL_00ff;
		}
	}
	{
		__this->___scrollSpeed = (0.0f);
	}

IL_00ff:
	{
		Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* L_26 = __this->___mat;
		int32_t L_27 = __this->___propertyShaderID;
		float L_28 = __this->___currValue;
		NullCheck(L_26);
		Material_SetFloat_m3ECFD92072347A8620254F014865984FA68211A8(L_26, L_27, L_28, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AllIn1VfxScrollShaderProperty_FlipGoingUp_m2E85C6CA803F28BD510C7D2B57F2C4FFF3D5F180 (AllIn1VfxScrollShaderProperty_tAC8F7ED4CC8304DBB58E1B23A2D2CE13EA22E07E* __this, const RuntimeMethod* method) 
{
	{
		bool L_0 = __this->___goingUp;
		__this->___goingUp = (bool)((((int32_t)L_0) == ((int32_t)0))? 1 : 0);
		float L_1 = __this->___scrollSpeed;
		__this->___scrollSpeed = ((float)il2cpp_codegen_multiply(L_1, (-1.0f)));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AllIn1VfxScrollShaderProperty_DestroyComponentAndLogError_mE541EDF927CBB77AF92844EDB5F13BC99EE07A36 (AllIn1VfxScrollShaderProperty_tAC8F7ED4CC8304DBB58E1B23A2D2CE13EA22E07E* __this, String_t* ___0_logError, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Debug_t8394C7EEAECA3689C2C9B9DE9C7166D73596276F_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		String_t* L_0 = ___0_logError;
		il2cpp_codegen_runtime_class_init_inline(Debug_t8394C7EEAECA3689C2C9B9DE9C7166D73596276F_il2cpp_TypeInfo_var);
		Debug_LogError_mB00B2B4468EF3CAF041B038D840820FB84C924B2(L_0, NULL);
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		Object_Destroy_mE97D0A766419A81296E8D4E5C23D01D3FE91ACBB(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AllIn1VfxScrollShaderProperty_OnDisable_m0F247B092283A86651B5ED5256D6C9E7FB5663EB (AllIn1VfxScrollShaderProperty_tAC8F7ED4CC8304DBB58E1B23A2D2CE13EA22E07E* __this, const RuntimeMethod* method) 
{
	{
		bool L_0 = __this->___restoreMaterialOnDisable;
		if (!L_0)
		{
			goto IL_0019;
		}
	}
	{
		Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* L_1 = __this->___mat;
		Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* L_2 = __this->___originalMat;
		NullCheck(L_1);
		Material_CopyPropertiesFromMaterial_m4148227E6A0B8E66315D8115F656B7F8BEAE915B(L_1, L_2, NULL);
	}

IL_0019:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AllIn1VfxScrollShaderProperty__ctor_m1384E828EC486AA3BBF98290DC65AA715322CE68 (AllIn1VfxScrollShaderProperty_tAC8F7ED4CC8304DBB58E1B23A2D2CE13EA22E07E* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralD53DF615DBAF7AA486744EFFCF7D2AB271BC7058);
		s_Il2CppMethodInitialized = true;
	}
	{
		__this->___numericPropertyName = _stringLiteralD53DF615DBAF7AA486744EFFCF7D2AB271BC7058;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___numericPropertyName), (void*)_stringLiteralD53DF615DBAF7AA486744EFFCF7D2AB271BC7058);
		__this->___maxValue = (1.0f);
		__this->___modulo = (360.0f);
		__this->___isValid = (bool)1;
		MonoBehaviour__ctor_m592DB0105CA0BC97AA1C5F4AD27B12D68A3B7C1E(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AllIn1VfxScrollShaderTexture_Start_mF0CF9576C87849A23149BD503FB5F299FBCF9A59 (AllIn1VfxScrollShaderTexture_t0B1CA0D34AF4808EA9BA40B9CDBA558936EB3103* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Component_GetComponent_TisRenderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF_mC91ACC92AD57CA6CA00991DAF1DB3830BCE07AF8_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral1647B084BF73860206F4BB01E3237ED88F61B4BA);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral46AFF93E738AD334DF787721BD7F7D0089E5D7AC);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralA7A626DEA2521BA48EA03C7C5828601203370D81);
		s_Il2CppMethodInitialized = true;
	}
	{
		Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* L_0 = __this->___mat;
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_1;
		L_1 = Object_op_Equality_mB6120F782D83091EF56A198FCEBCF066DB4A9605(L_0, (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*)NULL, NULL);
		if (!L_1)
		{
			goto IL_0021;
		}
	}
	{
		Renderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF* L_2;
		L_2 = Component_GetComponent_TisRenderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF_mC91ACC92AD57CA6CA00991DAF1DB3830BCE07AF8(__this, Component_GetComponent_TisRenderer_t320575F223BCB177A982E5DDB5DB19FAA89E7FBF_mC91ACC92AD57CA6CA00991DAF1DB3830BCE07AF8_RuntimeMethod_var);
		NullCheck(L_2);
		Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* L_3;
		L_3 = Renderer_get_material_m5BA2A00816C4CC66580D4B2E409CF10718C15656(L_2, NULL);
		__this->___mat = L_3;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___mat), (void*)L_3);
		goto IL_0039;
	}

IL_0021:
	{
		Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* L_4 = __this->___mat;
		Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* L_5 = (Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3*)il2cpp_codegen_object_new(Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3_il2cpp_TypeInfo_var);
		Material__ctor_mFCC42FB90257F1E8F7516A8640A79C465A39961C(L_5, L_4, NULL);
		__this->___originalMat = L_5;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___originalMat), (void*)L_5);
		__this->___restoreMaterialOnDisable = (bool)1;
	}

IL_0039:
	{
		Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* L_6 = __this->___mat;
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_7;
		L_7 = Object_op_Equality_mB6120F782D83091EF56A198FCEBCF066DB4A9605(L_6, (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*)NULL, NULL);
		if (!L_7)
		{
			goto IL_0063;
		}
	}
	{
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_8;
		L_8 = Component_get_gameObject_m57AEFBB14DB39EC476F740BA000E170355DE691B(__this, NULL);
		NullCheck(L_8);
		String_t* L_9;
		L_9 = Object_get_name_mAC2F6B897CF1303BA4249B4CB55271AFACBB6392(L_8, NULL);
		String_t* L_10;
		L_10 = String_Concat_m9E3155FB84015C823606188F53B47CB44C444991(L_9, _stringLiteral1647B084BF73860206F4BB01E3237ED88F61B4BA, NULL);
		AllIn1VfxScrollShaderTexture_DestroyComponentAndLogError_m0FF3DD2B9CEEF6E92BB15E65DAC3E1F5546C3E6E(__this, L_10, NULL);
		return;
	}

IL_0063:
	{
		Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* L_11 = __this->___mat;
		String_t* L_12 = __this->___texturePropertyName;
		NullCheck(L_11);
		bool L_13;
		L_13 = Material_HasProperty_mC09A83B44E368A217F606DD4954FA080CC03EC6C(L_11, L_12, NULL);
		if (!L_13)
		{
			goto IL_0089;
		}
	}
	{
		String_t* L_14 = __this->___texturePropertyName;
		int32_t L_15;
		L_15 = Shader_PropertyToID_mE98523D50F5656CAE89B30695C458253EB8956CA(L_14, NULL);
		__this->___propertyShaderID = L_15;
		goto IL_00af;
	}

IL_0089:
	{
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_16;
		L_16 = Component_get_gameObject_m57AEFBB14DB39EC476F740BA000E170355DE691B(__this, NULL);
		NullCheck(L_16);
		String_t* L_17;
		L_17 = Object_get_name_mAC2F6B897CF1303BA4249B4CB55271AFACBB6392(L_16, NULL);
		String_t* L_18 = __this->___texturePropertyName;
		String_t* L_19;
		L_19 = String_Concat_m093934F71A9B351911EE46311674ED463B180006(L_17, _stringLiteralA7A626DEA2521BA48EA03C7C5828601203370D81, L_18, _stringLiteral46AFF93E738AD334DF787721BD7F7D0089E5D7AC, NULL);
		AllIn1VfxScrollShaderTexture_DestroyComponentAndLogError_m0FF3DD2B9CEEF6E92BB15E65DAC3E1F5546C3E6E(__this, L_19, NULL);
	}

IL_00af:
	{
		bool L_20 = __this->___textureOffset;
		if (!L_20)
		{
			goto IL_00d0;
		}
	}
	{
		Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* L_21 = __this->___mat;
		String_t* L_22 = __this->___texturePropertyName;
		NullCheck(L_21);
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_23;
		L_23 = Material_GetTextureOffset_m4F9E2C96960DFE3C76B9D878F8E654D78185F9CA(L_21, L_22, NULL);
		__this->___currValue = L_23;
		goto IL_00e7;
	}

IL_00d0:
	{
		Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* L_24 = __this->___mat;
		String_t* L_25 = __this->___texturePropertyName;
		NullCheck(L_24);
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_26;
		L_26 = Material_GetTextureScale_mF3406F1439C275C25FEE0C4E19108B29AA77E693(L_24, L_25, NULL);
		__this->___currValue = L_26;
	}

IL_00e7:
	{
		bool L_27 = __this->___backAndForth;
		if (L_27)
		{
			goto IL_00fa;
		}
	}
	{
		bool L_28 = __this->___stopAtValue;
		if (!L_28)
		{
			goto IL_0202;
		}
	}

IL_00fa:
	{
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_29 = __this->___currValue;
		__this->___iniValue = L_29;
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7* L_30 = (Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7*)(&__this->___iniValue);
		float L_31 = L_30->___x;
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7* L_32 = (Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7*)(&__this->___maxValue);
		float L_33 = L_32->___x;
		__this->___goingUpX = (bool)((((float)L_31) < ((float)L_33))? 1 : 0);
		bool L_34 = __this->___goingUpX;
		if (L_34)
		{
			goto IL_0154;
		}
	}
	{
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7* L_35 = (Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7*)(&__this->___scrollSpeed);
		float L_36 = L_35->___x;
		if ((!(((float)L_36) > ((float)(0.0f)))))
		{
			goto IL_0154;
		}
	}
	{
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_37 = __this->___scrollSpeed;
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_38;
		L_38 = Vector2_op_Multiply_m2D984B613020089BF5165BA4CA10988E2DC771FE_inline(L_37, (-1.0f), NULL);
		__this->___scrollSpeed = L_38;
	}

IL_0154:
	{
		bool L_39 = __this->___goingUpX;
		if (!L_39)
		{
			goto IL_0184;
		}
	}
	{
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7* L_40 = (Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7*)(&__this->___scrollSpeed);
		float L_41 = L_40->___x;
		if ((!(((float)L_41) < ((float)(0.0f)))))
		{
			goto IL_0184;
		}
	}
	{
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_42 = __this->___scrollSpeed;
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_43;
		L_43 = Vector2_op_Multiply_m2D984B613020089BF5165BA4CA10988E2DC771FE_inline(L_42, (-1.0f), NULL);
		__this->___scrollSpeed = L_43;
	}

IL_0184:
	{
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7* L_44 = (Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7*)(&__this->___iniValue);
		float L_45 = L_44->___y;
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7* L_46 = (Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7*)(&__this->___maxValue);
		float L_47 = L_46->___y;
		__this->___goingUpY = (bool)((((float)L_45) < ((float)L_47))? 1 : 0);
		bool L_48 = __this->___goingUpY;
		if (L_48)
		{
			goto IL_01d2;
		}
	}
	{
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7* L_49 = (Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7*)(&__this->___scrollSpeed);
		float L_50 = L_49->___y;
		if ((!(((float)L_50) > ((float)(0.0f)))))
		{
			goto IL_01d2;
		}
	}
	{
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_51 = __this->___scrollSpeed;
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_52;
		L_52 = Vector2_op_Multiply_m2D984B613020089BF5165BA4CA10988E2DC771FE_inline(L_51, (-1.0f), NULL);
		__this->___scrollSpeed = L_52;
	}

IL_01d2:
	{
		bool L_53 = __this->___goingUpY;
		if (!L_53)
		{
			goto IL_0202;
		}
	}
	{
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7* L_54 = (Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7*)(&__this->___scrollSpeed);
		float L_55 = L_54->___y;
		if ((!(((float)L_55) < ((float)(0.0f)))))
		{
			goto IL_0202;
		}
	}
	{
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_56 = __this->___scrollSpeed;
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_57;
		L_57 = Vector2_op_Multiply_m2D984B613020089BF5165BA4CA10988E2DC771FE_inline(L_56, (-1.0f), NULL);
		__this->___scrollSpeed = L_57;
	}

IL_0202:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AllIn1VfxScrollShaderTexture_Update_m446DDC71A8F5C42761DCAD83195F3DC48F865AC9 (AllIn1VfxScrollShaderTexture_t0B1CA0D34AF4808EA9BA40B9CDBA558936EB3103* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Debug_t8394C7EEAECA3689C2C9B9DE9C7166D73596276F_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral68CB89848359D7BCEA0995C8FB01DAA1D5DFDE28);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralF5D8EF422ABD0284BA3EEB3BF58FBA9313575F4E);
		s_Il2CppMethodInitialized = true;
	}
	{
		Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* L_0 = __this->___mat;
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_1;
		L_1 = Object_op_Equality_mB6120F782D83091EF56A198FCEBCF066DB4A9605(L_0, (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*)NULL, NULL);
		if (!L_1)
		{
			goto IL_003d;
		}
	}
	{
		bool L_2 = __this->___isValid;
		if (!L_2)
		{
			goto IL_003c;
		}
	}
	{
		GameObject_t76FEDD663AB33C991A9C9A23129337651094216F* L_3;
		L_3 = Component_get_gameObject_m57AEFBB14DB39EC476F740BA000E170355DE691B(__this, NULL);
		NullCheck(L_3);
		String_t* L_4;
		L_4 = Object_get_name_mAC2F6B897CF1303BA4249B4CB55271AFACBB6392(L_3, NULL);
		String_t* L_5;
		L_5 = String_Concat_m8855A6DE10F84DA7F4EC113CADDB59873A25573B(_stringLiteralF5D8EF422ABD0284BA3EEB3BF58FBA9313575F4E, L_4, _stringLiteral68CB89848359D7BCEA0995C8FB01DAA1D5DFDE28, NULL);
		il2cpp_codegen_runtime_class_init_inline(Debug_t8394C7EEAECA3689C2C9B9DE9C7166D73596276F_il2cpp_TypeInfo_var);
		Debug_LogError_mB00B2B4468EF3CAF041B038D840820FB84C924B2(L_5, NULL);
		__this->___isValid = (bool)0;
	}

IL_003c:
	{
		return;
	}

IL_003d:
	{
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_6 = __this->___currValue;
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_7 = __this->___scrollSpeed;
		float L_8;
		L_8 = Time_get_deltaTime_mC3195000401F0FD167DD2F948FD2BC58330D0865(NULL);
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_9;
		L_9 = Vector2_op_Multiply_m2D984B613020089BF5165BA4CA10988E2DC771FE_inline(L_7, L_8, NULL);
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_10;
		L_10 = Vector2_op_Addition_m8136742CE6EE33BA4EB81C5F584678455917D2AE_inline(L_6, L_9, NULL);
		__this->___currValue = L_10;
		bool L_11 = __this->___backAndForth;
		if (!L_11)
		{
			goto IL_0109;
		}
	}
	{
		bool L_12 = __this->___goingUpX;
		if (!L_12)
		{
			goto IL_0092;
		}
	}
	{
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7* L_13 = (Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7*)(&__this->___currValue);
		float L_14 = L_13->___x;
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7* L_15 = (Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7*)(&__this->___maxValue);
		float L_16 = L_15->___x;
		if ((!(((float)L_14) >= ((float)L_16))))
		{
			goto IL_0092;
		}
	}
	{
		AllIn1VfxScrollShaderTexture_FlipGoingUp_mAF1B7A83DBB3020A14A98614C591A410344AC793(__this, (bool)1, NULL);
		goto IL_00b9;
	}

IL_0092:
	{
		bool L_17 = __this->___goingUpX;
		if (L_17)
		{
			goto IL_00b9;
		}
	}
	{
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7* L_18 = (Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7*)(&__this->___currValue);
		float L_19 = L_18->___x;
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7* L_20 = (Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7*)(&__this->___iniValue);
		float L_21 = L_20->___x;
		if ((!(((float)L_19) <= ((float)L_21))))
		{
			goto IL_00b9;
		}
	}
	{
		AllIn1VfxScrollShaderTexture_FlipGoingUp_mAF1B7A83DBB3020A14A98614C591A410344AC793(__this, (bool)1, NULL);
	}

IL_00b9:
	{
		bool L_22 = __this->___goingUpY;
		if (!L_22)
		{
			goto IL_00e2;
		}
	}
	{
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7* L_23 = (Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7*)(&__this->___currValue);
		float L_24 = L_23->___y;
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7* L_25 = (Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7*)(&__this->___maxValue);
		float L_26 = L_25->___y;
		if ((!(((float)L_24) >= ((float)L_26))))
		{
			goto IL_00e2;
		}
	}
	{
		AllIn1VfxScrollShaderTexture_FlipGoingUp_mAF1B7A83DBB3020A14A98614C591A410344AC793(__this, (bool)0, NULL);
		goto IL_0109;
	}

IL_00e2:
	{
		bool L_27 = __this->___goingUpY;
		if (L_27)
		{
			goto IL_0109;
		}
	}
	{
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7* L_28 = (Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7*)(&__this->___currValue);
		float L_29 = L_28->___y;
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7* L_30 = (Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7*)(&__this->___iniValue);
		float L_31 = L_30->___y;
		if ((!(((float)L_29) <= ((float)L_31))))
		{
			goto IL_0109;
		}
	}
	{
		AllIn1VfxScrollShaderTexture_FlipGoingUp_mAF1B7A83DBB3020A14A98614C591A410344AC793(__this, (bool)0, NULL);
	}

IL_0109:
	{
		bool L_32 = __this->___applyModulo;
		if (!L_32)
		{
			goto IL_0145;
		}
	}
	{
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7* L_33 = (Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7*)(&__this->___currValue);
		float* L_34 = (float*)(&L_33->___x);
		float* L_35 = L_34;
		float L_36 = *((float*)L_35);
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7* L_37 = (Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7*)(&__this->___modulo);
		float L_38 = L_37->___x;
		*((float*)L_35) = (float)(fmodf(L_36, L_38));
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7* L_39 = (Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7*)(&__this->___currValue);
		float* L_40 = (float*)(&L_39->___y);
		float* L_41 = L_40;
		float L_42 = *((float*)L_41);
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7* L_43 = (Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7*)(&__this->___modulo);
		float L_44 = L_43->___y;
		*((float*)L_41) = (float)(fmodf(L_42, L_44));
	}

IL_0145:
	{
		bool L_45 = __this->___stopAtValue;
		if (!L_45)
		{
			goto IL_0214;
		}
	}
	{
		bool L_46 = __this->___goingUpX;
		if (!L_46)
		{
			goto IL_0182;
		}
	}
	{
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7* L_47 = (Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7*)(&__this->___currValue);
		float L_48 = L_47->___x;
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7* L_49 = (Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7*)(&__this->___stopValue);
		float L_50 = L_49->___x;
		if ((!(((float)L_48) >= ((float)L_50))))
		{
			goto IL_0182;
		}
	}
	{
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7* L_51 = (Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7*)(&__this->___scrollSpeed);
		L_51->___x = (0.0f);
		goto IL_01b2;
	}

IL_0182:
	{
		bool L_52 = __this->___goingUpX;
		if (L_52)
		{
			goto IL_01b2;
		}
	}
	{
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7* L_53 = (Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7*)(&__this->___currValue);
		float L_54 = L_53->___x;
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7* L_55 = (Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7*)(&__this->___stopValue);
		float L_56 = L_55->___x;
		if ((!(((float)L_54) <= ((float)L_56))))
		{
			goto IL_01b2;
		}
	}
	{
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7* L_57 = (Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7*)(&__this->___scrollSpeed);
		L_57->___x = (0.0f);
	}

IL_01b2:
	{
		bool L_58 = __this->___goingUpY;
		if (!L_58)
		{
			goto IL_01e4;
		}
	}
	{
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7* L_59 = (Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7*)(&__this->___currValue);
		float L_60 = L_59->___y;
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7* L_61 = (Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7*)(&__this->___stopValue);
		float L_62 = L_61->___y;
		if ((!(((float)L_60) >= ((float)L_62))))
		{
			goto IL_01e4;
		}
	}
	{
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7* L_63 = (Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7*)(&__this->___scrollSpeed);
		L_63->___y = (0.0f);
		goto IL_0214;
	}

IL_01e4:
	{
		bool L_64 = __this->___goingUpY;
		if (L_64)
		{
			goto IL_0214;
		}
	}
	{
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7* L_65 = (Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7*)(&__this->___currValue);
		float L_66 = L_65->___y;
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7* L_67 = (Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7*)(&__this->___stopValue);
		float L_68 = L_67->___y;
		if ((!(((float)L_66) <= ((float)L_68))))
		{
			goto IL_0214;
		}
	}
	{
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7* L_69 = (Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7*)(&__this->___scrollSpeed);
		L_69->___y = (0.0f);
	}

IL_0214:
	{
		bool L_70 = __this->___textureOffset;
		if (!L_70)
		{
			goto IL_0234;
		}
	}
	{
		Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* L_71 = __this->___mat;
		int32_t L_72 = __this->___propertyShaderID;
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_73 = __this->___currValue;
		NullCheck(L_71);
		Material_SetTextureOffset_mB28E782AE9F9B4CB9D36F209C976F8A0FE7DF747(L_71, L_72, L_73, NULL);
		return;
	}

IL_0234:
	{
		Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* L_74 = __this->___mat;
		int32_t L_75 = __this->___propertyShaderID;
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_76 = __this->___currValue;
		NullCheck(L_74);
		Material_SetTextureScale_mBA092A3DCD393695B32801FD05F70A8CC58CB89D(L_74, L_75, L_76, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AllIn1VfxScrollShaderTexture_FlipGoingUp_mAF1B7A83DBB3020A14A98614C591A410344AC793 (AllIn1VfxScrollShaderTexture_t0B1CA0D34AF4808EA9BA40B9CDBA558936EB3103* __this, bool ___0_isXComponent, const RuntimeMethod* method) 
{
	{
		bool L_0 = ___0_isXComponent;
		if (!L_0)
		{
			goto IL_0027;
		}
	}
	{
		bool L_1 = __this->___goingUpX;
		__this->___goingUpX = (bool)((((int32_t)L_1) == ((int32_t)0))? 1 : 0);
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7* L_2 = (Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7*)(&__this->___scrollSpeed);
		float* L_3 = (float*)(&L_2->___x);
		float* L_4 = L_3;
		float L_5 = *((float*)L_4);
		*((float*)L_4) = (float)((float)il2cpp_codegen_multiply(L_5, (-1.0f)));
		return;
	}

IL_0027:
	{
		bool L_6 = __this->___goingUpY;
		__this->___goingUpY = (bool)((((int32_t)L_6) == ((int32_t)0))? 1 : 0);
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7* L_7 = (Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7*)(&__this->___scrollSpeed);
		float* L_8 = (float*)(&L_7->___y);
		float* L_9 = L_8;
		float L_10 = *((float*)L_9);
		*((float*)L_9) = (float)((float)il2cpp_codegen_multiply(L_10, (-1.0f)));
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AllIn1VfxScrollShaderTexture_DestroyComponentAndLogError_m0FF3DD2B9CEEF6E92BB15E65DAC3E1F5546C3E6E (AllIn1VfxScrollShaderTexture_t0B1CA0D34AF4808EA9BA40B9CDBA558936EB3103* __this, String_t* ___0_logError, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Debug_t8394C7EEAECA3689C2C9B9DE9C7166D73596276F_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		String_t* L_0 = ___0_logError;
		il2cpp_codegen_runtime_class_init_inline(Debug_t8394C7EEAECA3689C2C9B9DE9C7166D73596276F_il2cpp_TypeInfo_var);
		Debug_LogError_mB00B2B4468EF3CAF041B038D840820FB84C924B2(L_0, NULL);
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		Object_Destroy_mE97D0A766419A81296E8D4E5C23D01D3FE91ACBB(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AllIn1VfxScrollShaderTexture_OnDisable_mF76C26882A3006EE2D4B7CC25955F8B850E0B06D (AllIn1VfxScrollShaderTexture_t0B1CA0D34AF4808EA9BA40B9CDBA558936EB3103* __this, const RuntimeMethod* method) 
{
	{
		bool L_0 = __this->___restoreMaterialOnDisable;
		if (!L_0)
		{
			goto IL_0019;
		}
	}
	{
		Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* L_1 = __this->___mat;
		Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* L_2 = __this->___originalMat;
		NullCheck(L_1);
		Material_CopyPropertiesFromMaterial_m4148227E6A0B8E66315D8115F656B7F8BEAE915B(L_1, L_2, NULL);
	}

IL_0019:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AllIn1VfxScrollShaderTexture__ctor_mFF32DBC76C34C5F76D16CA6EE1193F16CEEE8AC5 (AllIn1VfxScrollShaderTexture_t0B1CA0D34AF4808EA9BA40B9CDBA558936EB3103* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral4B8146FB95E4F51B29DA41EB5F6D60F8FD0ECF21);
		s_Il2CppMethodInitialized = true;
	}
	{
		__this->___texturePropertyName = _stringLiteral4B8146FB95E4F51B29DA41EB5F6D60F8FD0ECF21;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___texturePropertyName), (void*)_stringLiteral4B8146FB95E4F51B29DA41EB5F6D60F8FD0ECF21);
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_0;
		L_0 = Vector2_get_zero_m32506C40EC2EE7D5D4410BF40D3EE683A3D5F32C_inline(NULL);
		__this->___scrollSpeed = L_0;
		__this->___textureOffset = (bool)1;
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_1;
		L_1 = Vector2_get_one_m9097EB8DC23C26118A591AF16702796C3EF51DFB_inline(NULL);
		__this->___maxValue = L_1;
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_2;
		L_2 = Vector2_get_zero_m32506C40EC2EE7D5D4410BF40D3EE683A3D5F32C_inline(NULL);
		__this->___iniValue = L_2;
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_3;
		L_3 = Vector2_get_one_m9097EB8DC23C26118A591AF16702796C3EF51DFB_inline(NULL);
		__this->___modulo = L_3;
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_4;
		L_4 = Vector2_get_zero_m32506C40EC2EE7D5D4410BF40D3EE683A3D5F32C_inline(NULL);
		__this->___stopValue = L_4;
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_5;
		L_5 = Vector2_get_zero_m32506C40EC2EE7D5D4410BF40D3EE683A3D5F32C_inline(NULL);
		__this->___currValue = L_5;
		__this->___isValid = (bool)1;
		MonoBehaviour__ctor_m592DB0105CA0BC97AA1C5F4AD27B12D68A3B7C1E(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SetAllIn1VfxCustomGlobalTime_Start_mC92C2125022DC43C6B927BF5948D4A797E61BE42 (SetAllIn1VfxCustomGlobalTime_t77136D14EF3C885BA7985A17CC75E8BB9D4D9CAE* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralF3D5ADFD704DD9FB58F49F6670F4DAA9E634657F);
		s_Il2CppMethodInitialized = true;
	}
	{
		int32_t L_0;
		L_0 = Shader_PropertyToID_mE98523D50F5656CAE89B30695C458253EB8956CA(_stringLiteralF3D5ADFD704DD9FB58F49F6670F4DAA9E634657F, NULL);
		__this->___globalTime = L_0;
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SetAllIn1VfxCustomGlobalTime_Update_m061450880471093ABC066065FE0725F732BDF5B1 (SetAllIn1VfxCustomGlobalTime_t77136D14EF3C885BA7985A17CC75E8BB9D4D9CAE* __this, const RuntimeMethod* method) 
{
	{
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3* L_0 = (Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3*)(&__this->___timeVector);
		float L_1;
		L_1 = Time_get_unscaledTime_mAF4040B858903E1325D1C65B8BF1AC61460B2503(NULL);
		L_0->___x = ((float)(L_1/(20.0f)));
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3* L_2 = (Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3*)(&__this->___timeVector);
		float L_3;
		L_3 = Time_get_unscaledTime_mAF4040B858903E1325D1C65B8BF1AC61460B2503(NULL);
		L_2->___y = L_3;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3* L_4 = (Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3*)(&__this->___timeVector);
		float L_5;
		L_5 = Time_get_unscaledTime_mAF4040B858903E1325D1C65B8BF1AC61460B2503(NULL);
		L_4->___z = ((float)il2cpp_codegen_multiply(L_5, (2.0f)));
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3* L_6 = (Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3*)(&__this->___timeVector);
		float L_7;
		L_7 = Time_get_unscaledTime_mAF4040B858903E1325D1C65B8BF1AC61460B2503(NULL);
		L_6->___w = ((float)il2cpp_codegen_multiply(L_7, (3.0f)));
		int32_t L_8 = __this->___globalTime;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_9 = __this->___timeVector;
		Shader_SetGlobalVector_mDC5F45B008D44A2C8BF6D450CFE8B58B847C8190(L_8, L_9, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SetAllIn1VfxCustomGlobalTime__ctor_mA35EECE553FF88334C3F456500E1FB33F28DDB88 (SetAllIn1VfxCustomGlobalTime_t77136D14EF3C885BA7985A17CC75E8BB9D4D9CAE* __this, const RuntimeMethod* method) 
{
	{
		MonoBehaviour__ctor_m592DB0105CA0BC97AA1C5F4AD27B12D68A3B7C1E(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AllIn1GraphicMaterialDuplicate_Awake_mAF133D27FBE98EFB1DEEE7A25C8CBC5476049BCC (AllIn1GraphicMaterialDuplicate_tEC6847E85A67C8AFB79A06C3E271091CCD6B3AA0* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Component_GetComponent_TisGraphic_tCBFCA4585A19E2B75465AECFEAC43F4016BF7931_mFE18E20FC92395F90E776DBC4CD214A4F2D97D90_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Graphic_tCBFCA4585A19E2B75465AECFEAC43F4016BF7931* L_0;
		L_0 = Component_GetComponent_TisGraphic_tCBFCA4585A19E2B75465AECFEAC43F4016BF7931_mFE18E20FC92395F90E776DBC4CD214A4F2D97D90(__this, Component_GetComponent_TisGraphic_tCBFCA4585A19E2B75465AECFEAC43F4016BF7931_mFE18E20FC92395F90E776DBC4CD214A4F2D97D90_RuntimeMethod_var);
		Graphic_tCBFCA4585A19E2B75465AECFEAC43F4016BF7931* L_1 = L_0;
		NullCheck(L_1);
		Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* L_2;
		L_2 = VirtualFuncInvoker0< Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* >::Invoke(32, L_1);
		Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* L_3 = (Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3*)il2cpp_codegen_object_new(Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3_il2cpp_TypeInfo_var);
		Material__ctor_mFCC42FB90257F1E8F7516A8640A79C465A39961C(L_3, L_2, NULL);
		NullCheck(L_1);
		VirtualActionInvoker1< Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* >::Invoke(33, L_1, L_3);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AllIn1GraphicMaterialDuplicate__ctor_mD51ED5244AFFF8CC67D241F800D0F836E152A148 (AllIn1GraphicMaterialDuplicate_tEC6847E85A67C8AFB79A06C3E271091CCD6B3AA0* __this, const RuntimeMethod* method) 
{
	{
		MonoBehaviour__ctor_m592DB0105CA0BC97AA1C5F4AD27B12D68A3B7C1E(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_op_Subtraction_mE42023FF80067CB44A1D4A27EB7CF2B24CABB828_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_a, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___1_b, const RuntimeMethod* method) 
{
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = ___0_a;
		float L_1 = L_0.___x;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_2 = ___1_b;
		float L_3 = L_2.___x;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_4 = ___0_a;
		float L_5 = L_4.___y;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_6 = ___1_b;
		float L_7 = L_6.___y;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_8 = ___0_a;
		float L_9 = L_8.___z;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_10 = ___1_b;
		float L_11 = L_10.___z;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_12;
		memset((&L_12), 0, sizeof(L_12));
		Vector3__ctor_m376936E6B999EF1ECBE57D990A386303E2283DE0_inline((&L_12), ((float)il2cpp_codegen_subtract(L_1, L_3)), ((float)il2cpp_codegen_subtract(L_5, L_7)), ((float)il2cpp_codegen_subtract(L_9, L_11)), NULL);
		V_0 = L_12;
		goto IL_0030;
	}

IL_0030:
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_13 = V_0;
		return L_13;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_get_normalized_m736BBF65D5CDA7A18414370D15B4DFCC1E466F07_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* __this, const RuntimeMethod* method) 
{
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = (*(Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)__this);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_1;
		L_1 = Vector3_Normalize_mEF8349CC39674236CFC694189AFD36E31F89AC8F_inline(L_0, NULL);
		V_0 = L_1;
		goto IL_000f;
	}

IL_000f:
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_2 = V_0;
		return L_2;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_op_UnaryNegation_m5450829F333BD2A88AF9A592C4EE331661225915_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_a, const RuntimeMethod* method) 
{
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = ___0_a;
		float L_1 = L_0.___x;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_2 = ___0_a;
		float L_3 = L_2.___y;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_4 = ___0_a;
		float L_5 = L_4.___z;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_6;
		memset((&L_6), 0, sizeof(L_6));
		Vector3__ctor_m376936E6B999EF1ECBE57D990A386303E2283DE0_inline((&L_6), ((-L_1)), ((-L_3)), ((-L_5)), NULL);
		V_0 = L_6;
		goto IL_001e;
	}

IL_001e:
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_7 = V_0;
		return L_7;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_op_Multiply_m87BA7C578F96C8E49BB07088DAAC4649F83B0353_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_a, float ___1_d, const RuntimeMethod* method) 
{
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = ___0_a;
		float L_1 = L_0.___x;
		float L_2 = ___1_d;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_3 = ___0_a;
		float L_4 = L_3.___y;
		float L_5 = ___1_d;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_6 = ___0_a;
		float L_7 = L_6.___z;
		float L_8 = ___1_d;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_9;
		memset((&L_9), 0, sizeof(L_9));
		Vector3__ctor_m376936E6B999EF1ECBE57D990A386303E2283DE0_inline((&L_9), ((float)il2cpp_codegen_multiply(L_1, L_2)), ((float)il2cpp_codegen_multiply(L_4, L_5)), ((float)il2cpp_codegen_multiply(L_7, L_8)), NULL);
		V_0 = L_9;
		goto IL_0021;
	}

IL_0021:
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_10 = V_0;
		return L_10;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_op_Addition_m78C0EC70CB66E8DCAC225743D82B268DAEE92067_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_a, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___1_b, const RuntimeMethod* method) 
{
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = ___0_a;
		float L_1 = L_0.___x;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_2 = ___1_b;
		float L_3 = L_2.___x;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_4 = ___0_a;
		float L_5 = L_4.___y;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_6 = ___1_b;
		float L_7 = L_6.___y;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_8 = ___0_a;
		float L_9 = L_8.___z;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_10 = ___1_b;
		float L_11 = L_10.___z;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_12;
		memset((&L_12), 0, sizeof(L_12));
		Vector3__ctor_m376936E6B999EF1ECBE57D990A386303E2283DE0_inline((&L_12), ((float)il2cpp_codegen_add(L_1, L_3)), ((float)il2cpp_codegen_add(L_5, L_7)), ((float)il2cpp_codegen_add(L_9, L_11)), NULL);
		V_0 = L_12;
		goto IL_0030;
	}

IL_0030:
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_13 = V_0;
		return L_13;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_get_up_m128AF3FDC820BF59D5DE86D973E7DE3F20C3AEBA_inline (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = ((Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_StaticFields*)il2cpp_codegen_static_fields_for(Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_il2cpp_TypeInfo_var))->___upVector;
		V_0 = L_0;
		goto IL_0009;
	}

IL_0009:
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_1 = V_0;
		return L_1;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 Vector4_op_Implicit_m2ECA73F345A7AD84144133E9E51657204002B12D_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_v, const RuntimeMethod* method) 
{
	Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = ___0_v;
		float L_1 = L_0.___x;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_2 = ___0_v;
		float L_3 = L_2.___y;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_4 = ___0_v;
		float L_5 = L_4.___z;
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_6;
		memset((&L_6), 0, sizeof(L_6));
		Vector4__ctor_m96B2CD8B862B271F513AF0BDC2EABD58E4DBC813_inline((&L_6), L_1, L_3, L_5, (0.0f), NULL);
		V_0 = L_6;
		goto IL_0020;
	}

IL_0020:
	{
		Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 L_7 = V_0;
		return L_7;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Color__ctor_m3786F0D6E510D9CFA544523A955870BD2A514C8C_inline (Color_tD001788D726C3A7F1379BEED0260B9591F440C1F* __this, float ___0_r, float ___1_g, float ___2_b, float ___3_a, const RuntimeMethod* method) 
{
	{
		float L_0 = ___0_r;
		__this->___r = L_0;
		float L_1 = ___1_g;
		__this->___g = L_1;
		float L_2 = ___2_b;
		__this->___b = L_2;
		float L_3 = ___3_a;
		__this->___a = L_3;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Mathf_Max_mF5379E63D2BBAC76D090748695D833934F8AD051_inline (float ___0_a, float ___1_b, const RuntimeMethod* method) 
{
	float V_0 = 0.0f;
	float G_B3_0 = 0.0f;
	{
		float L_0 = ___0_a;
		float L_1 = ___1_b;
		if ((((float)L_0) > ((float)L_1)))
		{
			goto IL_0008;
		}
	}
	{
		float L_2 = ___1_b;
		G_B3_0 = L_2;
		goto IL_0009;
	}

IL_0008:
	{
		float L_3 = ___0_a;
		G_B3_0 = L_3;
	}

IL_0009:
	{
		V_0 = G_B3_0;
		goto IL_000c;
	}

IL_000c:
	{
		float L_4 = V_0;
		return L_4;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Color_tD001788D726C3A7F1379BEED0260B9591F440C1F Color_Lerp_mE79F87889843ECDC188E4CB5B5E1F1B2256E5EBE_inline (Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___0_a, Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___1_b, float ___2_t, const RuntimeMethod* method) 
{
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		float L_0 = ___2_t;
		float L_1;
		L_1 = Mathf_Clamp01_mA7E048DBDA832D399A581BE4D6DED9FA44CE0F14_inline(L_0, NULL);
		___2_t = L_1;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_2 = ___0_a;
		float L_3 = L_2.___r;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_4 = ___1_b;
		float L_5 = L_4.___r;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_6 = ___0_a;
		float L_7 = L_6.___r;
		float L_8 = ___2_t;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_9 = ___0_a;
		float L_10 = L_9.___g;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_11 = ___1_b;
		float L_12 = L_11.___g;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_13 = ___0_a;
		float L_14 = L_13.___g;
		float L_15 = ___2_t;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_16 = ___0_a;
		float L_17 = L_16.___b;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_18 = ___1_b;
		float L_19 = L_18.___b;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_20 = ___0_a;
		float L_21 = L_20.___b;
		float L_22 = ___2_t;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_23 = ___0_a;
		float L_24 = L_23.___a;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_25 = ___1_b;
		float L_26 = L_25.___a;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_27 = ___0_a;
		float L_28 = L_27.___a;
		float L_29 = ___2_t;
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_30;
		memset((&L_30), 0, sizeof(L_30));
		Color__ctor_m3786F0D6E510D9CFA544523A955870BD2A514C8C_inline((&L_30), ((float)il2cpp_codegen_add(L_3, ((float)il2cpp_codegen_multiply(((float)il2cpp_codegen_subtract(L_5, L_7)), L_8)))), ((float)il2cpp_codegen_add(L_10, ((float)il2cpp_codegen_multiply(((float)il2cpp_codegen_subtract(L_12, L_14)), L_15)))), ((float)il2cpp_codegen_add(L_17, ((float)il2cpp_codegen_multiply(((float)il2cpp_codegen_subtract(L_19, L_21)), L_22)))), ((float)il2cpp_codegen_add(L_24, ((float)il2cpp_codegen_multiply(((float)il2cpp_codegen_subtract(L_26, L_28)), L_29)))), NULL);
		V_0 = L_30;
		goto IL_0069;
	}

IL_0069:
	{
		Color_tD001788D726C3A7F1379BEED0260B9591F440C1F L_31 = V_0;
		return L_31;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 Vector2_op_Multiply_m2D984B613020089BF5165BA4CA10988E2DC771FE_inline (Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___0_a, float ___1_d, const RuntimeMethod* method) 
{
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_0 = ___0_a;
		float L_1 = L_0.___x;
		float L_2 = ___1_d;
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_3 = ___0_a;
		float L_4 = L_3.___y;
		float L_5 = ___1_d;
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_6;
		memset((&L_6), 0, sizeof(L_6));
		Vector2__ctor_m9525B79969AFFE3254B303A40997A56DEEB6F548_inline((&L_6), ((float)il2cpp_codegen_multiply(L_1, L_2)), ((float)il2cpp_codegen_multiply(L_4, L_5)), NULL);
		V_0 = L_6;
		goto IL_0019;
	}

IL_0019:
	{
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_7 = V_0;
		return L_7;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 Vector2_op_Addition_m8136742CE6EE33BA4EB81C5F584678455917D2AE_inline (Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___0_a, Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___1_b, const RuntimeMethod* method) 
{
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_0 = ___0_a;
		float L_1 = L_0.___x;
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_2 = ___1_b;
		float L_3 = L_2.___x;
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_4 = ___0_a;
		float L_5 = L_4.___y;
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_6 = ___1_b;
		float L_7 = L_6.___y;
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_8;
		memset((&L_8), 0, sizeof(L_8));
		Vector2__ctor_m9525B79969AFFE3254B303A40997A56DEEB6F548_inline((&L_8), ((float)il2cpp_codegen_add(L_1, L_3)), ((float)il2cpp_codegen_add(L_5, L_7)), NULL);
		V_0 = L_8;
		goto IL_0023;
	}

IL_0023:
	{
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_9 = V_0;
		return L_9;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 Vector2_get_zero_m32506C40EC2EE7D5D4410BF40D3EE683A3D5F32C_inline (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_0 = ((Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7_StaticFields*)il2cpp_codegen_static_fields_for(Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7_il2cpp_TypeInfo_var))->___zeroVector;
		V_0 = L_0;
		goto IL_0009;
	}

IL_0009:
	{
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_1 = V_0;
		return L_1;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 Vector2_get_one_m9097EB8DC23C26118A591AF16702796C3EF51DFB_inline (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_0 = ((Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7_StaticFields*)il2cpp_codegen_static_fields_for(Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7_il2cpp_TypeInfo_var))->___oneVector;
		V_0 = L_0;
		goto IL_0009;
	}

IL_0009:
	{
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_1 = V_0;
		return L_1;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Vector3__ctor_m376936E6B999EF1ECBE57D990A386303E2283DE0_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* __this, float ___0_x, float ___1_y, float ___2_z, const RuntimeMethod* method) 
{
	{
		float L_0 = ___0_x;
		__this->___x = L_0;
		float L_1 = ___1_y;
		__this->___y = L_1;
		float L_2 = ___2_z;
		__this->___z = L_2;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_Normalize_mEF8349CC39674236CFC694189AFD36E31F89AC8F_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_value, const RuntimeMethod* method) 
{
	float V_0 = 0.0f;
	bool V_1 = false;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_2;
	memset((&V_2), 0, sizeof(V_2));
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = ___0_value;
		float L_1;
		L_1 = Vector3_Magnitude_m21652D951393A3D7CE92CE40049A0E7F76544D1B_inline(L_0, NULL);
		V_0 = L_1;
		float L_2 = V_0;
		V_1 = (bool)((((float)L_2) > ((float)(9.99999975E-06f)))? 1 : 0);
		bool L_3 = V_1;
		if (!L_3)
		{
			goto IL_001e;
		}
	}
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_4 = ___0_value;
		float L_5 = V_0;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_6;
		L_6 = Vector3_op_Division_mCC6BB24E372AB96B8380D1678446EF6A8BAE13BB_inline(L_4, L_5, NULL);
		V_2 = L_6;
		goto IL_0026;
	}

IL_001e:
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_7;
		L_7 = Vector3_get_zero_m0C1249C3F25B1C70EAD3CC8B31259975A457AE39_inline(NULL);
		V_2 = L_7;
		goto IL_0026;
	}

IL_0026:
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_8 = V_2;
		return L_8;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Vector4__ctor_m96B2CD8B862B271F513AF0BDC2EABD58E4DBC813_inline (Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3* __this, float ___0_x, float ___1_y, float ___2_z, float ___3_w, const RuntimeMethod* method) 
{
	{
		float L_0 = ___0_x;
		__this->___x = L_0;
		float L_1 = ___1_y;
		__this->___y = L_1;
		float L_2 = ___2_z;
		__this->___z = L_2;
		float L_3 = ___3_w;
		__this->___w = L_3;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Mathf_Clamp01_mA7E048DBDA832D399A581BE4D6DED9FA44CE0F14_inline (float ___0_value, const RuntimeMethod* method) 
{
	bool V_0 = false;
	float V_1 = 0.0f;
	bool V_2 = false;
	{
		float L_0 = ___0_value;
		V_0 = (bool)((((float)L_0) < ((float)(0.0f)))? 1 : 0);
		bool L_1 = V_0;
		if (!L_1)
		{
			goto IL_0015;
		}
	}
	{
		V_1 = (0.0f);
		goto IL_002d;
	}

IL_0015:
	{
		float L_2 = ___0_value;
		V_2 = (bool)((((float)L_2) > ((float)(1.0f)))? 1 : 0);
		bool L_3 = V_2;
		if (!L_3)
		{
			goto IL_0029;
		}
	}
	{
		V_1 = (1.0f);
		goto IL_002d;
	}

IL_0029:
	{
		float L_4 = ___0_value;
		V_1 = L_4;
		goto IL_002d;
	}

IL_002d:
	{
		float L_5 = V_1;
		return L_5;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Vector2__ctor_m9525B79969AFFE3254B303A40997A56DEEB6F548_inline (Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7* __this, float ___0_x, float ___1_y, const RuntimeMethod* method) 
{
	{
		float L_0 = ___0_x;
		__this->___x = L_0;
		float L_1 = ___1_y;
		__this->___y = L_1;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Vector3_Magnitude_m21652D951393A3D7CE92CE40049A0E7F76544D1B_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_vector, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Math_tEB65DE7CA8B083C412C969C92981C030865486CE_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	float V_0 = 0.0f;
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = ___0_vector;
		float L_1 = L_0.___x;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_2 = ___0_vector;
		float L_3 = L_2.___x;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_4 = ___0_vector;
		float L_5 = L_4.___y;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_6 = ___0_vector;
		float L_7 = L_6.___y;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_8 = ___0_vector;
		float L_9 = L_8.___z;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_10 = ___0_vector;
		float L_11 = L_10.___z;
		il2cpp_codegen_runtime_class_init_inline(Math_tEB65DE7CA8B083C412C969C92981C030865486CE_il2cpp_TypeInfo_var);
		double L_12;
		L_12 = sqrt(((double)((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_1, L_3)), ((float)il2cpp_codegen_multiply(L_5, L_7)))), ((float)il2cpp_codegen_multiply(L_9, L_11))))));
		V_0 = ((float)L_12);
		goto IL_0034;
	}

IL_0034:
	{
		float L_13 = V_0;
		return L_13;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_op_Division_mCC6BB24E372AB96B8380D1678446EF6A8BAE13BB_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_a, float ___1_d, const RuntimeMethod* method) 
{
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = ___0_a;
		float L_1 = L_0.___x;
		float L_2 = ___1_d;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_3 = ___0_a;
		float L_4 = L_3.___y;
		float L_5 = ___1_d;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_6 = ___0_a;
		float L_7 = L_6.___z;
		float L_8 = ___1_d;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_9;
		memset((&L_9), 0, sizeof(L_9));
		Vector3__ctor_m376936E6B999EF1ECBE57D990A386303E2283DE0_inline((&L_9), ((float)(L_1/L_2)), ((float)(L_4/L_5)), ((float)(L_7/L_8)), NULL);
		V_0 = L_9;
		goto IL_0021;
	}

IL_0021:
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_10 = V_0;
		return L_10;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_get_zero_m0C1249C3F25B1C70EAD3CC8B31259975A457AE39_inline (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = ((Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_StaticFields*)il2cpp_codegen_static_fields_for(Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_il2cpp_TypeInfo_var))->___zeroVector;
		V_0 = L_0;
		goto IL_0009;
	}

IL_0009:
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_1 = V_0;
		return L_1;
	}
}

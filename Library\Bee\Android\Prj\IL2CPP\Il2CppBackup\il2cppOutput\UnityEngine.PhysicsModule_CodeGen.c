﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void Collision_get_relativeVelocity_mAD9D45864C56FFAB284E77835BF75DF86D4E4CC0 (void);
extern void Collision_get_body_mA03043499C5BAF241F96F3FAEE183F7C5371A246 (void);
extern void Collision_get_collider_mBB5A086C78FE4BE0589E216F899B611673ADD25D (void);
extern void Collision_get_gameObject_m846FADBCA43E1849D3FE4D5EA44C02D055A70B3E (void);
extern void Collision_set_Flipped_m5AF63260E99357BB87655DDAD6316568BE65F3A1 (void);
extern void Collision_get_contactCount_m063F555F6D8E5D1BC995C69306041280CE8BF150 (void);
extern void Collision_get_contacts_m2E8E27E0399230DFA4303A4F4D81C1BD55CBC473 (void);
extern void Collision__ctor_mC3F14BC1026130B6B0E6BB83D7431753C3484912 (void);
extern void Collision__ctor_m6A02AD9B6F96A755B3A3A3A280CC7D2533228DA7 (void);
extern void Collision_Reuse_mC2E21A6480EE1DCEAF71F2EAF3E0CAEFD42EA90C (void);
extern void Collision_GetContact_m34D66AD97A8DB36AFE0711276C990742B6FE4BCD (void);
extern void Physics_OnSceneContactModify_m52106C18952BF4768B05F67FAF2B7F6F6F7D0C9D (void);
extern void Physics_get_gravity_m94393492AE4ED8B38A22ECCDCD2DDDB71BFA010D (void);
extern void Physics_get_invokeCollisionCallbacks_m73AE9C988EC57467D9B8699B376759D8E44133C8 (void);
extern void Physics_get_defaultPhysicsScene_mC5D2BC20734D32FB421163F066BD5FB4118C633A (void);
extern void Physics_Raycast_m453681A406AADE0A30227D955279F5E7050B790D (void);
extern void Physics_Raycast_m0679FB03C9AFC1E803B8F8AE6CAB409670D31377 (void);
extern void Physics_Raycast_mCFF84927BE3EC1780DBA34CCED374E7FF12ABCBE (void);
extern void Physics_Raycast_mCAA46C95211C7BB95697A347B036C012D26EB028 (void);
extern void Physics_Raycast_mA782767AD4F149FBEA32C71460DFF061B7563688 (void);
extern void Physics_Raycast_m56120FFEF0D4F0A44CCA505B5C946E6FB8742F12 (void);
extern void Physics_Raycast_m011EA7022C33B2C499EF744E5AF3E01EEB8FBD33 (void);
extern void Physics_Raycast_m1B27F500505FFB57D78548B9F5A540A2AD092903 (void);
extern void Physics_Raycast_m9879C28DFF6CD3048F2365BC01C855565EE141F8 (void);
extern void Physics_Raycast_m5CAA0AEDB2A6FB26E5F42A8EA560A61CAAF12E50 (void);
extern void Physics_Raycast_m7A0FEA813B93A82713C06D8466F0A21325743488 (void);
extern void Physics_Raycast_mDB89EB287ED040E534F6A933683A070D29DC14D3 (void);
extern void Physics_Raycast_mCCD2542138D11E665A5D4F413C1547EE7D794DEB (void);
extern void Physics_Raycast_m34AC1210E893A9EF969BD2C7104B10BE5B580025 (void);
extern void Physics_Raycast_m839BA104A76B928A03F075C622923C6FCD2F8685 (void);
extern void Physics_Raycast_mCAC9F02A1AAB49E16B384EBC8318E2DF30F4B0E5 (void);
extern void Physics_Linecast_m399C6C11AD7ECE11241A37C08BAB4D97CF3CB925 (void);
extern void Physics_Linecast_m4F2A0744FE106002EE26D12B6137FC21C019B932 (void);
extern void Physics_SphereCast_mDB2140FE8561D0CE870037527DACC44AB18A346D (void);
extern void Physics_SphereCast_mD53E280903384004D4304150552D50DD3F8C1F58 (void);
extern void Physics_Internal_RaycastAll_mC128593FD48E6F237BE59CFCDC7DDE7A4E8CB074 (void);
extern void Physics_RaycastAll_m8B7FB8419A65BEE78927D0EE84916E8DBE7ECD34 (void);
extern void Physics_RaycastAll_m69ED0FF0B70ADBC45B907783C87B308E786F6D51 (void);
extern void Physics_RaycastAll_mDCBE530EF2ACD21EAADEA829259291D7327BC80E (void);
extern void Physics_RaycastAll_mE56962F670046BE618FFE8D9B19595A896922789 (void);
extern void Physics_RaycastAll_mD1643DB52C4E415083E215B154FEB9DFA3AD6D74 (void);
extern void Physics_RaycastAll_m4055619E0F7EFA04620EAA0517F8393C4EBCFE87 (void);
extern void Physics_RaycastAll_m1BBD4E474814BEC9B52B015081A256AE2FE00468 (void);
extern void Physics_RaycastAll_mE94864EF8243F7D3A26C8666CEB02166C3742CB2 (void);
extern void Physics_RaycastNonAlloc_mB37DE98E8C9407C3DB2FB488BAB1CF3A7C6FFFCE (void);
extern void Physics_RaycastNonAlloc_m2BFEE9072E390ED6ACD500FD0AE4E714DE9549BC (void);
extern void Physics_RaycastNonAlloc_m1908CB5E0D0570E9C88B6C259041520DD4D3169C (void);
extern void Physics_RaycastNonAlloc_m1961CFCDB7631C7FF4D12F88904CF1BEB24A6C3E (void);
extern void Physics_RaycastNonAlloc_mB8FE279E06CE87D77387AA9A10562B8052DC8836 (void);
extern void Physics_RaycastNonAlloc_m4CFAA8CA088502DA71D748D276BDAAEF234B12B0 (void);
extern void Physics_RaycastNonAlloc_m3EEB10539C49FEAD9533142FEE6578148A48FFA9 (void);
extern void Physics_RaycastNonAlloc_mBDC9E19F4E3C82DCE03D799FDD41FB3314209460 (void);
extern void Physics_OverlapSphere_Internal_m654C73F0B586E5DCF2066466C4AB7B3221AE6E9B (void);
extern void Physics_OverlapSphere_m348CF43E53C703DEF4A6780A3B9DE2A1FB958318 (void);
extern void Physics_OverlapSphere_mCFA1C44458F8548C911C16F82077DA4C35D43F69 (void);
extern void Physics_get_reuseCollisionCallbacks_mB23A11C02B893238B5631A38F2FBB4C63A3B7541 (void);
extern void Physics_Query_ClosestPoint_mC72F36C1868C7830580ACF450FF84F797DC1C1EA (void);
extern void Physics_ClosestPoint_m440650B860B4298CEEAC45599E1B326A3B8FFF41 (void);
extern void Physics_OverlapSphereNonAlloc_mED890C8454FCC0354A94F97453707FA01B27AE83 (void);
extern void Physics_OverlapSphereNonAlloc_m48E30DC51482BBBBDD762ECE1DF83A70088F70DA (void);
extern void Physics_SphereCastNonAlloc_m21B951284ED5217AB1395B08B963C4C9661F928C (void);
extern void Physics_SphereCastNonAlloc_m942717E0D3B43A44253447354FC32BA148EBC105 (void);
extern void Physics_GetColliderByInstanceID_m0318A1C3CEC5AC6B42AB1F541EC3EE8909712220 (void);
extern void Physics_GetBodyByInstanceID_mC45F93E518D6F1FC136DD3FB4377B3CC9F244725 (void);
extern void Physics_SendOnCollisionEnter_mA48BA0630EE3D28320C602A15B5BDD887FA24144 (void);
extern void Physics_SendOnCollisionStay_m2CC0293E4757CF89183A125270047A5054515590 (void);
extern void Physics_SendOnCollisionExit_m6E96913C80E49A77FD367FBA5EF63755A81AA7D7 (void);
extern void Physics_OnSceneContact_mFE8673EF13DD22B5C727BF5E2EFC4CF5E410C73A (void);
extern void Physics_ReportContacts_m9E7B0F448F534C8DEBBA6E33D86506C2500C919D (void);
extern void Physics_GetCollisionToReport_m7D22E5DD29678C65830A05A249650C49947A5D4E (void);
extern void Physics__cctor_m1E2D4816C77050D34F6D01F43E63D7969410AE2A (void);
extern void Physics_get_gravity_Injected_mBB1051A73AE88880090895AB007FEF35965E192E (void);
extern void Physics_get_defaultPhysicsScene_Injected_mE86AE6A398435C1754A824B2B35DF13126A6C5D6 (void);
extern void Physics_Internal_RaycastAll_Injected_mAFAA47E2224DEA0ABF1A2188A969E7A663E50C92 (void);
extern void Physics_OverlapSphere_Internal_Injected_mB70E77E63A51711DDE22E7319B012CBD57DA6C0B (void);
extern void Physics_Query_ClosestPoint_Injected_mDB84C796000699B2CF45B2F054EDBE652371C975 (void);
extern void ContactEventDelegate__ctor_mF0844AC2AA36D48C199DDDBFA55627E43980CEEE (void);
extern void ContactEventDelegate_Invoke_m84BF3B9092BD4F6D43A870421F8389BC7B0E0769 (void);
extern void RaycastHit_get_collider_m84B160439BBEAB6D9E94B799F720E25C9E2D444D (void);
extern void RaycastHit_get_point_m02B764612562AFE0F998CC7CFB2EEDE41BA47F39 (void);
extern void RaycastHit_get_normal_mD8741B70D2039C5CAFC4368D4CE59D89562040B5 (void);
extern void RaycastHit_get_distance_m035194B0E9BB6229259CFC43B095A9C8E5011C78 (void);
extern void RaycastHit_get_transform_m89DB7FCFC50E0213A37CBE089400064B8FA19155 (void);
extern void RaycastHit_get_rigidbody_mE6FCB1B1A9F0C8D4185A484C10B9A5403CCD6005 (void);
extern void Rigidbody_get_velocity_mAE331303E7214402C93E2183D0AA1198F425F843 (void);
extern void Rigidbody_set_velocity_mE4031DF1C2C1CCE889F2AC9D8871D83795BB0D62 (void);
extern void Rigidbody_set_angularVelocity_m23266B4E52BF0D2E65CC984AC73CC40B8D4A27E0 (void);
extern void Rigidbody_get_mass_m09DDDDC437499B83B3BD0D77C134BFDC3E667054 (void);
extern void Rigidbody_get_useGravity_mBDA227BDCB0F9A81B61A6592929EE43EDDEE7D16 (void);
extern void Rigidbody_set_isKinematic_m6C3FD3EA358DADA3B191F2449CF1C4F8B22695ED (void);
extern void Rigidbody_set_freezeRotation_m6D049F82E9133020C31EEFB35A179A56364325DC (void);
extern void Rigidbody_set_constraints_mE81BF0DAEB980E320538231E092CA4663885A9A3 (void);
extern void Rigidbody_get_centerOfMass_mA66BE4DE0469545EBCF49A66EE4FDD3A5D0ADF91 (void);
extern void Rigidbody_set_centerOfMass_m9D4A68D102498C7DBCD91278FF5EE7EE0BF2B188 (void);
extern void Rigidbody_get_position_m4ECB79BDBBF8FD1EA572EDB792D3330DDED24691 (void);
extern void Rigidbody_set_position_mA15BE12B8D82220E8CA90A0F0CBFB206FE81B41C (void);
extern void Rigidbody_get_rotation_m07882A7024FB3F96BA13EC577A96163BBB621AA1 (void);
extern void Rigidbody_set_rotation_mF2FC85A4A26AD9FED7DE0061889DF5A408461A5D (void);
extern void Rigidbody_MovePosition_mB2CD29ABC8F59AC338C0A3A5A6B75C38FDA92CA9 (void);
extern void Rigidbody_MoveRotation_m85825C7206E770E39DED9EE6D792702F577A891D (void);
extern void Rigidbody_GetPointVelocity_m94324B9CDC28751DB27594ADE76FEAB5EC4EB1BD (void);
extern void Rigidbody_AddForce_mBDBC288D0E266BC1B62E3649B4FCE46E7EA9CCBC (void);
extern void Rigidbody_AddForce_m7A3EEEED21F986917107CBA6CC0106DCBC212198 (void);
extern void Rigidbody_AddTorque_m7922F76C73DACF9E1610D72726C01709C14F0937 (void);
extern void Rigidbody_AddTorque_m39C767D6CD12B2D12D575E2B469CB5565BFA30B6 (void);
extern void Rigidbody_AddForceAtPosition_m61575E676B16690BEC0FD29841EAD35CC40B642C (void);
extern void Rigidbody_AddForceAtPosition_mA4226D0A30E0B55CB0CAD2A956EA16C546505965 (void);
extern void Rigidbody_AddExplosionForce_mE4673F6D1DA0C206DA79659E9005A0F067348402 (void);
extern void Rigidbody_AddExplosionForce_mD36F7D864F32F22DA1783D20F6E9563A9C51DFA1 (void);
extern void Rigidbody__ctor_mB4E21922228AED3B52D8696D54F5B514F922CB07 (void);
extern void Rigidbody_get_velocity_Injected_mFD6FCA2857D9953AA953DB9AAF26A88CA881171C (void);
extern void Rigidbody_set_velocity_Injected_m41B399E90D6AA49BABD3C178B3183AD3BBB4EAC4 (void);
extern void Rigidbody_set_angularVelocity_Injected_mD7EA47CB618918BD45985951E5A5388853975E68 (void);
extern void Rigidbody_get_centerOfMass_Injected_m1E6C506D04F8D066FAD03C571FA926E8FACB1518 (void);
extern void Rigidbody_set_centerOfMass_Injected_mF33B1D20FF435377B48235CDD322478131AE4B1B (void);
extern void Rigidbody_get_position_Injected_m12A715C52CD3C7F66125950D7AB6ECFCF4336626 (void);
extern void Rigidbody_set_position_Injected_m8EB4DEECAE5A3D841A71B015807FF0820C3A361B (void);
extern void Rigidbody_get_rotation_Injected_m38431B37B78B4DF59B59F9CB7E609820430051F8 (void);
extern void Rigidbody_set_rotation_Injected_mDE5B37E97D8FE16D9AD8FC6B9213A106B28589D7 (void);
extern void Rigidbody_MovePosition_Injected_mF2CDF14960A920DCDDEAFA49A7E066A2FF021E37 (void);
extern void Rigidbody_MoveRotation_Injected_m75B6A86B8BE8D68714CA5356DDCC11D24B96B505 (void);
extern void Rigidbody_GetPointVelocity_Injected_m343FC8424FDE36AE563D07BC3B7506CF94E3E4A4 (void);
extern void Rigidbody_AddForce_Injected_m094E54DEA6CEAEA340F053D077CDF0753900F48E (void);
extern void Rigidbody_AddTorque_Injected_m22FC0760ED37AE38443DB926B2A7D87470A63CF2 (void);
extern void Rigidbody_AddForceAtPosition_Injected_m24F286471C9928629A50A5C1A0DF22698172438C (void);
extern void Rigidbody_AddExplosionForce_Injected_mB02BEE3B033E215880374706864990CEA12B17D7 (void);
extern void Collider_get_enabled_mDBFB488088ADB14C8016A83EF445653AC5A4A12B (void);
extern void Collider_set_enabled_m8D5C3B5047592D227A52560FC9723D176E209F70 (void);
extern void Collider_get_attachedRigidbody_m060304DB909A1FACD260EBB619D64D39129739AD (void);
extern void Collider_get_isTrigger_mFF457F6AA71D173F9A11BAF00C35E5AE12952F87 (void);
extern void Collider_set_isTrigger_mFCD22F3EB5E28C97863956AB725D53F7F4B7CA78 (void);
extern void Collider_ClosestPoint_mFFF9B6F6CF9F18B22B325835A3E2E78A1C03BFCB (void);
extern void Collider_get_bounds_mCC32F749590E9A85C7930E5355661367F78E4CB4 (void);
extern void Collider_Raycast_mBFA55E4B9BD7EE4E8D4107ADF24D2FA0F165FA2C (void);
extern void Collider_Raycast_mD7683E94051173B3FFC0862F4A17847E94AEB938 (void);
extern void Collider__ctor_m8975C6CCFC0E5740C523DB4A52ACC7F4A021F8FA (void);
extern void Collider_ClosestPoint_Injected_m4E218A16FABAA4615270B9CD82DC66E130AAFE77 (void);
extern void Collider_get_bounds_Injected_m1BDB8DBC0BC2BFC51D4A185C494EDB0997B93A43 (void);
extern void Collider_Raycast_Injected_mAFAD355B658765116985B737217587C68BF257A3 (void);
extern void CharacterController_Move_mE3F7AC1B4A2D6955980811C088B68ED3A31D2DA4 (void);
extern void CharacterController_get_isGrounded_m548072EC190878925C0F97595B6C307714EFDD67 (void);
extern void CharacterController__ctor_m6A906ADB773BC0AE534C9348A56747D931B194B3 (void);
extern void CharacterController_Move_Injected_m7F25C33CF948858A8D5822EF73FAE7A16AE65C86 (void);
extern void MeshCollider_get_sharedMesh_mFB4B8534501C29930D2D3710D6D82E60093FA21E (void);
extern void MeshCollider_get_convex_m0C0F6D0798413D633814D307EC970F7752B3C9D1 (void);
extern void CapsuleCollider_get_radius_m2462B43ECAC92386AAED85AA1DFD66440972D9D5 (void);
extern void CapsuleCollider_get_height_m63A31072F296AEE6222DC9C88704882BB6A54A24 (void);
extern void CapsuleCollider_get_direction_mE6D56B0990E3F2FACA983679C251949FE3FC6DFA (void);
extern void BoxCollider_get_size_mC1A2DD270B04DFF5961F9F90DC147C271F72258E (void);
extern void BoxCollider__ctor_m7E069AACEC2B76129335D222FC8B8A63FD50656F (void);
extern void BoxCollider_get_size_Injected_m4F20D7B3D8FB4360C9E2986FB6A8CC66ABC89511 (void);
extern void SphereCollider_get_center_m122A197607CD350873539A0EEE3BA10E8BE1759E (void);
extern void SphereCollider_get_radius_m1BB513491906E76A4F71929E3DB72A1542309697 (void);
extern void SphereCollider__ctor_mA8570CBE8C0E74C607669DC4E0CCA6CB1E4CB200 (void);
extern void SphereCollider_get_center_Injected_m26E71B48B49E3EF89A4DC523015F243A385CF0E9 (void);
extern void ContactPoint_get_point_mCCDFDACC5D8DB469898060A56A3CC45132911208 (void);
extern void ContactPoint_get_normal_mD7F0567CA2FD68644F7C6FE318E10C4D15F92AD6 (void);
extern void ContactPoint_get_thisCollider_m5CECC2F86CD3D73FE35543127C22C02D8ED1AFD6 (void);
extern void ContactPoint__ctor_mC0A53F0787CB05D31B97E761426675C3C2DC194B (void);
extern void PhysicsScene_ToString_mA4E28A3068A823D16D96BBA45115A2C457FC57C7 (void);
extern void PhysicsScene_GetHashCode_m368888FB861F994FADEEDD281BD02B090C561814 (void);
extern void PhysicsScene_Equals_mE3A11329AB6C2F4F76D2321D8BAE52671A2EDDA3 (void);
extern void PhysicsScene_Equals_m81E4A78FC3644FDC44044B3A5F19F1C4283648A1 (void);
extern void PhysicsScene_Raycast_m68D255133E274C5DDF33102EAAE70990C2A0A730 (void);
extern void PhysicsScene_Internal_RaycastTest_m729F4A577F5DD911131C5321EC28E44F98A60BA0 (void);
extern void PhysicsScene_Raycast_m6EE0783D1B113CAD5450A2CB876F6CA305BAD2CE (void);
extern void PhysicsScene_Internal_Raycast_m0211A7BDE011181718838F063296D51F88D92E74 (void);
extern void PhysicsScene_Raycast_m3BD571CF6901C59C286D7B58ED9D15D836BC54C3 (void);
extern void PhysicsScene_Internal_RaycastNonAlloc_mC339255AAFC484588C813D7BE2BDAE03797D26DB (void);
extern void PhysicsScene_Query_SphereCast_m8E6770FE64FB74157199217381AA1A99B3CF580B (void);
extern void PhysicsScene_Internal_SphereCast_mE4B0FBE790E2A7309F7807F5F1EFB909D21E07BF (void);
extern void PhysicsScene_SphereCast_mEB124233FFEA3BD179C9DE22E410290D7EB247C4 (void);
extern void PhysicsScene_Internal_SphereCastNonAlloc_mFAB1960B109B872B9712E5CED28E43A944E9649F (void);
extern void PhysicsScene_SphereCast_m2C89211A7462980013209F0B22B3D96B0963AF9F (void);
extern void PhysicsScene_OverlapSphereNonAlloc_Internal_m0F7B77B20925E6D449F858C08AD833E37FD406E1 (void);
extern void PhysicsScene_OverlapSphere_m0E853FB04ECE662CFA9FF522D8A4E9CE04903D01 (void);
extern void PhysicsScene_Internal_RaycastTest_Injected_m7633DAED691C6CFE296418FDBCE2E5E630456C62 (void);
extern void PhysicsScene_Internal_Raycast_Injected_m09A18038A5A35901A6825B805600525583FD404D (void);
extern void PhysicsScene_Internal_RaycastNonAlloc_Injected_mD6BA34F06BE743B2CBF46AA82EE6DDC9CCEC0F27 (void);
extern void PhysicsScene_Query_SphereCast_Injected_m660DCB273A7D7AC02A4CACC69BBC38DF397E0D9A (void);
extern void PhysicsScene_Internal_SphereCastNonAlloc_Injected_m8B19C4FB753820C4D4952D6BEB59B7044F7C7394 (void);
extern void PhysicsScene_OverlapSphereNonAlloc_Internal_Injected_m43D86F83F62FE2AF946A23B7C37AAB852106D737 (void);
extern void ContactPairHeader_get_Body_m15AE148B3D38AAE1564028022C60D593E4B590FA (void);
extern void ContactPairHeader_get_OtherBody_m02B0A310A4C0357ACC14C07FD31845FD57E20C08 (void);
extern void ContactPairHeader_get_HasRemovedBody_mB615CA10F918FFEABBF497E6C6232F9413F701A5 (void);
extern void ContactPairHeader_GetContactPair_m3DD517F464EDB35C4B0933854D95BE735DD2AC09 (void);
extern void ContactPairHeader_GetContactPair_Internal_mCED67397346C23F3ABC5063AFFCF1F099AF5FC27 (void);
extern void ContactPair_get_ColliderInstanceID_m9EB01473BB1DC19A73FE754B73ED5B86BE76384E (void);
extern void ContactPair_get_OtherColliderInstanceID_mB66A7D2C2BD0C5DFABCAB3CCFAF9A97D54DDA8D3 (void);
extern void ContactPair_get_Collider_m84ABA0A8AF353E3AC6592EF62515D6DF1E5B32AD (void);
extern void ContactPair_get_OtherCollider_m0D159FD7412F0AC0E1D35C9303BDF053CE544256 (void);
extern void ContactPair_get_IsCollisionEnter_m7B72CBBDBA0EF4F39E6DED1866EA14D30C0A1B39 (void);
extern void ContactPair_get_IsCollisionExit_m1BCEDE548BB79691F37FFEF74C898D7BBBEDB385 (void);
extern void ContactPair_get_IsCollisionStay_mB17B13FBAD21D5742964832ACD8F9FCB55FDC3D8 (void);
extern void ContactPair_get_HasRemovedCollider_mF659D89CCB509F18E5B434C5441DABEAD9C4B52E (void);
extern void ContactPair_ExtractContactsArray_mB82D786FF9A04BC4B5A4C10EA5DC400AB6D655EC (void);
extern void ContactPair_GetContactPoint_Internal_m121E9C831FE1A673C1D76E00B731D3F5D558E565 (void);
extern void ContactPair_ExtractContactsArray_Injected_mF91AF0D52F744FB7DEF1381BDDE212EF79A6932E (void);
extern void QueryParameters__ctor_mDDC93EFF78C4D037B77E44A36510F66B81BF821B (void);
extern void RaycastCommand__ctor_mE6082A5801EA093D304809288E73B86F0A84B971 (void);
extern void RaycastCommand_set_from_mF7FA016459E61AE4233A986B7B8753AA882FC2C2 (void);
extern void RaycastCommand_set_direction_m5C377513A02AD1D7773B25AD7F66A8E457F7B19D (void);
extern void RaycastCommand_set_physicsScene_mA4FAEC5C2526DA68F331762D79C9F3AC0432F946 (void);
extern void RaycastCommand_set_distance_m856F9B5B6DF423B0F869CFDE1BF1A535E3D39023 (void);
extern void RaycastCommand_ScheduleBatch_m250EB363528A1CAE1F9E3FD517C433905464C7DE (void);
extern void RaycastCommand_ScheduleBatch_m5CF1A3E3FE582BD990F7844F3D9C18B5A2295AF0 (void);
extern void RaycastCommand_ScheduleRaycastBatch_m2ECCFEE5C88640231C83DFD65411358E2B5C2CA2 (void);
extern void RaycastCommand_ScheduleRaycastBatch_Injected_m7E4418AF58937EBCB111749C2A5EC6DD2E3BF4F6 (void);
static Il2CppMethodPointer s_methodPointers[208] = 
{
	Collision_get_relativeVelocity_mAD9D45864C56FFAB284E77835BF75DF86D4E4CC0,
	Collision_get_body_mA03043499C5BAF241F96F3FAEE183F7C5371A246,
	Collision_get_collider_mBB5A086C78FE4BE0589E216F899B611673ADD25D,
	Collision_get_gameObject_m846FADBCA43E1849D3FE4D5EA44C02D055A70B3E,
	Collision_set_Flipped_m5AF63260E99357BB87655DDAD6316568BE65F3A1,
	Collision_get_contactCount_m063F555F6D8E5D1BC995C69306041280CE8BF150,
	Collision_get_contacts_m2E8E27E0399230DFA4303A4F4D81C1BD55CBC473,
	Collision__ctor_mC3F14BC1026130B6B0E6BB83D7431753C3484912,
	Collision__ctor_m6A02AD9B6F96A755B3A3A3A280CC7D2533228DA7,
	Collision_Reuse_mC2E21A6480EE1DCEAF71F2EAF3E0CAEFD42EA90C,
	Collision_GetContact_m34D66AD97A8DB36AFE0711276C990742B6FE4BCD,
	Physics_OnSceneContactModify_m52106C18952BF4768B05F67FAF2B7F6F6F7D0C9D,
	Physics_get_gravity_m94393492AE4ED8B38A22ECCDCD2DDDB71BFA010D,
	Physics_get_invokeCollisionCallbacks_m73AE9C988EC57467D9B8699B376759D8E44133C8,
	Physics_get_defaultPhysicsScene_mC5D2BC20734D32FB421163F066BD5FB4118C633A,
	Physics_Raycast_m453681A406AADE0A30227D955279F5E7050B790D,
	Physics_Raycast_m0679FB03C9AFC1E803B8F8AE6CAB409670D31377,
	Physics_Raycast_mCFF84927BE3EC1780DBA34CCED374E7FF12ABCBE,
	Physics_Raycast_mCAA46C95211C7BB95697A347B036C012D26EB028,
	Physics_Raycast_mA782767AD4F149FBEA32C71460DFF061B7563688,
	Physics_Raycast_m56120FFEF0D4F0A44CCA505B5C946E6FB8742F12,
	Physics_Raycast_m011EA7022C33B2C499EF744E5AF3E01EEB8FBD33,
	Physics_Raycast_m1B27F500505FFB57D78548B9F5A540A2AD092903,
	Physics_Raycast_m9879C28DFF6CD3048F2365BC01C855565EE141F8,
	Physics_Raycast_m5CAA0AEDB2A6FB26E5F42A8EA560A61CAAF12E50,
	Physics_Raycast_m7A0FEA813B93A82713C06D8466F0A21325743488,
	Physics_Raycast_mDB89EB287ED040E534F6A933683A070D29DC14D3,
	Physics_Raycast_mCCD2542138D11E665A5D4F413C1547EE7D794DEB,
	Physics_Raycast_m34AC1210E893A9EF969BD2C7104B10BE5B580025,
	Physics_Raycast_m839BA104A76B928A03F075C622923C6FCD2F8685,
	Physics_Raycast_mCAC9F02A1AAB49E16B384EBC8318E2DF30F4B0E5,
	Physics_Linecast_m399C6C11AD7ECE11241A37C08BAB4D97CF3CB925,
	Physics_Linecast_m4F2A0744FE106002EE26D12B6137FC21C019B932,
	Physics_SphereCast_mDB2140FE8561D0CE870037527DACC44AB18A346D,
	Physics_SphereCast_mD53E280903384004D4304150552D50DD3F8C1F58,
	Physics_Internal_RaycastAll_mC128593FD48E6F237BE59CFCDC7DDE7A4E8CB074,
	Physics_RaycastAll_m8B7FB8419A65BEE78927D0EE84916E8DBE7ECD34,
	Physics_RaycastAll_m69ED0FF0B70ADBC45B907783C87B308E786F6D51,
	Physics_RaycastAll_mDCBE530EF2ACD21EAADEA829259291D7327BC80E,
	Physics_RaycastAll_mE56962F670046BE618FFE8D9B19595A896922789,
	Physics_RaycastAll_mD1643DB52C4E415083E215B154FEB9DFA3AD6D74,
	Physics_RaycastAll_m4055619E0F7EFA04620EAA0517F8393C4EBCFE87,
	Physics_RaycastAll_m1BBD4E474814BEC9B52B015081A256AE2FE00468,
	Physics_RaycastAll_mE94864EF8243F7D3A26C8666CEB02166C3742CB2,
	Physics_RaycastNonAlloc_mB37DE98E8C9407C3DB2FB488BAB1CF3A7C6FFFCE,
	Physics_RaycastNonAlloc_m2BFEE9072E390ED6ACD500FD0AE4E714DE9549BC,
	Physics_RaycastNonAlloc_m1908CB5E0D0570E9C88B6C259041520DD4D3169C,
	Physics_RaycastNonAlloc_m1961CFCDB7631C7FF4D12F88904CF1BEB24A6C3E,
	Physics_RaycastNonAlloc_mB8FE279E06CE87D77387AA9A10562B8052DC8836,
	Physics_RaycastNonAlloc_m4CFAA8CA088502DA71D748D276BDAAEF234B12B0,
	Physics_RaycastNonAlloc_m3EEB10539C49FEAD9533142FEE6578148A48FFA9,
	Physics_RaycastNonAlloc_mBDC9E19F4E3C82DCE03D799FDD41FB3314209460,
	Physics_OverlapSphere_Internal_m654C73F0B586E5DCF2066466C4AB7B3221AE6E9B,
	Physics_OverlapSphere_m348CF43E53C703DEF4A6780A3B9DE2A1FB958318,
	Physics_OverlapSphere_mCFA1C44458F8548C911C16F82077DA4C35D43F69,
	Physics_get_reuseCollisionCallbacks_mB23A11C02B893238B5631A38F2FBB4C63A3B7541,
	Physics_Query_ClosestPoint_mC72F36C1868C7830580ACF450FF84F797DC1C1EA,
	Physics_ClosestPoint_m440650B860B4298CEEAC45599E1B326A3B8FFF41,
	Physics_OverlapSphereNonAlloc_mED890C8454FCC0354A94F97453707FA01B27AE83,
	Physics_OverlapSphereNonAlloc_m48E30DC51482BBBBDD762ECE1DF83A70088F70DA,
	Physics_SphereCastNonAlloc_m21B951284ED5217AB1395B08B963C4C9661F928C,
	Physics_SphereCastNonAlloc_m942717E0D3B43A44253447354FC32BA148EBC105,
	Physics_GetColliderByInstanceID_m0318A1C3CEC5AC6B42AB1F541EC3EE8909712220,
	Physics_GetBodyByInstanceID_mC45F93E518D6F1FC136DD3FB4377B3CC9F244725,
	Physics_SendOnCollisionEnter_mA48BA0630EE3D28320C602A15B5BDD887FA24144,
	Physics_SendOnCollisionStay_m2CC0293E4757CF89183A125270047A5054515590,
	Physics_SendOnCollisionExit_m6E96913C80E49A77FD367FBA5EF63755A81AA7D7,
	Physics_OnSceneContact_mFE8673EF13DD22B5C727BF5E2EFC4CF5E410C73A,
	Physics_ReportContacts_m9E7B0F448F534C8DEBBA6E33D86506C2500C919D,
	Physics_GetCollisionToReport_m7D22E5DD29678C65830A05A249650C49947A5D4E,
	Physics__cctor_m1E2D4816C77050D34F6D01F43E63D7969410AE2A,
	Physics_get_gravity_Injected_mBB1051A73AE88880090895AB007FEF35965E192E,
	Physics_get_defaultPhysicsScene_Injected_mE86AE6A398435C1754A824B2B35DF13126A6C5D6,
	Physics_Internal_RaycastAll_Injected_mAFAA47E2224DEA0ABF1A2188A969E7A663E50C92,
	Physics_OverlapSphere_Internal_Injected_mB70E77E63A51711DDE22E7319B012CBD57DA6C0B,
	Physics_Query_ClosestPoint_Injected_mDB84C796000699B2CF45B2F054EDBE652371C975,
	ContactEventDelegate__ctor_mF0844AC2AA36D48C199DDDBFA55627E43980CEEE,
	ContactEventDelegate_Invoke_m84BF3B9092BD4F6D43A870421F8389BC7B0E0769,
	RaycastHit_get_collider_m84B160439BBEAB6D9E94B799F720E25C9E2D444D,
	RaycastHit_get_point_m02B764612562AFE0F998CC7CFB2EEDE41BA47F39,
	RaycastHit_get_normal_mD8741B70D2039C5CAFC4368D4CE59D89562040B5,
	RaycastHit_get_distance_m035194B0E9BB6229259CFC43B095A9C8E5011C78,
	RaycastHit_get_transform_m89DB7FCFC50E0213A37CBE089400064B8FA19155,
	RaycastHit_get_rigidbody_mE6FCB1B1A9F0C8D4185A484C10B9A5403CCD6005,
	Rigidbody_get_velocity_mAE331303E7214402C93E2183D0AA1198F425F843,
	Rigidbody_set_velocity_mE4031DF1C2C1CCE889F2AC9D8871D83795BB0D62,
	Rigidbody_set_angularVelocity_m23266B4E52BF0D2E65CC984AC73CC40B8D4A27E0,
	Rigidbody_get_mass_m09DDDDC437499B83B3BD0D77C134BFDC3E667054,
	Rigidbody_get_useGravity_mBDA227BDCB0F9A81B61A6592929EE43EDDEE7D16,
	Rigidbody_set_isKinematic_m6C3FD3EA358DADA3B191F2449CF1C4F8B22695ED,
	Rigidbody_set_freezeRotation_m6D049F82E9133020C31EEFB35A179A56364325DC,
	Rigidbody_set_constraints_mE81BF0DAEB980E320538231E092CA4663885A9A3,
	Rigidbody_get_centerOfMass_mA66BE4DE0469545EBCF49A66EE4FDD3A5D0ADF91,
	Rigidbody_set_centerOfMass_m9D4A68D102498C7DBCD91278FF5EE7EE0BF2B188,
	Rigidbody_get_position_m4ECB79BDBBF8FD1EA572EDB792D3330DDED24691,
	Rigidbody_set_position_mA15BE12B8D82220E8CA90A0F0CBFB206FE81B41C,
	Rigidbody_get_rotation_m07882A7024FB3F96BA13EC577A96163BBB621AA1,
	Rigidbody_set_rotation_mF2FC85A4A26AD9FED7DE0061889DF5A408461A5D,
	Rigidbody_MovePosition_mB2CD29ABC8F59AC338C0A3A5A6B75C38FDA92CA9,
	Rigidbody_MoveRotation_m85825C7206E770E39DED9EE6D792702F577A891D,
	Rigidbody_GetPointVelocity_m94324B9CDC28751DB27594ADE76FEAB5EC4EB1BD,
	Rigidbody_AddForce_mBDBC288D0E266BC1B62E3649B4FCE46E7EA9CCBC,
	Rigidbody_AddForce_m7A3EEEED21F986917107CBA6CC0106DCBC212198,
	Rigidbody_AddTorque_m7922F76C73DACF9E1610D72726C01709C14F0937,
	Rigidbody_AddTorque_m39C767D6CD12B2D12D575E2B469CB5565BFA30B6,
	Rigidbody_AddForceAtPosition_m61575E676B16690BEC0FD29841EAD35CC40B642C,
	Rigidbody_AddForceAtPosition_mA4226D0A30E0B55CB0CAD2A956EA16C546505965,
	Rigidbody_AddExplosionForce_mE4673F6D1DA0C206DA79659E9005A0F067348402,
	Rigidbody_AddExplosionForce_mD36F7D864F32F22DA1783D20F6E9563A9C51DFA1,
	Rigidbody__ctor_mB4E21922228AED3B52D8696D54F5B514F922CB07,
	Rigidbody_get_velocity_Injected_mFD6FCA2857D9953AA953DB9AAF26A88CA881171C,
	Rigidbody_set_velocity_Injected_m41B399E90D6AA49BABD3C178B3183AD3BBB4EAC4,
	Rigidbody_set_angularVelocity_Injected_mD7EA47CB618918BD45985951E5A5388853975E68,
	Rigidbody_get_centerOfMass_Injected_m1E6C506D04F8D066FAD03C571FA926E8FACB1518,
	Rigidbody_set_centerOfMass_Injected_mF33B1D20FF435377B48235CDD322478131AE4B1B,
	Rigidbody_get_position_Injected_m12A715C52CD3C7F66125950D7AB6ECFCF4336626,
	Rigidbody_set_position_Injected_m8EB4DEECAE5A3D841A71B015807FF0820C3A361B,
	Rigidbody_get_rotation_Injected_m38431B37B78B4DF59B59F9CB7E609820430051F8,
	Rigidbody_set_rotation_Injected_mDE5B37E97D8FE16D9AD8FC6B9213A106B28589D7,
	Rigidbody_MovePosition_Injected_mF2CDF14960A920DCDDEAFA49A7E066A2FF021E37,
	Rigidbody_MoveRotation_Injected_m75B6A86B8BE8D68714CA5356DDCC11D24B96B505,
	Rigidbody_GetPointVelocity_Injected_m343FC8424FDE36AE563D07BC3B7506CF94E3E4A4,
	Rigidbody_AddForce_Injected_m094E54DEA6CEAEA340F053D077CDF0753900F48E,
	Rigidbody_AddTorque_Injected_m22FC0760ED37AE38443DB926B2A7D87470A63CF2,
	Rigidbody_AddForceAtPosition_Injected_m24F286471C9928629A50A5C1A0DF22698172438C,
	Rigidbody_AddExplosionForce_Injected_mB02BEE3B033E215880374706864990CEA12B17D7,
	Collider_get_enabled_mDBFB488088ADB14C8016A83EF445653AC5A4A12B,
	Collider_set_enabled_m8D5C3B5047592D227A52560FC9723D176E209F70,
	Collider_get_attachedRigidbody_m060304DB909A1FACD260EBB619D64D39129739AD,
	Collider_get_isTrigger_mFF457F6AA71D173F9A11BAF00C35E5AE12952F87,
	Collider_set_isTrigger_mFCD22F3EB5E28C97863956AB725D53F7F4B7CA78,
	Collider_ClosestPoint_mFFF9B6F6CF9F18B22B325835A3E2E78A1C03BFCB,
	Collider_get_bounds_mCC32F749590E9A85C7930E5355661367F78E4CB4,
	Collider_Raycast_mBFA55E4B9BD7EE4E8D4107ADF24D2FA0F165FA2C,
	Collider_Raycast_mD7683E94051173B3FFC0862F4A17847E94AEB938,
	Collider__ctor_m8975C6CCFC0E5740C523DB4A52ACC7F4A021F8FA,
	Collider_ClosestPoint_Injected_m4E218A16FABAA4615270B9CD82DC66E130AAFE77,
	Collider_get_bounds_Injected_m1BDB8DBC0BC2BFC51D4A185C494EDB0997B93A43,
	Collider_Raycast_Injected_mAFAD355B658765116985B737217587C68BF257A3,
	CharacterController_Move_mE3F7AC1B4A2D6955980811C088B68ED3A31D2DA4,
	CharacterController_get_isGrounded_m548072EC190878925C0F97595B6C307714EFDD67,
	CharacterController__ctor_m6A906ADB773BC0AE534C9348A56747D931B194B3,
	CharacterController_Move_Injected_m7F25C33CF948858A8D5822EF73FAE7A16AE65C86,
	MeshCollider_get_sharedMesh_mFB4B8534501C29930D2D3710D6D82E60093FA21E,
	MeshCollider_get_convex_m0C0F6D0798413D633814D307EC970F7752B3C9D1,
	CapsuleCollider_get_radius_m2462B43ECAC92386AAED85AA1DFD66440972D9D5,
	CapsuleCollider_get_height_m63A31072F296AEE6222DC9C88704882BB6A54A24,
	CapsuleCollider_get_direction_mE6D56B0990E3F2FACA983679C251949FE3FC6DFA,
	BoxCollider_get_size_mC1A2DD270B04DFF5961F9F90DC147C271F72258E,
	BoxCollider__ctor_m7E069AACEC2B76129335D222FC8B8A63FD50656F,
	BoxCollider_get_size_Injected_m4F20D7B3D8FB4360C9E2986FB6A8CC66ABC89511,
	SphereCollider_get_center_m122A197607CD350873539A0EEE3BA10E8BE1759E,
	SphereCollider_get_radius_m1BB513491906E76A4F71929E3DB72A1542309697,
	SphereCollider__ctor_mA8570CBE8C0E74C607669DC4E0CCA6CB1E4CB200,
	SphereCollider_get_center_Injected_m26E71B48B49E3EF89A4DC523015F243A385CF0E9,
	ContactPoint_get_point_mCCDFDACC5D8DB469898060A56A3CC45132911208,
	ContactPoint_get_normal_mD7F0567CA2FD68644F7C6FE318E10C4D15F92AD6,
	ContactPoint_get_thisCollider_m5CECC2F86CD3D73FE35543127C22C02D8ED1AFD6,
	ContactPoint__ctor_mC0A53F0787CB05D31B97E761426675C3C2DC194B,
	PhysicsScene_ToString_mA4E28A3068A823D16D96BBA45115A2C457FC57C7,
	PhysicsScene_GetHashCode_m368888FB861F994FADEEDD281BD02B090C561814,
	PhysicsScene_Equals_mE3A11329AB6C2F4F76D2321D8BAE52671A2EDDA3,
	PhysicsScene_Equals_m81E4A78FC3644FDC44044B3A5F19F1C4283648A1,
	PhysicsScene_Raycast_m68D255133E274C5DDF33102EAAE70990C2A0A730,
	PhysicsScene_Internal_RaycastTest_m729F4A577F5DD911131C5321EC28E44F98A60BA0,
	PhysicsScene_Raycast_m6EE0783D1B113CAD5450A2CB876F6CA305BAD2CE,
	PhysicsScene_Internal_Raycast_m0211A7BDE011181718838F063296D51F88D92E74,
	PhysicsScene_Raycast_m3BD571CF6901C59C286D7B58ED9D15D836BC54C3,
	PhysicsScene_Internal_RaycastNonAlloc_mC339255AAFC484588C813D7BE2BDAE03797D26DB,
	PhysicsScene_Query_SphereCast_m8E6770FE64FB74157199217381AA1A99B3CF580B,
	PhysicsScene_Internal_SphereCast_mE4B0FBE790E2A7309F7807F5F1EFB909D21E07BF,
	PhysicsScene_SphereCast_mEB124233FFEA3BD179C9DE22E410290D7EB247C4,
	PhysicsScene_Internal_SphereCastNonAlloc_mFAB1960B109B872B9712E5CED28E43A944E9649F,
	PhysicsScene_SphereCast_m2C89211A7462980013209F0B22B3D96B0963AF9F,
	PhysicsScene_OverlapSphereNonAlloc_Internal_m0F7B77B20925E6D449F858C08AD833E37FD406E1,
	PhysicsScene_OverlapSphere_m0E853FB04ECE662CFA9FF522D8A4E9CE04903D01,
	PhysicsScene_Internal_RaycastTest_Injected_m7633DAED691C6CFE296418FDBCE2E5E630456C62,
	PhysicsScene_Internal_Raycast_Injected_m09A18038A5A35901A6825B805600525583FD404D,
	PhysicsScene_Internal_RaycastNonAlloc_Injected_mD6BA34F06BE743B2CBF46AA82EE6DDC9CCEC0F27,
	PhysicsScene_Query_SphereCast_Injected_m660DCB273A7D7AC02A4CACC69BBC38DF397E0D9A,
	PhysicsScene_Internal_SphereCastNonAlloc_Injected_m8B19C4FB753820C4D4952D6BEB59B7044F7C7394,
	PhysicsScene_OverlapSphereNonAlloc_Internal_Injected_m43D86F83F62FE2AF946A23B7C37AAB852106D737,
	ContactPairHeader_get_Body_m15AE148B3D38AAE1564028022C60D593E4B590FA,
	ContactPairHeader_get_OtherBody_m02B0A310A4C0357ACC14C07FD31845FD57E20C08,
	ContactPairHeader_get_HasRemovedBody_mB615CA10F918FFEABBF497E6C6232F9413F701A5,
	ContactPairHeader_GetContactPair_m3DD517F464EDB35C4B0933854D95BE735DD2AC09,
	ContactPairHeader_GetContactPair_Internal_mCED67397346C23F3ABC5063AFFCF1F099AF5FC27,
	ContactPair_get_ColliderInstanceID_m9EB01473BB1DC19A73FE754B73ED5B86BE76384E,
	ContactPair_get_OtherColliderInstanceID_mB66A7D2C2BD0C5DFABCAB3CCFAF9A97D54DDA8D3,
	ContactPair_get_Collider_m84ABA0A8AF353E3AC6592EF62515D6DF1E5B32AD,
	ContactPair_get_OtherCollider_m0D159FD7412F0AC0E1D35C9303BDF053CE544256,
	ContactPair_get_IsCollisionEnter_m7B72CBBDBA0EF4F39E6DED1866EA14D30C0A1B39,
	ContactPair_get_IsCollisionExit_m1BCEDE548BB79691F37FFEF74C898D7BBBEDB385,
	ContactPair_get_IsCollisionStay_mB17B13FBAD21D5742964832ACD8F9FCB55FDC3D8,
	ContactPair_get_HasRemovedCollider_mF659D89CCB509F18E5B434C5441DABEAD9C4B52E,
	ContactPair_ExtractContactsArray_mB82D786FF9A04BC4B5A4C10EA5DC400AB6D655EC,
	ContactPair_GetContactPoint_Internal_m121E9C831FE1A673C1D76E00B731D3F5D558E565,
	ContactPair_ExtractContactsArray_Injected_mF91AF0D52F744FB7DEF1381BDDE212EF79A6932E,
	QueryParameters__ctor_mDDC93EFF78C4D037B77E44A36510F66B81BF821B,
	RaycastCommand__ctor_mE6082A5801EA093D304809288E73B86F0A84B971,
	RaycastCommand_set_from_mF7FA016459E61AE4233A986B7B8753AA882FC2C2,
	RaycastCommand_set_direction_m5C377513A02AD1D7773B25AD7F66A8E457F7B19D,
	RaycastCommand_set_physicsScene_mA4FAEC5C2526DA68F331762D79C9F3AC0432F946,
	RaycastCommand_set_distance_m856F9B5B6DF423B0F869CFDE1BF1A535E3D39023,
	RaycastCommand_ScheduleBatch_m250EB363528A1CAE1F9E3FD517C433905464C7DE,
	RaycastCommand_ScheduleBatch_m5CF1A3E3FE582BD990F7844F3D9C18B5A2295AF0,
	RaycastCommand_ScheduleRaycastBatch_m2ECCFEE5C88640231C83DFD65411358E2B5C2CA2,
	RaycastCommand_ScheduleRaycastBatch_Injected_m7E4418AF58937EBCB111749C2A5EC6DD2E3BF4F6,
};
extern void RaycastHit_get_collider_m84B160439BBEAB6D9E94B799F720E25C9E2D444D_AdjustorThunk (void);
extern void RaycastHit_get_point_m02B764612562AFE0F998CC7CFB2EEDE41BA47F39_AdjustorThunk (void);
extern void RaycastHit_get_normal_mD8741B70D2039C5CAFC4368D4CE59D89562040B5_AdjustorThunk (void);
extern void RaycastHit_get_distance_m035194B0E9BB6229259CFC43B095A9C8E5011C78_AdjustorThunk (void);
extern void RaycastHit_get_transform_m89DB7FCFC50E0213A37CBE089400064B8FA19155_AdjustorThunk (void);
extern void RaycastHit_get_rigidbody_mE6FCB1B1A9F0C8D4185A484C10B9A5403CCD6005_AdjustorThunk (void);
extern void ContactPoint_get_point_mCCDFDACC5D8DB469898060A56A3CC45132911208_AdjustorThunk (void);
extern void ContactPoint_get_normal_mD7F0567CA2FD68644F7C6FE318E10C4D15F92AD6_AdjustorThunk (void);
extern void ContactPoint_get_thisCollider_m5CECC2F86CD3D73FE35543127C22C02D8ED1AFD6_AdjustorThunk (void);
extern void ContactPoint__ctor_mC0A53F0787CB05D31B97E761426675C3C2DC194B_AdjustorThunk (void);
extern void PhysicsScene_ToString_mA4E28A3068A823D16D96BBA45115A2C457FC57C7_AdjustorThunk (void);
extern void PhysicsScene_GetHashCode_m368888FB861F994FADEEDD281BD02B090C561814_AdjustorThunk (void);
extern void PhysicsScene_Equals_mE3A11329AB6C2F4F76D2321D8BAE52671A2EDDA3_AdjustorThunk (void);
extern void PhysicsScene_Equals_m81E4A78FC3644FDC44044B3A5F19F1C4283648A1_AdjustorThunk (void);
extern void PhysicsScene_Raycast_m68D255133E274C5DDF33102EAAE70990C2A0A730_AdjustorThunk (void);
extern void PhysicsScene_Raycast_m6EE0783D1B113CAD5450A2CB876F6CA305BAD2CE_AdjustorThunk (void);
extern void PhysicsScene_Raycast_m3BD571CF6901C59C286D7B58ED9D15D836BC54C3_AdjustorThunk (void);
extern void PhysicsScene_SphereCast_mEB124233FFEA3BD179C9DE22E410290D7EB247C4_AdjustorThunk (void);
extern void PhysicsScene_SphereCast_m2C89211A7462980013209F0B22B3D96B0963AF9F_AdjustorThunk (void);
extern void PhysicsScene_OverlapSphere_m0E853FB04ECE662CFA9FF522D8A4E9CE04903D01_AdjustorThunk (void);
extern void ContactPairHeader_get_Body_m15AE148B3D38AAE1564028022C60D593E4B590FA_AdjustorThunk (void);
extern void ContactPairHeader_get_OtherBody_m02B0A310A4C0357ACC14C07FD31845FD57E20C08_AdjustorThunk (void);
extern void ContactPairHeader_get_HasRemovedBody_mB615CA10F918FFEABBF497E6C6232F9413F701A5_AdjustorThunk (void);
extern void ContactPairHeader_GetContactPair_m3DD517F464EDB35C4B0933854D95BE735DD2AC09_AdjustorThunk (void);
extern void ContactPairHeader_GetContactPair_Internal_mCED67397346C23F3ABC5063AFFCF1F099AF5FC27_AdjustorThunk (void);
extern void ContactPair_get_ColliderInstanceID_m9EB01473BB1DC19A73FE754B73ED5B86BE76384E_AdjustorThunk (void);
extern void ContactPair_get_OtherColliderInstanceID_mB66A7D2C2BD0C5DFABCAB3CCFAF9A97D54DDA8D3_AdjustorThunk (void);
extern void ContactPair_get_Collider_m84ABA0A8AF353E3AC6592EF62515D6DF1E5B32AD_AdjustorThunk (void);
extern void ContactPair_get_OtherCollider_m0D159FD7412F0AC0E1D35C9303BDF053CE544256_AdjustorThunk (void);
extern void ContactPair_get_IsCollisionEnter_m7B72CBBDBA0EF4F39E6DED1866EA14D30C0A1B39_AdjustorThunk (void);
extern void ContactPair_get_IsCollisionExit_m1BCEDE548BB79691F37FFEF74C898D7BBBEDB385_AdjustorThunk (void);
extern void ContactPair_get_IsCollisionStay_mB17B13FBAD21D5742964832ACD8F9FCB55FDC3D8_AdjustorThunk (void);
extern void ContactPair_get_HasRemovedCollider_mF659D89CCB509F18E5B434C5441DABEAD9C4B52E_AdjustorThunk (void);
extern void ContactPair_ExtractContactsArray_mB82D786FF9A04BC4B5A4C10EA5DC400AB6D655EC_AdjustorThunk (void);
extern void ContactPair_GetContactPoint_Internal_m121E9C831FE1A673C1D76E00B731D3F5D558E565_AdjustorThunk (void);
extern void QueryParameters__ctor_mDDC93EFF78C4D037B77E44A36510F66B81BF821B_AdjustorThunk (void);
extern void RaycastCommand__ctor_mE6082A5801EA093D304809288E73B86F0A84B971_AdjustorThunk (void);
extern void RaycastCommand_set_from_mF7FA016459E61AE4233A986B7B8753AA882FC2C2_AdjustorThunk (void);
extern void RaycastCommand_set_direction_m5C377513A02AD1D7773B25AD7F66A8E457F7B19D_AdjustorThunk (void);
extern void RaycastCommand_set_physicsScene_mA4FAEC5C2526DA68F331762D79C9F3AC0432F946_AdjustorThunk (void);
extern void RaycastCommand_set_distance_m856F9B5B6DF423B0F869CFDE1BF1A535E3D39023_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[41] = 
{
	{ 0x0600004F, RaycastHit_get_collider_m84B160439BBEAB6D9E94B799F720E25C9E2D444D_AdjustorThunk },
	{ 0x06000050, RaycastHit_get_point_m02B764612562AFE0F998CC7CFB2EEDE41BA47F39_AdjustorThunk },
	{ 0x06000051, RaycastHit_get_normal_mD8741B70D2039C5CAFC4368D4CE59D89562040B5_AdjustorThunk },
	{ 0x06000052, RaycastHit_get_distance_m035194B0E9BB6229259CFC43B095A9C8E5011C78_AdjustorThunk },
	{ 0x06000053, RaycastHit_get_transform_m89DB7FCFC50E0213A37CBE089400064B8FA19155_AdjustorThunk },
	{ 0x06000054, RaycastHit_get_rigidbody_mE6FCB1B1A9F0C8D4185A484C10B9A5403CCD6005_AdjustorThunk },
	{ 0x0600009C, ContactPoint_get_point_mCCDFDACC5D8DB469898060A56A3CC45132911208_AdjustorThunk },
	{ 0x0600009D, ContactPoint_get_normal_mD7F0567CA2FD68644F7C6FE318E10C4D15F92AD6_AdjustorThunk },
	{ 0x0600009E, ContactPoint_get_thisCollider_m5CECC2F86CD3D73FE35543127C22C02D8ED1AFD6_AdjustorThunk },
	{ 0x0600009F, ContactPoint__ctor_mC0A53F0787CB05D31B97E761426675C3C2DC194B_AdjustorThunk },
	{ 0x060000A0, PhysicsScene_ToString_mA4E28A3068A823D16D96BBA45115A2C457FC57C7_AdjustorThunk },
	{ 0x060000A1, PhysicsScene_GetHashCode_m368888FB861F994FADEEDD281BD02B090C561814_AdjustorThunk },
	{ 0x060000A2, PhysicsScene_Equals_mE3A11329AB6C2F4F76D2321D8BAE52671A2EDDA3_AdjustorThunk },
	{ 0x060000A3, PhysicsScene_Equals_m81E4A78FC3644FDC44044B3A5F19F1C4283648A1_AdjustorThunk },
	{ 0x060000A4, PhysicsScene_Raycast_m68D255133E274C5DDF33102EAAE70990C2A0A730_AdjustorThunk },
	{ 0x060000A6, PhysicsScene_Raycast_m6EE0783D1B113CAD5450A2CB876F6CA305BAD2CE_AdjustorThunk },
	{ 0x060000A8, PhysicsScene_Raycast_m3BD571CF6901C59C286D7B58ED9D15D836BC54C3_AdjustorThunk },
	{ 0x060000AC, PhysicsScene_SphereCast_mEB124233FFEA3BD179C9DE22E410290D7EB247C4_AdjustorThunk },
	{ 0x060000AE, PhysicsScene_SphereCast_m2C89211A7462980013209F0B22B3D96B0963AF9F_AdjustorThunk },
	{ 0x060000B0, PhysicsScene_OverlapSphere_m0E853FB04ECE662CFA9FF522D8A4E9CE04903D01_AdjustorThunk },
	{ 0x060000B7, ContactPairHeader_get_Body_m15AE148B3D38AAE1564028022C60D593E4B590FA_AdjustorThunk },
	{ 0x060000B8, ContactPairHeader_get_OtherBody_m02B0A310A4C0357ACC14C07FD31845FD57E20C08_AdjustorThunk },
	{ 0x060000B9, ContactPairHeader_get_HasRemovedBody_mB615CA10F918FFEABBF497E6C6232F9413F701A5_AdjustorThunk },
	{ 0x060000BA, ContactPairHeader_GetContactPair_m3DD517F464EDB35C4B0933854D95BE735DD2AC09_AdjustorThunk },
	{ 0x060000BB, ContactPairHeader_GetContactPair_Internal_mCED67397346C23F3ABC5063AFFCF1F099AF5FC27_AdjustorThunk },
	{ 0x060000BC, ContactPair_get_ColliderInstanceID_m9EB01473BB1DC19A73FE754B73ED5B86BE76384E_AdjustorThunk },
	{ 0x060000BD, ContactPair_get_OtherColliderInstanceID_mB66A7D2C2BD0C5DFABCAB3CCFAF9A97D54DDA8D3_AdjustorThunk },
	{ 0x060000BE, ContactPair_get_Collider_m84ABA0A8AF353E3AC6592EF62515D6DF1E5B32AD_AdjustorThunk },
	{ 0x060000BF, ContactPair_get_OtherCollider_m0D159FD7412F0AC0E1D35C9303BDF053CE544256_AdjustorThunk },
	{ 0x060000C0, ContactPair_get_IsCollisionEnter_m7B72CBBDBA0EF4F39E6DED1866EA14D30C0A1B39_AdjustorThunk },
	{ 0x060000C1, ContactPair_get_IsCollisionExit_m1BCEDE548BB79691F37FFEF74C898D7BBBEDB385_AdjustorThunk },
	{ 0x060000C2, ContactPair_get_IsCollisionStay_mB17B13FBAD21D5742964832ACD8F9FCB55FDC3D8_AdjustorThunk },
	{ 0x060000C3, ContactPair_get_HasRemovedCollider_mF659D89CCB509F18E5B434C5441DABEAD9C4B52E_AdjustorThunk },
	{ 0x060000C4, ContactPair_ExtractContactsArray_mB82D786FF9A04BC4B5A4C10EA5DC400AB6D655EC_AdjustorThunk },
	{ 0x060000C5, ContactPair_GetContactPoint_Internal_m121E9C831FE1A673C1D76E00B731D3F5D558E565_AdjustorThunk },
	{ 0x060000C7, QueryParameters__ctor_mDDC93EFF78C4D037B77E44A36510F66B81BF821B_AdjustorThunk },
	{ 0x060000C8, RaycastCommand__ctor_mE6082A5801EA093D304809288E73B86F0A84B971_AdjustorThunk },
	{ 0x060000C9, RaycastCommand_set_from_mF7FA016459E61AE4233A986B7B8753AA882FC2C2_AdjustorThunk },
	{ 0x060000CA, RaycastCommand_set_direction_m5C377513A02AD1D7773B25AD7F66A8E457F7B19D_AdjustorThunk },
	{ 0x060000CB, RaycastCommand_set_physicsScene_mA4FAEC5C2526DA68F331762D79C9F3AC0432F946_AdjustorThunk },
	{ 0x060000CC, RaycastCommand_set_distance_m856F9B5B6DF423B0F869CFDE1BF1A535E3D39023_AdjustorThunk },
};
static const int32_t s_InvokerIndices[208] = 
{
	13280,
	13052,
	13052,
	13052,
	10442,
	12996,
	13052,
	13298,
	2516,
	4702,
	8143,
	15701,
	21352,
	21225,
	21278,
	14118,
	14947,
	16061,
	17562,
	13872,
	14117,
	14946,
	16059,
	14934,
	16048,
	17499,
	19895,
	14106,
	14933,
	16047,
	17498,
	14116,
	16059,
	13740,
	14115,
	14475,
	14479,
	15493,
	16469,
	18045,
	15485,
	16455,
	18020,
	20516,
	14395,
	15327,
	16261,
	17852,
	13929,
	14420,
	15360,
	16311,
	14476,
	15492,
	18044,
	21225,
	15530,
	15531,
	14418,
	16310,
	13759,
	14419,
	20511,
	20511,
	18814,
	18814,
	18814,
	16926,
	20827,
	16386,
	21355,
	20831,
	20831,
	14427,
	14427,
	14703,
	5684,
	5735,
	13052,
	13280,
	13280,
	13195,
	13052,
	13052,
	13280,
	10912,
	10912,
	13195,
	12815,
	10442,
	10442,
	10629,
	13280,
	10912,
	13280,
	10912,
	13071,
	10702,
	10912,
	10702,
	9604,
	5841,
	10912,
	5841,
	10912,
	2911,
	5845,
	940,
	2867,
	13298,
	10415,
	10415,
	10415,
	10415,
	10415,
	10415,
	10415,
	10415,
	10415,
	10415,
	10415,
	4702,
	4705,
	4705,
	2517,
	934,
	12815,
	10442,
	13052,
	12815,
	10442,
	9604,
	12812,
	2446,
	2210,
	13298,
	4702,
	10415,
	1748,
	8969,
	12815,
	13298,
	8701,
	13052,
	12815,
	13195,
	13195,
	12996,
	13280,
	13298,
	10415,
	13280,
	13195,
	13298,
	10415,
	13280,
	13280,
	13052,
	470,
	13052,
	12996,
	7736,
	7740,
	518,
	14103,
	330,
	13869,
	340,
	13915,
	13655,
	13654,
	201,
	13662,
	206,
	13916,
	748,
	14066,
	13849,
	13884,
	13645,
	13658,
	13885,
	13052,
	13052,
	12815,
	6810,
	6810,
	12996,
	12996,
	13052,
	13052,
	12815,
	12815,
	12815,
	12815,
	4157,
	6810,
	16171,
	1800,
	2007,
	10912,
	10912,
	10688,
	10823,
	14424,
	15407,
	13760,
	13689,
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_PhysicsModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_PhysicsModule_CodeGenModule = 
{
	"UnityEngine.PhysicsModule.dll",
	208,
	s_methodPointers,
	41,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};

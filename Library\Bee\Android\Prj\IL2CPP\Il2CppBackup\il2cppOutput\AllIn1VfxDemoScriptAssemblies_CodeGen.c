﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_mE78A3557E01053B911C44AE85B460DC0B10E3E81 (void);
extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_mB1D1AA45BFA38733A8673EBFBAA8683B1601AF2E (void);
extern void All1DemoDropdownScroller_Start_m3AEA0F64B9CC9A0D9D225D9654A64C8DE72C6499 (void);
extern void All1DemoDropdownScroller_Update_mFCA3FDBE7EDD7B1154D80D1B7E42E54D96A53C49 (void);
extern void All1DemoDropdownScroller_NextDropdownElements_m1E50714A22689978979AD9250A85E8DC4C8E0329 (void);
extern void All1DemoDropdownScroller__ctor_m2C953D01FE52A0F5B5391BD6893B422F41F49879 (void);
extern void All1DemoMouseLocker_Start_m2953542DAFCBA2C99ADA01CC2C6AC02DC88B2FC9 (void);
extern void All1DemoMouseLocker_Update_m373EDE495F58832F0F8CFECCF6781EDBCEFB835C (void);
extern void All1DemoMouseLocker_DoMouseLockToggle_mDFD1E9F06646C7FDBFA67F9437F944AE91909E09 (void);
extern void All1DemoMouseLocker__ctor_mDBD522B8A448C17A081A04CDA7C3ABB00B88952C (void);
extern void All1DemoProjectileObstacle_Start_m93FC967736D46AEDE1593321C346A210398603B6 (void);
extern void All1DemoProjectileObstacle_DropdownValueChanged_mFBF48CCE57376065EADE55B9F3255561C6FACC90 (void);
extern void All1DemoProjectileObstacle_SetProjectileObstacleN_m7D19F65A703DCCF810FBB6FB9352EBBE9D9054F3 (void);
extern void All1DemoProjectileObstacle__ctor_m6580651067A10F23E21BB5F88860606CD7BE312B (void);
extern void All1DemoSceneColor_Start_m2886F8FCF122D2686A50DAE1FB3807BE5692B541 (void);
extern void All1DemoSceneColor_DropdownValueChanged_m2C75ECD302045FF882760252A3165EB024822C13 (void);
extern void All1DemoSceneColor_SetSceneColor_m0CA47927C241E75B0A075521FFB8D5F87B0BD7E4 (void);
extern void All1DemoSceneColor__ctor_mF7B97858EDDB17D72C1884FE4EE15BEA82BFE58C (void);
extern void All1VfxDemoEffect__ctor_mC1E58686925414B73A9E1F9C873D929AFACD2558 (void);
extern void All1VfxDemoEffectCollection__ctor_m7CE7091546FBEFF9C5D5BA179E1480982D07C40A (void);
extern void AllIn1AutoRotate_Update_mD11F12E83A72E6EE8FE3B2BF0C3A5D0ECEBE0AB6 (void);
extern void AllIn1AutoRotate__ctor_m519F8CADB35F2FBB2E9F72B39228D86E985B5C9F (void);
extern void AllIn1CanvasFader_Start_mAB42BE011A0BA53F3A06668E8EBE2BAD0299FEE8 (void);
extern void AllIn1CanvasFader_Update_mB32E243E002D97995F0B53BFB697D60FD040AD07 (void);
extern void AllIn1CanvasFader_HideUiButtonPressed_mE81198608A65F0B0C85C636160102A68E7B70E5F (void);
extern void AllIn1CanvasFader_MakeCanvasVisibleTween_mF19FBBD89C43813C394EBD432EFFEE8DDE825143 (void);
extern void AllIn1CanvasFader_MakeCanvasInvisibleTween_mC109D9FA7CE16C5584BD66BB2BB3D05110667D9C (void);
extern void AllIn1CanvasFader__ctor_m7AA5AEE9BDE70995CC60F1EC3495476D760170B4 (void);
extern void AllIn1ChangeAllChildTextFonts_Start_m36123800631AB3C1015BBEFD927A6A2E38BE56CE (void);
extern void AllIn1ChangeAllChildTextFonts_ChangeFonts_m815411A716334A1AE568870F9819242306C3DB67 (void);
extern void AllIn1ChangeAllChildTextFonts__ctor_m11492DDEDABE1DA9A4B43B17BBE1F303750B5D56 (void);
extern void AllIn1DemoProjectile_Initialize_mE93D7C4BE9805BD8196BC14DCD6A98FDCD7E536C (void);
extern void AllIn1DemoProjectile_AddScreenShakeOnImpact_m2E38DC55FE9DBEFBCFE50D3D2619A658BD1969B5 (void);
extern void AllIn1DemoProjectile_ApplyPrecisionOffsetToProjectileDir_mA1018FB2E8E2F1C1D75C1F95E1BD5EE27B52B5E2 (void);
extern void AllIn1DemoProjectile_OnTriggerEnter_m3035242F023DDD941DAA2B313034CFC6BB2CEE23 (void);
extern void AllIn1DemoProjectile__ctor_m8C715D018DC18C742F90A02A9CD0E4A2B64AE6A6 (void);
extern void AllIn1DemoScaleTween_Update_mAEA2E5770912B41E8CCABD5B2B58CD66AE4CF825 (void);
extern void AllIn1DemoScaleTween_UpdateScaleToApply_m0311F2480C4D2B7D493E6095DF81B277643BA68C (void);
extern void AllIn1DemoScaleTween_ApplyScale_m7AF485D335014314C02DE49F0252D2B17340D02E (void);
extern void AllIn1DemoScaleTween_ScaleUpTween_m7A248CB539848F376DB4CFF95BC94F3B4C3E41CB (void);
extern void AllIn1DemoScaleTween_ScaleDownTween_m160FC47A637AEEDD553012EEB5490E46557CD13B (void);
extern void AllIn1DemoScaleTween__ctor_m5A7DB72BF1EC71A3509C3F7C040A2FF96C556195 (void);
extern void AllIn1DoShake_Start_m2EDAED98519832DF28775BD1CD7A8DD2C0A62B88 (void);
extern void AllIn1DoShake_DoShake_mCC1F6B788ABB0486E029649DB71986D6FBD4FA3C (void);
extern void AllIn1DoShake__ctor_mEAF734E8BE0B7EAC043FB17A90C239795E2D5973 (void);
extern void AllIn1MouseRotate_Start_m0161A3F2E9D0DECB05F34FBF17524F8C534E992F (void);
extern void AllIn1MouseRotate_Update_m31DCA9EA5AF9440F0902AF5C255A61687D77211D (void);
extern void AllIn1MouseRotate_CamRotateAroundYAxis_m697A7DF105BCA5A6D48C0EEBF5F4C537690B98B4 (void);
extern void AllIn1MouseRotate_CamHeightTranslate_mECDE5C987BC68AA45C8EDA9FB44B09646308E28E (void);
extern void AllIn1MouseRotate_CamZoom_m3F0BC7D60154F66920D928D03DD823BD324C0C69 (void);
extern void AllIn1MouseRotate_ToggleCursorLocked_mFB02361AD8FD96F02D1172B34F32E94E6406F0F1 (void);
extern void AllIn1MouseRotate__ctor_mC0E5BE12022A71BD439199984CBEB7CA44C2E695 (void);
extern void AllIn1Shaker_Awake_mAA9625FCBC12A84FC29FC8503B7DA43982C1D61A (void);
extern void AllIn1Shaker_Update_mC86A4A4924E103DAF22E81E14FD8BBD5254731B1 (void);
extern void AllIn1Shaker_SmoothShakeToApply_m6D7E2F185D8727A5A2AA0B4FE502E5649603CA36 (void);
extern void AllIn1Shaker_ShakeRotation_mC7EF81B96148A9FA17D9B985143A37B1990F0AFD (void);
extern void AllIn1Shaker_ShakePosition_m22E42F2C6BBF315BAFCB97BC89F59955F201305A (void);
extern void AllIn1Shaker_DoCameraShake_mEA0E05A1E14EE7E970C3CDEF1496F8279127D121 (void);
extern void AllIn1Shaker__ctor_mAAA63B9ACABA2A3A92D6D86015D139447A58488C (void);
extern void AllIn1TimeControl_Start_mD6BFC61D78D359E43E55CEF9CB4EA7A6D1794CAC (void);
extern void AllIn1TimeControl_Update_m4E2E131577CFF98896A3487263FB756368219F17 (void);
extern void AllIn1TimeControl_ChangeTimeScale_m0BE7A4E2D50490F8CD8A8BDA8AC502E10E26DAAC (void);
extern void AllIn1TimeControl_UpdateTimeScaleUI_m4E3FB9587A57A8645A6107897F6FFB5A42AA9F0B (void);
extern void AllIn1TimeControl_PauseUiButton_mEE8DF596A943BD4D57C9F692701673CAB14F19F0 (void);
extern void AllIn1TimeControl_CurrentEffectChanged_m5D323E6E1178846163B333462A2E3C79D24B07C4 (void);
extern void AllIn1TimeControl__ctor_m67D4078E0E60537150B3382D2ABE361575953BB7 (void);
extern void AllIn1TimeControl_U3CStartU3Eb__13_0_mA6BE97428BA2CCD2D9F31472B53C74440A2A2B68 (void);
extern void AllIn1VfxAutoDestroy_Start_mBE6F5247B4DA9694E8360226A73071D98D953360 (void);
extern void AllIn1VfxAutoDestroy__ctor_mC5FDE209F3B11C2912FCF5EE29582864072C7AE4 (void);
extern void AllIn1VfxDemoController_Start_mB8B3D32873617B0D710AC889350FE2D1C00D8648 (void);
extern void AllIn1VfxDemoController_Update_mD85A99CA88122D7CB2D69D8ADC48A13F8E3F99ED (void);
extern void AllIn1VfxDemoController_CooldownHandling_m89084EC502F1EFA5F6F834CDE66769E5D64CAE5E (void);
extern void AllIn1VfxDemoController_PlayCurrentEffect_m24CB6CEA74A4B48CC98AF0A5CB53681706D95D8D (void);
extern void AllIn1VfxDemoController_ChangeCurrentEffect_m494D391AD8D79343DFF8FADCD391894324A61F50 (void);
extern void AllIn1VfxDemoController_SetupAndInstantiateCurrentEffect_m52AE39C1492F7AF923F0B724EB1FEAF5AE2118C0 (void);
extern void AllIn1VfxDemoController_ComputeValidEffectAndCollectionIndex_m01F07C601B6DAF66FF355FFE68E2A31E4C1DAC9C (void);
extern void AllIn1VfxDemoController_CurrentEffectLabelTweenEffectCR_mB8475D729EBC068C28EA4AC8BA65A1AF24E342CF (void);
extern void AllIn1VfxDemoController_DestroyAllChildren_mC172E11A2F807C75A7F81BB0C74B2D20A013DED3 (void);
extern void AllIn1VfxDemoController__ctor_mF78C27690839D42AB85B15EFD14CB81A49EDB7C8 (void);
extern void U3CCurrentEffectLabelTweenEffectCRU3Ed__38__ctor_mCCC473DFC618D8F567616AB17F82167AE7CA657E (void);
extern void U3CCurrentEffectLabelTweenEffectCRU3Ed__38_System_IDisposable_Dispose_m185332DA9227E65EED1BCB88F963EDDDEC82A4C5 (void);
extern void U3CCurrentEffectLabelTweenEffectCRU3Ed__38_MoveNext_mE937276C9F04C804735B1232C77F0F18CEC4FFE5 (void);
extern void U3CCurrentEffectLabelTweenEffectCRU3Ed__38_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mF3EF536605AFE036F86897BD9A367F5AC31F4665 (void);
extern void U3CCurrentEffectLabelTweenEffectCRU3Ed__38_System_Collections_IEnumerator_Reset_m7EB4182C76D651C0301AF6D458EE908C9216DFBC (void);
extern void U3CCurrentEffectLabelTweenEffectCRU3Ed__38_System_Collections_IEnumerator_get_Current_m5BB294BDE056B20F86EDC15F73148C0797FD22D3 (void);
extern void AllIn1VfxFadeLight_Start_mB099FB603674BA39FB6B8FF710402006319ABC55 (void);
extern void AllIn1VfxFadeLight_Update_mD027412F5A4FF1984E4796E7FD7B771242A06E97 (void);
extern void AllIn1VfxFadeLight__ctor_m750D81196CB5B49FFC0EC99D26BC9FCAF5F457E5 (void);
extern void AllIn1VfxParticleSystemTime_Start_m84BEFC53B33ECB32C0E3AB7733600495EEA14045 (void);
extern void AllIn1VfxParticleSystemTime_Update_m8AE28E49ABFE35DC4DB78D27AC6447B949D5E286 (void);
extern void AllIn1VfxParticleSystemTime_OnValidate_mF3A6B1153C0FEA9C9FD3C3FE3677BF3B3139F809 (void);
extern void AllIn1VfxParticleSystemTime_SetSimulationTime_mE10DE6B36542096BD573162D67BEB35F2F6ADCFF (void);
extern void AllIn1VfxParticleSystemTime__ctor_mF6C510D377E0C6F00C0DFFD16A4C6A9236CB6A13 (void);
static Il2CppMethodPointer s_methodPointers[93] = 
{
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_mE78A3557E01053B911C44AE85B460DC0B10E3E81,
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_mB1D1AA45BFA38733A8673EBFBAA8683B1601AF2E,
	All1DemoDropdownScroller_Start_m3AEA0F64B9CC9A0D9D225D9654A64C8DE72C6499,
	All1DemoDropdownScroller_Update_mFCA3FDBE7EDD7B1154D80D1B7E42E54D96A53C49,
	All1DemoDropdownScroller_NextDropdownElements_m1E50714A22689978979AD9250A85E8DC4C8E0329,
	All1DemoDropdownScroller__ctor_m2C953D01FE52A0F5B5391BD6893B422F41F49879,
	All1DemoMouseLocker_Start_m2953542DAFCBA2C99ADA01CC2C6AC02DC88B2FC9,
	All1DemoMouseLocker_Update_m373EDE495F58832F0F8CFECCF6781EDBCEFB835C,
	All1DemoMouseLocker_DoMouseLockToggle_mDFD1E9F06646C7FDBFA67F9437F944AE91909E09,
	All1DemoMouseLocker__ctor_mDBD522B8A448C17A081A04CDA7C3ABB00B88952C,
	All1DemoProjectileObstacle_Start_m93FC967736D46AEDE1593321C346A210398603B6,
	All1DemoProjectileObstacle_DropdownValueChanged_mFBF48CCE57376065EADE55B9F3255561C6FACC90,
	All1DemoProjectileObstacle_SetProjectileObstacleN_m7D19F65A703DCCF810FBB6FB9352EBBE9D9054F3,
	All1DemoProjectileObstacle__ctor_m6580651067A10F23E21BB5F88860606CD7BE312B,
	All1DemoSceneColor_Start_m2886F8FCF122D2686A50DAE1FB3807BE5692B541,
	All1DemoSceneColor_DropdownValueChanged_m2C75ECD302045FF882760252A3165EB024822C13,
	All1DemoSceneColor_SetSceneColor_m0CA47927C241E75B0A075521FFB8D5F87B0BD7E4,
	All1DemoSceneColor__ctor_mF7B97858EDDB17D72C1884FE4EE15BEA82BFE58C,
	All1VfxDemoEffect__ctor_mC1E58686925414B73A9E1F9C873D929AFACD2558,
	All1VfxDemoEffectCollection__ctor_m7CE7091546FBEFF9C5D5BA179E1480982D07C40A,
	AllIn1AutoRotate_Update_mD11F12E83A72E6EE8FE3B2BF0C3A5D0ECEBE0AB6,
	AllIn1AutoRotate__ctor_m519F8CADB35F2FBB2E9F72B39228D86E985B5C9F,
	AllIn1CanvasFader_Start_mAB42BE011A0BA53F3A06668E8EBE2BAD0299FEE8,
	AllIn1CanvasFader_Update_mB32E243E002D97995F0B53BFB697D60FD040AD07,
	AllIn1CanvasFader_HideUiButtonPressed_mE81198608A65F0B0C85C636160102A68E7B70E5F,
	AllIn1CanvasFader_MakeCanvasVisibleTween_mF19FBBD89C43813C394EBD432EFFEE8DDE825143,
	AllIn1CanvasFader_MakeCanvasInvisibleTween_mC109D9FA7CE16C5584BD66BB2BB3D05110667D9C,
	AllIn1CanvasFader__ctor_m7AA5AEE9BDE70995CC60F1EC3495476D760170B4,
	AllIn1ChangeAllChildTextFonts_Start_m36123800631AB3C1015BBEFD927A6A2E38BE56CE,
	AllIn1ChangeAllChildTextFonts_ChangeFonts_m815411A716334A1AE568870F9819242306C3DB67,
	AllIn1ChangeAllChildTextFonts__ctor_m11492DDEDABE1DA9A4B43B17BBE1F303750B5D56,
	AllIn1DemoProjectile_Initialize_mE93D7C4BE9805BD8196BC14DCD6A98FDCD7E536C,
	AllIn1DemoProjectile_AddScreenShakeOnImpact_m2E38DC55FE9DBEFBCFE50D3D2619A658BD1969B5,
	AllIn1DemoProjectile_ApplyPrecisionOffsetToProjectileDir_mA1018FB2E8E2F1C1D75C1F95E1BD5EE27B52B5E2,
	AllIn1DemoProjectile_OnTriggerEnter_m3035242F023DDD941DAA2B313034CFC6BB2CEE23,
	AllIn1DemoProjectile__ctor_m8C715D018DC18C742F90A02A9CD0E4A2B64AE6A6,
	AllIn1DemoScaleTween_Update_mAEA2E5770912B41E8CCABD5B2B58CD66AE4CF825,
	AllIn1DemoScaleTween_UpdateScaleToApply_m0311F2480C4D2B7D493E6095DF81B277643BA68C,
	AllIn1DemoScaleTween_ApplyScale_m7AF485D335014314C02DE49F0252D2B17340D02E,
	AllIn1DemoScaleTween_ScaleUpTween_m7A248CB539848F376DB4CFF95BC94F3B4C3E41CB,
	AllIn1DemoScaleTween_ScaleDownTween_m160FC47A637AEEDD553012EEB5490E46557CD13B,
	AllIn1DemoScaleTween__ctor_m5A7DB72BF1EC71A3509C3F7C040A2FF96C556195,
	AllIn1DoShake_Start_m2EDAED98519832DF28775BD1CD7A8DD2C0A62B88,
	AllIn1DoShake_DoShake_mCC1F6B788ABB0486E029649DB71986D6FBD4FA3C,
	AllIn1DoShake__ctor_mEAF734E8BE0B7EAC043FB17A90C239795E2D5973,
	AllIn1MouseRotate_Start_m0161A3F2E9D0DECB05F34FBF17524F8C534E992F,
	AllIn1MouseRotate_Update_m31DCA9EA5AF9440F0902AF5C255A61687D77211D,
	AllIn1MouseRotate_CamRotateAroundYAxis_m697A7DF105BCA5A6D48C0EEBF5F4C537690B98B4,
	AllIn1MouseRotate_CamHeightTranslate_mECDE5C987BC68AA45C8EDA9FB44B09646308E28E,
	AllIn1MouseRotate_CamZoom_m3F0BC7D60154F66920D928D03DD823BD324C0C69,
	AllIn1MouseRotate_ToggleCursorLocked_mFB02361AD8FD96F02D1172B34F32E94E6406F0F1,
	AllIn1MouseRotate__ctor_mC0E5BE12022A71BD439199984CBEB7CA44C2E695,
	AllIn1Shaker_Awake_mAA9625FCBC12A84FC29FC8503B7DA43982C1D61A,
	AllIn1Shaker_Update_mC86A4A4924E103DAF22E81E14FD8BBD5254731B1,
	AllIn1Shaker_SmoothShakeToApply_m6D7E2F185D8727A5A2AA0B4FE502E5649603CA36,
	AllIn1Shaker_ShakeRotation_mC7EF81B96148A9FA17D9B985143A37B1990F0AFD,
	AllIn1Shaker_ShakePosition_m22E42F2C6BBF315BAFCB97BC89F59955F201305A,
	AllIn1Shaker_DoCameraShake_mEA0E05A1E14EE7E970C3CDEF1496F8279127D121,
	AllIn1Shaker__ctor_mAAA63B9ACABA2A3A92D6D86015D139447A58488C,
	AllIn1TimeControl_Start_mD6BFC61D78D359E43E55CEF9CB4EA7A6D1794CAC,
	AllIn1TimeControl_Update_m4E2E131577CFF98896A3487263FB756368219F17,
	AllIn1TimeControl_ChangeTimeScale_m0BE7A4E2D50490F8CD8A8BDA8AC502E10E26DAAC,
	AllIn1TimeControl_UpdateTimeScaleUI_m4E3FB9587A57A8645A6107897F6FFB5A42AA9F0B,
	AllIn1TimeControl_PauseUiButton_mEE8DF596A943BD4D57C9F692701673CAB14F19F0,
	AllIn1TimeControl_CurrentEffectChanged_m5D323E6E1178846163B333462A2E3C79D24B07C4,
	AllIn1TimeControl__ctor_m67D4078E0E60537150B3382D2ABE361575953BB7,
	AllIn1TimeControl_U3CStartU3Eb__13_0_mA6BE97428BA2CCD2D9F31472B53C74440A2A2B68,
	AllIn1VfxAutoDestroy_Start_mBE6F5247B4DA9694E8360226A73071D98D953360,
	AllIn1VfxAutoDestroy__ctor_mC5FDE209F3B11C2912FCF5EE29582864072C7AE4,
	AllIn1VfxDemoController_Start_mB8B3D32873617B0D710AC889350FE2D1C00D8648,
	AllIn1VfxDemoController_Update_mD85A99CA88122D7CB2D69D8ADC48A13F8E3F99ED,
	AllIn1VfxDemoController_CooldownHandling_m89084EC502F1EFA5F6F834CDE66769E5D64CAE5E,
	AllIn1VfxDemoController_PlayCurrentEffect_m24CB6CEA74A4B48CC98AF0A5CB53681706D95D8D,
	AllIn1VfxDemoController_ChangeCurrentEffect_m494D391AD8D79343DFF8FADCD391894324A61F50,
	AllIn1VfxDemoController_SetupAndInstantiateCurrentEffect_m52AE39C1492F7AF923F0B724EB1FEAF5AE2118C0,
	AllIn1VfxDemoController_ComputeValidEffectAndCollectionIndex_m01F07C601B6DAF66FF355FFE68E2A31E4C1DAC9C,
	AllIn1VfxDemoController_CurrentEffectLabelTweenEffectCR_mB8475D729EBC068C28EA4AC8BA65A1AF24E342CF,
	AllIn1VfxDemoController_DestroyAllChildren_mC172E11A2F807C75A7F81BB0C74B2D20A013DED3,
	AllIn1VfxDemoController__ctor_mF78C27690839D42AB85B15EFD14CB81A49EDB7C8,
	U3CCurrentEffectLabelTweenEffectCRU3Ed__38__ctor_mCCC473DFC618D8F567616AB17F82167AE7CA657E,
	U3CCurrentEffectLabelTweenEffectCRU3Ed__38_System_IDisposable_Dispose_m185332DA9227E65EED1BCB88F963EDDDEC82A4C5,
	U3CCurrentEffectLabelTweenEffectCRU3Ed__38_MoveNext_mE937276C9F04C804735B1232C77F0F18CEC4FFE5,
	U3CCurrentEffectLabelTweenEffectCRU3Ed__38_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_mF3EF536605AFE036F86897BD9A367F5AC31F4665,
	U3CCurrentEffectLabelTweenEffectCRU3Ed__38_System_Collections_IEnumerator_Reset_m7EB4182C76D651C0301AF6D458EE908C9216DFBC,
	U3CCurrentEffectLabelTweenEffectCRU3Ed__38_System_Collections_IEnumerator_get_Current_m5BB294BDE056B20F86EDC15F73148C0797FD22D3,
	AllIn1VfxFadeLight_Start_mB099FB603674BA39FB6B8FF710402006319ABC55,
	AllIn1VfxFadeLight_Update_mD027412F5A4FF1984E4796E7FD7B771242A06E97,
	AllIn1VfxFadeLight__ctor_m750D81196CB5B49FFC0EC99D26BC9FCAF5F457E5,
	AllIn1VfxParticleSystemTime_Start_m84BEFC53B33ECB32C0E3AB7733600495EEA14045,
	AllIn1VfxParticleSystemTime_Update_m8AE28E49ABFE35DC4DB78D27AC6447B949D5E286,
	AllIn1VfxParticleSystemTime_OnValidate_mF3A6B1153C0FEA9C9FD3C3FE3677BF3B3139F809,
	AllIn1VfxParticleSystemTime_SetSimulationTime_mE10DE6B36542096BD573162D67BEB35F2F6ADCFF,
	AllIn1VfxParticleSystemTime__ctor_mF6C510D377E0C6F00C0DFFD16A4C6A9236CB6A13,
};
static const int32_t s_InvokerIndices[93] = 
{
	21379,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	10629,
	13298,
	13298,
	13298,
	10629,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	920,
	10823,
	10415,
	10682,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13195,
	10823,
	10823,
	10823,
	13298,
	13298,
	13298,
	10823,
	13298,
	13298,
	13298,
	13298,
	10823,
	13298,
	13298,
	13298,
	13298,
	13298,
	10442,
	10629,
	13298,
	13298,
	13052,
	13298,
	13298,
	10629,
	13298,
	12815,
	13052,
	13298,
	13052,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_AllIn1VfxDemoScriptAssemblies_CodeGenModule;
const Il2CppCodeGenModule g_AllIn1VfxDemoScriptAssemblies_CodeGenModule = 
{
	"AllIn1VfxDemoScriptAssemblies.dll",
	93,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};

{"logs": [{"outputFile": "com.moolego.rgame.launcher-merged_res-5:/values-v31_values-v31.arsc.flat", "map": [{"source": "E:\\Workspace\\RGameClient\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\build\\intermediates\\packaged_res\\release\\values-v31\\values-v31.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "8", "endColumns": "10", "endOffsets": "614"}}]}, {"outputFile": "com.moolego.rgame.launcher-merged_res-5:/values_values.arsc.flat", "map": [{"source": "E:\\Workspace\\RGameClient\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\launcher\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "2,3", "startColumns": "2,2", "startOffsets": "55,97", "endColumns": "40,65", "endOffsets": "93,160"}, "to": {"startLines": "9,10", "startColumns": "4,4", "startOffsets": "424,467", "endColumns": "42,67", "endOffsets": "462,530"}}, {"source": "E:\\Workspace\\RGameClient\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\build\\intermediates\\packaged_res\\release\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,11,14", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,123,169,226,282,331,377,424,524,662", "endLines": "2,3,4,5,6,7,8,10,13,17", "endColumns": "67,45,56,55,48,45,46,8,8,8", "endOffsets": "118,164,221,277,326,372,419,519,657,884"}, "to": {"startLines": "2,3,4,5,6,7,8,11,13,16", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,123,169,226,282,331,377,535,635,773", "endLines": "2,3,4,5,6,7,8,12,15,19", "endColumns": "67,45,56,55,48,45,46,8,8,8", "endOffsets": "118,164,221,277,326,372,419,630,768,995"}}]}, {"outputFile": "com.moolego.rgame.launcher-merged_res-5:/values-v30_values-v30.arsc.flat", "map": [{"source": "E:\\Workspace\\RGameClient\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\build\\intermediates\\packaged_res\\release\\values-v30\\values-v30.xml", "from": {"startLines": "2,3,4,5,6", "startColumns": "4,4,4,4,4", "startOffsets": "55,129,201,266,325", "endColumns": "73,71,64,58,60", "endOffsets": "124,196,261,320,381"}}]}, {"outputFile": "com.moolego.rgame.launcher-merged_res-5:/values-v28_values-v28.arsc.flat", "map": [{"source": "E:\\Workspace\\RGameClient\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\build\\intermediates\\packaged_res\\release\\values-v28\\values-v28.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "4", "endColumns": "8", "endOffsets": "229"}}]}, {"outputFile": "com.moolego.rgame.launcher-merged_res-5:/values-v21_values-v21.arsc.flat", "map": [{"source": "E:\\Workspace\\RGameClient\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\build\\intermediates\\packaged_res\\release\\values-v21\\values-v21.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "3", "endColumns": "8", "endOffsets": "154"}}]}]}
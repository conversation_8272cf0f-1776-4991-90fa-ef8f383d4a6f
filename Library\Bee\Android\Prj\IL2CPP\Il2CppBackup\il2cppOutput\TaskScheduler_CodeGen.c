﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_mA2345D2B8E39CC1F65A8587B8A74228B7077C018 (void);
extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_m9EAADF5D94F2B14ACC72BA9FBA9F3D1C623ACBE7 (void);
extern void Factory_FromAction_m368E677C8BEFE8EEDC19A03E037F3110F77781E9 (void);
extern void Factory_FromAsyncOperation_m45889044D10FFEE63839B564F42FC767CEB992D0 (void);
extern void Factory_FromDelay_m37D4044B18DD4750D2F5B3A7AB11C9DF7DE14FDA (void);
extern void Factory_FromWait_m990888590D2C15E2EA61AB95A3F908DB645B964D (void);
extern void U3CU3Ec__DisplayClass0_0__ctor_mC5A9A08671EF55D835B9F15993BF4FDE59F45646 (void);
extern void U3CU3Ec__DisplayClass0_0_U3CFromActionU3Eb__0_mDA12C1B15CBD69105213328290E0D9AB97B327F0 (void);
extern void U3CU3Ec__DisplayClass2_0__ctor_mFD5FCD46A9516125DB6711707C1B3B4BE2F097AB (void);
extern void U3CU3Ec__DisplayClass2_0_U3CFromAsyncOperationU3Eb__0_m8DEC88A191E826C2F11FDD5944C2097A78506492 (void);
extern void U3CU3Ec__DisplayClass3_0__ctor_mF934959DFE9764BAFDDCC0C5B101886006A0FD2E (void);
extern void U3CU3Ec__DisplayClass3_0_U3CFromDelayU3Eb__0_m7F78507D1058BFC830CB6D143BA5EA44A728D3A5 (void);
extern void U3CU3Ec__DisplayClass4_0__ctor_m64462E4856AF8D55FCFBD46364F682A329BB1C67 (void);
extern void U3CU3Ec__DisplayClass4_0_U3CFromWaitU3Eb__0_mD486A34388585FFD0DF8E01432FF2A6C0810E284 (void);
extern void SequentialScheduler_get__Tasks_mB83D22B61EB664FFBE0E0E07522B028B99171824 (void);
extern void SequentialScheduler_set__Tasks_mCF19B4F5391A29FB14661271837D793DDBBB4100 (void);
extern void SequentialScheduler_get_Count_m6DE625C63B7F644F6CC5FB21ECE7B69ADD24B5B6 (void);
extern void SequentialScheduler_get_Progress_m7D8F7C759715DD282FE64A2A037EFF57679C5E69 (void);
extern void SequentialScheduler_Reset_mA2BB284E6A00F6BE2FEC49883FFDED1BEB290194 (void);
extern void SequentialScheduler_Append_m71D5B36A865941635E94CA27D32053341561AF8E (void);
extern void SequentialScheduler_Execute_m0EDFFE2A88BB93B59C2BCD50AD9742132B6CFDB0 (void);
extern void SequentialScheduler_StartFrom_m5C13620E5B1E72D5DD4C9B989FC69F908EA841D5 (void);
extern void SequentialScheduler__ctor_m2E66B6C67B5AA19116ECFB5DE94CE89DC365BCAE (void);
extern void ParallelScheduler_get__Tasks_mC96C0BC294957F039A8E9D699E56F562886BD0F7 (void);
extern void ParallelScheduler_set__Tasks_m206C7AB318DD256547A83C04C7D52402AB7D7C30 (void);
extern void ParallelScheduler_get_Count_mEBDC238B524EFCB3CF5E41979FAE67E919CB7686 (void);
extern void ParallelScheduler_get_Progress_mEED41EFAF684B6B300EA8B9FE9BFB7C822C5B072 (void);
extern void ParallelScheduler_Reset_mC240DCA97124E58E163F36D1CF1399A1C23A2659 (void);
extern void ParallelScheduler_Append_m448C28DC5849BE41A8901850DC266ED7277DCD7C (void);
extern void ParallelScheduler_Execute_m128D59B9385566909843E65998A283B90260D39D (void);
extern void ParallelScheduler__ctor_mD5F54A9085D280823C0C888E458E10AF21F8F0B5 (void);
extern void CombinedScheduler_get__Schedulers_m29051BFB881A5D5FD9F106D462C3970259B866EC (void);
extern void CombinedScheduler_set__Schedulers_m405C6637EE371CA3714963884FAFB7A864673D4B (void);
extern void CombinedScheduler_get_Count_m4D45A55C53B5A74194916125792A3F45CEACD87F (void);
extern void CombinedScheduler_get_Progress_m59D06D92A29D308D3EBFCA8D447A25BF892A78D0 (void);
extern void CombinedScheduler_Reset_m27C8F150A6AA8259344542A17E28B1E9F91D773A (void);
extern void CombinedScheduler_Append_m1283EDAC2EAA44159839CA169C8B7CB940D177CD (void);
extern void CombinedScheduler_Append_mE3C2C0A61ABE0706690B42709661CB96629103CC (void);
extern void CombinedScheduler_Execute_m7DC0F73D8ECE7258EED9A91AE2C0C9CBBFE05060 (void);
extern void CombinedScheduler__ctor_mA84FA35F562461C5CF2D9DF6A593BA965988040C (void);
extern void Task_get_IsFinished_mB714DCFB6BC9CFD91296E670ED10248A7A52A153 (void);
extern void Task_set_IsFinished_mD6402087B874A5129E3E6DE8D00913475724DBBB (void);
extern void Task_get_Progress_m7621307F75BE2642677775BD0A075F9BA720D594 (void);
extern void Task_set_Progress_m3699ED176F6EFC3A9B805D4ADB8E8ACDAC3DF6E5 (void);
extern void Task__ctor_m75F016C7FAF44A76CF1FAD52D6761E1EA3228063 (void);
extern void Task__ctor_mE991E040C1E31C5AE097A795B42AB124FC3521ED (void);
extern void Task_Initialize_m1245EA3003E520F57ED20FBF2BD667C4D49FEF71 (void);
extern void Task_Reset_mADB1C64A00F3B065AD3CDAAAA2A2E47341A34543 (void);
extern void Task_Run_m478677E8DFA58763AA1A7497617C0E744C61C1B5 (void);
extern void Task_Tick_mFDF681D1AC67C60397C4C3CE6FE330E00A63827C (void);
extern void TaskDelay_get_IsFinished_m2D73702D0D386545FD1A2F962FAF230F047007FE (void);
extern void TaskDelay_set_IsFinished_m054FAB34FEFDEF22E3EA727665C4035009914F9C (void);
extern void TaskDelay_get_Progress_m8110CE4A3473567841046A03BED2BE91BEF9B26D (void);
extern void TaskDelay_set_Progress_mF54B802D8EF7B8B88692737F28BD19B8A3FB0A3D (void);
extern void TaskDelay__ctor_mB022423A8FB6D9B52DA87E7698E79D8780FAE17E (void);
extern void TaskDelay__ctor_m9C5A2D7BDFD2D2D7ABDD90FB2844DF14FA51DFDF (void);
extern void TaskDelay_Initialize_m21AD415596403721C63C81ECF69A5A1886F74609 (void);
extern void TaskDelay_Reset_mF8FFA09D2E643DF42F0DF68D6FEE4AA825977184 (void);
extern void TaskDelay_Run_m09A501B53D7E05475E02CA1BA8855E2249200415 (void);
extern void TaskDelay_Tick_mD4F8C2D0A48F995C114AC71743E4BA3D471C8B3B (void);
extern void TaskWait_get_IsFinished_m403C4658AE7A25F9CC2F3EEB5D8443207B3E2284 (void);
extern void TaskWait_set_IsFinished_mB7BA5FF0FE910A03891343EDEB0B0BA88D0C4F96 (void);
extern void TaskWait_get_Progress_mA4745C4730F2E5F19701B53A026C9C11751F890B (void);
extern void TaskWait__ctor_mBC9F0F91A9528C7FC4916B17D72BD37CED4DE9FB (void);
extern void TaskWait__ctor_m1BC340C98E6E79A59F49EA57430DFE235A2C9853 (void);
extern void TaskWait_Initialize_m57E23EB5BE81C2573B53470173A7268E34EF9E15 (void);
extern void TaskWait_Reset_m9C053488E95AFAE7E560414C16CBF604204F273C (void);
extern void TaskWait_Run_m676E989B58F9C167CBEDE500C60FD5692F09296E (void);
extern void TaskWait_Tick_m40301E65182205502D2D6D851379C68C13FA1038 (void);
extern void TaskWait_Evaluate_m0562B993210978EB140A1A66FDA26E7F2C379E58 (void);
extern void AsyncOperationTask_get_IsFinished_m274630F7C3BF342B87DC9704634956E4E87B7894 (void);
extern void AsyncOperationTask_get_Progress_mDA7152D16422255ABAED4BEBC1FD205ADFF7D0D2 (void);
extern void AsyncOperationTask__ctor_mF0D75B1EB6AA36F78EEF1363BE966F16039ABD6E (void);
extern void AsyncOperationTask__ctor_mC69D4362FA3DB2AA5A7F7C9D142CF69CB66C00E7 (void);
extern void AsyncOperationTask_Initialize_mAC8C2E376EB61E3551B7904E6ECEA40550FF3532 (void);
extern void AsyncOperationTask_Reset_m1026C7F4364FEBE32FFFCD4604DB851BB83A66E3 (void);
extern void AsyncOperationTask_Run_mD561ABF100E353B7FC64491874BF779466BCFD77 (void);
extern void AsyncOperationTask_Tick_m45B6FCA7D9198B0F15929A2759EE35AEB1A5E3D1 (void);
static Il2CppMethodPointer s_methodPointers[120] = 
{
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_mA2345D2B8E39CC1F65A8587B8A74228B7077C018,
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_m9EAADF5D94F2B14ACC72BA9FBA9F3D1C623ACBE7,
	Factory_FromAction_m368E677C8BEFE8EEDC19A03E037F3110F77781E9,
	NULL,
	Factory_FromAsyncOperation_m45889044D10FFEE63839B564F42FC767CEB992D0,
	Factory_FromDelay_m37D4044B18DD4750D2F5B3A7AB11C9DF7DE14FDA,
	Factory_FromWait_m990888590D2C15E2EA61AB95A3F908DB645B964D,
	NULL,
	NULL,
	U3CU3Ec__DisplayClass0_0__ctor_mC5A9A08671EF55D835B9F15993BF4FDE59F45646,
	U3CU3Ec__DisplayClass0_0_U3CFromActionU3Eb__0_mDA12C1B15CBD69105213328290E0D9AB97B327F0,
	NULL,
	NULL,
	U3CU3Ec__DisplayClass2_0__ctor_mFD5FCD46A9516125DB6711707C1B3B4BE2F097AB,
	U3CU3Ec__DisplayClass2_0_U3CFromAsyncOperationU3Eb__0_m8DEC88A191E826C2F11FDD5944C2097A78506492,
	U3CU3Ec__DisplayClass3_0__ctor_mF934959DFE9764BAFDDCC0C5B101886006A0FD2E,
	U3CU3Ec__DisplayClass3_0_U3CFromDelayU3Eb__0_m7F78507D1058BFC830CB6D143BA5EA44A728D3A5,
	U3CU3Ec__DisplayClass4_0__ctor_m64462E4856AF8D55FCFBD46364F682A329BB1C67,
	U3CU3Ec__DisplayClass4_0_U3CFromWaitU3Eb__0_mD486A34388585FFD0DF8E01432FF2A6C0810E284,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	SequentialScheduler_get__Tasks_mB83D22B61EB664FFBE0E0E07522B028B99171824,
	SequentialScheduler_set__Tasks_mCF19B4F5391A29FB14661271837D793DDBBB4100,
	SequentialScheduler_get_Count_m6DE625C63B7F644F6CC5FB21ECE7B69ADD24B5B6,
	SequentialScheduler_get_Progress_m7D8F7C759715DD282FE64A2A037EFF57679C5E69,
	SequentialScheduler_Reset_mA2BB284E6A00F6BE2FEC49883FFDED1BEB290194,
	SequentialScheduler_Append_m71D5B36A865941635E94CA27D32053341561AF8E,
	SequentialScheduler_Execute_m0EDFFE2A88BB93B59C2BCD50AD9742132B6CFDB0,
	SequentialScheduler_StartFrom_m5C13620E5B1E72D5DD4C9B989FC69F908EA841D5,
	SequentialScheduler__ctor_m2E66B6C67B5AA19116ECFB5DE94CE89DC365BCAE,
	ParallelScheduler_get__Tasks_mC96C0BC294957F039A8E9D699E56F562886BD0F7,
	ParallelScheduler_set__Tasks_m206C7AB318DD256547A83C04C7D52402AB7D7C30,
	ParallelScheduler_get_Count_mEBDC238B524EFCB3CF5E41979FAE67E919CB7686,
	ParallelScheduler_get_Progress_mEED41EFAF684B6B300EA8B9FE9BFB7C822C5B072,
	ParallelScheduler_Reset_mC240DCA97124E58E163F36D1CF1399A1C23A2659,
	ParallelScheduler_Append_m448C28DC5849BE41A8901850DC266ED7277DCD7C,
	ParallelScheduler_Execute_m128D59B9385566909843E65998A283B90260D39D,
	ParallelScheduler__ctor_mD5F54A9085D280823C0C888E458E10AF21F8F0B5,
	CombinedScheduler_get__Schedulers_m29051BFB881A5D5FD9F106D462C3970259B866EC,
	CombinedScheduler_set__Schedulers_m405C6637EE371CA3714963884FAFB7A864673D4B,
	CombinedScheduler_get_Count_m4D45A55C53B5A74194916125792A3F45CEACD87F,
	CombinedScheduler_get_Progress_m59D06D92A29D308D3EBFCA8D447A25BF892A78D0,
	CombinedScheduler_Reset_m27C8F150A6AA8259344542A17E28B1E9F91D773A,
	CombinedScheduler_Append_m1283EDAC2EAA44159839CA169C8B7CB940D177CD,
	CombinedScheduler_Append_mE3C2C0A61ABE0706690B42709661CB96629103CC,
	CombinedScheduler_Execute_m7DC0F73D8ECE7258EED9A91AE2C0C9CBBFE05060,
	CombinedScheduler__ctor_mA84FA35F562461C5CF2D9DF6A593BA965988040C,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	Task_get_IsFinished_mB714DCFB6BC9CFD91296E670ED10248A7A52A153,
	Task_set_IsFinished_mD6402087B874A5129E3E6DE8D00913475724DBBB,
	Task_get_Progress_m7621307F75BE2642677775BD0A075F9BA720D594,
	Task_set_Progress_m3699ED176F6EFC3A9B805D4ADB8E8ACDAC3DF6E5,
	Task__ctor_m75F016C7FAF44A76CF1FAD52D6761E1EA3228063,
	Task__ctor_mE991E040C1E31C5AE097A795B42AB124FC3521ED,
	Task_Initialize_m1245EA3003E520F57ED20FBF2BD667C4D49FEF71,
	Task_Reset_mADB1C64A00F3B065AD3CDAAAA2A2E47341A34543,
	Task_Run_m478677E8DFA58763AA1A7497617C0E744C61C1B5,
	Task_Tick_mFDF681D1AC67C60397C4C3CE6FE330E00A63827C,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	TaskDelay_get_IsFinished_m2D73702D0D386545FD1A2F962FAF230F047007FE,
	TaskDelay_set_IsFinished_m054FAB34FEFDEF22E3EA727665C4035009914F9C,
	TaskDelay_get_Progress_m8110CE4A3473567841046A03BED2BE91BEF9B26D,
	TaskDelay_set_Progress_mF54B802D8EF7B8B88692737F28BD19B8A3FB0A3D,
	TaskDelay__ctor_mB022423A8FB6D9B52DA87E7698E79D8780FAE17E,
	TaskDelay__ctor_m9C5A2D7BDFD2D2D7ABDD90FB2844DF14FA51DFDF,
	TaskDelay_Initialize_m21AD415596403721C63C81ECF69A5A1886F74609,
	TaskDelay_Reset_mF8FFA09D2E643DF42F0DF68D6FEE4AA825977184,
	TaskDelay_Run_m09A501B53D7E05475E02CA1BA8855E2249200415,
	TaskDelay_Tick_mD4F8C2D0A48F995C114AC71743E4BA3D471C8B3B,
	TaskWait_get_IsFinished_m403C4658AE7A25F9CC2F3EEB5D8443207B3E2284,
	TaskWait_set_IsFinished_mB7BA5FF0FE910A03891343EDEB0B0BA88D0C4F96,
	TaskWait_get_Progress_mA4745C4730F2E5F19701B53A026C9C11751F890B,
	TaskWait__ctor_mBC9F0F91A9528C7FC4916B17D72BD37CED4DE9FB,
	TaskWait__ctor_m1BC340C98E6E79A59F49EA57430DFE235A2C9853,
	TaskWait_Initialize_m57E23EB5BE81C2573B53470173A7268E34EF9E15,
	TaskWait_Reset_m9C053488E95AFAE7E560414C16CBF604204F273C,
	TaskWait_Run_m676E989B58F9C167CBEDE500C60FD5692F09296E,
	TaskWait_Tick_m40301E65182205502D2D6D851379C68C13FA1038,
	TaskWait_Evaluate_m0562B993210978EB140A1A66FDA26E7F2C379E58,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	AsyncOperationTask_get_IsFinished_m274630F7C3BF342B87DC9704634956E4E87B7894,
	AsyncOperationTask_get_Progress_mDA7152D16422255ABAED4BEBC1FD205ADFF7D0D2,
	AsyncOperationTask__ctor_mF0D75B1EB6AA36F78EEF1363BE966F16039ABD6E,
	AsyncOperationTask__ctor_mC69D4362FA3DB2AA5A7F7C9D142CF69CB66C00E7,
	AsyncOperationTask_Initialize_mAC8C2E376EB61E3551B7904E6ECEA40550FF3532,
	AsyncOperationTask_Reset_m1026C7F4364FEBE32FFFCD4604DB851BB83A66E3,
	AsyncOperationTask_Run_mD561ABF100E353B7FC64491874BF779466BCFD77,
	AsyncOperationTask_Tick_m45B6FCA7D9198B0F15929A2759EE35AEB1A5E3D1,
};
static const int32_t s_InvokerIndices[120] = 
{
	21395,
	13298,
	20515,
	0,
	20515,
	20526,
	18010,
	0,
	0,
	13298,
	13298,
	0,
	0,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	13052,
	10682,
	12996,
	13195,
	13298,
	9272,
	7822,
	10629,
	13298,
	13052,
	10682,
	12996,
	13195,
	13298,
	9272,
	7822,
	13298,
	13052,
	10682,
	12996,
	13195,
	13298,
	9272,
	9272,
	7822,
	13298,
	0,
	0,
	0,
	0,
	0,
	12815,
	10442,
	13195,
	10823,
	13298,
	10682,
	5688,
	13298,
	13298,
	10823,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	12815,
	10442,
	13195,
	10823,
	13298,
	10823,
	5783,
	13298,
	13298,
	10823,
	12815,
	10442,
	13195,
	13298,
	5703,
	2805,
	13298,
	13298,
	10823,
	12815,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	12815,
	13195,
	13298,
	10682,
	5688,
	13298,
	13298,
	10823,
};
static const Il2CppTokenRangePair s_rgctxIndices[8] = 
{
	{ 0x02000006, { 23, 3 } },
	{ 0x0200000A, { 26, 3 } },
	{ 0x0200000B, { 29, 9 } },
	{ 0x02000012, { 38, 7 } },
	{ 0x02000015, { 45, 7 } },
	{ 0x06000004, { 0, 10 } },
	{ 0x06000008, { 10, 10 } },
	{ 0x06000009, { 20, 3 } },
};
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass1_0_1_t109125A98079F47E0233E307DF62BBFE2ADCA5A7;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass1_0_1__ctor_m27134CC3533CC46ACAAD95A690B4373106D3E931;
extern const uint32_t g_rgctx_TaskPool_1_Get_mBCD871AF0047A9AF02D75B20C370084CF3A780B6;
extern const uint32_t g_rgctx_TaskPool_1_tFC3D1A58016F0A01D37EAEFBB55BE23E0BA179BE;
extern const uint32_t g_rgctx_Task_1_tCC72F2EC7E708057C14794BD44AFE6B163F35CC6;
extern const uint32_t g_rgctx_Task_1__ctor_mAC73F9A44DC590DDC22CB7E64167E3924EC78394;
extern const uint32_t g_rgctx_Action_1_tB9D18867B12C878A4139772BA78BA7B92CB1D3C3;
extern const uint32_t g_rgctx_T_tD95687287F3275115415501726C0318128595A90;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass1_0_1_U3CFromActionU3Eb__0_m490FE99A1B094D984AA1E8126603063FB37274BC;
extern const uint32_t g_rgctx_Task_1_Initialize_mBB02D15118DA9A79379B59E51C6D12019E5F8CCD;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass5_0_1_tF19A2334968831F9F70B04A37149C7B7F5F90F0C;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass5_0_1__ctor_mD48F6D78F22C640124E36933362C3CD9FFAB8AEA;
extern const uint32_t g_rgctx_TaskPool_1_Get_m6A351D7B87DBDF61C0DF6F63E917847E43568C77;
extern const uint32_t g_rgctx_TaskPool_1_tDD7D6FCA576A58B39D1DF2D2381B6397E58E1BB0;
extern const uint32_t g_rgctx_TaskWait_1_tABF99AC89A696C50639B8CEAA60E760CB3EE4576;
extern const uint32_t g_rgctx_TaskWait_1__ctor_m67793E68C8A4FA4A195647B1587073CED040EE75;
extern const uint32_t g_rgctx_Func_2_t6481EBC5A1EF32D86A0857754264201C83FC3F2A;
extern const uint32_t g_rgctx_T_t404602688ACA7E533605DB7EEE16419A638075FF;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass5_0_1_U3CFromWaitU3Eb__0_mC75D9FACE9D46D74BDA3975A15EDCFC806F848B8;
extern const uint32_t g_rgctx_TaskWait_1_Initialize_mAD655BD2F9895254CFCCC00C185B95E4BD3CDE45;
extern const uint32_t g_rgctx_T_t8BDC4EBEC0BDD1F9553D845F2E23BB34D5BD1D57;
extern const uint32_t g_rgctx_TaskPool_1_Return_m9DE58196DBAF3C3275A25805DA6BF018D77172E1;
extern const uint32_t g_rgctx_TaskPool_1_t9C7B4AAB598909BD368909D16D8173C2C795AD24;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass1_0_1_tC622542F453E593E8E592A3E0C5E720704D5FC34;
extern const uint32_t g_rgctx_Task_1_tCDBA7E03DB71342E5EF5A2E0610FB146B7B1B1D1;
extern const uint32_t g_rgctx_Factory_Return_TisTask_1_tCDBA7E03DB71342E5EF5A2E0610FB146B7B1B1D1_mC37EFC6054DA11640C997CEFA40E7F058ABF2C1A;
extern const uint32_t g_rgctx_U3CU3Ec__DisplayClass5_0_1_tDFD1489D20ED64A609597DC2DB70FF6D53A02DC6;
extern const uint32_t g_rgctx_TaskWait_1_t63BFEEB9DECBB23E37A0851546C47A5B1615293A;
extern const uint32_t g_rgctx_Factory_Return_TisTaskWait_1_t63BFEEB9DECBB23E37A0851546C47A5B1615293A_mE67102C3142123FB809E6E9CE13C131EDDF86DAD;
extern const uint32_t g_rgctx_TaskPool_1_tB1510E8CDE2D3B79B8619E668D1B98EE6A065534;
extern const uint32_t g_rgctx_Stack_1_tF1F5733F326318C1D57D5428E4EA7025C2765321;
extern const uint32_t g_rgctx_TaskPool_1_tB1510E8CDE2D3B79B8619E668D1B98EE6A065534;
extern const uint32_t g_rgctx_Stack_1_get_Count_m2B7400B103B2DBDA7D01235F34B8AAAA240BC080;
extern const uint32_t g_rgctx_T_t18A393766C1A3B3DC1E8B9037A9F719FCBA98432;
extern const uint32_t g_rgctx_Stack_1_Pop_m1811E34DC6966579D48CAF639F03A95885E36805;
extern const uint32_t g_rgctx_Stack_1_Push_m3E00164F78EAE0488C61E83332802329B1374BBF;
extern const uint32_t g_rgctx_Stack_1_Clear_m4E7F0AEFFF147E3261ADE03C35DB32063F82ED84;
extern const uint32_t g_rgctx_Stack_1__ctor_m5CCBCF84AFB7956558B34C97CEB4B886C8A44A6B;
extern const uint32_t g_rgctx_Task_1_tF69DDEA012E867A88BD86AD1D2C70B6F0899D7C7;
extern const uint32_t g_rgctx_Action_1_t4956FC36BAFAEA69959C3627F7AED5DCF5BA20D4;
extern const uint32_t g_rgctx_T_tF2025FB0AF702AD43A74482131E7CEBAD51DEC2F;
extern const uint32_t g_rgctx_Task_1_Initialize_mA7E54D9CC8F15F156E8AFF28A186CEC72266C832;
extern const uint32_t g_rgctx_Task_1_set_IsFinished_m268B7A0CB139A7B4C8B82A49DDFF19EA24ABB4B7;
extern const uint32_t g_rgctx_Task_1_set_Progress_mB50CC1A9A78AEE327671B00A358F91AA28982290;
extern const uint32_t g_rgctx_Action_1_Invoke_m39B67E02570F319127579AAAF83503BFCF2AB450;
extern const uint32_t g_rgctx_TaskWait_1_t27C7A55E965CD7E79E8ED61CEFCD995742ECBC50;
extern const uint32_t g_rgctx_Func_2_tFA7175CA9910640AA7B4DC8180D94464A41EA1DF;
extern const uint32_t g_rgctx_T_t2D662E6FA170931D78640815199A83A09BA04F41;
extern const uint32_t g_rgctx_TaskWait_1_Initialize_m16A417E29165E728BD72B5000C3332C2B8DA66BF;
extern const uint32_t g_rgctx_TaskWait_1_set_IsFinished_mD846EBB78A08860F32759F6FA0B01FF5E3E4A368;
extern const uint32_t g_rgctx_TaskWait_1_Evaluate_m2FF4FFB99AACB0BEA8863C67EB3E5CAFE27D2BAA;
extern const uint32_t g_rgctx_Func_2_Invoke_mB7B97E237A75C4D3863ADE18C5C4603A93A72650;
static const Il2CppRGCTXDefinition s_rgctxValues[52] = 
{
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec__DisplayClass1_0_1_t109125A98079F47E0233E307DF62BBFE2ADCA5A7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec__DisplayClass1_0_1__ctor_m27134CC3533CC46ACAAD95A690B4373106D3E931 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TaskPool_1_Get_mBCD871AF0047A9AF02D75B20C370084CF3A780B6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TaskPool_1_tFC3D1A58016F0A01D37EAEFBB55BE23E0BA179BE },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Task_1_tCC72F2EC7E708057C14794BD44AFE6B163F35CC6 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Task_1__ctor_mAC73F9A44DC590DDC22CB7E64167E3924EC78394 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_1_tB9D18867B12C878A4139772BA78BA7B92CB1D3C3 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tD95687287F3275115415501726C0318128595A90 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec__DisplayClass1_0_1_U3CFromActionU3Eb__0_m490FE99A1B094D984AA1E8126603063FB37274BC },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Task_1_Initialize_mBB02D15118DA9A79379B59E51C6D12019E5F8CCD },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec__DisplayClass5_0_1_tF19A2334968831F9F70B04A37149C7B7F5F90F0C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec__DisplayClass5_0_1__ctor_mD48F6D78F22C640124E36933362C3CD9FFAB8AEA },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TaskPool_1_Get_m6A351D7B87DBDF61C0DF6F63E917847E43568C77 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TaskPool_1_tDD7D6FCA576A58B39D1DF2D2381B6397E58E1BB0 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TaskWait_1_tABF99AC89A696C50639B8CEAA60E760CB3EE4576 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TaskWait_1__ctor_m67793E68C8A4FA4A195647B1587073CED040EE75 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_2_t6481EBC5A1EF32D86A0857754264201C83FC3F2A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t404602688ACA7E533605DB7EEE16419A638075FF },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec__DisplayClass5_0_1_U3CFromWaitU3Eb__0_mC75D9FACE9D46D74BDA3975A15EDCFC806F848B8 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TaskWait_1_Initialize_mAD655BD2F9895254CFCCC00C185B95E4BD3CDE45 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t8BDC4EBEC0BDD1F9553D845F2E23BB34D5BD1D57 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TaskPool_1_Return_m9DE58196DBAF3C3275A25805DA6BF018D77172E1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TaskPool_1_t9C7B4AAB598909BD368909D16D8173C2C795AD24 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec__DisplayClass1_0_1_tC622542F453E593E8E592A3E0C5E720704D5FC34 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Task_1_tCDBA7E03DB71342E5EF5A2E0610FB146B7B1B1D1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Factory_Return_TisTask_1_tCDBA7E03DB71342E5EF5A2E0610FB146B7B1B1D1_mC37EFC6054DA11640C997CEFA40E7F058ABF2C1A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec__DisplayClass5_0_1_tDFD1489D20ED64A609597DC2DB70FF6D53A02DC6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TaskWait_1_t63BFEEB9DECBB23E37A0851546C47A5B1615293A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Factory_Return_TisTaskWait_1_t63BFEEB9DECBB23E37A0851546C47A5B1615293A_mE67102C3142123FB809E6E9CE13C131EDDF86DAD },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TaskPool_1_tB1510E8CDE2D3B79B8619E668D1B98EE6A065534 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Stack_1_tF1F5733F326318C1D57D5428E4EA7025C2765321 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TaskPool_1_tB1510E8CDE2D3B79B8619E668D1B98EE6A065534 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Stack_1_get_Count_m2B7400B103B2DBDA7D01235F34B8AAAA240BC080 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t18A393766C1A3B3DC1E8B9037A9F719FCBA98432 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Stack_1_Pop_m1811E34DC6966579D48CAF639F03A95885E36805 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Stack_1_Push_m3E00164F78EAE0488C61E83332802329B1374BBF },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Stack_1_Clear_m4E7F0AEFFF147E3261ADE03C35DB32063F82ED84 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Stack_1__ctor_m5CCBCF84AFB7956558B34C97CEB4B886C8A44A6B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Task_1_tF69DDEA012E867A88BD86AD1D2C70B6F0899D7C7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_1_t4956FC36BAFAEA69959C3627F7AED5DCF5BA20D4 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tF2025FB0AF702AD43A74482131E7CEBAD51DEC2F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Task_1_Initialize_mA7E54D9CC8F15F156E8AFF28A186CEC72266C832 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Task_1_set_IsFinished_m268B7A0CB139A7B4C8B82A49DDFF19EA24ABB4B7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Task_1_set_Progress_mB50CC1A9A78AEE327671B00A358F91AA28982290 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Action_1_Invoke_m39B67E02570F319127579AAAF83503BFCF2AB450 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TaskWait_1_t27C7A55E965CD7E79E8ED61CEFCD995742ECBC50 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_2_tFA7175CA9910640AA7B4DC8180D94464A41EA1DF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t2D662E6FA170931D78640815199A83A09BA04F41 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TaskWait_1_Initialize_m16A417E29165E728BD72B5000C3332C2B8DA66BF },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TaskWait_1_set_IsFinished_mD846EBB78A08860F32759F6FA0B01FF5E3E4A368 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TaskWait_1_Evaluate_m2FF4FFB99AACB0BEA8863C67EB3E5CAFE27D2BAA },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Func_2_Invoke_mB7B97E237A75C4D3863ADE18C5C4603A93A72650 },
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_TaskScheduler_CodeGenModule;
const Il2CppCodeGenModule g_TaskScheduler_CodeGenModule = 
{
	"TaskScheduler.dll",
	120,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	8,
	s_rgctxIndices,
	52,
	s_rgctxValues,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};

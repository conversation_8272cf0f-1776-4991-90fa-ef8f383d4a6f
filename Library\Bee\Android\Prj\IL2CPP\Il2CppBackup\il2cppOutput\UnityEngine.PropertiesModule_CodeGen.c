﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void FieldMember__ctor_mA6F2BB809BD42F26804E1E9A78B80ECA6C65FF03 (void);
extern void FieldMember_get_Name_m9301F65E796E51B6F4EF0837AEB3EB9AD25293D6 (void);
extern void FieldMember_get_IsReadOnly_mF9CA4CBB2973908DA90F49DD86124FC769168FB0 (void);
extern void FieldMember_get_ValueType_m76238497CE787FD26A79341A8F3207EFF31CE63A (void);
extern void FieldMember_GetCustomAttributes_m3A1CF3269B1FD4C659FDEA8D51CFAE003612101D (void);
extern void PropertyMember_get_Name_mCB0EBF067D82D0871C8051116D34A7D82EBE67FB (void);
extern void PropertyMember_get_IsReadOnly_m595190E0A259DF7A4CD106BFF16949E939F511C5 (void);
extern void PropertyMember_get_ValueType_m40F8CF0BBF20782A6C594365911716F386FE4E1A (void);
extern void PropertyMember__ctor_m0B727AF7D87194ED8EA453CC4CF01A10563E2334 (void);
extern void PropertyMember_GetCustomAttributes_mE4214E27A5A04DF047573888EDD9590FD6C85409 (void);
extern void TypeTraits_IsContainer_m46D04F3E3219371CC5F133E2CC54BCA46FD72505 (void);
extern void TypeUtility__cctor_mFCC4029AD0D2F3FBE3F7117AC485DF687DE409A5 (void);
extern void TypeUtility_GetTypeDisplayName_m45C565A0737008926DD1D4CA6B453A475C8E77FD (void);
extern void TypeUtility_GetTypeDisplayName_mCDE259ECEE5E6A4E7C52D4319D7E5D856E19F862 (void);
extern void TypeUtility_GetRootType_mA1A5FDE24BC31C9CB9E4827160A1379BDEAEAB0B (void);
extern void TypeUtility_CreateTypeConstructor_mCD83647C237EE7D7DC578EE0FFF12124F4670D74 (void);
extern void TypeUtility_GetTypeConstructor_m46157C15601C266EE0DB2324E834E1CE4CBFE1DC (void);
extern void TypeUtility_CanBeInstantiated_m02649AACC5170FAB0419B5FA22FF5DE524E23828 (void);
extern void TypeUtility_CheckIsAssignableFrom_m284900A7E86ACD68227E5E3DCC9430EEC7E1E03E (void);
extern void TypeUtility_CheckCanBeInstantiated_mB2C921D41DC94D83758FC5CFDFEE26911F62B14F (void);
extern void NonConstructable_Unity_Properties_TypeUtility_ITypeConstructor_get_CanBeInstantiated_m12234E46124842FE494AB4C0B55045C1954F9A89 (void);
extern void NonConstructable_Instantiate_m9FE0A208C273DC31D79B29A334F4DA2BF63A8AF6 (void);
extern void NonConstructable__ctor_m6B6DB41FBB71A153AFAC3B10CF35D2503D8CA982 (void);
extern void TypeConstructorVisitor__ctor_m496AFA7B1A835D40C4BEDBD5A7D1804CF196DE23 (void);
extern void U3CU3Ec__cctor_m1E3F4DC530F8CB5240E8B5FFD5E5477BB7300FC2 (void);
extern void U3CU3Ec__ctor_mD06BD054A2682CAA505B6F75C45B8D4736FBFA39 (void);
extern void U3CU3Ec_U3C_cctorU3Eb__11_0_m59D3FD88A1841D56C6A2D231E993AA6D2E6465E1 (void);
extern void U3CU3Ec_U3C_cctorU3Eb__11_1_mCF36865D4A819F0C0DDA987F30077434E56BC826 (void);
extern void U3CU3Ec_U3C_cctorU3Eb__11_2_mBA773F3E344C145FE939B19C5EE690F9FEC60716 (void);
extern void DefaultPropertyBagInitializer_Initialize_m8BA3D4950B9C07EF2A0978A88D908FCF297BD080 (void);
extern void ColorPropertyBag__ctor_m695D2E7503EA97F7BB7252A882546C1C8B11D1BC (void);
extern void RProperty_get_Name_m768BEB2E8FA1995F41236DBA5C68C41FB75510BA (void);
extern void RProperty__ctor_m5B48CD3CF7C1DD802022B9AF99DAEEAB2A25B430 (void);
extern void GProperty_get_Name_mB1B8C4B15616728B9B97AE53213189FBC4E2420B (void);
extern void GProperty__ctor_m2E28D044AC6B8D72EA0595E1290599DC5F91EC35 (void);
extern void BProperty_get_Name_m43E6F7094BC817269A225E688C418557587C23D2 (void);
extern void BProperty__ctor_m3FA7E12A9E2B2B2AFFF6FC5C96AE4EB5BF9CCFA8 (void);
extern void AProperty_get_Name_m82413DC018A41D152B3574D620AC576392D913A5 (void);
extern void AProperty__ctor_mAED58387B9563DED80A12595C6A11B5B2BEA6D2A (void);
extern void Vector2PropertyBag__ctor_mF30206E01ECFEDF6726878BEDA3DA53A2D24D00F (void);
extern void XProperty_get_Name_m0C83B1AD112C546FBAAE21B64AA11F723AC30FE5 (void);
extern void XProperty__ctor_m21350D1E93A69FD0D148521382FD3B438C450692 (void);
extern void YProperty_get_Name_m6B355E40C67C04DD438964ED91CCCFEC87863BEC (void);
extern void YProperty__ctor_mB32B0091E33FBD963435FB9973DB0DD000C3FD5C (void);
extern void Vector3PropertyBag__ctor_mC00E39D196C789D68E6F910494E433E729E0E4B6 (void);
extern void XProperty_get_Name_m57E924B79868E3450299B50CF2E86F6EC4D2DB9D (void);
extern void XProperty__ctor_m46FEC277D299B82A75C8C20B0AEEACC479E3AEEB (void);
extern void YProperty_get_Name_mEC582AC60E11C6187A86D3EB8E90DA232D654D8D (void);
extern void YProperty__ctor_m4269E5BB6FCDFD8F849755DCA434EE9ACF9BE2E5 (void);
extern void ZProperty_get_Name_m2F0978F5D89C0A4269B629FF604403D5C6AB7E18 (void);
extern void ZProperty__ctor_mE1AC4D86341B82F2792FD53417D5ABE36E37B10E (void);
extern void Vector4PropertyBag__ctor_m6B7DFA6F651C0DC6522B1AB7E179752BA836AE10 (void);
extern void XProperty_get_Name_m17A1AA4B30199BAEF0B7072526E81C2B30014B4A (void);
extern void XProperty__ctor_mBE4223E008541E2DD13ADC1707FBB73E51631CBD (void);
extern void YProperty_get_Name_m0D5C29377B95EB977E61764CDD215197591DFFEA (void);
extern void YProperty__ctor_m26384F8C7D674046A6311567F6BD57C05C1E508A (void);
extern void ZProperty_get_Name_mC39EB7495554C0D98CCB62566565F09D1E731124 (void);
extern void ZProperty__ctor_mBE30B28C0B1F18C02DAEBD5354086C5E7412C795 (void);
extern void WProperty_get_Name_m16E79ED0A622375BCAE1FB7ACF221391A9849A10 (void);
extern void WProperty__ctor_mE6F8FD652CBFF130392D33791BD2961B09A2EFF4 (void);
extern void Vector2IntPropertyBag__ctor_mA5E766EC29B1E37CAA759CBB09997D25FAFB0631 (void);
extern void XProperty_get_Name_m6E8366D6ADA17D3A95511FE12147584A26159AFE (void);
extern void XProperty__ctor_m850B5684A9419E1A388FA41A041AD667EB1B2589 (void);
extern void YProperty_get_Name_m27BEC9727316585FA4BF59F19B53ECC6DC458318 (void);
extern void YProperty__ctor_mC8B0BA567D3BED03017F65512C6DE795C7F7558B (void);
extern void Vector3IntPropertyBag__ctor_mECB1D0CC81ACF263C6C0D126BB285A11CBACB2BC (void);
extern void XProperty_get_Name_m214358C5224F9EB07B17E4BACEAE9079B50D815C (void);
extern void XProperty__ctor_m8A1CCE2C8F5179029876BD678965D6C211CB332C (void);
extern void YProperty_get_Name_m2C54CF468EE63E89D8B301BBB2D5FD02F19101D4 (void);
extern void YProperty__ctor_m2233FD190B553BFBD445F8A2EE6BE3DDF0047DBC (void);
extern void ZProperty_get_Name_m2A323942BFFED99C2269258DBB195A8C75EC7032 (void);
extern void ZProperty__ctor_mBFB49B0AD7F5606851D8E8B29D0F3AC93B0E0FA7 (void);
extern void RectPropertyBag__ctor_m4C5D1C260902338AC90DB837C39FE28695ED0110 (void);
extern void XProperty_get_Name_m4847A49D2AA9C51AFF775ECDF8923AE2D9287C99 (void);
extern void XProperty__ctor_mB5A8967CB19EE4DFE4ECEBEB88D0E1E7B0E6A534 (void);
extern void YProperty_get_Name_m2ECB38AAD0B3AF556F16411B19A6E8051C7E6B2A (void);
extern void YProperty__ctor_m06A2AAA9690D1D2BFC24B0EB360DB3585A877441 (void);
extern void WidthProperty_get_Name_m418258980F9849C8B8974F1BFFE3B81A5A29205D (void);
extern void WidthProperty__ctor_m94F29A6177829CC11F530347605BB4592547C72D (void);
extern void HeightProperty_get_Name_mACC36167E0E725402D144CF52B5315FD01E17A3D (void);
extern void HeightProperty__ctor_m07DCB5A7DF246055CB471EF52A8B0C909C222F2E (void);
extern void RectIntPropertyBag__ctor_mFFAC4C410B920C26B29CB6A309FB7EEF90E75A4F (void);
extern void XProperty_get_Name_m6F3F6E0874574CA99F1067BC0CB937F03CBC84AB (void);
extern void XProperty__ctor_mE83AE59E6CAFA7F55F968FD2D8DBD9DCD0284632 (void);
extern void YProperty_get_Name_m2AE1DE95187BBD3082B9AEE36180867B7D0A9919 (void);
extern void YProperty__ctor_m484229CC408CD868ABA8D0DBE8B12BDEEE74E4C8 (void);
extern void WidthProperty_get_Name_mFEF2D1077A3A5D33C9B6BE8E4576FFE459B19EA2 (void);
extern void WidthProperty__ctor_m83F6E008F488F8C6DEC0A7BCCF5118221C576DCD (void);
extern void HeightProperty_get_Name_m0E2478B4BBEBFAFBB16F14DCC98730B7E79DC08F (void);
extern void HeightProperty__ctor_m9191C19D80EA3F2F645028F03A507E0D492DBB2A (void);
extern void BoundsPropertyBag__ctor_mB3B92E349452661D037A9AB2A8B6581ECFEBB111 (void);
extern void CenterProperty_get_Name_mE2932C9FBF1FBC93325E98817C5D7B377DEB0CCF (void);
extern void CenterProperty__ctor_m4ECB75EAEC52B0EDDC2EE91F4F46F12A7870420A (void);
extern void ExtentsProperty_get_Name_m01B60324ABC14349CAB683087AA1617A61F428B2 (void);
extern void ExtentsProperty__ctor_mC3315D3C24FA257EE106E3F6460360D65EB0DB16 (void);
extern void BoundsIntPropertyBag__ctor_mE2B2BB212558FE1C221E01D8FCEC4C41B02C6BF5 (void);
extern void PositionProperty_get_Name_m39AE78CF5CD19081CF1B424CE3182BE78AE55F62 (void);
extern void PositionProperty__ctor_mC574247EEEC6CFC189F13B0B961BD2A9C2CBB2CD (void);
extern void SizeProperty_get_Name_m6941C93A954B441C5188389C079E699FD0CBC41C (void);
extern void SizeProperty__ctor_m45114288E626A2F23A1035BDB8420DD663FE25BB (void);
extern void SystemVersionPropertyBag__ctor_mC0D23BC1F975382571DFA70EC5B52B24083BD4AF (void);
extern void MajorProperty__ctor_m5FA801277D886789A8732CE2E86F27C49E371679 (void);
extern void MajorProperty_get_Name_m95F7965B52F39B3620B82E47B30CF2893FA414EC (void);
extern void MinorProperty__ctor_mF391B7B9C95C2B48FB07127E463F0B788F9B0F29 (void);
extern void MinorProperty_get_Name_mC00B9C231673E72545064F606F64027117280A09 (void);
extern void BuildProperty__ctor_mD5BCC6C2F665694E7711CE8742008F6F2434BDAC (void);
extern void BuildProperty_get_Name_mE523A88F9890A84EA707B257639A18D3EC256E3A (void);
extern void RevisionProperty__ctor_m0606FE463AAA1C106582277BB0115987861BE2FC (void);
extern void RevisionProperty_get_Name_m3A7BF24E10B80CB5CD0F651215A73824BCF9D4CE (void);
extern void PropertyBagStore__cctor_mF5E7248419BE8AEAB2356857AE4E19759082AFBD (void);
extern void PropertyBagStore_GetPropertyBag_m90F2EBB48D60993594856358C572964C8011143E (void);
extern void ReflectedPropertyBagAttribute__ctor_m630B43FC9EA5EACF97A960B10C6A4F28E2786B10 (void);
extern void ReflectedPropertyBagProvider__ctor_mF83A77394EF7205D41A784158583F084E8CE04C2 (void);
extern void ReflectedPropertyBagProvider_CreatePropertyBag_m89CDB23B3D426162B7641A4BB14DB3D71FE56CB2 (void);
extern void ReflectedPropertyBagProvider_GetPropertyMembers_mCCEF82F4B8D8F8416FFE7703B4C797CCA16436D5 (void);
extern void ReflectedPropertyBagProvider_IsValidMember_m942BF637D7B010D3702E74009ABAA35CEF0624F2 (void);
extern void ReflectedPropertyBagProvider_IsValidPropertyType_mFBA6F8CF9F5B94652229D6A151B5302C460F05BF (void);
extern void U3CU3Ec__cctor_mB26D9D572137F1C808D3D94430FC54F4BE1E2079 (void);
extern void U3CU3Ec__ctor_m1B468CA26848A620321355E672EED6B1E6713535 (void);
extern void U3CU3Ec_U3C_ctorU3Eb__10_0_m98A9F8B7DD2A14EBBB86606DCFE2CB988291758F (void);
extern void U3CU3Ec_U3CGetPropertyMembersU3Eb__22_0_m50914E0FCA3654CF9091BFCA37780956B2CA0136 (void);
extern void U3CGetPropertyMembersU3Ed__22__ctor_mFCA3444C6665B9FD96AF049C35FAD5D1D330BBB2 (void);
extern void U3CGetPropertyMembersU3Ed__22_System_IDisposable_Dispose_m9F9302585189A3F427CBA6992B1257DE344119C4 (void);
extern void U3CGetPropertyMembersU3Ed__22_MoveNext_m729AF376AC4F43C28BD1BAA7792CE1D0C9C2EDB9 (void);
extern void U3CGetPropertyMembersU3Ed__22_U3CU3Em__Finally1_m199A76100E9211640529676C30A85BD1060498D4 (void);
extern void U3CGetPropertyMembersU3Ed__22_System_Collections_Generic_IEnumeratorU3CSystem_Reflection_MemberInfoU3E_get_Current_mB28BEAF4EB3FAB5E771DC9A6454DCE6A67741FD9 (void);
extern void U3CGetPropertyMembersU3Ed__22_System_Collections_IEnumerator_Reset_mBF507A053052F0001483795394E045C4E2EB826C (void);
extern void U3CGetPropertyMembersU3Ed__22_System_Collections_IEnumerator_get_Current_mC30C3E93C92C552E330B9A3D6D239E72A0586BA0 (void);
extern void U3CGetPropertyMembersU3Ed__22_System_Collections_Generic_IEnumerableU3CSystem_Reflection_MemberInfoU3E_GetEnumerator_mFB07F1A6491DB7EEA44C182930AD32C728599080 (void);
extern void U3CGetPropertyMembersU3Ed__22_System_Collections_IEnumerable_GetEnumerator_mD6684E626A039A8014D140C3BEEBD24928F7D5ED (void);
extern void ReflectionUtilities_SanitizeMemberName_m00515F1FC752D0A52D90143E58D19A7D40D799AE (void);
static Il2CppMethodPointer s_methodPointers[267] = 
{
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	FieldMember__ctor_mA6F2BB809BD42F26804E1E9A78B80ECA6C65FF03,
	FieldMember_get_Name_m9301F65E796E51B6F4EF0837AEB3EB9AD25293D6,
	FieldMember_get_IsReadOnly_mF9CA4CBB2973908DA90F49DD86124FC769168FB0,
	FieldMember_get_ValueType_m76238497CE787FD26A79341A8F3207EFF31CE63A,
	FieldMember_GetCustomAttributes_m3A1CF3269B1FD4C659FDEA8D51CFAE003612101D,
	PropertyMember_get_Name_mCB0EBF067D82D0871C8051116D34A7D82EBE67FB,
	PropertyMember_get_IsReadOnly_m595190E0A259DF7A4CD106BFF16949E939F511C5,
	PropertyMember_get_ValueType_m40F8CF0BBF20782A6C594365911716F386FE4E1A,
	PropertyMember__ctor_m0B727AF7D87194ED8EA453CC4CF01A10563E2334,
	PropertyMember_GetCustomAttributes_mE4214E27A5A04DF047573888EDD9590FD6C85409,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	TypeTraits_IsContainer_m46D04F3E3219371CC5F133E2CC54BCA46FD72505,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	TypeUtility__cctor_mFCC4029AD0D2F3FBE3F7117AC485DF687DE409A5,
	TypeUtility_GetTypeDisplayName_m45C565A0737008926DD1D4CA6B453A475C8E77FD,
	TypeUtility_GetTypeDisplayName_mCDE259ECEE5E6A4E7C52D4319D7E5D856E19F862,
	TypeUtility_GetRootType_mA1A5FDE24BC31C9CB9E4827160A1379BDEAEAB0B,
	TypeUtility_CreateTypeConstructor_mCD83647C237EE7D7DC578EE0FFF12124F4670D74,
	NULL,
	TypeUtility_GetTypeConstructor_m46157C15601C266EE0DB2324E834E1CE4CBFE1DC,
	NULL,
	TypeUtility_CanBeInstantiated_m02649AACC5170FAB0419B5FA22FF5DE524E23828,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	TypeUtility_CheckIsAssignableFrom_m284900A7E86ACD68227E5E3DCC9430EEC7E1E03E,
	NULL,
	TypeUtility_CheckCanBeInstantiated_mB2C921D41DC94D83758FC5CFDFEE26911F62B14F,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NonConstructable_Unity_Properties_TypeUtility_ITypeConstructor_get_CanBeInstantiated_m12234E46124842FE494AB4C0B55045C1954F9A89,
	NonConstructable_Instantiate_m9FE0A208C273DC31D79B29A334F4DA2BF63A8AF6,
	NonConstructable__ctor_m6B6DB41FBB71A153AFAC3B10CF35D2503D8CA982,
	NULL,
	TypeConstructorVisitor__ctor_m496AFA7B1A835D40C4BEDBD5A7D1804CF196DE23,
	U3CU3Ec__cctor_m1E3F4DC530F8CB5240E8B5FFD5E5477BB7300FC2,
	U3CU3Ec__ctor_mD06BD054A2682CAA505B6F75C45B8D4736FBFA39,
	U3CU3Ec_U3C_cctorU3Eb__11_0_m59D3FD88A1841D56C6A2D231E993AA6D2E6465E1,
	U3CU3Ec_U3C_cctorU3Eb__11_1_mCF36865D4A819F0C0DDA987F30077434E56BC826,
	U3CU3Ec_U3C_cctorU3Eb__11_2_mBA773F3E344C145FE939B19C5EE690F9FEC60716,
	NULL,
	NULL,
	DefaultPropertyBagInitializer_Initialize_m8BA3D4950B9C07EF2A0978A88D908FCF297BD080,
	ColorPropertyBag__ctor_m695D2E7503EA97F7BB7252A882546C1C8B11D1BC,
	RProperty_get_Name_m768BEB2E8FA1995F41236DBA5C68C41FB75510BA,
	RProperty__ctor_m5B48CD3CF7C1DD802022B9AF99DAEEAB2A25B430,
	GProperty_get_Name_mB1B8C4B15616728B9B97AE53213189FBC4E2420B,
	GProperty__ctor_m2E28D044AC6B8D72EA0595E1290599DC5F91EC35,
	BProperty_get_Name_m43E6F7094BC817269A225E688C418557587C23D2,
	BProperty__ctor_m3FA7E12A9E2B2B2AFFF6FC5C96AE4EB5BF9CCFA8,
	AProperty_get_Name_m82413DC018A41D152B3574D620AC576392D913A5,
	AProperty__ctor_mAED58387B9563DED80A12595C6A11B5B2BEA6D2A,
	Vector2PropertyBag__ctor_mF30206E01ECFEDF6726878BEDA3DA53A2D24D00F,
	XProperty_get_Name_m0C83B1AD112C546FBAAE21B64AA11F723AC30FE5,
	XProperty__ctor_m21350D1E93A69FD0D148521382FD3B438C450692,
	YProperty_get_Name_m6B355E40C67C04DD438964ED91CCCFEC87863BEC,
	YProperty__ctor_mB32B0091E33FBD963435FB9973DB0DD000C3FD5C,
	Vector3PropertyBag__ctor_mC00E39D196C789D68E6F910494E433E729E0E4B6,
	XProperty_get_Name_m57E924B79868E3450299B50CF2E86F6EC4D2DB9D,
	XProperty__ctor_m46FEC277D299B82A75C8C20B0AEEACC479E3AEEB,
	YProperty_get_Name_mEC582AC60E11C6187A86D3EB8E90DA232D654D8D,
	YProperty__ctor_m4269E5BB6FCDFD8F849755DCA434EE9ACF9BE2E5,
	ZProperty_get_Name_m2F0978F5D89C0A4269B629FF604403D5C6AB7E18,
	ZProperty__ctor_mE1AC4D86341B82F2792FD53417D5ABE36E37B10E,
	Vector4PropertyBag__ctor_m6B7DFA6F651C0DC6522B1AB7E179752BA836AE10,
	XProperty_get_Name_m17A1AA4B30199BAEF0B7072526E81C2B30014B4A,
	XProperty__ctor_mBE4223E008541E2DD13ADC1707FBB73E51631CBD,
	YProperty_get_Name_m0D5C29377B95EB977E61764CDD215197591DFFEA,
	YProperty__ctor_m26384F8C7D674046A6311567F6BD57C05C1E508A,
	ZProperty_get_Name_mC39EB7495554C0D98CCB62566565F09D1E731124,
	ZProperty__ctor_mBE30B28C0B1F18C02DAEBD5354086C5E7412C795,
	WProperty_get_Name_m16E79ED0A622375BCAE1FB7ACF221391A9849A10,
	WProperty__ctor_mE6F8FD652CBFF130392D33791BD2961B09A2EFF4,
	Vector2IntPropertyBag__ctor_mA5E766EC29B1E37CAA759CBB09997D25FAFB0631,
	XProperty_get_Name_m6E8366D6ADA17D3A95511FE12147584A26159AFE,
	XProperty__ctor_m850B5684A9419E1A388FA41A041AD667EB1B2589,
	YProperty_get_Name_m27BEC9727316585FA4BF59F19B53ECC6DC458318,
	YProperty__ctor_mC8B0BA567D3BED03017F65512C6DE795C7F7558B,
	Vector3IntPropertyBag__ctor_mECB1D0CC81ACF263C6C0D126BB285A11CBACB2BC,
	XProperty_get_Name_m214358C5224F9EB07B17E4BACEAE9079B50D815C,
	XProperty__ctor_m8A1CCE2C8F5179029876BD678965D6C211CB332C,
	YProperty_get_Name_m2C54CF468EE63E89D8B301BBB2D5FD02F19101D4,
	YProperty__ctor_m2233FD190B553BFBD445F8A2EE6BE3DDF0047DBC,
	ZProperty_get_Name_m2A323942BFFED99C2269258DBB195A8C75EC7032,
	ZProperty__ctor_mBFB49B0AD7F5606851D8E8B29D0F3AC93B0E0FA7,
	RectPropertyBag__ctor_m4C5D1C260902338AC90DB837C39FE28695ED0110,
	XProperty_get_Name_m4847A49D2AA9C51AFF775ECDF8923AE2D9287C99,
	XProperty__ctor_mB5A8967CB19EE4DFE4ECEBEB88D0E1E7B0E6A534,
	YProperty_get_Name_m2ECB38AAD0B3AF556F16411B19A6E8051C7E6B2A,
	YProperty__ctor_m06A2AAA9690D1D2BFC24B0EB360DB3585A877441,
	WidthProperty_get_Name_m418258980F9849C8B8974F1BFFE3B81A5A29205D,
	WidthProperty__ctor_m94F29A6177829CC11F530347605BB4592547C72D,
	HeightProperty_get_Name_mACC36167E0E725402D144CF52B5315FD01E17A3D,
	HeightProperty__ctor_m07DCB5A7DF246055CB471EF52A8B0C909C222F2E,
	RectIntPropertyBag__ctor_mFFAC4C410B920C26B29CB6A309FB7EEF90E75A4F,
	XProperty_get_Name_m6F3F6E0874574CA99F1067BC0CB937F03CBC84AB,
	XProperty__ctor_mE83AE59E6CAFA7F55F968FD2D8DBD9DCD0284632,
	YProperty_get_Name_m2AE1DE95187BBD3082B9AEE36180867B7D0A9919,
	YProperty__ctor_m484229CC408CD868ABA8D0DBE8B12BDEEE74E4C8,
	WidthProperty_get_Name_mFEF2D1077A3A5D33C9B6BE8E4576FFE459B19EA2,
	WidthProperty__ctor_m83F6E008F488F8C6DEC0A7BCCF5118221C576DCD,
	HeightProperty_get_Name_m0E2478B4BBEBFAFBB16F14DCC98730B7E79DC08F,
	HeightProperty__ctor_m9191C19D80EA3F2F645028F03A507E0D492DBB2A,
	BoundsPropertyBag__ctor_mB3B92E349452661D037A9AB2A8B6581ECFEBB111,
	CenterProperty_get_Name_mE2932C9FBF1FBC93325E98817C5D7B377DEB0CCF,
	CenterProperty__ctor_m4ECB75EAEC52B0EDDC2EE91F4F46F12A7870420A,
	ExtentsProperty_get_Name_m01B60324ABC14349CAB683087AA1617A61F428B2,
	ExtentsProperty__ctor_mC3315D3C24FA257EE106E3F6460360D65EB0DB16,
	BoundsIntPropertyBag__ctor_mE2B2BB212558FE1C221E01D8FCEC4C41B02C6BF5,
	PositionProperty_get_Name_m39AE78CF5CD19081CF1B424CE3182BE78AE55F62,
	PositionProperty__ctor_mC574247EEEC6CFC189F13B0B961BD2A9C2CBB2CD,
	SizeProperty_get_Name_m6941C93A954B441C5188389C079E699FD0CBC41C,
	SizeProperty__ctor_m45114288E626A2F23A1035BDB8420DD663FE25BB,
	SystemVersionPropertyBag__ctor_mC0D23BC1F975382571DFA70EC5B52B24083BD4AF,
	MajorProperty__ctor_m5FA801277D886789A8732CE2E86F27C49E371679,
	MajorProperty_get_Name_m95F7965B52F39B3620B82E47B30CF2893FA414EC,
	MinorProperty__ctor_mF391B7B9C95C2B48FB07127E463F0B788F9B0F29,
	MinorProperty_get_Name_mC00B9C231673E72545064F606F64027117280A09,
	BuildProperty__ctor_mD5BCC6C2F665694E7711CE8742008F6F2434BDAC,
	BuildProperty_get_Name_mE523A88F9890A84EA707B257639A18D3EC256E3A,
	RevisionProperty__ctor_m0606FE463AAA1C106582277BB0115987861BE2FC,
	RevisionProperty_get_Name_m3A7BF24E10B80CB5CD0F651215A73824BCF9D4CE,
	NULL,
	PropertyBagStore__cctor_mF5E7248419BE8AEAB2356857AE4E19759082AFBD,
	NULL,
	NULL,
	PropertyBagStore_GetPropertyBag_m90F2EBB48D60993594856358C572964C8011143E,
	ReflectedPropertyBagAttribute__ctor_m630B43FC9EA5EACF97A960B10C6A4F28E2786B10,
	NULL,
	NULL,
	ReflectedPropertyBagProvider__ctor_mF83A77394EF7205D41A784158583F084E8CE04C2,
	ReflectedPropertyBagProvider_CreatePropertyBag_m89CDB23B3D426162B7641A4BB14DB3D71FE56CB2,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	ReflectedPropertyBagProvider_GetPropertyMembers_mCCEF82F4B8D8F8416FFE7703B4C797CCA16436D5,
	ReflectedPropertyBagProvider_IsValidMember_m942BF637D7B010D3702E74009ABAA35CEF0624F2,
	ReflectedPropertyBagProvider_IsValidPropertyType_mFBA6F8CF9F5B94652229D6A151B5302C460F05BF,
	U3CU3Ec__cctor_mB26D9D572137F1C808D3D94430FC54F4BE1E2079,
	U3CU3Ec__ctor_m1B468CA26848A620321355E672EED6B1E6713535,
	U3CU3Ec_U3C_ctorU3Eb__10_0_m98A9F8B7DD2A14EBBB86606DCFE2CB988291758F,
	U3CU3Ec_U3CGetPropertyMembersU3Eb__22_0_m50914E0FCA3654CF9091BFCA37780956B2CA0136,
	U3CGetPropertyMembersU3Ed__22__ctor_mFCA3444C6665B9FD96AF049C35FAD5D1D330BBB2,
	U3CGetPropertyMembersU3Ed__22_System_IDisposable_Dispose_m9F9302585189A3F427CBA6992B1257DE344119C4,
	U3CGetPropertyMembersU3Ed__22_MoveNext_m729AF376AC4F43C28BD1BAA7792CE1D0C9C2EDB9,
	U3CGetPropertyMembersU3Ed__22_U3CU3Em__Finally1_m199A76100E9211640529676C30A85BD1060498D4,
	U3CGetPropertyMembersU3Ed__22_System_Collections_Generic_IEnumeratorU3CSystem_Reflection_MemberInfoU3E_get_Current_mB28BEAF4EB3FAB5E771DC9A6454DCE6A67741FD9,
	U3CGetPropertyMembersU3Ed__22_System_Collections_IEnumerator_Reset_mBF507A053052F0001483795394E045C4E2EB826C,
	U3CGetPropertyMembersU3Ed__22_System_Collections_IEnumerator_get_Current_mC30C3E93C92C552E330B9A3D6D239E72A0586BA0,
	U3CGetPropertyMembersU3Ed__22_System_Collections_Generic_IEnumerableU3CSystem_Reflection_MemberInfoU3E_GetEnumerator_mFB07F1A6491DB7EEA44C182930AD32C728599080,
	U3CGetPropertyMembersU3Ed__22_System_Collections_IEnumerable_GetEnumerator_mD6684E626A039A8014D140C3BEEBD24928F7D5ED,
	ReflectionUtilities_SanitizeMemberName_m00515F1FC752D0A52D90143E58D19A7D40D799AE,
};
extern void FieldMember__ctor_mA6F2BB809BD42F26804E1E9A78B80ECA6C65FF03_AdjustorThunk (void);
extern void FieldMember_get_Name_m9301F65E796E51B6F4EF0837AEB3EB9AD25293D6_AdjustorThunk (void);
extern void FieldMember_get_IsReadOnly_mF9CA4CBB2973908DA90F49DD86124FC769168FB0_AdjustorThunk (void);
extern void FieldMember_get_ValueType_m76238497CE787FD26A79341A8F3207EFF31CE63A_AdjustorThunk (void);
extern void FieldMember_GetCustomAttributes_m3A1CF3269B1FD4C659FDEA8D51CFAE003612101D_AdjustorThunk (void);
extern void PropertyMember_get_Name_mCB0EBF067D82D0871C8051116D34A7D82EBE67FB_AdjustorThunk (void);
extern void PropertyMember_get_IsReadOnly_m595190E0A259DF7A4CD106BFF16949E939F511C5_AdjustorThunk (void);
extern void PropertyMember_get_ValueType_m40F8CF0BBF20782A6C594365911716F386FE4E1A_AdjustorThunk (void);
extern void PropertyMember__ctor_m0B727AF7D87194ED8EA453CC4CF01A10563E2334_AdjustorThunk (void);
extern void PropertyMember_GetCustomAttributes_mE4214E27A5A04DF047573888EDD9590FD6C85409_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[10] = 
{
	{ 0x06000014, FieldMember__ctor_mA6F2BB809BD42F26804E1E9A78B80ECA6C65FF03_AdjustorThunk },
	{ 0x06000015, FieldMember_get_Name_m9301F65E796E51B6F4EF0837AEB3EB9AD25293D6_AdjustorThunk },
	{ 0x06000016, FieldMember_get_IsReadOnly_mF9CA4CBB2973908DA90F49DD86124FC769168FB0_AdjustorThunk },
	{ 0x06000017, FieldMember_get_ValueType_m76238497CE787FD26A79341A8F3207EFF31CE63A_AdjustorThunk },
	{ 0x06000018, FieldMember_GetCustomAttributes_m3A1CF3269B1FD4C659FDEA8D51CFAE003612101D_AdjustorThunk },
	{ 0x06000019, PropertyMember_get_Name_mCB0EBF067D82D0871C8051116D34A7D82EBE67FB_AdjustorThunk },
	{ 0x0600001A, PropertyMember_get_IsReadOnly_m595190E0A259DF7A4CD106BFF16949E939F511C5_AdjustorThunk },
	{ 0x0600001B, PropertyMember_get_ValueType_m40F8CF0BBF20782A6C594365911716F386FE4E1A_AdjustorThunk },
	{ 0x0600001C, PropertyMember__ctor_m0B727AF7D87194ED8EA453CC4CF01A10563E2334_AdjustorThunk },
	{ 0x0600001D, PropertyMember_GetCustomAttributes_mE4214E27A5A04DF047573888EDD9590FD6C85409_AdjustorThunk },
};
static const int32_t s_InvokerIndices[267] = 
{
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	10682,
	13052,
	12815,
	13052,
	13052,
	13052,
	12815,
	13052,
	10682,
	13052,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	19891,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	21355,
	20515,
	16437,
	20515,
	20515,
	0,
	20515,
	0,
	19891,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	18814,
	0,
	18814,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	12815,
	13052,
	13298,
	0,
	13298,
	21355,
	13298,
	13052,
	10682,
	13052,
	0,
	0,
	21355,
	13298,
	13052,
	13298,
	13052,
	13298,
	13052,
	13298,
	13052,
	13298,
	13298,
	13052,
	13298,
	13052,
	13298,
	13298,
	13052,
	13298,
	13052,
	13298,
	13052,
	13298,
	13298,
	13052,
	13298,
	13052,
	13298,
	13052,
	13298,
	13052,
	13298,
	13298,
	13052,
	13298,
	13052,
	13298,
	13298,
	13052,
	13298,
	13052,
	13298,
	13052,
	13298,
	13298,
	13052,
	13298,
	13052,
	13298,
	13052,
	13298,
	13052,
	13298,
	13298,
	13052,
	13298,
	13052,
	13298,
	13052,
	13298,
	13052,
	13298,
	13298,
	13052,
	13298,
	13052,
	13298,
	13298,
	13052,
	13298,
	13052,
	13298,
	13298,
	13298,
	13052,
	13298,
	13052,
	13298,
	13052,
	13298,
	13052,
	0,
	21355,
	0,
	0,
	20515,
	13298,
	0,
	0,
	13298,
	9272,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	20515,
	19891,
	19891,
	21355,
	13298,
	7736,
	8845,
	10629,
	13298,
	12815,
	13298,
	13052,
	13298,
	13052,
	13052,
	13052,
	20515,
};
static const Il2CppTokenRangePair s_rgctxIndices[49] = 
{
	{ 0x02000006, { 0, 5 } },
	{ 0x02000009, { 5, 2 } },
	{ 0x0200000D, { 8, 15 } },
	{ 0x02000012, { 23, 6 } },
	{ 0x02000013, { 29, 13 } },
	{ 0x02000014, { 44, 5 } },
	{ 0x02000015, { 49, 5 } },
	{ 0x02000018, { 54, 7 } },
	{ 0x02000019, { 61, 4 } },
	{ 0x0200001A, { 65, 5 } },
	{ 0x0200001B, { 70, 6 } },
	{ 0x0200001C, { 76, 18 } },
	{ 0x0200001D, { 94, 9 } },
	{ 0x0200001E, { 103, 6 } },
	{ 0x02000020, { 112, 8 } },
	{ 0x02000021, { 120, 5 } },
	{ 0x02000022, { 125, 5 } },
	{ 0x02000025, { 130, 12 } },
	{ 0x0200002D, { 192, 17 } },
	{ 0x02000065, { 222, 9 } },
	{ 0x0600000E, { 7, 1 } },
	{ 0x0600002D, { 42, 2 } },
	{ 0x0600004B, { 109, 3 } },
	{ 0x0600006E, { 142, 6 } },
	{ 0x06000070, { 148, 4 } },
	{ 0x06000072, { 152, 2 } },
	{ 0x06000073, { 154, 4 } },
	{ 0x06000074, { 158, 5 } },
	{ 0x06000075, { 163, 5 } },
	{ 0x06000076, { 168, 2 } },
	{ 0x06000077, { 170, 3 } },
	{ 0x06000078, { 173, 6 } },
	{ 0x06000079, { 179, 7 } },
	{ 0x0600007A, { 186, 4 } },
	{ 0x0600007C, { 190, 2 } },
	{ 0x0600008E, { 209, 2 } },
	{ 0x060000E9, { 211, 7 } },
	{ 0x060000EA, { 218, 4 } },
	{ 0x060000ED, { 231, 5 } },
	{ 0x060000F1, { 236, 7 } },
	{ 0x060000F2, { 243, 6 } },
	{ 0x060000F3, { 249, 3 } },
	{ 0x060000F4, { 252, 3 } },
	{ 0x060000F5, { 255, 3 } },
	{ 0x060000F6, { 258, 3 } },
	{ 0x060000F7, { 261, 3 } },
	{ 0x060000F8, { 264, 3 } },
	{ 0x060000F9, { 267, 3 } },
	{ 0x060000FA, { 270, 3 } },
};
extern const uint32_t g_rgctx_DelegateProperty_2_t5331A036050433E21EDE1FDEE3EEDFA62B6472C4;
extern const uint32_t g_rgctx_Property_2__ctor_m04847E65CE56B405159C6DDE72BCFF86F9C883BF;
extern const uint32_t g_rgctx_Property_2_t68E9B12BCFEBECAB0B4E5560C399DD629501088B;
extern const uint32_t g_rgctx_PropertyGetter_2_t222915A4AC4DC64DFE595AB12C66F4811289BF5F;
extern const uint32_t g_rgctx_PropertySetter_2_t93F8308648FBB6CE78B87D5E311439180EECD65F;
extern const uint32_t g_rgctx_TValue_t4379C2A6D64AA1D37094415E781EF910A816CE98;
extern const uint32_t g_rgctx_Property_2_t6003A1D86BF2008913824232EBC1A65B9244AA6F;
extern const uint32_t g_rgctx_TAttribute_tB70594628B155E59AAA7B6256C1F1F34DBD70423;
extern const uint32_t g_rgctx_ReflectedMemberProperty_2_tBE1ADA1DB047A75445E302CB56591C8B01096158;
extern const uint32_t g_rgctx_Property_2__ctor_m6D0A692DA6FCCAD3B44EB1374F7409194CB7CE50;
extern const uint32_t g_rgctx_Property_2_t40EBB65A9302889C9BD34EC3027323E6DE224B2C;
extern const uint32_t g_rgctx_TypeTraits_1_get_IsValueType_mE304A46C44A467FFCEE68C20B4FF7E38678118F0;
extern const uint32_t g_rgctx_TypeTraits_1_t91E347E71AC92A9155B16E3F209BF7DD2A0D6525;
extern const uint32_t g_rgctx_Property_2_AddAttributes_mDAFD0BFD44331F8442CA776CD8675AE5F3EB4174;
extern const uint32_t g_rgctx_Property_2_HasAttribute_TisReadOnlyAttribute_t890CAC7DF188F3F18CAED6DE8A0A03BA92BE5FB7_mD7063C906B2BD74278A8B157D13896F8606D1A6B;
extern const uint32_t g_rgctx_GetStructValueAction_tC156DADFFDD81D3570F3D8267C7311FC0B0DA002;
extern const uint32_t g_rgctx_GetStructValueAction_tC156DADFFDD81D3570F3D8267C7311FC0B0DA002;
extern const uint32_t g_rgctx_SetStructValueAction_t5050788B0C69686BA9129D27D9D9F639C8E7BB1D;
extern const uint32_t g_rgctx_SetStructValueAction_t5050788B0C69686BA9129D27D9D9F639C8E7BB1D;
extern const uint32_t g_rgctx_GetClassValueAction_t47DD65DE55011C5E561B15A42222B6453CB49A47;
extern const uint32_t g_rgctx_GetClassValueAction_t47DD65DE55011C5E561B15A42222B6453CB49A47;
extern const uint32_t g_rgctx_SetClassValueAction_t134A783F4A9C02CAD9196158B309FE95978A3864;
extern const uint32_t g_rgctx_SetClassValueAction_t134A783F4A9C02CAD9196158B309FE95978A3864;
extern const uint32_t g_rgctx_TElementU5BU5D_tFAE184E91386EF901A97616236F10AAD6688222B;
extern const uint32_t g_rgctx_TElementU5BU5D_tFAE184E91386EF901A97616236F10AAD6688222B;
extern const uint32_t g_rgctx_Array_Empty_TisTElement_t6E7495BCB5F62879AFDE69658FE2AA45B0C17D16_m56B17DBC1A1E46D5D28E6D534318207F06EEEB28;
extern const uint32_t g_rgctx_IndexedCollectionPropertyBag_2__ctor_m06ABD1DEEAE99D2FD1AFDFBA01B75299C6A53CAC;
extern const uint32_t g_rgctx_IndexedCollectionPropertyBag_2_t011B1B45676E5CC0566C8B01C6AA29F97AE8B4B7;
extern const uint32_t g_rgctx_PropertyBag_1_t00A345951C742C03544B1108FEFCC50B109A0A9C;
extern const uint32_t g_rgctx_TContainer_tDB4DA1D6EF0C419C43A246D89710278888AE969E;
extern const uint32_t g_rgctx_ContainerPropertyBag_1_tFE247E6843BF5543F9DB47D33793B727A2FD3AE4;
extern const uint32_t g_rgctx_List_1_tDEEE2ECCBF40FDE4328A89877EEA181247D8411D;
extern const uint32_t g_rgctx_List_1_Add_m64C4EFCC6488A79F045F0F6CF32ED0434A5C472A;
extern const uint32_t g_rgctx_IProperty_1_t4B78B4126F94FBDA2C9063B96BCFF3D62F789646;
extern const uint32_t g_rgctx_Dictionary_2_t9363B6D6B014FB8F50BAB5189A5F08B0059BDCFC;
extern const uint32_t g_rgctx_Dictionary_2_Add_m218F4620E2B1192A18649A497E38DF601AD65A57;
extern const uint32_t g_rgctx_IProperty_1U26_t9487AA64B58DDDEF3355A780B9E954ABDFC7FB9E;
extern const uint32_t g_rgctx_Dictionary_2_TryGetValue_m30014797192DEFE0814EDFFB26BF8ABE00FD1CC1;
extern const uint32_t g_rgctx_List_1__ctor_m292F0884AB2E2DDEDFB46075071E01066CAE25ED;
extern const uint32_t g_rgctx_Dictionary_2__ctor_mC6BDAB2F931CB4CDD76F0A80728DECB3C2D1FF17;
extern const uint32_t g_rgctx_PropertyBag_1__ctor_m70214E212BFCE25BBF691D4CA41B836EC64DE0F5;
extern const uint32_t g_rgctx_PropertyBag_1_t312704351BC14195A829B2E8D749424EBCBDDBDC;
extern const uint32_t g_rgctx_Property_2_t23B9E842F40CED4A23B153DF927AA4898B7918B2;
extern const uint32_t g_rgctx_Property_2_get_Name_m852ED3DE08342746E3C211BE96D8F80CC12120D8;
extern const uint32_t g_rgctx_Dictionary_2_t0F9AE4FBAD41E9902EA8A9F2B8465B9782B882EA;
extern const uint32_t g_rgctx_Dictionary_2__ctor_m017BBCFD659A1D6A1DC8D03791BDC486FB00A7AF;
extern const uint32_t g_rgctx_KeyValueCollectionPropertyBag_3__ctor_mA2FA31D6AA808C723DABB12459A718557D770D13;
extern const uint32_t g_rgctx_KeyValueCollectionPropertyBag_3_t4FB7BE07A947A198D92258DCBC775CC4190A6D2A;
extern const uint32_t g_rgctx_PropertyBag_1_t56FAEECC253C99070D76FD669E3FA94CEC50DF92;
extern const uint32_t g_rgctx_HashSet_1_t4115541EE1C991C8BAAD2C79C6099FF761A6ABB2;
extern const uint32_t g_rgctx_HashSet_1__ctor_m439990F4B359D0EF2C236ACD078471A1F7B28243;
extern const uint32_t g_rgctx_SetPropertyBagBase_2__ctor_m75889A8FE97737750419859F42099BBF5C338FAF;
extern const uint32_t g_rgctx_SetPropertyBagBase_2_tCB187E7D00B8B31AA3D7D04663B03E072F19C7E2;
extern const uint32_t g_rgctx_PropertyBag_1_t693A5789F880A494392E4C8DE2C312D069E69AC2;
extern const uint32_t g_rgctx_IndexedCollectionPropertyBag_2_t09E9851C98E1806338C93B3AF239998A529E456F;
extern const uint32_t g_rgctx_IndexedCollectionPropertyBag_2_InstantiateWithCount_mA71C85524DA2069960179B3342CAE42765A2C163;
extern const uint32_t g_rgctx_TList_tFD4E9B0BD28586ACFA09397434E3F676BA2CDB6E;
extern const uint32_t g_rgctx_ListElementProperty_t96D610C0230CD7B7EF6D3DABF756FABA4069F85C;
extern const uint32_t g_rgctx_ListElementProperty__ctor_m0B128E721298BAC28E4393E0480A23BB7FED20EC;
extern const uint32_t g_rgctx_PropertyBag_1__ctor_mE006A367A96DBFBB3A4AF6163DAED436BBA38081;
extern const uint32_t g_rgctx_PropertyBag_1_t0373345E84266E2F0EE99ED65057CA17E44A0B9B;
extern const uint32_t g_rgctx_ListElementProperty_t747F0A8B43946057BDD18012FAC059284F745E91;
extern const uint32_t g_rgctx_ListElementProperty_get_Index_mD339401966BC1171FDCDEDB274DF5694194F65F9;
extern const uint32_t g_rgctx_Property_2__ctor_m94CB1A99B391FEDAA63FAEF68863893C1755DE6C;
extern const uint32_t g_rgctx_Property_2_t5E8881A394EE334E9A3806EE25F6BE1ADC0D85A4;
extern const uint32_t g_rgctx_KeyValuePairProperty_tB98CCFE598145F721310AA49A04C775FB12D759A;
extern const uint32_t g_rgctx_KeyValuePairProperty__ctor_mB91620AC03FC2DEAD646A99B10E936A285BA1C5F;
extern const uint32_t g_rgctx_KeyValueCollectionPropertyBag_3_tD588A4EF05D9BEE4214BFD25898FB562916B2463;
extern const uint32_t g_rgctx_PropertyBag_1__ctor_mE60D12A79A42DB451B60D57525AD4CFA4EA0314B;
extern const uint32_t g_rgctx_PropertyBag_1_tBB33500E9E1424DE675155AFBDFE4D36BA6F24ED;
extern const uint32_t g_rgctx_KeyValuePairProperty_get_Key_mEE18C0D4A40FD87C23178B883CDB02F621AE7DD2;
extern const uint32_t g_rgctx_TKey_t1EBAA40D80FE2788705E676806E8EB90C8E6479C;
extern const Il2CppRGCTXConstrainedData g_rgctx_TKey_t1EBAA40D80FE2788705E676806E8EB90C8E6479C_Object_ToString_mF8AC1EB9D85AB52EC8FD8B8BDD131E855E69673F;
extern const uint32_t g_rgctx_KeyValuePairProperty_t46E6D55B08B6969D6B61189A3FE4E33DE337BDAF;
extern const uint32_t g_rgctx_Property_2__ctor_m3AE94E1CDA7799B49DDDF8BD4BF3E8DE7B072C89;
extern const uint32_t g_rgctx_Property_2_tC43D6423917B14A47DBCC318B4CA5AB0CFA58BBA;
extern const uint32_t g_rgctx_PropertyBag_1__ctor_m753F8F7D956E0C9DB74F58E442E18DFF56861842;
extern const uint32_t g_rgctx_PropertyBag_1_tCEDC81381928D2E1BC681CCA96CDB0F047B26AFD;
extern const uint32_t g_rgctx_U3CU3Ec_tDBF368DDE3F7FD5B8711628C764A814D0EE7CCF9;
extern const uint32_t g_rgctx_U3CU3Ec_tDBF368DDE3F7FD5B8711628C764A814D0EE7CCF9;
extern const uint32_t g_rgctx_U3CU3Ec_U3C_cctorU3Eb__7_0_m716C896D2964C8F554A863DEBF951241B3C9AF8A;
extern const uint32_t g_rgctx_PropertyGetter_2_tE010D520E67AF80244B776DA9E9C684A12F32130;
extern const uint32_t g_rgctx_PropertyGetter_2__ctor_m3AF18666C3B587289C5341A90E969C5BA9F3B717;
extern const uint32_t g_rgctx_DelegateProperty_2_tD907C14A3B3B9D693A87EAB8597EFB4C96B387A9;
extern const uint32_t g_rgctx_DelegateProperty_2__ctor_mF7B39CD907E20E0380C5A2D1B1332FC98EFB29B2;
extern const uint32_t g_rgctx_PropertySetter_2_t2A64290B3A213826FEA98F0999D8855A0FC2E0F6;
extern const uint32_t g_rgctx_KeyValuePairPropertyBag_2_tF02B56E3AB2E9B394C0C8C043FC8D2F5A5673CBD;
extern const uint32_t g_rgctx_KeyValuePairPropertyBag_2_tF02B56E3AB2E9B394C0C8C043FC8D2F5A5673CBD;
extern const uint32_t g_rgctx_U3CU3Ec_U3C_cctorU3Eb__7_1_m058F382483F14575A82E7CC9A6B08747CFD07600;
extern const uint32_t g_rgctx_PropertyGetter_2_t1A4DB2F87885C38F79EB695D4303BD65D87CA4B0;
extern const uint32_t g_rgctx_PropertyGetter_2__ctor_m95D630D84EF5468E7420BDA0203AC3ED030FA6C5;
extern const uint32_t g_rgctx_DelegateProperty_2_t7E399B9CEEC5A6F319173D8BFA9F243D176231C9;
extern const uint32_t g_rgctx_DelegateProperty_2__ctor_m22B710B4C580690E1CE1DEF784BAF1C3601B8A59;
extern const uint32_t g_rgctx_PropertySetter_2_tA205B11170859ADC67AE34944029E6307F5D9DCF;
extern const uint32_t g_rgctx_U3CU3Ec_t6138271933AF145EC0F2BABFF9C70C6C78C28B17;
extern const uint32_t g_rgctx_U3CU3Ec__ctor_mE749355AB3EAC4910C0A7C40129A032882A4F0B3;
extern const uint32_t g_rgctx_U3CU3Ec_t6138271933AF145EC0F2BABFF9C70C6C78C28B17;
extern const uint32_t g_rgctx_KeyValuePair_2U26_t52CCE69BE9C44B386E273D933AB0A709B925418A;
extern const uint32_t g_rgctx_KeyValuePair_2_get_Key_m8B1878707A0A29B3E79245494FA93474079731AE;
extern const uint32_t g_rgctx_KeyValuePair_2_tAD93024D03598BE8D7808A0A2D384A0E56234E19;
extern const uint32_t g_rgctx_TKey_tAFC7305C6DF61DD04A6F679790947CFEB11B7D4A;
extern const uint32_t g_rgctx_KeyValuePair_2_get_Value_m79496BE3ADABE2A375029A32761DF03397861278;
extern const uint32_t g_rgctx_TValue_tAFD7086A798848B26092B0B3B0A500A635EA4651;
extern const uint32_t g_rgctx_List_1_t149B0617761BAED05184417FDA349E2FCCE3FB89;
extern const uint32_t g_rgctx_List_1__ctor_m371913D4ACFD6B5E86B76B1735D63AFF11D54CFA;
extern const uint32_t g_rgctx_List_1__ctor_mD59118DD1E4030DF90A6A84DAC09E4235A82379B;
extern const uint32_t g_rgctx_IndexedCollectionPropertyBag_2__ctor_m66BEC6E5439602AD2946D09C6779DE5F908C978C;
extern const uint32_t g_rgctx_IndexedCollectionPropertyBag_2_t5459B3CEAEAFA45387B1711D9CEDF54ADD634E82;
extern const uint32_t g_rgctx_PropertyBag_1_tC370C50F83899743E5CE24E5FAFB0AE7D907D870;
extern const uint32_t g_rgctx_PropertyBag_1_t5C0E1125CDA2F505FF9FFE85E46377962287C990;
extern const uint32_t g_rgctx_PropertyBagStore_AddPropertyBag_TisTContainer_tEEEB75B3D916D977A92F2E8CCC46FF99DB4E4F35_m314935B9A3F5FEAA6C263D4D3EB19139118F3A39;
extern const uint32_t g_rgctx_IPropertyBag_1_t6CAF3F9EA42226EBD00D7252DB022C9592FD554F;
extern const uint32_t g_rgctx_TContainer_tDD41E5BDFD434E31190676C56EDFF26DCF9FB837;
extern const uint32_t g_rgctx_PropertyBagStore_AddPropertyBag_TisTContainer_tDD41E5BDFD434E31190676C56EDFF26DCF9FB837_mCDBAAE121680B6FBC3274DDF0AA79D3310275327;
extern const uint32_t g_rgctx_IPropertyBag_1_t40AA0A4DCE75F707CEC1CA8C3D792361BB2252CF;
extern const uint32_t g_rgctx_ITypeVisitor_Visit_TisTContainer_tDD41E5BDFD434E31190676C56EDFF26DCF9FB837_m47B9A0718F5D2E42BB2C2CC4C6991D8805BCD61C;
extern const uint32_t g_rgctx_PropertyBag_1_tAA81961C48D1E43D2970F86456A7E3FCB6CF6D3C;
extern const uint32_t g_rgctx_PropertyBag_1_get_InstantiationKind_mFC42FCE3386CA030A0C551C59B896898C5638290;
extern const uint32_t g_rgctx_PropertyBag_1_Instantiate_m44DBA5D460B3959ED0D14E9C059DE67603F89D64;
extern const uint32_t g_rgctx_TContainer_tDD41E5BDFD434E31190676C56EDFF26DCF9FB837;
extern const uint32_t g_rgctx_SetElementProperty_t79E40D6F965BA783B8704B570ED790DCD4FD0660;
extern const uint32_t g_rgctx_SetElementProperty__ctor_m4880623660F118AB1EE6C9E1C95A03F1DD1C30FE;
extern const uint32_t g_rgctx_SetPropertyBagBase_2_t181B7653ED5BA7E17B7BEF4E0F609AF0F59A391F;
extern const uint32_t g_rgctx_PropertyBag_1__ctor_mF37A1FE082D2FC87745455812ED71C1703F76B3D;
extern const uint32_t g_rgctx_PropertyBag_1_t8FAA35709E8AFCB5FE7EE54713F0392B2E3C718C;
extern const uint32_t g_rgctx_SetElementProperty_t1A06D4A45F9D747053E6DA2162135E9EAD0FD6A5;
extern const uint32_t g_rgctx_TElement_tE40EA2FE63703FE7EEEEB69B610B387016B79F9C;
extern const Il2CppRGCTXConstrainedData g_rgctx_TElement_tE40EA2FE63703FE7EEEEB69B610B387016B79F9C_Object_ToString_mF8AC1EB9D85AB52EC8FD8B8BDD131E855E69673F;
extern const uint32_t g_rgctx_Property_2__ctor_mE3EBAD76553C55764B535FE55FAEC0F8F617D88F;
extern const uint32_t g_rgctx_Property_2_t7475300F6DC6084C954B015A39C9DADBBD7DB2B9;
extern const uint32_t g_rgctx_TypeTraits_1_t31E0BD871531FA1AC46F0126260E83EF64BD836B;
extern const uint32_t g_rgctx_TypeTraits_1_t31E0BD871531FA1AC46F0126260E83EF64BD836B;
extern const uint32_t g_rgctx_T_t48AA913337386250BA48723BBEE6077769EFA7FD;
extern const uint32_t g_rgctx_TypeTraits_1_get_IsEnum_m7D53C75A10F62858CC77984700E921EE5AF7A001;
extern const uint32_t g_rgctx_TypeTraits_1_get_IsArray_m9C2FCB33DB3F34CF024D3EE58DFEE94684BDC082;
extern const uint32_t g_rgctx_TypeTraits_1_get_IsValueType_mA43E543EB3D222A763CCF1D5F7BF34A4246FB81D;
extern const uint32_t g_rgctx_TypeTraits_1_get_IsPrimitive_m61B4CDEBBFD2CFD541A693559B4F5E8AA890B761;
extern const uint32_t g_rgctx_TypeTraits_1_get_IsString_m14497DB237A4D2A7D3E5140670FC6E3F2549317A;
extern const uint32_t g_rgctx_TypeTraits_1_get_IsAbstract_mD5C6ABD7CE0ED2A0F4FE70BE78A0D903F4AA1E6D;
extern const uint32_t g_rgctx_TypeTraits_1_get_IsInterface_m3E9A5A42D5808CDDFCC5EFED5F402AB060218A60;
extern const uint32_t g_rgctx_TypeTraits_1_get_CanBeNull_mB93AC7C7835096F0C85B21D3F248F5A29FC51D60;
extern const uint32_t g_rgctx_TypeTraits_1_get_IsNullable_m9E5868FD357433D10471072DA30BBB142FAF3924;
extern const uint32_t g_rgctx_TypeConstructor_1_t25947538CE97E8474D0C33762D95D5823DBEFCB2;
extern const uint32_t g_rgctx_TypeConstructor_1__ctor_m39AD076368DBD3ACF6EECBAD4CFF37CF4297F040;
extern const uint32_t g_rgctx_Cache_1_t95E3A01444791F8FADC671EC90DCD730D147E81C;
extern const uint32_t g_rgctx_ITypeConstructor_1_t59C7502128DE494B9BC5F2BC13E7CDC9ACEE9DB3;
extern const uint32_t g_rgctx_Cache_1_t95E3A01444791F8FADC671EC90DCD730D147E81C;
extern const uint32_t g_rgctx_T_t885C84AE39D9F3A21088DDA238CA805F96DDCB8A;
extern const uint32_t g_rgctx_Cache_1_t00C8746060734F8889697EC085D2784942404433;
extern const uint32_t g_rgctx_ITypeConstructor_1_t31280538EF37597F328D24147C8AD70E927BC057;
extern const uint32_t g_rgctx_Cache_1_t00C8746060734F8889697EC085D2784942404433;
extern const uint32_t g_rgctx_TypeUtility_CreateTypeConstructor_TisT_tD62682AAA81A72D9311622EF85F145E801F9E6ED_m33501750F84E9B9DDA21EA22D941027E78BDA556;
extern const uint32_t g_rgctx_TypeUtility_GetTypeConstructor_TisT_t4CBEF85EE68546B72E0DE2D577CC0813FC5FF241_m9A7C74097B8D18F4626C3EFD359ABF73A99796D8;
extern const uint32_t g_rgctx_ITypeConstructor_1_t4295694AE9AD3084F128350CB4FF21620F26B27A;
extern const uint32_t g_rgctx_TypeUtility_GetTypeConstructor_TisT_t088C8F64C87F68C20545373FD11AF5F1F8C4F329_m1D72B8CE599D16AB8FD598D691C49D7A980B1F24;
extern const uint32_t g_rgctx_ITypeConstructor_1_t093BE0148C51B37AA96EF14B233DE7729E763EDA;
extern const uint32_t g_rgctx_Func_1_tF7ACD610530B48BEDE08AF397A9B01CFB1DCCA1E;
extern const uint32_t g_rgctx_ITypeConstructor_1_SetExplicitConstructor_m8B79A19736C0ED18F2BA2937F4B53A6C1DDFFEBB;
extern const uint32_t g_rgctx_TypeUtility_GetTypeConstructor_TisT_t7F735B1DDC2B091EDC358DEEF375CD6116C636EE_m175B20AF9CED5881B0462A2A843D65ABE50E66D6;
extern const uint32_t g_rgctx_ITypeConstructor_1_tD2876B3C9E133511E9EB6487CFBD886474C105EB;
extern const uint32_t g_rgctx_TypeUtility_CheckCanBeInstantiated_TisT_t7F735B1DDC2B091EDC358DEEF375CD6116C636EE_m266B53203A368B99D9A6833522FE9D1AE452F3E7;
extern const uint32_t g_rgctx_ITypeConstructor_1_Instantiate_mAC5E67FCE18E66FE80F9EB808734444C968FDFAD;
extern const uint32_t g_rgctx_T_t7F735B1DDC2B091EDC358DEEF375CD6116C636EE;
extern const uint32_t g_rgctx_TypeUtility_GetTypeConstructor_TisT_t82DD230CE8580C6DC3CD4CBEFFC184EADB0B7A95_mD989D217F7711886C34B8084669F1FCCD9F1C49A;
extern const uint32_t g_rgctx_ITypeConstructor_1_t81CA2BF5819B72272537BDE3820324B8FCBF0A53;
extern const uint32_t g_rgctx_TU26_t95472692F5F5B2AF22A23D65D349B8FA162EBEB7;
extern const uint32_t g_rgctx_ITypeConstructor_1_Instantiate_m61BAEE1045C9A6CDF1579D8FF5E81C17C6C85486;
extern const uint32_t g_rgctx_T_t82DD230CE8580C6DC3CD4CBEFFC184EADB0B7A95;
extern const uint32_t g_rgctx_T_t263F6929F47D144546F78710E99F878E6A286B45;
extern const uint32_t g_rgctx_T_t263F6929F47D144546F78710E99F878E6A286B45;
extern const uint32_t g_rgctx_T_tABFEBC08B2979D6B63177B9428E7CA01DCC3E3B4;
extern const uint32_t g_rgctx_TU26_t9ECA9CE3792B53D488C061AD5CBCE0C20DAFA21D;
extern const uint32_t g_rgctx_T_tABFEBC08B2979D6B63177B9428E7CA01DCC3E3B4;
extern const uint32_t g_rgctx_PropertyBagStore_GetPropertyBag_TisTArray_tCC895BE754673F9F64E3B106535BF9692E92AC48_m3FC89B867956A29667F38A9DF3AC0F227B1677FB;
extern const uint32_t g_rgctx_IPropertyBag_1_t94574374F5B1EE729FB39ECD1FAA1C2CFD1CCAF0;
extern const uint32_t g_rgctx_IConstructorWithCount_1_t55161DE9FBEAF6B1C1DB6549C88ED71176F8A552;
extern const uint32_t g_rgctx_IConstructorWithCount_1_InstantiateWithCount_m03B785A4E75E027EB572F5E2D98F70E878BB1972;
extern const uint32_t g_rgctx_TArray_tCC895BE754673F9F64E3B106535BF9692E92AC48;
extern const uint32_t g_rgctx_TArray_tCC895BE754673F9F64E3B106535BF9692E92AC48;
extern const uint32_t g_rgctx_TArrayU26_t6A504C9408395A4EC34BF0F8DCD3F6518AC8D931;
extern const uint32_t g_rgctx_TArray_tB79A9A6E8211AFCF57D3807AEBFC7AF87B0057A1;
extern const uint32_t g_rgctx_PropertyBagStore_GetPropertyBag_TisTArray_tB79A9A6E8211AFCF57D3807AEBFC7AF87B0057A1_m27AE8FA861F014A2323586E1D17ADB7A14392538;
extern const uint32_t g_rgctx_IPropertyBag_1_t38C5F60F5314A765517D5561678B9D4A7BE7779A;
extern const uint32_t g_rgctx_IConstructorWithCount_1_tCFDBEDE2DCBAFB75DBE301E3AFFF714BCC913F7A;
extern const uint32_t g_rgctx_IConstructorWithCount_1_InstantiateWithCount_m353D34809F1F5D6312D31ACAAA4881C31ADCAE05;
extern const uint32_t g_rgctx_TArray_tB79A9A6E8211AFCF57D3807AEBFC7AF87B0057A1;
extern const uint32_t g_rgctx_IConstructorWithCount_1_t9DFD44199E942F47328DCE32F4E6A89DE2713592;
extern const uint32_t g_rgctx_IConstructorWithCount_1_InstantiateWithCount_mB226AD82B7BDD8B92EC1C6E1F47C182DD10E5449;
extern const uint32_t g_rgctx_TArray_t386A802729FA5C7DB8F552663ADCDD7E1551080E;
extern const uint32_t g_rgctx_TArray_t386A802729FA5C7DB8F552663ADCDD7E1551080E;
extern const uint32_t g_rgctx_ITypeConstructor_1_t46DEBA6B13E0AEC5216C58BC26F3FA9921D9D9F5;
extern const uint32_t g_rgctx_T_t61934B9AD04128C12857D96350D039B585F477E8;
extern const uint32_t g_rgctx_TypeConstructor_1_t64D12FC4EB18B55786777CE2882F1D3D63510C66;
extern const uint32_t g_rgctx_Func_1_tDA05F49283791DE14D6E76D1DF7B57A7AAE544FA;
extern const uint32_t g_rgctx_IConstructor_1_t4EFA9443073BEDF7E0B2D7F1DDDEAA7B2FB2BE43;
extern const uint32_t g_rgctx_PropertyBagStore_GetPropertyBag_TisT_t37C62C3199851F00BDAC493E561783D14BAD4A68_m5AF20DA8BB092B1917CA662538784BBA9D9525A2;
extern const uint32_t g_rgctx_IPropertyBag_1_tE302AA5F2105A288FFE536BB5656AC20C0B19B82;
extern const uint32_t g_rgctx_TypeConstructor_1_SetImplicitConstructor_mD0BBE3B528E912D4B0233B208EB2443B04663F8C;
extern const uint32_t g_rgctx_T_t37C62C3199851F00BDAC493E561783D14BAD4A68;
extern const uint32_t g_rgctx_TypeConstructor_1_CreateValueTypeInstance_m95DA6F1F6FFDB09B992AEB6F6B6572F5E7FF02AF;
extern const uint32_t g_rgctx_Func_1__ctor_m68287EB0565E61F3A58134261D8DBD475C925B99;
extern const uint32_t g_rgctx_TypeConstructor_1_CreateScriptableObjectInstance_m2E1C5218B78308AD2652E810047EE4DF7DF6AFDE;
extern const uint32_t g_rgctx_TypeConstructor_1_CreateClassInstance_m323DBA74E6FFE04C34246277C6E0DD0189981449;
extern const uint32_t g_rgctx_T_t37C62C3199851F00BDAC493E561783D14BAD4A68;
extern const uint32_t g_rgctx_Activator_CreateInstance_TisT_t37C62C3199851F00BDAC493E561783D14BAD4A68_mF32DEEFD38B493CF0B65F499AAA66FC530031FFD;
extern const uint32_t g_rgctx_Func_1_Invoke_mF8A6CDB276750B3A6358C7826AD7AE31A48BA1C4;
extern const uint32_t g_rgctx_IConstructor_1_Instantiate_m5E4AB45A8C5C14BFAB935DCFFC8AC009E9F7DC61;
extern const uint32_t g_rgctx_ITypeConstructor_1_tDFB7AB319A5BCA0BDB1F09FB928689350FACC901;
extern const uint32_t g_rgctx_ITypeConstructor_1_Instantiate_m8ED0004CDD9ACACC624288227E26723206DC946A;
extern const uint32_t g_rgctx_TypeUtility_CreateTypeConstructor_TisTContainer_tC36F00479677E34F7A594AE0941C2A3A0CDE78A9_mAB77701AC7B61EBBB9377A5C766F10CFEAB8AF37;
extern const uint32_t g_rgctx_ITypeConstructor_1_t3B3C0683D2B31C558E6FE4B45406B7877BA987C4;
extern const uint32_t g_rgctx_TypeTraits_1_get_IsContainer_m542A5FF42F8965E2F1E6BDA125885D649D270FAA;
extern const uint32_t g_rgctx_TypeTraits_1_tDBA22CC817797D99C38F8A27587091E7D9D5D34E;
extern const uint32_t g_rgctx_TContainer_tBD4CBAFBFFD0739E77E5478196D2073D3B88A032;
extern const uint32_t g_rgctx_TypeTraits_1_get_IsAbstractOrInterface_mCEEA653D933E8F8AFC10FF1C5B0574BD0EB0A50A;
extern const uint32_t g_rgctx_TypedStore_1_t5A7FB7070DBEC2FF9CBE919AB2D9B6471A7D069E;
extern const uint32_t g_rgctx_IPropertyBag_1_t36EC206CECA2B2BC0462B6FADB9163EF2D411949;
extern const uint32_t g_rgctx_TypedStore_1_t5A7FB7070DBEC2FF9CBE919AB2D9B6471A7D069E;
extern const uint32_t g_rgctx_TypedStore_1_t52D7C98DB11647B84077919A338B2F0E406E7F32;
extern const uint32_t g_rgctx_IPropertyBag_1_t4AAC76883AAF55D5E256B9F7F4EF8C6E8CAB5097;
extern const uint32_t g_rgctx_TypedStore_1_t52D7C98DB11647B84077919A338B2F0E406E7F32;
extern const uint32_t g_rgctx_TContainer_t93F87C792617812E0497030A8E9743696A582508;
extern const uint32_t g_rgctx_TContainer_tBA215EB9698B59359CC8C9BF2B8B633F8E1225BB;
extern const uint32_t g_rgctx_ContainerPropertyBag_1_TryGetProperty_m8D97B3D6DC58B98915B3E11C706FC2EBE0429C58;
extern const uint32_t g_rgctx_TContainerU26_t2CA3EDA14F09157E09242942899AE74176048753;
extern const uint32_t g_rgctx_IProperty_1U26_t79F6B52531B6F25719A0055381A2940A6B1B7CD6;
extern const uint32_t g_rgctx_TContainer_tBA215EB9698B59359CC8C9BF2B8B633F8E1225BB;
extern const uint32_t g_rgctx_IProperty_1_t44D73C0034DE915ED4D4F755EDDCA9F75FE24BDE;
extern const uint32_t g_rgctx_ContainerPropertyBag_1__ctor_m0DB3E38E743C1CE31BAC0A5E5D42C24811EA03E6;
extern const uint32_t g_rgctx_ContainerPropertyBag_1_t3806CC6DCDFF60FB3F69045D743279FFBB26C3D5;
extern const uint32_t g_rgctx_PropertyBag_1_tFBC77D17C7A43620E37345E9A7540FB9B291C2E9;
extern const uint32_t g_rgctx_Property_2_t675504D339AE5B9908B3E512C0FFB0BB218DA7DE;
extern const uint32_t g_rgctx_Property_2_get_Name_mDCD05C6DAC5D208E12B9CAD548F09B9E9F8DF3BD;
extern const uint32_t g_rgctx_TValue_tE132A1A6FDE9C35A83669F9DEAD0AA19A5C3E355;
extern const uint32_t g_rgctx_Property_2_DeclaredValueType_mED6F2989213C202FA6EB47440DD236C46949D587;
extern const uint32_t g_rgctx_ContainerPropertyBag_1_AddProperty_TisTValue_tE132A1A6FDE9C35A83669F9DEAD0AA19A5C3E355_mFE8D7BD6E54E7161F35830A582D2AB5FF061795E;
extern const uint32_t g_rgctx_TypeTraits_1_get_IsContainer_m4CE86C3DC694B7FF97A8CE182CDF8D45EB95C276;
extern const uint32_t g_rgctx_TypeTraits_1_t483FC0F4C89BF4626152F4939487D87526B38F6E;
extern const uint32_t g_rgctx_TypeTraits_1_get_IsObject_mD95DF9C13A4929EB8ACF3595EFEA01BE87564CAE;
extern const uint32_t g_rgctx_TContainer_t09FED92FC5C09423AF54F257DF9B569866D5F64A;
extern const uint32_t g_rgctx_IPropertyBag_1_t5C88FC124F8234DC1FFAC334A2386087A8AB7133;
extern const uint32_t g_rgctx_ReflectedPropertyBag_1_tB5150C5F8355ACF7C232C64112C6C5604AE327EE;
extern const uint32_t g_rgctx_ReflectedPropertyBag_1__ctor_mDC8019356BDEB3DE10C010703BD27822E4392B02;
extern const uint32_t g_rgctx_TValue_tCA8A6436749BF2758699B74AB52BBA768DC74E50;
extern const uint32_t g_rgctx_ReflectedPropertyBag_1_tD6C5D734961A52A58E99B01D9452268288AD7BC7;
extern const uint32_t g_rgctx_ReflectedMemberProperty_2_t4AEC0F6E462155A02E89A2C7B9341756E332AC78;
extern const uint32_t g_rgctx_ReflectedMemberProperty_2__ctor_mED73332B00D6C761553A67BDC996FD67468B1B58;
extern const uint32_t g_rgctx_ReflectedPropertyBag_1_AddProperty_TisTValue_tCA8A6436749BF2758699B74AB52BBA768DC74E50_mA8878FE5D07587C20DE0948717D18ADF3F47E583;
extern const uint32_t g_rgctx_Property_2_tFE207589AF8AC1C21C648655309188722045DEEE;
extern const uint32_t g_rgctx_IndexedCollectionPropertyBag_2_t5F2100B58C1521310877B18F1F1F515336AD6FAF;
extern const uint32_t g_rgctx_IndexedCollectionPropertyBag_2__ctor_m82E4C06BA20030E7631673A848FF63B52CC1CEA8;
extern const uint32_t g_rgctx_IPropertyBag_1_t23DBDEE2BCE0E68D7B9C6EA248F41D6EE8B3805D;
extern const uint32_t g_rgctx_SetPropertyBagBase_2_tF2E25B0D19FEEAED6844B5649372AD0A2C0FCEFA;
extern const uint32_t g_rgctx_SetPropertyBagBase_2__ctor_m6A7DAD0B3D7BB886C3E43A5AAD438D97AD29B077;
extern const uint32_t g_rgctx_IPropertyBag_1_t4B3966194D154621A18F1AE50F6E1923E366381D;
extern const uint32_t g_rgctx_KeyValueCollectionPropertyBag_3_t93D99D472E9FA6C51CA7FBCFC34CB24965942D16;
extern const uint32_t g_rgctx_KeyValueCollectionPropertyBag_3__ctor_mB47F057C9F36544865ED0957DF1FF2AB483D3611;
extern const uint32_t g_rgctx_IPropertyBag_1_t3F67556433ADE497021D747E5D3F187BC26601A1;
extern const uint32_t g_rgctx_KeyValuePairPropertyBag_2_tB3FF046E02FD691A976CF2B299679AD64C87767F;
extern const uint32_t g_rgctx_KeyValuePairPropertyBag_2__ctor_mDA6FE7408210F3A83DD3B2CF8564B11C79D056EF;
extern const uint32_t g_rgctx_IPropertyBag_1_tAF262B3E2465C83CE689DF8E4C1A4A7A261AC925;
extern const uint32_t g_rgctx_ArrayPropertyBag_1_tE3FBBDE2CC3697A21876F04235D9AE37A272D4B0;
extern const uint32_t g_rgctx_ArrayPropertyBag_1__ctor_mF63DAC415DB047B3D3A4E3869378EA4864A4DE46;
extern const uint32_t g_rgctx_IPropertyBag_1_t657B355FBEDB15F7FA5B588D1FAB230347BF7EF3;
extern const uint32_t g_rgctx_ListPropertyBag_1_tFCC1D761DCDF7AFAB616F4D86E12EF7ED37D34FD;
extern const uint32_t g_rgctx_ListPropertyBag_1__ctor_mB869069D7AFEB50CBC016515F9FBCC1D95FB39A3;
extern const uint32_t g_rgctx_IPropertyBag_1_t63AFE4A36EC9A87D882FB104DC77CD9E05ECC470;
extern const uint32_t g_rgctx_HashSetPropertyBag_1_tE0468D1841DDB27942294737E6F86EA10DEA0B7F;
extern const uint32_t g_rgctx_HashSetPropertyBag_1__ctor_m7E808D7251C1DB56B86FC79BE6FDF989CCB9B8F4;
extern const uint32_t g_rgctx_IPropertyBag_1_t9A9A3F2EED1278CCA08BABB9DC60DE6B410BF874;
extern const uint32_t g_rgctx_DictionaryPropertyBag_2_tE606638F1CF524214E9C65398357242A53E1938B;
extern const uint32_t g_rgctx_DictionaryPropertyBag_2__ctor_m2AF546381FFCE3F5CD211F82CE8923EBB52A73F9;
extern const uint32_t g_rgctx_IPropertyBag_1_tA3E85A9DBE91225B32BC663975BD5BA2D37FC36E;
static const Il2CppRGCTXDefinition s_rgctxValues[273] = 
{
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_DelegateProperty_2_t5331A036050433E21EDE1FDEE3EEDFA62B6472C4 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Property_2__ctor_m04847E65CE56B405159C6DDE72BCFF86F9C883BF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Property_2_t68E9B12BCFEBECAB0B4E5560C399DD629501088B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_PropertyGetter_2_t222915A4AC4DC64DFE595AB12C66F4811289BF5F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_PropertySetter_2_t93F8308648FBB6CE78B87D5E311439180EECD65F },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TValue_t4379C2A6D64AA1D37094415E781EF910A816CE98 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Property_2_t6003A1D86BF2008913824232EBC1A65B9244AA6F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TAttribute_tB70594628B155E59AAA7B6256C1F1F34DBD70423 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ReflectedMemberProperty_2_tBE1ADA1DB047A75445E302CB56591C8B01096158 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Property_2__ctor_m6D0A692DA6FCCAD3B44EB1374F7409194CB7CE50 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Property_2_t40EBB65A9302889C9BD34EC3027323E6DE224B2C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TypeTraits_1_get_IsValueType_mE304A46C44A467FFCEE68C20B4FF7E38678118F0 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TypeTraits_1_t91E347E71AC92A9155B16E3F209BF7DD2A0D6525 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Property_2_AddAttributes_mDAFD0BFD44331F8442CA776CD8675AE5F3EB4174 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Property_2_HasAttribute_TisReadOnlyAttribute_t890CAC7DF188F3F18CAED6DE8A0A03BA92BE5FB7_mD7063C906B2BD74278A8B157D13896F8606D1A6B },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_GetStructValueAction_tC156DADFFDD81D3570F3D8267C7311FC0B0DA002 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_GetStructValueAction_tC156DADFFDD81D3570F3D8267C7311FC0B0DA002 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_SetStructValueAction_t5050788B0C69686BA9129D27D9D9F639C8E7BB1D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_SetStructValueAction_t5050788B0C69686BA9129D27D9D9F639C8E7BB1D },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_GetClassValueAction_t47DD65DE55011C5E561B15A42222B6453CB49A47 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_GetClassValueAction_t47DD65DE55011C5E561B15A42222B6453CB49A47 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_SetClassValueAction_t134A783F4A9C02CAD9196158B309FE95978A3864 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_SetClassValueAction_t134A783F4A9C02CAD9196158B309FE95978A3864 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TElementU5BU5D_tFAE184E91386EF901A97616236F10AAD6688222B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TElementU5BU5D_tFAE184E91386EF901A97616236F10AAD6688222B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Array_Empty_TisTElement_t6E7495BCB5F62879AFDE69658FE2AA45B0C17D16_m56B17DBC1A1E46D5D28E6D534318207F06EEEB28 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IndexedCollectionPropertyBag_2__ctor_m06ABD1DEEAE99D2FD1AFDFBA01B75299C6A53CAC },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IndexedCollectionPropertyBag_2_t011B1B45676E5CC0566C8B01C6AA29F97AE8B4B7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_PropertyBag_1_t00A345951C742C03544B1108FEFCC50B109A0A9C },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TContainer_tDB4DA1D6EF0C419C43A246D89710278888AE969E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ContainerPropertyBag_1_tFE247E6843BF5543F9DB47D33793B727A2FD3AE4 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_List_1_tDEEE2ECCBF40FDE4328A89877EEA181247D8411D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_Add_m64C4EFCC6488A79F045F0F6CF32ED0434A5C472A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IProperty_1_t4B78B4126F94FBDA2C9063B96BCFF3D62F789646 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Dictionary_2_t9363B6D6B014FB8F50BAB5189A5F08B0059BDCFC },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Dictionary_2_Add_m218F4620E2B1192A18649A497E38DF601AD65A57 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IProperty_1U26_t9487AA64B58DDDEF3355A780B9E954ABDFC7FB9E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Dictionary_2_TryGetValue_m30014797192DEFE0814EDFFB26BF8ABE00FD1CC1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1__ctor_m292F0884AB2E2DDEDFB46075071E01066CAE25ED },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Dictionary_2__ctor_mC6BDAB2F931CB4CDD76F0A80728DECB3C2D1FF17 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_PropertyBag_1__ctor_m70214E212BFCE25BBF691D4CA41B836EC64DE0F5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_PropertyBag_1_t312704351BC14195A829B2E8D749424EBCBDDBDC },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Property_2_t23B9E842F40CED4A23B153DF927AA4898B7918B2 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Property_2_get_Name_m852ED3DE08342746E3C211BE96D8F80CC12120D8 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Dictionary_2_t0F9AE4FBAD41E9902EA8A9F2B8465B9782B882EA },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Dictionary_2__ctor_m017BBCFD659A1D6A1DC8D03791BDC486FB00A7AF },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_KeyValueCollectionPropertyBag_3__ctor_mA2FA31D6AA808C723DABB12459A718557D770D13 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_KeyValueCollectionPropertyBag_3_t4FB7BE07A947A198D92258DCBC775CC4190A6D2A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_PropertyBag_1_t56FAEECC253C99070D76FD669E3FA94CEC50DF92 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_HashSet_1_t4115541EE1C991C8BAAD2C79C6099FF761A6ABB2 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_HashSet_1__ctor_m439990F4B359D0EF2C236ACD078471A1F7B28243 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SetPropertyBagBase_2__ctor_m75889A8FE97737750419859F42099BBF5C338FAF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_SetPropertyBagBase_2_tCB187E7D00B8B31AA3D7D04663B03E072F19C7E2 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_PropertyBag_1_t693A5789F880A494392E4C8DE2C312D069E69AC2 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IndexedCollectionPropertyBag_2_t09E9851C98E1806338C93B3AF239998A529E456F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IndexedCollectionPropertyBag_2_InstantiateWithCount_mA71C85524DA2069960179B3342CAE42765A2C163 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TList_tFD4E9B0BD28586ACFA09397434E3F676BA2CDB6E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ListElementProperty_t96D610C0230CD7B7EF6D3DABF756FABA4069F85C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ListElementProperty__ctor_m0B128E721298BAC28E4393E0480A23BB7FED20EC },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_PropertyBag_1__ctor_mE006A367A96DBFBB3A4AF6163DAED436BBA38081 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_PropertyBag_1_t0373345E84266E2F0EE99ED65057CA17E44A0B9B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ListElementProperty_t747F0A8B43946057BDD18012FAC059284F745E91 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ListElementProperty_get_Index_mD339401966BC1171FDCDEDB274DF5694194F65F9 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Property_2__ctor_m94CB1A99B391FEDAA63FAEF68863893C1755DE6C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Property_2_t5E8881A394EE334E9A3806EE25F6BE1ADC0D85A4 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_KeyValuePairProperty_tB98CCFE598145F721310AA49A04C775FB12D759A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_KeyValuePairProperty__ctor_mB91620AC03FC2DEAD646A99B10E936A285BA1C5F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_KeyValueCollectionPropertyBag_3_tD588A4EF05D9BEE4214BFD25898FB562916B2463 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_PropertyBag_1__ctor_mE60D12A79A42DB451B60D57525AD4CFA4EA0314B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_PropertyBag_1_tBB33500E9E1424DE675155AFBDFE4D36BA6F24ED },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_KeyValuePairProperty_get_Key_mEE18C0D4A40FD87C23178B883CDB02F621AE7DD2 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TKey_t1EBAA40D80FE2788705E676806E8EB90C8E6479C },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_TKey_t1EBAA40D80FE2788705E676806E8EB90C8E6479C_Object_ToString_mF8AC1EB9D85AB52EC8FD8B8BDD131E855E69673F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_KeyValuePairProperty_t46E6D55B08B6969D6B61189A3FE4E33DE337BDAF },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Property_2__ctor_m3AE94E1CDA7799B49DDDF8BD4BF3E8DE7B072C89 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Property_2_tC43D6423917B14A47DBCC318B4CA5AB0CFA58BBA },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_PropertyBag_1__ctor_m753F8F7D956E0C9DB74F58E442E18DFF56861842 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_PropertyBag_1_tCEDC81381928D2E1BC681CCA96CDB0F047B26AFD },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec_tDBF368DDE3F7FD5B8711628C764A814D0EE7CCF9 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec_tDBF368DDE3F7FD5B8711628C764A814D0EE7CCF9 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec_U3C_cctorU3Eb__7_0_m716C896D2964C8F554A863DEBF951241B3C9AF8A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_PropertyGetter_2_tE010D520E67AF80244B776DA9E9C684A12F32130 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_PropertyGetter_2__ctor_m3AF18666C3B587289C5341A90E969C5BA9F3B717 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_DelegateProperty_2_tD907C14A3B3B9D693A87EAB8597EFB4C96B387A9 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_DelegateProperty_2__ctor_mF7B39CD907E20E0380C5A2D1B1332FC98EFB29B2 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_PropertySetter_2_t2A64290B3A213826FEA98F0999D8855A0FC2E0F6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_KeyValuePairPropertyBag_2_tF02B56E3AB2E9B394C0C8C043FC8D2F5A5673CBD },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_KeyValuePairPropertyBag_2_tF02B56E3AB2E9B394C0C8C043FC8D2F5A5673CBD },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec_U3C_cctorU3Eb__7_1_m058F382483F14575A82E7CC9A6B08747CFD07600 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_PropertyGetter_2_t1A4DB2F87885C38F79EB695D4303BD65D87CA4B0 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_PropertyGetter_2__ctor_m95D630D84EF5468E7420BDA0203AC3ED030FA6C5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_DelegateProperty_2_t7E399B9CEEC5A6F319173D8BFA9F243D176231C9 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_DelegateProperty_2__ctor_m22B710B4C580690E1CE1DEF784BAF1C3601B8A59 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_PropertySetter_2_tA205B11170859ADC67AE34944029E6307F5D9DCF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec_t6138271933AF145EC0F2BABFF9C70C6C78C28B17 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec__ctor_mE749355AB3EAC4910C0A7C40129A032882A4F0B3 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec_t6138271933AF145EC0F2BABFF9C70C6C78C28B17 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_KeyValuePair_2U26_t52CCE69BE9C44B386E273D933AB0A709B925418A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_KeyValuePair_2_get_Key_m8B1878707A0A29B3E79245494FA93474079731AE },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_KeyValuePair_2_tAD93024D03598BE8D7808A0A2D384A0E56234E19 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TKey_tAFC7305C6DF61DD04A6F679790947CFEB11B7D4A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_KeyValuePair_2_get_Value_m79496BE3ADABE2A375029A32761DF03397861278 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TValue_tAFD7086A798848B26092B0B3B0A500A635EA4651 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_List_1_t149B0617761BAED05184417FDA349E2FCCE3FB89 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1__ctor_m371913D4ACFD6B5E86B76B1735D63AFF11D54CFA },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1__ctor_mD59118DD1E4030DF90A6A84DAC09E4235A82379B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IndexedCollectionPropertyBag_2__ctor_m66BEC6E5439602AD2946D09C6779DE5F908C978C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IndexedCollectionPropertyBag_2_t5459B3CEAEAFA45387B1711D9CEDF54ADD634E82 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_PropertyBag_1_tC370C50F83899743E5CE24E5FAFB0AE7D907D870 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_PropertyBag_1_t5C0E1125CDA2F505FF9FFE85E46377962287C990 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_PropertyBagStore_AddPropertyBag_TisTContainer_tEEEB75B3D916D977A92F2E8CCC46FF99DB4E4F35_m314935B9A3F5FEAA6C263D4D3EB19139118F3A39 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IPropertyBag_1_t6CAF3F9EA42226EBD00D7252DB022C9592FD554F },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TContainer_tDD41E5BDFD434E31190676C56EDFF26DCF9FB837 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_PropertyBagStore_AddPropertyBag_TisTContainer_tDD41E5BDFD434E31190676C56EDFF26DCF9FB837_mCDBAAE121680B6FBC3274DDF0AA79D3310275327 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IPropertyBag_1_t40AA0A4DCE75F707CEC1CA8C3D792361BB2252CF },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ITypeVisitor_Visit_TisTContainer_tDD41E5BDFD434E31190676C56EDFF26DCF9FB837_m47B9A0718F5D2E42BB2C2CC4C6991D8805BCD61C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_PropertyBag_1_tAA81961C48D1E43D2970F86456A7E3FCB6CF6D3C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_PropertyBag_1_get_InstantiationKind_mFC42FCE3386CA030A0C551C59B896898C5638290 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_PropertyBag_1_Instantiate_m44DBA5D460B3959ED0D14E9C059DE67603F89D64 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TContainer_tDD41E5BDFD434E31190676C56EDFF26DCF9FB837 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_SetElementProperty_t79E40D6F965BA783B8704B570ED790DCD4FD0660 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SetElementProperty__ctor_m4880623660F118AB1EE6C9E1C95A03F1DD1C30FE },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_SetPropertyBagBase_2_t181B7653ED5BA7E17B7BEF4E0F609AF0F59A391F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_PropertyBag_1__ctor_mF37A1FE082D2FC87745455812ED71C1703F76B3D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_PropertyBag_1_t8FAA35709E8AFCB5FE7EE54713F0392B2E3C718C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_SetElementProperty_t1A06D4A45F9D747053E6DA2162135E9EAD0FD6A5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TElement_tE40EA2FE63703FE7EEEEB69B610B387016B79F9C },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_TElement_tE40EA2FE63703FE7EEEEB69B610B387016B79F9C_Object_ToString_mF8AC1EB9D85AB52EC8FD8B8BDD131E855E69673F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Property_2__ctor_mE3EBAD76553C55764B535FE55FAEC0F8F617D88F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Property_2_t7475300F6DC6084C954B015A39C9DADBBD7DB2B9 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TypeTraits_1_t31E0BD871531FA1AC46F0126260E83EF64BD836B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TypeTraits_1_t31E0BD871531FA1AC46F0126260E83EF64BD836B },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_t48AA913337386250BA48723BBEE6077769EFA7FD },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TypeTraits_1_get_IsEnum_m7D53C75A10F62858CC77984700E921EE5AF7A001 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TypeTraits_1_get_IsArray_m9C2FCB33DB3F34CF024D3EE58DFEE94684BDC082 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TypeTraits_1_get_IsValueType_mA43E543EB3D222A763CCF1D5F7BF34A4246FB81D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TypeTraits_1_get_IsPrimitive_m61B4CDEBBFD2CFD541A693559B4F5E8AA890B761 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TypeTraits_1_get_IsString_m14497DB237A4D2A7D3E5140670FC6E3F2549317A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TypeTraits_1_get_IsAbstract_mD5C6ABD7CE0ED2A0F4FE70BE78A0D903F4AA1E6D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TypeTraits_1_get_IsInterface_m3E9A5A42D5808CDDFCC5EFED5F402AB060218A60 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TypeTraits_1_get_CanBeNull_mB93AC7C7835096F0C85B21D3F248F5A29FC51D60 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TypeTraits_1_get_IsNullable_m9E5868FD357433D10471072DA30BBB142FAF3924 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TypeConstructor_1_t25947538CE97E8474D0C33762D95D5823DBEFCB2 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TypeConstructor_1__ctor_m39AD076368DBD3ACF6EECBAD4CFF37CF4297F040 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Cache_1_t95E3A01444791F8FADC671EC90DCD730D147E81C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ITypeConstructor_1_t59C7502128DE494B9BC5F2BC13E7CDC9ACEE9DB3 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Cache_1_t95E3A01444791F8FADC671EC90DCD730D147E81C },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_t885C84AE39D9F3A21088DDA238CA805F96DDCB8A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Cache_1_t00C8746060734F8889697EC085D2784942404433 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ITypeConstructor_1_t31280538EF37597F328D24147C8AD70E927BC057 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Cache_1_t00C8746060734F8889697EC085D2784942404433 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TypeUtility_CreateTypeConstructor_TisT_tD62682AAA81A72D9311622EF85F145E801F9E6ED_m33501750F84E9B9DDA21EA22D941027E78BDA556 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TypeUtility_GetTypeConstructor_TisT_t4CBEF85EE68546B72E0DE2D577CC0813FC5FF241_m9A7C74097B8D18F4626C3EFD359ABF73A99796D8 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ITypeConstructor_1_t4295694AE9AD3084F128350CB4FF21620F26B27A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TypeUtility_GetTypeConstructor_TisT_t088C8F64C87F68C20545373FD11AF5F1F8C4F329_m1D72B8CE599D16AB8FD598D691C49D7A980B1F24 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ITypeConstructor_1_t093BE0148C51B37AA96EF14B233DE7729E763EDA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_1_tF7ACD610530B48BEDE08AF397A9B01CFB1DCCA1E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ITypeConstructor_1_SetExplicitConstructor_m8B79A19736C0ED18F2BA2937F4B53A6C1DDFFEBB },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TypeUtility_GetTypeConstructor_TisT_t7F735B1DDC2B091EDC358DEEF375CD6116C636EE_m175B20AF9CED5881B0462A2A843D65ABE50E66D6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ITypeConstructor_1_tD2876B3C9E133511E9EB6487CFBD886474C105EB },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TypeUtility_CheckCanBeInstantiated_TisT_t7F735B1DDC2B091EDC358DEEF375CD6116C636EE_m266B53203A368B99D9A6833522FE9D1AE452F3E7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ITypeConstructor_1_Instantiate_mAC5E67FCE18E66FE80F9EB808734444C968FDFAD },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t7F735B1DDC2B091EDC358DEEF375CD6116C636EE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TypeUtility_GetTypeConstructor_TisT_t82DD230CE8580C6DC3CD4CBEFFC184EADB0B7A95_mD989D217F7711886C34B8084669F1FCCD9F1C49A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ITypeConstructor_1_t81CA2BF5819B72272537BDE3820324B8FCBF0A53 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_t95472692F5F5B2AF22A23D65D349B8FA162EBEB7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ITypeConstructor_1_Instantiate_m61BAEE1045C9A6CDF1579D8FF5E81C17C6C85486 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t82DD230CE8580C6DC3CD4CBEFFC184EADB0B7A95 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_t263F6929F47D144546F78710E99F878E6A286B45 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t263F6929F47D144546F78710E99F878E6A286B45 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_tABFEBC08B2979D6B63177B9428E7CA01DCC3E3B4 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_t9ECA9CE3792B53D488C061AD5CBCE0C20DAFA21D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tABFEBC08B2979D6B63177B9428E7CA01DCC3E3B4 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_PropertyBagStore_GetPropertyBag_TisTArray_tCC895BE754673F9F64E3B106535BF9692E92AC48_m3FC89B867956A29667F38A9DF3AC0F227B1677FB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IPropertyBag_1_t94574374F5B1EE729FB39ECD1FAA1C2CFD1CCAF0 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IConstructorWithCount_1_t55161DE9FBEAF6B1C1DB6549C88ED71176F8A552 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IConstructorWithCount_1_InstantiateWithCount_m03B785A4E75E027EB572F5E2D98F70E878BB1972 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TArray_tCC895BE754673F9F64E3B106535BF9692E92AC48 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TArray_tCC895BE754673F9F64E3B106535BF9692E92AC48 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TArrayU26_t6A504C9408395A4EC34BF0F8DCD3F6518AC8D931 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TArray_tB79A9A6E8211AFCF57D3807AEBFC7AF87B0057A1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_PropertyBagStore_GetPropertyBag_TisTArray_tB79A9A6E8211AFCF57D3807AEBFC7AF87B0057A1_m27AE8FA861F014A2323586E1D17ADB7A14392538 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IPropertyBag_1_t38C5F60F5314A765517D5561678B9D4A7BE7779A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IConstructorWithCount_1_tCFDBEDE2DCBAFB75DBE301E3AFFF714BCC913F7A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IConstructorWithCount_1_InstantiateWithCount_m353D34809F1F5D6312D31ACAAA4881C31ADCAE05 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TArray_tB79A9A6E8211AFCF57D3807AEBFC7AF87B0057A1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IConstructorWithCount_1_t9DFD44199E942F47328DCE32F4E6A89DE2713592 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IConstructorWithCount_1_InstantiateWithCount_mB226AD82B7BDD8B92EC1C6E1F47C182DD10E5449 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TArray_t386A802729FA5C7DB8F552663ADCDD7E1551080E },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TArray_t386A802729FA5C7DB8F552663ADCDD7E1551080E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ITypeConstructor_1_t46DEBA6B13E0AEC5216C58BC26F3FA9921D9D9F5 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_t61934B9AD04128C12857D96350D039B585F477E8 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TypeConstructor_1_t64D12FC4EB18B55786777CE2882F1D3D63510C66 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_1_tDA05F49283791DE14D6E76D1DF7B57A7AAE544FA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IConstructor_1_t4EFA9443073BEDF7E0B2D7F1DDDEAA7B2FB2BE43 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_PropertyBagStore_GetPropertyBag_TisT_t37C62C3199851F00BDAC493E561783D14BAD4A68_m5AF20DA8BB092B1917CA662538784BBA9D9525A2 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IPropertyBag_1_tE302AA5F2105A288FFE536BB5656AC20C0B19B82 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TypeConstructor_1_SetImplicitConstructor_mD0BBE3B528E912D4B0233B208EB2443B04663F8C },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_t37C62C3199851F00BDAC493E561783D14BAD4A68 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TypeConstructor_1_CreateValueTypeInstance_m95DA6F1F6FFDB09B992AEB6F6B6572F5E7FF02AF },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Func_1__ctor_m68287EB0565E61F3A58134261D8DBD475C925B99 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TypeConstructor_1_CreateScriptableObjectInstance_m2E1C5218B78308AD2652E810047EE4DF7DF6AFDE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TypeConstructor_1_CreateClassInstance_m323DBA74E6FFE04C34246277C6E0DD0189981449 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t37C62C3199851F00BDAC493E561783D14BAD4A68 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Activator_CreateInstance_TisT_t37C62C3199851F00BDAC493E561783D14BAD4A68_mF32DEEFD38B493CF0B65F499AAA66FC530031FFD },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Func_1_Invoke_mF8A6CDB276750B3A6358C7826AD7AE31A48BA1C4 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IConstructor_1_Instantiate_m5E4AB45A8C5C14BFAB935DCFFC8AC009E9F7DC61 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ITypeConstructor_1_tDFB7AB319A5BCA0BDB1F09FB928689350FACC901 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ITypeConstructor_1_Instantiate_m8ED0004CDD9ACACC624288227E26723206DC946A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TypeUtility_CreateTypeConstructor_TisTContainer_tC36F00479677E34F7A594AE0941C2A3A0CDE78A9_mAB77701AC7B61EBBB9377A5C766F10CFEAB8AF37 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ITypeConstructor_1_t3B3C0683D2B31C558E6FE4B45406B7877BA987C4 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TypeTraits_1_get_IsContainer_m542A5FF42F8965E2F1E6BDA125885D649D270FAA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TypeTraits_1_tDBA22CC817797D99C38F8A27587091E7D9D5D34E },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TContainer_tBD4CBAFBFFD0739E77E5478196D2073D3B88A032 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TypeTraits_1_get_IsAbstractOrInterface_mCEEA653D933E8F8AFC10FF1C5B0574BD0EB0A50A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TypedStore_1_t5A7FB7070DBEC2FF9CBE919AB2D9B6471A7D069E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IPropertyBag_1_t36EC206CECA2B2BC0462B6FADB9163EF2D411949 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TypedStore_1_t5A7FB7070DBEC2FF9CBE919AB2D9B6471A7D069E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TypedStore_1_t52D7C98DB11647B84077919A338B2F0E406E7F32 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IPropertyBag_1_t4AAC76883AAF55D5E256B9F7F4EF8C6E8CAB5097 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TypedStore_1_t52D7C98DB11647B84077919A338B2F0E406E7F32 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TContainer_t93F87C792617812E0497030A8E9743696A582508 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TContainer_tBA215EB9698B59359CC8C9BF2B8B633F8E1225BB },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ContainerPropertyBag_1_TryGetProperty_m8D97B3D6DC58B98915B3E11C706FC2EBE0429C58 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TContainerU26_t2CA3EDA14F09157E09242942899AE74176048753 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IProperty_1U26_t79F6B52531B6F25719A0055381A2940A6B1B7CD6 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TContainer_tBA215EB9698B59359CC8C9BF2B8B633F8E1225BB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IProperty_1_t44D73C0034DE915ED4D4F755EDDCA9F75FE24BDE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ContainerPropertyBag_1__ctor_m0DB3E38E743C1CE31BAC0A5E5D42C24811EA03E6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ContainerPropertyBag_1_t3806CC6DCDFF60FB3F69045D743279FFBB26C3D5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_PropertyBag_1_tFBC77D17C7A43620E37345E9A7540FB9B291C2E9 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Property_2_t675504D339AE5B9908B3E512C0FFB0BB218DA7DE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Property_2_get_Name_mDCD05C6DAC5D208E12B9CAD548F09B9E9F8DF3BD },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TValue_tE132A1A6FDE9C35A83669F9DEAD0AA19A5C3E355 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Property_2_DeclaredValueType_mED6F2989213C202FA6EB47440DD236C46949D587 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ContainerPropertyBag_1_AddProperty_TisTValue_tE132A1A6FDE9C35A83669F9DEAD0AA19A5C3E355_mFE8D7BD6E54E7161F35830A582D2AB5FF061795E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TypeTraits_1_get_IsContainer_m4CE86C3DC694B7FF97A8CE182CDF8D45EB95C276 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TypeTraits_1_t483FC0F4C89BF4626152F4939487D87526B38F6E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TypeTraits_1_get_IsObject_mD95DF9C13A4929EB8ACF3595EFEA01BE87564CAE },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TContainer_t09FED92FC5C09423AF54F257DF9B569866D5F64A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IPropertyBag_1_t5C88FC124F8234DC1FFAC334A2386087A8AB7133 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ReflectedPropertyBag_1_tB5150C5F8355ACF7C232C64112C6C5604AE327EE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ReflectedPropertyBag_1__ctor_mDC8019356BDEB3DE10C010703BD27822E4392B02 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TValue_tCA8A6436749BF2758699B74AB52BBA768DC74E50 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ReflectedPropertyBag_1_tD6C5D734961A52A58E99B01D9452268288AD7BC7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ReflectedMemberProperty_2_t4AEC0F6E462155A02E89A2C7B9341756E332AC78 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ReflectedMemberProperty_2__ctor_mED73332B00D6C761553A67BDC996FD67468B1B58 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ReflectedPropertyBag_1_AddProperty_TisTValue_tCA8A6436749BF2758699B74AB52BBA768DC74E50_mA8878FE5D07587C20DE0948717D18ADF3F47E583 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Property_2_tFE207589AF8AC1C21C648655309188722045DEEE },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IndexedCollectionPropertyBag_2_t5F2100B58C1521310877B18F1F1F515336AD6FAF },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IndexedCollectionPropertyBag_2__ctor_m82E4C06BA20030E7631673A848FF63B52CC1CEA8 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IPropertyBag_1_t23DBDEE2BCE0E68D7B9C6EA248F41D6EE8B3805D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_SetPropertyBagBase_2_tF2E25B0D19FEEAED6844B5649372AD0A2C0FCEFA },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SetPropertyBagBase_2__ctor_m6A7DAD0B3D7BB886C3E43A5AAD438D97AD29B077 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IPropertyBag_1_t4B3966194D154621A18F1AE50F6E1923E366381D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_KeyValueCollectionPropertyBag_3_t93D99D472E9FA6C51CA7FBCFC34CB24965942D16 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_KeyValueCollectionPropertyBag_3__ctor_mB47F057C9F36544865ED0957DF1FF2AB483D3611 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IPropertyBag_1_t3F67556433ADE497021D747E5D3F187BC26601A1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_KeyValuePairPropertyBag_2_tB3FF046E02FD691A976CF2B299679AD64C87767F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_KeyValuePairPropertyBag_2__ctor_mDA6FE7408210F3A83DD3B2CF8564B11C79D056EF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IPropertyBag_1_tAF262B3E2465C83CE689DF8E4C1A4A7A261AC925 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ArrayPropertyBag_1_tE3FBBDE2CC3697A21876F04235D9AE37A272D4B0 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ArrayPropertyBag_1__ctor_mF63DAC415DB047B3D3A4E3869378EA4864A4DE46 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IPropertyBag_1_t657B355FBEDB15F7FA5B588D1FAB230347BF7EF3 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ListPropertyBag_1_tFCC1D761DCDF7AFAB616F4D86E12EF7ED37D34FD },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ListPropertyBag_1__ctor_mB869069D7AFEB50CBC016515F9FBCC1D95FB39A3 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IPropertyBag_1_t63AFE4A36EC9A87D882FB104DC77CD9E05ECC470 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_HashSetPropertyBag_1_tE0468D1841DDB27942294737E6F86EA10DEA0B7F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_HashSetPropertyBag_1__ctor_m7E808D7251C1DB56B86FC79BE6FDF989CCB9B8F4 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IPropertyBag_1_t9A9A3F2EED1278CCA08BABB9DC60DE6B410BF874 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_DictionaryPropertyBag_2_tE606638F1CF524214E9C65398357242A53E1938B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_DictionaryPropertyBag_2__ctor_m2AF546381FFCE3F5CD211F82CE8923EBB52A73F9 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IPropertyBag_1_tA3E85A9DBE91225B32BC663975BD5BA2D37FC36E },
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_PropertiesModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_PropertiesModule_CodeGenModule = 
{
	"UnityEngine.PropertiesModule.dll",
	267,
	s_methodPointers,
	10,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	49,
	s_rgctxIndices,
	273,
	s_rgctxValues,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};

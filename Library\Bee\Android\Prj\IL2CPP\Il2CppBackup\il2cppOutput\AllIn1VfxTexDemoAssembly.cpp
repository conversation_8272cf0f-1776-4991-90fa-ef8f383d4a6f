﻿#include "pch-cpp.hpp"

#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include <limits>


template <typename T1>
struct VirtualActionInvoker1
{
	typedef void (*Action)(void*, T1, const RuntimeMethod*);

	static inline void Invoke (Il2CppMethodSlot slot, RuntimeObject* obj, T1 p1)
	{
		const VirtualInvokeData& invokeData = il2cpp_codegen_get_virtual_invoke_data(slot, obj);
		((Action)invokeData.methodPtr)(obj, p1, invokeData.method);
	}
};

struct TweenRunner_1_t5BB0582F926E75E2FE795492679A6CF55A4B4BC4;
struct All1VfxDemoTextureCollectionU5BU5D_tCD8ABA1E139D181BB495F7CDEE87A1052D338ED3;
struct ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031;
struct CharU5BU5D_t799905CF001DD5F13F7DBB310181FC4D8B7D0AAB;
struct RawImageU5BU5D_tCF8D2C9175896CDEA0650874FCEA19472CBD6997;
struct TextureU5BU5D_t0C3F884241E8243E791A31B920CAA89212888E46;
struct UIVertexU5BU5D_tBC532486B45D071A520751A90E819C77BA4E3D2F;
struct Vector2U5BU5D_tFEBBC94BCC6C9C88277BA04047D2B3FDB6ED7FDA;
struct Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C;
struct All1VfxDemoTextureCollection_t0BBF17D7A41294B2105F1C983489D699C38D6A1B;
struct AllIn1DemoScaleTween_t94B5887F695262DEC379967C01BAB040F38CFE6F;
struct AllIn1VfxTextureDemoManager_t3FE0D01A0B982EDDBC839AF166E3F99B2144C9B0;
struct Behaviour_t01970CFBBA658497AE30F311C447DB0440BAB7FA;
struct CancellationTokenSource_tAAE1E0033BCFC233801F8CB4CED5C852B350CB7B;
struct Canvas_t2DB4CEFDFF732884866C83F11ABF75F5AE8FFB26;
struct CanvasRenderer_tAB9A55A976C4E3B2B37D0CE5616E5685A8B43860;
struct Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3;
struct FontData_tB8E562846C6CB59C43260F69AE346B9BF3157224;
struct Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3;
struct Mesh_t6D9C539763A09BC2B12AEAEF36F6DFFC98AE63D4;
struct MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71;
struct RawImage_tFF12F7DB574FBDC1863CF607C7A12A5D9F8D6179;
struct RectMask2D_tACF92BE999C791A665BD1ADEABF5BCEB82846670;
struct RectTransform_t6C5DA5E41A89E0F488B001E45E58963480E543A5;
struct ScriptableObject_tB3BFDB921A1B1795B38A5417D3B97A89A140436A;
struct String_t;
struct Text_tD60B2346DAA6666BF0D822FF607F0B220C2B9E62;
struct TextGenerator_t85D00417640A53953556C01F9D4E7DDE1ABD8FEC;
struct Texture_t791CBB51219779964E0E8A2ED7C1AA5F92A4A700;
struct Texture2D_tE6505BC111DD8A424A9DBE8E05D7D09E11FFFCF4;
struct Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1;
struct UnityAction_t11A1F3B953B365C072A5DCC32677EE1796A962A7;
struct UnitySourceGeneratedAssemblyMonoScriptTypes_v1_t979983055485449447C70366CB0227F1448FCECA;
struct VertexHelper_tB905FCB02AE67CBEE5F265FE37A5938FC5D136FE;
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915;
struct CullStateChangedEvent_t6073CD0D951EC1256BF74B8F9107D68FC89B99B8;

IL2CPP_EXTERN_C RuntimeClass* ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeField* U3CPrivateImplementationDetailsU3E_t6EF1BAAB70DD327A61595E05A8C1E0BF25D179E1____437D6913A8600E7741B13B05D7C477AE7ED89B66A55BB6DDBC386F0B3FE8303F_FieldInfo_var;
IL2CPP_EXTERN_C RuntimeField* U3CPrivateImplementationDetailsU3E_t6EF1BAAB70DD327A61595E05A8C1E0BF25D179E1____B2995547672443EC01BF49CD250739174B2FD1FCF4635EF92B62CC52A46D4647_FieldInfo_var;
IL2CPP_EXTERN_C String_t* _stringLiteral86BBAACC00198DBB3046818AD3FC2AA10AE48DE1;
IL2CPP_EXTERN_C String_t* _stringLiteralB1EFBA0C65B736BCC0888A4050F62D0D24AA95D9;

struct All1VfxDemoTextureCollectionU5BU5D_tCD8ABA1E139D181BB495F7CDEE87A1052D338ED3;
struct ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031;
struct RawImageU5BU5D_tCF8D2C9175896CDEA0650874FCEA19472CBD6997;
struct TextureU5BU5D_t0C3F884241E8243E791A31B920CAA89212888E46;

IL2CPP_EXTERN_C_BEGIN
IL2CPP_EXTERN_C_END

#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
struct U3CModuleU3E_tC496832DCC059C54195D5A3D7EE67C68397257F9 
{
};
struct U3CPrivateImplementationDetailsU3E_t6EF1BAAB70DD327A61595E05A8C1E0BF25D179E1  : public RuntimeObject
{
};
struct String_t  : public RuntimeObject
{
	int32_t ____stringLength;
	Il2CppChar ____firstChar;
};
struct UnitySourceGeneratedAssemblyMonoScriptTypes_v1_t979983055485449447C70366CB0227F1448FCECA  : public RuntimeObject
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F  : public RuntimeObject
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_pinvoke
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_com
{
};
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22 
{
	bool ___m_value;
};
struct Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3 
{
	uint8_t ___m_value;
};
struct Color_tD001788D726C3A7F1379BEED0260B9591F440C1F 
{
	float ___r;
	float ___g;
	float ___b;
	float ___a;
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2  : public ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F
{
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2_marshaled_pinvoke
{
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2_marshaled_com
{
};
struct Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C 
{
	int32_t ___m_value;
};
struct IntPtr_t 
{
	void* ___m_value;
};
struct Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D 
{
	float ___m_XMin;
	float ___m_YMin;
	float ___m_Width;
	float ___m_Height;
};
struct Single_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C 
{
	float ___m_value;
};
struct Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 
{
	float ___x;
	float ___y;
	float ___z;
};
struct Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 
{
	float ___x;
	float ___y;
	float ___z;
	float ___w;
};
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915 
{
	union
	{
		struct
		{
		};
		uint8_t Void_t4861ACF8F4594C3437BB48B6E56783494B843915__padding[1];
	};
};
#pragma pack(push, tp, 1)
struct __StaticArrayInitTypeSizeU3D237_t194319C8A216A9C1DFA14B1F18A192AFAFE2FFD7 
{
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D237_t194319C8A216A9C1DFA14B1F18A192AFAFE2FFD7__padding[237];
	};
};
#pragma pack(pop, tp)
#pragma pack(push, tp, 1)
struct __StaticArrayInitTypeSizeU3D291_t41B87EA1AFD379619859E95E4889A265FAAB897E 
{
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D291_t41B87EA1AFD379619859E95E4889A265FAAB897E__padding[291];
	};
};
#pragma pack(pop, tp)
struct MonoScriptData_t92A8C433C59C46C7E5164EE2B9FC0C35496B7637 
{
	ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* ___FilePathsData;
	ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* ___TypesData;
	int32_t ___TotalTypes;
	int32_t ___TotalFiles;
	bool ___IsEditorOnly;
};
struct MonoScriptData_t92A8C433C59C46C7E5164EE2B9FC0C35496B7637_marshaled_pinvoke
{
	Il2CppSafeArray* ___FilePathsData;
	Il2CppSafeArray* ___TypesData;
	int32_t ___TotalTypes;
	int32_t ___TotalFiles;
	int32_t ___IsEditorOnly;
};
struct MonoScriptData_t92A8C433C59C46C7E5164EE2B9FC0C35496B7637_marshaled_com
{
	Il2CppSafeArray* ___FilePathsData;
	Il2CppSafeArray* ___TypesData;
	int32_t ___TotalTypes;
	int32_t ___TotalFiles;
	int32_t ___IsEditorOnly;
};
struct KeyCode_t75B9ECCC26D858F55040DDFF9523681E996D17E9 
{
	int32_t ___value__;
};
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C  : public RuntimeObject
{
	intptr_t ___m_CachedPtr;
};
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_marshaled_pinvoke
{
	intptr_t ___m_CachedPtr;
};
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_marshaled_com
{
	intptr_t ___m_CachedPtr;
};
struct RuntimeFieldHandle_t6E4C45B6D2EA12FC99185805A7E77527899B25C5 
{
	intptr_t ___value;
};
struct Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3  : public Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C
{
};
struct ScriptableObject_tB3BFDB921A1B1795B38A5417D3B97A89A140436A  : public Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C
{
};
struct ScriptableObject_tB3BFDB921A1B1795B38A5417D3B97A89A140436A_marshaled_pinvoke : public Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_marshaled_pinvoke
{
};
struct ScriptableObject_tB3BFDB921A1B1795B38A5417D3B97A89A140436A_marshaled_com : public Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_marshaled_com
{
};
struct Texture_t791CBB51219779964E0E8A2ED7C1AA5F92A4A700  : public Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C
{
};
struct All1VfxDemoTextureCollection_t0BBF17D7A41294B2105F1C983489D699C38D6A1B  : public ScriptableObject_tB3BFDB921A1B1795B38A5417D3B97A89A140436A
{
	String_t* ___collectionName;
	TextureU5BU5D_t0C3F884241E8243E791A31B920CAA89212888E46* ___demoTextureCollection;
};
struct Behaviour_t01970CFBBA658497AE30F311C447DB0440BAB7FA  : public Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3
{
};
struct Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1  : public Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3
{
};
struct MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71  : public Behaviour_t01970CFBBA658497AE30F311C447DB0440BAB7FA
{
	CancellationTokenSource_tAAE1E0033BCFC233801F8CB4CED5C852B350CB7B* ___m_CancellationTokenSource;
};
struct AllIn1DemoScaleTween_t94B5887F695262DEC379967C01BAB040F38CFE6F  : public MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71
{
	float ___maxTweenScale;
	float ___minTweenScale;
	float ___tweenSpeed;
	bool ___isTweening;
	float ___currentScale;
	float ___iniScale;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___scaleToApply;
};
struct AllIn1VfxTextureDemoManager_t3FE0D01A0B982EDDBC839AF166E3F99B2144C9B0  : public MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71
{
	int32_t ___startingCollectionIndex;
	int32_t ___startingPageIndex;
	All1VfxDemoTextureCollectionU5BU5D_tCD8ABA1E139D181BB495F7CDEE87A1052D338ED3* ___textureCollections;
	int32_t ___nextPageKey;
	int32_t ___nextPageKeyAlt;
	int32_t ___previousPageKey;
	int32_t ___previousPageKeyAlt;
	int32_t ___nextCollectionKey;
	int32_t ___nextCollectionKeyAlt;
	int32_t ___previousCollectionKey;
	int32_t ___previousCollectionKeyAlt;
	RawImageU5BU5D_tCF8D2C9175896CDEA0650874FCEA19472CBD6997* ___images;
	Text_tD60B2346DAA6666BF0D822FF607F0B220C2B9E62* ___collectionText;
	Text_tD60B2346DAA6666BF0D822FF607F0B220C2B9E62* ___pageText;
	AllIn1DemoScaleTween_t94B5887F695262DEC379967C01BAB040F38CFE6F* ___expositorTween;
	AllIn1DemoScaleTween_t94B5887F695262DEC379967C01BAB040F38CFE6F* ___nextPageButtTween;
	AllIn1DemoScaleTween_t94B5887F695262DEC379967C01BAB040F38CFE6F* ___prevPageButtTween;
	AllIn1DemoScaleTween_t94B5887F695262DEC379967C01BAB040F38CFE6F* ___nextCollectionButtTween;
	AllIn1DemoScaleTween_t94B5887F695262DEC379967C01BAB040F38CFE6F* ___prevCollectionButtTween;
	int32_t ___currTextureCollectionIndex;
	int32_t ___currTextureIndex;
	int32_t ___numberOfImagesPerPage;
};
struct UIBehaviour_tB9D4295827BD2EEDEF0749200C6CA7090C742A9D  : public MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71
{
};
struct Graphic_tCBFCA4585A19E2B75465AECFEAC43F4016BF7931  : public UIBehaviour_tB9D4295827BD2EEDEF0749200C6CA7090C742A9D
{
	Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* ___m_Material;
	Color_tD001788D726C3A7F1379BEED0260B9591F440C1F ___m_Color;
	bool ___m_SkipLayoutUpdate;
	bool ___m_SkipMaterialUpdate;
	bool ___m_RaycastTarget;
	bool ___m_RaycastTargetCache;
	Vector4_t58B63D32F48C0DBF50DE2C60794C4676C80EDBE3 ___m_RaycastPadding;
	RectTransform_t6C5DA5E41A89E0F488B001E45E58963480E543A5* ___m_RectTransform;
	CanvasRenderer_tAB9A55A976C4E3B2B37D0CE5616E5685A8B43860* ___m_CanvasRenderer;
	Canvas_t2DB4CEFDFF732884866C83F11ABF75F5AE8FFB26* ___m_Canvas;
	bool ___m_VertsDirty;
	bool ___m_MaterialDirty;
	UnityAction_t11A1F3B953B365C072A5DCC32677EE1796A962A7* ___m_OnDirtyLayoutCallback;
	UnityAction_t11A1F3B953B365C072A5DCC32677EE1796A962A7* ___m_OnDirtyVertsCallback;
	UnityAction_t11A1F3B953B365C072A5DCC32677EE1796A962A7* ___m_OnDirtyMaterialCallback;
	Mesh_t6D9C539763A09BC2B12AEAEF36F6DFFC98AE63D4* ___m_CachedMesh;
	Vector2U5BU5D_tFEBBC94BCC6C9C88277BA04047D2B3FDB6ED7FDA* ___m_CachedUvs;
	TweenRunner_1_t5BB0582F926E75E2FE795492679A6CF55A4B4BC4* ___m_ColorTweenRunner;
	bool ___U3CuseLegacyMeshGenerationU3Ek__BackingField;
};
struct MaskableGraphic_tFC5B6BE351C90DE53744DF2A70940242774B361E  : public Graphic_tCBFCA4585A19E2B75465AECFEAC43F4016BF7931
{
	bool ___m_ShouldRecalculateStencil;
	Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* ___m_MaskMaterial;
	RectMask2D_tACF92BE999C791A665BD1ADEABF5BCEB82846670* ___m_ParentMask;
	bool ___m_Maskable;
	bool ___m_IsMaskingGraphic;
	bool ___m_IncludeForMasking;
	CullStateChangedEvent_t6073CD0D951EC1256BF74B8F9107D68FC89B99B8* ___m_OnCullStateChanged;
	bool ___m_ShouldRecalculate;
	int32_t ___m_StencilValue;
	Vector3U5BU5D_tFF1859CCE176131B909E2044F76443064254679C* ___m_Corners;
};
struct RawImage_tFF12F7DB574FBDC1863CF607C7A12A5D9F8D6179  : public MaskableGraphic_tFC5B6BE351C90DE53744DF2A70940242774B361E
{
	Texture_t791CBB51219779964E0E8A2ED7C1AA5F92A4A700* ___m_Texture;
	Rect_tA04E0F8A1830E767F40FB27ECD8D309303571F0D ___m_UVRect;
};
struct Text_tD60B2346DAA6666BF0D822FF607F0B220C2B9E62  : public MaskableGraphic_tFC5B6BE351C90DE53744DF2A70940242774B361E
{
	FontData_tB8E562846C6CB59C43260F69AE346B9BF3157224* ___m_FontData;
	String_t* ___m_Text;
	TextGenerator_t85D00417640A53953556C01F9D4E7DDE1ABD8FEC* ___m_TextCache;
	TextGenerator_t85D00417640A53953556C01F9D4E7DDE1ABD8FEC* ___m_TextCacheForLayout;
	bool ___m_DisableFontTextureRebuiltCallback;
	UIVertexU5BU5D_tBC532486B45D071A520751A90E819C77BA4E3D2F* ___m_TempVerts;
};
struct U3CPrivateImplementationDetailsU3E_t6EF1BAAB70DD327A61595E05A8C1E0BF25D179E1_StaticFields
{
	__StaticArrayInitTypeSizeU3D237_t194319C8A216A9C1DFA14B1F18A192AFAFE2FFD7 ___437D6913A8600E7741B13B05D7C477AE7ED89B66A55BB6DDBC386F0B3FE8303F;
	__StaticArrayInitTypeSizeU3D291_t41B87EA1AFD379619859E95E4889A265FAAB897E ___B2995547672443EC01BF49CD250739174B2FD1FCF4635EF92B62CC52A46D4647;
};
struct String_t_StaticFields
{
	String_t* ___Empty;
};
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_StaticFields
{
	String_t* ___TrueString;
	String_t* ___FalseString;
};
struct Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_StaticFields
{
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___zeroVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___oneVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___upVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___downVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___leftVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___rightVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___forwardVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___backVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___positiveInfinityVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___negativeInfinityVector;
};
struct Texture_t791CBB51219779964E0E8A2ED7C1AA5F92A4A700_StaticFields
{
	int32_t ___GenerateAllMips;
};
struct Text_tD60B2346DAA6666BF0D822FF607F0B220C2B9E62_StaticFields
{
	Material_t18053F08F347D0DCA5E1140EC7EC4533DD8A14E3* ___s_DefaultText;
};
#ifdef __clang__
#pragma clang diagnostic pop
#endif
struct ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031  : public RuntimeArray
{
	ALIGN_FIELD (8) uint8_t m_Items[1];

	inline uint8_t GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline uint8_t* GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, uint8_t value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
	}
	inline uint8_t GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline uint8_t* GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, uint8_t value)
	{
		m_Items[index] = value;
	}
};
struct RawImageU5BU5D_tCF8D2C9175896CDEA0650874FCEA19472CBD6997  : public RuntimeArray
{
	ALIGN_FIELD (8) RawImage_tFF12F7DB574FBDC1863CF607C7A12A5D9F8D6179* m_Items[1];

	inline RawImage_tFF12F7DB574FBDC1863CF607C7A12A5D9F8D6179* GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline RawImage_tFF12F7DB574FBDC1863CF607C7A12A5D9F8D6179** GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, RawImage_tFF12F7DB574FBDC1863CF607C7A12A5D9F8D6179* value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
	inline RawImage_tFF12F7DB574FBDC1863CF607C7A12A5D9F8D6179* GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline RawImage_tFF12F7DB574FBDC1863CF607C7A12A5D9F8D6179** GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, RawImage_tFF12F7DB574FBDC1863CF607C7A12A5D9F8D6179* value)
	{
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
};
struct All1VfxDemoTextureCollectionU5BU5D_tCD8ABA1E139D181BB495F7CDEE87A1052D338ED3  : public RuntimeArray
{
	ALIGN_FIELD (8) All1VfxDemoTextureCollection_t0BBF17D7A41294B2105F1C983489D699C38D6A1B* m_Items[1];

	inline All1VfxDemoTextureCollection_t0BBF17D7A41294B2105F1C983489D699C38D6A1B* GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline All1VfxDemoTextureCollection_t0BBF17D7A41294B2105F1C983489D699C38D6A1B** GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, All1VfxDemoTextureCollection_t0BBF17D7A41294B2105F1C983489D699C38D6A1B* value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
	inline All1VfxDemoTextureCollection_t0BBF17D7A41294B2105F1C983489D699C38D6A1B* GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline All1VfxDemoTextureCollection_t0BBF17D7A41294B2105F1C983489D699C38D6A1B** GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, All1VfxDemoTextureCollection_t0BBF17D7A41294B2105F1C983489D699C38D6A1B* value)
	{
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
};
struct TextureU5BU5D_t0C3F884241E8243E791A31B920CAA89212888E46  : public RuntimeArray
{
	ALIGN_FIELD (8) Texture_t791CBB51219779964E0E8A2ED7C1AA5F92A4A700* m_Items[1];

	inline Texture_t791CBB51219779964E0E8A2ED7C1AA5F92A4A700* GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline Texture_t791CBB51219779964E0E8A2ED7C1AA5F92A4A700** GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, Texture_t791CBB51219779964E0E8A2ED7C1AA5F92A4A700* value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
	inline Texture_t791CBB51219779964E0E8A2ED7C1AA5F92A4A700* GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline Texture_t791CBB51219779964E0E8A2ED7C1AA5F92A4A700** GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, Texture_t791CBB51219779964E0E8A2ED7C1AA5F92A4A700* value)
	{
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
};



IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RuntimeHelpers_InitializeArray_m751372AA3F24FBF6DA9B9D687CBFA2DE436CAB9B (RuntimeArray* ___0_array, RuntimeFieldHandle_t6E4C45B6D2EA12FC99185805A7E77527899B25C5 ___1_fldHandle, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2 (RuntimeObject* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ScriptableObject__ctor_mD037FDB0B487295EA47F79A4DB1BF1846C9087FF (ScriptableObject_tB3BFDB921A1B1795B38A5417D3B97A89A140436A* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371 (Component_t39FBE53E5EFCF4409111FB22C15FF73717632EC3* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Transform_get_localScale_m804A002A53A645CDFCD15BB0F37209162720363F (Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float Time_get_unscaledDeltaTime_mF057EECA857E5C0F90A3F910D26D3EE59F27C4B5 (const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Mathf_Lerp_m47EF2FFB7647BD0A1FDC26DC03E28B19812139B5_inline (float ___0_a, float ___1_b, float ___2_t, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AllIn1DemoScaleTween_UpdateScaleToApply_m4666E0FEAF897EF577E86E65146419F2E1624E28 (AllIn1DemoScaleTween_t94B5887F695262DEC379967C01BAB040F38CFE6F* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AllIn1DemoScaleTween_ApplyScale_mD52056E0897F1E588776D833842CAEB34C2C8280 (AllIn1DemoScaleTween_t94B5887F695262DEC379967C01BAB040F38CFE6F* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Transform_set_localScale_mBA79E811BAF6C47B80FF76414C12B47B3CD03633 (Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* __this, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_value, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_get_one_mC9B289F1E15C42C597180C9FE6FB492495B51D02_inline (const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void MonoBehaviour__ctor_m592DB0105CA0BC97AA1C5F4AD27B12D68A3B7C1E (MonoBehaviour_t532A11E69716D348D8AA7F854AFCBFCB8AD17F71* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AllIn1VfxTextureDemoManager_RefreshCollectionAndPageText_m8890CBBA19E8AB449B2B80C394410E7197CE3954 (AllIn1VfxTextureDemoManager_t3FE0D01A0B982EDDBC839AF166E3F99B2144C9B0* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AllIn1VfxTextureDemoManager_AssignCurrentImages_mFD05381DC5C95EA73ABD1CF331012BE5DD80B800 (AllIn1VfxTextureDemoManager_t3FE0D01A0B982EDDBC839AF166E3F99B2144C9B0* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Input_GetKeyDown_mB237DEA6244132670D38990BAB77D813FBB028D2 (int32_t ___0_key, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AllIn1VfxTextureDemoManager_ChangeTextureIndex_m15CD112C0A31ED729063C634DFBD795618EDCC60 (AllIn1VfxTextureDemoManager_t3FE0D01A0B982EDDBC839AF166E3F99B2144C9B0* __this, int32_t ___0_pagesAmount, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AllIn1VfxTextureDemoManager_ChangeCollectionIndex_m0D3E2B08840AC6D6D90D2979C0B6279E8B098251 (AllIn1VfxTextureDemoManager_t3FE0D01A0B982EDDBC839AF166E3F99B2144C9B0* __this, int32_t ___0_collectionChangeAmount, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AllIn1DemoScaleTween_ScaleDownTween_m6D50DF50CE6CD6C94F386058A877DED2C1C1C089 (AllIn1DemoScaleTween_t94B5887F695262DEC379967C01BAB040F38CFE6F* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* String_Concat_m9E3155FB84015C823606188F53B47CB44C444991 (String_t* ___0_str0, String_t* ___1_str1, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* Int32_ToString_m030E01C24E294D6762FB0B6F37CB541581F55CA5 (int32_t* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* String_Concat_m8855A6DE10F84DA7F4EC113CADDB59873A25573B (String_t* ___0_str0, String_t* ___1_str1, String_t* ___2_str2, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Behaviour_set_enabled_mF1DCFE60EB09E0529FE9476CA804A3AA2D72B16A (Behaviour_t01970CFBBA658497AE30F311C447DB0440BAB7FA* __this, bool ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RawImage_set_texture_mC016318C95CC17A826D57DD219DBCB6DFD295C02 (RawImage_tFF12F7DB574FBDC1863CF607C7A12A5D9F8D6179* __this, Texture_t791CBB51219779964E0E8A2ED7C1AA5F92A4A700* ___0_value, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Mathf_Clamp01_mA7E048DBDA832D399A581BE4D6DED9FA44CE0F14_inline (float ___0_value, const RuntimeMethod* method) ;
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR MonoScriptData_t92A8C433C59C46C7E5164EE2B9FC0C35496B7637 UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_mAE4E2A66CF010E8C2BED7DB9C2F20A6F14EFC515 (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CPrivateImplementationDetailsU3E_t6EF1BAAB70DD327A61595E05A8C1E0BF25D179E1____437D6913A8600E7741B13B05D7C477AE7ED89B66A55BB6DDBC386F0B3FE8303F_FieldInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CPrivateImplementationDetailsU3E_t6EF1BAAB70DD327A61595E05A8C1E0BF25D179E1____B2995547672443EC01BF49CD250739174B2FD1FCF4635EF92B62CC52A46D4647_FieldInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	MonoScriptData_t92A8C433C59C46C7E5164EE2B9FC0C35496B7637 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		il2cpp_codegen_initobj((&V_0), sizeof(MonoScriptData_t92A8C433C59C46C7E5164EE2B9FC0C35496B7637));
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_0 = (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)SZArrayNew(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031_il2cpp_TypeInfo_var, (uint32_t)((int32_t)291));
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_1 = L_0;
		RuntimeFieldHandle_t6E4C45B6D2EA12FC99185805A7E77527899B25C5 L_2 = { reinterpret_cast<intptr_t> (U3CPrivateImplementationDetailsU3E_t6EF1BAAB70DD327A61595E05A8C1E0BF25D179E1____B2995547672443EC01BF49CD250739174B2FD1FCF4635EF92B62CC52A46D4647_FieldInfo_var) };
		RuntimeHelpers_InitializeArray_m751372AA3F24FBF6DA9B9D687CBFA2DE436CAB9B((RuntimeArray*)L_1, L_2, NULL);
		(&V_0)->___FilePathsData = L_1;
		Il2CppCodeGenWriteBarrier((void**)(&(&V_0)->___FilePathsData), (void*)L_1);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_3 = (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)SZArrayNew(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031_il2cpp_TypeInfo_var, (uint32_t)((int32_t)237));
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_4 = L_3;
		RuntimeFieldHandle_t6E4C45B6D2EA12FC99185805A7E77527899B25C5 L_5 = { reinterpret_cast<intptr_t> (U3CPrivateImplementationDetailsU3E_t6EF1BAAB70DD327A61595E05A8C1E0BF25D179E1____437D6913A8600E7741B13B05D7C477AE7ED89B66A55BB6DDBC386F0B3FE8303F_FieldInfo_var) };
		RuntimeHelpers_InitializeArray_m751372AA3F24FBF6DA9B9D687CBFA2DE436CAB9B((RuntimeArray*)L_4, L_5, NULL);
		(&V_0)->___TypesData = L_4;
		Il2CppCodeGenWriteBarrier((void**)(&(&V_0)->___TypesData), (void*)L_4);
		(&V_0)->___TotalFiles = 3;
		(&V_0)->___TotalTypes = 3;
		(&V_0)->___IsEditorOnly = (bool)0;
		MonoScriptData_t92A8C433C59C46C7E5164EE2B9FC0C35496B7637 L_6 = V_0;
		return L_6;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_mE44828C7C605830E75A884206604FB7980FBE0C9 (UnitySourceGeneratedAssemblyMonoScriptTypes_v1_t979983055485449447C70366CB0227F1448FCECA* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C void MonoScriptData_t92A8C433C59C46C7E5164EE2B9FC0C35496B7637_marshal_pinvoke(const MonoScriptData_t92A8C433C59C46C7E5164EE2B9FC0C35496B7637& unmarshaled, MonoScriptData_t92A8C433C59C46C7E5164EE2B9FC0C35496B7637_marshaled_pinvoke& marshaled)
{
	marshaled.___FilePathsData = il2cpp_codegen_com_marshal_safe_array(IL2CPP_VT_I1, unmarshaled.___FilePathsData);
	marshaled.___TypesData = il2cpp_codegen_com_marshal_safe_array(IL2CPP_VT_I1, unmarshaled.___TypesData);
	marshaled.___TotalTypes = unmarshaled.___TotalTypes;
	marshaled.___TotalFiles = unmarshaled.___TotalFiles;
	marshaled.___IsEditorOnly = static_cast<int32_t>(unmarshaled.___IsEditorOnly);
}
IL2CPP_EXTERN_C void MonoScriptData_t92A8C433C59C46C7E5164EE2B9FC0C35496B7637_marshal_pinvoke_back(const MonoScriptData_t92A8C433C59C46C7E5164EE2B9FC0C35496B7637_marshaled_pinvoke& marshaled, MonoScriptData_t92A8C433C59C46C7E5164EE2B9FC0C35496B7637& unmarshaled)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	unmarshaled.___FilePathsData = (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I1, Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, marshaled.___FilePathsData);
	Il2CppCodeGenWriteBarrier((void**)(&unmarshaled.___FilePathsData), (void*)(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I1, Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, marshaled.___FilePathsData));
	unmarshaled.___TypesData = (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I1, Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, marshaled.___TypesData);
	Il2CppCodeGenWriteBarrier((void**)(&unmarshaled.___TypesData), (void*)(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I1, Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, marshaled.___TypesData));
	int32_t unmarshaledTotalTypes_temp_2 = 0;
	unmarshaledTotalTypes_temp_2 = marshaled.___TotalTypes;
	unmarshaled.___TotalTypes = unmarshaledTotalTypes_temp_2;
	int32_t unmarshaledTotalFiles_temp_3 = 0;
	unmarshaledTotalFiles_temp_3 = marshaled.___TotalFiles;
	unmarshaled.___TotalFiles = unmarshaledTotalFiles_temp_3;
	bool unmarshaledIsEditorOnly_temp_4 = false;
	unmarshaledIsEditorOnly_temp_4 = static_cast<bool>(marshaled.___IsEditorOnly);
	unmarshaled.___IsEditorOnly = unmarshaledIsEditorOnly_temp_4;
}
IL2CPP_EXTERN_C void MonoScriptData_t92A8C433C59C46C7E5164EE2B9FC0C35496B7637_marshal_pinvoke_cleanup(MonoScriptData_t92A8C433C59C46C7E5164EE2B9FC0C35496B7637_marshaled_pinvoke& marshaled)
{
	il2cpp_codegen_com_destroy_safe_array(marshaled.___FilePathsData);
	marshaled.___FilePathsData = NULL;
	il2cpp_codegen_com_destroy_safe_array(marshaled.___TypesData);
	marshaled.___TypesData = NULL;
}
IL2CPP_EXTERN_C void MonoScriptData_t92A8C433C59C46C7E5164EE2B9FC0C35496B7637_marshal_com(const MonoScriptData_t92A8C433C59C46C7E5164EE2B9FC0C35496B7637& unmarshaled, MonoScriptData_t92A8C433C59C46C7E5164EE2B9FC0C35496B7637_marshaled_com& marshaled)
{
	marshaled.___FilePathsData = il2cpp_codegen_com_marshal_safe_array(IL2CPP_VT_I1, unmarshaled.___FilePathsData);
	marshaled.___TypesData = il2cpp_codegen_com_marshal_safe_array(IL2CPP_VT_I1, unmarshaled.___TypesData);
	marshaled.___TotalTypes = unmarshaled.___TotalTypes;
	marshaled.___TotalFiles = unmarshaled.___TotalFiles;
	marshaled.___IsEditorOnly = static_cast<int32_t>(unmarshaled.___IsEditorOnly);
}
IL2CPP_EXTERN_C void MonoScriptData_t92A8C433C59C46C7E5164EE2B9FC0C35496B7637_marshal_com_back(const MonoScriptData_t92A8C433C59C46C7E5164EE2B9FC0C35496B7637_marshaled_com& marshaled, MonoScriptData_t92A8C433C59C46C7E5164EE2B9FC0C35496B7637& unmarshaled)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	unmarshaled.___FilePathsData = (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I1, Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, marshaled.___FilePathsData);
	Il2CppCodeGenWriteBarrier((void**)(&unmarshaled.___FilePathsData), (void*)(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I1, Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, marshaled.___FilePathsData));
	unmarshaled.___TypesData = (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I1, Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, marshaled.___TypesData);
	Il2CppCodeGenWriteBarrier((void**)(&unmarshaled.___TypesData), (void*)(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I1, Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, marshaled.___TypesData));
	int32_t unmarshaledTotalTypes_temp_2 = 0;
	unmarshaledTotalTypes_temp_2 = marshaled.___TotalTypes;
	unmarshaled.___TotalTypes = unmarshaledTotalTypes_temp_2;
	int32_t unmarshaledTotalFiles_temp_3 = 0;
	unmarshaledTotalFiles_temp_3 = marshaled.___TotalFiles;
	unmarshaled.___TotalFiles = unmarshaledTotalFiles_temp_3;
	bool unmarshaledIsEditorOnly_temp_4 = false;
	unmarshaledIsEditorOnly_temp_4 = static_cast<bool>(marshaled.___IsEditorOnly);
	unmarshaled.___IsEditorOnly = unmarshaledIsEditorOnly_temp_4;
}
IL2CPP_EXTERN_C void MonoScriptData_t92A8C433C59C46C7E5164EE2B9FC0C35496B7637_marshal_com_cleanup(MonoScriptData_t92A8C433C59C46C7E5164EE2B9FC0C35496B7637_marshaled_com& marshaled)
{
	il2cpp_codegen_com_destroy_safe_array(marshaled.___FilePathsData);
	marshaled.___FilePathsData = NULL;
	il2cpp_codegen_com_destroy_safe_array(marshaled.___TypesData);
	marshaled.___TypesData = NULL;
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void All1VfxDemoTextureCollection__ctor_m8701222B42461E227951BBD192B9872A7F3899DD (All1VfxDemoTextureCollection_t0BBF17D7A41294B2105F1C983489D699C38D6A1B* __this, const RuntimeMethod* method) 
{
	{
		ScriptableObject__ctor_mD037FDB0B487295EA47F79A4DB1BF1846C9087FF(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AllIn1DemoScaleTween_Start_m0B21571E9F74A64C41AB87F5BF24F8813A256A8C (AllIn1DemoScaleTween_t94B5887F695262DEC379967C01BAB040F38CFE6F* __this, const RuntimeMethod* method) 
{
	{
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_0;
		L_0 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		NullCheck(L_0);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_1;
		L_1 = Transform_get_localScale_m804A002A53A645CDFCD15BB0F37209162720363F(L_0, NULL);
		float L_2 = L_1.___x;
		__this->___iniScale = L_2;
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AllIn1DemoScaleTween_Update_m3938FE1C4E5421116868A2852379E0925CBA7D27 (AllIn1DemoScaleTween_t94B5887F695262DEC379967C01BAB040F38CFE6F* __this, const RuntimeMethod* method) 
{
	{
		bool L_0 = __this->___isTweening;
		if (L_0)
		{
			goto IL_0009;
		}
	}
	{
		return;
	}

IL_0009:
	{
		float L_1 = __this->___currentScale;
		float L_2 = __this->___iniScale;
		float L_3;
		L_3 = Time_get_unscaledDeltaTime_mF057EECA857E5C0F90A3F910D26D3EE59F27C4B5(NULL);
		float L_4 = __this->___tweenSpeed;
		float L_5;
		L_5 = Mathf_Lerp_m47EF2FFB7647BD0A1FDC26DC03E28B19812139B5_inline(L_1, L_2, ((float)il2cpp_codegen_multiply(L_3, L_4)), NULL);
		__this->___currentScale = L_5;
		AllIn1DemoScaleTween_UpdateScaleToApply_m4666E0FEAF897EF577E86E65146419F2E1624E28(__this, NULL);
		AllIn1DemoScaleTween_ApplyScale_mD52056E0897F1E588776D833842CAEB34C2C8280(__this, NULL);
		float L_6 = __this->___currentScale;
		float L_7;
		L_7 = fabsf(((float)il2cpp_codegen_subtract(L_6, (1.0f))));
		if ((!(((float)L_7) < ((float)(0.0199999996f)))))
		{
			goto IL_0057;
		}
	}
	{
		__this->___isTweening = (bool)0;
	}

IL_0057:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AllIn1DemoScaleTween_UpdateScaleToApply_m4666E0FEAF897EF577E86E65146419F2E1624E28 (AllIn1DemoScaleTween_t94B5887F695262DEC379967C01BAB040F38CFE6F* __this, const RuntimeMethod* method) 
{
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* L_0 = (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)(&__this->___scaleToApply);
		float L_1 = __this->___currentScale;
		L_0->___x = L_1;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2* L_2 = (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2*)(&__this->___scaleToApply);
		float L_3 = __this->___currentScale;
		L_2->___y = L_3;
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AllIn1DemoScaleTween_ApplyScale_mD52056E0897F1E588776D833842CAEB34C2C8280 (AllIn1DemoScaleTween_t94B5887F695262DEC379967C01BAB040F38CFE6F* __this, const RuntimeMethod* method) 
{
	{
		Transform_tB27202C6F4E36D225EE28A13E4D662BF99785DB1* L_0;
		L_0 = Component_get_transform_m2919A1D81931E6932C7F06D4C2F0AB8DDA9A5371(__this, NULL);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_1 = __this->___scaleToApply;
		NullCheck(L_0);
		Transform_set_localScale_mBA79E811BAF6C47B80FF76414C12B47B3CD03633(L_0, L_1, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AllIn1DemoScaleTween_ScaleUpTween_mC906F2123285187BF15A61CAA9E17E17D1C2ED2C (AllIn1DemoScaleTween_t94B5887F695262DEC379967C01BAB040F38CFE6F* __this, const RuntimeMethod* method) 
{
	{
		__this->___isTweening = (bool)1;
		float L_0 = __this->___iniScale;
		float L_1 = __this->___maxTweenScale;
		__this->___currentScale = ((float)il2cpp_codegen_multiply(L_0, L_1));
		AllIn1DemoScaleTween_UpdateScaleToApply_m4666E0FEAF897EF577E86E65146419F2E1624E28(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AllIn1DemoScaleTween_ScaleDownTween_m6D50DF50CE6CD6C94F386058A877DED2C1C1C089 (AllIn1DemoScaleTween_t94B5887F695262DEC379967C01BAB040F38CFE6F* __this, const RuntimeMethod* method) 
{
	{
		__this->___isTweening = (bool)1;
		float L_0 = __this->___iniScale;
		float L_1 = __this->___minTweenScale;
		__this->___currentScale = ((float)il2cpp_codegen_multiply(L_0, L_1));
		AllIn1DemoScaleTween_UpdateScaleToApply_m4666E0FEAF897EF577E86E65146419F2E1624E28(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AllIn1DemoScaleTween__ctor_m4A6D3B174060E167607564338E4FD923198C4DB2 (AllIn1DemoScaleTween_t94B5887F695262DEC379967C01BAB040F38CFE6F* __this, const RuntimeMethod* method) 
{
	{
		__this->___maxTweenScale = (2.0f);
		__this->___minTweenScale = (0.800000012f);
		__this->___tweenSpeed = (15.0f);
		__this->___currentScale = (1.0f);
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0;
		L_0 = Vector3_get_one_mC9B289F1E15C42C597180C9FE6FB492495B51D02_inline(NULL);
		__this->___scaleToApply = L_0;
		MonoBehaviour__ctor_m592DB0105CA0BC97AA1C5F4AD27B12D68A3B7C1E(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AllIn1VfxTextureDemoManager_Start_m36EFD84845B42769AE25B29548B0EA6D099E5C4C (AllIn1VfxTextureDemoManager_t3FE0D01A0B982EDDBC839AF166E3F99B2144C9B0* __this, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = __this->___startingCollectionIndex;
		__this->___currTextureCollectionIndex = L_0;
		int32_t L_1 = __this->___startingPageIndex;
		__this->___currTextureIndex = L_1;
		RawImageU5BU5D_tCF8D2C9175896CDEA0650874FCEA19472CBD6997* L_2 = __this->___images;
		NullCheck(L_2);
		__this->___numberOfImagesPerPage = ((int32_t)(((RuntimeArray*)L_2)->max_length));
		AllIn1VfxTextureDemoManager_RefreshCollectionAndPageText_m8890CBBA19E8AB449B2B80C394410E7197CE3954(__this, NULL);
		AllIn1VfxTextureDemoManager_AssignCurrentImages_mFD05381DC5C95EA73ABD1CF331012BE5DD80B800(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AllIn1VfxTextureDemoManager_Update_m3F9F917A13101C949BB72AEE73750A44417B8CD5 (AllIn1VfxTextureDemoManager_t3FE0D01A0B982EDDBC839AF166E3F99B2144C9B0* __this, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = __this->___nextPageKey;
		bool L_1;
		L_1 = Input_GetKeyDown_mB237DEA6244132670D38990BAB77D813FBB028D2(L_0, NULL);
		if (L_1)
		{
			goto IL_001a;
		}
	}
	{
		int32_t L_2 = __this->___nextPageKeyAlt;
		bool L_3;
		L_3 = Input_GetKeyDown_mB237DEA6244132670D38990BAB77D813FBB028D2(L_2, NULL);
		if (!L_3)
		{
			goto IL_0021;
		}
	}

IL_001a:
	{
		AllIn1VfxTextureDemoManager_ChangeTextureIndex_m15CD112C0A31ED729063C634DFBD795618EDCC60(__this, 1, NULL);
	}

IL_0021:
	{
		int32_t L_4 = __this->___previousPageKey;
		bool L_5;
		L_5 = Input_GetKeyDown_mB237DEA6244132670D38990BAB77D813FBB028D2(L_4, NULL);
		if (L_5)
		{
			goto IL_003b;
		}
	}
	{
		int32_t L_6 = __this->___previousPageKeyAlt;
		bool L_7;
		L_7 = Input_GetKeyDown_mB237DEA6244132670D38990BAB77D813FBB028D2(L_6, NULL);
		if (!L_7)
		{
			goto IL_0042;
		}
	}

IL_003b:
	{
		AllIn1VfxTextureDemoManager_ChangeTextureIndex_m15CD112C0A31ED729063C634DFBD795618EDCC60(__this, (-1), NULL);
	}

IL_0042:
	{
		int32_t L_8 = __this->___nextCollectionKey;
		bool L_9;
		L_9 = Input_GetKeyDown_mB237DEA6244132670D38990BAB77D813FBB028D2(L_8, NULL);
		if (L_9)
		{
			goto IL_005c;
		}
	}
	{
		int32_t L_10 = __this->___nextCollectionKeyAlt;
		bool L_11;
		L_11 = Input_GetKeyDown_mB237DEA6244132670D38990BAB77D813FBB028D2(L_10, NULL);
		if (!L_11)
		{
			goto IL_0063;
		}
	}

IL_005c:
	{
		AllIn1VfxTextureDemoManager_ChangeCollectionIndex_m0D3E2B08840AC6D6D90D2979C0B6279E8B098251(__this, 1, NULL);
	}

IL_0063:
	{
		int32_t L_12 = __this->___previousCollectionKey;
		bool L_13;
		L_13 = Input_GetKeyDown_mB237DEA6244132670D38990BAB77D813FBB028D2(L_12, NULL);
		if (L_13)
		{
			goto IL_007d;
		}
	}
	{
		int32_t L_14 = __this->___previousCollectionKeyAlt;
		bool L_15;
		L_15 = Input_GetKeyDown_mB237DEA6244132670D38990BAB77D813FBB028D2(L_14, NULL);
		if (!L_15)
		{
			goto IL_0084;
		}
	}

IL_007d:
	{
		AllIn1VfxTextureDemoManager_ChangeCollectionIndex_m0D3E2B08840AC6D6D90D2979C0B6279E8B098251(__this, (-1), NULL);
	}

IL_0084:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AllIn1VfxTextureDemoManager_ChangeTextureIndex_m15CD112C0A31ED729063C634DFBD795618EDCC60 (AllIn1VfxTextureDemoManager_t3FE0D01A0B982EDDBC839AF166E3F99B2144C9B0* __this, int32_t ___0_pagesAmount, const RuntimeMethod* method) 
{
	bool V_0 = false;
	{
		int32_t L_0 = __this->___currTextureIndex;
		int32_t L_1 = ___0_pagesAmount;
		int32_t L_2 = __this->___numberOfImagesPerPage;
		__this->___currTextureIndex = ((int32_t)il2cpp_codegen_add(L_0, ((int32_t)il2cpp_codegen_multiply(L_1, L_2))));
		int32_t L_3 = ___0_pagesAmount;
		if ((((int32_t)L_3) <= ((int32_t)0)))
		{
			goto IL_0026;
		}
	}
	{
		AllIn1DemoScaleTween_t94B5887F695262DEC379967C01BAB040F38CFE6F* L_4 = __this->___nextPageButtTween;
		NullCheck(L_4);
		AllIn1DemoScaleTween_ScaleDownTween_m6D50DF50CE6CD6C94F386058A877DED2C1C1C089(L_4, NULL);
		goto IL_0031;
	}

IL_0026:
	{
		AllIn1DemoScaleTween_t94B5887F695262DEC379967C01BAB040F38CFE6F* L_5 = __this->___prevPageButtTween;
		NullCheck(L_5);
		AllIn1DemoScaleTween_ScaleDownTween_m6D50DF50CE6CD6C94F386058A877DED2C1C1C089(L_5, NULL);
	}

IL_0031:
	{
		AllIn1DemoScaleTween_t94B5887F695262DEC379967C01BAB040F38CFE6F* L_6 = __this->___expositorTween;
		NullCheck(L_6);
		AllIn1DemoScaleTween_ScaleDownTween_m6D50DF50CE6CD6C94F386058A877DED2C1C1C089(L_6, NULL);
		V_0 = (bool)0;
		int32_t L_7 = __this->___currTextureIndex;
		if ((((int32_t)L_7) >= ((int32_t)0)))
		{
			goto IL_0052;
		}
	}
	{
		V_0 = (bool)1;
		AllIn1VfxTextureDemoManager_ChangeCollectionIndex_m0D3E2B08840AC6D6D90D2979C0B6279E8B098251(__this, (-1), NULL);
		goto IL_0077;
	}

IL_0052:
	{
		int32_t L_8 = __this->___currTextureIndex;
		All1VfxDemoTextureCollectionU5BU5D_tCD8ABA1E139D181BB495F7CDEE87A1052D338ED3* L_9 = __this->___textureCollections;
		int32_t L_10 = __this->___currTextureCollectionIndex;
		NullCheck(L_9);
		int32_t L_11 = L_10;
		All1VfxDemoTextureCollection_t0BBF17D7A41294B2105F1C983489D699C38D6A1B* L_12 = (L_9)->GetAt(static_cast<il2cpp_array_size_t>(L_11));
		NullCheck(L_12);
		TextureU5BU5D_t0C3F884241E8243E791A31B920CAA89212888E46* L_13 = L_12->___demoTextureCollection;
		NullCheck(L_13);
		if ((((int32_t)L_8) < ((int32_t)((int32_t)(((RuntimeArray*)L_13)->max_length)))))
		{
			goto IL_0077;
		}
	}
	{
		V_0 = (bool)1;
		AllIn1VfxTextureDemoManager_ChangeCollectionIndex_m0D3E2B08840AC6D6D90D2979C0B6279E8B098251(__this, 1, NULL);
	}

IL_0077:
	{
		bool L_14 = V_0;
		if (L_14)
		{
			goto IL_0086;
		}
	}
	{
		AllIn1VfxTextureDemoManager_AssignCurrentImages_mFD05381DC5C95EA73ABD1CF331012BE5DD80B800(__this, NULL);
		AllIn1VfxTextureDemoManager_RefreshCollectionAndPageText_m8890CBBA19E8AB449B2B80C394410E7197CE3954(__this, NULL);
	}

IL_0086:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AllIn1VfxTextureDemoManager_ChangeCollectionIndex_m0D3E2B08840AC6D6D90D2979C0B6279E8B098251 (AllIn1VfxTextureDemoManager_t3FE0D01A0B982EDDBC839AF166E3F99B2144C9B0* __this, int32_t ___0_collectionChangeAmount, const RuntimeMethod* method) 
{
	int32_t V_0 = 0;
	{
		int32_t L_0 = __this->___currTextureCollectionIndex;
		int32_t L_1 = ___0_collectionChangeAmount;
		__this->___currTextureCollectionIndex = ((int32_t)il2cpp_codegen_add(L_0, L_1));
		int32_t L_2 = ___0_collectionChangeAmount;
		if ((((int32_t)L_2) <= ((int32_t)0)))
		{
			goto IL_001f;
		}
	}
	{
		AllIn1DemoScaleTween_t94B5887F695262DEC379967C01BAB040F38CFE6F* L_3 = __this->___nextCollectionButtTween;
		NullCheck(L_3);
		AllIn1DemoScaleTween_ScaleDownTween_m6D50DF50CE6CD6C94F386058A877DED2C1C1C089(L_3, NULL);
		goto IL_002a;
	}

IL_001f:
	{
		AllIn1DemoScaleTween_t94B5887F695262DEC379967C01BAB040F38CFE6F* L_4 = __this->___prevCollectionButtTween;
		NullCheck(L_4);
		AllIn1DemoScaleTween_ScaleDownTween_m6D50DF50CE6CD6C94F386058A877DED2C1C1C089(L_4, NULL);
	}

IL_002a:
	{
		AllIn1DemoScaleTween_t94B5887F695262DEC379967C01BAB040F38CFE6F* L_5 = __this->___expositorTween;
		NullCheck(L_5);
		AllIn1DemoScaleTween_ScaleDownTween_m6D50DF50CE6CD6C94F386058A877DED2C1C1C089(L_5, NULL);
		int32_t L_6 = __this->___currTextureCollectionIndex;
		if ((((int32_t)L_6) >= ((int32_t)0)))
		{
			goto IL_0050;
		}
	}
	{
		All1VfxDemoTextureCollectionU5BU5D_tCD8ABA1E139D181BB495F7CDEE87A1052D338ED3* L_7 = __this->___textureCollections;
		NullCheck(L_7);
		__this->___currTextureCollectionIndex = ((int32_t)il2cpp_codegen_subtract(((int32_t)(((RuntimeArray*)L_7)->max_length)), 1));
		goto IL_0067;
	}

IL_0050:
	{
		int32_t L_8 = __this->___currTextureCollectionIndex;
		All1VfxDemoTextureCollectionU5BU5D_tCD8ABA1E139D181BB495F7CDEE87A1052D338ED3* L_9 = __this->___textureCollections;
		NullCheck(L_9);
		if ((((int32_t)L_8) < ((int32_t)((int32_t)(((RuntimeArray*)L_9)->max_length)))))
		{
			goto IL_0067;
		}
	}
	{
		__this->___currTextureCollectionIndex = 0;
	}

IL_0067:
	{
		int32_t L_10 = ___0_collectionChangeAmount;
		if ((((int32_t)L_10) <= ((int32_t)0)))
		{
			goto IL_0074;
		}
	}
	{
		__this->___currTextureIndex = 0;
		goto IL_00b6;
	}

IL_0074:
	{
		All1VfxDemoTextureCollectionU5BU5D_tCD8ABA1E139D181BB495F7CDEE87A1052D338ED3* L_11 = __this->___textureCollections;
		int32_t L_12 = __this->___currTextureCollectionIndex;
		NullCheck(L_11);
		int32_t L_13 = L_12;
		All1VfxDemoTextureCollection_t0BBF17D7A41294B2105F1C983489D699C38D6A1B* L_14 = (L_11)->GetAt(static_cast<il2cpp_array_size_t>(L_13));
		NullCheck(L_14);
		TextureU5BU5D_t0C3F884241E8243E791A31B920CAA89212888E46* L_15 = L_14->___demoTextureCollection;
		NullCheck(L_15);
		int32_t L_16 = __this->___numberOfImagesPerPage;
		V_0 = ((int32_t)(((int32_t)(((RuntimeArray*)L_15)->max_length))%L_16));
		int32_t L_17 = V_0;
		if (L_17)
		{
			goto IL_009a;
		}
	}
	{
		int32_t L_18 = __this->___numberOfImagesPerPage;
		V_0 = L_18;
	}

IL_009a:
	{
		All1VfxDemoTextureCollectionU5BU5D_tCD8ABA1E139D181BB495F7CDEE87A1052D338ED3* L_19 = __this->___textureCollections;
		int32_t L_20 = __this->___currTextureCollectionIndex;
		NullCheck(L_19);
		int32_t L_21 = L_20;
		All1VfxDemoTextureCollection_t0BBF17D7A41294B2105F1C983489D699C38D6A1B* L_22 = (L_19)->GetAt(static_cast<il2cpp_array_size_t>(L_21));
		NullCheck(L_22);
		TextureU5BU5D_t0C3F884241E8243E791A31B920CAA89212888E46* L_23 = L_22->___demoTextureCollection;
		NullCheck(L_23);
		int32_t L_24 = V_0;
		__this->___currTextureIndex = ((int32_t)il2cpp_codegen_subtract(((int32_t)(((RuntimeArray*)L_23)->max_length)), L_24));
	}

IL_00b6:
	{
		AllIn1VfxTextureDemoManager_AssignCurrentImages_mFD05381DC5C95EA73ABD1CF331012BE5DD80B800(__this, NULL);
		AllIn1VfxTextureDemoManager_RefreshCollectionAndPageText_m8890CBBA19E8AB449B2B80C394410E7197CE3954(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AllIn1VfxTextureDemoManager_RefreshCollectionAndPageText_m8890CBBA19E8AB449B2B80C394410E7197CE3954 (AllIn1VfxTextureDemoManager_t3FE0D01A0B982EDDBC839AF166E3F99B2144C9B0* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral86BBAACC00198DBB3046818AD3FC2AA10AE48DE1);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteralB1EFBA0C65B736BCC0888A4050F62D0D24AA95D9);
		s_Il2CppMethodInitialized = true;
	}
	int32_t V_0 = 0;
	int32_t V_1 = 0;
	{
		Text_tD60B2346DAA6666BF0D822FF607F0B220C2B9E62* L_0 = __this->___collectionText;
		All1VfxDemoTextureCollectionU5BU5D_tCD8ABA1E139D181BB495F7CDEE87A1052D338ED3* L_1 = __this->___textureCollections;
		int32_t L_2 = __this->___currTextureCollectionIndex;
		NullCheck(L_1);
		int32_t L_3 = L_2;
		All1VfxDemoTextureCollection_t0BBF17D7A41294B2105F1C983489D699C38D6A1B* L_4 = (L_1)->GetAt(static_cast<il2cpp_array_size_t>(L_3));
		NullCheck(L_4);
		String_t* L_5 = L_4->___collectionName;
		String_t* L_6;
		L_6 = String_Concat_m9E3155FB84015C823606188F53B47CB44C444991(L_5, _stringLiteralB1EFBA0C65B736BCC0888A4050F62D0D24AA95D9, NULL);
		NullCheck(L_0);
		VirtualActionInvoker1< String_t* >::Invoke(75, L_0, L_6);
		V_0 = 0;
		All1VfxDemoTextureCollectionU5BU5D_tCD8ABA1E139D181BB495F7CDEE87A1052D338ED3* L_7 = __this->___textureCollections;
		int32_t L_8 = __this->___currTextureCollectionIndex;
		NullCheck(L_7);
		int32_t L_9 = L_8;
		All1VfxDemoTextureCollection_t0BBF17D7A41294B2105F1C983489D699C38D6A1B* L_10 = (L_7)->GetAt(static_cast<il2cpp_array_size_t>(L_9));
		NullCheck(L_10);
		TextureU5BU5D_t0C3F884241E8243E791A31B920CAA89212888E46* L_11 = L_10->___demoTextureCollection;
		NullCheck(L_11);
		int32_t L_12 = __this->___numberOfImagesPerPage;
		float L_13;
		L_13 = ceilf(((float)(((float)((int32_t)(((RuntimeArray*)L_11)->max_length)))/((float)L_12))));
		V_1 = il2cpp_codegen_cast_double_to_int<int32_t>(L_13);
		int32_t L_14 = __this->___currTextureIndex;
		if ((((int32_t)L_14) <= ((int32_t)1)))
		{
			goto IL_0064;
		}
	}
	{
		int32_t L_15 = __this->___currTextureIndex;
		int32_t L_16 = __this->___numberOfImagesPerPage;
		V_0 = ((int32_t)(L_15/L_16));
	}

IL_0064:
	{
		int32_t L_17 = V_0;
		V_0 = ((int32_t)il2cpp_codegen_add(L_17, 1));
		Text_tD60B2346DAA6666BF0D822FF607F0B220C2B9E62* L_18 = __this->___pageText;
		String_t* L_19;
		L_19 = Int32_ToString_m030E01C24E294D6762FB0B6F37CB541581F55CA5((&V_0), NULL);
		String_t* L_20;
		L_20 = Int32_ToString_m030E01C24E294D6762FB0B6F37CB541581F55CA5((&V_1), NULL);
		String_t* L_21;
		L_21 = String_Concat_m8855A6DE10F84DA7F4EC113CADDB59873A25573B(L_19, _stringLiteral86BBAACC00198DBB3046818AD3FC2AA10AE48DE1, L_20, NULL);
		NullCheck(L_18);
		VirtualActionInvoker1< String_t* >::Invoke(75, L_18, L_21);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AllIn1VfxTextureDemoManager_AssignCurrentImages_mFD05381DC5C95EA73ABD1CF331012BE5DD80B800 (AllIn1VfxTextureDemoManager_t3FE0D01A0B982EDDBC839AF166E3F99B2144C9B0* __this, const RuntimeMethod* method) 
{
	int32_t V_0 = 0;
	RawImageU5BU5D_tCF8D2C9175896CDEA0650874FCEA19472CBD6997* V_1 = NULL;
	int32_t V_2 = 0;
	RawImage_tFF12F7DB574FBDC1863CF607C7A12A5D9F8D6179* V_3 = NULL;
	{
		V_0 = 0;
		RawImageU5BU5D_tCF8D2C9175896CDEA0650874FCEA19472CBD6997* L_0 = __this->___images;
		V_1 = L_0;
		V_2 = 0;
		goto IL_0068;
	}

IL_000d:
	{
		RawImageU5BU5D_tCF8D2C9175896CDEA0650874FCEA19472CBD6997* L_1 = V_1;
		int32_t L_2 = V_2;
		NullCheck(L_1);
		int32_t L_3 = L_2;
		RawImage_tFF12F7DB574FBDC1863CF607C7A12A5D9F8D6179* L_4 = (L_1)->GetAt(static_cast<il2cpp_array_size_t>(L_3));
		V_3 = L_4;
		int32_t L_5 = __this->___currTextureIndex;
		int32_t L_6 = V_0;
		All1VfxDemoTextureCollectionU5BU5D_tCD8ABA1E139D181BB495F7CDEE87A1052D338ED3* L_7 = __this->___textureCollections;
		int32_t L_8 = __this->___currTextureCollectionIndex;
		NullCheck(L_7);
		int32_t L_9 = L_8;
		All1VfxDemoTextureCollection_t0BBF17D7A41294B2105F1C983489D699C38D6A1B* L_10 = (L_7)->GetAt(static_cast<il2cpp_array_size_t>(L_9));
		NullCheck(L_10);
		TextureU5BU5D_t0C3F884241E8243E791A31B920CAA89212888E46* L_11 = L_10->___demoTextureCollection;
		NullCheck(L_11);
		if ((((int32_t)((int32_t)il2cpp_codegen_add(L_5, L_6))) < ((int32_t)((int32_t)(((RuntimeArray*)L_11)->max_length)))))
		{
			goto IL_0038;
		}
	}
	{
		RawImage_tFF12F7DB574FBDC1863CF607C7A12A5D9F8D6179* L_12 = V_3;
		NullCheck(L_12);
		Behaviour_set_enabled_mF1DCFE60EB09E0529FE9476CA804A3AA2D72B16A(L_12, (bool)0, NULL);
		goto IL_0064;
	}

IL_0038:
	{
		RawImage_tFF12F7DB574FBDC1863CF607C7A12A5D9F8D6179* L_13 = V_3;
		NullCheck(L_13);
		Behaviour_set_enabled_mF1DCFE60EB09E0529FE9476CA804A3AA2D72B16A(L_13, (bool)1, NULL);
		RawImage_tFF12F7DB574FBDC1863CF607C7A12A5D9F8D6179* L_14 = V_3;
		All1VfxDemoTextureCollectionU5BU5D_tCD8ABA1E139D181BB495F7CDEE87A1052D338ED3* L_15 = __this->___textureCollections;
		int32_t L_16 = __this->___currTextureCollectionIndex;
		NullCheck(L_15);
		int32_t L_17 = L_16;
		All1VfxDemoTextureCollection_t0BBF17D7A41294B2105F1C983489D699C38D6A1B* L_18 = (L_15)->GetAt(static_cast<il2cpp_array_size_t>(L_17));
		NullCheck(L_18);
		TextureU5BU5D_t0C3F884241E8243E791A31B920CAA89212888E46* L_19 = L_18->___demoTextureCollection;
		int32_t L_20 = __this->___currTextureIndex;
		int32_t L_21 = V_0;
		NullCheck(L_19);
		int32_t L_22 = ((int32_t)il2cpp_codegen_add(L_20, L_21));
		Texture_t791CBB51219779964E0E8A2ED7C1AA5F92A4A700* L_23 = (L_19)->GetAt(static_cast<il2cpp_array_size_t>(L_22));
		NullCheck(L_14);
		RawImage_set_texture_mC016318C95CC17A826D57DD219DBCB6DFD295C02(L_14, L_23, NULL);
		int32_t L_24 = V_0;
		V_0 = ((int32_t)il2cpp_codegen_add(L_24, 1));
	}

IL_0064:
	{
		int32_t L_25 = V_2;
		V_2 = ((int32_t)il2cpp_codegen_add(L_25, 1));
	}

IL_0068:
	{
		int32_t L_26 = V_2;
		RawImageU5BU5D_tCF8D2C9175896CDEA0650874FCEA19472CBD6997* L_27 = V_1;
		NullCheck(L_27);
		if ((((int32_t)L_26) < ((int32_t)((int32_t)(((RuntimeArray*)L_27)->max_length)))))
		{
			goto IL_000d;
		}
	}
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AllIn1VfxTextureDemoManager__ctor_mC15E39C98457B1B06DF25F6AB77FF3D50FF649AA (AllIn1VfxTextureDemoManager_t3FE0D01A0B982EDDBC839AF166E3F99B2144C9B0* __this, const RuntimeMethod* method) 
{
	{
		__this->___nextPageKey = ((int32_t)275);
		__this->___nextPageKeyAlt = ((int32_t)100);
		__this->___previousPageKey = ((int32_t)276);
		__this->___previousPageKeyAlt = ((int32_t)97);
		__this->___nextCollectionKey = ((int32_t)273);
		__this->___nextCollectionKeyAlt = ((int32_t)119);
		__this->___previousCollectionKey = ((int32_t)274);
		__this->___previousCollectionKeyAlt = ((int32_t)115);
		MonoBehaviour__ctor_m592DB0105CA0BC97AA1C5F4AD27B12D68A3B7C1E(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Mathf_Lerp_m47EF2FFB7647BD0A1FDC26DC03E28B19812139B5_inline (float ___0_a, float ___1_b, float ___2_t, const RuntimeMethod* method) 
{
	float V_0 = 0.0f;
	{
		float L_0 = ___0_a;
		float L_1 = ___1_b;
		float L_2 = ___0_a;
		float L_3 = ___2_t;
		float L_4;
		L_4 = Mathf_Clamp01_mA7E048DBDA832D399A581BE4D6DED9FA44CE0F14_inline(L_3, NULL);
		V_0 = ((float)il2cpp_codegen_add(L_0, ((float)il2cpp_codegen_multiply(((float)il2cpp_codegen_subtract(L_1, L_2)), L_4))));
		goto IL_0010;
	}

IL_0010:
	{
		float L_5 = V_0;
		return L_5;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_get_one_mC9B289F1E15C42C597180C9FE6FB492495B51D02_inline (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = ((Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_StaticFields*)il2cpp_codegen_static_fields_for(Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_il2cpp_TypeInfo_var))->___oneVector;
		V_0 = L_0;
		goto IL_0009;
	}

IL_0009:
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_1 = V_0;
		return L_1;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Mathf_Clamp01_mA7E048DBDA832D399A581BE4D6DED9FA44CE0F14_inline (float ___0_value, const RuntimeMethod* method) 
{
	bool V_0 = false;
	float V_1 = 0.0f;
	bool V_2 = false;
	{
		float L_0 = ___0_value;
		V_0 = (bool)((((float)L_0) < ((float)(0.0f)))? 1 : 0);
		bool L_1 = V_0;
		if (!L_1)
		{
			goto IL_0015;
		}
	}
	{
		V_1 = (0.0f);
		goto IL_002d;
	}

IL_0015:
	{
		float L_2 = ___0_value;
		V_2 = (bool)((((float)L_2) > ((float)(1.0f)))? 1 : 0);
		bool L_3 = V_2;
		if (!L_3)
		{
			goto IL_0029;
		}
	}
	{
		V_1 = (1.0f);
		goto IL_002d;
	}

IL_0029:
	{
		float L_4 = ___0_value;
		V_1 = L_4;
		goto IL_002d;
	}

IL_002d:
	{
		float L_5 = V_1;
		return L_5;
	}
}

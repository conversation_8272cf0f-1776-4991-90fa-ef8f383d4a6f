﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void AkMultiPosEvent_FinishedPlaying_mB1DA343FFF409823A2FBBE28516DF2E02798410B (void);
extern void AkMultiPosEvent__ctor_m969B286B87988B8FCE1F1A74F24ADC1220552FCF (void);
extern void AkAmbient_OnEnable_mDD3055F43B957A7B6294F7937F0949B1C8E84E9C (void);
extern void AkAmbient_Start_mF22CA33642A381DF7785E54AAAF8420D8105A554 (void);
extern void AkAmbient_OnDisable_m1E46830E42E18129AD30940950599218B358B2DD (void);
extern void AkAmbient_HandleEvent_m33028A608921D9C164C2474A7970422C6941A104 (void);
extern void AkAmbient_OnDrawGizmosSelected_mBA595823318C11E518B5A68B10BBF82F18F2B9B1 (void);
extern void AkAmbient_BuildMultiDirectionArray_mFA570DE8B5173C1AAFE3A560C4F3EC1862EDDD54 (void);
extern void AkAmbient_BuildAkPositionArray_mE4254647A752BD92236F3E04F406EC91A39F3F54 (void);
extern void AkAmbient__ctor_m37F0F07E42BE982AB44EFE0B73E14DE5965A6C69 (void);
extern void AkAmbient__cctor_mDE43BEBA430244F57DE2CA1FC4D9F56AEA9A8DAE (void);
extern void AkAmbientLargeModePositioner_get_Position_mFF8FA11422B44B9D26F3EAFAE2589A4FD7A06058 (void);
extern void AkAmbientLargeModePositioner_get_Forward_m15A55CABD01514B614B4BF1F6DBF2ECC77A3B507 (void);
extern void AkAmbientLargeModePositioner_get_Up_m982BFFA8B13520D44F04E72B71315DEC23B0F467 (void);
extern void AkAmbientLargeModePositioner__ctor_mBD8B2BE438B5023926CDE4D26A5D3E6676B119FC (void);
extern void AkAudioListener_get_DefaultListeners_m1DA42DB306675B13AD6DE3C6BC274903B5AE8818 (void);
extern void AkAudioListener_StartListeningToEmitter_m43021CF74B269EF32ADBD0B0EB30DF9AF30B4188 (void);
extern void AkAudioListener_StopListeningToEmitter_m1F4172B4BBA194355DC888B0E59C6F2FEFBD8CD1 (void);
extern void AkAudioListener_SetIsDefaultListener_mF83803897233E944962600FC26F08D5D345267CC (void);
extern void AkAudioListener_Awake_m1123189AF05EF895F2F31D25826361EDA8095BC8 (void);
extern void AkAudioListener_OnEnable_mEE167E087D9F0DDDEE6A1BF6A4E05A4860A59C0E (void);
extern void AkAudioListener_OnDisable_mD3F6F7E43C89654A1CA4DCC590F71D5123B5F652 (void);
extern void AkAudioListener_OnDestroy_mBD588EEAD35639CFEA7BD4995922D58CB946B448 (void);
extern void AkAudioListener_Update_m1FA30F9D13C6F7794FAF6A70582287879753C841 (void);
extern void AkAudioListener_GetAkGameObjectID_m5C10B12D6B850B5F5FA0C3FF3F179C13E478F03A (void);
extern void AkAudioListener_Migrate14_mE976FD130E0014917F9974CF6ADFFB68C015905E (void);
extern void AkAudioListener__ctor_mD39B99E45851233BDDA82345669AB5AFDF19B30E (void);
extern void AkAudioListener__cctor_m79D7FDDC9F900E525427D1F5025D6CDB08A66F8C (void);
extern void BaseListenerList_get_ListenerList_m110246E38BD12FA4A0A8F447879A36A1E2BAE7E3 (void);
extern void BaseListenerList_Add_m9B97D51304AA1FEE67D50597F91E459EFCCB2582 (void);
extern void BaseListenerList_Remove_mB091FA5BC74A33E7C77460292CB55D6991308974 (void);
extern void BaseListenerList_GetListenerIds_mD5658F6BBC9C43392220A61CF6936922B99E5069 (void);
extern void BaseListenerList__ctor_mCC8C874ED94EB99EF980D5D353078DBEBD34BC2A (void);
extern void DefaultListenerList_Add_mBE1534920F565194FC80EF941692E3ABE3F992DC (void);
extern void DefaultListenerList_Remove_m9167CE5693E3977C9B5A6BEA95763570DD84EDFF (void);
extern void DefaultListenerList__ctor_mBCD862553147AEFFD4283B03BD640002B25917EB (void);
extern void AkBank_Awake_m734785B456499606815B5F1D4A0270A92469D542 (void);
extern void AkBank_Start_m78FA94D5FA357395E9590AEFDE758AF0EEE3A846 (void);
extern void AkBank_HandleEvent_m553392E0631977E9A49BC1BCB1D1ECE93EEA550A (void);
extern void AkBank_HandleEvent_mC0C0A48A5EF1AC9995F1CA601CC56A3B270C92E0 (void);
extern void AkBank_UnloadBank_m9AB8EC2CC7C93AABF356923D4EC73B226D6D2090 (void);
extern void AkBank_OnDestroy_m7E823B3B153BCA9827DA0FA22A8D41F997740EF5 (void);
extern void AkBank_get_bankName_m9045CFFC9011DF21DA2868659C7FE81536DAC657 (void);
extern void AkBank_get_valueGuid_m1D0469F9792721BA640B35FC6EA1AB4AC2CF0E4B (void);
extern void AkBank__ctor_m1ECEBCE925FE38BEABD304E39CEEAAF8C6A06B48 (void);
extern void AkEarlyReflections_OnEnable_m7073409FC6CD8DC7EA72A975407565E19A1C962E (void);
extern void AkEarlyReflections_SetEarlyReflectionsVolume_m41BEEFF298EFE7561282A0F2902DCB48E2685E32 (void);
extern void AkEarlyReflections__ctor_mFE90A4AD4CE88813A0795054816F882450168602 (void);
extern void AkEmitterObstructionOcclusion_Awake_m595815388B4E8444DB74C25AC24EDF8371DB03EC (void);
extern void AkEmitterObstructionOcclusion_UpdateCurrentListenerList_m2B397C18EEFCFA64580262E23C4B2CB91BBCC862 (void);
extern void AkEmitterObstructionOcclusion_SetObstructionOcclusion_m5E8CB10D8BD38BC12F51818118E66C655CAD6F9E (void);
extern void AkEmitterObstructionOcclusion__ctor_mA26891E5D7C81A02C914343EB2971B249FFF903E (void);
extern void AkEnvironment_get_Collider_mE4A3BA8060502135FFD22DBEBC76C6E8DDD1D207 (void);
extern void AkEnvironment_set_Collider_m7E6854A06A7FD0035042AD3B760C048FC13F2F12 (void);
extern void AkEnvironment_Awake_mCF3E5ED65D4FBF08682511F5E5858AB156B01669 (void);
extern void AkEnvironment_get_m_auxBusID_mFCD4C43B299F286F1A3C239FD1852A38C2508B40 (void);
extern void AkEnvironment_get_valueGuid_m01CA9B50B2410B65AEA3E13EAC58D5DBBD4EDD82 (void);
extern void AkEnvironment_GetAuxBusID_m9797D4A2D90C8F9605AF78458A7C160998B3FFD6 (void);
extern void AkEnvironment_GetCollider_m6CC78BD3A9914216048C5D7B7C36043210BDC57F (void);
extern void AkEnvironment__ctor_mE5E56C98504D2B0D2EAFDFD49C0507BBA5F34397 (void);
extern void AkEnvironment__cctor_m5000F3EC28D9979812930B23955AC7B0AB036512 (void);
extern void AkEnvironment_CompareByPriority_Compare_m3D8802D827B62255BEC83A975651072244A13281 (void);
extern void AkEnvironment_CompareByPriority__ctor_mA4D2C5A4A584F573B71AD3F618AF0A532D9810EB (void);
extern void AkEnvironment_CompareBySelectionAlgorithm_Compare_m57FCFD54AD521C3E6D3D3B49667EEB6AB7A0256A (void);
extern void AkEnvironment_CompareBySelectionAlgorithm__ctor_mA7BF0431BFD749525892E9ACA1B99D2654443EFA (void);
extern void AkEnvironmentPortal_get_BoxCollider_m63C1B5579BB019914349C237CC19E38F24643226 (void);
extern void AkEnvironmentPortal_get_EnvironmentsShareAuxBus_m9EE0A319437142E82B578FEF9E6D3ADD4230AC7B (void);
extern void AkEnvironmentPortal_GetAuxSendValueForPosition_m3B4E498A364A00906BFFAC5598146DD832D3207C (void);
extern void AkEnvironmentPortal__ctor_m372BA4E695DD0E40849585C681BCEED73BDD812B (void);
extern void AkEventCallbackMsg__ctor_m7902A40F3A716B63781BD928B0ACE1E0B70CB5CA (void);
extern void AkEvent_get_WwiseType_m13C2E5D8BB00B08D2B5E87B0868093502A03B983 (void);
extern void AkEvent_Awake_m1065E401F0F482D2D167C48B799352870B333C41 (void);
extern void AkEvent_Start_mD442B7B6F1F2C827C285B4066692B8F410097DB3 (void);
extern void AkEvent_Callback_m24DF525783BA1FCF47E1DB66E12495201D9DF8A6 (void);
extern void AkEvent_HandleEvent_m02A435AFA67A02406B48921C51D14088504FAA8A (void);
extern void AkEvent_Stop_m74751AF8BDB3C8C6FD5793CF93CB2E9ACEE053B4 (void);
extern void AkEvent_Stop_mA9AA8F665A0D68155E2D10F74C7E519A2E5F3335 (void);
extern void AkEvent_get_eventID_m2C557F0564A281BF84AAA2AD29DEB362E4CB5B52 (void);
extern void AkEvent_get_valueGuid_mA425C6FCFE9311814094E3844E75F3240F2C338F (void);
extern void AkEvent_get_m_callbackData_m2E79D2D109964B1460EC0289A501EFE21E49C474 (void);
extern void AkEvent__ctor_mA17755DAE330E8779A6824DF5CA51D3A2CF1E63C (void);
extern void CallbackData_CallFunction_m209C555F73D365840F418E86849A8C9C04BBB377 (void);
extern void CallbackData__ctor_m4CA98158451205D19B44FBDC29C92321FFB05B1C (void);
extern void AkEventCallbackData__ctor_m20C1E0AD49CB3E1CA5D327D491B65B18AF212550 (void);
extern void AkGameObj_get_IsUsingDefaultListeners_mCB78ABCA467FCF314484DFCFCE4D08F0134068D3 (void);
extern void AkGameObj_get_ListenerList_m44035423031201A2886708684532B974D58B1155 (void);
extern void AkGameObj_AddListener_mBA131772E5B90F91E2E1E939A2D33A9B8D894CA9 (void);
extern void AkGameObj_RemoveListener_m932D77F1BAAE308CA71C41361D79425281C1FBCE (void);
extern void AkGameObj_Register_m19BB0D97E161CA814230E861F63B8FD21753C425 (void);
extern void AkGameObj_UnregisterGameObject_mF82AA1751DF50E447EBFF32ADE35E4C077FD62F1 (void);
extern void AkGameObj_Unregister_m773A01B4C9F871B51F7C9B4AC164D9A3B1DE9F80 (void);
extern void AkGameObj_SetPosition_mE6E48EF73298D8B5DA0D6B4D0FE4316A8EF1C7B1 (void);
extern void AkGameObj_Awake_m0D17D660596EBAD9CAE8E10EC455BB7161EB9E88 (void);
extern void AkGameObj_RegisterGameObject_m6AE10802D66399A067B17CEBFFCB970F8598CCED (void);
extern void AkGameObj_CheckStaticStatus_mB762AC0E35D2A352A1F9FC9ABDA6B5A1E917F25D (void);
extern void AkGameObj_OnEnable_m12161CBD8A2BF10D9B5914A4EDD263D3610C82D3 (void);
extern void AkGameObj_OnDestroy_m66ACFB652EA33670816CE0ED846414963D3506B2 (void);
extern void AkGameObj_Update_m61A4B63C863C589A72F2242E5C23A778990538D4 (void);
extern void AkGameObj_GetPosition_mC51C615928A0EED06050BABC2FBCB1B0A5F76FC5 (void);
extern void AkGameObj_GetForward_m49C636509AE30EB44D25810D633F24E28DC3E366 (void);
extern void AkGameObj_GetUpward_m823AA8496AE98953761DD2D0D9CBA99A8F8286C5 (void);
extern void AkGameObj_OnTriggerEnter_mE091BDC21F1986C538FF69C023B96172D217DAF0 (void);
extern void AkGameObj_OnTriggerExit_m66C793707D2491E76B4585AE485B11B1188754B7 (void);
extern void AkGameObj__ctor_mEB136EB17F2B2B1669C550A5A09D72B8946FE999 (void);
extern void AkGameObjEnvironmentData_AddHighestPriorityEnvironmentsFromPortals_m8F9F89273438157AF257755CE44858D4AC7BCC2E (void);
extern void AkGameObjEnvironmentData_AddHighestPriorityEnvironments_mD66A1393DFB8ED7C6633AEB68E3545BC6B8E7B1E (void);
extern void AkGameObjEnvironmentData_UpdateAuxSend_mD897D1AF64E209109E94E74566A49C9999432BB5 (void);
extern void AkGameObjEnvironmentData_TryAddEnvironment_m74B686C195D1961E62D5874BCFDA7722114F430B (void);
extern void AkGameObjEnvironmentData_RemoveEnvironment_m46B121A69B9D837EBD2B2CB490F7081BF6E33233 (void);
extern void AkGameObjEnvironmentData_AddAkEnvironment_mBB1D5ABFB680650CA0E632EB0BC62E29615013E0 (void);
extern void AkGameObjEnvironmentData_AkEnvironmentBelongsToActivePortals_mF55B6FC27B779558EFD0C258E551F59DC7E75A63 (void);
extern void AkGameObjEnvironmentData_RemoveAkEnvironment_mFB86DB462D1007A05E4FCBD03C3F47C7D59FA967 (void);
extern void AkGameObjEnvironmentData__ctor_m8366DD2C929D6605CFA184141A109F6C4D11F889 (void);
extern void AkGameObjListenerList_SetUseDefaultListeners_m45856C245E2A4BB40AAAEE5EED93535D4F0AD73C (void);
extern void AkGameObjListenerList_Init_m442531D8E77AD335CA8252625A41568AB9EBEFF5 (void);
extern void AkGameObjListenerList_Add_m9B9E48E13ADC1C37AD60B78C0BE0DB42B24D38B5 (void);
extern void AkGameObjListenerList_Remove_m52A7DF1DEC399DABDA3FED66405D75D608A6AC88 (void);
extern void AkGameObjListenerList__ctor_mF57AD5DB52BAFB54CD6A2041C3FB733B62F3CE63 (void);
extern void AkGameObjPositionData__ctor_mD2BC9463D55D9B7AF0BE920C621CECF251F99C3F (void);
extern void AkGameObjPositionOffsetData__ctor_m8E136C7C35C82EFA74AC0F591ABC94536C32C872 (void);
extern void AkGameObjPosOffsetData__ctor_mEB8883C8565C0EC6894C6A41675D024A840A3020 (void);
extern void AkInitializer_CreateRoomGeometryData_m07C86ABA10BA8EDDFD792C439AE109BD2D416EFB (void);
extern void AkInitializer_Awake_mF769719EF7589099BE90DC7B1EBD5E2FA2D27557 (void);
extern void AkInitializer_IsInstance_m0FB9B7261A3205128B23A63B9EB0997C7A19ACA3 (void);
extern void AkInitializer_GetAkInitializerGameObject_m71AA65E6BF35301AE2F3D5CC4C3BFE92F4A7D945 (void);
extern void AkInitializer_OnEnable_mB9C437A610EA8FF54EEE699E9EF32EA0282C5A59 (void);
extern void AkInitializer_InitializeInitializationSettings_mA8108F8794CF2BD3D40C159C65C26D166C6D2984 (void);
extern void AkInitializer_OnDisable_mC86E2276ED9577309D615AB68BF909508B094C87 (void);
extern void AkInitializer_OnDestroy_mAF05ED3B6614D5C5230AD32AD5F37C639683B5A3 (void);
extern void AkInitializer_OnApplicationPause_mB8D738D764CB95268852990EE8BB4C06381FA28D (void);
extern void AkInitializer_OnApplicationFocus_m7B1A17920BA497B057A577580AEBB422FB5306A7 (void);
extern void AkInitializer_OnApplicationQuit_m983DA840DB6F04491496BAEE15B7261D46F5A829 (void);
extern void AkInitializer_LateUpdate_m0B55517AE06F9845A32756C47ABF4CE9607F7608 (void);
extern void AkInitializer__ctor_m8CF01BC505FE1BA3AAEE9A163671C2A3DE2AFA7E (void);
extern void AkListenerDistanceProbe_OnEnable_mEB0B59DB54110D9D13DFC09D4094E3CBAE0D8B19 (void);
extern void AkListenerDistanceProbe_OnDisable_m910E4241AB9E482BBB60A3A81C33E42ADB0891DC (void);
extern void AkListenerDistanceProbe__ctor_mFA9BCB55EDACA50A2F5133E6EA090770E3720E08 (void);
extern void AkMemBankLoader_Start_m6C6D1C8140A7861AEA8EF9753ACA1B54D785571F (void);
extern void AkMemBankLoader_LoadNonLocalizedBank_mF62439AAE1DF224DF63EA8F7BB00F84C4853A971 (void);
extern void AkMemBankLoader_LoadLocalizedBank_mA17D8BC6B9834D377CB3F4DF507207587013DD36 (void);
extern void AkMemBankLoader_AllocateAlignedBuffer_m5E676960B96E5F2944EDB0181C0357924A4E3A6B (void);
extern void AkMemBankLoader_LoadFile_m8371D00616572E8321C9CAAF11584D63437EDAFE (void);
extern void AkMemBankLoader_DoLoadBank_mC9C73BD375BCFBCC6A9EDAE48886E1AC64B529F2 (void);
extern void AkMemBankLoader_OnDestroy_m928F2EF90D603BAD47C3E2711F28A39A43D5BF2B (void);
extern void AkMemBankLoader__ctor_m4F3D77F5EEFB2C182B0358833BDEB4C1BB878567 (void);
extern void U3CLoadFileU3Ed__14__ctor_mB219A90910D5676B13E4338364E5CBB1182EE009 (void);
extern void U3CLoadFileU3Ed__14_System_IDisposable_Dispose_m25D4A1875645611A43307A1F20BDC9638D2B525C (void);
extern void U3CLoadFileU3Ed__14_MoveNext_m1316C48AE2CE1957AC76D9226BAFD3F595F22D4B (void);
extern void U3CLoadFileU3Ed__14_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m2DAA70AFDD7AB8E12640584625D22D8A35447CA1 (void);
extern void U3CLoadFileU3Ed__14_System_Collections_IEnumerator_Reset_m17ECB87AE15F394286D0B2DAFFE0227F50BFB7C8 (void);
extern void U3CLoadFileU3Ed__14_System_Collections_IEnumerator_get_Current_mBDC11E6DFA74CFF99F1CDDC7A35FF3BE9DD6F536 (void);
extern void AkObstructionOcclusion_InitIntervalsAndFadeRates_mBB87F034F678D854A61B9EA5CE4E18F1DD7C7C7C (void);
extern void AkObstructionOcclusion_UpdateObstructionOcclusionValues_m071A982E381005333885755F0284B951DD9C3A0C (void);
extern void AkObstructionOcclusion_CastRays_m59620A5526671A84853745B450AB718AB461B637 (void);
extern void AkObstructionOcclusion_Update_m1D02F88DD50D3720018EA8D0D439CF868EE11F51 (void);
extern void AkObstructionOcclusion__ctor_mC819CC16D2636934CFC92F55C3BB7B1591507868 (void);
extern void ObstructionOcclusionValue_Update_m56B1EEBE9D0F66B191A5EEC4237B9DB3B45095E3 (void);
extern void ObstructionOcclusionValue__ctor_m96F5BCEA3532C2A71A82A1FAA6DA50E527A3C625 (void);
extern void AkRadialEmitter_SetGameObjectOuterRadius_m66A4EAB8FB2D0C0AE777F3DB878E72903BC99156 (void);
extern void AkRadialEmitter_SetGameObjectInnerRadius_mD0AFA664F8D8BFF3D4A75E063078FA916707925C (void);
extern void AkRadialEmitter_SetGameObjectRadius_m29C9B420B896320DABF395F61458E4740D0DE03F (void);
extern void AkRadialEmitter_SetGameObjectRadius_m2C20E19C7160D45DA5AB5FD20B26FF0FE4B71D4D (void);
extern void AkRadialEmitter_SetGameObjectRadius_m08036B50BB287E7C8B822B4C0D1EB4AE70DEB9DE (void);
extern void AkRadialEmitter_OnEnable_m141D0A278C23DC4CD7FD5FC0CD8693C58A063102 (void);
extern void AkRadialEmitter__ctor_mCA579DA8B8D3F159610530F2B122D9064EB39FD6 (void);
extern void AkReverbZone_SetReverbZone_m1FAB2E0EA0FC4A8723B7E26BD4AFCDF4A41214AC (void);
extern void AkReverbZone_RemoveReverbZone_m9C744A42EC02CB8E40DE960C757EFA517D5177C1 (void);
extern void AkReverbZone_SetReverbZone_m94EBE9A30F4D54EE909C400E3CABBCD2B2E3580C (void);
extern void AkReverbZone_RemoveReverbZone_m20B5F2504931EDABD47DB78923830EE7524A3F4C (void);
extern void AkReverbZone_OnEnable_m77126A19D3A035FBF58B957DAF2BB54E3C221FDD (void);
extern void AkReverbZone_OnDisable_m046224742D5DF0F56675AF8AFEFDCD28A7FBC9D2 (void);
extern void AkReverbZone_OnValidate_m67D1306DEE5C690C6A591526654A131510FD7B99 (void);
extern void AkReverbZone_Update_mA254B5B3460B07CEFF0329E5EB631E08A8B92EB7 (void);
extern void AkReverbZone__ctor_m72E587C4104077D54DA9094B5542DE5BA4E19B04 (void);
extern void AkRoom_GetAkRoomID_m9FFD9421E203C69197E98610E634A27D042EBD66 (void);
extern void AkRoom_get_RoomCount_m1BA15B52BB8DC6BFC7AD67972B5FB7C099B83D9D (void);
extern void AkRoom_set_RoomCount_m8C900803B19B5CE0CBCB9B115D7C12165CCF5BCC (void);
extern void AkRoom_get_IsAReverbZoneInWwise_mACAEDFBF0B18312AD8E98F3BD0061E46FC952AAE (void);
extern void AkRoom_get_ParentRoomID_m77352D317A358F316FB9B323E6839217ABF1B910 (void);
extern void AkRoom_GetRoomState_mE98AD8B5E75F746F31B501B84ED2F04A54DC4BBA (void);
extern void AkRoom_GetTransformState_m5DAFEEDC06C2FF8E993DF72758787342F528CAFD (void);
extern void AkRoom_GetGeometryState_m2634EEAAE6C4A771D1FA74A663A84474A369953F (void);
extern void AkRoom_TryEnter_m90CCF5A5A6F54701E2E7858C8B14D3DDBF59F374 (void);
extern void AkRoom_Exit_mF060454705E1B8B734CEC7DFBDA593522BD045E4 (void);
extern void AkRoom_GetID_mC3F100BCACFDDC79B2DC0D0FABC999A90990383C (void);
extern void AkRoom_IsAssociatedGeometryFromCollider_mCBCEEB061C550634AF29165F39349D8D76CF8534 (void);
extern void AkRoom_SetGeometryFromCollider_mF19F102E378201F9EC36AA1416AEC101937B1DAC (void);
extern void AkRoom_SetGeometryInstanceFromCollider_m3D957D96F5BFD87C8CB36BB4F158AE4DEEC7E95C (void);
extern void AkRoom_SetRoom_mDFCE9A842836C5DC1138AE85854100ECEEE51131 (void);
extern void AkRoom_SetRoom_m7FF1AE66B686B73AD4BEF2CE9200EE3A673A659C (void);
extern void AkRoom_UsesGeometry_m0BEB7CE08E784A32D1DB1926326F59F50454CBE5 (void);
extern void AkRoom_Update_mBFE7FD58FE555F4A1202A4734CA9EBB74A66DE6F (void);
extern void AkRoom_GetCubeScaleFromCapsule_mAE05F16C69DA9135DDB718EF603271FB9427AAF6 (void);
extern void AkRoom_OnEnable_m8C75BF27CE5C102DA7867BAD79B3B53641A9636D (void);
extern void AkRoom_OnDisable_m55E8163183159CF14C9ADEEB0E1ECEB0B8FE74B4 (void);
extern void AkRoom_OnDestroy_m8F000E6E272017593384D65F281BD8F959FAA843 (void);
extern void AkRoom_OnTriggerEnter_m1BC0D228E5EA4FD2664977EC3F55DAED21D80A6D (void);
extern void AkRoom_OnTriggerExit_mDEEA021627B6FF59B546E133D36E25A6A36C8961 (void);
extern void AkRoom_PostRoomTone_m0353CB2E0D5AC0D8D79CE53AA43087E6C68AA878 (void);
extern void AkRoom_HandleEvent_m53C6F84996700C0ED99B136A6FF06E15CE19FA5E (void);
extern void AkRoom_SetReverbZone_m7D90E71F5DC8491DF26783C41DF511AA0796F3F2 (void);
extern void AkRoom_RemoveReverbZone_m099D466FC8358C545D3A01FA2649BE6515C591A2 (void);
extern void AkRoom_get_wallOcclusion_mB797618ACE650C426E72B10114CE010178E4AA0C (void);
extern void AkRoom_set_wallOcclusion_m997DE7F08C8CF208A0A97A15ECBCC2410693BBD1 (void);
extern void AkRoom_GetGeometryID_mB851116D87C50035F4D79CEED004F72D692A6348 (void);
extern void AkRoom_SetGeometryID_mB09C90014D5145F55D512F15716FFCA3CBC13A33 (void);
extern void AkRoom__ctor_mB23965066BAC0A195D63D95CE8D390A18E0A7A31 (void);
extern void AkRoom__cctor_mC0F0494C13FE9685F34653A504CC0B25600CF344 (void);
extern void PriorityList_GetHighestPriorityActiveAndEnabledRoomID_m5FE9E2A6503DD334B1E886C14AB7B34528BB0B7B (void);
extern void PriorityList_GetHighestPriorityActiveAndEnabledRoom_m2DE4C83C15A8C755615A8367D8C1F7F2DF4E42AC (void);
extern void PriorityList_get_Count_m1B420C7230DBEA595F24A353989C2A3796BF270F (void);
extern void PriorityList_Clear_mE2F56C4B3AAE8EB3873138D859AC190C902B3234 (void);
extern void PriorityList_Add_mB150C9F45D1C12B1BACCC8FE4E6EE703C0055489 (void);
extern void PriorityList_Remove_m44E4F9A118C360048E294FB120280901EDDB4ED2 (void);
extern void PriorityList_Contains_m94D65244A532768EAC035193DEC534F05299C7E2 (void);
extern void PriorityList_BinarySearch_mFF93BF4CCE0E32BE8A35751FB3AEB2A2F9656C51 (void);
extern void PriorityList_get_Item_m92117DD19BB0BCC18C7B126B21D3B0047E1F9D55 (void);
extern void PriorityList__ctor_m8782DC56BF5CEBC101C87E12F9CCF294B1B929D2 (void);
extern void PriorityList__cctor_mA1726ECB8B0DF0454A58B365584946DC3E71D2F4 (void);
extern void CompareByPriority_Compare_mD7DDA32E11F1BBAA17257525289CB39F4ED990B5 (void);
extern void CompareByPriority__ctor_m0C5C813B98B74AC0B1CEFE1AEB07BFDC55ED7918 (void);
extern void AkRoomAwareManager_RegisterRoomAwareObject_mA5590CBE35B21DD9336D06E986AF3FCA86649483 (void);
extern void AkRoomAwareManager_UnregisterRoomAwareObject_m2B01071788647F16BCB24C6515AE8CC1FABC36B4 (void);
extern void AkRoomAwareManager_RegisterRoomAwareObjectForUpdate_m727D1713CC7A6151D8012B763736D06B509B2134 (void);
extern void AkRoomAwareManager_ObjectEnteredRoom_m949D2A1CD6BC84FA6DD13E105FB4B826D038DC70 (void);
extern void AkRoomAwareManager_ObjectEnteredRoom_mD997F784BDED9C4429A7B4A3E5C77334CD66C2B4 (void);
extern void AkRoomAwareManager_ObjectExitedRoom_mD9011B276FD1D5F2F87640F246CD4EA64D87CBE1 (void);
extern void AkRoomAwareManager_ObjectExitedRoom_m7055C35A4AE44B77C631D4E71E017134C421A325 (void);
extern void AkRoomAwareManager_UpdateRoomAwareObjects_m2E40426CED102169B20D8B0BC62B8BFF5CB03C0F (void);
extern void AkRoomAwareManager__cctor_m77EB52B3E58E8F6DB1E23A3B0BFE9E79231A4006 (void);
extern void AkRoomAwareObject_GetAkRoomAwareObjectFromCollider_m3C8CC10D25270981FA9A075487345C0FBD853532 (void);
extern void AkRoomAwareObject_Awake_m078F52F005A53E588ED03EADE969DBD9580AC950 (void);
extern void AkRoomAwareObject_OnEnable_m5644D869F5B5D28F8B63F1D52EF991B534D8AA26 (void);
extern void AkRoomAwareObject_OnDisable_mAE0751CE9AF41A2AF0645FEE8B1510D4E02E4F93 (void);
extern void AkRoomAwareObject_OnDestroy_mF91E2F83887266E400152FF2D453632BD0536004 (void);
extern void AkRoomAwareObject_SetGameObjectInHighestPriorityActiveAndEnabledRoom_m67D28D13EF594ED3C39622A90983F6F1EB14B97A (void);
extern void AkRoomAwareObject_SetGameObjectInRoom_m9D0CB8AF1E3482E191DBC9BBBDD2BB29BD75A532 (void);
extern void AkRoomAwareObject_EnteredRoom_m4CE90AF096C8FAF8E32467ED2CAADB70FEAB13FE (void);
extern void AkRoomAwareObject_ExitedRoom_m1AC15B0925BEAD5588F160E6ED24D5DC4C30B122 (void);
extern void AkRoomAwareObject__ctor_mA919A4786EFD9CA1B72FAA13F2B7CD2C99F06E4B (void);
extern void AkRoomAwareObject__cctor_mA354EF72CB88A888FABA91E475792AD4B6A8989B (void);
extern void AkRoomManager_Init_m7363C4E9B9DD276899B406DE3ADF92A8F78F3EFE (void);
extern void AkRoomManager_Terminate_m96A2235684F63F669B1A15136892DB9504144D79 (void);
extern void AkRoomManager_RegisterPortal_m1759F40339851E1FFF7EB811EAD3A9AE5DA8B07D (void);
extern void AkRoomManager_UnregisterPortal_m1608D2A65E14F4AC1E0B566829C4803032C2DD58 (void);
extern void AkRoomManager_RegisterReflector_m19C920616BD1303D0C586DE762C5A925E2A8F8BD (void);
extern void AkRoomManager_UnregisterReflector_mE5B54142FD7E977B7C65C38E05F1BB1B872558CA (void);
extern void AkRoomManager_RegisterPortalUpdate_mAA931F99EC8917B2BAB10F7ACBA7B3D28700257E (void);
extern void AkRoomManager_RegisterRoomUpdate_m5D9E7BB1219EBFC6F3FA1CE6DF26BAE4ED7BCCB2 (void);
extern void AkRoomManager_Update_m0FA2839D3F7E37916C6BB76FB0551F2EA33615D2 (void);
extern void AkRoomManager__ctor_mF4B2883F0E6275007FA12C4F8BB46E5A82C914E3 (void);
extern void AkRoomPortal_get_portalActive_mE6FA8BB482983538254E1AD30684F7758C3EFC50 (void);
extern void AkRoomPortal_set_portalActive_m98CDA9B305C2C745383A6F52236A2BF8BF7CDEDE (void);
extern void AkRoomPortal_get_frontRoomID_m9063EDB90F23658B5E7FE2AFFDB15F1195954E2D (void);
extern void AkRoomPortal_get_backRoomID_mEB9B1D4779EFE15F5A3356AEAF920301AA5E3203 (void);
extern void AkRoomPortal_GetRoom_mC849629CEB5AC0E687B6A468EA50479FCBA342BF (void);
extern void AkRoomPortal_get_frontRoom_mFBF362D3721CCBD005DBDAE34B823C70B16C5CF7 (void);
extern void AkRoomPortal_get_backRoom_mBB44E644A001A32AC13BAF2A34D582B7306110BA (void);
extern void AkRoomPortal_isSetInWwise_m4E4FE411AE615D00BE4E1C0B5767A5882CFD7D35 (void);
extern void AkRoomPortal_SetRoomPortal_m25707CA71868D19826F01F270A3B54A9469D715D (void);
extern void AkRoomPortal_UpdateRoomPortal_m4D9C2BF44186E39979E48FD002D0CCDB334107DA (void);
extern void AkRoomPortal_Overlaps_m468513C05A202E6E694CBAFE0A490FD049EF6880 (void);
extern void AkRoomPortal_get_IsValid_m4A24AE7F11A2315DB03DC6CFD59E48C4D9A986C0 (void);
extern void AkRoomPortal_GetID_m0BCE7F29B5E3E22AE35C53C9B9351AB523B21572 (void);
extern void AkRoomPortal_Awake_m1529DF0009AE8AD79207662F3075F0EB51EEE7D4 (void);
extern void AkRoomPortal_Start_mF05DD1395726D1B1EAC838A057846774B98FA532 (void);
extern void AkRoomPortal_HandleEvent_mB4DF144C54E48FF1F95512D0A094DFA4C6DFC1E6 (void);
extern void AkRoomPortal_ClosePortal_mD09E5BC86C8D78906C17E882F0DA47BA4B9105A1 (void);
extern void AkRoomPortal_OnDestroy_m94BC37428E7E2905FBF193843D0A74EC80D0DB4C (void);
extern void AkRoomPortal_OnEnable_mDE4B0AFF7A3A97C823ECB73D6FCF17EE1E3217B1 (void);
extern void AkRoomPortal_OnDisable_mF9F61385B08C660D471EE67B35E1EEDFF81DF9C7 (void);
extern void AkRoomPortal_Update_mB0E491E0194E47754C5E97BAF6A1113AD97500BA (void);
extern void AkRoomPortal_IsRoomActive_mD2AFFEB12888A25EAA087566935DFE4EEEA9C688 (void);
extern void AkRoomPortal_Open_mEF2D741DD62E612BDFE4CDC351CF23E112DD1599 (void);
extern void AkRoomPortal_Close_m846810ED65729C4B5089F3DBCB8553D2B1CACE6F (void);
extern void AkRoomPortal_FindOverlappingRooms_m4C6054ECF9359D9EDE1417B8132D930879BD8D5D (void);
extern void AkRoomPortal_FillRoomList_mD7D81666063D6B4AC5660A6695C1D198AA4282E2 (void);
extern void AkRoomPortal_UpdateRooms_m9543667EF3B96EB8ADD7A96DDA95FCC71C72C33D (void);
extern void AkRoomPortal_SetRoom_m1D0405109640421EC7D03DD5808540B040BDF4FE (void);
extern void AkRoomPortal_SetFrontRoom_m9AB9003BF6333E6249DA6F7FFC660A37F79CD139 (void);
extern void AkRoomPortal_SetBackRoom_mCD8C7185261944B38979BAF405DCE5DA21918BB4 (void);
extern void AkRoomPortal_UpdateSoundEngineRoomIDs_m47DB15EDD1766F379EB833214DA3ED7D5167D95D (void);
extern void AkRoomPortal_UpdateOverlappingRooms_m54B7CBE3A058EE6ABE6703CCDA77587012957DA3 (void);
extern void AkRoomPortal__ctor_m4E1C59F9984B5AEE4139B48F95D8D95CBD2D978C (void);
extern void AkRoomPortalObstruction_Awake_m0748A189E817E50646BB8B512D1F888FD4500963 (void);
extern void AkRoomPortalObstruction_UpdateCurrentListenerList_mF6C0A164812A08AB352BFE3B969427E4D600BFD6 (void);
extern void AkRoomPortalObstruction_SetObstructionOcclusion_mE0ED67BE8F956851656B718D793F04878850695B (void);
extern void AkRoomPortalObstruction__ctor_m1B9F8D6CE952ECBB5176E704FE0EA4C3B536C843 (void);
extern void AkSoundEngineController_get_Instance_m18BBB63D85447533F2063CCE9F499707F153100F (void);
extern void AkSoundEngineController__ctor_mA4CC403560FEF51FAA0681758120509DA8D003CC (void);
extern void AkSoundEngineController_Finalize_m3F434DAF681C4C71EEA905CC30557DDD4B1C3435 (void);
extern void AkSoundEngineController_LateUpdate_m27FAF02A8F7F2F294DB9E48CCA0608C2D7339F63 (void);
extern void AkSoundEngineController_GetInitSettingsInstance_mA0D72155ACDB7DE35F74BD6E61E3C8A4EE7B0E94 (void);
extern void AkSoundEngineController_Init_m570357CA0EA5357BEDD4CF02EE34C80369F8AC21 (void);
extern void AkSoundEngineController_OnDisable_m8B6EE878E061C8FCC05BCC52DEFA82C0F7391853 (void);
extern void AkSoundEngineController_Terminate_m5A23D147B3F89993456B2B33BB923444379EE9B3 (void);
extern void AkSoundEngineController_OnApplicationPause_m4EA52ABB2344EE5640F776A68EB6DFA7D8BD5061 (void);
extern void AkSoundEngineController_OnApplicationFocus_m2AE0D02E7B38ACDAB676B5389C3322B7CA9F1F74 (void);
extern void AkSoundEngineController_ActivateAudio_mD5536EDF1E67E931DC90F6E096FEBF1682480935 (void);
extern void AkSpatialAudioDebugDraw__ctor_m12F25288C8D1C84F2C9909247CDB8A2385D43807 (void);
extern void AkSpatialAudioEmitter__ctor_m06896BE6D4E1BD399367D0B0E54405A5DA49F2B3 (void);
extern void AkSpatialAudioListener_get_TheSpatialAudioListener_m24223EB3A47C357DCBE666EE672F73055BE29F17 (void);
extern void AkSpatialAudioListener_get_SpatialAudioListeners_m136F4092D50EBA36453AB5C7C59DE9F080E46469 (void);
extern void AkSpatialAudioListener_Awake_mA4107A6B1DAAF03A375D8939B9FCC4CA59E54220 (void);
extern void AkSpatialAudioListener_OnEnable_m895F69EB36456F955FBB184B237AC1A04A9EF9E4 (void);
extern void AkSpatialAudioListener_OnDisable_m5AE3F4D308E3B55D0FB80C79C221E18C0A661803 (void);
extern void AkSpatialAudioListener__ctor_mC54A514AECBB087572C4C705A426DEFE9F17B760 (void);
extern void AkSpatialAudioListener__cctor_mB25DDA024257A9FB5AC67B1BC05C1DE9B5DAD64F (void);
extern void SpatialAudioListenerList_get_ListenerList_m24C0443DDA2AFB67D33C588D07656517DFE6E9FA (void);
extern void SpatialAudioListenerList_Add_m26689619DE3D9A2AD0E2EF5581E920F9E48087FC (void);
extern void SpatialAudioListenerList_Remove_m8666A6D8BD63980CC8D5BC31C89FB25763F26EBC (void);
extern void SpatialAudioListenerList_Refresh_m9411FEC3B566E3AC719B9C59B0D8368CD38107AB (void);
extern void SpatialAudioListenerList__ctor_m2CD919F3C140D9CD66FC4012F00B507B21E11F37 (void);
extern void AkState_get_WwiseType_m008A7AD295189DA49F7187D23352D919AD01FE73 (void);
extern void AkState_Awake_m607BEE17C8B0E062790B06AE9FBCBDD0AF255B48 (void);
extern void AkState_HandleEvent_m5AD785F2E945B343DAE6C774A40014627BC74994 (void);
extern void AkState_get_valueID_mE079BC73DB5B15560E050155600138A91679E3FB (void);
extern void AkState_get_groupID_mC18F1C1741F5BC06FE6259EEEA57A5A016075C18 (void);
extern void AkState_get_valueGuid_mB9B02C167802764596F9A2CBC2A339F1F2E967BA (void);
extern void AkState_get_groupGuid_mCA0A7784FC30440E91360A6AE2002A188F65229B (void);
extern void AkState__ctor_m7F73F75C3D29D7E8A0F5EC2CC03331A4C8E9B07A (void);
extern void AkSurfaceReflector_GetTransformState_m0597688DA3947E27EF23ADDC33C42D840461BA1F (void);
extern void AkSurfaceReflector_GetGeometryState_m2832123605D30D127AB6262186F03FF571CBD2FA (void);
extern void AkSurfaceReflector_GetAssociatedRoomState_m53DFD6AD1FFAF511AA79FB94D90E7858B2A20488 (void);
extern void AkSurfaceReflector_GetID_mDD76B066CFE64C254E3630BD972D6F0196ABEDEE (void);
extern void AkSurfaceReflector_SetGeometryFromMesh_m9F3588884BA850A90AD880BC992DDC564CC6040C (void);
extern void AkSurfaceReflector_GetGeometryDataFromMesh_m9A39BFFE790C2188855AB9B2CED392353D829BD5 (void);
extern void AkSurfaceReflector_SetGeometryInstance_m714219257B3424DD45AD31D87C471FD718E75E84 (void);
extern void AkSurfaceReflector_SetAssociatedRoom_mC277F4415CB55F8F3D01DD6E22D6ECB371B5BF83 (void);
extern void AkSurfaceReflector_UpdateAssociatedRoom_m94D8FE0C8B89203C3B18D5D3F0B9A32A7D5F4ACF (void);
extern void AkSurfaceReflector_SetGeometry_mEFD67BE82D443F05C53988B6CB2C2BEF574A42D4 (void);
extern void AkSurfaceReflector_SetGeometryInstance_m78CB75607281C9D292F8173C62FE7E9F2E9AD153 (void);
extern void AkSurfaceReflector_UpdateGeometry_mAFBC1EA72F5EC42B10F98535C3C294814439FFC4 (void);
extern void AkSurfaceReflector_RemoveGeometry_mE9B1AC9945FFC7FC15B2B1BE058196B2B399C9D5 (void);
extern void AkSurfaceReflector_RemoveGeometryInstance_m40A074D53D0479E86B64F671FBB10D6CE41AD5AD (void);
extern void AkSurfaceReflector_Awake_mCFDB4DC450D9CF066817D3864362336BE379F160 (void);
extern void AkSurfaceReflector_OnEnable_mE42C843A2416A3167A0BB3A545A2764B3C462B8D (void);
extern void AkSurfaceReflector_OnDisable_m7C81197E8DAD6D03D318AC4C815945FA65F1E0CE (void);
extern void AkSurfaceReflector_OnDestroy_mAEABB1ED77E57FFDB43D5891470DD0DBC3F02FCB (void);
extern void AkSurfaceReflector_Update_mB7BAACDD396B09ADA57D2CB071A71B12ED963D3C (void);
extern void AkSurfaceReflector_GetAkGeometrySetID_m273187D5676F8D01B70942F8A2FFBE5DDC546DD7 (void);
extern void AkSurfaceReflector_AddGeometrySet_mF8A17941F4BAEABB9997426539849999FB89CC83 (void);
extern void AkSurfaceReflector_get_AcousticTexture_mF736E6E67403E9B19EB6B018878BACD0B46E594F (void);
extern void AkSurfaceReflector_set_AcousticTexture_m40B12B4C58C0F4D8AA294FFEF4A2F6DB7C0E5F37 (void);
extern void AkSurfaceReflector_RemoveGeometrySet_m4B6C98F0A0ADEAD6503AA42F74F7EF95D1B8FD21 (void);
extern void AkSurfaceReflector_get_OcclusionValues_m2A957D9E4DD8D692BC0DD34EB9D956217731D562 (void);
extern void AkSurfaceReflector_set_OcclusionValues_mE3C0D9B5203E2BD676F35FCFDF66C5A7B3A90672 (void);
extern void AkSurfaceReflector_SetGeometryFromMesh_m16663A29A6D1CCB3CEBAA1A7B34DB32C00E12618 (void);
extern void AkSurfaceReflector_SetGeometryFromMesh_m689090FF0F7454F9F13BB346234C3E0F19646CB0 (void);
extern void AkSurfaceReflector_SetGeometryInstance_m162EABC22494107B7D822BD1D205AA22CD72442E (void);
extern void AkSurfaceReflector__ctor_m107E1AF26E4F0AAFDAD9949041239D9D68BCE3B2 (void);
extern void AkSwitch_get_WwiseType_mF8F079F13A9B9743B29C667710A85A05621A4515 (void);
extern void AkSwitch_Awake_mB94B687B96282750F8BB68AFAC1ACF3FBBAFF89F (void);
extern void AkSwitch_HandleEvent_m0E37E0F13ECF9794FA2CAFF0D6156AF046D1F67D (void);
extern void AkSwitch_get_valueID_m0806E1C50AF719FB171FCDA8E0D93C4CC31B13A6 (void);
extern void AkSwitch_get_groupID_mFA2727946467A1E6F9485F127CC6A9FD2ED4C54E (void);
extern void AkSwitch_get_valueGuid_m4D5A58DFAE3EC010EDCBCF3E1AA385334B56E6C2 (void);
extern void AkSwitch_get_groupGuid_m6D4DAC791119406A6641465E0D2B0250E53EF4F7 (void);
extern void AkSwitch__ctor_m8A7F5BEB8B3C54157EF65DD07CA3E04E1A98A1B4 (void);
extern void AkTerminator__ctor_mBF10D5C8C172F94199BF9A00EB8486986423F36D (void);
extern void AkTriggerBase_GetAllDerivedTypes_m753935C1E3D411F960E6411E8A831237AA465951 (void);
extern void AkTriggerBase__ctor_mE5C9D591392840D1F5913CDE7890E6A732CC910C (void);
extern void Trigger__ctor_m336F6E1EDD066DA5A30692EA71F7355977E61E27 (void);
extern void Trigger_Invoke_m18D362775B551FB4046A861DB0034B4D6D4530B8 (void);
extern void Trigger_BeginInvoke_mFFCE26DFF8E2DB1A6137EAA5F4F147076D2D8846 (void);
extern void Trigger_EndInvoke_mD5F0E7B6E21DE0E31A97BEE33F2D5D5A320001AA (void);
extern void AkTriggerCollisionEnter_OnCollisionEnter_m63B5E49C9852661CE45920C0EE6575B40621B74D (void);
extern void AkTriggerCollisionEnter_OnTriggerEnter_mF7D2D98C5525885EDB3EC4BB211B96E73E8EB83E (void);
extern void AkTriggerCollisionEnter__ctor_m3D55FC77FB4F0FC4DFE59B25B49DC87F4B524307 (void);
extern void AkTriggerCollisionExit_OnCollisionExit_m2BB9CFD0400B48997E8349CB728BA45B7134A0FF (void);
extern void AkTriggerCollisionExit__ctor_mC466A64824E19641042F47D50A247C50FBB8121E (void);
extern void AkTriggerDisable_OnDisable_m07AE93FE755AECC7B64EB14CDA2B7CDA25FD1871 (void);
extern void AkTriggerDisable__ctor_mB98EEAECDB64F5AE0F722A325D8348C8D64257D7 (void);
extern void AkTriggerEnable__ctor_mB3C8E91D8F5BA6201A54EDDDDA0131BCEEA8CA8E (void);
extern void AkTriggerEnter_OnTriggerEnter_mF3439AE52A5B709901D45DBC5B2DF992A160F96D (void);
extern void AkTriggerEnter__ctor_mA4CC0D2FBAB173FD60A0C5F2432CAC291A86B079 (void);
extern void AkTriggerExit_OnTriggerExit_m35D23688AC53B817E9EFFDE1084A25DE740CEE7B (void);
extern void AkTriggerExit__ctor_m1999E7C19AC6F0A7666E0B47547FAB35C0C128C2 (void);
extern void AkTriggerHandler_Awake_m6E258385FFFFD5B0490574F2DB6EA2505D3C4E8E (void);
extern void AkTriggerHandler_Start_m1A8FD4B9A32A6D7FC1D66448833A70F7A6CA5053 (void);
extern void AkTriggerHandler_OnDestroy_m70C97E261B11EDCD6F19B3F8CF235044FD1E5D34 (void);
extern void AkTriggerHandler_DoDestroy_m42102BD450802799D6DD15DFA75C56BFA21AB9B0 (void);
extern void AkTriggerHandler_OnEnable_m7CDA2C01C0A8E6E5540D79D9E36B89F2620D89E8 (void);
extern void AkTriggerHandler_RegisterTriggers_mA21DDE1A82FED75E1A0FC47A5C7B0F517862F441 (void);
extern void AkTriggerHandler_UnregisterTriggers_m77586DB645862664A1F9C47EA980D3EF81AF68C1 (void);
extern void AkTriggerHandler__ctor_m6DBC177CFC80134E858E6D5F6FB213A41B90040C (void);
extern void AkTriggerHandler__cctor_m75F992F63BB5BE8F0CD0CE996D929C2F0757C66A (void);
extern void AkDragDropTriggerHandler_Awake_m0E2EAD8EDA41829B14257C705B88F7C839AB6845 (void);
extern void AkDragDropTriggerHandler_Start_mDFE3AC5024307B277F5CE7E131B936084342EBA1 (void);
extern void AkDragDropTriggerHandler_OnDestroy_m73FFBD56B0F1A36D329E4954B66CF92C687146EF (void);
extern void AkDragDropTriggerHandler__ctor_m303229B248E3E60471F57826CDF08BC50AE10A1C (void);
extern void AkTriggerMouseDown_OnMouseDown_m74A52FF517D8ADABDCE7C37366CAC3AF6DEB72C9 (void);
extern void AkTriggerMouseDown__ctor_mEF0D28BF7AE6EF2343685EE7E9D06B871331E819 (void);
extern void AkTriggerMouseEnter_OnMouseEnter_mC638D9014EC6666AB9544D47E9FA0926642EE855 (void);
extern void AkTriggerMouseEnter__ctor_mD11C7F581F47CE79EB54F5457B0D55C30ABB5716 (void);
extern void AkTriggerMouseExit_OnMouseExit_m3926F2A57CC3AA9A74F07E4891C5AD1C1F74D816 (void);
extern void AkTriggerMouseExit__ctor_m5450674F2138E645A7D8C2DB8F5E2B6745324ADF (void);
extern void AkTriggerMouseUp_OnMouseUp_mCDA018581F593BD59D81BE91A2E5C1CE5BEB4BB8 (void);
extern void AkTriggerMouseUp__ctor_m9277694D16CCC1352AC564B63108974E19654FC9 (void);
extern void AkWwiseTrigger_get_WwiseType_m8921D9D2F510A8726659E6719AE1ECB54D58D48B (void);
extern void AkWwiseTrigger_Awake_m8F2FD8C07EFEF6020E942A36BAB468C56D822FBD (void);
extern void AkWwiseTrigger_Start_m35E3EFB8BF5B903D54331F7CFCFEC6DF224DA357 (void);
extern void AkWwiseTrigger_HandleEvent_m724BB738500B4EAF94842888C3C891DB23537180 (void);
extern void AkWwiseTrigger__ctor_m5D48C451EA98AB558DFCE829D5315C6E3AFF23F2 (void);
extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_mF7FAC11CC3CADD957C9BF9E696F670EB52D309A2 (void);
extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_m8B6946BD5759414946AA033A7707D4A2141BB343 (void);
static Il2CppMethodPointer s_methodPointers[410] = 
{
	AkMultiPosEvent_FinishedPlaying_mB1DA343FFF409823A2FBBE28516DF2E02798410B,
	AkMultiPosEvent__ctor_m969B286B87988B8FCE1F1A74F24ADC1220552FCF,
	AkAmbient_OnEnable_mDD3055F43B957A7B6294F7937F0949B1C8E84E9C,
	AkAmbient_Start_mF22CA33642A381DF7785E54AAAF8420D8105A554,
	AkAmbient_OnDisable_m1E46830E42E18129AD30940950599218B358B2DD,
	AkAmbient_HandleEvent_m33028A608921D9C164C2474A7970422C6941A104,
	AkAmbient_OnDrawGizmosSelected_mBA595823318C11E518B5A68B10BBF82F18F2B9B1,
	AkAmbient_BuildMultiDirectionArray_mFA570DE8B5173C1AAFE3A560C4F3EC1862EDDD54,
	AkAmbient_BuildAkPositionArray_mE4254647A752BD92236F3E04F406EC91A39F3F54,
	AkAmbient__ctor_m37F0F07E42BE982AB44EFE0B73E14DE5965A6C69,
	AkAmbient__cctor_mDE43BEBA430244F57DE2CA1FC4D9F56AEA9A8DAE,
	AkAmbientLargeModePositioner_get_Position_mFF8FA11422B44B9D26F3EAFAE2589A4FD7A06058,
	AkAmbientLargeModePositioner_get_Forward_m15A55CABD01514B614B4BF1F6DBF2ECC77A3B507,
	AkAmbientLargeModePositioner_get_Up_m982BFFA8B13520D44F04E72B71315DEC23B0F467,
	AkAmbientLargeModePositioner__ctor_mBD8B2BE438B5023926CDE4D26A5D3E6676B119FC,
	AkAudioListener_get_DefaultListeners_m1DA42DB306675B13AD6DE3C6BC274903B5AE8818,
	AkAudioListener_StartListeningToEmitter_m43021CF74B269EF32ADBD0B0EB30DF9AF30B4188,
	AkAudioListener_StopListeningToEmitter_m1F4172B4BBA194355DC888B0E59C6F2FEFBD8CD1,
	AkAudioListener_SetIsDefaultListener_mF83803897233E944962600FC26F08D5D345267CC,
	AkAudioListener_Awake_m1123189AF05EF895F2F31D25826361EDA8095BC8,
	AkAudioListener_OnEnable_mEE167E087D9F0DDDEE6A1BF6A4E05A4860A59C0E,
	AkAudioListener_OnDisable_mD3F6F7E43C89654A1CA4DCC590F71D5123B5F652,
	AkAudioListener_OnDestroy_mBD588EEAD35639CFEA7BD4995922D58CB946B448,
	AkAudioListener_Update_m1FA30F9D13C6F7794FAF6A70582287879753C841,
	AkAudioListener_GetAkGameObjectID_m5C10B12D6B850B5F5FA0C3FF3F179C13E478F03A,
	AkAudioListener_Migrate14_mE976FD130E0014917F9974CF6ADFFB68C015905E,
	AkAudioListener__ctor_mD39B99E45851233BDDA82345669AB5AFDF19B30E,
	AkAudioListener__cctor_m79D7FDDC9F900E525427D1F5025D6CDB08A66F8C,
	BaseListenerList_get_ListenerList_m110246E38BD12FA4A0A8F447879A36A1E2BAE7E3,
	BaseListenerList_Add_m9B97D51304AA1FEE67D50597F91E459EFCCB2582,
	BaseListenerList_Remove_mB091FA5BC74A33E7C77460292CB55D6991308974,
	BaseListenerList_GetListenerIds_mD5658F6BBC9C43392220A61CF6936922B99E5069,
	BaseListenerList__ctor_mCC8C874ED94EB99EF980D5D353078DBEBD34BC2A,
	DefaultListenerList_Add_mBE1534920F565194FC80EF941692E3ABE3F992DC,
	DefaultListenerList_Remove_m9167CE5693E3977C9B5A6BEA95763570DD84EDFF,
	DefaultListenerList__ctor_mBCD862553147AEFFD4283B03BD640002B25917EB,
	AkBank_Awake_m734785B456499606815B5F1D4A0270A92469D542,
	AkBank_Start_m78FA94D5FA357395E9590AEFDE758AF0EEE3A846,
	AkBank_HandleEvent_m553392E0631977E9A49BC1BCB1D1ECE93EEA550A,
	AkBank_HandleEvent_mC0C0A48A5EF1AC9995F1CA601CC56A3B270C92E0,
	AkBank_UnloadBank_m9AB8EC2CC7C93AABF356923D4EC73B226D6D2090,
	AkBank_OnDestroy_m7E823B3B153BCA9827DA0FA22A8D41F997740EF5,
	AkBank_get_bankName_m9045CFFC9011DF21DA2868659C7FE81536DAC657,
	AkBank_get_valueGuid_m1D0469F9792721BA640B35FC6EA1AB4AC2CF0E4B,
	AkBank__ctor_m1ECEBCE925FE38BEABD304E39CEEAAF8C6A06B48,
	AkEarlyReflections_OnEnable_m7073409FC6CD8DC7EA72A975407565E19A1C962E,
	AkEarlyReflections_SetEarlyReflectionsVolume_m41BEEFF298EFE7561282A0F2902DCB48E2685E32,
	AkEarlyReflections__ctor_mFE90A4AD4CE88813A0795054816F882450168602,
	AkEmitterObstructionOcclusion_Awake_m595815388B4E8444DB74C25AC24EDF8371DB03EC,
	AkEmitterObstructionOcclusion_UpdateCurrentListenerList_m2B397C18EEFCFA64580262E23C4B2CB91BBCC862,
	AkEmitterObstructionOcclusion_SetObstructionOcclusion_m5E8CB10D8BD38BC12F51818118E66C655CAD6F9E,
	AkEmitterObstructionOcclusion__ctor_mA26891E5D7C81A02C914343EB2971B249FFF903E,
	AkEnvironment_get_Collider_mE4A3BA8060502135FFD22DBEBC76C6E8DDD1D207,
	AkEnvironment_set_Collider_m7E6854A06A7FD0035042AD3B760C048FC13F2F12,
	AkEnvironment_Awake_mCF3E5ED65D4FBF08682511F5E5858AB156B01669,
	AkEnvironment_get_m_auxBusID_mFCD4C43B299F286F1A3C239FD1852A38C2508B40,
	AkEnvironment_get_valueGuid_m01CA9B50B2410B65AEA3E13EAC58D5DBBD4EDD82,
	AkEnvironment_GetAuxBusID_m9797D4A2D90C8F9605AF78458A7C160998B3FFD6,
	AkEnvironment_GetCollider_m6CC78BD3A9914216048C5D7B7C36043210BDC57F,
	AkEnvironment__ctor_mE5E56C98504D2B0D2EAFDFD49C0507BBA5F34397,
	AkEnvironment__cctor_m5000F3EC28D9979812930B23955AC7B0AB036512,
	AkEnvironment_CompareByPriority_Compare_m3D8802D827B62255BEC83A975651072244A13281,
	AkEnvironment_CompareByPriority__ctor_mA4D2C5A4A584F573B71AD3F618AF0A532D9810EB,
	AkEnvironment_CompareBySelectionAlgorithm_Compare_m57FCFD54AD521C3E6D3D3B49667EEB6AB7A0256A,
	AkEnvironment_CompareBySelectionAlgorithm__ctor_mA7BF0431BFD749525892E9ACA1B99D2654443EFA,
	AkEnvironmentPortal_get_BoxCollider_m63C1B5579BB019914349C237CC19E38F24643226,
	AkEnvironmentPortal_get_EnvironmentsShareAuxBus_m9EE0A319437142E82B578FEF9E6D3ADD4230AC7B,
	AkEnvironmentPortal_GetAuxSendValueForPosition_m3B4E498A364A00906BFFAC5598146DD832D3207C,
	AkEnvironmentPortal__ctor_m372BA4E695DD0E40849585C681BCEED73BDD812B,
	AkEventCallbackMsg__ctor_m7902A40F3A716B63781BD928B0ACE1E0B70CB5CA,
	AkEvent_get_WwiseType_m13C2E5D8BB00B08D2B5E87B0868093502A03B983,
	AkEvent_Awake_m1065E401F0F482D2D167C48B799352870B333C41,
	AkEvent_Start_mD442B7B6F1F2C827C285B4066692B8F410097DB3,
	AkEvent_Callback_m24DF525783BA1FCF47E1DB66E12495201D9DF8A6,
	AkEvent_HandleEvent_m02A435AFA67A02406B48921C51D14088504FAA8A,
	AkEvent_Stop_m74751AF8BDB3C8C6FD5793CF93CB2E9ACEE053B4,
	AkEvent_Stop_mA9AA8F665A0D68155E2D10F74C7E519A2E5F3335,
	AkEvent_get_eventID_m2C557F0564A281BF84AAA2AD29DEB362E4CB5B52,
	AkEvent_get_valueGuid_mA425C6FCFE9311814094E3844E75F3240F2C338F,
	AkEvent_get_m_callbackData_m2E79D2D109964B1460EC0289A501EFE21E49C474,
	AkEvent__ctor_mA17755DAE330E8779A6824DF5CA51D3A2CF1E63C,
	CallbackData_CallFunction_m209C555F73D365840F418E86849A8C9C04BBB377,
	CallbackData__ctor_m4CA98158451205D19B44FBDC29C92321FFB05B1C,
	AkEventCallbackData__ctor_m20C1E0AD49CB3E1CA5D327D491B65B18AF212550,
	AkGameObj_get_IsUsingDefaultListeners_mCB78ABCA467FCF314484DFCFCE4D08F0134068D3,
	AkGameObj_get_ListenerList_m44035423031201A2886708684532B974D58B1155,
	AkGameObj_AddListener_mBA131772E5B90F91E2E1E939A2D33A9B8D894CA9,
	AkGameObj_RemoveListener_m932D77F1BAAE308CA71C41361D79425281C1FBCE,
	AkGameObj_Register_m19BB0D97E161CA814230E861F63B8FD21753C425,
	AkGameObj_UnregisterGameObject_mF82AA1751DF50E447EBFF32ADE35E4C077FD62F1,
	AkGameObj_Unregister_m773A01B4C9F871B51F7C9B4AC164D9A3B1DE9F80,
	AkGameObj_SetPosition_mE6E48EF73298D8B5DA0D6B4D0FE4316A8EF1C7B1,
	AkGameObj_Awake_m0D17D660596EBAD9CAE8E10EC455BB7161EB9E88,
	AkGameObj_RegisterGameObject_m6AE10802D66399A067B17CEBFFCB970F8598CCED,
	AkGameObj_CheckStaticStatus_mB762AC0E35D2A352A1F9FC9ABDA6B5A1E917F25D,
	AkGameObj_OnEnable_m12161CBD8A2BF10D9B5914A4EDD263D3610C82D3,
	AkGameObj_OnDestroy_m66ACFB652EA33670816CE0ED846414963D3506B2,
	AkGameObj_Update_m61A4B63C863C589A72F2242E5C23A778990538D4,
	AkGameObj_GetPosition_mC51C615928A0EED06050BABC2FBCB1B0A5F76FC5,
	AkGameObj_GetForward_m49C636509AE30EB44D25810D633F24E28DC3E366,
	AkGameObj_GetUpward_m823AA8496AE98953761DD2D0D9CBA99A8F8286C5,
	AkGameObj_OnTriggerEnter_mE091BDC21F1986C538FF69C023B96172D217DAF0,
	AkGameObj_OnTriggerExit_m66C793707D2491E76B4585AE485B11B1188754B7,
	AkGameObj__ctor_mEB136EB17F2B2B1669C550A5A09D72B8946FE999,
	AkGameObjEnvironmentData_AddHighestPriorityEnvironmentsFromPortals_m8F9F89273438157AF257755CE44858D4AC7BCC2E,
	AkGameObjEnvironmentData_AddHighestPriorityEnvironments_mD66A1393DFB8ED7C6633AEB68E3545BC6B8E7B1E,
	AkGameObjEnvironmentData_UpdateAuxSend_mD897D1AF64E209109E94E74566A49C9999432BB5,
	AkGameObjEnvironmentData_TryAddEnvironment_m74B686C195D1961E62D5874BCFDA7722114F430B,
	AkGameObjEnvironmentData_RemoveEnvironment_m46B121A69B9D837EBD2B2CB490F7081BF6E33233,
	AkGameObjEnvironmentData_AddAkEnvironment_mBB1D5ABFB680650CA0E632EB0BC62E29615013E0,
	AkGameObjEnvironmentData_AkEnvironmentBelongsToActivePortals_mF55B6FC27B779558EFD0C258E551F59DC7E75A63,
	AkGameObjEnvironmentData_RemoveAkEnvironment_mFB86DB462D1007A05E4FCBD03C3F47C7D59FA967,
	AkGameObjEnvironmentData__ctor_m8366DD2C929D6605CFA184141A109F6C4D11F889,
	AkGameObjListenerList_SetUseDefaultListeners_m45856C245E2A4BB40AAAEE5EED93535D4F0AD73C,
	AkGameObjListenerList_Init_m442531D8E77AD335CA8252625A41568AB9EBEFF5,
	AkGameObjListenerList_Add_m9B9E48E13ADC1C37AD60B78C0BE0DB42B24D38B5,
	AkGameObjListenerList_Remove_m52A7DF1DEC399DABDA3FED66405D75D608A6AC88,
	AkGameObjListenerList__ctor_mF57AD5DB52BAFB54CD6A2041C3FB733B62F3CE63,
	AkGameObjPositionData__ctor_mD2BC9463D55D9B7AF0BE920C621CECF251F99C3F,
	AkGameObjPositionOffsetData__ctor_m8E136C7C35C82EFA74AC0F591ABC94536C32C872,
	AkGameObjPosOffsetData__ctor_mEB8883C8565C0EC6894C6A41675D024A840A3020,
	AkInitializer_CreateRoomGeometryData_m07C86ABA10BA8EDDFD792C439AE109BD2D416EFB,
	AkInitializer_Awake_mF769719EF7589099BE90DC7B1EBD5E2FA2D27557,
	AkInitializer_IsInstance_m0FB9B7261A3205128B23A63B9EB0997C7A19ACA3,
	AkInitializer_GetAkInitializerGameObject_m71AA65E6BF35301AE2F3D5CC4C3BFE92F4A7D945,
	AkInitializer_OnEnable_mB9C437A610EA8FF54EEE699E9EF32EA0282C5A59,
	AkInitializer_InitializeInitializationSettings_mA8108F8794CF2BD3D40C159C65C26D166C6D2984,
	AkInitializer_OnDisable_mC86E2276ED9577309D615AB68BF909508B094C87,
	AkInitializer_OnDestroy_mAF05ED3B6614D5C5230AD32AD5F37C639683B5A3,
	AkInitializer_OnApplicationPause_mB8D738D764CB95268852990EE8BB4C06381FA28D,
	AkInitializer_OnApplicationFocus_m7B1A17920BA497B057A577580AEBB422FB5306A7,
	AkInitializer_OnApplicationQuit_m983DA840DB6F04491496BAEE15B7261D46F5A829,
	AkInitializer_LateUpdate_m0B55517AE06F9845A32756C47ABF4CE9607F7608,
	AkInitializer__ctor_m8CF01BC505FE1BA3AAEE9A163671C2A3DE2AFA7E,
	AkListenerDistanceProbe_OnEnable_mEB0B59DB54110D9D13DFC09D4094E3CBAE0D8B19,
	AkListenerDistanceProbe_OnDisable_m910E4241AB9E482BBB60A3A81C33E42ADB0891DC,
	AkListenerDistanceProbe__ctor_mFA9BCB55EDACA50A2F5133E6EA090770E3720E08,
	AkMemBankLoader_Start_m6C6D1C8140A7861AEA8EF9753ACA1B54D785571F,
	AkMemBankLoader_LoadNonLocalizedBank_mF62439AAE1DF224DF63EA8F7BB00F84C4853A971,
	AkMemBankLoader_LoadLocalizedBank_mA17D8BC6B9834D377CB3F4DF507207587013DD36,
	AkMemBankLoader_AllocateAlignedBuffer_m5E676960B96E5F2944EDB0181C0357924A4E3A6B,
	AkMemBankLoader_LoadFile_m8371D00616572E8321C9CAAF11584D63437EDAFE,
	AkMemBankLoader_DoLoadBank_mC9C73BD375BCFBCC6A9EDAE48886E1AC64B529F2,
	AkMemBankLoader_OnDestroy_m928F2EF90D603BAD47C3E2711F28A39A43D5BF2B,
	AkMemBankLoader__ctor_m4F3D77F5EEFB2C182B0358833BDEB4C1BB878567,
	U3CLoadFileU3Ed__14__ctor_mB219A90910D5676B13E4338364E5CBB1182EE009,
	U3CLoadFileU3Ed__14_System_IDisposable_Dispose_m25D4A1875645611A43307A1F20BDC9638D2B525C,
	U3CLoadFileU3Ed__14_MoveNext_m1316C48AE2CE1957AC76D9226BAFD3F595F22D4B,
	U3CLoadFileU3Ed__14_System_Collections_Generic_IEnumeratorU3CSystem_ObjectU3E_get_Current_m2DAA70AFDD7AB8E12640584625D22D8A35447CA1,
	U3CLoadFileU3Ed__14_System_Collections_IEnumerator_Reset_m17ECB87AE15F394286D0B2DAFFE0227F50BFB7C8,
	U3CLoadFileU3Ed__14_System_Collections_IEnumerator_get_Current_mBDC11E6DFA74CFF99F1CDDC7A35FF3BE9DD6F536,
	AkObstructionOcclusion_InitIntervalsAndFadeRates_mBB87F034F678D854A61B9EA5CE4E18F1DD7C7C7C,
	NULL,
	AkObstructionOcclusion_UpdateObstructionOcclusionValues_m071A982E381005333885755F0284B951DD9C3A0C,
	AkObstructionOcclusion_CastRays_m59620A5526671A84853745B450AB718AB461B637,
	NULL,
	AkObstructionOcclusion_Update_m1D02F88DD50D3720018EA8D0D439CF868EE11F51,
	AkObstructionOcclusion__ctor_mC819CC16D2636934CFC92F55C3BB7B1591507868,
	ObstructionOcclusionValue_Update_m56B1EEBE9D0F66B191A5EEC4237B9DB3B45095E3,
	ObstructionOcclusionValue__ctor_m96F5BCEA3532C2A71A82A1FAA6DA50E527A3C625,
	AkRadialEmitter_SetGameObjectOuterRadius_m66A4EAB8FB2D0C0AE777F3DB878E72903BC99156,
	AkRadialEmitter_SetGameObjectInnerRadius_mD0AFA664F8D8BFF3D4A75E063078FA916707925C,
	AkRadialEmitter_SetGameObjectRadius_m29C9B420B896320DABF395F61458E4740D0DE03F,
	AkRadialEmitter_SetGameObjectRadius_m2C20E19C7160D45DA5AB5FD20B26FF0FE4B71D4D,
	AkRadialEmitter_SetGameObjectRadius_m08036B50BB287E7C8B822B4C0D1EB4AE70DEB9DE,
	AkRadialEmitter_OnEnable_m141D0A278C23DC4CD7FD5FC0CD8693C58A063102,
	AkRadialEmitter__ctor_mCA579DA8B8D3F159610530F2B122D9064EB39FD6,
	AkReverbZone_SetReverbZone_m1FAB2E0EA0FC4A8723B7E26BD4AFCDF4A41214AC,
	AkReverbZone_RemoveReverbZone_m9C744A42EC02CB8E40DE960C757EFA517D5177C1,
	AkReverbZone_SetReverbZone_m94EBE9A30F4D54EE909C400E3CABBCD2B2E3580C,
	AkReverbZone_RemoveReverbZone_m20B5F2504931EDABD47DB78923830EE7524A3F4C,
	AkReverbZone_OnEnable_m77126A19D3A035FBF58B957DAF2BB54E3C221FDD,
	AkReverbZone_OnDisable_m046224742D5DF0F56675AF8AFEFDCD28A7FBC9D2,
	AkReverbZone_OnValidate_m67D1306DEE5C690C6A591526654A131510FD7B99,
	AkReverbZone_Update_mA254B5B3460B07CEFF0329E5EB631E08A8B92EB7,
	AkReverbZone__ctor_m72E587C4104077D54DA9094B5542DE5BA4E19B04,
	AkRoom_GetAkRoomID_m9FFD9421E203C69197E98610E634A27D042EBD66,
	AkRoom_get_RoomCount_m1BA15B52BB8DC6BFC7AD67972B5FB7C099B83D9D,
	AkRoom_set_RoomCount_m8C900803B19B5CE0CBCB9B115D7C12165CCF5BCC,
	AkRoom_get_IsAReverbZoneInWwise_mACAEDFBF0B18312AD8E98F3BD0061E46FC952AAE,
	AkRoom_get_ParentRoomID_m77352D317A358F316FB9B323E6839217ABF1B910,
	AkRoom_GetRoomState_mE98AD8B5E75F746F31B501B84ED2F04A54DC4BBA,
	AkRoom_GetTransformState_m5DAFEEDC06C2FF8E993DF72758787342F528CAFD,
	AkRoom_GetGeometryState_m2634EEAAE6C4A771D1FA74A663A84474A369953F,
	AkRoom_TryEnter_m90CCF5A5A6F54701E2E7858C8B14D3DDBF59F374,
	AkRoom_Exit_mF060454705E1B8B734CEC7DFBDA593522BD045E4,
	AkRoom_GetID_mC3F100BCACFDDC79B2DC0D0FABC999A90990383C,
	AkRoom_IsAssociatedGeometryFromCollider_mCBCEEB061C550634AF29165F39349D8D76CF8534,
	AkRoom_SetGeometryFromCollider_mF19F102E378201F9EC36AA1416AEC101937B1DAC,
	AkRoom_SetGeometryInstanceFromCollider_m3D957D96F5BFD87C8CB36BB4F158AE4DEEC7E95C,
	AkRoom_SetRoom_mDFCE9A842836C5DC1138AE85854100ECEEE51131,
	AkRoom_SetRoom_m7FF1AE66B686B73AD4BEF2CE9200EE3A673A659C,
	AkRoom_UsesGeometry_m0BEB7CE08E784A32D1DB1926326F59F50454CBE5,
	AkRoom_Update_mBFE7FD58FE555F4A1202A4734CA9EBB74A66DE6F,
	AkRoom_GetCubeScaleFromCapsule_mAE05F16C69DA9135DDB718EF603271FB9427AAF6,
	AkRoom_OnEnable_m8C75BF27CE5C102DA7867BAD79B3B53641A9636D,
	AkRoom_OnDisable_m55E8163183159CF14C9ADEEB0E1ECEB0B8FE74B4,
	AkRoom_OnDestroy_m8F000E6E272017593384D65F281BD8F959FAA843,
	AkRoom_OnTriggerEnter_m1BC0D228E5EA4FD2664977EC3F55DAED21D80A6D,
	AkRoom_OnTriggerExit_mDEEA021627B6FF59B546E133D36E25A6A36C8961,
	AkRoom_PostRoomTone_m0353CB2E0D5AC0D8D79CE53AA43087E6C68AA878,
	AkRoom_HandleEvent_m53C6F84996700C0ED99B136A6FF06E15CE19FA5E,
	AkRoom_SetReverbZone_m7D90E71F5DC8491DF26783C41DF511AA0796F3F2,
	AkRoom_RemoveReverbZone_m099D466FC8358C545D3A01FA2649BE6515C591A2,
	AkRoom_get_wallOcclusion_mB797618ACE650C426E72B10114CE010178E4AA0C,
	AkRoom_set_wallOcclusion_m997DE7F08C8CF208A0A97A15ECBCC2410693BBD1,
	AkRoom_GetGeometryID_mB851116D87C50035F4D79CEED004F72D692A6348,
	AkRoom_SetGeometryID_mB09C90014D5145F55D512F15716FFCA3CBC13A33,
	AkRoom__ctor_mB23965066BAC0A195D63D95CE8D390A18E0A7A31,
	AkRoom__cctor_mC0F0494C13FE9685F34653A504CC0B25600CF344,
	PriorityList_GetHighestPriorityActiveAndEnabledRoomID_m5FE9E2A6503DD334B1E886C14AB7B34528BB0B7B,
	PriorityList_GetHighestPriorityActiveAndEnabledRoom_m2DE4C83C15A8C755615A8367D8C1F7F2DF4E42AC,
	PriorityList_get_Count_m1B420C7230DBEA595F24A353989C2A3796BF270F,
	PriorityList_Clear_mE2F56C4B3AAE8EB3873138D859AC190C902B3234,
	PriorityList_Add_mB150C9F45D1C12B1BACCC8FE4E6EE703C0055489,
	PriorityList_Remove_m44E4F9A118C360048E294FB120280901EDDB4ED2,
	PriorityList_Contains_m94D65244A532768EAC035193DEC534F05299C7E2,
	PriorityList_BinarySearch_mFF93BF4CCE0E32BE8A35751FB3AEB2A2F9656C51,
	PriorityList_get_Item_m92117DD19BB0BCC18C7B126B21D3B0047E1F9D55,
	PriorityList__ctor_m8782DC56BF5CEBC101C87E12F9CCF294B1B929D2,
	PriorityList__cctor_mA1726ECB8B0DF0454A58B365584946DC3E71D2F4,
	CompareByPriority_Compare_mD7DDA32E11F1BBAA17257525289CB39F4ED990B5,
	CompareByPriority__ctor_m0C5C813B98B74AC0B1CEFE1AEB07BFDC55ED7918,
	AkRoomAwareManager_RegisterRoomAwareObject_mA5590CBE35B21DD9336D06E986AF3FCA86649483,
	AkRoomAwareManager_UnregisterRoomAwareObject_m2B01071788647F16BCB24C6515AE8CC1FABC36B4,
	AkRoomAwareManager_RegisterRoomAwareObjectForUpdate_m727D1713CC7A6151D8012B763736D06B509B2134,
	AkRoomAwareManager_ObjectEnteredRoom_m949D2A1CD6BC84FA6DD13E105FB4B826D038DC70,
	AkRoomAwareManager_ObjectEnteredRoom_mD997F784BDED9C4429A7B4A3E5C77334CD66C2B4,
	AkRoomAwareManager_ObjectExitedRoom_mD9011B276FD1D5F2F87640F246CD4EA64D87CBE1,
	AkRoomAwareManager_ObjectExitedRoom_m7055C35A4AE44B77C631D4E71E017134C421A325,
	AkRoomAwareManager_UpdateRoomAwareObjects_m2E40426CED102169B20D8B0BC62B8BFF5CB03C0F,
	AkRoomAwareManager__cctor_m77EB52B3E58E8F6DB1E23A3B0BFE9E79231A4006,
	AkRoomAwareObject_GetAkRoomAwareObjectFromCollider_m3C8CC10D25270981FA9A075487345C0FBD853532,
	AkRoomAwareObject_Awake_m078F52F005A53E588ED03EADE969DBD9580AC950,
	AkRoomAwareObject_OnEnable_m5644D869F5B5D28F8B63F1D52EF991B534D8AA26,
	AkRoomAwareObject_OnDisable_mAE0751CE9AF41A2AF0645FEE8B1510D4E02E4F93,
	AkRoomAwareObject_OnDestroy_mF91E2F83887266E400152FF2D453632BD0536004,
	AkRoomAwareObject_SetGameObjectInHighestPriorityActiveAndEnabledRoom_m67D28D13EF594ED3C39622A90983F6F1EB14B97A,
	AkRoomAwareObject_SetGameObjectInRoom_m9D0CB8AF1E3482E191DBC9BBBDD2BB29BD75A532,
	AkRoomAwareObject_EnteredRoom_m4CE90AF096C8FAF8E32467ED2CAADB70FEAB13FE,
	AkRoomAwareObject_ExitedRoom_m1AC15B0925BEAD5588F160E6ED24D5DC4C30B122,
	AkRoomAwareObject__ctor_mA919A4786EFD9CA1B72FAA13F2B7CD2C99F06E4B,
	AkRoomAwareObject__cctor_mA354EF72CB88A888FABA91E475792AD4B6A8989B,
	AkRoomManager_Init_m7363C4E9B9DD276899B406DE3ADF92A8F78F3EFE,
	AkRoomManager_Terminate_m96A2235684F63F669B1A15136892DB9504144D79,
	AkRoomManager_RegisterPortal_m1759F40339851E1FFF7EB811EAD3A9AE5DA8B07D,
	AkRoomManager_UnregisterPortal_m1608D2A65E14F4AC1E0B566829C4803032C2DD58,
	AkRoomManager_RegisterReflector_m19C920616BD1303D0C586DE762C5A925E2A8F8BD,
	AkRoomManager_UnregisterReflector_mE5B54142FD7E977B7C65C38E05F1BB1B872558CA,
	AkRoomManager_RegisterPortalUpdate_mAA931F99EC8917B2BAB10F7ACBA7B3D28700257E,
	AkRoomManager_RegisterRoomUpdate_m5D9E7BB1219EBFC6F3FA1CE6DF26BAE4ED7BCCB2,
	AkRoomManager_Update_m0FA2839D3F7E37916C6BB76FB0551F2EA33615D2,
	AkRoomManager__ctor_mF4B2883F0E6275007FA12C4F8BB46E5A82C914E3,
	AkRoomPortal_get_portalActive_mE6FA8BB482983538254E1AD30684F7758C3EFC50,
	AkRoomPortal_set_portalActive_m98CDA9B305C2C745383A6F52236A2BF8BF7CDEDE,
	AkRoomPortal_get_frontRoomID_m9063EDB90F23658B5E7FE2AFFDB15F1195954E2D,
	AkRoomPortal_get_backRoomID_mEB9B1D4779EFE15F5A3356AEAF920301AA5E3203,
	AkRoomPortal_GetRoom_mC849629CEB5AC0E687B6A468EA50479FCBA342BF,
	AkRoomPortal_get_frontRoom_mFBF362D3721CCBD005DBDAE34B823C70B16C5CF7,
	AkRoomPortal_get_backRoom_mBB44E644A001A32AC13BAF2A34D582B7306110BA,
	AkRoomPortal_isSetInWwise_m4E4FE411AE615D00BE4E1C0B5767A5882CFD7D35,
	AkRoomPortal_SetRoomPortal_m25707CA71868D19826F01F270A3B54A9469D715D,
	AkRoomPortal_UpdateRoomPortal_m4D9C2BF44186E39979E48FD002D0CCDB334107DA,
	AkRoomPortal_Overlaps_m468513C05A202E6E694CBAFE0A490FD049EF6880,
	AkRoomPortal_get_IsValid_m4A24AE7F11A2315DB03DC6CFD59E48C4D9A986C0,
	AkRoomPortal_GetID_m0BCE7F29B5E3E22AE35C53C9B9351AB523B21572,
	AkRoomPortal_Awake_m1529DF0009AE8AD79207662F3075F0EB51EEE7D4,
	AkRoomPortal_Start_mF05DD1395726D1B1EAC838A057846774B98FA532,
	AkRoomPortal_HandleEvent_mB4DF144C54E48FF1F95512D0A094DFA4C6DFC1E6,
	AkRoomPortal_ClosePortal_mD09E5BC86C8D78906C17E882F0DA47BA4B9105A1,
	AkRoomPortal_OnDestroy_m94BC37428E7E2905FBF193843D0A74EC80D0DB4C,
	AkRoomPortal_OnEnable_mDE4B0AFF7A3A97C823ECB73D6FCF17EE1E3217B1,
	AkRoomPortal_OnDisable_mF9F61385B08C660D471EE67B35E1EEDFF81DF9C7,
	AkRoomPortal_Update_mB0E491E0194E47754C5E97BAF6A1113AD97500BA,
	AkRoomPortal_IsRoomActive_mD2AFFEB12888A25EAA087566935DFE4EEEA9C688,
	AkRoomPortal_Open_mEF2D741DD62E612BDFE4CDC351CF23E112DD1599,
	AkRoomPortal_Close_m846810ED65729C4B5089F3DBCB8553D2B1CACE6F,
	AkRoomPortal_FindOverlappingRooms_m4C6054ECF9359D9EDE1417B8132D930879BD8D5D,
	AkRoomPortal_FillRoomList_mD7D81666063D6B4AC5660A6695C1D198AA4282E2,
	AkRoomPortal_UpdateRooms_m9543667EF3B96EB8ADD7A96DDA95FCC71C72C33D,
	AkRoomPortal_SetRoom_m1D0405109640421EC7D03DD5808540B040BDF4FE,
	AkRoomPortal_SetFrontRoom_m9AB9003BF6333E6249DA6F7FFC660A37F79CD139,
	AkRoomPortal_SetBackRoom_mCD8C7185261944B38979BAF405DCE5DA21918BB4,
	AkRoomPortal_UpdateSoundEngineRoomIDs_m47DB15EDD1766F379EB833214DA3ED7D5167D95D,
	AkRoomPortal_UpdateOverlappingRooms_m54B7CBE3A058EE6ABE6703CCDA77587012957DA3,
	AkRoomPortal__ctor_m4E1C59F9984B5AEE4139B48F95D8D95CBD2D978C,
	AkRoomPortalObstruction_Awake_m0748A189E817E50646BB8B512D1F888FD4500963,
	AkRoomPortalObstruction_UpdateCurrentListenerList_mF6C0A164812A08AB352BFE3B969427E4D600BFD6,
	AkRoomPortalObstruction_SetObstructionOcclusion_mE0ED67BE8F956851656B718D793F04878850695B,
	AkRoomPortalObstruction__ctor_m1B9F8D6CE952ECBB5176E704FE0EA4C3B536C843,
	AkSoundEngineController_get_Instance_m18BBB63D85447533F2063CCE9F499707F153100F,
	AkSoundEngineController__ctor_mA4CC403560FEF51FAA0681758120509DA8D003CC,
	AkSoundEngineController_Finalize_m3F434DAF681C4C71EEA905CC30557DDD4B1C3435,
	AkSoundEngineController_LateUpdate_m27FAF02A8F7F2F294DB9E48CCA0608C2D7339F63,
	AkSoundEngineController_GetInitSettingsInstance_mA0D72155ACDB7DE35F74BD6E61E3C8A4EE7B0E94,
	AkSoundEngineController_Init_m570357CA0EA5357BEDD4CF02EE34C80369F8AC21,
	AkSoundEngineController_OnDisable_m8B6EE878E061C8FCC05BCC52DEFA82C0F7391853,
	AkSoundEngineController_Terminate_m5A23D147B3F89993456B2B33BB923444379EE9B3,
	AkSoundEngineController_OnApplicationPause_m4EA52ABB2344EE5640F776A68EB6DFA7D8BD5061,
	AkSoundEngineController_OnApplicationFocus_m2AE0D02E7B38ACDAB676B5389C3322B7CA9F1F74,
	AkSoundEngineController_ActivateAudio_mD5536EDF1E67E931DC90F6E096FEBF1682480935,
	AkSpatialAudioDebugDraw__ctor_m12F25288C8D1C84F2C9909247CDB8A2385D43807,
	AkSpatialAudioEmitter__ctor_m06896BE6D4E1BD399367D0B0E54405A5DA49F2B3,
	AkSpatialAudioListener_get_TheSpatialAudioListener_m24223EB3A47C357DCBE666EE672F73055BE29F17,
	AkSpatialAudioListener_get_SpatialAudioListeners_m136F4092D50EBA36453AB5C7C59DE9F080E46469,
	AkSpatialAudioListener_Awake_mA4107A6B1DAAF03A375D8939B9FCC4CA59E54220,
	AkSpatialAudioListener_OnEnable_m895F69EB36456F955FBB184B237AC1A04A9EF9E4,
	AkSpatialAudioListener_OnDisable_m5AE3F4D308E3B55D0FB80C79C221E18C0A661803,
	AkSpatialAudioListener__ctor_mC54A514AECBB087572C4C705A426DEFE9F17B760,
	AkSpatialAudioListener__cctor_mB25DDA024257A9FB5AC67B1BC05C1DE9B5DAD64F,
	SpatialAudioListenerList_get_ListenerList_m24C0443DDA2AFB67D33C588D07656517DFE6E9FA,
	SpatialAudioListenerList_Add_m26689619DE3D9A2AD0E2EF5581E920F9E48087FC,
	SpatialAudioListenerList_Remove_m8666A6D8BD63980CC8D5BC31C89FB25763F26EBC,
	SpatialAudioListenerList_Refresh_m9411FEC3B566E3AC719B9C59B0D8368CD38107AB,
	SpatialAudioListenerList__ctor_m2CD919F3C140D9CD66FC4012F00B507B21E11F37,
	AkState_get_WwiseType_m008A7AD295189DA49F7187D23352D919AD01FE73,
	AkState_Awake_m607BEE17C8B0E062790B06AE9FBCBDD0AF255B48,
	AkState_HandleEvent_m5AD785F2E945B343DAE6C774A40014627BC74994,
	AkState_get_valueID_mE079BC73DB5B15560E050155600138A91679E3FB,
	AkState_get_groupID_mC18F1C1741F5BC06FE6259EEEA57A5A016075C18,
	AkState_get_valueGuid_mB9B02C167802764596F9A2CBC2A339F1F2E967BA,
	AkState_get_groupGuid_mCA0A7784FC30440E91360A6AE2002A188F65229B,
	AkState__ctor_m7F73F75C3D29D7E8A0F5EC2CC03331A4C8E9B07A,
	AkSurfaceReflector_GetTransformState_m0597688DA3947E27EF23ADDC33C42D840461BA1F,
	AkSurfaceReflector_GetGeometryState_m2832123605D30D127AB6262186F03FF571CBD2FA,
	AkSurfaceReflector_GetAssociatedRoomState_m53DFD6AD1FFAF511AA79FB94D90E7858B2A20488,
	AkSurfaceReflector_GetID_mDD76B066CFE64C254E3630BD972D6F0196ABEDEE,
	AkSurfaceReflector_SetGeometryFromMesh_m9F3588884BA850A90AD880BC992DDC564CC6040C,
	AkSurfaceReflector_GetGeometryDataFromMesh_m9A39BFFE790C2188855AB9B2CED392353D829BD5,
	AkSurfaceReflector_SetGeometryInstance_m714219257B3424DD45AD31D87C471FD718E75E84,
	AkSurfaceReflector_SetAssociatedRoom_mC277F4415CB55F8F3D01DD6E22D6ECB371B5BF83,
	AkSurfaceReflector_UpdateAssociatedRoom_m94D8FE0C8B89203C3B18D5D3F0B9A32A7D5F4ACF,
	AkSurfaceReflector_SetGeometry_mEFD67BE82D443F05C53988B6CB2C2BEF574A42D4,
	AkSurfaceReflector_SetGeometryInstance_m78CB75607281C9D292F8173C62FE7E9F2E9AD153,
	AkSurfaceReflector_UpdateGeometry_mAFBC1EA72F5EC42B10F98535C3C294814439FFC4,
	AkSurfaceReflector_RemoveGeometry_mE9B1AC9945FFC7FC15B2B1BE058196B2B399C9D5,
	AkSurfaceReflector_RemoveGeometryInstance_m40A074D53D0479E86B64F671FBB10D6CE41AD5AD,
	AkSurfaceReflector_Awake_mCFDB4DC450D9CF066817D3864362336BE379F160,
	AkSurfaceReflector_OnEnable_mE42C843A2416A3167A0BB3A545A2764B3C462B8D,
	AkSurfaceReflector_OnDisable_m7C81197E8DAD6D03D318AC4C815945FA65F1E0CE,
	AkSurfaceReflector_OnDestroy_mAEABB1ED77E57FFDB43D5891470DD0DBC3F02FCB,
	AkSurfaceReflector_Update_mB7BAACDD396B09ADA57D2CB071A71B12ED963D3C,
	AkSurfaceReflector_GetAkGeometrySetID_m273187D5676F8D01B70942F8A2FFBE5DDC546DD7,
	AkSurfaceReflector_AddGeometrySet_mF8A17941F4BAEABB9997426539849999FB89CC83,
	AkSurfaceReflector_get_AcousticTexture_mF736E6E67403E9B19EB6B018878BACD0B46E594F,
	AkSurfaceReflector_set_AcousticTexture_m40B12B4C58C0F4D8AA294FFEF4A2F6DB7C0E5F37,
	AkSurfaceReflector_RemoveGeometrySet_m4B6C98F0A0ADEAD6503AA42F74F7EF95D1B8FD21,
	AkSurfaceReflector_get_OcclusionValues_m2A957D9E4DD8D692BC0DD34EB9D956217731D562,
	AkSurfaceReflector_set_OcclusionValues_mE3C0D9B5203E2BD676F35FCFDF66C5A7B3A90672,
	AkSurfaceReflector_SetGeometryFromMesh_m16663A29A6D1CCB3CEBAA1A7B34DB32C00E12618,
	AkSurfaceReflector_SetGeometryFromMesh_m689090FF0F7454F9F13BB346234C3E0F19646CB0,
	AkSurfaceReflector_SetGeometryInstance_m162EABC22494107B7D822BD1D205AA22CD72442E,
	AkSurfaceReflector__ctor_m107E1AF26E4F0AAFDAD9949041239D9D68BCE3B2,
	AkSwitch_get_WwiseType_mF8F079F13A9B9743B29C667710A85A05621A4515,
	AkSwitch_Awake_mB94B687B96282750F8BB68AFAC1ACF3FBBAFF89F,
	AkSwitch_HandleEvent_m0E37E0F13ECF9794FA2CAFF0D6156AF046D1F67D,
	AkSwitch_get_valueID_m0806E1C50AF719FB171FCDA8E0D93C4CC31B13A6,
	AkSwitch_get_groupID_mFA2727946467A1E6F9485F127CC6A9FD2ED4C54E,
	AkSwitch_get_valueGuid_m4D5A58DFAE3EC010EDCBCF3E1AA385334B56E6C2,
	AkSwitch_get_groupGuid_m6D4DAC791119406A6641465E0D2B0250E53EF4F7,
	AkSwitch__ctor_m8A7F5BEB8B3C54157EF65DD07CA3E04E1A98A1B4,
	AkTerminator__ctor_mBF10D5C8C172F94199BF9A00EB8486986423F36D,
	AkTriggerBase_GetAllDerivedTypes_m753935C1E3D411F960E6411E8A831237AA465951,
	AkTriggerBase__ctor_mE5C9D591392840D1F5913CDE7890E6A732CC910C,
	Trigger__ctor_m336F6E1EDD066DA5A30692EA71F7355977E61E27,
	Trigger_Invoke_m18D362775B551FB4046A861DB0034B4D6D4530B8,
	Trigger_BeginInvoke_mFFCE26DFF8E2DB1A6137EAA5F4F147076D2D8846,
	Trigger_EndInvoke_mD5F0E7B6E21DE0E31A97BEE33F2D5D5A320001AA,
	AkTriggerCollisionEnter_OnCollisionEnter_m63B5E49C9852661CE45920C0EE6575B40621B74D,
	AkTriggerCollisionEnter_OnTriggerEnter_mF7D2D98C5525885EDB3EC4BB211B96E73E8EB83E,
	AkTriggerCollisionEnter__ctor_m3D55FC77FB4F0FC4DFE59B25B49DC87F4B524307,
	AkTriggerCollisionExit_OnCollisionExit_m2BB9CFD0400B48997E8349CB728BA45B7134A0FF,
	AkTriggerCollisionExit__ctor_mC466A64824E19641042F47D50A247C50FBB8121E,
	AkTriggerDisable_OnDisable_m07AE93FE755AECC7B64EB14CDA2B7CDA25FD1871,
	AkTriggerDisable__ctor_mB98EEAECDB64F5AE0F722A325D8348C8D64257D7,
	AkTriggerEnable__ctor_mB3C8E91D8F5BA6201A54EDDDDA0131BCEEA8CA8E,
	AkTriggerEnter_OnTriggerEnter_mF3439AE52A5B709901D45DBC5B2DF992A160F96D,
	AkTriggerEnter__ctor_mA4CC0D2FBAB173FD60A0C5F2432CAC291A86B079,
	AkTriggerExit_OnTriggerExit_m35D23688AC53B817E9EFFDE1084A25DE740CEE7B,
	AkTriggerExit__ctor_m1999E7C19AC6F0A7666E0B47547FAB35C0C128C2,
	NULL,
	AkTriggerHandler_Awake_m6E258385FFFFD5B0490574F2DB6EA2505D3C4E8E,
	AkTriggerHandler_Start_m1A8FD4B9A32A6D7FC1D66448833A70F7A6CA5053,
	AkTriggerHandler_OnDestroy_m70C97E261B11EDCD6F19B3F8CF235044FD1E5D34,
	AkTriggerHandler_DoDestroy_m42102BD450802799D6DD15DFA75C56BFA21AB9B0,
	AkTriggerHandler_OnEnable_m7CDA2C01C0A8E6E5540D79D9E36B89F2620D89E8,
	AkTriggerHandler_RegisterTriggers_mA21DDE1A82FED75E1A0FC47A5C7B0F517862F441,
	AkTriggerHandler_UnregisterTriggers_m77586DB645862664A1F9C47EA980D3EF81AF68C1,
	AkTriggerHandler__ctor_m6DBC177CFC80134E858E6D5F6FB213A41B90040C,
	AkTriggerHandler__cctor_m75F992F63BB5BE8F0CD0CE996D929C2F0757C66A,
	NULL,
	AkDragDropTriggerHandler_Awake_m0E2EAD8EDA41829B14257C705B88F7C839AB6845,
	AkDragDropTriggerHandler_Start_mDFE3AC5024307B277F5CE7E131B936084342EBA1,
	AkDragDropTriggerHandler_OnDestroy_m73FFBD56B0F1A36D329E4954B66CF92C687146EF,
	AkDragDropTriggerHandler__ctor_m303229B248E3E60471F57826CDF08BC50AE10A1C,
	AkTriggerMouseDown_OnMouseDown_m74A52FF517D8ADABDCE7C37366CAC3AF6DEB72C9,
	AkTriggerMouseDown__ctor_mEF0D28BF7AE6EF2343685EE7E9D06B871331E819,
	AkTriggerMouseEnter_OnMouseEnter_mC638D9014EC6666AB9544D47E9FA0926642EE855,
	AkTriggerMouseEnter__ctor_mD11C7F581F47CE79EB54F5457B0D55C30ABB5716,
	AkTriggerMouseExit_OnMouseExit_m3926F2A57CC3AA9A74F07E4891C5AD1C1F74D816,
	AkTriggerMouseExit__ctor_m5450674F2138E645A7D8C2DB8F5E2B6745324ADF,
	AkTriggerMouseUp_OnMouseUp_mCDA018581F593BD59D81BE91A2E5C1CE5BEB4BB8,
	AkTriggerMouseUp__ctor_m9277694D16CCC1352AC564B63108974E19654FC9,
	AkWwiseTrigger_get_WwiseType_m8921D9D2F510A8726659E6719AE1ECB54D58D48B,
	AkWwiseTrigger_Awake_m8F2FD8C07EFEF6020E942A36BAB468C56D822FBD,
	AkWwiseTrigger_Start_m35E3EFB8BF5B903D54331F7CFCFEC6DF224DA357,
	AkWwiseTrigger_HandleEvent_m724BB738500B4EAF94842888C3C891DB23537180,
	AkWwiseTrigger__ctor_m5D48C451EA98AB558DFCE829D5315C6E3AFF23F2,
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_mF7FAC11CC3CADD957C9BF9E696F670EB52D309A2,
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_m8B6946BD5759414946AA033A7707D4A2141BB343,
};
static const int32_t s_InvokerIndices[410] = 
{
	2739,
	13298,
	13298,
	13298,
	13298,
	10682,
	13298,
	9272,
	13052,
	13298,
	21355,
	13280,
	13280,
	13280,
	13298,
	21274,
	10682,
	10682,
	10442,
	13298,
	13298,
	13298,
	13298,
	13298,
	13262,
	13298,
	13298,
	21355,
	13052,
	7736,
	7736,
	13052,
	13298,
	7736,
	7736,
	13298,
	13298,
	13298,
	10682,
	13298,
	10682,
	13298,
	13052,
	13052,
	13298,
	13298,
	10823,
	13298,
	13298,
	13298,
	9844,
	13298,
	13052,
	10682,
	13298,
	12996,
	13052,
	13261,
	13052,
	13298,
	21355,
	4160,
	13298,
	4160,
	13298,
	13052,
	12815,
	4615,
	13298,
	13298,
	13052,
	13298,
	13298,
	2739,
	10682,
	10629,
	5266,
	12996,
	13052,
	13052,
	13298,
	10682,
	13298,
	13298,
	12815,
	13052,
	10682,
	10682,
	12996,
	13298,
	12996,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13280,
	13280,
	13280,
	10682,
	10682,
	13298,
	10912,
	10912,
	5716,
	10682,
	10682,
	5688,
	7736,
	5688,
	13298,
	10442,
	10682,
	7736,
	7736,
	13298,
	13298,
	10442,
	13298,
	13298,
	13298,
	12815,
	21274,
	13298,
	13298,
	13298,
	13298,
	10442,
	10442,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	10682,
	10682,
	9565,
	13052,
	10682,
	13298,
	13298,
	10629,
	13298,
	12815,
	13052,
	13298,
	13052,
	13298,
	0,
	13298,
	13298,
	0,
	13298,
	13298,
	7822,
	13298,
	10823,
	10823,
	5784,
	13298,
	10682,
	13298,
	13298,
	16907,
	20847,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	20775,
	21263,
	20840,
	12815,
	13262,
	12996,
	12996,
	12996,
	7736,
	10682,
	13262,
	12815,
	13298,
	13298,
	13298,
	10892,
	7893,
	13298,
	1717,
	13298,
	13298,
	13298,
	10682,
	10682,
	13298,
	10682,
	5703,
	13298,
	13195,
	10823,
	13262,
	10892,
	13298,
	21355,
	13262,
	13052,
	12996,
	13298,
	10682,
	10682,
	7736,
	8845,
	9267,
	13298,
	21355,
	4160,
	13298,
	20847,
	20847,
	20847,
	18814,
	18814,
	18814,
	18814,
	21355,
	21355,
	20515,
	13298,
	13298,
	13298,
	13298,
	13298,
	10682,
	10682,
	10682,
	13298,
	21355,
	21355,
	21355,
	20847,
	20847,
	20847,
	20847,
	20847,
	20847,
	21355,
	13298,
	12815,
	10442,
	13262,
	13262,
	9267,
	13052,
	13052,
	12815,
	13298,
	13298,
	7736,
	12815,
	13262,
	13298,
	13298,
	10682,
	10682,
	13298,
	13298,
	13298,
	13298,
	7736,
	13298,
	13298,
	10682,
	5842,
	12815,
	5309,
	10682,
	10682,
	13298,
	13298,
	13298,
	13298,
	13298,
	9844,
	13298,
	21274,
	13298,
	13298,
	13298,
	13052,
	10682,
	13298,
	13298,
	10442,
	10442,
	4722,
	13298,
	13298,
	21274,
	21274,
	13298,
	13298,
	13298,
	13298,
	21355,
	13052,
	7736,
	7736,
	13298,
	13298,
	13052,
	13298,
	10682,
	12996,
	12996,
	13052,
	13052,
	13298,
	12996,
	12996,
	12996,
	13262,
	13739,
	14706,
	14831,
	10682,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	20775,
	14021,
	13052,
	10682,
	20847,
	13052,
	10682,
	13589,
	13712,
	15714,
	13298,
	13052,
	13298,
	10682,
	12996,
	12996,
	13052,
	13052,
	13298,
	13298,
	21274,
	13298,
	5684,
	10682,
	2408,
	10682,
	10682,
	10682,
	13298,
	10682,
	13298,
	13298,
	13298,
	13298,
	10682,
	13298,
	10682,
	13298,
	0,
	13298,
	13298,
	13298,
	13298,
	13298,
	5688,
	5688,
	13298,
	21355,
	0,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13052,
	13298,
	13298,
	10682,
	13298,
	21375,
	13298,
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_AK_Wwise_Unity_MonoBehaviour_CodeGenModule;
const Il2CppCodeGenModule g_AK_Wwise_Unity_MonoBehaviour_CodeGenModule = 
{
	"AK.Wwise.Unity.MonoBehaviour.dll",
	410,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};

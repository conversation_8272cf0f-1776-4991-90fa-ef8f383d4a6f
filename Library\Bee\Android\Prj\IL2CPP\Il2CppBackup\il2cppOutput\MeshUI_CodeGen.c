﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void MeshLayoutGroup_DoHorizontalLayout_m2EA4C6E37DD9A0C3F6307CDD0468DC409721CBF4 (void);
extern void MeshLayoutGroup_DoVerticalLayout_mF90501064B89A77ABD0CF7AE5C1299B23ACA34EC (void);
extern void MeshLayoutGroup__ctor_mD7879D693A471412485E20703AAEEE6E0A1D0FDF (void);
extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_mED64698710EE9B23EE781DBED1AC6392DBBA1362 (void);
extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_m6A7998EAA0237D2D639CBA733555B4DFE306111A (void);
extern void Example_LateUpdate_mBECDD1DE34F0EFF2BADE135E4281861DE1F4C60F (void);
extern void Example__ctor_mEF29E8DD39A1C5722D97BCF3A69A945D525B9B30 (void);
extern void Example__cctor_m733F84BA5D1F7320051D820CB4541AE52C141DA9 (void);
extern void MeshCanvas_get_pixelsPerUnit_m294BEC1D45C4052A201301E23EC7287A90999DEA (void);
extern void MeshCanvas_get_planeDistance_mFFAE08E186124707B3832C740FC4E7016395377A (void);
extern void MeshCanvas_get_canvasScale_m773AB63EA8D6444BD6C73DA8B4C9795812589AC0 (void);
extern void MeshCanvas_get_screenScale_m29DDFFD93C704B920DAE46FDEE97D3C767D2F216 (void);
extern void MeshCanvas_get_canvasCamera_mA5D2EB539FC453A9ADB055B45ADB53B87ACDD6DF (void);
extern void MeshCanvas_get_depth_m74AB760344BE7373201671390F20E95AE3D288FA (void);
extern void MeshCanvas_set_depth_mB07336018F7D80702556EF3887485FFA6536B3F3 (void);
extern void MeshCanvas_get_material_mE3E4C47F79456C1C3E20FB56663BC00184739D8A (void);
extern void MeshCanvas_get_isDirty_mDB2BCFB1327C92187237C46A7618F61827E954CE (void);
extern void MeshCanvas_get_isRebuilding_m72AF66C8FA9EB3B81CFE8AE47661FDDA7975F37E (void);
extern void MeshCanvas_get_vertexHelper_m21490AFCD6E2D3B6B731D0F98A5846434BABA559 (void);
extern void MeshCanvas_Awake_m5334D2045658BD19BD73D7716FA265486CB9A4BF (void);
extern void MeshCanvas_SetupComponents_m04835E5092F3EF9A753077C2A8126A79B6FFC7BA (void);
extern void MeshCanvas_SetupCamera_mB0BB899466A18EDEA1941B6C8C11637BEE9D28CE (void);
extern void MeshCanvas_SetCamera_mBB6237713F21B5ABBE6C848B4E632A79B558D9B8 (void);
extern void MeshCanvas_OnScreenResolutionChanged_mD30D25862155831C6E118937877A250D015ECA80 (void);
extern void MeshCanvas_CalculateScale_m6B71B5BFC897E2D3FB7C0F1D54C437D4C823BAE3 (void);
extern void MeshCanvas_CalculateScreenScale_m48377C1123042E49C81965665EBC57DE487D413D (void);
extern void MeshCanvas_OnDestroy_m6F56EA308DECC9342065A1BB2EB6B5A26462CAC8 (void);
extern void MeshCanvas_OnEnable_m3E3130AEDB76EF156529009A7CFB56BA0066A331 (void);
extern void MeshCanvas_OnDisable_m0E9A9C93E713974EBC81C7ABC8FF42C3D5FA115B (void);
extern void MeshCanvas_AddToCanvas_m1AFF3179E84FC2D334018E48282777DF444A019B (void);
extern void MeshCanvas_RemoveFromCanvas_m3BCD54B292475BC0B579E5F267973EC6CC4D665A (void);
extern void MeshCanvas_MarkAsDirty_mA875BA270B4E7C989A3A6529819D00C09C95DDD0 (void);
extern void MeshCanvas_MarkAsHeavyDirty_m6EB20F7C93F8AD829460F476BA8F9721B65182EC (void);
extern void MeshCanvas_ClearMesh_m53C14E3F41EDD459AE5708A5F6D739C42F37BFC6 (void);
extern void MeshCanvas_SetTexture_mE455C31BD65AE9DF9A95B38597AB6E2341CF0C8D (void);
extern void MeshCanvas_SetEnabled_mB922A2DFC6F09295380BC771F20DA561925377E6 (void);
extern void MeshCanvas_Rebuild_m5A61FF7F48F4B70902B5A07A13CCF52379577936 (void);
extern void MeshCanvas_UpdateCanvases_m525C99373C9AEC21D576133634BF02C45F15DBD7 (void);
extern void MeshCanvas_ClearCanvases_m66B8239867B4058E02F637EEB0346CF572635583 (void);
extern void MeshCanvas_GetFirstScreenScale_m533D7F007953AFA4018A40403BDD70312B4BC5EC (void);
extern void MeshCanvas_ScreenPositionToUIPosition_m37A481821CC3A735FF87014508706AAED230CABF (void);
extern void MeshCanvas__ctor_m3795E9FDCD0AE58A59636C67DFC32A8CCF6E527E (void);
extern void MeshCanvas__cctor_mFA2E6A97FFA42191ACD69E04C5BF41B19A4905B7 (void);
extern void LayerAttribute__ctor_m0859CB54D2344916DCEE588B735AF3D973CE8F67 (void);
extern void MeshCanvasSettings_Initialize_m3350A2E98E44C7BC2D863F653C455B52E0E92254 (void);
extern void MeshCanvasSettings_Load_mEDC2AD0A64AC8E9404F3C500D84F693FEB6D3A77 (void);
extern void MeshCanvasSettings_LayerNameToID_m4159B399FD3A2A24B1CA3B59477A51EF1EBA334E (void);
extern void MeshCanvasSettings_LayerIDToName_mDB9A3282D3E216BF9D4E90BAF47B77C7B3AD00D5 (void);
extern void MeshCanvasSettings__ctor_m070D2B451F708A3927FC2D554ABE33F3D3F89A86 (void);
extern void MeshFontData_get_defaultFontData_m720EE0E7F23975854A27F6D4E77EB24BD7529F0A (void);
extern void MeshFontData_get_font_mAB3187346C06BF3891E94C5FDBA09AB8156BE917 (void);
extern void MeshFontData_set_font_mAF9EFD9E70B1B1E51ED4A056C34524A5F77F1107 (void);
extern void MeshFontData_get_fontSize_mD7668CF76B5AF4243C8CC8135EBFDC95FB8BF1E6 (void);
extern void MeshFontData_set_fontSize_m575CEF46953040334AB8CFD76077DE9273F5AB2C (void);
extern void MeshFontData_get_fontStyle_mAE4D6E2C5CE74D0156BD94871FB1E2BFEA84A5A5 (void);
extern void MeshFontData_set_fontStyle_m0AA6998F54AE921D9F428D8A17C703545AEFBBE2 (void);
extern void MeshFontData_get_bestFit_m4A6297ED97D585382B3A78D9FF251A2C8CD34942 (void);
extern void MeshFontData_set_bestFit_mDD99C7343F80440CCFB4FBFB9F6ECAD9A0BEE7BE (void);
extern void MeshFontData_get_minSize_mAA7207AA7E9163A97CD1E28A75F7BC467C4CC728 (void);
extern void MeshFontData_set_minSize_mBD10D5DC4D9796A4A3CB8C2ABD6B10C531A2463E (void);
extern void MeshFontData_get_maxSize_m6E82540A474DCFCAD5BBDE3433F14525373CCE1F (void);
extern void MeshFontData_set_maxSize_mB43D327D6B40390BBE15C011ED632730C35D9B10 (void);
extern void MeshFontData_get_alignment_mEB70E50BBD1880EFAC74D70F7FF9386F61E45261 (void);
extern void MeshFontData_set_alignment_m52BF5DA0BEA3395E50C7258DDB4CF3C606E1D5CC (void);
extern void MeshFontData_get_alignByGeometry_m1BD715CB90949C473A516EE4A1C44B5F067DF2C5 (void);
extern void MeshFontData_set_alignByGeometry_m5FBF024DA4B75BD3AD435B888A3938F1E9298046 (void);
extern void MeshFontData_get_richText_mCFBB3B15D68C9E8DE15B12DB387071C7BBEBFBDA (void);
extern void MeshFontData_set_richText_m2E91F18D1EEB5C2380C0B71FBA798ED3E332391C (void);
extern void MeshFontData_get_horizontalOverflow_m661A1E06F7F7E75269156C23B165E0AFB5C91C08 (void);
extern void MeshFontData_set_horizontalOverflow_mE341BD154261DF4963680A4FF62972C6BB299EC2 (void);
extern void MeshFontData_get_verticalOverflow_m30F0828EA05228EE73DF7C1179326D6B2373CB74 (void);
extern void MeshFontData_set_verticalOverflow_m668F21770A9667938547DE575AD91FAA6C820A4A (void);
extern void MeshFontData_get_lineSpacing_mDB91DC27129D601887FD03ECC28AD6BB93E43E03 (void);
extern void MeshFontData_set_lineSpacing_m0E02E832031BE76C1D3389ECE3FDF8A8F4C13734 (void);
extern void MeshFontData_UnityEngine_ISerializationCallbackReceiver_OnBeforeSerialize_m2EC3CB82EA36B86AE6C4BFFAB883D7F5C0B15B80 (void);
extern void MeshFontData_UnityEngine_ISerializationCallbackReceiver_OnAfterDeserialize_m48F9696AB9B37A7567949323D7A7B823A33ED8BB (void);
extern void MeshFontData__ctor_mF2AFB9269597F4EA446F6E3016784418EC008582 (void);
extern void MeshGraphic_get_isVisible_m7C359099FB91B6010011AF2EC12007FE316883C5 (void);
extern void MeshGraphic_get_materialIndex_mD3E3E12ED189EFD80AC8C73F16CEAA36D454C9F6 (void);
extern void MeshGraphic_get_color_m80E2E13943FC80CBA7996930F86DAAEBA5F1BC72 (void);
extern void MeshGraphic_set_color_mA425F5745693DEAA3D929A67D4624C437F3447E0 (void);
extern void MeshGraphic_get_texture_mE323D19DCF9D0EE9D4EC87DCB0D4ED7746A372CF (void);
extern void MeshGraphic_get_rectTransform_m63653581E1CE50680241FAE0DAB70591C3898FFE (void);
extern void MeshGraphic_set_rectTransform_mDD5E0706AFA88310700BA03317C1F2E99C14A22D (void);
extern void MeshGraphic_get_canvas_mBD6DD179756DBB99629EE9A7C6AA8E5041B0456F (void);
extern void MeshGraphic_Awake_m348238EA360B88BA9016200DA0E83FCE4B54C2A6 (void);
extern void MeshGraphic_OnEnable_m90E2F6B722CFAD09554F93DD7C8F53472A7317DA (void);
extern void MeshGraphic_OnDisable_mD45520DD69121B06530EDE2DD636CDFFE9A4F28E (void);
extern void MeshGraphic_OnDestroy_m6D20D62BC97F97BFAC0F64FE99A6B00EEDE7F338 (void);
extern void MeshGraphic_OnPositionChanged_m8825756720256B6C7FE82F9AA57F55AC9BD89A08 (void);
extern void MeshGraphic_OnSizeChanged_mD1721E5D7BB1F832D2F02A7BA578AB989127E15F (void);
extern void MeshGraphic_SetVerticesDirty_mBE9D65016D49CE8CB83F98692707DA4D91062352 (void);
extern void MeshGraphic_SetMaterialDirty_m7A0DF18DDE59E5B4EAB1ECB25568C2AB19F17ABB (void);
extern void MeshGraphic_SetVisible_m992689E46E2B26D3FA41CAFACE41BAC03E9E8DD6 (void);
extern void MeshGraphic_ClearMeshHandle_m40C6C8CC622B8D5EC370E50E367A6D807DEC7B2D (void);
extern void MeshGraphic_DoRebuild_m4BA16F1F8F32D7FA202C6E41D52C42D215A0CA85 (void);
extern void MeshGraphic_IsDirty_mB12EDAEA204D2F9E98A4659A5F46AEA2A6925926 (void);
extern void MeshGraphic_EnsureMeshHandle_mF3F7FF3850172FCFAA7C612C9489EECF8ECEA2C7 (void);
extern void MeshGraphic_RebuildGeometry_m2285A33ECC16A8470CAC434C8076D1AFE61A6A4E (void);
extern void MeshGraphic_ClearUvForMaterialIndex_mBB127F1D32769D534E1F55FCC9985F45145AC3A0 (void);
extern void MeshGraphic_RebuildMaterial_m500FA0C0ECA55B015FED25D24ED7ACFDDF59706F (void);
extern void MeshGraphic_GetLocalRect_m7095AE003EE4DC4FD70D7163A24765A53DE97A25 (void);
extern void MeshGraphic__ctor_m6540F099824B1454EAF845C36FE276560A0E6314 (void);
extern void MeshGraphic__cctor_m847AC1B6D20D4ABBEB3516DD217B0EA7DB0A82BC (void);
extern void MeshGraphicGroup_get_graphics_m1462B2DB1120FC738918EF039C9187D3E497E10F (void);
extern void MeshGraphicGroup_OnEnable_m39646969E22FA25AF7703E4AB722D20EC0C2928A (void);
extern void MeshGraphicGroup_SetVisible_m71B6AF9C8397E8D60A6CA19426CF798B05416C49 (void);
extern void MeshGraphicGroup_SetPosition_m9A6105C0883B1467379980C55274382612657EFF (void);
extern void MeshGraphicGroup_SetSize_mF8D048AABF7F2FE46B062BEC521BB3F7ADC52D08 (void);
extern void MeshGraphicGroup_Apply_m0CB216DD380543DB3B63330AE8CC874F7B7EE282 (void);
extern void MeshGraphicGroup__ctor_mC0DC89E68208B88460E2270D3709BB6786D2A5AB (void);
extern void MeshImage_get_type_m338E1A5417FC34F650CB410490CAB54CE27474E6 (void);
extern void MeshImage_set_type_m4AFC4D29C0E804542664E44AA9334CC7AD78F9CF (void);
extern void MeshImage_get_preserveAspect_m5C4186A4EBC7EB86DA1497758204980E54885367 (void);
extern void MeshImage_set_preserveAspect_mC0AEB178C0FBFEBD9073EB5806376D398E8CA458 (void);
extern void MeshImage_get_fillCenter_mD9BC95B3669B05F1C4E37058D365FEB07DD1AE27 (void);
extern void MeshImage_set_fillCenter_m748D877BA738A5C79A1D4E899A4A6371B1BBBA35 (void);
extern void MeshImage_get_fillMethod_mD909016FFB97F3235B192D9E348EF7BD04D19AA8 (void);
extern void MeshImage_set_fillMethod_mD335046B4D2497183175BF7E050F696430BCB5FC (void);
extern void MeshImage_get_fillAmount_m014D4E6971A7DF7560CCFB76168A44555A763B6F (void);
extern void MeshImage_set_fillAmount_m52364425D6C77F1F69FC9B23CC2C775B0D8DEEFC (void);
extern void MeshImage_get_fillClockwise_m2C466CE7786466144F20BEA4030E19D4EF981FD0 (void);
extern void MeshImage_set_fillClockwise_mB86E16F9CB274C6809E078F34798B2DC78D016C2 (void);
extern void MeshImage_get_fillOrigin_mD0AE62138350302B892EB6F8F9FCE03803A28C5F (void);
extern void MeshImage_set_fillOrigin_m81AC01D2A94D1815EFD148EE7D64A4273C44BAEF (void);
extern void MeshImage_get_sprite_m6C13179A56D293F1FD1954A8568E9D1C468BC22D (void);
extern void MeshImage_set_sprite_m47E5CE6CC3C3343FE7E7CDB739BB7AF02DC2FF73 (void);
extern void MeshImage_get_texture_mE3293AB3E58E6A24312BB0C0F734B772A1AA9F82 (void);
extern void MeshImage_get_hasBorder_m8A260B8DABEC7D23959EE8F1376566B04C587023 (void);
extern void MeshImage_PreserveSpriteAspectRatio_m3FD7993E65BB1730724BE276CFE70959EA5BB6D2 (void);
extern void MeshImage_GetDrawingDimension_mBF54D430AE4A4B825DB41D61466422783659D777 (void);
extern void MeshImage_RebuildGeometry_m93D80B4B7D3590CDC1A5BAF919A762E2A71F5DC0 (void);
extern void MeshImage_GenerateSimple_mA67DB8D4F9D103B847897BC8B4921643F4787386 (void);
extern void MeshImage_GenerateSliced_m12DB57F7AFB1E2D23E6466183815A459F54970B2 (void);
extern void MeshImage_GenerateFilled_m98518148228DFA0CBAB31941FA80705357F5E61D (void);
extern void MeshImage_SetQuad_mE9C228799F76BCA6D5CD638ED59147F564941401 (void);
extern void MeshImage_RadialCut_mEC3645F17A5DC90C59CC8B0067789B5CB397F238 (void);
extern void MeshImage_RadialCut_m545E7E28395666360912F6097E278FE3F013498B (void);
extern void MeshImage__ctor_m20299805126A2B82C7815A5641ADC19AE527E7B8 (void);
extern void MeshImage__cctor_m40E3CB32FBB4C62C9DBDD1F77B9473CA20A29875 (void);
extern void MeshRectTransformData_add_onPositionChangedEvent_mB27E3EE1E8D3738A3EAE7BAD4BF66EDC98FF9F23 (void);
extern void MeshRectTransformData_remove_onPositionChangedEvent_mDD4415F6033772D5386A9BDC5A65594CF76FA0D2 (void);
extern void MeshRectTransformData_add_onSizeChangedEvent_m31F8DABBDDF3C232C9EE73C586BA7561CD78F529 (void);
extern void MeshRectTransformData_remove_onSizeChangedEvent_m74F3F31C9C04951052609E0C130799CE2ABE91E9 (void);
extern void MeshRectTransformData_get_pivot_m8E28529FFD50BB0E410FA6B8FFC5A5DFDE02BA8E (void);
extern void MeshRectTransformData_get_localPosition_mE61A42E14CC47CFFD53E6BF5C2A9C0E2A59E50B2 (void);
extern void MeshRectTransformData_get_pivotPosition_m17F524B7AF834954AC3F2ADE0EB9673AE6B1EC52 (void);
extern void MeshRectTransformData_get_size_m3B11FEE0E99EFCAC5B9C36FE819A47EAE50EDD29 (void);
extern void MeshRectTransformData_SetOffset_m28149413BB87221B42328199273B880DB87A9785 (void);
extern void MeshRectTransformData_SetSize_m388BD1F13A5BF5E63CD3053F65CDDD9B98484D62 (void);
extern void MeshRectTransformData_GetLocalRect_mCD504E178D77EE70084063FD572EB5F23DF642C6 (void);
extern void MeshRectTransformData_GetWorldRect_mD80A2D7318510851E28117D06BCBCCF816763FF6 (void);
extern void MeshRectTransformData__ctor_mB51F3291AFE61812FD39754A6BBC4E18F28CB965 (void);
extern void OnPositionChanged__ctor_mACC8C43B3E27EB81DFBC900F53D7E1FFD4D9E418 (void);
extern void OnPositionChanged_Invoke_m0B3BB0150CDA083B2E0E7B320FE6CB45C89757F9 (void);
extern void OnPositionChanged_BeginInvoke_m3E7DBF7B1E985FD9136C80FB7A94BE1EE5A96DC8 (void);
extern void OnPositionChanged_EndInvoke_mFEFBE24469F0DFCB043BC2C4F0EEC455024EE749 (void);
extern void OnSizeChanged__ctor_m2B85FC54A06154D9083F2618684D04E2E48EFF17 (void);
extern void OnSizeChanged_Invoke_mF48D37F8418D6C96C8D75D3930FA4C940EAC975F (void);
extern void OnSizeChanged_BeginInvoke_mECE0D973C9CE746CC88870D3C3DD32206EE49992 (void);
extern void OnSizeChanged_EndInvoke_mD44571BF76DDA5EE9041F3838729FDE1E951CE82 (void);
extern void MeshRectTransform_Awake_m91166103838C1F80B921AF8FD7442E935F4703B3 (void);
extern void MeshRectTransform_OnPositionChanged_mEAFFFAB0D3A95EBF073243B16E7AF8A6C1452C67 (void);
extern void MeshRectTransform__ctor_m50D798A2C448FE3C65CAAD520969DE6F6E72A6C4 (void);
extern void MeshText_get_text_m532727C5D2F59340A2944CD781FB5E80F1AA977D (void);
extern void MeshText_set_text_m34245F193BDD11C6F26DD6E6F5583D18812AC0A3 (void);
extern void MeshText_get_font_m25847F359B9EDFEF8D46472229C628B3373AE41E (void);
extern void MeshText_set_font_mB9CBB1FE88459AAD4B2EBA04E3EA49F5877C66A0 (void);
extern void MeshText_get_fontSize_m7B2A9A06EFB43FAA47BEB6CAC22EC84043A994EA (void);
extern void MeshText_set_fontSize_m485E4B4D90FB875BD375D6F01396375E6F37E96D (void);
extern void MeshText_get_alignment_mACD9B910142BF894C4209FE8643492F468805182 (void);
extern void MeshText_set_alignment_mA1CECD831CF620AB24064354E4C844FD61964791 (void);
extern void MeshText_get_pixelsPerUnit_mF760A6F2A60E618ACFA77B329214849486CDFFAE (void);
extern void MeshText_get_texture_mE445904C1C1EDFE90756D133418DBA9A544E6E68 (void);
extern void MeshText_get_cachedTextGenerator_mD4550E1BA44A2973D25318695A418AFB19139747 (void);
extern void MeshText_Awake_mFDE58945678E258BF2BA41A749EC71054D92951F (void);
extern void MeshText_FontTextureChanged_m833206417EA3C16E5E26014721BC036EB605B0BA (void);
extern void MeshText_OnEnable_mE6D343FD36BD9E6557A10183317C3BB1FC86B214 (void);
extern void MeshText_OnDisable_m031F16A52E90E2DA780CAD93F61544ED3381778A (void);
extern void MeshText_OnSizeChanged_m99FC27864FAB89918FCAFD6A5B0163888ADCB0DB (void);
extern void MeshText_SetVisible_mE9E0BF6993CD2AED1C4EC0E9B6F5C9D3318242C3 (void);
extern void MeshText_GetGenerationSettings_m9B1A61E7591BC2A77CB8BD921EEC57189864D245 (void);
extern void MeshText_RebuildGeometry_mB4A250BA085B300B4422B6B33CB65141FD86183D (void);
extern void MeshText__ctor_mDFA68C4EAABBF5A195B20E4EB90A14DAA394B4F1 (void);
extern void BufferIndexer__ctor_mF59CBA80F45496ADFFA7098F9A54D6B72FA982DC (void);
extern void MeshVertexHelper_get_vertexCount_m2CC6B1129B8964EF4D7EB4E8F118071BCA629FAD (void);
extern void MeshVertexHelper_get_triangleCount_mF0A154C6C8A16D15DB7B94E1E038AA4796BBD86E (void);
extern void MeshVertexHelper__ctor_m7FC983B6BFB8809A4C001E32F740D49D8DB8DBD8 (void);
extern void MeshVertexHelper_Dispose_mC5A1381CF2F91AF11F2490F32A40A0A26E28E247 (void);
extern void MeshVertexHelper_SetDirty_m44CD1201FEB36335CB16ABB5DE9F2FE98467A16B (void);
extern void MeshVertexHelper_IsDirty_mB59F8C280D13C19DD0BD22F53A2B28AA361190FC (void);
extern void MeshVertexHelper_Clear_m5AA88A98606442F13D964EA92DE26D7284FFB8DD (void);
extern void MeshVertexHelper_Allocate_mE75539D6CEA6E5FEA3EB040BB9880564CBFD0858 (void);
extern void MeshVertexHelper_Reallocate_m79C220AC44463C87AD1B513A5C4ADABF9A92888E (void);
extern void MeshVertexHelper_Deallocate_m3BC5B6B796690D43833BD7C1DE02993DF68AAA86 (void);
extern void MeshVertexHelper_Resize_m7934D54F600BC2D02F6C5190E86AB0C915DB473E (void);
extern void MeshVertexHelper_BeginModify_mEDA40DB6D558FC7B15C1D74396BF5F72B53D5189 (void);
extern void MeshVertexHelper_SetPosition_m0BF02447856C3838E1201988A239BBE74DDCF630 (void);
extern void MeshVertexHelper_SetColor_m2F868C6BDC1BE1871A855018DEDA3CD7A55A1D77 (void);
extern void MeshVertexHelper_SetUv_m36B2AFC9594EB5987A5DE1C2C5F68E440CBC2146 (void);
extern void MeshVertexHelper_SetUv0_m1312E6FF38BC96DF6DC3DCD97591551E6A5FC07F (void);
extern void MeshVertexHelper_SetUv1_m4D038F430D2A98414C332BEA3810DE76475BA932 (void);
extern void MeshVertexHelper_SetTriangle_mEE233C28EC03C9C544F297454F701DC0A95BC729 (void);
extern void MeshVertexHelper_SetVertices_m025E69A1FB3FA06AB446D9EF8D6E762C9DF38D94 (void);
extern void MeshVertexHelper_SetTriangles_m687495959B776232DF307037D07E7852C8795838 (void);
extern void MeshVertexHelper_SetTriangles_m05CCA4A042A0AE1B37C8D92AD19919937639AD43 (void);
extern void MeshVertexHelper_SetPositions_mE777F142FF62690D20776749E919B2E3D696CC6C (void);
extern void MeshVertexHelper_SetPositions_mD5D4731D82125C3F7A827B974BA52C876AF28297 (void);
extern void MeshVertexHelper_SetColors_m948C822CBD000F04B000FDBEF6D75E0856297EC3 (void);
extern void MeshVertexHelper_SetColors_mD9CFC82DB0F252CC65D70A70F5CC28DD2FE60612 (void);
extern void MeshVertexHelper_SetUvs_m4D563A5F52366917FE5B0FA79AD0B39EFFCDE561 (void);
extern void MeshVertexHelper_SetUvs_mD6F93BB8D8C70C35FB51DC1DE264FE733C7E1E90 (void);
extern void MeshVertexHelper_SetUv0s_m450B76527FDDDE1FE1BB6227FEEF5AE5B1CED02B (void);
extern void MeshVertexHelper_SetUv0s_m78E24DDE95D5257711B789D7FE4525458896233B (void);
extern void MeshVertexHelper_SetUv1s_m8485B139850AC8744F13D996A77DF5D62EC505D8 (void);
extern void MeshVertexHelper_SetUv1s_mB56E5D7E6AC1AABF3CC5C220B4186387E533D504 (void);
extern void MeshVertexHelper_ApplyToMesh_m3840CFFA3684CFB657AE0DC6BA292E9F815DF9EC (void);
static Il2CppMethodPointer s_methodPointers[223] = 
{
	MeshLayoutGroup_DoHorizontalLayout_m2EA4C6E37DD9A0C3F6307CDD0468DC409721CBF4,
	MeshLayoutGroup_DoVerticalLayout_mF90501064B89A77ABD0CF7AE5C1299B23ACA34EC,
	MeshLayoutGroup__ctor_mD7879D693A471412485E20703AAEEE6E0A1D0FDF,
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_mED64698710EE9B23EE781DBED1AC6392DBBA1362,
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_m6A7998EAA0237D2D639CBA733555B4DFE306111A,
	Example_LateUpdate_mBECDD1DE34F0EFF2BADE135E4281861DE1F4C60F,
	Example__ctor_mEF29E8DD39A1C5722D97BCF3A69A945D525B9B30,
	Example__cctor_m733F84BA5D1F7320051D820CB4541AE52C141DA9,
	MeshCanvas_get_pixelsPerUnit_m294BEC1D45C4052A201301E23EC7287A90999DEA,
	MeshCanvas_get_planeDistance_mFFAE08E186124707B3832C740FC4E7016395377A,
	MeshCanvas_get_canvasScale_m773AB63EA8D6444BD6C73DA8B4C9795812589AC0,
	MeshCanvas_get_screenScale_m29DDFFD93C704B920DAE46FDEE97D3C767D2F216,
	MeshCanvas_get_canvasCamera_mA5D2EB539FC453A9ADB055B45ADB53B87ACDD6DF,
	MeshCanvas_get_depth_m74AB760344BE7373201671390F20E95AE3D288FA,
	MeshCanvas_set_depth_mB07336018F7D80702556EF3887485FFA6536B3F3,
	MeshCanvas_get_material_mE3E4C47F79456C1C3E20FB56663BC00184739D8A,
	MeshCanvas_get_isDirty_mDB2BCFB1327C92187237C46A7618F61827E954CE,
	MeshCanvas_get_isRebuilding_m72AF66C8FA9EB3B81CFE8AE47661FDDA7975F37E,
	MeshCanvas_get_vertexHelper_m21490AFCD6E2D3B6B731D0F98A5846434BABA559,
	MeshCanvas_Awake_m5334D2045658BD19BD73D7716FA265486CB9A4BF,
	MeshCanvas_SetupComponents_m04835E5092F3EF9A753077C2A8126A79B6FFC7BA,
	MeshCanvas_SetupCamera_mB0BB899466A18EDEA1941B6C8C11637BEE9D28CE,
	MeshCanvas_SetCamera_mBB6237713F21B5ABBE6C848B4E632A79B558D9B8,
	MeshCanvas_OnScreenResolutionChanged_mD30D25862155831C6E118937877A250D015ECA80,
	MeshCanvas_CalculateScale_m6B71B5BFC897E2D3FB7C0F1D54C437D4C823BAE3,
	MeshCanvas_CalculateScreenScale_m48377C1123042E49C81965665EBC57DE487D413D,
	MeshCanvas_OnDestroy_m6F56EA308DECC9342065A1BB2EB6B5A26462CAC8,
	MeshCanvas_OnEnable_m3E3130AEDB76EF156529009A7CFB56BA0066A331,
	MeshCanvas_OnDisable_m0E9A9C93E713974EBC81C7ABC8FF42C3D5FA115B,
	MeshCanvas_AddToCanvas_m1AFF3179E84FC2D334018E48282777DF444A019B,
	MeshCanvas_RemoveFromCanvas_m3BCD54B292475BC0B579E5F267973EC6CC4D665A,
	MeshCanvas_MarkAsDirty_mA875BA270B4E7C989A3A6529819D00C09C95DDD0,
	MeshCanvas_MarkAsHeavyDirty_m6EB20F7C93F8AD829460F476BA8F9721B65182EC,
	MeshCanvas_ClearMesh_m53C14E3F41EDD459AE5708A5F6D739C42F37BFC6,
	MeshCanvas_SetTexture_mE455C31BD65AE9DF9A95B38597AB6E2341CF0C8D,
	MeshCanvas_SetEnabled_mB922A2DFC6F09295380BC771F20DA561925377E6,
	MeshCanvas_Rebuild_m5A61FF7F48F4B70902B5A07A13CCF52379577936,
	MeshCanvas_UpdateCanvases_m525C99373C9AEC21D576133634BF02C45F15DBD7,
	MeshCanvas_ClearCanvases_m66B8239867B4058E02F637EEB0346CF572635583,
	MeshCanvas_GetFirstScreenScale_m533D7F007953AFA4018A40403BDD70312B4BC5EC,
	MeshCanvas_ScreenPositionToUIPosition_m37A481821CC3A735FF87014508706AAED230CABF,
	MeshCanvas__ctor_m3795E9FDCD0AE58A59636C67DFC32A8CCF6E527E,
	MeshCanvas__cctor_mFA2E6A97FFA42191ACD69E04C5BF41B19A4905B7,
	LayerAttribute__ctor_m0859CB54D2344916DCEE588B735AF3D973CE8F67,
	MeshCanvasSettings_Initialize_m3350A2E98E44C7BC2D863F653C455B52E0E92254,
	MeshCanvasSettings_Load_mEDC2AD0A64AC8E9404F3C500D84F693FEB6D3A77,
	MeshCanvasSettings_LayerNameToID_m4159B399FD3A2A24B1CA3B59477A51EF1EBA334E,
	MeshCanvasSettings_LayerIDToName_mDB9A3282D3E216BF9D4E90BAF47B77C7B3AD00D5,
	MeshCanvasSettings__ctor_m070D2B451F708A3927FC2D554ABE33F3D3F89A86,
	MeshFontData_get_defaultFontData_m720EE0E7F23975854A27F6D4E77EB24BD7529F0A,
	MeshFontData_get_font_mAB3187346C06BF3891E94C5FDBA09AB8156BE917,
	MeshFontData_set_font_mAF9EFD9E70B1B1E51ED4A056C34524A5F77F1107,
	MeshFontData_get_fontSize_mD7668CF76B5AF4243C8CC8135EBFDC95FB8BF1E6,
	MeshFontData_set_fontSize_m575CEF46953040334AB8CFD76077DE9273F5AB2C,
	MeshFontData_get_fontStyle_mAE4D6E2C5CE74D0156BD94871FB1E2BFEA84A5A5,
	MeshFontData_set_fontStyle_m0AA6998F54AE921D9F428D8A17C703545AEFBBE2,
	MeshFontData_get_bestFit_m4A6297ED97D585382B3A78D9FF251A2C8CD34942,
	MeshFontData_set_bestFit_mDD99C7343F80440CCFB4FBFB9F6ECAD9A0BEE7BE,
	MeshFontData_get_minSize_mAA7207AA7E9163A97CD1E28A75F7BC467C4CC728,
	MeshFontData_set_minSize_mBD10D5DC4D9796A4A3CB8C2ABD6B10C531A2463E,
	MeshFontData_get_maxSize_m6E82540A474DCFCAD5BBDE3433F14525373CCE1F,
	MeshFontData_set_maxSize_mB43D327D6B40390BBE15C011ED632730C35D9B10,
	MeshFontData_get_alignment_mEB70E50BBD1880EFAC74D70F7FF9386F61E45261,
	MeshFontData_set_alignment_m52BF5DA0BEA3395E50C7258DDB4CF3C606E1D5CC,
	MeshFontData_get_alignByGeometry_m1BD715CB90949C473A516EE4A1C44B5F067DF2C5,
	MeshFontData_set_alignByGeometry_m5FBF024DA4B75BD3AD435B888A3938F1E9298046,
	MeshFontData_get_richText_mCFBB3B15D68C9E8DE15B12DB387071C7BBEBFBDA,
	MeshFontData_set_richText_m2E91F18D1EEB5C2380C0B71FBA798ED3E332391C,
	MeshFontData_get_horizontalOverflow_m661A1E06F7F7E75269156C23B165E0AFB5C91C08,
	MeshFontData_set_horizontalOverflow_mE341BD154261DF4963680A4FF62972C6BB299EC2,
	MeshFontData_get_verticalOverflow_m30F0828EA05228EE73DF7C1179326D6B2373CB74,
	MeshFontData_set_verticalOverflow_m668F21770A9667938547DE575AD91FAA6C820A4A,
	MeshFontData_get_lineSpacing_mDB91DC27129D601887FD03ECC28AD6BB93E43E03,
	MeshFontData_set_lineSpacing_m0E02E832031BE76C1D3389ECE3FDF8A8F4C13734,
	MeshFontData_UnityEngine_ISerializationCallbackReceiver_OnBeforeSerialize_m2EC3CB82EA36B86AE6C4BFFAB883D7F5C0B15B80,
	MeshFontData_UnityEngine_ISerializationCallbackReceiver_OnAfterDeserialize_m48F9696AB9B37A7567949323D7A7B823A33ED8BB,
	MeshFontData__ctor_mF2AFB9269597F4EA446F6E3016784418EC008582,
	MeshGraphic_get_isVisible_m7C359099FB91B6010011AF2EC12007FE316883C5,
	MeshGraphic_get_materialIndex_mD3E3E12ED189EFD80AC8C73F16CEAA36D454C9F6,
	MeshGraphic_get_color_m80E2E13943FC80CBA7996930F86DAAEBA5F1BC72,
	MeshGraphic_set_color_mA425F5745693DEAA3D929A67D4624C437F3447E0,
	MeshGraphic_get_texture_mE323D19DCF9D0EE9D4EC87DCB0D4ED7746A372CF,
	MeshGraphic_get_rectTransform_m63653581E1CE50680241FAE0DAB70591C3898FFE,
	MeshGraphic_set_rectTransform_mDD5E0706AFA88310700BA03317C1F2E99C14A22D,
	MeshGraphic_get_canvas_mBD6DD179756DBB99629EE9A7C6AA8E5041B0456F,
	MeshGraphic_Awake_m348238EA360B88BA9016200DA0E83FCE4B54C2A6,
	MeshGraphic_OnEnable_m90E2F6B722CFAD09554F93DD7C8F53472A7317DA,
	MeshGraphic_OnDisable_mD45520DD69121B06530EDE2DD636CDFFE9A4F28E,
	MeshGraphic_OnDestroy_m6D20D62BC97F97BFAC0F64FE99A6B00EEDE7F338,
	MeshGraphic_OnPositionChanged_m8825756720256B6C7FE82F9AA57F55AC9BD89A08,
	MeshGraphic_OnSizeChanged_mD1721E5D7BB1F832D2F02A7BA578AB989127E15F,
	MeshGraphic_SetVerticesDirty_mBE9D65016D49CE8CB83F98692707DA4D91062352,
	MeshGraphic_SetMaterialDirty_m7A0DF18DDE59E5B4EAB1ECB25568C2AB19F17ABB,
	MeshGraphic_SetVisible_m992689E46E2B26D3FA41CAFACE41BAC03E9E8DD6,
	MeshGraphic_ClearMeshHandle_m40C6C8CC622B8D5EC370E50E367A6D807DEC7B2D,
	MeshGraphic_DoRebuild_m4BA16F1F8F32D7FA202C6E41D52C42D215A0CA85,
	MeshGraphic_IsDirty_mB12EDAEA204D2F9E98A4659A5F46AEA2A6925926,
	MeshGraphic_EnsureMeshHandle_mF3F7FF3850172FCFAA7C612C9489EECF8ECEA2C7,
	MeshGraphic_RebuildGeometry_m2285A33ECC16A8470CAC434C8076D1AFE61A6A4E,
	MeshGraphic_ClearUvForMaterialIndex_mBB127F1D32769D534E1F55FCC9985F45145AC3A0,
	MeshGraphic_RebuildMaterial_m500FA0C0ECA55B015FED25D24ED7ACFDDF59706F,
	MeshGraphic_GetLocalRect_m7095AE003EE4DC4FD70D7163A24765A53DE97A25,
	MeshGraphic__ctor_m6540F099824B1454EAF845C36FE276560A0E6314,
	MeshGraphic__cctor_m847AC1B6D20D4ABBEB3516DD217B0EA7DB0A82BC,
	MeshGraphicGroup_get_graphics_m1462B2DB1120FC738918EF039C9187D3E497E10F,
	MeshGraphicGroup_OnEnable_m39646969E22FA25AF7703E4AB722D20EC0C2928A,
	MeshGraphicGroup_SetVisible_m71B6AF9C8397E8D60A6CA19426CF798B05416C49,
	MeshGraphicGroup_SetPosition_m9A6105C0883B1467379980C55274382612657EFF,
	MeshGraphicGroup_SetSize_mF8D048AABF7F2FE46B062BEC521BB3F7ADC52D08,
	MeshGraphicGroup_Apply_m0CB216DD380543DB3B63330AE8CC874F7B7EE282,
	MeshGraphicGroup__ctor_mC0DC89E68208B88460E2270D3709BB6786D2A5AB,
	MeshImage_get_type_m338E1A5417FC34F650CB410490CAB54CE27474E6,
	MeshImage_set_type_m4AFC4D29C0E804542664E44AA9334CC7AD78F9CF,
	MeshImage_get_preserveAspect_m5C4186A4EBC7EB86DA1497758204980E54885367,
	MeshImage_set_preserveAspect_mC0AEB178C0FBFEBD9073EB5806376D398E8CA458,
	MeshImage_get_fillCenter_mD9BC95B3669B05F1C4E37058D365FEB07DD1AE27,
	MeshImage_set_fillCenter_m748D877BA738A5C79A1D4E899A4A6371B1BBBA35,
	MeshImage_get_fillMethod_mD909016FFB97F3235B192D9E348EF7BD04D19AA8,
	MeshImage_set_fillMethod_mD335046B4D2497183175BF7E050F696430BCB5FC,
	MeshImage_get_fillAmount_m014D4E6971A7DF7560CCFB76168A44555A763B6F,
	MeshImage_set_fillAmount_m52364425D6C77F1F69FC9B23CC2C775B0D8DEEFC,
	MeshImage_get_fillClockwise_m2C466CE7786466144F20BEA4030E19D4EF981FD0,
	MeshImage_set_fillClockwise_mB86E16F9CB274C6809E078F34798B2DC78D016C2,
	MeshImage_get_fillOrigin_mD0AE62138350302B892EB6F8F9FCE03803A28C5F,
	MeshImage_set_fillOrigin_m81AC01D2A94D1815EFD148EE7D64A4273C44BAEF,
	MeshImage_get_sprite_m6C13179A56D293F1FD1954A8568E9D1C468BC22D,
	MeshImage_set_sprite_m47E5CE6CC3C3343FE7E7CDB739BB7AF02DC2FF73,
	MeshImage_get_texture_mE3293AB3E58E6A24312BB0C0F734B772A1AA9F82,
	MeshImage_get_hasBorder_m8A260B8DABEC7D23959EE8F1376566B04C587023,
	MeshImage_PreserveSpriteAspectRatio_m3FD7993E65BB1730724BE276CFE70959EA5BB6D2,
	MeshImage_GetDrawingDimension_mBF54D430AE4A4B825DB41D61466422783659D777,
	MeshImage_RebuildGeometry_m93D80B4B7D3590CDC1A5BAF919A762E2A71F5DC0,
	MeshImage_GenerateSimple_mA67DB8D4F9D103B847897BC8B4921643F4787386,
	MeshImage_GenerateSliced_m12DB57F7AFB1E2D23E6466183815A459F54970B2,
	MeshImage_GenerateFilled_m98518148228DFA0CBAB31941FA80705357F5E61D,
	MeshImage_SetQuad_mE9C228799F76BCA6D5CD638ED59147F564941401,
	MeshImage_RadialCut_mEC3645F17A5DC90C59CC8B0067789B5CB397F238,
	MeshImage_RadialCut_m545E7E28395666360912F6097E278FE3F013498B,
	MeshImage__ctor_m20299805126A2B82C7815A5641ADC19AE527E7B8,
	MeshImage__cctor_m40E3CB32FBB4C62C9DBDD1F77B9473CA20A29875,
	MeshRectTransformData_add_onPositionChangedEvent_mB27E3EE1E8D3738A3EAE7BAD4BF66EDC98FF9F23,
	MeshRectTransformData_remove_onPositionChangedEvent_mDD4415F6033772D5386A9BDC5A65594CF76FA0D2,
	MeshRectTransformData_add_onSizeChangedEvent_m31F8DABBDDF3C232C9EE73C586BA7561CD78F529,
	MeshRectTransformData_remove_onSizeChangedEvent_m74F3F31C9C04951052609E0C130799CE2ABE91E9,
	MeshRectTransformData_get_pivot_m8E28529FFD50BB0E410FA6B8FFC5A5DFDE02BA8E,
	MeshRectTransformData_get_localPosition_mE61A42E14CC47CFFD53E6BF5C2A9C0E2A59E50B2,
	MeshRectTransformData_get_pivotPosition_m17F524B7AF834954AC3F2ADE0EB9673AE6B1EC52,
	MeshRectTransformData_get_size_m3B11FEE0E99EFCAC5B9C36FE819A47EAE50EDD29,
	MeshRectTransformData_SetOffset_m28149413BB87221B42328199273B880DB87A9785,
	MeshRectTransformData_SetSize_m388BD1F13A5BF5E63CD3053F65CDDD9B98484D62,
	MeshRectTransformData_GetLocalRect_mCD504E178D77EE70084063FD572EB5F23DF642C6,
	MeshRectTransformData_GetWorldRect_mD80A2D7318510851E28117D06BCBCCF816763FF6,
	MeshRectTransformData__ctor_mB51F3291AFE61812FD39754A6BBC4E18F28CB965,
	OnPositionChanged__ctor_mACC8C43B3E27EB81DFBC900F53D7E1FFD4D9E418,
	OnPositionChanged_Invoke_m0B3BB0150CDA083B2E0E7B320FE6CB45C89757F9,
	OnPositionChanged_BeginInvoke_m3E7DBF7B1E985FD9136C80FB7A94BE1EE5A96DC8,
	OnPositionChanged_EndInvoke_mFEFBE24469F0DFCB043BC2C4F0EEC455024EE749,
	OnSizeChanged__ctor_m2B85FC54A06154D9083F2618684D04E2E48EFF17,
	OnSizeChanged_Invoke_mF48D37F8418D6C96C8D75D3930FA4C940EAC975F,
	OnSizeChanged_BeginInvoke_mECE0D973C9CE746CC88870D3C3DD32206EE49992,
	OnSizeChanged_EndInvoke_mD44571BF76DDA5EE9041F3838729FDE1E951CE82,
	MeshRectTransform_Awake_m91166103838C1F80B921AF8FD7442E935F4703B3,
	MeshRectTransform_OnPositionChanged_mEAFFFAB0D3A95EBF073243B16E7AF8A6C1452C67,
	MeshRectTransform__ctor_m50D798A2C448FE3C65CAAD520969DE6F6E72A6C4,
	MeshText_get_text_m532727C5D2F59340A2944CD781FB5E80F1AA977D,
	MeshText_set_text_m34245F193BDD11C6F26DD6E6F5583D18812AC0A3,
	MeshText_get_font_m25847F359B9EDFEF8D46472229C628B3373AE41E,
	MeshText_set_font_mB9CBB1FE88459AAD4B2EBA04E3EA49F5877C66A0,
	MeshText_get_fontSize_m7B2A9A06EFB43FAA47BEB6CAC22EC84043A994EA,
	MeshText_set_fontSize_m485E4B4D90FB875BD375D6F01396375E6F37E96D,
	MeshText_get_alignment_mACD9B910142BF894C4209FE8643492F468805182,
	MeshText_set_alignment_mA1CECD831CF620AB24064354E4C844FD61964791,
	MeshText_get_pixelsPerUnit_mF760A6F2A60E618ACFA77B329214849486CDFFAE,
	MeshText_get_texture_mE445904C1C1EDFE90756D133418DBA9A544E6E68,
	MeshText_get_cachedTextGenerator_mD4550E1BA44A2973D25318695A418AFB19139747,
	MeshText_Awake_mFDE58945678E258BF2BA41A749EC71054D92951F,
	MeshText_FontTextureChanged_m833206417EA3C16E5E26014721BC036EB605B0BA,
	MeshText_OnEnable_mE6D343FD36BD9E6557A10183317C3BB1FC86B214,
	MeshText_OnDisable_m031F16A52E90E2DA780CAD93F61544ED3381778A,
	MeshText_OnSizeChanged_m99FC27864FAB89918FCAFD6A5B0163888ADCB0DB,
	MeshText_SetVisible_mE9E0BF6993CD2AED1C4EC0E9B6F5C9D3318242C3,
	MeshText_GetGenerationSettings_m9B1A61E7591BC2A77CB8BD921EEC57189864D245,
	MeshText_RebuildGeometry_mB4A250BA085B300B4422B6B33CB65141FD86183D,
	MeshText__ctor_mDFA68C4EAABBF5A195B20E4EB90A14DAA394B4F1,
	BufferIndexer__ctor_mF59CBA80F45496ADFFA7098F9A54D6B72FA982DC,
	MeshVertexHelper_get_vertexCount_m2CC6B1129B8964EF4D7EB4E8F118071BCA629FAD,
	MeshVertexHelper_get_triangleCount_mF0A154C6C8A16D15DB7B94E1E038AA4796BBD86E,
	MeshVertexHelper__ctor_m7FC983B6BFB8809A4C001E32F740D49D8DB8DBD8,
	MeshVertexHelper_Dispose_mC5A1381CF2F91AF11F2490F32A40A0A26E28E247,
	MeshVertexHelper_SetDirty_m44CD1201FEB36335CB16ABB5DE9F2FE98467A16B,
	MeshVertexHelper_IsDirty_mB59F8C280D13C19DD0BD22F53A2B28AA361190FC,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	MeshVertexHelper_Clear_m5AA88A98606442F13D964EA92DE26D7284FFB8DD,
	MeshVertexHelper_Allocate_mE75539D6CEA6E5FEA3EB040BB9880564CBFD0858,
	MeshVertexHelper_Reallocate_m79C220AC44463C87AD1B513A5C4ADABF9A92888E,
	MeshVertexHelper_Deallocate_m3BC5B6B796690D43833BD7C1DE02993DF68AAA86,
	MeshVertexHelper_Resize_m7934D54F600BC2D02F6C5190E86AB0C915DB473E,
	MeshVertexHelper_BeginModify_mEDA40DB6D558FC7B15C1D74396BF5F72B53D5189,
	MeshVertexHelper_SetPosition_m0BF02447856C3838E1201988A239BBE74DDCF630,
	MeshVertexHelper_SetColor_m2F868C6BDC1BE1871A855018DEDA3CD7A55A1D77,
	MeshVertexHelper_SetUv_m36B2AFC9594EB5987A5DE1C2C5F68E440CBC2146,
	MeshVertexHelper_SetUv0_m1312E6FF38BC96DF6DC3DCD97591551E6A5FC07F,
	MeshVertexHelper_SetUv1_m4D038F430D2A98414C332BEA3810DE76475BA932,
	MeshVertexHelper_SetTriangle_mEE233C28EC03C9C544F297454F701DC0A95BC729,
	MeshVertexHelper_SetVertices_m025E69A1FB3FA06AB446D9EF8D6E762C9DF38D94,
	MeshVertexHelper_SetTriangles_m687495959B776232DF307037D07E7852C8795838,
	MeshVertexHelper_SetTriangles_m05CCA4A042A0AE1B37C8D92AD19919937639AD43,
	MeshVertexHelper_SetPositions_mE777F142FF62690D20776749E919B2E3D696CC6C,
	MeshVertexHelper_SetPositions_mD5D4731D82125C3F7A827B974BA52C876AF28297,
	MeshVertexHelper_SetColors_m948C822CBD000F04B000FDBEF6D75E0856297EC3,
	MeshVertexHelper_SetColors_mD9CFC82DB0F252CC65D70A70F5CC28DD2FE60612,
	MeshVertexHelper_SetUvs_m4D563A5F52366917FE5B0FA79AD0B39EFFCDE561,
	MeshVertexHelper_SetUvs_mD6F93BB8D8C70C35FB51DC1DE264FE733C7E1E90,
	MeshVertexHelper_SetUv0s_m450B76527FDDDE1FE1BB6227FEEF5AE5B1CED02B,
	MeshVertexHelper_SetUv0s_m78E24DDE95D5257711B789D7FE4525458896233B,
	MeshVertexHelper_SetUv1s_m8485B139850AC8744F13D996A77DF5D62EC505D8,
	MeshVertexHelper_SetUv1s_mB56E5D7E6AC1AABF3CC5C220B4186387E533D504,
	MeshVertexHelper_ApplyToMesh_m3840CFFA3684CFB657AE0DC6BA292E9F815DF9EC,
};
extern void BufferIndexer__ctor_mF59CBA80F45496ADFFA7098F9A54D6B72FA982DC_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[1] = 
{
	{ 0x060000B9, BufferIndexer__ctor_mF59CBA80F45496ADFFA7098F9A54D6B72FA982DC_AdjustorThunk },
};
static const int32_t s_InvokerIndices[223] = 
{
	13298,
	13298,
	13298,
	21390,
	13298,
	13298,
	13298,
	21355,
	13195,
	13195,
	13195,
	13195,
	13052,
	12996,
	10629,
	13052,
	12815,
	12815,
	13052,
	13298,
	13298,
	13298,
	10682,
	13298,
	13298,
	13195,
	13298,
	13298,
	13298,
	10682,
	10682,
	13298,
	13298,
	10442,
	5309,
	10442,
	13298,
	21355,
	21355,
	21334,
	20798,
	13298,
	21355,
	13298,
	21355,
	21274,
	20211,
	20511,
	13298,
	21274,
	13052,
	10682,
	12996,
	10629,
	12996,
	10629,
	12815,
	10442,
	12996,
	10629,
	12996,
	10629,
	12996,
	10629,
	12815,
	10442,
	12815,
	10442,
	12996,
	10629,
	12996,
	10629,
	13195,
	10823,
	13298,
	13298,
	13298,
	12815,
	12996,
	12830,
	10458,
	13052,
	13052,
	10682,
	13052,
	13298,
	13298,
	13298,
	13298,
	10907,
	10907,
	10629,
	13298,
	10442,
	13298,
	5309,
	7685,
	2176,
	10682,
	16890,
	13298,
	13083,
	13298,
	21355,
	13052,
	13298,
	10442,
	10907,
	10907,
	13298,
	13298,
	12815,
	10442,
	12815,
	10442,
	12815,
	10442,
	12815,
	10442,
	13195,
	10823,
	12815,
	10442,
	12996,
	10629,
	13052,
	10682,
	13052,
	12815,
	4711,
	9610,
	10682,
	5666,
	10682,
	5666,
	1928,
	14100,
	14822,
	13298,
	21355,
	10682,
	10682,
	10682,
	10682,
	13275,
	13275,
	13275,
	13275,
	10907,
	10907,
	13083,
	13083,
	13298,
	5684,
	10907,
	2426,
	10682,
	5684,
	10907,
	2426,
	10682,
	13298,
	10907,
	13298,
	13052,
	10682,
	13052,
	10682,
	12996,
	10629,
	12996,
	10629,
	13195,
	13052,
	13052,
	13298,
	13298,
	13298,
	13298,
	10907,
	10442,
	9509,
	10682,
	13298,
	5266,
	13260,
	12996,
	13298,
	13298,
	10629,
	7685,
	0,
	0,
	0,
	0,
	0,
	0,
	13298,
	4112,
	2127,
	10629,
	2642,
	9217,
	5646,
	5644,
	2694,
	5645,
	5645,
	2693,
	5309,
	5309,
	5266,
	5309,
	5433,
	5309,
	5187,
	2643,
	2650,
	5309,
	5428,
	5309,
	5428,
	4703,
};
static const Il2CppTokenRangePair s_rgctxIndices[6] = 
{
	{ 0x060000C0, { 0, 3 } },
	{ 0x060000C1, { 3, 4 } },
	{ 0x060000C2, { 7, 4 } },
	{ 0x060000C3, { 11, 4 } },
	{ 0x060000C4, { 15, 4 } },
	{ 0x060000C5, { 19, 6 } },
};
extern const uint32_t g_rgctx_NativeArray_1U26_tC0544586A7E7DB5473CA1084A12C3F8AF6639A17;
extern const uint32_t g_rgctx_NativeArray_1_tD897708E3874E9ECA6634EE1CFB660470508632D;
extern const uint32_t g_rgctx_NativeArray_1__ctor_mE8676623DBA3CBBFFF3F94DA707BF2FB245142FB;
extern const uint32_t g_rgctx_NativeArray_1U26_t7A5C95DDC9806856BCC47CACA899A6606AA9DFBF;
extern const uint32_t g_rgctx_NativeArray_1_get_IsCreated_m9994448EB533397DC68EC2E7C0DB059BD2A968DA;
extern const uint32_t g_rgctx_NativeArray_1_tCFDA42B50A94AF62CAF42A94100835425D34C722;
extern const uint32_t g_rgctx_NativeArray_1_Dispose_m53896825E96DC779C3DCEAF094A53B780A6D3703;
extern const uint32_t g_rgctx_NativeArray_1_t064A1F7F05F8CB11FAFC09680C6F0A4954435EC2;
extern const uint32_t g_rgctx_NativeArray_1_get_Item_m63515FDEC7E798EEA4D58BF5A9DE78AFFC23A394;
extern const uint32_t g_rgctx_NativeArray_1_t064A1F7F05F8CB11FAFC09680C6F0A4954435EC2;
extern const uint32_t g_rgctx_T_t4F26FACE490B4D01D604568E0425EC76FE718012;
extern const uint32_t g_rgctx_NativeArray_1U26_tF1E6825C492A45CC4E8C28495D404A71842B8F40;
extern const uint32_t g_rgctx_T_t9F0F798D98E0824104CD36E21EBE75BF6FA42CC3;
extern const uint32_t g_rgctx_NativeArray_1_set_Item_m0B5BCD657DA011276F22D68B286E700D5883AF88;
extern const uint32_t g_rgctx_NativeArray_1_tD33C3F386FFC112CE0372EBA8A223A3D18E60912;
extern const uint32_t g_rgctx_NativeArray_1U26_t43BA3A9167FAB62CE45294FD4F2C4059258F08DB;
extern const uint32_t g_rgctx_T_t814F215B2AC7FB977A0CAF9C620AE906811C6D33;
extern const uint32_t g_rgctx_NativeArray_1_set_Item_m000EF9130E64C0ABACDD0EF0458B5A37EBD0500F;
extern const uint32_t g_rgctx_NativeArray_1_t018325F7E64D4CBB90FC46CD29A586BD226C1101;
extern const uint32_t g_rgctx_NativeArray_1__ctor_m864D9B40970CD8A83D4C09E735EF1C09964BBDEC;
extern const uint32_t g_rgctx_NativeArray_1_tC184CB445E7C6BA1E87B1FD514416FA2889DDE3A;
extern const uint32_t g_rgctx_NativeArray_1U26_t3740654A2AC011033857A181C888C7A1B7F7E7B7;
extern const uint32_t g_rgctx_NativeArray_1_tC184CB445E7C6BA1E87B1FD514416FA2889DDE3A;
extern const uint32_t g_rgctx_NativeArray_1_Copy_mCD6A6C3EECEB7881892AD66AD902B19DA8BDE656;
extern const uint32_t g_rgctx_NativeArray_1_Dispose_m648E254381A309081ED571931EA41002C08C28FA;
static const Il2CppRGCTXDefinition s_rgctxValues[25] = 
{
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1U26_tC0544586A7E7DB5473CA1084A12C3F8AF6639A17 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_tD897708E3874E9ECA6634EE1CFB660470508632D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1__ctor_mE8676623DBA3CBBFFF3F94DA707BF2FB245142FB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1U26_t7A5C95DDC9806856BCC47CACA899A6606AA9DFBF },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_get_IsCreated_m9994448EB533397DC68EC2E7C0DB059BD2A968DA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_tCFDA42B50A94AF62CAF42A94100835425D34C722 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_Dispose_m53896825E96DC779C3DCEAF094A53B780A6D3703 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_t064A1F7F05F8CB11FAFC09680C6F0A4954435EC2 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_get_Item_m63515FDEC7E798EEA4D58BF5A9DE78AFFC23A394 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_t064A1F7F05F8CB11FAFC09680C6F0A4954435EC2 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t4F26FACE490B4D01D604568E0425EC76FE718012 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1U26_tF1E6825C492A45CC4E8C28495D404A71842B8F40 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t9F0F798D98E0824104CD36E21EBE75BF6FA42CC3 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_set_Item_m0B5BCD657DA011276F22D68B286E700D5883AF88 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_tD33C3F386FFC112CE0372EBA8A223A3D18E60912 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1U26_t43BA3A9167FAB62CE45294FD4F2C4059258F08DB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t814F215B2AC7FB977A0CAF9C620AE906811C6D33 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_set_Item_m000EF9130E64C0ABACDD0EF0458B5A37EBD0500F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_t018325F7E64D4CBB90FC46CD29A586BD226C1101 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1__ctor_m864D9B40970CD8A83D4C09E735EF1C09964BBDEC },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_tC184CB445E7C6BA1E87B1FD514416FA2889DDE3A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1U26_t3740654A2AC011033857A181C888C7A1B7F7E7B7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_NativeArray_1_tC184CB445E7C6BA1E87B1FD514416FA2889DDE3A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_Copy_mCD6A6C3EECEB7881892AD66AD902B19DA8BDE656 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_NativeArray_1_Dispose_m648E254381A309081ED571931EA41002C08C28FA },
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_MeshUI_CodeGenModule;
const Il2CppCodeGenModule g_MeshUI_CodeGenModule = 
{
	"MeshUI.dll",
	223,
	s_methodPointers,
	1,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	6,
	s_rgctxIndices,
	25,
	s_rgctxValues,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};

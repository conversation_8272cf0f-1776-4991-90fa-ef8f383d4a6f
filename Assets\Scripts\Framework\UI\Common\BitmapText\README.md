# BitmapText Library

A fully functional BitmapText library for Unity that allows using custom sprites as glyphs from a BitmapFont, with string-based glyph indexing and flexible layout options.

## Features

- **Custom Sprite Glyphs**: Use any sprite as a glyph character
- **String-based Indexing**: Map characters to sprite names for flexible glyph lookup
- **Dual Rendering**: Support for both Unity UI (UIBitmapText) and MeshRenderer (MeshBitmapText)
- **Flexible Layout**: Horizontal/vertical alignment, word wrapping, truncation
- **Auto-sizing**: Automatically adjust font size to fit within bounds
- **Rich Text Support**: Basic rich text tag processing (extensible)
- **Performance Optimized**: Efficient mesh generation and caching
- **Editor Tools**: Custom editor for font creation and management

## Components

### Core Classes

- **BitmapFont**: ScriptableObject that stores font data and glyph mappings
- **BitmapGlyph**: Individual glyph data with sprite, metrics, and UV coordinates
- **BitmapTextSettings**: Configuration for text layout and rendering
- **BitmapTextGenerator**: Static utility class for text layout and mesh generation

### Rendering Components

- **UIBitmapText**: Unity UI component (inherits from MaskableGraphic)
- **MeshBitmapText**: Direct mesh rendering component (uses MeshRenderer)

### Editor Tools

- **BitmapFontEditor**: Custom inspector for BitmapFont assets
- **BitmapTextTestWindow**: Test window for development and debugging

## Quick Start

### 1. Create a Bitmap Font

1. Right-click in Project window → Create → UI → Bitmap Font
2. Assign an atlas texture and material
3. Configure font settings (line height, baseline, space width)

### 2. Add Glyphs

**Method 1: Manual**
- In the BitmapFont inspector, click "Add Glyph"
- Set glyph name, character, and sprite
- Configure metrics (width, height, offsets, advance)

**Method 2: From Sprites**
- Select sprites in Project window
- In BitmapFont inspector, click "Generate from Sprites"

**Method 3: Auto-generate ASCII**
- In BitmapFont inspector, click "Auto-Generate ASCII"
- Creates placeholder glyphs for characters 32-126

### 3. Use in UI

```csharp
// Add UIBitmapText component to a GameObject with RectTransform
var bitmapText = gameObject.AddComponent<UIBitmapText>();
bitmapText.font = myBitmapFont;
bitmapText.text = "Hello World!";
bitmapText.settings.fontSize = 32f;
bitmapText.settings.alignment = BitmapTextAlignment.Center;
```

### 4. Use with MeshRenderer

```csharp
// Add MeshBitmapText component to a GameObject
var meshText = gameObject.AddComponent<MeshBitmapText>();
meshText.font = myBitmapFont;
meshText.text = "Hello World!";
meshText.color = Color.white;
```

## Configuration

### BitmapTextSettings

```csharp
var settings = new BitmapTextSettings();

// Layout
settings.alignment = BitmapTextAlignment.Left;
settings.verticalAlignment = BitmapTextVerticalAlignment.Top;
settings.overflow = BitmapTextOverflow.Wrap;

// Spacing
settings.lineSpacing = 1.0f;
settings.characterSpacing = 0f;
settings.wordSpacing = 0f;

// Size
settings.fontSize = 32f;
settings.maxSize = new Vector2(400f, 200f);

// Auto-sizing
settings.autoSize = true;
settings.minFontSize = 8f;
settings.maxFontSize = 72f;

// Rich text
settings.richText = true;
```

### Glyph Mapping

Glyphs can be mapped in two ways:

1. **Character mapping**: Direct character to glyph mapping
   ```csharp
   glyph.character = 'A';
   ```

2. **String mapping**: Use sprite names for lookup
   ```csharp
   glyph.glyphName = "icon_heart";
   // Use in text: "I {icon_heart} Unity!"
   ```

## Advanced Usage

### Custom Glyph Metrics

```csharp
var glyph = new BitmapGlyph();
glyph.sprite = mySprite;
glyph.width = 32f;
glyph.height = 32f;
glyph.xOffset = -2f;    // Horizontal offset from baseline
glyph.yOffset = 4f;     // Vertical offset from baseline
glyph.xAdvance = 28f;   // How much to advance cursor
```

### Runtime Text Generation

```csharp
// Generate text layout info
var textInfo = BitmapTextGenerator.GenerateText(
    "Hello World!", 
    myFont, 
    settings, 
    Color.white
);

// Use with custom mesh generation
using (var vh = new VertexHelper())
{
    BitmapTextGenerator.PopulateMesh(vh, textInfo, Vector2.zero);
    vh.FillMesh(myMesh);
}
```

### Performance Tips

1. **Cache BitmapTextInfo**: Reuse text info when text doesn't change
2. **Use object pooling**: Pool UIBitmapText components for dynamic text
3. **Batch similar fonts**: Group text using the same atlas for better batching
4. **Optimize atlas**: Use texture atlases to reduce draw calls
5. **Disable auto-update**: Set `autoUpdateMesh = false` for manual control

## Shader Support

The library includes a custom UI shader (`UI/BitmapText`) that supports:
- Unity UI masking and clipping
- Stencil operations
- Alpha blending
- Color tinting

For custom rendering, create materials using this shader or compatible alternatives.

## Limitations

- Rich text support is basic (tags are stripped by default)
- No built-in text effects (outline, shadow, glow)
- Requires manual glyph setup for complex fonts
- No automatic kerning support

## Extension Points

The library is designed to be extensible:

1. **Custom text processors**: Extend `ProcessRichText()` for advanced formatting
2. **Custom layout**: Modify `BitmapTextGenerator` for special layout needs
3. **Custom effects**: Add post-processing to mesh generation
4. **Animation support**: Integrate with animation systems for dynamic effects

## Testing

Use the test window (Tools → Bitmap Text → Test Window) to:
- Test text generation with different settings
- Create test UI and mesh components
- Validate font configurations
- Debug layout issues

## Troubleshooting

**Text not appearing:**
- Check if font has glyphs for the characters
- Verify atlas texture is assigned
- Ensure material is set up correctly

**Incorrect layout:**
- Check font metrics (line height, baseline)
- Verify glyph metrics (offsets, advance)
- Test with different alignment settings

**Performance issues:**
- Use texture atlases to reduce draw calls
- Cache text info for static text
- Consider using MeshBitmapText for world-space text

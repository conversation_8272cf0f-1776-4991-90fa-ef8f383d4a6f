﻿#include "pch-cpp.hpp"

#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include <limits>



struct Dictionary_2_t14FE4A752A83D53771C584E4C8D14E01F2AFD7BA;
struct Dictionary_2_t46BDD5FB58E104E19811C6D31E65C42A8C31E22D;
struct IEqualityComparer_1_tAE94C8F24AD5B94D4EE85CA9FC59E3409D41CAF7;
struct KeyCollection_tC3A392A4EE0024F028A6499736FEAAD0FD59D528;
struct ValueCollection_t9912505E5A0274EDB46B2D902073B564943E85EF;
struct EntryU5BU5D_t8963514300A523BF0EB68259C2A5B7AE763C7D50;
struct ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031;
struct CharU5BU5D_t799905CF001DD5F13F7DBB310181FC4D8B7D0AAB;
struct Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C;
struct LanguageSettingU5BU5D_tAE0787335D4680BB400B842FB001822B83CBC442;
struct StyleSettingU5BU5D_tB9945EA1D6BD8D2649065131E77639B353E80256;
struct ItemU5BU5D_tD7F3B3D8226850AD66B5F16C0C7D94EB1EFFE543;
struct LocalizationSettings_t46904EA548DB13AFA7FFA7C22FC8C64394367E09;
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C;
struct ScriptableObject_tB3BFDB921A1B1795B38A5417D3B97A89A140436A;
struct String_t;
struct TextStyle_t33719BFAB33661D524398E879DE946CEF7557219;
struct UnitySourceGeneratedAssemblyMonoScriptTypes_v1_t72DF32F0C5F9EBF82AB1C017022844BA85746157;
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915;
struct SerializeHelper_tE39A1A0A5C8BD31451E8967CDCF10555C74E1899;
struct Translated_tA0B59BCB4DE04E161D84723BBE40FC8D5C9261E6;
struct LanguageSetting_t9400E93460512E993A221BDF49322D76A775E5EC;
struct StyleSetting_tA7D12294156C603EE91AD3F53512F3762F84F4E8;
struct Item_t837D18784B40CDF82BD13F22813E1BECF4FB4EAD;

IL2CPP_EXTERN_C RuntimeClass* ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Debug_t8394C7EEAECA3689C2C9B9DE9C7166D73596276F_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Dictionary_2_t46BDD5FB58E104E19811C6D31E65C42A8C31E22D_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Localization_t5FE49AA1BA29032C893FFC09EAE5D90EE32E7EEF_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* String_t_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* eLanguage_t8C1C96826F2E44762BB5944B7474A89D991C6A22_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeField* U3CPrivateImplementationDetailsU3E_t51C49F58D80CD992F80B05043C7E841F5C91AE18____B722E6A119B71C932586E55DD891CB8CC7745F31F9FC628A3C88908AB82BDE26_FieldInfo_var;
IL2CPP_EXTERN_C RuntimeField* U3CPrivateImplementationDetailsU3E_t51C49F58D80CD992F80B05043C7E841F5C91AE18____B9A95D496C5763438B517EFDF75DDBC84958BEA42C59C137B83AE176302F12A2_FieldInfo_var;
IL2CPP_EXTERN_C String_t* _stringLiteral3764F77195033ACDE8A1EEA686582EECE287BAD8;
IL2CPP_EXTERN_C String_t* _stringLiteral429D30B79086E6A5E61CA34363FFBB02F5066B69;
IL2CPP_EXTERN_C String_t* _stringLiteral96E9187B77341E2A75B3FFED0BDD4882BE8A5669;
IL2CPP_EXTERN_C const RuntimeMethod* Dictionary_2_Clear_m37FB230D1AE0147D3D1F1D110B257BCAC0C8FC1C_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Dictionary_2__ctor_mBDED3672E7C570203308A49157138FF083DAC0EC_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Resources_Load_TisLocalizationSettings_t46904EA548DB13AFA7FFA7C22FC8C64394367E09_mFB040E511E42247BECF2E53A9E90F041B514F22C_RuntimeMethod_var;

struct ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031;
struct LanguageSettingU5BU5D_tAE0787335D4680BB400B842FB001822B83CBC442;

IL2CPP_EXTERN_C_BEGIN
IL2CPP_EXTERN_C_END

#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
struct U3CModuleU3E_t599B6710EEFDCF40B71D97D2C0D02026421CC897 
{
};
struct Dictionary_2_t46BDD5FB58E104E19811C6D31E65C42A8C31E22D  : public RuntimeObject
{
	Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ____buckets;
	EntryU5BU5D_t8963514300A523BF0EB68259C2A5B7AE763C7D50* ____entries;
	int32_t ____count;
	int32_t ____freeList;
	int32_t ____freeCount;
	int32_t ____version;
	RuntimeObject* ____comparer;
	KeyCollection_tC3A392A4EE0024F028A6499736FEAAD0FD59D528* ____keys;
	ValueCollection_t9912505E5A0274EDB46B2D902073B564943E85EF* ____values;
	RuntimeObject* ____syncRoot;
};
struct U3CPrivateImplementationDetailsU3E_t51C49F58D80CD992F80B05043C7E841F5C91AE18  : public RuntimeObject
{
};
struct Localization_t5FE49AA1BA29032C893FFC09EAE5D90EE32E7EEF  : public RuntimeObject
{
};
struct String_t  : public RuntimeObject
{
	int32_t ____stringLength;
	Il2CppChar ____firstChar;
};
struct UnitySourceGeneratedAssemblyMonoScriptTypes_v1_t72DF32F0C5F9EBF82AB1C017022844BA85746157  : public RuntimeObject
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F  : public RuntimeObject
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_pinvoke
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_com
{
};
struct SerializeHelper_tE39A1A0A5C8BD31451E8967CDCF10555C74E1899  : public RuntimeObject
{
	ItemU5BU5D_tD7F3B3D8226850AD66B5F16C0C7D94EB1EFFE543* ___items;
};
struct Translated_tA0B59BCB4DE04E161D84723BBE40FC8D5C9261E6  : public RuntimeObject
{
	String_t* ___Value;
};
struct Item_t837D18784B40CDF82BD13F22813E1BECF4FB4EAD  : public RuntimeObject
{
	String_t* ___Key;
	String_t* ___Value;
};
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22 
{
	bool ___m_value;
};
struct Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3 
{
	uint8_t ___m_value;
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2  : public ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F
{
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2_marshaled_pinvoke
{
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2_marshaled_com
{
};
struct Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C 
{
	int32_t ___m_value;
};
struct IntPtr_t 
{
	void* ___m_value;
};
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915 
{
	union
	{
		struct
		{
		};
		uint8_t Void_t4861ACF8F4594C3437BB48B6E56783494B843915__padding[1];
	};
};
#pragma pack(push, tp, 1)
struct __StaticArrayInitTypeSizeU3D217_tC477EAD42B70007D7F13451C276BF9DE03353DEA 
{
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D217_tC477EAD42B70007D7F13451C276BF9DE03353DEA__padding[217];
	};
};
#pragma pack(pop, tp)
#pragma pack(push, tp, 1)
struct __StaticArrayInitTypeSizeU3D279_tDF367236F1291A556C4DC3A611D5AA77F7F5B2CF 
{
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D279_tDF367236F1291A556C4DC3A611D5AA77F7F5B2CF__padding[279];
	};
};
#pragma pack(pop, tp)
struct MonoScriptData_tC696FE8BC954E6967CD54CF6874817755B22C7CB 
{
	ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* ___FilePathsData;
	ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* ___TypesData;
	int32_t ___TotalTypes;
	int32_t ___TotalFiles;
	bool ___IsEditorOnly;
};
struct MonoScriptData_tC696FE8BC954E6967CD54CF6874817755B22C7CB_marshaled_pinvoke
{
	Il2CppSafeArray* ___FilePathsData;
	Il2CppSafeArray* ___TypesData;
	int32_t ___TotalTypes;
	int32_t ___TotalFiles;
	int32_t ___IsEditorOnly;
};
struct MonoScriptData_tC696FE8BC954E6967CD54CF6874817755B22C7CB_marshaled_com
{
	Il2CppSafeArray* ___FilePathsData;
	Il2CppSafeArray* ___TypesData;
	int32_t ___TotalTypes;
	int32_t ___TotalFiles;
	int32_t ___IsEditorOnly;
};
struct FontStyle_tDD46734FA9BCB99FB315CD7CAD1137EE536136D1 
{
	int32_t ___value__;
};
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C  : public RuntimeObject
{
	intptr_t ___m_CachedPtr;
};
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_marshaled_pinvoke
{
	intptr_t ___m_CachedPtr;
};
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_marshaled_com
{
	intptr_t ___m_CachedPtr;
};
struct RuntimeFieldHandle_t6E4C45B6D2EA12FC99185805A7E77527899B25C5 
{
	intptr_t ___value;
};
struct eLanguage_t8C1C96826F2E44762BB5944B7474A89D991C6A22 
{
	uint8_t ___value__;
};
struct ScriptableObject_tB3BFDB921A1B1795B38A5417D3B97A89A140436A  : public Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C
{
};
struct ScriptableObject_tB3BFDB921A1B1795B38A5417D3B97A89A140436A_marshaled_pinvoke : public Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_marshaled_pinvoke
{
};
struct ScriptableObject_tB3BFDB921A1B1795B38A5417D3B97A89A140436A_marshaled_com : public Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_marshaled_com
{
};
struct LanguageSetting_t9400E93460512E993A221BDF49322D76A775E5EC  : public RuntimeObject
{
	uint8_t ___Language;
	String_t* ___LocalizationFile;
};
struct StyleSetting_tA7D12294156C603EE91AD3F53512F3762F84F4E8  : public RuntimeObject
{
	String_t* ___StyleName;
	int32_t ___FontSize;
	int32_t ___FontStyle;
};
struct LocalizationSettings_t46904EA548DB13AFA7FFA7C22FC8C64394367E09  : public ScriptableObject_tB3BFDB921A1B1795B38A5417D3B97A89A140436A
{
	LanguageSettingU5BU5D_tAE0787335D4680BB400B842FB001822B83CBC442* ___languageSettings;
};
struct TextStyle_t33719BFAB33661D524398E879DE946CEF7557219  : public ScriptableObject_tB3BFDB921A1B1795B38A5417D3B97A89A140436A
{
	StyleSettingU5BU5D_tB9945EA1D6BD8D2649065131E77639B353E80256* ___Styles;
};
struct U3CPrivateImplementationDetailsU3E_t51C49F58D80CD992F80B05043C7E841F5C91AE18_StaticFields
{
	__StaticArrayInitTypeSizeU3D279_tDF367236F1291A556C4DC3A611D5AA77F7F5B2CF ___B722E6A119B71C932586E55DD891CB8CC7745F31F9FC628A3C88908AB82BDE26;
	__StaticArrayInitTypeSizeU3D217_tC477EAD42B70007D7F13451C276BF9DE03353DEA ___B9A95D496C5763438B517EFDF75DDBC84958BEA42C59C137B83AE176302F12A2;
};
struct Localization_t5FE49AA1BA29032C893FFC09EAE5D90EE32E7EEF_StaticFields
{
	String_t* ___kLocalizationSettingsPath;
	LocalizationSettings_t46904EA548DB13AFA7FFA7C22FC8C64394367E09* ____localizationSettings;
	uint8_t ___U3CCurrentLanguageU3Ek__BackingField;
	Dictionary_2_t46BDD5FB58E104E19811C6D31E65C42A8C31E22D* ____translations;
};
struct String_t_StaticFields
{
	String_t* ___Empty;
};
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_StaticFields
{
	String_t* ___TrueString;
	String_t* ___FalseString;
};
struct Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_StaticFields
{
	int32_t ___OffsetOfInstanceIDInCPlusPlusObject;
};
#ifdef __clang__
#pragma clang diagnostic pop
#endif
struct ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031  : public RuntimeArray
{
	ALIGN_FIELD (8) uint8_t m_Items[1];

	inline uint8_t GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline uint8_t* GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, uint8_t value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
	}
	inline uint8_t GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline uint8_t* GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, uint8_t value)
	{
		m_Items[index] = value;
	}
};
struct LanguageSettingU5BU5D_tAE0787335D4680BB400B842FB001822B83CBC442  : public RuntimeArray
{
	ALIGN_FIELD (8) LanguageSetting_t9400E93460512E993A221BDF49322D76A775E5EC* m_Items[1];

	inline LanguageSetting_t9400E93460512E993A221BDF49322D76A775E5EC* GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline LanguageSetting_t9400E93460512E993A221BDF49322D76A775E5EC** GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, LanguageSetting_t9400E93460512E993A221BDF49322D76A775E5EC* value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
	inline LanguageSetting_t9400E93460512E993A221BDF49322D76A775E5EC* GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline LanguageSetting_t9400E93460512E993A221BDF49322D76A775E5EC** GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, LanguageSetting_t9400E93460512E993A221BDF49322D76A775E5EC* value)
	{
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
};


IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* Resources_Load_TisRuntimeObject_mD1AF6299B14F87ED1D1A6199A51480919F7C79D7_gshared (String_t* ___0_path, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Dictionary_2_Clear_mCFB5EA7351D5860D2B91592B91A84CA265A41433_gshared (Dictionary_2_t14FE4A752A83D53771C584E4C8D14E01F2AFD7BA* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Dictionary_2__ctor_m5B32FBC624618211EB461D59CFBB10E987FD1329_gshared (Dictionary_2_t14FE4A752A83D53771C584E4C8D14E01F2AFD7BA* __this, const RuntimeMethod* method) ;

IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RuntimeHelpers_InitializeArray_m751372AA3F24FBF6DA9B9D687CBFA2DE436CAB9B (RuntimeArray* ___0_array, RuntimeFieldHandle_t6E4C45B6D2EA12FC99185805A7E77527899B25C5 ___1_fldHandle, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2 (RuntimeObject* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Object_op_Equality_mB6120F782D83091EF56A198FCEBCF066DB4A9605 (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C* ___0_x, Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C* ___1_y, const RuntimeMethod* method) ;
inline LocalizationSettings_t46904EA548DB13AFA7FFA7C22FC8C64394367E09* Resources_Load_TisLocalizationSettings_t46904EA548DB13AFA7FFA7C22FC8C64394367E09_mFB040E511E42247BECF2E53A9E90F041B514F22C (String_t* ___0_path, const RuntimeMethod* method)
{
	return ((  LocalizationSettings_t46904EA548DB13AFA7FFA7C22FC8C64394367E09* (*) (String_t*, const RuntimeMethod*))Resources_Load_TisRuntimeObject_mD1AF6299B14F87ED1D1A6199A51480919F7C79D7_gshared)(___0_path, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* String_Concat_m9E3155FB84015C823606188F53B47CB44C444991 (String_t* ___0_str0, String_t* ___1_str1, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Debug_LogError_mB00B2B4468EF3CAF041B038D840820FB84C924B2 (RuntimeObject* ___0_message, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* Localization_GetLocalizationFile_mFA807E3A9F56234F464044182627AC21A756D89D (uint8_t ___0_language, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool String_IsNullOrEmpty_mEA9E3FB005AC28FE02E69FCF95A7B8456192B478 (String_t* ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* String_Format_mA8DBB4C2516B9723C5A41E6CB1E2FAF4BBE96DD8 (String_t* ___0_format, RuntimeObject* ___1_arg0, const RuntimeMethod* method) ;
inline void Dictionary_2_Clear_m37FB230D1AE0147D3D1F1D110B257BCAC0C8FC1C (Dictionary_2_t46BDD5FB58E104E19811C6D31E65C42A8C31E22D* __this, const RuntimeMethod* method)
{
	((  void (*) (Dictionary_2_t46BDD5FB58E104E19811C6D31E65C42A8C31E22D*, const RuntimeMethod*))Dictionary_2_Clear_mCFB5EA7351D5860D2B91592B91A84CA265A41433_gshared)(__this, method);
}
inline void Dictionary_2__ctor_mBDED3672E7C570203308A49157138FF083DAC0EC (Dictionary_2_t46BDD5FB58E104E19811C6D31E65C42A8C31E22D* __this, const RuntimeMethod* method)
{
	((  void (*) (Dictionary_2_t46BDD5FB58E104E19811C6D31E65C42A8C31E22D*, const RuntimeMethod*))Dictionary_2__ctor_m5B32FBC624618211EB461D59CFBB10E987FD1329_gshared)(__this, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ScriptableObject__ctor_mD037FDB0B487295EA47F79A4DB1BF1846C9087FF (ScriptableObject_tB3BFDB921A1B1795B38A5417D3B97A89A140436A* __this, const RuntimeMethod* method) ;
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR MonoScriptData_tC696FE8BC954E6967CD54CF6874817755B22C7CB UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_m3FD80EDA842E2412B4692EAFCE58AB4212CCE716 (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CPrivateImplementationDetailsU3E_t51C49F58D80CD992F80B05043C7E841F5C91AE18____B722E6A119B71C932586E55DD891CB8CC7745F31F9FC628A3C88908AB82BDE26_FieldInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CPrivateImplementationDetailsU3E_t51C49F58D80CD992F80B05043C7E841F5C91AE18____B9A95D496C5763438B517EFDF75DDBC84958BEA42C59C137B83AE176302F12A2_FieldInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	MonoScriptData_tC696FE8BC954E6967CD54CF6874817755B22C7CB V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		il2cpp_codegen_initobj((&V_0), sizeof(MonoScriptData_tC696FE8BC954E6967CD54CF6874817755B22C7CB));
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_0 = (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)SZArrayNew(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031_il2cpp_TypeInfo_var, (uint32_t)((int32_t)217));
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_1 = L_0;
		RuntimeFieldHandle_t6E4C45B6D2EA12FC99185805A7E77527899B25C5 L_2 = { reinterpret_cast<intptr_t> (U3CPrivateImplementationDetailsU3E_t51C49F58D80CD992F80B05043C7E841F5C91AE18____B9A95D496C5763438B517EFDF75DDBC84958BEA42C59C137B83AE176302F12A2_FieldInfo_var) };
		RuntimeHelpers_InitializeArray_m751372AA3F24FBF6DA9B9D687CBFA2DE436CAB9B((RuntimeArray*)L_1, L_2, NULL);
		(&V_0)->___FilePathsData = L_1;
		Il2CppCodeGenWriteBarrier((void**)(&(&V_0)->___FilePathsData), (void*)L_1);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_3 = (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)SZArrayNew(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031_il2cpp_TypeInfo_var, (uint32_t)((int32_t)279));
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_4 = L_3;
		RuntimeFieldHandle_t6E4C45B6D2EA12FC99185805A7E77527899B25C5 L_5 = { reinterpret_cast<intptr_t> (U3CPrivateImplementationDetailsU3E_t51C49F58D80CD992F80B05043C7E841F5C91AE18____B722E6A119B71C932586E55DD891CB8CC7745F31F9FC628A3C88908AB82BDE26_FieldInfo_var) };
		RuntimeHelpers_InitializeArray_m751372AA3F24FBF6DA9B9D687CBFA2DE436CAB9B((RuntimeArray*)L_4, L_5, NULL);
		(&V_0)->___TypesData = L_4;
		Il2CppCodeGenWriteBarrier((void**)(&(&V_0)->___TypesData), (void*)L_4);
		(&V_0)->___TotalFiles = 3;
		(&V_0)->___TotalTypes = 8;
		(&V_0)->___IsEditorOnly = (bool)0;
		MonoScriptData_tC696FE8BC954E6967CD54CF6874817755B22C7CB L_6 = V_0;
		return L_6;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_m675666E40643F5105DBA62F0DF47E72BDFC21661 (UnitySourceGeneratedAssemblyMonoScriptTypes_v1_t72DF32F0C5F9EBF82AB1C017022844BA85746157* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C void MonoScriptData_tC696FE8BC954E6967CD54CF6874817755B22C7CB_marshal_pinvoke(const MonoScriptData_tC696FE8BC954E6967CD54CF6874817755B22C7CB& unmarshaled, MonoScriptData_tC696FE8BC954E6967CD54CF6874817755B22C7CB_marshaled_pinvoke& marshaled)
{
	marshaled.___FilePathsData = il2cpp_codegen_com_marshal_safe_array(IL2CPP_VT_I1, unmarshaled.___FilePathsData);
	marshaled.___TypesData = il2cpp_codegen_com_marshal_safe_array(IL2CPP_VT_I1, unmarshaled.___TypesData);
	marshaled.___TotalTypes = unmarshaled.___TotalTypes;
	marshaled.___TotalFiles = unmarshaled.___TotalFiles;
	marshaled.___IsEditorOnly = static_cast<int32_t>(unmarshaled.___IsEditorOnly);
}
IL2CPP_EXTERN_C void MonoScriptData_tC696FE8BC954E6967CD54CF6874817755B22C7CB_marshal_pinvoke_back(const MonoScriptData_tC696FE8BC954E6967CD54CF6874817755B22C7CB_marshaled_pinvoke& marshaled, MonoScriptData_tC696FE8BC954E6967CD54CF6874817755B22C7CB& unmarshaled)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	unmarshaled.___FilePathsData = (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I1, Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, marshaled.___FilePathsData);
	Il2CppCodeGenWriteBarrier((void**)(&unmarshaled.___FilePathsData), (void*)(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I1, Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, marshaled.___FilePathsData));
	unmarshaled.___TypesData = (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I1, Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, marshaled.___TypesData);
	Il2CppCodeGenWriteBarrier((void**)(&unmarshaled.___TypesData), (void*)(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I1, Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, marshaled.___TypesData));
	int32_t unmarshaledTotalTypes_temp_2 = 0;
	unmarshaledTotalTypes_temp_2 = marshaled.___TotalTypes;
	unmarshaled.___TotalTypes = unmarshaledTotalTypes_temp_2;
	int32_t unmarshaledTotalFiles_temp_3 = 0;
	unmarshaledTotalFiles_temp_3 = marshaled.___TotalFiles;
	unmarshaled.___TotalFiles = unmarshaledTotalFiles_temp_3;
	bool unmarshaledIsEditorOnly_temp_4 = false;
	unmarshaledIsEditorOnly_temp_4 = static_cast<bool>(marshaled.___IsEditorOnly);
	unmarshaled.___IsEditorOnly = unmarshaledIsEditorOnly_temp_4;
}
IL2CPP_EXTERN_C void MonoScriptData_tC696FE8BC954E6967CD54CF6874817755B22C7CB_marshal_pinvoke_cleanup(MonoScriptData_tC696FE8BC954E6967CD54CF6874817755B22C7CB_marshaled_pinvoke& marshaled)
{
	il2cpp_codegen_com_destroy_safe_array(marshaled.___FilePathsData);
	marshaled.___FilePathsData = NULL;
	il2cpp_codegen_com_destroy_safe_array(marshaled.___TypesData);
	marshaled.___TypesData = NULL;
}
IL2CPP_EXTERN_C void MonoScriptData_tC696FE8BC954E6967CD54CF6874817755B22C7CB_marshal_com(const MonoScriptData_tC696FE8BC954E6967CD54CF6874817755B22C7CB& unmarshaled, MonoScriptData_tC696FE8BC954E6967CD54CF6874817755B22C7CB_marshaled_com& marshaled)
{
	marshaled.___FilePathsData = il2cpp_codegen_com_marshal_safe_array(IL2CPP_VT_I1, unmarshaled.___FilePathsData);
	marshaled.___TypesData = il2cpp_codegen_com_marshal_safe_array(IL2CPP_VT_I1, unmarshaled.___TypesData);
	marshaled.___TotalTypes = unmarshaled.___TotalTypes;
	marshaled.___TotalFiles = unmarshaled.___TotalFiles;
	marshaled.___IsEditorOnly = static_cast<int32_t>(unmarshaled.___IsEditorOnly);
}
IL2CPP_EXTERN_C void MonoScriptData_tC696FE8BC954E6967CD54CF6874817755B22C7CB_marshal_com_back(const MonoScriptData_tC696FE8BC954E6967CD54CF6874817755B22C7CB_marshaled_com& marshaled, MonoScriptData_tC696FE8BC954E6967CD54CF6874817755B22C7CB& unmarshaled)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	unmarshaled.___FilePathsData = (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I1, Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, marshaled.___FilePathsData);
	Il2CppCodeGenWriteBarrier((void**)(&unmarshaled.___FilePathsData), (void*)(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I1, Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, marshaled.___FilePathsData));
	unmarshaled.___TypesData = (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I1, Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, marshaled.___TypesData);
	Il2CppCodeGenWriteBarrier((void**)(&unmarshaled.___TypesData), (void*)(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I1, Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, marshaled.___TypesData));
	int32_t unmarshaledTotalTypes_temp_2 = 0;
	unmarshaledTotalTypes_temp_2 = marshaled.___TotalTypes;
	unmarshaled.___TotalTypes = unmarshaledTotalTypes_temp_2;
	int32_t unmarshaledTotalFiles_temp_3 = 0;
	unmarshaledTotalFiles_temp_3 = marshaled.___TotalFiles;
	unmarshaled.___TotalFiles = unmarshaledTotalFiles_temp_3;
	bool unmarshaledIsEditorOnly_temp_4 = false;
	unmarshaledIsEditorOnly_temp_4 = static_cast<bool>(marshaled.___IsEditorOnly);
	unmarshaled.___IsEditorOnly = unmarshaledIsEditorOnly_temp_4;
}
IL2CPP_EXTERN_C void MonoScriptData_tC696FE8BC954E6967CD54CF6874817755B22C7CB_marshal_com_cleanup(MonoScriptData_tC696FE8BC954E6967CD54CF6874817755B22C7CB_marshaled_com& marshaled)
{
	il2cpp_codegen_com_destroy_safe_array(marshaled.___FilePathsData);
	marshaled.___FilePathsData = NULL;
	il2cpp_codegen_com_destroy_safe_array(marshaled.___TypesData);
	marshaled.___TypesData = NULL;
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR uint8_t Localization_get_CurrentLanguage_mDE9179BCA77E2547013721F10AB5484A03B3ECFC (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Localization_t5FE49AA1BA29032C893FFC09EAE5D90EE32E7EEF_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		il2cpp_codegen_runtime_class_init_inline(Localization_t5FE49AA1BA29032C893FFC09EAE5D90EE32E7EEF_il2cpp_TypeInfo_var);
		uint8_t L_0 = ((Localization_t5FE49AA1BA29032C893FFC09EAE5D90EE32E7EEF_StaticFields*)il2cpp_codegen_static_fields_for(Localization_t5FE49AA1BA29032C893FFC09EAE5D90EE32E7EEF_il2cpp_TypeInfo_var))->___U3CCurrentLanguageU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Localization_set_CurrentLanguage_m841FABFFFA84EB4B4B1CDC66409AF6A6E086379C (uint8_t ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Localization_t5FE49AA1BA29032C893FFC09EAE5D90EE32E7EEF_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		uint8_t L_0 = ___0_value;
		il2cpp_codegen_runtime_class_init_inline(Localization_t5FE49AA1BA29032C893FFC09EAE5D90EE32E7EEF_il2cpp_TypeInfo_var);
		((Localization_t5FE49AA1BA29032C893FFC09EAE5D90EE32E7EEF_StaticFields*)il2cpp_codegen_static_fields_for(Localization_t5FE49AA1BA29032C893FFC09EAE5D90EE32E7EEF_il2cpp_TypeInfo_var))->___U3CCurrentLanguageU3Ek__BackingField = L_0;
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Localization_Initialize_m26388D559D20CDFA24FFC1C0F2C95C4606D53EB1 (uint8_t ___0_language, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Debug_t8394C7EEAECA3689C2C9B9DE9C7166D73596276F_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Dictionary_2_Clear_m37FB230D1AE0147D3D1F1D110B257BCAC0C8FC1C_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Localization_t5FE49AA1BA29032C893FFC09EAE5D90EE32E7EEF_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Resources_Load_TisLocalizationSettings_t46904EA548DB13AFA7FFA7C22FC8C64394367E09_mFB040E511E42247BECF2E53A9E90F041B514F22C_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral429D30B79086E6A5E61CA34363FFBB02F5066B69);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral96E9187B77341E2A75B3FFED0BDD4882BE8A5669);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&eLanguage_t8C1C96826F2E44762BB5944B7474A89D991C6A22_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		il2cpp_codegen_runtime_class_init_inline(Localization_t5FE49AA1BA29032C893FFC09EAE5D90EE32E7EEF_il2cpp_TypeInfo_var);
		LocalizationSettings_t46904EA548DB13AFA7FFA7C22FC8C64394367E09* L_0 = ((Localization_t5FE49AA1BA29032C893FFC09EAE5D90EE32E7EEF_StaticFields*)il2cpp_codegen_static_fields_for(Localization_t5FE49AA1BA29032C893FFC09EAE5D90EE32E7EEF_il2cpp_TypeInfo_var))->____localizationSettings;
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_1;
		L_1 = Object_op_Equality_mB6120F782D83091EF56A198FCEBCF066DB4A9605(L_0, (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*)NULL, NULL);
		if (!L_1)
		{
			goto IL_001c;
		}
	}
	{
		il2cpp_codegen_runtime_class_init_inline(Localization_t5FE49AA1BA29032C893FFC09EAE5D90EE32E7EEF_il2cpp_TypeInfo_var);
		String_t* L_2 = ((Localization_t5FE49AA1BA29032C893FFC09EAE5D90EE32E7EEF_StaticFields*)il2cpp_codegen_static_fields_for(Localization_t5FE49AA1BA29032C893FFC09EAE5D90EE32E7EEF_il2cpp_TypeInfo_var))->___kLocalizationSettingsPath;
		LocalizationSettings_t46904EA548DB13AFA7FFA7C22FC8C64394367E09* L_3;
		L_3 = Resources_Load_TisLocalizationSettings_t46904EA548DB13AFA7FFA7C22FC8C64394367E09_mFB040E511E42247BECF2E53A9E90F041B514F22C(L_2, Resources_Load_TisLocalizationSettings_t46904EA548DB13AFA7FFA7C22FC8C64394367E09_mFB040E511E42247BECF2E53A9E90F041B514F22C_RuntimeMethod_var);
		((Localization_t5FE49AA1BA29032C893FFC09EAE5D90EE32E7EEF_StaticFields*)il2cpp_codegen_static_fields_for(Localization_t5FE49AA1BA29032C893FFC09EAE5D90EE32E7EEF_il2cpp_TypeInfo_var))->____localizationSettings = L_3;
		Il2CppCodeGenWriteBarrier((void**)(&((Localization_t5FE49AA1BA29032C893FFC09EAE5D90EE32E7EEF_StaticFields*)il2cpp_codegen_static_fields_for(Localization_t5FE49AA1BA29032C893FFC09EAE5D90EE32E7EEF_il2cpp_TypeInfo_var))->____localizationSettings), (void*)L_3);
	}

IL_001c:
	{
		il2cpp_codegen_runtime_class_init_inline(Localization_t5FE49AA1BA29032C893FFC09EAE5D90EE32E7EEF_il2cpp_TypeInfo_var);
		LocalizationSettings_t46904EA548DB13AFA7FFA7C22FC8C64394367E09* L_4 = ((Localization_t5FE49AA1BA29032C893FFC09EAE5D90EE32E7EEF_StaticFields*)il2cpp_codegen_static_fields_for(Localization_t5FE49AA1BA29032C893FFC09EAE5D90EE32E7EEF_il2cpp_TypeInfo_var))->____localizationSettings;
		il2cpp_codegen_runtime_class_init_inline(Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C_il2cpp_TypeInfo_var);
		bool L_5;
		L_5 = Object_op_Equality_mB6120F782D83091EF56A198FCEBCF066DB4A9605(L_4, (Object_tC12DECB6760A7F2CBF65D9DCF18D044C2D97152C*)NULL, NULL);
		if (!L_5)
		{
			goto IL_003f;
		}
	}
	{
		il2cpp_codegen_runtime_class_init_inline(Localization_t5FE49AA1BA29032C893FFC09EAE5D90EE32E7EEF_il2cpp_TypeInfo_var);
		String_t* L_6 = ((Localization_t5FE49AA1BA29032C893FFC09EAE5D90EE32E7EEF_StaticFields*)il2cpp_codegen_static_fields_for(Localization_t5FE49AA1BA29032C893FFC09EAE5D90EE32E7EEF_il2cpp_TypeInfo_var))->___kLocalizationSettingsPath;
		String_t* L_7;
		L_7 = String_Concat_m9E3155FB84015C823606188F53B47CB44C444991(_stringLiteral429D30B79086E6A5E61CA34363FFBB02F5066B69, L_6, NULL);
		il2cpp_codegen_runtime_class_init_inline(Debug_t8394C7EEAECA3689C2C9B9DE9C7166D73596276F_il2cpp_TypeInfo_var);
		Debug_LogError_mB00B2B4468EF3CAF041B038D840820FB84C924B2(L_7, NULL);
		return (bool)0;
	}

IL_003f:
	{
		uint8_t L_8 = ___0_language;
		il2cpp_codegen_runtime_class_init_inline(Localization_t5FE49AA1BA29032C893FFC09EAE5D90EE32E7EEF_il2cpp_TypeInfo_var);
		String_t* L_9;
		L_9 = Localization_GetLocalizationFile_mFA807E3A9F56234F464044182627AC21A756D89D(L_8, NULL);
		bool L_10;
		L_10 = String_IsNullOrEmpty_mEA9E3FB005AC28FE02E69FCF95A7B8456192B478(L_9, NULL);
		if (!L_10)
		{
			goto IL_0063;
		}
	}
	{
		uint8_t L_11 = ___0_language;
		uint8_t L_12 = L_11;
		RuntimeObject* L_13 = Box(eLanguage_t8C1C96826F2E44762BB5944B7474A89D991C6A22_il2cpp_TypeInfo_var, &L_12);
		String_t* L_14;
		L_14 = String_Format_mA8DBB4C2516B9723C5A41E6CB1E2FAF4BBE96DD8(_stringLiteral96E9187B77341E2A75B3FFED0BDD4882BE8A5669, L_13, NULL);
		il2cpp_codegen_runtime_class_init_inline(Debug_t8394C7EEAECA3689C2C9B9DE9C7166D73596276F_il2cpp_TypeInfo_var);
		Debug_LogError_mB00B2B4468EF3CAF041B038D840820FB84C924B2(L_14, NULL);
		return (bool)0;
	}

IL_0063:
	{
		il2cpp_codegen_runtime_class_init_inline(Localization_t5FE49AA1BA29032C893FFC09EAE5D90EE32E7EEF_il2cpp_TypeInfo_var);
		Dictionary_2_t46BDD5FB58E104E19811C6D31E65C42A8C31E22D* L_15 = ((Localization_t5FE49AA1BA29032C893FFC09EAE5D90EE32E7EEF_StaticFields*)il2cpp_codegen_static_fields_for(Localization_t5FE49AA1BA29032C893FFC09EAE5D90EE32E7EEF_il2cpp_TypeInfo_var))->____translations;
		NullCheck(L_15);
		Dictionary_2_Clear_m37FB230D1AE0147D3D1F1D110B257BCAC0C8FC1C(L_15, Dictionary_2_Clear_m37FB230D1AE0147D3D1F1D110B257BCAC0C8FC1C_RuntimeMethod_var);
		return (bool)1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* Localization_Get_m2F5E554580E25E1AF96814D0D45BFA5BD69B76B9 (String_t* ___0_key, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&String_t_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		String_t* L_0 = ___0_key;
		bool L_1;
		L_1 = String_IsNullOrEmpty_mEA9E3FB005AC28FE02E69FCF95A7B8456192B478(L_0, NULL);
		if (!L_1)
		{
			goto IL_000e;
		}
	}
	{
		String_t* L_2 = ((String_t_StaticFields*)il2cpp_codegen_static_fields_for(String_t_il2cpp_TypeInfo_var))->___Empty;
		return L_2;
	}

IL_000e:
	{
		String_t* L_3 = ___0_key;
		return L_3;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR String_t* Localization_GetLocalizationFile_mFA807E3A9F56234F464044182627AC21A756D89D (uint8_t ___0_language, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Localization_t5FE49AA1BA29032C893FFC09EAE5D90EE32E7EEF_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	LanguageSettingU5BU5D_tAE0787335D4680BB400B842FB001822B83CBC442* V_0 = NULL;
	int32_t V_1 = 0;
	LanguageSetting_t9400E93460512E993A221BDF49322D76A775E5EC* V_2 = NULL;
	{
		il2cpp_codegen_runtime_class_init_inline(Localization_t5FE49AA1BA29032C893FFC09EAE5D90EE32E7EEF_il2cpp_TypeInfo_var);
		LocalizationSettings_t46904EA548DB13AFA7FFA7C22FC8C64394367E09* L_0 = ((Localization_t5FE49AA1BA29032C893FFC09EAE5D90EE32E7EEF_StaticFields*)il2cpp_codegen_static_fields_for(Localization_t5FE49AA1BA29032C893FFC09EAE5D90EE32E7EEF_il2cpp_TypeInfo_var))->____localizationSettings;
		NullCheck(L_0);
		LanguageSettingU5BU5D_tAE0787335D4680BB400B842FB001822B83CBC442* L_1 = L_0->___languageSettings;
		V_0 = L_1;
		V_1 = 0;
		goto IL_0027;
	}

IL_000f:
	{
		LanguageSettingU5BU5D_tAE0787335D4680BB400B842FB001822B83CBC442* L_2 = V_0;
		int32_t L_3 = V_1;
		NullCheck(L_2);
		int32_t L_4 = L_3;
		LanguageSetting_t9400E93460512E993A221BDF49322D76A775E5EC* L_5 = (L_2)->GetAt(static_cast<il2cpp_array_size_t>(L_4));
		V_2 = L_5;
		LanguageSetting_t9400E93460512E993A221BDF49322D76A775E5EC* L_6 = V_2;
		NullCheck(L_6);
		uint8_t L_7 = L_6->___Language;
		uint8_t L_8 = ___0_language;
		if ((!(((uint32_t)L_7) == ((uint32_t)L_8))))
		{
			goto IL_0023;
		}
	}
	{
		LanguageSetting_t9400E93460512E993A221BDF49322D76A775E5EC* L_9 = V_2;
		NullCheck(L_9);
		String_t* L_10 = L_9->___LocalizationFile;
		return L_10;
	}

IL_0023:
	{
		int32_t L_11 = V_1;
		V_1 = ((int32_t)il2cpp_codegen_add(L_11, 1));
	}

IL_0027:
	{
		int32_t L_12 = V_1;
		LanguageSettingU5BU5D_tAE0787335D4680BB400B842FB001822B83CBC442* L_13 = V_0;
		NullCheck(L_13);
		if ((((int32_t)L_12) < ((int32_t)((int32_t)(((RuntimeArray*)L_13)->max_length)))))
		{
			goto IL_000f;
		}
	}
	{
		return (String_t*)NULL;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Localization__cctor_m1CB087AC2DF2B4E015CB0067DE05EAA0D335CB47 (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Dictionary_2__ctor_mBDED3672E7C570203308A49157138FF083DAC0EC_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Dictionary_2_t46BDD5FB58E104E19811C6D31E65C42A8C31E22D_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Localization_t5FE49AA1BA29032C893FFC09EAE5D90EE32E7EEF_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&_stringLiteral3764F77195033ACDE8A1EEA686582EECE287BAD8);
		s_Il2CppMethodInitialized = true;
	}
	{
		((Localization_t5FE49AA1BA29032C893FFC09EAE5D90EE32E7EEF_StaticFields*)il2cpp_codegen_static_fields_for(Localization_t5FE49AA1BA29032C893FFC09EAE5D90EE32E7EEF_il2cpp_TypeInfo_var))->___kLocalizationSettingsPath = _stringLiteral3764F77195033ACDE8A1EEA686582EECE287BAD8;
		Il2CppCodeGenWriteBarrier((void**)(&((Localization_t5FE49AA1BA29032C893FFC09EAE5D90EE32E7EEF_StaticFields*)il2cpp_codegen_static_fields_for(Localization_t5FE49AA1BA29032C893FFC09EAE5D90EE32E7EEF_il2cpp_TypeInfo_var))->___kLocalizationSettingsPath), (void*)_stringLiteral3764F77195033ACDE8A1EEA686582EECE287BAD8);
		((Localization_t5FE49AA1BA29032C893FFC09EAE5D90EE32E7EEF_StaticFields*)il2cpp_codegen_static_fields_for(Localization_t5FE49AA1BA29032C893FFC09EAE5D90EE32E7EEF_il2cpp_TypeInfo_var))->____localizationSettings = (LocalizationSettings_t46904EA548DB13AFA7FFA7C22FC8C64394367E09*)NULL;
		Il2CppCodeGenWriteBarrier((void**)(&((Localization_t5FE49AA1BA29032C893FFC09EAE5D90EE32E7EEF_StaticFields*)il2cpp_codegen_static_fields_for(Localization_t5FE49AA1BA29032C893FFC09EAE5D90EE32E7EEF_il2cpp_TypeInfo_var))->____localizationSettings), (void*)(LocalizationSettings_t46904EA548DB13AFA7FFA7C22FC8C64394367E09*)NULL);
		((Localization_t5FE49AA1BA29032C893FFC09EAE5D90EE32E7EEF_StaticFields*)il2cpp_codegen_static_fields_for(Localization_t5FE49AA1BA29032C893FFC09EAE5D90EE32E7EEF_il2cpp_TypeInfo_var))->___U3CCurrentLanguageU3Ek__BackingField = 0;
		Dictionary_2_t46BDD5FB58E104E19811C6D31E65C42A8C31E22D* L_0 = (Dictionary_2_t46BDD5FB58E104E19811C6D31E65C42A8C31E22D*)il2cpp_codegen_object_new(Dictionary_2_t46BDD5FB58E104E19811C6D31E65C42A8C31E22D_il2cpp_TypeInfo_var);
		Dictionary_2__ctor_mBDED3672E7C570203308A49157138FF083DAC0EC(L_0, Dictionary_2__ctor_mBDED3672E7C570203308A49157138FF083DAC0EC_RuntimeMethod_var);
		((Localization_t5FE49AA1BA29032C893FFC09EAE5D90EE32E7EEF_StaticFields*)il2cpp_codegen_static_fields_for(Localization_t5FE49AA1BA29032C893FFC09EAE5D90EE32E7EEF_il2cpp_TypeInfo_var))->____translations = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&((Localization_t5FE49AA1BA29032C893FFC09EAE5D90EE32E7EEF_StaticFields*)il2cpp_codegen_static_fields_for(Localization_t5FE49AA1BA29032C893FFC09EAE5D90EE32E7EEF_il2cpp_TypeInfo_var))->____translations), (void*)L_0);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Translated__ctor_mA07623BBB587277C2DAA9608C0CD291796E747DD (Translated_tA0B59BCB4DE04E161D84723BBE40FC8D5C9261E6* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SerializeHelper__ctor_mF63CEA171EE9F93470186A9CBE2988EDB09563ED (SerializeHelper_tE39A1A0A5C8BD31451E8967CDCF10555C74E1899* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Item__ctor_m7C789D170B24244FA5D664B2047C499CFC22DD67 (Item_t837D18784B40CDF82BD13F22813E1BECF4FB4EAD* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void LocalizationSettings__ctor_m9B324FDA14A8D5D2F35258B97A03C5CF882720CF (LocalizationSettings_t46904EA548DB13AFA7FFA7C22FC8C64394367E09* __this, const RuntimeMethod* method) 
{
	{
		ScriptableObject__ctor_mD037FDB0B487295EA47F79A4DB1BF1846C9087FF(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void LanguageSetting__ctor_m740895421A53000F08F4DCC408A188FD648F6B0E (LanguageSetting_t9400E93460512E993A221BDF49322D76A775E5EC* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TextStyle__ctor_m50D4367015A8C8B98F74822E65B6273177145B0E (TextStyle_t33719BFAB33661D524398E879DE946CEF7557219* __this, const RuntimeMethod* method) 
{
	{
		ScriptableObject__ctor_mD037FDB0B487295EA47F79A4DB1BF1846C9087FF(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void StyleSetting__ctor_m03088E68B6057880949CDF848CB37C01D76C8C6F (StyleSetting_tA7D12294156C603EE91AD3F53512F3762F84F4E8* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif

﻿#include "pch-cpp.hpp"

#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include <limits>


struct InterfaceActionInvoker0
{
	typedef void (*Action)(void*, const RuntimeMethod*);

	static inline void Invoke (Il2CppMethodSlot slot, RuntimeClass* declaringInterface, RuntimeObject* obj)
	{
		const VirtualInvokeData& invokeData = il2cpp_codegen_get_interface_invoke_data(slot, obj, declaringInterface);
		((Action)invokeData.methodPtr)(obj, invokeData.method);
	}
};
template <typename T1>
struct InterfaceActionInvoker1
{
	typedef void (*Action)(void*, T1, const RuntimeMethod*);

	static inline void Invoke (Il2CppMethodSlot slot, RuntimeClass* declaringInterface, RuntimeObject* obj, T1 p1)
	{
		const VirtualInvokeData& invokeData = il2cpp_codegen_get_interface_invoke_data(slot, obj, declaringInterface);
		((Action)invokeData.methodPtr)(obj, p1, invokeData.method);
	}
};
template <typename R>
struct InterfaceFuncInvoker0
{
	typedef R (*Func)(void*, const RuntimeMethod*);

	static inline R Invoke (Il2CppMethodSlot slot, RuntimeClass* declaringInterface, RuntimeObject* obj)
	{
		const VirtualInvokeData& invokeData = il2cpp_codegen_get_interface_invoke_data(slot, obj, declaringInterface);
		return ((Func)invokeData.methodPtr)(obj, invokeData.method);
	}
};
template <typename R, typename T1>
struct InterfaceFuncInvoker1
{
	typedef R (*Func)(void*, T1, const RuntimeMethod*);

	static inline R Invoke (Il2CppMethodSlot slot, RuntimeClass* declaringInterface, RuntimeObject* obj, T1 p1)
	{
		const VirtualInvokeData& invokeData = il2cpp_codegen_get_interface_invoke_data(slot, obj, declaringInterface);
		return ((Func)invokeData.methodPtr)(obj, p1, invokeData.method);
	}
};

struct Action_1_tE8693FF0E67CDBA52BAFB211BFF1844D076ABAFB;
struct Func_1_t2BE7F58348C9CC544A8973B3A9E55541DE43C457;
struct List_1_t34E53346ADC531C7809398BA078DED1E182FEF4C;
struct List_1_t930598996D1A93CCE7F51C4E2B0CC9645E9B8113;
struct List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D;
struct Queue_1_t1753017E9AC02C107096B1F64F9CA7D8066B3F68;
struct Queue_1_tE9EF546915795972C3BFD68FBB8FA859D3BAF3B5;
struct ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031;
struct DelegateU5BU5D_tC5AB7E8F745616680F337909D3A8E6C722CDF771;
struct ISchedulerU5BU5D_t02ECB933EF0AE391C135FCA7412369CBAA21BFD7;
struct ITaskU5BU5D_tCF6DC5C27E2FC50187648CEE5AD0D81D61642A05;
struct IntPtrU5BU5D_tFD177F8C806A6921AD7150264CCC62FA00CAD832;
struct ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918;
struct StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF;
struct Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07;
struct ArgumentOutOfRangeException_tEA2822DAF62B10EEED00E0E3A341D4BAF78CF85F;
struct AsyncOperation_tD2789250E4B098DEDA92B366A577E500A92D2D3C;
struct AsyncOperationTask_t99808195DAEE9EE75DBDD7F875A14E606A5EA053;
struct CombinedScheduler_tC9BC117BCE01E5068E0E5F7D76183ED3A2DFA0A8;
struct DelegateData_t9B286B493293CD2D23A5B2B5EF0E5B1324C2B77E;
struct IDictionary_t6D03155AF1FA9083817AA5B6AD7DEEACC26AB220;
struct IScheduler_tBA89D3CBD4600EE0421A94BCE19EC693B9E6678A;
struct ITask_tCF9AE3E9C450764F84E261819B70401C27BDE6C6;
struct MethodInfo_t;
struct ParallelScheduler_t8379A00B9D56775018796CE4487866984ED97D4E;
struct SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6;
struct SequentialScheduler_tA81A2FC813C06F461FF491C537503D1A0700832B;
struct String_t;
struct Task_t025E98A50FB47A9B2723A0B6676C457151F0280B;
struct TaskDelay_tF2D712504417D431B06EB95C4F323F8FE5BFAA4E;
struct TaskWait_t3EE9538F13FC35386954C2A0357508C54402C70D;
struct UnitySourceGeneratedAssemblyMonoScriptTypes_v1_t0F54272D0D2FD43327C5190A6EC63A00F34201D8;
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915;
struct U3CU3Ec__DisplayClass0_0_tD5AC230A70D2F0377E27947EF9A53F2B08B23CC3;
struct U3CU3Ec__DisplayClass2_0_t5D1712945E8ACD90F26C4380E606A54C7BC4DFF5;
struct U3CU3Ec__DisplayClass3_0_t53E6CDB6BAB3FEFE8401EE5D2A795DABBE094799;
struct U3CU3Ec__DisplayClass4_0_t48EF7895F2E37829242EF3221693B9A2352D13A9;

IL2CPP_EXTERN_C RuntimeClass* Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* ArgumentOutOfRangeException_tEA2822DAF62B10EEED00E0E3A341D4BAF78CF85F_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* AsyncOperationTask_t99808195DAEE9EE75DBDD7F875A14E606A5EA053_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* IScheduler_tBA89D3CBD4600EE0421A94BCE19EC693B9E6678A_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* ITask_tCF9AE3E9C450764F84E261819B70401C27BDE6C6_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* List_1_t34E53346ADC531C7809398BA078DED1E182FEF4C_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* List_1_t930598996D1A93CCE7F51C4E2B0CC9645E9B8113_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Queue_1_t1753017E9AC02C107096B1F64F9CA7D8066B3F68_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* TaskDelay_tF2D712504417D431B06EB95C4F323F8FE5BFAA4E_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* TaskPool_1_t0374BDF8E1E64F2188AD274557FD9C475E2B30A7_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* TaskPool_1_t8208ED8332FAD542289791A213F005A015F55636_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* TaskPool_1_tE4C032936710E899A0C76A261A479D82A424BBD0_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* TaskPool_1_tEA148E62BF07EA6D24F52D9850E5B079EE766B95_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* TaskWait_t3EE9538F13FC35386954C2A0357508C54402C70D_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Task_t025E98A50FB47A9B2723A0B6676C457151F0280B_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* U3CU3Ec__DisplayClass0_0_tD5AC230A70D2F0377E27947EF9A53F2B08B23CC3_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* U3CU3Ec__DisplayClass2_0_t5D1712945E8ACD90F26C4380E606A54C7BC4DFF5_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* U3CU3Ec__DisplayClass3_0_t53E6CDB6BAB3FEFE8401EE5D2A795DABBE094799_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* U3CU3Ec__DisplayClass4_0_t48EF7895F2E37829242EF3221693B9A2352D13A9_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeField* U3CPrivateImplementationDetailsU3E_tF3168826DB1B05F8B96B03AE2382C5A64C02B37B____3EB185591374ED022C1830963ECCD1350E6B55F888B8DD45F7E4286900524F59_FieldInfo_var;
IL2CPP_EXTERN_C RuntimeField* U3CPrivateImplementationDetailsU3E_tF3168826DB1B05F8B96B03AE2382C5A64C02B37B____7F5C03A7FFE1B1703D00023192818FB641AAF20805BA6DB4579473DDA4DE5BC4_FieldInfo_var;
IL2CPP_EXTERN_C String_t* _stringLiteralAC03F829E9BDCFEC7BE22227379C137652703B65;
IL2CPP_EXTERN_C String_t* _stringLiteralBC395BD5F69A0C8E0C05428831D01B1729D0C08C;
IL2CPP_EXTERN_C const RuntimeMethod* Enumerator_Dispose_m03E0C0F3E267D499A2042C972641859F55CA5FCB_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Enumerator_Dispose_m909DFF743AF500AD152AFE317C71F7AFC9EDF6A5_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Enumerator_MoveNext_m4D2FA9D284D9CC942BDDBA2136D35BA4E3C56912_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Enumerator_MoveNext_mD5C0DAF6826810806AFFCB26CAD48DE90D30FA0B_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Enumerator_get_Current_m3FC1302CC110A99701ADC815FE3DF672DD6C3C69_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Enumerator_get_Current_m9C237223D7642D034FE235121A5063E40C0CD0D0_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Factory_Return_TisAsyncOperationTask_t99808195DAEE9EE75DBDD7F875A14E606A5EA053_m9CEF567EBACA29E7A3375696419A011877493F30_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Factory_Return_TisTaskDelay_tF2D712504417D431B06EB95C4F323F8FE5BFAA4E_m5845A8D683D6C2BF02E9CB8CA5DFD93BBBE36031_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Factory_Return_TisTaskWait_t3EE9538F13FC35386954C2A0357508C54402C70D_mB42E50EDBE4F1D50308DF52F1AEA1467AB4D82C0_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Factory_Return_TisTask_t025E98A50FB47A9B2723A0B6676C457151F0280B_m566914B9DFC3E2B77C4CC79A354D4F56C08DE404_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1_Add_m9652946599533509ECAE57F6A3AD6B4AC39FFA8C_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1_Add_mD87DFAD1A3DDEB06156BFFE81A9C7F9D6D3D74C1_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1_Clear_m75F6D7D2191A3B21E9EFCAAAB61753E03B3CF78C_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1_GetEnumerator_m4B2195DBF47EFB502FADE2E39C377C6E068377CA_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1__ctor_m7B7D12AA36992ED80132FBEEE676F8C267664A97_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1__ctor_mFA461A6D907A1232C974461C054E0F3E5D570D95_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1_get_Count_m1CA38F17002C61754AC244EF759BA231250CA54A_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1_get_Count_mD2E9EFE20B0EEC3C6831330B6462B95B5218982A_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1_get_Item_m32546B9C12BB96B1EB358899E1EB3B5BD6A33A49_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1_get_Item_m5A3F4B3F4312888A93224C5CF7BEFCB5DE5342CB_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Queue_1_Clear_m6110B566D9B8D376BFCFF72D3FE9FF88B118E026_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Queue_1_Dequeue_mB8DA6E612E1335E5EBBBD1E30FB265B20BDC7220_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Queue_1_Enqueue_mE30AFD66E5DB5F92244805EF4E66CB554B2FEF95_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Queue_1_GetEnumerator_m9F588D95754FFCD6F96CEC48D8970EB912112FFE_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Queue_1__ctor_mFC3EB5A37E7A565A7833603DC722ACD9C4269AC5_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Queue_1_get_Count_m2E7A67F6083221FC900A76A6AE2DD99EAB10CEB5_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* SequentialScheduler_StartFrom_m5C13620E5B1E72D5DD4C9B989FC69F908EA841D5_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* TaskPool_1_Get_m3456F6C640DDAD96211002A3CC06FD33EA4F13CC_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* TaskPool_1_Get_m5DF572F6F84F4F83077EE74EE6FBF9B0E43583C7_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* TaskPool_1_Get_m7FDC447B1E1E903778115C4305CC76023916A8E2_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* TaskPool_1_Get_mDACEBA70983B71A33FE287B1F4B1A1B009291093_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CU3Ec__DisplayClass0_0_U3CFromActionU3Eb__0_mDA12C1B15CBD69105213328290E0D9AB97B327F0_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CU3Ec__DisplayClass2_0_U3CFromAsyncOperationU3Eb__0_m8DEC88A191E826C2F11FDD5944C2097A78506492_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CU3Ec__DisplayClass3_0_U3CFromDelayU3Eb__0_m7F78507D1058BFC830CB6D143BA5EA44A728D3A5_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* U3CU3Ec__DisplayClass4_0_U3CFromWaitU3Eb__0_mD486A34388585FFD0DF8E01432FF2A6C0810E284_RuntimeMethod_var;
struct Delegate_t_marshaled_com;
struct Delegate_t_marshaled_pinvoke;
struct Exception_t_marshaled_com;
struct Exception_t_marshaled_pinvoke;

struct ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031;
struct ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918;

IL2CPP_EXTERN_C_BEGIN
IL2CPP_EXTERN_C_END

#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
struct U3CModuleU3E_t02C6B4FDCA86ECBFA60E697CAE632F4A373897A3 
{
};
struct List_1_t34E53346ADC531C7809398BA078DED1E182FEF4C  : public RuntimeObject
{
	ISchedulerU5BU5D_t02ECB933EF0AE391C135FCA7412369CBAA21BFD7* ____items;
	int32_t ____size;
	int32_t ____version;
	RuntimeObject* ____syncRoot;
};
struct List_1_t930598996D1A93CCE7F51C4E2B0CC9645E9B8113  : public RuntimeObject
{
	ITaskU5BU5D_tCF6DC5C27E2FC50187648CEE5AD0D81D61642A05* ____items;
	int32_t ____size;
	int32_t ____version;
	RuntimeObject* ____syncRoot;
};
struct List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D  : public RuntimeObject
{
	ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* ____items;
	int32_t ____size;
	int32_t ____version;
	RuntimeObject* ____syncRoot;
};
struct Queue_1_t1753017E9AC02C107096B1F64F9CA7D8066B3F68  : public RuntimeObject
{
	ITaskU5BU5D_tCF6DC5C27E2FC50187648CEE5AD0D81D61642A05* ____array;
	int32_t ____head;
	int32_t ____tail;
	int32_t ____size;
	int32_t ____version;
	RuntimeObject* ____syncRoot;
};
struct Queue_1_tE9EF546915795972C3BFD68FBB8FA859D3BAF3B5  : public RuntimeObject
{
	ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* ____array;
	int32_t ____head;
	int32_t ____tail;
	int32_t ____size;
	int32_t ____version;
	RuntimeObject* ____syncRoot;
};
struct U3CPrivateImplementationDetailsU3E_tF3168826DB1B05F8B96B03AE2382C5A64C02B37B  : public RuntimeObject
{
};
struct AsyncOperationTask_t99808195DAEE9EE75DBDD7F875A14E606A5EA053  : public RuntimeObject
{
	AsyncOperation_tD2789250E4B098DEDA92B366A577E500A92D2D3C* ___op;
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ____postResetAction;
};
struct CombinedScheduler_tC9BC117BCE01E5068E0E5F7D76183ED3A2DFA0A8  : public RuntimeObject
{
	List_1_t34E53346ADC531C7809398BA078DED1E182FEF4C* ___U3C_SchedulersU3Ek__BackingField;
};
struct Factory_t0A7E4A3F0505FA615A99386B6B559A286D5389E7  : public RuntimeObject
{
};
struct ParallelScheduler_t8379A00B9D56775018796CE4487866984ED97D4E  : public RuntimeObject
{
	List_1_t930598996D1A93CCE7F51C4E2B0CC9645E9B8113* ___U3C_TasksU3Ek__BackingField;
};
struct SequentialScheduler_tA81A2FC813C06F461FF491C537503D1A0700832B  : public RuntimeObject
{
	RuntimeObject* ____RunningTask;
	Queue_1_t1753017E9AC02C107096B1F64F9CA7D8066B3F68* ___U3C_TasksU3Ek__BackingField;
	float ____CurProgress;
	float ____TotalProgress;
	float ____SubTotal;
};
struct String_t  : public RuntimeObject
{
	int32_t ____stringLength;
	Il2CppChar ____firstChar;
};
struct Task_t025E98A50FB47A9B2723A0B6676C457151F0280B  : public RuntimeObject
{
	bool ___U3CIsFinishedU3Ek__BackingField;
	float ___U3CProgressU3Ek__BackingField;
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ____action;
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ____postResetAction;
};
struct TaskDelay_tF2D712504417D431B06EB95C4F323F8FE5BFAA4E  : public RuntimeObject
{
	bool ___U3CIsFinishedU3Ek__BackingField;
	float ___U3CProgressU3Ek__BackingField;
	float ____delay;
	float ____timer;
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ____postResetAction;
};
struct TaskWait_t3EE9538F13FC35386954C2A0357508C54402C70D  : public RuntimeObject
{
	bool ___U3CIsFinishedU3Ek__BackingField;
	Func_1_t2BE7F58348C9CC544A8973B3A9E55541DE43C457* ____func;
	float ____curTimeInSeconds;
	float ____maxTimeInSeconds;
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ____postResetAction;
};
struct UnitySourceGeneratedAssemblyMonoScriptTypes_v1_t0F54272D0D2FD43327C5190A6EC63A00F34201D8  : public RuntimeObject
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F  : public RuntimeObject
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_pinvoke
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_com
{
};
struct YieldInstruction_tFCE35FD0907950EFEE9BC2890AC664E41C53728D  : public RuntimeObject
{
};
struct YieldInstruction_tFCE35FD0907950EFEE9BC2890AC664E41C53728D_marshaled_pinvoke
{
};
struct YieldInstruction_tFCE35FD0907950EFEE9BC2890AC664E41C53728D_marshaled_com
{
};
struct U3CU3Ec__DisplayClass0_0_tD5AC230A70D2F0377E27947EF9A53F2B08B23CC3  : public RuntimeObject
{
	Task_t025E98A50FB47A9B2723A0B6676C457151F0280B* ___task;
};
struct U3CU3Ec__DisplayClass2_0_t5D1712945E8ACD90F26C4380E606A54C7BC4DFF5  : public RuntimeObject
{
	AsyncOperationTask_t99808195DAEE9EE75DBDD7F875A14E606A5EA053* ___task;
};
struct U3CU3Ec__DisplayClass3_0_t53E6CDB6BAB3FEFE8401EE5D2A795DABBE094799  : public RuntimeObject
{
	TaskDelay_tF2D712504417D431B06EB95C4F323F8FE5BFAA4E* ___task;
};
struct U3CU3Ec__DisplayClass4_0_t48EF7895F2E37829242EF3221693B9A2352D13A9  : public RuntimeObject
{
	TaskWait_t3EE9538F13FC35386954C2A0357508C54402C70D* ___task;
};
struct Enumerator_t2558BB0501E98A606E95BEA700B600D43C4995EA 
{
	List_1_t930598996D1A93CCE7F51C4E2B0CC9645E9B8113* ____list;
	int32_t ____index;
	int32_t ____version;
	RuntimeObject* ____current;
};
struct Enumerator_t8A456E1C05D8FE1CF2370E2519618D4ACF57D6F3 
{
	Queue_1_t1753017E9AC02C107096B1F64F9CA7D8066B3F68* ____q;
	int32_t ____version;
	int32_t ____index;
	RuntimeObject* ____currentElement;
};
struct Enumerator_t9473BAB568A27E2339D48C1F91319E0F6D244D7A 
{
	List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D* ____list;
	int32_t ____index;
	int32_t ____version;
	RuntimeObject* ____current;
};
struct Enumerator_t30E3290EE12437374037B3CF0EE4D614F96D030A 
{
	Queue_1_tE9EF546915795972C3BFD68FBB8FA859D3BAF3B5* ____q;
	int32_t ____version;
	int32_t ____index;
	RuntimeObject* ____currentElement;
};
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22 
{
	bool ___m_value;
};
struct Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3 
{
	uint8_t ___m_value;
};
struct Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C 
{
	int32_t ___m_value;
};
struct IntPtr_t 
{
	void* ___m_value;
};
struct Single_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C 
{
	float ___m_value;
};
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915 
{
	union
	{
		struct
		{
		};
		uint8_t Void_t4861ACF8F4594C3437BB48B6E56783494B843915__padding[1];
	};
};
#pragma pack(push, tp, 1)
struct __StaticArrayInitTypeSizeU3D234_t721F015C0E629B84F889C2408E331519C3FD791D 
{
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D234_t721F015C0E629B84F889C2408E331519C3FD791D__padding[234];
	};
};
#pragma pack(pop, tp)
#pragma pack(push, tp, 1)
struct __StaticArrayInitTypeSizeU3D381_t89A92CD0989F2FFB5150EFA03EB34186D0A99B09 
{
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D381_t89A92CD0989F2FFB5150EFA03EB34186D0A99B09__padding[381];
	};
};
#pragma pack(pop, tp)
struct MonoScriptData_tC477D07AD440396933A37A440EDAB6468C2FDA0F 
{
	ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* ___FilePathsData;
	ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* ___TypesData;
	int32_t ___TotalTypes;
	int32_t ___TotalFiles;
	bool ___IsEditorOnly;
};
struct MonoScriptData_tC477D07AD440396933A37A440EDAB6468C2FDA0F_marshaled_pinvoke
{
	Il2CppSafeArray* ___FilePathsData;
	Il2CppSafeArray* ___TypesData;
	int32_t ___TotalTypes;
	int32_t ___TotalFiles;
	int32_t ___IsEditorOnly;
};
struct MonoScriptData_tC477D07AD440396933A37A440EDAB6468C2FDA0F_marshaled_com
{
	Il2CppSafeArray* ___FilePathsData;
	Il2CppSafeArray* ___TypesData;
	int32_t ___TotalTypes;
	int32_t ___TotalFiles;
	int32_t ___IsEditorOnly;
};
struct AsyncOperation_tD2789250E4B098DEDA92B366A577E500A92D2D3C  : public YieldInstruction_tFCE35FD0907950EFEE9BC2890AC664E41C53728D
{
	intptr_t ___m_Ptr;
	Action_1_tE8693FF0E67CDBA52BAFB211BFF1844D076ABAFB* ___m_completeCallback;
};
struct AsyncOperation_tD2789250E4B098DEDA92B366A577E500A92D2D3C_marshaled_pinvoke : public YieldInstruction_tFCE35FD0907950EFEE9BC2890AC664E41C53728D_marshaled_pinvoke
{
	intptr_t ___m_Ptr;
	Il2CppMethodPointer ___m_completeCallback;
};
struct AsyncOperation_tD2789250E4B098DEDA92B366A577E500A92D2D3C_marshaled_com : public YieldInstruction_tFCE35FD0907950EFEE9BC2890AC664E41C53728D_marshaled_com
{
	intptr_t ___m_Ptr;
	Il2CppMethodPointer ___m_completeCallback;
};
struct Delegate_t  : public RuntimeObject
{
	intptr_t ___method_ptr;
	intptr_t ___invoke_impl;
	RuntimeObject* ___m_target;
	intptr_t ___method;
	intptr_t ___delegate_trampoline;
	intptr_t ___extra_arg;
	intptr_t ___method_code;
	intptr_t ___interp_method;
	intptr_t ___interp_invoke_impl;
	MethodInfo_t* ___method_info;
	MethodInfo_t* ___original_method_info;
	DelegateData_t9B286B493293CD2D23A5B2B5EF0E5B1324C2B77E* ___data;
	bool ___method_is_virtual;
};
struct Delegate_t_marshaled_pinvoke
{
	intptr_t ___method_ptr;
	intptr_t ___invoke_impl;
	Il2CppIUnknown* ___m_target;
	intptr_t ___method;
	intptr_t ___delegate_trampoline;
	intptr_t ___extra_arg;
	intptr_t ___method_code;
	intptr_t ___interp_method;
	intptr_t ___interp_invoke_impl;
	MethodInfo_t* ___method_info;
	MethodInfo_t* ___original_method_info;
	DelegateData_t9B286B493293CD2D23A5B2B5EF0E5B1324C2B77E* ___data;
	int32_t ___method_is_virtual;
};
struct Delegate_t_marshaled_com
{
	intptr_t ___method_ptr;
	intptr_t ___invoke_impl;
	Il2CppIUnknown* ___m_target;
	intptr_t ___method;
	intptr_t ___delegate_trampoline;
	intptr_t ___extra_arg;
	intptr_t ___method_code;
	intptr_t ___interp_method;
	intptr_t ___interp_invoke_impl;
	MethodInfo_t* ___method_info;
	MethodInfo_t* ___original_method_info;
	DelegateData_t9B286B493293CD2D23A5B2B5EF0E5B1324C2B77E* ___data;
	int32_t ___method_is_virtual;
};
struct Exception_t  : public RuntimeObject
{
	String_t* ____className;
	String_t* ____message;
	RuntimeObject* ____data;
	Exception_t* ____innerException;
	String_t* ____helpURL;
	RuntimeObject* ____stackTrace;
	String_t* ____stackTraceString;
	String_t* ____remoteStackTraceString;
	int32_t ____remoteStackIndex;
	RuntimeObject* ____dynamicMethods;
	int32_t ____HResult;
	String_t* ____source;
	SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6* ____safeSerializationManager;
	StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF* ___captured_traces;
	IntPtrU5BU5D_tFD177F8C806A6921AD7150264CCC62FA00CAD832* ___native_trace_ips;
	int32_t ___caught_in_unmanaged;
};
struct Exception_t_marshaled_pinvoke
{
	char* ____className;
	char* ____message;
	RuntimeObject* ____data;
	Exception_t_marshaled_pinvoke* ____innerException;
	char* ____helpURL;
	Il2CppIUnknown* ____stackTrace;
	char* ____stackTraceString;
	char* ____remoteStackTraceString;
	int32_t ____remoteStackIndex;
	Il2CppIUnknown* ____dynamicMethods;
	int32_t ____HResult;
	char* ____source;
	SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6* ____safeSerializationManager;
	StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF* ___captured_traces;
	Il2CppSafeArray* ___native_trace_ips;
	int32_t ___caught_in_unmanaged;
};
struct Exception_t_marshaled_com
{
	Il2CppChar* ____className;
	Il2CppChar* ____message;
	RuntimeObject* ____data;
	Exception_t_marshaled_com* ____innerException;
	Il2CppChar* ____helpURL;
	Il2CppIUnknown* ____stackTrace;
	Il2CppChar* ____stackTraceString;
	Il2CppChar* ____remoteStackTraceString;
	int32_t ____remoteStackIndex;
	Il2CppIUnknown* ____dynamicMethods;
	int32_t ____HResult;
	Il2CppChar* ____source;
	SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6* ____safeSerializationManager;
	StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF* ___captured_traces;
	Il2CppSafeArray* ___native_trace_ips;
	int32_t ___caught_in_unmanaged;
};
struct RuntimeFieldHandle_t6E4C45B6D2EA12FC99185805A7E77527899B25C5 
{
	intptr_t ___value;
};
struct MulticastDelegate_t  : public Delegate_t
{
	DelegateU5BU5D_tC5AB7E8F745616680F337909D3A8E6C722CDF771* ___delegates;
};
struct MulticastDelegate_t_marshaled_pinvoke : public Delegate_t_marshaled_pinvoke
{
	Delegate_t_marshaled_pinvoke** ___delegates;
};
struct MulticastDelegate_t_marshaled_com : public Delegate_t_marshaled_com
{
	Delegate_t_marshaled_com** ___delegates;
};
struct SystemException_tCC48D868298F4C0705279823E34B00F4FBDB7295  : public Exception_t
{
};
struct Func_1_t2BE7F58348C9CC544A8973B3A9E55541DE43C457  : public MulticastDelegate_t
{
};
struct Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07  : public MulticastDelegate_t
{
};
struct ArgumentException_tAD90411542A20A9C72D5CDA3A84181D8B947A263  : public SystemException_tCC48D868298F4C0705279823E34B00F4FBDB7295
{
	String_t* ____paramName;
};
struct ArgumentOutOfRangeException_tEA2822DAF62B10EEED00E0E3A341D4BAF78CF85F  : public ArgumentException_tAD90411542A20A9C72D5CDA3A84181D8B947A263
{
	RuntimeObject* ____actualValue;
};
struct List_1_t34E53346ADC531C7809398BA078DED1E182FEF4C_StaticFields
{
	ISchedulerU5BU5D_t02ECB933EF0AE391C135FCA7412369CBAA21BFD7* ___s_emptyArray;
};
struct List_1_t930598996D1A93CCE7F51C4E2B0CC9645E9B8113_StaticFields
{
	ITaskU5BU5D_tCF6DC5C27E2FC50187648CEE5AD0D81D61642A05* ___s_emptyArray;
};
struct List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D_StaticFields
{
	ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* ___s_emptyArray;
};
struct U3CPrivateImplementationDetailsU3E_tF3168826DB1B05F8B96B03AE2382C5A64C02B37B_StaticFields
{
	__StaticArrayInitTypeSizeU3D234_t721F015C0E629B84F889C2408E331519C3FD791D ___3EB185591374ED022C1830963ECCD1350E6B55F888B8DD45F7E4286900524F59;
	__StaticArrayInitTypeSizeU3D381_t89A92CD0989F2FFB5150EFA03EB34186D0A99B09 ___7F5C03A7FFE1B1703D00023192818FB641AAF20805BA6DB4579473DDA4DE5BC4;
};
struct String_t_StaticFields
{
	String_t* ___Empty;
};
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_StaticFields
{
	String_t* ___TrueString;
	String_t* ___FalseString;
};
struct IntPtr_t_StaticFields
{
	intptr_t ___Zero;
};
#ifdef __clang__
#pragma clang diagnostic pop
#endif
struct ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031  : public RuntimeArray
{
	ALIGN_FIELD (8) uint8_t m_Items[1];

	inline uint8_t GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline uint8_t* GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, uint8_t value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
	}
	inline uint8_t GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline uint8_t* GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, uint8_t value)
	{
		m_Items[index] = value;
	}
};
struct ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918  : public RuntimeArray
{
	ALIGN_FIELD (8) RuntimeObject* m_Items[1];

	inline RuntimeObject* GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline RuntimeObject** GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, RuntimeObject* value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
	inline RuntimeObject* GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline RuntimeObject** GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, RuntimeObject* value)
	{
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
};


IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* TaskPool_1_Get_m8564A564738C6D5454F02F8F540221880B57B07F_gshared (const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Factory_Return_TisRuntimeObject_mE5BBA6F699F07DC9ADC22AA2DD428ABC09BB6EFB_gshared (RuntimeObject* ___0_task, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t Queue_1_get_Count_m1768ADA9855B7CDA14C9C42E098A287F1A39C3A2_gshared_inline (Queue_1_tE9EF546915795972C3BFD68FBB8FA859D3BAF3B5* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Enumerator_t30E3290EE12437374037B3CF0EE4D614F96D030A Queue_1_GetEnumerator_mBF0033C4BCEA408644D24F0B28A81F9145FB97C9_gshared (Queue_1_tE9EF546915795972C3BFD68FBB8FA859D3BAF3B5* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Enumerator_Dispose_m680926A5EFC7099ECBCE9DEF68F8DED03C103955_gshared (Enumerator_t30E3290EE12437374037B3CF0EE4D614F96D030A* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* Enumerator_get_Current_m5F2338F4C35E898DB7231D7E30F30155498FA9D7_gshared (Enumerator_t30E3290EE12437374037B3CF0EE4D614F96D030A* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Enumerator_MoveNext_mABD92CBE05B031E50E316375DDC8B2BDAD3F6F84_gshared (Enumerator_t30E3290EE12437374037B3CF0EE4D614F96D030A* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Queue_1_Clear_m70861E24CF43ECFF3BC5C2AD4EE55963D54D8711_gshared (Queue_1_tE9EF546915795972C3BFD68FBB8FA859D3BAF3B5* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Queue_1_Enqueue_m5CB8CF3906F1289F92036F0973EC5BE3450402EF_gshared (Queue_1_tE9EF546915795972C3BFD68FBB8FA859D3BAF3B5* __this, RuntimeObject* ___0_item, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* Queue_1_Dequeue_m86B243DF9EC238316EC3D27DF3E0AB8DB0987E84_gshared (Queue_1_tE9EF546915795972C3BFD68FBB8FA859D3BAF3B5* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Queue_1__ctor_m6E2A5A8173E0CC524496D5155C737DF8FD10D0EB_gshared (Queue_1_tE9EF546915795972C3BFD68FBB8FA859D3BAF3B5* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* List_1_get_Item_m33561245D64798C2AB07584C0EC4F240E4839A38_gshared (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D* __this, int32_t ___0_index, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t List_1_get_Count_m4407E4C389F22B8CEC282C15D56516658746C383_gshared_inline (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Enumerator_t9473BAB568A27E2339D48C1F91319E0F6D244D7A List_1_GetEnumerator_mD8294A7FA2BEB1929487127D476F8EC1CDC23BFC_gshared (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Enumerator_Dispose_mD9DC3E3C3697830A4823047AB29A77DBBB5ED419_gshared (Enumerator_t9473BAB568A27E2339D48C1F91319E0F6D244D7A* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR RuntimeObject* Enumerator_get_Current_m6330F15D18EE4F547C05DF9BF83C5EB710376027_gshared_inline (Enumerator_t9473BAB568A27E2339D48C1F91319E0F6D244D7A* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Enumerator_MoveNext_mE921CC8F29FBBDE7CC3209A0ED0D921D58D00BCB_gshared (Enumerator_t9473BAB568A27E2339D48C1F91319E0F6D244D7A* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void List_1_Clear_m16C1F2C61FED5955F10EB36BC1CB2DF34B128994_gshared_inline (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void List_1_Add_mEBCF994CC3814631017F46A387B1A192ED6C85C7_gshared_inline (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D* __this, RuntimeObject* ___0_item, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void List_1__ctor_m7F078BB342729BDF11327FD89D7872265328F690_gshared (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool Func_1_Invoke_mBB7F37C468451AF57FAF31635C544D6B8C4373B2_gshared_inline (Func_1_t2BE7F58348C9CC544A8973B3A9E55541DE43C457* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_NO_INLINE IL2CPP_METHOD_ATTR void List_1_AddWithResize_m79A9BF770BEF9C06BE40D5401E55E375F2726CC4_gshared (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D* __this, RuntimeObject* ___0_item, const RuntimeMethod* method) ;

IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RuntimeHelpers_InitializeArray_m751372AA3F24FBF6DA9B9D687CBFA2DE436CAB9B (RuntimeArray* ___0_array, RuntimeFieldHandle_t6E4C45B6D2EA12FC99185805A7E77527899B25C5 ___1_fldHandle, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2 (RuntimeObject* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__DisplayClass0_0__ctor_mC5A9A08671EF55D835B9F15993BF4FDE59F45646 (U3CU3Ec__DisplayClass0_0_tD5AC230A70D2F0377E27947EF9A53F2B08B23CC3* __this, const RuntimeMethod* method) ;
inline Task_t025E98A50FB47A9B2723A0B6676C457151F0280B* TaskPool_1_Get_m3456F6C640DDAD96211002A3CC06FD33EA4F13CC (const RuntimeMethod* method)
{
	return ((  Task_t025E98A50FB47A9B2723A0B6676C457151F0280B* (*) (const RuntimeMethod*))TaskPool_1_Get_m8564A564738C6D5454F02F8F540221880B57B07F_gshared)(method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Task__ctor_m75F016C7FAF44A76CF1FAD52D6761E1EA3228063 (Task_t025E98A50FB47A9B2723A0B6676C457151F0280B* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Action__ctor_mBDC7B0B4A3F583B64C2896F01BDED360772F67DC (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Task_Initialize_m1245EA3003E520F57ED20FBF2BD667C4D49FEF71 (Task_t025E98A50FB47A9B2723A0B6676C457151F0280B* __this, Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___0_action, Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___1_postResetAct, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__DisplayClass2_0__ctor_mFD5FCD46A9516125DB6711707C1B3B4BE2F097AB (U3CU3Ec__DisplayClass2_0_t5D1712945E8ACD90F26C4380E606A54C7BC4DFF5* __this, const RuntimeMethod* method) ;
inline AsyncOperationTask_t99808195DAEE9EE75DBDD7F875A14E606A5EA053* TaskPool_1_Get_m7FDC447B1E1E903778115C4305CC76023916A8E2 (const RuntimeMethod* method)
{
	return ((  AsyncOperationTask_t99808195DAEE9EE75DBDD7F875A14E606A5EA053* (*) (const RuntimeMethod*))TaskPool_1_Get_m8564A564738C6D5454F02F8F540221880B57B07F_gshared)(method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AsyncOperationTask__ctor_mF0D75B1EB6AA36F78EEF1363BE966F16039ABD6E (AsyncOperationTask_t99808195DAEE9EE75DBDD7F875A14E606A5EA053* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AsyncOperationTask_Initialize_mAC8C2E376EB61E3551B7904E6ECEA40550FF3532 (AsyncOperationTask_t99808195DAEE9EE75DBDD7F875A14E606A5EA053* __this, AsyncOperation_tD2789250E4B098DEDA92B366A577E500A92D2D3C* ___0_operation, Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___1__postResetAct, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__DisplayClass3_0__ctor_mF934959DFE9764BAFDDCC0C5B101886006A0FD2E (U3CU3Ec__DisplayClass3_0_t53E6CDB6BAB3FEFE8401EE5D2A795DABBE094799* __this, const RuntimeMethod* method) ;
inline TaskDelay_tF2D712504417D431B06EB95C4F323F8FE5BFAA4E* TaskPool_1_Get_m5DF572F6F84F4F83077EE74EE6FBF9B0E43583C7 (const RuntimeMethod* method)
{
	return ((  TaskDelay_tF2D712504417D431B06EB95C4F323F8FE5BFAA4E* (*) (const RuntimeMethod*))TaskPool_1_Get_m8564A564738C6D5454F02F8F540221880B57B07F_gshared)(method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TaskDelay__ctor_mB022423A8FB6D9B52DA87E7698E79D8780FAE17E (TaskDelay_tF2D712504417D431B06EB95C4F323F8FE5BFAA4E* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TaskDelay_Initialize_m21AD415596403721C63C81ECF69A5A1886F74609 (TaskDelay_tF2D712504417D431B06EB95C4F323F8FE5BFAA4E* __this, float ___0_delay, Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___1__postResetAct, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__DisplayClass4_0__ctor_m64462E4856AF8D55FCFBD46364F682A329BB1C67 (U3CU3Ec__DisplayClass4_0_t48EF7895F2E37829242EF3221693B9A2352D13A9* __this, const RuntimeMethod* method) ;
inline TaskWait_t3EE9538F13FC35386954C2A0357508C54402C70D* TaskPool_1_Get_mDACEBA70983B71A33FE287B1F4B1A1B009291093 (const RuntimeMethod* method)
{
	return ((  TaskWait_t3EE9538F13FC35386954C2A0357508C54402C70D* (*) (const RuntimeMethod*))TaskPool_1_Get_m8564A564738C6D5454F02F8F540221880B57B07F_gshared)(method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TaskWait__ctor_mBC9F0F91A9528C7FC4916B17D72BD37CED4DE9FB (TaskWait_t3EE9538F13FC35386954C2A0357508C54402C70D* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TaskWait_Initialize_m57E23EB5BE81C2573B53470173A7268E34EF9E15 (TaskWait_t3EE9538F13FC35386954C2A0357508C54402C70D* __this, Func_1_t2BE7F58348C9CC544A8973B3A9E55541DE43C457* ___0_func, float ___1_maxTimeInSeconds, Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___2__postResetAct, const RuntimeMethod* method) ;
inline void Factory_Return_TisTask_t025E98A50FB47A9B2723A0B6676C457151F0280B_m566914B9DFC3E2B77C4CC79A354D4F56C08DE404 (Task_t025E98A50FB47A9B2723A0B6676C457151F0280B* ___0_task, const RuntimeMethod* method)
{
	((  void (*) (Task_t025E98A50FB47A9B2723A0B6676C457151F0280B*, const RuntimeMethod*))Factory_Return_TisRuntimeObject_mE5BBA6F699F07DC9ADC22AA2DD428ABC09BB6EFB_gshared)(___0_task, method);
}
inline void Factory_Return_TisAsyncOperationTask_t99808195DAEE9EE75DBDD7F875A14E606A5EA053_m9CEF567EBACA29E7A3375696419A011877493F30 (AsyncOperationTask_t99808195DAEE9EE75DBDD7F875A14E606A5EA053* ___0_task, const RuntimeMethod* method)
{
	((  void (*) (AsyncOperationTask_t99808195DAEE9EE75DBDD7F875A14E606A5EA053*, const RuntimeMethod*))Factory_Return_TisRuntimeObject_mE5BBA6F699F07DC9ADC22AA2DD428ABC09BB6EFB_gshared)(___0_task, method);
}
inline void Factory_Return_TisTaskDelay_tF2D712504417D431B06EB95C4F323F8FE5BFAA4E_m5845A8D683D6C2BF02E9CB8CA5DFD93BBBE36031 (TaskDelay_tF2D712504417D431B06EB95C4F323F8FE5BFAA4E* ___0_task, const RuntimeMethod* method)
{
	((  void (*) (TaskDelay_tF2D712504417D431B06EB95C4F323F8FE5BFAA4E*, const RuntimeMethod*))Factory_Return_TisRuntimeObject_mE5BBA6F699F07DC9ADC22AA2DD428ABC09BB6EFB_gshared)(___0_task, method);
}
inline void Factory_Return_TisTaskWait_t3EE9538F13FC35386954C2A0357508C54402C70D_mB42E50EDBE4F1D50308DF52F1AEA1467AB4D82C0 (TaskWait_t3EE9538F13FC35386954C2A0357508C54402C70D* ___0_task, const RuntimeMethod* method)
{
	((  void (*) (TaskWait_t3EE9538F13FC35386954C2A0357508C54402C70D*, const RuntimeMethod*))Factory_Return_TisRuntimeObject_mE5BBA6F699F07DC9ADC22AA2DD428ABC09BB6EFB_gshared)(___0_task, method);
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Queue_1_t1753017E9AC02C107096B1F64F9CA7D8066B3F68* SequentialScheduler_get__Tasks_mB83D22B61EB664FFBE0E0E07522B028B99171824_inline (SequentialScheduler_tA81A2FC813C06F461FF491C537503D1A0700832B* __this, const RuntimeMethod* method) ;
inline int32_t Queue_1_get_Count_m2E7A67F6083221FC900A76A6AE2DD99EAB10CEB5_inline (Queue_1_t1753017E9AC02C107096B1F64F9CA7D8066B3F68* __this, const RuntimeMethod* method)
{
	return ((  int32_t (*) (Queue_1_t1753017E9AC02C107096B1F64F9CA7D8066B3F68*, const RuntimeMethod*))Queue_1_get_Count_m1768ADA9855B7CDA14C9C42E098A287F1A39C3A2_gshared_inline)(__this, method);
}
inline Enumerator_t8A456E1C05D8FE1CF2370E2519618D4ACF57D6F3 Queue_1_GetEnumerator_m9F588D95754FFCD6F96CEC48D8970EB912112FFE (Queue_1_t1753017E9AC02C107096B1F64F9CA7D8066B3F68* __this, const RuntimeMethod* method)
{
	return ((  Enumerator_t8A456E1C05D8FE1CF2370E2519618D4ACF57D6F3 (*) (Queue_1_t1753017E9AC02C107096B1F64F9CA7D8066B3F68*, const RuntimeMethod*))Queue_1_GetEnumerator_mBF0033C4BCEA408644D24F0B28A81F9145FB97C9_gshared)(__this, method);
}
inline void Enumerator_Dispose_m03E0C0F3E267D499A2042C972641859F55CA5FCB (Enumerator_t8A456E1C05D8FE1CF2370E2519618D4ACF57D6F3* __this, const RuntimeMethod* method)
{
	((  void (*) (Enumerator_t8A456E1C05D8FE1CF2370E2519618D4ACF57D6F3*, const RuntimeMethod*))Enumerator_Dispose_m680926A5EFC7099ECBCE9DEF68F8DED03C103955_gshared)(__this, method);
}
inline RuntimeObject* Enumerator_get_Current_m9C237223D7642D034FE235121A5063E40C0CD0D0 (Enumerator_t8A456E1C05D8FE1CF2370E2519618D4ACF57D6F3* __this, const RuntimeMethod* method)
{
	return ((  RuntimeObject* (*) (Enumerator_t8A456E1C05D8FE1CF2370E2519618D4ACF57D6F3*, const RuntimeMethod*))Enumerator_get_Current_m5F2338F4C35E898DB7231D7E30F30155498FA9D7_gshared)(__this, method);
}
inline bool Enumerator_MoveNext_m4D2FA9D284D9CC942BDDBA2136D35BA4E3C56912 (Enumerator_t8A456E1C05D8FE1CF2370E2519618D4ACF57D6F3* __this, const RuntimeMethod* method)
{
	return ((  bool (*) (Enumerator_t8A456E1C05D8FE1CF2370E2519618D4ACF57D6F3*, const RuntimeMethod*))Enumerator_MoveNext_mABD92CBE05B031E50E316375DDC8B2BDAD3F6F84_gshared)(__this, method);
}
inline void Queue_1_Clear_m6110B566D9B8D376BFCFF72D3FE9FF88B118E026 (Queue_1_t1753017E9AC02C107096B1F64F9CA7D8066B3F68* __this, const RuntimeMethod* method)
{
	((  void (*) (Queue_1_t1753017E9AC02C107096B1F64F9CA7D8066B3F68*, const RuntimeMethod*))Queue_1_Clear_m70861E24CF43ECFF3BC5C2AD4EE55963D54D8711_gshared)(__this, method);
}
inline void Queue_1_Enqueue_mE30AFD66E5DB5F92244805EF4E66CB554B2FEF95 (Queue_1_t1753017E9AC02C107096B1F64F9CA7D8066B3F68* __this, RuntimeObject* ___0_item, const RuntimeMethod* method)
{
	((  void (*) (Queue_1_t1753017E9AC02C107096B1F64F9CA7D8066B3F68*, RuntimeObject*, const RuntimeMethod*))Queue_1_Enqueue_m5CB8CF3906F1289F92036F0973EC5BE3450402EF_gshared)(__this, ___0_item, method);
}
inline RuntimeObject* Queue_1_Dequeue_mB8DA6E612E1335E5EBBBD1E30FB265B20BDC7220 (Queue_1_t1753017E9AC02C107096B1F64F9CA7D8066B3F68* __this, const RuntimeMethod* method)
{
	return ((  RuntimeObject* (*) (Queue_1_t1753017E9AC02C107096B1F64F9CA7D8066B3F68*, const RuntimeMethod*))Queue_1_Dequeue_m86B243DF9EC238316EC3D27DF3E0AB8DB0987E84_gshared)(__this, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ArgumentOutOfRangeException__ctor_mE5B2755F0BEA043CACF915D5CE140859EE58FA66 (ArgumentOutOfRangeException_tEA2822DAF62B10EEED00E0E3A341D4BAF78CF85F* __this, String_t* ___0_paramName, String_t* ___1_message, const RuntimeMethod* method) ;
inline void Queue_1__ctor_mFC3EB5A37E7A565A7833603DC722ACD9C4269AC5 (Queue_1_t1753017E9AC02C107096B1F64F9CA7D8066B3F68* __this, const RuntimeMethod* method)
{
	((  void (*) (Queue_1_t1753017E9AC02C107096B1F64F9CA7D8066B3F68*, const RuntimeMethod*))Queue_1__ctor_m6E2A5A8173E0CC524496D5155C737DF8FD10D0EB_gshared)(__this, method);
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR List_1_t930598996D1A93CCE7F51C4E2B0CC9645E9B8113* ParallelScheduler_get__Tasks_mC96C0BC294957F039A8E9D699E56F562886BD0F7_inline (ParallelScheduler_t8379A00B9D56775018796CE4487866984ED97D4E* __this, const RuntimeMethod* method) ;
inline RuntimeObject* List_1_get_Item_m32546B9C12BB96B1EB358899E1EB3B5BD6A33A49 (List_1_t930598996D1A93CCE7F51C4E2B0CC9645E9B8113* __this, int32_t ___0_index, const RuntimeMethod* method)
{
	return ((  RuntimeObject* (*) (List_1_t930598996D1A93CCE7F51C4E2B0CC9645E9B8113*, int32_t, const RuntimeMethod*))List_1_get_Item_m33561245D64798C2AB07584C0EC4F240E4839A38_gshared)(__this, ___0_index, method);
}
inline int32_t List_1_get_Count_m1CA38F17002C61754AC244EF759BA231250CA54A_inline (List_1_t930598996D1A93CCE7F51C4E2B0CC9645E9B8113* __this, const RuntimeMethod* method)
{
	return ((  int32_t (*) (List_1_t930598996D1A93CCE7F51C4E2B0CC9645E9B8113*, const RuntimeMethod*))List_1_get_Count_m4407E4C389F22B8CEC282C15D56516658746C383_gshared_inline)(__this, method);
}
inline Enumerator_t2558BB0501E98A606E95BEA700B600D43C4995EA List_1_GetEnumerator_m4B2195DBF47EFB502FADE2E39C377C6E068377CA (List_1_t930598996D1A93CCE7F51C4E2B0CC9645E9B8113* __this, const RuntimeMethod* method)
{
	return ((  Enumerator_t2558BB0501E98A606E95BEA700B600D43C4995EA (*) (List_1_t930598996D1A93CCE7F51C4E2B0CC9645E9B8113*, const RuntimeMethod*))List_1_GetEnumerator_mD8294A7FA2BEB1929487127D476F8EC1CDC23BFC_gshared)(__this, method);
}
inline void Enumerator_Dispose_m909DFF743AF500AD152AFE317C71F7AFC9EDF6A5 (Enumerator_t2558BB0501E98A606E95BEA700B600D43C4995EA* __this, const RuntimeMethod* method)
{
	((  void (*) (Enumerator_t2558BB0501E98A606E95BEA700B600D43C4995EA*, const RuntimeMethod*))Enumerator_Dispose_mD9DC3E3C3697830A4823047AB29A77DBBB5ED419_gshared)(__this, method);
}
inline RuntimeObject* Enumerator_get_Current_m3FC1302CC110A99701ADC815FE3DF672DD6C3C69_inline (Enumerator_t2558BB0501E98A606E95BEA700B600D43C4995EA* __this, const RuntimeMethod* method)
{
	return ((  RuntimeObject* (*) (Enumerator_t2558BB0501E98A606E95BEA700B600D43C4995EA*, const RuntimeMethod*))Enumerator_get_Current_m6330F15D18EE4F547C05DF9BF83C5EB710376027_gshared_inline)(__this, method);
}
inline bool Enumerator_MoveNext_mD5C0DAF6826810806AFFCB26CAD48DE90D30FA0B (Enumerator_t2558BB0501E98A606E95BEA700B600D43C4995EA* __this, const RuntimeMethod* method)
{
	return ((  bool (*) (Enumerator_t2558BB0501E98A606E95BEA700B600D43C4995EA*, const RuntimeMethod*))Enumerator_MoveNext_mE921CC8F29FBBDE7CC3209A0ED0D921D58D00BCB_gshared)(__this, method);
}
inline void List_1_Clear_m75F6D7D2191A3B21E9EFCAAAB61753E03B3CF78C_inline (List_1_t930598996D1A93CCE7F51C4E2B0CC9645E9B8113* __this, const RuntimeMethod* method)
{
	((  void (*) (List_1_t930598996D1A93CCE7F51C4E2B0CC9645E9B8113*, const RuntimeMethod*))List_1_Clear_m16C1F2C61FED5955F10EB36BC1CB2DF34B128994_gshared_inline)(__this, method);
}
inline void List_1_Add_mD87DFAD1A3DDEB06156BFFE81A9C7F9D6D3D74C1_inline (List_1_t930598996D1A93CCE7F51C4E2B0CC9645E9B8113* __this, RuntimeObject* ___0_item, const RuntimeMethod* method)
{
	((  void (*) (List_1_t930598996D1A93CCE7F51C4E2B0CC9645E9B8113*, RuntimeObject*, const RuntimeMethod*))List_1_Add_mEBCF994CC3814631017F46A387B1A192ED6C85C7_gshared_inline)(__this, ___0_item, method);
}
inline void List_1__ctor_m7B7D12AA36992ED80132FBEEE676F8C267664A97 (List_1_t930598996D1A93CCE7F51C4E2B0CC9645E9B8113* __this, const RuntimeMethod* method)
{
	((  void (*) (List_1_t930598996D1A93CCE7F51C4E2B0CC9645E9B8113*, const RuntimeMethod*))List_1__ctor_m7F078BB342729BDF11327FD89D7872265328F690_gshared)(__this, method);
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR List_1_t34E53346ADC531C7809398BA078DED1E182FEF4C* CombinedScheduler_get__Schedulers_m29051BFB881A5D5FD9F106D462C3970259B866EC_inline (CombinedScheduler_tC9BC117BCE01E5068E0E5F7D76183ED3A2DFA0A8* __this, const RuntimeMethod* method) ;
inline RuntimeObject* List_1_get_Item_m5A3F4B3F4312888A93224C5CF7BEFCB5DE5342CB (List_1_t34E53346ADC531C7809398BA078DED1E182FEF4C* __this, int32_t ___0_index, const RuntimeMethod* method)
{
	return ((  RuntimeObject* (*) (List_1_t34E53346ADC531C7809398BA078DED1E182FEF4C*, int32_t, const RuntimeMethod*))List_1_get_Item_m33561245D64798C2AB07584C0EC4F240E4839A38_gshared)(__this, ___0_index, method);
}
inline int32_t List_1_get_Count_mD2E9EFE20B0EEC3C6831330B6462B95B5218982A_inline (List_1_t34E53346ADC531C7809398BA078DED1E182FEF4C* __this, const RuntimeMethod* method)
{
	return ((  int32_t (*) (List_1_t34E53346ADC531C7809398BA078DED1E182FEF4C*, const RuntimeMethod*))List_1_get_Count_m4407E4C389F22B8CEC282C15D56516658746C383_gshared_inline)(__this, method);
}
inline void List_1_Add_m9652946599533509ECAE57F6A3AD6B4AC39FFA8C_inline (List_1_t34E53346ADC531C7809398BA078DED1E182FEF4C* __this, RuntimeObject* ___0_item, const RuntimeMethod* method)
{
	((  void (*) (List_1_t34E53346ADC531C7809398BA078DED1E182FEF4C*, RuntimeObject*, const RuntimeMethod*))List_1_Add_mEBCF994CC3814631017F46A387B1A192ED6C85C7_gshared_inline)(__this, ___0_item, method);
}
inline void List_1__ctor_mFA461A6D907A1232C974461C054E0F3E5D570D95 (List_1_t34E53346ADC531C7809398BA078DED1E182FEF4C* __this, const RuntimeMethod* method)
{
	((  void (*) (List_1_t34E53346ADC531C7809398BA078DED1E182FEF4C*, const RuntimeMethod*))List_1__ctor_m7F078BB342729BDF11327FD89D7872265328F690_gshared)(__this, method);
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Task_set_IsFinished_mD6402087B874A5129E3E6DE8D00913475724DBBB_inline (Task_t025E98A50FB47A9B2723A0B6676C457151F0280B* __this, bool ___0_value, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Task_set_Progress_m3699ED176F6EFC3A9B805D4ADB8E8ACDAC3DF6E5_inline (Task_t025E98A50FB47A9B2723A0B6676C457151F0280B* __this, float ___0_value, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Action_Invoke_m7126A54DACA72B845424072887B5F3A51FC3808E_inline (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void TaskDelay_set_IsFinished_m054FAB34FEFDEF22E3EA727665C4035009914F9C_inline (TaskDelay_tF2D712504417D431B06EB95C4F323F8FE5BFAA4E* __this, bool ___0_value, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void TaskDelay_set_Progress_mF54B802D8EF7B8B88692737F28BD19B8A3FB0A3D_inline (TaskDelay_tF2D712504417D431B06EB95C4F323F8FE5BFAA4E* __this, float ___0_value, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool TaskWait_get_IsFinished_m403C4658AE7A25F9CC2F3EEB5D8443207B3E2284_inline (TaskWait_t3EE9538F13FC35386954C2A0357508C54402C70D* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Mathf_Clamp01_mA7E048DBDA832D399A581BE4D6DED9FA44CE0F14_inline (float ___0_value, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void TaskWait_set_IsFinished_mB7BA5FF0FE910A03891343EDEB0B0BA88D0C4F96_inline (TaskWait_t3EE9538F13FC35386954C2A0357508C54402C70D* __this, bool ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool TaskWait_Evaluate_m0562B993210978EB140A1A66FDA26E7F2C379E58 (TaskWait_t3EE9538F13FC35386954C2A0357508C54402C70D* __this, const RuntimeMethod* method) ;
inline bool Func_1_Invoke_mBB7F37C468451AF57FAF31635C544D6B8C4373B2_inline (Func_1_t2BE7F58348C9CC544A8973B3A9E55541DE43C457* __this, const RuntimeMethod* method)
{
	return ((  bool (*) (Func_1_t2BE7F58348C9CC544A8973B3A9E55541DE43C457*, const RuntimeMethod*))Func_1_Invoke_mBB7F37C468451AF57FAF31635C544D6B8C4373B2_gshared_inline)(__this, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool AsyncOperation_get_isDone_m68A0682777E2132FC033182E9F50303566AA354D (AsyncOperation_tD2789250E4B098DEDA92B366A577E500A92D2D3C* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float AsyncOperation_get_progress_mF3B2837C1A5DDF3C2F7A3BA1E449DD4C71C632EE (AsyncOperation_tD2789250E4B098DEDA92B366A577E500A92D2D3C* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Array_Clear_m50BAA3751899858B097D3FF2ED31F284703FE5CB (RuntimeArray* ___0_array, int32_t ___1_index, int32_t ___2_length, const RuntimeMethod* method) ;
inline void List_1_AddWithResize_m79A9BF770BEF9C06BE40D5401E55E375F2726CC4 (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D* __this, RuntimeObject* ___0_item, const RuntimeMethod* method)
{
	((  void (*) (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D*, RuntimeObject*, const RuntimeMethod*))List_1_AddWithResize_m79A9BF770BEF9C06BE40D5401E55E375F2726CC4_gshared)(__this, ___0_item, method);
}
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR MonoScriptData_tC477D07AD440396933A37A440EDAB6468C2FDA0F UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_mA2345D2B8E39CC1F65A8587B8A74228B7077C018 (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CPrivateImplementationDetailsU3E_tF3168826DB1B05F8B96B03AE2382C5A64C02B37B____3EB185591374ED022C1830963ECCD1350E6B55F888B8DD45F7E4286900524F59_FieldInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CPrivateImplementationDetailsU3E_tF3168826DB1B05F8B96B03AE2382C5A64C02B37B____7F5C03A7FFE1B1703D00023192818FB641AAF20805BA6DB4579473DDA4DE5BC4_FieldInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	MonoScriptData_tC477D07AD440396933A37A440EDAB6468C2FDA0F V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		il2cpp_codegen_initobj((&V_0), sizeof(MonoScriptData_tC477D07AD440396933A37A440EDAB6468C2FDA0F));
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_0 = (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)SZArrayNew(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031_il2cpp_TypeInfo_var, (uint32_t)((int32_t)234));
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_1 = L_0;
		RuntimeFieldHandle_t6E4C45B6D2EA12FC99185805A7E77527899B25C5 L_2 = { reinterpret_cast<intptr_t> (U3CPrivateImplementationDetailsU3E_tF3168826DB1B05F8B96B03AE2382C5A64C02B37B____3EB185591374ED022C1830963ECCD1350E6B55F888B8DD45F7E4286900524F59_FieldInfo_var) };
		RuntimeHelpers_InitializeArray_m751372AA3F24FBF6DA9B9D687CBFA2DE436CAB9B((RuntimeArray*)L_1, L_2, NULL);
		(&V_0)->___FilePathsData = L_1;
		Il2CppCodeGenWriteBarrier((void**)(&(&V_0)->___FilePathsData), (void*)L_1);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_3 = (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)SZArrayNew(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031_il2cpp_TypeInfo_var, (uint32_t)((int32_t)381));
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_4 = L_3;
		RuntimeFieldHandle_t6E4C45B6D2EA12FC99185805A7E77527899B25C5 L_5 = { reinterpret_cast<intptr_t> (U3CPrivateImplementationDetailsU3E_tF3168826DB1B05F8B96B03AE2382C5A64C02B37B____7F5C03A7FFE1B1703D00023192818FB641AAF20805BA6DB4579473DDA4DE5BC4_FieldInfo_var) };
		RuntimeHelpers_InitializeArray_m751372AA3F24FBF6DA9B9D687CBFA2DE436CAB9B((RuntimeArray*)L_4, L_5, NULL);
		(&V_0)->___TypesData = L_4;
		Il2CppCodeGenWriteBarrier((void**)(&(&V_0)->___TypesData), (void*)L_4);
		(&V_0)->___TotalFiles = 4;
		(&V_0)->___TotalTypes = ((int32_t)13);
		(&V_0)->___IsEditorOnly = (bool)0;
		MonoScriptData_tC477D07AD440396933A37A440EDAB6468C2FDA0F L_6 = V_0;
		return L_6;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_m9EAADF5D94F2B14ACC72BA9FBA9F3D1C623ACBE7 (UnitySourceGeneratedAssemblyMonoScriptTypes_v1_t0F54272D0D2FD43327C5190A6EC63A00F34201D8* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C void MonoScriptData_tC477D07AD440396933A37A440EDAB6468C2FDA0F_marshal_pinvoke(const MonoScriptData_tC477D07AD440396933A37A440EDAB6468C2FDA0F& unmarshaled, MonoScriptData_tC477D07AD440396933A37A440EDAB6468C2FDA0F_marshaled_pinvoke& marshaled)
{
	marshaled.___FilePathsData = il2cpp_codegen_com_marshal_safe_array(IL2CPP_VT_I1, unmarshaled.___FilePathsData);
	marshaled.___TypesData = il2cpp_codegen_com_marshal_safe_array(IL2CPP_VT_I1, unmarshaled.___TypesData);
	marshaled.___TotalTypes = unmarshaled.___TotalTypes;
	marshaled.___TotalFiles = unmarshaled.___TotalFiles;
	marshaled.___IsEditorOnly = static_cast<int32_t>(unmarshaled.___IsEditorOnly);
}
IL2CPP_EXTERN_C void MonoScriptData_tC477D07AD440396933A37A440EDAB6468C2FDA0F_marshal_pinvoke_back(const MonoScriptData_tC477D07AD440396933A37A440EDAB6468C2FDA0F_marshaled_pinvoke& marshaled, MonoScriptData_tC477D07AD440396933A37A440EDAB6468C2FDA0F& unmarshaled)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	unmarshaled.___FilePathsData = (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I1, Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, marshaled.___FilePathsData);
	Il2CppCodeGenWriteBarrier((void**)(&unmarshaled.___FilePathsData), (void*)(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I1, Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, marshaled.___FilePathsData));
	unmarshaled.___TypesData = (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I1, Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, marshaled.___TypesData);
	Il2CppCodeGenWriteBarrier((void**)(&unmarshaled.___TypesData), (void*)(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I1, Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, marshaled.___TypesData));
	int32_t unmarshaledTotalTypes_temp_2 = 0;
	unmarshaledTotalTypes_temp_2 = marshaled.___TotalTypes;
	unmarshaled.___TotalTypes = unmarshaledTotalTypes_temp_2;
	int32_t unmarshaledTotalFiles_temp_3 = 0;
	unmarshaledTotalFiles_temp_3 = marshaled.___TotalFiles;
	unmarshaled.___TotalFiles = unmarshaledTotalFiles_temp_3;
	bool unmarshaledIsEditorOnly_temp_4 = false;
	unmarshaledIsEditorOnly_temp_4 = static_cast<bool>(marshaled.___IsEditorOnly);
	unmarshaled.___IsEditorOnly = unmarshaledIsEditorOnly_temp_4;
}
IL2CPP_EXTERN_C void MonoScriptData_tC477D07AD440396933A37A440EDAB6468C2FDA0F_marshal_pinvoke_cleanup(MonoScriptData_tC477D07AD440396933A37A440EDAB6468C2FDA0F_marshaled_pinvoke& marshaled)
{
	il2cpp_codegen_com_destroy_safe_array(marshaled.___FilePathsData);
	marshaled.___FilePathsData = NULL;
	il2cpp_codegen_com_destroy_safe_array(marshaled.___TypesData);
	marshaled.___TypesData = NULL;
}
IL2CPP_EXTERN_C void MonoScriptData_tC477D07AD440396933A37A440EDAB6468C2FDA0F_marshal_com(const MonoScriptData_tC477D07AD440396933A37A440EDAB6468C2FDA0F& unmarshaled, MonoScriptData_tC477D07AD440396933A37A440EDAB6468C2FDA0F_marshaled_com& marshaled)
{
	marshaled.___FilePathsData = il2cpp_codegen_com_marshal_safe_array(IL2CPP_VT_I1, unmarshaled.___FilePathsData);
	marshaled.___TypesData = il2cpp_codegen_com_marshal_safe_array(IL2CPP_VT_I1, unmarshaled.___TypesData);
	marshaled.___TotalTypes = unmarshaled.___TotalTypes;
	marshaled.___TotalFiles = unmarshaled.___TotalFiles;
	marshaled.___IsEditorOnly = static_cast<int32_t>(unmarshaled.___IsEditorOnly);
}
IL2CPP_EXTERN_C void MonoScriptData_tC477D07AD440396933A37A440EDAB6468C2FDA0F_marshal_com_back(const MonoScriptData_tC477D07AD440396933A37A440EDAB6468C2FDA0F_marshaled_com& marshaled, MonoScriptData_tC477D07AD440396933A37A440EDAB6468C2FDA0F& unmarshaled)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	unmarshaled.___FilePathsData = (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I1, Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, marshaled.___FilePathsData);
	Il2CppCodeGenWriteBarrier((void**)(&unmarshaled.___FilePathsData), (void*)(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I1, Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, marshaled.___FilePathsData));
	unmarshaled.___TypesData = (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I1, Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, marshaled.___TypesData);
	Il2CppCodeGenWriteBarrier((void**)(&unmarshaled.___TypesData), (void*)(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I1, Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, marshaled.___TypesData));
	int32_t unmarshaledTotalTypes_temp_2 = 0;
	unmarshaledTotalTypes_temp_2 = marshaled.___TotalTypes;
	unmarshaled.___TotalTypes = unmarshaledTotalTypes_temp_2;
	int32_t unmarshaledTotalFiles_temp_3 = 0;
	unmarshaledTotalFiles_temp_3 = marshaled.___TotalFiles;
	unmarshaled.___TotalFiles = unmarshaledTotalFiles_temp_3;
	bool unmarshaledIsEditorOnly_temp_4 = false;
	unmarshaledIsEditorOnly_temp_4 = static_cast<bool>(marshaled.___IsEditorOnly);
	unmarshaled.___IsEditorOnly = unmarshaledIsEditorOnly_temp_4;
}
IL2CPP_EXTERN_C void MonoScriptData_tC477D07AD440396933A37A440EDAB6468C2FDA0F_marshal_com_cleanup(MonoScriptData_tC477D07AD440396933A37A440EDAB6468C2FDA0F_marshaled_com& marshaled)
{
	il2cpp_codegen_com_destroy_safe_array(marshaled.___FilePathsData);
	marshaled.___FilePathsData = NULL;
	il2cpp_codegen_com_destroy_safe_array(marshaled.___TypesData);
	marshaled.___TypesData = NULL;
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* Factory_FromAction_m368E677C8BEFE8EEDC19A03E037F3110F77781E9 (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___0_action, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TaskPool_1_Get_m3456F6C640DDAD96211002A3CC06FD33EA4F13CC_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TaskPool_1_tE4C032936710E899A0C76A261A479D82A424BBD0_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Task_t025E98A50FB47A9B2723A0B6676C457151F0280B_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec__DisplayClass0_0_U3CFromActionU3Eb__0_mDA12C1B15CBD69105213328290E0D9AB97B327F0_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec__DisplayClass0_0_tD5AC230A70D2F0377E27947EF9A53F2B08B23CC3_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	U3CU3Ec__DisplayClass0_0_tD5AC230A70D2F0377E27947EF9A53F2B08B23CC3* V_0 = NULL;
	Task_t025E98A50FB47A9B2723A0B6676C457151F0280B* G_B2_0 = NULL;
	U3CU3Ec__DisplayClass0_0_tD5AC230A70D2F0377E27947EF9A53F2B08B23CC3* G_B2_1 = NULL;
	Task_t025E98A50FB47A9B2723A0B6676C457151F0280B* G_B1_0 = NULL;
	U3CU3Ec__DisplayClass0_0_tD5AC230A70D2F0377E27947EF9A53F2B08B23CC3* G_B1_1 = NULL;
	{
		U3CU3Ec__DisplayClass0_0_tD5AC230A70D2F0377E27947EF9A53F2B08B23CC3* L_0 = (U3CU3Ec__DisplayClass0_0_tD5AC230A70D2F0377E27947EF9A53F2B08B23CC3*)il2cpp_codegen_object_new(U3CU3Ec__DisplayClass0_0_tD5AC230A70D2F0377E27947EF9A53F2B08B23CC3_il2cpp_TypeInfo_var);
		U3CU3Ec__DisplayClass0_0__ctor_mC5A9A08671EF55D835B9F15993BF4FDE59F45646(L_0, NULL);
		V_0 = L_0;
		U3CU3Ec__DisplayClass0_0_tD5AC230A70D2F0377E27947EF9A53F2B08B23CC3* L_1 = V_0;
		il2cpp_codegen_runtime_class_init_inline(TaskPool_1_tE4C032936710E899A0C76A261A479D82A424BBD0_il2cpp_TypeInfo_var);
		Task_t025E98A50FB47A9B2723A0B6676C457151F0280B* L_2;
		L_2 = TaskPool_1_Get_m3456F6C640DDAD96211002A3CC06FD33EA4F13CC(TaskPool_1_Get_m3456F6C640DDAD96211002A3CC06FD33EA4F13CC_RuntimeMethod_var);
		Task_t025E98A50FB47A9B2723A0B6676C457151F0280B* L_3 = L_2;
		if (L_3)
		{
			G_B2_0 = L_3;
			G_B2_1 = L_1;
			goto IL_0015;
		}
		G_B1_0 = L_3;
		G_B1_1 = L_1;
	}
	{
		Task_t025E98A50FB47A9B2723A0B6676C457151F0280B* L_4 = (Task_t025E98A50FB47A9B2723A0B6676C457151F0280B*)il2cpp_codegen_object_new(Task_t025E98A50FB47A9B2723A0B6676C457151F0280B_il2cpp_TypeInfo_var);
		Task__ctor_m75F016C7FAF44A76CF1FAD52D6761E1EA3228063(L_4, NULL);
		G_B2_0 = L_4;
		G_B2_1 = G_B1_1;
	}

IL_0015:
	{
		NullCheck(G_B2_1);
		G_B2_1->___task = G_B2_0;
		Il2CppCodeGenWriteBarrier((void**)(&G_B2_1->___task), (void*)G_B2_0);
		U3CU3Ec__DisplayClass0_0_tD5AC230A70D2F0377E27947EF9A53F2B08B23CC3* L_5 = V_0;
		NullCheck(L_5);
		Task_t025E98A50FB47A9B2723A0B6676C457151F0280B* L_6 = L_5->___task;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_7 = ___0_action;
		U3CU3Ec__DisplayClass0_0_tD5AC230A70D2F0377E27947EF9A53F2B08B23CC3* L_8 = V_0;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_9 = (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)il2cpp_codegen_object_new(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		Action__ctor_mBDC7B0B4A3F583B64C2896F01BDED360772F67DC(L_9, L_8, (intptr_t)((void*)U3CU3Ec__DisplayClass0_0_U3CFromActionU3Eb__0_mDA12C1B15CBD69105213328290E0D9AB97B327F0_RuntimeMethod_var), NULL);
		NullCheck(L_6);
		Task_Initialize_m1245EA3003E520F57ED20FBF2BD667C4D49FEF71(L_6, L_7, L_9, NULL);
		U3CU3Ec__DisplayClass0_0_tD5AC230A70D2F0377E27947EF9A53F2B08B23CC3* L_10 = V_0;
		NullCheck(L_10);
		Task_t025E98A50FB47A9B2723A0B6676C457151F0280B* L_11 = L_10->___task;
		return L_11;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* Factory_FromAsyncOperation_m45889044D10FFEE63839B564F42FC767CEB992D0 (AsyncOperation_tD2789250E4B098DEDA92B366A577E500A92D2D3C* ___0_operation, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&AsyncOperationTask_t99808195DAEE9EE75DBDD7F875A14E606A5EA053_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TaskPool_1_Get_m7FDC447B1E1E903778115C4305CC76023916A8E2_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TaskPool_1_t8208ED8332FAD542289791A213F005A015F55636_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec__DisplayClass2_0_U3CFromAsyncOperationU3Eb__0_m8DEC88A191E826C2F11FDD5944C2097A78506492_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec__DisplayClass2_0_t5D1712945E8ACD90F26C4380E606A54C7BC4DFF5_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	U3CU3Ec__DisplayClass2_0_t5D1712945E8ACD90F26C4380E606A54C7BC4DFF5* V_0 = NULL;
	AsyncOperationTask_t99808195DAEE9EE75DBDD7F875A14E606A5EA053* G_B2_0 = NULL;
	U3CU3Ec__DisplayClass2_0_t5D1712945E8ACD90F26C4380E606A54C7BC4DFF5* G_B2_1 = NULL;
	AsyncOperationTask_t99808195DAEE9EE75DBDD7F875A14E606A5EA053* G_B1_0 = NULL;
	U3CU3Ec__DisplayClass2_0_t5D1712945E8ACD90F26C4380E606A54C7BC4DFF5* G_B1_1 = NULL;
	{
		U3CU3Ec__DisplayClass2_0_t5D1712945E8ACD90F26C4380E606A54C7BC4DFF5* L_0 = (U3CU3Ec__DisplayClass2_0_t5D1712945E8ACD90F26C4380E606A54C7BC4DFF5*)il2cpp_codegen_object_new(U3CU3Ec__DisplayClass2_0_t5D1712945E8ACD90F26C4380E606A54C7BC4DFF5_il2cpp_TypeInfo_var);
		U3CU3Ec__DisplayClass2_0__ctor_mFD5FCD46A9516125DB6711707C1B3B4BE2F097AB(L_0, NULL);
		V_0 = L_0;
		U3CU3Ec__DisplayClass2_0_t5D1712945E8ACD90F26C4380E606A54C7BC4DFF5* L_1 = V_0;
		il2cpp_codegen_runtime_class_init_inline(TaskPool_1_t8208ED8332FAD542289791A213F005A015F55636_il2cpp_TypeInfo_var);
		AsyncOperationTask_t99808195DAEE9EE75DBDD7F875A14E606A5EA053* L_2;
		L_2 = TaskPool_1_Get_m7FDC447B1E1E903778115C4305CC76023916A8E2(TaskPool_1_Get_m7FDC447B1E1E903778115C4305CC76023916A8E2_RuntimeMethod_var);
		AsyncOperationTask_t99808195DAEE9EE75DBDD7F875A14E606A5EA053* L_3 = L_2;
		if (L_3)
		{
			G_B2_0 = L_3;
			G_B2_1 = L_1;
			goto IL_0015;
		}
		G_B1_0 = L_3;
		G_B1_1 = L_1;
	}
	{
		AsyncOperationTask_t99808195DAEE9EE75DBDD7F875A14E606A5EA053* L_4 = (AsyncOperationTask_t99808195DAEE9EE75DBDD7F875A14E606A5EA053*)il2cpp_codegen_object_new(AsyncOperationTask_t99808195DAEE9EE75DBDD7F875A14E606A5EA053_il2cpp_TypeInfo_var);
		AsyncOperationTask__ctor_mF0D75B1EB6AA36F78EEF1363BE966F16039ABD6E(L_4, NULL);
		G_B2_0 = L_4;
		G_B2_1 = G_B1_1;
	}

IL_0015:
	{
		NullCheck(G_B2_1);
		G_B2_1->___task = G_B2_0;
		Il2CppCodeGenWriteBarrier((void**)(&G_B2_1->___task), (void*)G_B2_0);
		U3CU3Ec__DisplayClass2_0_t5D1712945E8ACD90F26C4380E606A54C7BC4DFF5* L_5 = V_0;
		NullCheck(L_5);
		AsyncOperationTask_t99808195DAEE9EE75DBDD7F875A14E606A5EA053* L_6 = L_5->___task;
		AsyncOperation_tD2789250E4B098DEDA92B366A577E500A92D2D3C* L_7 = ___0_operation;
		U3CU3Ec__DisplayClass2_0_t5D1712945E8ACD90F26C4380E606A54C7BC4DFF5* L_8 = V_0;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_9 = (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)il2cpp_codegen_object_new(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		Action__ctor_mBDC7B0B4A3F583B64C2896F01BDED360772F67DC(L_9, L_8, (intptr_t)((void*)U3CU3Ec__DisplayClass2_0_U3CFromAsyncOperationU3Eb__0_m8DEC88A191E826C2F11FDD5944C2097A78506492_RuntimeMethod_var), NULL);
		NullCheck(L_6);
		AsyncOperationTask_Initialize_mAC8C2E376EB61E3551B7904E6ECEA40550FF3532(L_6, L_7, L_9, NULL);
		U3CU3Ec__DisplayClass2_0_t5D1712945E8ACD90F26C4380E606A54C7BC4DFF5* L_10 = V_0;
		NullCheck(L_10);
		AsyncOperationTask_t99808195DAEE9EE75DBDD7F875A14E606A5EA053* L_11 = L_10->___task;
		return L_11;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* Factory_FromDelay_m37D4044B18DD4750D2F5B3A7AB11C9DF7DE14FDA (float ___0_delay, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TaskDelay_tF2D712504417D431B06EB95C4F323F8FE5BFAA4E_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TaskPool_1_Get_m5DF572F6F84F4F83077EE74EE6FBF9B0E43583C7_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TaskPool_1_t0374BDF8E1E64F2188AD274557FD9C475E2B30A7_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec__DisplayClass3_0_U3CFromDelayU3Eb__0_m7F78507D1058BFC830CB6D143BA5EA44A728D3A5_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec__DisplayClass3_0_t53E6CDB6BAB3FEFE8401EE5D2A795DABBE094799_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	U3CU3Ec__DisplayClass3_0_t53E6CDB6BAB3FEFE8401EE5D2A795DABBE094799* V_0 = NULL;
	TaskDelay_tF2D712504417D431B06EB95C4F323F8FE5BFAA4E* G_B2_0 = NULL;
	U3CU3Ec__DisplayClass3_0_t53E6CDB6BAB3FEFE8401EE5D2A795DABBE094799* G_B2_1 = NULL;
	TaskDelay_tF2D712504417D431B06EB95C4F323F8FE5BFAA4E* G_B1_0 = NULL;
	U3CU3Ec__DisplayClass3_0_t53E6CDB6BAB3FEFE8401EE5D2A795DABBE094799* G_B1_1 = NULL;
	{
		U3CU3Ec__DisplayClass3_0_t53E6CDB6BAB3FEFE8401EE5D2A795DABBE094799* L_0 = (U3CU3Ec__DisplayClass3_0_t53E6CDB6BAB3FEFE8401EE5D2A795DABBE094799*)il2cpp_codegen_object_new(U3CU3Ec__DisplayClass3_0_t53E6CDB6BAB3FEFE8401EE5D2A795DABBE094799_il2cpp_TypeInfo_var);
		U3CU3Ec__DisplayClass3_0__ctor_mF934959DFE9764BAFDDCC0C5B101886006A0FD2E(L_0, NULL);
		V_0 = L_0;
		U3CU3Ec__DisplayClass3_0_t53E6CDB6BAB3FEFE8401EE5D2A795DABBE094799* L_1 = V_0;
		il2cpp_codegen_runtime_class_init_inline(TaskPool_1_t0374BDF8E1E64F2188AD274557FD9C475E2B30A7_il2cpp_TypeInfo_var);
		TaskDelay_tF2D712504417D431B06EB95C4F323F8FE5BFAA4E* L_2;
		L_2 = TaskPool_1_Get_m5DF572F6F84F4F83077EE74EE6FBF9B0E43583C7(TaskPool_1_Get_m5DF572F6F84F4F83077EE74EE6FBF9B0E43583C7_RuntimeMethod_var);
		TaskDelay_tF2D712504417D431B06EB95C4F323F8FE5BFAA4E* L_3 = L_2;
		if (L_3)
		{
			G_B2_0 = L_3;
			G_B2_1 = L_1;
			goto IL_0015;
		}
		G_B1_0 = L_3;
		G_B1_1 = L_1;
	}
	{
		TaskDelay_tF2D712504417D431B06EB95C4F323F8FE5BFAA4E* L_4 = (TaskDelay_tF2D712504417D431B06EB95C4F323F8FE5BFAA4E*)il2cpp_codegen_object_new(TaskDelay_tF2D712504417D431B06EB95C4F323F8FE5BFAA4E_il2cpp_TypeInfo_var);
		TaskDelay__ctor_mB022423A8FB6D9B52DA87E7698E79D8780FAE17E(L_4, NULL);
		G_B2_0 = L_4;
		G_B2_1 = G_B1_1;
	}

IL_0015:
	{
		NullCheck(G_B2_1);
		G_B2_1->___task = G_B2_0;
		Il2CppCodeGenWriteBarrier((void**)(&G_B2_1->___task), (void*)G_B2_0);
		U3CU3Ec__DisplayClass3_0_t53E6CDB6BAB3FEFE8401EE5D2A795DABBE094799* L_5 = V_0;
		NullCheck(L_5);
		TaskDelay_tF2D712504417D431B06EB95C4F323F8FE5BFAA4E* L_6 = L_5->___task;
		float L_7 = ___0_delay;
		U3CU3Ec__DisplayClass3_0_t53E6CDB6BAB3FEFE8401EE5D2A795DABBE094799* L_8 = V_0;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_9 = (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)il2cpp_codegen_object_new(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		Action__ctor_mBDC7B0B4A3F583B64C2896F01BDED360772F67DC(L_9, L_8, (intptr_t)((void*)U3CU3Ec__DisplayClass3_0_U3CFromDelayU3Eb__0_m7F78507D1058BFC830CB6D143BA5EA44A728D3A5_RuntimeMethod_var), NULL);
		NullCheck(L_6);
		TaskDelay_Initialize_m21AD415596403721C63C81ECF69A5A1886F74609(L_6, L_7, L_9, NULL);
		U3CU3Ec__DisplayClass3_0_t53E6CDB6BAB3FEFE8401EE5D2A795DABBE094799* L_10 = V_0;
		NullCheck(L_10);
		TaskDelay_tF2D712504417D431B06EB95C4F323F8FE5BFAA4E* L_11 = L_10->___task;
		return L_11;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* Factory_FromWait_m990888590D2C15E2EA61AB95A3F908DB645B964D (Func_1_t2BE7F58348C9CC544A8973B3A9E55541DE43C457* ___0_predicate, float ___1_maxWaitTime, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TaskPool_1_Get_mDACEBA70983B71A33FE287B1F4B1A1B009291093_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TaskPool_1_tEA148E62BF07EA6D24F52D9850E5B079EE766B95_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TaskWait_t3EE9538F13FC35386954C2A0357508C54402C70D_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec__DisplayClass4_0_U3CFromWaitU3Eb__0_mD486A34388585FFD0DF8E01432FF2A6C0810E284_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CU3Ec__DisplayClass4_0_t48EF7895F2E37829242EF3221693B9A2352D13A9_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	U3CU3Ec__DisplayClass4_0_t48EF7895F2E37829242EF3221693B9A2352D13A9* V_0 = NULL;
	TaskWait_t3EE9538F13FC35386954C2A0357508C54402C70D* G_B2_0 = NULL;
	U3CU3Ec__DisplayClass4_0_t48EF7895F2E37829242EF3221693B9A2352D13A9* G_B2_1 = NULL;
	TaskWait_t3EE9538F13FC35386954C2A0357508C54402C70D* G_B1_0 = NULL;
	U3CU3Ec__DisplayClass4_0_t48EF7895F2E37829242EF3221693B9A2352D13A9* G_B1_1 = NULL;
	{
		U3CU3Ec__DisplayClass4_0_t48EF7895F2E37829242EF3221693B9A2352D13A9* L_0 = (U3CU3Ec__DisplayClass4_0_t48EF7895F2E37829242EF3221693B9A2352D13A9*)il2cpp_codegen_object_new(U3CU3Ec__DisplayClass4_0_t48EF7895F2E37829242EF3221693B9A2352D13A9_il2cpp_TypeInfo_var);
		U3CU3Ec__DisplayClass4_0__ctor_m64462E4856AF8D55FCFBD46364F682A329BB1C67(L_0, NULL);
		V_0 = L_0;
		U3CU3Ec__DisplayClass4_0_t48EF7895F2E37829242EF3221693B9A2352D13A9* L_1 = V_0;
		il2cpp_codegen_runtime_class_init_inline(TaskPool_1_tEA148E62BF07EA6D24F52D9850E5B079EE766B95_il2cpp_TypeInfo_var);
		TaskWait_t3EE9538F13FC35386954C2A0357508C54402C70D* L_2;
		L_2 = TaskPool_1_Get_mDACEBA70983B71A33FE287B1F4B1A1B009291093(TaskPool_1_Get_mDACEBA70983B71A33FE287B1F4B1A1B009291093_RuntimeMethod_var);
		TaskWait_t3EE9538F13FC35386954C2A0357508C54402C70D* L_3 = L_2;
		if (L_3)
		{
			G_B2_0 = L_3;
			G_B2_1 = L_1;
			goto IL_0015;
		}
		G_B1_0 = L_3;
		G_B1_1 = L_1;
	}
	{
		TaskWait_t3EE9538F13FC35386954C2A0357508C54402C70D* L_4 = (TaskWait_t3EE9538F13FC35386954C2A0357508C54402C70D*)il2cpp_codegen_object_new(TaskWait_t3EE9538F13FC35386954C2A0357508C54402C70D_il2cpp_TypeInfo_var);
		TaskWait__ctor_mBC9F0F91A9528C7FC4916B17D72BD37CED4DE9FB(L_4, NULL);
		G_B2_0 = L_4;
		G_B2_1 = G_B1_1;
	}

IL_0015:
	{
		NullCheck(G_B2_1);
		G_B2_1->___task = G_B2_0;
		Il2CppCodeGenWriteBarrier((void**)(&G_B2_1->___task), (void*)G_B2_0);
		U3CU3Ec__DisplayClass4_0_t48EF7895F2E37829242EF3221693B9A2352D13A9* L_5 = V_0;
		NullCheck(L_5);
		TaskWait_t3EE9538F13FC35386954C2A0357508C54402C70D* L_6 = L_5->___task;
		Func_1_t2BE7F58348C9CC544A8973B3A9E55541DE43C457* L_7 = ___0_predicate;
		float L_8 = ___1_maxWaitTime;
		U3CU3Ec__DisplayClass4_0_t48EF7895F2E37829242EF3221693B9A2352D13A9* L_9 = V_0;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_10 = (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)il2cpp_codegen_object_new(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		Action__ctor_mBDC7B0B4A3F583B64C2896F01BDED360772F67DC(L_10, L_9, (intptr_t)((void*)U3CU3Ec__DisplayClass4_0_U3CFromWaitU3Eb__0_mD486A34388585FFD0DF8E01432FF2A6C0810E284_RuntimeMethod_var), NULL);
		NullCheck(L_6);
		TaskWait_Initialize_m57E23EB5BE81C2573B53470173A7268E34EF9E15(L_6, L_7, L_8, L_10, NULL);
		U3CU3Ec__DisplayClass4_0_t48EF7895F2E37829242EF3221693B9A2352D13A9* L_11 = V_0;
		NullCheck(L_11);
		TaskWait_t3EE9538F13FC35386954C2A0357508C54402C70D* L_12 = L_11->___task;
		return L_12;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__DisplayClass0_0__ctor_mC5A9A08671EF55D835B9F15993BF4FDE59F45646 (U3CU3Ec__DisplayClass0_0_tD5AC230A70D2F0377E27947EF9A53F2B08B23CC3* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__DisplayClass0_0_U3CFromActionU3Eb__0_mDA12C1B15CBD69105213328290E0D9AB97B327F0 (U3CU3Ec__DisplayClass0_0_tD5AC230A70D2F0377E27947EF9A53F2B08B23CC3* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Factory_Return_TisTask_t025E98A50FB47A9B2723A0B6676C457151F0280B_m566914B9DFC3E2B77C4CC79A354D4F56C08DE404_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Task_t025E98A50FB47A9B2723A0B6676C457151F0280B* L_0 = __this->___task;
		Factory_Return_TisTask_t025E98A50FB47A9B2723A0B6676C457151F0280B_m566914B9DFC3E2B77C4CC79A354D4F56C08DE404(L_0, Factory_Return_TisTask_t025E98A50FB47A9B2723A0B6676C457151F0280B_m566914B9DFC3E2B77C4CC79A354D4F56C08DE404_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__DisplayClass2_0__ctor_mFD5FCD46A9516125DB6711707C1B3B4BE2F097AB (U3CU3Ec__DisplayClass2_0_t5D1712945E8ACD90F26C4380E606A54C7BC4DFF5* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__DisplayClass2_0_U3CFromAsyncOperationU3Eb__0_m8DEC88A191E826C2F11FDD5944C2097A78506492 (U3CU3Ec__DisplayClass2_0_t5D1712945E8ACD90F26C4380E606A54C7BC4DFF5* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Factory_Return_TisAsyncOperationTask_t99808195DAEE9EE75DBDD7F875A14E606A5EA053_m9CEF567EBACA29E7A3375696419A011877493F30_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		AsyncOperationTask_t99808195DAEE9EE75DBDD7F875A14E606A5EA053* L_0 = __this->___task;
		Factory_Return_TisAsyncOperationTask_t99808195DAEE9EE75DBDD7F875A14E606A5EA053_m9CEF567EBACA29E7A3375696419A011877493F30(L_0, Factory_Return_TisAsyncOperationTask_t99808195DAEE9EE75DBDD7F875A14E606A5EA053_m9CEF567EBACA29E7A3375696419A011877493F30_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__DisplayClass3_0__ctor_mF934959DFE9764BAFDDCC0C5B101886006A0FD2E (U3CU3Ec__DisplayClass3_0_t53E6CDB6BAB3FEFE8401EE5D2A795DABBE094799* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__DisplayClass3_0_U3CFromDelayU3Eb__0_m7F78507D1058BFC830CB6D143BA5EA44A728D3A5 (U3CU3Ec__DisplayClass3_0_t53E6CDB6BAB3FEFE8401EE5D2A795DABBE094799* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Factory_Return_TisTaskDelay_tF2D712504417D431B06EB95C4F323F8FE5BFAA4E_m5845A8D683D6C2BF02E9CB8CA5DFD93BBBE36031_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		TaskDelay_tF2D712504417D431B06EB95C4F323F8FE5BFAA4E* L_0 = __this->___task;
		Factory_Return_TisTaskDelay_tF2D712504417D431B06EB95C4F323F8FE5BFAA4E_m5845A8D683D6C2BF02E9CB8CA5DFD93BBBE36031(L_0, Factory_Return_TisTaskDelay_tF2D712504417D431B06EB95C4F323F8FE5BFAA4E_m5845A8D683D6C2BF02E9CB8CA5DFD93BBBE36031_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__DisplayClass4_0__ctor_m64462E4856AF8D55FCFBD46364F682A329BB1C67 (U3CU3Ec__DisplayClass4_0_t48EF7895F2E37829242EF3221693B9A2352D13A9* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void U3CU3Ec__DisplayClass4_0_U3CFromWaitU3Eb__0_mD486A34388585FFD0DF8E01432FF2A6C0810E284 (U3CU3Ec__DisplayClass4_0_t48EF7895F2E37829242EF3221693B9A2352D13A9* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Factory_Return_TisTaskWait_t3EE9538F13FC35386954C2A0357508C54402C70D_mB42E50EDBE4F1D50308DF52F1AEA1467AB4D82C0_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		TaskWait_t3EE9538F13FC35386954C2A0357508C54402C70D* L_0 = __this->___task;
		Factory_Return_TisTaskWait_t3EE9538F13FC35386954C2A0357508C54402C70D_mB42E50EDBE4F1D50308DF52F1AEA1467AB4D82C0(L_0, Factory_Return_TisTaskWait_t3EE9538F13FC35386954C2A0357508C54402C70D_mB42E50EDBE4F1D50308DF52F1AEA1467AB4D82C0_RuntimeMethod_var);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Queue_1_t1753017E9AC02C107096B1F64F9CA7D8066B3F68* SequentialScheduler_get__Tasks_mB83D22B61EB664FFBE0E0E07522B028B99171824 (SequentialScheduler_tA81A2FC813C06F461FF491C537503D1A0700832B* __this, const RuntimeMethod* method) 
{
	{
		Queue_1_t1753017E9AC02C107096B1F64F9CA7D8066B3F68* L_0 = __this->___U3C_TasksU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SequentialScheduler_set__Tasks_mCF19B4F5391A29FB14661271837D793DDBBB4100 (SequentialScheduler_tA81A2FC813C06F461FF491C537503D1A0700832B* __this, Queue_1_t1753017E9AC02C107096B1F64F9CA7D8066B3F68* ___0_value, const RuntimeMethod* method) 
{
	{
		Queue_1_t1753017E9AC02C107096B1F64F9CA7D8066B3F68* L_0 = ___0_value;
		__this->___U3C_TasksU3Ek__BackingField = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___U3C_TasksU3Ek__BackingField), (void*)L_0);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t SequentialScheduler_get_Count_m6DE625C63B7F644F6CC5FB21ECE7B69ADD24B5B6 (SequentialScheduler_tA81A2FC813C06F461FF491C537503D1A0700832B* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Queue_1_get_Count_m2E7A67F6083221FC900A76A6AE2DD99EAB10CEB5_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		RuntimeObject* L_0 = __this->____RunningTask;
		if (!L_0)
		{
			goto IL_0016;
		}
	}
	{
		Queue_1_t1753017E9AC02C107096B1F64F9CA7D8066B3F68* L_1;
		L_1 = SequentialScheduler_get__Tasks_mB83D22B61EB664FFBE0E0E07522B028B99171824_inline(__this, NULL);
		NullCheck(L_1);
		int32_t L_2;
		L_2 = Queue_1_get_Count_m2E7A67F6083221FC900A76A6AE2DD99EAB10CEB5_inline(L_1, Queue_1_get_Count_m2E7A67F6083221FC900A76A6AE2DD99EAB10CEB5_RuntimeMethod_var);
		return ((int32_t)il2cpp_codegen_add(L_2, 1));
	}

IL_0016:
	{
		Queue_1_t1753017E9AC02C107096B1F64F9CA7D8066B3F68* L_3;
		L_3 = SequentialScheduler_get__Tasks_mB83D22B61EB664FFBE0E0E07522B028B99171824_inline(__this, NULL);
		NullCheck(L_3);
		int32_t L_4;
		L_4 = Queue_1_get_Count_m2E7A67F6083221FC900A76A6AE2DD99EAB10CEB5_inline(L_3, Queue_1_get_Count_m2E7A67F6083221FC900A76A6AE2DD99EAB10CEB5_RuntimeMethod_var);
		return L_4;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float SequentialScheduler_get_Progress_m7D8F7C759715DD282FE64A2A037EFF57679C5E69 (SequentialScheduler_tA81A2FC813C06F461FF491C537503D1A0700832B* __this, const RuntimeMethod* method) 
{
	{
		float L_0 = __this->____CurProgress;
		float L_1 = __this->____TotalProgress;
		return ((float)(L_0/L_1));
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SequentialScheduler_Reset_mA2BB284E6A00F6BE2FEC49883FFDED1BEB290194 (SequentialScheduler_tA81A2FC813C06F461FF491C537503D1A0700832B* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Enumerator_Dispose_m03E0C0F3E267D499A2042C972641859F55CA5FCB_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Enumerator_MoveNext_m4D2FA9D284D9CC942BDDBA2136D35BA4E3C56912_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Enumerator_get_Current_m9C237223D7642D034FE235121A5063E40C0CD0D0_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ITask_tCF9AE3E9C450764F84E261819B70401C27BDE6C6_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Queue_1_Clear_m6110B566D9B8D376BFCFF72D3FE9FF88B118E026_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Queue_1_GetEnumerator_m9F588D95754FFCD6F96CEC48D8970EB912112FFE_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	Enumerator_t8A456E1C05D8FE1CF2370E2519618D4ACF57D6F3 V_0;
	memset((&V_0), 0, sizeof(V_0));
	float V_1 = 0.0f;
	{
		Queue_1_t1753017E9AC02C107096B1F64F9CA7D8066B3F68* L_0;
		L_0 = SequentialScheduler_get__Tasks_mB83D22B61EB664FFBE0E0E07522B028B99171824_inline(__this, NULL);
		NullCheck(L_0);
		Enumerator_t8A456E1C05D8FE1CF2370E2519618D4ACF57D6F3 L_1;
		L_1 = Queue_1_GetEnumerator_m9F588D95754FFCD6F96CEC48D8970EB912112FFE(L_0, Queue_1_GetEnumerator_m9F588D95754FFCD6F96CEC48D8970EB912112FFE_RuntimeMethod_var);
		V_0 = L_1;
	}
	{
		auto __finallyBlock = il2cpp::utils::Finally([&]
		{

FINALLY_0025:
			{
				Enumerator_Dispose_m03E0C0F3E267D499A2042C972641859F55CA5FCB((&V_0), Enumerator_Dispose_m03E0C0F3E267D499A2042C972641859F55CA5FCB_RuntimeMethod_var);
				return;
			}
		});
		try
		{
			{
				goto IL_001a_1;
			}

IL_000e_1:
			{
				RuntimeObject* L_2;
				L_2 = Enumerator_get_Current_m9C237223D7642D034FE235121A5063E40C0CD0D0((&V_0), Enumerator_get_Current_m9C237223D7642D034FE235121A5063E40C0CD0D0_RuntimeMethod_var);
				NullCheck(L_2);
				InterfaceActionInvoker0::Invoke(4, ITask_tCF9AE3E9C450764F84E261819B70401C27BDE6C6_il2cpp_TypeInfo_var, L_2);
			}

IL_001a_1:
			{
				bool L_3;
				L_3 = Enumerator_MoveNext_m4D2FA9D284D9CC942BDDBA2136D35BA4E3C56912((&V_0), Enumerator_MoveNext_m4D2FA9D284D9CC942BDDBA2136D35BA4E3C56912_RuntimeMethod_var);
				if (L_3)
				{
					goto IL_000e_1;
				}
			}
			{
				goto IL_0033;
			}
		}
		catch(Il2CppExceptionWrapper& e)
		{
			__finallyBlock.StoreException(e.ex);
		}
	}

IL_0033:
	{
		Queue_1_t1753017E9AC02C107096B1F64F9CA7D8066B3F68* L_4;
		L_4 = SequentialScheduler_get__Tasks_mB83D22B61EB664FFBE0E0E07522B028B99171824_inline(__this, NULL);
		NullCheck(L_4);
		Queue_1_Clear_m6110B566D9B8D376BFCFF72D3FE9FF88B118E026(L_4, Queue_1_Clear_m6110B566D9B8D376BFCFF72D3FE9FF88B118E026_RuntimeMethod_var);
		__this->____RunningTask = (RuntimeObject*)NULL;
		Il2CppCodeGenWriteBarrier((void**)(&__this->____RunningTask), (void*)(RuntimeObject*)NULL);
		float L_5 = (0.0f);
		V_1 = L_5;
		__this->____TotalProgress = L_5;
		float L_6 = V_1;
		__this->____CurProgress = L_6;
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* SequentialScheduler_Append_m71D5B36A865941635E94CA27D32053341561AF8E (SequentialScheduler_tA81A2FC813C06F461FF491C537503D1A0700832B* __this, RuntimeObject* ___0_task, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Queue_1_Enqueue_mE30AFD66E5DB5F92244805EF4E66CB554B2FEF95_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Queue_1_t1753017E9AC02C107096B1F64F9CA7D8066B3F68* L_0;
		L_0 = SequentialScheduler_get__Tasks_mB83D22B61EB664FFBE0E0E07522B028B99171824_inline(__this, NULL);
		RuntimeObject* L_1 = ___0_task;
		NullCheck(L_0);
		Queue_1_Enqueue_mE30AFD66E5DB5F92244805EF4E66CB554B2FEF95(L_0, L_1, Queue_1_Enqueue_mE30AFD66E5DB5F92244805EF4E66CB554B2FEF95_RuntimeMethod_var);
		float L_2 = __this->____TotalProgress;
		__this->____TotalProgress = ((float)il2cpp_codegen_add(L_2, (1.0f)));
		float L_3 = __this->____TotalProgress;
		__this->____SubTotal = ((float)((1.0f)/L_3));
		return __this;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool SequentialScheduler_Execute_m0EDFFE2A88BB93B59C2BCD50AD9742132B6CFDB0 (SequentialScheduler_tA81A2FC813C06F461FF491C537503D1A0700832B* __this, float ___0_deltaTime, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ITask_tCF9AE3E9C450764F84E261819B70401C27BDE6C6_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Queue_1_Dequeue_mB8DA6E612E1335E5EBBBD1E30FB265B20BDC7220_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Queue_1_get_Count_m2E7A67F6083221FC900A76A6AE2DD99EAB10CEB5_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		RuntimeObject* L_0 = __this->____RunningTask;
		if (!L_0)
		{
			goto IL_005f;
		}
	}
	{
		RuntimeObject* L_1 = __this->____RunningTask;
		NullCheck(L_1);
		bool L_2;
		L_2 = InterfaceFuncInvoker0< bool >::Invoke(0, ITask_tCF9AE3E9C450764F84E261819B70401C27BDE6C6_il2cpp_TypeInfo_var, L_1);
		if (L_2)
		{
			goto IL_003b;
		}
	}
	{
		RuntimeObject* L_3 = __this->____RunningTask;
		float L_4 = ___0_deltaTime;
		NullCheck(L_3);
		InterfaceActionInvoker1< float >::Invoke(3, ITask_tCF9AE3E9C450764F84E261819B70401C27BDE6C6_il2cpp_TypeInfo_var, L_3, L_4);
		RuntimeObject* L_5 = __this->____RunningTask;
		NullCheck(L_5);
		float L_6;
		L_6 = InterfaceFuncInvoker0< float >::Invoke(1, ITask_tCF9AE3E9C450764F84E261819B70401C27BDE6C6_il2cpp_TypeInfo_var, L_5);
		float L_7 = __this->____SubTotal;
		__this->____CurProgress = ((float)il2cpp_codegen_multiply(L_6, L_7));
		return (bool)0;
	}

IL_003b:
	{
		RuntimeObject* L_8 = __this->____RunningTask;
		NullCheck(L_8);
		InterfaceActionInvoker0::Invoke(4, ITask_tCF9AE3E9C450764F84E261819B70401C27BDE6C6_il2cpp_TypeInfo_var, L_8);
		__this->____RunningTask = (RuntimeObject*)NULL;
		Il2CppCodeGenWriteBarrier((void**)(&__this->____RunningTask), (void*)(RuntimeObject*)NULL);
		float L_9 = __this->____TotalProgress;
		__this->____TotalProgress = ((float)il2cpp_codegen_subtract(L_9, (1.0f)));
	}

IL_005f:
	{
		Queue_1_t1753017E9AC02C107096B1F64F9CA7D8066B3F68* L_10;
		L_10 = SequentialScheduler_get__Tasks_mB83D22B61EB664FFBE0E0E07522B028B99171824_inline(__this, NULL);
		NullCheck(L_10);
		int32_t L_11;
		L_11 = Queue_1_get_Count_m2E7A67F6083221FC900A76A6AE2DD99EAB10CEB5_inline(L_10, Queue_1_get_Count_m2E7A67F6083221FC900A76A6AE2DD99EAB10CEB5_RuntimeMethod_var);
		if ((((int32_t)L_11) <= ((int32_t)0)))
		{
			goto IL_00a3;
		}
	}
	{
		Queue_1_t1753017E9AC02C107096B1F64F9CA7D8066B3F68* L_12;
		L_12 = SequentialScheduler_get__Tasks_mB83D22B61EB664FFBE0E0E07522B028B99171824_inline(__this, NULL);
		NullCheck(L_12);
		RuntimeObject* L_13;
		L_13 = Queue_1_Dequeue_mB8DA6E612E1335E5EBBBD1E30FB265B20BDC7220(L_12, Queue_1_Dequeue_mB8DA6E612E1335E5EBBBD1E30FB265B20BDC7220_RuntimeMethod_var);
		__this->____RunningTask = L_13;
		Il2CppCodeGenWriteBarrier((void**)(&__this->____RunningTask), (void*)L_13);
		RuntimeObject* L_14 = __this->____RunningTask;
		NullCheck(L_14);
		InterfaceActionInvoker0::Invoke(2, ITask_tCF9AE3E9C450764F84E261819B70401C27BDE6C6_il2cpp_TypeInfo_var, L_14);
		RuntimeObject* L_15 = __this->____RunningTask;
		NullCheck(L_15);
		float L_16;
		L_16 = InterfaceFuncInvoker0< float >::Invoke(1, ITask_tCF9AE3E9C450764F84E261819B70401C27BDE6C6_il2cpp_TypeInfo_var, L_15);
		float L_17 = __this->____SubTotal;
		__this->____CurProgress = ((float)il2cpp_codegen_multiply(L_16, L_17));
		return (bool)0;
	}

IL_00a3:
	{
		return (bool)1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SequentialScheduler_StartFrom_m5C13620E5B1E72D5DD4C9B989FC69F908EA841D5 (SequentialScheduler_tA81A2FC813C06F461FF491C537503D1A0700832B* __this, int32_t ___0_taskIndex, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Queue_1_get_Count_m2E7A67F6083221FC900A76A6AE2DD99EAB10CEB5_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		int32_t L_0 = ___0_taskIndex;
		if ((((int32_t)L_0) < ((int32_t)0)))
		{
			goto IL_0012;
		}
	}
	{
		int32_t L_1 = ___0_taskIndex;
		Queue_1_t1753017E9AC02C107096B1F64F9CA7D8066B3F68* L_2;
		L_2 = SequentialScheduler_get__Tasks_mB83D22B61EB664FFBE0E0E07522B028B99171824_inline(__this, NULL);
		NullCheck(L_2);
		int32_t L_3;
		L_3 = Queue_1_get_Count_m2E7A67F6083221FC900A76A6AE2DD99EAB10CEB5_inline(L_2, Queue_1_get_Count_m2E7A67F6083221FC900A76A6AE2DD99EAB10CEB5_RuntimeMethod_var);
		if ((((int32_t)L_1) < ((int32_t)L_3)))
		{
			goto IL_0022;
		}
	}

IL_0012:
	{
		ArgumentOutOfRangeException_tEA2822DAF62B10EEED00E0E3A341D4BAF78CF85F* L_4 = (ArgumentOutOfRangeException_tEA2822DAF62B10EEED00E0E3A341D4BAF78CF85F*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ArgumentOutOfRangeException_tEA2822DAF62B10EEED00E0E3A341D4BAF78CF85F_il2cpp_TypeInfo_var)));
		ArgumentOutOfRangeException__ctor_mE5B2755F0BEA043CACF915D5CE140859EE58FA66(L_4, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteralAC03F829E9BDCFEC7BE22227379C137652703B65)), ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteralBC395BD5F69A0C8E0C05428831D01B1729D0C08C)), NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_4, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&SequentialScheduler_StartFrom_m5C13620E5B1E72D5DD4C9B989FC69F908EA841D5_RuntimeMethod_var)));
	}

IL_0022:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void SequentialScheduler__ctor_m2E66B6C67B5AA19116ECFB5DE94CE89DC365BCAE (SequentialScheduler_tA81A2FC813C06F461FF491C537503D1A0700832B* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Queue_1__ctor_mFC3EB5A37E7A565A7833603DC722ACD9C4269AC5_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Queue_1_t1753017E9AC02C107096B1F64F9CA7D8066B3F68_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Queue_1_t1753017E9AC02C107096B1F64F9CA7D8066B3F68* L_0 = (Queue_1_t1753017E9AC02C107096B1F64F9CA7D8066B3F68*)il2cpp_codegen_object_new(Queue_1_t1753017E9AC02C107096B1F64F9CA7D8066B3F68_il2cpp_TypeInfo_var);
		Queue_1__ctor_mFC3EB5A37E7A565A7833603DC722ACD9C4269AC5(L_0, Queue_1__ctor_mFC3EB5A37E7A565A7833603DC722ACD9C4269AC5_RuntimeMethod_var);
		__this->___U3C_TasksU3Ek__BackingField = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___U3C_TasksU3Ek__BackingField), (void*)L_0);
		__this->____SubTotal = (1.0f);
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR List_1_t930598996D1A93CCE7F51C4E2B0CC9645E9B8113* ParallelScheduler_get__Tasks_mC96C0BC294957F039A8E9D699E56F562886BD0F7 (ParallelScheduler_t8379A00B9D56775018796CE4487866984ED97D4E* __this, const RuntimeMethod* method) 
{
	{
		List_1_t930598996D1A93CCE7F51C4E2B0CC9645E9B8113* L_0 = __this->___U3C_TasksU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ParallelScheduler_set__Tasks_m206C7AB318DD256547A83C04C7D52402AB7D7C30 (ParallelScheduler_t8379A00B9D56775018796CE4487866984ED97D4E* __this, List_1_t930598996D1A93CCE7F51C4E2B0CC9645E9B8113* ___0_value, const RuntimeMethod* method) 
{
	{
		List_1_t930598996D1A93CCE7F51C4E2B0CC9645E9B8113* L_0 = ___0_value;
		__this->___U3C_TasksU3Ek__BackingField = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___U3C_TasksU3Ek__BackingField), (void*)L_0);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t ParallelScheduler_get_Count_mEBDC238B524EFCB3CF5E41979FAE67E919CB7686 (ParallelScheduler_t8379A00B9D56775018796CE4487866984ED97D4E* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ITask_tCF9AE3E9C450764F84E261819B70401C27BDE6C6_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_get_Count_m1CA38F17002C61754AC244EF759BA231250CA54A_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_get_Item_m32546B9C12BB96B1EB358899E1EB3B5BD6A33A49_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	int32_t V_0 = 0;
	int32_t V_1 = 0;
	{
		V_0 = 0;
		V_1 = 0;
		goto IL_0021;
	}

IL_0006:
	{
		List_1_t930598996D1A93CCE7F51C4E2B0CC9645E9B8113* L_0;
		L_0 = ParallelScheduler_get__Tasks_mC96C0BC294957F039A8E9D699E56F562886BD0F7_inline(__this, NULL);
		int32_t L_1 = V_1;
		NullCheck(L_0);
		RuntimeObject* L_2;
		L_2 = List_1_get_Item_m32546B9C12BB96B1EB358899E1EB3B5BD6A33A49(L_0, L_1, List_1_get_Item_m32546B9C12BB96B1EB358899E1EB3B5BD6A33A49_RuntimeMethod_var);
		NullCheck(L_2);
		bool L_3;
		L_3 = InterfaceFuncInvoker0< bool >::Invoke(0, ITask_tCF9AE3E9C450764F84E261819B70401C27BDE6C6_il2cpp_TypeInfo_var, L_2);
		if (L_3)
		{
			goto IL_001d;
		}
	}
	{
		int32_t L_4 = V_0;
		V_0 = ((int32_t)il2cpp_codegen_add(L_4, 1));
	}

IL_001d:
	{
		int32_t L_5 = V_1;
		V_1 = ((int32_t)il2cpp_codegen_add(L_5, 1));
	}

IL_0021:
	{
		int32_t L_6 = V_1;
		List_1_t930598996D1A93CCE7F51C4E2B0CC9645E9B8113* L_7;
		L_7 = ParallelScheduler_get__Tasks_mC96C0BC294957F039A8E9D699E56F562886BD0F7_inline(__this, NULL);
		NullCheck(L_7);
		int32_t L_8;
		L_8 = List_1_get_Count_m1CA38F17002C61754AC244EF759BA231250CA54A_inline(L_7, List_1_get_Count_m1CA38F17002C61754AC244EF759BA231250CA54A_RuntimeMethod_var);
		if ((((int32_t)L_6) < ((int32_t)L_8)))
		{
			goto IL_0006;
		}
	}
	{
		int32_t L_9 = V_0;
		return L_9;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float ParallelScheduler_get_Progress_mEED41EFAF684B6B300EA8B9FE9BFB7C822C5B072 (ParallelScheduler_t8379A00B9D56775018796CE4487866984ED97D4E* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ITask_tCF9AE3E9C450764F84E261819B70401C27BDE6C6_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_get_Count_m1CA38F17002C61754AC244EF759BA231250CA54A_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_get_Item_m32546B9C12BB96B1EB358899E1EB3B5BD6A33A49_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	float V_0 = 0.0f;
	int32_t V_1 = 0;
	{
		V_0 = (0.0f);
		V_1 = 0;
		goto IL_0022;
	}

IL_000a:
	{
		float L_0 = V_0;
		List_1_t930598996D1A93CCE7F51C4E2B0CC9645E9B8113* L_1;
		L_1 = ParallelScheduler_get__Tasks_mC96C0BC294957F039A8E9D699E56F562886BD0F7_inline(__this, NULL);
		int32_t L_2 = V_1;
		NullCheck(L_1);
		RuntimeObject* L_3;
		L_3 = List_1_get_Item_m32546B9C12BB96B1EB358899E1EB3B5BD6A33A49(L_1, L_2, List_1_get_Item_m32546B9C12BB96B1EB358899E1EB3B5BD6A33A49_RuntimeMethod_var);
		NullCheck(L_3);
		float L_4;
		L_4 = InterfaceFuncInvoker0< float >::Invoke(1, ITask_tCF9AE3E9C450764F84E261819B70401C27BDE6C6_il2cpp_TypeInfo_var, L_3);
		V_0 = ((float)il2cpp_codegen_add(L_0, L_4));
		int32_t L_5 = V_1;
		V_1 = ((int32_t)il2cpp_codegen_add(L_5, 1));
	}

IL_0022:
	{
		int32_t L_6 = V_1;
		List_1_t930598996D1A93CCE7F51C4E2B0CC9645E9B8113* L_7;
		L_7 = ParallelScheduler_get__Tasks_mC96C0BC294957F039A8E9D699E56F562886BD0F7_inline(__this, NULL);
		NullCheck(L_7);
		int32_t L_8;
		L_8 = List_1_get_Count_m1CA38F17002C61754AC244EF759BA231250CA54A_inline(L_7, List_1_get_Count_m1CA38F17002C61754AC244EF759BA231250CA54A_RuntimeMethod_var);
		if ((((int32_t)L_6) < ((int32_t)L_8)))
		{
			goto IL_000a;
		}
	}
	{
		float L_9 = V_0;
		List_1_t930598996D1A93CCE7F51C4E2B0CC9645E9B8113* L_10;
		L_10 = ParallelScheduler_get__Tasks_mC96C0BC294957F039A8E9D699E56F562886BD0F7_inline(__this, NULL);
		NullCheck(L_10);
		int32_t L_11;
		L_11 = List_1_get_Count_m1CA38F17002C61754AC244EF759BA231250CA54A_inline(L_10, List_1_get_Count_m1CA38F17002C61754AC244EF759BA231250CA54A_RuntimeMethod_var);
		return ((float)(L_9/((float)L_11)));
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ParallelScheduler_Reset_mC240DCA97124E58E163F36D1CF1399A1C23A2659 (ParallelScheduler_t8379A00B9D56775018796CE4487866984ED97D4E* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Enumerator_Dispose_m909DFF743AF500AD152AFE317C71F7AFC9EDF6A5_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Enumerator_MoveNext_mD5C0DAF6826810806AFFCB26CAD48DE90D30FA0B_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Enumerator_get_Current_m3FC1302CC110A99701ADC815FE3DF672DD6C3C69_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ITask_tCF9AE3E9C450764F84E261819B70401C27BDE6C6_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_Clear_m75F6D7D2191A3B21E9EFCAAAB61753E03B3CF78C_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_GetEnumerator_m4B2195DBF47EFB502FADE2E39C377C6E068377CA_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	Enumerator_t2558BB0501E98A606E95BEA700B600D43C4995EA V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		List_1_t930598996D1A93CCE7F51C4E2B0CC9645E9B8113* L_0;
		L_0 = ParallelScheduler_get__Tasks_mC96C0BC294957F039A8E9D699E56F562886BD0F7_inline(__this, NULL);
		NullCheck(L_0);
		Enumerator_t2558BB0501E98A606E95BEA700B600D43C4995EA L_1;
		L_1 = List_1_GetEnumerator_m4B2195DBF47EFB502FADE2E39C377C6E068377CA(L_0, List_1_GetEnumerator_m4B2195DBF47EFB502FADE2E39C377C6E068377CA_RuntimeMethod_var);
		V_0 = L_1;
	}
	{
		auto __finallyBlock = il2cpp::utils::Finally([&]
		{

FINALLY_0025:
			{
				Enumerator_Dispose_m909DFF743AF500AD152AFE317C71F7AFC9EDF6A5((&V_0), Enumerator_Dispose_m909DFF743AF500AD152AFE317C71F7AFC9EDF6A5_RuntimeMethod_var);
				return;
			}
		});
		try
		{
			{
				goto IL_001a_1;
			}

IL_000e_1:
			{
				RuntimeObject* L_2;
				L_2 = Enumerator_get_Current_m3FC1302CC110A99701ADC815FE3DF672DD6C3C69_inline((&V_0), Enumerator_get_Current_m3FC1302CC110A99701ADC815FE3DF672DD6C3C69_RuntimeMethod_var);
				NullCheck(L_2);
				InterfaceActionInvoker0::Invoke(4, ITask_tCF9AE3E9C450764F84E261819B70401C27BDE6C6_il2cpp_TypeInfo_var, L_2);
			}

IL_001a_1:
			{
				bool L_3;
				L_3 = Enumerator_MoveNext_mD5C0DAF6826810806AFFCB26CAD48DE90D30FA0B((&V_0), Enumerator_MoveNext_mD5C0DAF6826810806AFFCB26CAD48DE90D30FA0B_RuntimeMethod_var);
				if (L_3)
				{
					goto IL_000e_1;
				}
			}
			{
				goto IL_0033;
			}
		}
		catch(Il2CppExceptionWrapper& e)
		{
			__finallyBlock.StoreException(e.ex);
		}
	}

IL_0033:
	{
		List_1_t930598996D1A93CCE7F51C4E2B0CC9645E9B8113* L_4;
		L_4 = ParallelScheduler_get__Tasks_mC96C0BC294957F039A8E9D699E56F562886BD0F7_inline(__this, NULL);
		NullCheck(L_4);
		List_1_Clear_m75F6D7D2191A3B21E9EFCAAAB61753E03B3CF78C_inline(L_4, List_1_Clear_m75F6D7D2191A3B21E9EFCAAAB61753E03B3CF78C_RuntimeMethod_var);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* ParallelScheduler_Append_m448C28DC5849BE41A8901850DC266ED7277DCD7C (ParallelScheduler_t8379A00B9D56775018796CE4487866984ED97D4E* __this, RuntimeObject* ___0_task, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_Add_mD87DFAD1A3DDEB06156BFFE81A9C7F9D6D3D74C1_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		List_1_t930598996D1A93CCE7F51C4E2B0CC9645E9B8113* L_0;
		L_0 = ParallelScheduler_get__Tasks_mC96C0BC294957F039A8E9D699E56F562886BD0F7_inline(__this, NULL);
		RuntimeObject* L_1 = ___0_task;
		NullCheck(L_0);
		List_1_Add_mD87DFAD1A3DDEB06156BFFE81A9C7F9D6D3D74C1_inline(L_0, L_1, List_1_Add_mD87DFAD1A3DDEB06156BFFE81A9C7F9D6D3D74C1_RuntimeMethod_var);
		return __this;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool ParallelScheduler_Execute_m128D59B9385566909843E65998A283B90260D39D (ParallelScheduler_t8379A00B9D56775018796CE4487866984ED97D4E* __this, float ___0_deltaTime, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ITask_tCF9AE3E9C450764F84E261819B70401C27BDE6C6_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_get_Count_m1CA38F17002C61754AC244EF759BA231250CA54A_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_get_Item_m32546B9C12BB96B1EB358899E1EB3B5BD6A33A49_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	bool V_0 = false;
	int32_t V_1 = 0;
	{
		V_0 = (bool)1;
		V_1 = 0;
		goto IL_005c;
	}

IL_0006:
	{
		List_1_t930598996D1A93CCE7F51C4E2B0CC9645E9B8113* L_0;
		L_0 = ParallelScheduler_get__Tasks_mC96C0BC294957F039A8E9D699E56F562886BD0F7_inline(__this, NULL);
		int32_t L_1 = V_1;
		NullCheck(L_0);
		RuntimeObject* L_2;
		L_2 = List_1_get_Item_m32546B9C12BB96B1EB358899E1EB3B5BD6A33A49(L_0, L_1, List_1_get_Item_m32546B9C12BB96B1EB358899E1EB3B5BD6A33A49_RuntimeMethod_var);
		NullCheck(L_2);
		bool L_3;
		L_3 = InterfaceFuncInvoker0< bool >::Invoke(0, ITask_tCF9AE3E9C450764F84E261819B70401C27BDE6C6_il2cpp_TypeInfo_var, L_2);
		if (L_3)
		{
			goto IL_0058;
		}
	}
	{
		V_0 = (bool)0;
		List_1_t930598996D1A93CCE7F51C4E2B0CC9645E9B8113* L_4;
		L_4 = ParallelScheduler_get__Tasks_mC96C0BC294957F039A8E9D699E56F562886BD0F7_inline(__this, NULL);
		int32_t L_5 = V_1;
		NullCheck(L_4);
		RuntimeObject* L_6;
		L_6 = List_1_get_Item_m32546B9C12BB96B1EB358899E1EB3B5BD6A33A49(L_4, L_5, List_1_get_Item_m32546B9C12BB96B1EB358899E1EB3B5BD6A33A49_RuntimeMethod_var);
		NullCheck(L_6);
		float L_7;
		L_7 = InterfaceFuncInvoker0< float >::Invoke(1, ITask_tCF9AE3E9C450764F84E261819B70401C27BDE6C6_il2cpp_TypeInfo_var, L_6);
		if ((!(((float)L_7) <= ((float)(0.0f)))))
		{
			goto IL_0046;
		}
	}
	{
		List_1_t930598996D1A93CCE7F51C4E2B0CC9645E9B8113* L_8;
		L_8 = ParallelScheduler_get__Tasks_mC96C0BC294957F039A8E9D699E56F562886BD0F7_inline(__this, NULL);
		int32_t L_9 = V_1;
		NullCheck(L_8);
		RuntimeObject* L_10;
		L_10 = List_1_get_Item_m32546B9C12BB96B1EB358899E1EB3B5BD6A33A49(L_8, L_9, List_1_get_Item_m32546B9C12BB96B1EB358899E1EB3B5BD6A33A49_RuntimeMethod_var);
		NullCheck(L_10);
		InterfaceActionInvoker0::Invoke(2, ITask_tCF9AE3E9C450764F84E261819B70401C27BDE6C6_il2cpp_TypeInfo_var, L_10);
		goto IL_0058;
	}

IL_0046:
	{
		List_1_t930598996D1A93CCE7F51C4E2B0CC9645E9B8113* L_11;
		L_11 = ParallelScheduler_get__Tasks_mC96C0BC294957F039A8E9D699E56F562886BD0F7_inline(__this, NULL);
		int32_t L_12 = V_1;
		NullCheck(L_11);
		RuntimeObject* L_13;
		L_13 = List_1_get_Item_m32546B9C12BB96B1EB358899E1EB3B5BD6A33A49(L_11, L_12, List_1_get_Item_m32546B9C12BB96B1EB358899E1EB3B5BD6A33A49_RuntimeMethod_var);
		float L_14 = ___0_deltaTime;
		NullCheck(L_13);
		InterfaceActionInvoker1< float >::Invoke(3, ITask_tCF9AE3E9C450764F84E261819B70401C27BDE6C6_il2cpp_TypeInfo_var, L_13, L_14);
	}

IL_0058:
	{
		int32_t L_15 = V_1;
		V_1 = ((int32_t)il2cpp_codegen_add(L_15, 1));
	}

IL_005c:
	{
		int32_t L_16 = V_1;
		List_1_t930598996D1A93CCE7F51C4E2B0CC9645E9B8113* L_17;
		L_17 = ParallelScheduler_get__Tasks_mC96C0BC294957F039A8E9D699E56F562886BD0F7_inline(__this, NULL);
		NullCheck(L_17);
		int32_t L_18;
		L_18 = List_1_get_Count_m1CA38F17002C61754AC244EF759BA231250CA54A_inline(L_17, List_1_get_Count_m1CA38F17002C61754AC244EF759BA231250CA54A_RuntimeMethod_var);
		if ((((int32_t)L_16) < ((int32_t)L_18)))
		{
			goto IL_0006;
		}
	}
	{
		bool L_19 = V_0;
		return L_19;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ParallelScheduler__ctor_mD5F54A9085D280823C0C888E458E10AF21F8F0B5 (ParallelScheduler_t8379A00B9D56775018796CE4487866984ED97D4E* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1__ctor_m7B7D12AA36992ED80132FBEEE676F8C267664A97_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_t930598996D1A93CCE7F51C4E2B0CC9645E9B8113_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		List_1_t930598996D1A93CCE7F51C4E2B0CC9645E9B8113* L_0 = (List_1_t930598996D1A93CCE7F51C4E2B0CC9645E9B8113*)il2cpp_codegen_object_new(List_1_t930598996D1A93CCE7F51C4E2B0CC9645E9B8113_il2cpp_TypeInfo_var);
		List_1__ctor_m7B7D12AA36992ED80132FBEEE676F8C267664A97(L_0, List_1__ctor_m7B7D12AA36992ED80132FBEEE676F8C267664A97_RuntimeMethod_var);
		__this->___U3C_TasksU3Ek__BackingField = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___U3C_TasksU3Ek__BackingField), (void*)L_0);
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR List_1_t34E53346ADC531C7809398BA078DED1E182FEF4C* CombinedScheduler_get__Schedulers_m29051BFB881A5D5FD9F106D462C3970259B866EC (CombinedScheduler_tC9BC117BCE01E5068E0E5F7D76183ED3A2DFA0A8* __this, const RuntimeMethod* method) 
{
	{
		List_1_t34E53346ADC531C7809398BA078DED1E182FEF4C* L_0 = __this->___U3C_SchedulersU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void CombinedScheduler_set__Schedulers_m405C6637EE371CA3714963884FAFB7A864673D4B (CombinedScheduler_tC9BC117BCE01E5068E0E5F7D76183ED3A2DFA0A8* __this, List_1_t34E53346ADC531C7809398BA078DED1E182FEF4C* ___0_value, const RuntimeMethod* method) 
{
	{
		List_1_t34E53346ADC531C7809398BA078DED1E182FEF4C* L_0 = ___0_value;
		__this->___U3C_SchedulersU3Ek__BackingField = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___U3C_SchedulersU3Ek__BackingField), (void*)L_0);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t CombinedScheduler_get_Count_m4D45A55C53B5A74194916125792A3F45CEACD87F (CombinedScheduler_tC9BC117BCE01E5068E0E5F7D76183ED3A2DFA0A8* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IScheduler_tBA89D3CBD4600EE0421A94BCE19EC693B9E6678A_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_get_Count_mD2E9EFE20B0EEC3C6831330B6462B95B5218982A_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_get_Item_m5A3F4B3F4312888A93224C5CF7BEFCB5DE5342CB_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	int32_t V_0 = 0;
	int32_t V_1 = 0;
	{
		V_0 = 0;
		V_1 = 0;
		goto IL_001e;
	}

IL_0006:
	{
		int32_t L_0 = V_0;
		List_1_t34E53346ADC531C7809398BA078DED1E182FEF4C* L_1;
		L_1 = CombinedScheduler_get__Schedulers_m29051BFB881A5D5FD9F106D462C3970259B866EC_inline(__this, NULL);
		int32_t L_2 = V_1;
		NullCheck(L_1);
		RuntimeObject* L_3;
		L_3 = List_1_get_Item_m5A3F4B3F4312888A93224C5CF7BEFCB5DE5342CB(L_1, L_2, List_1_get_Item_m5A3F4B3F4312888A93224C5CF7BEFCB5DE5342CB_RuntimeMethod_var);
		NullCheck(L_3);
		int32_t L_4;
		L_4 = InterfaceFuncInvoker0< int32_t >::Invoke(0, IScheduler_tBA89D3CBD4600EE0421A94BCE19EC693B9E6678A_il2cpp_TypeInfo_var, L_3);
		V_0 = ((int32_t)il2cpp_codegen_add(L_0, L_4));
		int32_t L_5 = V_1;
		V_1 = ((int32_t)il2cpp_codegen_add(L_5, 1));
	}

IL_001e:
	{
		int32_t L_6 = V_1;
		List_1_t34E53346ADC531C7809398BA078DED1E182FEF4C* L_7;
		L_7 = CombinedScheduler_get__Schedulers_m29051BFB881A5D5FD9F106D462C3970259B866EC_inline(__this, NULL);
		NullCheck(L_7);
		int32_t L_8;
		L_8 = List_1_get_Count_mD2E9EFE20B0EEC3C6831330B6462B95B5218982A_inline(L_7, List_1_get_Count_mD2E9EFE20B0EEC3C6831330B6462B95B5218982A_RuntimeMethod_var);
		if ((((int32_t)L_6) < ((int32_t)L_8)))
		{
			goto IL_0006;
		}
	}
	{
		int32_t L_9 = V_0;
		return L_9;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float CombinedScheduler_get_Progress_m59D06D92A29D308D3EBFCA8D447A25BF892A78D0 (CombinedScheduler_tC9BC117BCE01E5068E0E5F7D76183ED3A2DFA0A8* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IScheduler_tBA89D3CBD4600EE0421A94BCE19EC693B9E6678A_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_get_Count_mD2E9EFE20B0EEC3C6831330B6462B95B5218982A_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_get_Item_m5A3F4B3F4312888A93224C5CF7BEFCB5DE5342CB_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	float V_0 = 0.0f;
	int32_t V_1 = 0;
	float G_B4_0 = 0.0f;
	{
		V_0 = ((std::numeric_limits<float>::max)());
		V_1 = 0;
		goto IL_0037;
	}

IL_000a:
	{
		List_1_t34E53346ADC531C7809398BA078DED1E182FEF4C* L_0;
		L_0 = CombinedScheduler_get__Schedulers_m29051BFB881A5D5FD9F106D462C3970259B866EC_inline(__this, NULL);
		int32_t L_1 = V_1;
		NullCheck(L_0);
		RuntimeObject* L_2;
		L_2 = List_1_get_Item_m5A3F4B3F4312888A93224C5CF7BEFCB5DE5342CB(L_0, L_1, List_1_get_Item_m5A3F4B3F4312888A93224C5CF7BEFCB5DE5342CB_RuntimeMethod_var);
		NullCheck(L_2);
		float L_3;
		L_3 = InterfaceFuncInvoker0< float >::Invoke(1, IScheduler_tBA89D3CBD4600EE0421A94BCE19EC693B9E6678A_il2cpp_TypeInfo_var, L_2);
		float L_4 = V_0;
		if ((((float)L_3) < ((float)L_4)))
		{
			goto IL_0021;
		}
	}
	{
		float L_5 = V_0;
		G_B4_0 = L_5;
		goto IL_0032;
	}

IL_0021:
	{
		List_1_t34E53346ADC531C7809398BA078DED1E182FEF4C* L_6;
		L_6 = CombinedScheduler_get__Schedulers_m29051BFB881A5D5FD9F106D462C3970259B866EC_inline(__this, NULL);
		int32_t L_7 = V_1;
		NullCheck(L_6);
		RuntimeObject* L_8;
		L_8 = List_1_get_Item_m5A3F4B3F4312888A93224C5CF7BEFCB5DE5342CB(L_6, L_7, List_1_get_Item_m5A3F4B3F4312888A93224C5CF7BEFCB5DE5342CB_RuntimeMethod_var);
		NullCheck(L_8);
		float L_9;
		L_9 = InterfaceFuncInvoker0< float >::Invoke(1, IScheduler_tBA89D3CBD4600EE0421A94BCE19EC693B9E6678A_il2cpp_TypeInfo_var, L_8);
		G_B4_0 = L_9;
	}

IL_0032:
	{
		V_0 = G_B4_0;
		int32_t L_10 = V_1;
		V_1 = ((int32_t)il2cpp_codegen_add(L_10, 1));
	}

IL_0037:
	{
		int32_t L_11 = V_1;
		List_1_t34E53346ADC531C7809398BA078DED1E182FEF4C* L_12;
		L_12 = CombinedScheduler_get__Schedulers_m29051BFB881A5D5FD9F106D462C3970259B866EC_inline(__this, NULL);
		NullCheck(L_12);
		int32_t L_13;
		L_13 = List_1_get_Count_mD2E9EFE20B0EEC3C6831330B6462B95B5218982A_inline(L_12, List_1_get_Count_mD2E9EFE20B0EEC3C6831330B6462B95B5218982A_RuntimeMethod_var);
		if ((((int32_t)L_11) < ((int32_t)L_13)))
		{
			goto IL_000a;
		}
	}
	{
		float L_14 = V_0;
		return L_14;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void CombinedScheduler_Reset_m27C8F150A6AA8259344542A17E28B1E9F91D773A (CombinedScheduler_tC9BC117BCE01E5068E0E5F7D76183ED3A2DFA0A8* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IScheduler_tBA89D3CBD4600EE0421A94BCE19EC693B9E6678A_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_get_Count_mD2E9EFE20B0EEC3C6831330B6462B95B5218982A_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_get_Item_m5A3F4B3F4312888A93224C5CF7BEFCB5DE5342CB_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	int32_t V_0 = 0;
	{
		V_0 = 0;
		goto IL_0019;
	}

IL_0004:
	{
		List_1_t34E53346ADC531C7809398BA078DED1E182FEF4C* L_0;
		L_0 = CombinedScheduler_get__Schedulers_m29051BFB881A5D5FD9F106D462C3970259B866EC_inline(__this, NULL);
		int32_t L_1 = V_0;
		NullCheck(L_0);
		RuntimeObject* L_2;
		L_2 = List_1_get_Item_m5A3F4B3F4312888A93224C5CF7BEFCB5DE5342CB(L_0, L_1, List_1_get_Item_m5A3F4B3F4312888A93224C5CF7BEFCB5DE5342CB_RuntimeMethod_var);
		NullCheck(L_2);
		InterfaceActionInvoker0::Invoke(2, IScheduler_tBA89D3CBD4600EE0421A94BCE19EC693B9E6678A_il2cpp_TypeInfo_var, L_2);
		int32_t L_3 = V_0;
		V_0 = ((int32_t)il2cpp_codegen_add(L_3, 1));
	}

IL_0019:
	{
		int32_t L_4 = V_0;
		List_1_t34E53346ADC531C7809398BA078DED1E182FEF4C* L_5;
		L_5 = CombinedScheduler_get__Schedulers_m29051BFB881A5D5FD9F106D462C3970259B866EC_inline(__this, NULL);
		NullCheck(L_5);
		int32_t L_6;
		L_6 = List_1_get_Count_mD2E9EFE20B0EEC3C6831330B6462B95B5218982A_inline(L_5, List_1_get_Count_mD2E9EFE20B0EEC3C6831330B6462B95B5218982A_RuntimeMethod_var);
		if ((((int32_t)L_4) < ((int32_t)L_6)))
		{
			goto IL_0004;
		}
	}
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* CombinedScheduler_Append_m1283EDAC2EAA44159839CA169C8B7CB940D177CD (CombinedScheduler_tC9BC117BCE01E5068E0E5F7D76183ED3A2DFA0A8* __this, RuntimeObject* ___0_scheduler, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_Add_m9652946599533509ECAE57F6A3AD6B4AC39FFA8C_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		List_1_t34E53346ADC531C7809398BA078DED1E182FEF4C* L_0;
		L_0 = CombinedScheduler_get__Schedulers_m29051BFB881A5D5FD9F106D462C3970259B866EC_inline(__this, NULL);
		RuntimeObject* L_1 = ___0_scheduler;
		NullCheck(L_0);
		List_1_Add_m9652946599533509ECAE57F6A3AD6B4AC39FFA8C_inline(L_0, L_1, List_1_Add_m9652946599533509ECAE57F6A3AD6B4AC39FFA8C_RuntimeMethod_var);
		return __this;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* CombinedScheduler_Append_mE3C2C0A61ABE0706690B42709661CB96629103CC (CombinedScheduler_tC9BC117BCE01E5068E0E5F7D76183ED3A2DFA0A8* __this, RuntimeObject* ___0_task, const RuntimeMethod* method) 
{
	{
		return __this;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool CombinedScheduler_Execute_m7DC0F73D8ECE7258EED9A91AE2C0C9CBBFE05060 (CombinedScheduler_tC9BC117BCE01E5068E0E5F7D76183ED3A2DFA0A8* __this, float ___0_deltaTime, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&IScheduler_tBA89D3CBD4600EE0421A94BCE19EC693B9E6678A_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_get_Count_mD2E9EFE20B0EEC3C6831330B6462B95B5218982A_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_get_Item_m5A3F4B3F4312888A93224C5CF7BEFCB5DE5342CB_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	bool V_0 = false;
	int32_t V_1 = 0;
	int32_t G_B4_0 = 0;
	{
		V_0 = (bool)1;
		V_1 = 0;
		goto IL_0023;
	}

IL_0006:
	{
		bool L_0 = V_0;
		if (!L_0)
		{
			goto IL_001d;
		}
	}
	{
		List_1_t34E53346ADC531C7809398BA078DED1E182FEF4C* L_1;
		L_1 = CombinedScheduler_get__Schedulers_m29051BFB881A5D5FD9F106D462C3970259B866EC_inline(__this, NULL);
		int32_t L_2 = V_1;
		NullCheck(L_1);
		RuntimeObject* L_3;
		L_3 = List_1_get_Item_m5A3F4B3F4312888A93224C5CF7BEFCB5DE5342CB(L_1, L_2, List_1_get_Item_m5A3F4B3F4312888A93224C5CF7BEFCB5DE5342CB_RuntimeMethod_var);
		float L_4 = ___0_deltaTime;
		NullCheck(L_3);
		bool L_5;
		L_5 = InterfaceFuncInvoker1< bool, float >::Invoke(4, IScheduler_tBA89D3CBD4600EE0421A94BCE19EC693B9E6678A_il2cpp_TypeInfo_var, L_3, L_4);
		G_B4_0 = ((int32_t)(L_5));
		goto IL_001e;
	}

IL_001d:
	{
		G_B4_0 = 0;
	}

IL_001e:
	{
		V_0 = (bool)G_B4_0;
		int32_t L_6 = V_1;
		V_1 = ((int32_t)il2cpp_codegen_add(L_6, 1));
	}

IL_0023:
	{
		int32_t L_7 = V_1;
		List_1_t34E53346ADC531C7809398BA078DED1E182FEF4C* L_8;
		L_8 = CombinedScheduler_get__Schedulers_m29051BFB881A5D5FD9F106D462C3970259B866EC_inline(__this, NULL);
		NullCheck(L_8);
		int32_t L_9;
		L_9 = List_1_get_Count_mD2E9EFE20B0EEC3C6831330B6462B95B5218982A_inline(L_8, List_1_get_Count_mD2E9EFE20B0EEC3C6831330B6462B95B5218982A_RuntimeMethod_var);
		if ((((int32_t)L_7) < ((int32_t)L_9)))
		{
			goto IL_0006;
		}
	}
	{
		bool L_10 = V_0;
		return L_10;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void CombinedScheduler__ctor_mA84FA35F562461C5CF2D9DF6A593BA965988040C (CombinedScheduler_tC9BC117BCE01E5068E0E5F7D76183ED3A2DFA0A8* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1__ctor_mFA461A6D907A1232C974461C054E0F3E5D570D95_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_t34E53346ADC531C7809398BA078DED1E182FEF4C_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		List_1_t34E53346ADC531C7809398BA078DED1E182FEF4C* L_0 = (List_1_t34E53346ADC531C7809398BA078DED1E182FEF4C*)il2cpp_codegen_object_new(List_1_t34E53346ADC531C7809398BA078DED1E182FEF4C_il2cpp_TypeInfo_var);
		List_1__ctor_mFA461A6D907A1232C974461C054E0F3E5D570D95(L_0, List_1__ctor_mFA461A6D907A1232C974461C054E0F3E5D570D95_RuntimeMethod_var);
		__this->___U3C_SchedulersU3Ek__BackingField = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___U3C_SchedulersU3Ek__BackingField), (void*)L_0);
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Task_get_IsFinished_mB714DCFB6BC9CFD91296E670ED10248A7A52A153 (Task_t025E98A50FB47A9B2723A0B6676C457151F0280B* __this, const RuntimeMethod* method) 
{
	{
		bool L_0 = __this->___U3CIsFinishedU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Task_set_IsFinished_mD6402087B874A5129E3E6DE8D00913475724DBBB (Task_t025E98A50FB47A9B2723A0B6676C457151F0280B* __this, bool ___0_value, const RuntimeMethod* method) 
{
	{
		bool L_0 = ___0_value;
		__this->___U3CIsFinishedU3Ek__BackingField = L_0;
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float Task_get_Progress_m7621307F75BE2642677775BD0A075F9BA720D594 (Task_t025E98A50FB47A9B2723A0B6676C457151F0280B* __this, const RuntimeMethod* method) 
{
	{
		float L_0 = __this->___U3CProgressU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Task_set_Progress_m3699ED176F6EFC3A9B805D4ADB8E8ACDAC3DF6E5 (Task_t025E98A50FB47A9B2723A0B6676C457151F0280B* __this, float ___0_value, const RuntimeMethod* method) 
{
	{
		float L_0 = ___0_value;
		__this->___U3CProgressU3Ek__BackingField = L_0;
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Task__ctor_m75F016C7FAF44A76CF1FAD52D6761E1EA3228063 (Task_t025E98A50FB47A9B2723A0B6676C457151F0280B* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Task__ctor_mE991E040C1E31C5AE097A795B42AB124FC3521ED (Task_t025E98A50FB47A9B2723A0B6676C457151F0280B* __this, Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___0_action, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_0 = ___0_action;
		Task_Initialize_m1245EA3003E520F57ED20FBF2BD667C4D49FEF71(__this, L_0, (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)NULL, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Task_Initialize_m1245EA3003E520F57ED20FBF2BD667C4D49FEF71 (Task_t025E98A50FB47A9B2723A0B6676C457151F0280B* __this, Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___0_action, Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___1_postResetAct, const RuntimeMethod* method) 
{
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_0 = ___0_action;
		__this->____action = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->____action), (void*)L_0);
		Task_set_IsFinished_mD6402087B874A5129E3E6DE8D00913475724DBBB_inline(__this, (bool)0, NULL);
		Task_set_Progress_m3699ED176F6EFC3A9B805D4ADB8E8ACDAC3DF6E5_inline(__this, (0.0f), NULL);
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_1 = ___1_postResetAct;
		__this->____postResetAction = L_1;
		Il2CppCodeGenWriteBarrier((void**)(&__this->____postResetAction), (void*)L_1);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Task_Reset_mADB1C64A00F3B065AD3CDAAAA2A2E47341A34543 (Task_t025E98A50FB47A9B2723A0B6676C457151F0280B* __this, const RuntimeMethod* method) 
{
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* G_B2_0 = NULL;
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* G_B1_0 = NULL;
	{
		__this->____action = (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)NULL;
		Il2CppCodeGenWriteBarrier((void**)(&__this->____action), (void*)(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)NULL);
		Task_set_IsFinished_mD6402087B874A5129E3E6DE8D00913475724DBBB_inline(__this, (bool)0, NULL);
		Task_set_Progress_m3699ED176F6EFC3A9B805D4ADB8E8ACDAC3DF6E5_inline(__this, (0.0f), NULL);
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_0 = __this->____postResetAction;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_1 = L_0;
		if (L_1)
		{
			G_B2_0 = L_1;
			goto IL_0025;
		}
		G_B1_0 = L_1;
	}
	{
		goto IL_002a;
	}

IL_0025:
	{
		NullCheck(G_B2_0);
		Action_Invoke_m7126A54DACA72B845424072887B5F3A51FC3808E_inline(G_B2_0, NULL);
	}

IL_002a:
	{
		__this->____postResetAction = (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)NULL;
		Il2CppCodeGenWriteBarrier((void**)(&__this->____postResetAction), (void*)(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Task_Run_m478677E8DFA58763AA1A7497617C0E744C61C1B5 (Task_t025E98A50FB47A9B2723A0B6676C457151F0280B* __this, const RuntimeMethod* method) 
{
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* G_B2_0 = NULL;
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* G_B1_0 = NULL;
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_0 = __this->____action;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_1 = L_0;
		if (L_1)
		{
			G_B2_0 = L_1;
			goto IL_000c;
		}
		G_B1_0 = L_1;
	}
	{
		goto IL_0011;
	}

IL_000c:
	{
		NullCheck(G_B2_0);
		Action_Invoke_m7126A54DACA72B845424072887B5F3A51FC3808E_inline(G_B2_0, NULL);
	}

IL_0011:
	{
		Task_set_IsFinished_mD6402087B874A5129E3E6DE8D00913475724DBBB_inline(__this, (bool)1, NULL);
		Task_set_Progress_m3699ED176F6EFC3A9B805D4ADB8E8ACDAC3DF6E5_inline(__this, (1.0f), NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Task_Tick_mFDF681D1AC67C60397C4C3CE6FE330E00A63827C (Task_t025E98A50FB47A9B2723A0B6676C457151F0280B* __this, float ___0_deltaTime, const RuntimeMethod* method) 
{
	{
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool TaskDelay_get_IsFinished_m2D73702D0D386545FD1A2F962FAF230F047007FE (TaskDelay_tF2D712504417D431B06EB95C4F323F8FE5BFAA4E* __this, const RuntimeMethod* method) 
{
	{
		bool L_0 = __this->___U3CIsFinishedU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TaskDelay_set_IsFinished_m054FAB34FEFDEF22E3EA727665C4035009914F9C (TaskDelay_tF2D712504417D431B06EB95C4F323F8FE5BFAA4E* __this, bool ___0_value, const RuntimeMethod* method) 
{
	{
		bool L_0 = ___0_value;
		__this->___U3CIsFinishedU3Ek__BackingField = L_0;
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float TaskDelay_get_Progress_m8110CE4A3473567841046A03BED2BE91BEF9B26D (TaskDelay_tF2D712504417D431B06EB95C4F323F8FE5BFAA4E* __this, const RuntimeMethod* method) 
{
	{
		float L_0 = __this->___U3CProgressU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TaskDelay_set_Progress_mF54B802D8EF7B8B88692737F28BD19B8A3FB0A3D (TaskDelay_tF2D712504417D431B06EB95C4F323F8FE5BFAA4E* __this, float ___0_value, const RuntimeMethod* method) 
{
	{
		float L_0 = ___0_value;
		__this->___U3CProgressU3Ek__BackingField = L_0;
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TaskDelay__ctor_mB022423A8FB6D9B52DA87E7698E79D8780FAE17E (TaskDelay_tF2D712504417D431B06EB95C4F323F8FE5BFAA4E* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TaskDelay__ctor_m9C5A2D7BDFD2D2D7ABDD90FB2844DF14FA51DFDF (TaskDelay_tF2D712504417D431B06EB95C4F323F8FE5BFAA4E* __this, float ___0_delay, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		float L_0 = ___0_delay;
		TaskDelay_Initialize_m21AD415596403721C63C81ECF69A5A1886F74609(__this, L_0, (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)NULL, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TaskDelay_Initialize_m21AD415596403721C63C81ECF69A5A1886F74609 (TaskDelay_tF2D712504417D431B06EB95C4F323F8FE5BFAA4E* __this, float ___0_delay, Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___1__postResetAct, const RuntimeMethod* method) 
{
	{
		float L_0 = ___0_delay;
		__this->____delay = L_0;
		__this->____timer = (0.0f);
		TaskDelay_set_IsFinished_m054FAB34FEFDEF22E3EA727665C4035009914F9C_inline(__this, (bool)0, NULL);
		TaskDelay_set_Progress_mF54B802D8EF7B8B88692737F28BD19B8A3FB0A3D_inline(__this, (0.0f), NULL);
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_1 = ___1__postResetAct;
		__this->____postResetAction = L_1;
		Il2CppCodeGenWriteBarrier((void**)(&__this->____postResetAction), (void*)L_1);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TaskDelay_Reset_mF8FFA09D2E643DF42F0DF68D6FEE4AA825977184 (TaskDelay_tF2D712504417D431B06EB95C4F323F8FE5BFAA4E* __this, const RuntimeMethod* method) 
{
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* G_B2_0 = NULL;
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* G_B1_0 = NULL;
	{
		__this->____delay = (0.0f);
		__this->____timer = (0.0f);
		TaskDelay_set_IsFinished_m054FAB34FEFDEF22E3EA727665C4035009914F9C_inline(__this, (bool)0, NULL);
		TaskDelay_set_Progress_mF54B802D8EF7B8B88692737F28BD19B8A3FB0A3D_inline(__this, (0.0f), NULL);
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_0 = __this->____postResetAction;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_1 = L_0;
		if (L_1)
		{
			G_B2_0 = L_1;
			goto IL_0034;
		}
		G_B1_0 = L_1;
	}
	{
		goto IL_0039;
	}

IL_0034:
	{
		NullCheck(G_B2_0);
		Action_Invoke_m7126A54DACA72B845424072887B5F3A51FC3808E_inline(G_B2_0, NULL);
	}

IL_0039:
	{
		__this->____postResetAction = (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)NULL;
		Il2CppCodeGenWriteBarrier((void**)(&__this->____postResetAction), (void*)(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TaskDelay_Run_m09A501B53D7E05475E02CA1BA8855E2249200415 (TaskDelay_tF2D712504417D431B06EB95C4F323F8FE5BFAA4E* __this, const RuntimeMethod* method) 
{
	{
		TaskDelay_set_IsFinished_m054FAB34FEFDEF22E3EA727665C4035009914F9C_inline(__this, (bool)0, NULL);
		TaskDelay_set_Progress_mF54B802D8EF7B8B88692737F28BD19B8A3FB0A3D_inline(__this, (0.0f), NULL);
		__this->____timer = (0.0f);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TaskDelay_Tick_mD4F8C2D0A48F995C114AC71743E4BA3D471C8B3B (TaskDelay_tF2D712504417D431B06EB95C4F323F8FE5BFAA4E* __this, float ___0_deltaTime, const RuntimeMethod* method) 
{
	{
		float L_0 = __this->____timer;
		float L_1 = ___0_deltaTime;
		__this->____timer = ((float)il2cpp_codegen_add(L_0, L_1));
		float L_2 = __this->____timer;
		float L_3 = __this->____delay;
		TaskDelay_set_Progress_mF54B802D8EF7B8B88692737F28BD19B8A3FB0A3D_inline(__this, ((float)(L_2/L_3)), NULL);
		float L_4 = __this->____timer;
		float L_5 = __this->____delay;
		if ((!(((float)L_4) >= ((float)L_5))))
		{
			goto IL_0041;
		}
	}
	{
		TaskDelay_set_IsFinished_m054FAB34FEFDEF22E3EA727665C4035009914F9C_inline(__this, (bool)1, NULL);
		TaskDelay_set_Progress_mF54B802D8EF7B8B88692737F28BD19B8A3FB0A3D_inline(__this, (1.0f), NULL);
	}

IL_0041:
	{
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool TaskWait_get_IsFinished_m403C4658AE7A25F9CC2F3EEB5D8443207B3E2284 (TaskWait_t3EE9538F13FC35386954C2A0357508C54402C70D* __this, const RuntimeMethod* method) 
{
	{
		bool L_0 = __this->___U3CIsFinishedU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TaskWait_set_IsFinished_mB7BA5FF0FE910A03891343EDEB0B0BA88D0C4F96 (TaskWait_t3EE9538F13FC35386954C2A0357508C54402C70D* __this, bool ___0_value, const RuntimeMethod* method) 
{
	{
		bool L_0 = ___0_value;
		__this->___U3CIsFinishedU3Ek__BackingField = L_0;
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float TaskWait_get_Progress_mA4745C4730F2E5F19701B53A026C9C11751F890B (TaskWait_t3EE9538F13FC35386954C2A0357508C54402C70D* __this, const RuntimeMethod* method) 
{
	{
		bool L_0;
		L_0 = TaskWait_get_IsFinished_m403C4658AE7A25F9CC2F3EEB5D8443207B3E2284_inline(__this, NULL);
		if (L_0)
		{
			goto IL_001b;
		}
	}
	{
		float L_1 = __this->____curTimeInSeconds;
		float L_2 = __this->____maxTimeInSeconds;
		float L_3;
		L_3 = Mathf_Clamp01_mA7E048DBDA832D399A581BE4D6DED9FA44CE0F14_inline(((float)(L_1/L_2)), NULL);
		return L_3;
	}

IL_001b:
	{
		return (1.0f);
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TaskWait__ctor_mBC9F0F91A9528C7FC4916B17D72BD37CED4DE9FB (TaskWait_t3EE9538F13FC35386954C2A0357508C54402C70D* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TaskWait__ctor_m1BC340C98E6E79A59F49EA57430DFE235A2C9853 (TaskWait_t3EE9538F13FC35386954C2A0357508C54402C70D* __this, Func_1_t2BE7F58348C9CC544A8973B3A9E55541DE43C457* ___0_func, float ___1_maxTimeInSeconds, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		Func_1_t2BE7F58348C9CC544A8973B3A9E55541DE43C457* L_0 = ___0_func;
		float L_1 = ___1_maxTimeInSeconds;
		TaskWait_Initialize_m57E23EB5BE81C2573B53470173A7268E34EF9E15(__this, L_0, L_1, (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)NULL, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TaskWait_Initialize_m57E23EB5BE81C2573B53470173A7268E34EF9E15 (TaskWait_t3EE9538F13FC35386954C2A0357508C54402C70D* __this, Func_1_t2BE7F58348C9CC544A8973B3A9E55541DE43C457* ___0_func, float ___1_maxTimeInSeconds, Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___2__postResetAct, const RuntimeMethod* method) 
{
	{
		Func_1_t2BE7F58348C9CC544A8973B3A9E55541DE43C457* L_0 = ___0_func;
		__this->____func = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->____func), (void*)L_0);
		TaskWait_set_IsFinished_mB7BA5FF0FE910A03891343EDEB0B0BA88D0C4F96_inline(__this, (bool)0, NULL);
		float L_1 = ___1_maxTimeInSeconds;
		__this->____maxTimeInSeconds = L_1;
		__this->____curTimeInSeconds = (0.0f);
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_2 = ___2__postResetAct;
		__this->____postResetAction = L_2;
		Il2CppCodeGenWriteBarrier((void**)(&__this->____postResetAction), (void*)L_2);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TaskWait_Reset_m9C053488E95AFAE7E560414C16CBF604204F273C (TaskWait_t3EE9538F13FC35386954C2A0357508C54402C70D* __this, const RuntimeMethod* method) 
{
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* G_B2_0 = NULL;
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* G_B1_0 = NULL;
	{
		__this->____func = (Func_1_t2BE7F58348C9CC544A8973B3A9E55541DE43C457*)NULL;
		Il2CppCodeGenWriteBarrier((void**)(&__this->____func), (void*)(Func_1_t2BE7F58348C9CC544A8973B3A9E55541DE43C457*)NULL);
		TaskWait_set_IsFinished_mB7BA5FF0FE910A03891343EDEB0B0BA88D0C4F96_inline(__this, (bool)0, NULL);
		__this->____curTimeInSeconds = (0.0f);
		__this->____maxTimeInSeconds = (0.0f);
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_0 = __this->____postResetAction;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_1 = L_0;
		if (L_1)
		{
			G_B2_0 = L_1;
			goto IL_0030;
		}
		G_B1_0 = L_1;
	}
	{
		goto IL_0035;
	}

IL_0030:
	{
		NullCheck(G_B2_0);
		Action_Invoke_m7126A54DACA72B845424072887B5F3A51FC3808E_inline(G_B2_0, NULL);
	}

IL_0035:
	{
		__this->____postResetAction = (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)NULL;
		Il2CppCodeGenWriteBarrier((void**)(&__this->____postResetAction), (void*)(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TaskWait_Run_m676E989B58F9C167CBEDE500C60FD5692F09296E (TaskWait_t3EE9538F13FC35386954C2A0357508C54402C70D* __this, const RuntimeMethod* method) 
{
	{
		TaskWait_set_IsFinished_mB7BA5FF0FE910A03891343EDEB0B0BA88D0C4F96_inline(__this, (bool)0, NULL);
		__this->____curTimeInSeconds = (0.0f);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TaskWait_Tick_m40301E65182205502D2D6D851379C68C13FA1038 (TaskWait_t3EE9538F13FC35386954C2A0357508C54402C70D* __this, float ___0_deltaTime, const RuntimeMethod* method) 
{
	{
		float L_0 = __this->____curTimeInSeconds;
		float L_1 = ___0_deltaTime;
		__this->____curTimeInSeconds = ((float)il2cpp_codegen_add(L_0, L_1));
		bool L_2;
		L_2 = TaskWait_Evaluate_m0562B993210978EB140A1A66FDA26E7F2C379E58(__this, NULL);
		if (L_2)
		{
			goto IL_0024;
		}
	}
	{
		float L_3 = __this->____curTimeInSeconds;
		float L_4 = __this->____maxTimeInSeconds;
		if ((!(((float)L_3) >= ((float)L_4))))
		{
			goto IL_002b;
		}
	}

IL_0024:
	{
		TaskWait_set_IsFinished_mB7BA5FF0FE910A03891343EDEB0B0BA88D0C4F96_inline(__this, (bool)1, NULL);
	}

IL_002b:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool TaskWait_Evaluate_m0562B993210978EB140A1A66FDA26E7F2C379E58 (TaskWait_t3EE9538F13FC35386954C2A0357508C54402C70D* __this, const RuntimeMethod* method) 
{
	Func_1_t2BE7F58348C9CC544A8973B3A9E55541DE43C457* G_B2_0 = NULL;
	Func_1_t2BE7F58348C9CC544A8973B3A9E55541DE43C457* G_B1_0 = NULL;
	{
		Func_1_t2BE7F58348C9CC544A8973B3A9E55541DE43C457* L_0 = __this->____func;
		Func_1_t2BE7F58348C9CC544A8973B3A9E55541DE43C457* L_1 = L_0;
		if (L_1)
		{
			G_B2_0 = L_1;
			goto IL_000c;
		}
		G_B1_0 = L_1;
	}
	{
		return (bool)1;
	}

IL_000c:
	{
		NullCheck(G_B2_0);
		bool L_2;
		L_2 = Func_1_Invoke_mBB7F37C468451AF57FAF31635C544D6B8C4373B2_inline(G_B2_0, NULL);
		return L_2;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool AsyncOperationTask_get_IsFinished_m274630F7C3BF342B87DC9704634956E4E87B7894 (AsyncOperationTask_t99808195DAEE9EE75DBDD7F875A14E606A5EA053* __this, const RuntimeMethod* method) 
{
	{
		AsyncOperation_tD2789250E4B098DEDA92B366A577E500A92D2D3C* L_0 = __this->___op;
		if (!L_0)
		{
			goto IL_0014;
		}
	}
	{
		AsyncOperation_tD2789250E4B098DEDA92B366A577E500A92D2D3C* L_1 = __this->___op;
		NullCheck(L_1);
		bool L_2;
		L_2 = AsyncOperation_get_isDone_m68A0682777E2132FC033182E9F50303566AA354D(L_1, NULL);
		return L_2;
	}

IL_0014:
	{
		return (bool)0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float AsyncOperationTask_get_Progress_mDA7152D16422255ABAED4BEBC1FD205ADFF7D0D2 (AsyncOperationTask_t99808195DAEE9EE75DBDD7F875A14E606A5EA053* __this, const RuntimeMethod* method) 
{
	{
		AsyncOperation_tD2789250E4B098DEDA92B366A577E500A92D2D3C* L_0 = __this->___op;
		if (L_0)
		{
			goto IL_000e;
		}
	}
	{
		return (0.0f);
	}

IL_000e:
	{
		AsyncOperation_tD2789250E4B098DEDA92B366A577E500A92D2D3C* L_1 = __this->___op;
		NullCheck(L_1);
		float L_2;
		L_2 = AsyncOperation_get_progress_mF3B2837C1A5DDF3C2F7A3BA1E449DD4C71C632EE(L_1, NULL);
		return L_2;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AsyncOperationTask__ctor_mF0D75B1EB6AA36F78EEF1363BE966F16039ABD6E (AsyncOperationTask_t99808195DAEE9EE75DBDD7F875A14E606A5EA053* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AsyncOperationTask__ctor_mC69D4362FA3DB2AA5A7F7C9D142CF69CB66C00E7 (AsyncOperationTask_t99808195DAEE9EE75DBDD7F875A14E606A5EA053* __this, AsyncOperation_tD2789250E4B098DEDA92B366A577E500A92D2D3C* ___0_operation, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		AsyncOperation_tD2789250E4B098DEDA92B366A577E500A92D2D3C* L_0 = ___0_operation;
		AsyncOperationTask_Initialize_mAC8C2E376EB61E3551B7904E6ECEA40550FF3532(__this, L_0, (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)NULL, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AsyncOperationTask_Initialize_mAC8C2E376EB61E3551B7904E6ECEA40550FF3532 (AsyncOperationTask_t99808195DAEE9EE75DBDD7F875A14E606A5EA053* __this, AsyncOperation_tD2789250E4B098DEDA92B366A577E500A92D2D3C* ___0_operation, Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___1__postResetAct, const RuntimeMethod* method) 
{
	{
		AsyncOperation_tD2789250E4B098DEDA92B366A577E500A92D2D3C* L_0 = ___0_operation;
		__this->___op = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___op), (void*)L_0);
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_1 = ___1__postResetAct;
		__this->____postResetAction = L_1;
		Il2CppCodeGenWriteBarrier((void**)(&__this->____postResetAction), (void*)L_1);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AsyncOperationTask_Reset_m1026C7F4364FEBE32FFFCD4604DB851BB83A66E3 (AsyncOperationTask_t99808195DAEE9EE75DBDD7F875A14E606A5EA053* __this, const RuntimeMethod* method) 
{
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* G_B2_0 = NULL;
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* G_B1_0 = NULL;
	{
		__this->___op = (AsyncOperation_tD2789250E4B098DEDA92B366A577E500A92D2D3C*)NULL;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___op), (void*)(AsyncOperation_tD2789250E4B098DEDA92B366A577E500A92D2D3C*)NULL);
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_0 = __this->____postResetAction;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_1 = L_0;
		if (L_1)
		{
			G_B2_0 = L_1;
			goto IL_0013;
		}
		G_B1_0 = L_1;
	}
	{
		goto IL_0018;
	}

IL_0013:
	{
		NullCheck(G_B2_0);
		Action_Invoke_m7126A54DACA72B845424072887B5F3A51FC3808E_inline(G_B2_0, NULL);
	}

IL_0018:
	{
		__this->____postResetAction = (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)NULL;
		Il2CppCodeGenWriteBarrier((void**)(&__this->____postResetAction), (void*)(Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07*)NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AsyncOperationTask_Run_mD561ABF100E353B7FC64491874BF779466BCFD77 (AsyncOperationTask_t99808195DAEE9EE75DBDD7F875A14E606A5EA053* __this, const RuntimeMethod* method) 
{
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void AsyncOperationTask_Tick_m45B6FCA7D9198B0F15929A2759EE35AEB1A5E3D1 (AsyncOperationTask_t99808195DAEE9EE75DBDD7F875A14E606A5EA053* __this, float ___0_deltaTime, const RuntimeMethod* method) 
{
	{
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Queue_1_t1753017E9AC02C107096B1F64F9CA7D8066B3F68* SequentialScheduler_get__Tasks_mB83D22B61EB664FFBE0E0E07522B028B99171824_inline (SequentialScheduler_tA81A2FC813C06F461FF491C537503D1A0700832B* __this, const RuntimeMethod* method) 
{
	{
		Queue_1_t1753017E9AC02C107096B1F64F9CA7D8066B3F68* L_0 = __this->___U3C_TasksU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR List_1_t930598996D1A93CCE7F51C4E2B0CC9645E9B8113* ParallelScheduler_get__Tasks_mC96C0BC294957F039A8E9D699E56F562886BD0F7_inline (ParallelScheduler_t8379A00B9D56775018796CE4487866984ED97D4E* __this, const RuntimeMethod* method) 
{
	{
		List_1_t930598996D1A93CCE7F51C4E2B0CC9645E9B8113* L_0 = __this->___U3C_TasksU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR List_1_t34E53346ADC531C7809398BA078DED1E182FEF4C* CombinedScheduler_get__Schedulers_m29051BFB881A5D5FD9F106D462C3970259B866EC_inline (CombinedScheduler_tC9BC117BCE01E5068E0E5F7D76183ED3A2DFA0A8* __this, const RuntimeMethod* method) 
{
	{
		List_1_t34E53346ADC531C7809398BA078DED1E182FEF4C* L_0 = __this->___U3C_SchedulersU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Task_set_IsFinished_mD6402087B874A5129E3E6DE8D00913475724DBBB_inline (Task_t025E98A50FB47A9B2723A0B6676C457151F0280B* __this, bool ___0_value, const RuntimeMethod* method) 
{
	{
		bool L_0 = ___0_value;
		__this->___U3CIsFinishedU3Ek__BackingField = L_0;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Task_set_Progress_m3699ED176F6EFC3A9B805D4ADB8E8ACDAC3DF6E5_inline (Task_t025E98A50FB47A9B2723A0B6676C457151F0280B* __this, float ___0_value, const RuntimeMethod* method) 
{
	{
		float L_0 = ___0_value;
		__this->___U3CProgressU3Ek__BackingField = L_0;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Action_Invoke_m7126A54DACA72B845424072887B5F3A51FC3808E_inline (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* __this, const RuntimeMethod* method) 
{
	typedef void (*FunctionPointerType) (RuntimeObject*, const RuntimeMethod*);
	((FunctionPointerType)__this->___invoke_impl)((Il2CppObject*)__this->___method_code, reinterpret_cast<RuntimeMethod*>(__this->___method));
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void TaskDelay_set_IsFinished_m054FAB34FEFDEF22E3EA727665C4035009914F9C_inline (TaskDelay_tF2D712504417D431B06EB95C4F323F8FE5BFAA4E* __this, bool ___0_value, const RuntimeMethod* method) 
{
	{
		bool L_0 = ___0_value;
		__this->___U3CIsFinishedU3Ek__BackingField = L_0;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void TaskDelay_set_Progress_mF54B802D8EF7B8B88692737F28BD19B8A3FB0A3D_inline (TaskDelay_tF2D712504417D431B06EB95C4F323F8FE5BFAA4E* __this, float ___0_value, const RuntimeMethod* method) 
{
	{
		float L_0 = ___0_value;
		__this->___U3CProgressU3Ek__BackingField = L_0;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool TaskWait_get_IsFinished_m403C4658AE7A25F9CC2F3EEB5D8443207B3E2284_inline (TaskWait_t3EE9538F13FC35386954C2A0357508C54402C70D* __this, const RuntimeMethod* method) 
{
	{
		bool L_0 = __this->___U3CIsFinishedU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Mathf_Clamp01_mA7E048DBDA832D399A581BE4D6DED9FA44CE0F14_inline (float ___0_value, const RuntimeMethod* method) 
{
	bool V_0 = false;
	float V_1 = 0.0f;
	bool V_2 = false;
	{
		float L_0 = ___0_value;
		V_0 = (bool)((((float)L_0) < ((float)(0.0f)))? 1 : 0);
		bool L_1 = V_0;
		if (!L_1)
		{
			goto IL_0015;
		}
	}
	{
		V_1 = (0.0f);
		goto IL_002d;
	}

IL_0015:
	{
		float L_2 = ___0_value;
		V_2 = (bool)((((float)L_2) > ((float)(1.0f)))? 1 : 0);
		bool L_3 = V_2;
		if (!L_3)
		{
			goto IL_0029;
		}
	}
	{
		V_1 = (1.0f);
		goto IL_002d;
	}

IL_0029:
	{
		float L_4 = ___0_value;
		V_1 = L_4;
		goto IL_002d;
	}

IL_002d:
	{
		float L_5 = V_1;
		return L_5;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void TaskWait_set_IsFinished_mB7BA5FF0FE910A03891343EDEB0B0BA88D0C4F96_inline (TaskWait_t3EE9538F13FC35386954C2A0357508C54402C70D* __this, bool ___0_value, const RuntimeMethod* method) 
{
	{
		bool L_0 = ___0_value;
		__this->___U3CIsFinishedU3Ek__BackingField = L_0;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t Queue_1_get_Count_m1768ADA9855B7CDA14C9C42E098A287F1A39C3A2_gshared_inline (Queue_1_tE9EF546915795972C3BFD68FBB8FA859D3BAF3B5* __this, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = __this->____size;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t List_1_get_Count_m4407E4C389F22B8CEC282C15D56516658746C383_gshared_inline (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D* __this, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = __this->____size;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR RuntimeObject* Enumerator_get_Current_m6330F15D18EE4F547C05DF9BF83C5EB710376027_gshared_inline (Enumerator_t9473BAB568A27E2339D48C1F91319E0F6D244D7A* __this, const RuntimeMethod* method) 
{
	{
		RuntimeObject* L_0 = __this->____current;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void List_1_Clear_m16C1F2C61FED5955F10EB36BC1CB2DF34B128994_gshared_inline (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D* __this, const RuntimeMethod* method) 
{
	int32_t V_0 = 0;
	{
		int32_t L_0 = __this->____version;
		__this->____version = ((int32_t)il2cpp_codegen_add(L_0, 1));
	}
	{
		int32_t L_1 = __this->____size;
		V_0 = L_1;
		__this->____size = 0;
		int32_t L_2 = V_0;
		if ((((int32_t)L_2) <= ((int32_t)0)))
		{
			goto IL_003c;
		}
	}
	{
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_3 = __this->____items;
		int32_t L_4 = V_0;
		Array_Clear_m50BAA3751899858B097D3FF2ED31F284703FE5CB((RuntimeArray*)L_3, 0, L_4, NULL);
		return;
	}

IL_003c:
	{
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void List_1_Add_mEBCF994CC3814631017F46A387B1A192ED6C85C7_gshared_inline (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D* __this, RuntimeObject* ___0_item, const RuntimeMethod* method) 
{
	ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* V_0 = NULL;
	int32_t V_1 = 0;
	{
		int32_t L_0 = __this->____version;
		__this->____version = ((int32_t)il2cpp_codegen_add(L_0, 1));
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_1 = __this->____items;
		V_0 = L_1;
		int32_t L_2 = __this->____size;
		V_1 = L_2;
		int32_t L_3 = V_1;
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_4 = V_0;
		NullCheck(L_4);
		if ((!(((uint32_t)L_3) < ((uint32_t)((int32_t)(((RuntimeArray*)L_4)->max_length))))))
		{
			goto IL_0034;
		}
	}
	{
		int32_t L_5 = V_1;
		__this->____size = ((int32_t)il2cpp_codegen_add(L_5, 1));
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_6 = V_0;
		int32_t L_7 = V_1;
		RuntimeObject* L_8 = ___0_item;
		NullCheck(L_6);
		(L_6)->SetAt(static_cast<il2cpp_array_size_t>(L_7), (RuntimeObject*)L_8);
		return;
	}

IL_0034:
	{
		RuntimeObject* L_9 = ___0_item;
		List_1_AddWithResize_m79A9BF770BEF9C06BE40D5401E55E375F2726CC4(__this, L_9, il2cpp_rgctx_method(method->klass->rgctx_data, 14));
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool Func_1_Invoke_mBB7F37C468451AF57FAF31635C544D6B8C4373B2_gshared_inline (Func_1_t2BE7F58348C9CC544A8973B3A9E55541DE43C457* __this, const RuntimeMethod* method) 
{
	typedef bool (*FunctionPointerType) (RuntimeObject*, const RuntimeMethod*);
	return ((FunctionPointerType)__this->___invoke_impl)((Il2CppObject*)__this->___method_code, reinterpret_cast<RuntimeMethod*>(__this->___method));
}

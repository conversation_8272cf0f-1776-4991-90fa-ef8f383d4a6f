﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void EmbeddedAttribute__ctor_m43CCC2B1DEE103B98BFF31EE6090FA59DA8A1103 (void);
extern void IsUnmanagedAttribute__ctor_mF5002E8D38A31E57A4DC7F5DA00D31B5D88D934F (void);
extern void RectTransformSizeDeltaAnimation_GetValue_m41F50265689F1582BFF9D95FAED03C2AA04697A3 (void);
extern void RectTransformSizeDeltaAnimation_SetValue_m3DB3F02FE77B17EADB4E4CD08A5160835A5F5FA0 (void);
extern void RectTransformSizeDeltaAnimation__ctor_m4ABAB5D30DAA4A1E0A894ED5ACF641B30C789CD7 (void);
extern void RectTransformPivotAnimation_GetValue_mDDC6AE296276918D77A551D1272DABAAF76C5B34 (void);
extern void RectTransformPivotAnimation_SetValue_m3A205C846693426B426DAC9993AF041489214904 (void);
extern void RectTransformPivotAnimation__ctor_m8E2E83E3B8ABAFD7CE97CB5C70E22B858E9BCF98 (void);
extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_m80B6A513F4ADF9E40FB55BDD20F5E30998A90941 (void);
extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_m145372F693CCD1311B8D6739DDF5283BA0E58C87 (void);
extern void LitMotionAnimation_get_Components_mE4468AC0ACFD680534475A97C65B73001D5DC47D (void);
extern void LitMotionAnimation_Start_m08CD9F50D29ACAB3BC814B024B247BFC4993EB07 (void);
extern void LitMotionAnimation_MoveNextMotion_m758C60CEC79EB60134B2B283113E9F5D7D40B518 (void);
extern void LitMotionAnimation_Play_m597E40471DA063B32C89155C4A7531A18E180D44 (void);
extern void LitMotionAnimation_Pause_mB7C40D6C139E1AC10D0B6E293F950D32BA20CD10 (void);
extern void LitMotionAnimation_Stop_m05B7E202159A8AE86BBF69671025C1EB439B13BE (void);
extern void LitMotionAnimation_Restart_mD00836C97E7C7D5CEDD7C4FD55C170BC689C63FE (void);
extern void LitMotionAnimation_get_IsActive_m0B1FBE994BFE23FB181A193CCA45FBA31456F581 (void);
extern void LitMotionAnimation_get_IsPlaying_mBC613A1EF1342DD779D8E3F5EFF1A910863E5F05 (void);
extern void LitMotionAnimation_OnDestroy_m47A84B980BB95E548742B6852B39274F51808CB9 (void);
extern void LitMotionAnimation__ctor_m17EB0D2D850BEB3718A200621A8543741C1C478B (void);
extern void LitMotionAnimationComponent__ctor_mD62326B8DB8585E40CFAA315FC8F3A94B8FA356B (void);
extern void LitMotionAnimationComponent_get_Enabled_m910F7930C43DDE7E7951EA1EC5767B3DE7EFF718 (void);
extern void LitMotionAnimationComponent_get_DisplayName_mC4D963924B10F1CDD9F78D4E52464C89198912EC (void);
extern void LitMotionAnimationComponent_OnResume_m58544AC3643B1915D7CCC9C3FA958F076FC42A37 (void);
extern void LitMotionAnimationComponent_OnPause_m4816CF7B8FEC93F345909CC3B5FF2F84EAA5B67E (void);
extern void LitMotionAnimationComponent_OnStop_mBF3F5B3295E59AB4210C89B341B6E0AD349CA8AC (void);
extern void LitMotionAnimationComponent_get_TrackedHandle_m5B903D2A947941090BE2F79870D73368DD0A0606 (void);
extern void LitMotionAnimationComponent_set_TrackedHandle_mC6DE281FF2BE11BB93377BCB3B311226AA8CAE31 (void);
extern void LitMotionAnimationComponentMenuAttribute__ctor_mFB980AD8F8EE812DBD0C7A75C376AD53DFB31462 (void);
extern void LitMotionAnimationComponentMenuAttribute_get_MenuName_mEC11A3D4C87AB226A6F15C3469B96BE0FEB4FFF3 (void);
extern void AudioSourceVolumeAnimation_GetValue_m8BF329DE9619DCC0E1C23A731F8D2B849C2D6471 (void);
extern void AudioSourceVolumeAnimation_SetValue_m6C23E9337AD89C1237F2CF65360333206595A496 (void);
extern void AudioSourceVolumeAnimation__ctor_m25000CB1A95A753C109E667EF5FDE5F9239780F2 (void);
extern void AudioSourcePitchAnimation_GetValue_m678A92D5A613E2F9DF330A43C7412202DB68B8C4 (void);
extern void AudioSourcePitchAnimation_SetValue_m319255C814C45450FA7F626DED27FDB8568E96F4 (void);
extern void AudioSourcePitchAnimation__ctor_mD45A1C54E1C33173DBDBAC0BFD420042D5E73379 (void);
extern void CameraAspectAnimation_GetValue_mB2F65796DBD88DBB0495E1BB99C3DCFC6C979BB5 (void);
extern void CameraAspectAnimation_SetValue_mAF5484690CE01F2FA34B5922698A98779CCABF99 (void);
extern void CameraAspectAnimation__ctor_mCF081386A5F8DBD9E6DA9677E8F737EFDC5B93E4 (void);
extern void CameraNearClipPlaneAnimation_GetValue_mFCF2557AB6EB89787D43288DC74DD6F92DD96D62 (void);
extern void CameraNearClipPlaneAnimation_SetValue_mCC13EE87F12433A9B6D097B59A1D1BB6E6B1CC8B (void);
extern void CameraNearClipPlaneAnimation__ctor_mD09AD373B26E3D40BB6B2D418AED3B0535444F40 (void);
extern void CameraFarClipPlaneAnimation_GetValue_m89401C1C8CC4CEC71CBC1583DF386285FF484A92 (void);
extern void CameraFarClipPlaneAnimation_SetValue_m1F2FD5F4C72B64C1A2B6E2ADC212D9EEF5A5B9A9 (void);
extern void CameraFarClipPlaneAnimation__ctor_m053D594578414E468D4EAD4B1D808E1D4D182CAF (void);
extern void CameraFieldOfViewAnimation_GetValue_m6A0CBFCC6AA1CBC5386C161D45CD5B8032952B13 (void);
extern void CameraFieldOfViewAnimation_SetValue_mC1BEB8E9F3329270644F17281CA2117CB9D04A8D (void);
extern void CameraFieldOfViewAnimation__ctor_m99A290058C2B174F6996C95F2C79FE2AB8A47B2B (void);
extern void CameraOrthographicSizeAnimation_GetValue_mDE03A8F9B59FA5FEDF19665EAA82BE6D57D7928D (void);
extern void CameraOrthographicSizeAnimation_SetValue_m739E7F8FB599195BF88FE7ADCDA117987EC3403F (void);
extern void CameraOrthographicSizeAnimation__ctor_m37AB2469AF8399CD0E00EC6D2EA175465C03DB07 (void);
extern void CameraRectAnimation_GetValue_mA7F60A3EA3B78869B56569046D0420722974174A (void);
extern void CameraRectAnimation_SetValue_m1B84BFE29522923A31937F3A7B5696E9672723BE (void);
extern void CameraRectAnimation__ctor_m3395A4EEE94E4472024B10C7B14572658857AC8A (void);
extern void CameraPixelRectAnimation_GetValue_mBD00F2EFAF43C862C13BBACE0916CAC52DBA4B47 (void);
extern void CameraPixelRectAnimation_SetValue_m0B9FD0055D0815BF8F5116B57C447729FA347802 (void);
extern void CameraPixelRectAnimation__ctor_m6D28F37BF6F2751A9CB88831907C24155FBE8453 (void);
extern void CameraBackgroundColorAnimation_GetValue_m31BE05B04683C476A5CA5050F09294848C8E12C1 (void);
extern void CameraBackgroundColorAnimation_SetValue_m411AC4DCB8D4ED18AE96ACFAEDD24E799E732289 (void);
extern void CameraBackgroundColorAnimation__ctor_mA749FF07FCC9FC17152E9A3443E995347F13F0AF (void);
extern void DelayComponent_Play_m3F91995ABA2874838D9D918ABC5415DD9E521BAB (void);
extern void DelayComponent_OnStop_mE97AC1EF7713C0166CC6E5491643C200FA8E36B1 (void);
extern void DelayComponent__ctor_mB00D0CC724B87AF745CCC79983A445B77F3DC977 (void);
extern void EventComponent_Play_mD9F8DC42830A7BA0A418342DD035B67D27492B06 (void);
extern void EventComponent_OnStop_m4F401B9C7C96D2DDEBC8E34661147F920E6DFFE2 (void);
extern void EventComponent__ctor_m17720E8105FC1A33080C19C16BEA0E1EAC3BE080 (void);
extern void PlayLitMotionAnimationComponent_Play_m7F8186399E3C5A58C964A05F673BE3EAA447E199 (void);
extern void PlayLitMotionAnimationComponent_OnResume_m700E111782AC225D624070F53C12F87F75E6EF3F (void);
extern void PlayLitMotionAnimationComponent_OnPause_m0E3614B876F3B5D8FA3B5798DDA995194A3FFCCF (void);
extern void PlayLitMotionAnimationComponent_OnStop_m6ADBA0EA4AA6EE4562F45A2FEA91333ED852D9A4 (void);
extern void PlayLitMotionAnimationComponent__ctor_mAA68F59ACD285F2E25D516A081503E70C16957F7 (void);
extern void PlayLitMotionAnimationComponent_U3CPlayU3Eb__1_0_m8B3A9D036CC26B921B03359877171C5B8F39CF4E (void);
extern void MaterialFloatAnimation_GetValue_mC9281C689D2D7C7D706153F3A885E3FA2C7FD469 (void);
extern void MaterialFloatAnimation_SetValue_mF451A3A973CA19534A8DBA5996E964B1F04863E3 (void);
extern void MaterialFloatAnimation__ctor_m1AE831576A3226E8C5EEBFC1271CCE58C50261F4 (void);
extern void MaterialIntAnimation_GetValue_m7C5901F45C8D9980DB6D44F1A3728501B9AB630C (void);
extern void MaterialIntAnimation_SetValue_m6E13273971C4761F22B6BDB773851363EFE7272A (void);
extern void MaterialIntAnimation__ctor_m01E1DB0656117DCCA165CE82D93917092B94572A (void);
extern void MaterialVectorAnimation_GetValue_m5A1BC717030106C681FF2F6332ABCB30C54FCE3D (void);
extern void MaterialVectorAnimation_SetValue_m16E33B65AE12CFE8C3439C130284E5073CB55DCD (void);
extern void MaterialVectorAnimation__ctor_m969F0AF882C11E94A3B47C5E1A2099CE12CFA547 (void);
extern void MaterialColorAnimation_GetValue_mF7E2B9141566DFF9B4CA9B80BCC019985E1EF285 (void);
extern void MaterialColorAnimation_SetValue_m2A399FEDA21CBAC9EA4679C4ED815B89D09F3125 (void);
extern void MaterialColorAnimation__ctor_mC93EA57970134869E98AEF99D233CFD13238EE37 (void);
extern void SpriteRendererColorAnimation_GetValue_mFCE79EDAD5702719670F9A63EEE03C3605B6D90C (void);
extern void SpriteRendererColorAnimation_SetValue_m855981BECBAF9D6601927212C0BC64287530FADE (void);
extern void SpriteRendererColorAnimation__ctor_m6CE828A5751B7AB0A00DE6FA07844059C9FA3B19 (void);
extern void VolumeWeightAnimation_GetValue_mE05D0E9F954AC84043852EA736BF1C143D9344D3 (void);
extern void VolumeWeightAnimation_SetValue_mF6C4D9429A04BD51E156C42663EEB8C6BFC55019 (void);
extern void VolumeWeightAnimation__ctor_mBDE0BF73D81E3F87A295CB6B8FDF51A0D9C0CAB9 (void);
extern void Rigidbody2DPositionAnimation__ctor_m09D70B6A6BE5891C63B89E9544F054507AEADAC0 (void);
extern void Rigidbody2DPositionPunchAnimation__ctor_mEB018B7047644AACA9C76896312F4F08B21C99DA (void);
extern void Rigidbody2DPositionShakeAnimation__ctor_m165BB68F36998824D0F29111A66A460529795F38 (void);
extern void Rigidbody2DRotationAnimation__ctor_m473389D364DEA5868735B2DD3D6AC832631A13FC (void);
extern void Rigidbody2DRotationPunchAnimation__ctor_m405B03D393E2A81609644454D3684A88B4B66F57 (void);
extern void Rigidbody2DRotationShakeAnimation__ctor_mD53EA361A92A6AB113DE2E2FE0FC615E4EA96D43 (void);
extern void RigidbodyPositionAnimation__ctor_m1882F91F2244565437466E2B533C194619CA1E6D (void);
extern void RigidbodyPositionPunchAnimation__ctor_m46F5FDC977E89382B0D4D6EAE5109AE28CCE73AA (void);
extern void RigidbodyPositionShakeAnimation__ctor_m1DE452B65A8A8A93D3A7C847DEB570047E6FAE17 (void);
extern void RigidbodyRotationAnimation__ctor_mA94BF194AD0899B93733595768AC4B6CCCAF2AEE (void);
extern void RigidbodyRotationPunchAnimation__ctor_m5A9ED12A880FEA940F27A2B250D24D85B1122F76 (void);
extern void RigidbodyRotationShakeAnimation__ctor_m73DF76DD05E3E869D070CF02631E97B98D32D78B (void);
extern void TMPTextAnimation_GetValue_m1A9253407828CD9A271E728CE5B8742BB88FD226 (void);
extern void TMPTextAnimation_SetValue_m6EB1FAAD752E90EDDD48539959D5FEF6AF38295C (void);
extern void TMPTextAnimation__ctor_m083C2B177D26F479A531C951A201DA679D1E93D9 (void);
extern void TMPTextCharacterSpacingAnimation_GetValue_mC757EF527CE4D02D69BCFCECD6054C55BAB76718 (void);
extern void TMPTextCharacterSpacingAnimation_SetValue_mCDA746D1BB0C80C50CA7103763ED14BCA4A8C6DE (void);
extern void TMPTextCharacterSpacingAnimation__ctor_m694CA6949B6B3E5D7067E5202CB5EB5D24AC9DC8 (void);
extern void TMPTextWordSpacingAnimation_GetValue_mCA8CBCC9E379337CAFA6D14E73428567D5F363CE (void);
extern void TMPTextWordSpacingAnimation_SetValue_m4D8AA237A51CC69FB0DEA75C46FCBBCA6C3F1937 (void);
extern void TMPTextWordSpacingAnimation__ctor_m65DC5AAC1A7577B22703EF07250A40E497F653B7 (void);
extern void TMPTextLineSpacingAnimation_GetValue_m05233598FE33596D89413A1FD01D00743A38DB7B (void);
extern void TMPTextLineSpacingAnimation_SetValue_m373729AE91AB2555B60C55B56B1E9F26FF843231 (void);
extern void TMPTextLineSpacingAnimation__ctor_m9260FDF44FCD3EA447F6D86F076B30A8286AAE3F (void);
extern void TMPTextParagraphSpacingAnimation_GetValue_mD6E1E6C3A03BA44CCF1200A39E373B5809B6E91A (void);
extern void TMPTextParagraphSpacingAnimation_SetValue_mBAE2A40EC1BAE81D9998F75D82568878CCAA5ED6 (void);
extern void TMPTextParagraphSpacingAnimation__ctor_mB84E4F61B30568A0B804F701F84511453C494CCB (void);
extern void TMPTextFontSizeAnimation_GetValue_m7C458EA58BEB38C0B4315EE9BE5F776BD9DDECF4 (void);
extern void TMPTextFontSizeAnimation_SetValue_m9A341ADAC9A8D5A60F28B9C82AAEDDAE63EEB703 (void);
extern void TMPTextFontSizeAnimation__ctor_m2350D9664C3403E9BB28D99D2843C75FE1D610FC (void);
extern void TMPTextColorAnimation_GetValue_mF3C30D6348C84401B338B08F900239E852FE3584 (void);
extern void TMPTextColorAnimation_SetValue_m3D841E628CDCBD935481E73B4C4029619DC21139 (void);
extern void TMPTextColorAnimation__ctor_m14D4994498C2AA1A521FFC8C0C2664412709D72C (void);
extern void TMPTextColorAlphaAnimation_GetValue_mD62B97CD9047FAA1366060EE9AA64CBFBECC011E (void);
extern void TMPTextColorAlphaAnimation_SetValue_mE3A411F9E6E47F00D334E7958D8671C20B2D486F (void);
extern void TMPTextColorAlphaAnimation__ctor_m910A471AE89548D7C868C040B74895C7007035A2 (void);
extern void TransformPositionAnimation__ctor_mDAE0A5F3015E2316E2FD359B01AB10B030074CE6 (void);
extern void TransformPositionPunchAnimation__ctor_mD17E2FDB3BFC1B16A74E041E363565B821000411 (void);
extern void TransformPositionShakeAnimation__ctor_m260221C7FCF468E50E6029CE02117E50DAB7BA36 (void);
extern void TransformRotationAnimation__ctor_mC409D029250FC3AC1D7DB420A274A8B85B736592 (void);
extern void TransformRotationPunchAnimation__ctor_m75EF145CEA3F9D335EF7B81A4537FF6266E9676A (void);
extern void TransformRotationShakeAnimation__ctor_m1B7A4DE7414129E250D6B664CEB82CDCDAF852F0 (void);
extern void TransformScaleAnimation__ctor_m334568E452D0775E3C56D11DA2DD189A705711DB (void);
extern void TransformScalePunchAnimation__ctor_m8FDA121800FCBF9743D192CAF49EE5ACCE52E809 (void);
extern void TransformScaleShakeAnimation__ctor_m743D01D1659D867B3C13B8E01B72D261D3BF73FD (void);
extern void TextAnimation_GetValue_m8600BFFA502918A0F2142FFF08B71905EB58CA6F (void);
extern void TextAnimation_SetValue_m33F9E42F4BF1C6F4BD4E29A5109A26FAE6F8F8BC (void);
extern void TextAnimation__ctor_m587A6D6554ACC86C6C3444FFBF7F9EBC51BB36B0 (void);
extern void TextColorAnimation_GetValue_mADB6804970D3F3630CB2578C15C352574ACA22FD (void);
extern void TextColorAnimation_SetValue_m913E227B8E497A3782658FFAD5AD0308A126A7A3 (void);
extern void TextColorAnimation__ctor_m048855B24ECEC5A6AFBC71D9995A88CF8E279EA1 (void);
extern void TextFontSizeAnimation_GetValue_m1AF051D4E265FC2C0610785E2D302B1863612DF1 (void);
extern void TextFontSizeAnimation_SetValue_m99196F3D82934735A6D5FCB1451C845FABE1703C (void);
extern void TextFontSizeAnimation__ctor_m87C9A28C8A8853CC198F3111A5E314DE665695A1 (void);
extern void GraphicColorAnimation_GetValue_mE8C9561CD10F966B50ACE0DEAB3F775874782E24 (void);
extern void GraphicColorAnimation_SetValue_m3CB23168572E2E90EFAAF4D66D547E3861FAAA35 (void);
extern void GraphicColorAnimation__ctor_m288E56671326902591010E3AB28533431B0CF290 (void);
extern void ImageColorAnimation_GetValue_m6E1EDD657548A690F465E7A49C393DCAEE6153CB (void);
extern void ImageColorAnimation_SetValue_mA09FDF3568497950BD81024995A823457D80EBD5 (void);
extern void ImageColorAnimation__ctor_m88E171095847625BF018289C724B34286FF30262 (void);
extern void ImageColorAlphaAnimation_GetValue_mE6A08B0E3B4ECF52D2ECC04505A5F74C04DD48E4 (void);
extern void ImageColorAlphaAnimation_SetValue_mFC57F8AAAC85DA0D172896A780151E8D5939AE57 (void);
extern void ImageColorAlphaAnimation__ctor_mAFB1ACD9F6207441409EBF45682D8DEF60106C1D (void);
extern void ImageFillAmountAnimation_GetValue_mB62C07E93DBABFA12485068607449D1CED67D655 (void);
extern void ImageFillAmountAnimation_SetValue_m8E63709BBA578130019B438D4CE88869839F8992 (void);
extern void ImageFillAmountAnimation__ctor_m5DCCED55FA424D55D759E67C935CDBFF6725419E (void);
extern void SliderValueAnimation_GetValue_m9BB623607B6D753DEF65B9370DEF7C804AFF3DAA (void);
extern void SliderValueAnimation_SetValue_mE6887006C98BEC23920124938DC3EF838CDCEBED (void);
extern void SliderValueAnimation__ctor_m26F9E778C4ED855C2654EB0D20CF7B63D50F9A2A (void);
extern void CanvasGroupAlphaAnimation_GetValue_m804B59FFDBA9BDD2EB2A29DE43406564F42BCD9E (void);
extern void CanvasGroupAlphaAnimation_SetValue_m97E2BA49705950C6DDB62791E0E06EFF171C8512 (void);
extern void CanvasGroupAlphaAnimation__ctor_mDF253ED1F8ABF5CA03225ACA4C490C7AAE89A041 (void);
extern void FloatValueAnimation__ctor_mE153CF3E7794FDAB805AA6BD6320991BA80068B8 (void);
extern void DoubleValueAnimation__ctor_m056B5D3F68A271AA480DD0EA0A60F7780CF2C0D6 (void);
extern void IntValueAnimation__ctor_m352EA06516720DA4BE2AB2C883094B776D1A0237 (void);
extern void LongValueAnimation__ctor_m153F089490214E4DB4C01540D6B4304E004B9135 (void);
extern void Vector2ValueAnimation__ctor_m136E4D95ABF8CB2785A23E77BFC4EC8F900252DA (void);
extern void Vector3ValueAnimation__ctor_m301EC277CBC1EE37B80A0290AFF38347C3CEAFAD (void);
extern void Vector4ValueAnimation__ctor_mBE48AD9477B7A05A511D3B0B15F4EE9DCA82DA49 (void);
extern void ColorValueAnimation__ctor_m4C8AE07F352EC0DC93EB4D29D162B812A3E95937 (void);
extern void StringValueAnimation_Play_m2341ABE671694F0E753497A0FF8E7A16EB73C68E (void);
extern void StringValueAnimation_OnStop_mF02A154A26B0A52CA9BBD83A6C5C4238E664A914 (void);
extern void StringValueAnimation__ctor_m9D852268322B756973640E0954A6FAF6A95A3417 (void);
extern void U3CU3Ec__cctor_m1B8082DD774DBB205D93FCE9C36A71817E2FF5B8 (void);
extern void U3CU3Ec__ctor_m6282724279A29E01D74080870544C26EA3CF5E66 (void);
extern void U3CU3Ec_U3CPlayU3Eb__2_0_mF360A40509FD2F30750DCF886F357F69F1629B7B (void);
static Il2CppMethodPointer s_methodPointers[240] = 
{
	EmbeddedAttribute__ctor_m43CCC2B1DEE103B98BFF31EE6090FA59DA8A1103,
	IsUnmanagedAttribute__ctor_mF5002E8D38A31E57A4DC7F5DA00D31B5D88D934F,
	RectTransformSizeDeltaAnimation_GetValue_m41F50265689F1582BFF9D95FAED03C2AA04697A3,
	RectTransformSizeDeltaAnimation_SetValue_m3DB3F02FE77B17EADB4E4CD08A5160835A5F5FA0,
	RectTransformSizeDeltaAnimation__ctor_m4ABAB5D30DAA4A1E0A894ED5ACF641B30C789CD7,
	RectTransformPivotAnimation_GetValue_mDDC6AE296276918D77A551D1272DABAAF76C5B34,
	RectTransformPivotAnimation_SetValue_m3A205C846693426B426DAC9993AF041489214904,
	RectTransformPivotAnimation__ctor_m8E2E83E3B8ABAFD7CE97CB5C70E22B858E9BCF98,
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_m80B6A513F4ADF9E40FB55BDD20F5E30998A90941,
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_m145372F693CCD1311B8D6739DDF5283BA0E58C87,
	LitMotionAnimation_get_Components_mE4468AC0ACFD680534475A97C65B73001D5DC47D,
	LitMotionAnimation_Start_m08CD9F50D29ACAB3BC814B024B247BFC4993EB07,
	LitMotionAnimation_MoveNextMotion_m758C60CEC79EB60134B2B283113E9F5D7D40B518,
	LitMotionAnimation_Play_m597E40471DA063B32C89155C4A7531A18E180D44,
	LitMotionAnimation_Pause_mB7C40D6C139E1AC10D0B6E293F950D32BA20CD10,
	LitMotionAnimation_Stop_m05B7E202159A8AE86BBF69671025C1EB439B13BE,
	LitMotionAnimation_Restart_mD00836C97E7C7D5CEDD7C4FD55C170BC689C63FE,
	LitMotionAnimation_get_IsActive_m0B1FBE994BFE23FB181A193CCA45FBA31456F581,
	LitMotionAnimation_get_IsPlaying_mBC613A1EF1342DD779D8E3F5EFF1A910863E5F05,
	LitMotionAnimation_OnDestroy_m47A84B980BB95E548742B6852B39274F51808CB9,
	LitMotionAnimation__ctor_m17EB0D2D850BEB3718A200621A8543741C1C478B,
	LitMotionAnimationComponent__ctor_mD62326B8DB8585E40CFAA315FC8F3A94B8FA356B,
	LitMotionAnimationComponent_get_Enabled_m910F7930C43DDE7E7951EA1EC5767B3DE7EFF718,
	LitMotionAnimationComponent_get_DisplayName_mC4D963924B10F1CDD9F78D4E52464C89198912EC,
	NULL,
	LitMotionAnimationComponent_OnResume_m58544AC3643B1915D7CCC9C3FA958F076FC42A37,
	LitMotionAnimationComponent_OnPause_m4816CF7B8FEC93F345909CC3B5FF2F84EAA5B67E,
	LitMotionAnimationComponent_OnStop_mBF3F5B3295E59AB4210C89B341B6E0AD349CA8AC,
	LitMotionAnimationComponent_get_TrackedHandle_m5B903D2A947941090BE2F79870D73368DD0A0606,
	LitMotionAnimationComponent_set_TrackedHandle_mC6DE281FF2BE11BB93377BCB3B311226AA8CAE31,
	LitMotionAnimationComponentMenuAttribute__ctor_mFB980AD8F8EE812DBD0C7A75C376AD53DFB31462,
	LitMotionAnimationComponentMenuAttribute_get_MenuName_mEC11A3D4C87AB226A6F15C3469B96BE0FEB4FFF3,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	AudioSourceVolumeAnimation_GetValue_m8BF329DE9619DCC0E1C23A731F8D2B849C2D6471,
	AudioSourceVolumeAnimation_SetValue_m6C23E9337AD89C1237F2CF65360333206595A496,
	AudioSourceVolumeAnimation__ctor_m25000CB1A95A753C109E667EF5FDE5F9239780F2,
	AudioSourcePitchAnimation_GetValue_m678A92D5A613E2F9DF330A43C7412202DB68B8C4,
	AudioSourcePitchAnimation_SetValue_m319255C814C45450FA7F626DED27FDB8568E96F4,
	AudioSourcePitchAnimation__ctor_mD45A1C54E1C33173DBDBAC0BFD420042D5E73379,
	CameraAspectAnimation_GetValue_mB2F65796DBD88DBB0495E1BB99C3DCFC6C979BB5,
	CameraAspectAnimation_SetValue_mAF5484690CE01F2FA34B5922698A98779CCABF99,
	CameraAspectAnimation__ctor_mCF081386A5F8DBD9E6DA9677E8F737EFDC5B93E4,
	CameraNearClipPlaneAnimation_GetValue_mFCF2557AB6EB89787D43288DC74DD6F92DD96D62,
	CameraNearClipPlaneAnimation_SetValue_mCC13EE87F12433A9B6D097B59A1D1BB6E6B1CC8B,
	CameraNearClipPlaneAnimation__ctor_mD09AD373B26E3D40BB6B2D418AED3B0535444F40,
	CameraFarClipPlaneAnimation_GetValue_m89401C1C8CC4CEC71CBC1583DF386285FF484A92,
	CameraFarClipPlaneAnimation_SetValue_m1F2FD5F4C72B64C1A2B6E2ADC212D9EEF5A5B9A9,
	CameraFarClipPlaneAnimation__ctor_m053D594578414E468D4EAD4B1D808E1D4D182CAF,
	CameraFieldOfViewAnimation_GetValue_m6A0CBFCC6AA1CBC5386C161D45CD5B8032952B13,
	CameraFieldOfViewAnimation_SetValue_mC1BEB8E9F3329270644F17281CA2117CB9D04A8D,
	CameraFieldOfViewAnimation__ctor_m99A290058C2B174F6996C95F2C79FE2AB8A47B2B,
	CameraOrthographicSizeAnimation_GetValue_mDE03A8F9B59FA5FEDF19665EAA82BE6D57D7928D,
	CameraOrthographicSizeAnimation_SetValue_m739E7F8FB599195BF88FE7ADCDA117987EC3403F,
	CameraOrthographicSizeAnimation__ctor_m37AB2469AF8399CD0E00EC6D2EA175465C03DB07,
	CameraRectAnimation_GetValue_mA7F60A3EA3B78869B56569046D0420722974174A,
	CameraRectAnimation_SetValue_m1B84BFE29522923A31937F3A7B5696E9672723BE,
	CameraRectAnimation__ctor_m3395A4EEE94E4472024B10C7B14572658857AC8A,
	CameraPixelRectAnimation_GetValue_mBD00F2EFAF43C862C13BBACE0916CAC52DBA4B47,
	CameraPixelRectAnimation_SetValue_m0B9FD0055D0815BF8F5116B57C447729FA347802,
	CameraPixelRectAnimation__ctor_m6D28F37BF6F2751A9CB88831907C24155FBE8453,
	CameraBackgroundColorAnimation_GetValue_m31BE05B04683C476A5CA5050F09294848C8E12C1,
	CameraBackgroundColorAnimation_SetValue_m411AC4DCB8D4ED18AE96ACFAEDD24E799E732289,
	CameraBackgroundColorAnimation__ctor_mA749FF07FCC9FC17152E9A3443E995347F13F0AF,
	DelayComponent_Play_m3F91995ABA2874838D9D918ABC5415DD9E521BAB,
	DelayComponent_OnStop_mE97AC1EF7713C0166CC6E5491643C200FA8E36B1,
	DelayComponent__ctor_mB00D0CC724B87AF745CCC79983A445B77F3DC977,
	EventComponent_Play_mD9F8DC42830A7BA0A418342DD035B67D27492B06,
	EventComponent_OnStop_m4F401B9C7C96D2DDEBC8E34661147F920E6DFFE2,
	EventComponent__ctor_m17720E8105FC1A33080C19C16BEA0E1EAC3BE080,
	PlayLitMotionAnimationComponent_Play_m7F8186399E3C5A58C964A05F673BE3EAA447E199,
	PlayLitMotionAnimationComponent_OnResume_m700E111782AC225D624070F53C12F87F75E6EF3F,
	PlayLitMotionAnimationComponent_OnPause_m0E3614B876F3B5D8FA3B5798DDA995194A3FFCCF,
	PlayLitMotionAnimationComponent_OnStop_m6ADBA0EA4AA6EE4562F45A2FEA91333ED852D9A4,
	PlayLitMotionAnimationComponent__ctor_mAA68F59ACD285F2E25D516A081503E70C16957F7,
	PlayLitMotionAnimationComponent_U3CPlayU3Eb__1_0_m8B3A9D036CC26B921B03359877171C5B8F39CF4E,
	MaterialFloatAnimation_GetValue_mC9281C689D2D7C7D706153F3A885E3FA2C7FD469,
	MaterialFloatAnimation_SetValue_mF451A3A973CA19534A8DBA5996E964B1F04863E3,
	MaterialFloatAnimation__ctor_m1AE831576A3226E8C5EEBFC1271CCE58C50261F4,
	MaterialIntAnimation_GetValue_m7C5901F45C8D9980DB6D44F1A3728501B9AB630C,
	MaterialIntAnimation_SetValue_m6E13273971C4761F22B6BDB773851363EFE7272A,
	MaterialIntAnimation__ctor_m01E1DB0656117DCCA165CE82D93917092B94572A,
	MaterialVectorAnimation_GetValue_m5A1BC717030106C681FF2F6332ABCB30C54FCE3D,
	MaterialVectorAnimation_SetValue_m16E33B65AE12CFE8C3439C130284E5073CB55DCD,
	MaterialVectorAnimation__ctor_m969F0AF882C11E94A3B47C5E1A2099CE12CFA547,
	MaterialColorAnimation_GetValue_mF7E2B9141566DFF9B4CA9B80BCC019985E1EF285,
	MaterialColorAnimation_SetValue_m2A399FEDA21CBAC9EA4679C4ED815B89D09F3125,
	MaterialColorAnimation__ctor_mC93EA57970134869E98AEF99D233CFD13238EE37,
	SpriteRendererColorAnimation_GetValue_mFCE79EDAD5702719670F9A63EEE03C3605B6D90C,
	SpriteRendererColorAnimation_SetValue_m855981BECBAF9D6601927212C0BC64287530FADE,
	SpriteRendererColorAnimation__ctor_m6CE828A5751B7AB0A00DE6FA07844059C9FA3B19,
	VolumeWeightAnimation_GetValue_mE05D0E9F954AC84043852EA736BF1C143D9344D3,
	VolumeWeightAnimation_SetValue_mF6C4D9429A04BD51E156C42663EEB8C6BFC55019,
	VolumeWeightAnimation__ctor_mBDE0BF73D81E3F87A295CB6B8FDF51A0D9C0CAB9,
	NULL,
	NULL,
	NULL,
	NULL,
	Rigidbody2DPositionAnimation__ctor_m09D70B6A6BE5891C63B89E9544F054507AEADAC0,
	Rigidbody2DPositionPunchAnimation__ctor_mEB018B7047644AACA9C76896312F4F08B21C99DA,
	Rigidbody2DPositionShakeAnimation__ctor_m165BB68F36998824D0F29111A66A460529795F38,
	NULL,
	NULL,
	NULL,
	NULL,
	Rigidbody2DRotationAnimation__ctor_m473389D364DEA5868735B2DD3D6AC832631A13FC,
	Rigidbody2DRotationPunchAnimation__ctor_m405B03D393E2A81609644454D3684A88B4B66F57,
	Rigidbody2DRotationShakeAnimation__ctor_mD53EA361A92A6AB113DE2E2FE0FC615E4EA96D43,
	NULL,
	NULL,
	NULL,
	NULL,
	RigidbodyPositionAnimation__ctor_m1882F91F2244565437466E2B533C194619CA1E6D,
	RigidbodyPositionPunchAnimation__ctor_m46F5FDC977E89382B0D4D6EAE5109AE28CCE73AA,
	RigidbodyPositionShakeAnimation__ctor_m1DE452B65A8A8A93D3A7C847DEB570047E6FAE17,
	NULL,
	NULL,
	NULL,
	NULL,
	RigidbodyRotationAnimation__ctor_mA94BF194AD0899B93733595768AC4B6CCCAF2AEE,
	RigidbodyRotationPunchAnimation__ctor_m5A9ED12A880FEA940F27A2B250D24D85B1122F76,
	RigidbodyRotationShakeAnimation__ctor_m73DF76DD05E3E869D070CF02631E97B98D32D78B,
	TMPTextAnimation_GetValue_m1A9253407828CD9A271E728CE5B8742BB88FD226,
	TMPTextAnimation_SetValue_m6EB1FAAD752E90EDDD48539959D5FEF6AF38295C,
	TMPTextAnimation__ctor_m083C2B177D26F479A531C951A201DA679D1E93D9,
	TMPTextCharacterSpacingAnimation_GetValue_mC757EF527CE4D02D69BCFCECD6054C55BAB76718,
	TMPTextCharacterSpacingAnimation_SetValue_mCDA746D1BB0C80C50CA7103763ED14BCA4A8C6DE,
	TMPTextCharacterSpacingAnimation__ctor_m694CA6949B6B3E5D7067E5202CB5EB5D24AC9DC8,
	TMPTextWordSpacingAnimation_GetValue_mCA8CBCC9E379337CAFA6D14E73428567D5F363CE,
	TMPTextWordSpacingAnimation_SetValue_m4D8AA237A51CC69FB0DEA75C46FCBBCA6C3F1937,
	TMPTextWordSpacingAnimation__ctor_m65DC5AAC1A7577B22703EF07250A40E497F653B7,
	TMPTextLineSpacingAnimation_GetValue_m05233598FE33596D89413A1FD01D00743A38DB7B,
	TMPTextLineSpacingAnimation_SetValue_m373729AE91AB2555B60C55B56B1E9F26FF843231,
	TMPTextLineSpacingAnimation__ctor_m9260FDF44FCD3EA447F6D86F076B30A8286AAE3F,
	TMPTextParagraphSpacingAnimation_GetValue_mD6E1E6C3A03BA44CCF1200A39E373B5809B6E91A,
	TMPTextParagraphSpacingAnimation_SetValue_mBAE2A40EC1BAE81D9998F75D82568878CCAA5ED6,
	TMPTextParagraphSpacingAnimation__ctor_mB84E4F61B30568A0B804F701F84511453C494CCB,
	TMPTextFontSizeAnimation_GetValue_m7C458EA58BEB38C0B4315EE9BE5F776BD9DDECF4,
	TMPTextFontSizeAnimation_SetValue_m9A341ADAC9A8D5A60F28B9C82AAEDDAE63EEB703,
	TMPTextFontSizeAnimation__ctor_m2350D9664C3403E9BB28D99D2843C75FE1D610FC,
	TMPTextColorAnimation_GetValue_mF3C30D6348C84401B338B08F900239E852FE3584,
	TMPTextColorAnimation_SetValue_m3D841E628CDCBD935481E73B4C4029619DC21139,
	TMPTextColorAnimation__ctor_m14D4994498C2AA1A521FFC8C0C2664412709D72C,
	TMPTextColorAlphaAnimation_GetValue_mD62B97CD9047FAA1366060EE9AA64CBFBECC011E,
	TMPTextColorAlphaAnimation_SetValue_mE3A411F9E6E47F00D334E7958D8671C20B2D486F,
	TMPTextColorAlphaAnimation__ctor_m910A471AE89548D7C868C040B74895C7007035A2,
	NULL,
	NULL,
	NULL,
	NULL,
	TransformPositionAnimation__ctor_mDAE0A5F3015E2316E2FD359B01AB10B030074CE6,
	TransformPositionPunchAnimation__ctor_mD17E2FDB3BFC1B16A74E041E363565B821000411,
	TransformPositionShakeAnimation__ctor_m260221C7FCF468E50E6029CE02117E50DAB7BA36,
	NULL,
	NULL,
	NULL,
	NULL,
	TransformRotationAnimation__ctor_mC409D029250FC3AC1D7DB420A274A8B85B736592,
	TransformRotationPunchAnimation__ctor_m75EF145CEA3F9D335EF7B81A4537FF6266E9676A,
	TransformRotationShakeAnimation__ctor_m1B7A4DE7414129E250D6B664CEB82CDCDAF852F0,
	NULL,
	NULL,
	NULL,
	NULL,
	TransformScaleAnimation__ctor_m334568E452D0775E3C56D11DA2DD189A705711DB,
	TransformScalePunchAnimation__ctor_m8FDA121800FCBF9743D192CAF49EE5ACCE52E809,
	TransformScaleShakeAnimation__ctor_m743D01D1659D867B3C13B8E01B72D261D3BF73FD,
	TextAnimation_GetValue_m8600BFFA502918A0F2142FFF08B71905EB58CA6F,
	TextAnimation_SetValue_m33F9E42F4BF1C6F4BD4E29A5109A26FAE6F8F8BC,
	TextAnimation__ctor_m587A6D6554ACC86C6C3444FFBF7F9EBC51BB36B0,
	TextColorAnimation_GetValue_mADB6804970D3F3630CB2578C15C352574ACA22FD,
	TextColorAnimation_SetValue_m913E227B8E497A3782658FFAD5AD0308A126A7A3,
	TextColorAnimation__ctor_m048855B24ECEC5A6AFBC71D9995A88CF8E279EA1,
	TextFontSizeAnimation_GetValue_m1AF051D4E265FC2C0610785E2D302B1863612DF1,
	TextFontSizeAnimation_SetValue_m99196F3D82934735A6D5FCB1451C845FABE1703C,
	TextFontSizeAnimation__ctor_m87C9A28C8A8853CC198F3111A5E314DE665695A1,
	GraphicColorAnimation_GetValue_mE8C9561CD10F966B50ACE0DEAB3F775874782E24,
	GraphicColorAnimation_SetValue_m3CB23168572E2E90EFAAF4D66D547E3861FAAA35,
	GraphicColorAnimation__ctor_m288E56671326902591010E3AB28533431B0CF290,
	ImageColorAnimation_GetValue_m6E1EDD657548A690F465E7A49C393DCAEE6153CB,
	ImageColorAnimation_SetValue_mA09FDF3568497950BD81024995A823457D80EBD5,
	ImageColorAnimation__ctor_m88E171095847625BF018289C724B34286FF30262,
	ImageColorAlphaAnimation_GetValue_mE6A08B0E3B4ECF52D2ECC04505A5F74C04DD48E4,
	ImageColorAlphaAnimation_SetValue_mFC57F8AAAC85DA0D172896A780151E8D5939AE57,
	ImageColorAlphaAnimation__ctor_mAFB1ACD9F6207441409EBF45682D8DEF60106C1D,
	ImageFillAmountAnimation_GetValue_mB62C07E93DBABFA12485068607449D1CED67D655,
	ImageFillAmountAnimation_SetValue_m8E63709BBA578130019B438D4CE88869839F8992,
	ImageFillAmountAnimation__ctor_m5DCCED55FA424D55D759E67C935CDBFF6725419E,
	SliderValueAnimation_GetValue_m9BB623607B6D753DEF65B9370DEF7C804AFF3DAA,
	SliderValueAnimation_SetValue_mE6887006C98BEC23920124938DC3EF838CDCEBED,
	SliderValueAnimation__ctor_m26F9E778C4ED855C2654EB0D20CF7B63D50F9A2A,
	CanvasGroupAlphaAnimation_GetValue_m804B59FFDBA9BDD2EB2A29DE43406564F42BCD9E,
	CanvasGroupAlphaAnimation_SetValue_m97E2BA49705950C6DDB62791E0E06EFF171C8512,
	CanvasGroupAlphaAnimation__ctor_mDF253ED1F8ABF5CA03225ACA4C490C7AAE89A041,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	FloatValueAnimation__ctor_mE153CF3E7794FDAB805AA6BD6320991BA80068B8,
	DoubleValueAnimation__ctor_m056B5D3F68A271AA480DD0EA0A60F7780CF2C0D6,
	IntValueAnimation__ctor_m352EA06516720DA4BE2AB2C883094B776D1A0237,
	LongValueAnimation__ctor_m153F089490214E4DB4C01540D6B4304E004B9135,
	Vector2ValueAnimation__ctor_m136E4D95ABF8CB2785A23E77BFC4EC8F900252DA,
	Vector3ValueAnimation__ctor_m301EC277CBC1EE37B80A0290AFF38347C3CEAFAD,
	Vector4ValueAnimation__ctor_mBE48AD9477B7A05A511D3B0B15F4EE9DCA82DA49,
	ColorValueAnimation__ctor_m4C8AE07F352EC0DC93EB4D29D162B812A3E95937,
	StringValueAnimation_Play_m2341ABE671694F0E753497A0FF8E7A16EB73C68E,
	StringValueAnimation_OnStop_mF02A154A26B0A52CA9BBD83A6C5C4238E664A914,
	StringValueAnimation__ctor_m9D852268322B756973640E0954A6FAF6A95A3417,
	U3CU3Ec__cctor_m1B8082DD774DBB205D93FCE9C36A71817E2FF5B8,
	U3CU3Ec__ctor_m6282724279A29E01D74080870544C26EA3CF5E66,
	U3CU3Ec_U3CPlayU3Eb__2_0_mF360A40509FD2F30750DCF886F357F69F1629B7B,
};
static const int32_t s_InvokerIndices[240] = 
{
	13298,
	13298,
	9584,
	5662,
	13298,
	9584,
	5662,
	13298,
	21387,
	13298,
	13052,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	12815,
	12815,
	13298,
	13298,
	13298,
	12815,
	13052,
	0,
	13298,
	13298,
	13298,
	13045,
	10674,
	10682,
	13052,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	9457,
	5662,
	13298,
	9457,
	5662,
	13298,
	9457,
	5662,
	13298,
	9457,
	5662,
	13298,
	9457,
	5662,
	13298,
	9457,
	5662,
	13298,
	9457,
	5662,
	13298,
	9339,
	5662,
	13298,
	9339,
	5662,
	13298,
	8119,
	5662,
	13298,
	13045,
	13298,
	13298,
	13045,
	13298,
	13298,
	13045,
	13298,
	13298,
	13298,
	13298,
	5783,
	9457,
	5662,
	13298,
	8845,
	5662,
	13298,
	9612,
	5662,
	13298,
	8119,
	5662,
	13298,
	8119,
	5662,
	13298,
	9457,
	5662,
	13298,
	0,
	0,
	0,
	0,
	13298,
	13298,
	13298,
	0,
	0,
	0,
	0,
	13298,
	13298,
	13298,
	0,
	0,
	0,
	0,
	13298,
	13298,
	13298,
	0,
	0,
	0,
	0,
	13298,
	13298,
	13298,
	8234,
	5662,
	13298,
	9457,
	5662,
	13298,
	9457,
	5662,
	13298,
	9457,
	5662,
	13298,
	9457,
	5662,
	13298,
	9457,
	5662,
	13298,
	8119,
	5662,
	13298,
	9457,
	5662,
	13298,
	0,
	0,
	0,
	0,
	13298,
	13298,
	13298,
	0,
	0,
	0,
	0,
	13298,
	13298,
	13298,
	0,
	0,
	0,
	0,
	13298,
	13298,
	13298,
	8234,
	5662,
	13298,
	8119,
	5662,
	13298,
	8845,
	5662,
	13298,
	8119,
	5662,
	13298,
	8119,
	5662,
	13298,
	9457,
	5662,
	13298,
	9457,
	5662,
	13298,
	9457,
	5662,
	13298,
	9457,
	5662,
	13298,
	0,
	0,
	0,
	0,
	0,
	0,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13045,
	13298,
	13298,
	21355,
	13298,
	4777,
};
static const Il2CppTokenRangePair s_rgctxIndices[20] = 
{
	{ 0x0200000C, { 0, 17 } },
	{ 0x0200000D, { 17, 2 } },
	{ 0x0200000E, { 19, 2 } },
	{ 0x0200000F, { 21, 2 } },
	{ 0x02000010, { 23, 2 } },
	{ 0x02000011, { 25, 2 } },
	{ 0x02000012, { 27, 2 } },
	{ 0x02000013, { 29, 2 } },
	{ 0x02000014, { 31, 2 } },
	{ 0x02000015, { 33, 2 } },
	{ 0x02000016, { 35, 2 } },
	{ 0x0200002A, { 37, 3 } },
	{ 0x0200002E, { 40, 3 } },
	{ 0x02000032, { 43, 3 } },
	{ 0x02000036, { 46, 3 } },
	{ 0x02000042, { 49, 3 } },
	{ 0x02000046, { 52, 3 } },
	{ 0x0200004A, { 55, 2 } },
	{ 0x02000057, { 57, 12 } },
	{ 0x02000058, { 69, 7 } },
};
extern const uint32_t g_rgctx_PropertyAnimationComponent_4_tEC9C218F31B9F7A59CF7AF659D6275D2FF3741E5;
extern const uint32_t g_rgctx_TObject_tFA2911F852CD783D61DB76D34C15886173A24FFE;
extern const uint32_t g_rgctx_TValue_t3529886B5EFABD6754F277013BB63E6D28A7BC50;
extern const uint32_t g_rgctx_PropertyAnimationComponent_4_SetValue_m3E782ED365D26BCCE24FC0BCAFB7A4732EEAC012;
extern const uint32_t g_rgctx_TValueU26_t28F8A1B002718BF62AC15196C364656B233C1B29;
extern const uint32_t g_rgctx_PropertyAnimationComponent_4_GetValue_m6EE3F87C8BFBD20FF9FEDD6FE938B4556AE695FA;
extern const uint32_t g_rgctx_SerializableMotionSettings_2_t6D4C370F778AC87F53194451D90ED58006C5EF48;
extern const uint32_t g_rgctx_LMotion_Create_TisTValue_t3529886B5EFABD6754F277013BB63E6D28A7BC50_TisTOptions_t599247EF699CA77B545F4DD28087BF44310C0DD8_TisTAdapter_t205C5B4FDD55DF54ECE0B0254B6ADDC490EAE442_mBBDD50E823366459F322A5A21A40D77B38D65988;
extern const uint32_t g_rgctx_MotionSettings_2_t916E3491BA56074EF9AC156F6773AC1C5F842CD1;
extern const uint32_t g_rgctx_MotionBuilder_3_t413E2603DE0921D819E5F2D58A3A4D8F1F583CD1;
extern const uint32_t g_rgctx_PropertyAnimationComponent_4_U3CPlayU3Eb__5_0_m1787BBB1EDE00D781EA944A11BECEE3B2007ACC0;
extern const uint32_t g_rgctx_Action_2_t63ABDE6894403A65FD2DFAF9E5778A65855A023D;
extern const uint32_t g_rgctx_Action_2__ctor_mECA27C27CFC5F8616C22EFBFEA1641A76BA36E4E;
extern const uint32_t g_rgctx_MotionBuilder_3_Bind_TisPropertyAnimationComponent_4_tEC9C218F31B9F7A59CF7AF659D6275D2FF3741E5_mBB0765C0E253CEE9EEF33A7C5170DCB4146E1E0B;
extern const uint32_t g_rgctx_MotionBuilder_3_t413E2603DE0921D819E5F2D58A3A4D8F1F583CD1;
extern const uint32_t g_rgctx_PropertyAnimationComponent_4_U3CPlayU3Eb__5_1_m5A2BF377F90FAB5B2D99B63AEB81F198F040A61C;
extern const uint32_t g_rgctx_PropertyAnimationComponent_4_GetRelativeValue_m541D89D47603484E54617CC2B590A65C7FA1E523;
extern const uint32_t g_rgctx_PropertyAnimationComponent_4__ctor_m646BB18E9E3B455E963B1A652C08EDF5819361F0;
extern const uint32_t g_rgctx_PropertyAnimationComponent_4_tDF3DA488C4A1CBB358444D0AEBC1EFB0FDE022AD;
extern const uint32_t g_rgctx_PropertyAnimationComponent_4__ctor_mB23797FF220713F54B0D13785EAE0D3E2C7BE099;
extern const uint32_t g_rgctx_PropertyAnimationComponent_4_t322AFFBB6D1FEBA985F5C7C4800FF53033F73097;
extern const uint32_t g_rgctx_PropertyAnimationComponent_4__ctor_mB5A0A4AF1E7CF709DE730585DE11C584586C8D1E;
extern const uint32_t g_rgctx_PropertyAnimationComponent_4_t86981ECFA0FD07B104A635C9308E9F5BC3A690F6;
extern const uint32_t g_rgctx_PropertyAnimationComponent_4__ctor_m3DB5FF2EAB13B556048C04762334E5CE33AFE906;
extern const uint32_t g_rgctx_PropertyAnimationComponent_4_t705880B735EB1FD00DB3FF018097E2FCC5A26B2C;
extern const uint32_t g_rgctx_PropertyAnimationComponent_4__ctor_mC1BEA38ED1953D432221626D3017CCCEEF7AABCE;
extern const uint32_t g_rgctx_PropertyAnimationComponent_4_tF85D8C254DB9F10F16948289FFBE21D73016FC9B;
extern const uint32_t g_rgctx_PropertyAnimationComponent_4__ctor_m20270E6A97F6EB92CD796BA0087D1A566F7C27C4;
extern const uint32_t g_rgctx_PropertyAnimationComponent_4_tC784310925B84784A03421C2F13CDAFFA03EF7F0;
extern const uint32_t g_rgctx_PropertyAnimationComponent_4__ctor_m2156D56B502F535EFBCBC4F8F70B3A6BBDA3D908;
extern const uint32_t g_rgctx_PropertyAnimationComponent_4_tAA56740FFDD012351E92C486E27794C49A21E1EE;
extern const uint32_t g_rgctx_PropertyAnimationComponent_4__ctor_m56872C255C062A1DD6AFA0AC6259839A1218C3E3;
extern const uint32_t g_rgctx_PropertyAnimationComponent_4_tF03B00C7F29F6F9C02FB313B634B0BB7139D5FE5;
extern const uint32_t g_rgctx_PropertyAnimationComponent_4__ctor_m0E05773E933C7D6DE80348A8987859C110436007;
extern const uint32_t g_rgctx_PropertyAnimationComponent_4_t1AA2800614B78F1D5CE34D4453EDE71A1B18A358;
extern const uint32_t g_rgctx_PropertyAnimationComponent_4__ctor_m861BCA17FD783BF1F83FBC773F321DAFE79F4C8A;
extern const uint32_t g_rgctx_PropertyAnimationComponent_4_t188CB8BA0D27A95618D0F01656DFA0EA2DA9B121;
extern const uint32_t g_rgctx_Rigidbody2DPositionAnimationBase_2_tFDCCEC3B3FF4A848D1FE19D857F4501DC234B5E7;
extern const uint32_t g_rgctx_PropertyAnimationComponent_4__ctor_mD483AFAC045FC3558A62878AD1F2A3ADEED90EB0;
extern const uint32_t g_rgctx_PropertyAnimationComponent_4_t4E7C9E422974851992A356CA577CE66374D302E1;
extern const uint32_t g_rgctx_Rigidbody2DRotationAnimationBase_2_t71F684EC757B36B652EEE9D373FE70DD597DE635;
extern const uint32_t g_rgctx_PropertyAnimationComponent_4__ctor_mE59A41633A27288D8EDD800C0377073D004D12D8;
extern const uint32_t g_rgctx_PropertyAnimationComponent_4_tCA6444E01983A002DDC033AA93881ECA599D8228;
extern const uint32_t g_rgctx_RigidbodyPositionAnimationBase_2_t190A5B2A93FE0515A55A991D346F09B16915D321;
extern const uint32_t g_rgctx_PropertyAnimationComponent_4__ctor_m108B1CCB3B25D12E05A8C0BE177F7A75EA52BB54;
extern const uint32_t g_rgctx_PropertyAnimationComponent_4_tBE7F91E6751B9A6D89C7D2AD59948B29D4E6211F;
extern const uint32_t g_rgctx_RigidbodyRotationAnimationBase_2_t8157CA8F457AF7BAF54E22C41D67F6E082979346;
extern const uint32_t g_rgctx_PropertyAnimationComponent_4__ctor_mDBF9D4C790C2F23D9E6FE5031B827E9A1357DD1F;
extern const uint32_t g_rgctx_PropertyAnimationComponent_4_tEE38B3ABDE0F3E398CE461B5442561DDD5413629;
extern const uint32_t g_rgctx_TransformPositionAnimationBase_2_t9BC203A7E2E71215F7511D289DC05A27C02FEBBA;
extern const uint32_t g_rgctx_PropertyAnimationComponent_4__ctor_m609801D7391B54F0E15412EC5EEF45FAB7BDB8AE;
extern const uint32_t g_rgctx_PropertyAnimationComponent_4_tDEA2EDFCA0AEF98D48201D137B8DA3C388D6DCC1;
extern const uint32_t g_rgctx_TransformRotationAnimationBase_2_t7F09F954DAE234290C09810142054B4D79CF87C9;
extern const uint32_t g_rgctx_PropertyAnimationComponent_4__ctor_mCC658DF78CE8255FAA05FE874DD2A5241FB29878;
extern const uint32_t g_rgctx_PropertyAnimationComponent_4_tB9EAD51BB386E125C75DCA1F61D1CCB59639A766;
extern const uint32_t g_rgctx_PropertyAnimationComponent_4__ctor_m37D5F5954F82D2B7D91FC02F0E82B7A6F53E3A5A;
extern const uint32_t g_rgctx_PropertyAnimationComponent_4_tED1DC77A09C69AE9F32EBF32ADFE6DC0D534D354;
extern const uint32_t g_rgctx_ValueAnimationComponent_3_t4ACB8810E6D1BD26D7D9A8EE64DD97FADDA0EBD3;
extern const uint32_t g_rgctx_SerializableMotionSettings_2_t816CA466CD76DF0037E59B954565BCD1D65C686E;
extern const uint32_t g_rgctx_LMotion_Create_TisTValue_tA103C8E2EBAD61F83315A30D0DBCEB60D848DB0D_TisTOptions_t955E98711E0C83B3D6ED2572A1E511C507C47E7C_TisTAdapter_t0EDC67E6F63F9169E93515B42871404E69048870_mECE6D3AA104BB53D464B9F26AC428395374B1382;
extern const uint32_t g_rgctx_MotionSettings_2_t99C5F49589B6854E11E6F5B6D6CDBC8D91F31D9C;
extern const uint32_t g_rgctx_MotionBuilder_3_t357C08188F824A124BD16A6576169CD24376CF6D;
extern const uint32_t g_rgctx_U3CU3Ec_tFAD97BDC918087CC738F3D159B4A0300D0FD02AE;
extern const uint32_t g_rgctx_Action_2_t4510FFA1C494F9DCF1E3578D18130B8AF6EBC1EB;
extern const uint32_t g_rgctx_U3CU3Ec_tFAD97BDC918087CC738F3D159B4A0300D0FD02AE;
extern const uint32_t g_rgctx_U3CU3Ec_U3CPlayU3Eb__2_0_m9079983AE60417FBF49183A1B20DCC973A943E8B;
extern const uint32_t g_rgctx_Action_2__ctor_m401AD222860E5DC79C64C3FBD51A3FE096FA1AF6;
extern const uint32_t g_rgctx_MotionBuilder_3_Bind_TisValueAnimationComponent_3_t4ACB8810E6D1BD26D7D9A8EE64DD97FADDA0EBD3_mF9DB9FB5AF3D24669AA681446A6BF4B2A44D04CB;
extern const uint32_t g_rgctx_MotionBuilder_3_t357C08188F824A124BD16A6576169CD24376CF6D;
extern const uint32_t g_rgctx_U3CU3Ec_t0EAC76168734AB15A90614BDB7C06EE31AB7D8B0;
extern const uint32_t g_rgctx_U3CU3Ec__ctor_mEC0A1EABC6513880270133976DCC1340D2F21A6E;
extern const uint32_t g_rgctx_U3CU3Ec_t0EAC76168734AB15A90614BDB7C06EE31AB7D8B0;
extern const uint32_t g_rgctx_ValueAnimationComponent_3_t51972CBB4D296BE800CA494B4C5A26B04DFA2398;
extern const uint32_t g_rgctx_UnityEvent_1_t73408E78C95E45047D60B7CA98515C8F855586EA;
extern const uint32_t g_rgctx_TValue_t48B2501A93D37BDDF6A0766F5BC5C99A39AEF3F5;
extern const uint32_t g_rgctx_UnityEvent_1_Invoke_m603C2226FCC858EB69E3B7070C53E87488185108;
static const Il2CppRGCTXDefinition s_rgctxValues[76] = 
{
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_PropertyAnimationComponent_4_tEC9C218F31B9F7A59CF7AF659D6275D2FF3741E5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TObject_tFA2911F852CD783D61DB76D34C15886173A24FFE },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TValue_t3529886B5EFABD6754F277013BB63E6D28A7BC50 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_PropertyAnimationComponent_4_SetValue_m3E782ED365D26BCCE24FC0BCAFB7A4732EEAC012 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TValueU26_t28F8A1B002718BF62AC15196C364656B233C1B29 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_PropertyAnimationComponent_4_GetValue_m6EE3F87C8BFBD20FF9FEDD6FE938B4556AE695FA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_SerializableMotionSettings_2_t6D4C370F778AC87F53194451D90ED58006C5EF48 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_LMotion_Create_TisTValue_t3529886B5EFABD6754F277013BB63E6D28A7BC50_TisTOptions_t599247EF699CA77B545F4DD28087BF44310C0DD8_TisTAdapter_t205C5B4FDD55DF54ECE0B0254B6ADDC490EAE442_mBBDD50E823366459F322A5A21A40D77B38D65988 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MotionSettings_2_t916E3491BA56074EF9AC156F6773AC1C5F842CD1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MotionBuilder_3_t413E2603DE0921D819E5F2D58A3A4D8F1F583CD1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_PropertyAnimationComponent_4_U3CPlayU3Eb__5_0_m1787BBB1EDE00D781EA944A11BECEE3B2007ACC0 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_2_t63ABDE6894403A65FD2DFAF9E5778A65855A023D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Action_2__ctor_mECA27C27CFC5F8616C22EFBFEA1641A76BA36E4E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MotionBuilder_3_Bind_TisPropertyAnimationComponent_4_tEC9C218F31B9F7A59CF7AF659D6275D2FF3741E5_mBB0765C0E253CEE9EEF33A7C5170DCB4146E1E0B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MotionBuilder_3_t413E2603DE0921D819E5F2D58A3A4D8F1F583CD1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_PropertyAnimationComponent_4_U3CPlayU3Eb__5_1_m5A2BF377F90FAB5B2D99B63AEB81F198F040A61C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_PropertyAnimationComponent_4_GetRelativeValue_m541D89D47603484E54617CC2B590A65C7FA1E523 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_PropertyAnimationComponent_4__ctor_m646BB18E9E3B455E963B1A652C08EDF5819361F0 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_PropertyAnimationComponent_4_tDF3DA488C4A1CBB358444D0AEBC1EFB0FDE022AD },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_PropertyAnimationComponent_4__ctor_mB23797FF220713F54B0D13785EAE0D3E2C7BE099 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_PropertyAnimationComponent_4_t322AFFBB6D1FEBA985F5C7C4800FF53033F73097 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_PropertyAnimationComponent_4__ctor_mB5A0A4AF1E7CF709DE730585DE11C584586C8D1E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_PropertyAnimationComponent_4_t86981ECFA0FD07B104A635C9308E9F5BC3A690F6 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_PropertyAnimationComponent_4__ctor_m3DB5FF2EAB13B556048C04762334E5CE33AFE906 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_PropertyAnimationComponent_4_t705880B735EB1FD00DB3FF018097E2FCC5A26B2C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_PropertyAnimationComponent_4__ctor_mC1BEA38ED1953D432221626D3017CCCEEF7AABCE },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_PropertyAnimationComponent_4_tF85D8C254DB9F10F16948289FFBE21D73016FC9B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_PropertyAnimationComponent_4__ctor_m20270E6A97F6EB92CD796BA0087D1A566F7C27C4 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_PropertyAnimationComponent_4_tC784310925B84784A03421C2F13CDAFFA03EF7F0 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_PropertyAnimationComponent_4__ctor_m2156D56B502F535EFBCBC4F8F70B3A6BBDA3D908 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_PropertyAnimationComponent_4_tAA56740FFDD012351E92C486E27794C49A21E1EE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_PropertyAnimationComponent_4__ctor_m56872C255C062A1DD6AFA0AC6259839A1218C3E3 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_PropertyAnimationComponent_4_tF03B00C7F29F6F9C02FB313B634B0BB7139D5FE5 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_PropertyAnimationComponent_4__ctor_m0E05773E933C7D6DE80348A8987859C110436007 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_PropertyAnimationComponent_4_t1AA2800614B78F1D5CE34D4453EDE71A1B18A358 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_PropertyAnimationComponent_4__ctor_m861BCA17FD783BF1F83FBC773F321DAFE79F4C8A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_PropertyAnimationComponent_4_t188CB8BA0D27A95618D0F01656DFA0EA2DA9B121 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Rigidbody2DPositionAnimationBase_2_tFDCCEC3B3FF4A848D1FE19D857F4501DC234B5E7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_PropertyAnimationComponent_4__ctor_mD483AFAC045FC3558A62878AD1F2A3ADEED90EB0 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_PropertyAnimationComponent_4_t4E7C9E422974851992A356CA577CE66374D302E1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Rigidbody2DRotationAnimationBase_2_t71F684EC757B36B652EEE9D373FE70DD597DE635 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_PropertyAnimationComponent_4__ctor_mE59A41633A27288D8EDD800C0377073D004D12D8 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_PropertyAnimationComponent_4_tCA6444E01983A002DDC033AA93881ECA599D8228 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_RigidbodyPositionAnimationBase_2_t190A5B2A93FE0515A55A991D346F09B16915D321 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_PropertyAnimationComponent_4__ctor_m108B1CCB3B25D12E05A8C0BE177F7A75EA52BB54 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_PropertyAnimationComponent_4_tBE7F91E6751B9A6D89C7D2AD59948B29D4E6211F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_RigidbodyRotationAnimationBase_2_t8157CA8F457AF7BAF54E22C41D67F6E082979346 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_PropertyAnimationComponent_4__ctor_mDBF9D4C790C2F23D9E6FE5031B827E9A1357DD1F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_PropertyAnimationComponent_4_tEE38B3ABDE0F3E398CE461B5442561DDD5413629 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TransformPositionAnimationBase_2_t9BC203A7E2E71215F7511D289DC05A27C02FEBBA },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_PropertyAnimationComponent_4__ctor_m609801D7391B54F0E15412EC5EEF45FAB7BDB8AE },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_PropertyAnimationComponent_4_tDEA2EDFCA0AEF98D48201D137B8DA3C388D6DCC1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TransformRotationAnimationBase_2_t7F09F954DAE234290C09810142054B4D79CF87C9 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_PropertyAnimationComponent_4__ctor_mCC658DF78CE8255FAA05FE874DD2A5241FB29878 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_PropertyAnimationComponent_4_tB9EAD51BB386E125C75DCA1F61D1CCB59639A766 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_PropertyAnimationComponent_4__ctor_m37D5F5954F82D2B7D91FC02F0E82B7A6F53E3A5A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_PropertyAnimationComponent_4_tED1DC77A09C69AE9F32EBF32ADFE6DC0D534D354 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ValueAnimationComponent_3_t4ACB8810E6D1BD26D7D9A8EE64DD97FADDA0EBD3 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_SerializableMotionSettings_2_t816CA466CD76DF0037E59B954565BCD1D65C686E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_LMotion_Create_TisTValue_tA103C8E2EBAD61F83315A30D0DBCEB60D848DB0D_TisTOptions_t955E98711E0C83B3D6ED2572A1E511C507C47E7C_TisTAdapter_t0EDC67E6F63F9169E93515B42871404E69048870_mECE6D3AA104BB53D464B9F26AC428395374B1382 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MotionSettings_2_t99C5F49589B6854E11E6F5B6D6CDBC8D91F31D9C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MotionBuilder_3_t357C08188F824A124BD16A6576169CD24376CF6D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec_tFAD97BDC918087CC738F3D159B4A0300D0FD02AE },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_2_t4510FFA1C494F9DCF1E3578D18130B8AF6EBC1EB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec_tFAD97BDC918087CC738F3D159B4A0300D0FD02AE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec_U3CPlayU3Eb__2_0_m9079983AE60417FBF49183A1B20DCC973A943E8B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Action_2__ctor_m401AD222860E5DC79C64C3FBD51A3FE096FA1AF6 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_MotionBuilder_3_Bind_TisValueAnimationComponent_3_t4ACB8810E6D1BD26D7D9A8EE64DD97FADDA0EBD3_mF9DB9FB5AF3D24669AA681446A6BF4B2A44D04CB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_MotionBuilder_3_t357C08188F824A124BD16A6576169CD24376CF6D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec_t0EAC76168734AB15A90614BDB7C06EE31AB7D8B0 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_U3CU3Ec__ctor_mEC0A1EABC6513880270133976DCC1340D2F21A6E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_U3CU3Ec_t0EAC76168734AB15A90614BDB7C06EE31AB7D8B0 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ValueAnimationComponent_3_t51972CBB4D296BE800CA494B4C5A26B04DFA2398 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_UnityEvent_1_t73408E78C95E45047D60B7CA98515C8F855586EA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TValue_t48B2501A93D37BDDF6A0766F5BC5C99A39AEF3F5 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnityEvent_1_Invoke_m603C2226FCC858EB69E3B7070C53E87488185108 },
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_LitMotion_Animation_CodeGenModule;
const Il2CppCodeGenModule g_LitMotion_Animation_CodeGenModule = 
{
	"LitMotion.Animation.dll",
	240,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	20,
	s_rgctxIndices,
	76,
	s_rgctxValues,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};

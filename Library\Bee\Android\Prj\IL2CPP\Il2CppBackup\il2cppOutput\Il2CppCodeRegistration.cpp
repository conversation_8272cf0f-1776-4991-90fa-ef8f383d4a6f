﻿#include "pch-cpp.hpp"

#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif







IL2CPP_EXTERN_C const Il2CppMethodPointer g_ReversePInvokeWrapperPointers[];
IL2CPP_EXTERN_C const Il2CppMethodPointer g_Il2CppGenericMethodPointers[];
IL2CPP_EXTERN_C const Il2CppMethodPointer g_Il2CppGenericAdjustorThunks[];
IL2CPP_EXTERN_C const InvokerMethod g_Il2CppInvokerPointers[];
IL2CPP_EXTERN_C const Il2CppMethodPointer g_UnresolvedVirtualMethodPointers[];
IL2CPP_EXTERN_C const Il2CppMethodPointer g_UnresolvedInstanceMethodPointers[];
IL2CPP_EXTERN_C const Il2CppMethodPointer g_UnresolvedStaticMethodPointers[];
IL2CPP_EXTERN_C Il2CppInteropData g_Il2CppInteropData[];
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_AK_Wwise_Unity_API_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_AK_Wwise_Unity_API_WwiseTypes_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_AK_Wwise_Unity_MonoBehaviour_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_AK_Wwise_Unity_Timeline_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_Ak_Wwise_Api_WAAPI_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_AllIn1VfxAssmebly_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_AllIn1VfxDemoScriptAssemblies_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_AllIn1VfxTexDemoAssembly_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_AssemblyU2DCSharpU2Dfirstpass_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_AssemblyU2DCSharp_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_CallbackManager_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_Clipper2Lib_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_Events_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_Google_FlatBuffers_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_KinoBloom_Runtime_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_LitMotion_Animation_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_LitMotion_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_LitMotion_Extensions_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_Localization_Runtime_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_Luban_Runtime_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_MeshUI_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_Mono_Security_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_NLog_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_Polyperfect_Common_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_Polyperfect_War_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_SimpleTouch_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_Singleton_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_System_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_System_Configuration_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_System_Core_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_System_IO_Compression_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_System_Numerics_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_System_Runtime_CompilerServices_Unsafe_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_System_Xml_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_TaskScheduler_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_ToonyColorsPro2_Demo_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_ToonyColorsPro_Runtime_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_AIModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_AndroidJNIModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_AnimationModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_AssetBundleModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_AudioModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_CoreModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_DirectorModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_GridModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_IMGUIModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_ImageConversionModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_InputLegacyModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_InputModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_JSONSerializeModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_ParticleSystemModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_Physics2DModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_PhysicsModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_PropertiesModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_SharedInternalsModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_SpriteMaskModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_SpriteShapeModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_SubsystemsModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_TerrainModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_TerrainPhysicsModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_TextCoreFontEngineModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_TextCoreTextEngineModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_TextRenderingModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_TilemapModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_UIElementsModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_UIModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_UI_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_UnityAnalyticsModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_UnityWebRequestModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_UnityWebRequestWWWModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_VFXModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_VRModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_VehiclesModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_VideoModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_UnityEngine_XRModule_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_Unity_Burst_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_Unity_Burst_Unsafe_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_Unity_Collections_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_Unity_Collections_LowLevel_ILSupport_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_Unity_Mathematics_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_Unity_RenderPipeline_Universal_ShaderLibrary_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_Unity_RenderPipelines_Core_Runtime_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_Unity_RenderPipelines_Universal_Runtime_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_Unity_TextMeshPro_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_Unity_Timeline_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_Unity_VisualEffectGraph_Runtime_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g___Generated_CodeGenModule;
IL2CPP_EXTERN_C_CONST Il2CppCodeGenModule g_mscorlib_CodeGenModule;
IL2CPP_EXTERN_C const Il2CppCodeGenModule* g_CodeGenModules[];
const Il2CppCodeGenModule* g_CodeGenModules[89] = 
{
	(&g_AK_Wwise_Unity_API_CodeGenModule),
	(&g_AK_Wwise_Unity_API_WwiseTypes_CodeGenModule),
	(&g_AK_Wwise_Unity_MonoBehaviour_CodeGenModule),
	(&g_AK_Wwise_Unity_Timeline_CodeGenModule),
	(&g_Ak_Wwise_Api_WAAPI_CodeGenModule),
	(&g_AllIn1VfxAssmebly_CodeGenModule),
	(&g_AllIn1VfxDemoScriptAssemblies_CodeGenModule),
	(&g_AllIn1VfxTexDemoAssembly_CodeGenModule),
	(&g_AssemblyU2DCSharpU2Dfirstpass_CodeGenModule),
	(&g_AssemblyU2DCSharp_CodeGenModule),
	(&g_CallbackManager_CodeGenModule),
	(&g_Clipper2Lib_CodeGenModule),
	(&g_Events_CodeGenModule),
	(&g_Google_FlatBuffers_CodeGenModule),
	(&g_KinoBloom_Runtime_CodeGenModule),
	(&g_LitMotion_Animation_CodeGenModule),
	(&g_LitMotion_CodeGenModule),
	(&g_LitMotion_Extensions_CodeGenModule),
	(&g_Localization_Runtime_CodeGenModule),
	(&g_Luban_Runtime_CodeGenModule),
	(&g_MeshUI_CodeGenModule),
	(&g_Mono_Security_CodeGenModule),
	(&g_NLog_CodeGenModule),
	(&g_Polyperfect_Common_CodeGenModule),
	(&g_Polyperfect_War_CodeGenModule),
	(&g_SimpleTouch_CodeGenModule),
	(&g_Singleton_CodeGenModule),
	(&g_System_CodeGenModule),
	(&g_System_Configuration_CodeGenModule),
	(&g_System_Core_CodeGenModule),
	(&g_System_IO_Compression_CodeGenModule),
	(&g_System_Numerics_CodeGenModule),
	(&g_System_Runtime_CompilerServices_Unsafe_CodeGenModule),
	(&g_System_Xml_CodeGenModule),
	(&g_TaskScheduler_CodeGenModule),
	(&g_ToonyColorsPro2_Demo_CodeGenModule),
	(&g_ToonyColorsPro_Runtime_CodeGenModule),
	(&g_UnityEngine_AIModule_CodeGenModule),
	(&g_UnityEngine_AndroidJNIModule_CodeGenModule),
	(&g_UnityEngine_AnimationModule_CodeGenModule),
	(&g_UnityEngine_AssetBundleModule_CodeGenModule),
	(&g_UnityEngine_AudioModule_CodeGenModule),
	(&g_UnityEngine_CodeGenModule),
	(&g_UnityEngine_CoreModule_CodeGenModule),
	(&g_UnityEngine_DirectorModule_CodeGenModule),
	(&g_UnityEngine_GridModule_CodeGenModule),
	(&g_UnityEngine_IMGUIModule_CodeGenModule),
	(&g_UnityEngine_ImageConversionModule_CodeGenModule),
	(&g_UnityEngine_InputLegacyModule_CodeGenModule),
	(&g_UnityEngine_InputModule_CodeGenModule),
	(&g_UnityEngine_JSONSerializeModule_CodeGenModule),
	(&g_UnityEngine_ParticleSystemModule_CodeGenModule),
	(&g_UnityEngine_Physics2DModule_CodeGenModule),
	(&g_UnityEngine_PhysicsModule_CodeGenModule),
	(&g_UnityEngine_PropertiesModule_CodeGenModule),
	(&g_UnityEngine_SharedInternalsModule_CodeGenModule),
	(&g_UnityEngine_SpriteMaskModule_CodeGenModule),
	(&g_UnityEngine_SpriteShapeModule_CodeGenModule),
	(&g_UnityEngine_SubsystemsModule_CodeGenModule),
	(&g_UnityEngine_TerrainModule_CodeGenModule),
	(&g_UnityEngine_TerrainPhysicsModule_CodeGenModule),
	(&g_UnityEngine_TextCoreFontEngineModule_CodeGenModule),
	(&g_UnityEngine_TextCoreTextEngineModule_CodeGenModule),
	(&g_UnityEngine_TextRenderingModule_CodeGenModule),
	(&g_UnityEngine_TilemapModule_CodeGenModule),
	(&g_UnityEngine_UIElementsModule_CodeGenModule),
	(&g_UnityEngine_UIModule_CodeGenModule),
	(&g_UnityEngine_UI_CodeGenModule),
	(&g_UnityEngine_UnityAnalyticsModule_CodeGenModule),
	(&g_UnityEngine_UnityWebRequestModule_CodeGenModule),
	(&g_UnityEngine_UnityWebRequestWWWModule_CodeGenModule),
	(&g_UnityEngine_VFXModule_CodeGenModule),
	(&g_UnityEngine_VRModule_CodeGenModule),
	(&g_UnityEngine_VehiclesModule_CodeGenModule),
	(&g_UnityEngine_VideoModule_CodeGenModule),
	(&g_UnityEngine_XRModule_CodeGenModule),
	(&g_Unity_Burst_CodeGenModule),
	(&g_Unity_Burst_Unsafe_CodeGenModule),
	(&g_Unity_Collections_CodeGenModule),
	(&g_Unity_Collections_LowLevel_ILSupport_CodeGenModule),
	(&g_Unity_Mathematics_CodeGenModule),
	(&g_Unity_RenderPipeline_Universal_ShaderLibrary_CodeGenModule),
	(&g_Unity_RenderPipelines_Core_Runtime_CodeGenModule),
	(&g_Unity_RenderPipelines_Universal_Runtime_CodeGenModule),
	(&g_Unity_TextMeshPro_CodeGenModule),
	(&g_Unity_Timeline_CodeGenModule),
	(&g_Unity_VisualEffectGraph_Runtime_CodeGenModule),
	(&g___Generated_CodeGenModule),
	(&g_mscorlib_CodeGenModule),
};
IL2CPP_EXTERN_C const Il2CppCodeRegistration g_CodeRegistration;
const Il2CppCodeRegistration g_CodeRegistration = 
{
	21,
	g_ReversePInvokeWrapperPointers,
	99798,
	g_Il2CppGenericMethodPointers,
	g_Il2CppGenericAdjustorThunks,
	21403,
	g_Il2CppInvokerPointers,
	3833,
	g_UnresolvedVirtualMethodPointers,
	g_UnresolvedInstanceMethodPointers,
	g_UnresolvedStaticMethodPointers,
	966,
	g_Il2CppInteropData,
	0,
	NULL,
	89,
	g_CodeGenModules,
};
IL2CPP_EXTERN_C_CONST Il2CppMetadataRegistration g_MetadataRegistration;
static const Il2CppCodeGenOptions s_Il2CppCodeGenOptions = 
{
	true,
	7,
	1,
};
void s_Il2CppCodegenRegistration()
{
	il2cpp_codegen_register (&g_CodeRegistration, &g_MetadataRegistration, &s_Il2CppCodeGenOptions);
}
#if RUNTIME_IL2CPP
typedef void (*CodegenRegistrationFunction)();
CodegenRegistrationFunction g_CodegenRegistration = s_Il2CppCodegenRegistration;
#endif

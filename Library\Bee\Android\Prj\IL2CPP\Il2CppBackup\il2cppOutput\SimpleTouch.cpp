﻿#include "pch-cpp.hpp"

#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include <limits>


struct VirtualActionInvoker0
{
	typedef void (*Action)(void*, const RuntimeMethod*);

	static inline void Invoke (Il2CppMethodSlot slot, RuntimeObject* obj)
	{
		const VirtualInvokeData& invokeData = il2cpp_codegen_get_virtual_invoke_data(slot, obj);
		((Action)invokeData.methodPtr)(obj, invokeData.method);
	}
};
template <typename T1>
struct VirtualActionInvoker1
{
	typedef void (*Action)(void*, T1, const RuntimeMethod*);

	static inline void Invoke (Il2CppMethodSlot slot, RuntimeObject* obj, T1 p1)
	{
		const VirtualInvokeData& invokeData = il2cpp_codegen_get_virtual_invoke_data(slot, obj);
		((Action)invokeData.methodPtr)(obj, p1, invokeData.method);
	}
};
struct GenericVirtualActionInvoker0
{
	typedef void (*Action)(void*, const RuntimeMethod*);

	static inline void Invoke (const RuntimeMethod* method, RuntimeObject* obj)
	{
		VirtualInvokeData invokeData;
		il2cpp_codegen_get_generic_virtual_invoke_data(method, obj, &invokeData);
		((Action)invokeData.methodPtr)(obj, invokeData.method);
	}
};
struct InterfaceActionInvoker0
{
	typedef void (*Action)(void*, const RuntimeMethod*);

	static inline void Invoke (Il2CppMethodSlot slot, RuntimeClass* declaringInterface, RuntimeObject* obj)
	{
		const VirtualInvokeData& invokeData = il2cpp_codegen_get_interface_invoke_data(slot, obj, declaringInterface);
		((Action)invokeData.methodPtr)(obj, invokeData.method);
	}
};
struct GenericInterfaceActionInvoker0
{
	typedef void (*Action)(void*, const RuntimeMethod*);

	static inline void Invoke (const RuntimeMethod* method, RuntimeObject* obj)
	{
		VirtualInvokeData invokeData;
		il2cpp_codegen_get_generic_interface_invoke_data(method, obj, &invokeData);
		((Action)invokeData.methodPtr)(obj, invokeData.method);
	}
};

struct ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031;
struct CharU5BU5D_t799905CF001DD5F13F7DBB310181FC4D8B7D0AAB;
struct DelegateU5BU5D_tC5AB7E8F745616680F337909D3A8E6C722CDF771;
struct FingerU5BU5D_tCA8476438690E4763B1CA04761ADB4F6C5FC8174;
struct FingerMotionEventU5BU5D_tBA16855F843A01A1DD72393A8DAF5CB5641930FA;
struct FingerTouchEventU5BU5D_tDF450EC4BBEC5A0F2773E7C4B52E29181EF88AD5;
struct IntPtrU5BU5D_tFD177F8C806A6921AD7150264CCC62FA00CAD832;
struct ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918;
struct StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF;
struct ArgumentOutOfRangeException_tEA2822DAF62B10EEED00E0E3A341D4BAF78CF85F;
struct AsyncCallback_t7FEF460CBDCFB9C5FA2EF776984778B9A4145F4C;
struct BaseInput_t2384DD6C51B50C487DFB643E20B936AB7AB3D955;
struct Delegate_t;
struct DelegateData_t9B286B493293CD2D23A5B2B5EF0E5B1324C2B77E;
struct Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7;
struct FingerMotionEvent_t9B15ED4278906BF70EE19462D3663C0A7AAC266F;
struct FingerTouchEvent_t34E5F496B643A2E72BA3BE9F010AC0AD637CE010;
struct IAsyncResult_t7B9B5A0ECB35DCEC31B8A8122C37D687369253B5;
struct IDictionary_t6D03155AF1FA9083817AA5B6AD7DEEACC26AB220;
struct MethodInfo_t;
struct MouseInput_t271B5F56C59AE6FC0D33D06407F9FEAEC540E3ED;
struct SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6;
struct String_t;
struct TouchInput_tD27C80459CCFF7B548F3E9665BDD0F2E24BE27D7;
struct TouchSystem_tC2752B654ED03B776574555FAFE34482FFEE019F;
struct UnitySourceGeneratedAssemblyMonoScriptTypes_v1_t7AC9A5762A7AB68BF29734E609A9F9DA9B7B2BA7;
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915;
struct FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51;
struct MouseEventHandler_t3456CF90ECEDAF94161F5D08E4AAD59CF39A0D58;

IL2CPP_EXTERN_C RuntimeClass* ArgumentOutOfRangeException_tEA2822DAF62B10EEED00E0E3A341D4BAF78CF85F_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* FingerU5BU5D_tCA8476438690E4763B1CA04761ADB4F6C5FC8174_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* MouseEventHandler_t3456CF90ECEDAF94161F5D08E4AAD59CF39A0D58_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* TouchInput_tD27C80459CCFF7B548F3E9665BDD0F2E24BE27D7_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeField* U3CPrivateImplementationDetailsU3E_tDFB4A33678FEA3D8A874CE58B63A3E605809F764____9909C6A8CC95B44B44183436A87FD1ABD491DE68BD2CF791104492BB6186B735_FieldInfo_var;
IL2CPP_EXTERN_C RuntimeField* U3CPrivateImplementationDetailsU3E_tDFB4A33678FEA3D8A874CE58B63A3E605809F764____9BCD93637B2D90B07EF0A1E25AD2FE848E667481ACFFDDCC86205E8F1B211B71_FieldInfo_var;
IL2CPP_EXTERN_C String_t* _stringLiteral96BAEB2BB90B854432B64BE441E7F64653B59C93;
IL2CPP_EXTERN_C String_t* _stringLiteralB5DC5D7CC41FCD1D493CBD69FA5646C6D8EA5F58;
IL2CPP_EXTERN_C const RuntimeMethod* TouchSystem_GetFingerMotionEvent_m5F6EF7B406DE7BE437CDB89235E4F5244DFC875B_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* TouchSystem_GetFingerTouchEvent_m8586CA78A0B7E12ACF5F8165AB114C2DF69CA1C0_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* TouchSystem_InitializeEvent_TisFingerMotionEvent_t9B15ED4278906BF70EE19462D3663C0A7AAC266F_m4F62EF6433E6C4F61900807A3F74AC4EB66D2D84_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* TouchSystem_InitializeEvent_TisFingerTouchEvent_t34E5F496B643A2E72BA3BE9F010AC0AD637CE010_m70F4E3C822C303856202895DFE1A132C0DEFBB7D_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* TouchSystem_ResetEvent_TisFingerMotionEvent_t9B15ED4278906BF70EE19462D3663C0A7AAC266F_mA73DFA8CFBEC4F29886CD41F5ECF3CCF79F152BA_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* TouchSystem_ResetEvent_TisFingerTouchEvent_t34E5F496B643A2E72BA3BE9F010AC0AD637CE010_m315CA4166B78F0499E14182E13D6333236C2F7E1_RuntimeMethod_var;
struct Delegate_t_marshaled_com;
struct Delegate_t_marshaled_pinvoke;
struct Exception_t_marshaled_com;
struct Exception_t_marshaled_pinvoke;

struct ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031;
struct DelegateU5BU5D_tC5AB7E8F745616680F337909D3A8E6C722CDF771;
struct FingerU5BU5D_tCA8476438690E4763B1CA04761ADB4F6C5FC8174;
struct FingerMotionEventU5BU5D_tBA16855F843A01A1DD72393A8DAF5CB5641930FA;
struct FingerTouchEventU5BU5D_tDF450EC4BBEC5A0F2773E7C4B52E29181EF88AD5;

IL2CPP_EXTERN_C_BEGIN
IL2CPP_EXTERN_C_END

#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
struct U3CModuleU3E_t45A3D108D2D8DF6762299EC7934FF22AED7383A7 
{
};
struct U3CPrivateImplementationDetailsU3E_tDFB4A33678FEA3D8A874CE58B63A3E605809F764  : public RuntimeObject
{
};
struct BaseInput_t2384DD6C51B50C487DFB643E20B936AB7AB3D955  : public RuntimeObject
{
};
struct FingerTouchEvent_t34E5F496B643A2E72BA3BE9F010AC0AD637CE010  : public RuntimeObject
{
	Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* ___U3CfingerU3Ek__BackingField;
};
struct String_t  : public RuntimeObject
{
	int32_t ____stringLength;
	Il2CppChar ____firstChar;
};
struct TouchSystem_tC2752B654ED03B776574555FAFE34482FFEE019F  : public RuntimeObject
{
	FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* ___onTouchBegan;
	FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* ___onTouchMoved;
	FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* ___onTouchStationary;
	FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* ___onTouchEnded;
	FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* ___onTouchCanceled;
	MouseEventHandler_t3456CF90ECEDAF94161F5D08E4AAD59CF39A0D58* ___onMouseMove;
	bool ___isEnabled;
	FingerU5BU5D_tCA8476438690E4763B1CA04761ADB4F6C5FC8174* ___fingers;
	FingerTouchEventU5BU5D_tDF450EC4BBEC5A0F2773E7C4B52E29181EF88AD5* ___fingerTouchEvents;
	FingerMotionEventU5BU5D_tBA16855F843A01A1DD72393A8DAF5CB5641930FA* ___fingerMotionEvents;
	BaseInput_t2384DD6C51B50C487DFB643E20B936AB7AB3D955* ___input;
};
struct UnitySourceGeneratedAssemblyMonoScriptTypes_v1_t7AC9A5762A7AB68BF29734E609A9F9DA9B7B2BA7  : public RuntimeObject
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F  : public RuntimeObject
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_pinvoke
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_com
{
};
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22 
{
	bool ___m_value;
};
struct Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3 
{
	uint8_t ___m_value;
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2  : public ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F
{
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2_marshaled_pinvoke
{
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2_marshaled_com
{
};
struct Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C 
{
	int32_t ___m_value;
};
struct IntPtr_t 
{
	void* ___m_value;
};
struct Single_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C 
{
	float ___m_value;
};
struct TouchInput_tD27C80459CCFF7B548F3E9665BDD0F2E24BE27D7  : public BaseInput_t2384DD6C51B50C487DFB643E20B936AB7AB3D955
{
};
struct Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 
{
	float ___x;
	float ___y;
};
struct Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 
{
	float ___x;
	float ___y;
	float ___z;
};
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915 
{
	union
	{
		struct
		{
		};
		uint8_t Void_t4861ACF8F4594C3437BB48B6E56783494B843915__padding[1];
	};
};
#pragma pack(push, tp, 1)
struct __StaticArrayInitTypeSizeU3D169_tD11210E19EFA1E0FD5D2DAEF58B2622050F730B1 
{
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D169_tD11210E19EFA1E0FD5D2DAEF58B2622050F730B1__padding[169];
	};
};
#pragma pack(pop, tp)
#pragma pack(push, tp, 1)
struct __StaticArrayInitTypeSizeU3D198_tBE8667363A22927FAB44DBF619772D174F3D6BBB 
{
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D198_tBE8667363A22927FAB44DBF619772D174F3D6BBB__padding[198];
	};
};
#pragma pack(pop, tp)
struct MonoScriptData_t47294C7BA3E74B244F4C0447A18A23A29FD7DDD5 
{
	ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* ___FilePathsData;
	ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* ___TypesData;
	int32_t ___TotalTypes;
	int32_t ___TotalFiles;
	bool ___IsEditorOnly;
};
struct MonoScriptData_t47294C7BA3E74B244F4C0447A18A23A29FD7DDD5_marshaled_pinvoke
{
	Il2CppSafeArray* ___FilePathsData;
	Il2CppSafeArray* ___TypesData;
	int32_t ___TotalTypes;
	int32_t ___TotalFiles;
	int32_t ___IsEditorOnly;
};
struct MonoScriptData_t47294C7BA3E74B244F4C0447A18A23A29FD7DDD5_marshaled_com
{
	Il2CppSafeArray* ___FilePathsData;
	Il2CppSafeArray* ___TypesData;
	int32_t ___TotalTypes;
	int32_t ___TotalFiles;
	int32_t ___IsEditorOnly;
};
struct Delegate_t  : public RuntimeObject
{
	intptr_t ___method_ptr;
	intptr_t ___invoke_impl;
	RuntimeObject* ___m_target;
	intptr_t ___method;
	intptr_t ___delegate_trampoline;
	intptr_t ___extra_arg;
	intptr_t ___method_code;
	intptr_t ___interp_method;
	intptr_t ___interp_invoke_impl;
	MethodInfo_t* ___method_info;
	MethodInfo_t* ___original_method_info;
	DelegateData_t9B286B493293CD2D23A5B2B5EF0E5B1324C2B77E* ___data;
	bool ___method_is_virtual;
};
struct Delegate_t_marshaled_pinvoke
{
	intptr_t ___method_ptr;
	intptr_t ___invoke_impl;
	Il2CppIUnknown* ___m_target;
	intptr_t ___method;
	intptr_t ___delegate_trampoline;
	intptr_t ___extra_arg;
	intptr_t ___method_code;
	intptr_t ___interp_method;
	intptr_t ___interp_invoke_impl;
	MethodInfo_t* ___method_info;
	MethodInfo_t* ___original_method_info;
	DelegateData_t9B286B493293CD2D23A5B2B5EF0E5B1324C2B77E* ___data;
	int32_t ___method_is_virtual;
};
struct Delegate_t_marshaled_com
{
	intptr_t ___method_ptr;
	intptr_t ___invoke_impl;
	Il2CppIUnknown* ___m_target;
	intptr_t ___method;
	intptr_t ___delegate_trampoline;
	intptr_t ___extra_arg;
	intptr_t ___method_code;
	intptr_t ___interp_method;
	intptr_t ___interp_invoke_impl;
	MethodInfo_t* ___method_info;
	MethodInfo_t* ___original_method_info;
	DelegateData_t9B286B493293CD2D23A5B2B5EF0E5B1324C2B77E* ___data;
	int32_t ___method_is_virtual;
};
struct Exception_t  : public RuntimeObject
{
	String_t* ____className;
	String_t* ____message;
	RuntimeObject* ____data;
	Exception_t* ____innerException;
	String_t* ____helpURL;
	RuntimeObject* ____stackTrace;
	String_t* ____stackTraceString;
	String_t* ____remoteStackTraceString;
	int32_t ____remoteStackIndex;
	RuntimeObject* ____dynamicMethods;
	int32_t ____HResult;
	String_t* ____source;
	SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6* ____safeSerializationManager;
	StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF* ___captured_traces;
	IntPtrU5BU5D_tFD177F8C806A6921AD7150264CCC62FA00CAD832* ___native_trace_ips;
	int32_t ___caught_in_unmanaged;
};
struct Exception_t_marshaled_pinvoke
{
	char* ____className;
	char* ____message;
	RuntimeObject* ____data;
	Exception_t_marshaled_pinvoke* ____innerException;
	char* ____helpURL;
	Il2CppIUnknown* ____stackTrace;
	char* ____stackTraceString;
	char* ____remoteStackTraceString;
	int32_t ____remoteStackIndex;
	Il2CppIUnknown* ____dynamicMethods;
	int32_t ____HResult;
	char* ____source;
	SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6* ____safeSerializationManager;
	StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF* ___captured_traces;
	Il2CppSafeArray* ___native_trace_ips;
	int32_t ___caught_in_unmanaged;
};
struct Exception_t_marshaled_com
{
	Il2CppChar* ____className;
	Il2CppChar* ____message;
	RuntimeObject* ____data;
	Exception_t_marshaled_com* ____innerException;
	Il2CppChar* ____helpURL;
	Il2CppIUnknown* ____stackTrace;
	Il2CppChar* ____stackTraceString;
	Il2CppChar* ____remoteStackTraceString;
	int32_t ____remoteStackIndex;
	Il2CppIUnknown* ____dynamicMethods;
	int32_t ____HResult;
	Il2CppChar* ____source;
	SafeSerializationManager_tCBB85B95DFD1634237140CD892E82D06ECB3F5E6* ____safeSerializationManager;
	StackTraceU5BU5D_t32FBCB20930EAF5BAE3F450FF75228E5450DA0DF* ___captured_traces;
	Il2CppSafeArray* ___native_trace_ips;
	int32_t ___caught_in_unmanaged;
};
struct FingerState_t796AD3E8B8FB0BBDF3F2752A6DFC9ADEBB239B06 
{
	int32_t ___value__;
};
struct MouseInput_t271B5F56C59AE6FC0D33D06407F9FEAEC540E3ED  : public BaseInput_t2384DD6C51B50C487DFB643E20B936AB7AB3D955
{
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___m_LastMousePosition;
};
struct RuntimeFieldHandle_t6E4C45B6D2EA12FC99185805A7E77527899B25C5 
{
	intptr_t ___value;
};
struct TouchPhase_t54E0A1AF80465997849420A72317B733E1D49A9E 
{
	int32_t ___value__;
};
struct TouchType_t84F82C73BC1A6012141735AD84DA67AA7F7AB43F 
{
	int32_t ___value__;
};
struct MotionPhase_tCCDEC6460746A0EDF143F27E7EA1D295DB17E3FD 
{
	int32_t ___value__;
};
struct Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7  : public RuntimeObject
{
	int32_t ___U3CstateU3Ek__BackingField;
	int32_t ___U3ClastStateU3Ek__BackingField;
	int32_t ___U3CindexU3Ek__BackingField;
	int32_t ___U3CfingerIdU3Ek__BackingField;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___U3CstartPositionU3Ek__BackingField;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___U3CpositionU3Ek__BackingField;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___U3ClastPositionU3Ek__BackingField;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___U3CdeltaPositionU3Ek__BackingField;
	float ___U3CcurrentStateDurationU3Ek__BackingField;
	bool ___U3CMovedU3Ek__BackingField;
};
struct FingerMotionEvent_t9B15ED4278906BF70EE19462D3663C0A7AAC266F  : public FingerTouchEvent_t34E5F496B643A2E72BA3BE9F010AC0AD637CE010
{
	int32_t ___U3CphaseU3Ek__BackingField;
};
struct MulticastDelegate_t  : public Delegate_t
{
	DelegateU5BU5D_tC5AB7E8F745616680F337909D3A8E6C722CDF771* ___delegates;
};
struct MulticastDelegate_t_marshaled_pinvoke : public Delegate_t_marshaled_pinvoke
{
	Delegate_t_marshaled_pinvoke** ___delegates;
};
struct MulticastDelegate_t_marshaled_com : public Delegate_t_marshaled_com
{
	Delegate_t_marshaled_com** ___delegates;
};
struct SystemException_tCC48D868298F4C0705279823E34B00F4FBDB7295  : public Exception_t
{
};
struct Touch_t03E51455ED508492B3F278903A0114FA0E87B417 
{
	int32_t ___m_FingerId;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___m_Position;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___m_RawPosition;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___m_PositionDelta;
	float ___m_TimeDelta;
	int32_t ___m_TapCount;
	int32_t ___m_Phase;
	int32_t ___m_Type;
	float ___m_Pressure;
	float ___m_maximumPossiblePressure;
	float ___m_Radius;
	float ___m_RadiusVariance;
	float ___m_AltitudeAngle;
	float ___m_AzimuthAngle;
};
struct ArgumentException_tAD90411542A20A9C72D5CDA3A84181D8B947A263  : public SystemException_tCC48D868298F4C0705279823E34B00F4FBDB7295
{
	String_t* ____paramName;
};
struct AsyncCallback_t7FEF460CBDCFB9C5FA2EF776984778B9A4145F4C  : public MulticastDelegate_t
{
};
struct FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51  : public MulticastDelegate_t
{
};
struct MouseEventHandler_t3456CF90ECEDAF94161F5D08E4AAD59CF39A0D58  : public MulticastDelegate_t
{
};
struct ArgumentOutOfRangeException_tEA2822DAF62B10EEED00E0E3A341D4BAF78CF85F  : public ArgumentException_tAD90411542A20A9C72D5CDA3A84181D8B947A263
{
	RuntimeObject* ____actualValue;
};
struct U3CPrivateImplementationDetailsU3E_tDFB4A33678FEA3D8A874CE58B63A3E605809F764_StaticFields
{
	__StaticArrayInitTypeSizeU3D169_tD11210E19EFA1E0FD5D2DAEF58B2622050F730B1 ___9909C6A8CC95B44B44183436A87FD1ABD491DE68BD2CF791104492BB6186B735;
	__StaticArrayInitTypeSizeU3D198_tBE8667363A22927FAB44DBF619772D174F3D6BBB ___9BCD93637B2D90B07EF0A1E25AD2FE848E667481ACFFDDCC86205E8F1B211B71;
};
struct String_t_StaticFields
{
	String_t* ___Empty;
};
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_StaticFields
{
	String_t* ___TrueString;
	String_t* ___FalseString;
};
struct IntPtr_t_StaticFields
{
	intptr_t ___Zero;
};
struct Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7_StaticFields
{
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___zeroVector;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___oneVector;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___upVector;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___downVector;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___leftVector;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___rightVector;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___positiveInfinityVector;
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___negativeInfinityVector;
};
struct Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_StaticFields
{
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___zeroVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___oneVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___upVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___downVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___leftVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___rightVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___forwardVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___backVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___positiveInfinityVector;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___negativeInfinityVector;
};
#ifdef __clang__
#pragma clang diagnostic pop
#endif
struct ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031  : public RuntimeArray
{
	ALIGN_FIELD (8) uint8_t m_Items[1];

	inline uint8_t GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline uint8_t* GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, uint8_t value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
	}
	inline uint8_t GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline uint8_t* GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, uint8_t value)
	{
		m_Items[index] = value;
	}
};
struct FingerU5BU5D_tCA8476438690E4763B1CA04761ADB4F6C5FC8174  : public RuntimeArray
{
	ALIGN_FIELD (8) Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* m_Items[1];

	inline Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7** GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
	inline Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7** GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* value)
	{
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
};
struct FingerTouchEventU5BU5D_tDF450EC4BBEC5A0F2773E7C4B52E29181EF88AD5  : public RuntimeArray
{
	ALIGN_FIELD (8) FingerTouchEvent_t34E5F496B643A2E72BA3BE9F010AC0AD637CE010* m_Items[1];

	inline FingerTouchEvent_t34E5F496B643A2E72BA3BE9F010AC0AD637CE010* GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline FingerTouchEvent_t34E5F496B643A2E72BA3BE9F010AC0AD637CE010** GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, FingerTouchEvent_t34E5F496B643A2E72BA3BE9F010AC0AD637CE010* value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
	inline FingerTouchEvent_t34E5F496B643A2E72BA3BE9F010AC0AD637CE010* GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline FingerTouchEvent_t34E5F496B643A2E72BA3BE9F010AC0AD637CE010** GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, FingerTouchEvent_t34E5F496B643A2E72BA3BE9F010AC0AD637CE010* value)
	{
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
};
struct FingerMotionEventU5BU5D_tBA16855F843A01A1DD72393A8DAF5CB5641930FA  : public RuntimeArray
{
	ALIGN_FIELD (8) FingerMotionEvent_t9B15ED4278906BF70EE19462D3663C0A7AAC266F* m_Items[1];

	inline FingerMotionEvent_t9B15ED4278906BF70EE19462D3663C0A7AAC266F* GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline FingerMotionEvent_t9B15ED4278906BF70EE19462D3663C0A7AAC266F** GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, FingerMotionEvent_t9B15ED4278906BF70EE19462D3663C0A7AAC266F* value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
	inline FingerMotionEvent_t9B15ED4278906BF70EE19462D3663C0A7AAC266F* GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline FingerMotionEvent_t9B15ED4278906BF70EE19462D3663C0A7AAC266F** GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, FingerMotionEvent_t9B15ED4278906BF70EE19462D3663C0A7AAC266F* value)
	{
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
};
struct DelegateU5BU5D_tC5AB7E8F745616680F337909D3A8E6C722CDF771  : public RuntimeArray
{
	ALIGN_FIELD (8) Delegate_t* m_Items[1];

	inline Delegate_t* GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline Delegate_t** GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, Delegate_t* value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
	inline Delegate_t* GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline Delegate_t** GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, Delegate_t* value)
	{
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
};


IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* TouchSystem_InitializeEvent_TisRuntimeObject_mDF10B363CF93B376C2BBBE256D39B30024CA8EB3_gshared (TouchSystem_tC2752B654ED03B776574555FAFE34482FFEE019F* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TouchSystem_ResetEvent_TisRuntimeObject_m4B42737F62F2CAC6DF65E7F3918C5D586E2CD775_gshared (TouchSystem_tC2752B654ED03B776574555FAFE34482FFEE019F* __this, ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* ___0_fingerEvent, const RuntimeMethod* method) ;

IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RuntimeHelpers_InitializeArray_m751372AA3F24FBF6DA9B9D687CBFA2DE436CAB9B (RuntimeArray* ___0_array, RuntimeFieldHandle_t6E4C45B6D2EA12FC99185805A7E77527899B25C5 ___1_fldHandle, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2 (RuntimeObject* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Finger_set_index_m29E24C2ACFC92E1AE6AF91E9FCADD9B12D253B31_inline (Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* __this, int32_t ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Finger_Reset_m8AA9BB5E549A0C766CE9994C43243CAC507CBE7E (Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Finger_set_state_m496CDBF5130ABB33CB2DE4CE3D458DA92A02B18A_inline (Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* __this, int32_t ___0_value, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Finger_set_lastState_m632AE93F19BA6FB11C6C38138F852F4C94B12BCA_inline (Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* __this, int32_t ___0_value, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Finger_set_fingerId_m1FF76B25E1F89C7F8A9EAEA171F3C5338BA94C7C_inline (Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* __this, int32_t ___0_value, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 Vector2_get_zero_m32506C40EC2EE7D5D4410BF40D3EE683A3D5F32C_inline (const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Finger_set_startPosition_mE12A9B74F21D9B66FB9B6F67FEA1D5DE8483B36A_inline (Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* __this, Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___0_value, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Finger_set_position_m377C1C4ADC62778CA08627A068DA396D4DB04C7B_inline (Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* __this, Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___0_value, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Finger_set_deltaPosition_mD5D4C0FA06DB9577FB53FFE7962AA94C662A6C78_inline (Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* __this, Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___0_value, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Finger_set_lastPosition_m74327C5F1FF0FA9A7434D7FB7BBC9BFC135FEA2A_inline (Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* __this, Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___0_value, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Finger_set_currentStateDuration_m94BDC11890516A0E7342F2DBB75BDBE4C3848C1C_inline (Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* __this, float ___0_value, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t Finger_get_state_m812016FD4AFDECF6F08E0011AFE7E09F4BA93A7D_inline (Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Finger_get_WasDown_m237B3519DD64E0ECEC87ECBCD3061645862D9D25 (Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Finger_set_Moved_m9CF46413BEAAD5E8E6DD4867E8E99388CEFFF8CE_inline (Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* __this, bool ___0_value, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 Finger_get_position_mD7E4F25C5EC08239EA13505CED61A2E2A7223E93_inline (Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 Finger_get_lastPosition_m6C385CAF630D4AEEC49C233097D5C5A0E4F651F7_inline (Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 Vector2_op_Subtraction_m44475FCDAD2DA2F98D78A6625EC2DCDFE8803837_inline (Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___0_a, Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___1_b, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 Finger_get_deltaPosition_mFF81C99D713BCAD5170BC781EC4A5B39FF2692E3_inline (Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Vector2_get_sqrMagnitude_mA16336720C14EEF8BA9B55AE33B98C9EE2082BDC_inline (Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Finger_get_IsMoving_m64569640AD76608F9AED5FE4B3A32CB0978DA48F (Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t Finger_get_lastState_mFE618D5B1F679CD097822524430FCC7AAB61811E_inline (Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Finger_get_currentStateDuration_mD0A03B3889C5FC15D90D7D482569D44F8949E234_inline (Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FingerTouchEvent__ctor_m3E8F05B22D939D32A0D0E7E8094CD012142D884B (FingerTouchEvent_t34E5F496B643A2E72BA3BE9F010AC0AD637CE010* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BaseInput_FingerDownDetect_m85C3143AA65DD55F8D814009E69987D6C2128C1E (BaseInput_t2384DD6C51B50C487DFB643E20B936AB7AB3D955* __this, TouchSystem_tC2752B654ED03B776574555FAFE34482FFEE019F* ___0_touchSystem, Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* ___1_finger, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BaseInput_FingerUpDetect_m85145B7062E17C4E70925B1FF7AE35940DC0C84B (BaseInput_t2384DD6C51B50C487DFB643E20B936AB7AB3D955* __this, TouchSystem_tC2752B654ED03B776574555FAFE34482FFEE019F* ___0_touchSystem, Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* ___1_finger, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BaseInput_FingerMotionDetect_mCDC757C25A4037792BD59A93CD7A39404315C08D (BaseInput_t2384DD6C51B50C487DFB643E20B936AB7AB3D955* __this, TouchSystem_tC2752B654ED03B776574555FAFE34482FFEE019F* ___0_touchSystem, Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* ___1_finger, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Finger_get_IsDown_mE1A0287C740B3E37E75F541F79730C49E31E43CD (Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t Finger_get_index_mC06528C915844D87E8DB1AB8282667856733F816_inline (Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR FingerTouchEvent_t34E5F496B643A2E72BA3BE9F010AC0AD637CE010* TouchSystem_GetFingerTouchEvent_m8586CA78A0B7E12ACF5F8165AB114C2DF69CA1C0 (TouchSystem_tC2752B654ED03B776574555FAFE34482FFEE019F* __this, int32_t ___0_index, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void FingerTouchEvent_set_finger_m5415B5FACC66E0442B88CE0371ED517C31B884C3_inline (FingerTouchEvent_t34E5F496B643A2E72BA3BE9F010AC0AD637CE010* __this, Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TouchSystem_OnTouchBegan_mC66AB49CD896F6A6D269F96B43C347AC2FB87989 (TouchSystem_tC2752B654ED03B776574555FAFE34482FFEE019F* __this, FingerTouchEvent_t34E5F496B643A2E72BA3BE9F010AC0AD637CE010* ___0_e, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TouchSystem_OnTouchEnded_mEE172ACC2021B4F930507388362C4B8258BB4E2F (TouchSystem_tC2752B654ED03B776574555FAFE34482FFEE019F* __this, FingerTouchEvent_t34E5F496B643A2E72BA3BE9F010AC0AD637CE010* ___0_e, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR FingerMotionEvent_t9B15ED4278906BF70EE19462D3663C0A7AAC266F* TouchSystem_GetFingerMotionEvent_m5F6EF7B406DE7BE437CDB89235E4F5244DFC875B (TouchSystem_tC2752B654ED03B776574555FAFE34482FFEE019F* __this, int32_t ___0_index, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void FingerMotionEvent_set_phase_m7BAB2F38CFEE73443835EAE8815CC64BCB7CB782_inline (FingerMotionEvent_t9B15ED4278906BF70EE19462D3663C0A7AAC266F* __this, int32_t ___0_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TouchSystem_OnTouchMoved_m2DCD48D75A308C714E5CE32E4D2699998213DF33 (TouchSystem_tC2752B654ED03B776574555FAFE34482FFEE019F* __this, FingerTouchEvent_t34E5F496B643A2E72BA3BE9F010AC0AD637CE010* ___0_e, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Finger_get_IsStationary_mEAEBF6AC6728498061C9E7ECC20FF8FA8DE60D6B (Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TouchSystem_OnTouchStationary_m6A121B7F5B3168946089FF18D66534AF206C741F (TouchSystem_tC2752B654ED03B776574555FAFE34482FFEE019F* __this, FingerTouchEvent_t34E5F496B643A2E72BA3BE9F010AC0AD637CE010* ___0_e, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float Time_get_deltaTime_mC3195000401F0FD167DD2F948FD2BC58330D0865 (const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Input_get_touchCount_m057388BFC67A0F4CA53764B1022867ED81D01E39 (const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* TouchSystem_GetFinger_m68BB32C0E4CF8DE2BD9B2FAE1008A6DA6BE92447 (TouchSystem_tC2752B654ED03B776574555FAFE34482FFEE019F* __this, int32_t ___0_index, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Touch_t03E51455ED508492B3F278903A0114FA0E87B417 Input_GetTouch_m75D99FE801A94279874FA8DC6B6ADAD35F5123B1 (int32_t ___0_index, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Touch_get_fingerId_mC1DCE93BFA0574960A3AE5329AE6C5F7E06962BD (Touch_t03E51455ED508492B3F278903A0114FA0E87B417* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Touch_get_phase_mB82409FB2BE1C32ABDBA6A72E52A099D28AB70B0 (Touch_t03E51455ED508492B3F278903A0114FA0E87B417* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 Touch_get_position_m41B9EB0F3F3E1BE98CEB388253A9E31979CB964A (Touch_t03E51455ED508492B3F278903A0114FA0E87B417* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Finger_Update_m8A7DBD8E7C51BEBAC9510DDB6D74631921D0573E (Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* __this, float ___0_deltaTime, Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___1_pos, int32_t ___2_fingerState, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t Finger_get_fingerId_m6DBB39CBC887BAEE6815383E9095FDF7C9EB5DD7_inline (Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BaseInput_ProcessFingerEvent_m24AC7960A0D255CB4660A38436D4ECA5D6696965 (BaseInput_t2384DD6C51B50C487DFB643E20B936AB7AB3D955* __this, TouchSystem_tC2752B654ED03B776574555FAFE34482FFEE019F* ___0_touchSystem, Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* ___1_finger, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BaseInput__ctor_mB6868ADDD8A711942038C76BF195BDDF092ADD34 (BaseInput_t2384DD6C51B50C487DFB643E20B936AB7AB3D955* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Input_GetMouseButton_m4995DD4A2D4F916565C1B1B5AAF7DF17C126B3EA (int32_t ___0_button, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Input_get_mousePosition_mFF21FBD2647DAE2A23BD4C45571CA95D05A0A42C (const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 Vector2_op_Implicit_mE8EBEE9291F11BB02F062D6E000F4798968CBD96_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_v, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool Vector3_op_Inequality_m9F170CDFBF1E490E559DA5D06D6547501A402BBF_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_lhs, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___1_rhs, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TouchSystem_OnMouseMove_m29102FF4E1122CCD48DF0713C0384050498E45B1 (TouchSystem_tC2752B654ED03B776574555FAFE34482FFEE019F* __this, Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___0_position, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_get_zero_m0C1249C3F25B1C70EAD3CC8B31259975A457AE39_inline (const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Delegate_t* Delegate_Combine_m1F725AEF318BE6F0426863490691A6F4606E7D00 (Delegate_t* ___0_a, Delegate_t* ___1_b, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Delegate_t* Delegate_Remove_m8B7DD5661308FA972E23CA1CC3FC9CEB355504E3 (Delegate_t* ___0_source, Delegate_t* ___1_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Finger__ctor_m3705D1A51634F357E3AE9EFAE4E0C1BF185F5A79 (Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* __this, int32_t ___0_index, const RuntimeMethod* method) ;
inline FingerTouchEventU5BU5D_tDF450EC4BBEC5A0F2773E7C4B52E29181EF88AD5* TouchSystem_InitializeEvent_TisFingerTouchEvent_t34E5F496B643A2E72BA3BE9F010AC0AD637CE010_m70F4E3C822C303856202895DFE1A132C0DEFBB7D (TouchSystem_tC2752B654ED03B776574555FAFE34482FFEE019F* __this, const RuntimeMethod* method)
{
	return ((  FingerTouchEventU5BU5D_tDF450EC4BBEC5A0F2773E7C4B52E29181EF88AD5* (*) (TouchSystem_tC2752B654ED03B776574555FAFE34482FFEE019F*, const RuntimeMethod*))TouchSystem_InitializeEvent_TisRuntimeObject_mDF10B363CF93B376C2BBBE256D39B30024CA8EB3_gshared)(__this, method);
}
inline FingerMotionEventU5BU5D_tBA16855F843A01A1DD72393A8DAF5CB5641930FA* TouchSystem_InitializeEvent_TisFingerMotionEvent_t9B15ED4278906BF70EE19462D3663C0A7AAC266F_m4F62EF6433E6C4F61900807A3F74AC4EB66D2D84 (TouchSystem_tC2752B654ED03B776574555FAFE34482FFEE019F* __this, const RuntimeMethod* method)
{
	return ((  FingerMotionEventU5BU5D_tBA16855F843A01A1DD72393A8DAF5CB5641930FA* (*) (TouchSystem_tC2752B654ED03B776574555FAFE34482FFEE019F*, const RuntimeMethod*))TouchSystem_InitializeEvent_TisRuntimeObject_mDF10B363CF93B376C2BBBE256D39B30024CA8EB3_gshared)(__this, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TouchInput__ctor_m6E613CB00B7819C21FE35D7C3CD54CEB961EB376 (TouchInput_tD27C80459CCFF7B548F3E9665BDD0F2E24BE27D7* __this, const RuntimeMethod* method) ;
inline void TouchSystem_ResetEvent_TisFingerTouchEvent_t34E5F496B643A2E72BA3BE9F010AC0AD637CE010_m315CA4166B78F0499E14182E13D6333236C2F7E1 (TouchSystem_tC2752B654ED03B776574555FAFE34482FFEE019F* __this, FingerTouchEventU5BU5D_tDF450EC4BBEC5A0F2773E7C4B52E29181EF88AD5* ___0_fingerEvent, const RuntimeMethod* method)
{
	((  void (*) (TouchSystem_tC2752B654ED03B776574555FAFE34482FFEE019F*, FingerTouchEventU5BU5D_tDF450EC4BBEC5A0F2773E7C4B52E29181EF88AD5*, const RuntimeMethod*))TouchSystem_ResetEvent_TisRuntimeObject_m4B42737F62F2CAC6DF65E7F3918C5D586E2CD775_gshared)(__this, ___0_fingerEvent, method);
}
inline void TouchSystem_ResetEvent_TisFingerMotionEvent_t9B15ED4278906BF70EE19462D3663C0A7AAC266F_mA73DFA8CFBEC4F29886CD41F5ECF3CCF79F152BA (TouchSystem_tC2752B654ED03B776574555FAFE34482FFEE019F* __this, FingerMotionEventU5BU5D_tBA16855F843A01A1DD72393A8DAF5CB5641930FA* ___0_fingerEvent, const RuntimeMethod* method)
{
	((  void (*) (TouchSystem_tC2752B654ED03B776574555FAFE34482FFEE019F*, FingerMotionEventU5BU5D_tBA16855F843A01A1DD72393A8DAF5CB5641930FA*, const RuntimeMethod*))TouchSystem_ResetEvent_TisRuntimeObject_m4B42737F62F2CAC6DF65E7F3918C5D586E2CD775_gshared)(__this, ___0_fingerEvent, method);
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void FingerEventHandler_Invoke_mA20FAE4131B5472E16C4D31CCD3C0C5514F0DAEF_inline (FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* __this, FingerTouchEvent_t34E5F496B643A2E72BA3BE9F010AC0AD637CE010* ___0_e, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void MouseEventHandler_Invoke_m7768333417BCEA31AD71AA27D5ED80ED6CB56D49_inline (MouseEventHandler_t3456CF90ECEDAF94161F5D08E4AAD59CF39A0D58* __this, Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___0_position, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void ArgumentOutOfRangeException__ctor_mBC1D5DEEA1BA41DE77228CB27D6BAFEB6DCCBF4A (ArgumentOutOfRangeException_tEA2822DAF62B10EEED00E0E3A341D4BAF78CF85F* __this, String_t* ___0_paramName, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Vector2__ctor_m9525B79969AFFE3254B303A40997A56DEEB6F548_inline (Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7* __this, float ___0_x, float ___1_y, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool Vector3_op_Equality_mCDCBB8D2EDC3D3BF20F31A25ACB34705D352B479_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_lhs, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___1_rhs, const RuntimeMethod* method) ;
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR MonoScriptData_t47294C7BA3E74B244F4C0447A18A23A29FD7DDD5 UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_m564F24ACFEA522CC301200C564353DBD40286B07 (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CPrivateImplementationDetailsU3E_tDFB4A33678FEA3D8A874CE58B63A3E605809F764____9909C6A8CC95B44B44183436A87FD1ABD491DE68BD2CF791104492BB6186B735_FieldInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CPrivateImplementationDetailsU3E_tDFB4A33678FEA3D8A874CE58B63A3E605809F764____9BCD93637B2D90B07EF0A1E25AD2FE848E667481ACFFDDCC86205E8F1B211B71_FieldInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	MonoScriptData_t47294C7BA3E74B244F4C0447A18A23A29FD7DDD5 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		il2cpp_codegen_initobj((&V_0), sizeof(MonoScriptData_t47294C7BA3E74B244F4C0447A18A23A29FD7DDD5));
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_0 = (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)SZArrayNew(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031_il2cpp_TypeInfo_var, (uint32_t)((int32_t)169));
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_1 = L_0;
		RuntimeFieldHandle_t6E4C45B6D2EA12FC99185805A7E77527899B25C5 L_2 = { reinterpret_cast<intptr_t> (U3CPrivateImplementationDetailsU3E_tDFB4A33678FEA3D8A874CE58B63A3E605809F764____9909C6A8CC95B44B44183436A87FD1ABD491DE68BD2CF791104492BB6186B735_FieldInfo_var) };
		RuntimeHelpers_InitializeArray_m751372AA3F24FBF6DA9B9D687CBFA2DE436CAB9B((RuntimeArray*)L_1, L_2, NULL);
		(&V_0)->___FilePathsData = L_1;
		Il2CppCodeGenWriteBarrier((void**)(&(&V_0)->___FilePathsData), (void*)L_1);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_3 = (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)SZArrayNew(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031_il2cpp_TypeInfo_var, (uint32_t)((int32_t)198));
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_4 = L_3;
		RuntimeFieldHandle_t6E4C45B6D2EA12FC99185805A7E77527899B25C5 L_5 = { reinterpret_cast<intptr_t> (U3CPrivateImplementationDetailsU3E_tDFB4A33678FEA3D8A874CE58B63A3E605809F764____9BCD93637B2D90B07EF0A1E25AD2FE848E667481ACFFDDCC86205E8F1B211B71_FieldInfo_var) };
		RuntimeHelpers_InitializeArray_m751372AA3F24FBF6DA9B9D687CBFA2DE436CAB9B((RuntimeArray*)L_4, L_5, NULL);
		(&V_0)->___TypesData = L_4;
		Il2CppCodeGenWriteBarrier((void**)(&(&V_0)->___TypesData), (void*)L_4);
		(&V_0)->___TotalFiles = 3;
		(&V_0)->___TotalTypes = 7;
		(&V_0)->___IsEditorOnly = (bool)0;
		MonoScriptData_t47294C7BA3E74B244F4C0447A18A23A29FD7DDD5 L_6 = V_0;
		return L_6;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_mFCEEFC861E4F9EDB450802A81ED0911D6F779F8E (UnitySourceGeneratedAssemblyMonoScriptTypes_v1_t7AC9A5762A7AB68BF29734E609A9F9DA9B7B2BA7* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C void MonoScriptData_t47294C7BA3E74B244F4C0447A18A23A29FD7DDD5_marshal_pinvoke(const MonoScriptData_t47294C7BA3E74B244F4C0447A18A23A29FD7DDD5& unmarshaled, MonoScriptData_t47294C7BA3E74B244F4C0447A18A23A29FD7DDD5_marshaled_pinvoke& marshaled)
{
	marshaled.___FilePathsData = il2cpp_codegen_com_marshal_safe_array(IL2CPP_VT_I1, unmarshaled.___FilePathsData);
	marshaled.___TypesData = il2cpp_codegen_com_marshal_safe_array(IL2CPP_VT_I1, unmarshaled.___TypesData);
	marshaled.___TotalTypes = unmarshaled.___TotalTypes;
	marshaled.___TotalFiles = unmarshaled.___TotalFiles;
	marshaled.___IsEditorOnly = static_cast<int32_t>(unmarshaled.___IsEditorOnly);
}
IL2CPP_EXTERN_C void MonoScriptData_t47294C7BA3E74B244F4C0447A18A23A29FD7DDD5_marshal_pinvoke_back(const MonoScriptData_t47294C7BA3E74B244F4C0447A18A23A29FD7DDD5_marshaled_pinvoke& marshaled, MonoScriptData_t47294C7BA3E74B244F4C0447A18A23A29FD7DDD5& unmarshaled)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	unmarshaled.___FilePathsData = (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I1, Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, marshaled.___FilePathsData);
	Il2CppCodeGenWriteBarrier((void**)(&unmarshaled.___FilePathsData), (void*)(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I1, Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, marshaled.___FilePathsData));
	unmarshaled.___TypesData = (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I1, Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, marshaled.___TypesData);
	Il2CppCodeGenWriteBarrier((void**)(&unmarshaled.___TypesData), (void*)(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I1, Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, marshaled.___TypesData));
	int32_t unmarshaledTotalTypes_temp_2 = 0;
	unmarshaledTotalTypes_temp_2 = marshaled.___TotalTypes;
	unmarshaled.___TotalTypes = unmarshaledTotalTypes_temp_2;
	int32_t unmarshaledTotalFiles_temp_3 = 0;
	unmarshaledTotalFiles_temp_3 = marshaled.___TotalFiles;
	unmarshaled.___TotalFiles = unmarshaledTotalFiles_temp_3;
	bool unmarshaledIsEditorOnly_temp_4 = false;
	unmarshaledIsEditorOnly_temp_4 = static_cast<bool>(marshaled.___IsEditorOnly);
	unmarshaled.___IsEditorOnly = unmarshaledIsEditorOnly_temp_4;
}
IL2CPP_EXTERN_C void MonoScriptData_t47294C7BA3E74B244F4C0447A18A23A29FD7DDD5_marshal_pinvoke_cleanup(MonoScriptData_t47294C7BA3E74B244F4C0447A18A23A29FD7DDD5_marshaled_pinvoke& marshaled)
{
	il2cpp_codegen_com_destroy_safe_array(marshaled.___FilePathsData);
	marshaled.___FilePathsData = NULL;
	il2cpp_codegen_com_destroy_safe_array(marshaled.___TypesData);
	marshaled.___TypesData = NULL;
}
IL2CPP_EXTERN_C void MonoScriptData_t47294C7BA3E74B244F4C0447A18A23A29FD7DDD5_marshal_com(const MonoScriptData_t47294C7BA3E74B244F4C0447A18A23A29FD7DDD5& unmarshaled, MonoScriptData_t47294C7BA3E74B244F4C0447A18A23A29FD7DDD5_marshaled_com& marshaled)
{
	marshaled.___FilePathsData = il2cpp_codegen_com_marshal_safe_array(IL2CPP_VT_I1, unmarshaled.___FilePathsData);
	marshaled.___TypesData = il2cpp_codegen_com_marshal_safe_array(IL2CPP_VT_I1, unmarshaled.___TypesData);
	marshaled.___TotalTypes = unmarshaled.___TotalTypes;
	marshaled.___TotalFiles = unmarshaled.___TotalFiles;
	marshaled.___IsEditorOnly = static_cast<int32_t>(unmarshaled.___IsEditorOnly);
}
IL2CPP_EXTERN_C void MonoScriptData_t47294C7BA3E74B244F4C0447A18A23A29FD7DDD5_marshal_com_back(const MonoScriptData_t47294C7BA3E74B244F4C0447A18A23A29FD7DDD5_marshaled_com& marshaled, MonoScriptData_t47294C7BA3E74B244F4C0447A18A23A29FD7DDD5& unmarshaled)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	unmarshaled.___FilePathsData = (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I1, Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, marshaled.___FilePathsData);
	Il2CppCodeGenWriteBarrier((void**)(&unmarshaled.___FilePathsData), (void*)(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I1, Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, marshaled.___FilePathsData));
	unmarshaled.___TypesData = (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I1, Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, marshaled.___TypesData);
	Il2CppCodeGenWriteBarrier((void**)(&unmarshaled.___TypesData), (void*)(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I1, Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, marshaled.___TypesData));
	int32_t unmarshaledTotalTypes_temp_2 = 0;
	unmarshaledTotalTypes_temp_2 = marshaled.___TotalTypes;
	unmarshaled.___TotalTypes = unmarshaledTotalTypes_temp_2;
	int32_t unmarshaledTotalFiles_temp_3 = 0;
	unmarshaledTotalFiles_temp_3 = marshaled.___TotalFiles;
	unmarshaled.___TotalFiles = unmarshaledTotalFiles_temp_3;
	bool unmarshaledIsEditorOnly_temp_4 = false;
	unmarshaledIsEditorOnly_temp_4 = static_cast<bool>(marshaled.___IsEditorOnly);
	unmarshaled.___IsEditorOnly = unmarshaledIsEditorOnly_temp_4;
}
IL2CPP_EXTERN_C void MonoScriptData_t47294C7BA3E74B244F4C0447A18A23A29FD7DDD5_marshal_com_cleanup(MonoScriptData_t47294C7BA3E74B244F4C0447A18A23A29FD7DDD5_marshaled_com& marshaled)
{
	il2cpp_codegen_com_destroy_safe_array(marshaled.___FilePathsData);
	marshaled.___FilePathsData = NULL;
	il2cpp_codegen_com_destroy_safe_array(marshaled.___TypesData);
	marshaled.___TypesData = NULL;
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Finger_get_state_m812016FD4AFDECF6F08E0011AFE7E09F4BA93A7D (Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* __this, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = __this->___U3CstateU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Finger_set_state_m496CDBF5130ABB33CB2DE4CE3D458DA92A02B18A (Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* __this, int32_t ___0_value, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = ___0_value;
		__this->___U3CstateU3Ek__BackingField = L_0;
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Finger_get_lastState_mFE618D5B1F679CD097822524430FCC7AAB61811E (Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* __this, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = __this->___U3ClastStateU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Finger_set_lastState_m632AE93F19BA6FB11C6C38138F852F4C94B12BCA (Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* __this, int32_t ___0_value, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = ___0_value;
		__this->___U3ClastStateU3Ek__BackingField = L_0;
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Finger_get_index_mC06528C915844D87E8DB1AB8282667856733F816 (Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* __this, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = __this->___U3CindexU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Finger_set_index_m29E24C2ACFC92E1AE6AF91E9FCADD9B12D253B31 (Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* __this, int32_t ___0_value, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = ___0_value;
		__this->___U3CindexU3Ek__BackingField = L_0;
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t Finger_get_fingerId_m6DBB39CBC887BAEE6815383E9095FDF7C9EB5DD7 (Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* __this, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = __this->___U3CfingerIdU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Finger_set_fingerId_m1FF76B25E1F89C7F8A9EAEA171F3C5338BA94C7C (Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* __this, int32_t ___0_value, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = ___0_value;
		__this->___U3CfingerIdU3Ek__BackingField = L_0;
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 Finger_get_startPosition_m145EB6FE7F3955A72B6808889D483CE5D3F32A17 (Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* __this, const RuntimeMethod* method) 
{
	{
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_0 = __this->___U3CstartPositionU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Finger_set_startPosition_mE12A9B74F21D9B66FB9B6F67FEA1D5DE8483B36A (Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* __this, Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___0_value, const RuntimeMethod* method) 
{
	{
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_0 = ___0_value;
		__this->___U3CstartPositionU3Ek__BackingField = L_0;
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 Finger_get_position_mD7E4F25C5EC08239EA13505CED61A2E2A7223E93 (Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* __this, const RuntimeMethod* method) 
{
	{
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_0 = __this->___U3CpositionU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Finger_set_position_m377C1C4ADC62778CA08627A068DA396D4DB04C7B (Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* __this, Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___0_value, const RuntimeMethod* method) 
{
	{
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_0 = ___0_value;
		__this->___U3CpositionU3Ek__BackingField = L_0;
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 Finger_get_lastPosition_m6C385CAF630D4AEEC49C233097D5C5A0E4F651F7 (Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* __this, const RuntimeMethod* method) 
{
	{
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_0 = __this->___U3ClastPositionU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Finger_set_lastPosition_m74327C5F1FF0FA9A7434D7FB7BBC9BFC135FEA2A (Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* __this, Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___0_value, const RuntimeMethod* method) 
{
	{
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_0 = ___0_value;
		__this->___U3ClastPositionU3Ek__BackingField = L_0;
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 Finger_get_deltaPosition_mFF81C99D713BCAD5170BC781EC4A5B39FF2692E3 (Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* __this, const RuntimeMethod* method) 
{
	{
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_0 = __this->___U3CdeltaPositionU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Finger_set_deltaPosition_mD5D4C0FA06DB9577FB53FFE7962AA94C662A6C78 (Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* __this, Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___0_value, const RuntimeMethod* method) 
{
	{
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_0 = ___0_value;
		__this->___U3CdeltaPositionU3Ek__BackingField = L_0;
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float Finger_get_currentStateDuration_mD0A03B3889C5FC15D90D7D482569D44F8949E234 (Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* __this, const RuntimeMethod* method) 
{
	{
		float L_0 = __this->___U3CcurrentStateDurationU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Finger_set_currentStateDuration_m94BDC11890516A0E7342F2DBB75BDBE4C3848C1C (Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* __this, float ___0_value, const RuntimeMethod* method) 
{
	{
		float L_0 = ___0_value;
		__this->___U3CcurrentStateDurationU3Ek__BackingField = L_0;
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Finger__ctor_m3705D1A51634F357E3AE9EFAE4E0C1BF185F5A79 (Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* __this, int32_t ___0_index, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		int32_t L_0 = ___0_index;
		Finger_set_index_m29E24C2ACFC92E1AE6AF91E9FCADD9B12D253B31_inline(__this, L_0, NULL);
		Finger_Reset_m8AA9BB5E549A0C766CE9994C43243CAC507CBE7E(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Finger_Reset_m8AA9BB5E549A0C766CE9994C43243CAC507CBE7E (Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* __this, const RuntimeMethod* method) 
{
	{
		Finger_set_state_m496CDBF5130ABB33CB2DE4CE3D458DA92A02B18A_inline(__this, 0, NULL);
		Finger_set_lastState_m632AE93F19BA6FB11C6C38138F852F4C94B12BCA_inline(__this, 0, NULL);
		Finger_set_fingerId_m1FF76B25E1F89C7F8A9EAEA171F3C5338BA94C7C_inline(__this, (-1), NULL);
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_0;
		L_0 = Vector2_get_zero_m32506C40EC2EE7D5D4410BF40D3EE683A3D5F32C_inline(NULL);
		Finger_set_startPosition_mE12A9B74F21D9B66FB9B6F67FEA1D5DE8483B36A_inline(__this, L_0, NULL);
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_1;
		L_1 = Vector2_get_zero_m32506C40EC2EE7D5D4410BF40D3EE683A3D5F32C_inline(NULL);
		Finger_set_position_m377C1C4ADC62778CA08627A068DA396D4DB04C7B_inline(__this, L_1, NULL);
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_2;
		L_2 = Vector2_get_zero_m32506C40EC2EE7D5D4410BF40D3EE683A3D5F32C_inline(NULL);
		Finger_set_deltaPosition_mD5D4C0FA06DB9577FB53FFE7962AA94C662A6C78_inline(__this, L_2, NULL);
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_3;
		L_3 = Vector2_get_zero_m32506C40EC2EE7D5D4410BF40D3EE683A3D5F32C_inline(NULL);
		Finger_set_lastPosition_m74327C5F1FF0FA9A7434D7FB7BBC9BFC135FEA2A_inline(__this, L_3, NULL);
		Finger_set_currentStateDuration_m94BDC11890516A0E7342F2DBB75BDBE4C3848C1C_inline(__this, (0.0f), NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Finger_Update_m8A7DBD8E7C51BEBAC9510DDB6D74631921D0573E (Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* __this, float ___0_deltaTime, Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___1_pos, int32_t ___2_fingerState, const RuntimeMethod* method) 
{
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 V_0;
	memset((&V_0), 0, sizeof(V_0));
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 V_1;
	memset((&V_1), 0, sizeof(V_1));
	{
		int32_t L_0;
		L_0 = Finger_get_state_m812016FD4AFDECF6F08E0011AFE7E09F4BA93A7D_inline(__this, NULL);
		Finger_set_lastState_m632AE93F19BA6FB11C6C38138F852F4C94B12BCA_inline(__this, L_0, NULL);
		int32_t L_1 = ___2_fingerState;
		if (!((((int32_t)L_1) == ((int32_t)1))? 1 : 0))
		{
			goto IL_00b4;
		}
	}
	{
		bool L_2;
		L_2 = Finger_get_WasDown_m237B3519DD64E0ECEC87ECBCD3061645862D9D25(__this, NULL);
		if (L_2)
		{
			goto IL_0054;
		}
	}
	{
		Finger_set_state_m496CDBF5130ABB33CB2DE4CE3D458DA92A02B18A_inline(__this, 1, NULL);
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_3 = ___1_pos;
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_4 = L_3;
		V_1 = L_4;
		Finger_set_lastPosition_m74327C5F1FF0FA9A7434D7FB7BBC9BFC135FEA2A_inline(__this, L_4, NULL);
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_5 = V_1;
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_6 = L_5;
		V_0 = L_6;
		Finger_set_position_m377C1C4ADC62778CA08627A068DA396D4DB04C7B_inline(__this, L_6, NULL);
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_7 = V_0;
		Finger_set_startPosition_mE12A9B74F21D9B66FB9B6F67FEA1D5DE8483B36A_inline(__this, L_7, NULL);
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_8;
		L_8 = Vector2_get_zero_m32506C40EC2EE7D5D4410BF40D3EE683A3D5F32C_inline(NULL);
		Finger_set_deltaPosition_mD5D4C0FA06DB9577FB53FFE7962AA94C662A6C78_inline(__this, L_8, NULL);
		Finger_set_Moved_m9CF46413BEAAD5E8E6DD4867E8E99388CEFFF8CE_inline(__this, (bool)0, NULL);
		goto IL_00ed;
	}

IL_0054:
	{
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_9;
		L_9 = Finger_get_position_mD7E4F25C5EC08239EA13505CED61A2E2A7223E93_inline(__this, NULL);
		Finger_set_lastPosition_m74327C5F1FF0FA9A7434D7FB7BBC9BFC135FEA2A_inline(__this, L_9, NULL);
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_10 = ___1_pos;
		Finger_set_position_m377C1C4ADC62778CA08627A068DA396D4DB04C7B_inline(__this, L_10, NULL);
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_11;
		L_11 = Finger_get_position_mD7E4F25C5EC08239EA13505CED61A2E2A7223E93_inline(__this, NULL);
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_12;
		L_12 = Finger_get_lastPosition_m6C385CAF630D4AEEC49C233097D5C5A0E4F651F7_inline(__this, NULL);
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_13;
		L_13 = Vector2_op_Subtraction_m44475FCDAD2DA2F98D78A6625EC2DCDFE8803837_inline(L_11, L_12, NULL);
		Finger_set_deltaPosition_mD5D4C0FA06DB9577FB53FFE7962AA94C662A6C78_inline(__this, L_13, NULL);
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_14;
		L_14 = Finger_get_deltaPosition_mFF81C99D713BCAD5170BC781EC4A5B39FF2692E3_inline(__this, NULL);
		V_0 = L_14;
		float L_15;
		L_15 = Vector2_get_sqrMagnitude_mA16336720C14EEF8BA9B55AE33B98C9EE2082BDC_inline((&V_0), NULL);
		if ((!(((float)L_15) > ((float)(0.0f)))))
		{
			goto IL_00a3;
		}
	}
	{
		Finger_set_state_m496CDBF5130ABB33CB2DE4CE3D458DA92A02B18A_inline(__this, 2, NULL);
		Finger_set_Moved_m9CF46413BEAAD5E8E6DD4867E8E99388CEFFF8CE_inline(__this, (bool)1, NULL);
		goto IL_00ed;
	}

IL_00a3:
	{
		bool L_16;
		L_16 = Finger_get_IsMoving_m64569640AD76608F9AED5FE4B3A32CB0978DA48F(__this, NULL);
		if (L_16)
		{
			goto IL_00ed;
		}
	}
	{
		Finger_set_state_m496CDBF5130ABB33CB2DE4CE3D458DA92A02B18A_inline(__this, 3, NULL);
		goto IL_00ed;
	}

IL_00b4:
	{
		bool L_17;
		L_17 = Finger_get_WasDown_m237B3519DD64E0ECEC87ECBCD3061645862D9D25(__this, NULL);
		if (!L_17)
		{
			goto IL_00ed;
		}
	}
	{
		int32_t L_18 = ___2_fingerState;
		Finger_set_state_m496CDBF5130ABB33CB2DE4CE3D458DA92A02B18A_inline(__this, L_18, NULL);
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_19;
		L_19 = Finger_get_position_mD7E4F25C5EC08239EA13505CED61A2E2A7223E93_inline(__this, NULL);
		Finger_set_lastPosition_m74327C5F1FF0FA9A7434D7FB7BBC9BFC135FEA2A_inline(__this, L_19, NULL);
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_20 = ___1_pos;
		Finger_set_position_m377C1C4ADC62778CA08627A068DA396D4DB04C7B_inline(__this, L_20, NULL);
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_21;
		L_21 = Finger_get_position_mD7E4F25C5EC08239EA13505CED61A2E2A7223E93_inline(__this, NULL);
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_22;
		L_22 = Finger_get_lastPosition_m6C385CAF630D4AEEC49C233097D5C5A0E4F651F7_inline(__this, NULL);
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_23;
		L_23 = Vector2_op_Subtraction_m44475FCDAD2DA2F98D78A6625EC2DCDFE8803837_inline(L_21, L_22, NULL);
		Finger_set_deltaPosition_mD5D4C0FA06DB9577FB53FFE7962AA94C662A6C78_inline(__this, L_23, NULL);
	}

IL_00ed:
	{
		int32_t L_24;
		L_24 = Finger_get_state_m812016FD4AFDECF6F08E0011AFE7E09F4BA93A7D_inline(__this, NULL);
		int32_t L_25;
		L_25 = Finger_get_lastState_mFE618D5B1F679CD097822524430FCC7AAB61811E_inline(__this, NULL);
		if ((((int32_t)L_24) == ((int32_t)L_25)))
		{
			goto IL_0107;
		}
	}
	{
		Finger_set_currentStateDuration_m94BDC11890516A0E7342F2DBB75BDBE4C3848C1C_inline(__this, (0.0f), NULL);
		return;
	}

IL_0107:
	{
		float L_26;
		L_26 = Finger_get_currentStateDuration_mD0A03B3889C5FC15D90D7D482569D44F8949E234_inline(__this, NULL);
		float L_27 = ___0_deltaTime;
		Finger_set_currentStateDuration_m94BDC11890516A0E7342F2DBB75BDBE4C3848C1C_inline(__this, ((float)il2cpp_codegen_add(L_26, L_27)), NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Finger_get_Moved_m49BF5CE5299489209594FFEEAD82F5932854CD68 (Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* __this, const RuntimeMethod* method) 
{
	{
		bool L_0 = __this->___U3CMovedU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Finger_set_Moved_m9CF46413BEAAD5E8E6DD4867E8E99388CEFFF8CE (Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* __this, bool ___0_value, const RuntimeMethod* method) 
{
	{
		bool L_0 = ___0_value;
		__this->___U3CMovedU3Ek__BackingField = L_0;
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Finger_get_WasDown_m237B3519DD64E0ECEC87ECBCD3061645862D9D25 (Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* __this, const RuntimeMethod* method) 
{
	{
		int32_t L_0;
		L_0 = Finger_get_lastState_mFE618D5B1F679CD097822524430FCC7AAB61811E_inline(__this, NULL);
		return (bool)((!(((uint32_t)L_0) <= ((uint32_t)0)))? 1 : 0);
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Finger_get_WasMoving_m7ED124C626CFA72C1DCD7C365F529BFD49834570 (Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* __this, const RuntimeMethod* method) 
{
	{
		int32_t L_0;
		L_0 = Finger_get_lastState_mFE618D5B1F679CD097822524430FCC7AAB61811E_inline(__this, NULL);
		return (bool)((((int32_t)L_0) == ((int32_t)2))? 1 : 0);
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Finger_get_WasStationary_mFF49E4D74D9A2848CB787EAB1185EDD0BD94AC99 (Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* __this, const RuntimeMethod* method) 
{
	{
		int32_t L_0;
		L_0 = Finger_get_lastState_mFE618D5B1F679CD097822524430FCC7AAB61811E_inline(__this, NULL);
		return (bool)((((int32_t)L_0) == ((int32_t)3))? 1 : 0);
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Finger_get_IsDown_mE1A0287C740B3E37E75F541F79730C49E31E43CD (Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* __this, const RuntimeMethod* method) 
{
	{
		int32_t L_0;
		L_0 = Finger_get_state_m812016FD4AFDECF6F08E0011AFE7E09F4BA93A7D_inline(__this, NULL);
		return (bool)((!(((uint32_t)L_0) <= ((uint32_t)0)))? 1 : 0);
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Finger_get_IsMoving_m64569640AD76608F9AED5FE4B3A32CB0978DA48F (Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* __this, const RuntimeMethod* method) 
{
	{
		int32_t L_0;
		L_0 = Finger_get_state_m812016FD4AFDECF6F08E0011AFE7E09F4BA93A7D_inline(__this, NULL);
		return (bool)((((int32_t)L_0) == ((int32_t)2))? 1 : 0);
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Finger_get_IsStationary_mEAEBF6AC6728498061C9E7ECC20FF8FA8DE60D6B (Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* __this, const RuntimeMethod* method) 
{
	{
		int32_t L_0;
		L_0 = Finger_get_state_m812016FD4AFDECF6F08E0011AFE7E09F4BA93A7D_inline(__this, NULL);
		return (bool)((((int32_t)L_0) == ((int32_t)3))? 1 : 0);
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* FingerTouchEvent_get_finger_m9FE6E136CA29BC49889868DAEE4B764092D28843 (FingerTouchEvent_t34E5F496B643A2E72BA3BE9F010AC0AD637CE010* __this, const RuntimeMethod* method) 
{
	{
		Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* L_0 = __this->___U3CfingerU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FingerTouchEvent_set_finger_m5415B5FACC66E0442B88CE0371ED517C31B884C3 (FingerTouchEvent_t34E5F496B643A2E72BA3BE9F010AC0AD637CE010* __this, Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* ___0_value, const RuntimeMethod* method) 
{
	{
		Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* L_0 = ___0_value;
		__this->___U3CfingerU3Ek__BackingField = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___U3CfingerU3Ek__BackingField), (void*)L_0);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FingerTouchEvent__ctor_m3E8F05B22D939D32A0D0E7E8094CD012142D884B (FingerTouchEvent_t34E5F496B643A2E72BA3BE9F010AC0AD637CE010* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR int32_t FingerMotionEvent_get_phase_m382EE2A04D7A21A48078F74A4262EBFB290E68A3 (FingerMotionEvent_t9B15ED4278906BF70EE19462D3663C0A7AAC266F* __this, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = __this->___U3CphaseU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FingerMotionEvent_set_phase_m7BAB2F38CFEE73443835EAE8815CC64BCB7CB782 (FingerMotionEvent_t9B15ED4278906BF70EE19462D3663C0A7AAC266F* __this, int32_t ___0_value, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = ___0_value;
		__this->___U3CphaseU3Ek__BackingField = L_0;
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FingerMotionEvent__ctor_m8EA10E74A28A021BCDA16851CD99305E67FB7AB3 (FingerMotionEvent_t9B15ED4278906BF70EE19462D3663C0A7AAC266F* __this, const RuntimeMethod* method) 
{
	{
		FingerTouchEvent__ctor_m3E8F05B22D939D32A0D0E7E8094CD012142D884B(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BaseInput_ProcessFingerEvent_m24AC7960A0D255CB4660A38436D4ECA5D6696965 (BaseInput_t2384DD6C51B50C487DFB643E20B936AB7AB3D955* __this, TouchSystem_tC2752B654ED03B776574555FAFE34482FFEE019F* ___0_touchSystem, Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* ___1_finger, const RuntimeMethod* method) 
{
	{
		TouchSystem_tC2752B654ED03B776574555FAFE34482FFEE019F* L_0 = ___0_touchSystem;
		Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* L_1 = ___1_finger;
		BaseInput_FingerDownDetect_m85C3143AA65DD55F8D814009E69987D6C2128C1E(__this, L_0, L_1, NULL);
		TouchSystem_tC2752B654ED03B776574555FAFE34482FFEE019F* L_2 = ___0_touchSystem;
		Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* L_3 = ___1_finger;
		BaseInput_FingerUpDetect_m85145B7062E17C4E70925B1FF7AE35940DC0C84B(__this, L_2, L_3, NULL);
		TouchSystem_tC2752B654ED03B776574555FAFE34482FFEE019F* L_4 = ___0_touchSystem;
		Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* L_5 = ___1_finger;
		BaseInput_FingerMotionDetect_mCDC757C25A4037792BD59A93CD7A39404315C08D(__this, L_4, L_5, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BaseInput_FingerDownDetect_m85C3143AA65DD55F8D814009E69987D6C2128C1E (BaseInput_t2384DD6C51B50C487DFB643E20B936AB7AB3D955* __this, TouchSystem_tC2752B654ED03B776574555FAFE34482FFEE019F* ___0_touchSystem, Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* ___1_finger, const RuntimeMethod* method) 
{
	FingerTouchEvent_t34E5F496B643A2E72BA3BE9F010AC0AD637CE010* V_0 = NULL;
	{
		Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* L_0 = ___1_finger;
		NullCheck(L_0);
		bool L_1;
		L_1 = Finger_get_IsDown_mE1A0287C740B3E37E75F541F79730C49E31E43CD(L_0, NULL);
		if (!L_1)
		{
			goto IL_002b;
		}
	}
	{
		Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* L_2 = ___1_finger;
		NullCheck(L_2);
		bool L_3;
		L_3 = Finger_get_WasDown_m237B3519DD64E0ECEC87ECBCD3061645862D9D25(L_2, NULL);
		if (L_3)
		{
			goto IL_002b;
		}
	}
	{
		TouchSystem_tC2752B654ED03B776574555FAFE34482FFEE019F* L_4 = ___0_touchSystem;
		Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* L_5 = ___1_finger;
		NullCheck(L_5);
		int32_t L_6;
		L_6 = Finger_get_index_mC06528C915844D87E8DB1AB8282667856733F816_inline(L_5, NULL);
		NullCheck(L_4);
		FingerTouchEvent_t34E5F496B643A2E72BA3BE9F010AC0AD637CE010* L_7;
		L_7 = TouchSystem_GetFingerTouchEvent_m8586CA78A0B7E12ACF5F8165AB114C2DF69CA1C0(L_4, L_6, NULL);
		V_0 = L_7;
		FingerTouchEvent_t34E5F496B643A2E72BA3BE9F010AC0AD637CE010* L_8 = V_0;
		Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* L_9 = ___1_finger;
		NullCheck(L_8);
		FingerTouchEvent_set_finger_m5415B5FACC66E0442B88CE0371ED517C31B884C3_inline(L_8, L_9, NULL);
		TouchSystem_tC2752B654ED03B776574555FAFE34482FFEE019F* L_10 = ___0_touchSystem;
		FingerTouchEvent_t34E5F496B643A2E72BA3BE9F010AC0AD637CE010* L_11 = V_0;
		NullCheck(L_10);
		TouchSystem_OnTouchBegan_mC66AB49CD896F6A6D269F96B43C347AC2FB87989(L_10, L_11, NULL);
	}

IL_002b:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BaseInput_FingerUpDetect_m85145B7062E17C4E70925B1FF7AE35940DC0C84B (BaseInput_t2384DD6C51B50C487DFB643E20B936AB7AB3D955* __this, TouchSystem_tC2752B654ED03B776574555FAFE34482FFEE019F* ___0_touchSystem, Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* ___1_finger, const RuntimeMethod* method) 
{
	FingerTouchEvent_t34E5F496B643A2E72BA3BE9F010AC0AD637CE010* V_0 = NULL;
	{
		Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* L_0 = ___1_finger;
		NullCheck(L_0);
		bool L_1;
		L_1 = Finger_get_IsDown_mE1A0287C740B3E37E75F541F79730C49E31E43CD(L_0, NULL);
		if (L_1)
		{
			goto IL_002b;
		}
	}
	{
		Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* L_2 = ___1_finger;
		NullCheck(L_2);
		bool L_3;
		L_3 = Finger_get_WasDown_m237B3519DD64E0ECEC87ECBCD3061645862D9D25(L_2, NULL);
		if (!L_3)
		{
			goto IL_002b;
		}
	}
	{
		TouchSystem_tC2752B654ED03B776574555FAFE34482FFEE019F* L_4 = ___0_touchSystem;
		Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* L_5 = ___1_finger;
		NullCheck(L_5);
		int32_t L_6;
		L_6 = Finger_get_index_mC06528C915844D87E8DB1AB8282667856733F816_inline(L_5, NULL);
		NullCheck(L_4);
		FingerTouchEvent_t34E5F496B643A2E72BA3BE9F010AC0AD637CE010* L_7;
		L_7 = TouchSystem_GetFingerTouchEvent_m8586CA78A0B7E12ACF5F8165AB114C2DF69CA1C0(L_4, L_6, NULL);
		V_0 = L_7;
		FingerTouchEvent_t34E5F496B643A2E72BA3BE9F010AC0AD637CE010* L_8 = V_0;
		Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* L_9 = ___1_finger;
		NullCheck(L_8);
		FingerTouchEvent_set_finger_m5415B5FACC66E0442B88CE0371ED517C31B884C3_inline(L_8, L_9, NULL);
		TouchSystem_tC2752B654ED03B776574555FAFE34482FFEE019F* L_10 = ___0_touchSystem;
		FingerTouchEvent_t34E5F496B643A2E72BA3BE9F010AC0AD637CE010* L_11 = V_0;
		NullCheck(L_10);
		TouchSystem_OnTouchEnded_mEE172ACC2021B4F930507388362C4B8258BB4E2F(L_10, L_11, NULL);
	}

IL_002b:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BaseInput_FingerMotionDetect_mCDC757C25A4037792BD59A93CD7A39404315C08D (BaseInput_t2384DD6C51B50C487DFB643E20B936AB7AB3D955* __this, TouchSystem_tC2752B654ED03B776574555FAFE34482FFEE019F* ___0_touchSystem, Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* ___1_finger, const RuntimeMethod* method) 
{
	FingerMotionEvent_t9B15ED4278906BF70EE19462D3663C0A7AAC266F* V_0 = NULL;
	{
		TouchSystem_tC2752B654ED03B776574555FAFE34482FFEE019F* L_0 = ___0_touchSystem;
		Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* L_1 = ___1_finger;
		NullCheck(L_1);
		int32_t L_2;
		L_2 = Finger_get_index_mC06528C915844D87E8DB1AB8282667856733F816_inline(L_1, NULL);
		NullCheck(L_0);
		FingerMotionEvent_t9B15ED4278906BF70EE19462D3663C0A7AAC266F* L_3;
		L_3 = TouchSystem_GetFingerMotionEvent_m5F6EF7B406DE7BE437CDB89235E4F5244DFC875B(L_0, L_2, NULL);
		V_0 = L_3;
		FingerMotionEvent_t9B15ED4278906BF70EE19462D3663C0A7AAC266F* L_4 = V_0;
		Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* L_5 = ___1_finger;
		NullCheck(L_4);
		FingerTouchEvent_set_finger_m5415B5FACC66E0442B88CE0371ED517C31B884C3_inline(L_4, L_5, NULL);
		Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* L_6 = ___1_finger;
		NullCheck(L_6);
		int32_t L_7;
		L_7 = Finger_get_state_m812016FD4AFDECF6F08E0011AFE7E09F4BA93A7D_inline(L_6, NULL);
		Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* L_8 = ___1_finger;
		NullCheck(L_8);
		int32_t L_9;
		L_9 = Finger_get_lastState_mFE618D5B1F679CD097822524430FCC7AAB61811E_inline(L_8, NULL);
		if ((((int32_t)L_7) == ((int32_t)L_9)))
		{
			goto IL_002b;
		}
	}
	{
		FingerMotionEvent_t9B15ED4278906BF70EE19462D3663C0A7AAC266F* L_10 = V_0;
		NullCheck(L_10);
		FingerMotionEvent_set_phase_m7BAB2F38CFEE73443835EAE8815CC64BCB7CB782_inline(L_10, 1, NULL);
		goto IL_0032;
	}

IL_002b:
	{
		FingerMotionEvent_t9B15ED4278906BF70EE19462D3663C0A7AAC266F* L_11 = V_0;
		NullCheck(L_11);
		FingerMotionEvent_set_phase_m7BAB2F38CFEE73443835EAE8815CC64BCB7CB782_inline(L_11, 2, NULL);
	}

IL_0032:
	{
		Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* L_12 = ___1_finger;
		NullCheck(L_12);
		bool L_13;
		L_13 = Finger_get_IsMoving_m64569640AD76608F9AED5FE4B3A32CB0978DA48F(L_12, NULL);
		if (!L_13)
		{
			goto IL_0042;
		}
	}
	{
		TouchSystem_tC2752B654ED03B776574555FAFE34482FFEE019F* L_14 = ___0_touchSystem;
		FingerMotionEvent_t9B15ED4278906BF70EE19462D3663C0A7AAC266F* L_15 = V_0;
		NullCheck(L_14);
		TouchSystem_OnTouchMoved_m2DCD48D75A308C714E5CE32E4D2699998213DF33(L_14, L_15, NULL);
		return;
	}

IL_0042:
	{
		Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* L_16 = ___1_finger;
		NullCheck(L_16);
		bool L_17;
		L_17 = Finger_get_IsStationary_mEAEBF6AC6728498061C9E7ECC20FF8FA8DE60D6B(L_16, NULL);
		if (!L_17)
		{
			goto IL_0051;
		}
	}
	{
		TouchSystem_tC2752B654ED03B776574555FAFE34482FFEE019F* L_18 = ___0_touchSystem;
		FingerMotionEvent_t9B15ED4278906BF70EE19462D3663C0A7AAC266F* L_19 = V_0;
		NullCheck(L_18);
		TouchSystem_OnTouchStationary_m6A121B7F5B3168946089FF18D66534AF206C741F(L_18, L_19, NULL);
	}

IL_0051:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void BaseInput__ctor_mB6868ADDD8A711942038C76BF195BDDF092ADD34 (BaseInput_t2384DD6C51B50C487DFB643E20B936AB7AB3D955* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TouchInput_Update_m06E3A199603EA55BD90766C191DB7329690A1213 (TouchInput_tD27C80459CCFF7B548F3E9665BDD0F2E24BE27D7* __this, TouchSystem_tC2752B654ED03B776574555FAFE34482FFEE019F* ___0_touchSystem, const RuntimeMethod* method) 
{
	float V_0 = 0.0f;
	int32_t V_1 = 0;
	int32_t V_2 = 0;
	Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* V_3 = NULL;
	Touch_t03E51455ED508492B3F278903A0114FA0E87B417 V_4;
	memset((&V_4), 0, sizeof(V_4));
	int32_t V_5 = 0;
	int32_t V_6 = 0;
	int32_t V_7 = 0;
	Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* V_8 = NULL;
	{
		float L_0;
		L_0 = Time_get_deltaTime_mC3195000401F0FD167DD2F948FD2BC58330D0865(NULL);
		V_0 = L_0;
		int32_t L_1;
		L_1 = Input_get_touchCount_m057388BFC67A0F4CA53764B1022867ED81D01E39(NULL);
		V_1 = L_1;
		V_2 = 0;
		goto IL_00a7;
	}

IL_0013:
	{
		TouchSystem_tC2752B654ED03B776574555FAFE34482FFEE019F* L_2 = ___0_touchSystem;
		int32_t L_3 = V_2;
		NullCheck(L_2);
		Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* L_4;
		L_4 = TouchSystem_GetFinger_m68BB32C0E4CF8DE2BD9B2FAE1008A6DA6BE92447(L_2, L_3, NULL);
		V_3 = L_4;
		Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* L_5 = V_3;
		if (!L_5)
		{
			goto IL_00a3;
		}
	}
	{
		int32_t L_6 = V_2;
		int32_t L_7 = V_1;
		if ((((int32_t)L_6) >= ((int32_t)L_7)))
		{
			goto IL_008c;
		}
	}
	{
		int32_t L_8 = V_2;
		Touch_t03E51455ED508492B3F278903A0114FA0E87B417 L_9;
		L_9 = Input_GetTouch_m75D99FE801A94279874FA8DC6B6ADAD35F5123B1(L_8, NULL);
		V_4 = L_9;
		Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* L_10 = V_3;
		int32_t L_11;
		L_11 = Touch_get_fingerId_mC1DCE93BFA0574960A3AE5329AE6C5F7E06962BD((&V_4), NULL);
		NullCheck(L_10);
		Finger_set_fingerId_m1FF76B25E1F89C7F8A9EAEA171F3C5338BA94C7C_inline(L_10, L_11, NULL);
		V_5 = 0;
		int32_t L_12;
		L_12 = Touch_get_phase_mB82409FB2BE1C32ABDBA6A72E52A099D28AB70B0((&V_4), NULL);
		V_6 = L_12;
		int32_t L_13 = V_6;
		switch (L_13)
		{
			case 0:
			{
				goto IL_0063;
			}
			case 1:
			{
				goto IL_0068;
			}
			case 2:
			{
				goto IL_006d;
			}
			case 3:
			{
				goto IL_0072;
			}
			case 4:
			{
				goto IL_0077;
			}
		}
	}
	{
		goto IL_007a;
	}

IL_0063:
	{
		V_5 = 1;
		goto IL_007a;
	}

IL_0068:
	{
		V_5 = 2;
		goto IL_007a;
	}

IL_006d:
	{
		V_5 = 3;
		goto IL_007a;
	}

IL_0072:
	{
		V_5 = 4;
		goto IL_007a;
	}

IL_0077:
	{
		V_5 = 5;
	}

IL_007a:
	{
		Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* L_14 = V_3;
		float L_15 = V_0;
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_16;
		L_16 = Touch_get_position_m41B9EB0F3F3E1BE98CEB388253A9E31979CB964A((&V_4), NULL);
		int32_t L_17 = V_5;
		NullCheck(L_14);
		Finger_Update_m8A7DBD8E7C51BEBAC9510DDB6D74631921D0573E(L_14, L_15, L_16, L_17, NULL);
		goto IL_00a3;
	}

IL_008c:
	{
		Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* L_18 = V_3;
		NullCheck(L_18);
		int32_t L_19;
		L_19 = Finger_get_fingerId_m6DBB39CBC887BAEE6815383E9095FDF7C9EB5DD7_inline(L_18, NULL);
		if ((((int32_t)L_19) < ((int32_t)0)))
		{
			goto IL_00a3;
		}
	}
	{
		Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* L_20 = V_3;
		float L_21 = V_0;
		Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* L_22 = V_3;
		NullCheck(L_22);
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_23;
		L_23 = Finger_get_position_mD7E4F25C5EC08239EA13505CED61A2E2A7223E93_inline(L_22, NULL);
		NullCheck(L_20);
		Finger_Update_m8A7DBD8E7C51BEBAC9510DDB6D74631921D0573E(L_20, L_21, L_23, 0, NULL);
	}

IL_00a3:
	{
		int32_t L_24 = V_2;
		V_2 = ((int32_t)il2cpp_codegen_add(L_24, 1));
	}

IL_00a7:
	{
		int32_t L_25 = V_2;
		if ((((int32_t)L_25) < ((int32_t)((int32_t)10))))
		{
			goto IL_0013;
		}
	}
	{
		V_7 = 0;
		goto IL_00f3;
	}

IL_00b4:
	{
		TouchSystem_tC2752B654ED03B776574555FAFE34482FFEE019F* L_26 = ___0_touchSystem;
		int32_t L_27 = V_7;
		NullCheck(L_26);
		Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* L_28;
		L_28 = TouchSystem_GetFinger_m68BB32C0E4CF8DE2BD9B2FAE1008A6DA6BE92447(L_26, L_27, NULL);
		V_8 = L_28;
		Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* L_29 = V_8;
		if (!L_29)
		{
			goto IL_00ed;
		}
	}
	{
		int32_t L_30 = V_7;
		int32_t L_31 = V_1;
		if ((((int32_t)L_30) >= ((int32_t)L_31)))
		{
			goto IL_00d2;
		}
	}
	{
		TouchSystem_tC2752B654ED03B776574555FAFE34482FFEE019F* L_32 = ___0_touchSystem;
		Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* L_33 = V_8;
		BaseInput_ProcessFingerEvent_m24AC7960A0D255CB4660A38436D4ECA5D6696965(__this, L_32, L_33, NULL);
		goto IL_00ed;
	}

IL_00d2:
	{
		Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* L_34 = V_8;
		NullCheck(L_34);
		int32_t L_35;
		L_35 = Finger_get_fingerId_m6DBB39CBC887BAEE6815383E9095FDF7C9EB5DD7_inline(L_34, NULL);
		if ((((int32_t)L_35) < ((int32_t)0)))
		{
			goto IL_00e5;
		}
	}
	{
		TouchSystem_tC2752B654ED03B776574555FAFE34482FFEE019F* L_36 = ___0_touchSystem;
		Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* L_37 = V_8;
		BaseInput_ProcessFingerEvent_m24AC7960A0D255CB4660A38436D4ECA5D6696965(__this, L_36, L_37, NULL);
	}

IL_00e5:
	{
		Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* L_38 = V_8;
		NullCheck(L_38);
		Finger_set_fingerId_m1FF76B25E1F89C7F8A9EAEA171F3C5338BA94C7C_inline(L_38, (-1), NULL);
	}

IL_00ed:
	{
		int32_t L_39 = V_7;
		V_7 = ((int32_t)il2cpp_codegen_add(L_39, 1));
	}

IL_00f3:
	{
		int32_t L_40 = V_7;
		if ((((int32_t)L_40) < ((int32_t)((int32_t)10))))
		{
			goto IL_00b4;
		}
	}
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TouchInput__ctor_m6E613CB00B7819C21FE35D7C3CD54CEB961EB376 (TouchInput_tD27C80459CCFF7B548F3E9665BDD0F2E24BE27D7* __this, const RuntimeMethod* method) 
{
	{
		BaseInput__ctor_mB6868ADDD8A711942038C76BF195BDDF092ADD34(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void MouseInput_Update_mB57E2E8F5293F5C1E7461583DAED83B26D5F7582 (MouseInput_t271B5F56C59AE6FC0D33D06407F9FEAEC540E3ED* __this, TouchSystem_tC2752B654ED03B776574555FAFE34482FFEE019F* ___0_touchSystem, const RuntimeMethod* method) 
{
	float V_0 = 0.0f;
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_1;
	memset((&V_1), 0, sizeof(V_1));
	int32_t V_2 = 0;
	Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* V_3 = NULL;
	int32_t V_4 = 0;
	int32_t V_5 = 0;
	Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* V_6 = NULL;
	{
		float L_0;
		L_0 = Time_get_deltaTime_mC3195000401F0FD167DD2F948FD2BC58330D0865(NULL);
		V_0 = L_0;
		V_2 = 0;
		goto IL_005e;
	}

IL_000a:
	{
		TouchSystem_tC2752B654ED03B776574555FAFE34482FFEE019F* L_1 = ___0_touchSystem;
		int32_t L_2 = V_2;
		NullCheck(L_1);
		Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* L_3;
		L_3 = TouchSystem_GetFinger_m68BB32C0E4CF8DE2BD9B2FAE1008A6DA6BE92447(L_1, L_2, NULL);
		V_3 = L_3;
		Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* L_4 = V_3;
		if (!L_4)
		{
			goto IL_005a;
		}
	}
	{
		int32_t L_5 = V_2;
		if ((((int32_t)L_5) > ((int32_t)2)))
		{
			goto IL_0043;
		}
	}
	{
		Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* L_6 = V_3;
		int32_t L_7 = V_2;
		NullCheck(L_6);
		Finger_set_fingerId_m1FF76B25E1F89C7F8A9EAEA171F3C5338BA94C7C_inline(L_6, L_7, NULL);
		V_4 = 0;
		int32_t L_8 = V_2;
		bool L_9;
		L_9 = Input_GetMouseButton_m4995DD4A2D4F916565C1B1B5AAF7DF17C126B3EA(L_8, NULL);
		if (!L_9)
		{
			goto IL_002e;
		}
	}
	{
		V_4 = 1;
	}

IL_002e:
	{
		Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* L_10 = V_3;
		float L_11 = V_0;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_12;
		L_12 = Input_get_mousePosition_mFF21FBD2647DAE2A23BD4C45571CA95D05A0A42C(NULL);
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_13;
		L_13 = Vector2_op_Implicit_mE8EBEE9291F11BB02F062D6E000F4798968CBD96_inline(L_12, NULL);
		int32_t L_14 = V_4;
		NullCheck(L_10);
		Finger_Update_m8A7DBD8E7C51BEBAC9510DDB6D74631921D0573E(L_10, L_11, L_13, L_14, NULL);
		goto IL_005a;
	}

IL_0043:
	{
		Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* L_15 = V_3;
		NullCheck(L_15);
		int32_t L_16;
		L_16 = Finger_get_fingerId_m6DBB39CBC887BAEE6815383E9095FDF7C9EB5DD7_inline(L_15, NULL);
		if ((((int32_t)L_16) < ((int32_t)0)))
		{
			goto IL_005a;
		}
	}
	{
		Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* L_17 = V_3;
		float L_18 = V_0;
		Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* L_19 = V_3;
		NullCheck(L_19);
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_20;
		L_20 = Finger_get_position_mD7E4F25C5EC08239EA13505CED61A2E2A7223E93_inline(L_19, NULL);
		NullCheck(L_17);
		Finger_Update_m8A7DBD8E7C51BEBAC9510DDB6D74631921D0573E(L_17, L_18, L_20, 0, NULL);
	}

IL_005a:
	{
		int32_t L_21 = V_2;
		V_2 = ((int32_t)il2cpp_codegen_add(L_21, 1));
	}

IL_005e:
	{
		int32_t L_22 = V_2;
		if ((((int32_t)L_22) < ((int32_t)((int32_t)10))))
		{
			goto IL_000a;
		}
	}
	{
		V_5 = 0;
		goto IL_00a7;
	}

IL_0068:
	{
		TouchSystem_tC2752B654ED03B776574555FAFE34482FFEE019F* L_23 = ___0_touchSystem;
		int32_t L_24 = V_5;
		NullCheck(L_23);
		Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* L_25;
		L_25 = TouchSystem_GetFinger_m68BB32C0E4CF8DE2BD9B2FAE1008A6DA6BE92447(L_23, L_24, NULL);
		V_6 = L_25;
		Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* L_26 = V_6;
		if (!L_26)
		{
			goto IL_00a1;
		}
	}
	{
		int32_t L_27 = V_5;
		if ((((int32_t)L_27) > ((int32_t)2)))
		{
			goto IL_0086;
		}
	}
	{
		TouchSystem_tC2752B654ED03B776574555FAFE34482FFEE019F* L_28 = ___0_touchSystem;
		Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* L_29 = V_6;
		BaseInput_ProcessFingerEvent_m24AC7960A0D255CB4660A38436D4ECA5D6696965(__this, L_28, L_29, NULL);
		goto IL_00a1;
	}

IL_0086:
	{
		Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* L_30 = V_6;
		NullCheck(L_30);
		int32_t L_31;
		L_31 = Finger_get_fingerId_m6DBB39CBC887BAEE6815383E9095FDF7C9EB5DD7_inline(L_30, NULL);
		if ((((int32_t)L_31) < ((int32_t)0)))
		{
			goto IL_0099;
		}
	}
	{
		TouchSystem_tC2752B654ED03B776574555FAFE34482FFEE019F* L_32 = ___0_touchSystem;
		Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* L_33 = V_6;
		BaseInput_ProcessFingerEvent_m24AC7960A0D255CB4660A38436D4ECA5D6696965(__this, L_32, L_33, NULL);
	}

IL_0099:
	{
		Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* L_34 = V_6;
		NullCheck(L_34);
		Finger_set_fingerId_m1FF76B25E1F89C7F8A9EAEA171F3C5338BA94C7C_inline(L_34, (-1), NULL);
	}

IL_00a1:
	{
		int32_t L_35 = V_5;
		V_5 = ((int32_t)il2cpp_codegen_add(L_35, 1));
	}

IL_00a7:
	{
		int32_t L_36 = V_5;
		if ((((int32_t)L_36) < ((int32_t)((int32_t)10))))
		{
			goto IL_0068;
		}
	}
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_37;
		L_37 = Input_get_mousePosition_mFF21FBD2647DAE2A23BD4C45571CA95D05A0A42C(NULL);
		V_1 = L_37;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_38 = V_1;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_39 = __this->___m_LastMousePosition;
		bool L_40;
		L_40 = Vector3_op_Inequality_m9F170CDFBF1E490E559DA5D06D6547501A402BBF_inline(L_38, L_39, NULL);
		if (!L_40)
		{
			goto IL_00d9;
		}
	}
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_41 = V_1;
		__this->___m_LastMousePosition = L_41;
		TouchSystem_tC2752B654ED03B776574555FAFE34482FFEE019F* L_42 = ___0_touchSystem;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_43 = __this->___m_LastMousePosition;
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_44;
		L_44 = Vector2_op_Implicit_mE8EBEE9291F11BB02F062D6E000F4798968CBD96_inline(L_43, NULL);
		NullCheck(L_42);
		TouchSystem_OnMouseMove_m29102FF4E1122CCD48DF0713C0384050498E45B1(L_42, L_44, NULL);
	}

IL_00d9:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void MouseInput__ctor_m32B1FFB988204A5F86316FBB5E255BC01A19D662 (MouseInput_t271B5F56C59AE6FC0D33D06407F9FEAEC540E3ED* __this, const RuntimeMethod* method) 
{
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0;
		L_0 = Vector3_get_zero_m0C1249C3F25B1C70EAD3CC8B31259975A457AE39_inline(NULL);
		__this->___m_LastMousePosition = L_0;
		BaseInput__ctor_mB6868ADDD8A711942038C76BF195BDDF092ADD34(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TouchSystem_add_onTouchBegan_mA28750785276F381DAAD08F08988DBA440F0A58F (TouchSystem_tC2752B654ED03B776574555FAFE34482FFEE019F* __this, FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* V_0 = NULL;
	FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* V_1 = NULL;
	FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* V_2 = NULL;
	{
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_0 = __this->___onTouchBegan;
		V_0 = L_0;
	}

IL_0007:
	{
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_1 = V_0;
		V_1 = L_1;
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_2 = V_1;
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_3 = ___0_value;
		Delegate_t* L_4;
		L_4 = Delegate_Combine_m1F725AEF318BE6F0426863490691A6F4606E7D00(L_2, L_3, NULL);
		V_2 = ((FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51*)CastclassSealed((RuntimeObject*)L_4, FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51_il2cpp_TypeInfo_var));
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51** L_5 = (FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51**)(&__this->___onTouchBegan);
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_6 = V_2;
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_7 = V_1;
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_8;
		L_8 = InterlockedCompareExchangeImpl<FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51*>(L_5, L_6, L_7);
		V_0 = L_8;
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_9 = V_0;
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_10 = V_1;
		if ((!(((RuntimeObject*)(FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51*)L_9) == ((RuntimeObject*)(FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51*)L_10))))
		{
			goto IL_0007;
		}
	}
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TouchSystem_remove_onTouchBegan_m113B6467C1144BE2EE7F790768D6E4D29907B858 (TouchSystem_tC2752B654ED03B776574555FAFE34482FFEE019F* __this, FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* V_0 = NULL;
	FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* V_1 = NULL;
	FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* V_2 = NULL;
	{
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_0 = __this->___onTouchBegan;
		V_0 = L_0;
	}

IL_0007:
	{
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_1 = V_0;
		V_1 = L_1;
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_2 = V_1;
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_3 = ___0_value;
		Delegate_t* L_4;
		L_4 = Delegate_Remove_m8B7DD5661308FA972E23CA1CC3FC9CEB355504E3(L_2, L_3, NULL);
		V_2 = ((FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51*)CastclassSealed((RuntimeObject*)L_4, FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51_il2cpp_TypeInfo_var));
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51** L_5 = (FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51**)(&__this->___onTouchBegan);
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_6 = V_2;
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_7 = V_1;
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_8;
		L_8 = InterlockedCompareExchangeImpl<FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51*>(L_5, L_6, L_7);
		V_0 = L_8;
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_9 = V_0;
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_10 = V_1;
		if ((!(((RuntimeObject*)(FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51*)L_9) == ((RuntimeObject*)(FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51*)L_10))))
		{
			goto IL_0007;
		}
	}
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TouchSystem_add_onTouchMoved_m3E8BEF833C4D9899E3154CA18E047C98BB38E244 (TouchSystem_tC2752B654ED03B776574555FAFE34482FFEE019F* __this, FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* V_0 = NULL;
	FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* V_1 = NULL;
	FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* V_2 = NULL;
	{
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_0 = __this->___onTouchMoved;
		V_0 = L_0;
	}

IL_0007:
	{
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_1 = V_0;
		V_1 = L_1;
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_2 = V_1;
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_3 = ___0_value;
		Delegate_t* L_4;
		L_4 = Delegate_Combine_m1F725AEF318BE6F0426863490691A6F4606E7D00(L_2, L_3, NULL);
		V_2 = ((FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51*)CastclassSealed((RuntimeObject*)L_4, FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51_il2cpp_TypeInfo_var));
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51** L_5 = (FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51**)(&__this->___onTouchMoved);
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_6 = V_2;
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_7 = V_1;
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_8;
		L_8 = InterlockedCompareExchangeImpl<FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51*>(L_5, L_6, L_7);
		V_0 = L_8;
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_9 = V_0;
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_10 = V_1;
		if ((!(((RuntimeObject*)(FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51*)L_9) == ((RuntimeObject*)(FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51*)L_10))))
		{
			goto IL_0007;
		}
	}
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TouchSystem_remove_onTouchMoved_mBBED6A01619E86B8888668E8B20CE8DFDE1B96EF (TouchSystem_tC2752B654ED03B776574555FAFE34482FFEE019F* __this, FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* V_0 = NULL;
	FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* V_1 = NULL;
	FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* V_2 = NULL;
	{
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_0 = __this->___onTouchMoved;
		V_0 = L_0;
	}

IL_0007:
	{
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_1 = V_0;
		V_1 = L_1;
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_2 = V_1;
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_3 = ___0_value;
		Delegate_t* L_4;
		L_4 = Delegate_Remove_m8B7DD5661308FA972E23CA1CC3FC9CEB355504E3(L_2, L_3, NULL);
		V_2 = ((FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51*)CastclassSealed((RuntimeObject*)L_4, FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51_il2cpp_TypeInfo_var));
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51** L_5 = (FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51**)(&__this->___onTouchMoved);
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_6 = V_2;
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_7 = V_1;
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_8;
		L_8 = InterlockedCompareExchangeImpl<FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51*>(L_5, L_6, L_7);
		V_0 = L_8;
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_9 = V_0;
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_10 = V_1;
		if ((!(((RuntimeObject*)(FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51*)L_9) == ((RuntimeObject*)(FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51*)L_10))))
		{
			goto IL_0007;
		}
	}
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TouchSystem_add_onTouchStationary_m1333F178F707435EEFD189A29BE4058E35149AD9 (TouchSystem_tC2752B654ED03B776574555FAFE34482FFEE019F* __this, FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* V_0 = NULL;
	FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* V_1 = NULL;
	FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* V_2 = NULL;
	{
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_0 = __this->___onTouchStationary;
		V_0 = L_0;
	}

IL_0007:
	{
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_1 = V_0;
		V_1 = L_1;
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_2 = V_1;
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_3 = ___0_value;
		Delegate_t* L_4;
		L_4 = Delegate_Combine_m1F725AEF318BE6F0426863490691A6F4606E7D00(L_2, L_3, NULL);
		V_2 = ((FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51*)CastclassSealed((RuntimeObject*)L_4, FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51_il2cpp_TypeInfo_var));
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51** L_5 = (FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51**)(&__this->___onTouchStationary);
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_6 = V_2;
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_7 = V_1;
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_8;
		L_8 = InterlockedCompareExchangeImpl<FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51*>(L_5, L_6, L_7);
		V_0 = L_8;
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_9 = V_0;
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_10 = V_1;
		if ((!(((RuntimeObject*)(FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51*)L_9) == ((RuntimeObject*)(FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51*)L_10))))
		{
			goto IL_0007;
		}
	}
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TouchSystem_remove_onTouchStationary_m0F0C0285C1789CD109B7CEC9775DB750C06B26DF (TouchSystem_tC2752B654ED03B776574555FAFE34482FFEE019F* __this, FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* V_0 = NULL;
	FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* V_1 = NULL;
	FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* V_2 = NULL;
	{
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_0 = __this->___onTouchStationary;
		V_0 = L_0;
	}

IL_0007:
	{
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_1 = V_0;
		V_1 = L_1;
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_2 = V_1;
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_3 = ___0_value;
		Delegate_t* L_4;
		L_4 = Delegate_Remove_m8B7DD5661308FA972E23CA1CC3FC9CEB355504E3(L_2, L_3, NULL);
		V_2 = ((FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51*)CastclassSealed((RuntimeObject*)L_4, FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51_il2cpp_TypeInfo_var));
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51** L_5 = (FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51**)(&__this->___onTouchStationary);
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_6 = V_2;
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_7 = V_1;
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_8;
		L_8 = InterlockedCompareExchangeImpl<FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51*>(L_5, L_6, L_7);
		V_0 = L_8;
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_9 = V_0;
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_10 = V_1;
		if ((!(((RuntimeObject*)(FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51*)L_9) == ((RuntimeObject*)(FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51*)L_10))))
		{
			goto IL_0007;
		}
	}
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TouchSystem_add_onTouchEnded_m9729108953ADA35687DFD161187C528CF22E4AF0 (TouchSystem_tC2752B654ED03B776574555FAFE34482FFEE019F* __this, FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* V_0 = NULL;
	FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* V_1 = NULL;
	FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* V_2 = NULL;
	{
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_0 = __this->___onTouchEnded;
		V_0 = L_0;
	}

IL_0007:
	{
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_1 = V_0;
		V_1 = L_1;
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_2 = V_1;
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_3 = ___0_value;
		Delegate_t* L_4;
		L_4 = Delegate_Combine_m1F725AEF318BE6F0426863490691A6F4606E7D00(L_2, L_3, NULL);
		V_2 = ((FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51*)CastclassSealed((RuntimeObject*)L_4, FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51_il2cpp_TypeInfo_var));
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51** L_5 = (FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51**)(&__this->___onTouchEnded);
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_6 = V_2;
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_7 = V_1;
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_8;
		L_8 = InterlockedCompareExchangeImpl<FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51*>(L_5, L_6, L_7);
		V_0 = L_8;
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_9 = V_0;
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_10 = V_1;
		if ((!(((RuntimeObject*)(FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51*)L_9) == ((RuntimeObject*)(FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51*)L_10))))
		{
			goto IL_0007;
		}
	}
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TouchSystem_remove_onTouchEnded_m7C759B5981235F37D2280C3A95DA032D67352D89 (TouchSystem_tC2752B654ED03B776574555FAFE34482FFEE019F* __this, FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* V_0 = NULL;
	FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* V_1 = NULL;
	FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* V_2 = NULL;
	{
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_0 = __this->___onTouchEnded;
		V_0 = L_0;
	}

IL_0007:
	{
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_1 = V_0;
		V_1 = L_1;
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_2 = V_1;
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_3 = ___0_value;
		Delegate_t* L_4;
		L_4 = Delegate_Remove_m8B7DD5661308FA972E23CA1CC3FC9CEB355504E3(L_2, L_3, NULL);
		V_2 = ((FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51*)CastclassSealed((RuntimeObject*)L_4, FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51_il2cpp_TypeInfo_var));
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51** L_5 = (FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51**)(&__this->___onTouchEnded);
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_6 = V_2;
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_7 = V_1;
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_8;
		L_8 = InterlockedCompareExchangeImpl<FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51*>(L_5, L_6, L_7);
		V_0 = L_8;
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_9 = V_0;
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_10 = V_1;
		if ((!(((RuntimeObject*)(FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51*)L_9) == ((RuntimeObject*)(FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51*)L_10))))
		{
			goto IL_0007;
		}
	}
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TouchSystem_add_onTouchCanceled_m3460ECB73F0331674084779E2AB41C584B6D7640 (TouchSystem_tC2752B654ED03B776574555FAFE34482FFEE019F* __this, FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* V_0 = NULL;
	FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* V_1 = NULL;
	FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* V_2 = NULL;
	{
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_0 = __this->___onTouchCanceled;
		V_0 = L_0;
	}

IL_0007:
	{
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_1 = V_0;
		V_1 = L_1;
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_2 = V_1;
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_3 = ___0_value;
		Delegate_t* L_4;
		L_4 = Delegate_Combine_m1F725AEF318BE6F0426863490691A6F4606E7D00(L_2, L_3, NULL);
		V_2 = ((FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51*)CastclassSealed((RuntimeObject*)L_4, FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51_il2cpp_TypeInfo_var));
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51** L_5 = (FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51**)(&__this->___onTouchCanceled);
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_6 = V_2;
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_7 = V_1;
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_8;
		L_8 = InterlockedCompareExchangeImpl<FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51*>(L_5, L_6, L_7);
		V_0 = L_8;
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_9 = V_0;
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_10 = V_1;
		if ((!(((RuntimeObject*)(FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51*)L_9) == ((RuntimeObject*)(FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51*)L_10))))
		{
			goto IL_0007;
		}
	}
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TouchSystem_remove_onTouchCanceled_mF38F32046D4457A8755562A6C5A12A3F83D9E220 (TouchSystem_tC2752B654ED03B776574555FAFE34482FFEE019F* __this, FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* V_0 = NULL;
	FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* V_1 = NULL;
	FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* V_2 = NULL;
	{
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_0 = __this->___onTouchCanceled;
		V_0 = L_0;
	}

IL_0007:
	{
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_1 = V_0;
		V_1 = L_1;
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_2 = V_1;
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_3 = ___0_value;
		Delegate_t* L_4;
		L_4 = Delegate_Remove_m8B7DD5661308FA972E23CA1CC3FC9CEB355504E3(L_2, L_3, NULL);
		V_2 = ((FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51*)CastclassSealed((RuntimeObject*)L_4, FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51_il2cpp_TypeInfo_var));
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51** L_5 = (FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51**)(&__this->___onTouchCanceled);
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_6 = V_2;
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_7 = V_1;
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_8;
		L_8 = InterlockedCompareExchangeImpl<FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51*>(L_5, L_6, L_7);
		V_0 = L_8;
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_9 = V_0;
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_10 = V_1;
		if ((!(((RuntimeObject*)(FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51*)L_9) == ((RuntimeObject*)(FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51*)L_10))))
		{
			goto IL_0007;
		}
	}
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TouchSystem_add_onMouseMove_m9D56943AD0784F26B578D6684F823FE21D20D2A8 (TouchSystem_tC2752B654ED03B776574555FAFE34482FFEE019F* __this, MouseEventHandler_t3456CF90ECEDAF94161F5D08E4AAD59CF39A0D58* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MouseEventHandler_t3456CF90ECEDAF94161F5D08E4AAD59CF39A0D58_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	MouseEventHandler_t3456CF90ECEDAF94161F5D08E4AAD59CF39A0D58* V_0 = NULL;
	MouseEventHandler_t3456CF90ECEDAF94161F5D08E4AAD59CF39A0D58* V_1 = NULL;
	MouseEventHandler_t3456CF90ECEDAF94161F5D08E4AAD59CF39A0D58* V_2 = NULL;
	{
		MouseEventHandler_t3456CF90ECEDAF94161F5D08E4AAD59CF39A0D58* L_0 = __this->___onMouseMove;
		V_0 = L_0;
	}

IL_0007:
	{
		MouseEventHandler_t3456CF90ECEDAF94161F5D08E4AAD59CF39A0D58* L_1 = V_0;
		V_1 = L_1;
		MouseEventHandler_t3456CF90ECEDAF94161F5D08E4AAD59CF39A0D58* L_2 = V_1;
		MouseEventHandler_t3456CF90ECEDAF94161F5D08E4AAD59CF39A0D58* L_3 = ___0_value;
		Delegate_t* L_4;
		L_4 = Delegate_Combine_m1F725AEF318BE6F0426863490691A6F4606E7D00(L_2, L_3, NULL);
		V_2 = ((MouseEventHandler_t3456CF90ECEDAF94161F5D08E4AAD59CF39A0D58*)CastclassSealed((RuntimeObject*)L_4, MouseEventHandler_t3456CF90ECEDAF94161F5D08E4AAD59CF39A0D58_il2cpp_TypeInfo_var));
		MouseEventHandler_t3456CF90ECEDAF94161F5D08E4AAD59CF39A0D58** L_5 = (MouseEventHandler_t3456CF90ECEDAF94161F5D08E4AAD59CF39A0D58**)(&__this->___onMouseMove);
		MouseEventHandler_t3456CF90ECEDAF94161F5D08E4AAD59CF39A0D58* L_6 = V_2;
		MouseEventHandler_t3456CF90ECEDAF94161F5D08E4AAD59CF39A0D58* L_7 = V_1;
		MouseEventHandler_t3456CF90ECEDAF94161F5D08E4AAD59CF39A0D58* L_8;
		L_8 = InterlockedCompareExchangeImpl<MouseEventHandler_t3456CF90ECEDAF94161F5D08E4AAD59CF39A0D58*>(L_5, L_6, L_7);
		V_0 = L_8;
		MouseEventHandler_t3456CF90ECEDAF94161F5D08E4AAD59CF39A0D58* L_9 = V_0;
		MouseEventHandler_t3456CF90ECEDAF94161F5D08E4AAD59CF39A0D58* L_10 = V_1;
		if ((!(((RuntimeObject*)(MouseEventHandler_t3456CF90ECEDAF94161F5D08E4AAD59CF39A0D58*)L_9) == ((RuntimeObject*)(MouseEventHandler_t3456CF90ECEDAF94161F5D08E4AAD59CF39A0D58*)L_10))))
		{
			goto IL_0007;
		}
	}
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TouchSystem_remove_onMouseMove_mF108DB89D3F374F149A69E5B9A522EEA5FC31C34 (TouchSystem_tC2752B654ED03B776574555FAFE34482FFEE019F* __this, MouseEventHandler_t3456CF90ECEDAF94161F5D08E4AAD59CF39A0D58* ___0_value, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&MouseEventHandler_t3456CF90ECEDAF94161F5D08E4AAD59CF39A0D58_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	MouseEventHandler_t3456CF90ECEDAF94161F5D08E4AAD59CF39A0D58* V_0 = NULL;
	MouseEventHandler_t3456CF90ECEDAF94161F5D08E4AAD59CF39A0D58* V_1 = NULL;
	MouseEventHandler_t3456CF90ECEDAF94161F5D08E4AAD59CF39A0D58* V_2 = NULL;
	{
		MouseEventHandler_t3456CF90ECEDAF94161F5D08E4AAD59CF39A0D58* L_0 = __this->___onMouseMove;
		V_0 = L_0;
	}

IL_0007:
	{
		MouseEventHandler_t3456CF90ECEDAF94161F5D08E4AAD59CF39A0D58* L_1 = V_0;
		V_1 = L_1;
		MouseEventHandler_t3456CF90ECEDAF94161F5D08E4AAD59CF39A0D58* L_2 = V_1;
		MouseEventHandler_t3456CF90ECEDAF94161F5D08E4AAD59CF39A0D58* L_3 = ___0_value;
		Delegate_t* L_4;
		L_4 = Delegate_Remove_m8B7DD5661308FA972E23CA1CC3FC9CEB355504E3(L_2, L_3, NULL);
		V_2 = ((MouseEventHandler_t3456CF90ECEDAF94161F5D08E4AAD59CF39A0D58*)CastclassSealed((RuntimeObject*)L_4, MouseEventHandler_t3456CF90ECEDAF94161F5D08E4AAD59CF39A0D58_il2cpp_TypeInfo_var));
		MouseEventHandler_t3456CF90ECEDAF94161F5D08E4AAD59CF39A0D58** L_5 = (MouseEventHandler_t3456CF90ECEDAF94161F5D08E4AAD59CF39A0D58**)(&__this->___onMouseMove);
		MouseEventHandler_t3456CF90ECEDAF94161F5D08E4AAD59CF39A0D58* L_6 = V_2;
		MouseEventHandler_t3456CF90ECEDAF94161F5D08E4AAD59CF39A0D58* L_7 = V_1;
		MouseEventHandler_t3456CF90ECEDAF94161F5D08E4AAD59CF39A0D58* L_8;
		L_8 = InterlockedCompareExchangeImpl<MouseEventHandler_t3456CF90ECEDAF94161F5D08E4AAD59CF39A0D58*>(L_5, L_6, L_7);
		V_0 = L_8;
		MouseEventHandler_t3456CF90ECEDAF94161F5D08E4AAD59CF39A0D58* L_9 = V_0;
		MouseEventHandler_t3456CF90ECEDAF94161F5D08E4AAD59CF39A0D58* L_10 = V_1;
		if ((!(((RuntimeObject*)(MouseEventHandler_t3456CF90ECEDAF94161F5D08E4AAD59CF39A0D58*)L_9) == ((RuntimeObject*)(MouseEventHandler_t3456CF90ECEDAF94161F5D08E4AAD59CF39A0D58*)L_10))))
		{
			goto IL_0007;
		}
	}
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool TouchSystem_get_IsEnabled_mA9E66B9FBB3257435EE78472D1AED7E9D2E3ABDA (TouchSystem_tC2752B654ED03B776574555FAFE34482FFEE019F* __this, const RuntimeMethod* method) 
{
	{
		bool L_0 = __this->___isEnabled;
		return L_0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TouchSystem_set_IsEnabled_mF77C83E99B5BA493D9B9E7EFCD5DE4DE3D87B09F (TouchSystem_tC2752B654ED03B776574555FAFE34482FFEE019F* __this, bool ___0_value, const RuntimeMethod* method) 
{
	{
		bool L_0 = ___0_value;
		__this->___isEnabled = L_0;
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TouchSystem__ctor_m5A6CE395609E76073CE87AFA629CABF25E48C849 (TouchSystem_tC2752B654ED03B776574555FAFE34482FFEE019F* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&FingerU5BU5D_tCA8476438690E4763B1CA04761ADB4F6C5FC8174_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TouchInput_tD27C80459CCFF7B548F3E9665BDD0F2E24BE27D7_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TouchSystem_InitializeEvent_TisFingerMotionEvent_t9B15ED4278906BF70EE19462D3663C0A7AAC266F_m4F62EF6433E6C4F61900807A3F74AC4EB66D2D84_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TouchSystem_InitializeEvent_TisFingerTouchEvent_t34E5F496B643A2E72BA3BE9F010AC0AD637CE010_m70F4E3C822C303856202895DFE1A132C0DEFBB7D_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	int32_t V_0 = 0;
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		__this->___isEnabled = (bool)1;
		FingerU5BU5D_tCA8476438690E4763B1CA04761ADB4F6C5FC8174* L_0 = (FingerU5BU5D_tCA8476438690E4763B1CA04761ADB4F6C5FC8174*)(FingerU5BU5D_tCA8476438690E4763B1CA04761ADB4F6C5FC8174*)SZArrayNew(FingerU5BU5D_tCA8476438690E4763B1CA04761ADB4F6C5FC8174_il2cpp_TypeInfo_var, (uint32_t)((int32_t)10));
		__this->___fingers = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___fingers), (void*)L_0);
		V_0 = 0;
		goto IL_0030;
	}

IL_001e:
	{
		FingerU5BU5D_tCA8476438690E4763B1CA04761ADB4F6C5FC8174* L_1 = __this->___fingers;
		int32_t L_2 = V_0;
		int32_t L_3 = V_0;
		Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* L_4 = (Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7*)il2cpp_codegen_object_new(Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7_il2cpp_TypeInfo_var);
		Finger__ctor_m3705D1A51634F357E3AE9EFAE4E0C1BF185F5A79(L_4, L_3, NULL);
		NullCheck(L_1);
		ArrayElementTypeCheck (L_1, L_4);
		(L_1)->SetAt(static_cast<il2cpp_array_size_t>(L_2), (Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7*)L_4);
		int32_t L_5 = V_0;
		V_0 = ((int32_t)il2cpp_codegen_add(L_5, 1));
	}

IL_0030:
	{
		int32_t L_6 = V_0;
		if ((((int32_t)L_6) < ((int32_t)((int32_t)10))))
		{
			goto IL_001e;
		}
	}
	{
		FingerTouchEventU5BU5D_tDF450EC4BBEC5A0F2773E7C4B52E29181EF88AD5* L_7;
		L_7 = TouchSystem_InitializeEvent_TisFingerTouchEvent_t34E5F496B643A2E72BA3BE9F010AC0AD637CE010_m70F4E3C822C303856202895DFE1A132C0DEFBB7D(__this, TouchSystem_InitializeEvent_TisFingerTouchEvent_t34E5F496B643A2E72BA3BE9F010AC0AD637CE010_m70F4E3C822C303856202895DFE1A132C0DEFBB7D_RuntimeMethod_var);
		__this->___fingerTouchEvents = L_7;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___fingerTouchEvents), (void*)L_7);
		FingerMotionEventU5BU5D_tBA16855F843A01A1DD72393A8DAF5CB5641930FA* L_8;
		L_8 = TouchSystem_InitializeEvent_TisFingerMotionEvent_t9B15ED4278906BF70EE19462D3663C0A7AAC266F_m4F62EF6433E6C4F61900807A3F74AC4EB66D2D84(__this, TouchSystem_InitializeEvent_TisFingerMotionEvent_t9B15ED4278906BF70EE19462D3663C0A7AAC266F_m4F62EF6433E6C4F61900807A3F74AC4EB66D2D84_RuntimeMethod_var);
		__this->___fingerMotionEvents = L_8;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___fingerMotionEvents), (void*)L_8);
		TouchInput_tD27C80459CCFF7B548F3E9665BDD0F2E24BE27D7* L_9 = (TouchInput_tD27C80459CCFF7B548F3E9665BDD0F2E24BE27D7*)il2cpp_codegen_object_new(TouchInput_tD27C80459CCFF7B548F3E9665BDD0F2E24BE27D7_il2cpp_TypeInfo_var);
		TouchInput__ctor_m6E613CB00B7819C21FE35D7C3CD54CEB961EB376(L_9, NULL);
		__this->___input = L_9;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___input), (void*)L_9);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TouchSystem_Reset_m02A68AE3AA2871E72DD1EBFF973C84715C71F3E3 (TouchSystem_tC2752B654ED03B776574555FAFE34482FFEE019F* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TouchSystem_ResetEvent_TisFingerMotionEvent_t9B15ED4278906BF70EE19462D3663C0A7AAC266F_mA73DFA8CFBEC4F29886CD41F5ECF3CCF79F152BA_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&TouchSystem_ResetEvent_TisFingerTouchEvent_t34E5F496B643A2E72BA3BE9F010AC0AD637CE010_m315CA4166B78F0499E14182E13D6333236C2F7E1_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	int32_t V_0 = 0;
	{
		V_0 = 0;
		goto IL_0015;
	}

IL_0004:
	{
		FingerU5BU5D_tCA8476438690E4763B1CA04761ADB4F6C5FC8174* L_0 = __this->___fingers;
		int32_t L_1 = V_0;
		NullCheck(L_0);
		int32_t L_2 = L_1;
		Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* L_3 = (L_0)->GetAt(static_cast<il2cpp_array_size_t>(L_2));
		NullCheck(L_3);
		Finger_Reset_m8AA9BB5E549A0C766CE9994C43243CAC507CBE7E(L_3, NULL);
		int32_t L_4 = V_0;
		V_0 = ((int32_t)il2cpp_codegen_add(L_4, 1));
	}

IL_0015:
	{
		int32_t L_5 = V_0;
		if ((((int32_t)L_5) < ((int32_t)((int32_t)10))))
		{
			goto IL_0004;
		}
	}
	{
		FingerTouchEventU5BU5D_tDF450EC4BBEC5A0F2773E7C4B52E29181EF88AD5* L_6 = __this->___fingerTouchEvents;
		TouchSystem_ResetEvent_TisFingerTouchEvent_t34E5F496B643A2E72BA3BE9F010AC0AD637CE010_m315CA4166B78F0499E14182E13D6333236C2F7E1(__this, L_6, TouchSystem_ResetEvent_TisFingerTouchEvent_t34E5F496B643A2E72BA3BE9F010AC0AD637CE010_m315CA4166B78F0499E14182E13D6333236C2F7E1_RuntimeMethod_var);
		FingerMotionEventU5BU5D_tBA16855F843A01A1DD72393A8DAF5CB5641930FA* L_7 = __this->___fingerMotionEvents;
		TouchSystem_ResetEvent_TisFingerMotionEvent_t9B15ED4278906BF70EE19462D3663C0A7AAC266F_mA73DFA8CFBEC4F29886CD41F5ECF3CCF79F152BA(__this, L_7, TouchSystem_ResetEvent_TisFingerMotionEvent_t9B15ED4278906BF70EE19462D3663C0A7AAC266F_mA73DFA8CFBEC4F29886CD41F5ECF3CCF79F152BA_RuntimeMethod_var);
		__this->___onTouchBegan = (FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51*)NULL;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___onTouchBegan), (void*)(FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51*)NULL);
		__this->___onTouchMoved = (FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51*)NULL;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___onTouchMoved), (void*)(FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51*)NULL);
		__this->___onTouchStationary = (FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51*)NULL;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___onTouchStationary), (void*)(FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51*)NULL);
		__this->___onTouchEnded = (FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51*)NULL;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___onTouchEnded), (void*)(FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51*)NULL);
		__this->___onTouchCanceled = (FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51*)NULL;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___onTouchCanceled), (void*)(FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51*)NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TouchSystem_OnTouchBegan_mC66AB49CD896F6A6D269F96B43C347AC2FB87989 (TouchSystem_tC2752B654ED03B776574555FAFE34482FFEE019F* __this, FingerTouchEvent_t34E5F496B643A2E72BA3BE9F010AC0AD637CE010* ___0_e, const RuntimeMethod* method) 
{
	{
		bool L_0 = __this->___isEnabled;
		if (!L_0)
		{
			goto IL_001c;
		}
	}
	{
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_1 = __this->___onTouchBegan;
		if (!L_1)
		{
			goto IL_001c;
		}
	}
	{
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_2 = __this->___onTouchBegan;
		FingerTouchEvent_t34E5F496B643A2E72BA3BE9F010AC0AD637CE010* L_3 = ___0_e;
		NullCheck(L_2);
		FingerEventHandler_Invoke_mA20FAE4131B5472E16C4D31CCD3C0C5514F0DAEF_inline(L_2, L_3, NULL);
	}

IL_001c:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TouchSystem_OnTouchMoved_m2DCD48D75A308C714E5CE32E4D2699998213DF33 (TouchSystem_tC2752B654ED03B776574555FAFE34482FFEE019F* __this, FingerTouchEvent_t34E5F496B643A2E72BA3BE9F010AC0AD637CE010* ___0_e, const RuntimeMethod* method) 
{
	{
		bool L_0 = __this->___isEnabled;
		if (!L_0)
		{
			goto IL_001c;
		}
	}
	{
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_1 = __this->___onTouchMoved;
		if (!L_1)
		{
			goto IL_001c;
		}
	}
	{
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_2 = __this->___onTouchMoved;
		FingerTouchEvent_t34E5F496B643A2E72BA3BE9F010AC0AD637CE010* L_3 = ___0_e;
		NullCheck(L_2);
		FingerEventHandler_Invoke_mA20FAE4131B5472E16C4D31CCD3C0C5514F0DAEF_inline(L_2, L_3, NULL);
	}

IL_001c:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TouchSystem_OnTouchStationary_m6A121B7F5B3168946089FF18D66534AF206C741F (TouchSystem_tC2752B654ED03B776574555FAFE34482FFEE019F* __this, FingerTouchEvent_t34E5F496B643A2E72BA3BE9F010AC0AD637CE010* ___0_e, const RuntimeMethod* method) 
{
	{
		bool L_0 = __this->___isEnabled;
		if (!L_0)
		{
			goto IL_001c;
		}
	}
	{
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_1 = __this->___onTouchStationary;
		if (!L_1)
		{
			goto IL_001c;
		}
	}
	{
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_2 = __this->___onTouchStationary;
		FingerTouchEvent_t34E5F496B643A2E72BA3BE9F010AC0AD637CE010* L_3 = ___0_e;
		NullCheck(L_2);
		FingerEventHandler_Invoke_mA20FAE4131B5472E16C4D31CCD3C0C5514F0DAEF_inline(L_2, L_3, NULL);
	}

IL_001c:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TouchSystem_OnTouchEnded_mEE172ACC2021B4F930507388362C4B8258BB4E2F (TouchSystem_tC2752B654ED03B776574555FAFE34482FFEE019F* __this, FingerTouchEvent_t34E5F496B643A2E72BA3BE9F010AC0AD637CE010* ___0_e, const RuntimeMethod* method) 
{
	{
		bool L_0 = __this->___isEnabled;
		if (!L_0)
		{
			goto IL_001c;
		}
	}
	{
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_1 = __this->___onTouchEnded;
		if (!L_1)
		{
			goto IL_001c;
		}
	}
	{
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_2 = __this->___onTouchEnded;
		FingerTouchEvent_t34E5F496B643A2E72BA3BE9F010AC0AD637CE010* L_3 = ___0_e;
		NullCheck(L_2);
		FingerEventHandler_Invoke_mA20FAE4131B5472E16C4D31CCD3C0C5514F0DAEF_inline(L_2, L_3, NULL);
	}

IL_001c:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TouchSystem_OnTouchCanceled_mC3CF47578D002BAE807B5555CA26950B7A9BD7AB (TouchSystem_tC2752B654ED03B776574555FAFE34482FFEE019F* __this, FingerTouchEvent_t34E5F496B643A2E72BA3BE9F010AC0AD637CE010* ___0_e, const RuntimeMethod* method) 
{
	{
		bool L_0 = __this->___isEnabled;
		if (!L_0)
		{
			goto IL_001c;
		}
	}
	{
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_1 = __this->___onTouchCanceled;
		if (!L_1)
		{
			goto IL_001c;
		}
	}
	{
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* L_2 = __this->___onTouchCanceled;
		FingerTouchEvent_t34E5F496B643A2E72BA3BE9F010AC0AD637CE010* L_3 = ___0_e;
		NullCheck(L_2);
		FingerEventHandler_Invoke_mA20FAE4131B5472E16C4D31CCD3C0C5514F0DAEF_inline(L_2, L_3, NULL);
	}

IL_001c:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TouchSystem_OnMouseMove_m29102FF4E1122CCD48DF0713C0384050498E45B1 (TouchSystem_tC2752B654ED03B776574555FAFE34482FFEE019F* __this, Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___0_position, const RuntimeMethod* method) 
{
	{
		bool L_0 = __this->___isEnabled;
		if (!L_0)
		{
			goto IL_001c;
		}
	}
	{
		MouseEventHandler_t3456CF90ECEDAF94161F5D08E4AAD59CF39A0D58* L_1 = __this->___onMouseMove;
		if (!L_1)
		{
			goto IL_001c;
		}
	}
	{
		MouseEventHandler_t3456CF90ECEDAF94161F5D08E4AAD59CF39A0D58* L_2 = __this->___onMouseMove;
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_3 = ___0_position;
		NullCheck(L_2);
		MouseEventHandler_Invoke_m7768333417BCEA31AD71AA27D5ED80ED6CB56D49_inline(L_2, L_3, NULL);
	}

IL_001c:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void TouchSystem_Update_m96A2A02D383B08DE9A4FCB8777885EC17B2ACC00 (TouchSystem_tC2752B654ED03B776574555FAFE34482FFEE019F* __this, const RuntimeMethod* method) 
{
	{
		bool L_0 = __this->___isEnabled;
		if (!L_0)
		{
			goto IL_0014;
		}
	}
	{
		BaseInput_t2384DD6C51B50C487DFB643E20B936AB7AB3D955* L_1 = __this->___input;
		NullCheck(L_1);
		VirtualActionInvoker1< TouchSystem_tC2752B654ED03B776574555FAFE34482FFEE019F* >::Invoke(4, L_1, __this);
	}

IL_0014:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool TouchSystem_get_IsAnyFingerDown_mD8CC65BEC5775BC68623FA8BC05AF53390068399 (TouchSystem_tC2752B654ED03B776574555FAFE34482FFEE019F* __this, const RuntimeMethod* method) 
{
	int32_t V_0 = 0;
	{
		V_0 = 0;
		goto IL_0019;
	}

IL_0004:
	{
		FingerU5BU5D_tCA8476438690E4763B1CA04761ADB4F6C5FC8174* L_0 = __this->___fingers;
		int32_t L_1 = V_0;
		NullCheck(L_0);
		int32_t L_2 = L_1;
		Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* L_3 = (L_0)->GetAt(static_cast<il2cpp_array_size_t>(L_2));
		NullCheck(L_3);
		bool L_4;
		L_4 = Finger_get_IsDown_mE1A0287C740B3E37E75F541F79730C49E31E43CD(L_3, NULL);
		if (!L_4)
		{
			goto IL_0015;
		}
	}
	{
		return (bool)1;
	}

IL_0015:
	{
		int32_t L_5 = V_0;
		V_0 = ((int32_t)il2cpp_codegen_add(L_5, 1));
	}

IL_0019:
	{
		int32_t L_6 = V_0;
		if ((((int32_t)L_6) < ((int32_t)((int32_t)10))))
		{
			goto IL_0004;
		}
	}
	{
		return (bool)0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* TouchSystem_GetFinger_m68BB32C0E4CF8DE2BD9B2FAE1008A6DA6BE92447 (TouchSystem_tC2752B654ED03B776574555FAFE34482FFEE019F* __this, int32_t ___0_index, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = ___0_index;
		if ((((int32_t)L_0) < ((int32_t)0)))
		{
			goto IL_0009;
		}
	}
	{
		int32_t L_1 = ___0_index;
		if ((((int32_t)L_1) < ((int32_t)((int32_t)10))))
		{
			goto IL_000b;
		}
	}

IL_0009:
	{
		return (Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7*)NULL;
	}

IL_000b:
	{
		FingerU5BU5D_tCA8476438690E4763B1CA04761ADB4F6C5FC8174* L_2 = __this->___fingers;
		int32_t L_3 = ___0_index;
		NullCheck(L_2);
		int32_t L_4 = L_3;
		Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* L_5 = (L_2)->GetAt(static_cast<il2cpp_array_size_t>(L_4));
		return L_5;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR FingerTouchEvent_t34E5F496B643A2E72BA3BE9F010AC0AD637CE010* TouchSystem_GetFingerTouchEvent_m8586CA78A0B7E12ACF5F8165AB114C2DF69CA1C0 (TouchSystem_tC2752B654ED03B776574555FAFE34482FFEE019F* __this, int32_t ___0_index, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = ___0_index;
		if ((((int32_t)L_0) < ((int32_t)0)))
		{
			goto IL_0009;
		}
	}
	{
		int32_t L_1 = ___0_index;
		if ((((int32_t)L_1) < ((int32_t)((int32_t)10))))
		{
			goto IL_0014;
		}
	}

IL_0009:
	{
		ArgumentOutOfRangeException_tEA2822DAF62B10EEED00E0E3A341D4BAF78CF85F* L_2 = (ArgumentOutOfRangeException_tEA2822DAF62B10EEED00E0E3A341D4BAF78CF85F*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ArgumentOutOfRangeException_tEA2822DAF62B10EEED00E0E3A341D4BAF78CF85F_il2cpp_TypeInfo_var)));
		ArgumentOutOfRangeException__ctor_mBC1D5DEEA1BA41DE77228CB27D6BAFEB6DCCBF4A(L_2, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteralB5DC5D7CC41FCD1D493CBD69FA5646C6D8EA5F58)), NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_2, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&TouchSystem_GetFingerTouchEvent_m8586CA78A0B7E12ACF5F8165AB114C2DF69CA1C0_RuntimeMethod_var)));
	}

IL_0014:
	{
		FingerTouchEventU5BU5D_tDF450EC4BBEC5A0F2773E7C4B52E29181EF88AD5* L_3 = __this->___fingerTouchEvents;
		int32_t L_4 = ___0_index;
		NullCheck(L_3);
		int32_t L_5 = L_4;
		FingerTouchEvent_t34E5F496B643A2E72BA3BE9F010AC0AD637CE010* L_6 = (L_3)->GetAt(static_cast<il2cpp_array_size_t>(L_5));
		return L_6;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR FingerMotionEvent_t9B15ED4278906BF70EE19462D3663C0A7AAC266F* TouchSystem_GetFingerMotionEvent_m5F6EF7B406DE7BE437CDB89235E4F5244DFC875B (TouchSystem_tC2752B654ED03B776574555FAFE34482FFEE019F* __this, int32_t ___0_index, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = ___0_index;
		if ((((int32_t)L_0) < ((int32_t)0)))
		{
			goto IL_0009;
		}
	}
	{
		int32_t L_1 = ___0_index;
		if ((((int32_t)L_1) < ((int32_t)((int32_t)10))))
		{
			goto IL_0014;
		}
	}

IL_0009:
	{
		ArgumentOutOfRangeException_tEA2822DAF62B10EEED00E0E3A341D4BAF78CF85F* L_2 = (ArgumentOutOfRangeException_tEA2822DAF62B10EEED00E0E3A341D4BAF78CF85F*)il2cpp_codegen_object_new(((RuntimeClass*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&ArgumentOutOfRangeException_tEA2822DAF62B10EEED00E0E3A341D4BAF78CF85F_il2cpp_TypeInfo_var)));
		ArgumentOutOfRangeException__ctor_mBC1D5DEEA1BA41DE77228CB27D6BAFEB6DCCBF4A(L_2, ((String_t*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&_stringLiteral96BAEB2BB90B854432B64BE441E7F64653B59C93)), NULL);
		IL2CPP_RAISE_MANAGED_EXCEPTION(L_2, ((RuntimeMethod*)il2cpp_codegen_initialize_runtime_metadata_inline((uintptr_t*)&TouchSystem_GetFingerMotionEvent_m5F6EF7B406DE7BE437CDB89235E4F5244DFC875B_RuntimeMethod_var)));
	}

IL_0014:
	{
		FingerMotionEventU5BU5D_tBA16855F843A01A1DD72393A8DAF5CB5641930FA* L_3 = __this->___fingerMotionEvents;
		int32_t L_4 = ___0_index;
		NullCheck(L_3);
		int32_t L_5 = L_4;
		FingerMotionEvent_t9B15ED4278906BF70EE19462D3663C0A7AAC266F* L_6 = (L_3)->GetAt(static_cast<il2cpp_array_size_t>(L_5));
		return L_6;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* TouchSystem_GetFingerByID_m58A4BA1C79F5710B7140450880F0F3EFF247FC8C (TouchSystem_tC2752B654ED03B776574555FAFE34482FFEE019F* __this, int32_t ___0_fingerId, const RuntimeMethod* method) 
{
	int32_t V_0 = 0;
	{
		V_0 = 0;
		goto IL_0021;
	}

IL_0004:
	{
		FingerU5BU5D_tCA8476438690E4763B1CA04761ADB4F6C5FC8174* L_0 = __this->___fingers;
		int32_t L_1 = V_0;
		NullCheck(L_0);
		int32_t L_2 = L_1;
		Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* L_3 = (L_0)->GetAt(static_cast<il2cpp_array_size_t>(L_2));
		NullCheck(L_3);
		int32_t L_4;
		L_4 = Finger_get_fingerId_m6DBB39CBC887BAEE6815383E9095FDF7C9EB5DD7_inline(L_3, NULL);
		int32_t L_5 = ___0_fingerId;
		if ((!(((uint32_t)L_4) == ((uint32_t)L_5))))
		{
			goto IL_001d;
		}
	}
	{
		FingerU5BU5D_tCA8476438690E4763B1CA04761ADB4F6C5FC8174* L_6 = __this->___fingers;
		int32_t L_7 = V_0;
		NullCheck(L_6);
		int32_t L_8 = L_7;
		Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* L_9 = (L_6)->GetAt(static_cast<il2cpp_array_size_t>(L_8));
		return L_9;
	}

IL_001d:
	{
		int32_t L_10 = V_0;
		V_0 = ((int32_t)il2cpp_codegen_add(L_10, 1));
	}

IL_0021:
	{
		int32_t L_11 = V_0;
		if ((((int32_t)L_11) < ((int32_t)((int32_t)10))))
		{
			goto IL_0004;
		}
	}
	{
		return (Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7*)NULL;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
void FingerEventHandler_Invoke_mA20FAE4131B5472E16C4D31CCD3C0C5514F0DAEF_Multicast(FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* __this, FingerTouchEvent_t34E5F496B643A2E72BA3BE9F010AC0AD637CE010* ___0_e, const RuntimeMethod* method)
{
	il2cpp_array_size_t length = __this->___delegates->max_length;
	Delegate_t** delegatesToInvoke = reinterpret_cast<Delegate_t**>(__this->___delegates->GetAddressAtUnchecked(0));
	for (il2cpp_array_size_t i = 0; i < length; i++)
	{
		FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* currentDelegate = reinterpret_cast<FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51*>(delegatesToInvoke[i]);
		typedef void (*FunctionPointerType) (RuntimeObject*, FingerTouchEvent_t34E5F496B643A2E72BA3BE9F010AC0AD637CE010*, const RuntimeMethod*);
		((FunctionPointerType)currentDelegate->___invoke_impl)((Il2CppObject*)currentDelegate->___method_code, ___0_e, reinterpret_cast<RuntimeMethod*>(currentDelegate->___method));
	}
}
void FingerEventHandler_Invoke_mA20FAE4131B5472E16C4D31CCD3C0C5514F0DAEF_OpenInst(FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* __this, FingerTouchEvent_t34E5F496B643A2E72BA3BE9F010AC0AD637CE010* ___0_e, const RuntimeMethod* method)
{
	NullCheck(___0_e);
	typedef void (*FunctionPointerType) (FingerTouchEvent_t34E5F496B643A2E72BA3BE9F010AC0AD637CE010*, const RuntimeMethod*);
	((FunctionPointerType)__this->___method_ptr)(___0_e, method);
}
void FingerEventHandler_Invoke_mA20FAE4131B5472E16C4D31CCD3C0C5514F0DAEF_OpenStatic(FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* __this, FingerTouchEvent_t34E5F496B643A2E72BA3BE9F010AC0AD637CE010* ___0_e, const RuntimeMethod* method)
{
	typedef void (*FunctionPointerType) (FingerTouchEvent_t34E5F496B643A2E72BA3BE9F010AC0AD637CE010*, const RuntimeMethod*);
	((FunctionPointerType)__this->___method_ptr)(___0_e, method);
}
void FingerEventHandler_Invoke_mA20FAE4131B5472E16C4D31CCD3C0C5514F0DAEF_OpenVirtual(FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* __this, FingerTouchEvent_t34E5F496B643A2E72BA3BE9F010AC0AD637CE010* ___0_e, const RuntimeMethod* method)
{
	NullCheck(___0_e);
	VirtualActionInvoker0::Invoke(il2cpp_codegen_method_get_slot(method), ___0_e);
}
void FingerEventHandler_Invoke_mA20FAE4131B5472E16C4D31CCD3C0C5514F0DAEF_OpenInterface(FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* __this, FingerTouchEvent_t34E5F496B643A2E72BA3BE9F010AC0AD637CE010* ___0_e, const RuntimeMethod* method)
{
	NullCheck(___0_e);
	InterfaceActionInvoker0::Invoke(il2cpp_codegen_method_get_slot(method), il2cpp_codegen_method_get_declaring_type(method), ___0_e);
}
void FingerEventHandler_Invoke_mA20FAE4131B5472E16C4D31CCD3C0C5514F0DAEF_OpenGenericVirtual(FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* __this, FingerTouchEvent_t34E5F496B643A2E72BA3BE9F010AC0AD637CE010* ___0_e, const RuntimeMethod* method)
{
	NullCheck(___0_e);
	GenericVirtualActionInvoker0::Invoke(method, ___0_e);
}
void FingerEventHandler_Invoke_mA20FAE4131B5472E16C4D31CCD3C0C5514F0DAEF_OpenGenericInterface(FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* __this, FingerTouchEvent_t34E5F496B643A2E72BA3BE9F010AC0AD637CE010* ___0_e, const RuntimeMethod* method)
{
	NullCheck(___0_e);
	GenericInterfaceActionInvoker0::Invoke(method, ___0_e);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FingerEventHandler__ctor_m669822C432A87AC6BBD4722944404DDDA1DD2544 (FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method) 
{
	__this->___method_ptr = (intptr_t)il2cpp_codegen_get_method_pointer((RuntimeMethod*)___1_method);
	__this->___method = ___1_method;
	__this->___m_target = ___0_object;
	Il2CppCodeGenWriteBarrier((void**)(&__this->___m_target), (void*)___0_object);
	int parameterCount = il2cpp_codegen_method_parameter_count((RuntimeMethod*)___1_method);
	__this->___method_code = (intptr_t)__this;
	if (MethodIsStatic((RuntimeMethod*)___1_method))
	{
		bool isOpen = parameterCount == 1;
		if (isOpen)
			__this->___invoke_impl = (intptr_t)&FingerEventHandler_Invoke_mA20FAE4131B5472E16C4D31CCD3C0C5514F0DAEF_OpenStatic;
		else
			{
				__this->___invoke_impl = __this->___method_ptr;
				__this->___method_code = (intptr_t)__this->___m_target;
			}
	}
	else
	{
		bool isOpen = parameterCount == 0;
		if (isOpen)
		{
			if (__this->___method_is_virtual)
			{
				if (il2cpp_codegen_method_is_generic_instance_method((RuntimeMethod*)___1_method))
					if (il2cpp_codegen_method_is_interface_method((RuntimeMethod*)___1_method))
						__this->___invoke_impl = (intptr_t)&FingerEventHandler_Invoke_mA20FAE4131B5472E16C4D31CCD3C0C5514F0DAEF_OpenGenericInterface;
					else
						__this->___invoke_impl = (intptr_t)&FingerEventHandler_Invoke_mA20FAE4131B5472E16C4D31CCD3C0C5514F0DAEF_OpenGenericVirtual;
				else
					if (il2cpp_codegen_method_is_interface_method((RuntimeMethod*)___1_method))
						__this->___invoke_impl = (intptr_t)&FingerEventHandler_Invoke_mA20FAE4131B5472E16C4D31CCD3C0C5514F0DAEF_OpenInterface;
					else
						__this->___invoke_impl = (intptr_t)&FingerEventHandler_Invoke_mA20FAE4131B5472E16C4D31CCD3C0C5514F0DAEF_OpenVirtual;
			}
			else
			{
				__this->___invoke_impl = (intptr_t)&FingerEventHandler_Invoke_mA20FAE4131B5472E16C4D31CCD3C0C5514F0DAEF_OpenInst;
			}
		}
		else
		{
			if (___0_object == NULL)
				il2cpp_codegen_raise_exception(il2cpp_codegen_get_argument_exception(NULL, "Delegate to an instance method cannot have null 'this'."), NULL);
			__this->___invoke_impl = __this->___method_ptr;
			__this->___method_code = (intptr_t)__this->___m_target;
		}
	}
	__this->___extra_arg = (intptr_t)&FingerEventHandler_Invoke_mA20FAE4131B5472E16C4D31CCD3C0C5514F0DAEF_Multicast;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FingerEventHandler_Invoke_mA20FAE4131B5472E16C4D31CCD3C0C5514F0DAEF (FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* __this, FingerTouchEvent_t34E5F496B643A2E72BA3BE9F010AC0AD637CE010* ___0_e, const RuntimeMethod* method) 
{
	typedef void (*FunctionPointerType) (RuntimeObject*, FingerTouchEvent_t34E5F496B643A2E72BA3BE9F010AC0AD637CE010*, const RuntimeMethod*);
	((FunctionPointerType)__this->___invoke_impl)((Il2CppObject*)__this->___method_code, ___0_e, reinterpret_cast<RuntimeMethod*>(__this->___method));
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* FingerEventHandler_BeginInvoke_mDB3AB14294345203982F8D2A4D7F8C762149BC6E (FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* __this, FingerTouchEvent_t34E5F496B643A2E72BA3BE9F010AC0AD637CE010* ___0_e, AsyncCallback_t7FEF460CBDCFB9C5FA2EF776984778B9A4145F4C* ___1_callback, RuntimeObject* ___2_object, const RuntimeMethod* method) 
{
	void *__d_args[2] = {0};
	__d_args[0] = ___0_e;
	return (RuntimeObject*)il2cpp_codegen_delegate_begin_invoke((RuntimeDelegate*)__this, __d_args, (RuntimeDelegate*)___1_callback, (RuntimeObject*)___2_object);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void FingerEventHandler_EndInvoke_m33A342396E28C25FBBDDA3E2EFA692AA6FB3AFE0 (FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* __this, RuntimeObject* ___0_result, const RuntimeMethod* method) 
{
	il2cpp_codegen_delegate_end_invoke((Il2CppAsyncResult*) ___0_result, 0);
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
void MouseEventHandler_Invoke_m7768333417BCEA31AD71AA27D5ED80ED6CB56D49_Multicast(MouseEventHandler_t3456CF90ECEDAF94161F5D08E4AAD59CF39A0D58* __this, Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___0_position, const RuntimeMethod* method)
{
	il2cpp_array_size_t length = __this->___delegates->max_length;
	Delegate_t** delegatesToInvoke = reinterpret_cast<Delegate_t**>(__this->___delegates->GetAddressAtUnchecked(0));
	for (il2cpp_array_size_t i = 0; i < length; i++)
	{
		MouseEventHandler_t3456CF90ECEDAF94161F5D08E4AAD59CF39A0D58* currentDelegate = reinterpret_cast<MouseEventHandler_t3456CF90ECEDAF94161F5D08E4AAD59CF39A0D58*>(delegatesToInvoke[i]);
		typedef void (*FunctionPointerType) (RuntimeObject*, Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7, const RuntimeMethod*);
		((FunctionPointerType)currentDelegate->___invoke_impl)((Il2CppObject*)currentDelegate->___method_code, ___0_position, reinterpret_cast<RuntimeMethod*>(currentDelegate->___method));
	}
}
void MouseEventHandler_Invoke_m7768333417BCEA31AD71AA27D5ED80ED6CB56D49_OpenInst(MouseEventHandler_t3456CF90ECEDAF94161F5D08E4AAD59CF39A0D58* __this, Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___0_position, const RuntimeMethod* method)
{
	typedef void (*FunctionPointerType) (Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7, const RuntimeMethod*);
	((FunctionPointerType)__this->___method_ptr)(___0_position, method);
}
void MouseEventHandler_Invoke_m7768333417BCEA31AD71AA27D5ED80ED6CB56D49_OpenStatic(MouseEventHandler_t3456CF90ECEDAF94161F5D08E4AAD59CF39A0D58* __this, Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___0_position, const RuntimeMethod* method)
{
	typedef void (*FunctionPointerType) (Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7, const RuntimeMethod*);
	((FunctionPointerType)__this->___method_ptr)(___0_position, method);
}
IL2CPP_EXTERN_C  void DelegatePInvokeWrapper_MouseEventHandler_t3456CF90ECEDAF94161F5D08E4AAD59CF39A0D58 (MouseEventHandler_t3456CF90ECEDAF94161F5D08E4AAD59CF39A0D58* __this, Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___0_position, const RuntimeMethod* method)
{
	typedef void (DEFAULT_CALL *PInvokeFunc)(Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7);
	PInvokeFunc il2cppPInvokeFunc = reinterpret_cast<PInvokeFunc>(il2cpp_codegen_get_reverse_pinvoke_function_ptr(__this));
	il2cppPInvokeFunc(___0_position);

}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void MouseEventHandler__ctor_mEE0BB11D23A343C6A8699F352D29A59C7EF1A773 (MouseEventHandler_t3456CF90ECEDAF94161F5D08E4AAD59CF39A0D58* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method) 
{
	__this->___method_ptr = (intptr_t)il2cpp_codegen_get_method_pointer((RuntimeMethod*)___1_method);
	__this->___method = ___1_method;
	__this->___m_target = ___0_object;
	Il2CppCodeGenWriteBarrier((void**)(&__this->___m_target), (void*)___0_object);
	int parameterCount = il2cpp_codegen_method_parameter_count((RuntimeMethod*)___1_method);
	__this->___method_code = (intptr_t)__this;
	if (MethodIsStatic((RuntimeMethod*)___1_method))
	{
		bool isOpen = parameterCount == 1;
		if (isOpen)
			__this->___invoke_impl = (intptr_t)&MouseEventHandler_Invoke_m7768333417BCEA31AD71AA27D5ED80ED6CB56D49_OpenStatic;
		else
			{
				__this->___invoke_impl = __this->___method_ptr;
				__this->___method_code = (intptr_t)__this->___m_target;
			}
	}
	else
	{
		if (___0_object == NULL)
			il2cpp_codegen_raise_exception(il2cpp_codegen_get_argument_exception(NULL, "Delegate to an instance method cannot have null 'this'."), NULL);
		__this->___invoke_impl = __this->___method_ptr;
		__this->___method_code = (intptr_t)__this->___m_target;
	}
	__this->___extra_arg = (intptr_t)&MouseEventHandler_Invoke_m7768333417BCEA31AD71AA27D5ED80ED6CB56D49_Multicast;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void MouseEventHandler_Invoke_m7768333417BCEA31AD71AA27D5ED80ED6CB56D49 (MouseEventHandler_t3456CF90ECEDAF94161F5D08E4AAD59CF39A0D58* __this, Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___0_position, const RuntimeMethod* method) 
{
	typedef void (*FunctionPointerType) (RuntimeObject*, Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7, const RuntimeMethod*);
	((FunctionPointerType)__this->___invoke_impl)((Il2CppObject*)__this->___method_code, ___0_position, reinterpret_cast<RuntimeMethod*>(__this->___method));
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* MouseEventHandler_BeginInvoke_mD633EAAA68460879E021416D51341C685E88F249 (MouseEventHandler_t3456CF90ECEDAF94161F5D08E4AAD59CF39A0D58* __this, Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___0_position, AsyncCallback_t7FEF460CBDCFB9C5FA2EF776984778B9A4145F4C* ___1_callback, RuntimeObject* ___2_object, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	void *__d_args[2] = {0};
	__d_args[0] = Box(Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7_il2cpp_TypeInfo_var, &___0_position);
	return (RuntimeObject*)il2cpp_codegen_delegate_begin_invoke((RuntimeDelegate*)__this, __d_args, (RuntimeDelegate*)___1_callback, (RuntimeObject*)___2_object);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void MouseEventHandler_EndInvoke_m3A755D246163047889D5799FC95E64E1AD17422B (MouseEventHandler_t3456CF90ECEDAF94161F5D08E4AAD59CF39A0D58* __this, RuntimeObject* ___0_result, const RuntimeMethod* method) 
{
	il2cpp_codegen_delegate_end_invoke((Il2CppAsyncResult*) ___0_result, 0);
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Finger_set_index_m29E24C2ACFC92E1AE6AF91E9FCADD9B12D253B31_inline (Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* __this, int32_t ___0_value, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = ___0_value;
		__this->___U3CindexU3Ek__BackingField = L_0;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Finger_set_state_m496CDBF5130ABB33CB2DE4CE3D458DA92A02B18A_inline (Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* __this, int32_t ___0_value, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = ___0_value;
		__this->___U3CstateU3Ek__BackingField = L_0;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Finger_set_lastState_m632AE93F19BA6FB11C6C38138F852F4C94B12BCA_inline (Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* __this, int32_t ___0_value, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = ___0_value;
		__this->___U3ClastStateU3Ek__BackingField = L_0;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Finger_set_fingerId_m1FF76B25E1F89C7F8A9EAEA171F3C5338BA94C7C_inline (Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* __this, int32_t ___0_value, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = ___0_value;
		__this->___U3CfingerIdU3Ek__BackingField = L_0;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 Vector2_get_zero_m32506C40EC2EE7D5D4410BF40D3EE683A3D5F32C_inline (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_0 = ((Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7_StaticFields*)il2cpp_codegen_static_fields_for(Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7_il2cpp_TypeInfo_var))->___zeroVector;
		V_0 = L_0;
		goto IL_0009;
	}

IL_0009:
	{
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_1 = V_0;
		return L_1;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Finger_set_startPosition_mE12A9B74F21D9B66FB9B6F67FEA1D5DE8483B36A_inline (Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* __this, Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___0_value, const RuntimeMethod* method) 
{
	{
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_0 = ___0_value;
		__this->___U3CstartPositionU3Ek__BackingField = L_0;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Finger_set_position_m377C1C4ADC62778CA08627A068DA396D4DB04C7B_inline (Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* __this, Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___0_value, const RuntimeMethod* method) 
{
	{
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_0 = ___0_value;
		__this->___U3CpositionU3Ek__BackingField = L_0;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Finger_set_deltaPosition_mD5D4C0FA06DB9577FB53FFE7962AA94C662A6C78_inline (Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* __this, Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___0_value, const RuntimeMethod* method) 
{
	{
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_0 = ___0_value;
		__this->___U3CdeltaPositionU3Ek__BackingField = L_0;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Finger_set_lastPosition_m74327C5F1FF0FA9A7434D7FB7BBC9BFC135FEA2A_inline (Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* __this, Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___0_value, const RuntimeMethod* method) 
{
	{
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_0 = ___0_value;
		__this->___U3ClastPositionU3Ek__BackingField = L_0;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Finger_set_currentStateDuration_m94BDC11890516A0E7342F2DBB75BDBE4C3848C1C_inline (Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* __this, float ___0_value, const RuntimeMethod* method) 
{
	{
		float L_0 = ___0_value;
		__this->___U3CcurrentStateDurationU3Ek__BackingField = L_0;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t Finger_get_state_m812016FD4AFDECF6F08E0011AFE7E09F4BA93A7D_inline (Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* __this, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = __this->___U3CstateU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Finger_set_Moved_m9CF46413BEAAD5E8E6DD4867E8E99388CEFFF8CE_inline (Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* __this, bool ___0_value, const RuntimeMethod* method) 
{
	{
		bool L_0 = ___0_value;
		__this->___U3CMovedU3Ek__BackingField = L_0;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 Finger_get_position_mD7E4F25C5EC08239EA13505CED61A2E2A7223E93_inline (Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* __this, const RuntimeMethod* method) 
{
	{
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_0 = __this->___U3CpositionU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 Finger_get_lastPosition_m6C385CAF630D4AEEC49C233097D5C5A0E4F651F7_inline (Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* __this, const RuntimeMethod* method) 
{
	{
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_0 = __this->___U3ClastPositionU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 Vector2_op_Subtraction_m44475FCDAD2DA2F98D78A6625EC2DCDFE8803837_inline (Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___0_a, Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___1_b, const RuntimeMethod* method) 
{
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_0 = ___0_a;
		float L_1 = L_0.___x;
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_2 = ___1_b;
		float L_3 = L_2.___x;
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_4 = ___0_a;
		float L_5 = L_4.___y;
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_6 = ___1_b;
		float L_7 = L_6.___y;
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_8;
		memset((&L_8), 0, sizeof(L_8));
		Vector2__ctor_m9525B79969AFFE3254B303A40997A56DEEB6F548_inline((&L_8), ((float)il2cpp_codegen_subtract(L_1, L_3)), ((float)il2cpp_codegen_subtract(L_5, L_7)), NULL);
		V_0 = L_8;
		goto IL_0023;
	}

IL_0023:
	{
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_9 = V_0;
		return L_9;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 Finger_get_deltaPosition_mFF81C99D713BCAD5170BC781EC4A5B39FF2692E3_inline (Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* __this, const RuntimeMethod* method) 
{
	{
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_0 = __this->___U3CdeltaPositionU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Vector2_get_sqrMagnitude_mA16336720C14EEF8BA9B55AE33B98C9EE2082BDC_inline (Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7* __this, const RuntimeMethod* method) 
{
	float V_0 = 0.0f;
	{
		float L_0 = __this->___x;
		float L_1 = __this->___x;
		float L_2 = __this->___y;
		float L_3 = __this->___y;
		V_0 = ((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_0, L_1)), ((float)il2cpp_codegen_multiply(L_2, L_3))));
		goto IL_001f;
	}

IL_001f:
	{
		float L_4 = V_0;
		return L_4;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t Finger_get_lastState_mFE618D5B1F679CD097822524430FCC7AAB61811E_inline (Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* __this, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = __this->___U3ClastStateU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR float Finger_get_currentStateDuration_mD0A03B3889C5FC15D90D7D482569D44F8949E234_inline (Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* __this, const RuntimeMethod* method) 
{
	{
		float L_0 = __this->___U3CcurrentStateDurationU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t Finger_get_index_mC06528C915844D87E8DB1AB8282667856733F816_inline (Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* __this, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = __this->___U3CindexU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void FingerTouchEvent_set_finger_m5415B5FACC66E0442B88CE0371ED517C31B884C3_inline (FingerTouchEvent_t34E5F496B643A2E72BA3BE9F010AC0AD637CE010* __this, Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* ___0_value, const RuntimeMethod* method) 
{
	{
		Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* L_0 = ___0_value;
		__this->___U3CfingerU3Ek__BackingField = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->___U3CfingerU3Ek__BackingField), (void*)L_0);
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void FingerMotionEvent_set_phase_m7BAB2F38CFEE73443835EAE8815CC64BCB7CB782_inline (FingerMotionEvent_t9B15ED4278906BF70EE19462D3663C0A7AAC266F* __this, int32_t ___0_value, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = ___0_value;
		__this->___U3CphaseU3Ek__BackingField = L_0;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t Finger_get_fingerId_m6DBB39CBC887BAEE6815383E9095FDF7C9EB5DD7_inline (Finger_tC9547737A276474B38FA91F52F2F77C0C095ECC7* __this, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = __this->___U3CfingerIdU3Ek__BackingField;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 Vector2_op_Implicit_mE8EBEE9291F11BB02F062D6E000F4798968CBD96_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_v, const RuntimeMethod* method) 
{
	Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = ___0_v;
		float L_1 = L_0.___x;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_2 = ___0_v;
		float L_3 = L_2.___y;
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_4;
		memset((&L_4), 0, sizeof(L_4));
		Vector2__ctor_m9525B79969AFFE3254B303A40997A56DEEB6F548_inline((&L_4), L_1, L_3, NULL);
		V_0 = L_4;
		goto IL_0015;
	}

IL_0015:
	{
		Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 L_5 = V_0;
		return L_5;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool Vector3_op_Inequality_m9F170CDFBF1E490E559DA5D06D6547501A402BBF_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_lhs, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___1_rhs, const RuntimeMethod* method) 
{
	bool V_0 = false;
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = ___0_lhs;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_1 = ___1_rhs;
		bool L_2;
		L_2 = Vector3_op_Equality_mCDCBB8D2EDC3D3BF20F31A25ACB34705D352B479_inline(L_0, L_1, NULL);
		V_0 = (bool)((((int32_t)L_2) == ((int32_t)0))? 1 : 0);
		goto IL_000e;
	}

IL_000e:
	{
		bool L_3 = V_0;
		return L_3;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 Vector3_get_zero_m0C1249C3F25B1C70EAD3CC8B31259975A457AE39_inline (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = ((Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_StaticFields*)il2cpp_codegen_static_fields_for(Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2_il2cpp_TypeInfo_var))->___zeroVector;
		V_0 = L_0;
		goto IL_0009;
	}

IL_0009:
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_1 = V_0;
		return L_1;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void FingerEventHandler_Invoke_mA20FAE4131B5472E16C4D31CCD3C0C5514F0DAEF_inline (FingerEventHandler_t234702B01DD9394C803FC1805B803195BCED4F51* __this, FingerTouchEvent_t34E5F496B643A2E72BA3BE9F010AC0AD637CE010* ___0_e, const RuntimeMethod* method) 
{
	typedef void (*FunctionPointerType) (RuntimeObject*, FingerTouchEvent_t34E5F496B643A2E72BA3BE9F010AC0AD637CE010*, const RuntimeMethod*);
	((FunctionPointerType)__this->___invoke_impl)((Il2CppObject*)__this->___method_code, ___0_e, reinterpret_cast<RuntimeMethod*>(__this->___method));
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void MouseEventHandler_Invoke_m7768333417BCEA31AD71AA27D5ED80ED6CB56D49_inline (MouseEventHandler_t3456CF90ECEDAF94161F5D08E4AAD59CF39A0D58* __this, Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7 ___0_position, const RuntimeMethod* method) 
{
	typedef void (*FunctionPointerType) (RuntimeObject*, Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7, const RuntimeMethod*);
	((FunctionPointerType)__this->___invoke_impl)((Il2CppObject*)__this->___method_code, ___0_position, reinterpret_cast<RuntimeMethod*>(__this->___method));
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Vector2__ctor_m9525B79969AFFE3254B303A40997A56DEEB6F548_inline (Vector2_t1FD6F485C871E832B347AB2DC8CBA08B739D8DF7* __this, float ___0_x, float ___1_y, const RuntimeMethod* method) 
{
	{
		float L_0 = ___0_x;
		__this->___x = L_0;
		float L_1 = ___1_y;
		__this->___y = L_1;
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR bool Vector3_op_Equality_mCDCBB8D2EDC3D3BF20F31A25ACB34705D352B479_inline (Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___0_lhs, Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 ___1_rhs, const RuntimeMethod* method) 
{
	float V_0 = 0.0f;
	float V_1 = 0.0f;
	float V_2 = 0.0f;
	float V_3 = 0.0f;
	bool V_4 = false;
	{
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_0 = ___0_lhs;
		float L_1 = L_0.___x;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_2 = ___1_rhs;
		float L_3 = L_2.___x;
		V_0 = ((float)il2cpp_codegen_subtract(L_1, L_3));
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_4 = ___0_lhs;
		float L_5 = L_4.___y;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_6 = ___1_rhs;
		float L_7 = L_6.___y;
		V_1 = ((float)il2cpp_codegen_subtract(L_5, L_7));
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_8 = ___0_lhs;
		float L_9 = L_8.___z;
		Vector3_t24C512C7B96BBABAD472002D0BA2BDA40A5A80B2 L_10 = ___1_rhs;
		float L_11 = L_10.___z;
		V_2 = ((float)il2cpp_codegen_subtract(L_9, L_11));
		float L_12 = V_0;
		float L_13 = V_0;
		float L_14 = V_1;
		float L_15 = V_1;
		float L_16 = V_2;
		float L_17 = V_2;
		V_3 = ((float)il2cpp_codegen_add(((float)il2cpp_codegen_add(((float)il2cpp_codegen_multiply(L_12, L_13)), ((float)il2cpp_codegen_multiply(L_14, L_15)))), ((float)il2cpp_codegen_multiply(L_16, L_17))));
		float L_18 = V_3;
		V_4 = (bool)((((float)L_18) < ((float)(9.99999944E-11f)))? 1 : 0);
		goto IL_0043;
	}

IL_0043:
	{
		bool L_19 = V_4;
		return L_19;
	}
}

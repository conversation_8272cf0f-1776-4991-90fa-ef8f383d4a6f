﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void EmbeddedAttribute__ctor_mC28DF84064B558747D4183D3B2A79E1774796A48 (void);
extern void NullableAttribute__ctor_m1F90AAE7475A2C0B7FC4B75E6B9E3A24F2C7CF34 (void);
extern void NullableAttribute__ctor_mC25AD0826D15C01627A059DDC504F38951B7A8AD (void);
extern void NullableContextAttribute__ctor_m820C5562C9E012B01BCEED6F85AF73ACB1539A16 (void);
extern void Point64__ctor_mF9971046CBA488FECD3C0B5F177040AF3754B4D7 (void);
extern void Point64__ctor_m17D797B27CC0BEFD2D843D7D81948347FEC6C7B4 (void);
extern void Point64_op_Equality_mFB2C15957F0D3D7254188AA534820B646BF3BD12 (void);
extern void Point64_op_Inequality_m04FD86A71A0E4079ABA6E09C29B0FC940B209FD2 (void);
extern void Point64_ToString_m10DAF5E06F218D9515B89677210B51D10427D1F7 (void);
extern void Point64_Equals_m3B2E07852DBE1C5CA56A1B56ACA70FD6AE61CF08 (void);
extern void Point64_GetHashCode_m9FA878AE2C6E7AFA451DEB41AE64EAE115A68625 (void);
extern void Rect64__ctor_m995D41FC85D9AE84AB1E471AF335D9C2374FAEF3 (void);
extern void Rect64_MidPoint_mE8279166C986661FF2BCA74CE3446431929C5718 (void);
extern void RectD__ctor_mCCCE082BE4AC0560A49E7BBFD841156CAF042BF0 (void);
extern void Path64__ctor_m25A1D1EC69A1E1D6ED4854A62FC93A94CD1C7CFE (void);
extern void Path64__ctor_mBA27E245E819ADF18DB1C6EBDE139090BF4EDB1B (void);
extern void Path64_ToString_m2F985F26F27060EEC4EDC2B559FF26ADAFF75BA0 (void);
extern void Paths64__ctor_m9BE521B6DF86122D5A41B3FBCF3FEBDCAF138252 (void);
extern void Paths64__ctor_mC9DAA02B35F001703FA006BA5461EFEE4DAF2F26 (void);
extern void Paths64_ToString_m5B685CF1599202EF4B7DE2CFEDAFFD248AC818EE (void);
extern void InternalClipper_CrossProduct_m6AC2BB47608A0E67EF1F79CE971FCA7A33179F37 (void);
extern void InternalClipper_TriSign_m3CE544499A059873F8310DDFC66F140843CCB4AA (void);
extern void InternalClipper_MultiplyUInt64_mAE48809FE68EFDD0506D994D63E8ED438718E506 (void);
extern void InternalClipper_ProductsAreEqual_m2F49A1C1710FDCDE3B99CC1CC00D7DEED3CBDD0F (void);
extern void InternalClipper_IsCollinear_mD7C98F017F4AB31AFE2B535A323CC559848AF189 (void);
extern void InternalClipper_DotProduct_m6B7CE78CC20DC6A6ADC2AF3285BE3B285DBC5BB3 (void);
extern void InternalClipper_GetSegmentIntersectPt_m8291F319CDE2F4573B8BF3EA45DF5F352517D3A2 (void);
extern void InternalClipper_SegsIntersect_m5F0E67A663D7B7A0AE9D4C457318EAB7CE8248FD (void);
extern void InternalClipper_GetClosestPtOnSegment_m40AE078127347509967DA7AD973E26A9AB9A3655 (void);
extern void InternalClipper_PointInPolygon_m1C81F68906455BF0CB4692A591227CB538F34958 (void);
extern void InternalClipper__cctor_m83F8B0A6287D630EDCAE28DE14590BF589546D8A (void);
extern void Clipper_get_InvalidRect64_mD57A29C755F37751A335E4FBAA4F2092357C085C (void);
extern void Clipper_Union_mDE849841829EE888B4A5179CEE1D055EAC581677 (void);
extern void Clipper_Difference_m75DD0C7ABA188B24C61ED217D992F57B1C29B68C (void);
extern void Clipper_BooleanOp_mF67C7CF0A97F2F7AC0ABE2750D9262A9FE834688 (void);
extern void Clipper_MakePath_m1E865DBC0BEAE70EA916D0ABCB7464211CA232EE (void);
extern void Clipper_Sqr_m6CECF8B02923AF48730AADEC93F9DC7861DF548C (void);
extern void Clipper_PerpendicDistFromLineSqrd_mB04133770612AE43CEC37D4FCAF2ADEE7BB4E98B (void);
extern void Clipper_GetNext_m63E307385074CD2737C3441C1F9B66E0356A0224 (void);
extern void Clipper_GetPrior_m5974DCD51B26A62D8614D2419836E51BCAD83A96 (void);
extern void Clipper_SimplifyPath_mEACE93FAA0BAA624D925C9D1F7A22680266A5CCB (void);
extern void Clipper_SimplifyPaths_m45DE99F8819EE0DB71AD198A65044FEDE59C7AEE (void);
extern void Clipper__cctor_m8639AB0A068DBEB225A80BBAC1C9CBA3844184C1 (void);
extern void Vertex__ctor_mF07CB1242D7C9520E827399BE2D30A3D45D99D9F (void);
extern void LocalMinima__ctor_m9D966414658DD7F9C601017C612D389197E67245 (void);
extern void LocalMinima_op_Equality_mB6206019EDF51FD3DA54A38A47358E337F6122F7 (void);
extern void LocalMinima_Equals_mD513A7CA7E9C1A558AD11B1BE4BA97024E63D296 (void);
extern void LocalMinima_GetHashCode_mD7FF6ABFEECD4F8D3F879706EA0B6EA6BC12E3E8 (void);
extern void IntersectNode__ctor_mDEBDF90BD3CBD5CF96B52F1C41154CFB32B6C5B7 (void);
extern void LocMinSorter_Compare_m7D633616DEBCF6AA5FF59F1152F311D5A83CF1EC (void);
extern void OutPt__ctor_m617DF76F4D28137FDC4F9A40E5256C2092EF7FC6 (void);
extern void OutRec__ctor_m590BF07621691890E2E147624AA5736AE49E5170 (void);
extern void HorzSegment__ctor_m15366625D6D71D25EF5BB63D26DDCAAEBF6B0152 (void);
extern void HorzJoin__ctor_m1177906068DD413EEB15E55C246472954E992261 (void);
extern void Active__ctor_m1AE9AEF369125200BB71DC373414D1D58B22E96A (void);
extern void ClipperEngine_AddLocMin_mAD40DF5797F627BE98AB7116B4648383757F39E5 (void);
extern void ClipperEngine_AddPathsToVertexList_mF2A414AA2DE284C7E2AF45D207A598B7D03294B8 (void);
extern void ClipperBase_get_PreserveCollinear_m5F970B6B2916E2A75AE3B38027312E32F89F98A4 (void);
extern void ClipperBase_set_PreserveCollinear_m5B7A0EBF8BA10CD54E10C6DAA44B3857114FD2AD (void);
extern void ClipperBase_get_ReverseSolution_mDDAD5652A573150E8AC45C72BA28DE89BBB16F3B (void);
extern void ClipperBase__ctor_m15D6D8377BD80480BE51995EE53917CDB2DF3DB3 (void);
extern void ClipperBase_IsOdd_m38BD6FECDED7A5FFAB923A26479ECC54AE5DF680 (void);
extern void ClipperBase_IsHotEdge_mD4F4DFC6BEDE69577569BCE6E4132A918F01BA0D (void);
extern void ClipperBase_IsOpen_mD5EC2A0084CDD52047A87863B0BF213973DE67FF (void);
extern void ClipperBase_IsOpenEnd_m568426D908698902C2113B9336890D45318131A2 (void);
extern void ClipperBase_IsOpenEnd_mC6AAE7603894142CD5A6BF3188BE7E6EFE8FDA37 (void);
extern void ClipperBase_GetPrevHotEdge_m128B8C2FB6248E829C1FA5B2B1225690A76AB488 (void);
extern void ClipperBase_IsFront_m730A903FE911FEB565A8AC5AF3A6025D9ABB43C1 (void);
extern void ClipperBase_GetDx_mAE9B3EB6825F4D58E09CE4A6E311A7653C764D1A (void);
extern void ClipperBase_TopX_mFA50F2D25DEC6E477B44005A006DB66ACC735FDB (void);
extern void ClipperBase_IsHorizontal_mFADD1B7D3840627D10ADD04F2B14613FD3D077BC (void);
extern void ClipperBase_IsHeadingRightHorz_m3B37DFA4030BDB983E2B2FEE9E50E9F566E33294 (void);
extern void ClipperBase_IsHeadingLeftHorz_mD596EFEB454E7D2A142AC04F3EC65FF6A8FDE241 (void);
extern void ClipperBase_SwapActives_mCBDDA17E51A186DA6C53354FF6C338356C2EF753 (void);
extern void ClipperBase_GetPolyType_m8A4FCEBA4C6081705E856F08D0D41263837742E7 (void);
extern void ClipperBase_IsSamePolyType_m3A22471008251EFEF1C64D2A2459AD14C5E58B28 (void);
extern void ClipperBase_SetDx_m751EF0FB64A2C5B1B36DE63FFF7608A1199C7FDC (void);
extern void ClipperBase_NextVertex_mE02CA2235286088E2C4B86FE2A18F4DDE8ACFE75 (void);
extern void ClipperBase_PrevPrevVertex_mC4994FA26D97E537813157973B905F5AC64A1523 (void);
extern void ClipperBase_IsMaxima_mD3F711B0AF95507AB4C38475489958EFF3D02468 (void);
extern void ClipperBase_IsMaxima_m3F33654998B88112220B3BB1BC42525D6CD59348 (void);
extern void ClipperBase_GetMaximaPair_mC64F4BFDC8A9DBCA79185C53E444BE51E5793A25 (void);
extern void ClipperBase_GetCurrYMaximaVertex_Open_mA3651FBD5C3331DBD3904E1C57F5550598FC3FB2 (void);
extern void ClipperBase_GetCurrYMaximaVertex_mF4ABDFC342896420A6889D1BC5016B621A30C7F7 (void);
extern void ClipperBase_SetSides_m02728897B44491FD7AAF7CE7AEED2E769CBCE710 (void);
extern void ClipperBase_SwapOutrecs_m82DBD9C4BDF6996AD0F0C140A6730C5A0FE2B3C9 (void);
extern void ClipperBase_SetOwner_m2A608B0000D84A2FC6A8AE5282DB9933D6C3A8F2 (void);
extern void ClipperBase_Area_mA6F7EDB40731A50868F1FFE7A81363269D2EDA48 (void);
extern void ClipperBase_AreaTriangle_mEF7F6B1FE2F7AA4AF2E2C786BDDF9739600AB553 (void);
extern void ClipperBase_GetRealOutRec_m39CB69D4003F450B80E440C9F94EE6CB8D3A1AD0 (void);
extern void ClipperBase_UncoupleOutRec_m478D866A6DD19FC659B4787E96F78EB1089BCB7C (void);
extern void ClipperBase_OutrecIsAscending_m3408506C9D658BA30BB467464BD88D1F3896A4BE (void);
extern void ClipperBase_SwapFrontBackSides_mE1640FA42B2443D31BA549F208F4A54FD1E5074D (void);
extern void ClipperBase_EdgesAdjacentInAEL_mEC5985CB572064F202E05890D2CEA223FE542BC3 (void);
extern void ClipperBase_ClearSolutionOnly_m6859369FACB1360B9055A7108FE3222FABAB9081 (void);
extern void ClipperBase_Reset_m82317FE5CA5F803D2B6ABE08751F784C3EF25576 (void);
extern void ClipperBase_InsertScanline_m74520872F41DD67A6E09D6AA18FED1F8F9BB4D39 (void);
extern void ClipperBase_PopScanline_m1EFDF1739C40D8393E616BAC7E1F3B75FD61064B (void);
extern void ClipperBase_HasLocMinAtY_m229E54ADA66095BB96F6665E29D376B9B024FA29 (void);
extern void ClipperBase_PopLocalMinima_mE2F488A90D012973235159915B07C2BEC590138D (void);
extern void ClipperBase_AddPaths_m421C0F37D752F8AE32A9AC5602B7A296DB54E71D (void);
extern void ClipperBase_IsContributingClosed_m9CBDC26E963CD9110B66E96A95E59FD7EE21A950 (void);
extern void ClipperBase_IsContributingOpen_mE4A60C6FA08F8C1DDD320E64E2EA79563205BD8B (void);
extern void ClipperBase_SetWindCountForClosedPathEdge_mCA906DB9C1A298A1B47FB7B0AD6236FE59A91A59 (void);
extern void ClipperBase_SetWindCountForOpenPathEdge_m3D1520A1A20D75073582E5923CF64760ABCDD4F3 (void);
extern void ClipperBase_IsValidAelOrder_m73F3BDEDF521D5EC029A3EDE1A6627103F78DD08 (void);
extern void ClipperBase_InsertLeftEdge_mF5B55554825F3D1D41ABDDFE2F3771EC4841EE84 (void);
extern void ClipperBase_InsertRightEdge_m5062F6B687889372988A976BCDF208C490603D4C (void);
extern void ClipperBase_InsertLocalMinimaIntoAEL_mA4EFAE098C2D96E869280082729981555C522849 (void);
extern void ClipperBase_PushHorz_m0C1F6710F414121D75B71062F105328B93C1A4F3 (void);
extern void ClipperBase_PopHorz_mE2462EE12AE7DF1FAC03FB8E356A68F00E461C11 (void);
extern void ClipperBase_AddLocalMinPoly_m9CB7C13058FD6869D232B898202549CF1CD90836 (void);
extern void ClipperBase_AddLocalMaxPoly_m119ADB5864A3C3ADD75E87293BB53ED417CF6087 (void);
extern void ClipperBase_JoinOutrecPaths_mF7B4D52F384EE02BD77AB5726B732CF2851A7274 (void);
extern void ClipperBase_AddOutPt_m11D3E0C66A00C276F146BC90F3759373A5B3A37D (void);
extern void ClipperBase_NewOutRec_mC59F8ABF7CB365511D9DCB7277C4C3802DA7B654 (void);
extern void ClipperBase_StartOpenPath_mECCD3DFDB216B4AD8378E76D4FE8C4165BE9DA68 (void);
extern void ClipperBase_UpdateEdgeIntoAEL_m4167A630D3D7893A2A66B120E9560CE7312D18B7 (void);
extern void ClipperBase_FindEdgeWithMatchingLocMin_m946291605700DE2B64D336784F46CF901DF99993 (void);
extern void ClipperBase_IntersectEdges_mE2C3BED7B339B2C957E78A75967D55FE9329D4C4 (void);
extern void ClipperBase_DeleteFromAEL_m78AC171B0BDB4210E81C0377A8FCD82A5073ED03 (void);
extern void ClipperBase_AdjustCurrXAndCopyToSEL_mD4D7E6DAEA21FAD80867B1A8D7B292590EC6CFA6 (void);
extern void ClipperBase_ExecuteInternal_m86394436C5E3BBE13FA3F25D41D847B94B3FFEED (void);
extern void ClipperBase_DoIntersections_m5854158BECDD490E66CBCA96804193B756F3B438 (void);
extern void ClipperBase_DisposeIntersectNodes_m0D460ADCABFFFF90BF98A9EF903A5DF566C50081 (void);
extern void ClipperBase_AddNewIntersectNode_m0C2563B9AC0494DAF8830F5CB842B0C895536E38 (void);
extern void ClipperBase_ExtractFromSEL_m22B4CF63DD8696EFF025CD84A9118496AEE932DD (void);
extern void ClipperBase_Insert1Before2InSEL_mC1200100E69953935698152F53E92921E9B4B043 (void);
extern void ClipperBase_BuildIntersectList_m1C8E8667DC7806FDBBC465525BE83FA57A30BE30 (void);
extern void ClipperBase_ProcessIntersectList_m7E506618DA25AC4275821CF369D6C83B3CC61A1E (void);
extern void ClipperBase_SwapPositionsInAEL_m4478945310C4A754BA21E676BED9F6F09A9BFB9B (void);
extern void ClipperBase_ResetHorzDirection_mC637E3325459D32821AB892B2CCA77B67AA6B0DA (void);
extern void ClipperBase_TrimHorz_mA28C81F6472841B06F98C0A636280CDEC5A1CC8A (void);
extern void ClipperBase_AddToHorzSegList_mF43EA27D75EB68E0A8AF603A42E34C8301B3A38B (void);
extern void ClipperBase_GetLastOp_m10F26753947BF040B9A4510081010961EAF30368 (void);
extern void ClipperBase_DoHorizontal_m0130445CCC5E526C6E6558FAD8CA9F886F254057 (void);
extern void ClipperBase_DoTopOfScanbeam_m7604358A61EF1B44BC93CBE35B9010BAA1D6F529 (void);
extern void ClipperBase_DoMaxima_m1D94F88BDB0983BB1178136E3A1C6456F20A9FD7 (void);
extern void ClipperBase_IsJoined_mE08E9195C39CCA3AA34188B077338B68AF227D92 (void);
extern void ClipperBase_Split_mBAFC85DD92805EBFBEE6DC872059E5DF961BF378 (void);
extern void ClipperBase_CheckJoinLeft_mE38C32EC01A85576FC80B9D2407DF270FD28B6A3 (void);
extern void ClipperBase_CheckJoinRight_m6156FEF6A544B92EF1AEB4822D4E91EC90CDA1FE (void);
extern void ClipperBase_FixOutRecPts_mE9A4EDFF0CF696106D8B78E4DF56EBA3F34AB4B5 (void);
extern void ClipperBase_SetHorzSegHeadingForward_mA4C6AB7E5F95ABDAC7D5BD37D06BB7135427B98B (void);
extern void ClipperBase_UpdateHorzSegment_mFE1482F0110BFFDDFE2721B24D7FDFC8064B12F8 (void);
extern void ClipperBase_DuplicateOp_m35C926545813AF5A8501475BA61E4B1E3A279258 (void);
extern void ClipperBase_HorzSegSort_mB885DBC0126BBF98C1D38471FD87D3AD55C453CD (void);
extern void ClipperBase_ConvertHorzSegsToJoins_m8E763BA5A68250B97F4876ED4517E4B2DA3396F7 (void);
extern void ClipperBase_GetCleanPath_m8E1D21BAF2BC36A1447357E81502EE4D45D41989 (void);
extern void ClipperBase_PointInOpPolygon_mB5E96C110F44509D6959F306C409A141CC624FB1 (void);
extern void ClipperBase_Path1InsidePath2_mDE305675D500463CD2D5C57B96175B55F842559B (void);
extern void ClipperBase_MoveSplits_m0A6EBE5DF0216762DF2FCDF75AA805B407584E85 (void);
extern void ClipperBase_ProcessHorzJoins_m878F0E33009FF1C030CAA972EE4C93C7761C57CE (void);
extern void ClipperBase_PtsReallyClose_m24BDD8EF15F89315057742E3990B84DA20673A9C (void);
extern void ClipperBase_IsVerySmallTriangle_m8F73EA520E66ED507309F09D1C49D2BEE7151E12 (void);
extern void ClipperBase_IsValidClosedPath_mA092F73299965CC603DB61F49EB73608AF954A31 (void);
extern void ClipperBase_DisposeOutPt_m2EC81C0E82E34CB5A1645247448AC48FF87DA4BD (void);
extern void ClipperBase_CleanCollinear_m0706C771BDA754B7A16C9F7BE86A5B8C109CC1E2 (void);
extern void ClipperBase_DoSplitOp_mBB11429FFA2403B5FACBEE3B2BC9B70CFF28769D (void);
extern void ClipperBase_FixSelfIntersects_mBA2B12E2AA04513500151E68423978970DE30EDA (void);
extern void ClipperBase_BuildPath_m64215AE747B71A77EB07E99AD14C7D8A24B86DA5 (void);
extern void ClipperBase_BuildPaths_m28DA9B8FED80ECB90A6AE8FC40F513C4F87231DB (void);
extern void ClipperBase_GetBounds_m627795235CED6D474EB99E3916E4BF6E9F414106 (void);
extern void IntersectListSort_Compare_mCFC760BDE68EA03CBD5E38FB6FF804F6DC5B9C29 (void);
extern void Clipper64_AddPaths_mA552C0A5BD96AC62EC60011F1F19C6C37BA3F329 (void);
extern void Clipper64_Execute_m5373250BAF3432661AA9303D33F25B6A946B4C20 (void);
extern void Clipper64_Execute_m846CC6E481B9ABB54B6D9077CBD1B7362293AF2D (void);
extern void Clipper64__ctor_m65DAE4888DB74F297F4BAF4FF3A6AA408B4AE37B (void);
extern void HashCode_GenerateGlobalSeed_mBF2AA725D3D14055A8A9FE0D2F98FA2E3328147C (void);
extern void HashCode_QueueRound_mA893A73573B049BB6D6B5B4DE33C89A5D966FF24 (void);
extern void HashCode_MixEmptyState_mEC84035C59982622516EDAC6C6C917BEECDB65A1 (void);
extern void HashCode_MixFinal_mF645FE1749BE17BB1D42ECE4FDDED7D500EB6925 (void);
extern void HashCode_RotateLeft_mA03897AE9D93A7579863ACCA6BC478368D4E364E (void);
extern void HashCode_GetHashCode_mACA5B473CB9B28121630CC2F3D3D98D1E52DC422 (void);
extern void HashCode_Equals_m241C7AC17DE4A9EB8AADCE626BCB019CCBAA4FDB (void);
extern void HashCode__cctor_mC183C7F8C43F543B8C319B848DC330BF7B13794B (void);
static Il2CppMethodPointer s_methodPointers[178] = 
{
	EmbeddedAttribute__ctor_mC28DF84064B558747D4183D3B2A79E1774796A48,
	NullableAttribute__ctor_m1F90AAE7475A2C0B7FC4B75E6B9E3A24F2C7CF34,
	NullableAttribute__ctor_mC25AD0826D15C01627A059DDC504F38951B7A8AD,
	NullableContextAttribute__ctor_m820C5562C9E012B01BCEED6F85AF73ACB1539A16,
	Point64__ctor_mF9971046CBA488FECD3C0B5F177040AF3754B4D7,
	Point64__ctor_m17D797B27CC0BEFD2D843D7D81948347FEC6C7B4,
	Point64_op_Equality_mFB2C15957F0D3D7254188AA534820B646BF3BD12,
	Point64_op_Inequality_m04FD86A71A0E4079ABA6E09C29B0FC940B209FD2,
	Point64_ToString_m10DAF5E06F218D9515B89677210B51D10427D1F7,
	Point64_Equals_m3B2E07852DBE1C5CA56A1B56ACA70FD6AE61CF08,
	Point64_GetHashCode_m9FA878AE2C6E7AFA451DEB41AE64EAE115A68625,
	Rect64__ctor_m995D41FC85D9AE84AB1E471AF335D9C2374FAEF3,
	Rect64_MidPoint_mE8279166C986661FF2BCA74CE3446431929C5718,
	RectD__ctor_mCCCE082BE4AC0560A49E7BBFD841156CAF042BF0,
	Path64__ctor_m25A1D1EC69A1E1D6ED4854A62FC93A94CD1C7CFE,
	Path64__ctor_mBA27E245E819ADF18DB1C6EBDE139090BF4EDB1B,
	Path64_ToString_m2F985F26F27060EEC4EDC2B559FF26ADAFF75BA0,
	Paths64__ctor_m9BE521B6DF86122D5A41B3FBCF3FEBDCAF138252,
	Paths64__ctor_mC9DAA02B35F001703FA006BA5461EFEE4DAF2F26,
	Paths64_ToString_m5B685CF1599202EF4B7DE2CFEDAFFD248AC818EE,
	InternalClipper_CrossProduct_m6AC2BB47608A0E67EF1F79CE971FCA7A33179F37,
	InternalClipper_TriSign_m3CE544499A059873F8310DDFC66F140843CCB4AA,
	InternalClipper_MultiplyUInt64_mAE48809FE68EFDD0506D994D63E8ED438718E506,
	InternalClipper_ProductsAreEqual_m2F49A1C1710FDCDE3B99CC1CC00D7DEED3CBDD0F,
	InternalClipper_IsCollinear_mD7C98F017F4AB31AFE2B535A323CC559848AF189,
	InternalClipper_DotProduct_m6B7CE78CC20DC6A6ADC2AF3285BE3B285DBC5BB3,
	InternalClipper_GetSegmentIntersectPt_m8291F319CDE2F4573B8BF3EA45DF5F352517D3A2,
	InternalClipper_SegsIntersect_m5F0E67A663D7B7A0AE9D4C457318EAB7CE8248FD,
	InternalClipper_GetClosestPtOnSegment_m40AE078127347509967DA7AD973E26A9AB9A3655,
	InternalClipper_PointInPolygon_m1C81F68906455BF0CB4692A591227CB538F34958,
	InternalClipper__cctor_m83F8B0A6287D630EDCAE28DE14590BF589546D8A,
	Clipper_get_InvalidRect64_mD57A29C755F37751A335E4FBAA4F2092357C085C,
	Clipper_Union_mDE849841829EE888B4A5179CEE1D055EAC581677,
	Clipper_Difference_m75DD0C7ABA188B24C61ED217D992F57B1C29B68C,
	Clipper_BooleanOp_mF67C7CF0A97F2F7AC0ABE2750D9262A9FE834688,
	Clipper_MakePath_m1E865DBC0BEAE70EA916D0ABCB7464211CA232EE,
	Clipper_Sqr_m6CECF8B02923AF48730AADEC93F9DC7861DF548C,
	Clipper_PerpendicDistFromLineSqrd_mB04133770612AE43CEC37D4FCAF2ADEE7BB4E98B,
	Clipper_GetNext_m63E307385074CD2737C3441C1F9B66E0356A0224,
	Clipper_GetPrior_m5974DCD51B26A62D8614D2419836E51BCAD83A96,
	Clipper_SimplifyPath_mEACE93FAA0BAA624D925C9D1F7A22680266A5CCB,
	Clipper_SimplifyPaths_m45DE99F8819EE0DB71AD198A65044FEDE59C7AEE,
	Clipper__cctor_m8639AB0A068DBEB225A80BBAC1C9CBA3844184C1,
	Vertex__ctor_mF07CB1242D7C9520E827399BE2D30A3D45D99D9F,
	LocalMinima__ctor_m9D966414658DD7F9C601017C612D389197E67245,
	LocalMinima_op_Equality_mB6206019EDF51FD3DA54A38A47358E337F6122F7,
	LocalMinima_Equals_mD513A7CA7E9C1A558AD11B1BE4BA97024E63D296,
	LocalMinima_GetHashCode_mD7FF6ABFEECD4F8D3F879706EA0B6EA6BC12E3E8,
	IntersectNode__ctor_mDEBDF90BD3CBD5CF96B52F1C41154CFB32B6C5B7,
	LocMinSorter_Compare_m7D633616DEBCF6AA5FF59F1152F311D5A83CF1EC,
	OutPt__ctor_m617DF76F4D28137FDC4F9A40E5256C2092EF7FC6,
	OutRec__ctor_m590BF07621691890E2E147624AA5736AE49E5170,
	HorzSegment__ctor_m15366625D6D71D25EF5BB63D26DDCAAEBF6B0152,
	HorzJoin__ctor_m1177906068DD413EEB15E55C246472954E992261,
	Active__ctor_m1AE9AEF369125200BB71DC373414D1D58B22E96A,
	ClipperEngine_AddLocMin_mAD40DF5797F627BE98AB7116B4648383757F39E5,
	NULL,
	ClipperEngine_AddPathsToVertexList_mF2A414AA2DE284C7E2AF45D207A598B7D03294B8,
	ClipperBase_get_PreserveCollinear_m5F970B6B2916E2A75AE3B38027312E32F89F98A4,
	ClipperBase_set_PreserveCollinear_m5B7A0EBF8BA10CD54E10C6DAA44B3857114FD2AD,
	ClipperBase_get_ReverseSolution_mDDAD5652A573150E8AC45C72BA28DE89BBB16F3B,
	ClipperBase__ctor_m15D6D8377BD80480BE51995EE53917CDB2DF3DB3,
	ClipperBase_IsOdd_m38BD6FECDED7A5FFAB923A26479ECC54AE5DF680,
	ClipperBase_IsHotEdge_mD4F4DFC6BEDE69577569BCE6E4132A918F01BA0D,
	ClipperBase_IsOpen_mD5EC2A0084CDD52047A87863B0BF213973DE67FF,
	ClipperBase_IsOpenEnd_m568426D908698902C2113B9336890D45318131A2,
	ClipperBase_IsOpenEnd_mC6AAE7603894142CD5A6BF3188BE7E6EFE8FDA37,
	ClipperBase_GetPrevHotEdge_m128B8C2FB6248E829C1FA5B2B1225690A76AB488,
	ClipperBase_IsFront_m730A903FE911FEB565A8AC5AF3A6025D9ABB43C1,
	ClipperBase_GetDx_mAE9B3EB6825F4D58E09CE4A6E311A7653C764D1A,
	ClipperBase_TopX_mFA50F2D25DEC6E477B44005A006DB66ACC735FDB,
	ClipperBase_IsHorizontal_mFADD1B7D3840627D10ADD04F2B14613FD3D077BC,
	ClipperBase_IsHeadingRightHorz_m3B37DFA4030BDB983E2B2FEE9E50E9F566E33294,
	ClipperBase_IsHeadingLeftHorz_mD596EFEB454E7D2A142AC04F3EC65FF6A8FDE241,
	ClipperBase_SwapActives_mCBDDA17E51A186DA6C53354FF6C338356C2EF753,
	ClipperBase_GetPolyType_m8A4FCEBA4C6081705E856F08D0D41263837742E7,
	ClipperBase_IsSamePolyType_m3A22471008251EFEF1C64D2A2459AD14C5E58B28,
	ClipperBase_SetDx_m751EF0FB64A2C5B1B36DE63FFF7608A1199C7FDC,
	ClipperBase_NextVertex_mE02CA2235286088E2C4B86FE2A18F4DDE8ACFE75,
	ClipperBase_PrevPrevVertex_mC4994FA26D97E537813157973B905F5AC64A1523,
	ClipperBase_IsMaxima_mD3F711B0AF95507AB4C38475489958EFF3D02468,
	ClipperBase_IsMaxima_m3F33654998B88112220B3BB1BC42525D6CD59348,
	ClipperBase_GetMaximaPair_mC64F4BFDC8A9DBCA79185C53E444BE51E5793A25,
	ClipperBase_GetCurrYMaximaVertex_Open_mA3651FBD5C3331DBD3904E1C57F5550598FC3FB2,
	ClipperBase_GetCurrYMaximaVertex_mF4ABDFC342896420A6889D1BC5016B621A30C7F7,
	ClipperBase_SetSides_m02728897B44491FD7AAF7CE7AEED2E769CBCE710,
	ClipperBase_SwapOutrecs_m82DBD9C4BDF6996AD0F0C140A6730C5A0FE2B3C9,
	ClipperBase_SetOwner_m2A608B0000D84A2FC6A8AE5282DB9933D6C3A8F2,
	ClipperBase_Area_mA6F7EDB40731A50868F1FFE7A81363269D2EDA48,
	ClipperBase_AreaTriangle_mEF7F6B1FE2F7AA4AF2E2C786BDDF9739600AB553,
	ClipperBase_GetRealOutRec_m39CB69D4003F450B80E440C9F94EE6CB8D3A1AD0,
	ClipperBase_UncoupleOutRec_m478D866A6DD19FC659B4787E96F78EB1089BCB7C,
	ClipperBase_OutrecIsAscending_m3408506C9D658BA30BB467464BD88D1F3896A4BE,
	ClipperBase_SwapFrontBackSides_mE1640FA42B2443D31BA549F208F4A54FD1E5074D,
	ClipperBase_EdgesAdjacentInAEL_mEC5985CB572064F202E05890D2CEA223FE542BC3,
	ClipperBase_ClearSolutionOnly_m6859369FACB1360B9055A7108FE3222FABAB9081,
	ClipperBase_Reset_m82317FE5CA5F803D2B6ABE08751F784C3EF25576,
	ClipperBase_InsertScanline_m74520872F41DD67A6E09D6AA18FED1F8F9BB4D39,
	ClipperBase_PopScanline_m1EFDF1739C40D8393E616BAC7E1F3B75FD61064B,
	ClipperBase_HasLocMinAtY_m229E54ADA66095BB96F6665E29D376B9B024FA29,
	ClipperBase_PopLocalMinima_mE2F488A90D012973235159915B07C2BEC590138D,
	ClipperBase_AddPaths_m421C0F37D752F8AE32A9AC5602B7A296DB54E71D,
	ClipperBase_IsContributingClosed_m9CBDC26E963CD9110B66E96A95E59FD7EE21A950,
	ClipperBase_IsContributingOpen_mE4A60C6FA08F8C1DDD320E64E2EA79563205BD8B,
	ClipperBase_SetWindCountForClosedPathEdge_mCA906DB9C1A298A1B47FB7B0AD6236FE59A91A59,
	ClipperBase_SetWindCountForOpenPathEdge_m3D1520A1A20D75073582E5923CF64760ABCDD4F3,
	ClipperBase_IsValidAelOrder_m73F3BDEDF521D5EC029A3EDE1A6627103F78DD08,
	ClipperBase_InsertLeftEdge_mF5B55554825F3D1D41ABDDFE2F3771EC4841EE84,
	ClipperBase_InsertRightEdge_m5062F6B687889372988A976BCDF208C490603D4C,
	ClipperBase_InsertLocalMinimaIntoAEL_mA4EFAE098C2D96E869280082729981555C522849,
	ClipperBase_PushHorz_m0C1F6710F414121D75B71062F105328B93C1A4F3,
	ClipperBase_PopHorz_mE2462EE12AE7DF1FAC03FB8E356A68F00E461C11,
	ClipperBase_AddLocalMinPoly_m9CB7C13058FD6869D232B898202549CF1CD90836,
	ClipperBase_AddLocalMaxPoly_m119ADB5864A3C3ADD75E87293BB53ED417CF6087,
	ClipperBase_JoinOutrecPaths_mF7B4D52F384EE02BD77AB5726B732CF2851A7274,
	ClipperBase_AddOutPt_m11D3E0C66A00C276F146BC90F3759373A5B3A37D,
	ClipperBase_NewOutRec_mC59F8ABF7CB365511D9DCB7277C4C3802DA7B654,
	ClipperBase_StartOpenPath_mECCD3DFDB216B4AD8378E76D4FE8C4165BE9DA68,
	ClipperBase_UpdateEdgeIntoAEL_m4167A630D3D7893A2A66B120E9560CE7312D18B7,
	ClipperBase_FindEdgeWithMatchingLocMin_m946291605700DE2B64D336784F46CF901DF99993,
	ClipperBase_IntersectEdges_mE2C3BED7B339B2C957E78A75967D55FE9329D4C4,
	ClipperBase_DeleteFromAEL_m78AC171B0BDB4210E81C0377A8FCD82A5073ED03,
	ClipperBase_AdjustCurrXAndCopyToSEL_mD4D7E6DAEA21FAD80867B1A8D7B292590EC6CFA6,
	ClipperBase_ExecuteInternal_m86394436C5E3BBE13FA3F25D41D847B94B3FFEED,
	ClipperBase_DoIntersections_m5854158BECDD490E66CBCA96804193B756F3B438,
	ClipperBase_DisposeIntersectNodes_m0D460ADCABFFFF90BF98A9EF903A5DF566C50081,
	ClipperBase_AddNewIntersectNode_m0C2563B9AC0494DAF8830F5CB842B0C895536E38,
	ClipperBase_ExtractFromSEL_m22B4CF63DD8696EFF025CD84A9118496AEE932DD,
	ClipperBase_Insert1Before2InSEL_mC1200100E69953935698152F53E92921E9B4B043,
	ClipperBase_BuildIntersectList_m1C8E8667DC7806FDBBC465525BE83FA57A30BE30,
	ClipperBase_ProcessIntersectList_m7E506618DA25AC4275821CF369D6C83B3CC61A1E,
	ClipperBase_SwapPositionsInAEL_m4478945310C4A754BA21E676BED9F6F09A9BFB9B,
	ClipperBase_ResetHorzDirection_mC637E3325459D32821AB892B2CCA77B67AA6B0DA,
	ClipperBase_TrimHorz_mA28C81F6472841B06F98C0A636280CDEC5A1CC8A,
	ClipperBase_AddToHorzSegList_mF43EA27D75EB68E0A8AF603A42E34C8301B3A38B,
	ClipperBase_GetLastOp_m10F26753947BF040B9A4510081010961EAF30368,
	ClipperBase_DoHorizontal_m0130445CCC5E526C6E6558FAD8CA9F886F254057,
	ClipperBase_DoTopOfScanbeam_m7604358A61EF1B44BC93CBE35B9010BAA1D6F529,
	ClipperBase_DoMaxima_m1D94F88BDB0983BB1178136E3A1C6456F20A9FD7,
	ClipperBase_IsJoined_mE08E9195C39CCA3AA34188B077338B68AF227D92,
	ClipperBase_Split_mBAFC85DD92805EBFBEE6DC872059E5DF961BF378,
	ClipperBase_CheckJoinLeft_mE38C32EC01A85576FC80B9D2407DF270FD28B6A3,
	ClipperBase_CheckJoinRight_m6156FEF6A544B92EF1AEB4822D4E91EC90CDA1FE,
	ClipperBase_FixOutRecPts_mE9A4EDFF0CF696106D8B78E4DF56EBA3F34AB4B5,
	ClipperBase_SetHorzSegHeadingForward_mA4C6AB7E5F95ABDAC7D5BD37D06BB7135427B98B,
	ClipperBase_UpdateHorzSegment_mFE1482F0110BFFDDFE2721B24D7FDFC8064B12F8,
	ClipperBase_DuplicateOp_m35C926545813AF5A8501475BA61E4B1E3A279258,
	ClipperBase_HorzSegSort_mB885DBC0126BBF98C1D38471FD87D3AD55C453CD,
	ClipperBase_ConvertHorzSegsToJoins_m8E763BA5A68250B97F4876ED4517E4B2DA3396F7,
	ClipperBase_GetCleanPath_m8E1D21BAF2BC36A1447357E81502EE4D45D41989,
	ClipperBase_PointInOpPolygon_mB5E96C110F44509D6959F306C409A141CC624FB1,
	ClipperBase_Path1InsidePath2_mDE305675D500463CD2D5C57B96175B55F842559B,
	ClipperBase_MoveSplits_m0A6EBE5DF0216762DF2FCDF75AA805B407584E85,
	ClipperBase_ProcessHorzJoins_m878F0E33009FF1C030CAA972EE4C93C7761C57CE,
	ClipperBase_PtsReallyClose_m24BDD8EF15F89315057742E3990B84DA20673A9C,
	ClipperBase_IsVerySmallTriangle_m8F73EA520E66ED507309F09D1C49D2BEE7151E12,
	ClipperBase_IsValidClosedPath_mA092F73299965CC603DB61F49EB73608AF954A31,
	ClipperBase_DisposeOutPt_m2EC81C0E82E34CB5A1645247448AC48FF87DA4BD,
	ClipperBase_CleanCollinear_m0706C771BDA754B7A16C9F7BE86A5B8C109CC1E2,
	ClipperBase_DoSplitOp_mBB11429FFA2403B5FACBEE3B2BC9B70CFF28769D,
	ClipperBase_FixSelfIntersects_mBA2B12E2AA04513500151E68423978970DE30EDA,
	ClipperBase_BuildPath_m64215AE747B71A77EB07E99AD14C7D8A24B86DA5,
	ClipperBase_BuildPaths_m28DA9B8FED80ECB90A6AE8FC40F513C4F87231DB,
	ClipperBase_GetBounds_m627795235CED6D474EB99E3916E4BF6E9F414106,
	IntersectListSort_Compare_mCFC760BDE68EA03CBD5E38FB6FF804F6DC5B9C29,
	Clipper64_AddPaths_mA552C0A5BD96AC62EC60011F1F19C6C37BA3F329,
	Clipper64_Execute_m5373250BAF3432661AA9303D33F25B6A946B4C20,
	Clipper64_Execute_m846CC6E481B9ABB54B6D9077CBD1B7362293AF2D,
	Clipper64__ctor_m65DAE4888DB74F297F4BAF4FF3A6AA408B4AE37B,
	HashCode_GenerateGlobalSeed_mBF2AA725D3D14055A8A9FE0D2F98FA2E3328147C,
	NULL,
	HashCode_QueueRound_mA893A73573B049BB6D6B5B4DE33C89A5D966FF24,
	HashCode_MixEmptyState_mEC84035C59982622516EDAC6C6C917BEECDB65A1,
	HashCode_MixFinal_mF645FE1749BE17BB1D42ECE4FDDED7D500EB6925,
	HashCode_RotateLeft_mA03897AE9D93A7579863ACCA6BC478368D4E364E,
	HashCode_GetHashCode_mACA5B473CB9B28121630CC2F3D3D98D1E52DC422,
	HashCode_Equals_m241C7AC17DE4A9EB8AADCE626BCB019CCBAA4FDB,
	HashCode__cctor_mC183C7F8C43F543B8C319B848DC330BF7B13794B,
};
extern void Point64__ctor_mF9971046CBA488FECD3C0B5F177040AF3754B4D7_AdjustorThunk (void);
extern void Point64__ctor_m17D797B27CC0BEFD2D843D7D81948347FEC6C7B4_AdjustorThunk (void);
extern void Point64_ToString_m10DAF5E06F218D9515B89677210B51D10427D1F7_AdjustorThunk (void);
extern void Point64_Equals_m3B2E07852DBE1C5CA56A1B56ACA70FD6AE61CF08_AdjustorThunk (void);
extern void Point64_GetHashCode_m9FA878AE2C6E7AFA451DEB41AE64EAE115A68625_AdjustorThunk (void);
extern void Rect64__ctor_m995D41FC85D9AE84AB1E471AF335D9C2374FAEF3_AdjustorThunk (void);
extern void Rect64_MidPoint_mE8279166C986661FF2BCA74CE3446431929C5718_AdjustorThunk (void);
extern void RectD__ctor_mCCCE082BE4AC0560A49E7BBFD841156CAF042BF0_AdjustorThunk (void);
extern void LocalMinima__ctor_m9D966414658DD7F9C601017C612D389197E67245_AdjustorThunk (void);
extern void LocalMinima_Equals_mD513A7CA7E9C1A558AD11B1BE4BA97024E63D296_AdjustorThunk (void);
extern void LocalMinima_GetHashCode_mD7FF6ABFEECD4F8D3F879706EA0B6EA6BC12E3E8_AdjustorThunk (void);
extern void IntersectNode__ctor_mDEBDF90BD3CBD5CF96B52F1C41154CFB32B6C5B7_AdjustorThunk (void);
extern void LocMinSorter_Compare_m7D633616DEBCF6AA5FF59F1152F311D5A83CF1EC_AdjustorThunk (void);
extern void IntersectListSort_Compare_mCFC760BDE68EA03CBD5E38FB6FF804F6DC5B9C29_AdjustorThunk (void);
extern void HashCode_GetHashCode_mACA5B473CB9B28121630CC2F3D3D98D1E52DC422_AdjustorThunk (void);
extern void HashCode_Equals_m241C7AC17DE4A9EB8AADCE626BCB019CCBAA4FDB_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[16] = 
{
	{ 0x06000005, Point64__ctor_mF9971046CBA488FECD3C0B5F177040AF3754B4D7_AdjustorThunk },
	{ 0x06000006, Point64__ctor_m17D797B27CC0BEFD2D843D7D81948347FEC6C7B4_AdjustorThunk },
	{ 0x06000009, Point64_ToString_m10DAF5E06F218D9515B89677210B51D10427D1F7_AdjustorThunk },
	{ 0x0600000A, Point64_Equals_m3B2E07852DBE1C5CA56A1B56ACA70FD6AE61CF08_AdjustorThunk },
	{ 0x0600000B, Point64_GetHashCode_m9FA878AE2C6E7AFA451DEB41AE64EAE115A68625_AdjustorThunk },
	{ 0x0600000C, Rect64__ctor_m995D41FC85D9AE84AB1E471AF335D9C2374FAEF3_AdjustorThunk },
	{ 0x0600000D, Rect64_MidPoint_mE8279166C986661FF2BCA74CE3446431929C5718_AdjustorThunk },
	{ 0x0600000E, RectD__ctor_mCCCE082BE4AC0560A49E7BBFD841156CAF042BF0_AdjustorThunk },
	{ 0x0600002D, LocalMinima__ctor_m9D966414658DD7F9C601017C612D389197E67245_AdjustorThunk },
	{ 0x0600002F, LocalMinima_Equals_mD513A7CA7E9C1A558AD11B1BE4BA97024E63D296_AdjustorThunk },
	{ 0x06000030, LocalMinima_GetHashCode_mD7FF6ABFEECD4F8D3F879706EA0B6EA6BC12E3E8_AdjustorThunk },
	{ 0x06000031, IntersectNode__ctor_mDEBDF90BD3CBD5CF96B52F1C41154CFB32B6C5B7_AdjustorThunk },
	{ 0x06000032, LocMinSorter_Compare_m7D633616DEBCF6AA5FF59F1152F311D5A83CF1EC_AdjustorThunk },
	{ 0x060000A5, IntersectListSort_Compare_mCFC760BDE68EA03CBD5E38FB6FF804F6DC5B9C29_AdjustorThunk },
	{ 0x060000B0, HashCode_GetHashCode_mACA5B473CB9B28121630CC2F3D3D98D1E52DC422_AdjustorThunk },
	{ 0x060000B1, HashCode_Equals_m241C7AC17DE4A9EB8AADCE626BCB019CCBAA4FDB_AdjustorThunk },
};
static const int32_t s_InvokerIndices[178] = 
{
	13298,
	10442,
	10682,
	10442,
	5624,
	4750,
	17494,
	17494,
	13052,
	7736,
	12996,
	10442,
	13066,
	10442,
	13298,
	10629,
	13052,
	13298,
	10629,
	13052,
	16094,
	20208,
	18944,
	14903,
	16045,
	16094,
	14104,
	14105,
	16474,
	17850,
	21355,
	21293,
	18000,
	16439,
	15437,
	20515,
	19989,
	16094,
	16182,
	16182,
	16428,
	16428,
	21355,
	2831,
	2732,
	17463,
	7736,
	12996,
	2832,
	4136,
	5741,
	13298,
	10682,
	5688,
	13298,
	15636,
	0,
	14784,
	12815,
	10442,
	12815,
	13298,
	19885,
	19891,
	19891,
	19891,
	19891,
	20515,
	19891,
	17650,
	17890,
	19891,
	19891,
	19891,
	18502,
	20211,
	17478,
	20847,
	20515,
	20515,
	19891,
	19891,
	20515,
	20515,
	20515,
	16904,
	18814,
	18814,
	19997,
	16094,
	20515,
	20847,
	19891,
	20847,
	19888,
	13298,
	13298,
	10630,
	7532,
	7686,
	13028,
	2732,
	7736,
	7736,
	10682,
	10682,
	17478,
	10682,
	18814,
	10630,
	10682,
	7532,
	1668,
	2410,
	18814,
	18007,
	13052,
	4490,
	10682,
	20515,
	2781,
	10682,
	10630,
	5266,
	10630,
	13298,
	2775,
	20515,
	18814,
	7686,
	13298,
	5688,
	14921,
	18795,
	10682,
	20515,
	10682,
	10630,
	9272,
	19891,
	5691,
	2797,
	2797,
	20847,
	16033,
	19891,
	17991,
	17842,
	13298,
	20515,
	17850,
	17478,
	18814,
	13298,
	17494,
	19891,
	19891,
	20515,
	10682,
	5688,
	10682,
	14913,
	3500,
	20563,
	4129,
	2732,
	989,
	2128,
	13298,
	21347,
	0,
	18237,
	21347,
	20748,
	18235,
	12996,
	7736,
	21355,
};
static const Il2CppTokenRangePair s_rgctxIndices[2] = 
{
	{ 0x06000039, { 0, 3 } },
	{ 0x060000AB, { 3, 4 } },
};
extern const uint32_t g_rgctx_List_1_tC4D9AFC71A264946403FBFFF382BE8119AADDF29;
extern const uint32_t g_rgctx_List_1_get_Capacity_m95723AEE2367A9CA150BB7945752B39FDC69A3ED;
extern const uint32_t g_rgctx_List_1_set_Capacity_m2760EE031F62C1C1E2BC6012E3DF3BEB47EA7AB2;
extern const uint32_t g_rgctx_T1_t8C5698DF13E2B307AC51C1CE272FAEF4D790356E;
extern const Il2CppRGCTXConstrainedData g_rgctx_T1_t8C5698DF13E2B307AC51C1CE272FAEF4D790356E_Object_GetHashCode_m372C5A7AB16CAC13307C11C4256D706CE57E090C;
extern const uint32_t g_rgctx_T2_t65176B060A4F711FF4C70E67AB55D476A26C2B35;
extern const Il2CppRGCTXConstrainedData g_rgctx_T2_t65176B060A4F711FF4C70E67AB55D476A26C2B35_Object_GetHashCode_m372C5A7AB16CAC13307C11C4256D706CE57E090C;
static const Il2CppRGCTXDefinition s_rgctxValues[7] = 
{
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_List_1_tC4D9AFC71A264946403FBFFF382BE8119AADDF29 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_get_Capacity_m95723AEE2367A9CA150BB7945752B39FDC69A3ED },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_set_Capacity_m2760EE031F62C1C1E2BC6012E3DF3BEB47EA7AB2 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T1_t8C5698DF13E2B307AC51C1CE272FAEF4D790356E },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T1_t8C5698DF13E2B307AC51C1CE272FAEF4D790356E_Object_GetHashCode_m372C5A7AB16CAC13307C11C4256D706CE57E090C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T2_t65176B060A4F711FF4C70E67AB55D476A26C2B35 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_T2_t65176B060A4F711FF4C70E67AB55D476A26C2B35_Object_GetHashCode_m372C5A7AB16CAC13307C11C4256D706CE57E090C },
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_Clipper2Lib_CodeGenModule;
const Il2CppCodeGenModule g_Clipper2Lib_CodeGenModule = 
{
	"Clipper2Lib.dll",
	178,
	s_methodPointers,
	16,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	2,
	s_rgctxIndices,
	7,
	s_rgctxValues,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};

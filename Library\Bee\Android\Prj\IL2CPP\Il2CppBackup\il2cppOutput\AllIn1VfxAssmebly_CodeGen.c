﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_m92A8DADA841D24C84DF32D2A8551A954DA3A7FCE (void);
extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_m01AD9B8879E7D22FF293831D78BB116128A99F0D (void);
extern void All1VfxRandomTimeSeed_Start_mDA15A7000478CFFD6A05A4682D8264A4C25EE08E (void);
extern void All1VfxRandomTimeSeed__ctor_m7C1941EA9B48C2B65733D95234AFFCD643604CE2 (void);
extern void AllIn1LookAt_Start_mCC8D994795CB43E68509E633B1815DB6537658F0 (void);
extern void AllIn1LookAt_Update_m0A08D68C2DEBDBC9ADBFB2DDEDCE62BD0D8676F8 (void);
extern void AllIn1LookAt_LookAtCompute_m729D7D16E1394FB0818C7E6BCB3661828AFB608E (void);
extern void AllIn1LookAt__ctor_mF02E6C137CA1BC8C258B7C14A30B8C66F158369A (void);
extern void AllIn1ParticleHelperComponent_SetSceneDirty_mA8C50FFDA9C4CCE517E1FF13921E34A61554D0FD (void);
extern void AllIn1ParticleHelperComponent__ctor_m68AF57BA2F4F0E580F068414810D768F0F7FC368 (void);
extern void AllIn1ParticleHelperSO__ctor_m9AEBFEDE1FCCE704444E3C59C7C7A3D344601DD8 (void);
extern void AllIn1VfxBounceAnimation_Start_m6980DB7335C5A381ED59D3098EF626C0FD242503 (void);
extern void AllIn1VfxBounceAnimation_Update_mD47D1739EF4F7267CCE4E33B575AC9E498149727 (void);
extern void AllIn1VfxBounceAnimation__ctor_m4F526F9FB3184ECC0EAC866948AE87859C2767C7 (void);
extern void AllIn1VfxComponent_MakeNewMaterial_m995B26BFCD13754FCC6E97DE0A42BA17EB4BBCFF (void);
extern void AllIn1VfxComponent_MakeCopy_m7EEE43932ACDB5E9A9F201770C6C45465D0BAFB5 (void);
extern void AllIn1VfxComponent_FetchCurrentMaterial_m39F8AE40F3507E0BE6FAADA12ADA81C59718B026 (void);
extern void AllIn1VfxComponent_ResetAllProperties_m50ABAF51C59617C87828194552AA89EC998BAFB2 (void);
extern void AllIn1VfxComponent_SetMaterial_mBFA6195608E559482B63D71AB64188AEC8793F89 (void);
extern void AllIn1VfxComponent_DoAfterSetAction_mE585EF484C1E4657FBAAF98041E7A2772829E727 (void);
extern void AllIn1VfxComponent_TryCreateNew_mC64450CB86324EEE3F25245B8BC495F20F23F4D7 (void);
extern void AllIn1VfxComponent_ClearAllKeywords_m59BC37A1506BB4271456D76BF6E53597D2652F6E (void);
extern void AllIn1VfxComponent_SetKeyword_m95EC164255D1EA16944BE4564EFE7730F9864BF2 (void);
extern void AllIn1VfxComponent_FindCurrMaterial_m6005E8C7A35C0F7F353D15BD4026AD790B609BCD (void);
extern void AllIn1VfxComponent_CleanMaterial_mFAD12AF2EDBE22342D512879ABFF5F12D059BE78 (void);
extern void AllIn1VfxComponent_SaveMaterial_mEBF5E599BFBC05BCFD6CA3C99253D86427672C44 (void);
extern void AllIn1VfxComponent_SaveMaterialWithOtherName_m813A30F3AC436425C72CAE067E4696DEBAEEAE32 (void);
extern void AllIn1VfxComponent_DoSaving_m936CEFF84DEAAA780F01E1774FD78018AB351FE7 (void);
extern void AllIn1VfxComponent_SetSceneDirty_m9E6A673E9394C3A3B775625029362C9D996410EA (void);
extern void AllIn1VfxComponent_MissingRenderer_mB023CB700C00CC98DFECD40F146C57164D16C77E (void);
extern void AllIn1VfxComponent__ctor_m8ACEF8DBF8B3771EEDB5F36F03F1727BE579862F (void);
extern void AllIn1VfxFakeLightDirSetter_Awake_mFC968AF1A810D7A7ECC44EC14A230CC25280A6A2 (void);
extern void AllIn1VfxFakeLightDirSetter_Update_m49832CBC47E8981C8683A61E9139330F894E49BE (void);
extern void AllIn1VfxFakeLightDirSetter_OnValidate_mAEDEC47E4DEB9BE90B5FABA0578040D8C06085A5 (void);
extern void AllIn1VfxFakeLightDirSetter_SetGlobalFakeLightDir_mC9E6091C093D3A23241CA19E45E032A889CB71B0 (void);
extern void AllIn1VfxFakeLightDirSetter_SetNewFakeLightDir_mC8F9F66AF38019A5523C57E2ACB9F4D3DC07C2EE (void);
extern void AllIn1VfxFakeLightDirSetter_SetNewTarget_m0F2F25862843224A6D62E6A3784691B25599DE8A (void);
extern void AllIn1VfxFakeLightDirSetter_SetOnUpdateBool_mB9B0A836D23E435B99D73D67F247F6722345DD71 (void);
extern void AllIn1VfxFakeLightDirSetter__ctor_m4C5EA3BCE548CFE5D3AC5842C8540136B460DE4C (void);
extern void AllIn1VfxNoiseCreator_PerlinNoise_mF395BD7B1C25E3A3B0A31239B7783F1D3A04B964 (void);
extern void AllIn1VfxNoiseCreator_CalculatePerlinColor_m3675833D2D56F07A38F4B3FAE9493989B733D9F3 (void);
extern void AllIn1VfxNoiseCreator_PerlinBorderless_m49ADBDE375DC489A6473ED11D71EAD34E771AC45 (void);
extern void AllIn1VfxScrollShaderProperty_Start_mD66CC06C259D0358916B2E72EC5E0F495A4C52EB (void);
extern void AllIn1VfxScrollShaderProperty_Update_mC7E9894774CBE05469B32451E8EA242254567EAF (void);
extern void AllIn1VfxScrollShaderProperty_FlipGoingUp_m2E85C6CA803F28BD510C7D2B57F2C4FFF3D5F180 (void);
extern void AllIn1VfxScrollShaderProperty_DestroyComponentAndLogError_mE541EDF927CBB77AF92844EDB5F13BC99EE07A36 (void);
extern void AllIn1VfxScrollShaderProperty_OnDisable_m0F247B092283A86651B5ED5256D6C9E7FB5663EB (void);
extern void AllIn1VfxScrollShaderProperty__ctor_m1384E828EC486AA3BBF98290DC65AA715322CE68 (void);
extern void AllIn1VfxScrollShaderTexture_Start_mF0CF9576C87849A23149BD503FB5F299FBCF9A59 (void);
extern void AllIn1VfxScrollShaderTexture_Update_m446DDC71A8F5C42761DCAD83195F3DC48F865AC9 (void);
extern void AllIn1VfxScrollShaderTexture_FlipGoingUp_mAF1B7A83DBB3020A14A98614C591A410344AC793 (void);
extern void AllIn1VfxScrollShaderTexture_DestroyComponentAndLogError_m0FF3DD2B9CEEF6E92BB15E65DAC3E1F5546C3E6E (void);
extern void AllIn1VfxScrollShaderTexture_OnDisable_mF76C26882A3006EE2D4B7CC25955F8B850E0B06D (void);
extern void AllIn1VfxScrollShaderTexture__ctor_mFF32DBC76C34C5F76D16CA6EE1193F16CEEE8AC5 (void);
extern void SetAllIn1VfxCustomGlobalTime_Start_mC92C2125022DC43C6B927BF5948D4A797E61BE42 (void);
extern void SetAllIn1VfxCustomGlobalTime_Update_m061450880471093ABC066065FE0725F732BDF5B1 (void);
extern void SetAllIn1VfxCustomGlobalTime__ctor_mA35EECE553FF88334C3F456500E1FB33F28DDB88 (void);
extern void AllIn1GraphicMaterialDuplicate_Awake_mAF133D27FBE98EFB1DEEE7A25C8CBC5476049BCC (void);
extern void AllIn1GraphicMaterialDuplicate__ctor_mD51ED5244AFFF8CC67D241F800D0F836E152A148 (void);
static Il2CppMethodPointer s_methodPointers[59] = 
{
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_m92A8DADA841D24C84DF32D2A8551A954DA3A7FCE,
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_m01AD9B8879E7D22FF293831D78BB116128A99F0D,
	All1VfxRandomTimeSeed_Start_mDA15A7000478CFFD6A05A4682D8264A4C25EE08E,
	All1VfxRandomTimeSeed__ctor_m7C1941EA9B48C2B65733D95234AFFCD643604CE2,
	AllIn1LookAt_Start_mCC8D994795CB43E68509E633B1815DB6537658F0,
	AllIn1LookAt_Update_m0A08D68C2DEBDBC9ADBFB2DDEDCE62BD0D8676F8,
	AllIn1LookAt_LookAtCompute_m729D7D16E1394FB0818C7E6BCB3661828AFB608E,
	AllIn1LookAt__ctor_mF02E6C137CA1BC8C258B7C14A30B8C66F158369A,
	AllIn1ParticleHelperComponent_SetSceneDirty_mA8C50FFDA9C4CCE517E1FF13921E34A61554D0FD,
	AllIn1ParticleHelperComponent__ctor_m68AF57BA2F4F0E580F068414810D768F0F7FC368,
	AllIn1ParticleHelperSO__ctor_m9AEBFEDE1FCCE704444E3C59C7C7A3D344601DD8,
	AllIn1VfxBounceAnimation_Start_m6980DB7335C5A381ED59D3098EF626C0FD242503,
	AllIn1VfxBounceAnimation_Update_mD47D1739EF4F7267CCE4E33B575AC9E498149727,
	AllIn1VfxBounceAnimation__ctor_m4F526F9FB3184ECC0EAC866948AE87859C2767C7,
	AllIn1VfxComponent_MakeNewMaterial_m995B26BFCD13754FCC6E97DE0A42BA17EB4BBCFF,
	AllIn1VfxComponent_MakeCopy_m7EEE43932ACDB5E9A9F201770C6C45465D0BAFB5,
	AllIn1VfxComponent_FetchCurrentMaterial_m39F8AE40F3507E0BE6FAADA12ADA81C59718B026,
	AllIn1VfxComponent_ResetAllProperties_m50ABAF51C59617C87828194552AA89EC998BAFB2,
	AllIn1VfxComponent_SetMaterial_mBFA6195608E559482B63D71AB64188AEC8793F89,
	AllIn1VfxComponent_DoAfterSetAction_mE585EF484C1E4657FBAAF98041E7A2772829E727,
	AllIn1VfxComponent_TryCreateNew_mC64450CB86324EEE3F25245B8BC495F20F23F4D7,
	AllIn1VfxComponent_ClearAllKeywords_m59BC37A1506BB4271456D76BF6E53597D2652F6E,
	AllIn1VfxComponent_SetKeyword_m95EC164255D1EA16944BE4564EFE7730F9864BF2,
	AllIn1VfxComponent_FindCurrMaterial_m6005E8C7A35C0F7F353D15BD4026AD790B609BCD,
	AllIn1VfxComponent_CleanMaterial_mFAD12AF2EDBE22342D512879ABFF5F12D059BE78,
	AllIn1VfxComponent_SaveMaterial_mEBF5E599BFBC05BCFD6CA3C99253D86427672C44,
	AllIn1VfxComponent_SaveMaterialWithOtherName_m813A30F3AC436425C72CAE067E4696DEBAEEAE32,
	AllIn1VfxComponent_DoSaving_m936CEFF84DEAAA780F01E1774FD78018AB351FE7,
	AllIn1VfxComponent_SetSceneDirty_m9E6A673E9394C3A3B775625029362C9D996410EA,
	AllIn1VfxComponent_MissingRenderer_mB023CB700C00CC98DFECD40F146C57164D16C77E,
	AllIn1VfxComponent__ctor_m8ACEF8DBF8B3771EEDB5F36F03F1727BE579862F,
	AllIn1VfxFakeLightDirSetter_Awake_mFC968AF1A810D7A7ECC44EC14A230CC25280A6A2,
	AllIn1VfxFakeLightDirSetter_Update_m49832CBC47E8981C8683A61E9139330F894E49BE,
	AllIn1VfxFakeLightDirSetter_OnValidate_mAEDEC47E4DEB9BE90B5FABA0578040D8C06085A5,
	AllIn1VfxFakeLightDirSetter_SetGlobalFakeLightDir_mC9E6091C093D3A23241CA19E45E032A889CB71B0,
	AllIn1VfxFakeLightDirSetter_SetNewFakeLightDir_mC8F9F66AF38019A5523C57E2ACB9F4D3DC07C2EE,
	AllIn1VfxFakeLightDirSetter_SetNewTarget_m0F2F25862843224A6D62E6A3784691B25599DE8A,
	AllIn1VfxFakeLightDirSetter_SetOnUpdateBool_mB9B0A836D23E435B99D73D67F247F6722345DD71,
	AllIn1VfxFakeLightDirSetter__ctor_m4C5EA3BCE548CFE5D3AC5842C8540136B460DE4C,
	AllIn1VfxNoiseCreator_PerlinNoise_mF395BD7B1C25E3A3B0A31239B7783F1D3A04B964,
	AllIn1VfxNoiseCreator_CalculatePerlinColor_m3675833D2D56F07A38F4B3FAE9493989B733D9F3,
	AllIn1VfxNoiseCreator_PerlinBorderless_m49ADBDE375DC489A6473ED11D71EAD34E771AC45,
	AllIn1VfxScrollShaderProperty_Start_mD66CC06C259D0358916B2E72EC5E0F495A4C52EB,
	AllIn1VfxScrollShaderProperty_Update_mC7E9894774CBE05469B32451E8EA242254567EAF,
	AllIn1VfxScrollShaderProperty_FlipGoingUp_m2E85C6CA803F28BD510C7D2B57F2C4FFF3D5F180,
	AllIn1VfxScrollShaderProperty_DestroyComponentAndLogError_mE541EDF927CBB77AF92844EDB5F13BC99EE07A36,
	AllIn1VfxScrollShaderProperty_OnDisable_m0F247B092283A86651B5ED5256D6C9E7FB5663EB,
	AllIn1VfxScrollShaderProperty__ctor_m1384E828EC486AA3BBF98290DC65AA715322CE68,
	AllIn1VfxScrollShaderTexture_Start_mF0CF9576C87849A23149BD503FB5F299FBCF9A59,
	AllIn1VfxScrollShaderTexture_Update_m446DDC71A8F5C42761DCAD83195F3DC48F865AC9,
	AllIn1VfxScrollShaderTexture_FlipGoingUp_mAF1B7A83DBB3020A14A98614C591A410344AC793,
	AllIn1VfxScrollShaderTexture_DestroyComponentAndLogError_m0FF3DD2B9CEEF6E92BB15E65DAC3E1F5546C3E6E,
	AllIn1VfxScrollShaderTexture_OnDisable_mF76C26882A3006EE2D4B7CC25955F8B850E0B06D,
	AllIn1VfxScrollShaderTexture__ctor_mFF32DBC76C34C5F76D16CA6EE1193F16CEEE8AC5,
	SetAllIn1VfxCustomGlobalTime_Start_mC92C2125022DC43C6B927BF5948D4A797E61BE42,
	SetAllIn1VfxCustomGlobalTime_Update_m061450880471093ABC066065FE0725F732BDF5B1,
	SetAllIn1VfxCustomGlobalTime__ctor_mA35EECE553FF88334C3F456500E1FB33F28DDB88,
	AllIn1GraphicMaterialDuplicate_Awake_mAF133D27FBE98EFB1DEEE7A25C8CBC5476049BCC,
	AllIn1GraphicMaterialDuplicate__ctor_mD51ED5244AFFF8CC67D241F800D0F836E152A148,
};
static const int32_t s_InvokerIndices[59] = 
{
	21378,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	5666,
	12815,
	12815,
	10682,
	3436,
	10629,
	12815,
	13298,
	5666,
	13298,
	13298,
	12815,
	5681,
	10682,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	10912,
	10682,
	10442,
	13298,
	15473,
	13875,
	13741,
	13298,
	13298,
	13298,
	10682,
	13298,
	13298,
	13298,
	13298,
	10442,
	10682,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_AllIn1VfxAssmebly_CodeGenModule;
const Il2CppCodeGenModule g_AllIn1VfxAssmebly_CodeGenModule = 
{
	"AllIn1VfxAssmebly.dll",
	59,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};

﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void Il2CppEagerStaticClassConstructionAttribute__ctor_mCACE94326399F3059F318EB568BD8E45037E3139 (void);
extern void bool2__ctor_m097D0D586C955D0F4E04DD5D308F0DD17297203D (void);
extern void bool2_Equals_mA73BA304B87D4C007247008330E8AE017413F727 (void);
extern void bool2_Equals_mCEC91B262F6D86366DC9EA06BA9495043F21D050 (void);
extern void bool2_GetHashCode_mE8ADDB566B213BA0F2C6359CDBCA8B2169F64B91 (void);
extern void bool2_ToString_mBB7BC458D3C5F3427EBE7EFD5323DCDCECBB14FD (void);
extern void math_hash_m8111065B3777555BB22B9C3F88A1B31BD8CC388D (void);
extern void math_hash_m6C080FB0A7D1E2425DBBEEFF1B206E5C18F725C6 (void);
extern void math_float2_m24A922BBF741AF168DD4591FC8C4CF9E67A85BAE (void);
extern void math_hash_m102FB27D63A32D75D8CB1430F32BAFDEE29BCED8 (void);
extern void math_float2x2_m3C7E37DA31EE873D94E436595BC660B30FE50BA3 (void);
extern void math_determinant_mEEFE10F738EA4C8F09E0BF2C7F6A5B1C786A32C0 (void);
extern void math_hash_mB247638FEE355912BFED2DE534B317779199F284 (void);
extern void math_float3_m4F96A74FEE1D6C85241B8E62386C5DE1C439837F (void);
extern void math_float3_mA7B7F259A567DAFC65F7808E9CEDDE655E0F6F94 (void);
extern void math_float3_m7AB8F47ADB3AC275D701AF1E376438B146541493 (void);
extern void math_hash_m8ECC7CFBA8D302A2A3E9468DE65D705E9C1298EB (void);
extern void math_hash_mCC0D346D77A7BAE4C16EB878E1FDF69E863A09C3 (void);
extern void math_float4_m16697C284FA0C25A84F3DC3E99F3D4C306B6BFBF (void);
extern void math_float4_m8177BCC80431B291731F53C99FF25D3090AB286D (void);
extern void math_float4_mE797E50A2E9FF0935867108F0F25C0F683E47B15 (void);
extern void math_float4_mE54104D60E6B9A358C75CB6F378118AB4914BFC4 (void);
extern void math_hash_m53E875B2DC4324BD20573419DBE27D0F651FA4D4 (void);
extern void math_float4x4_m7C95B2B93CDEE0AF483EB84446F9F06F7B1AD261 (void);
extern void math_hash_m20286BA0E4D2F6DC3C7013DA713AADCFFD87D444 (void);
extern void math_int2_m0A9A8113022D7954E7DA5BE5436989C2DF01650D (void);
extern void math_hash_m6B6E0FC08FCC3BC0940397676690F22B03DB4F16 (void);
extern void math_hash_mC744961C63A1CA98FB11450AAFA373AFA4DE73EF (void);
extern void math_hash_mE07D6FDDFDA633E2F3A93048E26573F181ECBB45 (void);
extern void math_asint_mBDED7FE966CA65F6A8ACEAEF8FD779B1B8998288 (void);
extern void math_asuint_m64DA623C5CFEB8445663480384F2B1C202150EE5 (void);
extern void math_asuint_m6EDCEC78D97FE38F42C86EED91C0550F5B85DFBF (void);
extern void math_asuint_m39B6AA9AB80F5EC34FBDF14A8C9289C9D9904D89 (void);
extern void math_asuint_m503D1ABF19E4BA615FD8AE1BF1A2E103BBED6139 (void);
extern void math_asuint_m22CC00686F9722FF2ED6330E3C0B4699C55CB1EE (void);
extern void math_asuint_m4AEE8C17FEDA05D4C77C427818D1C9EF5E31521E (void);
extern void math_asuint_mDF3C61EF6F9D9D10A1D3EB9D0075149707B461B9 (void);
extern void math_aslong_mCD3846AC0EFB4901B00A20D0960C80C8CBE66366 (void);
extern void math_asulong_m2CF160E23B5FF618A85C3C29B2FB1C000E40290F (void);
extern void math_asfloat_m9FA56DE5C61FCEF3DCD0675252D40DFD9C9B712F (void);
extern void math_asfloat_m20D259DAAB46464B59BD8BF5678F9D59800F70A9 (void);
extern void math_asfloat_m21833833779E44053D1E1B38677D9C5D83753D28 (void);
extern void math_asfloat_m7A90E1FAABD250FCEC00839D01B098BB046F7933 (void);
extern void math_asdouble_m4C4CC1B9299FE33530ED375768D67B00676C31C8 (void);
extern void math_asdouble_m3E7BC790C743E67EA45476AECD6D2D9A9E62E4F2 (void);
extern void math_isfinite_mC7FACC44EBD8D443AA9CE0F0F47F8426EB68CDF8 (void);
extern void math_isinf_m4901864832BAA489A01E23F560733ACEF6E3ED60 (void);
extern void math_min_m02D43DF516544C279AF660EA4731449C82991849 (void);
extern void math_min_mFBB411A5384A9CFD7787E398A6F758553D3700A9 (void);
extern void math_min_mA22BCFB62A81B533821704D26BE23D8D6402C8EB (void);
extern void math_min_m54FD010BEF505D2BA1F79FC793BEB0723C329C3B (void);
extern void math_min_m68ED612C41E325FA3446050EA04D0AC0CD191558 (void);
extern void math_min_m13CC8D5B7844D954C3125DD72831C693AB8A7FF5 (void);
extern void math_min_m29A6A5FB36524D911D13DDB4866FF005C7BF00D5 (void);
extern void math_min_m1D64D6B67B27FD9738D14BCEE6298146CB05CE00 (void);
extern void math_max_m9083201D37A8ED0157B127B5878D9B7F3A2A40BE (void);
extern void math_max_m11CA4E4999B4F371CB5189F06CAD3FD0E852B2B4 (void);
extern void math_max_mD9D4307218A8CFA92F9C26871E508B23C17F6395 (void);
extern void math_max_mEBAE1BF7FA6B43BD0F4AE2E47FB6190041F8CE43 (void);
extern void math_max_m4B454A91AE8827997609E74C4C24036BBD3CC496 (void);
extern void math_max_mFD64D6399932C2D91018BA7895C06FD055E1361B (void);
extern void math_max_m247D41258606F80861E72309300DF6A3F8B50AE4 (void);
extern void math_max_m8830F8721EFC73BCF991CD497115A103B86BF3BE (void);
extern void math_lerp_m58A82DB48BBA11871FFA81583C700875B3A9BC84 (void);
extern void math_lerp_mA20BFB8D988B57C1CFA28047538F3B47208D1371 (void);
extern void math_lerp_mF6942D2A43740A2D3924483C724F9BC900D953A2 (void);
extern void math_clamp_m9EABD008C8EAD9D150062ABE724D96FA2121EE1C (void);
extern void math_clamp_mB7233FC9D6C27522014C4E6D4E056D36CE82C97E (void);
extern void math_clamp_m39FE4EA2420B8DF536A1344B16D9E39EF5B7155F (void);
extern void math_abs_mFF027629978A9039B059528ED3075D775AA0B0AB (void);
extern void math_abs_m3D9508B36B045BFE7B89C6C69AD34596264E4FE1 (void);
extern void math_abs_mDF669CF3AF2C60713E8E118578461CDA050DAFD0 (void);
extern void math_dot_mF673D3E5B7D267C0A8569B678D05BDCCB667D04D (void);
extern void math_dot_mE193D8880350D74CC8D63A0D53CDC5902F844AAD (void);
extern void math_dot_m20F2285F7227DC308D9CF2DCB8EAAD3E774501D4 (void);
extern void math_dot_mA992F4ADC67180A7EB3850222857193CD0F6B21E (void);
extern void math_atan_m47A3F5B37A1469CA1E78FB682B6337673EB02AFE (void);
extern void math_atan_mFC948313E50230B5E4328AE5ED4075665E78A3E7 (void);
extern void math_cos_m28B6228E047D552B1312CCFADB8AE95DDD94A6AF (void);
extern void math_cos_m42275E85C55A660ABC711D07B4349A82F4BBCBC4 (void);
extern void math_sin_m231F847C28B88B17BDAD7F49A7A38E46DF12D3FF (void);
extern void math_sin_m43618973AB0574A29896B4479E1F72A829644A33 (void);
extern void math_floor_m0FDF19C33B0B1062079FCB10FB081869AEC1FB48 (void);
extern void math_floor_m49AF91133E08FD4B7A652BCC41F25EEC9DF52D07 (void);
extern void math_ceil_m01FC8783CB8656774F0A793EA3BBF831F7CE19C0 (void);
extern void math_ceil_mC879669549692BD4136602F9C3937B5D2B30981C (void);
extern void math_round_mA3594A8D6D29ACA14FF38F38FFBB83DF579D87B9 (void);
extern void math_round_m649C0F15E3AD1A8899E68C17D02E332F5F94A054 (void);
extern void math_trunc_mFB08702CED1DAFA9E112CAA94ECF59026C066112 (void);
extern void math_trunc_m0B8E3445AAF1F11E3BD64086C7402BB3CE1A06C1 (void);
extern void math_rcp_mED2BCEE83560EEE59CE06EBD90332CAFA9C08024 (void);
extern void math_rcp_m6A26002B1F4212C525255E31010FF526C84F591D (void);
extern void math_pow_m2B2C611A37952CFB13BB0AE800A6A601A2E4A49B (void);
extern void math_log2_m07B499B0DDA692EDD9DF4780349C26EB28199156 (void);
extern void math_fmod_mE5EE515D4B99F417B55111171D6100728FA68BA2 (void);
extern void math_sqrt_mEF31DE7BD0179009683C5D7B0C58E6571B30CF4A (void);
extern void math_sqrt_mA3A9D5DFDF6841F8836E3ECD5D83555842383F36 (void);
extern void math_rsqrt_mC67B3430EAADA7C5347E87B23859C569BC010E72 (void);
extern void math_normalize_mAB67BF670979DC2A6C8B30791BFF8476FEC1B5CD (void);
extern void math_normalize_mF02431EFC9E3212E0245EFF5C13BC9DC34512399 (void);
extern void math_length_m3DB47D254C8544FBB740A892B4AE2143E8F45634 (void);
extern void math_length_m6A2B63D7A3B84261C2F7FCAA2CB382288A57D257 (void);
extern void math_length_mDA291F159E5B088CF2EF354538EBDBC60063C9E7 (void);
extern void math_length_mBC9788A14DDEC3FA5794F7F49EDD1516C5EDE4E3 (void);
extern void math_lengthsq_mDC7DB2DF1C069D45CE87BC737AF59CB0094042FA (void);
extern void math_lengthsq_mC699F3F214F05B26BEBAF1B46E3AA3C00407A532 (void);
extern void math_lengthsq_m246AAF09A2EA30D8FE4314442E031D9B5AFF31FF (void);
extern void math_distance_mE5E0FFDD103E710A4CB23360BFCAFD0AF2E1EFA9 (void);
extern void math_distance_m72BEFBAADFC4404FADD3AD81F7EDD40E32624F4D (void);
extern void math_distancesq_m491F2EBC94F119EA8EE8EA6BB30C44BB0BEB37E3 (void);
extern void math_distancesq_mA49E8B34404D0C4DB3C9D4E065CE4CA255C9770B (void);
extern void math_distancesq_m609DF85E2355430E1F4CD51CDC1971BD5F7D4AF3 (void);
extern void math_cross_m4CA2DAE150C6381B0D05E8AA9E48E88CF6157180 (void);
extern void math_any_m76E4E0A6EAF4782AB83EC7BE0BB7E2513791C0B0 (void);
extern void math_any_mCBBE4E2611B227A8AE1A4DA7F104D779203539F9 (void);
extern void math_select_m8BBDC6133225059EE5F0FCA74BA9497AD5496024 (void);
extern void math_select_m85465C9438F81A86CBDA83FC4E8201D8D2A61828 (void);
extern void math_sincos_m28D7C74E99CF12DE35172DC6F26C77FD4D46D1B7 (void);
extern void math_countbits_m4CA1327F4995735E6AA4862E641E128312C404BD (void);
extern void math_countbits_m17808348628239FFE20276AB711D0B609F3EF6A3 (void);
extern void math_lzcnt_mA6B7E71DB1B5D4CE8B67C66FF1AC4339FA368D07 (void);
extern void math_lzcnt_m121BDDDEE89F5A401E2E5F0AD900D22E47C8741C (void);
extern void math_tzcnt_m85FEAD596A8E327F7B6820310B7FBD9822BA735C (void);
extern void math_tzcnt_m07FD7550AAB5D94312E99571B112D652E8230360 (void);
extern void math_tzcnt_mA20C30896B5942219FA80AA79653F59629D28E78 (void);
extern void math_tzcnt_mB3717EA8D7052CBC8F048C8ADB37D6C6060C5490 (void);
extern void math_ceilpow2_mA00505409975D36AB3D7658687AC3BD5A26F3769 (void);
extern void math_ceilpow2_m55FA5F42278E2409D11BF05A5A9E3B2A883C82CB (void);
extern void math_ceilpow2_m7941384EAF6F776691CD2601130055362B0C967C (void);
extern void math_ceillog2_m1F8130D49F06A9A97699F43FDB771782FE1A7930 (void);
extern void math_floorlog2_m27DF7DB26915A7FE16A4A72ACDA15795BF95562E (void);
extern void math_radians_m0903022269E582AD877B86952497295D642036E8 (void);
extern void math_csum_m0B6655875FE24428E18B97FC3F5C745E374530A3 (void);
extern void math_csum_m9C15CCCED13E8ADB45EFC388D141E55091A61C1C (void);
extern void math_csum_m6A99E69A84442A729781A97F78B260223DD01D8F (void);
extern void math_csum_mE1D11CB696F7217C722D039256B183F6EEBAFF96 (void);
extern void math_f16tof32_m558AB5CFC4B974A267AFB1E799C3F8D7D530B419 (void);
extern void math_fold_to_uint_m9A795A00E9FC9F4E4BE563540711D2F47A9D9F28 (void);
extern void math_fold_to_uint_m3BD058FD32F4D8F26960CEA718F6E2A5FAE9B3B3 (void);
extern void math_float3x3_m850F2B065688B7C95009136DAA44853A04069298 (void);
extern void math_mul_m4C38B3C1EB3A8DEBFD06421A2FCBE54E7CDC46EE (void);
extern void math_mul_m080515A2DCB7CCE0F4635A8E87B1635C02D72A4E (void);
extern void math_mul_mF9EE8CEC6C3D6A57A74F806EA436DA9465FCB01E (void);
extern void math_quaternion_m315B4CA2F8475CC33F3C73187F00AC8B64BBF939 (void);
extern void math_mul_m3CC0941E6A3DE5718C6439421E74D7F80793F652 (void);
extern void math_hash_mEE8D76145C2CD7DDEE85990255444BCC3DCC161A (void);
extern void math_uint2_m861F5F74EBBBD3DA19E84A1155320B89FF7341C3 (void);
extern void math_hash_m5D21276708BFB4DEEF3498774D296FE6B14FC5B0 (void);
extern void math_uint3_mC94DDA8B357EA045D5A36B81CECD0C5C223B71B0 (void);
extern void math_hash_m31E070E721A961************************** (void);
extern void math_uint4_m7F6A5341327B9EF6B80C82FD1B93E4F32287A336 (void);
extern void math_hash_m1A4778A79FFB5E05B04BD09B0F85EA9483D8A3CA (void);
extern void double2__ctor_m4026FE95F69FAEBD29D7092ADAA1CB845A8E859B (void);
extern void double2__ctor_m3355A4008574AE2483EAD2841176C67734F10F33 (void);
extern void double2_op_Implicit_m168C031549D6C086B7C49ECA5B18C892B3112F17 (void);
extern void double2_op_Subtraction_mDAD1E402F52C548544D20D62D7FA098F4F858BC8 (void);
extern void double2_Equals_m3047316C17464EDC21C1A6F79DB472D5812F75DF (void);
extern void double2_Equals_mFF460810B0184AFE3BF8DC1865F306AD087FEC33 (void);
extern void double2_GetHashCode_mAD6AF7BC1D935CF5B63D519D9F811A3486D609DE (void);
extern void double2_ToString_mE9B5B271B464187E1772BE62DE23A047E8C93164 (void);
extern void double2_ToString_m512CFA1B15214C11C12E52771B83BB4765BDB8F6 (void);
extern void float2__ctor_m3D598E2C2D173DE852F3AB157502968261383C97 (void);
extern void float2__ctor_m36DFF2F3BE02DB4AC5E36F0CDFF2DB54D872F979 (void);
extern void float2__ctor_m119EA382660CA825559E95AE3770C59196ED449F (void);
extern void float2_op_Implicit_mDCE8EF24B96CB48EEFA3D9C75760ECA01874C735 (void);
extern void float2_op_Implicit_m9CAFCA676C0609D02F3F23A214D01DA3FF480FF4 (void);
extern void float2_op_Multiply_mD97F1F12A640BE857FD483CD188E7EDF44FB19A9 (void);
extern void float2_op_Multiply_m34D03129CE0D7AD665A914DE83CB749585B2455F (void);
extern void float2_op_Multiply_m9117237F9A26A1B934C1DE0A5FE5BD6EF7D3B26C (void);
extern void float2_op_Addition_m718974663A956F64D7C45D06C088550637F13693 (void);
extern void float2_op_Addition_m6A1EEFBE2B92691F07967739DB3ECEEB432968AD (void);
extern void float2_op_Addition_mB2553B04CC2B7BA017644D2F33A956A56F5E334B (void);
extern void float2_op_Subtraction_m28172675A65BCFFBC8C9023BE815019E668B8380 (void);
extern void float2_op_Subtraction_mEBC8F4CCCAE3FF01E674C44AE24FD7BA4B3FAB08 (void);
extern void float2_op_Division_mA1BE4D539CEEC02DE7F3C3C35060D3CEF1CBEDDA (void);
extern void float2_op_Division_m4AA175CD0895AA1A50F5A73B54722CA53876EE6A (void);
extern void float2_op_Division_mE9F0EA43229DDFD549088D0F71AABF831698EE5F (void);
extern void float2_op_UnaryNegation_m63834E95475E7EF1CA1583F6A08420C3BFA34458 (void);
extern void float2_Equals_m1E68B5EDCDB491FEBA657E58D6A91E05AD550CDA (void);
extern void float2_Equals_mD389D74AC9D1E4E537F95C97B79026A6C3244788 (void);
extern void float2_GetHashCode_m8E40B8206F9E87300406D8DCA54F6AC88CAC4FB7 (void);
extern void float2_ToString_m41C07CB0EC7D3A938DBEC3A113A0FDB29E2B98D0 (void);
extern void float2_ToString_m0921A1A5C64FC14E7E808684F079B7BE29EC5AB1 (void);
extern void float2_op_Implicit_mBB339033BC4FFDA8BC7A41DF06C1FA97F60115D9 (void);
extern void float2_op_Implicit_m274CE0EFDC0FFD5168817614BF1A3699784DE02B (void);
extern void float2x2__ctor_m4191D97630C2FF4ABB7B21317DBD4547BB2F187B (void);
extern void float2x2__ctor_m9FEC95CA119B472C39298B2AECFD1CC6DD83691E (void);
extern void float2x2_Equals_m29387F36A0F27E8686227D47EA0677CAD1BC0942 (void);
extern void float2x2_Equals_mDA00F71DC8D21B2033290BDA1DD483C134396EFD (void);
extern void float2x2_GetHashCode_m4233F3E1E849EAB3A4E0FC81F52774A1C9AA4C42 (void);
extern void float2x2_ToString_m9DB46B1F9E2DE945733549ADD173B32E883B6DF0 (void);
extern void float2x2_ToString_m19F5377833DC86DB067E77A46FEAAFC3E07628D4 (void);
extern void float2x2__cctor_mE7DBC93D8DC4B775E51C768D25622D845E45FDF5 (void);
extern void float3__ctor_mC61002CD0EC13D7C37D846D021A78C028FB80DB9 (void);
extern void float3__ctor_mD111893D7A308AFFD7AC0CC410B348877E301B22 (void);
extern void float3__ctor_m294ACD85A4CE5F3F9C41D30933CA52AC64660830 (void);
extern void float3__ctor_m777F109CF7EBEEC78FAE5DBE52E4498CFA65E8C4 (void);
extern void float3__ctor_m131368AA87C134F64DD0B5B8684C1345BEC6EE59 (void);
extern void float3_op_Implicit_m495864276F56A9757CE235005D9908DBB47D8031 (void);
extern void float3_op_Implicit_m0E52B9E5BF532737C8B469DA33E08C16CC53AEB3 (void);
extern void float3_op_Multiply_m05E57074FBD5FAB0E72940C9CC019C41915280D7 (void);
extern void float3_op_Multiply_m6E5DC552C8B0F9A180298BD9197FF47B14E0EA81 (void);
extern void float3_op_Multiply_m38F52B61F8E5636955A1A6DF3A75BD0724148350 (void);
extern void float3_op_Addition_mFFCF4F7457594F5EFB0678C0DE90AAD3D3F6947B (void);
extern void float3_op_Subtraction_mB6036E9849D95650D6E73DA0D179CD7B61E696F2 (void);
extern void float3_op_Subtraction_m111BEEA770E140739DDC6A3410736DFF7EE32045 (void);
extern void float3_op_Division_m59FB3E510B03034B8834D7D724561FB9EC4DBB81 (void);
extern void float3_op_UnaryNegation_m862876969881839716CBAF9AE074FA4BFDFABDF1 (void);
extern void float3_get_yxxy_mF003E86CB17AE3175090DEF37A636AD75087AF81 (void);
extern void float3_get_zzyz_m78B967EBE527FA5705C0E7B38563D1E33BB992C1 (void);
extern void float3_get_xyz_m720A862AA512BE0B0B1089527A43EEF2B6766BEF (void);
extern void float3_get_yzx_mDF6DE39B69C5DE384F74C0D1EC91AA0388E23535 (void);
extern void float3_get_xy_mFD536022DC4F5162E2FE30328BE2418E4878349A (void);
extern void float3_get_xz_m687D2F3B82CB637649325B969C1406D86133EB30 (void);
extern void float3_get_yz_m271DC12D413A6F5E411E48BB6300705BC453D7F0 (void);
extern void float3_Equals_m4A47BDC70977496712F3BE7DA359E840D99C020A (void);
extern void float3_Equals_mD988046122040559D8C9EFED2DA347495D722A2C (void);
extern void float3_GetHashCode_m659801FA23576CC26412BE644FA19636BFF8A017 (void);
extern void float3_ToString_m334A7FA7041C2C5D83B68231F1FDEB4037CF5DEA (void);
extern void float3_ToString_mE1BA3E91F84DDD745685C09F509410358A83D081 (void);
extern void float3_op_Implicit_m9CC301DFD67EEFAA15CA05E91913E862B22326F6 (void);
extern void float3_op_Implicit_mE1831A3AC179B7EB3236F8202EC8DD5CE05376AB (void);
extern void float3x3__ctor_m3AB31C9B587ABDCF15C8BF0E3A5B0158996A75ED (void);
extern void float3x3_Equals_m5AA50ACE9FA1CFD3981FD1F2F94B1C782C20ACD0 (void);
extern void float3x3_Equals_m340F5B6BC1C9B1DCF517AA56077E2D3430CEFCFA (void);
extern void float3x3_GetHashCode_mCA45DB35B95265E20B9E17E363AEAC9649056705 (void);
extern void float3x3_ToString_m2265642475440D69F1A8D1C2C466E1DC31F48934 (void);
extern void float3x3_ToString_m42DAD14EF6EAA5DFB6F501645C325D29A6079522 (void);
extern void float3x3__ctor_mF94488DFF7867CFC89648E024FA89A19F23E2FAE (void);
extern void float3x3__cctor_mDD98D4703621A506785CC046B18E86B0B5388675 (void);
extern void float4__ctor_mB2F7F2D8BCE8159BEF5A0D6400499E211858ED2D (void);
extern void float4__ctor_m2A6267BB3EF610ED0F833FD07322B70EA219E9C7 (void);
extern void float4__ctor_m062DCDEE8A8DFB587E88A4F1D18E7415B683CB00 (void);
extern void float4__ctor_m2A21052EF06884F609D1CDA9A2C2ED84A7584345 (void);
extern void float4__ctor_mDE7B1FCF1C1895705616AB2D0C0ED42422743D1D (void);
extern void float4_op_Implicit_mA889A779BA63B2654EEEF1F5CE2407DAA0E33142 (void);
extern void float4_op_Multiply_m0E98338FB7DFF55B101EBCD78A8703ADB9C08667 (void);
extern void float4_op_Multiply_m712573F441DA8AF0843DE2167927FB76E642B1EB (void);
extern void float4_op_Addition_m2CF2E1B2DAD4996DE3C5B6DFB90185E4CC8F0F44 (void);
extern void float4_op_Subtraction_mBC40F52B8A8EF499A1AA3CC987E5935BD188B4E3 (void);
extern void float4_op_Division_mA82993BB826C4799764D5B0E543D0D7776F4F8A2 (void);
extern void float4_get_xyzx_m5FFC709BBF25AC7B7755AE077E4288E47089C76C (void);
extern void float4_get_yzxy_m78C60157637373AC6CB36343778B04CAA046ADDD (void);
extern void float4_get_yzxz_mE81FC33B6A0D370B2C8DB339CC5501533BB31223 (void);
extern void float4_get_zxyy_m4B8D0449EEB8DD3CE5A79C96E911C70617339C4E (void);
extern void float4_get_zxyz_m91C90CDA336353253B33F0407198ACCC593EBB1B (void);
extern void float4_get_wwwx_m698BBD6517D0DCF19C3529E0E686FF430642E480 (void);
extern void float4_get_wwww_mF04E8B99431D2717DEE58BDAE207EF4C7E1A0009 (void);
extern void float4_get_xyz_mE6EC829F35512C7BC159047FDC134E80F3B37A06 (void);
extern void float4_get_yxw_mBACC7261DBDD87BA51639CE46D9958D97B18DD05 (void);
extern void float4_get_zwx_mF2EB9E380A5092E72833677BC468332F5493A4E6 (void);
extern void float4_get_wzy_m38FCFC0E1F5BB656A426E039349E69B8D5AA24EF (void);
extern void float4_get_xy_m26BE8952E15131592B83837998D1EDC33FA9E6DF (void);
extern void float4_get_zw_mE9148903506B6AC51F8BA30DBDE2CD34C67107BB (void);
extern void float4_get_Item_m42C2633E42BEBFB2230FF9125BC95D13EF5DD7DF (void);
extern void float4_set_Item_m96DA990882049B7CDDE20DB9C8B3685F60278BC4 (void);
extern void float4_Equals_m5E14C29D104399C399A11F52E7C2D507504AC0D1 (void);
extern void float4_Equals_m4D841ABA91D26FA16B07205DC9D51612265FFAB4 (void);
extern void float4_GetHashCode_m73A9ACC41256A6E98F883A72ED1EB0047947DA48 (void);
extern void float4_ToString_m63852A53A6218EBA6CF7782E0E38DE0719AA6DAA (void);
extern void float4_ToString_mF0B7870047A0CB59185C85DE13ECECFAC5783C2A (void);
extern void float4_op_Implicit_m6D2091EB2CF6E0629A029A7BE9AD230F5F394CB2 (void);
extern void float4_op_Implicit_m5E3AEBAF5F12155549CC051E1EEEE81DF3516E92 (void);
extern void float4x4__ctor_mF0B7C823E36025A539E8024123057CAC380E97EF (void);
extern void float4x4__ctor_m89D3EDF09863C02553147CDA696E48B8D2622AC5 (void);
extern void float4x4_get_Item_m91F2AB53518F231D479AF5D50DD6017F658F6828 (void);
extern void float4x4_Equals_mB22F89A6EC760BECED603A00205294B0C185CCBB (void);
extern void float4x4_Equals_mD77FF5880D1151E85DC213AFF8E24B12044DB6B8 (void);
extern void float4x4_GetHashCode_m260D00DFB6FDDAA4052A0893C7AEA6E5599DF9F7 (void);
extern void float4x4_ToString_mB25DD7002ADF39A9117BEAF2A69EE880FB8E7584 (void);
extern void float4x4_ToString_mB33825C3DB524BA41F1DF5356C61B0F8BA6A8B81 (void);
extern void float4x4_op_Implicit_mCDD72C5454A8DAFB2A659484006D18D5979BE6AB (void);
extern void float4x4_op_Implicit_mC056D7C9F17B6D06E026163034B5414E56DA04B4 (void);
extern void float4x4__ctor_m36D57BB3339FCFAE6FA39D35CAB4AD9010183D04 (void);
extern void float4x4_TRS_m253E5B92580E420E113F8C195664D06F967EEF03 (void);
extern void float4x4__cctor_m8AD96186FE409BAF9B9D69DF283AF3BBF336BE19 (void);
extern void half_get_MinValue_m60E08648E62A1C135FA2437D67C33BAD32498696 (void);
extern void half_Equals_m60FC2376046103BB9AB11E9AD896585BA5FC5A13 (void);
extern void half_Equals_m7593A00CF2CA899C147FE9813C57A7332F4346F3 (void);
extern void half_GetHashCode_m6ED49A661CFDE2082EF21678A7D559182734A812 (void);
extern void half_ToString_m1D04F7DC5C8D90E2F92E614F58BDF3F015D6558C (void);
extern void half_ToString_mB95CFCA3EC40127249350792090F08EFC1B7DC01 (void);
extern void int2__ctor_m452D21510717D0961119C89A72BBB8D84DCD49F4 (void);
extern void int2__ctor_m1F683444787E63F08971693F833BBF8012827CE6 (void);
extern void int2_op_Implicit_m8CFB0C58F0EE9CF236FC42E64F57838140AF7D46 (void);
extern void int2_op_Addition_mB8210BD1CFEF218431BCFB14816FA467B196BCB7 (void);
extern void int2_op_Addition_mDC6C2A91C24EE95C695A1F0179AC85A845045D2E (void);
extern void int2_op_Subtraction_mC938968ABFBC65A87958D0C460911E66345F7AAA (void);
extern void int2_op_Division_mE73B8B8DF181E932008B939A87FC6A7CB0547F75 (void);
extern void int2_op_LessThan_mDA9D38A2DFECDECA7FE2934167CF14DE2A710D13 (void);
extern void int2_op_RightShift_mC401366956E76AC2BB075841BF4FD16CEEF95227 (void);
extern void int2_op_BitwiseOr_m0570B3FC79725BF4940B9008B64AE03D25A85190 (void);
extern void int2_get_xyxy_mAD2CD0BE7639D713E52DAE9D6EE7534DF1B6E71A (void);
extern void int2_Equals_m4DB2E24953677E4E2497F1D0C4B48794257A89B6 (void);
extern void int2_Equals_m6B885828097BED2665EAA565C07E9FE83627C481 (void);
extern void int2_GetHashCode_mDC30EB8816F1ABC7195872B81F3EA9E8090866A6 (void);
extern void int2_ToString_mC1A7775631D1F6B67B370423AF94BF60249A2466 (void);
extern void int2_ToString_mB5FA23BE92C8532E53BAAA3B743D89ED6C23EE47 (void);
extern void int3__ctor_mE478318DE4CA648614FEF2C1DD438C0455284BF2 (void);
extern void int3_Equals_m677848DDE5FAE2DDF177465F5E38B1DAB2E39419 (void);
extern void int3_Equals_mAD3F27218DB5A5341B15F177DBA5ED687A0727D1 (void);
extern void int3_GetHashCode_m8AC6D1E8012392878EC4C8894FC3306D5DB2350E (void);
extern void int3_ToString_mF0D5E77289718FA92B3B386C9E956CA9F83D92FB (void);
extern void int3_ToString_mAB969F813E35B83C1B4ADC074583E3FAC5982632 (void);
extern void int4__ctor_m4E8D71A09721E26F7FCCE82EA8AD699062EE6216 (void);
extern void int4__ctor_mC08BC6E7709B75AEBCFDA6A26B4AB4525C1B2D26 (void);
extern void int4_op_Explicit_mCBE5AD9158BC21B2E93CFB154927523284100E6A (void);
extern void int4_get_xy_m9402873A61D837606A752C019B7AB157EA4FC6C9 (void);
extern void int4_get_zw_mE8F9C45028C9B218050B7DBDF61063B60F62349D (void);
extern void int4_get_Item_m2281A8F3D1D12AE2E09614009F90CEDAED6CB284 (void);
extern void int4_Equals_m966659D148734F0C67F1D7F78F1B391DDB2D12B2 (void);
extern void int4_Equals_mD93E26A15709372D4D3E34D8797F80C3FA01CD97 (void);
extern void int4_GetHashCode_mDE43A26C805DF1BA46E907BFE67B7A74559FE8B7 (void);
extern void int4_ToString_m555B1DA60EDDB2F461216E91E740D63953BA0112 (void);
extern void int4_ToString_mEC239D567C5A8C38EC41A06435499AB1E887066C (void);
extern void quaternion_op_Implicit_m12C97CDC5FE642C53837F73E2F9B8761D530C502 (void);
extern void quaternion__ctor_m8B8E0BD6A1BEC18AD2E0B7C5B30A4C87D5A695E9 (void);
extern void quaternion__ctor_m2F6A34CCFD1150A326CB4CE108260A8BD8B1D75F (void);
extern void quaternion_EulerXYZ_m0AD608E878C0F5683DD56F636D10C75071C50E83 (void);
extern void quaternion_EulerXYZ_m2035C2A216C0480D847181E970F4C1F2AC4972DE (void);
extern void quaternion_Equals_m58271A16502DE355CBB7C1AA07F8F670C53850CE (void);
extern void quaternion_Equals_m1E4294224F7667752144BC9428406E356350CA20 (void);
extern void quaternion_GetHashCode_m1BD3621B7DA9BF2F4A0963A4A6CF33CDAAD4A1F0 (void);
extern void quaternion_ToString_mA6A3D313DCC0B56922CA8D3F968DB3495986C7C8 (void);
extern void quaternion_ToString_mD67EE41F69A4C8353D2B2C7EDBB2B158465E1349 (void);
extern void quaternion__cctor_mB4B1769F995B8CB2E48C997119A490F8AEBA2239 (void);
extern void Random__ctor_m84FE6BF5CD2493F5460F36A914836E3D41B22D94 (void);
extern void Random_InitState_mCDAC36582272DAF59478FEA71F1307CF0E58716D (void);
extern void Random_NextInt_m794218A3A149A97A6276B37A546E381D44E0222B (void);
extern void Random_NextFloat_mBC3BB545723E36B4C517C523D62ABD1784C76DFF (void);
extern void Random_NextFloat2_m8FA06B9693CAA84F3DE6A8B7EBDC3AB99378F44E (void);
extern void Random_NextFloat3_m4D28B50E2F5BDA0E040C44D480B975CAF9640ABE (void);
extern void Random_NextFloat_m44CDBE1BE5D74FAF9C9DC1A23F3D861973D789F5 (void);
extern void Random_NextFloat2_m517B39301262965A06333D66494502560A7AD1A8 (void);
extern void Random_NextFloat3_m8A372C6C69747F8A600A4DAA2258CFA623A47186 (void);
extern void Random_NextState_m3C669E9C9DBB958FABE0879335C0732A91DA02F7 (void);
extern void uint2__ctor_mDE945EFF54FDA16335AC19E9E01A9BAE161B8D3F (void);
extern void uint2_op_Multiply_m2A4BC394328643E664AD9C17DA4BF1B0AC58E857 (void);
extern void uint2_op_Addition_m208A269559B52DBA5098C871B450E3AC857A1E4E (void);
extern void uint2_op_RightShift_mF64B7DFA49EECBF1E049BEF02033D02F8FC96A9F (void);
extern void uint2_op_BitwiseOr_mD444E2D37685955B50E3A0824D6BA8C90EF1E0C5 (void);
extern void uint2_Equals_m3F1C93E4B1C83F2142B53B222A9966479229614C (void);
extern void uint2_Equals_mDD29FD4B71CE0B814B38BA1CE90F3EF2C1782257 (void);
extern void uint2_GetHashCode_m0B3D1D91DF8C75E948C020CD260B4114D6A158B4 (void);
extern void uint2_ToString_mFD106FD9C2FC96096DE048AAD1B4B59F6B11EFD5 (void);
extern void uint2_ToString_m19B7C2EAB06A69C94317C4ADC679E3AC551277AD (void);
extern void uint3__ctor_mEFEA14BBA36F53111474B0C3C3B729061F1ACCAF (void);
extern void uint3_op_Multiply_m756859015AC9BE9CB34BACE67DF92F64EA76C9AD (void);
extern void uint3_op_Addition_mD11BEB362388E20A02C6D431C2ED912807585589 (void);
extern void uint3_op_RightShift_m804311580E841B7943BB5538963CE546A781BAC6 (void);
extern void uint3_op_BitwiseOr_m52865281597051A876380769DBF13C61AB5E8B82 (void);
extern void uint3_op_ExclusiveOr_mEFD0F65EF3400EF9E154EEF0BFF0284E1F148AA4 (void);
extern void uint3_Equals_m071EEFA66ACDE8A413C27DD0E8C989D317B52D81 (void);
extern void uint3_Equals_m02016E995E9557006CE71FEAD24C2B67E69A8A0D (void);
extern void uint3_GetHashCode_m0EFF5352F8DE8618A24717A32EFA8EB66719F56D (void);
extern void uint3_ToString_mCD235901AC027194EDB244BB9BD80A73CB6F3633 (void);
extern void uint3_ToString_m1EAAF8E74678E9D172485B76193CD1557FB8BFEE (void);
extern void uint4__ctor_m59B6A219A0285C60FCF2977679BF89C72B502008 (void);
extern void uint4_op_Multiply_mDD93D0730642A1089848321B9B0E5E923EE575ED (void);
extern void uint4_op_Addition_m391200EB43B6C4CDD219079A0D3CDFFA35B0B652 (void);
extern void uint4_Equals_mAA88D674B2411C0A7D6C25AA596320EF79B58DDB (void);
extern void uint4_Equals_m689E5D21501C5846BF031E4864E8DBB46F467386 (void);
extern void uint4_GetHashCode_m195FED91BE8D7CCE0039A8DE6B6B0BB849FBCC5A (void);
extern void uint4_ToString_mEF61205FE152AAB74331B24080C347AA829E435B (void);
extern void uint4_ToString_m3350471364AB1D6033E088C0DF789376954921E3 (void);
static Il2CppMethodPointer s_methodPointers[365] = 
{
	Il2CppEagerStaticClassConstructionAttribute__ctor_mCACE94326399F3059F318EB568BD8E45037E3139,
	bool2__ctor_m097D0D586C955D0F4E04DD5D308F0DD17297203D,
	bool2_Equals_mA73BA304B87D4C007247008330E8AE017413F727,
	bool2_Equals_mCEC91B262F6D86366DC9EA06BA9495043F21D050,
	bool2_GetHashCode_mE8ADDB566B213BA0F2C6359CDBCA8B2169F64B91,
	bool2_ToString_mBB7BC458D3C5F3427EBE7EFD5323DCDCECBB14FD,
	math_hash_m8111065B3777555BB22B9C3F88A1B31BD8CC388D,
	math_hash_m6C080FB0A7D1E2425DBBEEFF1B206E5C18F725C6,
	math_float2_m24A922BBF741AF168DD4591FC8C4CF9E67A85BAE,
	math_hash_m102FB27D63A32D75D8CB1430F32BAFDEE29BCED8,
	math_float2x2_m3C7E37DA31EE873D94E436595BC660B30FE50BA3,
	math_determinant_mEEFE10F738EA4C8F09E0BF2C7F6A5B1C786A32C0,
	math_hash_mB247638FEE355912BFED2DE534B317779199F284,
	math_float3_m4F96A74FEE1D6C85241B8E62386C5DE1C439837F,
	math_float3_mA7B7F259A567DAFC65F7808E9CEDDE655E0F6F94,
	math_float3_m7AB8F47ADB3AC275D701AF1E376438B146541493,
	math_hash_m8ECC7CFBA8D302A2A3E9468DE65D705E9C1298EB,
	math_hash_mCC0D346D77A7BAE4C16EB878E1FDF69E863A09C3,
	math_float4_m16697C284FA0C25A84F3DC3E99F3D4C306B6BFBF,
	math_float4_m8177BCC80431B291731F53C99FF25D3090AB286D,
	math_float4_mE797E50A2E9FF0935867108F0F25C0F683E47B15,
	math_float4_mE54104D60E6B9A358C75CB6F378118AB4914BFC4,
	math_hash_m53E875B2DC4324BD20573419DBE27D0F651FA4D4,
	math_float4x4_m7C95B2B93CDEE0AF483EB84446F9F06F7B1AD261,
	math_hash_m20286BA0E4D2F6DC3C7013DA713AADCFFD87D444,
	math_int2_m0A9A8113022D7954E7DA5BE5436989C2DF01650D,
	math_hash_m6B6E0FC08FCC3BC0940397676690F22B03DB4F16,
	math_hash_mC744961C63A1CA98FB11450AAFA373AFA4DE73EF,
	math_hash_mE07D6FDDFDA633E2F3A93048E26573F181ECBB45,
	math_asint_mBDED7FE966CA65F6A8ACEAEF8FD779B1B8998288,
	math_asuint_m64DA623C5CFEB8445663480384F2B1C202150EE5,
	math_asuint_m6EDCEC78D97FE38F42C86EED91C0550F5B85DFBF,
	math_asuint_m39B6AA9AB80F5EC34FBDF14A8C9289C9D9904D89,
	math_asuint_m503D1ABF19E4BA615FD8AE1BF1A2E103BBED6139,
	math_asuint_m22CC00686F9722FF2ED6330E3C0B4699C55CB1EE,
	math_asuint_m4AEE8C17FEDA05D4C77C427818D1C9EF5E31521E,
	math_asuint_mDF3C61EF6F9D9D10A1D3EB9D0075149707B461B9,
	math_aslong_mCD3846AC0EFB4901B00A20D0960C80C8CBE66366,
	math_asulong_m2CF160E23B5FF618A85C3C29B2FB1C000E40290F,
	math_asfloat_m9FA56DE5C61FCEF3DCD0675252D40DFD9C9B712F,
	math_asfloat_m20D259DAAB46464B59BD8BF5678F9D59800F70A9,
	math_asfloat_m21833833779E44053D1E1B38677D9C5D83753D28,
	math_asfloat_m7A90E1FAABD250FCEC00839D01B098BB046F7933,
	math_asdouble_m4C4CC1B9299FE33530ED375768D67B00676C31C8,
	math_asdouble_m3E7BC790C743E67EA45476AECD6D2D9A9E62E4F2,
	math_isfinite_mC7FACC44EBD8D443AA9CE0F0F47F8426EB68CDF8,
	math_isinf_m4901864832BAA489A01E23F560733ACEF6E3ED60,
	math_min_m02D43DF516544C279AF660EA4731449C82991849,
	math_min_mFBB411A5384A9CFD7787E398A6F758553D3700A9,
	math_min_mA22BCFB62A81B533821704D26BE23D8D6402C8EB,
	math_min_m54FD010BEF505D2BA1F79FC793BEB0723C329C3B,
	math_min_m68ED612C41E325FA3446050EA04D0AC0CD191558,
	math_min_m13CC8D5B7844D954C3125DD72831C693AB8A7FF5,
	math_min_m29A6A5FB36524D911D13DDB4866FF005C7BF00D5,
	math_min_m1D64D6B67B27FD9738D14BCEE6298146CB05CE00,
	math_max_m9083201D37A8ED0157B127B5878D9B7F3A2A40BE,
	math_max_m11CA4E4999B4F371CB5189F06CAD3FD0E852B2B4,
	math_max_mD9D4307218A8CFA92F9C26871E508B23C17F6395,
	math_max_mEBAE1BF7FA6B43BD0F4AE2E47FB6190041F8CE43,
	math_max_m4B454A91AE8827997609E74C4C24036BBD3CC496,
	math_max_mFD64D6399932C2D91018BA7895C06FD055E1361B,
	math_max_m247D41258606F80861E72309300DF6A3F8B50AE4,
	math_max_m8830F8721EFC73BCF991CD497115A103B86BF3BE,
	math_lerp_m58A82DB48BBA11871FFA81583C700875B3A9BC84,
	math_lerp_mA20BFB8D988B57C1CFA28047538F3B47208D1371,
	math_lerp_mF6942D2A43740A2D3924483C724F9BC900D953A2,
	math_clamp_m9EABD008C8EAD9D150062ABE724D96FA2121EE1C,
	math_clamp_mB7233FC9D6C27522014C4E6D4E056D36CE82C97E,
	math_clamp_m39FE4EA2420B8DF536A1344B16D9E39EF5B7155F,
	math_abs_mFF027629978A9039B059528ED3075D775AA0B0AB,
	math_abs_m3D9508B36B045BFE7B89C6C69AD34596264E4FE1,
	math_abs_mDF669CF3AF2C60713E8E118578461CDA050DAFD0,
	math_dot_mF673D3E5B7D267C0A8569B678D05BDCCB667D04D,
	math_dot_mE193D8880350D74CC8D63A0D53CDC5902F844AAD,
	math_dot_m20F2285F7227DC308D9CF2DCB8EAAD3E774501D4,
	math_dot_mA992F4ADC67180A7EB3850222857193CD0F6B21E,
	math_atan_m47A3F5B37A1469CA1E78FB682B6337673EB02AFE,
	math_atan_mFC948313E50230B5E4328AE5ED4075665E78A3E7,
	math_cos_m28B6228E047D552B1312CCFADB8AE95DDD94A6AF,
	math_cos_m42275E85C55A660ABC711D07B4349A82F4BBCBC4,
	math_sin_m231F847C28B88B17BDAD7F49A7A38E46DF12D3FF,
	math_sin_m43618973AB0574A29896B4479E1F72A829644A33,
	math_floor_m0FDF19C33B0B1062079FCB10FB081869AEC1FB48,
	math_floor_m49AF91133E08FD4B7A652BCC41F25EEC9DF52D07,
	math_ceil_m01FC8783CB8656774F0A793EA3BBF831F7CE19C0,
	math_ceil_mC879669549692BD4136602F9C3937B5D2B30981C,
	math_round_mA3594A8D6D29ACA14FF38F38FFBB83DF579D87B9,
	math_round_m649C0F15E3AD1A8899E68C17D02E332F5F94A054,
	math_trunc_mFB08702CED1DAFA9E112CAA94ECF59026C066112,
	math_trunc_m0B8E3445AAF1F11E3BD64086C7402BB3CE1A06C1,
	math_rcp_mED2BCEE83560EEE59CE06EBD90332CAFA9C08024,
	math_rcp_m6A26002B1F4212C525255E31010FF526C84F591D,
	math_pow_m2B2C611A37952CFB13BB0AE800A6A601A2E4A49B,
	math_log2_m07B499B0DDA692EDD9DF4780349C26EB28199156,
	math_fmod_mE5EE515D4B99F417B55111171D6100728FA68BA2,
	math_sqrt_mEF31DE7BD0179009683C5D7B0C58E6571B30CF4A,
	math_sqrt_mA3A9D5DFDF6841F8836E3ECD5D83555842383F36,
	math_rsqrt_mC67B3430EAADA7C5347E87B23859C569BC010E72,
	math_normalize_mAB67BF670979DC2A6C8B30791BFF8476FEC1B5CD,
	math_normalize_mF02431EFC9E3212E0245EFF5C13BC9DC34512399,
	math_length_m3DB47D254C8544FBB740A892B4AE2143E8F45634,
	math_length_m6A2B63D7A3B84261C2F7FCAA2CB382288A57D257,
	math_length_mDA291F159E5B088CF2EF354538EBDBC60063C9E7,
	math_length_mBC9788A14DDEC3FA5794F7F49EDD1516C5EDE4E3,
	math_lengthsq_mDC7DB2DF1C069D45CE87BC737AF59CB0094042FA,
	math_lengthsq_mC699F3F214F05B26BEBAF1B46E3AA3C00407A532,
	math_lengthsq_m246AAF09A2EA30D8FE4314442E031D9B5AFF31FF,
	math_distance_mE5E0FFDD103E710A4CB23360BFCAFD0AF2E1EFA9,
	math_distance_m72BEFBAADFC4404FADD3AD81F7EDD40E32624F4D,
	math_distancesq_m491F2EBC94F119EA8EE8EA6BB30C44BB0BEB37E3,
	math_distancesq_mA49E8B34404D0C4DB3C9D4E065CE4CA255C9770B,
	math_distancesq_m609DF85E2355430E1F4CD51CDC1971BD5F7D4AF3,
	math_cross_m4CA2DAE150C6381B0D05E8AA9E48E88CF6157180,
	math_any_m76E4E0A6EAF4782AB83EC7BE0BB7E2513791C0B0,
	math_any_mCBBE4E2611B227A8AE1A4DA7F104D779203539F9,
	math_select_m8BBDC6133225059EE5F0FCA74BA9497AD5496024,
	math_select_m85465C9438F81A86CBDA83FC4E8201D8D2A61828,
	math_sincos_m28D7C74E99CF12DE35172DC6F26C77FD4D46D1B7,
	math_countbits_m4CA1327F4995735E6AA4862E641E128312C404BD,
	math_countbits_m17808348628239FFE20276AB711D0B609F3EF6A3,
	math_lzcnt_mA6B7E71DB1B5D4CE8B67C66FF1AC4339FA368D07,
	math_lzcnt_m121BDDDEE89F5A401E2E5F0AD900D22E47C8741C,
	math_tzcnt_m85FEAD596A8E327F7B6820310B7FBD9822BA735C,
	math_tzcnt_m07FD7550AAB5D94312E99571B112D652E8230360,
	math_tzcnt_mA20C30896B5942219FA80AA79653F59629D28E78,
	math_tzcnt_mB3717EA8D7052CBC8F048C8ADB37D6C6060C5490,
	math_ceilpow2_mA00505409975D36AB3D7658687AC3BD5A26F3769,
	math_ceilpow2_m55FA5F42278E2409D11BF05A5A9E3B2A883C82CB,
	math_ceilpow2_m7941384EAF6F776691CD2601130055362B0C967C,
	math_ceillog2_m1F8130D49F06A9A97699F43FDB771782FE1A7930,
	math_floorlog2_m27DF7DB26915A7FE16A4A72ACDA15795BF95562E,
	math_radians_m0903022269E582AD877B86952497295D642036E8,
	math_csum_m0B6655875FE24428E18B97FC3F5C745E374530A3,
	math_csum_m9C15CCCED13E8ADB45EFC388D141E55091A61C1C,
	math_csum_m6A99E69A84442A729781A97F78B260223DD01D8F,
	math_csum_mE1D11CB696F7217C722D039256B183F6EEBAFF96,
	math_f16tof32_m558AB5CFC4B974A267AFB1E799C3F8D7D530B419,
	math_fold_to_uint_m9A795A00E9FC9F4E4BE563540711D2F47A9D9F28,
	math_fold_to_uint_m3BD058FD32F4D8F26960CEA718F6E2A5FAE9B3B3,
	math_float3x3_m850F2B065688B7C95009136DAA44853A04069298,
	math_mul_m4C38B3C1EB3A8DEBFD06421A2FCBE54E7CDC46EE,
	math_mul_m080515A2DCB7CCE0F4635A8E87B1635C02D72A4E,
	math_mul_mF9EE8CEC6C3D6A57A74F806EA436DA9465FCB01E,
	math_quaternion_m315B4CA2F8475CC33F3C73187F00AC8B64BBF939,
	math_mul_m3CC0941E6A3DE5718C6439421E74D7F80793F652,
	math_hash_mEE8D76145C2CD7DDEE85990255444BCC3DCC161A,
	math_uint2_m861F5F74EBBBD3DA19E84A1155320B89FF7341C3,
	math_hash_m5D21276708BFB4DEEF3498774D296FE6B14FC5B0,
	math_uint3_mC94DDA8B357EA045D5A36B81CECD0C5C223B71B0,
	math_hash_m31E070E721A961**************************,
	math_uint4_m7F6A5341327B9EF6B80C82FD1B93E4F32287A336,
	math_hash_m1A4778A79FFB5E05B04BD09B0F85EA9483D8A3CA,
	double2__ctor_m4026FE95F69FAEBD29D7092ADAA1CB845A8E859B,
	double2__ctor_m3355A4008574AE2483EAD2841176C67734F10F33,
	double2_op_Implicit_m168C031549D6C086B7C49ECA5B18C892B3112F17,
	double2_op_Subtraction_mDAD1E402F52C548544D20D62D7FA098F4F858BC8,
	double2_Equals_m3047316C17464EDC21C1A6F79DB472D5812F75DF,
	double2_Equals_mFF460810B0184AFE3BF8DC1865F306AD087FEC33,
	double2_GetHashCode_mAD6AF7BC1D935CF5B63D519D9F811A3486D609DE,
	double2_ToString_mE9B5B271B464187E1772BE62DE23A047E8C93164,
	double2_ToString_m512CFA1B15214C11C12E52771B83BB4765BDB8F6,
	float2__ctor_m3D598E2C2D173DE852F3AB157502968261383C97,
	float2__ctor_m36DFF2F3BE02DB4AC5E36F0CDFF2DB54D872F979,
	float2__ctor_m119EA382660CA825559E95AE3770C59196ED449F,
	float2_op_Implicit_mDCE8EF24B96CB48EEFA3D9C75760ECA01874C735,
	float2_op_Implicit_m9CAFCA676C0609D02F3F23A214D01DA3FF480FF4,
	float2_op_Multiply_mD97F1F12A640BE857FD483CD188E7EDF44FB19A9,
	float2_op_Multiply_m34D03129CE0D7AD665A914DE83CB749585B2455F,
	float2_op_Multiply_m9117237F9A26A1B934C1DE0A5FE5BD6EF7D3B26C,
	float2_op_Addition_m718974663A956F64D7C45D06C088550637F13693,
	float2_op_Addition_m6A1EEFBE2B92691F07967739DB3ECEEB432968AD,
	float2_op_Addition_mB2553B04CC2B7BA017644D2F33A956A56F5E334B,
	float2_op_Subtraction_m28172675A65BCFFBC8C9023BE815019E668B8380,
	float2_op_Subtraction_mEBC8F4CCCAE3FF01E674C44AE24FD7BA4B3FAB08,
	float2_op_Division_mA1BE4D539CEEC02DE7F3C3C35060D3CEF1CBEDDA,
	float2_op_Division_m4AA175CD0895AA1A50F5A73B54722CA53876EE6A,
	float2_op_Division_mE9F0EA43229DDFD549088D0F71AABF831698EE5F,
	float2_op_UnaryNegation_m63834E95475E7EF1CA1583F6A08420C3BFA34458,
	float2_Equals_m1E68B5EDCDB491FEBA657E58D6A91E05AD550CDA,
	float2_Equals_mD389D74AC9D1E4E537F95C97B79026A6C3244788,
	float2_GetHashCode_m8E40B8206F9E87300406D8DCA54F6AC88CAC4FB7,
	float2_ToString_m41C07CB0EC7D3A938DBEC3A113A0FDB29E2B98D0,
	float2_ToString_m0921A1A5C64FC14E7E808684F079B7BE29EC5AB1,
	float2_op_Implicit_mBB339033BC4FFDA8BC7A41DF06C1FA97F60115D9,
	float2_op_Implicit_m274CE0EFDC0FFD5168817614BF1A3699784DE02B,
	float2x2__ctor_m4191D97630C2FF4ABB7B21317DBD4547BB2F187B,
	float2x2__ctor_m9FEC95CA119B472C39298B2AECFD1CC6DD83691E,
	float2x2_Equals_m29387F36A0F27E8686227D47EA0677CAD1BC0942,
	float2x2_Equals_mDA00F71DC8D21B2033290BDA1DD483C134396EFD,
	float2x2_GetHashCode_m4233F3E1E849EAB3A4E0FC81F52774A1C9AA4C42,
	float2x2_ToString_m9DB46B1F9E2DE945733549ADD173B32E883B6DF0,
	float2x2_ToString_m19F5377833DC86DB067E77A46FEAAFC3E07628D4,
	float2x2__cctor_mE7DBC93D8DC4B775E51C768D25622D845E45FDF5,
	float3__ctor_mC61002CD0EC13D7C37D846D021A78C028FB80DB9,
	float3__ctor_mD111893D7A308AFFD7AC0CC410B348877E301B22,
	float3__ctor_m294ACD85A4CE5F3F9C41D30933CA52AC64660830,
	float3__ctor_m777F109CF7EBEEC78FAE5DBE52E4498CFA65E8C4,
	float3__ctor_m131368AA87C134F64DD0B5B8684C1345BEC6EE59,
	float3_op_Implicit_m495864276F56A9757CE235005D9908DBB47D8031,
	float3_op_Implicit_m0E52B9E5BF532737C8B469DA33E08C16CC53AEB3,
	float3_op_Multiply_m05E57074FBD5FAB0E72940C9CC019C41915280D7,
	float3_op_Multiply_m6E5DC552C8B0F9A180298BD9197FF47B14E0EA81,
	float3_op_Multiply_m38F52B61F8E5636955A1A6DF3A75BD0724148350,
	float3_op_Addition_mFFCF4F7457594F5EFB0678C0DE90AAD3D3F6947B,
	float3_op_Subtraction_mB6036E9849D95650D6E73DA0D179CD7B61E696F2,
	float3_op_Subtraction_m111BEEA770E140739DDC6A3410736DFF7EE32045,
	float3_op_Division_m59FB3E510B03034B8834D7D724561FB9EC4DBB81,
	float3_op_UnaryNegation_m862876969881839716CBAF9AE074FA4BFDFABDF1,
	float3_get_yxxy_mF003E86CB17AE3175090DEF37A636AD75087AF81,
	float3_get_zzyz_m78B967EBE527FA5705C0E7B38563D1E33BB992C1,
	float3_get_xyz_m720A862AA512BE0B0B1089527A43EEF2B6766BEF,
	float3_get_yzx_mDF6DE39B69C5DE384F74C0D1EC91AA0388E23535,
	float3_get_xy_mFD536022DC4F5162E2FE30328BE2418E4878349A,
	float3_get_xz_m687D2F3B82CB637649325B969C1406D86133EB30,
	float3_get_yz_m271DC12D413A6F5E411E48BB6300705BC453D7F0,
	float3_Equals_m4A47BDC70977496712F3BE7DA359E840D99C020A,
	float3_Equals_mD988046122040559D8C9EFED2DA347495D722A2C,
	float3_GetHashCode_m659801FA23576CC26412BE644FA19636BFF8A017,
	float3_ToString_m334A7FA7041C2C5D83B68231F1FDEB4037CF5DEA,
	float3_ToString_mE1BA3E91F84DDD745685C09F509410358A83D081,
	float3_op_Implicit_m9CC301DFD67EEFAA15CA05E91913E862B22326F6,
	float3_op_Implicit_mE1831A3AC179B7EB3236F8202EC8DD5CE05376AB,
	float3x3__ctor_m3AB31C9B587ABDCF15C8BF0E3A5B0158996A75ED,
	float3x3_Equals_m5AA50ACE9FA1CFD3981FD1F2F94B1C782C20ACD0,
	float3x3_Equals_m340F5B6BC1C9B1DCF517AA56077E2D3430CEFCFA,
	float3x3_GetHashCode_mCA45DB35B95265E20B9E17E363AEAC9649056705,
	float3x3_ToString_m2265642475440D69F1A8D1C2C466E1DC31F48934,
	float3x3_ToString_m42DAD14EF6EAA5DFB6F501645C325D29A6079522,
	float3x3__ctor_mF94488DFF7867CFC89648E024FA89A19F23E2FAE,
	float3x3__cctor_mDD98D4703621A506785CC046B18E86B0B5388675,
	float4__ctor_mB2F7F2D8BCE8159BEF5A0D6400499E211858ED2D,
	float4__ctor_m2A6267BB3EF610ED0F833FD07322B70EA219E9C7,
	float4__ctor_m062DCDEE8A8DFB587E88A4F1D18E7415B683CB00,
	float4__ctor_m2A21052EF06884F609D1CDA9A2C2ED84A7584345,
	float4__ctor_mDE7B1FCF1C1895705616AB2D0C0ED42422743D1D,
	float4_op_Implicit_mA889A779BA63B2654EEEF1F5CE2407DAA0E33142,
	float4_op_Multiply_m0E98338FB7DFF55B101EBCD78A8703ADB9C08667,
	float4_op_Multiply_m712573F441DA8AF0843DE2167927FB76E642B1EB,
	float4_op_Addition_m2CF2E1B2DAD4996DE3C5B6DFB90185E4CC8F0F44,
	float4_op_Subtraction_mBC40F52B8A8EF499A1AA3CC987E5935BD188B4E3,
	float4_op_Division_mA82993BB826C4799764D5B0E543D0D7776F4F8A2,
	float4_get_xyzx_m5FFC709BBF25AC7B7755AE077E4288E47089C76C,
	float4_get_yzxy_m78C60157637373AC6CB36343778B04CAA046ADDD,
	float4_get_yzxz_mE81FC33B6A0D370B2C8DB339CC5501533BB31223,
	float4_get_zxyy_m4B8D0449EEB8DD3CE5A79C96E911C70617339C4E,
	float4_get_zxyz_m91C90CDA336353253B33F0407198ACCC593EBB1B,
	float4_get_wwwx_m698BBD6517D0DCF19C3529E0E686FF430642E480,
	float4_get_wwww_mF04E8B99431D2717DEE58BDAE207EF4C7E1A0009,
	float4_get_xyz_mE6EC829F35512C7BC159047FDC134E80F3B37A06,
	float4_get_yxw_mBACC7261DBDD87BA51639CE46D9958D97B18DD05,
	float4_get_zwx_mF2EB9E380A5092E72833677BC468332F5493A4E6,
	float4_get_wzy_m38FCFC0E1F5BB656A426E039349E69B8D5AA24EF,
	float4_get_xy_m26BE8952E15131592B83837998D1EDC33FA9E6DF,
	float4_get_zw_mE9148903506B6AC51F8BA30DBDE2CD34C67107BB,
	float4_get_Item_m42C2633E42BEBFB2230FF9125BC95D13EF5DD7DF,
	float4_set_Item_m96DA990882049B7CDDE20DB9C8B3685F60278BC4,
	float4_Equals_m5E14C29D104399C399A11F52E7C2D507504AC0D1,
	float4_Equals_m4D841ABA91D26FA16B07205DC9D51612265FFAB4,
	float4_GetHashCode_m73A9ACC41256A6E98F883A72ED1EB0047947DA48,
	float4_ToString_m63852A53A6218EBA6CF7782E0E38DE0719AA6DAA,
	float4_ToString_mF0B7870047A0CB59185C85DE13ECECFAC5783C2A,
	float4_op_Implicit_m6D2091EB2CF6E0629A029A7BE9AD230F5F394CB2,
	float4_op_Implicit_m5E3AEBAF5F12155549CC051E1EEEE81DF3516E92,
	float4x4__ctor_mF0B7C823E36025A539E8024123057CAC380E97EF,
	float4x4__ctor_m89D3EDF09863C02553147CDA696E48B8D2622AC5,
	float4x4_get_Item_m91F2AB53518F231D479AF5D50DD6017F658F6828,
	float4x4_Equals_mB22F89A6EC760BECED603A00205294B0C185CCBB,
	float4x4_Equals_mD77FF5880D1151E85DC213AFF8E24B12044DB6B8,
	float4x4_GetHashCode_m260D00DFB6FDDAA4052A0893C7AEA6E5599DF9F7,
	float4x4_ToString_mB25DD7002ADF39A9117BEAF2A69EE880FB8E7584,
	float4x4_ToString_mB33825C3DB524BA41F1DF5356C61B0F8BA6A8B81,
	float4x4_op_Implicit_mCDD72C5454A8DAFB2A659484006D18D5979BE6AB,
	float4x4_op_Implicit_mC056D7C9F17B6D06E026163034B5414E56DA04B4,
	float4x4__ctor_m36D57BB3339FCFAE6FA39D35CAB4AD9010183D04,
	float4x4_TRS_m253E5B92580E420E113F8C195664D06F967EEF03,
	float4x4__cctor_m8AD96186FE409BAF9B9D69DF283AF3BBF336BE19,
	half_get_MinValue_m60E08648E62A1C135FA2437D67C33BAD32498696,
	half_Equals_m60FC2376046103BB9AB11E9AD896585BA5FC5A13,
	half_Equals_m7593A00CF2CA899C147FE9813C57A7332F4346F3,
	half_GetHashCode_m6ED49A661CFDE2082EF21678A7D559182734A812,
	half_ToString_m1D04F7DC5C8D90E2F92E614F58BDF3F015D6558C,
	half_ToString_mB95CFCA3EC40127249350792090F08EFC1B7DC01,
	int2__ctor_m452D21510717D0961119C89A72BBB8D84DCD49F4,
	int2__ctor_m1F683444787E63F08971693F833BBF8012827CE6,
	int2_op_Implicit_m8CFB0C58F0EE9CF236FC42E64F57838140AF7D46,
	int2_op_Addition_mB8210BD1CFEF218431BCFB14816FA467B196BCB7,
	int2_op_Addition_mDC6C2A91C24EE95C695A1F0179AC85A845045D2E,
	int2_op_Subtraction_mC938968ABFBC65A87958D0C460911E66345F7AAA,
	int2_op_Division_mE73B8B8DF181E932008B939A87FC6A7CB0547F75,
	int2_op_LessThan_mDA9D38A2DFECDECA7FE2934167CF14DE2A710D13,
	int2_op_RightShift_mC401366956E76AC2BB075841BF4FD16CEEF95227,
	int2_op_BitwiseOr_m0570B3FC79725BF4940B9008B64AE03D25A85190,
	int2_get_xyxy_mAD2CD0BE7639D713E52DAE9D6EE7534DF1B6E71A,
	int2_Equals_m4DB2E24953677E4E2497F1D0C4B48794257A89B6,
	int2_Equals_m6B885828097BED2665EAA565C07E9FE83627C481,
	int2_GetHashCode_mDC30EB8816F1ABC7195872B81F3EA9E8090866A6,
	int2_ToString_mC1A7775631D1F6B67B370423AF94BF60249A2466,
	int2_ToString_mB5FA23BE92C8532E53BAAA3B743D89ED6C23EE47,
	int3__ctor_mE478318DE4CA648614FEF2C1DD438C0455284BF2,
	int3_Equals_m677848DDE5FAE2DDF177465F5E38B1DAB2E39419,
	int3_Equals_mAD3F27218DB5A5341B15F177DBA5ED687A0727D1,
	int3_GetHashCode_m8AC6D1E8012392878EC4C8894FC3306D5DB2350E,
	int3_ToString_mF0D5E77289718FA92B3B386C9E956CA9F83D92FB,
	int3_ToString_mAB969F813E35B83C1B4ADC074583E3FAC5982632,
	int4__ctor_m4E8D71A09721E26F7FCCE82EA8AD699062EE6216,
	int4__ctor_mC08BC6E7709B75AEBCFDA6A26B4AB4525C1B2D26,
	int4_op_Explicit_mCBE5AD9158BC21B2E93CFB154927523284100E6A,
	int4_get_xy_m9402873A61D837606A752C019B7AB157EA4FC6C9,
	int4_get_zw_mE8F9C45028C9B218050B7DBDF61063B60F62349D,
	int4_get_Item_m2281A8F3D1D12AE2E09614009F90CEDAED6CB284,
	int4_Equals_m966659D148734F0C67F1D7F78F1B391DDB2D12B2,
	int4_Equals_mD93E26A15709372D4D3E34D8797F80C3FA01CD97,
	int4_GetHashCode_mDE43A26C805DF1BA46E907BFE67B7A74559FE8B7,
	int4_ToString_m555B1DA60EDDB2F461216E91E740D63953BA0112,
	int4_ToString_mEC239D567C5A8C38EC41A06435499AB1E887066C,
	quaternion_op_Implicit_m12C97CDC5FE642C53837F73E2F9B8761D530C502,
	quaternion__ctor_m8B8E0BD6A1BEC18AD2E0B7C5B30A4C87D5A695E9,
	quaternion__ctor_m2F6A34CCFD1150A326CB4CE108260A8BD8B1D75F,
	quaternion_EulerXYZ_m0AD608E878C0F5683DD56F636D10C75071C50E83,
	quaternion_EulerXYZ_m2035C2A216C0480D847181E970F4C1F2AC4972DE,
	quaternion_Equals_m58271A16502DE355CBB7C1AA07F8F670C53850CE,
	quaternion_Equals_m1E4294224F7667752144BC9428406E356350CA20,
	quaternion_GetHashCode_m1BD3621B7DA9BF2F4A0963A4A6CF33CDAAD4A1F0,
	quaternion_ToString_mA6A3D313DCC0B56922CA8D3F968DB3495986C7C8,
	quaternion_ToString_mD67EE41F69A4C8353D2B2C7EDBB2B158465E1349,
	quaternion__cctor_mB4B1769F995B8CB2E48C997119A490F8AEBA2239,
	Random__ctor_m84FE6BF5CD2493F5460F36A914836E3D41B22D94,
	Random_InitState_mCDAC36582272DAF59478FEA71F1307CF0E58716D,
	Random_NextInt_m794218A3A149A97A6276B37A546E381D44E0222B,
	Random_NextFloat_mBC3BB545723E36B4C517C523D62ABD1784C76DFF,
	Random_NextFloat2_m8FA06B9693CAA84F3DE6A8B7EBDC3AB99378F44E,
	Random_NextFloat3_m4D28B50E2F5BDA0E040C44D480B975CAF9640ABE,
	Random_NextFloat_m44CDBE1BE5D74FAF9C9DC1A23F3D861973D789F5,
	Random_NextFloat2_m517B39301262965A06333D66494502560A7AD1A8,
	Random_NextFloat3_m8A372C6C69747F8A600A4DAA2258CFA623A47186,
	Random_NextState_m3C669E9C9DBB958FABE0879335C0732A91DA02F7,
	uint2__ctor_mDE945EFF54FDA16335AC19E9E01A9BAE161B8D3F,
	uint2_op_Multiply_m2A4BC394328643E664AD9C17DA4BF1B0AC58E857,
	uint2_op_Addition_m208A269559B52DBA5098C871B450E3AC857A1E4E,
	uint2_op_RightShift_mF64B7DFA49EECBF1E049BEF02033D02F8FC96A9F,
	uint2_op_BitwiseOr_mD444E2D37685955B50E3A0824D6BA8C90EF1E0C5,
	uint2_Equals_m3F1C93E4B1C83F2142B53B222A9966479229614C,
	uint2_Equals_mDD29FD4B71CE0B814B38BA1CE90F3EF2C1782257,
	uint2_GetHashCode_m0B3D1D91DF8C75E948C020CD260B4114D6A158B4,
	uint2_ToString_mFD106FD9C2FC96096DE048AAD1B4B59F6B11EFD5,
	uint2_ToString_m19B7C2EAB06A69C94317C4ADC679E3AC551277AD,
	uint3__ctor_mEFEA14BBA36F53111474B0C3C3B729061F1ACCAF,
	uint3_op_Multiply_m756859015AC9BE9CB34BACE67DF92F64EA76C9AD,
	uint3_op_Addition_mD11BEB362388E20A02C6D431C2ED912807585589,
	uint3_op_RightShift_m804311580E841B7943BB5538963CE546A781BAC6,
	uint3_op_BitwiseOr_m52865281597051A876380769DBF13C61AB5E8B82,
	uint3_op_ExclusiveOr_mEFD0F65EF3400EF9E154EEF0BFF0284E1F148AA4,
	uint3_Equals_m071EEFA66ACDE8A413C27DD0E8C989D317B52D81,
	uint3_Equals_m02016E995E9557006CE71FEAD24C2B67E69A8A0D,
	uint3_GetHashCode_m0EFF5352F8DE8618A24717A32EFA8EB66719F56D,
	uint3_ToString_mCD235901AC027194EDB244BB9BD80A73CB6F3633,
	uint3_ToString_m1EAAF8E74678E9D172485B76193CD1557FB8BFEE,
	uint4__ctor_m59B6A219A0285C60FCF2977679BF89C72B502008,
	uint4_op_Multiply_mDD93D0730642A1089848321B9B0E5E923EE575ED,
	uint4_op_Addition_m391200EB43B6C4CDD219079A0D3CDFFA35B0B652,
	uint4_Equals_mAA88D674B2411C0A7D6C25AA596320EF79B58DDB,
	uint4_Equals_m689E5D21501C5846BF031E4864E8DBB46F467386,
	uint4_GetHashCode_m195FED91BE8D7CCE0039A8DE6B6B0BB849FBCC5A,
	uint4_ToString_mEF61205FE152AAB74331B24080C347AA829E435B,
	uint4_ToString_m3350471364AB1D6033E088C0DF789376954921E3,
};
extern void bool2__ctor_m097D0D586C955D0F4E04DD5D308F0DD17297203D_AdjustorThunk (void);
extern void bool2_Equals_mA73BA304B87D4C007247008330E8AE017413F727_AdjustorThunk (void);
extern void bool2_Equals_mCEC91B262F6D86366DC9EA06BA9495043F21D050_AdjustorThunk (void);
extern void bool2_GetHashCode_mE8ADDB566B213BA0F2C6359CDBCA8B2169F64B91_AdjustorThunk (void);
extern void bool2_ToString_mBB7BC458D3C5F3427EBE7EFD5323DCDCECBB14FD_AdjustorThunk (void);
extern void double2__ctor_m4026FE95F69FAEBD29D7092ADAA1CB845A8E859B_AdjustorThunk (void);
extern void double2__ctor_m3355A4008574AE2483EAD2841176C67734F10F33_AdjustorThunk (void);
extern void double2_Equals_m3047316C17464EDC21C1A6F79DB472D5812F75DF_AdjustorThunk (void);
extern void double2_Equals_mFF460810B0184AFE3BF8DC1865F306AD087FEC33_AdjustorThunk (void);
extern void double2_GetHashCode_mAD6AF7BC1D935CF5B63D519D9F811A3486D609DE_AdjustorThunk (void);
extern void double2_ToString_mE9B5B271B464187E1772BE62DE23A047E8C93164_AdjustorThunk (void);
extern void double2_ToString_m512CFA1B15214C11C12E52771B83BB4765BDB8F6_AdjustorThunk (void);
extern void float2__ctor_m3D598E2C2D173DE852F3AB157502968261383C97_AdjustorThunk (void);
extern void float2__ctor_m36DFF2F3BE02DB4AC5E36F0CDFF2DB54D872F979_AdjustorThunk (void);
extern void float2__ctor_m119EA382660CA825559E95AE3770C59196ED449F_AdjustorThunk (void);
extern void float2_Equals_m1E68B5EDCDB491FEBA657E58D6A91E05AD550CDA_AdjustorThunk (void);
extern void float2_Equals_mD389D74AC9D1E4E537F95C97B79026A6C3244788_AdjustorThunk (void);
extern void float2_GetHashCode_m8E40B8206F9E87300406D8DCA54F6AC88CAC4FB7_AdjustorThunk (void);
extern void float2_ToString_m41C07CB0EC7D3A938DBEC3A113A0FDB29E2B98D0_AdjustorThunk (void);
extern void float2_ToString_m0921A1A5C64FC14E7E808684F079B7BE29EC5AB1_AdjustorThunk (void);
extern void float2x2__ctor_m4191D97630C2FF4ABB7B21317DBD4547BB2F187B_AdjustorThunk (void);
extern void float2x2__ctor_m9FEC95CA119B472C39298B2AECFD1CC6DD83691E_AdjustorThunk (void);
extern void float2x2_Equals_m29387F36A0F27E8686227D47EA0677CAD1BC0942_AdjustorThunk (void);
extern void float2x2_Equals_mDA00F71DC8D21B2033290BDA1DD483C134396EFD_AdjustorThunk (void);
extern void float2x2_GetHashCode_m4233F3E1E849EAB3A4E0FC81F52774A1C9AA4C42_AdjustorThunk (void);
extern void float2x2_ToString_m9DB46B1F9E2DE945733549ADD173B32E883B6DF0_AdjustorThunk (void);
extern void float2x2_ToString_m19F5377833DC86DB067E77A46FEAAFC3E07628D4_AdjustorThunk (void);
extern void float3__ctor_mC61002CD0EC13D7C37D846D021A78C028FB80DB9_AdjustorThunk (void);
extern void float3__ctor_mD111893D7A308AFFD7AC0CC410B348877E301B22_AdjustorThunk (void);
extern void float3__ctor_m294ACD85A4CE5F3F9C41D30933CA52AC64660830_AdjustorThunk (void);
extern void float3__ctor_m777F109CF7EBEEC78FAE5DBE52E4498CFA65E8C4_AdjustorThunk (void);
extern void float3__ctor_m131368AA87C134F64DD0B5B8684C1345BEC6EE59_AdjustorThunk (void);
extern void float3_get_yxxy_mF003E86CB17AE3175090DEF37A636AD75087AF81_AdjustorThunk (void);
extern void float3_get_zzyz_m78B967EBE527FA5705C0E7B38563D1E33BB992C1_AdjustorThunk (void);
extern void float3_get_xyz_m720A862AA512BE0B0B1089527A43EEF2B6766BEF_AdjustorThunk (void);
extern void float3_get_yzx_mDF6DE39B69C5DE384F74C0D1EC91AA0388E23535_AdjustorThunk (void);
extern void float3_get_xy_mFD536022DC4F5162E2FE30328BE2418E4878349A_AdjustorThunk (void);
extern void float3_get_xz_m687D2F3B82CB637649325B969C1406D86133EB30_AdjustorThunk (void);
extern void float3_get_yz_m271DC12D413A6F5E411E48BB6300705BC453D7F0_AdjustorThunk (void);
extern void float3_Equals_m4A47BDC70977496712F3BE7DA359E840D99C020A_AdjustorThunk (void);
extern void float3_Equals_mD988046122040559D8C9EFED2DA347495D722A2C_AdjustorThunk (void);
extern void float3_GetHashCode_m659801FA23576CC26412BE644FA19636BFF8A017_AdjustorThunk (void);
extern void float3_ToString_m334A7FA7041C2C5D83B68231F1FDEB4037CF5DEA_AdjustorThunk (void);
extern void float3_ToString_mE1BA3E91F84DDD745685C09F509410358A83D081_AdjustorThunk (void);
extern void float3x3__ctor_m3AB31C9B587ABDCF15C8BF0E3A5B0158996A75ED_AdjustorThunk (void);
extern void float3x3_Equals_m5AA50ACE9FA1CFD3981FD1F2F94B1C782C20ACD0_AdjustorThunk (void);
extern void float3x3_Equals_m340F5B6BC1C9B1DCF517AA56077E2D3430CEFCFA_AdjustorThunk (void);
extern void float3x3_GetHashCode_mCA45DB35B95265E20B9E17E363AEAC9649056705_AdjustorThunk (void);
extern void float3x3_ToString_m2265642475440D69F1A8D1C2C466E1DC31F48934_AdjustorThunk (void);
extern void float3x3_ToString_m42DAD14EF6EAA5DFB6F501645C325D29A6079522_AdjustorThunk (void);
extern void float3x3__ctor_mF94488DFF7867CFC89648E024FA89A19F23E2FAE_AdjustorThunk (void);
extern void float4__ctor_mB2F7F2D8BCE8159BEF5A0D6400499E211858ED2D_AdjustorThunk (void);
extern void float4__ctor_m2A6267BB3EF610ED0F833FD07322B70EA219E9C7_AdjustorThunk (void);
extern void float4__ctor_m062DCDEE8A8DFB587E88A4F1D18E7415B683CB00_AdjustorThunk (void);
extern void float4__ctor_m2A21052EF06884F609D1CDA9A2C2ED84A7584345_AdjustorThunk (void);
extern void float4__ctor_mDE7B1FCF1C1895705616AB2D0C0ED42422743D1D_AdjustorThunk (void);
extern void float4_get_xyzx_m5FFC709BBF25AC7B7755AE077E4288E47089C76C_AdjustorThunk (void);
extern void float4_get_yzxy_m78C60157637373AC6CB36343778B04CAA046ADDD_AdjustorThunk (void);
extern void float4_get_yzxz_mE81FC33B6A0D370B2C8DB339CC5501533BB31223_AdjustorThunk (void);
extern void float4_get_zxyy_m4B8D0449EEB8DD3CE5A79C96E911C70617339C4E_AdjustorThunk (void);
extern void float4_get_zxyz_m91C90CDA336353253B33F0407198ACCC593EBB1B_AdjustorThunk (void);
extern void float4_get_wwwx_m698BBD6517D0DCF19C3529E0E686FF430642E480_AdjustorThunk (void);
extern void float4_get_wwww_mF04E8B99431D2717DEE58BDAE207EF4C7E1A0009_AdjustorThunk (void);
extern void float4_get_xyz_mE6EC829F35512C7BC159047FDC134E80F3B37A06_AdjustorThunk (void);
extern void float4_get_yxw_mBACC7261DBDD87BA51639CE46D9958D97B18DD05_AdjustorThunk (void);
extern void float4_get_zwx_mF2EB9E380A5092E72833677BC468332F5493A4E6_AdjustorThunk (void);
extern void float4_get_wzy_m38FCFC0E1F5BB656A426E039349E69B8D5AA24EF_AdjustorThunk (void);
extern void float4_get_xy_m26BE8952E15131592B83837998D1EDC33FA9E6DF_AdjustorThunk (void);
extern void float4_get_zw_mE9148903506B6AC51F8BA30DBDE2CD34C67107BB_AdjustorThunk (void);
extern void float4_get_Item_m42C2633E42BEBFB2230FF9125BC95D13EF5DD7DF_AdjustorThunk (void);
extern void float4_set_Item_m96DA990882049B7CDDE20DB9C8B3685F60278BC4_AdjustorThunk (void);
extern void float4_Equals_m5E14C29D104399C399A11F52E7C2D507504AC0D1_AdjustorThunk (void);
extern void float4_Equals_m4D841ABA91D26FA16B07205DC9D51612265FFAB4_AdjustorThunk (void);
extern void float4_GetHashCode_m73A9ACC41256A6E98F883A72ED1EB0047947DA48_AdjustorThunk (void);
extern void float4_ToString_m63852A53A6218EBA6CF7782E0E38DE0719AA6DAA_AdjustorThunk (void);
extern void float4_ToString_mF0B7870047A0CB59185C85DE13ECECFAC5783C2A_AdjustorThunk (void);
extern void float4x4__ctor_mF0B7C823E36025A539E8024123057CAC380E97EF_AdjustorThunk (void);
extern void float4x4__ctor_m89D3EDF09863C02553147CDA696E48B8D2622AC5_AdjustorThunk (void);
extern void float4x4_get_Item_m91F2AB53518F231D479AF5D50DD6017F658F6828_AdjustorThunk (void);
extern void float4x4_Equals_mB22F89A6EC760BECED603A00205294B0C185CCBB_AdjustorThunk (void);
extern void float4x4_Equals_mD77FF5880D1151E85DC213AFF8E24B12044DB6B8_AdjustorThunk (void);
extern void float4x4_GetHashCode_m260D00DFB6FDDAA4052A0893C7AEA6E5599DF9F7_AdjustorThunk (void);
extern void float4x4_ToString_mB25DD7002ADF39A9117BEAF2A69EE880FB8E7584_AdjustorThunk (void);
extern void float4x4_ToString_mB33825C3DB524BA41F1DF5356C61B0F8BA6A8B81_AdjustorThunk (void);
extern void float4x4__ctor_m36D57BB3339FCFAE6FA39D35CAB4AD9010183D04_AdjustorThunk (void);
extern void half_Equals_m60FC2376046103BB9AB11E9AD896585BA5FC5A13_AdjustorThunk (void);
extern void half_Equals_m7593A00CF2CA899C147FE9813C57A7332F4346F3_AdjustorThunk (void);
extern void half_GetHashCode_m6ED49A661CFDE2082EF21678A7D559182734A812_AdjustorThunk (void);
extern void half_ToString_m1D04F7DC5C8D90E2F92E614F58BDF3F015D6558C_AdjustorThunk (void);
extern void half_ToString_mB95CFCA3EC40127249350792090F08EFC1B7DC01_AdjustorThunk (void);
extern void int2__ctor_m452D21510717D0961119C89A72BBB8D84DCD49F4_AdjustorThunk (void);
extern void int2__ctor_m1F683444787E63F08971693F833BBF8012827CE6_AdjustorThunk (void);
extern void int2_get_xyxy_mAD2CD0BE7639D713E52DAE9D6EE7534DF1B6E71A_AdjustorThunk (void);
extern void int2_Equals_m4DB2E24953677E4E2497F1D0C4B48794257A89B6_AdjustorThunk (void);
extern void int2_Equals_m6B885828097BED2665EAA565C07E9FE83627C481_AdjustorThunk (void);
extern void int2_GetHashCode_mDC30EB8816F1ABC7195872B81F3EA9E8090866A6_AdjustorThunk (void);
extern void int2_ToString_mC1A7775631D1F6B67B370423AF94BF60249A2466_AdjustorThunk (void);
extern void int2_ToString_mB5FA23BE92C8532E53BAAA3B743D89ED6C23EE47_AdjustorThunk (void);
extern void int3__ctor_mE478318DE4CA648614FEF2C1DD438C0455284BF2_AdjustorThunk (void);
extern void int3_Equals_m677848DDE5FAE2DDF177465F5E38B1DAB2E39419_AdjustorThunk (void);
extern void int3_Equals_mAD3F27218DB5A5341B15F177DBA5ED687A0727D1_AdjustorThunk (void);
extern void int3_GetHashCode_m8AC6D1E8012392878EC4C8894FC3306D5DB2350E_AdjustorThunk (void);
extern void int3_ToString_mF0D5E77289718FA92B3B386C9E956CA9F83D92FB_AdjustorThunk (void);
extern void int3_ToString_mAB969F813E35B83C1B4ADC074583E3FAC5982632_AdjustorThunk (void);
extern void int4__ctor_m4E8D71A09721E26F7FCCE82EA8AD699062EE6216_AdjustorThunk (void);
extern void int4__ctor_mC08BC6E7709B75AEBCFDA6A26B4AB4525C1B2D26_AdjustorThunk (void);
extern void int4_get_xy_m9402873A61D837606A752C019B7AB157EA4FC6C9_AdjustorThunk (void);
extern void int4_get_zw_mE8F9C45028C9B218050B7DBDF61063B60F62349D_AdjustorThunk (void);
extern void int4_get_Item_m2281A8F3D1D12AE2E09614009F90CEDAED6CB284_AdjustorThunk (void);
extern void int4_Equals_m966659D148734F0C67F1D7F78F1B391DDB2D12B2_AdjustorThunk (void);
extern void int4_Equals_mD93E26A15709372D4D3E34D8797F80C3FA01CD97_AdjustorThunk (void);
extern void int4_GetHashCode_mDE43A26C805DF1BA46E907BFE67B7A74559FE8B7_AdjustorThunk (void);
extern void int4_ToString_m555B1DA60EDDB2F461216E91E740D63953BA0112_AdjustorThunk (void);
extern void int4_ToString_mEC239D567C5A8C38EC41A06435499AB1E887066C_AdjustorThunk (void);
extern void quaternion__ctor_m8B8E0BD6A1BEC18AD2E0B7C5B30A4C87D5A695E9_AdjustorThunk (void);
extern void quaternion__ctor_m2F6A34CCFD1150A326CB4CE108260A8BD8B1D75F_AdjustorThunk (void);
extern void quaternion_Equals_m58271A16502DE355CBB7C1AA07F8F670C53850CE_AdjustorThunk (void);
extern void quaternion_Equals_m1E4294224F7667752144BC9428406E356350CA20_AdjustorThunk (void);
extern void quaternion_GetHashCode_m1BD3621B7DA9BF2F4A0963A4A6CF33CDAAD4A1F0_AdjustorThunk (void);
extern void quaternion_ToString_mA6A3D313DCC0B56922CA8D3F968DB3495986C7C8_AdjustorThunk (void);
extern void quaternion_ToString_mD67EE41F69A4C8353D2B2C7EDBB2B158465E1349_AdjustorThunk (void);
extern void Random__ctor_m84FE6BF5CD2493F5460F36A914836E3D41B22D94_AdjustorThunk (void);
extern void Random_InitState_mCDAC36582272DAF59478FEA71F1307CF0E58716D_AdjustorThunk (void);
extern void Random_NextInt_m794218A3A149A97A6276B37A546E381D44E0222B_AdjustorThunk (void);
extern void Random_NextFloat_mBC3BB545723E36B4C517C523D62ABD1784C76DFF_AdjustorThunk (void);
extern void Random_NextFloat2_m8FA06B9693CAA84F3DE6A8B7EBDC3AB99378F44E_AdjustorThunk (void);
extern void Random_NextFloat3_m4D28B50E2F5BDA0E040C44D480B975CAF9640ABE_AdjustorThunk (void);
extern void Random_NextFloat_m44CDBE1BE5D74FAF9C9DC1A23F3D861973D789F5_AdjustorThunk (void);
extern void Random_NextFloat2_m517B39301262965A06333D66494502560A7AD1A8_AdjustorThunk (void);
extern void Random_NextFloat3_m8A372C6C69747F8A600A4DAA2258CFA623A47186_AdjustorThunk (void);
extern void Random_NextState_m3C669E9C9DBB958FABE0879335C0732A91DA02F7_AdjustorThunk (void);
extern void uint2__ctor_mDE945EFF54FDA16335AC19E9E01A9BAE161B8D3F_AdjustorThunk (void);
extern void uint2_Equals_m3F1C93E4B1C83F2142B53B222A9966479229614C_AdjustorThunk (void);
extern void uint2_Equals_mDD29FD4B71CE0B814B38BA1CE90F3EF2C1782257_AdjustorThunk (void);
extern void uint2_GetHashCode_m0B3D1D91DF8C75E948C020CD260B4114D6A158B4_AdjustorThunk (void);
extern void uint2_ToString_mFD106FD9C2FC96096DE048AAD1B4B59F6B11EFD5_AdjustorThunk (void);
extern void uint2_ToString_m19B7C2EAB06A69C94317C4ADC679E3AC551277AD_AdjustorThunk (void);
extern void uint3__ctor_mEFEA14BBA36F53111474B0C3C3B729061F1ACCAF_AdjustorThunk (void);
extern void uint3_Equals_m071EEFA66ACDE8A413C27DD0E8C989D317B52D81_AdjustorThunk (void);
extern void uint3_Equals_m02016E995E9557006CE71FEAD24C2B67E69A8A0D_AdjustorThunk (void);
extern void uint3_GetHashCode_m0EFF5352F8DE8618A24717A32EFA8EB66719F56D_AdjustorThunk (void);
extern void uint3_ToString_mCD235901AC027194EDB244BB9BD80A73CB6F3633_AdjustorThunk (void);
extern void uint3_ToString_m1EAAF8E74678E9D172485B76193CD1557FB8BFEE_AdjustorThunk (void);
extern void uint4__ctor_m59B6A219A0285C60FCF2977679BF89C72B502008_AdjustorThunk (void);
extern void uint4_Equals_mAA88D674B2411C0A7D6C25AA596320EF79B58DDB_AdjustorThunk (void);
extern void uint4_Equals_m689E5D21501C5846BF031E4864E8DBB46F467386_AdjustorThunk (void);
extern void uint4_GetHashCode_m195FED91BE8D7CCE0039A8DE6B6B0BB849FBCC5A_AdjustorThunk (void);
extern void uint4_ToString_mEF61205FE152AAB74331B24080C347AA829E435B_AdjustorThunk (void);
extern void uint4_ToString_m3350471364AB1D6033E088C0DF789376954921E3_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[149] = 
{
	{ 0x06000002, bool2__ctor_m097D0D586C955D0F4E04DD5D308F0DD17297203D_AdjustorThunk },
	{ 0x06000003, bool2_Equals_mA73BA304B87D4C007247008330E8AE017413F727_AdjustorThunk },
	{ 0x06000004, bool2_Equals_mCEC91B262F6D86366DC9EA06BA9495043F21D050_AdjustorThunk },
	{ 0x06000005, bool2_GetHashCode_mE8ADDB566B213BA0F2C6359CDBCA8B2169F64B91_AdjustorThunk },
	{ 0x06000006, bool2_ToString_mBB7BC458D3C5F3427EBE7EFD5323DCDCECBB14FD_AdjustorThunk },
	{ 0x06000099, double2__ctor_m4026FE95F69FAEBD29D7092ADAA1CB845A8E859B_AdjustorThunk },
	{ 0x0600009A, double2__ctor_m3355A4008574AE2483EAD2841176C67734F10F33_AdjustorThunk },
	{ 0x0600009D, double2_Equals_m3047316C17464EDC21C1A6F79DB472D5812F75DF_AdjustorThunk },
	{ 0x0600009E, double2_Equals_mFF460810B0184AFE3BF8DC1865F306AD087FEC33_AdjustorThunk },
	{ 0x0600009F, double2_GetHashCode_mAD6AF7BC1D935CF5B63D519D9F811A3486D609DE_AdjustorThunk },
	{ 0x060000A0, double2_ToString_mE9B5B271B464187E1772BE62DE23A047E8C93164_AdjustorThunk },
	{ 0x060000A1, double2_ToString_m512CFA1B15214C11C12E52771B83BB4765BDB8F6_AdjustorThunk },
	{ 0x060000A2, float2__ctor_m3D598E2C2D173DE852F3AB157502968261383C97_AdjustorThunk },
	{ 0x060000A3, float2__ctor_m36DFF2F3BE02DB4AC5E36F0CDFF2DB54D872F979_AdjustorThunk },
	{ 0x060000A4, float2__ctor_m119EA382660CA825559E95AE3770C59196ED449F_AdjustorThunk },
	{ 0x060000B3, float2_Equals_m1E68B5EDCDB491FEBA657E58D6A91E05AD550CDA_AdjustorThunk },
	{ 0x060000B4, float2_Equals_mD389D74AC9D1E4E537F95C97B79026A6C3244788_AdjustorThunk },
	{ 0x060000B5, float2_GetHashCode_m8E40B8206F9E87300406D8DCA54F6AC88CAC4FB7_AdjustorThunk },
	{ 0x060000B6, float2_ToString_m41C07CB0EC7D3A938DBEC3A113A0FDB29E2B98D0_AdjustorThunk },
	{ 0x060000B7, float2_ToString_m0921A1A5C64FC14E7E808684F079B7BE29EC5AB1_AdjustorThunk },
	{ 0x060000BA, float2x2__ctor_m4191D97630C2FF4ABB7B21317DBD4547BB2F187B_AdjustorThunk },
	{ 0x060000BB, float2x2__ctor_m9FEC95CA119B472C39298B2AECFD1CC6DD83691E_AdjustorThunk },
	{ 0x060000BC, float2x2_Equals_m29387F36A0F27E8686227D47EA0677CAD1BC0942_AdjustorThunk },
	{ 0x060000BD, float2x2_Equals_mDA00F71DC8D21B2033290BDA1DD483C134396EFD_AdjustorThunk },
	{ 0x060000BE, float2x2_GetHashCode_m4233F3E1E849EAB3A4E0FC81F52774A1C9AA4C42_AdjustorThunk },
	{ 0x060000BF, float2x2_ToString_m9DB46B1F9E2DE945733549ADD173B32E883B6DF0_AdjustorThunk },
	{ 0x060000C0, float2x2_ToString_m19F5377833DC86DB067E77A46FEAAFC3E07628D4_AdjustorThunk },
	{ 0x060000C2, float3__ctor_mC61002CD0EC13D7C37D846D021A78C028FB80DB9_AdjustorThunk },
	{ 0x060000C3, float3__ctor_mD111893D7A308AFFD7AC0CC410B348877E301B22_AdjustorThunk },
	{ 0x060000C4, float3__ctor_m294ACD85A4CE5F3F9C41D30933CA52AC64660830_AdjustorThunk },
	{ 0x060000C5, float3__ctor_m777F109CF7EBEEC78FAE5DBE52E4498CFA65E8C4_AdjustorThunk },
	{ 0x060000C6, float3__ctor_m131368AA87C134F64DD0B5B8684C1345BEC6EE59_AdjustorThunk },
	{ 0x060000D1, float3_get_yxxy_mF003E86CB17AE3175090DEF37A636AD75087AF81_AdjustorThunk },
	{ 0x060000D2, float3_get_zzyz_m78B967EBE527FA5705C0E7B38563D1E33BB992C1_AdjustorThunk },
	{ 0x060000D3, float3_get_xyz_m720A862AA512BE0B0B1089527A43EEF2B6766BEF_AdjustorThunk },
	{ 0x060000D4, float3_get_yzx_mDF6DE39B69C5DE384F74C0D1EC91AA0388E23535_AdjustorThunk },
	{ 0x060000D5, float3_get_xy_mFD536022DC4F5162E2FE30328BE2418E4878349A_AdjustorThunk },
	{ 0x060000D6, float3_get_xz_m687D2F3B82CB637649325B969C1406D86133EB30_AdjustorThunk },
	{ 0x060000D7, float3_get_yz_m271DC12D413A6F5E411E48BB6300705BC453D7F0_AdjustorThunk },
	{ 0x060000D8, float3_Equals_m4A47BDC70977496712F3BE7DA359E840D99C020A_AdjustorThunk },
	{ 0x060000D9, float3_Equals_mD988046122040559D8C9EFED2DA347495D722A2C_AdjustorThunk },
	{ 0x060000DA, float3_GetHashCode_m659801FA23576CC26412BE644FA19636BFF8A017_AdjustorThunk },
	{ 0x060000DB, float3_ToString_m334A7FA7041C2C5D83B68231F1FDEB4037CF5DEA_AdjustorThunk },
	{ 0x060000DC, float3_ToString_mE1BA3E91F84DDD745685C09F509410358A83D081_AdjustorThunk },
	{ 0x060000DF, float3x3__ctor_m3AB31C9B587ABDCF15C8BF0E3A5B0158996A75ED_AdjustorThunk },
	{ 0x060000E0, float3x3_Equals_m5AA50ACE9FA1CFD3981FD1F2F94B1C782C20ACD0_AdjustorThunk },
	{ 0x060000E1, float3x3_Equals_m340F5B6BC1C9B1DCF517AA56077E2D3430CEFCFA_AdjustorThunk },
	{ 0x060000E2, float3x3_GetHashCode_mCA45DB35B95265E20B9E17E363AEAC9649056705_AdjustorThunk },
	{ 0x060000E3, float3x3_ToString_m2265642475440D69F1A8D1C2C466E1DC31F48934_AdjustorThunk },
	{ 0x060000E4, float3x3_ToString_m42DAD14EF6EAA5DFB6F501645C325D29A6079522_AdjustorThunk },
	{ 0x060000E5, float3x3__ctor_mF94488DFF7867CFC89648E024FA89A19F23E2FAE_AdjustorThunk },
	{ 0x060000E7, float4__ctor_mB2F7F2D8BCE8159BEF5A0D6400499E211858ED2D_AdjustorThunk },
	{ 0x060000E8, float4__ctor_m2A6267BB3EF610ED0F833FD07322B70EA219E9C7_AdjustorThunk },
	{ 0x060000E9, float4__ctor_m062DCDEE8A8DFB587E88A4F1D18E7415B683CB00_AdjustorThunk },
	{ 0x060000EA, float4__ctor_m2A21052EF06884F609D1CDA9A2C2ED84A7584345_AdjustorThunk },
	{ 0x060000EB, float4__ctor_mDE7B1FCF1C1895705616AB2D0C0ED42422743D1D_AdjustorThunk },
	{ 0x060000F2, float4_get_xyzx_m5FFC709BBF25AC7B7755AE077E4288E47089C76C_AdjustorThunk },
	{ 0x060000F3, float4_get_yzxy_m78C60157637373AC6CB36343778B04CAA046ADDD_AdjustorThunk },
	{ 0x060000F4, float4_get_yzxz_mE81FC33B6A0D370B2C8DB339CC5501533BB31223_AdjustorThunk },
	{ 0x060000F5, float4_get_zxyy_m4B8D0449EEB8DD3CE5A79C96E911C70617339C4E_AdjustorThunk },
	{ 0x060000F6, float4_get_zxyz_m91C90CDA336353253B33F0407198ACCC593EBB1B_AdjustorThunk },
	{ 0x060000F7, float4_get_wwwx_m698BBD6517D0DCF19C3529E0E686FF430642E480_AdjustorThunk },
	{ 0x060000F8, float4_get_wwww_mF04E8B99431D2717DEE58BDAE207EF4C7E1A0009_AdjustorThunk },
	{ 0x060000F9, float4_get_xyz_mE6EC829F35512C7BC159047FDC134E80F3B37A06_AdjustorThunk },
	{ 0x060000FA, float4_get_yxw_mBACC7261DBDD87BA51639CE46D9958D97B18DD05_AdjustorThunk },
	{ 0x060000FB, float4_get_zwx_mF2EB9E380A5092E72833677BC468332F5493A4E6_AdjustorThunk },
	{ 0x060000FC, float4_get_wzy_m38FCFC0E1F5BB656A426E039349E69B8D5AA24EF_AdjustorThunk },
	{ 0x060000FD, float4_get_xy_m26BE8952E15131592B83837998D1EDC33FA9E6DF_AdjustorThunk },
	{ 0x060000FE, float4_get_zw_mE9148903506B6AC51F8BA30DBDE2CD34C67107BB_AdjustorThunk },
	{ 0x060000FF, float4_get_Item_m42C2633E42BEBFB2230FF9125BC95D13EF5DD7DF_AdjustorThunk },
	{ 0x06000100, float4_set_Item_m96DA990882049B7CDDE20DB9C8B3685F60278BC4_AdjustorThunk },
	{ 0x06000101, float4_Equals_m5E14C29D104399C399A11F52E7C2D507504AC0D1_AdjustorThunk },
	{ 0x06000102, float4_Equals_m4D841ABA91D26FA16B07205DC9D51612265FFAB4_AdjustorThunk },
	{ 0x06000103, float4_GetHashCode_m73A9ACC41256A6E98F883A72ED1EB0047947DA48_AdjustorThunk },
	{ 0x06000104, float4_ToString_m63852A53A6218EBA6CF7782E0E38DE0719AA6DAA_AdjustorThunk },
	{ 0x06000105, float4_ToString_mF0B7870047A0CB59185C85DE13ECECFAC5783C2A_AdjustorThunk },
	{ 0x06000108, float4x4__ctor_mF0B7C823E36025A539E8024123057CAC380E97EF_AdjustorThunk },
	{ 0x06000109, float4x4__ctor_m89D3EDF09863C02553147CDA696E48B8D2622AC5_AdjustorThunk },
	{ 0x0600010A, float4x4_get_Item_m91F2AB53518F231D479AF5D50DD6017F658F6828_AdjustorThunk },
	{ 0x0600010B, float4x4_Equals_mB22F89A6EC760BECED603A00205294B0C185CCBB_AdjustorThunk },
	{ 0x0600010C, float4x4_Equals_mD77FF5880D1151E85DC213AFF8E24B12044DB6B8_AdjustorThunk },
	{ 0x0600010D, float4x4_GetHashCode_m260D00DFB6FDDAA4052A0893C7AEA6E5599DF9F7_AdjustorThunk },
	{ 0x0600010E, float4x4_ToString_mB25DD7002ADF39A9117BEAF2A69EE880FB8E7584_AdjustorThunk },
	{ 0x0600010F, float4x4_ToString_mB33825C3DB524BA41F1DF5356C61B0F8BA6A8B81_AdjustorThunk },
	{ 0x06000112, float4x4__ctor_m36D57BB3339FCFAE6FA39D35CAB4AD9010183D04_AdjustorThunk },
	{ 0x06000116, half_Equals_m60FC2376046103BB9AB11E9AD896585BA5FC5A13_AdjustorThunk },
	{ 0x06000117, half_Equals_m7593A00CF2CA899C147FE9813C57A7332F4346F3_AdjustorThunk },
	{ 0x06000118, half_GetHashCode_m6ED49A661CFDE2082EF21678A7D559182734A812_AdjustorThunk },
	{ 0x06000119, half_ToString_m1D04F7DC5C8D90E2F92E614F58BDF3F015D6558C_AdjustorThunk },
	{ 0x0600011A, half_ToString_mB95CFCA3EC40127249350792090F08EFC1B7DC01_AdjustorThunk },
	{ 0x0600011B, int2__ctor_m452D21510717D0961119C89A72BBB8D84DCD49F4_AdjustorThunk },
	{ 0x0600011C, int2__ctor_m1F683444787E63F08971693F833BBF8012827CE6_AdjustorThunk },
	{ 0x06000125, int2_get_xyxy_mAD2CD0BE7639D713E52DAE9D6EE7534DF1B6E71A_AdjustorThunk },
	{ 0x06000126, int2_Equals_m4DB2E24953677E4E2497F1D0C4B48794257A89B6_AdjustorThunk },
	{ 0x06000127, int2_Equals_m6B885828097BED2665EAA565C07E9FE83627C481_AdjustorThunk },
	{ 0x06000128, int2_GetHashCode_mDC30EB8816F1ABC7195872B81F3EA9E8090866A6_AdjustorThunk },
	{ 0x06000129, int2_ToString_mC1A7775631D1F6B67B370423AF94BF60249A2466_AdjustorThunk },
	{ 0x0600012A, int2_ToString_mB5FA23BE92C8532E53BAAA3B743D89ED6C23EE47_AdjustorThunk },
	{ 0x0600012B, int3__ctor_mE478318DE4CA648614FEF2C1DD438C0455284BF2_AdjustorThunk },
	{ 0x0600012C, int3_Equals_m677848DDE5FAE2DDF177465F5E38B1DAB2E39419_AdjustorThunk },
	{ 0x0600012D, int3_Equals_mAD3F27218DB5A5341B15F177DBA5ED687A0727D1_AdjustorThunk },
	{ 0x0600012E, int3_GetHashCode_m8AC6D1E8012392878EC4C8894FC3306D5DB2350E_AdjustorThunk },
	{ 0x0600012F, int3_ToString_mF0D5E77289718FA92B3B386C9E956CA9F83D92FB_AdjustorThunk },
	{ 0x06000130, int3_ToString_mAB969F813E35B83C1B4ADC074583E3FAC5982632_AdjustorThunk },
	{ 0x06000131, int4__ctor_m4E8D71A09721E26F7FCCE82EA8AD699062EE6216_AdjustorThunk },
	{ 0x06000132, int4__ctor_mC08BC6E7709B75AEBCFDA6A26B4AB4525C1B2D26_AdjustorThunk },
	{ 0x06000134, int4_get_xy_m9402873A61D837606A752C019B7AB157EA4FC6C9_AdjustorThunk },
	{ 0x06000135, int4_get_zw_mE8F9C45028C9B218050B7DBDF61063B60F62349D_AdjustorThunk },
	{ 0x06000136, int4_get_Item_m2281A8F3D1D12AE2E09614009F90CEDAED6CB284_AdjustorThunk },
	{ 0x06000137, int4_Equals_m966659D148734F0C67F1D7F78F1B391DDB2D12B2_AdjustorThunk },
	{ 0x06000138, int4_Equals_mD93E26A15709372D4D3E34D8797F80C3FA01CD97_AdjustorThunk },
	{ 0x06000139, int4_GetHashCode_mDE43A26C805DF1BA46E907BFE67B7A74559FE8B7_AdjustorThunk },
	{ 0x0600013A, int4_ToString_m555B1DA60EDDB2F461216E91E740D63953BA0112_AdjustorThunk },
	{ 0x0600013B, int4_ToString_mEC239D567C5A8C38EC41A06435499AB1E887066C_AdjustorThunk },
	{ 0x0600013D, quaternion__ctor_m8B8E0BD6A1BEC18AD2E0B7C5B30A4C87D5A695E9_AdjustorThunk },
	{ 0x0600013E, quaternion__ctor_m2F6A34CCFD1150A326CB4CE108260A8BD8B1D75F_AdjustorThunk },
	{ 0x06000141, quaternion_Equals_m58271A16502DE355CBB7C1AA07F8F670C53850CE_AdjustorThunk },
	{ 0x06000142, quaternion_Equals_m1E4294224F7667752144BC9428406E356350CA20_AdjustorThunk },
	{ 0x06000143, quaternion_GetHashCode_m1BD3621B7DA9BF2F4A0963A4A6CF33CDAAD4A1F0_AdjustorThunk },
	{ 0x06000144, quaternion_ToString_mA6A3D313DCC0B56922CA8D3F968DB3495986C7C8_AdjustorThunk },
	{ 0x06000145, quaternion_ToString_mD67EE41F69A4C8353D2B2C7EDBB2B158465E1349_AdjustorThunk },
	{ 0x06000147, Random__ctor_m84FE6BF5CD2493F5460F36A914836E3D41B22D94_AdjustorThunk },
	{ 0x06000148, Random_InitState_mCDAC36582272DAF59478FEA71F1307CF0E58716D_AdjustorThunk },
	{ 0x06000149, Random_NextInt_m794218A3A149A97A6276B37A546E381D44E0222B_AdjustorThunk },
	{ 0x0600014A, Random_NextFloat_mBC3BB545723E36B4C517C523D62ABD1784C76DFF_AdjustorThunk },
	{ 0x0600014B, Random_NextFloat2_m8FA06B9693CAA84F3DE6A8B7EBDC3AB99378F44E_AdjustorThunk },
	{ 0x0600014C, Random_NextFloat3_m4D28B50E2F5BDA0E040C44D480B975CAF9640ABE_AdjustorThunk },
	{ 0x0600014D, Random_NextFloat_m44CDBE1BE5D74FAF9C9DC1A23F3D861973D789F5_AdjustorThunk },
	{ 0x0600014E, Random_NextFloat2_m517B39301262965A06333D66494502560A7AD1A8_AdjustorThunk },
	{ 0x0600014F, Random_NextFloat3_m8A372C6C69747F8A600A4DAA2258CFA623A47186_AdjustorThunk },
	{ 0x06000150, Random_NextState_m3C669E9C9DBB958FABE0879335C0732A91DA02F7_AdjustorThunk },
	{ 0x06000151, uint2__ctor_mDE945EFF54FDA16335AC19E9E01A9BAE161B8D3F_AdjustorThunk },
	{ 0x06000156, uint2_Equals_m3F1C93E4B1C83F2142B53B222A9966479229614C_AdjustorThunk },
	{ 0x06000157, uint2_Equals_mDD29FD4B71CE0B814B38BA1CE90F3EF2C1782257_AdjustorThunk },
	{ 0x06000158, uint2_GetHashCode_m0B3D1D91DF8C75E948C020CD260B4114D6A158B4_AdjustorThunk },
	{ 0x06000159, uint2_ToString_mFD106FD9C2FC96096DE048AAD1B4B59F6B11EFD5_AdjustorThunk },
	{ 0x0600015A, uint2_ToString_m19B7C2EAB06A69C94317C4ADC679E3AC551277AD_AdjustorThunk },
	{ 0x0600015B, uint3__ctor_mEFEA14BBA36F53111474B0C3C3B729061F1ACCAF_AdjustorThunk },
	{ 0x06000161, uint3_Equals_m071EEFA66ACDE8A413C27DD0E8C989D317B52D81_AdjustorThunk },
	{ 0x06000162, uint3_Equals_m02016E995E9557006CE71FEAD24C2B67E69A8A0D_AdjustorThunk },
	{ 0x06000163, uint3_GetHashCode_m0EFF5352F8DE8618A24717A32EFA8EB66719F56D_AdjustorThunk },
	{ 0x06000164, uint3_ToString_mCD235901AC027194EDB244BB9BD80A73CB6F3633_AdjustorThunk },
	{ 0x06000165, uint3_ToString_m1EAAF8E74678E9D172485B76193CD1557FB8BFEE_AdjustorThunk },
	{ 0x06000166, uint4__ctor_m59B6A219A0285C60FCF2977679BF89C72B502008_AdjustorThunk },
	{ 0x06000169, uint4_Equals_mAA88D674B2411C0A7D6C25AA596320EF79B58DDB_AdjustorThunk },
	{ 0x0600016A, uint4_Equals_m689E5D21501C5846BF031E4864E8DBB46F467386_AdjustorThunk },
	{ 0x0600016B, uint4_GetHashCode_m195FED91BE8D7CCE0039A8DE6B6B0BB849FBCC5A_AdjustorThunk },
	{ 0x0600016C, uint4_ToString_mEF61205FE152AAB74331B24080C347AA829E435B_AdjustorThunk },
	{ 0x0600016D, uint4_ToString_m3350471364AB1D6033E088C0DF789376954921E3_AdjustorThunk },
};
static const int32_t s_InvokerIndices[365] = 
{
	13298,
	4722,
	7936,
	7736,
	12996,
	13052,
	20750,
	20751,
	18897,
	20752,
	18901,
	20675,
	20753,
	16971,
	18903,
	18905,
	20754,
	20755,
	15726,
	16973,
	18910,
	18911,
	20756,
	15727,
	20757,
	18918,
	20758,
	20759,
	20760,
	20217,
	20911,
	20913,
	20915,
	20746,
	20910,
	20912,
	20914,
	20229,
	20770,
	20661,
	20669,
	20892,
	20897,
	19995,
	20003,
	19899,
	19882,
	17822,
	18237,
	17884,
	18172,
	18900,
	18907,
	17642,
	18895,
	17822,
	18920,
	18237,
	17884,
	18172,
	18900,
	18907,
	17642,
	16509,
	16972,
	16086,
	16183,
	16509,
	16086,
	20207,
	20667,
	19989,
	18179,
	18180,
	18181,
	17651,
	20667,
	20890,
	20667,
	20896,
	20667,
	20896,
	20667,
	19989,
	20667,
	19989,
	20667,
	19989,
	20667,
	19989,
	20667,
	20890,
	18172,
	20667,
	17642,
	20667,
	19989,
	20667,
	20890,
	20896,
	20674,
	20676,
	20677,
	20005,
	20674,
	20676,
	20677,
	18179,
	17651,
	18179,
	18180,
	18181,
	18907,
	19909,
	19910,
	16534,
	16976,
	16959,
	20222,
	20208,
	20207,
	20221,
	20207,
	20221,
	20208,
	20222,
	20207,
	20903,
	20234,
	20207,
	20207,
	20667,
	20762,
	20763,
	20764,
	20676,
	20669,
	20739,
	20909,
	20898,
	18908,
	18914,
	18916,
	20907,
	18924,
	20761,
	18925,
	20762,
	16977,
	20763,
	15728,
	20764,
	4750,
	10945,
	20888,
	18895,
	7937,
	7736,
	12996,
	13052,
	4489,
	5784,
	10949,
	10954,
	20891,
	20892,
	18900,
	18899,
	18898,
	18900,
	18899,
	18898,
	18900,
	18899,
	18900,
	18899,
	18898,
	20890,
	7938,
	7736,
	12996,
	13052,
	4489,
	20801,
	20889,
	5865,
	1983,
	7939,
	7736,
	12996,
	13052,
	4489,
	21355,
	2865,
	5787,
	5864,
	10823,
	10629,
	20894,
	20893,
	18907,
	18906,
	18904,
	18907,
	18907,
	18906,
	18906,
	20896,
	13316,
	13316,
	13315,
	13315,
	13314,
	13314,
	13314,
	7940,
	7736,
	12996,
	13052,
	4489,
	20812,
	20895,
	78,
	7941,
	7736,
	12996,
	13052,
	4489,
	10953,
	21355,
	1983,
	2934,
	5865,
	5866,
	10951,
	20900,
	18913,
	18912,
	18913,
	18913,
	18912,
	13316,
	13316,
	13316,
	13316,
	13316,
	13316,
	13316,
	13315,
	13315,
	13315,
	13315,
	13314,
	13314,
	9456,
	5375,
	7942,
	7736,
	12996,
	13052,
	4489,
	20899,
	20823,
	2023,
	7,
	6810,
	7943,
	7736,
	12996,
	13052,
	4489,
	20901,
	20289,
	5869,
	16974,
	21355,
	21334,
	7944,
	7736,
	12996,
	13052,
	4489,
	5266,
	10629,
	20902,
	18920,
	18919,
	18919,
	18919,
	18893,
	18919,
	18920,
	13320,
	7945,
	7736,
	12996,
	13052,
	4489,
	2642,
	7946,
	7736,
	12996,
	13052,
	4489,
	1808,
	10947,
	20904,
	13318,
	13318,
	8801,
	7947,
	7736,
	12996,
	13052,
	4489,
	20905,
	1983,
	10947,
	20906,
	16975,
	7949,
	7736,
	12996,
	13052,
	4489,
	21355,
	10891,
	10891,
	4112,
	13195,
	13314,
	13315,
	4614,
	5905,
	5906,
	13261,
	5821,
	18928,
	18928,
	18927,
	18926,
	7950,
	7736,
	12996,
	13052,
	4489,
	2877,
	18931,
	18931,
	18930,
	18929,
	18931,
	7951,
	7736,
	12996,
	13052,
	4489,
	1996,
	18933,
	18933,
	7952,
	7736,
	12996,
	13052,
	4489,
};
static TypeDefinitionIndex s_staticConstructorsToRunAtStartup[19] = 
{
	12563,
	12566,
	12568,
	12570,
	12571,
	12573,
	12574,
	12576,
	12577,
	12578,
	12580,
	12582,
	12584,
	12585,
	12586,
	12588,
	12590,
	12592,
	0,
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_Unity_Mathematics_CodeGenModule;
const Il2CppCodeGenModule g_Unity_Mathematics_CodeGenModule = 
{
	"Unity.Mathematics.dll",
	365,
	s_methodPointers,
	149,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	NULL,
	s_staticConstructorsToRunAtStartup,
	NULL,
	NULL,
};

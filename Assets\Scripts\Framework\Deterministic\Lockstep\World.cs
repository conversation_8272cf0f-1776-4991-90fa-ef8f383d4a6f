
namespace RGame.Framework
{
    public class World
    {
        public uint CurTick
        {
            get;
            private set;
        }


        private ServiceContainer _serviceContainer = null;

        public void Init(ServiceContainer serviceContainer)
        {
            _serviceContainer = serviceContainer;
        }

        public void FrameBegin()
        {
            foreach (IService service in _serviceContainer.GetAllServices())
            {
                service.FrameBegin();
            }
        }

        public void Step()
        {
            CurTick++;

            foreach (IService service in _serviceContainer.GetAllServices())
            {
                service.Step();
            }
        }

        public void FrameEnd()
        {
            foreach (IService service in _serviceContainer.GetAllServices())
            {
                service.FrameEnd();
            }
        }

        public void Reset()
        {
            CurTick = 0;
        }
    }
}

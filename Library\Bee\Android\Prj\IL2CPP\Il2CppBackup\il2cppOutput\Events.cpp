﻿#include "pch-cpp.hpp"

#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include <limits>



struct List_1_tDB72209F35D56F62A287633F9450978E90B90987;
struct List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D;
struct List_1_t403F6918E06CE77A4CC149AA4602E84EC3619DC8;
struct List_1_t0D8986210159B0978FAA5E0FC48117F05BD036C8;
struct ActionU5BU5D_tF6161335A0A12A221AB081D78725C8AB6FE506D2;
struct ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031;
struct DelegateU5BU5D_tC5AB7E8F745616680F337909D3A8E6C722CDF771;
struct ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918;
struct UnsubscriberU5BU5D_tCD1BBAE5C8B5E72DC7E3E14571F021B8B58834A0;
struct OnResetCallbackU5BU5D_t4FD3E1DDCA4808D750D632629A469A0E7D341581;
struct Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07;
struct AsyncCallback_t7FEF460CBDCFB9C5FA2EF776984778B9A4145F4C;
struct Delegate_t;
struct DelegateData_t9B286B493293CD2D23A5B2B5EF0E5B1324C2B77E;
struct EventBusRegistry_tA1D5325A5D0BAA4114C95BF53A1F6A2549B081AD;
struct IAsyncResult_t7B9B5A0ECB35DCEC31B8A8122C37D687369253B5;
struct MethodInfo_t;
struct String_t;
struct UnitySourceGeneratedAssemblyMonoScriptTypes_v1_t86051C51DBB716E3B34FCDAD871A9C3139CB70B0;
struct Unsubscriber_tA10207CB5BE682290BAE7CA7AEA26752826C011F;
struct UnsubscriberPool_tECBE47E24CC047552D783042AC2F12481C6F43BF;
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915;
struct OnResetCallback_t1A3F3E34E43B86041C7D7BA5D112278BCE960EFE;

IL2CPP_EXTERN_C RuntimeClass* ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* EventBusRegistry_tA1D5325A5D0BAA4114C95BF53A1F6A2549B081AD_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* List_1_t0D8986210159B0978FAA5E0FC48117F05BD036C8_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* List_1_t403F6918E06CE77A4CC149AA4602E84EC3619DC8_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* List_1_tDB72209F35D56F62A287633F9450978E90B90987_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* OnResetCallback_t1A3F3E34E43B86041C7D7BA5D112278BCE960EFE_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* UnsubscriberPool_tECBE47E24CC047552D783042AC2F12481C6F43BF_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Unsubscriber_tA10207CB5BE682290BAE7CA7AEA26752826C011F_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeField* U3CPrivateImplementationDetailsU3E_t91E583536B2268208F70E7FAA714AAD91B153FCB____4940CE2FC81BAA193881F275E8E0FF3930C96EE1287AB3B4977DB3D788D5CD3E_FieldInfo_var;
IL2CPP_EXTERN_C RuntimeField* U3CPrivateImplementationDetailsU3E_t91E583536B2268208F70E7FAA714AAD91B153FCB____A1E2EF64923F23F9F4F8F59AEF30FCFA9D0125F14441CA22E55EF48D795F0CBE_FieldInfo_var;
IL2CPP_EXTERN_C const RuntimeMethod* Enumerator_Dispose_m38649B9DEF7D4F646A462973BE165EB686A8D7C0_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Enumerator_Dispose_m7AF54658D389DA447BBE4C9F2D82278217B0972B_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Enumerator_MoveNext_m415B7DAE20047A9CD1D3DA11207C12461534EBD0_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Enumerator_MoveNext_m6096A4A06C95B18044A650D3E9AE2AA3D2ECA568_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Enumerator_get_Current_m5CDD305956FDEFA478FF8F1F8D44992132542261_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Enumerator_get_Current_m876044D2A7B93E91C18C8883C9A8F198D05FBFF3_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1_Add_m2FDC5D3C2EDA7288094659E1EBD89DA82EFF6E82_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1_Add_mF4795A21A5EC5078085EBED3A9FA8AB8AE63018D_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1_Clear_m344AD90676A608EA37B9DF93050BA9F80C23D17E_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1_Clear_m7CC2FD761D87EC9E86CDEA42F042B9CC19422B22_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1_GetEnumerator_m55D6C6FE67818245AB7970AF894ED4FF6864DA88_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1_GetEnumerator_mB6C244AAEBAD19DF16131057B7A205817A226254_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1_RemoveAt_mECCA0666FDE900F66E216A7926393D8BA022BB91_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1_Remove_m81309EBB00EF191E34458121484976F1C6B7516C_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1__ctor_m73E77591F8861A8B325656F5AB8B5FF6C87AC661_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1__ctor_mBFD6DF02E045EDF3C322E2112C922457016C8212_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1__ctor_mF6BA420407FB4D0D9A4B8A07B3D9AB12ECDE6F65_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1_get_Count_m2455A764B71057857832D6C464690B863F4B9E7F_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1_get_Item_mFDA769C4F98C964654A8867429CADE6FD34779AA_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* UnsubscriberPool_Clear_m67EFA629F192EE2A1DDB9F9F1DEA6EC64F141481_RuntimeMethod_var;
struct Delegate_t_marshaled_com;
struct Delegate_t_marshaled_pinvoke;

struct ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031;
struct DelegateU5BU5D_tC5AB7E8F745616680F337909D3A8E6C722CDF771;
struct ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918;

IL2CPP_EXTERN_C_BEGIN
IL2CPP_EXTERN_C_END

#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
struct U3CModuleU3E_t40A518237738C2961421A01FCCC653CA5E8DB4C1 
{
};
struct List_1_tDB72209F35D56F62A287633F9450978E90B90987  : public RuntimeObject
{
	ActionU5BU5D_tF6161335A0A12A221AB081D78725C8AB6FE506D2* ____items;
	int32_t ____size;
	int32_t ____version;
	RuntimeObject* ____syncRoot;
};
struct List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D  : public RuntimeObject
{
	ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* ____items;
	int32_t ____size;
	int32_t ____version;
	RuntimeObject* ____syncRoot;
};
struct List_1_t403F6918E06CE77A4CC149AA4602E84EC3619DC8  : public RuntimeObject
{
	UnsubscriberU5BU5D_tCD1BBAE5C8B5E72DC7E3E14571F021B8B58834A0* ____items;
	int32_t ____size;
	int32_t ____version;
	RuntimeObject* ____syncRoot;
};
struct List_1_t0D8986210159B0978FAA5E0FC48117F05BD036C8  : public RuntimeObject
{
	OnResetCallbackU5BU5D_t4FD3E1DDCA4808D750D632629A469A0E7D341581* ____items;
	int32_t ____size;
	int32_t ____version;
	RuntimeObject* ____syncRoot;
};
struct U3CPrivateImplementationDetailsU3E_t91E583536B2268208F70E7FAA714AAD91B153FCB  : public RuntimeObject
{
};
struct EventBusRegistry_tA1D5325A5D0BAA4114C95BF53A1F6A2549B081AD  : public RuntimeObject
{
};
struct UnitySourceGeneratedAssemblyMonoScriptTypes_v1_t86051C51DBB716E3B34FCDAD871A9C3139CB70B0  : public RuntimeObject
{
};
struct Unsubscriber_tA10207CB5BE682290BAE7CA7AEA26752826C011F  : public RuntimeObject
{
	List_1_tDB72209F35D56F62A287633F9450978E90B90987* ____unsubscribers;
};
struct UnsubscriberPool_tECBE47E24CC047552D783042AC2F12481C6F43BF  : public RuntimeObject
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F  : public RuntimeObject
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_pinvoke
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_com
{
};
struct Enumerator_t3787AC8C42D500C005E2D239B9F7650C1E44A58D 
{
	List_1_tDB72209F35D56F62A287633F9450978E90B90987* ____list;
	int32_t ____index;
	int32_t ____version;
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ____current;
};
struct Enumerator_t9473BAB568A27E2339D48C1F91319E0F6D244D7A 
{
	List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D* ____list;
	int32_t ____index;
	int32_t ____version;
	RuntimeObject* ____current;
};
struct Enumerator_t9FCE726F3E5ABBA96E7F4432D93A08B441FC8AAA 
{
	List_1_t0D8986210159B0978FAA5E0FC48117F05BD036C8* ____list;
	int32_t ____index;
	int32_t ____version;
	OnResetCallback_t1A3F3E34E43B86041C7D7BA5D112278BCE960EFE* ____current;
};
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22 
{
	bool ___m_value;
};
struct Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3 
{
	uint8_t ___m_value;
};
struct Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C 
{
	int32_t ___m_value;
};
struct IntPtr_t 
{
	void* ___m_value;
};
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915 
{
	union
	{
		struct
		{
		};
		uint8_t Void_t4861ACF8F4594C3437BB48B6E56783494B843915__padding[1];
	};
};
#pragma pack(push, tp, 1)
struct __StaticArrayInitTypeSizeU3D118_tF2BFCFEAE3BE10B5010481DC17EC8BBD0A14AC82 
{
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D118_tF2BFCFEAE3BE10B5010481DC17EC8BBD0A14AC82__padding[118];
	};
};
#pragma pack(pop, tp)
#pragma pack(push, tp, 1)
struct __StaticArrayInitTypeSizeU3D61_t67715AA3263BF48176BD7F9ADBEBC0C72BAA4993 
{
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D61_t67715AA3263BF48176BD7F9ADBEBC0C72BAA4993__padding[61];
	};
};
#pragma pack(pop, tp)
struct MonoScriptData_t3CF209F27DD42C89E3EA7B9AA8BC5F5BD42C7855 
{
	ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* ___FilePathsData;
	ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* ___TypesData;
	int32_t ___TotalTypes;
	int32_t ___TotalFiles;
	bool ___IsEditorOnly;
};
struct MonoScriptData_t3CF209F27DD42C89E3EA7B9AA8BC5F5BD42C7855_marshaled_pinvoke
{
	Il2CppSafeArray* ___FilePathsData;
	Il2CppSafeArray* ___TypesData;
	int32_t ___TotalTypes;
	int32_t ___TotalFiles;
	int32_t ___IsEditorOnly;
};
struct MonoScriptData_t3CF209F27DD42C89E3EA7B9AA8BC5F5BD42C7855_marshaled_com
{
	Il2CppSafeArray* ___FilePathsData;
	Il2CppSafeArray* ___TypesData;
	int32_t ___TotalTypes;
	int32_t ___TotalFiles;
	int32_t ___IsEditorOnly;
};
struct Delegate_t  : public RuntimeObject
{
	intptr_t ___method_ptr;
	intptr_t ___invoke_impl;
	RuntimeObject* ___m_target;
	intptr_t ___method;
	intptr_t ___delegate_trampoline;
	intptr_t ___extra_arg;
	intptr_t ___method_code;
	intptr_t ___interp_method;
	intptr_t ___interp_invoke_impl;
	MethodInfo_t* ___method_info;
	MethodInfo_t* ___original_method_info;
	DelegateData_t9B286B493293CD2D23A5B2B5EF0E5B1324C2B77E* ___data;
	bool ___method_is_virtual;
};
struct Delegate_t_marshaled_pinvoke
{
	intptr_t ___method_ptr;
	intptr_t ___invoke_impl;
	Il2CppIUnknown* ___m_target;
	intptr_t ___method;
	intptr_t ___delegate_trampoline;
	intptr_t ___extra_arg;
	intptr_t ___method_code;
	intptr_t ___interp_method;
	intptr_t ___interp_invoke_impl;
	MethodInfo_t* ___method_info;
	MethodInfo_t* ___original_method_info;
	DelegateData_t9B286B493293CD2D23A5B2B5EF0E5B1324C2B77E* ___data;
	int32_t ___method_is_virtual;
};
struct Delegate_t_marshaled_com
{
	intptr_t ___method_ptr;
	intptr_t ___invoke_impl;
	Il2CppIUnknown* ___m_target;
	intptr_t ___method;
	intptr_t ___delegate_trampoline;
	intptr_t ___extra_arg;
	intptr_t ___method_code;
	intptr_t ___interp_method;
	intptr_t ___interp_invoke_impl;
	MethodInfo_t* ___method_info;
	MethodInfo_t* ___original_method_info;
	DelegateData_t9B286B493293CD2D23A5B2B5EF0E5B1324C2B77E* ___data;
	int32_t ___method_is_virtual;
};
struct RuntimeFieldHandle_t6E4C45B6D2EA12FC99185805A7E77527899B25C5 
{
	intptr_t ___value;
};
struct MulticastDelegate_t  : public Delegate_t
{
	DelegateU5BU5D_tC5AB7E8F745616680F337909D3A8E6C722CDF771* ___delegates;
};
struct MulticastDelegate_t_marshaled_pinvoke : public Delegate_t_marshaled_pinvoke
{
	Delegate_t_marshaled_pinvoke** ___delegates;
};
struct MulticastDelegate_t_marshaled_com : public Delegate_t_marshaled_com
{
	Delegate_t_marshaled_com** ___delegates;
};
struct Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07  : public MulticastDelegate_t
{
};
struct AsyncCallback_t7FEF460CBDCFB9C5FA2EF776984778B9A4145F4C  : public MulticastDelegate_t
{
};
struct OnResetCallback_t1A3F3E34E43B86041C7D7BA5D112278BCE960EFE  : public MulticastDelegate_t
{
};
struct List_1_tDB72209F35D56F62A287633F9450978E90B90987_StaticFields
{
	ActionU5BU5D_tF6161335A0A12A221AB081D78725C8AB6FE506D2* ___s_emptyArray;
};
struct List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D_StaticFields
{
	ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* ___s_emptyArray;
};
struct List_1_t403F6918E06CE77A4CC149AA4602E84EC3619DC8_StaticFields
{
	UnsubscriberU5BU5D_tCD1BBAE5C8B5E72DC7E3E14571F021B8B58834A0* ___s_emptyArray;
};
struct List_1_t0D8986210159B0978FAA5E0FC48117F05BD036C8_StaticFields
{
	OnResetCallbackU5BU5D_t4FD3E1DDCA4808D750D632629A469A0E7D341581* ___s_emptyArray;
};
struct U3CPrivateImplementationDetailsU3E_t91E583536B2268208F70E7FAA714AAD91B153FCB_StaticFields
{
	__StaticArrayInitTypeSizeU3D61_t67715AA3263BF48176BD7F9ADBEBC0C72BAA4993 ___4940CE2FC81BAA193881F275E8E0FF3930C96EE1287AB3B4977DB3D788D5CD3E;
	__StaticArrayInitTypeSizeU3D118_tF2BFCFEAE3BE10B5010481DC17EC8BBD0A14AC82 ___A1E2EF64923F23F9F4F8F59AEF30FCFA9D0125F14441CA22E55EF48D795F0CBE;
};
struct EventBusRegistry_tA1D5325A5D0BAA4114C95BF53A1F6A2549B081AD_StaticFields
{
	List_1_t0D8986210159B0978FAA5E0FC48117F05BD036C8* ____resetCallbacks;
};
struct UnsubscriberPool_tECBE47E24CC047552D783042AC2F12481C6F43BF_StaticFields
{
	List_1_t403F6918E06CE77A4CC149AA4602E84EC3619DC8* ____pool;
};
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_StaticFields
{
	String_t* ___TrueString;
	String_t* ___FalseString;
};
struct IntPtr_t_StaticFields
{
	intptr_t ___Zero;
};
#ifdef __clang__
#pragma clang diagnostic pop
#endif
struct ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031  : public RuntimeArray
{
	ALIGN_FIELD (8) uint8_t m_Items[1];

	inline uint8_t GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline uint8_t* GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, uint8_t value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
	}
	inline uint8_t GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline uint8_t* GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, uint8_t value)
	{
		m_Items[index] = value;
	}
};
struct DelegateU5BU5D_tC5AB7E8F745616680F337909D3A8E6C722CDF771  : public RuntimeArray
{
	ALIGN_FIELD (8) Delegate_t* m_Items[1];

	inline Delegate_t* GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline Delegate_t** GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, Delegate_t* value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
	inline Delegate_t* GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline Delegate_t** GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, Delegate_t* value)
	{
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
};
struct ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918  : public RuntimeArray
{
	ALIGN_FIELD (8) RuntimeObject* m_Items[1];

	inline RuntimeObject* GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline RuntimeObject** GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, RuntimeObject* value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
	inline RuntimeObject* GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline RuntimeObject** GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, RuntimeObject* value)
	{
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)m_Items + index, (void*)value);
	}
};


IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Enumerator_t9473BAB568A27E2339D48C1F91319E0F6D244D7A List_1_GetEnumerator_mD8294A7FA2BEB1929487127D476F8EC1CDC23BFC_gshared (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Enumerator_Dispose_mD9DC3E3C3697830A4823047AB29A77DBBB5ED419_gshared (Enumerator_t9473BAB568A27E2339D48C1F91319E0F6D244D7A* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR RuntimeObject* Enumerator_get_Current_m6330F15D18EE4F547C05DF9BF83C5EB710376027_gshared_inline (Enumerator_t9473BAB568A27E2339D48C1F91319E0F6D244D7A* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Enumerator_MoveNext_mE921CC8F29FBBDE7CC3209A0ED0D921D58D00BCB_gshared (Enumerator_t9473BAB568A27E2339D48C1F91319E0F6D244D7A* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void List_1_Clear_m16C1F2C61FED5955F10EB36BC1CB2DF34B128994_gshared_inline (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void List_1__ctor_m7F078BB342729BDF11327FD89D7872265328F690_gshared (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D* __this, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void List_1_Add_mEBCF994CC3814631017F46A387B1A192ED6C85C7_gshared_inline (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D* __this, RuntimeObject* ___0_item, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool List_1_Remove_m4DFA48F4CEB9169601E75FC28517C5C06EFA5AD7_gshared (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D* __this, RuntimeObject* ___0_item, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t List_1_get_Count_m4407E4C389F22B8CEC282C15D56516658746C383_gshared_inline (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* List_1_get_Item_m33561245D64798C2AB07584C0EC4F240E4839A38_gshared (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D* __this, int32_t ___0_index, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void List_1_RemoveAt_m54F62297ADEE4D4FDA697F49ED807BF901201B54_gshared (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D* __this, int32_t ___0_index, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_NO_INLINE IL2CPP_METHOD_ATTR void List_1_AddWithResize_m79A9BF770BEF9C06BE40D5401E55E375F2726CC4_gshared (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D* __this, RuntimeObject* ___0_item, const RuntimeMethod* method) ;

IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RuntimeHelpers_InitializeArray_m751372AA3F24FBF6DA9B9D687CBFA2DE436CAB9B (RuntimeArray* ___0_array, RuntimeFieldHandle_t6E4C45B6D2EA12FC99185805A7E77527899B25C5 ___1_fldHandle, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2 (RuntimeObject* __this, const RuntimeMethod* method) ;
inline Enumerator_t3787AC8C42D500C005E2D239B9F7650C1E44A58D List_1_GetEnumerator_mB6C244AAEBAD19DF16131057B7A205817A226254 (List_1_tDB72209F35D56F62A287633F9450978E90B90987* __this, const RuntimeMethod* method)
{
	return ((  Enumerator_t3787AC8C42D500C005E2D239B9F7650C1E44A58D (*) (List_1_tDB72209F35D56F62A287633F9450978E90B90987*, const RuntimeMethod*))List_1_GetEnumerator_mD8294A7FA2BEB1929487127D476F8EC1CDC23BFC_gshared)(__this, method);
}
inline void Enumerator_Dispose_m7AF54658D389DA447BBE4C9F2D82278217B0972B (Enumerator_t3787AC8C42D500C005E2D239B9F7650C1E44A58D* __this, const RuntimeMethod* method)
{
	((  void (*) (Enumerator_t3787AC8C42D500C005E2D239B9F7650C1E44A58D*, const RuntimeMethod*))Enumerator_Dispose_mD9DC3E3C3697830A4823047AB29A77DBBB5ED419_gshared)(__this, method);
}
inline Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* Enumerator_get_Current_m5CDD305956FDEFA478FF8F1F8D44992132542261_inline (Enumerator_t3787AC8C42D500C005E2D239B9F7650C1E44A58D* __this, const RuntimeMethod* method)
{
	return ((  Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* (*) (Enumerator_t3787AC8C42D500C005E2D239B9F7650C1E44A58D*, const RuntimeMethod*))Enumerator_get_Current_m6330F15D18EE4F547C05DF9BF83C5EB710376027_gshared_inline)(__this, method);
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Action_Invoke_m7126A54DACA72B845424072887B5F3A51FC3808E_inline (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* __this, const RuntimeMethod* method) ;
inline bool Enumerator_MoveNext_m6096A4A06C95B18044A650D3E9AE2AA3D2ECA568 (Enumerator_t3787AC8C42D500C005E2D239B9F7650C1E44A58D* __this, const RuntimeMethod* method)
{
	return ((  bool (*) (Enumerator_t3787AC8C42D500C005E2D239B9F7650C1E44A58D*, const RuntimeMethod*))Enumerator_MoveNext_mE921CC8F29FBBDE7CC3209A0ED0D921D58D00BCB_gshared)(__this, method);
}
inline void List_1_Clear_m344AD90676A608EA37B9DF93050BA9F80C23D17E_inline (List_1_tDB72209F35D56F62A287633F9450978E90B90987* __this, const RuntimeMethod* method)
{
	((  void (*) (List_1_tDB72209F35D56F62A287633F9450978E90B90987*, const RuntimeMethod*))List_1_Clear_m16C1F2C61FED5955F10EB36BC1CB2DF34B128994_gshared_inline)(__this, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void UnsubscriberPool_Release_m9C0E8092F933B52EFA6099F495DEEB34AB039F74 (Unsubscriber_tA10207CB5BE682290BAE7CA7AEA26752826C011F* ___0_handle, const RuntimeMethod* method) ;
inline void List_1__ctor_mBFD6DF02E045EDF3C322E2112C922457016C8212 (List_1_tDB72209F35D56F62A287633F9450978E90B90987* __this, const RuntimeMethod* method)
{
	((  void (*) (List_1_tDB72209F35D56F62A287633F9450978E90B90987*, const RuntimeMethod*))List_1__ctor_m7F078BB342729BDF11327FD89D7872265328F690_gshared)(__this, method);
}
inline void List_1_Add_mF4795A21A5EC5078085EBED3A9FA8AB8AE63018D_inline (List_1_t0D8986210159B0978FAA5E0FC48117F05BD036C8* __this, OnResetCallback_t1A3F3E34E43B86041C7D7BA5D112278BCE960EFE* ___0_item, const RuntimeMethod* method)
{
	((  void (*) (List_1_t0D8986210159B0978FAA5E0FC48117F05BD036C8*, OnResetCallback_t1A3F3E34E43B86041C7D7BA5D112278BCE960EFE*, const RuntimeMethod*))List_1_Add_mEBCF994CC3814631017F46A387B1A192ED6C85C7_gshared_inline)(__this, ___0_item, method);
}
inline bool List_1_Remove_m81309EBB00EF191E34458121484976F1C6B7516C (List_1_t0D8986210159B0978FAA5E0FC48117F05BD036C8* __this, OnResetCallback_t1A3F3E34E43B86041C7D7BA5D112278BCE960EFE* ___0_item, const RuntimeMethod* method)
{
	return ((  bool (*) (List_1_t0D8986210159B0978FAA5E0FC48117F05BD036C8*, OnResetCallback_t1A3F3E34E43B86041C7D7BA5D112278BCE960EFE*, const RuntimeMethod*))List_1_Remove_m4DFA48F4CEB9169601E75FC28517C5C06EFA5AD7_gshared)(__this, ___0_item, method);
}
inline Enumerator_t9FCE726F3E5ABBA96E7F4432D93A08B441FC8AAA List_1_GetEnumerator_m55D6C6FE67818245AB7970AF894ED4FF6864DA88 (List_1_t0D8986210159B0978FAA5E0FC48117F05BD036C8* __this, const RuntimeMethod* method)
{
	return ((  Enumerator_t9FCE726F3E5ABBA96E7F4432D93A08B441FC8AAA (*) (List_1_t0D8986210159B0978FAA5E0FC48117F05BD036C8*, const RuntimeMethod*))List_1_GetEnumerator_mD8294A7FA2BEB1929487127D476F8EC1CDC23BFC_gshared)(__this, method);
}
inline void Enumerator_Dispose_m38649B9DEF7D4F646A462973BE165EB686A8D7C0 (Enumerator_t9FCE726F3E5ABBA96E7F4432D93A08B441FC8AAA* __this, const RuntimeMethod* method)
{
	((  void (*) (Enumerator_t9FCE726F3E5ABBA96E7F4432D93A08B441FC8AAA*, const RuntimeMethod*))Enumerator_Dispose_mD9DC3E3C3697830A4823047AB29A77DBBB5ED419_gshared)(__this, method);
}
inline OnResetCallback_t1A3F3E34E43B86041C7D7BA5D112278BCE960EFE* Enumerator_get_Current_m876044D2A7B93E91C18C8883C9A8F198D05FBFF3_inline (Enumerator_t9FCE726F3E5ABBA96E7F4432D93A08B441FC8AAA* __this, const RuntimeMethod* method)
{
	return ((  OnResetCallback_t1A3F3E34E43B86041C7D7BA5D112278BCE960EFE* (*) (Enumerator_t9FCE726F3E5ABBA96E7F4432D93A08B441FC8AAA*, const RuntimeMethod*))Enumerator_get_Current_m6330F15D18EE4F547C05DF9BF83C5EB710376027_gshared_inline)(__this, method);
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void OnResetCallback_Invoke_mEB3B40B366C17A01388AF539F1E22251A7C1B3E9_inline (OnResetCallback_t1A3F3E34E43B86041C7D7BA5D112278BCE960EFE* __this, const RuntimeMethod* method) ;
inline bool Enumerator_MoveNext_m415B7DAE20047A9CD1D3DA11207C12461534EBD0 (Enumerator_t9FCE726F3E5ABBA96E7F4432D93A08B441FC8AAA* __this, const RuntimeMethod* method)
{
	return ((  bool (*) (Enumerator_t9FCE726F3E5ABBA96E7F4432D93A08B441FC8AAA*, const RuntimeMethod*))Enumerator_MoveNext_mE921CC8F29FBBDE7CC3209A0ED0D921D58D00BCB_gshared)(__this, method);
}
inline void List_1__ctor_m73E77591F8861A8B325656F5AB8B5FF6C87AC661 (List_1_t0D8986210159B0978FAA5E0FC48117F05BD036C8* __this, const RuntimeMethod* method)
{
	((  void (*) (List_1_t0D8986210159B0978FAA5E0FC48117F05BD036C8*, const RuntimeMethod*))List_1__ctor_m7F078BB342729BDF11327FD89D7872265328F690_gshared)(__this, method);
}
inline void List_1__ctor_mF6BA420407FB4D0D9A4B8A07B3D9AB12ECDE6F65 (List_1_t403F6918E06CE77A4CC149AA4602E84EC3619DC8* __this, const RuntimeMethod* method)
{
	((  void (*) (List_1_t403F6918E06CE77A4CC149AA4602E84EC3619DC8*, const RuntimeMethod*))List_1__ctor_m7F078BB342729BDF11327FD89D7872265328F690_gshared)(__this, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void OnResetCallback__ctor_m2EDB8669DF8246B4ECBD3CA8BD2151DC70B2FF8C (OnResetCallback_t1A3F3E34E43B86041C7D7BA5D112278BCE960EFE* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void EventBusRegistry_RegisterResetCallback_m6C7853D22467C435203CECBE703D561E8B56ACAA (OnResetCallback_t1A3F3E34E43B86041C7D7BA5D112278BCE960EFE* ___0_callback, const RuntimeMethod* method) ;
inline int32_t List_1_get_Count_m2455A764B71057857832D6C464690B863F4B9E7F_inline (List_1_t403F6918E06CE77A4CC149AA4602E84EC3619DC8* __this, const RuntimeMethod* method)
{
	return ((  int32_t (*) (List_1_t403F6918E06CE77A4CC149AA4602E84EC3619DC8*, const RuntimeMethod*))List_1_get_Count_m4407E4C389F22B8CEC282C15D56516658746C383_gshared_inline)(__this, method);
}
inline Unsubscriber_tA10207CB5BE682290BAE7CA7AEA26752826C011F* List_1_get_Item_mFDA769C4F98C964654A8867429CADE6FD34779AA (List_1_t403F6918E06CE77A4CC149AA4602E84EC3619DC8* __this, int32_t ___0_index, const RuntimeMethod* method)
{
	return ((  Unsubscriber_tA10207CB5BE682290BAE7CA7AEA26752826C011F* (*) (List_1_t403F6918E06CE77A4CC149AA4602E84EC3619DC8*, int32_t, const RuntimeMethod*))List_1_get_Item_m33561245D64798C2AB07584C0EC4F240E4839A38_gshared)(__this, ___0_index, method);
}
inline void List_1_RemoveAt_mECCA0666FDE900F66E216A7926393D8BA022BB91 (List_1_t403F6918E06CE77A4CC149AA4602E84EC3619DC8* __this, int32_t ___0_index, const RuntimeMethod* method)
{
	((  void (*) (List_1_t403F6918E06CE77A4CC149AA4602E84EC3619DC8*, int32_t, const RuntimeMethod*))List_1_RemoveAt_m54F62297ADEE4D4FDA697F49ED807BF901201B54_gshared)(__this, ___0_index, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Unsubscriber__ctor_m11B27CCC64445E9582A3F7F98B147AED3A31F344 (Unsubscriber_tA10207CB5BE682290BAE7CA7AEA26752826C011F* __this, const RuntimeMethod* method) ;
inline void List_1_Add_m2FDC5D3C2EDA7288094659E1EBD89DA82EFF6E82_inline (List_1_t403F6918E06CE77A4CC149AA4602E84EC3619DC8* __this, Unsubscriber_tA10207CB5BE682290BAE7CA7AEA26752826C011F* ___0_item, const RuntimeMethod* method)
{
	((  void (*) (List_1_t403F6918E06CE77A4CC149AA4602E84EC3619DC8*, Unsubscriber_tA10207CB5BE682290BAE7CA7AEA26752826C011F*, const RuntimeMethod*))List_1_Add_mEBCF994CC3814631017F46A387B1A192ED6C85C7_gshared_inline)(__this, ___0_item, method);
}
inline void List_1_Clear_m7CC2FD761D87EC9E86CDEA42F042B9CC19422B22_inline (List_1_t403F6918E06CE77A4CC149AA4602E84EC3619DC8* __this, const RuntimeMethod* method)
{
	((  void (*) (List_1_t403F6918E06CE77A4CC149AA4602E84EC3619DC8*, const RuntimeMethod*))List_1_Clear_m16C1F2C61FED5955F10EB36BC1CB2DF34B128994_gshared_inline)(__this, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Array_Clear_m50BAA3751899858B097D3FF2ED31F284703FE5CB (RuntimeArray* ___0_array, int32_t ___1_index, int32_t ___2_length, const RuntimeMethod* method) ;
inline void List_1_AddWithResize_m79A9BF770BEF9C06BE40D5401E55E375F2726CC4 (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D* __this, RuntimeObject* ___0_item, const RuntimeMethod* method)
{
	((  void (*) (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D*, RuntimeObject*, const RuntimeMethod*))List_1_AddWithResize_m79A9BF770BEF9C06BE40D5401E55E375F2726CC4_gshared)(__this, ___0_item, method);
}
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR MonoScriptData_t3CF209F27DD42C89E3EA7B9AA8BC5F5BD42C7855 UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_mB44254F75E2A952C73A8E207FF900809E76D09AC (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CPrivateImplementationDetailsU3E_t91E583536B2268208F70E7FAA714AAD91B153FCB____4940CE2FC81BAA193881F275E8E0FF3930C96EE1287AB3B4977DB3D788D5CD3E_FieldInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CPrivateImplementationDetailsU3E_t91E583536B2268208F70E7FAA714AAD91B153FCB____A1E2EF64923F23F9F4F8F59AEF30FCFA9D0125F14441CA22E55EF48D795F0CBE_FieldInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	MonoScriptData_t3CF209F27DD42C89E3EA7B9AA8BC5F5BD42C7855 V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		il2cpp_codegen_initobj((&V_0), sizeof(MonoScriptData_t3CF209F27DD42C89E3EA7B9AA8BC5F5BD42C7855));
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_0 = (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)SZArrayNew(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031_il2cpp_TypeInfo_var, (uint32_t)((int32_t)61));
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_1 = L_0;
		RuntimeFieldHandle_t6E4C45B6D2EA12FC99185805A7E77527899B25C5 L_2 = { reinterpret_cast<intptr_t> (U3CPrivateImplementationDetailsU3E_t91E583536B2268208F70E7FAA714AAD91B153FCB____4940CE2FC81BAA193881F275E8E0FF3930C96EE1287AB3B4977DB3D788D5CD3E_FieldInfo_var) };
		RuntimeHelpers_InitializeArray_m751372AA3F24FBF6DA9B9D687CBFA2DE436CAB9B((RuntimeArray*)L_1, L_2, NULL);
		(&V_0)->___FilePathsData = L_1;
		Il2CppCodeGenWriteBarrier((void**)(&(&V_0)->___FilePathsData), (void*)L_1);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_3 = (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)SZArrayNew(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031_il2cpp_TypeInfo_var, (uint32_t)((int32_t)118));
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_4 = L_3;
		RuntimeFieldHandle_t6E4C45B6D2EA12FC99185805A7E77527899B25C5 L_5 = { reinterpret_cast<intptr_t> (U3CPrivateImplementationDetailsU3E_t91E583536B2268208F70E7FAA714AAD91B153FCB____A1E2EF64923F23F9F4F8F59AEF30FCFA9D0125F14441CA22E55EF48D795F0CBE_FieldInfo_var) };
		RuntimeHelpers_InitializeArray_m751372AA3F24FBF6DA9B9D687CBFA2DE436CAB9B((RuntimeArray*)L_4, L_5, NULL);
		(&V_0)->___TypesData = L_4;
		Il2CppCodeGenWriteBarrier((void**)(&(&V_0)->___TypesData), (void*)L_4);
		(&V_0)->___TotalFiles = 1;
		(&V_0)->___TotalTypes = 5;
		(&V_0)->___IsEditorOnly = (bool)0;
		MonoScriptData_t3CF209F27DD42C89E3EA7B9AA8BC5F5BD42C7855 L_6 = V_0;
		return L_6;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_m6C71DB95393219594C0809438F3543C15F8E93E3 (UnitySourceGeneratedAssemblyMonoScriptTypes_v1_t86051C51DBB716E3B34FCDAD871A9C3139CB70B0* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C void MonoScriptData_t3CF209F27DD42C89E3EA7B9AA8BC5F5BD42C7855_marshal_pinvoke(const MonoScriptData_t3CF209F27DD42C89E3EA7B9AA8BC5F5BD42C7855& unmarshaled, MonoScriptData_t3CF209F27DD42C89E3EA7B9AA8BC5F5BD42C7855_marshaled_pinvoke& marshaled)
{
	marshaled.___FilePathsData = il2cpp_codegen_com_marshal_safe_array(IL2CPP_VT_I1, unmarshaled.___FilePathsData);
	marshaled.___TypesData = il2cpp_codegen_com_marshal_safe_array(IL2CPP_VT_I1, unmarshaled.___TypesData);
	marshaled.___TotalTypes = unmarshaled.___TotalTypes;
	marshaled.___TotalFiles = unmarshaled.___TotalFiles;
	marshaled.___IsEditorOnly = static_cast<int32_t>(unmarshaled.___IsEditorOnly);
}
IL2CPP_EXTERN_C void MonoScriptData_t3CF209F27DD42C89E3EA7B9AA8BC5F5BD42C7855_marshal_pinvoke_back(const MonoScriptData_t3CF209F27DD42C89E3EA7B9AA8BC5F5BD42C7855_marshaled_pinvoke& marshaled, MonoScriptData_t3CF209F27DD42C89E3EA7B9AA8BC5F5BD42C7855& unmarshaled)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	unmarshaled.___FilePathsData = (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I1, Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, marshaled.___FilePathsData);
	Il2CppCodeGenWriteBarrier((void**)(&unmarshaled.___FilePathsData), (void*)(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I1, Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, marshaled.___FilePathsData));
	unmarshaled.___TypesData = (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I1, Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, marshaled.___TypesData);
	Il2CppCodeGenWriteBarrier((void**)(&unmarshaled.___TypesData), (void*)(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I1, Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, marshaled.___TypesData));
	int32_t unmarshaledTotalTypes_temp_2 = 0;
	unmarshaledTotalTypes_temp_2 = marshaled.___TotalTypes;
	unmarshaled.___TotalTypes = unmarshaledTotalTypes_temp_2;
	int32_t unmarshaledTotalFiles_temp_3 = 0;
	unmarshaledTotalFiles_temp_3 = marshaled.___TotalFiles;
	unmarshaled.___TotalFiles = unmarshaledTotalFiles_temp_3;
	bool unmarshaledIsEditorOnly_temp_4 = false;
	unmarshaledIsEditorOnly_temp_4 = static_cast<bool>(marshaled.___IsEditorOnly);
	unmarshaled.___IsEditorOnly = unmarshaledIsEditorOnly_temp_4;
}
IL2CPP_EXTERN_C void MonoScriptData_t3CF209F27DD42C89E3EA7B9AA8BC5F5BD42C7855_marshal_pinvoke_cleanup(MonoScriptData_t3CF209F27DD42C89E3EA7B9AA8BC5F5BD42C7855_marshaled_pinvoke& marshaled)
{
	il2cpp_codegen_com_destroy_safe_array(marshaled.___FilePathsData);
	marshaled.___FilePathsData = NULL;
	il2cpp_codegen_com_destroy_safe_array(marshaled.___TypesData);
	marshaled.___TypesData = NULL;
}
IL2CPP_EXTERN_C void MonoScriptData_t3CF209F27DD42C89E3EA7B9AA8BC5F5BD42C7855_marshal_com(const MonoScriptData_t3CF209F27DD42C89E3EA7B9AA8BC5F5BD42C7855& unmarshaled, MonoScriptData_t3CF209F27DD42C89E3EA7B9AA8BC5F5BD42C7855_marshaled_com& marshaled)
{
	marshaled.___FilePathsData = il2cpp_codegen_com_marshal_safe_array(IL2CPP_VT_I1, unmarshaled.___FilePathsData);
	marshaled.___TypesData = il2cpp_codegen_com_marshal_safe_array(IL2CPP_VT_I1, unmarshaled.___TypesData);
	marshaled.___TotalTypes = unmarshaled.___TotalTypes;
	marshaled.___TotalFiles = unmarshaled.___TotalFiles;
	marshaled.___IsEditorOnly = static_cast<int32_t>(unmarshaled.___IsEditorOnly);
}
IL2CPP_EXTERN_C void MonoScriptData_t3CF209F27DD42C89E3EA7B9AA8BC5F5BD42C7855_marshal_com_back(const MonoScriptData_t3CF209F27DD42C89E3EA7B9AA8BC5F5BD42C7855_marshaled_com& marshaled, MonoScriptData_t3CF209F27DD42C89E3EA7B9AA8BC5F5BD42C7855& unmarshaled)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	unmarshaled.___FilePathsData = (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I1, Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, marshaled.___FilePathsData);
	Il2CppCodeGenWriteBarrier((void**)(&unmarshaled.___FilePathsData), (void*)(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I1, Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, marshaled.___FilePathsData));
	unmarshaled.___TypesData = (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I1, Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, marshaled.___TypesData);
	Il2CppCodeGenWriteBarrier((void**)(&unmarshaled.___TypesData), (void*)(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I1, Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, marshaled.___TypesData));
	int32_t unmarshaledTotalTypes_temp_2 = 0;
	unmarshaledTotalTypes_temp_2 = marshaled.___TotalTypes;
	unmarshaled.___TotalTypes = unmarshaledTotalTypes_temp_2;
	int32_t unmarshaledTotalFiles_temp_3 = 0;
	unmarshaledTotalFiles_temp_3 = marshaled.___TotalFiles;
	unmarshaled.___TotalFiles = unmarshaledTotalFiles_temp_3;
	bool unmarshaledIsEditorOnly_temp_4 = false;
	unmarshaledIsEditorOnly_temp_4 = static_cast<bool>(marshaled.___IsEditorOnly);
	unmarshaled.___IsEditorOnly = unmarshaledIsEditorOnly_temp_4;
}
IL2CPP_EXTERN_C void MonoScriptData_t3CF209F27DD42C89E3EA7B9AA8BC5F5BD42C7855_marshal_com_cleanup(MonoScriptData_t3CF209F27DD42C89E3EA7B9AA8BC5F5BD42C7855_marshaled_com& marshaled)
{
	il2cpp_codegen_com_destroy_safe_array(marshaled.___FilePathsData);
	marshaled.___FilePathsData = NULL;
	il2cpp_codegen_com_destroy_safe_array(marshaled.___TypesData);
	marshaled.___TypesData = NULL;
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Unsubscriber_Dispose_mCCD048F3A4DFFB9391E74BBBF5CC03F61E93EDC0 (Unsubscriber_tA10207CB5BE682290BAE7CA7AEA26752826C011F* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Enumerator_Dispose_m7AF54658D389DA447BBE4C9F2D82278217B0972B_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Enumerator_MoveNext_m6096A4A06C95B18044A650D3E9AE2AA3D2ECA568_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Enumerator_get_Current_m5CDD305956FDEFA478FF8F1F8D44992132542261_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_Clear_m344AD90676A608EA37B9DF93050BA9F80C23D17E_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_GetEnumerator_mB6C244AAEBAD19DF16131057B7A205817A226254_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&UnsubscriberPool_tECBE47E24CC047552D783042AC2F12481C6F43BF_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	Enumerator_t3787AC8C42D500C005E2D239B9F7650C1E44A58D V_0;
	memset((&V_0), 0, sizeof(V_0));
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* G_B4_0 = NULL;
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* G_B3_0 = NULL;
	{
		List_1_tDB72209F35D56F62A287633F9450978E90B90987* L_0 = __this->____unsubscribers;
		NullCheck(L_0);
		Enumerator_t3787AC8C42D500C005E2D239B9F7650C1E44A58D L_1;
		L_1 = List_1_GetEnumerator_mB6C244AAEBAD19DF16131057B7A205817A226254(L_0, List_1_GetEnumerator_mB6C244AAEBAD19DF16131057B7A205817A226254_RuntimeMethod_var);
		V_0 = L_1;
	}
	{
		auto __finallyBlock = il2cpp::utils::Finally([&]
		{

FINALLY_002b:
			{
				Enumerator_Dispose_m7AF54658D389DA447BBE4C9F2D82278217B0972B((&V_0), Enumerator_Dispose_m7AF54658D389DA447BBE4C9F2D82278217B0972B_RuntimeMethod_var);
				return;
			}
		});
		try
		{
			{
				goto IL_0020_1;
			}

IL_000e_1:
			{
				Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_2;
				L_2 = Enumerator_get_Current_m5CDD305956FDEFA478FF8F1F8D44992132542261_inline((&V_0), Enumerator_get_Current_m5CDD305956FDEFA478FF8F1F8D44992132542261_RuntimeMethod_var);
				Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_3 = L_2;
				if (L_3)
				{
					G_B4_0 = L_3;
					goto IL_001b_1;
				}
				G_B3_0 = L_3;
			}
			{
				goto IL_0020_1;
			}

IL_001b_1:
			{
				NullCheck(G_B4_0);
				Action_Invoke_m7126A54DACA72B845424072887B5F3A51FC3808E_inline(G_B4_0, NULL);
			}

IL_0020_1:
			{
				bool L_4;
				L_4 = Enumerator_MoveNext_m6096A4A06C95B18044A650D3E9AE2AA3D2ECA568((&V_0), Enumerator_MoveNext_m6096A4A06C95B18044A650D3E9AE2AA3D2ECA568_RuntimeMethod_var);
				if (L_4)
				{
					goto IL_000e_1;
				}
			}
			{
				goto IL_0039;
			}
		}
		catch(Il2CppExceptionWrapper& e)
		{
			__finallyBlock.StoreException(e.ex);
		}
	}

IL_0039:
	{
		List_1_tDB72209F35D56F62A287633F9450978E90B90987* L_5 = __this->____unsubscribers;
		NullCheck(L_5);
		List_1_Clear_m344AD90676A608EA37B9DF93050BA9F80C23D17E_inline(L_5, List_1_Clear_m344AD90676A608EA37B9DF93050BA9F80C23D17E_RuntimeMethod_var);
		il2cpp_codegen_runtime_class_init_inline(UnsubscriberPool_tECBE47E24CC047552D783042AC2F12481C6F43BF_il2cpp_TypeInfo_var);
		UnsubscriberPool_Release_m9C0E8092F933B52EFA6099F495DEEB34AB039F74(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Unsubscriber__ctor_m11B27CCC64445E9582A3F7F98B147AED3A31F344 (Unsubscriber_tA10207CB5BE682290BAE7CA7AEA26752826C011F* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1__ctor_mBFD6DF02E045EDF3C322E2112C922457016C8212_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_tDB72209F35D56F62A287633F9450978E90B90987_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		List_1_tDB72209F35D56F62A287633F9450978E90B90987* L_0 = (List_1_tDB72209F35D56F62A287633F9450978E90B90987*)il2cpp_codegen_object_new(List_1_tDB72209F35D56F62A287633F9450978E90B90987_il2cpp_TypeInfo_var);
		List_1__ctor_mBFD6DF02E045EDF3C322E2112C922457016C8212(L_0, List_1__ctor_mBFD6DF02E045EDF3C322E2112C922457016C8212_RuntimeMethod_var);
		__this->____unsubscribers = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&__this->____unsubscribers), (void*)L_0);
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void EventBusRegistry_RegisterResetCallback_m6C7853D22467C435203CECBE703D561E8B56ACAA (OnResetCallback_t1A3F3E34E43B86041C7D7BA5D112278BCE960EFE* ___0_callback, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&EventBusRegistry_tA1D5325A5D0BAA4114C95BF53A1F6A2549B081AD_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_Add_mF4795A21A5EC5078085EBED3A9FA8AB8AE63018D_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		il2cpp_codegen_runtime_class_init_inline(EventBusRegistry_tA1D5325A5D0BAA4114C95BF53A1F6A2549B081AD_il2cpp_TypeInfo_var);
		List_1_t0D8986210159B0978FAA5E0FC48117F05BD036C8* L_0 = ((EventBusRegistry_tA1D5325A5D0BAA4114C95BF53A1F6A2549B081AD_StaticFields*)il2cpp_codegen_static_fields_for(EventBusRegistry_tA1D5325A5D0BAA4114C95BF53A1F6A2549B081AD_il2cpp_TypeInfo_var))->____resetCallbacks;
		OnResetCallback_t1A3F3E34E43B86041C7D7BA5D112278BCE960EFE* L_1 = ___0_callback;
		NullCheck(L_0);
		List_1_Add_mF4795A21A5EC5078085EBED3A9FA8AB8AE63018D_inline(L_0, L_1, List_1_Add_mF4795A21A5EC5078085EBED3A9FA8AB8AE63018D_RuntimeMethod_var);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void EventBusRegistry_UnregisterResetCallback_m342BA1B17E1933E9FEC244F0F8DB3D93548DD789 (OnResetCallback_t1A3F3E34E43B86041C7D7BA5D112278BCE960EFE* ___0_callback, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&EventBusRegistry_tA1D5325A5D0BAA4114C95BF53A1F6A2549B081AD_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_Remove_m81309EBB00EF191E34458121484976F1C6B7516C_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		il2cpp_codegen_runtime_class_init_inline(EventBusRegistry_tA1D5325A5D0BAA4114C95BF53A1F6A2549B081AD_il2cpp_TypeInfo_var);
		List_1_t0D8986210159B0978FAA5E0FC48117F05BD036C8* L_0 = ((EventBusRegistry_tA1D5325A5D0BAA4114C95BF53A1F6A2549B081AD_StaticFields*)il2cpp_codegen_static_fields_for(EventBusRegistry_tA1D5325A5D0BAA4114C95BF53A1F6A2549B081AD_il2cpp_TypeInfo_var))->____resetCallbacks;
		OnResetCallback_t1A3F3E34E43B86041C7D7BA5D112278BCE960EFE* L_1 = ___0_callback;
		NullCheck(L_0);
		bool L_2;
		L_2 = List_1_Remove_m81309EBB00EF191E34458121484976F1C6B7516C(L_0, L_1, List_1_Remove_m81309EBB00EF191E34458121484976F1C6B7516C_RuntimeMethod_var);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void EventBusRegistry_Reset_m6A7C6FD1E9397CD00AB7EBF83F98A7A8F5A26A67 (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Enumerator_Dispose_m38649B9DEF7D4F646A462973BE165EB686A8D7C0_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Enumerator_MoveNext_m415B7DAE20047A9CD1D3DA11207C12461534EBD0_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Enumerator_get_Current_m876044D2A7B93E91C18C8883C9A8F198D05FBFF3_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&EventBusRegistry_tA1D5325A5D0BAA4114C95BF53A1F6A2549B081AD_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_GetEnumerator_m55D6C6FE67818245AB7970AF894ED4FF6864DA88_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	Enumerator_t9FCE726F3E5ABBA96E7F4432D93A08B441FC8AAA V_0;
	memset((&V_0), 0, sizeof(V_0));
	OnResetCallback_t1A3F3E34E43B86041C7D7BA5D112278BCE960EFE* G_B4_0 = NULL;
	OnResetCallback_t1A3F3E34E43B86041C7D7BA5D112278BCE960EFE* G_B3_0 = NULL;
	{
		il2cpp_codegen_runtime_class_init_inline(EventBusRegistry_tA1D5325A5D0BAA4114C95BF53A1F6A2549B081AD_il2cpp_TypeInfo_var);
		List_1_t0D8986210159B0978FAA5E0FC48117F05BD036C8* L_0 = ((EventBusRegistry_tA1D5325A5D0BAA4114C95BF53A1F6A2549B081AD_StaticFields*)il2cpp_codegen_static_fields_for(EventBusRegistry_tA1D5325A5D0BAA4114C95BF53A1F6A2549B081AD_il2cpp_TypeInfo_var))->____resetCallbacks;
		NullCheck(L_0);
		Enumerator_t9FCE726F3E5ABBA96E7F4432D93A08B441FC8AAA L_1;
		L_1 = List_1_GetEnumerator_m55D6C6FE67818245AB7970AF894ED4FF6864DA88(L_0, List_1_GetEnumerator_m55D6C6FE67818245AB7970AF894ED4FF6864DA88_RuntimeMethod_var);
		V_0 = L_1;
	}
	{
		auto __finallyBlock = il2cpp::utils::Finally([&]
		{

FINALLY_002a:
			{
				Enumerator_Dispose_m38649B9DEF7D4F646A462973BE165EB686A8D7C0((&V_0), Enumerator_Dispose_m38649B9DEF7D4F646A462973BE165EB686A8D7C0_RuntimeMethod_var);
				return;
			}
		});
		try
		{
			{
				goto IL_001f_1;
			}

IL_000d_1:
			{
				OnResetCallback_t1A3F3E34E43B86041C7D7BA5D112278BCE960EFE* L_2;
				L_2 = Enumerator_get_Current_m876044D2A7B93E91C18C8883C9A8F198D05FBFF3_inline((&V_0), Enumerator_get_Current_m876044D2A7B93E91C18C8883C9A8F198D05FBFF3_RuntimeMethod_var);
				OnResetCallback_t1A3F3E34E43B86041C7D7BA5D112278BCE960EFE* L_3 = L_2;
				if (L_3)
				{
					G_B4_0 = L_3;
					goto IL_001a_1;
				}
				G_B3_0 = L_3;
			}
			{
				goto IL_001f_1;
			}

IL_001a_1:
			{
				NullCheck(G_B4_0);
				OnResetCallback_Invoke_mEB3B40B366C17A01388AF539F1E22251A7C1B3E9_inline(G_B4_0, NULL);
			}

IL_001f_1:
			{
				bool L_4;
				L_4 = Enumerator_MoveNext_m415B7DAE20047A9CD1D3DA11207C12461534EBD0((&V_0), Enumerator_MoveNext_m415B7DAE20047A9CD1D3DA11207C12461534EBD0_RuntimeMethod_var);
				if (L_4)
				{
					goto IL_000d_1;
				}
			}
			{
				goto IL_0038;
			}
		}
		catch(Il2CppExceptionWrapper& e)
		{
			__finallyBlock.StoreException(e.ex);
		}
	}

IL_0038:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void EventBusRegistry__ctor_mBBD18537BAD3FCD98B80E87FA93BC13DD7A73463 (EventBusRegistry_tA1D5325A5D0BAA4114C95BF53A1F6A2549B081AD* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void EventBusRegistry__cctor_m9E5D450ABEC4D23E265CDD5A6374A5D374F46BD3 (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&EventBusRegistry_tA1D5325A5D0BAA4114C95BF53A1F6A2549B081AD_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1__ctor_m73E77591F8861A8B325656F5AB8B5FF6C87AC661_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_t0D8986210159B0978FAA5E0FC48117F05BD036C8_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		List_1_t0D8986210159B0978FAA5E0FC48117F05BD036C8* L_0 = (List_1_t0D8986210159B0978FAA5E0FC48117F05BD036C8*)il2cpp_codegen_object_new(List_1_t0D8986210159B0978FAA5E0FC48117F05BD036C8_il2cpp_TypeInfo_var);
		List_1__ctor_m73E77591F8861A8B325656F5AB8B5FF6C87AC661(L_0, List_1__ctor_m73E77591F8861A8B325656F5AB8B5FF6C87AC661_RuntimeMethod_var);
		((EventBusRegistry_tA1D5325A5D0BAA4114C95BF53A1F6A2549B081AD_StaticFields*)il2cpp_codegen_static_fields_for(EventBusRegistry_tA1D5325A5D0BAA4114C95BF53A1F6A2549B081AD_il2cpp_TypeInfo_var))->____resetCallbacks = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&((EventBusRegistry_tA1D5325A5D0BAA4114C95BF53A1F6A2549B081AD_StaticFields*)il2cpp_codegen_static_fields_for(EventBusRegistry_tA1D5325A5D0BAA4114C95BF53A1F6A2549B081AD_il2cpp_TypeInfo_var))->____resetCallbacks), (void*)L_0);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
void OnResetCallback_Invoke_mEB3B40B366C17A01388AF539F1E22251A7C1B3E9_Multicast(OnResetCallback_t1A3F3E34E43B86041C7D7BA5D112278BCE960EFE* __this, const RuntimeMethod* method)
{
	il2cpp_array_size_t length = __this->___delegates->max_length;
	Delegate_t** delegatesToInvoke = reinterpret_cast<Delegate_t**>(__this->___delegates->GetAddressAtUnchecked(0));
	for (il2cpp_array_size_t i = 0; i < length; i++)
	{
		OnResetCallback_t1A3F3E34E43B86041C7D7BA5D112278BCE960EFE* currentDelegate = reinterpret_cast<OnResetCallback_t1A3F3E34E43B86041C7D7BA5D112278BCE960EFE*>(delegatesToInvoke[i]);
		typedef void (*FunctionPointerType) (RuntimeObject*, const RuntimeMethod*);
		((FunctionPointerType)currentDelegate->___invoke_impl)((Il2CppObject*)currentDelegate->___method_code, reinterpret_cast<RuntimeMethod*>(currentDelegate->___method));
	}
}
void OnResetCallback_Invoke_mEB3B40B366C17A01388AF539F1E22251A7C1B3E9_OpenInst(OnResetCallback_t1A3F3E34E43B86041C7D7BA5D112278BCE960EFE* __this, const RuntimeMethod* method)
{
	typedef void (*FunctionPointerType) (const RuntimeMethod*);
	((FunctionPointerType)__this->___method_ptr)(method);
}
void OnResetCallback_Invoke_mEB3B40B366C17A01388AF539F1E22251A7C1B3E9_OpenStatic(OnResetCallback_t1A3F3E34E43B86041C7D7BA5D112278BCE960EFE* __this, const RuntimeMethod* method)
{
	typedef void (*FunctionPointerType) (const RuntimeMethod*);
	((FunctionPointerType)__this->___method_ptr)(method);
}
IL2CPP_EXTERN_C  void DelegatePInvokeWrapper_OnResetCallback_t1A3F3E34E43B86041C7D7BA5D112278BCE960EFE (OnResetCallback_t1A3F3E34E43B86041C7D7BA5D112278BCE960EFE* __this, const RuntimeMethod* method)
{
	typedef void (DEFAULT_CALL *PInvokeFunc)();
	PInvokeFunc il2cppPInvokeFunc = reinterpret_cast<PInvokeFunc>(il2cpp_codegen_get_reverse_pinvoke_function_ptr(__this));
	il2cppPInvokeFunc();

}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void OnResetCallback__ctor_m2EDB8669DF8246B4ECBD3CA8BD2151DC70B2FF8C (OnResetCallback_t1A3F3E34E43B86041C7D7BA5D112278BCE960EFE* __this, RuntimeObject* ___0_object, intptr_t ___1_method, const RuntimeMethod* method) 
{
	__this->___method_ptr = (intptr_t)il2cpp_codegen_get_method_pointer((RuntimeMethod*)___1_method);
	__this->___method = ___1_method;
	__this->___m_target = ___0_object;
	Il2CppCodeGenWriteBarrier((void**)(&__this->___m_target), (void*)___0_object);
	int parameterCount = il2cpp_codegen_method_parameter_count((RuntimeMethod*)___1_method);
	__this->___method_code = (intptr_t)__this;
	if (MethodIsStatic((RuntimeMethod*)___1_method))
	{
		bool isOpen = parameterCount == 0;
		if (isOpen)
			__this->___invoke_impl = (intptr_t)&OnResetCallback_Invoke_mEB3B40B366C17A01388AF539F1E22251A7C1B3E9_OpenStatic;
		else
			{
				__this->___invoke_impl = __this->___method_ptr;
				__this->___method_code = (intptr_t)__this->___m_target;
			}
	}
	else
	{
		if (___0_object == NULL)
			il2cpp_codegen_raise_exception(il2cpp_codegen_get_argument_exception(NULL, "Delegate to an instance method cannot have null 'this'."), NULL);
		__this->___invoke_impl = __this->___method_ptr;
		__this->___method_code = (intptr_t)__this->___m_target;
	}
	__this->___extra_arg = (intptr_t)&OnResetCallback_Invoke_mEB3B40B366C17A01388AF539F1E22251A7C1B3E9_Multicast;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void OnResetCallback_Invoke_mEB3B40B366C17A01388AF539F1E22251A7C1B3E9 (OnResetCallback_t1A3F3E34E43B86041C7D7BA5D112278BCE960EFE* __this, const RuntimeMethod* method) 
{
	typedef void (*FunctionPointerType) (RuntimeObject*, const RuntimeMethod*);
	((FunctionPointerType)__this->___invoke_impl)((Il2CppObject*)__this->___method_code, reinterpret_cast<RuntimeMethod*>(__this->___method));
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR RuntimeObject* OnResetCallback_BeginInvoke_m8E254F51841595AC81F9775141EDE5B1AA992FA7 (OnResetCallback_t1A3F3E34E43B86041C7D7BA5D112278BCE960EFE* __this, AsyncCallback_t7FEF460CBDCFB9C5FA2EF776984778B9A4145F4C* ___0_callback, RuntimeObject* ___1_object, const RuntimeMethod* method) 
{
	void *__d_args[1] = {0};
	return (RuntimeObject*)il2cpp_codegen_delegate_begin_invoke((RuntimeDelegate*)__this, __d_args, (RuntimeDelegate*)___0_callback, (RuntimeObject*)___1_object);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void OnResetCallback_EndInvoke_m24025A79BE8BE6470DB0699F689F350F31540A05 (OnResetCallback_t1A3F3E34E43B86041C7D7BA5D112278BCE960EFE* __this, RuntimeObject* ___0_result, const RuntimeMethod* method) 
{
	il2cpp_codegen_delegate_end_invoke((Il2CppAsyncResult*) ___0_result, 0);
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void UnsubscriberPool__cctor_m54D0EE577CC777903047CA1E7E3B07AE2415ACF6 (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&EventBusRegistry_tA1D5325A5D0BAA4114C95BF53A1F6A2549B081AD_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1__ctor_mF6BA420407FB4D0D9A4B8A07B3D9AB12ECDE6F65_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_t403F6918E06CE77A4CC149AA4602E84EC3619DC8_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&OnResetCallback_t1A3F3E34E43B86041C7D7BA5D112278BCE960EFE_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&UnsubscriberPool_Clear_m67EFA629F192EE2A1DDB9F9F1DEA6EC64F141481_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&UnsubscriberPool_tECBE47E24CC047552D783042AC2F12481C6F43BF_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		List_1_t403F6918E06CE77A4CC149AA4602E84EC3619DC8* L_0 = (List_1_t403F6918E06CE77A4CC149AA4602E84EC3619DC8*)il2cpp_codegen_object_new(List_1_t403F6918E06CE77A4CC149AA4602E84EC3619DC8_il2cpp_TypeInfo_var);
		List_1__ctor_mF6BA420407FB4D0D9A4B8A07B3D9AB12ECDE6F65(L_0, List_1__ctor_mF6BA420407FB4D0D9A4B8A07B3D9AB12ECDE6F65_RuntimeMethod_var);
		((UnsubscriberPool_tECBE47E24CC047552D783042AC2F12481C6F43BF_StaticFields*)il2cpp_codegen_static_fields_for(UnsubscriberPool_tECBE47E24CC047552D783042AC2F12481C6F43BF_il2cpp_TypeInfo_var))->____pool = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&((UnsubscriberPool_tECBE47E24CC047552D783042AC2F12481C6F43BF_StaticFields*)il2cpp_codegen_static_fields_for(UnsubscriberPool_tECBE47E24CC047552D783042AC2F12481C6F43BF_il2cpp_TypeInfo_var))->____pool), (void*)L_0);
		OnResetCallback_t1A3F3E34E43B86041C7D7BA5D112278BCE960EFE* L_1 = (OnResetCallback_t1A3F3E34E43B86041C7D7BA5D112278BCE960EFE*)il2cpp_codegen_object_new(OnResetCallback_t1A3F3E34E43B86041C7D7BA5D112278BCE960EFE_il2cpp_TypeInfo_var);
		OnResetCallback__ctor_m2EDB8669DF8246B4ECBD3CA8BD2151DC70B2FF8C(L_1, NULL, (intptr_t)((void*)UnsubscriberPool_Clear_m67EFA629F192EE2A1DDB9F9F1DEA6EC64F141481_RuntimeMethod_var), NULL);
		il2cpp_codegen_runtime_class_init_inline(EventBusRegistry_tA1D5325A5D0BAA4114C95BF53A1F6A2549B081AD_il2cpp_TypeInfo_var);
		EventBusRegistry_RegisterResetCallback_m6C7853D22467C435203CECBE703D561E8B56ACAA(L_1, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR Unsubscriber_tA10207CB5BE682290BAE7CA7AEA26752826C011F* UnsubscriberPool_Get_m4F0BAFC94EDFDE8BC85879C12E7D0BE708BADD25 (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_RemoveAt_mECCA0666FDE900F66E216A7926393D8BA022BB91_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_get_Count_m2455A764B71057857832D6C464690B863F4B9E7F_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_get_Item_mFDA769C4F98C964654A8867429CADE6FD34779AA_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&UnsubscriberPool_tECBE47E24CC047552D783042AC2F12481C6F43BF_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Unsubscriber_tA10207CB5BE682290BAE7CA7AEA26752826C011F_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		il2cpp_codegen_runtime_class_init_inline(UnsubscriberPool_tECBE47E24CC047552D783042AC2F12481C6F43BF_il2cpp_TypeInfo_var);
		List_1_t403F6918E06CE77A4CC149AA4602E84EC3619DC8* L_0 = ((UnsubscriberPool_tECBE47E24CC047552D783042AC2F12481C6F43BF_StaticFields*)il2cpp_codegen_static_fields_for(UnsubscriberPool_tECBE47E24CC047552D783042AC2F12481C6F43BF_il2cpp_TypeInfo_var))->____pool;
		NullCheck(L_0);
		int32_t L_1;
		L_1 = List_1_get_Count_m2455A764B71057857832D6C464690B863F4B9E7F_inline(L_0, List_1_get_Count_m2455A764B71057857832D6C464690B863F4B9E7F_RuntimeMethod_var);
		if ((((int32_t)L_1) <= ((int32_t)0)))
		{
			goto IL_003a;
		}
	}
	{
		il2cpp_codegen_runtime_class_init_inline(UnsubscriberPool_tECBE47E24CC047552D783042AC2F12481C6F43BF_il2cpp_TypeInfo_var);
		List_1_t403F6918E06CE77A4CC149AA4602E84EC3619DC8* L_2 = ((UnsubscriberPool_tECBE47E24CC047552D783042AC2F12481C6F43BF_StaticFields*)il2cpp_codegen_static_fields_for(UnsubscriberPool_tECBE47E24CC047552D783042AC2F12481C6F43BF_il2cpp_TypeInfo_var))->____pool;
		List_1_t403F6918E06CE77A4CC149AA4602E84EC3619DC8* L_3 = ((UnsubscriberPool_tECBE47E24CC047552D783042AC2F12481C6F43BF_StaticFields*)il2cpp_codegen_static_fields_for(UnsubscriberPool_tECBE47E24CC047552D783042AC2F12481C6F43BF_il2cpp_TypeInfo_var))->____pool;
		NullCheck(L_3);
		int32_t L_4;
		L_4 = List_1_get_Count_m2455A764B71057857832D6C464690B863F4B9E7F_inline(L_3, List_1_get_Count_m2455A764B71057857832D6C464690B863F4B9E7F_RuntimeMethod_var);
		NullCheck(L_2);
		Unsubscriber_tA10207CB5BE682290BAE7CA7AEA26752826C011F* L_5;
		L_5 = List_1_get_Item_mFDA769C4F98C964654A8867429CADE6FD34779AA(L_2, ((int32_t)il2cpp_codegen_subtract(L_4, 1)), List_1_get_Item_mFDA769C4F98C964654A8867429CADE6FD34779AA_RuntimeMethod_var);
		List_1_t403F6918E06CE77A4CC149AA4602E84EC3619DC8* L_6 = ((UnsubscriberPool_tECBE47E24CC047552D783042AC2F12481C6F43BF_StaticFields*)il2cpp_codegen_static_fields_for(UnsubscriberPool_tECBE47E24CC047552D783042AC2F12481C6F43BF_il2cpp_TypeInfo_var))->____pool;
		List_1_t403F6918E06CE77A4CC149AA4602E84EC3619DC8* L_7 = ((UnsubscriberPool_tECBE47E24CC047552D783042AC2F12481C6F43BF_StaticFields*)il2cpp_codegen_static_fields_for(UnsubscriberPool_tECBE47E24CC047552D783042AC2F12481C6F43BF_il2cpp_TypeInfo_var))->____pool;
		NullCheck(L_7);
		int32_t L_8;
		L_8 = List_1_get_Count_m2455A764B71057857832D6C464690B863F4B9E7F_inline(L_7, List_1_get_Count_m2455A764B71057857832D6C464690B863F4B9E7F_RuntimeMethod_var);
		NullCheck(L_6);
		List_1_RemoveAt_mECCA0666FDE900F66E216A7926393D8BA022BB91(L_6, ((int32_t)il2cpp_codegen_subtract(L_8, 1)), List_1_RemoveAt_mECCA0666FDE900F66E216A7926393D8BA022BB91_RuntimeMethod_var);
		return L_5;
	}

IL_003a:
	{
		Unsubscriber_tA10207CB5BE682290BAE7CA7AEA26752826C011F* L_9 = (Unsubscriber_tA10207CB5BE682290BAE7CA7AEA26752826C011F*)il2cpp_codegen_object_new(Unsubscriber_tA10207CB5BE682290BAE7CA7AEA26752826C011F_il2cpp_TypeInfo_var);
		Unsubscriber__ctor_m11B27CCC64445E9582A3F7F98B147AED3A31F344(L_9, NULL);
		return L_9;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void UnsubscriberPool_Release_m9C0E8092F933B52EFA6099F495DEEB34AB039F74 (Unsubscriber_tA10207CB5BE682290BAE7CA7AEA26752826C011F* ___0_handle, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_Add_m2FDC5D3C2EDA7288094659E1EBD89DA82EFF6E82_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&UnsubscriberPool_tECBE47E24CC047552D783042AC2F12481C6F43BF_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		il2cpp_codegen_runtime_class_init_inline(UnsubscriberPool_tECBE47E24CC047552D783042AC2F12481C6F43BF_il2cpp_TypeInfo_var);
		List_1_t403F6918E06CE77A4CC149AA4602E84EC3619DC8* L_0 = ((UnsubscriberPool_tECBE47E24CC047552D783042AC2F12481C6F43BF_StaticFields*)il2cpp_codegen_static_fields_for(UnsubscriberPool_tECBE47E24CC047552D783042AC2F12481C6F43BF_il2cpp_TypeInfo_var))->____pool;
		Unsubscriber_tA10207CB5BE682290BAE7CA7AEA26752826C011F* L_1 = ___0_handle;
		NullCheck(L_0);
		List_1_Add_m2FDC5D3C2EDA7288094659E1EBD89DA82EFF6E82_inline(L_0, L_1, List_1_Add_m2FDC5D3C2EDA7288094659E1EBD89DA82EFF6E82_RuntimeMethod_var);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void UnsubscriberPool_Clear_m67EFA629F192EE2A1DDB9F9F1DEA6EC64F141481 (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_Clear_m7CC2FD761D87EC9E86CDEA42F042B9CC19422B22_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&UnsubscriberPool_tECBE47E24CC047552D783042AC2F12481C6F43BF_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		il2cpp_codegen_runtime_class_init_inline(UnsubscriberPool_tECBE47E24CC047552D783042AC2F12481C6F43BF_il2cpp_TypeInfo_var);
		List_1_t403F6918E06CE77A4CC149AA4602E84EC3619DC8* L_0 = ((UnsubscriberPool_tECBE47E24CC047552D783042AC2F12481C6F43BF_StaticFields*)il2cpp_codegen_static_fields_for(UnsubscriberPool_tECBE47E24CC047552D783042AC2F12481C6F43BF_il2cpp_TypeInfo_var))->____pool;
		NullCheck(L_0);
		List_1_Clear_m7CC2FD761D87EC9E86CDEA42F042B9CC19422B22_inline(L_0, List_1_Clear_m7CC2FD761D87EC9E86CDEA42F042B9CC19422B22_RuntimeMethod_var);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void UnsubscriberPool__ctor_m4EC4BCA518A09E75742FB76C65664191BF319EC8 (UnsubscriberPool_tECBE47E24CC047552D783042AC2F12481C6F43BF* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Action_Invoke_m7126A54DACA72B845424072887B5F3A51FC3808E_inline (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* __this, const RuntimeMethod* method) 
{
	typedef void (*FunctionPointerType) (RuntimeObject*, const RuntimeMethod*);
	((FunctionPointerType)__this->___invoke_impl)((Il2CppObject*)__this->___method_code, reinterpret_cast<RuntimeMethod*>(__this->___method));
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void OnResetCallback_Invoke_mEB3B40B366C17A01388AF539F1E22251A7C1B3E9_inline (OnResetCallback_t1A3F3E34E43B86041C7D7BA5D112278BCE960EFE* __this, const RuntimeMethod* method) 
{
	typedef void (*FunctionPointerType) (RuntimeObject*, const RuntimeMethod*);
	((FunctionPointerType)__this->___invoke_impl)((Il2CppObject*)__this->___method_code, reinterpret_cast<RuntimeMethod*>(__this->___method));
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR RuntimeObject* Enumerator_get_Current_m6330F15D18EE4F547C05DF9BF83C5EB710376027_gshared_inline (Enumerator_t9473BAB568A27E2339D48C1F91319E0F6D244D7A* __this, const RuntimeMethod* method) 
{
	{
		RuntimeObject* L_0 = __this->____current;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void List_1_Clear_m16C1F2C61FED5955F10EB36BC1CB2DF34B128994_gshared_inline (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D* __this, const RuntimeMethod* method) 
{
	int32_t V_0 = 0;
	{
		int32_t L_0 = __this->____version;
		__this->____version = ((int32_t)il2cpp_codegen_add(L_0, 1));
	}
	{
		int32_t L_1 = __this->____size;
		V_0 = L_1;
		__this->____size = 0;
		int32_t L_2 = V_0;
		if ((((int32_t)L_2) <= ((int32_t)0)))
		{
			goto IL_003c;
		}
	}
	{
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_3 = __this->____items;
		int32_t L_4 = V_0;
		Array_Clear_m50BAA3751899858B097D3FF2ED31F284703FE5CB((RuntimeArray*)L_3, 0, L_4, NULL);
		return;
	}

IL_003c:
	{
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void List_1_Add_mEBCF994CC3814631017F46A387B1A192ED6C85C7_gshared_inline (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D* __this, RuntimeObject* ___0_item, const RuntimeMethod* method) 
{
	ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* V_0 = NULL;
	int32_t V_1 = 0;
	{
		int32_t L_0 = __this->____version;
		__this->____version = ((int32_t)il2cpp_codegen_add(L_0, 1));
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_1 = __this->____items;
		V_0 = L_1;
		int32_t L_2 = __this->____size;
		V_1 = L_2;
		int32_t L_3 = V_1;
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_4 = V_0;
		NullCheck(L_4);
		if ((!(((uint32_t)L_3) < ((uint32_t)((int32_t)(((RuntimeArray*)L_4)->max_length))))))
		{
			goto IL_0034;
		}
	}
	{
		int32_t L_5 = V_1;
		__this->____size = ((int32_t)il2cpp_codegen_add(L_5, 1));
		ObjectU5BU5D_t8061030B0A12A55D5AD8652A20C922FE99450918* L_6 = V_0;
		int32_t L_7 = V_1;
		RuntimeObject* L_8 = ___0_item;
		NullCheck(L_6);
		(L_6)->SetAt(static_cast<il2cpp_array_size_t>(L_7), (RuntimeObject*)L_8);
		return;
	}

IL_0034:
	{
		RuntimeObject* L_9 = ___0_item;
		List_1_AddWithResize_m79A9BF770BEF9C06BE40D5401E55E375F2726CC4(__this, L_9, il2cpp_rgctx_method(method->klass->rgctx_data, 14));
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t List_1_get_Count_m4407E4C389F22B8CEC282C15D56516658746C383_gshared_inline (List_1_tA239CB83DE5615F348BB0507E45F490F4F7C9A8D* __this, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = __this->____size;
		return L_0;
	}
}

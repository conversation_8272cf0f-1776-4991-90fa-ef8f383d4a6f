﻿<Project ToolsVersion="Current">
  <!-- Generated file, do not modify, your changes will be overwritten (use AssetPostprocessor.OnGeneratedCSProject) -->
  <PropertyGroup>
    <BaseIntermediateOutputPath>Temp\obj\$(Configuration)\$(MSBuildProjectName)</BaseIntermediateOutputPath>
    <IntermediateOutputPath>$(BaseIntermediateOutputPath)</IntermediateOutputPath>
  </PropertyGroup>
  <Import Project="Sdk.props" Sdk="Microsoft.NET.Sdk" />
  <ItemGroup>
    <ProjectCapability Include="Unity" />
  </ItemGroup>
  <PropertyGroup>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    <EnableDefaultItems>false</EnableDefaultItems>
    <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
    <LangVersion>9.0</LangVersion>
    <Configurations>Debug;Release</Configurations>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <RootNamespace></RootNamespace>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <AssemblyName>Assembly-CSharp</AssemblyName>
    <TargetFramework>netstandard2.1</TargetFramework>
    <BaseDirectory>.</BaseDirectory>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>Temp\bin\Debug\</OutputPath>
    <DefineConstants>UNITY_2022_3_57;UNITY_2022_3;UNITY_2022;UNITY_5_3_OR_NEWER;UNITY_5_4_OR_NEWER;UNITY_5_5_OR_NEWER;UNITY_5_6_OR_NEWER;UNITY_2017_1_OR_NEWER;UNITY_2017_2_OR_NEWER;UNITY_2017_3_OR_NEWER;UNITY_2017_4_OR_NEWER;UNITY_2018_1_OR_NEWER;UNITY_2018_2_OR_NEWER;UNITY_2018_3_OR_NEWER;UNITY_2018_4_OR_NEWER;UNITY_2019_1_OR_NEWER;UNITY_2019_2_OR_NEWER;UNITY_2019_3_OR_NEWER;UNITY_2019_4_OR_NEWER;UNITY_2020_1_OR_NEWER;UNITY_2020_2_OR_NEWER;UNITY_2020_3_OR_NEWER;UNITY_2021_1_OR_NEWER;UNITY_2021_2_OR_NEWER;UNITY_2021_3_OR_NEWER;UNITY_2022_1_OR_NEWER;UNITY_2022_2_OR_NEWER;UNITY_2022_3_OR_NEWER;UNITY_INCLUDE_TESTS;ENABLE_AR;ENABLE_AUDIO;ENABLE_CACHING;ENABLE_CLOTH;ENABLE_EVENT_QUEUE;ENABLE_MICROPHONE;ENABLE_MULTIPLE_DISPLAYS;ENABLE_PHYSICS;ENABLE_TEXTURE_STREAMING;ENABLE_LZMA;ENABLE_UNITYEVENTS;ENABLE_VR;ENABLE_WEBCAM;ENABLE_UNITYWEBREQUEST;ENABLE_WWW;ENABLE_CLOUD_SERVICES;ENABLE_CLOUD_SERVICES_ADS;ENABLE_CLOUD_SERVICES_USE_WEBREQUEST;ENABLE_CLOUD_SERVICES_CRASH_REPORTING;ENABLE_CLOUD_SERVICES_NATIVE_CRASH_REPORTING;ENABLE_CLOUD_SERVICES_PURCHASING;ENABLE_CLOUD_SERVICES_ANALYTICS;ENABLE_CLOUD_SERVICES_BUILD;ENABLE_EDITOR_GAME_SERVICES;ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT;ENABLE_CLOUD_LICENSE;ENABLE_EDITOR_HUB_LICENSE;ENABLE_WEBSOCKET_CLIENT;ENABLE_GENERATE_NATIVE_PLUGINS_FOR_ASSEMBLIES_API;ENABLE_DIRECTOR_AUDIO;ENABLE_DIRECTOR_TEXTURE;ENABLE_MANAGED_JOBS;ENABLE_MANAGED_TRANSFORM_JOBS;ENABLE_MANAGED_ANIMATION_JOBS;ENABLE_MANAGED_AUDIO_JOBS;ENABLE_RUNTIME_PERMISSIONS;ENABLE_ENGINE_CODE_STRIPPING;ENABLE_ONSCREEN_KEYBOARD;ENABLE_MANAGED_UNITYTLS;INCLUDE_DYNAMIC_GI;ENABLE_SCRIPTING_GC_WBARRIERS;PLATFORM_SUPPORTS_MONO;ENABLE_VIDEO;ENABLE_ACCELERATOR_CLIENT_DEBUGGING;ENABLE_NAVIGATION_PACKAGE_DEBUG_VISUALIZATION;ENABLE_NAVIGATION_HEIGHTMESH_RUNTIME_SUPPORT;ENABLE_NAVIGATION_UI_REQUIRES_PACKAGE;PLATFORM_ANDROID;TEXTCORE_1_0_OR_NEWER;UNITY_ANDROID;UNITY_ANDROID_API;ENABLE_EGL;ENABLE_NETWORK;ENABLE_RUNTIME_GI;ENABLE_CRUNCH_TEXTURE_COMPRESSION;UNITY_CAN_SHOW_SPLASH_SCREEN;UNITY_HAS_GOOGLEVR;UNITY_HAS_TANGO;ENABLE_SPATIALTRACKING;ENABLE_ETC_COMPRESSION;PLATFORM_EXTENDS_VULKAN_DEVICE;PLATFORM_HAS_MULTIPLE_SWAPCHAINS;UNITY_ANDROID_SUPPORTS_SHADOWFILES;PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP;ENABLE_UNITYADS_RUNTIME;UNITY_UNITYADS_API;ENABLE_MONO;NET_STANDARD_2_0;NET_STANDARD;NET_STANDARD_2_1;NETSTANDARD;NETSTANDARD2_1;ENABLE_PROFILER;DEBUG;TRACE;UNITY_ASSERTIONS;UNITY_EDITOR;UNITY_EDITOR_64;UNITY_EDITOR_WIN;ENABLE_UNITY_COLLECTIONS_CHECKS;ENABLE_BURST_AOT;UNITY_TEAM_LICENSE;ENABLE_CUSTOM_RENDER_TEXTURE;ENABLE_DIRECTOR;ENABLE_LOCALIZATION;ENABLE_SPRITES;ENABLE_TERRAIN;ENABLE_TILEMAP;ENABLE_TIMELINE;ENABLE_LEGACY_INPUT_MANAGER;TEXTCORE_FONT_ENGINE_1_5_OR_NEWER;TEXTCORE_FONT_ENGINE_1_6_OR_NEWER;ENABLE_SPAN_T;WWISE_ADDRESSABLES_POST_2023;CSHARP_7_OR_LATER;CSHARP_7_3_OR_NEWER</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <NoWarn>0169;USG0001</NoWarn>
    <AllowUnsafeBlocks>True</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>Temp\bin\Release\</OutputPath>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <NoWarn>0169;USG0001</NoWarn>
    <AllowUnsafeBlocks>True</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup>
    <NoStandardLibraries>true</NoStandardLibraries>
    <NoStdLib>true</NoStdLib>
    <NoConfig>true</NoConfig>
    <DisableImplicitFrameworkReferences>true</DisableImplicitFrameworkReferences>
    <MSBuildWarningsAsMessages>MSB3277</MSBuildWarningsAsMessages>
  </PropertyGroup>
  <PropertyGroup>
    <UnityProjectGenerator>Package</UnityProjectGenerator>
    <UnityProjectGeneratorVersion>2.0.22</UnityProjectGeneratorVersion>
    <UnityProjectGeneratorStyle>SDK</UnityProjectGeneratorStyle>
    <UnityProjectType>Game:1</UnityProjectType>
    <UnityBuildTarget>Android:13</UnityBuildTarget>
    <UnityVersion>2022.3.57f1c1</UnityVersion>
  </PropertyGroup>
  <ItemGroup>
    <Analyzer Include="C:\Users\<USER>\.vscode\extensions\visualstudiotoolsforunity.vstuc-1.1.2\Analyzers\Microsoft.Unity.Analyzers.dll" />
    <Analyzer Include="C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\Tools\Unity.SourceGenerators\Unity.SourceGenerators.dll" />
    <Analyzer Include="C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\Tools\Unity.SourceGenerators\Unity.Properties.SourceGenerator.dll" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Assets\BulletEffectCore\packs\WebDemoAssets\scripts\EffectActor.cs" />
    <Compile Include="Assets\Scripts\GameModules\InGame\Views\UIBattleBroadcast\UIBattleBroadcastCtrl.cs" />
    <Compile Include="Assets\Scripts\GameModules\Common\Views\UIGM\UIGMItemCtrls\UIGMSliderItem.cs" />
    <Compile Include="Assets\Scripts\Framework\UI\Components\UIViewRaycaster.cs" />
    <Compile Include="Assets\Unluck Software\Demo Resources\Scripts\SmoothCameraOrbit.cs" />
    <Compile Include="Assets\Arts\SetCartoonTanks\Demo_Table_Animations\Scripts\SCShips_Ui_Char_Panel.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\SearchCondition\RgSearchConditionFilter.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Battle\Effect\RgEffect​Field.cs" />
    <Compile Include="Assets\Scripts\AutoGen\Luban\vector4.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\Room\ListRoomReq.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Template\RgTemplateManager.cs" />
    <Compile Include="Assets\Scripts\GameModules\Common\GM\Commands\CommonCommands.cs" />
    <Compile Include="Assets\Scripts\Framework\UI\Model\UIModel.cs" />
    <Compile Include="Assets\Scripts\AutoGen\Luban\gameplay\UnitType.cs" />
    <Compile Include="Assets\Scripts\GameModules\Common\Components\SimpleVideoController.cs" />
    <Compile Include="Assets\Scripts\GameModules\InGame\Views\UIBattleMain\UIGameStats.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\FixMath\F64Vec2.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\cs\GetRoleS.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Converter\Part\RgPhysicsPart.cs" />
    <Compile Include="Assets\Scripts\AutoGen\Luban\gameplay\Area.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Collision\RgCollisionType.cs" />
    <Compile Include="Assets\Scripts\GameFlow\States\GameStateIdle.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\DataTable\RgEntityCarrierTemplates.cs" />
    <Compile Include="Assets\Scripts\Message\RGameMsgDef.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\Room\RoomInfoSync.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Converter\Part\RgEntityPart.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Entity\RgEntityMovable.cs" />
    <Compile Include="Assets\Scripts\GameModules\InGame\Views\InputSystem\Joystick\UICardJoystick.cs" />
    <Compile Include="Assets\Scripts\GameModules\Common\GM\GMAttributes.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Audio\RgAudioConfig.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\DataTable\RgEffectTemplates.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\cs\CustomRoomListS.cs" />
    <Compile Include="Assets\Unluck Software\Scripts\AnimatedLight.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\cs\RoleCmdSeqItem.cs" />
    <Compile Include="Assets\Arts\SetCartoonTanks\Demo_Game_Scene\Scripts\SCT_TankMovement.cs" />
    <Compile Include="Assets\Scripts\GameModules\InGame\Views\UIBattleMain\Temp\UICardSelectionTemp.cs" />
    <Compile Include="Assets\Arts\Archanor\Stylized Fire FX\Demo\Scripts\ProjectileScript.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\Triangulation\Iterators\FromFaceToInnerVertices.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\Framework\Equip.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\Triangulation\AI\AStar.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Message\RgLogicMessages.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Entity\Skill\RgEntitySkill_EmergencyMobilization.cs" />
    <Compile Include="Assets\Scripts\AutoGen\Table\ResDamage_generated.cs" />
    <Compile Include="Assets\Arts\Vefects\Explosions VFX URP\Demo\Resources\Scripts\SC_Vefects_Easy_Spawn.cs" />
    <Compile Include="Assets\Scripts\Framework\Core\IMessage.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\Room\RoomRspBody.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\LogicParts\RgLogicPart_Producer.cs" />
    <Compile Include="Assets\FogOfWar\Scripts\Revealers\FogOfWarRevealer.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Map\Building\RgBuildingPlacer.cs" />
    <Compile Include="Assets\Scripts\Network\Core\Message\NetworkSrvMsgManager.cs" />
    <Compile Include="Assets\Scripts\GameModules\AroundGame\BattlePrepare\UIHeroSelectFilter.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\Room\RoomBatchFrameRsp.cs" />
    <Compile Include="Assets\Scripts\Framework\Deterministic\Lockstep\NetService.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Physics\VolatilePhysics\RayCast\VoltRayCast.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Audio\RgEntityRender_AudioPlayerLogic.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\cs\CustomRoomJoinC.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\StateMachine\AtomAbilities\RgAbilityMove.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Animation\RgAnimationManager_AtomicBombLauncher.cs" />
    <Compile Include="Assets\Scripts\GameModules\Common\Components\LayoutElementImageFill.cs" />
    <Compile Include="Assets\Unluck Software\Demo Resources\Scripts\MoveInACircle.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\Crowds\IMovableEntityManager.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\Crowds\MovableEntityManager.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Converter\Part\Weapon\RgWeaponGunPart.cs" />
    <Compile Include="Assets\Scripts\Framework\Core\TimeHelper.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\cs\CardUpdateS.cs" />
    <Compile Include="Assets\Scripts\Framework\UI\Common\ListView\Extension\Effect\ReceiverSizeDelta.cs" />
    <Compile Include="Assets\Scripts\GameModules\InGame\Views\UIBattleMain\PressTips\UIPressTipsEventHandler.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Battle\Weapon\RgWeaponTurretGun.cs" />
    <Compile Include="Assets\Scripts\Framework\Core\ResourcesManager.cs" />
    <Compile Include="Assets\Scripts\Network\Core\Message\MsgDef.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\cs\CSCustomRoomBaseInfo.cs" />
    <Compile Include="Assets\Tools\TextureUnpacker\TextureUnpacker.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Skill\RgSkillCaster_BoostMorale.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\uPools\Runtime\ObjectPool.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\Battle\UserInput.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Group\RgTemplateFromation.cs" />
    <Compile Include="Assets\Scripts\GameFlow\States\GameStateBattleFinish.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\Util\Debug.cs" />
    <Compile Include="Assets\GabrielAguiarProductions\Script\SimpleCameraShake.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Battle\Weapon\RgWeaponRocket.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\uPools\Runtime\SharedGameObjectPool.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\cs\GetHistoryRoundListC.cs" />
    <Compile Include="Assets\Scripts\Framework\PropertyAttributes\EnumFlag.cs" />
    <Compile Include="Assets\Scripts\Framework\UI\Common\ListView\Extension\NoTemplate\GeneralDragListView.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Template\RgTemplate.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\cs\GMS.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\Triangulation\Iterators\FromVertexToNeighbourVertices.cs" />
    <Compile Include="Assets\Scripts\Framework\UI\Common\ListView\Extension\Effect\ReceiverGraphicRaycaster.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\SharpSteer2\Helpers\Colors.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Entity\Skill\RgEntitySkill.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Entity\RgEntity.cs" />
    <Compile Include="Assets\Redclue\ShieldFX_02\Scripts_ShieldFX02\PreviousAndNext_Shield02fx.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\cs\GetRoleBaseAttrC.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\SharpSteer2\ILocalSpaceBasis.cs" />
    <Compile Include="Assets\Scripts\AutoGen\Luban\gameplay\UnitMover.cs" />
    <Compile Include="Assets\Scripts\Gameplay\UnitTest\RgUnitTest_GUI.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\Room\RoomFrameCheckReq.cs" />
    <Compile Include="Assets\Scripts\Network\NetworkConfig.cs" />
    <Compile Include="Assets\Scripts\AutoGen\Luban\gameplay\TbCard.cs" />
    <Compile Include="Assets\Scripts\Gameplay\UnitTest\RgPathFinderTest.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Mover\Interpolation\RgHermiteSplineInterpolation.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\comm\AreaStatus.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\SharpSteer2\Pathway\TrianglePathway.cs" />
    <Compile Include="Assets\Scripts\GameFlow\Systems\NetworkSystems\BattleNetworkSystem.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\Util\Geometry.cs" />
    <Compile Include="Assets\Scripts\AutoGen\Luban\gameplay\ArmorType.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Physics\VolatilePhysics\Internals\Collision\ContactManager.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Crowd\RgObstacleSegment.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Spawn\RgSpawnPoint.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Entity\RgEntityRender_EffectField.cs" />
    <Compile Include="Assets\Scripts\Framework\UI\Common\ListView\Extension\Effect\ListViewScrollEventReceiver.cs" />
    <Compile Include="Assets\Scripts\AutoGen\Luban\gameplay\PlaceRect.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Effect\RgSpecialEffectSocketHub.cs" />
    <Compile Include="Assets\Scripts\Gameplay\UnitTest\RgUnitTest.cs" />
    <Compile Include="Assets\Scripts\GameModules\AroundGame\BattlePrepare\CardSelect\UICardTitleItem.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Team\RgPlayerManager.cs" />
    <Compile Include="Assets\Scripts\Framework\Core\ExtensionUtils.cs" />
    <Compile Include="Assets\Scripts\GameFlow\Systems\ResultSystem.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\StateMachine\AtomAbilities\RgAbilityIdle.cs" />
    <Compile Include="Assets\Scripts\AutoGen\Luban\vector3.cs" />
    <Compile Include="Assets\StandaloneFileBrowser\StandaloneFileBrowserWindows.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\FixPointCS\Fixed32.cs" />
    <Compile Include="Assets\Arts\SetCartoonTanks\Demo_Table_Animations\Scripts\SCShips_Actions.cs" />
    <Compile Include="Assets\Arts\Vefects\Explosions VFX URP\Demo\Resources\Scripts\SC_Vefects_Delete_After_Time.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\Room\RoomEnterWay.cs" />
    <Compile Include="Assets\Scripts\Framework\AssetManagement\Runtime\AssetLoaderImpl.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\Room\RoomInfoReq.cs" />
    <Compile Include="Assets\Scripts\GameFlow\States\GameStateInitialize.cs" />
    <Compile Include="Assets\Scripts\Message\IRGameMessgaeBody.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Entity\Building\RgEntityRender_Building_Skill.cs" />
    <Compile Include="Assets\Scripts\GameModules\AroundGame\BattlePrepare\CardSelect\UICampItem.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\FixMath\F64.cs" />
    <Compile Include="Assets\Scripts\Network\Core\KCP\KCPRoomService.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Converter\Part\Skill\RgBuildingSkillPart.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\Battle\JoinGameNtf.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\Room\RoomFrameSync.cs" />
    <Compile Include="Assets\Scripts\AutoGen\Luban\gameplay\ResCard.cs" />
    <Compile Include="Assets\Scripts\Message\RGameMessage.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Battle\Weapon\RgWeaponMedical.cs" />
    <Compile Include="Assets\Arts\SetCartoonTanks\Demo_Game_Scene\Scripts\SCT_CameraControl.cs" />
    <Compile Include="Assets\Scripts\GameModules\InGame\Views\UIBattleMain\CardController\UICardParent.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Physics\VolatilePhysics\VoltAABB.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Battle\Effect\RgEffect_Buff.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\cs\GameResult.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Physics\VolatilePhysics\VoltBuffer.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\cs\CustomRoomListC.cs" />
    <Compile Include="Assets\Scripts\Framework\UI\Common\MeshEffect\MirrorMeshEffect.cs" />
    <Compile Include="Assets\Buildings_constructor\Scripts\Controller.cs" />
    <Compile Include="Assets\Real Fire &amp; Smoke\Scripts\RealFireLightFade.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Message\Messages\RgEntityMessages.cs" />
    <Compile Include="Assets\EffectCore\scripts\ECparticleLinerendererColorChangerMaster.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Entity\Building\RgEntity_Building.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\SharpSteer2\Pathway\IPathway.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Map\Building\RgBuildingManager.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\cs\CSCustomRoomInfo.cs" />
    <Compile Include="Assets\Engine Particle\Script\Exit.cs" />
    <Compile Include="Assets\Scripts\GameModules\Common\Utilities\AnimUtility.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\DataTable\RgAIBrainTemplates.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\Crowds\Flowfield\FlowfieldManager.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Converter\Part\Weapon\RgWeaponExplodePart.cs" />
    <Compile Include="Assets\Engine Particle\Script\ExtendedFlycam.cs" />
    <Compile Include="Assets\Scripts\GameModules\Common\Views\UIGM\UIGMButtonEntry.cs" />
    <Compile Include="Assets\Scripts\Framework\UI\Common\ListView\Attributes\AssetRefAttribute.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Animation\RgAnimationManager_Solider.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Entity\RgEntityRender_Actor.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Converter\Part\Beam\IBeamEffectInterface.cs" />
    <Compile Include="Assets\Scripts\Framework\AssetManagement\Runtime\AssetLoader.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\SharpSteer2\Helpers\PathwayHelpers.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Mover\RgSteerBehaviorMover.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Buff\BuffTypeDef.cs" />
    <Compile Include="Assets\Scripts\GameFlow\GameStateController.cs" />
    <Compile Include="Assets\Scripts\AutoGen\Luban\gameplay\CardKind.cs" />
    <Compile Include="Assets\Hovl Studio\Toon Projectiles 2\Demo scene\DemoShooting.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\HUD\HeadBar\UIHeadBarSkill.cs" />
    <Compile Include="Assets\Real Fire &amp; Smoke\Demo\Scripts\RealFireSceneSelect.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\RgLogicMessageManager.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\cs\GameStartS.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\SharpSteer2\Obstacles\LocalSpaceObstacle.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\Room\RoomPlayerInfo.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\cs\CustomRoomExitC.cs" />
    <Compile Include="Assets\Scripts\Framework\UI\Common\ListView\Extension\GridView.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Entity\RgEntityRender_Skill.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\Room\RoomExitRsp.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Map\Building\RgBuildingGridCell.cs" />
    <Compile Include="Assets\Scripts\Framework\UI\Common\AssetCollection\SpriteCollection.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\Battle\FrameNtf.cs" />
    <Compile Include="Assets\Engine Particle\Script\UserCamera.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Team\RgTeamManager.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Converter\Part\Weapon\RgWeaponElectromagneticEffectPart.cs" />
    <Compile Include="Assets\MagicArsenal\Demo\Scripts\MagicFireProjectile.cs" />
    <Compile Include="Assets\Scripts\GameModules\Common\Views\UIGM\UIBaseGMPanel.cs" />
    <Compile Include="Assets\BulletEffectCore\packs\WebDemoAssets\scripts\CameraShake.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\cs\EMode.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Entity\Skill\RgEntitySkill_ExecuteBuff.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Mover\RgPathFollowMover.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Effect\RgDestructionExplosion.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\Battle\Command.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\SharpSteer2\LocalSpace.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\SearchCondition\RgSearchConditionCombine.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\cs\CustomRoomExitS.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\LogicParts\RgLogicPart_ExecuteBuff.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\Util\UniqueId.cs" />
    <Compile Include="Assets\Scripts\Network\Core\NetworkService.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\Triangulation\Iterators\FromFaceToNeighbourFaces.cs" />
    <Compile Include="Assets\RTS Effects\Demo\Scripts\RTSLoopFX.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\Room\JoinRoomRsp.cs" />
    <Compile Include="Assets\Scripts\Framework\Deterministic\Lockstep\FrameManager.cs" />
    <Compile Include="Assets\FogOfWar\Scripts\Built-In (legacy) RP\FOWImageEffect.cs" />
    <Compile Include="Assets\Scripts\GameModules\InGame\Views\UIBattleMain\UISkillItemCtrl.cs" />
    <Compile Include="Assets\Scripts\Message\RGameMsgManager.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\Triangulation\Iterators\FromVertexToOutgoingEdges.cs" />
    <Compile Include="Assets\Scripts\AutoGen\Luban\gameplay\TbUnit.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Entity\RgManager.cs" />
    <Compile Include="Assets\Scripts\GameModules\InGame\Views\InputSystem\Joystick\BaseJoystick.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\Battle\SendHashCodeReq.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\DataTable\RgGlobalEntityTemplates.cs" />
    <Compile Include="Assets\Scripts\Framework\Deterministic\Math\Fix64SinLut.cs" />
    <Compile Include="Assets\Scripts\GameModules\Common\Components\LayoutElementEx.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\cs\CustomRoomInfoC.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\cs\GetHistoryRoundListS.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Shape\RgShape.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\HUD\FloatingText\UIFloatingText.cs" />
    <Compile Include="Assets\Scripts\GameModules\InGame\Views\InputSystem\BuildingPolicy.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Physics\VolatilePhysics\Internals\Collision\Manifold.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Physics\FixedMath.Net\src\Fix64.cs" />
    <Compile Include="Assets\Scripts\GameModules\InGame\Message_InGameUI.cs" />
    <Compile Include="Assets\Scripts\GameModules\Common\Views\UIGM\UIGMItemCtrls\UIGMButtonItem.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Crowd\Gizmos\UnityPhysicsGizmosDrawer.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Cameras\RgMainCameraZoomComponent.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Group\RgFormation.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Physics\VolatilePhysics\Internals\Broadphase\TreeBroadphase.cs" />
    <Compile Include="Assets\Scripts\GameModules\InGame\Views\UIBattleBroadcast\BattleEventSystem.cs" />
    <Compile Include="Assets\Scripts\GameModules\Common\Views\UIGM\UIGMContainerItem.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\uPools\Runtime\External\UniTask\AsyncObjectPool.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\uPools\Runtime\Internal\PoolCallbackHelper.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\Framework\Vec2.cs" />
    <Compile Include="Assets\Scripts\GameModules\InGame\Views\UIBattleMain\UIBattleModel.cs" />
    <Compile Include="Assets\MagicArsenal\Demo\Scripts\MagicLoopScript.cs" />
    <Compile Include="Assets\Redclue\ShieldFX_02\Scripts_ShieldFX02\Gun Scripts\Spawn.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Mover\Interpolation\RgCatmullRomSplineInterpolation.cs" />
    <Compile Include="Assets\Buildings_constructor\Scripts\TopDpwnCamera.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\StateMachine\RgStateMachine.cs" />
    <Compile Include="Assets\Scripts\Network\Core\FrameRecord\FrameRecord.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Collision\RgCollisionManager.cs" />
    <Compile Include="Assets\Scripts\GameFlow\States\SubStates\LoginSubState_Connect.cs" />
    <Compile Include="Assets\ExplsionFx\packs\WebDemoAssets\scripts\ECCameraShakeCaller.cs" />
    <Compile Include="Assets\Arts\SetCartoonTanks\Demo_Table_Animations\Scripts\SCAirCrafts_Ui_Char_Panel.cs" />
    <Compile Include="Assets\Scripts\Framework\UI\View\UIViewConfig.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Input\Cmds\CreateEntityCmd.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Physics\VolatilePhysics\Internals\Broadphase\NaiveBroadphase.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\Triangulation\Data\Obstacle.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\FixMath\F32Vec2.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\Room\UnitCardInfo.cs" />
    <Compile Include="Assets\Scripts\Framework\Deterministic\Lockstep\LockstepController.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\Crowds\MoveStrategy\AttackMoveStrategy.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\Room\RoomAddAIRsp.cs" />
    <Compile Include="Assets\Arts\SetCartoonTanks\Demo_Game_Scene\Scripts\SCT_Shell.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Converter\Part\Weapon\RgBulletTrajectory.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\DataTable\RgLogicPartTemplates.cs" />
    <Compile Include="Assets\FogOfWar\Scripts\Hiders\PartialHiderRegisterer.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Buff\IBuff.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\Triangulation\Iterators\FromFaceToInnerEdges.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Physics\VolatilePhysics\Internals\Axis.cs" />
    <Compile Include="Assets\Arts\UNI VFX\Common\Scripts\UNI_SimpleCameraController.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Entity\RgEntityProjectile.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Entity\RgEntityTemplateStruct.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\Crowds\ICollisionEvent.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Map\RgMapSearchManager.cs" />
    <Compile Include="Assets\Scripts\Framework\UI\Common\BitmapText\BitmapTextSettings.cs" />
    <Compile Include="Assets\Scripts\Framework\UI\Common\ListView\Core\ListViewInputHandler.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Audio\RgMonoAudioPlayer.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\Room\RoomFrameCheckRsp.cs" />
    <Compile Include="Assets\Scripts\GameModules\InGame\Views\UIBattleMain\PressTips\UIPressTips.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\DataTable\RgArmorTemplates.cs" />
    <Compile Include="Assets\Scripts\GameFlow\Datas\ServerData\RoleData.cs" />
    <Compile Include="Assets\TutorialInfo\Scripts\Readme.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\cs\CardUpdateAction.cs" />
    <Compile Include="Assets\Scripts\GameFlow\Systems\NetworkSystems\BattleNetworkSystem_Response.cs" />
    <Compile Include="Assets\GabrielAguiarProductions\Script\ParticleSystemController\ParticleSystemControllerEditor.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Battle\EffectTypeDef.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Converter\Part\RgSegmentPathCollectionPart.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Path\RgFlowField.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Entity\Skill\RgEntitySkill_MissileStrike.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Physics\VolatilePhysics\Util\VoltDebug.cs" />
    <Compile Include="Assets\Scripts\Framework\UI\Common\ListView\Extension\Effect\ListViewScrollEventDispatcher.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Converter\Part\RgAudioPart.cs" />
    <Compile Include="Assets\RTS Effects\Scripts\AnimatedUVs.cs" />
    <Compile Include="Assets\Scripts\Gameplay\UnitTest\RgLockstepTest.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Physics\VolatilePhysics\VoltMath.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Skill\RgSkillCaster_CallTransportPlane.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\Crowds\SteeringForce\ISteeringForce.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Curve\BezierCurve3D.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Utils\GizmosUtils.cs" />
    <Compile Include="Assets\GabrielAguiarProductions\Script\SimpleCameraController.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\Room\RoomPlayerProgress.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\Triangulation\Math\Matrix2D.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\HUD\HeadBar\UIHeadBarBase.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Shape\RgOBBShape.cs" />
    <Compile Include="Assets\MagicArsenal\Effects\Scripts\MagicLightFlicker.cs" />
    <Compile Include="Assets\Scripts\GameFlow\Systems\NetworkSystems\NetworkSystemBase.cs" />
    <Compile Include="Assets\StandaloneFileBrowser\StandaloneFileBrowser.cs" />
    <Compile Include="Assets\Scripts\GameModules\Common\Views\UICommonBox\UICommonBoxView.cs" />
    <Compile Include="Assets\Scripts\GameFlow\Datas\IGameData.cs" />
    <Compile Include="Assets\Scripts\GameModules\Common\Components\UIRelativeDepth.cs" />
    <Compile Include="Assets\Real Fire &amp; Smoke\Demo\Scripts\RealFireDragMouseOrbit.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Animation\RgAnimationManager_Base.cs" />
    <Compile Include="Assets\Scripts\GameModules\Common\Views\UICommonNetworkMask\UINetworkMaskView.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Entity\RgEntityRender_Human.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\DataTable\RgVisionTemplates.cs" />
    <Compile Include="Assets\Scripts\GameFlow\States\GameStateBattleLoading.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Map\MapGridUtil.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Core\Singleton.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\Room\RoomFrameInput.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\comm\GenderType.cs" />
    <Compile Include="Assets\Scripts\Framework\UI\Common\MeshEffect\MaskMeshEffect.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Utils\DebugUtils.cs" />
    <Compile Include="Assets\Scripts\Gameplay\UnitTest\RgDTCrowdTest.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Utils\RGamePoolingBehaviour.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\Crowds\SteeringForce\AvoidObstacleForce.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Effect\RgSpecialEffectSocket.cs" />
    <Compile Include="Assets\Scripts\Framework\Collections\Fixed64List.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Crowd\RgObstacleAvoidanceQuery.cs" />
    <Compile Include="Assets\ExplsionFx\packs\WebDemoAssets\scripts\ECCameraShake.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Map\MapGridCell.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\SharpSteer2\Obstacles\PlaneObstacle.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Battle\RgEffectFieldManager.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\DataTable\RgEntityGroupTemplates.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\Crowds\MoveStrategy\FollowPathMoveStrategy.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\MapBlock\RgInfluenceMap.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\Crowds\SteeringForce\FlockSeparationForce.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\Room\RoomPlayerStatusSync.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Physics\VolatilePhysics\VoltConfig.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\cs\HeartbeatC.cs" />
    <Compile Include="Assets\Scripts\GameModules\AroundGame\Login\UILoginView.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Battle\Effect\RgEffect_CreateEntity.cs" />
    <Compile Include="Assets\Scripts\Framework\Deterministic\Lockstep\INetService.cs" />
    <Compile Include="Assets\Scripts\GameFlow\Systems\RecordSystem.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\cs\CSCustomRoomPlayerInfo.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Selection\RgSelectionManager.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\SharpSteer2\Pathway\GatewayPathway.cs" />
    <Compile Include="Assets\Scripts\Config\DataTable\DataTableLoader.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\cs\RoleSimpleDataType.cs" />
    <Compile Include="Assets\Arts\SetCartoonTanks\Demo_Game_Scene\Scripts\SCT_Tower_Cannon.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Entity\RgEntityComponent.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\uPools\Runtime\IObjectPool.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Mover\RgFlyTowardsPosition.cs" />
    <Compile Include="Assets\Scripts\GameModules\InGame\Views\UIBattleMain\UIProgressBarMaanager.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\Crowds\IPathwayQuerier.cs" />
    <Compile Include="Assets\Scripts\GameModules\Common\Components\UIRawImageScreenMatch.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\uPool\Runtime\GameObjectPool.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\Room\RoomReq.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\RgRenderMessageManager.cs" />
    <Compile Include="Assets\Scripts\AutoGen\Table\ResHero_generated.cs" />
    <Compile Include="Assets\Scripts\GameModules\AroundGame\BattlePrepare\UIHeroSelectItem.cs" />
    <Compile Include="Assets\Scripts\GameModules\Common\Views\UIGM\CachedInputField.cs" />
    <Compile Include="Assets\Arts\SetCartoonTanks\Demo_Table_Animations\Scripts\SCT_CharacterViewer.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Path\RgSegmentPath.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Bootstrap\ServerBootstrap.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\comm\Camp.cs" />
    <Compile Include="Assets\StandaloneFileBrowser\Sample\CanvasSampleSaveFileText.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\cs\UpdateClientDataS.cs" />
    <Compile Include="Assets\Low Poly Rocks Pack\Bonus Assets\Scripts\CameraControl.cs" />
    <Compile Include="Assets\FogOfWar\Scripts\Revealers\FogOfWarRevealer2D.cs" />
    <Compile Include="Assets\Scripts\AutoGen\Table\ResCamp_generated.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\SharpSteer2\IFlowField.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\Framework\Vec1.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\cs\HeroUnlockS.cs" />
    <Compile Include="Assets\Scripts\GameModules\Common\Views\UIGM\UIGMItemCtrls\UIGMToggleItem.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Entity\RgEntityCarrier.cs" />
    <Compile Include="Assets\Resources\Prefabs\FX\FlashEffect.cs" />
    <Compile Include="Assets\Scripts\Framework\UI\Common\Animation\UIAnimationKeyFrame.cs" />
    <Compile Include="Assets\Scripts\Gameplay\UnitTest\RgPhysicsTest.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\RgRenderWorld.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Buff\IBuffController.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\comm\CardGroupInfo.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\Room\RoomLoadingSync.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Sync\RingBuffer.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\Room\RoomFinishReq.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Entity\RgEntityAirport.cs" />
    <Compile Include="Assets\FogOfWar\Scripts\URP\FogOfWarRenderFeature.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\uPools\Runtime\ObjectPoolBase.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\ExtType\Int2.cs" />
    <Compile Include="Assets\Scripts\AutoGen\Luban\Tables.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\Room\ListRoomRsp.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Physics\VolatilePhysics\Shapes\VoltPolygon.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Message\RgGameplayMsgBody.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\SharpSteer2\Obstacles\IObstacle.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\cs\GMC.cs" />
    <Compile Include="Assets\GabrielAguiarProductions\Script\SpawnAbilities.cs" />
    <Compile Include="Assets\Scripts\GameFlow\Systems\NetworkSystems\BattleNetworkSystem_Request.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\cs\HistoryRoundSimple.cs" />
    <Compile Include="Assets\Scripts\GameModules\InGame\Views\UIBattleMain\CardController\UICardItemDataListener.cs" />
    <Compile Include="Assets\Scripts\AutoGen\Table\ResWeapon_generated.cs" />
    <Compile Include="Assets\Arts\Archanor\Stylized Fire FX\Scripts\loopScript.cs" />
    <Compile Include="Assets\Scripts\Framework\UI\Interfaces\IProperty.cs" />
    <Compile Include="Assets\FogOfWar\Scripts\Extras\MiniMapFrustum.cs" />
    <Compile Include="Assets\FogOfWar\Demo\Scripts\FowCharacterDemo.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Brain\RgAIBrain.cs" />
    <Compile Include="Assets\Scripts\GameModules\Common\GM\Commands\GameplayCommands.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Group\RgFormation_FromGeneratedPoints.cs" />
    <Compile Include="Assets\Scripts\AutoGen\Luban\gameplay\WeaponSet.cs" />
    <Compile Include="Assets\Unluck Software\Demo Resources\Scripts\EnableSelectedGameObject.cs" />
    <Compile Include="Assets\Scripts\Framework\UI\Common\ListView\Extension\ListViewLayoutElement.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Entity\Building\RgEntityRender_Snapshot.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\Framework\Vec3.cs" />
    <Compile Include="Assets\Scripts\Network\Core\TCP\TCPService.cs" />
    <Compile Include="Assets\MagicArsenal\Demo\Scripts\MagicProjectileScript.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Entity\RgEntityRender_Buff.cs" />
    <Compile Include="Assets\ExplsionFx\packs\WebDemoAssets\scripts\ECEffectActor.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\FixMath\F32Vec4.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\SharpSteer2\IAnnotationService.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Converter\Part\RgHitBoxPart.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Message\Messages\RgStateMesssages.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Debug\StartPauseDebuger.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\Battle\SendInputReq.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\Util\TypeCast.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\ExtType\ExtTypeUtils.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Utils\NetMode.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\cs\CMode.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Path\RgPath.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\cs\CustomRoomStartS.cs" />
    <Compile Include="Assets\Scripts\Framework\AssetManagement\Runtime\AssetReference.cs" />
    <Compile Include="Assets\FogOfWar\Scripts\URP\FogOfWarPass.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Entity\RgEntityRender.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Entity\RgEntityActor.cs" />
    <Compile Include="Assets\Scripts\GameModules\Common\EventsDefine\GameStateEvent.cs" />
    <Compile Include="Assets\BulletEffectCore\packs\WebDemoAssets\scripts\CameraShakeProjectile.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\cs\CSCmd.cs" />
    <Compile Include="Assets\ExplsionFx\packs\StylizedExplosionPack1\WebDemo\scripts\projectileActorExplosion1.cs" />
    <Compile Include="Assets\Scripts\AutoGen\Table\Global_generated.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Physics\VolatilePhysics\Shapes\VoltCircle.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\DataTable\RgSnapshotTemplates.cs" />
    <Compile Include="Assets\Scripts\GameModules\AroundGame\BattleLoading\UIBattleLoadingView.cs" />
    <Compile Include="Assets\Scripts\GameFlow\Systems\PlayerSystem.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Converter\Part\Weapon\RgWeaponBombPart.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Team\RgPlayer.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\cs\GameEndS.cs" />
    <Compile Include="Assets\Scripts\GameModules\AroundGame\Lobby\UIRoomItemCtrl.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\cs\CustomRoomUpdateReason.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\StateMachine\AtomAbilities\RgAbilityBase.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Mover\MoveMode\RgPhysicsSweepMoveMode.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\SharpSteer2\Obstacles\Obstacle.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\Crowds\MoveStrategy\AbstractMoveStrategy.cs" />
    <Compile Include="Assets\Scripts\Gameplay\UnitTest\RgFormationTest.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\SharpSteer2\BaseVehicle.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Entity\RgEntityProperty.cs" />
    <Compile Include="Assets\Scripts\Framework\UI\Common\BitmapText\MeshBitmapText.cs" />
    <Compile Include="Assets\Scripts\GameModules\Common\EventsDefine\GMRetEvent.cs" />
    <Compile Include="Assets\Redclue\ShieldFX_02\Scripts_ShieldFX02\CameraController.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\SharpSteer2\Obstacles\RectangleObstacle.cs" />
    <Compile Include="Assets\Scripts\GameModules\InGame\Views\UIBattleMain\FloatingText\UIFloatingTextSpawner.cs" />
    <Compile Include="Assets\Scripts\AutoGen\Table\IResTable.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Input\Cmds\CastSkillCmd.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\Room\RoomHeartbeatReq.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Physics\VolatilePhysics\VoltShape.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\cs\GetHeroListS.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\comm\GidStatus.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Converter\Part\RgMeshRendererPart.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Battle\Weapon\RgWeaponMortar.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Shape\RgOBBShape.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Mover\RgMover.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Input\Cmds\BuildingCmd.cs" />
    <Compile Include="Assets\Scripts\Framework\UI\UIRoot.cs" />
    <Compile Include="Assets\FogOfWar\Scripts\Revealers\RevealerDebug.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Physics\VolatilePhysics\Util\Pooling\VoltPool.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\SharpSteer2\Helpers\VehicleHelpers.cs" />
    <Compile Include="Assets\Scripts\GameFlow\Systems\RegisterGameSystemAttribute.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\comm\RetCode.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Battle\Weapon\RgWeaponTurret.cs" />
    <Compile Include="Assets\Scripts\GameModules\Common\GM\Commands\ExampleCommands.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\comm\RoleAcc.cs" />
    <Compile Include="Assets\Scripts\Framework\UI\Common\Animation\UIAnimationProperty.cs" />
    <Compile Include="Assets\FogOfWar\Scripts\GenerateInvertedCollider.cs" />
    <Compile Include="Assets\Scripts\Framework\UI\Common\ListView\Core\MListViewItem.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Message\RgMessageDispatcher.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\comm\RoomMode.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Map\Building\RgBuildingArea.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\HUD\HeadBar\UIHeadBarExplode.cs" />
    <Compile Include="Assets\Scripts\Framework\Deterministic\Lockstep\ServiceContainer.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Cameras\RgMainCameraLimitComponent.cs" />
    <Compile Include="Assets\GabrielAguiarProductions\Script\ParticleSystemController\SaveParticleSystemScript.cs" />
    <Compile Include="Assets\Scripts\Network\Core\SocketNonAlloc.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\cs\CustomRoomInfoS.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\cs\GetGameInfoC.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\Battle\GameStartNtf.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\RgLogicManager.cs" />
    <Compile Include="Assets\Redclue\ShieldFX_02\Scripts_ShieldFX02\Gun Scripts\Rotate.cs" />
    <Compile Include="Assets\StylizedTanks_StarterKit\Scripts\TankControllerSkinned.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\FixMath\F64Matrix.cs" />
    <Compile Include="Assets\Scripts\Framework\UI\Common\ListView\Extension\ListViewDragHandler.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Physics\VolatilePhysics\Internals\History\HistoryRecord.cs" />
    <Compile Include="Assets\Scripts\GameFlow\States\eGameEventIDs.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\FixMath\F64Vec4.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\uPools\Runtime\External\UniTask\AsyncObjectPoolBase.cs" />
    <Compile Include="Assets\Scripts\Network\Core\FrameRecord\RecordRoomService.cs" />
    <Compile Include="Assets\Scripts\AutoGen\Luban\vector2.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\DataTable\RgCrowdEntityMoverTemplates.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Input\CmdDefine.cs" />
    <Compile Include="Assets\Scripts\GameModules\InGame\Views\UIBattleMain\UIBattleMainView.cs" />
    <Compile Include="Assets\Arts\SetCartoonTanks\Demo_Table_Animations\Scripts\SCT_Ui_Char_Panel.cs" />
    <Compile Include="Assets\Scripts\Framework\AssetManagement\Runtime\AssetManifestSetting.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Brain\RgAIBrainCarrierTransportPlane.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\Crowds\MoveStrategy\IdleMoveStrategy.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\Room\RoomStartReq.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Converter\Part\Weapon\RgWeaponTrajectory.cs" />
    <Compile Include="Assets\GabrielAguiarProductions\Script\ParticleSystemController\Serializables.cs" />
    <Compile Include="Assets\Scripts\Framework\Core\MessageQueue.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\Room\CreateRoomReq.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\cs\KickOffS.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\cs\CustomRoomAddAIC.cs" />
    <Compile Include="Assets\Scripts\Framework\UI\Model\PropertyCollection.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Mover\Interpolation\RgInterpolation.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\Triangulation\Math\Geom2D.cs" />
    <Compile Include="Assets\FogOfWar\Scripts\Hiders\HiderDisableObjects.cs" />
    <Compile Include="Assets\Scripts\Framework\Collections\Fixed32List.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\Framework\Fin.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\Triangulation\Iterators\FromEdgeToRotatedEdges.cs" />
    <Compile Include="Assets\Scripts\Framework\AssetManagement\Runtime\AssetOperation.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\cs\GamePlayer.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Skill\RgSkillCaster_AtomicBomb.cs" />
    <Compile Include="Assets\Scripts\GameModules\AroundGame\BattlePrepare\CardSelect\UICardTempInfo.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\cs\HeartbeatS.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\Room\CachedFrameInputUnit.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Converter\Part\Beam\RgBeamEffect_MagicBeamStatic.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\Room\GameStartRsp.cs" />
    <Compile Include="Assets\StandaloneFileBrowser\StandaloneFileBrowserEditor.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\cs\GetRoleExtAttrS.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Physics\VolatilePhysics\VoltVector2.cs" />
    <Compile Include="Assets\Scripts\GameModules\Common\Views\UIGM\UIInputGMPanel.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Physics\VolatilePhysics\Internals\Collision\Contact.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Physics\VolatilePhysics\Internals\Collision\IContactListener.cs" />
    <Compile Include="Assets\Scripts\Gameplay\UnitTest\RgLockstepCmdTest.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\Room\SyncRoomInfoRsp.cs" />
    <Compile Include="Assets\Scripts\Framework\AssetManagement\Runtime\AssetHandle.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\DataTable\RgStateMachineTemplates.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Skill\RgSkillCaster.cs" />
    <Compile Include="Assets\Scripts\Framework\UI\Common\ListView\Core\ListViewItem.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\Framework\SyncTime.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\Framework\HeartBeat.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\SharpSteer2\Obstacles\BoxObstacle.cs" />
    <Compile Include="Assets\BulletEffectCore\packs\WebDemoAssets\scripts\Projectile.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Entity\Building\RgEntityRender_Building_ControlPoint.cs" />
    <Compile Include="Assets\Scripts\Framework\Deterministic\Math\Fix64TanLut.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\DataTable\RgEntityMovableTemplates.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Physics\VolatilePhysics\RayCast\VoltRayResult.cs" />
    <Compile Include="Assets\Scripts\GameModules\Common\Views\UIGM\UIGMCategoryItem.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\Triangulation\Data\ConstraintShape.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Input\Cmds\GMCmd.cs" />
    <Compile Include="Assets\ExplsionFx\packs\WebDemoAssets\scripts\ECProjectile.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Shape\RgShape.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\SharpSteer2\Helpers\Vector3Helpers.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\Room\RoomResultSync.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\cs\RoleCmdSeq.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\cs\GidInfo.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Bootstrap\IBootstrap.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\uPools\Runtime\External\UniTask\IAsyncObjectPool.cs" />
    <Compile Include="Assets\Buildings_constructor\Components\Doors_and_Windows\Doors.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Mover\RgPhysicsMover.cs" />
    <Compile Include="Assets\Scripts\GameModules\AroundGame\Login\UICreateRoleView.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Audio\RgAudioEffectPlayer.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\cs\CustomRoomChangePosC.cs" />
    <Compile Include="Assets\Scripts\Framework\Deterministic\Math\Fix64Vector3.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Entity\Skill\RgEntitySkill_CallTransportPlane.cs" />
    <Compile Include="Assets\Arts\SetCartoonTanks\Demo_Table_Animations\Scripts\SCAirCrafts_Actions.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Input\CmdMgr.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Brain\RgAIBrainParatrooper.cs" />
    <Compile Include="Assets\Scripts\Framework\UI\UIMgr.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\cs\RoleSimpleDataExt.cs" />
    <Compile Include="Assets\MagicArsenal\Effects\Scripts\MagicLightFade.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Utils\GizmosUtils.cs" />
    <Compile Include="Assets\Scripts\Framework\AssetManagement\Runtime\Ref.cs" />
    <Compile Include="Assets\Scripts\AutoGen\Luban\gameplay\PlanType.cs" />
    <Compile Include="Assets\Scripts\Gameplay\UnitTest\RgLockstepTest_InputService.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Converter\RgEntitySpecialEffects.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\Crowds\SteeringForce\FollowPathForce.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Cameras\IRgMainCameraComponent.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Map\RgMapLogic.cs" />
    <Compile Include="Assets\Scripts\GameFlow\Game.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\cs\CustomRoomCreateC.cs" />
    <Compile Include="Assets\Scripts\Framework\UI\View\UIView.cs" />
    <Compile Include="Assets\Scripts\Framework\Collections\Fixed16List.cs" />
    <Compile Include="Assets\Scripts\Framework\Collections\BitMask.cs" />
    <Compile Include="Assets\Scripts\GameModules\Common\Components\DragTarget.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\Triangulation\Iterators\FromVertexToIncomingEdges.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\SharpSteer2\SteerLibrary.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\DataTable\RgMoveModeTemplates.cs" />
    <Compile Include="Assets\Scripts\Framework\UI\Model\TProperty.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\SharpSteer2\Database\LocalityQueryProximityDatabase.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Physics\FixedMath.Net\src\Fix64TanLut.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Skill\RgSkillCaster_SelectTarget.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\Room\UpdateRoomInfoReq.cs" />
    <Compile Include="Assets\Scripts\Config\GameplayConstDef.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\Crowds\Flowfield\FlowfieldTile.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\Triangulation\Data\Constants.cs" />
    <Compile Include="Assets\ExplsionFx\scripts\ECparticleColorChangerMaster.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Converter\IRgEntityConverter.cs" />
    <Compile Include="Assets\Scripts\Framework\UI\Common\ListView\Core\ListView.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Entity\RgRenderEntityManager.cs" />
    <Compile Include="Assets\Scripts\GameFlow\Datas\ServerData\RoomData.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\Crowds\SteeringForce\AvoidMoveNeighborForce.cs" />
    <Compile Include="Assets\Scripts\AutoGen\Luban\gameplay\FogVisibilityType.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Mover\MoveMode\RgPhysicsSimulationMoveMode.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\DataTable\RgEntityTurretTemplates.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Input\CmdService.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Battle\Weapon\RgWeaponTurretProjectile.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\SharpSteer2\IVehicle.cs" />
    <Compile Include="Assets\Scripts\Network\Core\KCP\KCP.cs" />
    <Compile Include="Assets\Scripts\GameModules\InGame\Views\UIBattleMain\UIMoveJoystick.cs" />
    <Compile Include="Assets\Scripts\GameModules\Common\Components\Blurs\UIAreaBlurRect.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Map\RgMapManager.cs" />
    <Compile Include="Assets\Scripts\Framework\UI\Components\UIViewAnimation.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\cs\AreaInfo.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\Crowds\Recorder.cs" />
    <Compile Include="Assets\Scripts\GameFlow\Datas\SaveData\SaveDataBase.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\comm\CardGroupItem.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\cs\GetCardListS.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\Room\RoomLoadingProgress.cs" />
    <Compile Include="Assets\ExplsionFx\scripts\ECdestroyMe.cs" />
    <Compile Include="Assets\Scripts\AutoGen\Luban\gameplay\UnitCategory.cs" />
    <Compile Include="Assets\Scripts\AutoGen\Table\ResUnit_generated.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Skill\RgSkillCaster_EmergencyMobilization.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Map\RgVisionManager.cs" />
    <Compile Include="Assets\Scripts\Audio\AudioProvider.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Cameras\RgMainCamera.cs" />
    <Compile Include="Assets\Scripts\GameModules\InGame\Views\UIBattleMain\CardController\UICardMaterialApplier.cs" />
    <Compile Include="Assets\Unluck Software\Demo Resources\Scripts\HideCursor.cs" />
    <Compile Include="Assets\Scripts\Framework\Deterministic\Math\Fix64.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Battle\BattleTypeDef.cs" />
    <Compile Include="Assets\Scripts\Message\RGameMsgDispatcher.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Brain\RgAIBrainCarrier.cs" />
    <Compile Include="Assets\Scripts\Framework\UI\Components\UIViewScripting.cs" />
    <Compile Include="Assets\BulletEffectCore\packs\WebDemoAssets\scripts\ExplodingProjectile.cs" />
    <Compile Include="Assets\Scripts\Framework\UI\Common\ListView\Extension\NoTemplate\GeneralGridView.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Entity\RgEntityRender_Tank.cs" />
    <Compile Include="Assets\StandaloneFileBrowser\Sample\CanvasSampleOpenFileTextMultiple.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\Room\RoomBatchFrameReq.cs" />
    <Compile Include="Assets\Scripts\AutoGen\Table\ResCard_generated.cs" />
    <Compile Include="Assets\Scripts\Framework\UI\Common\ListView\Extension\MListViewSwipeHandler.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Battle\Effect\RgEffect_TakeDamage.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\cs\CustomRoomChangePosS.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\Crowds\SteeringForce\ArriveTargetForce.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\DataTable\RgWeaponTemplates.cs" />
    <Compile Include="Assets\Scripts\GameModules\Common\Views\UIGM\UIGMUinText.cs" />
    <Compile Include="Assets\MagicArsenal\Effects\Scripts\MagicBeamStatic.cs" />
    <Compile Include="Assets\Real Fire &amp; Smoke\Demo\Scripts\RealFireLoopScript.cs" />
    <Compile Include="Assets\Scripts\Network\Core\Message\NetworkMsg.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\RgGameManager.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Input\BinarySerializer.cs" />
    <Compile Include="Assets\Scripts\Config\EnvDef.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Utils\GeometryUtils.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\Framework\Syn.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Crowd\RgCrowdEntityManager.cs" />
    <Compile Include="Assets\Scripts\Framework\UI\Common\ListView\Core\ListViewBase.cs" />
    <Compile Include="Assets\Scripts\Framework\Core\MessageDefine.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Physics\VolatilePhysics\Internals\CheapList.cs" />
    <Compile Include="Assets\Scripts\GameFlow\States\GameStateRoom.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Entity\RgEntityManager.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\cs\CustomRoomStartC.cs" />
    <Compile Include="Assets\Scripts\GameModules\AroundGame\BattlePrepare\CardSelect\UICardGroupItem.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Converter\Part\Weapon\RgWeaponRocketPart.cs" />
    <Compile Include="Assets\Arts\SetCartoonTanks\Demo_Table_Animations\Scripts\SCT_Actions.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\cs\GetRoleSimpleC.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\cs\GetHeroListC.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\cs\SetCardGroupC.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\SharpSteer2\Database\LocalityQueryDatabase.cs" />
    <Compile Include="Assets\Arts\Archanor\Stylized Fire FX\Demo\Scripts\DragMouseOrbit.cs" />
    <Compile Include="Assets\Scripts\GameModules\AroundGame\Room\UIRoomCampPlayerItem.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Skill\RgSkillCaster_NoTarget.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\DataTable\RgGameTemplates.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\Crowds\SteeringForce\AvoidIdleNeighborForce.cs" />
    <Compile Include="Assets\Low Poly Rocks Pack\Bonus Assets\Scripts\FireLightControl.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Mover\Interpolation\RgBezierSplineInterpolation.cs" />
    <Compile Include="Assets\Scripts\AutoGen\Luban\gameplay\CardQuality.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Entity\Skill\RgSkillManager.cs" />
    <Compile Include="Assets\Scripts\AutoGen\Luban\Int2.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\cs\GameStartInfo.cs" />
    <Compile Include="Assets\Scripts\Framework\Deterministic\Math\Fix64Util.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\DataTable\RgPathFollowMoverTemplates.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\GM\RgGMCheatManager.cs" />
    <Compile Include="Assets\Scripts\GameModules\AroundGame\BattleFinish\UIBattleFinishLogic.cs" />
    <Compile Include="Assets\Scripts\GameModules\InGame\Views\UIBattleMain\CardController\UICardCancelCtrl.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\Crowds\SteeringForce\FlockAlignmentForce.cs" />
    <Compile Include="Assets\Scripts\Gameplay\UnitTest\RgStateMachineTest.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\DataTable\RgBuildingTemplates.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Battle\Effect\RgEffect_ExposeSight.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\Crowds\AvoidanceQuerySystem.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Brain\RgAIBrainAircraft.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Selection\RgBoxSelector.cs" />
    <Compile Include="Assets\Scripts\Framework\UI\Common\AssetCollection\AssetCollection.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\Room\EnterRoomReq.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Group\RgGroupManger.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\Triangulation\Data\Edge.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\SharpSteer2\Pathway\PolylinePathway.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Physics\VolatilePhysics\Internals\History\HistoryBuffer.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Battle\Effect\RgEffect_ChangeDamageResistance.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\cs\UpdateClientDataC.cs" />
    <Compile Include="Assets\Real Fire &amp; Smoke\Scripts\RealFireLightFlicker.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Physics\VolatilePhysics\Internals\Broadphase\IBroadphase.cs" />
    <Compile Include="Assets\Scripts\GameModules\Common\Utilities\SecurityUtility.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\SharpSteer2\Helpers\MatrixHelpers.cs" />
    <Compile Include="Assets\BulletEffectCore\packs\WebDemoAssets\scripts\projectileActor.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Converter\Part\Weapon\RgWeaponTeslaTowerPart.cs" />
    <Compile Include="Assets\Scripts\GameFlow\Systems\SaveDataSystem.cs" />
    <Compile Include="Assets\Scripts\Framework\UI\Components\UIViewDepth.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\comm\RoomStatus.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Animation\RgAnimationManager_Airplane.cs" />
    <Compile Include="Assets\Scripts\AutoGen\Table\ResMap_generated.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Entity\RgEntityRender_Projectile.cs" />
    <Compile Include="Assets\Scripts\GameFlow\Systems\IGameSystem.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\HUD\HeadBar\RgEntityHeadBar.cs" />
    <Compile Include="Assets\Scripts\Framework\UI\Common\Animation\UIAnimationTrack.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\PtoHelper_Room.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\SharpSteer2\Database\IProximityDatabase.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Core\Variant.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\DataTable\RgMapInfoTemplates.cs" />
    <Compile Include="Assets\Scripts\Framework\UI\Common\Animation\UIAnimation.cs" />
    <Compile Include="Assets\Scripts\GameModules\Common\Components\ContentProvider.cs" />
    <Compile Include="Assets\Scripts\Framework\UI\Common\ListView\Core\MListView.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Converter\Part\RgParachutePart.cs" />
    <Compile Include="Assets\Scripts\GameModules\Common\GM\GMCommand.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Utils\MathUtils.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Cameras\RgMainCameraAnimationComponent.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\uPools\Runtime\External\Addressables\AsyncAddressableGameObjectPool.cs" />
    <Compile Include="Assets\Scripts\Framework\UI\Common\BitmapText\UIBitmapText.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\cs\HeroUpdateAction.cs" />
    <Compile Include="Assets\Scripts\Gameplay\UnitTest\APITest\TestCatmullRomSpline.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Brain\RgFollowMarchingRoute.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\LogicParts\RgLogicPart.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Entity\RgEntityRender_Airport.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\cs\RoleSimpleDataBase.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Core\Crc32.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Audio\RgAudioPlayer.cs" />
    <Compile Include="Assets\Scripts\Framework\Deterministic\Lockstep\LockstepMessageBody.cs" />
    <Compile Include="Assets\Scripts\Framework\UI\Common\Resolution\ResolutionMonitor.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Battle\BuildingTypeDef.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Entity\Building\RgEntity_Building_CloningVats.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Entity\Building\RgEntity_Building_Factory.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\SharpSteer2\Obstacles\SphericalObstacle.cs" />
    <Compile Include="Assets\Scripts\GameFlow\Systems\NetworkSystems\BattleNetworkSystem_NetService.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Crowd\RgObstacleAvoidanceParams.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Battle\Effect\RgEffect_Repair.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Cameras\RgMainCameraJumpComponent.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\Room\RoomSetCardGroupRsp.cs" />
    <Compile Include="Assets\Scripts\Audio\AudioMgr.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Converter\Part\RgAiportParkingSpot.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Entity\RgEntityState.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\cs\HeroUnlockC.cs" />
    <Compile Include="Assets\Scripts\Audio\DebugAudio.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Entity\RgWorld.cs" />
    <Compile Include="Assets\ExplsionFx\packs\WebDemoAssets\scripts\ECprojectileActor.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\cs\GetCardGroupS.cs" />
    <Compile Include="Assets\Scripts\Framework\Deterministic\Lockstep\Frame.cs" />
    <Compile Include="Assets\Scripts\GameModules\InGame\Views\UIBattleMain\CardController\UICardFrameApplier.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\cs\CardUnlockC.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\Triangulation\Data\Mesh.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Battle\Effect\RgEffect_ModifyEntityState.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\comm\CustomRoomStatus.cs" />
    <Compile Include="Assets\Scripts\GameFlow\States\GameStateLogin.cs" />
    <Compile Include="Assets\Scripts\GameModules\Common\Utilities\StringFormatter.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Battle\Weapon\RgWeaponRifle.cs" />
    <Compile Include="Assets\ExplsionFx\packs\StylizedExplosionPack1\WebDemo\scripts\ExplodingProjectileExplosion1.cs" />
    <Compile Include="Assets\Buildings_constructor\Scripts\Rotate.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\Triangulation\Data\Face.cs" />
    <Compile Include="Assets\Scripts\Framework\UI\Components\UIViewEnable.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Entity\Buff\RgBuff.cs" />
    <Compile Include="Assets\Arts\Archanor\Stylized Fire FX\Demo\Scripts\LoadSceneOnClick.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\cs\GetGameInfoS.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\cs\GetRoleBaseAttrS.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\Framework\CheckLatency.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\Crowds\EntityAnnotationServerice.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\uPools\Runtime\External\Addressables\AddressableGameObjectPool.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Entity\Buff\RgBuffController.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\cs\GetCardListC.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\SharpSteer2\SimpleVehicle.cs" />
    <Compile Include="Assets\Scripts\GameModules\AroundGame\Room\UIRoomCampView.cs" />
    <Compile Include="Assets\Scripts\GameModules\Common\EventsDefine\GameplayEvent.cs" />
    <Compile Include="Assets\Redclue\ShieldFX_02\Scripts_ShieldFX02\Gun Scripts\Fire.cs" />
    <Compile Include="Assets\Scripts\GameModules\AroundGame\BattlePrepare\UIHeroSelectView.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\LogicParts\RgLogicPart_WeaponProxy.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Battle\Weapon\RgWeaponExplode.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Entity\Skill\RgEntitySkill_AtomicBomb.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\SearchCondition\RgSearchCondition.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Entity\RgEntityCellManager.cs" />
    <Compile Include="Assets\Scripts\GameFlow\Systems\DataProviderSystem.cs" />
    <Compile Include="Assets\Scripts\GameModules\InGame\Views\InputSystem\GameInputSystem.cs" />
    <Compile Include="Assets\Scripts\GameModules\InGame\Views\InputSystem\Joystick\UIJoystick.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Message\RgMessage.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\cs\CustomRoomAddAIS.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\cs\CustomRoomCreateS.cs" />
    <Compile Include="Assets\Scripts\Network\NetworkManager.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\Room\RoomExitReq.cs" />
    <Compile Include="Assets\Scripts\Framework\UI\Common\ListView\Core\Easings.cs" />
    <Compile Include="Assets\Scripts\GameFlow\Systems\BGMSystem.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Utils\FPSUtil.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\DataTable\RgMoverTargetTemplates.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Physics\FixedMath.Net\src\Fix64SinLut.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Converter\Part\Weapon\RgWeaponTurretPart.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Battle\Weapon\RgWeaponDefault.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Converter\Part\Weapon\RgWeaponPart.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\cs\GameInfo.cs" />
    <Compile Include="Assets\Scripts\GameModules\Common\Utilities\UIUtility.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Entity\RgRenderEntityCatalog.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\FixPointCS\FixedUtil.cs" />
    <Compile Include="Assets\Scripts\Gameplay\UnitTest\APITest\SegmentAABBIntersection.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\FixPointCS\Fixed64.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\StateMachine\AtomAbilities\RgAbilityAttack.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Brain\RgAIBrainMedical.cs" />
    <Compile Include="Assets\FogOfWar\Scripts\Revealers\FogOfWarRevealer3D.cs" />
    <Compile Include="Assets\Scripts\GameFlow\States\GameStateBase.cs" />
    <Compile Include="Assets\Scripts\GameFlow\Datas\SaveData\CardSelectTempData.cs" />
    <Compile Include="Assets\Scripts\GameModules\AroundGame\BattlePrepare\CardSelect\UICardSelectionLogic.cs" />
    <Compile Include="Assets\Scripts\GameModules\InGame\Views\UIBattleBroadcast\UIBroadcastItemBase.cs" />
    <Compile Include="Assets\Scripts\AutoGen\Luban\gameplay\ResUnit.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\cs\GetAreaListS.cs" />
    <Compile Include="Assets\Scripts\Framework\UI\Common\ListView\Extension\Effect\DragListViewDisableDragIfFits.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Converter\Part\Weapon\RgWeaponMortarPart.cs" />
    <Compile Include="Assets\FogOfWar\Scripts\UnityBugWorkaround.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\Framework\Ack.cs" />
    <Compile Include="Assets\Scripts\GameModules\InGame\Views\UIBattleMain\CardController\UICardItemCtrl.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\Crowds\ICrowdEntityActor.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\Triangulation\AI\PathFinder.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\HUD\FloatingText\UIFloatingBase.cs" />
    <Compile Include="Assets\MagicArsenal\Demo\Scripts\MagicEffectCycler.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\cs\RoleExtAttr.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Brain\RgAiBrainUnitFuShe.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Animation\IRgAnimationInterface.cs" />
    <Compile Include="Assets\Scripts\GameFlow\Datas\ServerData\Player.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\Util\Draw.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\HUD\HeadBar\UIHeadBarFactory.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Selection\RgSelectionView.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\cs\GetRoleSimpleS.cs" />
    <Compile Include="Assets\BulletEffectCore\packs\WebDemoAssets\scripts\CameraShakeCaller.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Crowd\RgObstacleCircle.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Physics\VolatilePhysics\Extensions\VoltExplosion.cs" />
    <Compile Include="Assets\Scripts\Framework\UI\Common\MeshEffect\CircleMeshEffect.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Path\RgPathFinder.cs" />
    <Compile Include="Assets\Scripts\Framework\UI\Common\BitmapText\BitmapFont.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Core\Rectangle.cs" />
    <Compile Include="Assets\Low Poly Rocks Pack\Bonus Assets\Scripts\CloudsControl.cs" />
    <Compile Include="Assets\EffectCore\packs\WebDemoAssets\scripts\ECelectricActor.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\cs\GetCardGroupC.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Map\Building\RgBuildingPlacementHandler.cs" />
    <Compile Include="Assets\FogOfWar\Scripts\Hiders\PartialHider.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Entity\RgEntityRender_Carrier.cs" />
    <Compile Include="Assets\Scripts\Gameplay\UnitTest\RgAvoidanceTest.cs" />
    <Compile Include="Assets\RTS Effects\Demo\Scripts\RTSSceneSelect.cs" />
    <Compile Include="Assets\Scripts\GameModules\Common\Views\UIGM\UIGMLogic.cs" />
    <Compile Include="Assets\StylizedTanks_StarterKit\Scripts\TankController.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\DataTable\RgFormationTemplates.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\Framework\Any.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\cs\HeroUpdateS.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\Room\GameStartReq.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Skill\RgSkillCaster_Repair.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Entity\Building\RgEntity_Building_Skill_AutomaticallyAddBuff.cs" />
    <Compile Include="Assets\FogOfWar\Scripts\FogOfWarWorld.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\DataTable\RgSearchConditionTemplates.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Physics\VolatilePhysics\Unity\VolatileUtility.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Cameras\RgMainCameraMoveComponent.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Physics\VolatilePhysics\VoltBody.cs" />
    <Compile Include="Assets\MagicArsenal\Demo\Scripts\MagicDragMouseOrbit.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\Crowds\MovableEntity.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\Crowds\RecordOperations.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Brain\RgMarchingRouteManager.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Entity\RgEntityRender_Paratrooper.cs" />
    <Compile Include="Assets\Scripts\GameModules\AroundGame\PreLogin\UIPreLoginView.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\Triangulation\View\SimpleView.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Mover\RgProjectileMover.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Crowd\RgObstacleAvoidanceDebugData.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\uPools\Runtime\IPoolCallbackReceiver.cs" />
    <Compile Include="Assets\StandaloneFileBrowser\Sample\CanvasSampleSaveFileImage.cs" />
    <Compile Include="Assets\Arts\Archanor\Stylized Fire FX\Demo\Scripts\FireProjectile.cs" />
    <Compile Include="Assets\Scripts\Framework\Game\Manager.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\Room\RoomSetCardGroupReq.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\cs\CSCardInfo.cs" />
    <Compile Include="Assets\Scripts\Framework\UI\Components\UIViewBGM.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\Room\RoomEnterReq.cs" />
    <Compile Include="Assets\Scripts\GameModules\InGame\Views\UIBattleMain\PhaseController\UIBattlePhaseApplier.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Entity\RgEntityTags.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Converter\RgRenderTemplateManager.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Core\RttiClass.cs" />
    <Compile Include="Assets\ExplsionFx\packs\WebDemoAssets\scripts\ECCameraShakeProjectile.cs" />
    <Compile Include="Assets\Scripts\GameModules\Common\GM\GMSystem.cs" />
    <Compile Include="Assets\Scripts\AutoGen\Table\ResGlobal_generated.cs" />
    <Compile Include="Assets\Hovl Studio\Toon Projectiles 2\Scripts\AutoDestroyPS.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\comm\RoleLoc.cs" />
    <Compile Include="Assets\FogOfWar\Scripts\Hiders\HiderToggleObjects.cs" />
    <Compile Include="Assets\Scripts\GameModules\InGame\UnitTestHelper.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Crowd\Gizmos\UnityGizmosDrawer.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\DataTable\RgEntityAirplaneTemplates.cs" />
    <Compile Include="Assets\Scripts\Network\NetworkHelper.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Skill\RgSkillCaster_MissileStrike.cs" />
    <Compile Include="Assets\Scripts\Framework\UI\View\UIViewComponent.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\cs\GetAreaListC.cs" />
    <Compile Include="Assets\Scripts\GameModules\Common\Components\Blurs\UITextureBlur.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Cameras\RgMainCameraAdaptScreenComponent.cs" />
    <Compile Include="Assets\Scripts\Debug\DebugMeshCanvas.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Physics\VolatilePhysics\Gizmos\IGizmosDrawer.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Bootstrap\WorldInitializeData.cs" />
    <Compile Include="Assets\FogOfWar\Scripts\Hiders\HiderBehavior.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\Framework\Weapon.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Battle\Weapon\RgWeaponCollection.cs" />
    <Compile Include="Assets\Scripts\Framework\UI\Interfaces\IViewHolder.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\SharpSteer2\Database\ITokenForProximityDatabase.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Physics\VolatilePhysics\Util\Pooling\IVoltPoolable.cs" />
    <Compile Include="Assets\Scripts\Config\DataTable\DataMgr.cs" />
    <Compile Include="Assets\Scripts\AutoGen\Luban\ResWhiteList.cs" />
    <Compile Include="Assets\RTS Effects\Demo\Scripts\RTSClickSpawn.cs" />
    <Compile Include="Assets\BulletEffectCore\scripts\particleColorChangerMaster.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Converter\Part\RgAirplanePart.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Input\Cmds\SpawnUnitCmd.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Mover\RgMoveTarget.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Entity\Building\RgEntityRender_Building.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\Crowds\SteeringForce\AbstractSteeringForce.cs" />
    <Compile Include="Assets\StandaloneFileBrowser\IStandaloneFileBrowser.cs" />
    <Compile Include="Assets\Scripts\GameModules\Common\Components\DontDestroyOnLoad.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Battle\RgDamage.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\Crowds\AbstractCrowdEntity.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\Triangulation\Factories\RectMesh.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Effect\RgSimulateSpecialEffect.cs" />
    <Compile Include="Assets\Scripts\Framework\Deterministic\Lockstep\IService.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\Crowds\SteeringForce\FlockCohesionForce.cs" />
    <Compile Include="Assets\Scripts\Framework\Core\Logger.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Entity\RgEntityRender_RocketHuman.cs" />
    <Compile Include="Assets\StandaloneFileBrowser\StandaloneFileBrowserLinux.cs" />
    <Compile Include="Assets\Arts\Archanor\Stylized Fire FX\Scripts\lightScript.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\DataTable\RgEntityProjectileTemplates.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Battle\Weapon\RgWeaponBomb.cs" />
    <Compile Include="Assets\Scripts\AutoGen\Table\GlobalConst_generated.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\comm\AccountType.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Debug\DrawLogicGunDebuger.cs" />
    <Compile Include="Assets\Scripts\Framework\UI\Common\BitmapText\BitmapTextGenerator.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\Room\RoomStartRsp.cs" />
    <Compile Include="Assets\Scripts\Gameplay\UnitTest\RgSkillCasterHub.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\DataTable\RgEntitySkillTemplates.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\RgLogicWorld.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\GlobalVaraint.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\cs\GetCmdSeqC.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\SharpSteer2\Obstacles\ObstacleGroup.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Message\RgMessageHandler.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Entity\RgEntityAirplane.cs" />
    <Compile Include="Assets\Hovl Studio\Toon Projectiles 2\Scripts\ProjectileMover.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Serialize\RgArchive.cs" />
    <Compile Include="Assets\Redclue\ShieldFX_02\Scripts_ShieldFX02\CollisonShieldInstantiate.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\Room\FrameInputUnit.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Map\RgVision.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Physics\VolatilePhysics\VoltWorld.cs" />
    <Compile Include="Assets\Scripts\Framework\AssetManagement\Runtime\AssetUtility.cs" />
    <Compile Include="Assets\MagicArsenal\Demo\Scripts\MagicBeamScript.cs" />
    <Compile Include="Assets\StandaloneFileBrowser\Sample\CanvasSampleOpenFileText.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\Room\RoomAddAIReq.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Battle\Effect\RgEffect_CreateEffectField.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Input\Cmds\MoveCmd.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\Main\PathfindingMoudle.cs" />
    <Compile Include="Assets\BulletEffectCore\scripts\destroyMe.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Battle\Effect\RgEffect_ChangeAttribute.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Entity\Building\RgEntityRender_Building_Base.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\Util\Crc32.cs" />
    <Compile Include="Assets\FogOfWar\Scripts\Hiders\FogOfWarHider.cs" />
    <Compile Include="Assets\Scripts\Framework\AssetManagement\Runtime\AssetRequest.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\Crowds\UnMovableEntity.cs" />
    <Compile Include="Assets\StandaloneFileBrowser\Sample\BasicSample.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\Room\RoomBaseInfo.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\cs\GetRoleC.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\Room\RoomReqBody.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Debug\PlayAnimationDebuger.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\Crowds\ILocalBoundaryQuerier.cs" />
    <Compile Include="Assets\Scripts\Framework\Core\ObjectPoolManager.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\FixMath\F32Vec3.cs" />
    <Compile Include="Assets\Scripts\GameModules\AroundGame\Lobby\UIRoomListCtrl.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\SharpSteer2\Helpers\Utilities.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\DataTable\RgEffectFieldTemplates.cs" />
    <Compile Include="Assets\FogOfWar\Demo\Scripts\TeamsDemo.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Entity\RgEntityRender_Turret.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Cameras\RgMainCameraManager.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Converter\Part\RgSpecialEffectPart.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Debug\LockTransformDebuger.cs" />
    <Compile Include="Assets\Scripts\Framework\UI\Common\MeshEffect\TextGradientEffect.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\cs\GetRoleExtAttrC.cs" />
    <Compile Include="Assets\Scripts\GameModules\InGame\Views\UIBattleMain\UIBattleMainViewTemp.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Converter\Part\Skill\RgSkillFrozenPart.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\MapBlock\RgMapBlock.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\cs\CustomRoomJoinS.cs" />
    <Compile Include="Assets\GabrielAguiarProductions\Script\ParticleSystemController\ParticleSystemController.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Utils\Vec3Utils.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Sync\RgSyncLogBuffer.cs" />
    <Compile Include="Assets\Scripts\GameFlow\States\GameStateBattle.cs" />
    <Compile Include="Assets\Scripts\Framework\Deterministic\Lockstep\AIService.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Mover\RgCurvePathMover.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\Room\RoomEnterRsp.cs" />
    <Compile Include="Assets\MagicArsenal\Effects\Scripts\MagicRotation.cs" />
    <Compile Include="Assets\Scripts\Framework\Deterministic\Lockstep\World.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Bootstrap\ClientBootstrap.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\comm\GameResult.cs" />
    <Compile Include="Assets\Scripts\Framework\Deterministic\Lockstep\PlayerInput.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\SharpSteer2\Helpers\RandomHelpers.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\cs\CSHeroInfo.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Entity\Building\RgEntity_Building_Producer.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\cs\CardUnlockS.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Mover\RgCrowdEntityMover.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\DataTable\RgBuffTemplates.cs" />
    <Compile Include="Assets\Scripts\AutoGen\Luban\gameplay\CardFaction.cs" />
    <Compile Include="Assets\Scripts\GameModules\Gen\UINames.cs" />
    <Compile Include="Assets\Scripts\Framework\UI\Components\UIViewActive.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Battle\Weapon\RgWeaponBase.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Physics\VolatilePhysics\Gizmos\Colors.cs" />
    <Compile Include="Assets\Engine Particle\Script\Particles.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Converter\Part\Beam\RgBeamEffect_Default.cs" />
    <Compile Include="Assets\Scripts\Framework\Core\MessageDispatcher.cs" />
    <Compile Include="Assets\Scripts\Gameplay\UnitTest\APITest\QuaternionTest.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\DataTable\RgEntityTemplates.cs" />
    <Compile Include="Assets\StandaloneFileBrowser\Sample\CanvasSampleOpenFileImage.cs" />
    <Compile Include="Assets\StandaloneFileBrowser\StandaloneFileBrowserMac.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\Crowds\Map.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Mover\RgFollowSegmentPath.cs" />
    <Compile Include="Assets\Arts\SetCartoonTanks\Demo_Table_Animations\Scripts\Anims_Scripts_SCT.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Converter\Part\RgAirportPart.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Entity\RgEntityRender_Airplane.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Entity\RgLogicEntityManager.cs" />
    <Compile Include="Assets\Scripts\GameFlow\States\GameContext.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\Battle\FrameHashNtf.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Entity\Building\RgEntity_Building_OilDerrick.cs" />
    <Compile Include="Assets\Arts\Archanor\Stylized Fire FX\Demo\Scripts\ButtonScript.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\SharpSteer2\Helpers\LocalSpaceBasisHelpers.cs" />
    <Compile Include="Assets\ExplsionFx\packs\StylizedExplosionPack1\WebDemo\scripts\instantiateEffectCallerExplosion1.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\DataTable\RgProjectileMoverTemplates.cs" />
    <Compile Include="Assets\ExplsionFx\packs\WebDemoAssets\scripts\ECExplodingProjectile.cs" />
    <Compile Include="Assets\Scripts\GameModules\AroundGame\Lobby\UILobbyView.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\PtoHelper_Lobby.cs" />
    <Compile Include="Assets\Scripts\Framework\Core\UnityObjectPool.cs" />
    <Compile Include="Assets\Scripts\Framework\UI\Common\BitmapText\BitmapGlyph.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Converter\Part\Skill\RgSkillAtomicBombPart.cs" />
    <Compile Include="Assets\Scripts\Framework\AssetManagement\Runtime\LowLevelLoader.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Physics\VolatilePhysics\Util\VoltUtil.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\Triangulation\AI\Funnel.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\Room\RoomInfo.cs" />
    <Compile Include="Assets\Scripts\Gameplay\UnitTest\RgUnitTest_API.cs" />
    <Compile Include="Assets\Scripts\Framework\UI\Interfaces\IViewComponent.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\DataTable\RgFlyTowardsPositionTemplates.cs" />
    <Compile Include="Assets\Scripts\Framework\Deterministic\Lockstep\Simulator.cs" />
    <Compile Include="Assets\Scripts\GameModules\Common\Views\UICommonBorderTips\UICommonBorderTipsView.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\FixMath\F32.cs" />
    <Compile Include="Assets\Scripts\GameModules\InGame\TestSolider.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\Framework\Color.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\cs\GamePlayerStat.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Shape\RgCircleShape.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Curve\BezierPoint3D.cs" />
    <Compile Include="Assets\Scripts\GameModules\Common\Components\UIRaycastElement.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\cs\SetCardGroupS.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Mover\MoveMode\RgEntityMoveMode.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Battle\Effect\RgEffectBase.cs" />
    <Compile Include="Assets\Scripts\GameModules\Common\Components\PlayerHeadCtrl.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Converter\Part\RgSpawnInfoPart.cs" />
    <Compile Include="Assets\Scripts\GameModules\Common\Utilities\StringUtility.cs" />
    <Compile Include="Assets\Scripts\Framework\Deterministic\Math\Fix64Quaternion.cs" />
    <Compile Include="Assets\Scripts\AutoGen\Table\ResMode_generated.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\DataTable\RgPlayerTemplates.cs" />
    <Compile Include="Assets\Scripts\Network\Core\NetworkThread.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Timer\RgTimerManager.cs" />
    <Compile Include="Assets\Scripts\Network\Core\IPEndPointNonAlloc.cs" />
    <Compile Include="Assets\Scripts\GameFlow\Systems\RoomSystem.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\FixMath\F64Quat.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\DataTable\RgFollowSegmentPathTemplates.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Physics\VolatilePhysics\Internals\Collision\Collision.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\LogicParts\RgLogicPart_ExpandTerritory.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\DataTable\RgFlowFieldTemplates.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\Triangulation\Math\RandGenerator.cs" />
    <Compile Include="Assets\Scripts\GameFlow\Systems\NetworkSystems\LobbyNetworkSystem.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\HUD\Text\RgEntityTextDisplay.cs" />
    <Compile Include="Assets\Scripts\Config\DataTable\ResUtility.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Entity\Building\RgEntityRender_Building_Factory.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\Triangulation\Data\Vertex.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\Triangulation\Iterators\FromMeshToVertices.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Skill\RgSkillCaster_Frozen.cs" />
    <Compile Include="Assets\Scripts\Gameplay\UnitTest\RgUnitTest_SkillCaster.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\cs\RoleBaseAttr.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Skill\Effect\RgSkillCaster_RangeEffect.cs" />
    <Compile Include="Assets\FogOfWar\Scripts\Hiders\HiderDisableRenderers.cs" />
    <Compile Include="Assets\Scripts\GameModules\AroundGame\Room\UIRoomView.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\Room\RoomRsp.cs" />
    <Compile Include="Assets\MagicArsenal\Demo\Scripts\MagicLoadSceneOnClick.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Entity\Building\RgEntity_Building_Skill.cs" />
    <Compile Include="Assets\Scripts\GameFlow\Systems\LoginSystem.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\Room\RoomHeartbeatRsp.cs" />
    <Compile Include="Assets\Low Poly Rocks Pack\Bonus Assets\Scripts\SunControl.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\cs\GetCmdSeqS.cs" />
    <Compile Include="Assets\Scripts\Network\Core\Message\MsgBinding.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\DataTable\RgEntityPropertyTemplates.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Entity\Building\RgEntity_Building_ControlPoint.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\Triangulation\Data\ConstraintSegment.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\Crowds\MoveStrategy\IMoveStrategy.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Animation\RgAnimationManager_CartoonTank.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\cs\StartupS.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\DataTable\RgEntityAirportTemplates.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Game\RgGameAPI.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\comm\GameMode.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\Room\RoomStatusSync.cs" />
    <Compile Include="Assets\Scripts\GameModules\Common\Views\UICommonTips\UICommonTipsView.cs" />
    <Compile Include="Assets\Scripts\Network\Core\KCP\KCPService.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\LogicParts\RgLogicPart_ExecuteEffect.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Map\RgMap.cs" />
    <Compile Include="Assets\FogOfWar\Demo\Scripts\BlinkingRevealer.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\cs\GetGidsC.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\Triangulation\Iterators\FromVertexToHoldingFaces.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Audio\RgAudioBroadcastPlayer.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Debug\NeverDestroyDebuger.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\Crowds\MovableEntityDebuger.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Entity\Building\RgEntity_Building_Base.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\Crowds\SteeringForce\ForwardMoveForce.cs" />
    <Compile Include="Assets\Scripts\Network\Protocols\cs\GetGidsS.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Physics\VolatilePhysics\Internals\IIndexedValue.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\SharpSteer2\NullAnnotationService.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\Pathfinding\Triangulation\Iterators\FromMeshToFaces.cs" />
    <Compile Include="Assets\Scripts\GameFlow\Datas\SaveData\GlobalSaveData.cs" />
    <Compile Include="Assets\Scripts\Framework\AssetManagement\Runtime\AssetManifest.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Render\Utils\MathUtils.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Common\FixMath\F64Vec3.cs" />
    <Compile Include="Assets\Scripts\Framework\Deterministic\Lockstep\LockstepUtil.cs" />
    <Compile Include="Assets\Scripts\GameModules\InGame\Views\UIBattleMain\CardController\UICardLayoutApplier.cs" />
    <Compile Include="Assets\Buildings_constructor\Scripts\Cycle_moving.cs" />
    <Compile Include="Assets\Scripts\Gameplay\Core\Logic\Group\RgEntityGroup.cs" />
    <Compile Include="Assets\Scripts\GameModules\Common\Views\UIGM\UIGMItemCtrls\UIGMDropdownItem.cs" />
    <Compile Include="Assets\Scripts\Framework\Deterministic\Math\Fix64Vector2.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Assets\RockyDesert\Shaders\Master_Side_Rock.shader" />
    <None Include="Assets\Puffy_Smoke_Readme.txt" />
    <None Include="Assets\polyperfect\Low Poly War Pack\TextMesh Pro\Resources\Shaders\TMPro.cginc" />
    <None Include="Assets\JMO Assets\Toony Colors Pro\Shader Templates 2\SG2_Template_URP.txt" />
    <None Include="Assets\JMO Assets\Toony Colors Pro\Shader Templates 2\Modules\Module_Triplanar Sampling.txt" />
    <None Include="Assets\JMO Assets\Toony Colors Pro\Shaders Generated\Examples\Default\Dissolve.shader" />
    <None Include="Assets\JMO Assets\Toony Colors Pro\Shaders\Standard PBS\TCP2_Standard PBS Outline.shader" />
    <None Include="Assets\sound\Menu\菜单音效对照.txt" />
    <None Include="Assets\StreamingAssets\Audio\GeneratedSoundBanks\Android\PlatformInfo.xml" />
    <None Include="Assets\JMO Assets\Toony Colors Pro\Shaders\Legacy\Include\TCP2_Outline_Behind_SM2.shader" />
    <None Include="Assets\polyperfect\Low Poly War Pack\TextMesh Pro\Resources\Shaders\TMP_SDF-Mobile Overlay.shader" />
    <None Include="Assets\MagicArsenal\Readme.txt" />
    <None Include="Assets\Arts\Vefects\Explosions VFX URP\Shared\Shaders\SH_Vefects_URP_VFX_Sprite_Debris.shader" />
    <None Include="Assets\Arts\Vefects\Explosions VFX URP\Shared\Shaders\SH_Vefects_URP_VFX_Pyro_Thin_Smoke.shader" />
    <None Include="Assets\JMO Assets\Toony Colors Pro\Shader Templates 2\Modules\Module_VertexDisplacement.txt" />
    <None Include="Assets\PolygonSciFiWorlds\Shaders\SyntyStudios_NoFogUnlit.shader" />
    <None Include="Assets\JMO Assets\Toony Colors Pro\Shader Templates 2\Modules\Module_Outline.txt" />
    <None Include="Assets\JMO Assets\Toony Colors Pro\Shaders\Hybrid 2\TCP2 Hybrid 2 Include.cginc" />
    <None Include="Assets\Arts\Vefects\Explosions VFX URP\Shared\Shaders\SH_Vefects_URP_VFX_Pyro_MV_Alpha.shader" />
    <None Include="Assets\JMO Assets\Toony Colors Pro\Shaders Generated\Examples\SG2\Stylized Water.shader" />
    <None Include="Assets\Resources\Shaders\UI\UI_Blur.shader" />
    <None Include="Assets\JMO Assets\Toony Colors Pro\Shader Templates 2\Modules\Module_Terrain.txt" />
    <None Include="Assets\Arts\Vefects\Explosions VFX URP\Explosions VFX\Sci-Fi\Shaders\SH_Vefects_URP_VFX_EMP_Sphere.shader" />
    <None Include="Assets\JMO Assets\Toony Colors Pro\Shaders Generated\Examples\SG2\Detail Texture Simple.shader" />
    <None Include="Assets\JMO Assets\Toony Colors Pro\Shaders\Standard PBS\TCP2_PBS_Main.cginc" />
    <None Include="Assets\Real Fire &amp; Smoke\Shaders\RealRefractionShader.shader" />
    <None Include="Assets\polyperfect\Low Poly War Pack\TextMesh Pro\Resources\Shaders\TMP_Bitmap.shader" />
    <None Include="Assets\JMO Assets\Toony Colors Pro\Shader Templates 2\Modules\Module_DepthTexture.txt" />
    <None Include="Assets\JMO Assets\Toony Colors Pro\Shaders Generated\Examples\SG2\Vertex Color Albedo.shader" />
    <None Include="Assets\ExplsionFx\packs\StylizedExplosionPack1\Tips &amp; Documentations StylizedExplosionPack1.txt" />
    <None Include="Assets\JMO Assets\Toony Colors Pro\Shader Templates 2\Modules\Module_MatCap.txt" />
    <None Include="Assets\Real Fire &amp; Smoke\Shaders\RefractionShader.shader" />
    <None Include="Assets\PolygonSciFiWorlds\Shaders\SyntyStudios_ColourChange.shader" />
    <None Include="Assets\EffectCore\packs\StylizedElectricPack\Tips &amp; Documentations StylizedElectricPack1.txt" />
    <None Include="Assets\Hovl Studio\Realistic explosions\Shaders\ExplosionLit.shader" />
    <None Include="Assets\Arts\Vefects\Explosions VFX URP\Shared\Shaders\SH_Vefects_URP_VFX_Flash.shader" />
    <None Include="Assets\StreamingAssets\Audio\GeneratedSoundBanks\Android\Battle_Common.txt" />
    <None Include="Assets\Hovl Studio\Toon Projectiles 2\Demo scene\Readme.txt" />
    <None Include="Assets\JMO Assets\Toony Colors Pro\Shaders Generated\Examples\Default\HSV.shader" />
    <None Include="Assets\JMO Assets\Toony Colors Pro\Shaders Generated\Examples\Default\Old School.shader" />
    <None Include="Assets\JMO Assets\Toony Colors Pro\Shader Templates 2\Modules\Module_Dissolve.txt" />
    <None Include="Assets\explosion1\Shaders\Explosion_lab.shader" />
    <None Include="Assets\RockyDesert\Shaders\M_Clouds.shader" />
    <None Include="Assets\JMO Assets\Toony Colors Pro\Shaders\Standard PBS\TCP2_Standard PBS Outline Blended (Specular).shader" />
    <None Include="Assets\JMO Assets\Toony Colors Pro\Shaders Generated\Examples\SG2\Animated Dissolve.shader" />
    <None Include="Assets\Arts\Vefects\Explosions VFX URP\Shared\Shaders\SH_Vefects_URP_VFX_Emissive_Wicks.shader" />
    <None Include="Assets\FogOfWar\Shaders\Resources\FOW_GrayScale.shader" />
    <None Include="Assets\Hovl Studio\Toon Projectiles 2\Shaders\DissolveNoise.shader" />
    <None Include="Assets\Arts\Beautify\_README FIRST!.txt" />
    <None Include="Assets\JMO Assets\Toony Colors Pro\Shaders\Standard PBS\TCP2_Standard PBS Outline (Specular).shader" />
    <None Include="Assets\JMO Assets\Toony Colors Pro\Shaders Generated\Examples\PBS\Sketch.shader" />
    <None Include="Assets\JMO Assets\Toony Colors Pro\Shader Templates 2\Modules\Module_Texture Blending.txt" />
    <None Include="Assets\explosion1\ReadMe.txt" />
    <None Include="Assets\Unluck Software\Stylized Particles\Boosts\Sylized Boost Paricles Readme.txt" />
    <None Include="Assets\PolygonSciFiWorlds\Shaders\SyntyStudios_SciFiPlant.shader" />
    <None Include="Assets\JMO Assets\Toony Colors Pro\Shader Templates 2\Modules\Module_ShadowHSV.txt" />
    <None Include="Assets\explosion1\Shaders\Fx_explosion_apb.shader" />
    <None Include="Assets\JMO Assets\Toony Colors Pro\Shader Templates\TCP2_ShaderTemplate_Water.txt" />
    <None Include="Assets\Arts\Vefects\Explosions VFX URP\Shared\Shaders\SH_Vefects_URP_VFX_Pyro_MV.shader" />
    <None Include="Assets\Arts\Vefects\Explosions VFX URP\Shared\Shaders\SH_Vefects_URP_VFX_Pyro_No_Blend.shader" />
    <None Include="Assets\Arts\Vefects\Explosions VFX URP\Shared\Shaders\SH_Vefects_URP_VFX_Concrete.shader" />
    <None Include="Assets\JMO Assets\Toony Colors Pro\Shader Templates 2\SG2_Template_Default.txt" />
    <None Include="Assets\JMO Assets\Toony Colors Pro\Shaders Generated\Examples\Default\Snow Accumulation.shader" />
    <None Include="Assets\BulletEffectCore\packs\StylizedProjectilePack1\Tips &amp; DocumentationsStylizedProjectilePack1.txt" />
    <None Include="Assets\polyperfect\Low Poly War Pack\TextMesh Pro\Resources\Shaders\TMP_SDF-Surface-Mobile.shader" />
    <None Include="Assets\Arts\SetCartoonTanks\Models\Shaders\Cubic_Shader.shader" />
    <None Include="Assets\FogOfWar\Shaders\Resources\FOW_SolidColor.shader" />
    <None Include="Assets\JMO Assets\Toony Colors Pro\Shaders\Legacy\TCP2_OutlineOnly Blended.shader" />
    <None Include="Assets\Real Fire &amp; Smoke\Shaders\BloomFire.shader" />
    <None Include="Assets\JMO Assets\Toony Colors Pro\Shader Templates 2\Modules\Module_NdotL Stylization.txt" />
    <None Include="Assets\StreamingAssets\Audio\GeneratedSoundBanks\Android\Init.txt" />
    <None Include="Assets\JMO Assets\Toony Colors Pro\Shader Templates 2\Modules\Module_Water.txt" />
    <None Include="Assets\JMO Assets\Toony Colors Pro\Shaders Generated\UserMy TCP2 Shader.shader" />
    <None Include="Assets\RockyDesert\Readme.txt" />
    <None Include="Assets\JMO Assets\Toony Colors Pro\Shaders Generated\Examples\Default\Comic Book.shader" />
    <None Include="Assets\StreamingAssets\Audio\GeneratedSoundBanks\Android\SoundbanksInfo.xml" />
    <None Include="Assets\Hovl Studio\Realistic explosions\Shaders\Distortion.shader" />
    <None Include="Assets\JMO Assets\Toony Colors Pro\Shader Templates 2\Modules\Module_CurvedWorld.txt" />
    <None Include="Assets\Hovl Studio\Toon Projectiles 2\Shaders\Blend_TwoSides.shader" />
    <None Include="Assets\JMO Assets\Toony Colors Pro\Shaders\Standard PBS\TCP2_PBS_Core.cginc" />
    <None Include="Assets\Arts\SetCartoonTanks\Demo_Game_Scene\Shaders\SurfaceShader_VC.shader" />
    <None Include="Assets\Hovl Studio\Realistic explosions\Shaders\Blend_CenterGlow.shader" />
    <None Include="Assets\EffectCore\shaders\ElectricityFlipbook-Add.shader" />
    <None Include="Assets\JMO Assets\Toony Colors Pro\Shader Templates 2\Modules\Module_Wind.txt" />
    <None Include="Assets\JMO Assets\Toony Colors Pro\Shader Templates\TCP2_ShaderTemplate_SurfacePBS.txt" />
    <None Include="Assets\JMO Assets\Toony Colors Pro\!ToonyColorsPro Readme.txt" />
    <None Include="Assets\FogOfWar\Shaders\Partial Hiders\2D\PartialHider2D.shader" />
    <None Include="Assets\StandaloneFileBrowser\Plugins\Ookii.Dialogs.dll" />
    <None Include="Assets\FogOfWar\Shaders\Partial Hiders\BIRP\ASE\PartialHiderLitCutout.shader" />
    <None Include="Assets\JMO Assets\Toony Colors Pro\Shader Templates 2\Modules\Module_Dithering.txt" />
    <None Include="Assets\FogOfWar\Shaders\Resources\FogOfWarLogic.hlsl" />
    <None Include="Assets\Arts\Vefects\Explosions VFX URP\Explosions VFX\Sci-Fi\Shaders\SH_Vefects_URP_VFX_Energy_Disk.shader" />
    <None Include="Assets\JMO Assets\Toony Colors Pro\Shaders\Legacy\TCP2_OutlineOnly SM2.shader" />
    <None Include="Assets\Arts\Vefects\Explosions VFX URP\Explosions VFX\Sci-Fi\Shaders\SH_Vefects_URP_VFX_Energy_Waves.shader" />
    <None Include="Assets\JMO Assets\Toony Colors Pro\Shader Templates 2\Modules\Module_Ramp Shading LWRP.txt" />
    <None Include="Assets\JMO Assets\Toony Colors Pro\Shaders Generated\Examples\SG2\Hologram.shader" />
    <None Include="Assets\WwiseSettings.xml" />
    <None Include="Assets\Resources\Shaders\UI\UI_Gray.shader" />
    <None Include="Assets\Wwise\Version.txt" />
    <None Include="Assets\Arts\Vefects\Explosions VFX URP\Shared\Shaders\SH_Vefects_URP_VFX_Distortion_Mesh.shader" />
    <None Include="Assets\JMO Assets\Toony Colors Pro\Shaders\Legacy\TCP2_OutlineOnly Blended SM2.shader" />
    <None Include="Assets\JMO Assets\Toony Colors Pro\Shaders Generated\Examples\Water\Realistic.shader" />
    <None Include="Assets\JMO Assets\Toony Colors Pro\Shaders\Legacy\Include\TCP2_Outline_Include.cginc" />
    <None Include="Assets\explosion1\Shaders\Fx_explosion_add.shader" />
    <None Include="Assets\JMO Assets\Toony Colors Pro\Shader Templates 2\Modules\Module_AlbedoHSV.txt" />
    <None Include="Assets\polyperfect\Low Poly War Pack\TextMesh Pro\Resources\Shaders\TMP_SDF-Surface.shader" />
    <None Include="Assets\Arts\Vefects\Explosions VFX URP\Shared\Shaders\SH_Vefects_URP_VFX_Pyro_MV_Emission_Dissolve.shader" />
    <None Include="Assets\polyperfect\Low Poly War Pack\TextMesh Pro\Sprites\EmojiOne Attribution.txt" />
    <None Include="Assets\JMO Assets\Toony Colors Pro\Shaders\Legacy\Include\TCP2_Outline_Behind.shader" />
    <None Include="Assets\JMO Assets\Toony Colors Pro\Shader Templates 2\Modules\Module_Normal Mapping.txt" />
    <None Include="Assets\polyperfect\Low Poly War Pack\TextMesh Pro\Resources\Shaders\TMP_Bitmap-Custom-Atlas.shader" />
    <None Include="Assets\polyperfect\Low Poly War Pack\TextMesh Pro\Resources\Shaders\TMP_SDF.shader" />
    <None Include="Assets\JMO Assets\Toony Colors Pro\Shaders Generated\Examples\PBS\Blended Outline.shader" />
    <None Include="Assets\ExplsionFx\packs\StylizedExplosionPack1\shader\alphaBlend_depthBlend_glow.shader" />
    <None Include="Assets\SimpleMilitary\Old\Materials\UnlitVertexColour_Alpha.shader" />
    <None Include="Assets\Scripts\Framework\UI\Common\BitmapText\Shaders\BitmapText.shader" />
    <None Include="Assets\polyperfect\Low Poly War Pack\TextMesh Pro\Resources\LineBreaking Leading Characters.txt" />
    <None Include="Assets\Arts\Vefects\Explosions VFX URP\Explosions VFX\Water\Shaders\SH_Vefects_URP_VFX_Water_Splash.shader" />
    <None Include="Assets\JMO Assets\Toony Colors Pro\Shaders\Standard PBS\TCP2_Standard PBS.shader" />
    <None Include="Assets\JMO Assets\Toony Colors Pro\Shader Templates 2\Modules\Module_VertExmotion.txt" />
    <None Include="Assets\FogOfWar\Shaders\Resources\FOW_RT.shader" />
    <None Include="Assets\polyperfect\Low Poly War Pack\TextMesh Pro\Resources\Shaders\TMPro_Properties.cginc" />
    <None Include="Assets\JMO Assets\Toony Colors Pro\Shaders\Legacy\Include\TCP2_Outline.shader" />
    <None Include="Assets\JMO Assets\Toony Colors Pro\Shaders Generated\Examples\Default\Subsurface Scattering.shader" />
    <None Include="Assets\StreamingAssets\Audio\GeneratedSoundBanks\Android\PluginInfo.xml" />
    <None Include="Assets\Hovl Studio\Toon Projectiles 2\Demo scene\HDRP and URP(LWRP).txt" />
    <None Include="Assets\Arts\Vefects\Explosions VFX URP\Explosions VFX\Water\Shaders\SH_Vefects_URP_VFX_Water_Ring.shader" />
    <None Include="Assets\polyperfect\Low Poly War Pack\TextMesh Pro\Resources\Shaders\TMPro_Surface.cginc" />
    <None Include="Assets\Arts\Vefects\Explosions VFX URP\Explosions VFX\Sci-Fi\Shaders\SH_Vefects_URP_VFX_EMP_Sphere_Distortion.shader" />
    <None Include="Assets\Arts\Vefects\Explosions VFX URP\Shared\Shaders\SH_Vefects_URP_VFX_Distortion_Shockwave.shader" />
    <None Include="Assets\JMO Assets\Toony Colors Pro\Shader Templates 2\Modules\Module_Vertical Fog.txt" />
    <None Include="Assets\JMO Assets\Toony Colors Pro\Shaders Generated\Examples\Water\Water WindWaker2.shader" />
    <None Include="Assets\JMO Assets\Toony Colors Pro\Shader Templates\TCP2_ShaderTemplate_Water_CurvedWorld.txt" />
    <None Include="Assets\RockyDesert\Shaders\M_Terrain.shader" />
    <None Include="Assets\JMO Assets\Toony Colors Pro\Shaders Generated\Examples\PBS\Hand Painted.shader" />
    <None Include="Assets\JMO Assets\Toony Colors Pro\Shaders\Legacy\Include\TCP2_Outline_Stencil.shader" />
    <None Include="Assets\Arts\Vefects\Explosions VFX URP\Shared\Shaders\SH_Vefects_URP_VFX_Pyro_MV_Fire.shader" />
    <None Include="Assets\FogOfWar\Shaders\Resources\FOW_TextureSample.shader" />
    <None Include="Assets\Arts\Vefects\Explosions VFX URP\Demo\Resources\Shaders\SH_Vefects_URP_VFX_Character_Showoff.shader" />
    <None Include="Assets\JMO Assets\Toony Colors Pro\Shader Templates 2\Modules\Module_Ramp Shading.txt" />
    <None Include="Assets\Arts\Vefects\Explosions VFX URP\Explosions VFX\Sci-Fi\Shaders\SH_Vefects_URP_VFX_EMP_Sphere_Modulate.shader" />
    <None Include="Assets\FogOfWar\Shaders\Partial Hiders\BIRP\ASE\PartialHiderLitTransparent.shader" />
    <None Include="Assets\polyperfect\Low Poly War Pack\TextMesh Pro\Resources\LineBreaking Following Characters.txt" />
    <None Include="Assets\JMO Assets\Toony Colors Pro\Shaders\Standard PBS\TCP2_Standard PBS Outline Blended.shader" />
    <None Include="Assets\JMO Assets\Toony Colors Pro\Shader Templates 2\Modules\Module_Sketch.txt" />
    <None Include="Assets\PolygonSciFiWorlds\Shaders\SyntyStudios_Holographic_Sign.shader" />
    <None Include="Assets\JMO Assets\Toony Colors Pro\Shaders\Legacy\Include\TCP2_Outline_SM2.shader" />
    <None Include="Assets\PolygonSciFiWorlds\Shaders\SyntyStudios_Skybox.shader" />
    <None Include="Assets\JMO Assets\Toony Colors Pro\Shader Templates 2\Modules\Module_Aura2.txt" />
    <None Include="Assets\JMO Assets\Toony Colors Pro\Shaders Generated\Examples\Water\Reflection.shader" />
    <None Include="Assets\JMO Assets\Toony Colors Pro\Shaders Generated\Examples\Water\Lava.shader" />
    <None Include="Assets\JMO Assets\Toony Colors Pro\Shader Templates 2\Modules\Module_Screen Space UV.txt" />
    <None Include="Assets\sound\BuildingOther\SoundBuildingDestroy\音效对照.txt" />
    <None Include="Assets\JMO Assets\Toony Colors Pro\Shader Templates 2\Modules\Module_Hash Functions.txt" />
    <None Include="Assets\JMO Assets\Toony Colors Pro\Shader Templates\TCP2_ShaderTemplate_Terrain.txt" />
    <None Include="Assets\JMO Assets\Toony Colors Pro\Shader Templates\TCP2_ShaderTemplate_Default_CurvedWorld.txt" />
    <None Include="Assets\StylizedTanks_StarterKit\Documentation\README.txt" />
    <None Include="Assets\JMO Assets\Toony Colors Pro\Shader Templates 2\Modules\Module_Triplanar.txt" />
    <None Include="Assets\JMO Assets\Toony Colors Pro\Shader Templates\TCP2_ShaderTemplate_PBS.txt" />
    <None Include="Assets\JMO Assets\Toony Colors Pro\Shaders Generated\Examples\Default\Sketch.shader" />
    <None Include="Assets\FogOfWar\Shaders\Resources\FOW_Outline.shader" />
    <None Include="Assets\JMO Assets\Toony Colors Pro\Shaders\Standard PBS\TCP2_PBS_ShadowMeta.shader" />
    <None Include="Assets\ExplsionFx\packs\StylizedExplosionPack1\shader\alphaBlend_glow.shader" />
    <None Include="Assets\SimpleMilitary\Old\ReadMe.txt" />
    <None Include="Assets\polyperfect\Low Poly War Pack\TextMesh Pro\Resources\Shaders\TMP_SDF-Mobile Masking.shader" />
    <None Include="Assets\JMO Assets\Toony Colors Pro\Shaders Generated\Examples\Water\Lava Cartoon.shader" />
    <None Include="Assets\JMO Assets\Toony Colors Pro\Shaders Generated\Examples\PBS\Outline Behind.shader" />
    <None Include="Assets\Arts\Vefects\Explosions VFX URP\Shared\Shaders\SH_Vefects_URP_VFX_Pyro_MV_New.shader" />
    <None Include="Assets\JMO Assets\Toony Colors Pro\Shader Templates 2\Modules\Module_HSV.txt" />
    <None Include="Assets\Arts\Vefects\Explosions VFX URP\Shared\Shaders\SH_Vefects_URP_VFX_Fake_Decal.shader" />
    <None Include="Assets\JMO Assets\Toony Colors Pro\Shaders\Standard PBS\TCP2_Standard PBS (Specular).shader" />
    <None Include="Assets\StandaloneFileBrowser\Plugins\System.Windows.Forms.dll" />
    <None Include="Assets\Buildings_constructor\Shaders\Standard_2s.shader" />
    <None Include="Assets\JMO Assets\Toony Colors Pro\Shaders Generated\Examples\SG2\Water.shader" />
    <None Include="Assets\Hovl Studio\Realistic explosions\Shaders\Add_CenterGlow.shader" />
    <None Include="Assets\polyperfect\Low Poly War Pack\TextMesh Pro\Resources\Shaders\TMP_SDF Overlay.shader" />
    <None Include="Assets\Arts\Vefects\Explosions VFX URP\Explosions VFX\Sci-Fi\Shaders\SH_Vefects_URP_VFX_Lightning.shader" />
    <None Include="Assets\JMO Assets\Toony Colors Pro\Shader Templates 2\Modules\Module_Rim Lighting.txt" />
    <None Include="Assets\polyperfect\Low Poly War Pack\TextMesh Pro\Resources\Shaders\TMP_Bitmap-Mobile.shader" />
    <None Include="Assets\Hovl Studio\Realistic explosions\Demo scene\Readme.txt" />
    <None Include="Assets\JMO Assets\Toony Colors Pro\Shader Templates 2\Modules\Module_Subsurface Scattering.txt" />
    <None Include="Assets\Arts\Archanor\Stylized Fire FX\Readme.txt" />
    <None Include="Assets\polyperfect\Low Poly War Pack\TextMesh Pro\Fonts\LiberationSans - OFL.txt" />
    <None Include="Assets\JMO Assets\Toony Colors Pro\Shader Templates 2\Modules\Module_NoTile Sampling.txt" />
    <None Include="Assets\JMO Assets\Toony Colors Pro\Shaders\Standard PBS\TCP2_PBS_BRDF.cginc" />
    <None Include="Assets\PolygonSciFiWorlds\Shaders\SyntyStudios_EnvTriplanar.shader" />
    <None Include="Assets\PolygonSciFiWorlds\Shaders\SyntyStudios_Triplanar_Worlds.shader" />
    <None Include="Assets\Arts\Vefects\Explosions VFX URP\Shared\Shaders\SH_Vefects_URP_VFX_Dirt_Debris.shader" />
    <None Include="Assets\JMO Assets\Toony Colors Pro\Shaders\Legacy\TCP2_OutlineOnly.shader" />
    <None Include="Assets\polyperfect\Low Poly War Pack\TextMesh Pro\Resources\Shaders\TMP_SDF-Mobile.shader" />
    <None Include="Assets\JMO Assets\Toony Colors Pro\Shaders Generated\Examples\Water\Water WindWaker.shader" />
    <None Include="Assets\JMO Assets\Toony Colors Pro\Shader Templates 2\Modules\Module_CustomAmbient.txt" />
    <None Include="Assets\Real Fire &amp; Smoke\Readme.txt" />
    <None Include="Assets\EffectCore\packs\prefabs\ElecLoop\NOTE.txt" />
    <None Include="Assets\Hovl Studio\Realistic explosions\Demo scene\HDRP and URP(LWRP).txt" />
    <None Include="Assets\JMO Assets\Toony Colors Pro\Shaders Generated\Examples\SG2\Wind Animation.shader" />
    <None Include="Assets\JMO Assets\Toony Colors Pro\Shader Templates\TCP2_ShaderTemplate_Default.txt" />
    <None Include="Assets\JMO Assets\Toony Colors Pro\Shaders Generated\Examples\Water\Poison.shader" />
    <None Include="Assets\Resources\Prefabs\FX\FlashShader.shader" />
    <None Include="Assets\FogOfWar\Shaders\Resources\FOW_Blur.shader" />
    <None Include="Assets\Resources\Prefabs\FX\Effect\Shader\PandaShaderSampleURPV1.0.shader" />
    <None Include="Assets\JMO Assets\Toony Colors Pro\Shader Templates 2\Modules\Module_Specular.txt" />
    <None Include="Assets\Hovl Studio\Realistic explosions\Shaders\Explosion.shader" />
    <None Include="Assets\JMO Assets\Toony Colors Pro\Shaders Generated\Examples\SG2\Detail Texture.shader" />
    <None Include="Assets\polyperfect\Low Poly War Pack\TextMesh Pro\Resources\Shaders\TMP_Sprite.shader" />
    <None Include="Assets\JMO Assets\Toony Colors Pro\Shader Templates 2\Modules\Module_Reflection.txt" />
    <None Include="Assets\RTS Effects\RTS FX.txt" />
    <None Include="Assets\Resources\Shaders\UI\MeshUI\MeshUI_Default.shader" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="UnityEngine">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AIModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.AIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ARModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.ARModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AccessibilityModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.AccessibilityModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AndroidJNIModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.AndroidJNIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AnimationModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.AnimationModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AssetBundleModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.AssetBundleModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AudioModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.AudioModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ClothModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.ClothModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ContentLoadModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.ContentLoadModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.CoreModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.CoreModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.CrashReportingModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.CrashReportingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.DSPGraphModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.DSPGraphModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.DirectorModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.DirectorModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GIModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.GIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GameCenterModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.GameCenterModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GridModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.GridModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.HotReloadModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.HotReloadModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.IMGUIModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.IMGUIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ImageConversionModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.ImageConversionModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.InputModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.InputModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.InputLegacyModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.InputLegacyModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.JSONSerializeModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.JSONSerializeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.LocalizationModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.LocalizationModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ParticleSystemModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.ParticleSystemModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.PerformanceReportingModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.PerformanceReportingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.PhysicsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.PhysicsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.Physics2DModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.Physics2DModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ProfilerModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.ProfilerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.PropertiesModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.PropertiesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ScreenCaptureModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.ScreenCaptureModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SharedInternalsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.SharedInternalsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SpriteMaskModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.SpriteMaskModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SpriteShapeModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.SpriteShapeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.StreamingModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.StreamingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SubstanceModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.SubstanceModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SubsystemsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.SubsystemsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TLSModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.TLSModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TerrainModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.TerrainModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TerrainPhysicsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.TerrainPhysicsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TextCoreFontEngineModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.TextCoreFontEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TextCoreTextEngineModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.TextCoreTextEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TextRenderingModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.TextRenderingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TilemapModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.TilemapModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UIModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.UIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UIElementsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.UIElementsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UmbraModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.UmbraModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityAnalyticsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityAnalyticsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityAnalyticsCommonModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityAnalyticsCommonModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityConnectModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityConnectModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityCurlModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityCurlModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityTestProtocolModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityTestProtocolModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestAssetBundleModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestAssetBundleModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestAudioModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestAudioModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestTextureModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestTextureModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestWWWModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestWWWModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VFXModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.VFXModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VRModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.VRModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VehiclesModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.VehiclesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VideoModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.VideoModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.WindModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.WindModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.XRModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.XRModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\Managed\UnityEngine\UnityEditor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.CoreModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\Managed\UnityEngine\UnityEditor.CoreModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.DeviceSimulatorModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\Managed\UnityEngine\UnityEditor.DeviceSimulatorModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.DiagnosticsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\Managed\UnityEngine\UnityEditor.DiagnosticsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.EditorToolbarModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\Managed\UnityEngine\UnityEditor.EditorToolbarModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.GraphViewModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\Managed\UnityEngine\UnityEditor.GraphViewModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.PresetsUIModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\Managed\UnityEngine\UnityEditor.PresetsUIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.QuickSearchModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\Managed\UnityEngine\UnityEditor.QuickSearchModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SceneTemplateModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\Managed\UnityEngine\UnityEditor.SceneTemplateModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SceneViewModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\Managed\UnityEngine\UnityEditor.SceneViewModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TextCoreFontEngineModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\Managed\UnityEngine\UnityEditor.TextCoreFontEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TextCoreTextEngineModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\Managed\UnityEngine\UnityEditor.TextCoreTextEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIBuilderModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\Managed\UnityEngine\UnityEditor.UIBuilderModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIElementsModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\Managed\UnityEngine\UnityEditor.UIElementsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIElementsSamplesModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\Managed\UnityEngine\UnityEditor.UIElementsSamplesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UnityConnectModule">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\Managed\UnityEngine\UnityEditor.UnityConnectModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Google.FlatBuffers">
      <HintPath>Assets\Plugins\Google.FlatBuffers.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Collections.LowLevel.ILSupport">
      <HintPath>Library\PackageCache\com.unity.collections@1.5.1\Unity.Collections.LowLevel.ILSupport\Unity.Collections.LowLevel.ILSupport.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Ookii.Dialogs">
      <HintPath>Assets\StandaloneFileBrowser\Plugins\Ookii.Dialogs.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="NLog">
      <HintPath>Assets\Plugins\NLog.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualScripting.Antlr3.Runtime">
      <HintPath>Library\PackageCache\com.unity.visualscripting@1.9.4\Runtime\VisualScripting.Flow\Dependencies\NCalc\Unity.VisualScripting.Antlr3.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Clipper2Lib">
      <HintPath>Assets\Plugins\Clipper2Lib.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.Unsafe">
      <HintPath>Assets\Plugins\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.IO.RecyclableMemoryStream">
      <HintPath>Assets\Plugins\Microsoft.IO.RecyclableMemoryStream.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Mono.Cecil">
      <HintPath>Library\PackageCache\com.unity.nuget.mono-cecil@1.11.4\Mono.Cecil.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Windows.Forms">
      <HintPath>Assets\StandaloneFileBrowser\Plugins\System.Windows.Forms.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.iOS.Extensions.Xcode">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\PlaybackEngines\iOSSupport\UnityEditor.iOS.Extensions.Xcode.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.iOS.Extensions.Common">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\PlaybackEngines\iOSSupport\UnityEditor.iOS.Extensions.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Android.Types">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\PlaybackEngines\AndroidPlayer\Unity.Android.Types.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Android.Gradle">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\PlaybackEngines\AndroidPlayer\Unity.Android.Gradle.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Android.GradleProject">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\PlaybackEngines\AndroidPlayer\Unity.Android.GradleProject.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="netstandard">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\ref\2.1.0\netstandard.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.Win32.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\Microsoft.Win32.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.AppContext">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.AppContext.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Buffers">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Buffers.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections.Concurrent">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Collections.Concurrent.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Collections.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections.NonGeneric">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Collections.NonGeneric.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections.Specialized">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Collections.Specialized.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.ComponentModel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.EventBasedAsync">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.ComponentModel.EventBasedAsync.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.ComponentModel.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.TypeConverter">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.ComponentModel.TypeConverter.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Console">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Console.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Data.Common">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Data.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Contracts">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.Contracts.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Debug">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.Debug.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.FileVersionInfo">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.FileVersionInfo.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Process">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.Process.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.StackTrace">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.StackTrace.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.TextWriterTraceListener">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.TextWriterTraceListener.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Tools">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.Tools.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.TraceSource">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.TraceSource.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Tracing">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Diagnostics.Tracing.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Drawing.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Drawing.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Dynamic.Runtime">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Dynamic.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Globalization.Calendars">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Globalization.Calendars.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Globalization">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Globalization.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Globalization.Extensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Globalization.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Compression">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.Compression.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Compression.ZipFile">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.Compression.ZipFile.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.FileSystem.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.DriveInfo">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.FileSystem.DriveInfo.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.FileSystem.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.Watcher">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.FileSystem.Watcher.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.IsolatedStorage">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.IsolatedStorage.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.MemoryMappedFiles">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.MemoryMappedFiles.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Pipes">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.Pipes.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.UnmanagedMemoryStream">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.IO.UnmanagedMemoryStream.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Linq.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq.Expressions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Linq.Expressions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq.Parallel">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Linq.Parallel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq.Queryable">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Linq.Queryable.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Memory">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Memory.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Http">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.Http.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.NameResolution">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.NameResolution.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.NetworkInformation">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.NetworkInformation.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Ping">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.Ping.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Requests">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.Requests.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Security">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.Security.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Sockets">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.Sockets.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.WebHeaderCollection">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.WebHeaderCollection.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.WebSockets.Client">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.WebSockets.Client.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.WebSockets">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Net.WebSockets.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Numerics.Vectors">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Numerics.Vectors.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ObjectModel">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.ObjectModel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.DispatchProxy">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.DispatchProxy.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Emit">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.Emit.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Emit.ILGeneration">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.Emit.ILGeneration.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Emit.Lightweight">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.Emit.Lightweight.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Extensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Reflection.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Resources.Reader">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Resources.Reader.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Resources.ResourceManager">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Resources.ResourceManager.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Resources.Writer">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Resources.Writer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.VisualC">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.CompilerServices.VisualC.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Extensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Handles">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Handles.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.InteropServices.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.RuntimeInformation">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.InteropServices.RuntimeInformation.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Numerics">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Numerics.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Formatters">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Serialization.Formatters.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Json">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Serialization.Json.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Serialization.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Xml">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Runtime.Serialization.Xml.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Claims">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Claims.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Algorithms">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Cryptography.Algorithms.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Csp">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Cryptography.Csp.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Encoding">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Cryptography.Encoding.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Primitives">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Cryptography.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.X509Certificates">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Cryptography.X509Certificates.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Principal">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.Principal.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.SecureString">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Security.SecureString.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.Encoding">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Text.Encoding.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.Encoding.Extensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Text.Encoding.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.RegularExpressions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Text.RegularExpressions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Overlapped">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.Overlapped.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.Tasks.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.Tasks.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks.Parallel">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.Tasks.Parallel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Thread">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.Thread.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.ThreadPool">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.ThreadPool.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Timer">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Threading.Timer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ValueTuple">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.ValueTuple.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.ReaderWriter">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Xml.ReaderWriter.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XDocument">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Xml.XDocument.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XmlDocument">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Xml.XmlDocument.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XmlSerializer">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Xml.XmlSerializer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XPath">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Xml.XPath.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XPath.XDocument">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netstandard\System.Xml.XPath.XDocument.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.WindowsRuntime">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\Extensions\2.0.0\System.Runtime.InteropServices.WindowsRuntime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="mscorlib">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\mscorlib.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.Composition">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.ComponentModel.Composition.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Core">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Data">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Data.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Drawing">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Drawing.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Compression.FileSystem">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.IO.Compression.FileSystem.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Net.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Numerics">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Numerics.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Runtime.Serialization.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ServiceModel.Web">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.ServiceModel.Web.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Transactions">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Transactions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Web">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Web.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Windows">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Windows.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Xml.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.Linq">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Xml.Linq.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.Serialization">
      <HintPath>C:\Program Files\Unity\Hub\Editor\2022.3.57f1c1\Editor\Data\NetStandard\compat\2.1.0\shims\netfx\System.Xml.Serialization.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualEffectGraph.Runtime">
      <HintPath>Library\ScriptAssemblies\Unity.VisualEffectGraph.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Core.Runtime">
      <HintPath>Library\ScriptAssemblies\Unity.RenderPipelines.Core.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Burst.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.Burst.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.ResourceManager">
      <HintPath>Library\ScriptAssemblies\Unity.ResourceManager.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.ScriptableBuildPipeline">
      <HintPath>Library\ScriptAssemblies\Unity.ScriptableBuildPipeline.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Rider.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.Rider.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.2D.Sprite.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.2D.Sprite.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Universal.Config.Runtime">
      <HintPath>Library\ScriptAssemblies\Unity.RenderPipelines.Universal.Config.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Core.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.RenderPipelines.Core.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualEffectGraph.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.VisualEffectGraph.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualScripting.Flow">
      <HintPath>Library\ScriptAssemblies\Unity.VisualScripting.Flow.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Mathematics.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.Mathematics.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Core.ShaderLibrary">
      <HintPath>Library\ScriptAssemblies\Unity.RenderPipelines.Core.ShaderLibrary.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Collections">
      <HintPath>Library\ScriptAssemblies\Unity.Collections.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.PlasticSCM.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.PlasticSCM.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Collections.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.Collections.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="PPv2URPConverters">
      <HintPath>Library\ScriptAssemblies\PPv2URPConverters.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.TextMeshPro">
      <HintPath>Library\ScriptAssemblies\Unity.TextMeshPro.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VSCode.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.VSCode.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Universal.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.RenderPipelines.Universal.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary">
      <HintPath>Library\ScriptAssemblies\Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Universal.Runtime">
      <HintPath>Library\ScriptAssemblies\Unity.RenderPipelines.Universal.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Burst">
      <HintPath>Library\ScriptAssemblies\Unity.Burst.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Timeline.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.Timeline.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualScripting.Shared.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.VisualScripting.Shared.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.ShaderGraph.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.ShaderGraph.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualStudio.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.VisualStudio.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UI">
      <HintPath>Library\ScriptAssemblies\UnityEditor.UI.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualScripting.Core">
      <HintPath>Library\ScriptAssemblies\Unity.VisualScripting.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualScripting.Core.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.VisualScripting.Core.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UI">
      <HintPath>Library\ScriptAssemblies\UnityEngine.UI.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualScripting.Flow.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.VisualScripting.Flow.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.RenderPipeline.Universal.ShaderLibrary">
      <HintPath>Library\ScriptAssemblies\Unity.RenderPipeline.Universal.ShaderLibrary.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Timeline">
      <HintPath>Library\ScriptAssemblies\Unity.Timeline.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualScripting.SettingsProvider.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.VisualScripting.SettingsProvider.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Addressables.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.Addressables.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualScripting.State.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.VisualScripting.State.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Mathematics">
      <HintPath>Library\ScriptAssemblies\Unity.Mathematics.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Universal.Shaders">
      <HintPath>Library\ScriptAssemblies\Unity.RenderPipelines.Universal.Shaders.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.TextMeshPro.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.TextMeshPro.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Searcher.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.Searcher.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualScripting.State">
      <HintPath>Library\ScriptAssemblies\Unity.VisualScripting.State.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.ScriptableBuildPipeline.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.ScriptableBuildPipeline.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Addressables">
      <HintPath>Library\ScriptAssemblies\Unity.Addressables.dll</HintPath>
      <Private>False</Private>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="Assembly-CSharp-firstpass.csproj" />
    <ProjectReference Include="AK.Wwise.Unity.Timeline.csproj" />
    <ProjectReference Include="AllIn1VfxTexDemoAssembly.csproj" />
    <ProjectReference Include="SimpleTouch.csproj" />
    <ProjectReference Include="AK.Wwise.Unity.API.Editor.csproj" />
    <ProjectReference Include="Polyperfect.War.Editor.csproj" />
    <ProjectReference Include="Singleton.csproj" />
    <ProjectReference Include="AK.Wwise.Unity.API.csproj" />
    <ProjectReference Include="KinoBloom.Runtime.csproj" />
    <ProjectReference Include="AK.Wwise.Unity.MonoBehaviour.csproj" />
    <ProjectReference Include="CallbackManager.csproj" />
    <ProjectReference Include="Events.csproj" />
    <ProjectReference Include="Polyperfect.War.csproj" />
    <ProjectReference Include="Ak.Wwise.Api.WAAPI.csproj" />
    <ProjectReference Include="ToonyColorsPro2.Demo.csproj" />
    <ProjectReference Include="LitMotion.Extensions.csproj" />
    <ProjectReference Include="LitMotion.Animation.csproj" />
    <ProjectReference Include="Luban.Editor.csproj" />
    <ProjectReference Include="AllIn1VfxAssmebly.csproj" />
    <ProjectReference Include="AllIn1VfxDemoScriptAssemblies.csproj" />
    <ProjectReference Include="Luban.Runtime.csproj" />
    <ProjectReference Include="Polyperfect.Common.Editor.csproj" />
    <ProjectReference Include="AK.Wwise.Unity.MonoBehaviour.Editor.csproj" />
    <ProjectReference Include="AK.Wwise.Unity.Timeline.Editor.csproj" />
    <ProjectReference Include="Localization.Runtime.csproj" />
    <ProjectReference Include="AK.Wwise.Unity.API.WwiseTypes.csproj" />
    <ProjectReference Include="LitMotion.csproj" />
    <ProjectReference Include="ToonyColorsPro.Runtime.csproj" />
    <ProjectReference Include="ToonyColorsPro.Editor.csproj" />
    <ProjectReference Include="ToonyColorsPro.Demo.Editor.csproj" />
    <ProjectReference Include="LitMotion.Animation.Editor.csproj" />
    <ProjectReference Include="Polyperfect.Common.csproj" />
    <ProjectReference Include="MeshUI.csproj" />
    <ProjectReference Include="LitMotion.Editor.csproj" />
    <ProjectReference Include="TaskScheduler.csproj" />
  </ItemGroup>
  <Import Project="Sdk.targets" Sdk="Microsoft.NET.Sdk" />
  <ItemGroup>
    <ProjectCapability Remove="LaunchProfiles" />
    <ProjectCapability Remove="SharedProjectReferences" />
    <ProjectCapability Remove="ReferenceManagerSharedProjects" />
    <ProjectCapability Remove="ProjectReferences" />
    <ProjectCapability Remove="ReferenceManagerProjects" />
    <ProjectCapability Remove="COMReferences" />
    <ProjectCapability Remove="ReferenceManagerCOM" />
    <ProjectCapability Remove="AssemblyReferences" />
    <ProjectCapability Remove="ReferenceManagerAssemblies" />
  </ItemGroup>
</Project>

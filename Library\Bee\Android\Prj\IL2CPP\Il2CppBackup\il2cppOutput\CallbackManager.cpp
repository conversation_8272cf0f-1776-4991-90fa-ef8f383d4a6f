﻿#include "pch-cpp.hpp"

#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include <limits>



struct Dictionary_2_t514396B90715EDD83BB0470C76C2F426F9381C71;
struct Dictionary_2_t710C8281BB25AB47EA84E7D852BE0E831EB82C31;
struct IEqualityComparer_1_tCF272F999412330F5435156C2E8668262117199B;
struct KeyCollection_t5AB5652A21CE82A3996B7CBD11002C6DDD31B3DF;
struct List_1_t6133AE9445F833C234B275E156F970487A5C1A4A;
struct ValueCollection_tADD1CB8E19AE3C7F594B0574B2431B5D22F4C7A2;
struct EntryU5BU5D_t4F3925A0A18E2DF3071BAC60AFE8256333F614DA;
struct ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031;
struct CharU5BU5D_t799905CF001DD5F13F7DBB310181FC4D8B7D0AAB;
struct DelegateU5BU5D_tC5AB7E8F745616680F337909D3A8E6C722CDF771;
struct Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C;
struct CallbackInfoU5BU5D_t5EC13989002A3E3C9F374CED44CDC69E602C461E;
struct Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07;
struct CallbackManager_t19FF0BF336ADE5A782EFCB9C894B3A9351431233;
struct DelegateData_t9B286B493293CD2D23A5B2B5EF0E5B1324C2B77E;
struct MethodInfo_t;
struct String_t;
struct UnitySourceGeneratedAssemblyMonoScriptTypes_v1_t9871BF4842F83067A2941AF2366D6C96111F3B4A;
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915;

IL2CPP_EXTERN_C RuntimeClass* Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* CallbackManager_t19FF0BF336ADE5A782EFCB9C894B3A9351431233_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* Dictionary_2_t710C8281BB25AB47EA84E7D852BE0E831EB82C31_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeClass* List_1_t6133AE9445F833C234B275E156F970487A5C1A4A_il2cpp_TypeInfo_var;
IL2CPP_EXTERN_C RuntimeField* U3CPrivateImplementationDetailsU3E_t2D539A39CC04CF8A9D26E6B8CA2B4C16BDB47397____3B5BBC8ECC1581028625D2E5351B6F8557C61CCD55ED1ADDFE63AAEDD342A3C9_FieldInfo_var;
IL2CPP_EXTERN_C RuntimeField* U3CPrivateImplementationDetailsU3E_t2D539A39CC04CF8A9D26E6B8CA2B4C16BDB47397____AC7D7530D06E91E224F69F67EBAD4E304F12DDE6CACD0D2233C8C936A849BB45_FieldInfo_var;
IL2CPP_EXTERN_C const RuntimeMethod* Dictionary_2_Clear_m819255C8722E66F2B4CD9B6746F5C78734E9DC35_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Dictionary_2_Remove_m35C384BB7E7C99D8BB766D164AE52DC69FBC2612_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Dictionary_2_TryGetValue_m49E9CE1E830D15D25D10B8B5E4C989FB8A4D40B4_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Dictionary_2__ctor_m0E132E62D74A767FE4EDA3BF1987D11DA6557D65_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* Dictionary_2_set_Item_mF3EFD099128AB040A21FE90E5ED67E8EB7065A38_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1_Add_mBDD7C010CDF02D1D1DCDE763219EECF6B674B31B_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1_Clear_mB9FD8653ACB072F2B37239AA109F9FCEEF552D1F_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1_RemoveAt_mD499A88E9E091C1605D6AD025613729B87B11350_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1__ctor_mCC7ECC615E36C87B23B685E76B7B7770A3C6C02E_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1_get_Count_mA4EB8F9036B4BE1B5D1E9F6F0617B8CEAACBBE34_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1_get_Item_mCEFF728AFC5447D2EDB722AD83D034CEE0D75691_RuntimeMethod_var;
IL2CPP_EXTERN_C const RuntimeMethod* List_1_set_Item_m2A352F23425005A1161BBA095CB21917038EE6E0_RuntimeMethod_var;
struct Delegate_t_marshaled_com;
struct Delegate_t_marshaled_pinvoke;

struct ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031;
struct CallbackInfoU5BU5D_t5EC13989002A3E3C9F374CED44CDC69E602C461E;

IL2CPP_EXTERN_C_BEGIN
IL2CPP_EXTERN_C_END

#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
struct U3CModuleU3E_t203AC2A4184D406CF3AB37D66A58A154268B84CE 
{
};
struct Dictionary_2_t710C8281BB25AB47EA84E7D852BE0E831EB82C31  : public RuntimeObject
{
	Int32U5BU5D_t19C97395396A72ECAF310612F0760F165060314C* ____buckets;
	EntryU5BU5D_t4F3925A0A18E2DF3071BAC60AFE8256333F614DA* ____entries;
	int32_t ____count;
	int32_t ____freeList;
	int32_t ____freeCount;
	int32_t ____version;
	RuntimeObject* ____comparer;
	KeyCollection_t5AB5652A21CE82A3996B7CBD11002C6DDD31B3DF* ____keys;
	ValueCollection_tADD1CB8E19AE3C7F594B0574B2431B5D22F4C7A2* ____values;
	RuntimeObject* ____syncRoot;
};
struct List_1_t6133AE9445F833C234B275E156F970487A5C1A4A  : public RuntimeObject
{
	CallbackInfoU5BU5D_t5EC13989002A3E3C9F374CED44CDC69E602C461E* ____items;
	int32_t ____size;
	int32_t ____version;
	RuntimeObject* ____syncRoot;
};
struct U3CPrivateImplementationDetailsU3E_t2D539A39CC04CF8A9D26E6B8CA2B4C16BDB47397  : public RuntimeObject
{
};
struct CallbackManager_t19FF0BF336ADE5A782EFCB9C894B3A9351431233  : public RuntimeObject
{
};
struct UnitySourceGeneratedAssemblyMonoScriptTypes_v1_t9871BF4842F83067A2941AF2366D6C96111F3B4A  : public RuntimeObject
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F  : public RuntimeObject
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_pinvoke
{
};
struct ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F_marshaled_com
{
};
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22 
{
	bool ___m_value;
};
struct Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3 
{
	uint8_t ___m_value;
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2  : public ValueType_t6D9B272BD21782F0A9A14F2E41F85A50E97A986F
{
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2_marshaled_pinvoke
{
};
struct Enum_t2A1A94B24E3B776EEF4E5E485E290BB9D4D072E2_marshaled_com
{
};
struct Int32_t680FF22E76F6EFAD4375103CBBFFA0421349384C 
{
	int32_t ___m_value;
};
struct IntPtr_t 
{
	void* ___m_value;
};
struct Single_t4530F2FF86FCB0DC29F35385CA1BD21BE294761C 
{
	float ___m_value;
};
struct UInt32_t1833D51FFA667B18A5AA4B8D34DE284F8495D29B 
{
	uint32_t ___m_value;
};
struct Void_t4861ACF8F4594C3437BB48B6E56783494B843915 
{
	union
	{
		struct
		{
		};
		uint8_t Void_t4861ACF8F4594C3437BB48B6E56783494B843915__padding[1];
	};
};
#pragma pack(push, tp, 1)
struct __StaticArrayInitTypeSizeU3D68_tC73A93F7230D950F971EC3C3ABEC3AABF67BA34B 
{
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D68_tC73A93F7230D950F971EC3C3ABEC3AABF67BA34B__padding[68];
	};
};
#pragma pack(pop, tp)
#pragma pack(push, tp, 1)
struct __StaticArrayInitTypeSizeU3D99_tBE968481E5EF084CE9F5A7B6C369B71B2EA98246 
{
	union
	{
		struct
		{
			union
			{
			};
		};
		uint8_t __StaticArrayInitTypeSizeU3D99_tBE968481E5EF084CE9F5A7B6C369B71B2EA98246__padding[99];
	};
};
#pragma pack(pop, tp)
struct MonoScriptData_tC4741F8605468725923C14E79920D6E03729678A 
{
	ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* ___FilePathsData;
	ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* ___TypesData;
	int32_t ___TotalTypes;
	int32_t ___TotalFiles;
	bool ___IsEditorOnly;
};
struct MonoScriptData_tC4741F8605468725923C14E79920D6E03729678A_marshaled_pinvoke
{
	Il2CppSafeArray* ___FilePathsData;
	Il2CppSafeArray* ___TypesData;
	int32_t ___TotalTypes;
	int32_t ___TotalFiles;
	int32_t ___IsEditorOnly;
};
struct MonoScriptData_tC4741F8605468725923C14E79920D6E03729678A_marshaled_com
{
	Il2CppSafeArray* ___FilePathsData;
	Il2CppSafeArray* ___TypesData;
	int32_t ___TotalTypes;
	int32_t ___TotalFiles;
	int32_t ___IsEditorOnly;
};
struct Delegate_t  : public RuntimeObject
{
	intptr_t ___method_ptr;
	intptr_t ___invoke_impl;
	RuntimeObject* ___m_target;
	intptr_t ___method;
	intptr_t ___delegate_trampoline;
	intptr_t ___extra_arg;
	intptr_t ___method_code;
	intptr_t ___interp_method;
	intptr_t ___interp_invoke_impl;
	MethodInfo_t* ___method_info;
	MethodInfo_t* ___original_method_info;
	DelegateData_t9B286B493293CD2D23A5B2B5EF0E5B1324C2B77E* ___data;
	bool ___method_is_virtual;
};
struct Delegate_t_marshaled_pinvoke
{
	intptr_t ___method_ptr;
	intptr_t ___invoke_impl;
	Il2CppIUnknown* ___m_target;
	intptr_t ___method;
	intptr_t ___delegate_trampoline;
	intptr_t ___extra_arg;
	intptr_t ___method_code;
	intptr_t ___interp_method;
	intptr_t ___interp_invoke_impl;
	MethodInfo_t* ___method_info;
	MethodInfo_t* ___original_method_info;
	DelegateData_t9B286B493293CD2D23A5B2B5EF0E5B1324C2B77E* ___data;
	int32_t ___method_is_virtual;
};
struct Delegate_t_marshaled_com
{
	intptr_t ___method_ptr;
	intptr_t ___invoke_impl;
	Il2CppIUnknown* ___m_target;
	intptr_t ___method;
	intptr_t ___delegate_trampoline;
	intptr_t ___extra_arg;
	intptr_t ___method_code;
	intptr_t ___interp_method;
	intptr_t ___interp_invoke_impl;
	MethodInfo_t* ___method_info;
	MethodInfo_t* ___original_method_info;
	DelegateData_t9B286B493293CD2D23A5B2B5EF0E5B1324C2B77E* ___data;
	int32_t ___method_is_virtual;
};
struct Int32Enum_tCBAC8BA2BFF3A845FA599F303093BBBA374B6F0C 
{
	int32_t ___value__;
};
struct RuntimeFieldHandle_t6E4C45B6D2EA12FC99185805A7E77527899B25C5 
{
	intptr_t ___value;
};
struct eCallbackType_tF28C4AC00A573E04BFAE7997D6DB3D3000C5DBE4 
{
	int32_t ___value__;
};
struct CallbackHandle_t914DE6EB40EA9CA30F3369CC846342328562A3D8 
{
	int32_t ____callbackType;
	uint32_t ____id;
};
struct MulticastDelegate_t  : public Delegate_t
{
	DelegateU5BU5D_tC5AB7E8F745616680F337909D3A8E6C722CDF771* ___delegates;
};
struct MulticastDelegate_t_marshaled_pinvoke : public Delegate_t_marshaled_pinvoke
{
	Delegate_t_marshaled_pinvoke** ___delegates;
};
struct MulticastDelegate_t_marshaled_com : public Delegate_t_marshaled_com
{
	Delegate_t_marshaled_com** ___delegates;
};
struct CallbackInfo_t172261DACE580EF109EA5901886ED422A5849948 
{
	uint32_t ___Id;
	int32_t ___Type;
	float ___DelayTime;
	float ___Interval;
	float ___LastTriggerTime;
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___Callback;
};
struct CallbackInfo_t172261DACE580EF109EA5901886ED422A5849948_marshaled_pinvoke
{
	uint32_t ___Id;
	int32_t ___Type;
	float ___DelayTime;
	float ___Interval;
	float ___LastTriggerTime;
	Il2CppMethodPointer ___Callback;
};
struct CallbackInfo_t172261DACE580EF109EA5901886ED422A5849948_marshaled_com
{
	uint32_t ___Id;
	int32_t ___Type;
	float ___DelayTime;
	float ___Interval;
	float ___LastTriggerTime;
	Il2CppMethodPointer ___Callback;
};
struct Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07  : public MulticastDelegate_t
{
};
struct List_1_t6133AE9445F833C234B275E156F970487A5C1A4A_StaticFields
{
	CallbackInfoU5BU5D_t5EC13989002A3E3C9F374CED44CDC69E602C461E* ___s_emptyArray;
};
struct U3CPrivateImplementationDetailsU3E_t2D539A39CC04CF8A9D26E6B8CA2B4C16BDB47397_StaticFields
{
	__StaticArrayInitTypeSizeU3D99_tBE968481E5EF084CE9F5A7B6C369B71B2EA98246 ___3B5BBC8ECC1581028625D2E5351B6F8557C61CCD55ED1ADDFE63AAEDD342A3C9;
	__StaticArrayInitTypeSizeU3D68_tC73A93F7230D950F971EC3C3ABEC3AABF67BA34B ___AC7D7530D06E91E224F69F67EBAD4E304F12DDE6CACD0D2233C8C936A849BB45;
};
struct CallbackManager_t19FF0BF336ADE5A782EFCB9C894B3A9351431233_StaticFields
{
	uint32_t ____idCounter;
	Dictionary_2_t710C8281BB25AB47EA84E7D852BE0E831EB82C31* ____callbacks;
};
struct Boolean_t09A6377A54BE2F9E6985A8149F19234FD7DDFE22_StaticFields
{
	String_t* ___TrueString;
	String_t* ___FalseString;
};
#ifdef __clang__
#pragma clang diagnostic pop
#endif
struct ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031  : public RuntimeArray
{
	ALIGN_FIELD (8) uint8_t m_Items[1];

	inline uint8_t GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline uint8_t* GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, uint8_t value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
	}
	inline uint8_t GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline uint8_t* GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, uint8_t value)
	{
		m_Items[index] = value;
	}
};
struct CallbackInfoU5BU5D_t5EC13989002A3E3C9F374CED44CDC69E602C461E  : public RuntimeArray
{
	ALIGN_FIELD (8) CallbackInfo_t172261DACE580EF109EA5901886ED422A5849948 m_Items[1];

	inline CallbackInfo_t172261DACE580EF109EA5901886ED422A5849948 GetAt(il2cpp_array_size_t index) const
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items[index];
	}
	inline CallbackInfo_t172261DACE580EF109EA5901886ED422A5849948* GetAddressAt(il2cpp_array_size_t index)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		return m_Items + index;
	}
	inline void SetAt(il2cpp_array_size_t index, CallbackInfo_t172261DACE580EF109EA5901886ED422A5849948 value)
	{
		IL2CPP_ARRAY_BOUNDS_CHECK(index, (uint32_t)(this)->max_length);
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)&((m_Items + index)->___Callback), (void*)NULL);
	}
	inline CallbackInfo_t172261DACE580EF109EA5901886ED422A5849948 GetAtUnchecked(il2cpp_array_size_t index) const
	{
		return m_Items[index];
	}
	inline CallbackInfo_t172261DACE580EF109EA5901886ED422A5849948* GetAddressAtUnchecked(il2cpp_array_size_t index)
	{
		return m_Items + index;
	}
	inline void SetAtUnchecked(il2cpp_array_size_t index, CallbackInfo_t172261DACE580EF109EA5901886ED422A5849948 value)
	{
		m_Items[index] = value;
		Il2CppCodeGenWriteBarrier((void**)&((m_Items + index)->___Callback), (void*)NULL);
	}
};


IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Dictionary_2_TryGetValue_mD8A6CDD5C954C86D2150FCFF1B76EB62F35A881D_gshared (Dictionary_2_t514396B90715EDD83BB0470C76C2F426F9381C71* __this, int32_t ___0_key, RuntimeObject** ___1_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void List_1__ctor_mCC7ECC615E36C87B23B685E76B7B7770A3C6C02E_gshared (List_1_t6133AE9445F833C234B275E156F970487A5C1A4A* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Dictionary_2_set_Item_m78DDC81EE49FB9D4194E83685FFED445DFDB75CA_gshared (Dictionary_2_t514396B90715EDD83BB0470C76C2F426F9381C71* __this, int32_t ___0_key, RuntimeObject* ___1_value, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void List_1_Add_mBDD7C010CDF02D1D1DCDE763219EECF6B674B31B_gshared_inline (List_1_t6133AE9445F833C234B275E156F970487A5C1A4A* __this, CallbackInfo_t172261DACE580EF109EA5901886ED422A5849948 ___0_item, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR CallbackInfo_t172261DACE580EF109EA5901886ED422A5849948 List_1_get_Item_mCEFF728AFC5447D2EDB722AD83D034CEE0D75691_gshared (List_1_t6133AE9445F833C234B275E156F970487A5C1A4A* __this, int32_t ___0_index, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t List_1_get_Count_mA4EB8F9036B4BE1B5D1E9F6F0617B8CEAACBBE34_gshared_inline (List_1_t6133AE9445F833C234B275E156F970487A5C1A4A* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void List_1_RemoveAt_mD499A88E9E091C1605D6AD025613729B87B11350_gshared (List_1_t6133AE9445F833C234B275E156F970487A5C1A4A* __this, int32_t ___0_index, const RuntimeMethod* method) ;
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void List_1_Clear_mB9FD8653ACB072F2B37239AA109F9FCEEF552D1F_gshared_inline (List_1_t6133AE9445F833C234B275E156F970487A5C1A4A* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Dictionary_2_Clear_m763AA956A8325F527F831F49E2563FF2871A680E_gshared (Dictionary_2_t514396B90715EDD83BB0470C76C2F426F9381C71* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void List_1_set_Item_m2A352F23425005A1161BBA095CB21917038EE6E0_gshared (List_1_t6133AE9445F833C234B275E156F970487A5C1A4A* __this, int32_t ___0_index, CallbackInfo_t172261DACE580EF109EA5901886ED422A5849948 ___1_value, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool Dictionary_2_Remove_mA2D94572DAE1E370288187F54B99416C7DB26A5B_gshared (Dictionary_2_t514396B90715EDD83BB0470C76C2F426F9381C71* __this, int32_t ___0_key, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Dictionary_2__ctor_mCC9983804D8DC41E938E080075F9EA7BDD0C7059_gshared (Dictionary_2_t514396B90715EDD83BB0470C76C2F426F9381C71* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_NO_INLINE IL2CPP_METHOD_ATTR void List_1_AddWithResize_m0FA6BEE13AB653D361F2E0EE8FC01A73F2E519B2_gshared (List_1_t6133AE9445F833C234B275E156F970487A5C1A4A* __this, CallbackInfo_t172261DACE580EF109EA5901886ED422A5849948 ___0_item, const RuntimeMethod* method) ;

IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void RuntimeHelpers_InitializeArray_m751372AA3F24FBF6DA9B9D687CBFA2DE436CAB9B (RuntimeArray* ___0_array, RuntimeFieldHandle_t6E4C45B6D2EA12FC99185805A7E77527899B25C5 ___1_fldHandle, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2 (RuntimeObject* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void CallbackHandle__ctor_m97D3F2122D4D4B7C29D896ABD6CFD1A0A59F4F5A (CallbackHandle_t914DE6EB40EA9CA30F3369CC846342328562A3D8* __this, uint32_t ___0_id, int32_t ___1_callbackType, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool CallbackHandle_get_IsValid_mE586BDFFBE7C43187113900DF7F1CEA541BCF7D5 (CallbackHandle_t914DE6EB40EA9CA30F3369CC846342328562A3D8* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool CallbackManager_IsScheduled_m2E4F185FA620760970FEEA6525EE13539F893C7E (uint32_t ___0_id, int32_t ___1_callbackType, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool CallbackHandle_get_IsScheduled_m0631C17FD5DAD74B9813E5AFC1CB252E348F1606 (CallbackHandle_t914DE6EB40EA9CA30F3369CC846342328562A3D8* __this, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool CallbackManager_Cancel_m1ECC8FD014AC3696596474119492EA2FC8A7ECDF (uint32_t ___0_id, int32_t ___1_callbackType, const RuntimeMethod* method) ;
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool CallbackHandle_Cancel_m93E2C960EF412C0FF80D2A17690F0F0905582CF4 (CallbackHandle_t914DE6EB40EA9CA30F3369CC846342328562A3D8* __this, const RuntimeMethod* method) ;
inline bool Dictionary_2_TryGetValue_m49E9CE1E830D15D25D10B8B5E4C989FB8A4D40B4 (Dictionary_2_t710C8281BB25AB47EA84E7D852BE0E831EB82C31* __this, int32_t ___0_key, List_1_t6133AE9445F833C234B275E156F970487A5C1A4A** ___1_value, const RuntimeMethod* method)
{
	return ((  bool (*) (Dictionary_2_t710C8281BB25AB47EA84E7D852BE0E831EB82C31*, int32_t, List_1_t6133AE9445F833C234B275E156F970487A5C1A4A**, const RuntimeMethod*))Dictionary_2_TryGetValue_mD8A6CDD5C954C86D2150FCFF1B76EB62F35A881D_gshared)(__this, ___0_key, ___1_value, method);
}
inline void List_1__ctor_mCC7ECC615E36C87B23B685E76B7B7770A3C6C02E (List_1_t6133AE9445F833C234B275E156F970487A5C1A4A* __this, const RuntimeMethod* method)
{
	((  void (*) (List_1_t6133AE9445F833C234B275E156F970487A5C1A4A*, const RuntimeMethod*))List_1__ctor_mCC7ECC615E36C87B23B685E76B7B7770A3C6C02E_gshared)(__this, method);
}
inline void Dictionary_2_set_Item_mF3EFD099128AB040A21FE90E5ED67E8EB7065A38 (Dictionary_2_t710C8281BB25AB47EA84E7D852BE0E831EB82C31* __this, int32_t ___0_key, List_1_t6133AE9445F833C234B275E156F970487A5C1A4A* ___1_value, const RuntimeMethod* method)
{
	((  void (*) (Dictionary_2_t710C8281BB25AB47EA84E7D852BE0E831EB82C31*, int32_t, List_1_t6133AE9445F833C234B275E156F970487A5C1A4A*, const RuntimeMethod*))Dictionary_2_set_Item_m78DDC81EE49FB9D4194E83685FFED445DFDB75CA_gshared)(__this, ___0_key, ___1_value, method);
}
inline void List_1_Add_mBDD7C010CDF02D1D1DCDE763219EECF6B674B31B_inline (List_1_t6133AE9445F833C234B275E156F970487A5C1A4A* __this, CallbackInfo_t172261DACE580EF109EA5901886ED422A5849948 ___0_item, const RuntimeMethod* method)
{
	((  void (*) (List_1_t6133AE9445F833C234B275E156F970487A5C1A4A*, CallbackInfo_t172261DACE580EF109EA5901886ED422A5849948, const RuntimeMethod*))List_1_Add_mBDD7C010CDF02D1D1DCDE763219EECF6B674B31B_gshared_inline)(__this, ___0_item, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR CallbackHandle_t914DE6EB40EA9CA30F3369CC846342328562A3D8 CallbackManager_ScheduleInternal_m154FC729A38E3C5A06491802E29BB0202ABC6B83 (int32_t ___0_callbackType, float ___1_delay, float ___2_interval, Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___3_callback, const RuntimeMethod* method) ;
inline CallbackInfo_t172261DACE580EF109EA5901886ED422A5849948 List_1_get_Item_mCEFF728AFC5447D2EDB722AD83D034CEE0D75691 (List_1_t6133AE9445F833C234B275E156F970487A5C1A4A* __this, int32_t ___0_index, const RuntimeMethod* method)
{
	return ((  CallbackInfo_t172261DACE580EF109EA5901886ED422A5849948 (*) (List_1_t6133AE9445F833C234B275E156F970487A5C1A4A*, int32_t, const RuntimeMethod*))List_1_get_Item_mCEFF728AFC5447D2EDB722AD83D034CEE0D75691_gshared)(__this, ___0_index, method);
}
inline int32_t List_1_get_Count_mA4EB8F9036B4BE1B5D1E9F6F0617B8CEAACBBE34_inline (List_1_t6133AE9445F833C234B275E156F970487A5C1A4A* __this, const RuntimeMethod* method)
{
	return ((  int32_t (*) (List_1_t6133AE9445F833C234B275E156F970487A5C1A4A*, const RuntimeMethod*))List_1_get_Count_mA4EB8F9036B4BE1B5D1E9F6F0617B8CEAACBBE34_gshared_inline)(__this, method);
}
inline void List_1_RemoveAt_mD499A88E9E091C1605D6AD025613729B87B11350 (List_1_t6133AE9445F833C234B275E156F970487A5C1A4A* __this, int32_t ___0_index, const RuntimeMethod* method)
{
	((  void (*) (List_1_t6133AE9445F833C234B275E156F970487A5C1A4A*, int32_t, const RuntimeMethod*))List_1_RemoveAt_mD499A88E9E091C1605D6AD025613729B87B11350_gshared)(__this, ___0_index, method);
}
inline void List_1_Clear_mB9FD8653ACB072F2B37239AA109F9FCEEF552D1F_inline (List_1_t6133AE9445F833C234B275E156F970487A5C1A4A* __this, const RuntimeMethod* method)
{
	((  void (*) (List_1_t6133AE9445F833C234B275E156F970487A5C1A4A*, const RuntimeMethod*))List_1_Clear_mB9FD8653ACB072F2B37239AA109F9FCEEF552D1F_gshared_inline)(__this, method);
}
inline void Dictionary_2_Clear_m819255C8722E66F2B4CD9B6746F5C78734E9DC35 (Dictionary_2_t710C8281BB25AB47EA84E7D852BE0E831EB82C31* __this, const RuntimeMethod* method)
{
	((  void (*) (Dictionary_2_t710C8281BB25AB47EA84E7D852BE0E831EB82C31*, const RuntimeMethod*))Dictionary_2_Clear_m763AA956A8325F527F831F49E2563FF2871A680E_gshared)(__this, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void CallbackManager_Invoke_m8E23D655062E2C22BFADA57F555C57470E3D1B58 (int32_t ___0_type, float ___1_deltaTime, const RuntimeMethod* method) ;
inline void List_1_set_Item_m2A352F23425005A1161BBA095CB21917038EE6E0 (List_1_t6133AE9445F833C234B275E156F970487A5C1A4A* __this, int32_t ___0_index, CallbackInfo_t172261DACE580EF109EA5901886ED422A5849948 ___1_value, const RuntimeMethod* method)
{
	((  void (*) (List_1_t6133AE9445F833C234B275E156F970487A5C1A4A*, int32_t, CallbackInfo_t172261DACE580EF109EA5901886ED422A5849948, const RuntimeMethod*))List_1_set_Item_m2A352F23425005A1161BBA095CB21917038EE6E0_gshared)(__this, ___0_index, ___1_value, method);
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Action_Invoke_m7126A54DACA72B845424072887B5F3A51FC3808E_inline (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* __this, const RuntimeMethod* method) ;
inline bool Dictionary_2_Remove_m35C384BB7E7C99D8BB766D164AE52DC69FBC2612 (Dictionary_2_t710C8281BB25AB47EA84E7D852BE0E831EB82C31* __this, int32_t ___0_key, const RuntimeMethod* method)
{
	return ((  bool (*) (Dictionary_2_t710C8281BB25AB47EA84E7D852BE0E831EB82C31*, int32_t, const RuntimeMethod*))Dictionary_2_Remove_mA2D94572DAE1E370288187F54B99416C7DB26A5B_gshared)(__this, ___0_key, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR float Time_get_deltaTime_mC3195000401F0FD167DD2F948FD2BC58330D0865 (const RuntimeMethod* method) ;
inline void Dictionary_2__ctor_m0E132E62D74A767FE4EDA3BF1987D11DA6557D65 (Dictionary_2_t710C8281BB25AB47EA84E7D852BE0E831EB82C31* __this, const RuntimeMethod* method)
{
	((  void (*) (Dictionary_2_t710C8281BB25AB47EA84E7D852BE0E831EB82C31*, const RuntimeMethod*))Dictionary_2__ctor_mCC9983804D8DC41E938E080075F9EA7BDD0C7059_gshared)(__this, method);
}
inline void List_1_AddWithResize_m0FA6BEE13AB653D361F2E0EE8FC01A73F2E519B2 (List_1_t6133AE9445F833C234B275E156F970487A5C1A4A* __this, CallbackInfo_t172261DACE580EF109EA5901886ED422A5849948 ___0_item, const RuntimeMethod* method)
{
	((  void (*) (List_1_t6133AE9445F833C234B275E156F970487A5C1A4A*, CallbackInfo_t172261DACE580EF109EA5901886ED422A5849948, const RuntimeMethod*))List_1_AddWithResize_m0FA6BEE13AB653D361F2E0EE8FC01A73F2E519B2_gshared)(__this, ___0_item, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void Array_Clear_m50BAA3751899858B097D3FF2ED31F284703FE5CB (RuntimeArray* ___0_array, int32_t ___1_index, int32_t ___2_length, const RuntimeMethod* method) ;
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR MonoScriptData_tC4741F8605468725923C14E79920D6E03729678A UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_m9E1171D3B13A6FBC7EE20648D271AF3E76DB42B2 (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CPrivateImplementationDetailsU3E_t2D539A39CC04CF8A9D26E6B8CA2B4C16BDB47397____3B5BBC8ECC1581028625D2E5351B6F8557C61CCD55ED1ADDFE63AAEDD342A3C9_FieldInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&U3CPrivateImplementationDetailsU3E_t2D539A39CC04CF8A9D26E6B8CA2B4C16BDB47397____AC7D7530D06E91E224F69F67EBAD4E304F12DDE6CACD0D2233C8C936A849BB45_FieldInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	MonoScriptData_tC4741F8605468725923C14E79920D6E03729678A V_0;
	memset((&V_0), 0, sizeof(V_0));
	{
		il2cpp_codegen_initobj((&V_0), sizeof(MonoScriptData_tC4741F8605468725923C14E79920D6E03729678A));
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_0 = (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)SZArrayNew(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031_il2cpp_TypeInfo_var, (uint32_t)((int32_t)68));
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_1 = L_0;
		RuntimeFieldHandle_t6E4C45B6D2EA12FC99185805A7E77527899B25C5 L_2 = { reinterpret_cast<intptr_t> (U3CPrivateImplementationDetailsU3E_t2D539A39CC04CF8A9D26E6B8CA2B4C16BDB47397____AC7D7530D06E91E224F69F67EBAD4E304F12DDE6CACD0D2233C8C936A849BB45_FieldInfo_var) };
		RuntimeHelpers_InitializeArray_m751372AA3F24FBF6DA9B9D687CBFA2DE436CAB9B((RuntimeArray*)L_1, L_2, NULL);
		(&V_0)->___FilePathsData = L_1;
		Il2CppCodeGenWriteBarrier((void**)(&(&V_0)->___FilePathsData), (void*)L_1);
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_3 = (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)SZArrayNew(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031_il2cpp_TypeInfo_var, (uint32_t)((int32_t)99));
		ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031* L_4 = L_3;
		RuntimeFieldHandle_t6E4C45B6D2EA12FC99185805A7E77527899B25C5 L_5 = { reinterpret_cast<intptr_t> (U3CPrivateImplementationDetailsU3E_t2D539A39CC04CF8A9D26E6B8CA2B4C16BDB47397____3B5BBC8ECC1581028625D2E5351B6F8557C61CCD55ED1ADDFE63AAEDD342A3C9_FieldInfo_var) };
		RuntimeHelpers_InitializeArray_m751372AA3F24FBF6DA9B9D687CBFA2DE436CAB9B((RuntimeArray*)L_4, L_5, NULL);
		(&V_0)->___TypesData = L_4;
		Il2CppCodeGenWriteBarrier((void**)(&(&V_0)->___TypesData), (void*)L_4);
		(&V_0)->___TotalFiles = 1;
		(&V_0)->___TotalTypes = 3;
		(&V_0)->___IsEditorOnly = (bool)0;
		MonoScriptData_tC4741F8605468725923C14E79920D6E03729678A L_6 = V_0;
		return L_6;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_m43F5330E7D7DE9DBD8911622268D046C9AADD27F (UnitySourceGeneratedAssemblyMonoScriptTypes_v1_t9871BF4842F83067A2941AF2366D6C96111F3B4A* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C void MonoScriptData_tC4741F8605468725923C14E79920D6E03729678A_marshal_pinvoke(const MonoScriptData_tC4741F8605468725923C14E79920D6E03729678A& unmarshaled, MonoScriptData_tC4741F8605468725923C14E79920D6E03729678A_marshaled_pinvoke& marshaled)
{
	marshaled.___FilePathsData = il2cpp_codegen_com_marshal_safe_array(IL2CPP_VT_I1, unmarshaled.___FilePathsData);
	marshaled.___TypesData = il2cpp_codegen_com_marshal_safe_array(IL2CPP_VT_I1, unmarshaled.___TypesData);
	marshaled.___TotalTypes = unmarshaled.___TotalTypes;
	marshaled.___TotalFiles = unmarshaled.___TotalFiles;
	marshaled.___IsEditorOnly = static_cast<int32_t>(unmarshaled.___IsEditorOnly);
}
IL2CPP_EXTERN_C void MonoScriptData_tC4741F8605468725923C14E79920D6E03729678A_marshal_pinvoke_back(const MonoScriptData_tC4741F8605468725923C14E79920D6E03729678A_marshaled_pinvoke& marshaled, MonoScriptData_tC4741F8605468725923C14E79920D6E03729678A& unmarshaled)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	unmarshaled.___FilePathsData = (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I1, Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, marshaled.___FilePathsData);
	Il2CppCodeGenWriteBarrier((void**)(&unmarshaled.___FilePathsData), (void*)(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I1, Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, marshaled.___FilePathsData));
	unmarshaled.___TypesData = (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I1, Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, marshaled.___TypesData);
	Il2CppCodeGenWriteBarrier((void**)(&unmarshaled.___TypesData), (void*)(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I1, Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, marshaled.___TypesData));
	int32_t unmarshaledTotalTypes_temp_2 = 0;
	unmarshaledTotalTypes_temp_2 = marshaled.___TotalTypes;
	unmarshaled.___TotalTypes = unmarshaledTotalTypes_temp_2;
	int32_t unmarshaledTotalFiles_temp_3 = 0;
	unmarshaledTotalFiles_temp_3 = marshaled.___TotalFiles;
	unmarshaled.___TotalFiles = unmarshaledTotalFiles_temp_3;
	bool unmarshaledIsEditorOnly_temp_4 = false;
	unmarshaledIsEditorOnly_temp_4 = static_cast<bool>(marshaled.___IsEditorOnly);
	unmarshaled.___IsEditorOnly = unmarshaledIsEditorOnly_temp_4;
}
IL2CPP_EXTERN_C void MonoScriptData_tC4741F8605468725923C14E79920D6E03729678A_marshal_pinvoke_cleanup(MonoScriptData_tC4741F8605468725923C14E79920D6E03729678A_marshaled_pinvoke& marshaled)
{
	il2cpp_codegen_com_destroy_safe_array(marshaled.___FilePathsData);
	marshaled.___FilePathsData = NULL;
	il2cpp_codegen_com_destroy_safe_array(marshaled.___TypesData);
	marshaled.___TypesData = NULL;
}
IL2CPP_EXTERN_C void MonoScriptData_tC4741F8605468725923C14E79920D6E03729678A_marshal_com(const MonoScriptData_tC4741F8605468725923C14E79920D6E03729678A& unmarshaled, MonoScriptData_tC4741F8605468725923C14E79920D6E03729678A_marshaled_com& marshaled)
{
	marshaled.___FilePathsData = il2cpp_codegen_com_marshal_safe_array(IL2CPP_VT_I1, unmarshaled.___FilePathsData);
	marshaled.___TypesData = il2cpp_codegen_com_marshal_safe_array(IL2CPP_VT_I1, unmarshaled.___TypesData);
	marshaled.___TotalTypes = unmarshaled.___TotalTypes;
	marshaled.___TotalFiles = unmarshaled.___TotalFiles;
	marshaled.___IsEditorOnly = static_cast<int32_t>(unmarshaled.___IsEditorOnly);
}
IL2CPP_EXTERN_C void MonoScriptData_tC4741F8605468725923C14E79920D6E03729678A_marshal_com_back(const MonoScriptData_tC4741F8605468725923C14E79920D6E03729678A_marshaled_com& marshaled, MonoScriptData_tC4741F8605468725923C14E79920D6E03729678A& unmarshaled)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	unmarshaled.___FilePathsData = (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I1, Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, marshaled.___FilePathsData);
	Il2CppCodeGenWriteBarrier((void**)(&unmarshaled.___FilePathsData), (void*)(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I1, Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, marshaled.___FilePathsData));
	unmarshaled.___TypesData = (ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I1, Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, marshaled.___TypesData);
	Il2CppCodeGenWriteBarrier((void**)(&unmarshaled.___TypesData), (void*)(ByteU5BU5D_tA6237BF417AE52AD70CFB4EF24A7A82613DF9031*)il2cpp_codegen_com_marshal_safe_array_result(IL2CPP_VT_I1, Byte_t94D9231AC217BE4D2E004C4CD32DF6D099EA41A3_il2cpp_TypeInfo_var, marshaled.___TypesData));
	int32_t unmarshaledTotalTypes_temp_2 = 0;
	unmarshaledTotalTypes_temp_2 = marshaled.___TotalTypes;
	unmarshaled.___TotalTypes = unmarshaledTotalTypes_temp_2;
	int32_t unmarshaledTotalFiles_temp_3 = 0;
	unmarshaledTotalFiles_temp_3 = marshaled.___TotalFiles;
	unmarshaled.___TotalFiles = unmarshaledTotalFiles_temp_3;
	bool unmarshaledIsEditorOnly_temp_4 = false;
	unmarshaledIsEditorOnly_temp_4 = static_cast<bool>(marshaled.___IsEditorOnly);
	unmarshaled.___IsEditorOnly = unmarshaledIsEditorOnly_temp_4;
}
IL2CPP_EXTERN_C void MonoScriptData_tC4741F8605468725923C14E79920D6E03729678A_marshal_com_cleanup(MonoScriptData_tC4741F8605468725923C14E79920D6E03729678A_marshaled_com& marshaled)
{
	il2cpp_codegen_com_destroy_safe_array(marshaled.___FilePathsData);
	marshaled.___FilePathsData = NULL;
	il2cpp_codegen_com_destroy_safe_array(marshaled.___TypesData);
	marshaled.___TypesData = NULL;
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void CallbackHandle__ctor_m97D3F2122D4D4B7C29D896ABD6CFD1A0A59F4F5A (CallbackHandle_t914DE6EB40EA9CA30F3369CC846342328562A3D8* __this, uint32_t ___0_id, int32_t ___1_callbackType, const RuntimeMethod* method) 
{
	{
		uint32_t L_0 = ___0_id;
		__this->____id = L_0;
		int32_t L_1 = ___1_callbackType;
		__this->____callbackType = L_1;
		return;
	}
}
IL2CPP_EXTERN_C  void CallbackHandle__ctor_m97D3F2122D4D4B7C29D896ABD6CFD1A0A59F4F5A_AdjustorThunk (RuntimeObject* __this, uint32_t ___0_id, int32_t ___1_callbackType, const RuntimeMethod* method)
{
	CallbackHandle_t914DE6EB40EA9CA30F3369CC846342328562A3D8* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<CallbackHandle_t914DE6EB40EA9CA30F3369CC846342328562A3D8*>(__this + _offset);
	CallbackHandle__ctor_m97D3F2122D4D4B7C29D896ABD6CFD1A0A59F4F5A(_thisAdjusted, ___0_id, ___1_callbackType, method);
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool CallbackHandle_get_IsValid_mE586BDFFBE7C43187113900DF7F1CEA541BCF7D5 (CallbackHandle_t914DE6EB40EA9CA30F3369CC846342328562A3D8* __this, const RuntimeMethod* method) 
{
	{
		uint32_t L_0 = __this->____id;
		return (bool)((!(((uint32_t)L_0) <= ((uint32_t)0)))? 1 : 0);
	}
}
IL2CPP_EXTERN_C  bool CallbackHandle_get_IsValid_mE586BDFFBE7C43187113900DF7F1CEA541BCF7D5_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	CallbackHandle_t914DE6EB40EA9CA30F3369CC846342328562A3D8* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<CallbackHandle_t914DE6EB40EA9CA30F3369CC846342328562A3D8*>(__this + _offset);
	bool _returnValue;
	_returnValue = CallbackHandle_get_IsValid_mE586BDFFBE7C43187113900DF7F1CEA541BCF7D5(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool CallbackHandle_get_IsScheduled_m0631C17FD5DAD74B9813E5AFC1CB252E348F1606 (CallbackHandle_t914DE6EB40EA9CA30F3369CC846342328562A3D8* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&CallbackManager_t19FF0BF336ADE5A782EFCB9C894B3A9351431233_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		bool L_0;
		L_0 = CallbackHandle_get_IsValid_mE586BDFFBE7C43187113900DF7F1CEA541BCF7D5(__this, NULL);
		if (!L_0)
		{
			goto IL_001a;
		}
	}
	{
		uint32_t L_1 = __this->____id;
		int32_t L_2 = __this->____callbackType;
		il2cpp_codegen_runtime_class_init_inline(CallbackManager_t19FF0BF336ADE5A782EFCB9C894B3A9351431233_il2cpp_TypeInfo_var);
		bool L_3;
		L_3 = CallbackManager_IsScheduled_m2E4F185FA620760970FEEA6525EE13539F893C7E(L_1, L_2, NULL);
		return L_3;
	}

IL_001a:
	{
		return (bool)0;
	}
}
IL2CPP_EXTERN_C  bool CallbackHandle_get_IsScheduled_m0631C17FD5DAD74B9813E5AFC1CB252E348F1606_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	CallbackHandle_t914DE6EB40EA9CA30F3369CC846342328562A3D8* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<CallbackHandle_t914DE6EB40EA9CA30F3369CC846342328562A3D8*>(__this + _offset);
	bool _returnValue;
	_returnValue = CallbackHandle_get_IsScheduled_m0631C17FD5DAD74B9813E5AFC1CB252E348F1606(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool CallbackHandle_Cancel_m93E2C960EF412C0FF80D2A17690F0F0905582CF4 (CallbackHandle_t914DE6EB40EA9CA30F3369CC846342328562A3D8* __this, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&CallbackManager_t19FF0BF336ADE5A782EFCB9C894B3A9351431233_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		bool L_0;
		L_0 = CallbackHandle_get_IsValid_mE586BDFFBE7C43187113900DF7F1CEA541BCF7D5(__this, NULL);
		if (!L_0)
		{
			goto IL_001a;
		}
	}
	{
		uint32_t L_1 = __this->____id;
		int32_t L_2 = __this->____callbackType;
		il2cpp_codegen_runtime_class_init_inline(CallbackManager_t19FF0BF336ADE5A782EFCB9C894B3A9351431233_il2cpp_TypeInfo_var);
		bool L_3;
		L_3 = CallbackManager_Cancel_m1ECC8FD014AC3696596474119492EA2FC8A7ECDF(L_1, L_2, NULL);
		return L_3;
	}

IL_001a:
	{
		return (bool)0;
	}
}
IL2CPP_EXTERN_C  bool CallbackHandle_Cancel_m93E2C960EF412C0FF80D2A17690F0F0905582CF4_AdjustorThunk (RuntimeObject* __this, const RuntimeMethod* method)
{
	CallbackHandle_t914DE6EB40EA9CA30F3369CC846342328562A3D8* _thisAdjusted;
	int32_t _offset = 1;
	_thisAdjusted = reinterpret_cast<CallbackHandle_t914DE6EB40EA9CA30F3369CC846342328562A3D8*>(__this + _offset);
	bool _returnValue;
	_returnValue = CallbackHandle_Cancel_m93E2C960EF412C0FF80D2A17690F0F0905582CF4(_thisAdjusted, method);
	return _returnValue;
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR CallbackHandle_t914DE6EB40EA9CA30F3369CC846342328562A3D8 CallbackHandle_get_Invalid_mE4F35DC8D50CEFB2A060DD143AE61619169FA9CD (const RuntimeMethod* method) 
{
	{
		CallbackHandle_t914DE6EB40EA9CA30F3369CC846342328562A3D8 L_0;
		memset((&L_0), 0, sizeof(L_0));
		CallbackHandle__ctor_m97D3F2122D4D4B7C29D896ABD6CFD1A0A59F4F5A((&L_0), 0, 0, NULL);
		return L_0;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR CallbackHandle_t914DE6EB40EA9CA30F3369CC846342328562A3D8 CallbackManager_ScheduleInternal_m154FC729A38E3C5A06491802E29BB0202ABC6B83 (int32_t ___0_callbackType, float ___1_delay, float ___2_interval, Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___3_callback, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&CallbackManager_t19FF0BF336ADE5A782EFCB9C894B3A9351431233_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Dictionary_2_TryGetValue_m49E9CE1E830D15D25D10B8B5E4C989FB8A4D40B4_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Dictionary_2_set_Item_mF3EFD099128AB040A21FE90E5ED67E8EB7065A38_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_Add_mBDD7C010CDF02D1D1DCDE763219EECF6B674B31B_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1__ctor_mCC7ECC615E36C87B23B685E76B7B7770A3C6C02E_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_t6133AE9445F833C234B275E156F970487A5C1A4A_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	List_1_t6133AE9445F833C234B275E156F970487A5C1A4A* V_0 = NULL;
	CallbackInfo_t172261DACE580EF109EA5901886ED422A5849948 V_1;
	memset((&V_1), 0, sizeof(V_1));
	CallbackInfo_t172261DACE580EF109EA5901886ED422A5849948 V_2;
	memset((&V_2), 0, sizeof(V_2));
	{
		il2cpp_codegen_runtime_class_init_inline(CallbackManager_t19FF0BF336ADE5A782EFCB9C894B3A9351431233_il2cpp_TypeInfo_var);
		Dictionary_2_t710C8281BB25AB47EA84E7D852BE0E831EB82C31* L_0 = ((CallbackManager_t19FF0BF336ADE5A782EFCB9C894B3A9351431233_StaticFields*)il2cpp_codegen_static_fields_for(CallbackManager_t19FF0BF336ADE5A782EFCB9C894B3A9351431233_il2cpp_TypeInfo_var))->____callbacks;
		int32_t L_1 = ___0_callbackType;
		NullCheck(L_0);
		bool L_2;
		L_2 = Dictionary_2_TryGetValue_m49E9CE1E830D15D25D10B8B5E4C989FB8A4D40B4(L_0, L_1, (&V_0), Dictionary_2_TryGetValue_m49E9CE1E830D15D25D10B8B5E4C989FB8A4D40B4_RuntimeMethod_var);
		if (L_2)
		{
			goto IL_0021;
		}
	}
	{
		List_1_t6133AE9445F833C234B275E156F970487A5C1A4A* L_3 = (List_1_t6133AE9445F833C234B275E156F970487A5C1A4A*)il2cpp_codegen_object_new(List_1_t6133AE9445F833C234B275E156F970487A5C1A4A_il2cpp_TypeInfo_var);
		List_1__ctor_mCC7ECC615E36C87B23B685E76B7B7770A3C6C02E(L_3, List_1__ctor_mCC7ECC615E36C87B23B685E76B7B7770A3C6C02E_RuntimeMethod_var);
		V_0 = L_3;
		il2cpp_codegen_runtime_class_init_inline(CallbackManager_t19FF0BF336ADE5A782EFCB9C894B3A9351431233_il2cpp_TypeInfo_var);
		Dictionary_2_t710C8281BB25AB47EA84E7D852BE0E831EB82C31* L_4 = ((CallbackManager_t19FF0BF336ADE5A782EFCB9C894B3A9351431233_StaticFields*)il2cpp_codegen_static_fields_for(CallbackManager_t19FF0BF336ADE5A782EFCB9C894B3A9351431233_il2cpp_TypeInfo_var))->____callbacks;
		int32_t L_5 = ___0_callbackType;
		List_1_t6133AE9445F833C234B275E156F970487A5C1A4A* L_6 = V_0;
		NullCheck(L_4);
		Dictionary_2_set_Item_mF3EFD099128AB040A21FE90E5ED67E8EB7065A38(L_4, L_5, L_6, Dictionary_2_set_Item_mF3EFD099128AB040A21FE90E5ED67E8EB7065A38_RuntimeMethod_var);
	}

IL_0021:
	{
		il2cpp_codegen_initobj((&V_2), sizeof(CallbackInfo_t172261DACE580EF109EA5901886ED422A5849948));
		il2cpp_codegen_runtime_class_init_inline(CallbackManager_t19FF0BF336ADE5A782EFCB9C894B3A9351431233_il2cpp_TypeInfo_var);
		uint32_t L_7 = ((CallbackManager_t19FF0BF336ADE5A782EFCB9C894B3A9351431233_StaticFields*)il2cpp_codegen_static_fields_for(CallbackManager_t19FF0BF336ADE5A782EFCB9C894B3A9351431233_il2cpp_TypeInfo_var))->____idCounter;
		uint32_t L_8 = L_7;
		((CallbackManager_t19FF0BF336ADE5A782EFCB9C894B3A9351431233_StaticFields*)il2cpp_codegen_static_fields_for(CallbackManager_t19FF0BF336ADE5A782EFCB9C894B3A9351431233_il2cpp_TypeInfo_var))->____idCounter = ((int32_t)il2cpp_codegen_add((int32_t)L_8, 1));
		(&V_2)->___Id = L_8;
		int32_t L_9 = ___0_callbackType;
		(&V_2)->___Type = L_9;
		float L_10 = ___1_delay;
		(&V_2)->___DelayTime = L_10;
		float L_11 = ___2_interval;
		(&V_2)->___Interval = L_11;
		(&V_2)->___LastTriggerTime = (0.0f);
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_12 = ___3_callback;
		(&V_2)->___Callback = L_12;
		Il2CppCodeGenWriteBarrier((void**)(&(&V_2)->___Callback), (void*)L_12);
		CallbackInfo_t172261DACE580EF109EA5901886ED422A5849948 L_13 = V_2;
		V_1 = L_13;
		List_1_t6133AE9445F833C234B275E156F970487A5C1A4A* L_14 = V_0;
		CallbackInfo_t172261DACE580EF109EA5901886ED422A5849948 L_15 = V_1;
		NullCheck(L_14);
		List_1_Add_mBDD7C010CDF02D1D1DCDE763219EECF6B674B31B_inline(L_14, L_15, List_1_Add_mBDD7C010CDF02D1D1DCDE763219EECF6B674B31B_RuntimeMethod_var);
		CallbackInfo_t172261DACE580EF109EA5901886ED422A5849948 L_16 = V_1;
		uint32_t L_17 = L_16.___Id;
		int32_t L_18 = ___0_callbackType;
		CallbackHandle_t914DE6EB40EA9CA30F3369CC846342328562A3D8 L_19;
		memset((&L_19), 0, sizeof(L_19));
		CallbackHandle__ctor_m97D3F2122D4D4B7C29D896ABD6CFD1A0A59F4F5A((&L_19), L_17, L_18, NULL);
		return L_19;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR CallbackHandle_t914DE6EB40EA9CA30F3369CC846342328562A3D8 CallbackManager_ScheduleDelayed_m3A806D1328E82A8855EF2EBBC78CC69190C44271 (float ___0_delay, Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___1_Callback, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&CallbackManager_t19FF0BF336ADE5A782EFCB9C894B3A9351431233_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		float L_0 = ___0_delay;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_1 = ___1_Callback;
		il2cpp_codegen_runtime_class_init_inline(CallbackManager_t19FF0BF336ADE5A782EFCB9C894B3A9351431233_il2cpp_TypeInfo_var);
		CallbackHandle_t914DE6EB40EA9CA30F3369CC846342328562A3D8 L_2;
		L_2 = CallbackManager_ScheduleInternal_m154FC729A38E3C5A06491802E29BB0202ABC6B83(0, L_0, (0.0f), L_1, NULL);
		return L_2;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR CallbackHandle_t914DE6EB40EA9CA30F3369CC846342328562A3D8 CallbackManager_ScheduleDelayed_m02C498F3ACD8BCB4C8903C0B90DDD4983114D65D (int32_t ___0_delayFrame, Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___1_Callback, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&CallbackManager_t19FF0BF336ADE5A782EFCB9C894B3A9351431233_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		int32_t L_0 = ___0_delayFrame;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_1 = ___1_Callback;
		il2cpp_codegen_runtime_class_init_inline(CallbackManager_t19FF0BF336ADE5A782EFCB9C894B3A9351431233_il2cpp_TypeInfo_var);
		CallbackHandle_t914DE6EB40EA9CA30F3369CC846342328562A3D8 L_2;
		L_2 = CallbackManager_ScheduleInternal_m154FC729A38E3C5A06491802E29BB0202ABC6B83(1, ((float)L_0), (0.0f), L_1, NULL);
		return L_2;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR CallbackHandle_t914DE6EB40EA9CA30F3369CC846342328562A3D8 CallbackManager_ScheduleTick_m5A33E4AC1506D69768C391AD82453FE5B029321A (float ___0_delay, float ___1_interval, Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___2_Callback, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&CallbackManager_t19FF0BF336ADE5A782EFCB9C894B3A9351431233_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		float L_0 = ___0_delay;
		float L_1 = ___1_interval;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_2 = ___2_Callback;
		il2cpp_codegen_runtime_class_init_inline(CallbackManager_t19FF0BF336ADE5A782EFCB9C894B3A9351431233_il2cpp_TypeInfo_var);
		CallbackHandle_t914DE6EB40EA9CA30F3369CC846342328562A3D8 L_3;
		L_3 = CallbackManager_ScheduleInternal_m154FC729A38E3C5A06491802E29BB0202ABC6B83(2, L_0, L_1, L_2, NULL);
		return L_3;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR CallbackHandle_t914DE6EB40EA9CA30F3369CC846342328562A3D8 CallbackManager_SchedulePerUpdate_m573ECC524C493CB40D7F7B9353600B2C7CFA2CF1 (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___0_Callback, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&CallbackManager_t19FF0BF336ADE5A782EFCB9C894B3A9351431233_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_0 = ___0_Callback;
		il2cpp_codegen_runtime_class_init_inline(CallbackManager_t19FF0BF336ADE5A782EFCB9C894B3A9351431233_il2cpp_TypeInfo_var);
		CallbackHandle_t914DE6EB40EA9CA30F3369CC846342328562A3D8 L_1;
		L_1 = CallbackManager_ScheduleInternal_m154FC729A38E3C5A06491802E29BB0202ABC6B83(2, (0.0f), (0.0f), L_0, NULL);
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR CallbackHandle_t914DE6EB40EA9CA30F3369CC846342328562A3D8 CallbackManager_SchedulePerLateUpdate_mED89E248ACFA62446D47D06FF0582B9D9874D336 (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* ___0_Callback, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&CallbackManager_t19FF0BF336ADE5A782EFCB9C894B3A9351431233_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_0 = ___0_Callback;
		il2cpp_codegen_runtime_class_init_inline(CallbackManager_t19FF0BF336ADE5A782EFCB9C894B3A9351431233_il2cpp_TypeInfo_var);
		CallbackHandle_t914DE6EB40EA9CA30F3369CC846342328562A3D8 L_1;
		L_1 = CallbackManager_ScheduleInternal_m154FC729A38E3C5A06491802E29BB0202ABC6B83(3, (0.0f), (0.0f), L_0, NULL);
		return L_1;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool CallbackManager_IsScheduled_m2E4F185FA620760970FEEA6525EE13539F893C7E (uint32_t ___0_id, int32_t ___1_callbackType, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&CallbackManager_t19FF0BF336ADE5A782EFCB9C894B3A9351431233_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Dictionary_2_TryGetValue_m49E9CE1E830D15D25D10B8B5E4C989FB8A4D40B4_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_get_Count_mA4EB8F9036B4BE1B5D1E9F6F0617B8CEAACBBE34_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_get_Item_mCEFF728AFC5447D2EDB722AD83D034CEE0D75691_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	List_1_t6133AE9445F833C234B275E156F970487A5C1A4A* V_0 = NULL;
	int32_t V_1 = 0;
	{
		il2cpp_codegen_runtime_class_init_inline(CallbackManager_t19FF0BF336ADE5A782EFCB9C894B3A9351431233_il2cpp_TypeInfo_var);
		Dictionary_2_t710C8281BB25AB47EA84E7D852BE0E831EB82C31* L_0 = ((CallbackManager_t19FF0BF336ADE5A782EFCB9C894B3A9351431233_StaticFields*)il2cpp_codegen_static_fields_for(CallbackManager_t19FF0BF336ADE5A782EFCB9C894B3A9351431233_il2cpp_TypeInfo_var))->____callbacks;
		int32_t L_1 = ___1_callbackType;
		NullCheck(L_0);
		bool L_2;
		L_2 = Dictionary_2_TryGetValue_m49E9CE1E830D15D25D10B8B5E4C989FB8A4D40B4(L_0, L_1, (&V_0), Dictionary_2_TryGetValue_m49E9CE1E830D15D25D10B8B5E4C989FB8A4D40B4_RuntimeMethod_var);
		if (!L_2)
		{
			goto IL_0031;
		}
	}
	{
		V_1 = 0;
		goto IL_0028;
	}

IL_0013:
	{
		List_1_t6133AE9445F833C234B275E156F970487A5C1A4A* L_3 = V_0;
		int32_t L_4 = V_1;
		NullCheck(L_3);
		CallbackInfo_t172261DACE580EF109EA5901886ED422A5849948 L_5;
		L_5 = List_1_get_Item_mCEFF728AFC5447D2EDB722AD83D034CEE0D75691(L_3, L_4, List_1_get_Item_mCEFF728AFC5447D2EDB722AD83D034CEE0D75691_RuntimeMethod_var);
		uint32_t L_6 = L_5.___Id;
		uint32_t L_7 = ___0_id;
		if ((!(((uint32_t)L_6) == ((uint32_t)L_7))))
		{
			goto IL_0024;
		}
	}
	{
		return (bool)1;
	}

IL_0024:
	{
		int32_t L_8 = V_1;
		V_1 = ((int32_t)il2cpp_codegen_add(L_8, 1));
	}

IL_0028:
	{
		int32_t L_9 = V_1;
		List_1_t6133AE9445F833C234B275E156F970487A5C1A4A* L_10 = V_0;
		NullCheck(L_10);
		int32_t L_11;
		L_11 = List_1_get_Count_mA4EB8F9036B4BE1B5D1E9F6F0617B8CEAACBBE34_inline(L_10, List_1_get_Count_mA4EB8F9036B4BE1B5D1E9F6F0617B8CEAACBBE34_RuntimeMethod_var);
		if ((((int32_t)L_9) < ((int32_t)L_11)))
		{
			goto IL_0013;
		}
	}

IL_0031:
	{
		return (bool)0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR bool CallbackManager_Cancel_m1ECC8FD014AC3696596474119492EA2FC8A7ECDF (uint32_t ___0_id, int32_t ___1_callbackType, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&CallbackManager_t19FF0BF336ADE5A782EFCB9C894B3A9351431233_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Dictionary_2_TryGetValue_m49E9CE1E830D15D25D10B8B5E4C989FB8A4D40B4_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_RemoveAt_mD499A88E9E091C1605D6AD025613729B87B11350_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_get_Count_mA4EB8F9036B4BE1B5D1E9F6F0617B8CEAACBBE34_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_get_Item_mCEFF728AFC5447D2EDB722AD83D034CEE0D75691_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	List_1_t6133AE9445F833C234B275E156F970487A5C1A4A* V_0 = NULL;
	int32_t V_1 = 0;
	{
		il2cpp_codegen_runtime_class_init_inline(CallbackManager_t19FF0BF336ADE5A782EFCB9C894B3A9351431233_il2cpp_TypeInfo_var);
		Dictionary_2_t710C8281BB25AB47EA84E7D852BE0E831EB82C31* L_0 = ((CallbackManager_t19FF0BF336ADE5A782EFCB9C894B3A9351431233_StaticFields*)il2cpp_codegen_static_fields_for(CallbackManager_t19FF0BF336ADE5A782EFCB9C894B3A9351431233_il2cpp_TypeInfo_var))->____callbacks;
		int32_t L_1 = ___1_callbackType;
		NullCheck(L_0);
		bool L_2;
		L_2 = Dictionary_2_TryGetValue_m49E9CE1E830D15D25D10B8B5E4C989FB8A4D40B4(L_0, L_1, (&V_0), Dictionary_2_TryGetValue_m49E9CE1E830D15D25D10B8B5E4C989FB8A4D40B4_RuntimeMethod_var);
		if (!L_2)
		{
			goto IL_0038;
		}
	}
	{
		V_1 = 0;
		goto IL_002f;
	}

IL_0013:
	{
		List_1_t6133AE9445F833C234B275E156F970487A5C1A4A* L_3 = V_0;
		int32_t L_4 = V_1;
		NullCheck(L_3);
		CallbackInfo_t172261DACE580EF109EA5901886ED422A5849948 L_5;
		L_5 = List_1_get_Item_mCEFF728AFC5447D2EDB722AD83D034CEE0D75691(L_3, L_4, List_1_get_Item_mCEFF728AFC5447D2EDB722AD83D034CEE0D75691_RuntimeMethod_var);
		uint32_t L_6 = L_5.___Id;
		uint32_t L_7 = ___0_id;
		if ((!(((uint32_t)L_6) == ((uint32_t)L_7))))
		{
			goto IL_002b;
		}
	}
	{
		List_1_t6133AE9445F833C234B275E156F970487A5C1A4A* L_8 = V_0;
		int32_t L_9 = V_1;
		NullCheck(L_8);
		List_1_RemoveAt_mD499A88E9E091C1605D6AD025613729B87B11350(L_8, L_9, List_1_RemoveAt_mD499A88E9E091C1605D6AD025613729B87B11350_RuntimeMethod_var);
		return (bool)1;
	}

IL_002b:
	{
		int32_t L_10 = V_1;
		V_1 = ((int32_t)il2cpp_codegen_add(L_10, 1));
	}

IL_002f:
	{
		int32_t L_11 = V_1;
		List_1_t6133AE9445F833C234B275E156F970487A5C1A4A* L_12 = V_0;
		NullCheck(L_12);
		int32_t L_13;
		L_13 = List_1_get_Count_mA4EB8F9036B4BE1B5D1E9F6F0617B8CEAACBBE34_inline(L_12, List_1_get_Count_mA4EB8F9036B4BE1B5D1E9F6F0617B8CEAACBBE34_RuntimeMethod_var);
		if ((((int32_t)L_11) < ((int32_t)L_13)))
		{
			goto IL_0013;
		}
	}

IL_0038:
	{
		return (bool)0;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void CallbackManager_Clear_m5906EDAB09F274110C1215280074D5C7F5B03788 (int32_t ___0_type, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&CallbackManager_t19FF0BF336ADE5A782EFCB9C894B3A9351431233_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Dictionary_2_TryGetValue_m49E9CE1E830D15D25D10B8B5E4C989FB8A4D40B4_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_Clear_mB9FD8653ACB072F2B37239AA109F9FCEEF552D1F_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	List_1_t6133AE9445F833C234B275E156F970487A5C1A4A* V_0 = NULL;
	{
		il2cpp_codegen_runtime_class_init_inline(CallbackManager_t19FF0BF336ADE5A782EFCB9C894B3A9351431233_il2cpp_TypeInfo_var);
		Dictionary_2_t710C8281BB25AB47EA84E7D852BE0E831EB82C31* L_0 = ((CallbackManager_t19FF0BF336ADE5A782EFCB9C894B3A9351431233_StaticFields*)il2cpp_codegen_static_fields_for(CallbackManager_t19FF0BF336ADE5A782EFCB9C894B3A9351431233_il2cpp_TypeInfo_var))->____callbacks;
		int32_t L_1 = ___0_type;
		NullCheck(L_0);
		bool L_2;
		L_2 = Dictionary_2_TryGetValue_m49E9CE1E830D15D25D10B8B5E4C989FB8A4D40B4(L_0, L_1, (&V_0), Dictionary_2_TryGetValue_m49E9CE1E830D15D25D10B8B5E4C989FB8A4D40B4_RuntimeMethod_var);
		if (!L_2)
		{
			goto IL_0015;
		}
	}
	{
		List_1_t6133AE9445F833C234B275E156F970487A5C1A4A* L_3 = V_0;
		NullCheck(L_3);
		List_1_Clear_mB9FD8653ACB072F2B37239AA109F9FCEEF552D1F_inline(L_3, List_1_Clear_mB9FD8653ACB072F2B37239AA109F9FCEEF552D1F_RuntimeMethod_var);
	}

IL_0015:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void CallbackManager_ClearAll_m0D452895F49356DC933A9858D4F42DA2B02A8631 (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&CallbackManager_t19FF0BF336ADE5A782EFCB9C894B3A9351431233_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Dictionary_2_Clear_m819255C8722E66F2B4CD9B6746F5C78734E9DC35_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		il2cpp_codegen_runtime_class_init_inline(CallbackManager_t19FF0BF336ADE5A782EFCB9C894B3A9351431233_il2cpp_TypeInfo_var);
		((CallbackManager_t19FF0BF336ADE5A782EFCB9C894B3A9351431233_StaticFields*)il2cpp_codegen_static_fields_for(CallbackManager_t19FF0BF336ADE5A782EFCB9C894B3A9351431233_il2cpp_TypeInfo_var))->____idCounter = 1;
		Dictionary_2_t710C8281BB25AB47EA84E7D852BE0E831EB82C31* L_0 = ((CallbackManager_t19FF0BF336ADE5A782EFCB9C894B3A9351431233_StaticFields*)il2cpp_codegen_static_fields_for(CallbackManager_t19FF0BF336ADE5A782EFCB9C894B3A9351431233_il2cpp_TypeInfo_var))->____callbacks;
		NullCheck(L_0);
		Dictionary_2_Clear_m819255C8722E66F2B4CD9B6746F5C78734E9DC35(L_0, Dictionary_2_Clear_m819255C8722E66F2B4CD9B6746F5C78734E9DC35_RuntimeMethod_var);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void CallbackManager_Tick_m58BC1D9D775B8808A439F1D1619009F19FE9FDA7 (float ___0_deltaTime, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&CallbackManager_t19FF0BF336ADE5A782EFCB9C894B3A9351431233_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Dictionary_2_Remove_m35C384BB7E7C99D8BB766D164AE52DC69FBC2612_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Dictionary_2_TryGetValue_m49E9CE1E830D15D25D10B8B5E4C989FB8A4D40B4_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_RemoveAt_mD499A88E9E091C1605D6AD025613729B87B11350_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_get_Count_mA4EB8F9036B4BE1B5D1E9F6F0617B8CEAACBBE34_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_get_Item_mCEFF728AFC5447D2EDB722AD83D034CEE0D75691_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_set_Item_m2A352F23425005A1161BBA095CB21917038EE6E0_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	List_1_t6133AE9445F833C234B275E156F970487A5C1A4A* V_0 = NULL;
	List_1_t6133AE9445F833C234B275E156F970487A5C1A4A* V_1 = NULL;
	int32_t V_2 = 0;
	CallbackInfo_t172261DACE580EF109EA5901886ED422A5849948 V_3;
	memset((&V_3), 0, sizeof(V_3));
	int32_t V_4 = 0;
	CallbackInfo_t172261DACE580EF109EA5901886ED422A5849948 V_5;
	memset((&V_5), 0, sizeof(V_5));
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* G_B5_0 = NULL;
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* G_B4_0 = NULL;
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* G_B16_0 = NULL;
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* G_B15_0 = NULL;
	{
		float L_0 = ___0_deltaTime;
		il2cpp_codegen_runtime_class_init_inline(CallbackManager_t19FF0BF336ADE5A782EFCB9C894B3A9351431233_il2cpp_TypeInfo_var);
		CallbackManager_Invoke_m8E23D655062E2C22BFADA57F555C57470E3D1B58(2, L_0, NULL);
		Dictionary_2_t710C8281BB25AB47EA84E7D852BE0E831EB82C31* L_1 = ((CallbackManager_t19FF0BF336ADE5A782EFCB9C894B3A9351431233_StaticFields*)il2cpp_codegen_static_fields_for(CallbackManager_t19FF0BF336ADE5A782EFCB9C894B3A9351431233_il2cpp_TypeInfo_var))->____callbacks;
		NullCheck(L_1);
		bool L_2;
		L_2 = Dictionary_2_TryGetValue_m49E9CE1E830D15D25D10B8B5E4C989FB8A4D40B4(L_1, 0, (&V_0), Dictionary_2_TryGetValue_m49E9CE1E830D15D25D10B8B5E4C989FB8A4D40B4_RuntimeMethod_var);
		if (!L_2)
		{
			goto IL_007f;
		}
	}
	{
		List_1_t6133AE9445F833C234B275E156F970487A5C1A4A* L_3 = V_0;
		NullCheck(L_3);
		int32_t L_4;
		L_4 = List_1_get_Count_mA4EB8F9036B4BE1B5D1E9F6F0617B8CEAACBBE34_inline(L_3, List_1_get_Count_mA4EB8F9036B4BE1B5D1E9F6F0617B8CEAACBBE34_RuntimeMethod_var);
		V_2 = ((int32_t)il2cpp_codegen_subtract(L_4, 1));
		goto IL_0066;
	}

IL_0021:
	{
		List_1_t6133AE9445F833C234B275E156F970487A5C1A4A* L_5 = V_0;
		int32_t L_6 = V_2;
		NullCheck(L_5);
		CallbackInfo_t172261DACE580EF109EA5901886ED422A5849948 L_7;
		L_7 = List_1_get_Item_mCEFF728AFC5447D2EDB722AD83D034CEE0D75691(L_5, L_6, List_1_get_Item_mCEFF728AFC5447D2EDB722AD83D034CEE0D75691_RuntimeMethod_var);
		V_3 = L_7;
		float* L_8 = (float*)(&(&V_3)->___DelayTime);
		float* L_9 = L_8;
		float L_10 = *((float*)L_9);
		float L_11 = ___0_deltaTime;
		*((float*)L_9) = (float)((float)il2cpp_codegen_subtract(L_10, L_11));
		List_1_t6133AE9445F833C234B275E156F970487A5C1A4A* L_12 = V_0;
		int32_t L_13 = V_2;
		CallbackInfo_t172261DACE580EF109EA5901886ED422A5849948 L_14 = V_3;
		NullCheck(L_12);
		List_1_set_Item_m2A352F23425005A1161BBA095CB21917038EE6E0(L_12, L_13, L_14, List_1_set_Item_m2A352F23425005A1161BBA095CB21917038EE6E0_RuntimeMethod_var);
		CallbackInfo_t172261DACE580EF109EA5901886ED422A5849948 L_15 = V_3;
		float L_16 = L_15.___DelayTime;
		if ((!(((float)L_16) <= ((float)(0.0f)))))
		{
			goto IL_0062;
		}
	}
	{
		CallbackInfo_t172261DACE580EF109EA5901886ED422A5849948 L_17 = V_3;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_18 = L_17.___Callback;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_19 = L_18;
		if (L_19)
		{
			G_B5_0 = L_19;
			goto IL_0056;
		}
		G_B4_0 = L_19;
	}
	{
		goto IL_005b;
	}

IL_0056:
	{
		NullCheck(G_B5_0);
		Action_Invoke_m7126A54DACA72B845424072887B5F3A51FC3808E_inline(G_B5_0, NULL);
	}

IL_005b:
	{
		List_1_t6133AE9445F833C234B275E156F970487A5C1A4A* L_20 = V_0;
		int32_t L_21 = V_2;
		NullCheck(L_20);
		List_1_RemoveAt_mD499A88E9E091C1605D6AD025613729B87B11350(L_20, L_21, List_1_RemoveAt_mD499A88E9E091C1605D6AD025613729B87B11350_RuntimeMethod_var);
	}

IL_0062:
	{
		int32_t L_22 = V_2;
		V_2 = ((int32_t)il2cpp_codegen_subtract(L_22, 1));
	}

IL_0066:
	{
		int32_t L_23 = V_2;
		if ((((int32_t)L_23) >= ((int32_t)0)))
		{
			goto IL_0021;
		}
	}
	{
		List_1_t6133AE9445F833C234B275E156F970487A5C1A4A* L_24 = V_0;
		NullCheck(L_24);
		int32_t L_25;
		L_25 = List_1_get_Count_mA4EB8F9036B4BE1B5D1E9F6F0617B8CEAACBBE34_inline(L_24, List_1_get_Count_mA4EB8F9036B4BE1B5D1E9F6F0617B8CEAACBBE34_RuntimeMethod_var);
		if ((((int32_t)L_25) > ((int32_t)0)))
		{
			goto IL_007f;
		}
	}
	{
		il2cpp_codegen_runtime_class_init_inline(CallbackManager_t19FF0BF336ADE5A782EFCB9C894B3A9351431233_il2cpp_TypeInfo_var);
		Dictionary_2_t710C8281BB25AB47EA84E7D852BE0E831EB82C31* L_26 = ((CallbackManager_t19FF0BF336ADE5A782EFCB9C894B3A9351431233_StaticFields*)il2cpp_codegen_static_fields_for(CallbackManager_t19FF0BF336ADE5A782EFCB9C894B3A9351431233_il2cpp_TypeInfo_var))->____callbacks;
		NullCheck(L_26);
		bool L_27;
		L_27 = Dictionary_2_Remove_m35C384BB7E7C99D8BB766D164AE52DC69FBC2612(L_26, 0, Dictionary_2_Remove_m35C384BB7E7C99D8BB766D164AE52DC69FBC2612_RuntimeMethod_var);
	}

IL_007f:
	{
		il2cpp_codegen_runtime_class_init_inline(CallbackManager_t19FF0BF336ADE5A782EFCB9C894B3A9351431233_il2cpp_TypeInfo_var);
		Dictionary_2_t710C8281BB25AB47EA84E7D852BE0E831EB82C31* L_28 = ((CallbackManager_t19FF0BF336ADE5A782EFCB9C894B3A9351431233_StaticFields*)il2cpp_codegen_static_fields_for(CallbackManager_t19FF0BF336ADE5A782EFCB9C894B3A9351431233_il2cpp_TypeInfo_var))->____callbacks;
		NullCheck(L_28);
		bool L_29;
		L_29 = Dictionary_2_TryGetValue_m49E9CE1E830D15D25D10B8B5E4C989FB8A4D40B4(L_28, 1, (&V_1), Dictionary_2_TryGetValue_m49E9CE1E830D15D25D10B8B5E4C989FB8A4D40B4_RuntimeMethod_var);
		if (!L_29)
		{
			goto IL_0106;
		}
	}
	{
		List_1_t6133AE9445F833C234B275E156F970487A5C1A4A* L_30 = V_1;
		NullCheck(L_30);
		int32_t L_31;
		L_31 = List_1_get_Count_mA4EB8F9036B4BE1B5D1E9F6F0617B8CEAACBBE34_inline(L_30, List_1_get_Count_mA4EB8F9036B4BE1B5D1E9F6F0617B8CEAACBBE34_RuntimeMethod_var);
		V_4 = ((int32_t)il2cpp_codegen_subtract(L_31, 1));
		goto IL_00ec;
	}

IL_009a:
	{
		List_1_t6133AE9445F833C234B275E156F970487A5C1A4A* L_32 = V_1;
		int32_t L_33 = V_4;
		NullCheck(L_32);
		CallbackInfo_t172261DACE580EF109EA5901886ED422A5849948 L_34;
		L_34 = List_1_get_Item_mCEFF728AFC5447D2EDB722AD83D034CEE0D75691(L_32, L_33, List_1_get_Item_mCEFF728AFC5447D2EDB722AD83D034CEE0D75691_RuntimeMethod_var);
		V_5 = L_34;
		float* L_35 = (float*)(&(&V_5)->___DelayTime);
		float* L_36 = L_35;
		float L_37 = *((float*)L_36);
		*((float*)L_36) = (float)((float)il2cpp_codegen_subtract(L_37, (1.0f)));
		List_1_t6133AE9445F833C234B275E156F970487A5C1A4A* L_38 = V_1;
		int32_t L_39 = V_4;
		CallbackInfo_t172261DACE580EF109EA5901886ED422A5849948 L_40 = V_5;
		NullCheck(L_38);
		List_1_set_Item_m2A352F23425005A1161BBA095CB21917038EE6E0(L_38, L_39, L_40, List_1_set_Item_m2A352F23425005A1161BBA095CB21917038EE6E0_RuntimeMethod_var);
		CallbackInfo_t172261DACE580EF109EA5901886ED422A5849948 L_41 = V_5;
		float L_42 = L_41.___DelayTime;
		if ((!(((float)L_42) <= ((float)(0.0f)))))
		{
			goto IL_00e6;
		}
	}
	{
		CallbackInfo_t172261DACE580EF109EA5901886ED422A5849948 L_43 = V_5;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_44 = L_43.___Callback;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_45 = L_44;
		if (L_45)
		{
			G_B16_0 = L_45;
			goto IL_00d9;
		}
		G_B15_0 = L_45;
	}
	{
		goto IL_00de;
	}

IL_00d9:
	{
		NullCheck(G_B16_0);
		Action_Invoke_m7126A54DACA72B845424072887B5F3A51FC3808E_inline(G_B16_0, NULL);
	}

IL_00de:
	{
		List_1_t6133AE9445F833C234B275E156F970487A5C1A4A* L_46 = V_1;
		int32_t L_47 = V_4;
		NullCheck(L_46);
		List_1_RemoveAt_mD499A88E9E091C1605D6AD025613729B87B11350(L_46, L_47, List_1_RemoveAt_mD499A88E9E091C1605D6AD025613729B87B11350_RuntimeMethod_var);
	}

IL_00e6:
	{
		int32_t L_48 = V_4;
		V_4 = ((int32_t)il2cpp_codegen_subtract(L_48, 1));
	}

IL_00ec:
	{
		int32_t L_49 = V_4;
		if ((((int32_t)L_49) >= ((int32_t)0)))
		{
			goto IL_009a;
		}
	}
	{
		List_1_t6133AE9445F833C234B275E156F970487A5C1A4A* L_50 = V_1;
		NullCheck(L_50);
		int32_t L_51;
		L_51 = List_1_get_Count_mA4EB8F9036B4BE1B5D1E9F6F0617B8CEAACBBE34_inline(L_50, List_1_get_Count_mA4EB8F9036B4BE1B5D1E9F6F0617B8CEAACBBE34_RuntimeMethod_var);
		if ((((int32_t)L_51) > ((int32_t)0)))
		{
			goto IL_0106;
		}
	}
	{
		il2cpp_codegen_runtime_class_init_inline(CallbackManager_t19FF0BF336ADE5A782EFCB9C894B3A9351431233_il2cpp_TypeInfo_var);
		Dictionary_2_t710C8281BB25AB47EA84E7D852BE0E831EB82C31* L_52 = ((CallbackManager_t19FF0BF336ADE5A782EFCB9C894B3A9351431233_StaticFields*)il2cpp_codegen_static_fields_for(CallbackManager_t19FF0BF336ADE5A782EFCB9C894B3A9351431233_il2cpp_TypeInfo_var))->____callbacks;
		NullCheck(L_52);
		bool L_53;
		L_53 = Dictionary_2_Remove_m35C384BB7E7C99D8BB766D164AE52DC69FBC2612(L_52, 1, Dictionary_2_Remove_m35C384BB7E7C99D8BB766D164AE52DC69FBC2612_RuntimeMethod_var);
	}

IL_0106:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void CallbackManager_LateTick_m4A7494B7DE2B34EEA097692E72F94CA845A4A486 (float ___0_deltaTime, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&CallbackManager_t19FF0BF336ADE5A782EFCB9C894B3A9351431233_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		float L_0 = ___0_deltaTime;
		il2cpp_codegen_runtime_class_init_inline(CallbackManager_t19FF0BF336ADE5A782EFCB9C894B3A9351431233_il2cpp_TypeInfo_var);
		CallbackManager_Invoke_m8E23D655062E2C22BFADA57F555C57470E3D1B58(3, L_0, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void CallbackManager_Invoke_m8E23D655062E2C22BFADA57F555C57470E3D1B58 (int32_t ___0_type, float ___1_deltaTime, const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&CallbackManager_t19FF0BF336ADE5A782EFCB9C894B3A9351431233_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Dictionary_2_TryGetValue_m49E9CE1E830D15D25D10B8B5E4C989FB8A4D40B4_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_get_Count_mA4EB8F9036B4BE1B5D1E9F6F0617B8CEAACBBE34_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_get_Item_mCEFF728AFC5447D2EDB722AD83D034CEE0D75691_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&List_1_set_Item_m2A352F23425005A1161BBA095CB21917038EE6E0_RuntimeMethod_var);
		s_Il2CppMethodInitialized = true;
	}
	List_1_t6133AE9445F833C234B275E156F970487A5C1A4A* V_0 = NULL;
	int32_t V_1 = 0;
	CallbackInfo_t172261DACE580EF109EA5901886ED422A5849948 V_2;
	memset((&V_2), 0, sizeof(V_2));
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* G_B8_0 = NULL;
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* G_B7_0 = NULL;
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* G_B13_0 = NULL;
	Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* G_B12_0 = NULL;
	{
		il2cpp_codegen_runtime_class_init_inline(CallbackManager_t19FF0BF336ADE5A782EFCB9C894B3A9351431233_il2cpp_TypeInfo_var);
		Dictionary_2_t710C8281BB25AB47EA84E7D852BE0E831EB82C31* L_0 = ((CallbackManager_t19FF0BF336ADE5A782EFCB9C894B3A9351431233_StaticFields*)il2cpp_codegen_static_fields_for(CallbackManager_t19FF0BF336ADE5A782EFCB9C894B3A9351431233_il2cpp_TypeInfo_var))->____callbacks;
		int32_t L_1 = ___0_type;
		NullCheck(L_0);
		bool L_2;
		L_2 = Dictionary_2_TryGetValue_m49E9CE1E830D15D25D10B8B5E4C989FB8A4D40B4(L_0, L_1, (&V_0), Dictionary_2_TryGetValue_m49E9CE1E830D15D25D10B8B5E4C989FB8A4D40B4_RuntimeMethod_var);
		if (!L_2)
		{
			goto IL_00b7;
		}
	}
	{
		V_1 = 0;
		goto IL_00ab;
	}

IL_0019:
	{
		List_1_t6133AE9445F833C234B275E156F970487A5C1A4A* L_3 = V_0;
		int32_t L_4 = V_1;
		NullCheck(L_3);
		CallbackInfo_t172261DACE580EF109EA5901886ED422A5849948 L_5;
		L_5 = List_1_get_Item_mCEFF728AFC5447D2EDB722AD83D034CEE0D75691(L_3, L_4, List_1_get_Item_mCEFF728AFC5447D2EDB722AD83D034CEE0D75691_RuntimeMethod_var);
		V_2 = L_5;
		CallbackInfo_t172261DACE580EF109EA5901886ED422A5849948 L_6 = V_2;
		float L_7 = L_6.___DelayTime;
		if ((!(((float)L_7) > ((float)(0.0f)))))
		{
			goto IL_0048;
		}
	}
	{
		float* L_8 = (float*)(&(&V_2)->___DelayTime);
		float* L_9 = L_8;
		float L_10 = *((float*)L_9);
		float L_11;
		L_11 = Time_get_deltaTime_mC3195000401F0FD167DD2F948FD2BC58330D0865(NULL);
		*((float*)L_9) = (float)((float)il2cpp_codegen_subtract(L_10, L_11));
		List_1_t6133AE9445F833C234B275E156F970487A5C1A4A* L_12 = V_0;
		int32_t L_13 = V_1;
		CallbackInfo_t172261DACE580EF109EA5901886ED422A5849948 L_14 = V_2;
		NullCheck(L_12);
		List_1_set_Item_m2A352F23425005A1161BBA095CB21917038EE6E0(L_12, L_13, L_14, List_1_set_Item_m2A352F23425005A1161BBA095CB21917038EE6E0_RuntimeMethod_var);
		goto IL_00a7;
	}

IL_0048:
	{
		CallbackInfo_t172261DACE580EF109EA5901886ED422A5849948 L_15 = V_2;
		float L_16 = L_15.___Interval;
		if ((!(((float)L_16) > ((float)(0.0f)))))
		{
			goto IL_0096;
		}
	}
	{
		float* L_17 = (float*)(&(&V_2)->___LastTriggerTime);
		float* L_18 = L_17;
		float L_19 = *((float*)L_18);
		float L_20 = ___1_deltaTime;
		*((float*)L_18) = (float)((float)il2cpp_codegen_add(L_19, L_20));
		CallbackInfo_t172261DACE580EF109EA5901886ED422A5849948 L_21 = V_2;
		float L_22 = L_21.___LastTriggerTime;
		CallbackInfo_t172261DACE580EF109EA5901886ED422A5849948 L_23 = V_2;
		float L_24 = L_23.___Interval;
		if ((!(((float)L_22) >= ((float)L_24))))
		{
			goto IL_008c;
		}
	}
	{
		CallbackInfo_t172261DACE580EF109EA5901886ED422A5849948 L_25 = V_2;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_26 = L_25.___Callback;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_27 = L_26;
		if (L_27)
		{
			G_B8_0 = L_27;
			goto IL_007b;
		}
		G_B7_0 = L_27;
	}
	{
		goto IL_0080;
	}

IL_007b:
	{
		NullCheck(G_B8_0);
		Action_Invoke_m7126A54DACA72B845424072887B5F3A51FC3808E_inline(G_B8_0, NULL);
	}

IL_0080:
	{
		(&V_2)->___LastTriggerTime = (0.0f);
	}

IL_008c:
	{
		List_1_t6133AE9445F833C234B275E156F970487A5C1A4A* L_28 = V_0;
		int32_t L_29 = V_1;
		CallbackInfo_t172261DACE580EF109EA5901886ED422A5849948 L_30 = V_2;
		NullCheck(L_28);
		List_1_set_Item_m2A352F23425005A1161BBA095CB21917038EE6E0(L_28, L_29, L_30, List_1_set_Item_m2A352F23425005A1161BBA095CB21917038EE6E0_RuntimeMethod_var);
		goto IL_00a7;
	}

IL_0096:
	{
		CallbackInfo_t172261DACE580EF109EA5901886ED422A5849948 L_31 = V_2;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_32 = L_31.___Callback;
		Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* L_33 = L_32;
		if (L_33)
		{
			G_B13_0 = L_33;
			goto IL_00a2;
		}
		G_B12_0 = L_33;
	}
	{
		goto IL_00a7;
	}

IL_00a2:
	{
		NullCheck(G_B13_0);
		Action_Invoke_m7126A54DACA72B845424072887B5F3A51FC3808E_inline(G_B13_0, NULL);
	}

IL_00a7:
	{
		int32_t L_34 = V_1;
		V_1 = ((int32_t)il2cpp_codegen_add(L_34, 1));
	}

IL_00ab:
	{
		int32_t L_35 = V_1;
		List_1_t6133AE9445F833C234B275E156F970487A5C1A4A* L_36 = V_0;
		NullCheck(L_36);
		int32_t L_37;
		L_37 = List_1_get_Count_mA4EB8F9036B4BE1B5D1E9F6F0617B8CEAACBBE34_inline(L_36, List_1_get_Count_mA4EB8F9036B4BE1B5D1E9F6F0617B8CEAACBBE34_RuntimeMethod_var);
		if ((((int32_t)L_35) < ((int32_t)L_37)))
		{
			goto IL_0019;
		}
	}

IL_00b7:
	{
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void CallbackManager__ctor_m11AEDA56FDCDB6D3D53C7E6E336A27E4A373C10B (CallbackManager_t19FF0BF336ADE5A782EFCB9C894B3A9351431233* __this, const RuntimeMethod* method) 
{
	{
		Object__ctor_mE837C6B9FA8C6D5D109F4B2EC885D79919AC0EA2(__this, NULL);
		return;
	}
}
IL2CPP_EXTERN_C IL2CPP_METHOD_ATTR void CallbackManager__cctor_m01BB5757690E8A266451D9CB2DB36220D0ED6D43 (const RuntimeMethod* method) 
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&CallbackManager_t19FF0BF336ADE5A782EFCB9C894B3A9351431233_il2cpp_TypeInfo_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Dictionary_2__ctor_m0E132E62D74A767FE4EDA3BF1987D11DA6557D65_RuntimeMethod_var);
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Dictionary_2_t710C8281BB25AB47EA84E7D852BE0E831EB82C31_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	{
		((CallbackManager_t19FF0BF336ADE5A782EFCB9C894B3A9351431233_StaticFields*)il2cpp_codegen_static_fields_for(CallbackManager_t19FF0BF336ADE5A782EFCB9C894B3A9351431233_il2cpp_TypeInfo_var))->____idCounter = 1;
		Dictionary_2_t710C8281BB25AB47EA84E7D852BE0E831EB82C31* L_0 = (Dictionary_2_t710C8281BB25AB47EA84E7D852BE0E831EB82C31*)il2cpp_codegen_object_new(Dictionary_2_t710C8281BB25AB47EA84E7D852BE0E831EB82C31_il2cpp_TypeInfo_var);
		Dictionary_2__ctor_m0E132E62D74A767FE4EDA3BF1987D11DA6557D65(L_0, Dictionary_2__ctor_m0E132E62D74A767FE4EDA3BF1987D11DA6557D65_RuntimeMethod_var);
		((CallbackManager_t19FF0BF336ADE5A782EFCB9C894B3A9351431233_StaticFields*)il2cpp_codegen_static_fields_for(CallbackManager_t19FF0BF336ADE5A782EFCB9C894B3A9351431233_il2cpp_TypeInfo_var))->____callbacks = L_0;
		Il2CppCodeGenWriteBarrier((void**)(&((CallbackManager_t19FF0BF336ADE5A782EFCB9C894B3A9351431233_StaticFields*)il2cpp_codegen_static_fields_for(CallbackManager_t19FF0BF336ADE5A782EFCB9C894B3A9351431233_il2cpp_TypeInfo_var))->____callbacks), (void*)L_0);
		return;
	}
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
IL2CPP_EXTERN_C void CallbackInfo_t172261DACE580EF109EA5901886ED422A5849948_marshal_pinvoke(const CallbackInfo_t172261DACE580EF109EA5901886ED422A5849948& unmarshaled, CallbackInfo_t172261DACE580EF109EA5901886ED422A5849948_marshaled_pinvoke& marshaled)
{
	marshaled.___Id = unmarshaled.___Id;
	marshaled.___Type = unmarshaled.___Type;
	marshaled.___DelayTime = unmarshaled.___DelayTime;
	marshaled.___Interval = unmarshaled.___Interval;
	marshaled.___LastTriggerTime = unmarshaled.___LastTriggerTime;
	marshaled.___Callback = il2cpp_codegen_marshal_delegate(reinterpret_cast<MulticastDelegate_t*>(unmarshaled.___Callback));
}
IL2CPP_EXTERN_C void CallbackInfo_t172261DACE580EF109EA5901886ED422A5849948_marshal_pinvoke_back(const CallbackInfo_t172261DACE580EF109EA5901886ED422A5849948_marshaled_pinvoke& marshaled, CallbackInfo_t172261DACE580EF109EA5901886ED422A5849948& unmarshaled)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	uint32_t unmarshaledId_temp_0 = 0;
	unmarshaledId_temp_0 = marshaled.___Id;
	unmarshaled.___Id = unmarshaledId_temp_0;
	int32_t unmarshaledType_temp_1 = 0;
	unmarshaledType_temp_1 = marshaled.___Type;
	unmarshaled.___Type = unmarshaledType_temp_1;
	float unmarshaledDelayTime_temp_2 = 0.0f;
	unmarshaledDelayTime_temp_2 = marshaled.___DelayTime;
	unmarshaled.___DelayTime = unmarshaledDelayTime_temp_2;
	float unmarshaledInterval_temp_3 = 0.0f;
	unmarshaledInterval_temp_3 = marshaled.___Interval;
	unmarshaled.___Interval = unmarshaledInterval_temp_3;
	float unmarshaledLastTriggerTime_temp_4 = 0.0f;
	unmarshaledLastTriggerTime_temp_4 = marshaled.___LastTriggerTime;
	unmarshaled.___LastTriggerTime = unmarshaledLastTriggerTime_temp_4;
	unmarshaled.___Callback = il2cpp_codegen_marshal_function_ptr_to_delegate<Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07>(marshaled.___Callback, Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
	Il2CppCodeGenWriteBarrier((void**)(&unmarshaled.___Callback), (void*)il2cpp_codegen_marshal_function_ptr_to_delegate<Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07>(marshaled.___Callback, Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var));
}
IL2CPP_EXTERN_C void CallbackInfo_t172261DACE580EF109EA5901886ED422A5849948_marshal_pinvoke_cleanup(CallbackInfo_t172261DACE580EF109EA5901886ED422A5849948_marshaled_pinvoke& marshaled)
{
}
IL2CPP_EXTERN_C void CallbackInfo_t172261DACE580EF109EA5901886ED422A5849948_marshal_com(const CallbackInfo_t172261DACE580EF109EA5901886ED422A5849948& unmarshaled, CallbackInfo_t172261DACE580EF109EA5901886ED422A5849948_marshaled_com& marshaled)
{
	marshaled.___Id = unmarshaled.___Id;
	marshaled.___Type = unmarshaled.___Type;
	marshaled.___DelayTime = unmarshaled.___DelayTime;
	marshaled.___Interval = unmarshaled.___Interval;
	marshaled.___LastTriggerTime = unmarshaled.___LastTriggerTime;
	marshaled.___Callback = il2cpp_codegen_marshal_delegate(reinterpret_cast<MulticastDelegate_t*>(unmarshaled.___Callback));
}
IL2CPP_EXTERN_C void CallbackInfo_t172261DACE580EF109EA5901886ED422A5849948_marshal_com_back(const CallbackInfo_t172261DACE580EF109EA5901886ED422A5849948_marshaled_com& marshaled, CallbackInfo_t172261DACE580EF109EA5901886ED422A5849948& unmarshaled)
{
	static bool s_Il2CppMethodInitialized;
	if (!s_Il2CppMethodInitialized)
	{
		il2cpp_codegen_initialize_runtime_metadata((uintptr_t*)&Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
		s_Il2CppMethodInitialized = true;
	}
	uint32_t unmarshaledId_temp_0 = 0;
	unmarshaledId_temp_0 = marshaled.___Id;
	unmarshaled.___Id = unmarshaledId_temp_0;
	int32_t unmarshaledType_temp_1 = 0;
	unmarshaledType_temp_1 = marshaled.___Type;
	unmarshaled.___Type = unmarshaledType_temp_1;
	float unmarshaledDelayTime_temp_2 = 0.0f;
	unmarshaledDelayTime_temp_2 = marshaled.___DelayTime;
	unmarshaled.___DelayTime = unmarshaledDelayTime_temp_2;
	float unmarshaledInterval_temp_3 = 0.0f;
	unmarshaledInterval_temp_3 = marshaled.___Interval;
	unmarshaled.___Interval = unmarshaledInterval_temp_3;
	float unmarshaledLastTriggerTime_temp_4 = 0.0f;
	unmarshaledLastTriggerTime_temp_4 = marshaled.___LastTriggerTime;
	unmarshaled.___LastTriggerTime = unmarshaledLastTriggerTime_temp_4;
	unmarshaled.___Callback = il2cpp_codegen_marshal_function_ptr_to_delegate<Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07>(marshaled.___Callback, Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var);
	Il2CppCodeGenWriteBarrier((void**)(&unmarshaled.___Callback), (void*)il2cpp_codegen_marshal_function_ptr_to_delegate<Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07>(marshaled.___Callback, Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07_il2cpp_TypeInfo_var));
}
IL2CPP_EXTERN_C void CallbackInfo_t172261DACE580EF109EA5901886ED422A5849948_marshal_com_cleanup(CallbackInfo_t172261DACE580EF109EA5901886ED422A5849948_marshaled_com& marshaled)
{
}
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
#ifdef __clang__
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Winvalid-offsetof"
#pragma clang diagnostic ignored "-Wunused-variable"
#endif
#ifdef __clang__
#pragma clang diagnostic pop
#endif
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void Action_Invoke_m7126A54DACA72B845424072887B5F3A51FC3808E_inline (Action_tD00B0A84D7945E50C2DFFC28EFEE6ED44ED2AD07* __this, const RuntimeMethod* method) 
{
	typedef void (*FunctionPointerType) (RuntimeObject*, const RuntimeMethod*);
	((FunctionPointerType)__this->___invoke_impl)((Il2CppObject*)__this->___method_code, reinterpret_cast<RuntimeMethod*>(__this->___method));
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void List_1_Add_mBDD7C010CDF02D1D1DCDE763219EECF6B674B31B_gshared_inline (List_1_t6133AE9445F833C234B275E156F970487A5C1A4A* __this, CallbackInfo_t172261DACE580EF109EA5901886ED422A5849948 ___0_item, const RuntimeMethod* method) 
{
	CallbackInfoU5BU5D_t5EC13989002A3E3C9F374CED44CDC69E602C461E* V_0 = NULL;
	int32_t V_1 = 0;
	{
		int32_t L_0 = __this->____version;
		__this->____version = ((int32_t)il2cpp_codegen_add(L_0, 1));
		CallbackInfoU5BU5D_t5EC13989002A3E3C9F374CED44CDC69E602C461E* L_1 = __this->____items;
		V_0 = L_1;
		int32_t L_2 = __this->____size;
		V_1 = L_2;
		int32_t L_3 = V_1;
		CallbackInfoU5BU5D_t5EC13989002A3E3C9F374CED44CDC69E602C461E* L_4 = V_0;
		NullCheck(L_4);
		if ((!(((uint32_t)L_3) < ((uint32_t)((int32_t)(((RuntimeArray*)L_4)->max_length))))))
		{
			goto IL_0034;
		}
	}
	{
		int32_t L_5 = V_1;
		__this->____size = ((int32_t)il2cpp_codegen_add(L_5, 1));
		CallbackInfoU5BU5D_t5EC13989002A3E3C9F374CED44CDC69E602C461E* L_6 = V_0;
		int32_t L_7 = V_1;
		CallbackInfo_t172261DACE580EF109EA5901886ED422A5849948 L_8 = ___0_item;
		NullCheck(L_6);
		(L_6)->SetAt(static_cast<il2cpp_array_size_t>(L_7), (CallbackInfo_t172261DACE580EF109EA5901886ED422A5849948)L_8);
		return;
	}

IL_0034:
	{
		CallbackInfo_t172261DACE580EF109EA5901886ED422A5849948 L_9 = ___0_item;
		List_1_AddWithResize_m0FA6BEE13AB653D361F2E0EE8FC01A73F2E519B2(__this, L_9, il2cpp_rgctx_method(method->klass->rgctx_data, 14));
		return;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR int32_t List_1_get_Count_mA4EB8F9036B4BE1B5D1E9F6F0617B8CEAACBBE34_gshared_inline (List_1_t6133AE9445F833C234B275E156F970487A5C1A4A* __this, const RuntimeMethod* method) 
{
	{
		int32_t L_0 = __this->____size;
		return L_0;
	}
}
IL2CPP_MANAGED_FORCE_INLINE IL2CPP_METHOD_ATTR void List_1_Clear_mB9FD8653ACB072F2B37239AA109F9FCEEF552D1F_gshared_inline (List_1_t6133AE9445F833C234B275E156F970487A5C1A4A* __this, const RuntimeMethod* method) 
{
	int32_t V_0 = 0;
	{
		int32_t L_0 = __this->____version;
		__this->____version = ((int32_t)il2cpp_codegen_add(L_0, 1));
	}
	{
		int32_t L_1 = __this->____size;
		V_0 = L_1;
		__this->____size = 0;
		int32_t L_2 = V_0;
		if ((((int32_t)L_2) <= ((int32_t)0)))
		{
			goto IL_003c;
		}
	}
	{
		CallbackInfoU5BU5D_t5EC13989002A3E3C9F374CED44CDC69E602C461E* L_3 = __this->____items;
		int32_t L_4 = V_0;
		Array_Clear_m50BAA3751899858B097D3FF2ED31F284703FE5CB((RuntimeArray*)L_3, 0, L_4, NULL);
		return;
	}

IL_003c:
	{
		return;
	}
}

﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void ConfigurationElement_get_Properties_m85E584B7C5EAFA411191A245AF41DEC274DE8F93 (void);
extern void ConfigurationElement_IsModified_m03570122B9C781EE3AFC094BDDEA82F56BE2F850 (void);
extern void ConfigurationElement_Reset_mA1EA05A353D2606B81CF9B50BDBC9D5F9B6DF8AF (void);
extern void ConfigurationElement_ResetModified_m1CCB91632C7E81454C9E3A7F259AD72C06BED4B7 (void);
extern void ConfigurationSection_DeserializeSection_m337F6D10C212ACA2900FCEFC8098393D7776A0CD (void);
extern void ConfigurationSection_IsModified_m65E5503E4AB960336F17AF49AD94FDCA63EC7DD0 (void);
extern void ConfigurationSection_ResetModified_m3A4EF275904DF31400B33FD9C4F22537D2922844 (void);
extern void ConfigurationSection_SerializeSection_m4526B82EBA81F4B2A049AA668905A27C58A07540 (void);
extern void ConfigurationCollectionAttribute__ctor_m1C1204D379E75BB9D1AC794CAD78B0C95FDEDB8D (void);
extern void IgnoreSection__ctor_mDF97B44EFE0B08DF0D7E89F7B79553E010597066 (void);
extern void IgnoreSection_get_Properties_mE3DBA6242176B6E6438CEEBEB3A48319E9EFF133 (void);
extern void IgnoreSection_DeserializeSection_m622C6FAE1160DCC952A4E36FC9E2DCB9DCC34CEC (void);
extern void IgnoreSection_IsModified_mB1D57799DA9AE024B99CB05766D5497A3DD8F19F (void);
extern void IgnoreSection_Reset_m8A41B00CEC8C72D608FEE005D438864B5638B84E (void);
extern void IgnoreSection_ResetModified_m13E416D3841F85E3B334CF9EB517FFBE9F7E224C (void);
extern void IgnoreSection_SerializeSection_m12BD59834DBCACE13758DA83BD3DEF2B8A6F3DBE (void);
extern void ThrowStub_ThrowNotSupportedException_mA14F496FFE8A1B92C4565A9F18F2113E1C1F2A77 (void);
static Il2CppMethodPointer s_methodPointers[17] = 
{
	ConfigurationElement_get_Properties_m85E584B7C5EAFA411191A245AF41DEC274DE8F93,
	ConfigurationElement_IsModified_m03570122B9C781EE3AFC094BDDEA82F56BE2F850,
	ConfigurationElement_Reset_mA1EA05A353D2606B81CF9B50BDBC9D5F9B6DF8AF,
	ConfigurationElement_ResetModified_m1CCB91632C7E81454C9E3A7F259AD72C06BED4B7,
	ConfigurationSection_DeserializeSection_m337F6D10C212ACA2900FCEFC8098393D7776A0CD,
	ConfigurationSection_IsModified_m65E5503E4AB960336F17AF49AD94FDCA63EC7DD0,
	ConfigurationSection_ResetModified_m3A4EF275904DF31400B33FD9C4F22537D2922844,
	ConfigurationSection_SerializeSection_m4526B82EBA81F4B2A049AA668905A27C58A07540,
	ConfigurationCollectionAttribute__ctor_m1C1204D379E75BB9D1AC794CAD78B0C95FDEDB8D,
	IgnoreSection__ctor_mDF97B44EFE0B08DF0D7E89F7B79553E010597066,
	IgnoreSection_get_Properties_mE3DBA6242176B6E6438CEEBEB3A48319E9EFF133,
	IgnoreSection_DeserializeSection_m622C6FAE1160DCC952A4E36FC9E2DCB9DCC34CEC,
	IgnoreSection_IsModified_mB1D57799DA9AE024B99CB05766D5497A3DD8F19F,
	IgnoreSection_Reset_m8A41B00CEC8C72D608FEE005D438864B5638B84E,
	IgnoreSection_ResetModified_m13E416D3841F85E3B334CF9EB517FFBE9F7E224C,
	IgnoreSection_SerializeSection_m12BD59834DBCACE13758DA83BD3DEF2B8A6F3DBE,
	ThrowStub_ThrowNotSupportedException_mA14F496FFE8A1B92C4565A9F18F2113E1C1F2A77,
};
static const int32_t s_InvokerIndices[17] = 
{
	13052,
	12815,
	10682,
	13298,
	10682,
	12815,
	13298,
	2406,
	10682,
	13298,
	13052,
	10682,
	12815,
	10682,
	13298,
	2406,
	21355,
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_System_Configuration_CodeGenModule;
const Il2CppCodeGenModule g_System_Configuration_CodeGenModule = 
{
	"System.Configuration.dll",
	17,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};

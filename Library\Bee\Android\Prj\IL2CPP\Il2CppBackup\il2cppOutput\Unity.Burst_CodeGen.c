﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"


extern const RuntimeMethod* BurstCompilerHelper_IsBurstEnabled_m8F3C6D0129D14359B51860FBA51933C4FE92F131_RuntimeMethod_var;



extern void BurstCompileAttribute_set_FloatMode_mFC4C13A636FAF57510757D42EA1017C1A3FA6580 (void);
extern void BurstCompileAttribute_set_FloatPrecision_m62685CD0A4F258FB8779A37BC01DAABB83DFD465 (void);
extern void BurstCompileAttribute_set_CompileSynchronously_mED8B25B60ABC1EA5327DE691DCE129C9BD34BD58 (void);
extern void BurstCompileAttribute_set_DisableSafetyChecks_m69160424C71631B9E3A7C2334B64527E91662A9E (void);
extern void BurstCompileAttribute_set_OptimizeFor_mE87B58F69F2AAB69DF48FBC70B4D7348BCCDEE28 (void);
extern void BurstCompileAttribute_set_Options_mEC956014E83B4671F431159FE7D1DDD6BE5BC91E (void);
extern void BurstCompileAttribute__ctor_mFCB7FEAFCE1A2CE6A5268A4EA062E33E5472ABBE (void);
extern void BurstCompileAttribute__ctor_mBA3EAC7C435927F67F10F2D185F5CBE8F88DC3C6 (void);
extern void BurstCompiler_get_IsEnabled_m55FDBCB2279A83AC8926260034F870E3A11116C7 (void);
extern void BurstCompiler_CompileILPPMethod2_m545A8FC57B460871C1715F32DD601F2C1CA9C7FA (void);
extern void BurstCompiler_GetILPPMethodFunctionPointer2_m8C671F61D031A10FC46911AC94B57C1E58D1F567 (void);
extern void BurstCompiler_Compile_m32F70B1E6E7AE85F0AB1FC51B153598DEAE5F462 (void);
extern void BurstCompiler_Compile_m0038D8F2B6CB3915CB12F71E15B14C7355BFC8EF (void);
extern void BurstCompiler_DummyMethod_m44E6D413356022A0F6BA962A31026BA4EE5FE95F (void);
extern void BurstCompiler__cctor_mA29CF2918E31D89BB314B5CC1AF842BE93E9EE6B (void);
extern void StaticTypeReinitAttribute__ctor_m8642643889129E11741654F66EE77046A2A7D1CB (void);
extern void BurstCompilerHelper_IsBurstEnabled_m8F3C6D0129D14359B51860FBA51933C4FE92F131 (void);
extern void BurstCompilerHelper_DiscardedMethod_mE9B27FDCAB7B17C7B7496ADACFDBB72E3F155F6B (void);
extern void BurstCompilerHelper_IsCompiledByBurst_m0239AE7BCAF7076EE75C46D528F04AC34F3761DD (void);
extern void BurstCompilerHelper__cctor_m2B57C7C8A7B5F4CEE1E1DE05C5FC63C10AE37FD3 (void);
extern void IsBurstEnabledDelegate__ctor_m675CBAB9E803A7723AB3601DEB086E706E98A86E (void);
extern void IsBurstEnabledDelegate_Invoke_m9FA44E7FDC323DE7DF1232200DED8C89A67D7F65 (void);
extern void FakeDelegate__ctor_mC2654CB88A21F64F4C25E02A0C89854E2F74484B (void);
extern void FakeDelegate_get_Method_m36F3C3DAC1377B07AF18BAC6EDF28F3FAE5BA828 (void);
extern void U3CU3Ec__cctor_m8FF612FA8632F867C2CA577D7FF7A080320568BF (void);
extern void U3CU3Ec__ctor_mEC9179CC84E1FA4BB4AB4B39A87C134F481976C9 (void);
extern void U3CU3Ec_U3CCompileU3Eb__22_0_m2326454433F78E8E68A7EB9191933F393BDB0401 (void);
extern void BurstCompilerOptions__ctor_m644EA41CAFD4F89CE36074DBD77BAC761C122285 (void);
extern void BurstCompilerOptions_get_IsGlobal_m8500610C2E650CFE58411EAD12DEE4F5F49C30B4 (void);
extern void BurstCompilerOptions_get_EnableBurstCompilation_mE10DF1EAAF0A56906D9784498FD48EAC1B012CD0 (void);
extern void BurstCompilerOptions_set_EnableBurstCompilation_mEDD4E93926B3E03A8E38CA9D483D4E4FD649D849 (void);
extern void BurstCompilerOptions_set_EnableBurstSafetyChecks_m2AB857BC80AE1546031305C47F88ADB147A8BB83 (void);
extern void BurstCompilerOptions_get_OptionsChanged_m073BEAA6F0BA3EA5F42853CE2BB33681D6274C69 (void);
extern void BurstCompilerOptions_TryGetAttribute_m4EE3F62FAF2A482C444060E1CCB480711CC377F8 (void);
extern void BurstCompilerOptions_GetBurstCompileAttribute_m9383E7E419C41B6BD078452FC1E2EF94A9AD2972 (void);
extern void BurstCompilerOptions_HasBurstCompileAttribute_mC68CA53F4A77780A30D34E895B120188F31F7826 (void);
extern void BurstCompilerOptions_OnOptionsChanged_m20C25705A1D7B2A9C6265D3D5FE2A10A42AAABB7 (void);
extern void BurstCompilerOptions_MaybeTriggerRecompilation_mBE68BE4083665B2DE194184223A6BF14CA7F3821 (void);
extern void BurstCompilerOptions__cctor_m00F05309A6D0721099EBAF2FB553AD1A409815F6 (void);
extern void BurstCompilerOptions_CheckIsSecondaryUnityProcess_mAA7A85682C937E5FF55B6B9ADCF0F1789F889E67 (void);
extern void BurstRuntime_HashStringWithFNV1A64_mCC26696CC5168AE7CA59EAA4BD15440F434CB7AD (void);
extern void BurstRuntime_RuntimeLog_m01D9192CF1CE1F0113F51431413D5F002C82E12A (void);
extern void BurstRuntime_PreventRequiredAttributeStrip_mEB29E8C73D86AC18C902D6CA4B85C9D1DC0DB540 (void);
extern void BurstRuntime_Log_mACD9C0A258B393532ED8AE9DB127D494C14D0E88 (void);
extern void PreserveAttribute__ctor_m73E16FAB2119900D63EE60E6A868357D44E175F5 (void);
extern void BurstString_CopyFixedString_m5C7937A0D221B27A3D5FE9C1021B2210A7E72A16 (void);
extern void BurstString_Format_m5B430D57A65E74E0921325EC12E2920FACE2B684 (void);
extern void BurstString_Format_mEC129A0C1267C5438D13D9B8DA5BE80C9C6D3B8B (void);
extern void BurstString_Format_m77916B0A75CB28DF9F0BD6F32290D31BB24C7D4C (void);
extern void BurstString_Format_m8BCCCB4132CE427768D9A118E49B3F1F6C222102 (void);
extern void BurstString_Format_m19E81CEC5B4BA84C250AE5BAADC37D414E736730 (void);
extern void BurstString_Format_mF3FC2B176298B24C25FBF6DA92E700174C318000 (void);
extern void BurstString_Format_m234EB67007839F6D88BD31306502FB35A9F06FE1 (void);
extern void BurstString_Format_m69268960549C3B448843D0EB215B43DE6BFB75CE (void);
extern void BurstString_Format_m206A288B53D79DF5ACDD39B3F3A9A79AC1CF3844 (void);
extern void BurstString_Format_m23EDDB41EF95146DB17FED537050D7AC3A6901B6 (void);
extern void BurstString_Format_m4F5213B5469A6BDEEAB4B678F771A6F32CB952E7 (void);
extern void BurstString_Format_m2B7D17E527F80FA75BBE1D5B8C58C3B929B6664D (void);
extern void BurstString_Format_mEB0F69187C05D4543A5FF23A4E8E7A8DC27745A5 (void);
extern void BurstString_ConvertUnsignedIntegerToString_mE3D0034223E80A9185BE378CE7E0833972B1CA33 (void);
extern void BurstString_GetLengthIntegerToString_m7C848D6F1F8062C53DDBCF15BC3C48492B1D6772 (void);
extern void BurstString_ConvertIntegerToString_mA7D50BDF32DDABA6FC2C6CB1E5FF995C80A1C7F8 (void);
extern void BurstString_FormatNumber_m84AA91726082A3F72562B6B579F3D030D6D3C673 (void);
extern void BurstString_FormatDecimalOrHexadecimal_mA06BC7EC5DFAC150C462EBDD98CC067917E468AF (void);
extern void BurstString_ValueToIntegerChar_mC277F5B4A56CD3A028AB49004C97B878D2AE1313 (void);
extern void BurstString_AlignRight_m42725CF76779C09A0664D895DA590CEB4E1A8A37 (void);
extern void BurstString_AlignLeft_mE09478055A126F1675FF9C15B6572186785585D0 (void);
extern void BurstString_GetLengthForFormatGeneral_m8C803B634ACAA22001B49BEFBB5AB9CE7BD69766 (void);
extern void BurstString_FormatGeneral_m6BD2A28E369BBBF4444ED8D8A71EA6641A7DBCD1 (void);
extern void BurstString_RoundNumber_m524D9772E74FA38A0C43453F17AB2C7BAAB004E4 (void);
extern void BurstString_ShouldRoundUp_m409E5BBC77EF196F3CCAC9B2AFF01225E2119464 (void);
extern void BurstString_LogBase2_m034E17C8FE477EA2D6D3DDBCDAE5155EE0188F54 (void);
extern void BurstString_BigInt_Compare_m6815CCBF0899BF17AC14F259C329C715EFB6EBA1 (void);
extern void BurstString_BigInt_Add_m4E1C5A27B4D6168D2967BF79174DA2A04A07669E (void);
extern void BurstString_BigInt_Add_internal_m6CF758D9927E3261E88334B90E80ECF6C20E6DEF (void);
extern void BurstString_BigInt_Multiply_m90F6D119D0DD397B1B0FB3C76EEE1126C6DFE8A9 (void);
extern void BurstString_BigInt_Multiply_internal_mEA2BBAA8C72283721474B5EF6F7BEBB426294CB3 (void);
extern void BurstString_BigInt_Multiply_m80C42811355207D0CD9E4E14BB916F0242D44FDF (void);
extern void BurstString_BigInt_Multiply2_m2C3E74572DBF8B4600AC3AB75B2CF00A6498105C (void);
extern void BurstString_BigInt_Multiply2_m45D9B179615B4A6BAD47C2EAE92AEDE7A2406252 (void);
extern void BurstString_BigInt_Multiply10_m82AC5B11EB311D603B1A70235E95CC83D39E701E (void);
extern void BurstString_g_PowerOf10_Big_mD308778BE6E3F6102AA2FEB7F8092DD82B7F6D43 (void);
extern void BurstString_BigInt_Pow10_mE53CE39D44AABA6924D6544F12E564EC2DCFE642 (void);
extern void BurstString_BigInt_MultiplyPow10_mA62F7C4D0BC220B200E0AF031CEA586C59E1EEBD (void);
extern void BurstString_BigInt_Pow2_m7D6C74FD7591BA82DFAD8CFAEB2DC0727427587A (void);
extern void BurstString_BigInt_DivideWithRemainder_MaxQuotient9_m88E9DEA846064D23C9C090B9626B66DB52A844E9 (void);
extern void BurstString_BigInt_ShiftLeft_m0B99AC393DDF011FAC8F453039F4240C8F2BB583 (void);
extern void BurstString_Dragon4_mCA09B197DEF9912F76B915FDC179A5EF9A1560EE (void);
extern void BurstString_FormatInfinityNaN_mD90B190A044F0940A2F7681A79124103BD177979 (void);
extern void BurstString_ConvertFloatToString_m31A31291376EE1C7AA2DFA26573312B25E0DDCDA (void);
extern void BurstString_ConvertDoubleToString_m5B4644F134166CA236077075A11108590892EDD0 (void);
extern void BurstString__cctor_m7DAF55C23F1F9D98FC9F78D057E3730166E28B78 (void);
extern void PreserveAttribute__ctor_mBA1653B32D31972033C043A55588458B03F262B1 (void);
extern void NumberBuffer__ctor_m1A0D288DFB6432947BB55759502F97BA25348141 (void);
extern void NumberBuffer_GetDigitsPointer_m877C617CEE264BE12DE38D7289D741ED39B99846 (void);
extern void FormatOptions__ctor_mCF1FCAD2F6EE383DC6A602CA1F82BD16852CC055 (void);
extern void FormatOptions_get_Uppercase_mE85E979D733EB67187AC1BCEB045508A0EF005C3 (void);
extern void FormatOptions_GetBase_m0466B18B4E020F258E2402BE194FB8D670B2C789 (void);
extern void FormatOptions_ToString_m96B89E42F1553D5D3B78D7238443ACC628EFB488 (void);
extern void tBigInt_GetLength_m223AD69D6DB118C879FC58EF544D50C4A2E978E7 (void);
extern void tBigInt_GetBlock_m6E4E377A7A4591B136D20D711B06CB1D145FC9D2 (void);
extern void tBigInt_IsZero_mE0C94B9A59A09BFCE51C418F4C8C05EC253D68C8 (void);
extern void tBigInt_SetU64_m72EE55FD3169036C517DAF7392CE0A133DD50C14 (void);
extern void tBigInt_SetU32_m9EF2E1018CDA89AED4F0FA625E91878BF1772233 (void);
extern void tFloatUnion32_IsNegative_m75BC8B54D468278FCBA4535D6118346B3C8F9388 (void);
extern void tFloatUnion32_GetExponent_m83ED8E199331F83BC7AE3E48DCCCA8E6212CA6A6 (void);
extern void tFloatUnion32_GetMantissa_mAB906EE8DD2E27CFB6D98FF99CC2D764FF44F0EF (void);
extern void tFloatUnion64_IsNegative_m5427680D1918AB7410EDC266B0524E42313F171B (void);
extern void tFloatUnion64_GetExponent_m85B0BB29969C376B7FF866A1793C1997645D1D60 (void);
extern void tFloatUnion64_GetMantissa_m6EAD50CE3D1BFDABD12A308F2FF83F586F61328C (void);
extern void SharedStatic_GetOrCreateSharedStaticInternal_m9850783202F2E2DCA43597CD97C129C683D6FEBD (void);
extern void PreserveAttribute__ctor_m5C7C403F74E9EAEB24409A43B4EB60B4A161AB0F (void);
extern void Common_umul128_m6BE762AD1B87296A151EDD918A0802E9FB0846B7 (void);
extern void v256__ctor_m267402531F6CFFB7F6B60508FF475D5DF65B20EE (void);
extern void v256__ctor_mA9B9CC971837A7F271235EFCFFEA5D12E2E67E4A (void);
extern void Avx_mm256_load_ps_mDEC29DE0AC8C7A62975D63B512D5FD825D83E749 (void);
extern void Avx_mm256_store_ps_mAA874350740C462A1059A066894E57E93D7B6697 (void);
extern void Avx_mm256_loadu_si256_m63575B1FA1C174A5D442A2F53E7A1708AC7E7F31 (void);
extern void Avx_mm256_storeu_si256_m52989726D29436BCF95D32D4319C6CBDF31D11D4 (void);
extern void Avx_mm256_set1_epi32_m326DB72B5F59FC760340BFD50C022F573F032D3C (void);
extern void Avx2_get_IsAvx2Supported_mDE53DA491B5B96753F6558B1DF3C5D5A9BB1ADB4 (void);
extern void Avx2_mm256_xor_si256_mDD46C306F796DE4284677678DB751CC4AD998614 (void);
extern void Avx2_mm256_add_epi64_m0312FD16FB80EFA4C0B72A8FBB32C2735285F9B8 (void);
extern void Avx2_mm256_mul_epu32_mBA8AE42AD7D5F226187ECD9FD132F6EC138C6512 (void);
extern void Avx2_mm256_slli_epi64_m81DB24BED37FB2D8CBDDC0B6B655F920642BB559 (void);
extern void Avx2_mm256_srli_epi64_m629ADF7A5EBCAC5A84A2CA773EE8F18FE66DDD58 (void);
extern void Avx2_mm256_shuffle_epi32_m226985CA8797C3192874F3822295A40BDEEA850B (void);
extern void Sse_SHUFFLE_m80B322C7F945F0225AFA5E2995108547DA36391E (void);
extern void Sse2_add_epi64_m7F48D1953DDBDBF38CA494BABE0A3390BA6C86BF (void);
extern void Sse2_mul_epu32_mCD9FF71C6DA28E454D1EBE3249DC4F9E99DC11DB (void);
extern void Sse2_slli_epi64_mB984CF9AA56B47FDD860A6C0D9DCC5CFEC420B4D (void);
extern void Sse2_srli_epi64_m2B154276738045C80B2C8857CFDB232487262CFD (void);
extern void Sse2_xor_si128_m54213FFE7B2D891507E00B3395DB3AC25820A8A9 (void);
extern void Sse2_shuffle_epi32_m1EA2B8A82D93417EA3B3789381D8117DC635F35F (void);
extern void AssumeRangeAttribute__ctor_m079EDF7E5EEC31A177E1B9825784AB0C8192ECE7 (void);
extern void Hint_Likely_m55D631F5AB972E280D08882560FBE559147FAF76 (void);
extern void Hint_Unlikely_m1EF9F222AAE0D7381F834FEB8150C13EBF7F49F2 (void);
static Il2CppMethodPointer s_methodPointers[147] = 
{
	BurstCompileAttribute_set_FloatMode_mFC4C13A636FAF57510757D42EA1017C1A3FA6580,
	BurstCompileAttribute_set_FloatPrecision_m62685CD0A4F258FB8779A37BC01DAABB83DFD465,
	BurstCompileAttribute_set_CompileSynchronously_mED8B25B60ABC1EA5327DE691DCE129C9BD34BD58,
	BurstCompileAttribute_set_DisableSafetyChecks_m69160424C71631B9E3A7C2334B64527E91662A9E,
	BurstCompileAttribute_set_OptimizeFor_mE87B58F69F2AAB69DF48FBC70B4D7348BCCDEE28,
	BurstCompileAttribute_set_Options_mEC956014E83B4671F431159FE7D1DDD6BE5BC91E,
	BurstCompileAttribute__ctor_mFCB7FEAFCE1A2CE6A5268A4EA062E33E5472ABBE,
	BurstCompileAttribute__ctor_mBA3EAC7C435927F67F10F2D185F5CBE8F88DC3C6,
	BurstCompiler_get_IsEnabled_m55FDBCB2279A83AC8926260034F870E3A11116C7,
	BurstCompiler_CompileILPPMethod2_m545A8FC57B460871C1715F32DD601F2C1CA9C7FA,
	BurstCompiler_GetILPPMethodFunctionPointer2_m8C671F61D031A10FC46911AC94B57C1E58D1F567,
	NULL,
	BurstCompiler_Compile_m32F70B1E6E7AE85F0AB1FC51B153598DEAE5F462,
	BurstCompiler_Compile_m0038D8F2B6CB3915CB12F71E15B14C7355BFC8EF,
	BurstCompiler_DummyMethod_m44E6D413356022A0F6BA962A31026BA4EE5FE95F,
	BurstCompiler__cctor_mA29CF2918E31D89BB314B5CC1AF842BE93E9EE6B,
	StaticTypeReinitAttribute__ctor_m8642643889129E11741654F66EE77046A2A7D1CB,
	BurstCompilerHelper_IsBurstEnabled_m8F3C6D0129D14359B51860FBA51933C4FE92F131,
	BurstCompilerHelper_DiscardedMethod_mE9B27FDCAB7B17C7B7496ADACFDBB72E3F155F6B,
	BurstCompilerHelper_IsCompiledByBurst_m0239AE7BCAF7076EE75C46D528F04AC34F3761DD,
	BurstCompilerHelper__cctor_m2B57C7C8A7B5F4CEE1E1DE05C5FC63C10AE37FD3,
	IsBurstEnabledDelegate__ctor_m675CBAB9E803A7723AB3601DEB086E706E98A86E,
	IsBurstEnabledDelegate_Invoke_m9FA44E7FDC323DE7DF1232200DED8C89A67D7F65,
	FakeDelegate__ctor_mC2654CB88A21F64F4C25E02A0C89854E2F74484B,
	FakeDelegate_get_Method_m36F3C3DAC1377B07AF18BAC6EDF28F3FAE5BA828,
	U3CU3Ec__cctor_m8FF612FA8632F867C2CA577D7FF7A080320568BF,
	U3CU3Ec__ctor_mEC9179CC84E1FA4BB4AB4B39A87C134F481976C9,
	U3CU3Ec_U3CCompileU3Eb__22_0_m2326454433F78E8E68A7EB9191933F393BDB0401,
	BurstCompilerOptions__ctor_m644EA41CAFD4F89CE36074DBD77BAC761C122285,
	BurstCompilerOptions_get_IsGlobal_m8500610C2E650CFE58411EAD12DEE4F5F49C30B4,
	BurstCompilerOptions_get_EnableBurstCompilation_mE10DF1EAAF0A56906D9784498FD48EAC1B012CD0,
	BurstCompilerOptions_set_EnableBurstCompilation_mEDD4E93926B3E03A8E38CA9D483D4E4FD649D849,
	BurstCompilerOptions_set_EnableBurstSafetyChecks_m2AB857BC80AE1546031305C47F88ADB147A8BB83,
	BurstCompilerOptions_get_OptionsChanged_m073BEAA6F0BA3EA5F42853CE2BB33681D6274C69,
	BurstCompilerOptions_TryGetAttribute_m4EE3F62FAF2A482C444060E1CCB480711CC377F8,
	BurstCompilerOptions_GetBurstCompileAttribute_m9383E7E419C41B6BD078452FC1E2EF94A9AD2972,
	BurstCompilerOptions_HasBurstCompileAttribute_mC68CA53F4A77780A30D34E895B120188F31F7826,
	BurstCompilerOptions_OnOptionsChanged_m20C25705A1D7B2A9C6265D3D5FE2A10A42AAABB7,
	BurstCompilerOptions_MaybeTriggerRecompilation_mBE68BE4083665B2DE194184223A6BF14CA7F3821,
	BurstCompilerOptions__cctor_m00F05309A6D0721099EBAF2FB553AD1A409815F6,
	BurstCompilerOptions_CheckIsSecondaryUnityProcess_mAA7A85682C937E5FF55B6B9ADCF0F1789F889E67,
	NULL,
	BurstRuntime_HashStringWithFNV1A64_mCC26696CC5168AE7CA59EAA4BD15440F434CB7AD,
	BurstRuntime_RuntimeLog_m01D9192CF1CE1F0113F51431413D5F002C82E12A,
	BurstRuntime_PreventRequiredAttributeStrip_mEB29E8C73D86AC18C902D6CA4B85C9D1DC0DB540,
	BurstRuntime_Log_mACD9C0A258B393532ED8AE9DB127D494C14D0E88,
	NULL,
	PreserveAttribute__ctor_m73E16FAB2119900D63EE60E6A868357D44E175F5,
	BurstString_CopyFixedString_m5C7937A0D221B27A3D5FE9C1021B2210A7E72A16,
	BurstString_Format_m5B430D57A65E74E0921325EC12E2920FACE2B684,
	BurstString_Format_mEC129A0C1267C5438D13D9B8DA5BE80C9C6D3B8B,
	BurstString_Format_m77916B0A75CB28DF9F0BD6F32290D31BB24C7D4C,
	BurstString_Format_m8BCCCB4132CE427768D9A118E49B3F1F6C222102,
	BurstString_Format_m19E81CEC5B4BA84C250AE5BAADC37D414E736730,
	BurstString_Format_mF3FC2B176298B24C25FBF6DA92E700174C318000,
	BurstString_Format_m234EB67007839F6D88BD31306502FB35A9F06FE1,
	BurstString_Format_m69268960549C3B448843D0EB215B43DE6BFB75CE,
	BurstString_Format_m206A288B53D79DF5ACDD39B3F3A9A79AC1CF3844,
	BurstString_Format_m23EDDB41EF95146DB17FED537050D7AC3A6901B6,
	BurstString_Format_m4F5213B5469A6BDEEAB4B678F771A6F32CB952E7,
	BurstString_Format_m2B7D17E527F80FA75BBE1D5B8C58C3B929B6664D,
	BurstString_Format_mEB0F69187C05D4543A5FF23A4E8E7A8DC27745A5,
	BurstString_ConvertUnsignedIntegerToString_mE3D0034223E80A9185BE378CE7E0833972B1CA33,
	BurstString_GetLengthIntegerToString_m7C848D6F1F8062C53DDBCF15BC3C48492B1D6772,
	BurstString_ConvertIntegerToString_mA7D50BDF32DDABA6FC2C6CB1E5FF995C80A1C7F8,
	BurstString_FormatNumber_m84AA91726082A3F72562B6B579F3D030D6D3C673,
	BurstString_FormatDecimalOrHexadecimal_mA06BC7EC5DFAC150C462EBDD98CC067917E468AF,
	BurstString_ValueToIntegerChar_mC277F5B4A56CD3A028AB49004C97B878D2AE1313,
	BurstString_AlignRight_m42725CF76779C09A0664D895DA590CEB4E1A8A37,
	BurstString_AlignLeft_mE09478055A126F1675FF9C15B6572186785585D0,
	BurstString_GetLengthForFormatGeneral_m8C803B634ACAA22001B49BEFBB5AB9CE7BD69766,
	BurstString_FormatGeneral_m6BD2A28E369BBBF4444ED8D8A71EA6641A7DBCD1,
	BurstString_RoundNumber_m524D9772E74FA38A0C43453F17AB2C7BAAB004E4,
	BurstString_ShouldRoundUp_m409E5BBC77EF196F3CCAC9B2AFF01225E2119464,
	BurstString_LogBase2_m034E17C8FE477EA2D6D3DDBCDAE5155EE0188F54,
	BurstString_BigInt_Compare_m6815CCBF0899BF17AC14F259C329C715EFB6EBA1,
	BurstString_BigInt_Add_m4E1C5A27B4D6168D2967BF79174DA2A04A07669E,
	BurstString_BigInt_Add_internal_m6CF758D9927E3261E88334B90E80ECF6C20E6DEF,
	BurstString_BigInt_Multiply_m90F6D119D0DD397B1B0FB3C76EEE1126C6DFE8A9,
	BurstString_BigInt_Multiply_internal_mEA2BBAA8C72283721474B5EF6F7BEBB426294CB3,
	BurstString_BigInt_Multiply_m80C42811355207D0CD9E4E14BB916F0242D44FDF,
	BurstString_BigInt_Multiply2_m2C3E74572DBF8B4600AC3AB75B2CF00A6498105C,
	BurstString_BigInt_Multiply2_m45D9B179615B4A6BAD47C2EAE92AEDE7A2406252,
	BurstString_BigInt_Multiply10_m82AC5B11EB311D603B1A70235E95CC83D39E701E,
	BurstString_g_PowerOf10_Big_mD308778BE6E3F6102AA2FEB7F8092DD82B7F6D43,
	BurstString_BigInt_Pow10_mE53CE39D44AABA6924D6544F12E564EC2DCFE642,
	BurstString_BigInt_MultiplyPow10_mA62F7C4D0BC220B200E0AF031CEA586C59E1EEBD,
	BurstString_BigInt_Pow2_m7D6C74FD7591BA82DFAD8CFAEB2DC0727427587A,
	BurstString_BigInt_DivideWithRemainder_MaxQuotient9_m88E9DEA846064D23C9C090B9626B66DB52A844E9,
	BurstString_BigInt_ShiftLeft_m0B99AC393DDF011FAC8F453039F4240C8F2BB583,
	BurstString_Dragon4_mCA09B197DEF9912F76B915FDC179A5EF9A1560EE,
	BurstString_FormatInfinityNaN_mD90B190A044F0940A2F7681A79124103BD177979,
	BurstString_ConvertFloatToString_m31A31291376EE1C7AA2DFA26573312B25E0DDCDA,
	BurstString_ConvertDoubleToString_m5B4644F134166CA236077075A11108590892EDD0,
	BurstString__cctor_m7DAF55C23F1F9D98FC9F78D057E3730166E28B78,
	PreserveAttribute__ctor_mBA1653B32D31972033C043A55588458B03F262B1,
	NumberBuffer__ctor_m1A0D288DFB6432947BB55759502F97BA25348141,
	NumberBuffer_GetDigitsPointer_m877C617CEE264BE12DE38D7289D741ED39B99846,
	FormatOptions__ctor_mCF1FCAD2F6EE383DC6A602CA1F82BD16852CC055,
	FormatOptions_get_Uppercase_mE85E979D733EB67187AC1BCEB045508A0EF005C3,
	FormatOptions_GetBase_m0466B18B4E020F258E2402BE194FB8D670B2C789,
	FormatOptions_ToString_m96B89E42F1553D5D3B78D7238443ACC628EFB488,
	tBigInt_GetLength_m223AD69D6DB118C879FC58EF544D50C4A2E978E7,
	tBigInt_GetBlock_m6E4E377A7A4591B136D20D711B06CB1D145FC9D2,
	tBigInt_IsZero_mE0C94B9A59A09BFCE51C418F4C8C05EC253D68C8,
	tBigInt_SetU64_m72EE55FD3169036C517DAF7392CE0A133DD50C14,
	tBigInt_SetU32_m9EF2E1018CDA89AED4F0FA625E91878BF1772233,
	tFloatUnion32_IsNegative_m75BC8B54D468278FCBA4535D6118346B3C8F9388,
	tFloatUnion32_GetExponent_m83ED8E199331F83BC7AE3E48DCCCA8E6212CA6A6,
	tFloatUnion32_GetMantissa_mAB906EE8DD2E27CFB6D98FF99CC2D764FF44F0EF,
	tFloatUnion64_IsNegative_m5427680D1918AB7410EDC266B0524E42313F171B,
	tFloatUnion64_GetExponent_m85B0BB29969C376B7FF866A1793C1997645D1D60,
	tFloatUnion64_GetMantissa_m6EAD50CE3D1BFDABD12A308F2FF83F586F61328C,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	SharedStatic_GetOrCreateSharedStaticInternal_m9850783202F2E2DCA43597CD97C129C683D6FEBD,
	PreserveAttribute__ctor_m5C7C403F74E9EAEB24409A43B4EB60B4A161AB0F,
	Common_umul128_m6BE762AD1B87296A151EDD918A0802E9FB0846B7,
	v256__ctor_m267402531F6CFFB7F6B60508FF475D5DF65B20EE,
	v256__ctor_mA9B9CC971837A7F271235EFCFFEA5D12E2E67E4A,
	Avx_mm256_load_ps_mDEC29DE0AC8C7A62975D63B512D5FD825D83E749,
	Avx_mm256_store_ps_mAA874350740C462A1059A066894E57E93D7B6697,
	Avx_mm256_loadu_si256_m63575B1FA1C174A5D442A2F53E7A1708AC7E7F31,
	Avx_mm256_storeu_si256_m52989726D29436BCF95D32D4319C6CBDF31D11D4,
	Avx_mm256_set1_epi32_m326DB72B5F59FC760340BFD50C022F573F032D3C,
	Avx2_get_IsAvx2Supported_mDE53DA491B5B96753F6558B1DF3C5D5A9BB1ADB4,
	Avx2_mm256_xor_si256_mDD46C306F796DE4284677678DB751CC4AD998614,
	Avx2_mm256_add_epi64_m0312FD16FB80EFA4C0B72A8FBB32C2735285F9B8,
	Avx2_mm256_mul_epu32_mBA8AE42AD7D5F226187ECD9FD132F6EC138C6512,
	Avx2_mm256_slli_epi64_m81DB24BED37FB2D8CBDDC0B6B655F920642BB559,
	Avx2_mm256_srli_epi64_m629ADF7A5EBCAC5A84A2CA773EE8F18FE66DDD58,
	Avx2_mm256_shuffle_epi32_m226985CA8797C3192874F3822295A40BDEEA850B,
	Sse_SHUFFLE_m80B322C7F945F0225AFA5E2995108547DA36391E,
	Sse2_add_epi64_m7F48D1953DDBDBF38CA494BABE0A3390BA6C86BF,
	Sse2_mul_epu32_mCD9FF71C6DA28E454D1EBE3249DC4F9E99DC11DB,
	Sse2_slli_epi64_mB984CF9AA56B47FDD860A6C0D9DCC5CFEC420B4D,
	Sse2_srli_epi64_m2B154276738045C80B2C8857CFDB232487262CFD,
	Sse2_xor_si128_m54213FFE7B2D891507E00B3395DB3AC25820A8A9,
	Sse2_shuffle_epi32_m1EA2B8A82D93417EA3B3789381D8117DC635F35F,
	AssumeRangeAttribute__ctor_m079EDF7E5EEC31A177E1B9825784AB0C8192ECE7,
	Hint_Likely_m55D631F5AB972E280D08882560FBE559147FAF76,
	Hint_Unlikely_m1EF9F222AAE0D7381F834FEB8150C13EBF7F49F2,
};
extern void NumberBuffer__ctor_m1A0D288DFB6432947BB55759502F97BA25348141_AdjustorThunk (void);
extern void NumberBuffer_GetDigitsPointer_m877C617CEE264BE12DE38D7289D741ED39B99846_AdjustorThunk (void);
extern void FormatOptions__ctor_mCF1FCAD2F6EE383DC6A602CA1F82BD16852CC055_AdjustorThunk (void);
extern void FormatOptions_get_Uppercase_mE85E979D733EB67187AC1BCEB045508A0EF005C3_AdjustorThunk (void);
extern void FormatOptions_GetBase_m0466B18B4E020F258E2402BE194FB8D670B2C789_AdjustorThunk (void);
extern void FormatOptions_ToString_m96B89E42F1553D5D3B78D7238443ACC628EFB488_AdjustorThunk (void);
extern void tBigInt_GetLength_m223AD69D6DB118C879FC58EF544D50C4A2E978E7_AdjustorThunk (void);
extern void tBigInt_GetBlock_m6E4E377A7A4591B136D20D711B06CB1D145FC9D2_AdjustorThunk (void);
extern void tBigInt_IsZero_mE0C94B9A59A09BFCE51C418F4C8C05EC253D68C8_AdjustorThunk (void);
extern void tBigInt_SetU64_m72EE55FD3169036C517DAF7392CE0A133DD50C14_AdjustorThunk (void);
extern void tBigInt_SetU32_m9EF2E1018CDA89AED4F0FA625E91878BF1772233_AdjustorThunk (void);
extern void tFloatUnion32_IsNegative_m75BC8B54D468278FCBA4535D6118346B3C8F9388_AdjustorThunk (void);
extern void tFloatUnion32_GetExponent_m83ED8E199331F83BC7AE3E48DCCCA8E6212CA6A6_AdjustorThunk (void);
extern void tFloatUnion32_GetMantissa_mAB906EE8DD2E27CFB6D98FF99CC2D764FF44F0EF_AdjustorThunk (void);
extern void tFloatUnion64_IsNegative_m5427680D1918AB7410EDC266B0524E42313F171B_AdjustorThunk (void);
extern void tFloatUnion64_GetExponent_m85B0BB29969C376B7FF866A1793C1997645D1D60_AdjustorThunk (void);
extern void tFloatUnion64_GetMantissa_m6EAD50CE3D1BFDABD12A308F2FF83F586F61328C_AdjustorThunk (void);
extern void v256__ctor_m267402531F6CFFB7F6B60508FF475D5DF65B20EE_AdjustorThunk (void);
extern void v256__ctor_mA9B9CC971837A7F271235EFCFFEA5D12E2E67E4A_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[19] = 
{
	{ 0x06000061, NumberBuffer__ctor_m1A0D288DFB6432947BB55759502F97BA25348141_AdjustorThunk },
	{ 0x06000062, NumberBuffer_GetDigitsPointer_m877C617CEE264BE12DE38D7289D741ED39B99846_AdjustorThunk },
	{ 0x06000063, FormatOptions__ctor_mCF1FCAD2F6EE383DC6A602CA1F82BD16852CC055_AdjustorThunk },
	{ 0x06000064, FormatOptions_get_Uppercase_mE85E979D733EB67187AC1BCEB045508A0EF005C3_AdjustorThunk },
	{ 0x06000065, FormatOptions_GetBase_m0466B18B4E020F258E2402BE194FB8D670B2C789_AdjustorThunk },
	{ 0x06000066, FormatOptions_ToString_m96B89E42F1553D5D3B78D7238443ACC628EFB488_AdjustorThunk },
	{ 0x06000067, tBigInt_GetLength_m223AD69D6DB118C879FC58EF544D50C4A2E978E7_AdjustorThunk },
	{ 0x06000068, tBigInt_GetBlock_m6E4E377A7A4591B136D20D711B06CB1D145FC9D2_AdjustorThunk },
	{ 0x06000069, tBigInt_IsZero_mE0C94B9A59A09BFCE51C418F4C8C05EC253D68C8_AdjustorThunk },
	{ 0x0600006A, tBigInt_SetU64_m72EE55FD3169036C517DAF7392CE0A133DD50C14_AdjustorThunk },
	{ 0x0600006B, tBigInt_SetU32_m9EF2E1018CDA89AED4F0FA625E91878BF1772233_AdjustorThunk },
	{ 0x0600006C, tFloatUnion32_IsNegative_m75BC8B54D468278FCBA4535D6118346B3C8F9388_AdjustorThunk },
	{ 0x0600006D, tFloatUnion32_GetExponent_m83ED8E199331F83BC7AE3E48DCCCA8E6212CA6A6_AdjustorThunk },
	{ 0x0600006E, tFloatUnion32_GetMantissa_mAB906EE8DD2E27CFB6D98FF99CC2D764FF44F0EF_AdjustorThunk },
	{ 0x0600006F, tFloatUnion64_IsNegative_m5427680D1918AB7410EDC266B0524E42313F171B_AdjustorThunk },
	{ 0x06000070, tFloatUnion64_GetExponent_m85B0BB29969C376B7FF866A1793C1997645D1D60_AdjustorThunk },
	{ 0x06000071, tFloatUnion64_GetMantissa_m6EAD50CE3D1BFDABD12A308F2FF83F586F61328C_AdjustorThunk },
	{ 0x0600007C, v256__ctor_m267402531F6CFFB7F6B60508FF475D5DF65B20EE_AdjustorThunk },
	{ 0x0600007D, v256__ctor_mA9B9CC971837A7F271235EFCFFEA5D12E2E67E4A_AdjustorThunk },
};
static const int32_t s_InvokerIndices[147] = 
{
	10629,
	10629,
	10442,
	10442,
	10629,
	10682,
	13298,
	5266,
	21225,
	20256,
	15959,
	0,
	17111,
	14865,
	21355,
	21355,
	10682,
	21225,
	20831,
	19891,
	21355,
	5684,
	12815,
	10682,
	13052,
	21355,
	13298,
	7736,
	10442,
	12815,
	12815,
	10442,
	10442,
	13052,
	17470,
	20515,
	19891,
	13298,
	13298,
	21355,
	21225,
	0,
	20236,
	15551,
	21355,
	15551,
	0,
	13298,
	15551,
	13977,
	14670,
	14662,
	14661,
	14672,
	14661,
	14672,
	14673,
	14674,
	14669,
	14664,
	14665,
	14667,
	14675,
	16196,
	14668,
	13978,
	13976,
	17445,
	14065,
	14065,
	17812,
	13976,
	16728,
	15985,
	20748,
	17811,
	16710,
	16710,
	16710,
	16710,
	16717,
	18502,
	20831,
	20831,
	20922,
	18513,
	16717,
	18513,
	18221,
	18513,
	13619,
	13980,
	14671,
	14663,
	21355,
	13298,
	824,
	12792,
	1769,
	12815,
	12996,
	13052,
	12996,
	9562,
	12815,
	10892,
	10891,
	12815,
	13261,
	13261,
	12815,
	13261,
	13262,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	14864,
	13298,
	16540,
	10629,
	5870,
	20916,
	18518,
	20916,
	18518,
	20917,
	21225,
	18937,
	18937,
	18937,
	18936,
	18936,
	18936,
	15010,
	18935,
	18935,
	18934,
	18934,
	18935,
	18934,
	5624,
	19880,
	19880,
};
static const Il2CppTokenIndexMethodTuple s_reversePInvokeIndices[1] = 
{
	{ 0x06000012, 20,  (void**)&BurstCompilerHelper_IsBurstEnabled_m8F3C6D0129D14359B51860FBA51933C4FE92F131_RuntimeMethod_var, 0 },
};
static const Il2CppTokenRangePair s_rgctxIndices[6] = 
{
	{ 0x0200000E, { 5, 3 } },
	{ 0x0200001B, { 8, 3 } },
	{ 0x0200001C, { 11, 7 } },
	{ 0x0600000C, { 0, 3 } },
	{ 0x0600002A, { 3, 2 } },
	{ 0x06000077, { 18, 1 } },
};
extern const uint32_t g_rgctx_T_tC927C7E1A508BB0ED244CC4ADF544BDD5F1CB1D5;
extern const uint32_t g_rgctx_FunctionPointer_1_t9104E86B4429A44F4430F1344600A055BC17E03C;
extern const uint32_t g_rgctx_FunctionPointer_1__ctor_m64D42256EAD6BB2C3CA12D51523190DFCC247B9B;
extern const uint32_t g_rgctx_HashCode64_1_t9F53957FAF7149A07640A3C57FBB93D01811C2EB;
extern const uint32_t g_rgctx_HashCode64_1_t9F53957FAF7149A07640A3C57FBB93D01811C2EB;
extern const uint32_t g_rgctx_T_tDAC5992311902D10838C1F5E430629D6CD6629C5;
extern const uint32_t g_rgctx_HashCode64_1_t005181D745B5539B887DCED6AB90B7E8730829EF;
extern const uint32_t g_rgctx_HashCode64_1_t005181D745B5539B887DCED6AB90B7E8730829EF;
extern const uint32_t g_rgctx_FunctionPointer_1_t0666C00338C9DBCF4C31C1B1326ED43190DE0F38;
extern const uint32_t g_rgctx_Marshal_GetDelegateForFunctionPointer_TisT_t9E37FA2330E4A886B47120B954AAD7D9426B8783_mEA087B9A129C0AB2D73817CF23AC8B3121787C3C;
extern const uint32_t g_rgctx_T_t9E37FA2330E4A886B47120B954AAD7D9426B8783;
extern const uint32_t g_rgctx_SharedStatic_1_tB929B3357445BF112CFE0DA3DACBBEEAC8749C22;
extern const uint32_t g_rgctx_Unsafe_AsRef_TisT_t00138E42C19C859C1D3054928656D969BBE3897E_mEE7B8BA32C960B9A36668D1A55993B6596E7B11A;
extern const uint32_t g_rgctx_TU26_t2BAB852B77A3158AA79460B09AA152D26C15E11E;
extern const uint32_t g_rgctx_SharedStatic_1_GetOrCreateUnsafe_m3E0CD49DA78523EAF58ACBB3F2373AA0F84C3DB1;
extern const uint32_t g_rgctx_SharedStatic_1_tB929B3357445BF112CFE0DA3DACBBEEAC8749C22;
extern const uint32_t g_rgctx_UnsafeUtility_SizeOf_TisT_t00138E42C19C859C1D3054928656D969BBE3897E_m2A4C0FE147D438A6E478B7DE3EFD0FA088D18428;
extern const uint32_t g_rgctx_SharedStatic_1__ctor_m8C24499DF79560507F2AFDB01D8E2DFE40C5A86C;
extern const uint32_t g_rgctx_BurstRuntime_GetHashCode64_TisTContext_t590307E39F07A8D8C45F643B6DB8670A5D1D72F6_m9E3E6DD73DB690ACBF72F3E101A48259276D8010;
static const Il2CppRGCTXDefinition s_rgctxValues[19] = 
{
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tC927C7E1A508BB0ED244CC4ADF544BDD5F1CB1D5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FunctionPointer_1_t9104E86B4429A44F4430F1344600A055BC17E03C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_FunctionPointer_1__ctor_m64D42256EAD6BB2C3CA12D51523190DFCC247B9B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_HashCode64_1_t9F53957FAF7149A07640A3C57FBB93D01811C2EB },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_HashCode64_1_t9F53957FAF7149A07640A3C57FBB93D01811C2EB },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_T_tDAC5992311902D10838C1F5E430629D6CD6629C5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_HashCode64_1_t005181D745B5539B887DCED6AB90B7E8730829EF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_HashCode64_1_t005181D745B5539B887DCED6AB90B7E8730829EF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FunctionPointer_1_t0666C00338C9DBCF4C31C1B1326ED43190DE0F38 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Marshal_GetDelegateForFunctionPointer_TisT_t9E37FA2330E4A886B47120B954AAD7D9426B8783_mEA087B9A129C0AB2D73817CF23AC8B3121787C3C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t9E37FA2330E4A886B47120B954AAD7D9426B8783 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_SharedStatic_1_tB929B3357445BF112CFE0DA3DACBBEEAC8749C22 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Unsafe_AsRef_TisT_t00138E42C19C859C1D3054928656D969BBE3897E_mEE7B8BA32C960B9A36668D1A55993B6596E7B11A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_t2BAB852B77A3158AA79460B09AA152D26C15E11E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SharedStatic_1_GetOrCreateUnsafe_m3E0CD49DA78523EAF58ACBB3F2373AA0F84C3DB1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_SharedStatic_1_tB929B3357445BF112CFE0DA3DACBBEEAC8749C22 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_SizeOf_TisT_t00138E42C19C859C1D3054928656D969BBE3897E_m2A4C0FE147D438A6E478B7DE3EFD0FA088D18428 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_SharedStatic_1__ctor_m8C24499DF79560507F2AFDB01D8E2DFE40C5A86C },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_BurstRuntime_GetHashCode64_TisTContext_t590307E39F07A8D8C45F643B6DB8670A5D1D72F6_m9E3E6DD73DB690ACBF72F3E101A48259276D8010 },
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_Unity_Burst_CodeGenModule;
const Il2CppCodeGenModule g_Unity_Burst_CodeGenModule = 
{
	"Unity.Burst.dll",
	147,
	s_methodPointers,
	19,
	s_adjustorThunks,
	s_InvokerIndices,
	1,
	s_reversePInvokeIndices,
	6,
	s_rgctxIndices,
	19,
	s_rgctxValues,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};

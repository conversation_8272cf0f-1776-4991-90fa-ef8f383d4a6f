﻿#include "pch-c.h"
#ifndef _MSC_VER
# include <alloca.h>
#else
# include <malloc.h>
#endif


#include "codegen/il2cpp-codegen-metadata.h"





extern void AkWaapiClient_add_Disconnected_m3EE3CE69870D8F308F53861B898B766FCC499075 (void);
extern void AkWaapiClient_remove_Disconnected_m0DD21DFADB273925618CB65ECDFD088A734E9C34 (void);
extern void AkWaapiClient_Connect_m8D1B70229C31E53192F3EDBD413E67C22D2A2F3C (void);
extern void AkWaapiClient_Wamp_Disconnected_mFB513CCD15106D41D902B980CC0347083E150D69 (void);
extern void AkWaapiClient_Close_mD96B0DD52FBBB241C2AD017C5256889C549F703F (void);
extern void AkWaapiClient_IsConnected_mA016495B2FC851FAA70072DD188EC4F7DD51A3EE (void);
extern void AkWaapiClient_Call_mDAC1560EE41DB637E4F1AE04A092FA19399CA405 (void);
extern void AkWaapiClient_Subscribe_m81ECB1FEA375A1047DD5208DCF005CAD1069538A (void);
extern void AkWaapiClient_Unsubscribe_m08CE3FFE70CAC8EF589ADA7F47E76A43B2CB1BC0 (void);
extern void AkWaapiClient__ctor_mBE306CDA22DE9CD04C3590E33C9ECEEAF73F7A38 (void);
extern void U3CCallU3Ed__8_MoveNext_m63A48834FFEDB4113ABA23BAE5A0ADE6DE59DB29 (void);
extern void U3CCallU3Ed__8_SetStateMachine_mBA23957B8BEE5C2D8B02DA4800568C02BC5C55EB (void);
extern void U3CCloseU3Ed__6_MoveNext_m707864C8BEBA6F1EF15842D9656BB5B09991FB4F (void);
extern void U3CCloseU3Ed__6_SetStateMachine_mC54AFDE6EBB5B7CF3C501D9AED69A4230C99E5BC (void);
extern void U3CConnectU3Ed__4_MoveNext_m22875C191692C1325D4792CC5A1590BA0F96F625 (void);
extern void U3CConnectU3Ed__4_SetStateMachine_m3249867A255316ADBC0D342AFA6E106FF7EE6B2F (void);
extern void U3CSubscribeU3Ed__9_MoveNext_mF55841D70D84500B22E69996CD928189E2E64367 (void);
extern void U3CSubscribeU3Ed__9_SetStateMachine_mDE7275CA3BDF2A4D5450A7393E294B6A847BF139 (void);
extern void U3CUnsubscribeU3Ed__10_MoveNext_m83AB55A384C8D833262F33FEF90C1B852D419540 (void);
extern void U3CUnsubscribeU3Ed__10_SetStateMachine_m525702619D3E46359D490DAC3882E60D950BBA44 (void);
extern void JsonSerializable_op_Implicit_mC01A66A5CAE40E863E047F4E3554A5D1145AAF1E (void);
extern void JsonSerializable__ctor_m248DB234644A91E53E760CD9184FD80FFCBC2BBE (void);
extern void Args__ctor_mA991E7F75DFDD2CFAF7C29CE316B0E2C649754C3 (void);
extern void WaqlArgs__ctor_mCB73986A0B84EC59CE355D7090BC335B8DB5C60D (void);
extern void ArgsObject__ctor_m0AC7ECEFE0E492C712DB75A2CD559F9D14A4C696 (void);
extern void ArgsRename__ctor_m2CDDAAF1CCC293C71434E09E4D432EFEADA2F9DB (void);
extern void ArgsDisplayName__ctor_m7E149536574461E3D9BC0E61851054F35FBB09C2 (void);
extern void ArgsCommand__ctor_m028CAD0317E5D9FD8DAAADFED99C524E8342C07E (void);
extern void ArgsPlay__ctor_mB3874B0933182F0D45619B4DD713941E2540E44F (void);
extern void ArgsTransport__ctor_mE37257A46D2D966D04718356C061B2990152693B (void);
extern void Options__ctor_m7AC7E4E5C80E17CE8297B7A1598B05DC9013BDBF (void);
extern void ReturnOptions__ctor_m2193F9AD15303B580EAB4EA8CD1B5305B4044919 (void);
extern void TransportOptions__ctor_mAEFA9776D08B034590959E06FB2F5A6250F60AF5 (void);
extern void ReturnTransport__ctor_mD3CBAEB13A7E539991DA11A645AA1EB291CB8068 (void);
extern void TransportState__ctor_mCD549180E9749183618697F843A8E3BC2CEAB4CC (void);
extern void ErrorMessage__ctor_mA402D3081A7DB5E4A4B2800AA98A261F79ED84A6 (void);
extern void ErrorDetails__ctor_m9889BC3B9FE34B58D1BACD8FE2F213F3C2CA6324 (void);
extern void ReturnWwiseObjects__ctor_m462ACA39B3864DED9B65A19FF7AAFA4C3EB51CC4 (void);
extern void SelectedWwiseObjects__ctor_mD79ADF702D6D037876DFC47AF78FD754950AFDC6 (void);
extern void WwiseRenameInfo_ParseInfo_m8A5FBD378BA3CDE822BF08F0CDD098B2A8A1624A (void);
extern void WwiseRenameInfo__ctor_m6B94E08BC453BB47111ABD2C65602B8F698AC37B (void);
extern void WwiseChildModifiedInfo_ParseInfo_m3D75BEB94A617B9F5F9B688292BEA2A260DA7E22 (void);
extern void WwiseChildModifiedInfo__ctor_m6F5F77E2C76582C40A76C791553C94525C609A17 (void);
extern void WwiseObjectInfoJsonObject_op_Implicit_m8633163F3B60DAA705ACAB8DEFA640F636B0A5DD (void);
extern void WwiseObjectInfoJsonObject_ToObjectInfo_m0362B7F4EC882D4D9D61519963360D1A5FF22122 (void);
extern void WwiseObjectInfoJsonObject__ctor_mF6C6541CB4A7B09FD7E81F845134ED3103783DFC (void);
extern void WwiseObjectInfoParent__ctor_mEFB3F49F390B21FDCD8C6D27160A568967541441 (void);
extern void WaapiHelper_GetWwiseObjectTypeFromString_mDB58104670B3987959E524F81173AF332A10A764 (void);
extern void WaapiKeywords__ctor_m574A23AD4E164874CA1D27E0D7517F84861AFB37 (void);
extern void WaapiKeywords__cctor_m03AAE389D2FE07F2A2A300822B9A769EBB090162 (void);
extern void ak__ctor_m236CC79276E4536CA0DE22BD3D4AE1F9F58190FC (void);
extern void soundengine__ctor_mF2D8AB024AB57F8BEAF8ADC49A516B188D56F280 (void);
extern void error__ctor_m23F41466577CD097990F00BC09DD182CB6764E21 (void);
extern void wwise__ctor_m27B8C4ECF23E1BCBA19042734C315BEBE5774C80 (void);
extern void error__ctor_m1C10C5D48DEAD61E9D6C4C2B562B69ADF87D69B0 (void);
extern void debug__ctor_mFB492523C3115481E93B0F3CD465B65037FC3D6D (void);
extern void core__ctor_m2B7EEBC43372F4C32603906E00D53184647BA07E (void);
extern void audioSourcePeaks__ctor_m647AECDE2A2AE5EB7A6F51D6040BCCAC90CCC093 (void);
extern void remote__ctor_mBAF79B13149E130AAE6C1CC0773BEE97520C6F0F (void);
extern void log__ctor_mB93906103EE2EBC3FD8FFA90772EE0516226BAEA (void);
extern void object__ctor_mB955A6219B9CE74C0112625235540C465CAF7DDC (void);
extern void undo__ctor_mD20593EB71CE585C9ECFBF488952A4B9F66EB4A3 (void);
extern void profiler__ctor_m70763186198F26F25C62FFC5F6D40BA661DF90A9 (void);
extern void project__ctor_m0A56174A80723E9A0D39D4E2619FBDFFAD16579F (void);
extern void transport__ctor_mC2BE128858E90A74E666197F2EBFF774D38646FC (void);
extern void soundbank__ctor_m74DEFAA221A6D71AB0DB7E6E8A3C624C36AA3F6E (void);
extern void audio__ctor_m10C8F0D3E8394AD0D25D1D4AF65438E7757C1BF3 (void);
extern void switchContainer__ctor_mB353BE4AC14754497CFE4F8174D44E44DE0F00F1 (void);
extern void plugin__ctor_mE8B64EF96418909B418E03C24A87D0D1C2DE1E1E (void);
extern void ui__ctor_m7D4D251F14C5B2EE566B03E8972C0814F16A3B24 (void);
extern void project__ctor_m7642C15B39B637A914E46E0CEBE00362316E833E (void);
extern void commands__ctor_m45937C38BDDE4EFB8C96A45AE8A3DFB46D0B8202 (void);
extern void waapi__ctor_m0E8FF2EF0B1BB5267A1012821488E10D90969135 (void);
extern void Wamp_add_Disconnected_m24B538DF784DBC58D0DC6E7ACD528DF5EFA8D324 (void);
extern void Wamp_remove_Disconnected_m2F7C5ECC83137434F1299F0B66A0F821659D04DD (void);
extern void Wamp_Send_m39C30360CF6A73121E77807684FE0527D5F81FED (void);
extern void Wamp_Parse_m301D6F58B826B315F2DC78CD0608D07CB25610F6 (void);
extern void Wamp_ParseResult_m0246DB22C75B6A0A9F005A90C11B29C35C85AA8C (void);
extern void Wamp_ParseSubscribed_mE8592364DDEAF0303B79A86A9698DFF23EA7892D (void);
extern void Wamp_ParseUnsubscribed_m3963FC413B77DF3BE930EA7B237CF90B202315FC (void);
extern void Wamp_ParseGoodbye_mF417BFFBB0225C32ACE893A151434C2407F19B77 (void);
extern void Wamp_ParseWelcome_m3F2A745D19AB83D95D5B9631C2E88E4267CC3150 (void);
extern void Wamp_ParseEvent_mC3B3F2BC38E13A3E363CAAC93CD4C9CE3E69B458 (void);
extern void Wamp_ReceiveMessage_m317AE6BCBF0B5C22099A768D0E2A6E10119A12BF (void);
extern void Wamp_Receive_m58FF60117FB025D85905F762C7779F0940B7A51C (void);
extern void Wamp_ReceiveExpect_m9C778F7133B19EA200F8A325FC207F8525C18D7B (void);
extern void Wamp_Connect_mF59B76D936B619D654B20D18951021575221DB41 (void);
extern void Wamp_IsConnected_mEF4B01DB2A19D659F267199004CDE222B54A9FA8 (void);
extern void Wamp_SocketState_mEBB95AA45E17F560E077C60457F474EC096492E4 (void);
extern void Wamp_Close_m5E8CD3382607C1AB1B252841BFB6AC374AE17BD3 (void);
extern void Wamp_ProcessEvent_mF11FEBC6D50F09C103298C1A84E01CC2AC7779DB (void);
extern void Wamp_StartListen_m8ACB6D14789878DF2323D1BCA0694FE9A1A532FA (void);
extern void Wamp_OnDisconnect_mEC21A79CB1D4F4E124503A8C94B708EB5682CF7C (void);
extern void Wamp_Call_m16D0F9E6DCFE7F749DA4876241C6A5AD93DA445F (void);
extern void Wamp_Subscribe_mCB91F96A86138F1642527A9DC00DCFE76D7166FD (void);
extern void Wamp_Unsubscribe_m188E7525777557859C34400DF95F7DF931F0B188 (void);
extern void Wamp__ctor_m051B4B32C8476B34B3C3D148EB799B7FE1C5E20A (void);
extern void TimeoutException__ctor_m3AD9E04E57CD7F33F78F72751A8FBBA2E252521B (void);
extern void WampNotConnectedException__ctor_mE574B2C7D409A71F851EFF10E3B5377B51B7C69A (void);
extern void ErrorException_get_Json_mE2A3D3D418A345D8B26897779F692030A2D5A262 (void);
extern void ErrorException_set_Json_m71522D256298AFB903EFDFCDF631330998864331 (void);
extern void ErrorException_get_MessageId_m8F353A6A4889B20E4A9B4798EDA8E23A15F871C8 (void);
extern void ErrorException_set_MessageId_m8604287B869DD6DDEFDCD726E624693A1B946132 (void);
extern void ErrorException_get_RequestId_m26766F7BEE7293FF62EEDC71103808EBE7EBC57B (void);
extern void ErrorException_set_RequestId_m6B0BA2D786A429B217B0027856F924071BDCF6BF (void);
extern void ErrorException_get_Uri_mDF998F0C30D9357C44624DB61D9189CBCC840E8C (void);
extern void ErrorException_set_Uri_m9FCA4E114C3CB127B9BE39B15C3618DD5622C4C7 (void);
extern void ErrorException__ctor_m4BABDE468063418F39C3A8072796241E9A30A3E9 (void);
extern void ErrorException_FromResponse_m16A1011D626F5196F5DB582A12CA44F3D9AA78CD (void);
extern void Response_get_MessageId_m75A45AE0F60F7D580672CE91565C07F3ABC142F2 (void);
extern void Response_set_MessageId_m8621DB298C277944439F38BBE113643E293BC0B6 (void);
extern void Response_get_RequestId_mF89E9AF5B2C3151A1322AB96191DAA767777687C (void);
extern void Response_set_RequestId_m7D0ED5F9B4A145F1800DA6782C7F979090E230B6 (void);
extern void Response_get_ContextSpecificResultId_m87989A7BDA80C30ECDD0EE1238E657E8B829619C (void);
extern void Response_set_ContextSpecificResultId_m5BC869422AD7C407BECEA8D43990F8064FC5CD5E (void);
extern void Response_get_SubscriptionId_m9B7DDE6F0E7D9DB2F86633EAD0653BC959E6182F (void);
extern void Response_set_SubscriptionId_m50A32B2E340514D2D2A5AD1AEFDF0919B70B292E (void);
extern void Response_get_Json_m28B4D189EAD9987B1529391E9ACDC36AB57B8C58 (void);
extern void Response_set_Json_m75F4022540CD60DEAFEC05571AF7EBC90010C3DD (void);
extern void Response__ctor_m11A6C28239EFAF0FF4AA30ED97745F3A42175592 (void);
extern void PublishHandler__ctor_m9430559B2CFBAD436F5DE733C80AE222BEEA7136 (void);
extern void PublishHandler_Invoke_m247568BD6433EE39B2AFB4B1A85E5B4C8135391A (void);
extern void PublishHandler_BeginInvoke_m8E239655985B6D97E93B48F25C27233987AEC218 (void);
extern void PublishHandler_EndInvoke_m91F33670CF3526AB104B78AEE4617342C5214F04 (void);
extern void DisconnectedHandler__ctor_mBA58F30A6AF91061DFF3237F539B7954AB35109D (void);
extern void DisconnectedHandler_Invoke_mE284B3DD0C8FDD8AD4B3BBDE5FB451A6A5E383BB (void);
extern void DisconnectedHandler_BeginInvoke_mA4A7BC7DEA37FB1A8326CC6E3734B6274DAC65D2 (void);
extern void DisconnectedHandler_EndInvoke_m7D78E5BD97AFA7D401595BCBD8965FC4B80F729C (void);
extern void U3CU3Ec__cctor_mC55A89BF22CAD5922FA3A9B950611D91B3AE2DA5 (void);
extern void U3CU3Ec__ctor_m570FDCE4E074EAE4D3A5ECA70680C23EAA0FE97A (void);
extern void U3CU3Ec_U3CReceiveMessageU3Eb__24_0_m1E541742E32AA2ED862BB6C8DBAE21186050ED2B (void);
extern void U3CU3Ec__DisplayClass32_0__ctor_mE8018952898B53CE0CE8A1AFB8922A78D741F291 (void);
extern void U3CU3Ec__DisplayClass32_0_U3CStartListenU3Eb__0_mA676731807C20FD3B51D86EE201367DDB0DA0144 (void);
extern void U3CCallU3Ed__34_MoveNext_m54ED3C0283048835E069CAD05B089EF0EFCBAB2B (void);
extern void U3CCallU3Ed__34_SetStateMachine_m837C83ED9493AA864A1EBE926E575C1593CA9279 (void);
extern void U3CCloseU3Ed__30_MoveNext_mF18AF4BA55A066D63961F1938A75E0EAC5646AE9 (void);
extern void U3CCloseU3Ed__30_SetStateMachine_m06E7825F79AB3C3E81E75170F0B4A8C7069D7A99 (void);
extern void U3CConnectU3Ed__27_MoveNext_m06B59474414A992DCEA250C0A9B21488AA090179 (void);
extern void U3CConnectU3Ed__27_SetStateMachine_m930518CB4C4BD837B4FF80D2DA00530BE61712D0 (void);
extern void U3CReceiveU3Ed__25_MoveNext_m6EC266521292D3B55C7DB7ED3A8D4D9D56701C16 (void);
extern void U3CReceiveU3Ed__25_SetStateMachine_m221CACBD7A2DE323CF1B44493A646A6FAF5D5CF5 (void);
extern void U3CReceiveExpectU3Ed__26_MoveNext_mB03BF6E4FDE3D904867E6C6D566594261507BAA8 (void);
extern void U3CReceiveExpectU3Ed__26_SetStateMachine_m6563ED712C8219C754A8DBE94A7644168A2A3B8C (void);
extern void U3CReceiveMessageU3Ed__24_MoveNext_m4C6FB49F6F1D041AF4E78563D273F957B0ECA655 (void);
extern void U3CReceiveMessageU3Ed__24_SetStateMachine_mA989588BD2C536D69CA8AA43DB9E593EABE0C050 (void);
extern void U3CSendU3Ed__16_MoveNext_m419CF19A556AA855D7DDB98CEC155592D6357F34 (void);
extern void U3CSendU3Ed__16_SetStateMachine_m879AC2C9C66179B49D045D8299B1F8951C09EE96 (void);
extern void U3CSubscribeU3Ed__35_MoveNext_m3EE764B0381AFF8C9D500CFDF8D783F79D2F478F (void);
extern void U3CSubscribeU3Ed__35_SetStateMachine_m4F4E48F930F77799C2B260D67664142DFD7A2C5D (void);
extern void U3CUnsubscribeU3Ed__36_MoveNext_mBC62527D11FC1EA69290E7B0A408D6B53999621C (void);
extern void U3CUnsubscribeU3Ed__36_SetStateMachine_mC5974177CA87CE1A17F74C7B41B9DF34F7E13350 (void);
extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_m14F5752F863CF900B30C6E7744B4470577F56138 (void);
extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_m512A56EA2F8F450E1822D8461B5EF74207F749F4 (void);
static Il2CppMethodPointer s_methodPointers[154] = 
{
	AkWaapiClient_add_Disconnected_m3EE3CE69870D8F308F53861B898B766FCC499075,
	AkWaapiClient_remove_Disconnected_m0DD21DFADB273925618CB65ECDFD088A734E9C34,
	AkWaapiClient_Connect_m8D1B70229C31E53192F3EDBD413E67C22D2A2F3C,
	AkWaapiClient_Wamp_Disconnected_mFB513CCD15106D41D902B980CC0347083E150D69,
	AkWaapiClient_Close_mD96B0DD52FBBB241C2AD017C5256889C549F703F,
	AkWaapiClient_IsConnected_mA016495B2FC851FAA70072DD188EC4F7DD51A3EE,
	AkWaapiClient_Call_mDAC1560EE41DB637E4F1AE04A092FA19399CA405,
	AkWaapiClient_Subscribe_m81ECB1FEA375A1047DD5208DCF005CAD1069538A,
	AkWaapiClient_Unsubscribe_m08CE3FFE70CAC8EF589ADA7F47E76A43B2CB1BC0,
	AkWaapiClient__ctor_mBE306CDA22DE9CD04C3590E33C9ECEEAF73F7A38,
	U3CCallU3Ed__8_MoveNext_m63A48834FFEDB4113ABA23BAE5A0ADE6DE59DB29,
	U3CCallU3Ed__8_SetStateMachine_mBA23957B8BEE5C2D8B02DA4800568C02BC5C55EB,
	U3CCloseU3Ed__6_MoveNext_m707864C8BEBA6F1EF15842D9656BB5B09991FB4F,
	U3CCloseU3Ed__6_SetStateMachine_mC54AFDE6EBB5B7CF3C501D9AED69A4230C99E5BC,
	U3CConnectU3Ed__4_MoveNext_m22875C191692C1325D4792CC5A1590BA0F96F625,
	U3CConnectU3Ed__4_SetStateMachine_m3249867A255316ADBC0D342AFA6E106FF7EE6B2F,
	U3CSubscribeU3Ed__9_MoveNext_mF55841D70D84500B22E69996CD928189E2E64367,
	U3CSubscribeU3Ed__9_SetStateMachine_mDE7275CA3BDF2A4D5450A7393E294B6A847BF139,
	U3CUnsubscribeU3Ed__10_MoveNext_m83AB55A384C8D833262F33FEF90C1B852D419540,
	U3CUnsubscribeU3Ed__10_SetStateMachine_m525702619D3E46359D490DAC3882E60D950BBA44,
	JsonSerializable_op_Implicit_mC01A66A5CAE40E863E047F4E3554A5D1145AAF1E,
	JsonSerializable__ctor_m248DB234644A91E53E760CD9184FD80FFCBC2BBE,
	Args__ctor_mA991E7F75DFDD2CFAF7C29CE316B0E2C649754C3,
	WaqlArgs__ctor_mCB73986A0B84EC59CE355D7090BC335B8DB5C60D,
	ArgsObject__ctor_m0AC7ECEFE0E492C712DB75A2CD559F9D14A4C696,
	ArgsRename__ctor_m2CDDAAF1CCC293C71434E09E4D432EFEADA2F9DB,
	ArgsDisplayName__ctor_m7E149536574461E3D9BC0E61851054F35FBB09C2,
	ArgsCommand__ctor_m028CAD0317E5D9FD8DAAADFED99C524E8342C07E,
	ArgsPlay__ctor_mB3874B0933182F0D45619B4DD713941E2540E44F,
	ArgsTransport__ctor_mE37257A46D2D966D04718356C061B2990152693B,
	Options__ctor_m7AC7E4E5C80E17CE8297B7A1598B05DC9013BDBF,
	ReturnOptions__ctor_m2193F9AD15303B580EAB4EA8CD1B5305B4044919,
	TransportOptions__ctor_mAEFA9776D08B034590959E06FB2F5A6250F60AF5,
	ReturnTransport__ctor_mD3CBAEB13A7E539991DA11A645AA1EB291CB8068,
	TransportState__ctor_mCD549180E9749183618697F843A8E3BC2CEAB4CC,
	ErrorMessage__ctor_mA402D3081A7DB5E4A4B2800AA98A261F79ED84A6,
	ErrorDetails__ctor_m9889BC3B9FE34B58D1BACD8FE2F213F3C2CA6324,
	ReturnWwiseObjects__ctor_m462ACA39B3864DED9B65A19FF7AAFA4C3EB51CC4,
	NULL,
	SelectedWwiseObjects__ctor_mD79ADF702D6D037876DFC47AF78FD754950AFDC6,
	WwiseRenameInfo_ParseInfo_m8A5FBD378BA3CDE822BF08F0CDD098B2A8A1624A,
	WwiseRenameInfo__ctor_m6B94E08BC453BB47111ABD2C65602B8F698AC37B,
	WwiseChildModifiedInfo_ParseInfo_m3D75BEB94A617B9F5F9B688292BEA2A260DA7E22,
	WwiseChildModifiedInfo__ctor_m6F5F77E2C76582C40A76C791553C94525C609A17,
	WwiseObjectInfoJsonObject_op_Implicit_m8633163F3B60DAA705ACAB8DEFA640F636B0A5DD,
	WwiseObjectInfoJsonObject_ToObjectInfo_m0362B7F4EC882D4D9D61519963360D1A5FF22122,
	WwiseObjectInfoJsonObject__ctor_mF6C6541CB4A7B09FD7E81F845134ED3103783DFC,
	WwiseObjectInfoParent__ctor_mEFB3F49F390B21FDCD8C6D27160A568967541441,
	WaapiHelper_GetWwiseObjectTypeFromString_mDB58104670B3987959E524F81173AF332A10A764,
	WaapiKeywords__ctor_m574A23AD4E164874CA1D27E0D7517F84861AFB37,
	WaapiKeywords__cctor_m03AAE389D2FE07F2A2A300822B9A769EBB090162,
	ak__ctor_m236CC79276E4536CA0DE22BD3D4AE1F9F58190FC,
	soundengine__ctor_mF2D8AB024AB57F8BEAF8ADC49A516B188D56F280,
	error__ctor_m23F41466577CD097990F00BC09DD182CB6764E21,
	wwise__ctor_m27B8C4ECF23E1BCBA19042734C315BEBE5774C80,
	error__ctor_m1C10C5D48DEAD61E9D6C4C2B562B69ADF87D69B0,
	debug__ctor_mFB492523C3115481E93B0F3CD465B65037FC3D6D,
	core__ctor_m2B7EEBC43372F4C32603906E00D53184647BA07E,
	audioSourcePeaks__ctor_m647AECDE2A2AE5EB7A6F51D6040BCCAC90CCC093,
	remote__ctor_mBAF79B13149E130AAE6C1CC0773BEE97520C6F0F,
	log__ctor_mB93906103EE2EBC3FD8FFA90772EE0516226BAEA,
	object__ctor_mB955A6219B9CE74C0112625235540C465CAF7DDC,
	undo__ctor_mD20593EB71CE585C9ECFBF488952A4B9F66EB4A3,
	profiler__ctor_m70763186198F26F25C62FFC5F6D40BA661DF90A9,
	project__ctor_m0A56174A80723E9A0D39D4E2619FBDFFAD16579F,
	transport__ctor_mC2BE128858E90A74E666197F2EBFF774D38646FC,
	soundbank__ctor_m74DEFAA221A6D71AB0DB7E6E8A3C624C36AA3F6E,
	audio__ctor_m10C8F0D3E8394AD0D25D1D4AF65438E7757C1BF3,
	switchContainer__ctor_mB353BE4AC14754497CFE4F8174D44E44DE0F00F1,
	plugin__ctor_mE8B64EF96418909B418E03C24A87D0D1C2DE1E1E,
	ui__ctor_m7D4D251F14C5B2EE566B03E8972C0814F16A3B24,
	project__ctor_m7642C15B39B637A914E46E0CEBE00362316E833E,
	commands__ctor_m45937C38BDDE4EFB8C96A45AE8A3DFB46D0B8202,
	waapi__ctor_m0E8FF2EF0B1BB5267A1012821488E10D90969135,
	Wamp_add_Disconnected_m24B538DF784DBC58D0DC6E7ACD528DF5EFA8D324,
	Wamp_remove_Disconnected_m2F7C5ECC83137434F1299F0B66A0F821659D04DD,
	Wamp_Send_m39C30360CF6A73121E77807684FE0527D5F81FED,
	Wamp_Parse_m301D6F58B826B315F2DC78CD0608D07CB25610F6,
	Wamp_ParseResult_m0246DB22C75B6A0A9F005A90C11B29C35C85AA8C,
	Wamp_ParseSubscribed_mE8592364DDEAF0303B79A86A9698DFF23EA7892D,
	Wamp_ParseUnsubscribed_m3963FC413B77DF3BE930EA7B237CF90B202315FC,
	Wamp_ParseGoodbye_mF417BFFBB0225C32ACE893A151434C2407F19B77,
	Wamp_ParseWelcome_m3F2A745D19AB83D95D5B9631C2E88E4267CC3150,
	Wamp_ParseEvent_mC3B3F2BC38E13A3E363CAAC93CD4C9CE3E69B458,
	Wamp_ReceiveMessage_m317AE6BCBF0B5C22099A768D0E2A6E10119A12BF,
	Wamp_Receive_m58FF60117FB025D85905F762C7779F0940B7A51C,
	Wamp_ReceiveExpect_m9C778F7133B19EA200F8A325FC207F8525C18D7B,
	Wamp_Connect_mF59B76D936B619D654B20D18951021575221DB41,
	Wamp_IsConnected_mEF4B01DB2A19D659F267199004CDE222B54A9FA8,
	Wamp_SocketState_mEBB95AA45E17F560E077C60457F474EC096492E4,
	Wamp_Close_m5E8CD3382607C1AB1B252841BFB6AC374AE17BD3,
	Wamp_ProcessEvent_mF11FEBC6D50F09C103298C1A84E01CC2AC7779DB,
	Wamp_StartListen_m8ACB6D14789878DF2323D1BCA0694FE9A1A532FA,
	Wamp_OnDisconnect_mEC21A79CB1D4F4E124503A8C94B708EB5682CF7C,
	Wamp_Call_m16D0F9E6DCFE7F749DA4876241C6A5AD93DA445F,
	Wamp_Subscribe_mCB91F96A86138F1642527A9DC00DCFE76D7166FD,
	Wamp_Unsubscribe_m188E7525777557859C34400DF95F7DF931F0B188,
	Wamp__ctor_m051B4B32C8476B34B3C3D148EB799B7FE1C5E20A,
	TimeoutException__ctor_m3AD9E04E57CD7F33F78F72751A8FBBA2E252521B,
	WampNotConnectedException__ctor_mE574B2C7D409A71F851EFF10E3B5377B51B7C69A,
	ErrorException_get_Json_mE2A3D3D418A345D8B26897779F692030A2D5A262,
	ErrorException_set_Json_m71522D256298AFB903EFDFCDF631330998864331,
	ErrorException_get_MessageId_m8F353A6A4889B20E4A9B4798EDA8E23A15F871C8,
	ErrorException_set_MessageId_m8604287B869DD6DDEFDCD726E624693A1B946132,
	ErrorException_get_RequestId_m26766F7BEE7293FF62EEDC71103808EBE7EBC57B,
	ErrorException_set_RequestId_m6B0BA2D786A429B217B0027856F924071BDCF6BF,
	ErrorException_get_Uri_mDF998F0C30D9357C44624DB61D9189CBCC840E8C,
	ErrorException_set_Uri_m9FCA4E114C3CB127B9BE39B15C3618DD5622C4C7,
	ErrorException__ctor_m4BABDE468063418F39C3A8072796241E9A30A3E9,
	ErrorException_FromResponse_m16A1011D626F5196F5DB582A12CA44F3D9AA78CD,
	Response_get_MessageId_m75A45AE0F60F7D580672CE91565C07F3ABC142F2,
	Response_set_MessageId_m8621DB298C277944439F38BBE113643E293BC0B6,
	Response_get_RequestId_mF89E9AF5B2C3151A1322AB96191DAA767777687C,
	Response_set_RequestId_m7D0ED5F9B4A145F1800DA6782C7F979090E230B6,
	Response_get_ContextSpecificResultId_m87989A7BDA80C30ECDD0EE1238E657E8B829619C,
	Response_set_ContextSpecificResultId_m5BC869422AD7C407BECEA8D43990F8064FC5CD5E,
	Response_get_SubscriptionId_m9B7DDE6F0E7D9DB2F86633EAD0653BC959E6182F,
	Response_set_SubscriptionId_m50A32B2E340514D2D2A5AD1AEFDF0919B70B292E,
	Response_get_Json_m28B4D189EAD9987B1529391E9ACDC36AB57B8C58,
	Response_set_Json_m75F4022540CD60DEAFEC05571AF7EBC90010C3DD,
	Response__ctor_m11A6C28239EFAF0FF4AA30ED97745F3A42175592,
	PublishHandler__ctor_m9430559B2CFBAD436F5DE733C80AE222BEEA7136,
	PublishHandler_Invoke_m247568BD6433EE39B2AFB4B1A85E5B4C8135391A,
	PublishHandler_BeginInvoke_m8E239655985B6D97E93B48F25C27233987AEC218,
	PublishHandler_EndInvoke_m91F33670CF3526AB104B78AEE4617342C5214F04,
	DisconnectedHandler__ctor_mBA58F30A6AF91061DFF3237F539B7954AB35109D,
	DisconnectedHandler_Invoke_mE284B3DD0C8FDD8AD4B3BBDE5FB451A6A5E383BB,
	DisconnectedHandler_BeginInvoke_mA4A7BC7DEA37FB1A8326CC6E3734B6274DAC65D2,
	DisconnectedHandler_EndInvoke_m7D78E5BD97AFA7D401595BCBD8965FC4B80F729C,
	U3CU3Ec__cctor_mC55A89BF22CAD5922FA3A9B950611D91B3AE2DA5,
	U3CU3Ec__ctor_m570FDCE4E074EAE4D3A5ECA70680C23EAA0FE97A,
	U3CU3Ec_U3CReceiveMessageU3Eb__24_0_m1E541742E32AA2ED862BB6C8DBAE21186050ED2B,
	U3CU3Ec__DisplayClass32_0__ctor_mE8018952898B53CE0CE8A1AFB8922A78D741F291,
	U3CU3Ec__DisplayClass32_0_U3CStartListenU3Eb__0_mA676731807C20FD3B51D86EE201367DDB0DA0144,
	U3CCallU3Ed__34_MoveNext_m54ED3C0283048835E069CAD05B089EF0EFCBAB2B,
	U3CCallU3Ed__34_SetStateMachine_m837C83ED9493AA864A1EBE926E575C1593CA9279,
	U3CCloseU3Ed__30_MoveNext_mF18AF4BA55A066D63961F1938A75E0EAC5646AE9,
	U3CCloseU3Ed__30_SetStateMachine_m06E7825F79AB3C3E81E75170F0B4A8C7069D7A99,
	U3CConnectU3Ed__27_MoveNext_m06B59474414A992DCEA250C0A9B21488AA090179,
	U3CConnectU3Ed__27_SetStateMachine_m930518CB4C4BD837B4FF80D2DA00530BE61712D0,
	U3CReceiveU3Ed__25_MoveNext_m6EC266521292D3B55C7DB7ED3A8D4D9D56701C16,
	U3CReceiveU3Ed__25_SetStateMachine_m221CACBD7A2DE323CF1B44493A646A6FAF5D5CF5,
	U3CReceiveExpectU3Ed__26_MoveNext_mB03BF6E4FDE3D904867E6C6D566594261507BAA8,
	U3CReceiveExpectU3Ed__26_SetStateMachine_m6563ED712C8219C754A8DBE94A7644168A2A3B8C,
	U3CReceiveMessageU3Ed__24_MoveNext_m4C6FB49F6F1D041AF4E78563D273F957B0ECA655,
	U3CReceiveMessageU3Ed__24_SetStateMachine_mA989588BD2C536D69CA8AA43DB9E593EABE0C050,
	U3CSendU3Ed__16_MoveNext_m419CF19A556AA855D7DDB98CEC155592D6357F34,
	U3CSendU3Ed__16_SetStateMachine_m879AC2C9C66179B49D045D8299B1F8951C09EE96,
	U3CSubscribeU3Ed__35_MoveNext_m3EE764B0381AFF8C9D500CFDF8D783F79D2F478F,
	U3CSubscribeU3Ed__35_SetStateMachine_m4F4E48F930F77799C2B260D67664142DFD7A2C5D,
	U3CUnsubscribeU3Ed__36_MoveNext_mBC62527D11FC1EA69290E7B0A408D6B53999621C,
	U3CUnsubscribeU3Ed__36_SetStateMachine_mC5974177CA87CE1A17F74C7B41B9DF34F7E13350,
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_m14F5752F863CF900B30C6E7744B4470577F56138,
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_m512A56EA2F8F450E1822D8461B5EF74207F749F4,
};
extern void U3CCallU3Ed__8_MoveNext_m63A48834FFEDB4113ABA23BAE5A0ADE6DE59DB29_AdjustorThunk (void);
extern void U3CCallU3Ed__8_SetStateMachine_mBA23957B8BEE5C2D8B02DA4800568C02BC5C55EB_AdjustorThunk (void);
extern void U3CCloseU3Ed__6_MoveNext_m707864C8BEBA6F1EF15842D9656BB5B09991FB4F_AdjustorThunk (void);
extern void U3CCloseU3Ed__6_SetStateMachine_mC54AFDE6EBB5B7CF3C501D9AED69A4230C99E5BC_AdjustorThunk (void);
extern void U3CConnectU3Ed__4_MoveNext_m22875C191692C1325D4792CC5A1590BA0F96F625_AdjustorThunk (void);
extern void U3CConnectU3Ed__4_SetStateMachine_m3249867A255316ADBC0D342AFA6E106FF7EE6B2F_AdjustorThunk (void);
extern void U3CSubscribeU3Ed__9_MoveNext_mF55841D70D84500B22E69996CD928189E2E64367_AdjustorThunk (void);
extern void U3CSubscribeU3Ed__9_SetStateMachine_mDE7275CA3BDF2A4D5450A7393E294B6A847BF139_AdjustorThunk (void);
extern void U3CUnsubscribeU3Ed__10_MoveNext_m83AB55A384C8D833262F33FEF90C1B852D419540_AdjustorThunk (void);
extern void U3CUnsubscribeU3Ed__10_SetStateMachine_m525702619D3E46359D490DAC3882E60D950BBA44_AdjustorThunk (void);
extern void U3CCallU3Ed__34_MoveNext_m54ED3C0283048835E069CAD05B089EF0EFCBAB2B_AdjustorThunk (void);
extern void U3CCallU3Ed__34_SetStateMachine_m837C83ED9493AA864A1EBE926E575C1593CA9279_AdjustorThunk (void);
extern void U3CCloseU3Ed__30_MoveNext_mF18AF4BA55A066D63961F1938A75E0EAC5646AE9_AdjustorThunk (void);
extern void U3CCloseU3Ed__30_SetStateMachine_m06E7825F79AB3C3E81E75170F0B4A8C7069D7A99_AdjustorThunk (void);
extern void U3CConnectU3Ed__27_MoveNext_m06B59474414A992DCEA250C0A9B21488AA090179_AdjustorThunk (void);
extern void U3CConnectU3Ed__27_SetStateMachine_m930518CB4C4BD837B4FF80D2DA00530BE61712D0_AdjustorThunk (void);
extern void U3CReceiveU3Ed__25_MoveNext_m6EC266521292D3B55C7DB7ED3A8D4D9D56701C16_AdjustorThunk (void);
extern void U3CReceiveU3Ed__25_SetStateMachine_m221CACBD7A2DE323CF1B44493A646A6FAF5D5CF5_AdjustorThunk (void);
extern void U3CReceiveExpectU3Ed__26_MoveNext_mB03BF6E4FDE3D904867E6C6D566594261507BAA8_AdjustorThunk (void);
extern void U3CReceiveExpectU3Ed__26_SetStateMachine_m6563ED712C8219C754A8DBE94A7644168A2A3B8C_AdjustorThunk (void);
extern void U3CReceiveMessageU3Ed__24_MoveNext_m4C6FB49F6F1D041AF4E78563D273F957B0ECA655_AdjustorThunk (void);
extern void U3CReceiveMessageU3Ed__24_SetStateMachine_mA989588BD2C536D69CA8AA43DB9E593EABE0C050_AdjustorThunk (void);
extern void U3CSendU3Ed__16_MoveNext_m419CF19A556AA855D7DDB98CEC155592D6357F34_AdjustorThunk (void);
extern void U3CSendU3Ed__16_SetStateMachine_m879AC2C9C66179B49D045D8299B1F8951C09EE96_AdjustorThunk (void);
extern void U3CSubscribeU3Ed__35_MoveNext_m3EE764B0381AFF8C9D500CFDF8D783F79D2F478F_AdjustorThunk (void);
extern void U3CSubscribeU3Ed__35_SetStateMachine_m4F4E48F930F77799C2B260D67664142DFD7A2C5D_AdjustorThunk (void);
extern void U3CUnsubscribeU3Ed__36_MoveNext_mBC62527D11FC1EA69290E7B0A408D6B53999621C_AdjustorThunk (void);
extern void U3CUnsubscribeU3Ed__36_SetStateMachine_mC5974177CA87CE1A17F74C7B41B9DF34F7E13350_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[28] = 
{
	{ 0x0600000B, U3CCallU3Ed__8_MoveNext_m63A48834FFEDB4113ABA23BAE5A0ADE6DE59DB29_AdjustorThunk },
	{ 0x0600000C, U3CCallU3Ed__8_SetStateMachine_mBA23957B8BEE5C2D8B02DA4800568C02BC5C55EB_AdjustorThunk },
	{ 0x0600000D, U3CCloseU3Ed__6_MoveNext_m707864C8BEBA6F1EF15842D9656BB5B09991FB4F_AdjustorThunk },
	{ 0x0600000E, U3CCloseU3Ed__6_SetStateMachine_mC54AFDE6EBB5B7CF3C501D9AED69A4230C99E5BC_AdjustorThunk },
	{ 0x0600000F, U3CConnectU3Ed__4_MoveNext_m22875C191692C1325D4792CC5A1590BA0F96F625_AdjustorThunk },
	{ 0x06000010, U3CConnectU3Ed__4_SetStateMachine_m3249867A255316ADBC0D342AFA6E106FF7EE6B2F_AdjustorThunk },
	{ 0x06000011, U3CSubscribeU3Ed__9_MoveNext_mF55841D70D84500B22E69996CD928189E2E64367_AdjustorThunk },
	{ 0x06000012, U3CSubscribeU3Ed__9_SetStateMachine_mDE7275CA3BDF2A4D5450A7393E294B6A847BF139_AdjustorThunk },
	{ 0x06000013, U3CUnsubscribeU3Ed__10_MoveNext_m83AB55A384C8D833262F33FEF90C1B852D419540_AdjustorThunk },
	{ 0x06000014, U3CUnsubscribeU3Ed__10_SetStateMachine_m525702619D3E46359D490DAC3882E60D950BBA44_AdjustorThunk },
	{ 0x06000087, U3CCallU3Ed__34_MoveNext_m54ED3C0283048835E069CAD05B089EF0EFCBAB2B_AdjustorThunk },
	{ 0x06000088, U3CCallU3Ed__34_SetStateMachine_m837C83ED9493AA864A1EBE926E575C1593CA9279_AdjustorThunk },
	{ 0x06000089, U3CCloseU3Ed__30_MoveNext_mF18AF4BA55A066D63961F1938A75E0EAC5646AE9_AdjustorThunk },
	{ 0x0600008A, U3CCloseU3Ed__30_SetStateMachine_m06E7825F79AB3C3E81E75170F0B4A8C7069D7A99_AdjustorThunk },
	{ 0x0600008B, U3CConnectU3Ed__27_MoveNext_m06B59474414A992DCEA250C0A9B21488AA090179_AdjustorThunk },
	{ 0x0600008C, U3CConnectU3Ed__27_SetStateMachine_m930518CB4C4BD837B4FF80D2DA00530BE61712D0_AdjustorThunk },
	{ 0x0600008D, U3CReceiveU3Ed__25_MoveNext_m6EC266521292D3B55C7DB7ED3A8D4D9D56701C16_AdjustorThunk },
	{ 0x0600008E, U3CReceiveU3Ed__25_SetStateMachine_m221CACBD7A2DE323CF1B44493A646A6FAF5D5CF5_AdjustorThunk },
	{ 0x0600008F, U3CReceiveExpectU3Ed__26_MoveNext_mB03BF6E4FDE3D904867E6C6D566594261507BAA8_AdjustorThunk },
	{ 0x06000090, U3CReceiveExpectU3Ed__26_SetStateMachine_m6563ED712C8219C754A8DBE94A7644168A2A3B8C_AdjustorThunk },
	{ 0x06000091, U3CReceiveMessageU3Ed__24_MoveNext_m4C6FB49F6F1D041AF4E78563D273F957B0ECA655_AdjustorThunk },
	{ 0x06000092, U3CReceiveMessageU3Ed__24_SetStateMachine_mA989588BD2C536D69CA8AA43DB9E593EABE0C050_AdjustorThunk },
	{ 0x06000093, U3CSendU3Ed__16_MoveNext_m419CF19A556AA855D7DDB98CEC155592D6357F34_AdjustorThunk },
	{ 0x06000094, U3CSendU3Ed__16_SetStateMachine_m879AC2C9C66179B49D045D8299B1F8951C09EE96_AdjustorThunk },
	{ 0x06000095, U3CSubscribeU3Ed__35_MoveNext_m3EE764B0381AFF8C9D500CFDF8D783F79D2F478F_AdjustorThunk },
	{ 0x06000096, U3CSubscribeU3Ed__35_SetStateMachine_m4F4E48F930F77799C2B260D67664142DFD7A2C5D_AdjustorThunk },
	{ 0x06000097, U3CUnsubscribeU3Ed__36_MoveNext_mBC62527D11FC1EA69290E7B0A408D6B53999621C_AdjustorThunk },
	{ 0x06000098, U3CUnsubscribeU3Ed__36_SetStateMachine_mC5974177CA87CE1A17F74C7B41B9DF34F7E13350_AdjustorThunk },
};
static const int32_t s_InvokerIndices[154] = 
{
	10682,
	10682,
	4487,
	13298,
	9267,
	12815,
	1666,
	1666,
	4509,
	13298,
	13298,
	10682,
	13298,
	10682,
	13298,
	10682,
	13298,
	10682,
	13298,
	10682,
	20515,
	13298,
	13298,
	10682,
	10682,
	5688,
	10682,
	5688,
	5681,
	10629,
	13298,
	10682,
	10629,
	13298,
	13298,
	13298,
	13298,
	13298,
	0,
	13298,
	13298,
	13298,
	13298,
	13298,
	20879,
	20879,
	13298,
	13298,
	17842,
	13298,
	21355,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	13298,
	10682,
	10682,
	4487,
	9272,
	20515,
	20515,
	20515,
	20515,
	20515,
	20515,
	13052,
	9267,
	2373,
	4487,
	12815,
	12996,
	9267,
	10682,
	13298,
	13298,
	1666,
	1666,
	4509,
	13298,
	10682,
	10682,
	13052,
	10682,
	12996,
	10629,
	12996,
	10629,
	13052,
	10682,
	10682,
	20515,
	12996,
	10629,
	12996,
	10629,
	12996,
	10629,
	13261,
	10891,
	13052,
	10682,
	13298,
	5684,
	10682,
	2408,
	10682,
	5684,
	13298,
	4489,
	10682,
	21355,
	13298,
	9272,
	13298,
	13298,
	13298,
	10682,
	13298,
	10682,
	13298,
	10682,
	13298,
	10682,
	13298,
	10682,
	13298,
	10682,
	13298,
	10682,
	13298,
	10682,
	13298,
	10682,
	21377,
	13298,
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_Ak_Wwise_Api_WAAPI_CodeGenModule;
const Il2CppCodeGenModule g_Ak_Wwise_Api_WAAPI_CodeGenModule = 
{
	"Ak.Wwise.Api.WAAPI.dll",
	154,
	s_methodPointers,
	28,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};
